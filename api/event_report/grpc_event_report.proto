// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v3.9.1

syntax = "proto3";
package ga.api.event_report;



import "eventreportlogic/event-report-logic_.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/event_report;event_report";

service EventReportLogic {
    option (ga.api.extension.logic_service_name) = "event-report-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.EventReportLogic/";
    rpc ReportPlayroomEvent(ga.eventreportlogic.ReportPlayroomEventReq) returns (ga.eventreportlogic.ReportPlayroomEventResp) {
        option (ga.api.extension.command) = {
             id: 90000
        };
    }
    rpc RecordNewUserTimeCount(ga.eventreportlogic.RecordNewUserTimeCountReq) returns (ga.eventreportlogic.RecordNewUserTimeCountResp) {
        option (ga.api.extension.command) = {
             id: 90001
        };
    }
    rpc ReportUserMicStatus(ga.eventreportlogic.ReportMicStatusReq) returns (ga.eventreportlogic.ReportMicStatusResp) {
        option (ga.api.extension.command) = {
             id: 90002
        };
    }
}

