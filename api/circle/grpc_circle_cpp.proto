syntax = "proto3";
package ga.api.circle;



import "api/extension/extension.proto";
import "circle/circle_.proto";
import "circle/circle2_.proto";
import "recruit/recruit_.proto";

//server_name=circlelogic
//language=cpp

option java_package = "com.quwan.tt.proto.api";
option go_package = "golang.52tt.com/protocol/app/api/circle;circle";
option objc_class_prefix = "RPC";

service CircleLogic {
    //circle
    rpc GameCircleCancelHighlightTopic(ga.circle.GameCircleCancelHighlightTopicReq) returns (ga.circle.GameCircleCancelHighlightTopicResp) {
        option (ga.api.extension.command) = {
            id: 179
        };
    }

    rpc GameCircleCheckCircleUpdate(ga.circle.CheckCircleUpdateReq) returns (ga.circle.CheckCircleUpdateResp) {
        option (ga.api.extension.command) = {
            id: 176
        };
    }

    rpc GameCircleDeleteComment(ga.circle.GameCircleDeleteCommentReq) returns (ga.circle.GameCircleDeleteCommentResp) {
        option (ga.api.extension.command) = {
            id: 154
        };
    }

    rpc GameCircleDeleteTopic(ga.circle.GameCircleDeleteTopicReq) returns (ga.circle.GameCircleDeleteTopicResp) {
        option (ga.api.extension.command) = {
            id: 153
        };
    }

    rpc GameCircleGetCircle(ga.circle.GameCircleGetCircleReq) returns (ga.circle.GameCircleGetCircleResp) {
        option (ga.api.extension.command) = {
            id: 155
        };
    }

    rpc GameCircleGetCommentList(ga.circle.GameCircleGetCommentListReq) returns (ga.circle.GameCircleGetCommentListResp) {
        option (ga.api.extension.command) = {
            id: 149
        };
    }

    rpc GameCircleGetLikeUserList(ga.circle.GameCircleGetLikeUserListReq) returns (ga.circle.GameCircleGetLikeUserListResp) {
        option (ga.api.extension.command) = {
            id: 160
        };
    }

    rpc GameCircleGetMyCircle(ga.circle.GameCircleGetMyCircleReq) returns (ga.circle.GameCircleGetMyCircleResp) {
        option (ga.api.extension.command) = {
            id: 142
        };
    }

    rpc GameCircleGetTopList(ga.circle.GameCircleGetTopListReq) returns (ga.circle.GameCircleGetTopListResp) {
        option (ga.api.extension.command) = {
            id: 143
        };
    }

    rpc GameCircleGetTopicList(ga.circle.GameCircleGetTopicListReq) returns (ga.circle.GameCircleGetTopicListResp) {
        option (ga.api.extension.command) = {
            id: 146
        };
    }

    rpc GameCircleGetTopic(ga.circle.GameCircleGetTopicReq) returns (ga.circle.GameCircleGetTopicResp) {
        option (ga.api.extension.command) = {
            id: 147
        };
    }

    rpc GameCircleGetUserTopic(ga.circle.GameCircleGetUserTopicReq) returns (ga.circle.GameCircleGetUserTopicResp) {
        option (ga.api.extension.command) = {
            id: 156
        };
    }

    rpc GameCircleHighlightTopic(ga.circle.GameCircleHighlightTopicReq) returns (ga.circle.GameCircleHighlightTopicResp) {
        option (ga.api.extension.command) = {
            id: 178
        };
    }

    rpc GameCircleJoin(ga.circle.GameCircleJoinReq) returns (ga.circle.GameCircleJoinResp) {
        option (ga.api.extension.command) = {
            id: 144
        };
    }

    rpc GameCircleLikeTopic(ga.circle.GameCircleLikeTopicReq) returns (ga.circle.GameCircleLikeTopicResp) {
        option (ga.api.extension.command) = {
            id: 151
        };
    }

    rpc GameCircleManagerDeleteComment(ga.circle.GameCircleManagerDeleteCommentReq) returns (ga.circle.GameCircleManagerDeleteCommentResp) {
        option (ga.api.extension.command) = {
            id: 180
        };
    }

    rpc GameCircleMarkReaded(ga.circle.GameCircleMarkReadedReq) returns (ga.circle.GameCircleMarkReadedResp) {
        option (ga.api.extension.command) = {
            id: 157
        };
    }

    rpc GameCircleMuteUser(ga.circle.GameCircleMuteUserReq) returns (ga.circle.GameCircleMuteUserResp) {
        option (ga.api.extension.command) = {
            id: 164
        };
    }

    rpc GameCircleQuit(ga.circle.GameCircleQuitReq) returns (ga.circle.GameCircleQuitResp) {
        option (ga.api.extension.command) = {
            id: 145
        };
    }

    rpc GameCircleReportTopic(ga.circle.GameCircleReportTopicReq) returns (ga.circle.GameCircleReportTopicResp) {
        option (ga.api.extension.command) = {
            id: 152
        };
    }

    rpc GameCircleSendComment(ga.circle.GameCircleSendCommentReq) returns (ga.circle.GameCircleSendCommentResp) {
        option (ga.api.extension.command) = {
            id: 150
        };
    }

    rpc GameCircleSendTopic(ga.circle.GameCircleSendTopicReq) returns (ga.circle.GameCircleSendTopicResp) {
        option (ga.api.extension.command) = {
            id: 148
        };
    }

    rpc GameCircleSetMyCircleOrder(ga.circle.MyGameCircleOrderReq) returns (ga.circle.MyGameCircleOrderResp) {
        option (ga.api.extension.command) = {
            id: 177
        };
    }

    rpc GameCircleUnmuteUser(ga.circle.GameCircleUnmuteUserReq) returns (ga.circle.GameCircleUnmuteUserResp) {
        option (ga.api.extension.command) = {
            id: 165
        };
    }

    //circle2
    rpc CircleGetAnn(ga.circle.CircleGetActivityListReq) returns (ga.circle.CircleGetActivityListResp) {
        option (ga.api.extension.command) = {
            id: 314
        };
    }

    rpc CircleGetCircleDetail(ga.circle.CircleGetCircleDetailReq) returns (ga.circle.CircleGetCircleDetailResp) {
        option (ga.api.extension.command) = {
            id: 308
        };
    }

    rpc CircleGetCommentList(ga.circle.CircleGetCommentListReq) returns (ga.circle.CircleGetCommentListResp) {
        option (ga.api.extension.command) = {
            id: 306
        };
    }

    rpc CircleGetCommentListV2(ga.circle.CircleGetNomalCommentListReq) returns (ga.circle.CircleGetNomalCommentListResp) {
        option (ga.api.extension.command) = {
            id: 312
        };
    }

    rpc CircleGetCommentReplyList(ga.circle.CircleGetCommentReplyListReq) returns (ga.circle.CircleGetCommentReplyListResp) {
        option (ga.api.extension.command) = {
            id: 313
        };
    }

    rpc CircleGetHot(ga.circle.CircleGetHotReq) returns (ga.circle.CircleGetHotResp) {
        option (ga.api.extension.command) = {
            id: 315
        };
    }

    rpc CircleGetLikeUserList(ga.circle.CircleGetLikeUserListReq) returns (ga.circle.CircleGetLikeUserListResp) {
        option (ga.api.extension.command) = {
            id: 310
        };
    }

    rpc CircleGetList(ga.circle.CircleGetListReq) returns (ga.circle.CircleGetListResp) {
        option (ga.api.extension.command) = {
            id: 300
        };
    }

    rpc CircleGetTopicDetail(ga.circle.CircleGetTopicReq) returns (ga.circle.CircleGetTopicResp) {
        option (ga.api.extension.command) = {
            id: 304
        };
    }

    rpc CircleGetTopicList(ga.circle.CircleGetTopicListReq) returns (ga.circle.CircleGetTopicListResp) {
        option (ga.api.extension.command) = {
            id: 303
        };
    }

    rpc CircleGetUserTopicList(ga.circle.CircleGetUserTopicReq) returns (ga.circle.CircleGetUserTopicResp) {
        option (ga.api.extension.command) = {
            id: 309
        };
    }

    rpc CirclePostComment(ga.circle.CirclePostCommentReq) returns (ga.circle.CirclePostCommentResp) {
        option (ga.api.extension.command) = {
            id: 307
        };
    }

    rpc CirclePostTopic(ga.circle.CirclePostTopicReq) returns (ga.circle.CirclePostTopicResp) {
        option (ga.api.extension.command) = {
            id: 305
        };
    }

    rpc GameCircleMuteReasonList(ga.circle.GameCircleMuteReasonListReq) returns (ga.circle.GameCircleMuteReasonListResp) {
        option (ga.api.extension.command) = {
            id: 311
        };
    }

    rpc GetRecruitRecommendGameList(ga.recruit.GetRecruitGameListReq) returns (ga.recruit.GetRecruitGameListResp) {
        option (ga.api.extension.command) = {
            id: 474
        };
    }

    rpc GetRecruitList(ga.recruit.GetGuildRecruitListByGameIDReq) returns (ga.recruit.GetGuildRecruitListByGameIDResp) {
        option (ga.api.extension.command) = {
            id: 472
        };
    }

    rpc PostRecruit(ga.recruit.PostGuildGameMemberRecruitReq) returns (ga.recruit.PostGuildGameMemberRecruitResp) {
        option (ga.api.extension.command) = {
            id: 470
        };
    }

    rpc ReportRecruit(ga.recruit.ReportGuildGameMemberRecruitReq) returns (ga.recruit.ReportGuildGameMemberRecruitResp) {
        option (ga.api.extension.command) = {
            id: 473
        };
    }

    rpc SupportRecruit(ga.recruit.SupportGuildGameMemberRecruitReq) returns (ga.recruit.SupportGuildGameMemberRecruitResp) {
        option (ga.api.extension.command) = {
            id: 471
        };
    }

    option (ga.api.extension.logic_service_name) = "circlelogic";
    option (ga.api.extension.logic_service_language) = "cpp";
}
