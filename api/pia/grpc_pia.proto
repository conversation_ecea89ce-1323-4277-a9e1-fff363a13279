// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v3.9.1

syntax = "proto3";
package ga.api.pia;

import "pia/pia_.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/pia;pia";

service PiaLogic {
    option (ga.api.extension.logic_service_name) = "pia-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.pia_logic.PiaLogic/";
    rpc GetChannelPiaStatus(ga.pia.GetChannelPiaStatusReq) returns (ga.pia.GetChannelPiaStatusResp) {
        option (ga.api.extension.command) = {
             id: 33021;
        };
    }
    rpc SetPiaSwitch(ga.pia.SetPiaSwitchReq) returns (ga.pia.SetPiaSwitchResp) {
        option (ga.api.extension.command) = {
             id: 33022;
        };
    }
    rpc GetDrama(ga.pia.GetDramaReq) returns (ga.pia.GetDramaResp) {
        option (ga.api.extension.command) = {
             id: 33023;
        };
    }
    rpc GetSearchOptionGroup(ga.pia.GetSearchOptionGroupReq) returns (ga.pia.GetSearchOptionGroupResp) {
        option (ga.api.extension.command) = {
             id: 33024;
        };
    }
    rpc GetCurrentPiaInfo(ga.pia.GetCurrentPiaInfoReq) returns (ga.pia.GetCurrentPiaInfoResp) {
        option (ga.api.extension.command) = {
             id: 33026;
        };
    }
    rpc SetPiaPhase(ga.pia.SetPiaPhaseReq) returns (ga.pia.SetPiaPhaseResp) {
        option (ga.api.extension.command) = {
             id: 33027;
        };
    }
    rpc SetPiaProgress(ga.pia.SetPiaProgressReq) returns (ga.pia.SetPiaProgressResp) {
        option (ga.api.extension.command) = {
             id: 33028;
        };
    }
    rpc GetPlayingChannel(ga.pia.GetPlayingChannelReq) returns (ga.pia.GetPlayingChannelResp) {
        option (ga.api.extension.command) = {
             id: 33029;
        };
    }
    rpc GetQualityDramaList(ga.pia.GetQualityDramaListReq) returns (ga.pia.GetQualityDramaListResp) {
        option (ga.api.extension.command) = {
             id: 33030;
        };
    }
    rpc GetPracticeDramaList(ga.pia.GetPracticeDramaListReq) returns (ga.pia.GetPracticeDramaListResp) {
        option (ga.api.extension.command) = {
             id: 33031;
        };
    }
    rpc SelectDrama(ga.pia.SelectDramaReq) returns (ga.pia.SelectDramaResp) {
        option (ga.api.extension.command) = {
             id: 33032;
        };
    }
    rpc SetBgmInfo(ga.pia.SetBgmInfoReq) returns (ga.pia.SetBgmInfoResp) {
        option (ga.api.extension.command) = {
             id: 33033;
        };
    }
    rpc GetBgmInfo(ga.pia.GetBgmInfoReq) returns (ga.pia.GetBgmInfoResp) {
        option (ga.api.extension.command) = {
             id: 33034;
        };
    }
    rpc SetCompereMic(ga.pia.SetCompereMicReq) returns (ga.pia.SetCompereMicResp) {
        option (ga.api.extension.command) = {
             id: 33035;
        };
    }
    rpc OrderDrama(ga.pia.OrderDramaReq) returns (ga.pia.OrderDramaResp) {
        option (ga.api.extension.command) = {
             id: 33036;
        };
    }
    rpc GetOrderDramaList(ga.pia.GetOrderDramaListReq) returns (ga.pia.GetOrderDramaListResp) {
        option (ga.api.extension.command) = {
             id: 33037;
        };
    }
    rpc DeleteOrderDrama(ga.pia.DeleteOrderDramaReq) returns (ga.pia.DeleteOrderDramaResp) {
        option (ga.api.extension.command) = {
             id: 33038;
        };
    }
    rpc PiaSelectRole(ga.pia.PiaSelectRoleReq) returns (ga.pia.PiaSelectRoleResp) {
        option (ga.api.extension.command) = {
             id: 33039;
        };
    }
    rpc PiaCancelSelectRole(ga.pia.PiaCancelSelectRoleReq) returns (ga.pia.PiaCancelSelectRoleResp) {
        option (ga.api.extension.command) = {
             id: 33040;
        };
    }
    rpc SelectDramaV2(ga.pia.SelectDramaV2Req) returns (ga.pia.SelectDramaV2Resp) {
        option (ga.api.extension.command) = {
             id: 33041;
        };
    }
    rpc PiaOperateDrama(ga.pia.PiaOperateDramaReq) returns (ga.pia.PiaOperateDramaResp) {
        option (ga.api.extension.command) = {
             id: 33042;
        };
    }
    rpc PiaGetDramaStatus(ga.pia.PiaGetDramaStatusReq) returns (ga.pia.PiaGetDramaStatusResp) {
        option (ga.api.extension.command) = {
             id: 33043;
        };
    }
    rpc PiaOperateBgm(ga.pia.PiaOperateBgmReq) returns (ga.pia.PiaOperateBgmResp) {
        option (ga.api.extension.command) = {
             id: 33044;
        };
    }
    rpc GetSearchOptionGroupV2(ga.pia.GetSearchOptionGroupV2Req) returns (ga.pia.GetSearchOptionGroupV2Resp) {
        option (ga.api.extension.command) = {
             id: 33045;
        };
    }
    rpc GetDramaList(ga.pia.GetDramaListReq) returns (ga.pia.GetDramaListResp) {
        option (ga.api.extension.command) = {
             id: 33046;
        };
    }
    rpc GetDramaDetailById(ga.pia.GetDramaDetailByIdReq) returns (ga.pia.GetDramaDetailByIdResp) {
        option (ga.api.extension.command) = {
             id: 33047;
        };
    }
    rpc PiaGetDramaCopyId(ga.pia.PiaGetDramaCopyIdReq) returns (ga.pia.PiaGetDramaCopyIdResp) {
        option (ga.api.extension.command) = {
             id: 33048;
        };
    }
    rpc PiaCreateDramaCopy(ga.pia.PiaCreateDramaCopyReq) returns (ga.pia.PiaCreateDramaCopyResp) {
        option (ga.api.extension.command) = {
             id: 33049;
        };
    }
    rpc PiaOperateBgmVol(ga.pia.PiaOperateBgmVolReq) returns (ga.pia.PiaOperateBgmVolResp) {
        option (ga.api.extension.command) = {
             id: 33050;
        };
    }
    rpc DoUserDramaCollect(ga.pia.DoUserDramaCollectReq) returns (ga.pia.DoUserDramaCollectResp) {
        option (ga.api.extension.command) = {
             id: 33051;
        };
    }
    rpc GetUserDramaCollection(ga.pia.GetUserDramaCollectionReq) returns (ga.pia.GetUserDramaCollectionResp) {
        option (ga.api.extension.command) = {
             id: 33052;
        };
    }
    rpc GetPlayingChannelV2(ga.pia.GetPlayingChannelV2Req) returns (ga.pia.GetPlayingChannelV2Resp) {
        option (ga.api.extension.command) = {
             id: 33053;
        };
    }
    rpc PiaChangePlayType(ga.pia.PiaChangePlayTypeReq) returns (ga.pia.PiaChangePlayTypeResp) {
        option (ga.api.extension.command) = {
             id: 33054;
        };
    }
    rpc GetMyDramaPlayingRecord(ga.pia.GetMyDramaPlayingRecordReq) returns (ga.pia.GetMyDramaPlayingRecordResp) {
        option (ga.api.extension.command) = {
             id: 33055;
        };
    }
    rpc PiaBatchDeleteMyPlayingRecord(ga.pia.PiaBatchDeleteMyPlayingRecordReq) returns (ga.pia.PiaBatchDeleteMyPlayingRecordResp) {
        option (ga.api.extension.command) = {
             id: 33056;
        };
    }
    rpc PiaGetRankingList(ga.pia.PiaGetRankingListReq) returns (ga.pia.PiaGetRankingListResp) {
        option (ga.api.extension.command) = {
             id: 33057;
        };
    }
    rpc PiaGetMyPlayingRecordIdList(ga.pia.PiaGetMyPlayingRecordIdListReq) returns (ga.pia.PiaGetMyPlayingRecordIdListResp) {
        option (ga.api.extension.command) = {
             id: 33058;
        };
    }
    rpc GetMyDramaCopyList(ga.pia.GetMyDramaCopyListReq) returns (ga.pia.GetMyDramaCopyListResp) {
        option (ga.api.extension.command) = {
             id: 33059;
        };
    }
    rpc PiaCopyDramaList(ga.pia.PiaCopyDramaListReq) returns (ga.pia.PiaCopyDramaListResp) {
        option (ga.api.extension.command) = {
             id: 33060;
        };
    }
    rpc PiaConfirmCoverCopy(ga.pia.PiaConfirmCoverCopyDramaReq) returns (ga.pia.PiaConfirmCoverCopyDramaResp) {
        option (ga.api.extension.command) = {
             id: 33061;
        };
    }
    rpc SetDramaCopyStatus(ga.pia.SetDramaCopyStatusReq) returns (ga.pia.SetDramaCopyStatusResp) {
        option (ga.api.extension.command) = {
             id: 33062;
        };
    }
    rpc DeleteDramaCopy(ga.pia.DeleteDramaCopyReq) returns (ga.pia.DeleteDramaCopyResp) {
        option (ga.api.extension.command) = {
             id: 33063;
        };
    }
    rpc PiaCreateDramaCopyV2(ga.pia.PiaCreateDramaCopyV2Req) returns (ga.pia.PiaCreateDramaCopyV2Resp) {
        option (ga.api.extension.command) = {
             id: 33064;
        };
    }
    rpc PiaPerformDrama(ga.pia.PiaPerformDramaRequest) returns (ga.pia.PiaPerformDramaResponse) {
        option (ga.api.extension.command) = {
             id: 33065;
        };
    }
    rpc PiaSendDialogueIndex(ga.pia.PiaSendDialogueIndexRequest) returns (ga.pia.PiaSendDialogueIndexResponse) {
        option (ga.api.extension.command) = {
             id: 33066;
        };
    }
    rpc PiaFollowMic(ga.pia.PiaFollowMicRequest) returns (ga.pia.PiaFollowMicResponse) {
        option (ga.api.extension.command) = {
             id: 33067;
        };
    }
    rpc PiaReportDialogueIndex(ga.pia.PiaReportDialogueIndexRequest) returns (ga.pia.PiaReportDialogueIndexResponse) {
        option (ga.api.extension.command) = {
             id: 33068;
        };
    }
    rpc PiaGetPreviousDialogueIndex(ga.pia.PiaGetPreviousDialogueIndexRequest) returns (ga.pia.PiaGetPreviousDialogueIndexResponse) {
        option (ga.api.extension.command) = {
             id: 33069;
        };
    }
    rpc PiaUnFollowMic(ga.pia.PiaUnFollowMicRequest) returns (ga.pia.PiaUnFollowMicResponse) {
        option (ga.api.extension.command) = {
             id: 33070;
        };
    }
    rpc PiaGetMyFollowInfo(ga.pia.PiaGetMyFollowInfoRequest) returns (ga.pia.PiaGetMyFollowInfoResponse) {
        option (ga.api.extension.command) = {
             id: 33071;
        };
    }
    rpc PiaGetFollowedStatusOfMicList(ga.pia.PiaGetFollowedStatusOfMicListRequest) returns (ga.pia.PiaGetFollowedStatusOfMicListResponse) {
        option (ga.api.extension.command) = {
             id: 33072;
        };
    }
}


