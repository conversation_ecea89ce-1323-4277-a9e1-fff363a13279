// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v3.9.1

syntax = "proto3";
package ga.api.chat_card;



import "chatcardlogic/chat-card-logic_.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/chat_card;chat_card";

service ChatCardLogic {
    option (ga.api.extension.logic_service_name) = "chat-card-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.ChatCardLogic/";
    rpc GetChatCardConfig(ga.chatcardlogic.GetChatCardConfigReq) returns (ga.chatcardlogic.GetChatCardConfigResp) {
        option (ga.api.extension.command) = {
             id: 30500
        };
    }
    rpc GetChatCardStatus(ga.chatcardlogic.GetChatCardStatusReq) returns (ga.chatcardlogic.GetChatCardStatusResp) {
        option (ga.api.extension.command) = {
             id: 30501
        };
    }
    rpc OpenChatCard(ga.chatcardlogic.OpenChatCardReq) returns (ga.chatcardlogic.OpenChatCardResp) {
        option (ga.api.extension.command) = {
             id: 30502
        };
    }
    rpc CloseChatCard(ga.chatcardlogic.CloseChatCardReq) returns (ga.chatcardlogic.CloseChatCardResp) {
        option (ga.api.extension.command) = {
             id: 30503
        };
    }
    rpc GetChatCardList(ga.chatcardlogic.GetChatCardListReq) returns (ga.chatcardlogic.GetChatCardListResp) {
        option (ga.api.extension.command) = {
             id: 30504
        };
    }
    rpc SayHi(ga.chatcardlogic.SayHiReq) returns (ga.chatcardlogic.SayHiResp) {
        option (ga.api.extension.command) = {
             id: 30505
        };
    }
    rpc Reply(ga.chatcardlogic.ReplyReq) returns (ga.chatcardlogic.ReplyResp) {
        option (ga.api.extension.command) = {
             id: 30506
        };
    }
    rpc LikeChatCard(ga.chatcardlogic.LikeChatCardReq) returns (ga.chatcardlogic.LikeChatCardResp) {
        option (ga.api.extension.command) = {
             id: 30507
        };
    }
    rpc MarkChatCardMsgRead(ga.chatcardlogic.MarkChatCardMsgReadReq) returns (ga.chatcardlogic.MarkChatCardMsgReadResp) {
        option (ga.api.extension.command) = {
             id: 30508
        };
    }
    rpc GetChatCardMsgUnreadCount(ga.chatcardlogic.GetChatCardMsgUnreadCountReq) returns (ga.chatcardlogic.GetChatCardMsgUnreadCountResp) {
        option (ga.api.extension.command) = {
             id: 30509
        };
    }
    rpc GetChatCardMsgList(ga.chatcardlogic.GetChatCardMsgListReq) returns (ga.chatcardlogic.GetChatCardMsgListResp) {
        option (ga.api.extension.command) = {
             id: 30510
        };
    }
    rpc ReportInterestedChatCardTags(ga.chatcardlogic.ReportInterestedChatCardTagsReq) returns (ga.chatcardlogic.ReportInterestedChatCardTagsResp) {
        option (ga.api.extension.command) = {
             id: 30511
        };
    }
    rpc SayHiV2(ga.chatcardlogic.SayHiV2Req) returns (ga.chatcardlogic.SayHiV2Resp) {
        option (ga.api.extension.command) = {
             id: 30512
        };
    }
}

