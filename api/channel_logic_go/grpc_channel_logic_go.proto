// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v3.9.1

syntax = "proto3";
package ga.api.channel_logic_go;

import "channel_logic_go/channel_logic_go.proto";
import "api/extension/extension.proto";
import "channel/channel_.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/channel_logic_go;channel_logic_go";

service ChannelLogicGo {
    option (ga.api.extension.logic_service_name) = "channel-logic-go";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "";
    rpc GetChannelEnterExtInfo(ga.channel_logic_go.GetChannelEnterExtInfoRequest) returns (ga.channel_logic_go.GetChannelEnterExtInfoResponse) {
        option (ga.api.extension.command) = {
             id: 50859;
        };
    }
    rpc ChannelModifyName(ga.channel.ChannelModifyNameReq) returns (ga.channel.ChannelModifyNameResp) {
        option (ga.api.extension.command) = {
            id: 422;
        };
    }

    rpc ChannelMemberMute(ga.channel.ChannelMemberMuteReq) returns (ga.channel.ChannelMemberMuteResp) {
        option (ga.api.extension.command) = {
            id: 428;
        };
    }
    rpc ChannelMemberUnmute(ga.channel.ChannelMemberUnmuteReq) returns (ga.channel.ChannelMemberUnmuteResp) {
        option (ga.api.extension.command) = {
            id: 429;
        };
    }

    rpc ChannelGetMutedMemberList(ga.channel.ChannelGetMutedMemberListReq) returns (ga.channel.ChannelGetMutedMemberListResp) {
        option (ga.api.extension.command) = {
            id: 427;
        };
    }
    rpc GetUserAdminChannelList(ga.channel.GetUserAdminChannelListReq) returns (ga.channel.GetUserAdminChannelListResp) {
        option (ga.api.extension.command) = {
            id: 447;
        };
    }
    rpc BatchGetChannelList(ga.channel.BatchGetChannelListReq) returns (ga.channel.BatchGetChannelListResp) {
        option (ga.api.extension.command) = {
            id: 449;
        };
    }
    rpc ChannelChannelSetAdmin(ga.channel.OperChannelAdminReq) returns (ga.channel.OperChannelAdminResp) {
        option (ga.api.extension.command) = {
            id: 2054;
        };
    }
    rpc GetChannelList(ga.channel.GetChannelListReq) returns (ga.channel.GetChannelListResp) {
        option (ga.api.extension.command) = {
            id: 425;
        };
    }
    rpc ChannelModifyExtend(ga.channel.ModifyChannelExtentReq) returns (ga.channel.ModifyChannelExtentResp) {
        option (ga.api.extension.command) = {
            id: 2070;
            deprecated: true;
        };
    }
}


