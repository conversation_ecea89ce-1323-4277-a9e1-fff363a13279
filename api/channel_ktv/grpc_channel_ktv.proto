// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v3.9.1

syntax = "proto3";
package ga.api.channel_ktv;

import "channel_ktv/channel-ktv-logic_.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/channel_ktv;channel_ktv";

service ChannelKTVLogic {
    option (ga.api.extension.logic_service_name) = "channel-ktv-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.ChannelKTVLogic/";
    rpc GetChannelKTVSongList(ga.channel_ktv.GetChannelKTVSongListReq) returns (ga.channel_ktv.GetChannelKTVSongListResp) {
        option (ga.api.extension.command) = {
             id: 30971;
        };
    }
    rpc GetChannelKTVHistoryList(ga.channel_ktv.GetChannelKTVHistoryListReq) returns (ga.channel_ktv.GetChannelKTVHistoryListResp) {
        option (ga.api.extension.command) = {
             id: 30972;
        };
    }
    rpc GetChannelKTVRecommendList(ga.channel_ktv.GetChannelKTVRecommendListReq) returns (ga.channel_ktv.GetChannelKTVRecommendListResp) {
        option (ga.api.extension.command) = {
             id: 30973;
        };
    }
    rpc GetChannelKTVPlayList(ga.channel_ktv.GetChannelKTVPlayListReq) returns (ga.channel_ktv.GetChannelKTVPlayListResp) {
        option (ga.api.extension.command) = {
             id: 30974;
        };
    }
    rpc GetChannelKTVGuessLikeSongList(ga.channel_ktv.GetChannelKTVGuessLikeSongListReq) returns (ga.channel_ktv.GetChannelKTVGuessLikeSongListResp) {
        option (ga.api.extension.command) = {
             id: 30975;
        };
    }
    rpc AddChannelKTVSongToPlayList(ga.channel_ktv.AddChannelKTVSongToPlayListReq) returns (ga.channel_ktv.AddChannelKTVSongToPlayListResp) {
        option (ga.api.extension.command) = {
             id: 30976;
        };
    }
    rpc MoveUpChannelKTVSong(ga.channel_ktv.MoveUpChannelKTVSongReq) returns (ga.channel_ktv.MoveUpChannelKTVSongResp) {
        option (ga.api.extension.command) = {
             id: 30977;
        };
    }
    rpc RemoveChannelKTVSong(ga.channel_ktv.RemoveChannelKTVSongReq) returns (ga.channel_ktv.RemoveChannelKTVSongResp) {
        option (ga.api.extension.command) = {
             id: 30978;
        };
    }
    rpc BeginChannelKTVSing(ga.channel_ktv.BeginChannelKTVSingReq) returns (ga.channel_ktv.BeginChannelKTVSingResp) {
        option (ga.api.extension.command) = {
             id: 30979;
        };
    }
    rpc GetChannelKTVInfo(ga.channel_ktv.GetChannelKTVInfoReq) returns (ga.channel_ktv.GetChannelKTVInfoResp) {
        option (ga.api.extension.command) = {
             id: 30980;
        };
    }
    rpc JoinChannelKTVSing(ga.channel_ktv.JoinChannelKTVSingReq) returns (ga.channel_ktv.JoinChannelKTVSingResp) {
        option (ga.api.extension.command) = {
             id: 30981;
        };
    }
    rpc QuitChannelKTVSing(ga.channel_ktv.QuitChannelKTVSingReq) returns (ga.channel_ktv.QuitChannelKTVSingResp) {
        option (ga.api.extension.command) = {
             id: 30982;
        };
    }
    rpc UpdateChannelKTVScore(ga.channel_ktv.UpdateChannelKTVScoreReq) returns (ga.channel_ktv.UpdateChannelKTVScoreResp) {
        option (ga.api.extension.command) = {
             id: 30983;
        };
    }
    rpc SwitchChannelKTVBG(ga.channel_ktv.SwitchChannelKTVBGReq) returns (ga.channel_ktv.SwitchChannelKTVBGResp) {
        option (ga.api.extension.command) = {
             id: 30984;
        };
    }
    rpc ChannelKTVHandClap(ga.channel_ktv.ChannelKTVHandClapReq) returns (ga.channel_ktv.ChannelKTVHandClapResp) {
        option (ga.api.extension.command) = {
             id: 30985;
        };
    }
    rpc EndChannelKTVSing(ga.channel_ktv.EndChannelKTVSingReq) returns (ga.channel_ktv.EndChannelKTVSingResp) {
        option (ga.api.extension.command) = {
             id: 30986;
        };
    }
    rpc KickOutKTVMember(ga.channel_ktv.KickOutKTVMemberReq) returns (ga.channel_ktv.KickOutKTVMemberResp) {
        option (ga.api.extension.command) = {
             id: 30989;
        };
    }
    rpc CutChannelKTVSong(ga.channel_ktv.CutChannelKTVSongReq) returns (ga.channel_ktv.CutChannelKTVSongResp) {
        option (ga.api.extension.command) = {
             id: 30990;
        };
    }
    rpc GetChannelKTVBG(ga.channel_ktv.GetChannelKTVBGReq) returns (ga.channel_ktv.GetChannelKTVBGResp) {
        option (ga.api.extension.command) = {
             id: 30992;
        };
    }
    rpc ListChannelKTVSongListType(ga.channel_ktv.ListChannelKTVSongListTypeReq) returns (ga.channel_ktv.ListChannelKTVSongListTypeResp) {
        option (ga.api.extension.command) = {
             id: 30991;
        };
    }
    rpc GetSpecialEventCopyWriting(ga.channel_ktv.GetSpecialEventCopyWritingReq) returns (ga.channel_ktv.GetSpecialEventCopyWritingResp) {
        option (ga.api.extension.command) = {
             id: 30993;
        };
    }
    rpc GetChannelKTVSongListById(ga.channel_ktv.GetChannelKTVSongListByIdReq) returns (ga.channel_ktv.GetChannelKTVSongListByIdResp) {
        option (ga.api.extension.command) = {
             id: 30994;
        };
    }
    rpc ChannelKTVBurstLight(ga.channel_ktv.ChannelKTVBurstLightReq) returns (ga.channel_ktv.ChannelKTVBurstLightResp) {
        option (ga.api.extension.command) = {
             id: 30995;
        };
    }
    rpc ChannelKTVHighFive(ga.channel_ktv.ChannelKTVHighFiveReq) returns (ga.channel_ktv.ChannelKTVHighFiveResp) {
        option (ga.api.extension.command) = {
             id: 30996;
        };
    }
    rpc ChannelKTVGuideClimaxSongList(ga.channel_ktv.ChannelKTVGuideClimaxSongListReq) returns (ga.channel_ktv.ChannelKTVGuideClimaxSongListResp) {
        option (ga.api.extension.command) = {
             id: 30997;
        };
    }
    rpc ChannelKTVBatchSongClimaxInfos(ga.channel_ktv.ChannelKTVBatchSongClimaxInfosReq) returns (ga.channel_ktv.ChannelKTVBatchSongClimaxInfosResp) {
        option (ga.api.extension.command) = {
             id: 30998;
        };
    }
    rpc ChannelKTVSkipTheIntro(ga.channel_ktv.ChannelKTVSkipTheIntroReq) returns (ga.channel_ktv.ChannelKTVSkipTheIntroResp) {
        option (ga.api.extension.command) = {
             id: 30999;
        };
    }
    rpc ChannelKTVFollowTrigger(ga.channel_ktv.ChannelKTVFollowTriggerReq) returns (ga.channel_ktv.ChannelKTVFollowTriggerResp) {
        option (ga.api.extension.command) = {
             id: 31000;
        };
    }
    rpc ChannelKTVQuerySong(ga.channel_ktv.ChannelKTVQuerySongReq) returns (ga.channel_ktv.ChannelKTVQuerySongResp) {
        option (ga.api.extension.command) = {
             id: 31001;
        };
    }
}


