// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v3.9.1

syntax = "proto3";
package ga.api.cat_canteen;

import "cat_canteen_logic/cat_canteen_logic.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/cat_canteen;cat_canteen";

service CatCanteenLogic {
    option (ga.api.extension.logic_service_name) = "cat-canteen-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.CatCanteenLogic/";
    rpc GetChanceGameAccessNotifyInfo(ga.cat_canteen_logic.GetChanceGameAccessNotifyInfoRequest) returns (ga.cat_canteen_logic.GetChanceGameAccessNotifyInfoResponse) {
        option (ga.api.extension.command) = {
             id: 36571;
        };
    }
    rpc LotteryDraw(ga.cat_canteen_logic.LotteryDrawRequest) returns (ga.cat_canteen_logic.LotteryDrawResponse) {
        option (ga.api.extension.command) = {
             id: 36572;
        };
    }
    rpc BuyChance(ga.cat_canteen_logic.BuyChanceRequest) returns (ga.cat_canteen_logic.BuyChanceResponse) {
        option (ga.api.extension.command) = {
             id: 36573;
        };
    }
    rpc GetGameInfo(ga.cat_canteen_logic.GetGameInfoRequest) returns (ga.cat_canteen_logic.GetGameInfoResponse) {
        option (ga.api.extension.command) = {
             id: 36574;
        };
    }
    rpc GetWinningRecords(ga.cat_canteen_logic.GetWinningRecordsRequest) returns (ga.cat_canteen_logic.GetWinningRecordsResponse) {
        option (ga.api.extension.command) = {
             id: 36575;
        };
    }
    rpc GetRecentWinningRecords(ga.cat_canteen_logic.GetRecentWinningRecordsRequest) returns (ga.cat_canteen_logic.GetRecentWinningRecordsResponse) {
        option (ga.api.extension.command) = {
             id: 36576;
        };
    }
    rpc GetUserPlayFile(ga.cat_canteen_logic.GetUserPlayFileRequest) returns (ga.cat_canteen_logic.GetUserPlayFileResponse) {
        option (ga.api.extension.command) = {
             id: 36577;
        };
    }
    rpc GetUserCatPropList(ga.cat_canteen_logic.GetUserCatPropListRequest) returns (ga.cat_canteen_logic.GetUserCatPropListResponse) {
        option (ga.api.extension.command) = {
             id: 36578;
        };
    }
    rpc GetUserExpireCatPropNotify(ga.cat_canteen_logic.GetUserExpireCatPropNotifyRequest) returns (ga.cat_canteen_logic.GetUserExpireCatPropNotifyResponse) {
        option (ga.api.extension.command) = {
             id: 36579;
        };
    }
    rpc GetCatCanteenResource(ga.cat_canteen_logic.GetCatCanteenResourceRequest) returns (ga.cat_canteen_logic.GetCatCanteenResourceResponse) {
        option (ga.api.extension.command) = {
             id: 36580;
        };
    }
}


