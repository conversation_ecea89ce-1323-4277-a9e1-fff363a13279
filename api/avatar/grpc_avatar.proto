// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v3.9.1

syntax = "proto3";
package ga.api.avatar;

import "avatar_logic/avatar-logic_.proto";
import "face/face.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/avatar;avatar";

service AvatarLogic {
    option (ga.api.extension.logic_service_name) = "avatar-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.AvatarLogic/";
    rpc UploadAvatar(ga.avatar_logic.Avatar_UploadAvatarReq) returns (ga.avatar_logic.Avatar_UploadAvatarResp) {
        option (ga.api.extension.command) = {
             id: 20020;
        };
    }
    rpc BatchGetSmallFaceUrl(ga.face.BatchGetSmallFaceUrlReq) returns (ga.face.BatchGetSmallFaceUrlResp) {
        option (ga.api.extension.command) = {
             id: 600;
        };
    }
    rpc GetBigFace(ga.face.GetBigFaceReq) returns (ga.face.GetBigFaceResp) {
        option (ga.api.extension.command) = {
             id: 22;
        };
    }
    rpc GetSmallFace(ga.face.GetSmallFaceReq) returns (ga.face.GetSmallFaceResp) {
        option (ga.api.extension.command) = {
             id: 21;
        };
    }
    rpc UploadFace(ga.face.UserUploadFaceReq) returns (ga.face.UserUploadFaceResp) {
        option (ga.api.extension.command) = {
             id: 20;
        };
    }
}


