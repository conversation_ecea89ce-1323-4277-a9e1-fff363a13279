syntax = "proto3";
package ga.api.channel_guild;



import "api/extension/extension.proto";
import "channel/channelguild_.proto";

//server_name=channelguildlogic
//language=cpp

option java_package = "com.quwan.tt.proto.api";
option go_package = "golang.52tt.com/protocol/app/api/channel_guild;channel_guild";
option objc_class_prefix = "RPC";

service ChannelGuildLogic {
    rpc ChannelGuildGetListByType(ga.channel.ChannelGuildGetListByTypeReq) returns (ga.channel.ChannelGuildGetListByTypeResp) {
        option (ga.api.extension.command) = {
            id: 2301
        };
    }

    rpc ChannelGuildGetListSummaryService(ga.channel.ChannelGuildGetListSummaryReq) returns (ga.channel.ChannelGuildGetListSummaryResp) {
        option (ga.api.extension.command) = {
            id: 2300
        };
    }

    rpc ChannelGuildGetTotalMemberCountService(ga.channel.ChannelGuildGetTotalMemberCountReq) returns (ga.channel.ChannelGuildGetTotalMemberCountResp) {
        option (ga.api.extension.command) = {
            id: 2302
        };
    }

    option (ga.api.extension.logic_service_name) = "channelguildlogic";
    option (ga.api.extension.logic_service_language) = "cpp";
}
