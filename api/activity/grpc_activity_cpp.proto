syntax = "proto3";
package ga.api.activity;

import "activity/activity_.proto";
import "api/extension/extension.proto";
import "push/push_.proto";

//server_name=activitylogic
//language=cpp

option java_package = "com.quwan.tt.proto.api";
option go_package = "golang.52tt.com/protocol/app/api/activity;activity";
option objc_class_prefix = "RPC";

service ActivityLogic {
    rpc AwardUserTTGiftPkg(ga.activity.AwardUserTTGiftPkgReq) returns (ga.activity.AwardUserTTGiftPkgResp) {
        option (ga.api.extension.command) = {
            id: 1220
        };
    }

    rpc CheckFirstRechargeActEntry(ga.activity.CheckUserFirstRechargeActEntryReq) returns (ga.activity.CheckUserFirstRechargeActEntryResp) {
        option (ga.api.extension.command) = {
            id: 1221
        };
    }

    rpc GetAnchorRankingListConfig(ga.activity.GetAnchorRankingListConfigReq) returns (ga.activity.GetAnchorRankingListConfigResp) {
        option (ga.api.extension.command) = {
            id: 2708
        };
    }

    rpc GetCommonWebActiveBreakingInfo(ga.push.GetCommonWebActiveBreakingInfoReq) returns (ga.push.GetCommonWebActiveBreakingInfoResp) {
        option (ga.api.extension.command) = {
            id: 2246
        };
    }

    rpc GetCommonRankingListConfig(ga.activity.GetCommonRankingListConfigReq) returns (ga.activity.GetCommonRankingListConfigResp) {
        option (ga.api.extension.command) = {
            id: 2707
        };
    }

    rpc GetGangupTabAdvConf(ga.activity.GetGangupTabAdvInfoRep) returns (ga.activity.GetGangupTabAdvInfoResp) {
        option (ga.api.extension.command) = {
            id: 30080
        };
    }

    rpc GetMutiLocationActEntry(ga.activity.GetMutiLocationActEntryReq) returns (ga.activity.GetMutiLocationActEntryResp) {
        option (ga.api.extension.command) = {
            id: 1222
        };
    }

    rpc GetMyFirstsVoucher(ga.activity.GetMyFirstVoucherReq) returns (ga.activity.GetMyFirstVoucherResp) {
        option (ga.api.extension.command) = {
            id: 400
        };
    }

    rpc GetYearActRankingListEntry(ga.activity.Get2019YearActRankingListEntryReq) returns (ga.activity.Get2019YearActRankingListEntryResp) {
        option (ga.api.extension.command) = {
            id: 2706
        };
    }

    option (ga.api.extension.logic_service_name) = "activitylogic";
    option (ga.api.extension.logic_service_language) = "cpp";
}
