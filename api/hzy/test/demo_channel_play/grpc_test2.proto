// auto gen by pbtools v1

syntax = "proto3";

package ga.api.hzy.test.demo_channel_play;


import "api/extension/extension.proto";
import "hzy/test/v1/foo.proto";

option go_package = "golang.52tt.com/protocol/app/api/hzy/test/v1;v1";
option java_package = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";

service DemoChannelPlayLogic {
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_name) = "demo-logic";
    
    rpc Foo(ga.hzy.test.v1.FooRequest) returns (ga.hzy.test.v1.FooResponse) {
        option (ga.api.extension.command) = {
            id: 1234567;
        };
    }
}
