// auto gen by pbtools v1

syntax = "proto3";

package ga.api.newbie_page;

import "newbie_page_logic/newbie_page_logic.proto";
import "api/extension/extension.proto";

option go_package = "golang.52tt.com/protocol/app/api/newbie_page;newbie_page";
option java_package = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";

service NewbiePageLogic {
  option (ga.api.extension.logic_service_language) = "go";
  option (ga.api.extension.logic_service_name) = "newbie-page-logic";

  /* 获取新用户承接页配置 */
  rpc GetNewbiePageConfig (ga.newbie_page_logic.GetNewbiePageConfigRequest) returns (ga.newbie_page_logic.GetNewbiePageConfigResponse) {
    option (ga.api.extension.command) = {
      id: 3310
    };
  }

  /* 用户选择兴趣标签 */
  rpc SetUserNewbiePageTag (ga.newbie_page_logic.SetUserNewbiePageTagRequest) returns (ga.newbie_page_logic.SetUserNewbiePageTagResponse) {
    option (ga.api.extension.command) = {
      id: 3311
    };
  }
  /*设置用户标签页列表*/
  rpc SetUserPageTagList (ga.newbie_page_logic.SetUserPageTagListReq) returns (ga.newbie_page_logic.SetUserPageTagListResp) {
    option (ga.api.extension.command) = {
      id: 3312
    };
  }

  /*获取用户标签列表*/
  rpc GetUserPageTagList (ga.newbie_page_logic.GetUserPageTagListRequest) returns (ga.newbie_page_logic.GetUserPageTagListResponse) {
    option (ga.api.extension.command) = {
      id: 3313
    };
  }

}
