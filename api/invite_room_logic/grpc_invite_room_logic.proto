syntax = "proto3";
package ga.api.invite_room_logic;

import "invite_room_logic/invite_room_logic.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/invite_room_logic;invite_room_logic";

service InviteRoomLogic {
    option (ga.api.extension.logic_service_name) = "invite-room-logic";
    option (ga.api.extension.logic_service_language) = "go";

    // 获取开黑邀请进房入口信息
    rpc GetInviteEntranceInfo(ga.invite_room_logic.GetInviteEntranceInfoReq) returns (ga.invite_room_logic.GetInviteEntranceInfoResp) {
        option (ga.api.extension.command) = {
             id: 6001;
        };
    }

    // 获取邀请进房列表
    rpc GetInviteList(ga.invite_room_logic.GetInviteListReq) returns (ga.invite_room_logic.GetInviteListResp) {
        option (ga.api.extension.command) = {
             id: 6002;
        };
    }

    // 获取受邀进房记录
    rpc GetBeInvitedRecord(ga.invite_room_logic.GetBeInvitedRecordReq) returns (ga.invite_room_logic.GetBeInvitedRecordResp) {
        option (ga.api.extension.command) = {
            id: 6003;
        };
    }

    // 获取邀请列表筛选标签
    rpc GetFilterLabels(ga.invite_room_logic.GetFilterLabelsRequest) returns (ga.invite_room_logic.GetFilterLabelsResponse) {
        option (ga.api.extension.command) = {
            id: 6004;
        };
    }

    // 用户发出邀请
    rpc UserSendInvite(ga.invite_room_logic.UserSendInviteReq) returns (ga.invite_room_logic.UserSendInviteResp) {
        option (ga.api.extension.command) = {
            id: 6005;
        };
    }

    // 用户拒绝邀请
    rpc UserRefuseInvite(ga.invite_room_logic.UserRefuseInviteReq) returns (ga.invite_room_logic.UserRefuseInviteResp) {
        option (ga.api.extension.command) = {
            id: 6006;
        };
    }

    // 开启今日免打扰
    rpc OpenTodayDontDisturb(ga.invite_room_logic.OpenTodayDontDisturbReq) returns (ga.invite_room_logic.OpenTodayDontDisturbResp) {
        option (ga.api.extension.command) = {
            id: 6007;
        };
    }

    // 屏蔽该用户
    rpc InviteBlockUser(ga.invite_room_logic.InviteBlockUserReq) returns (ga.invite_room_logic.InviteBlockUserResp) {
        option (ga.api.extension.command) = {
            id: 6008;
        };
    }

    // 用户行为状态数据上报
    rpc UserBehaviorStatusReport(ga.invite_room_logic.UserBehaviorStatusReportReq) returns (ga.invite_room_logic.UserBehaviorStatusReportResp) {
        option (ga.api.extension.command) = {
            id: 6009;
        };
    }
}