// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v3.9.1

syntax = "proto3";
package ga.api.udesk_api;



import "udeskapilogic/udesk-api-logic_.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/udesk_api;udesk_api";

service UdeskApiLogic {
    option (ga.api.extension.logic_service_name) = "udesk-api-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.UdeskApiLogic/";
    rpc GetUdeskUnReadMsg(ga.udeskapilogic.GetUdeskUnReadMsgReq) returns (ga.udeskapilogic.GetUdeskUnReadMsgResp) {
        option (ga.api.extension.command) = {
             id: 95000
        };
    }
    rpc CheckVipKefuAccess(ga.udeskapilogic.CheckVipKefuAccessReq) returns (ga.udeskapilogic.CheckVipKefuAccessResp) {
        option (ga.api.extension.command) = {
             id: 95001
        };
    }
    rpc AckVipKefuAccess(ga.udeskapilogic.AckVipKefuAccessReq) returns (ga.udeskapilogic.AckVipKefuAccessResp) {
        option (ga.api.extension.command) = {
             id: 95002
        };
    }
    rpc EnterVipKefu(ga.udeskapilogic.EnterVipKefuReq) returns (ga.udeskapilogic.EnterVipKefuResp) {
        option (ga.api.extension.command) = {
             id: 95003
        };
    }
}

