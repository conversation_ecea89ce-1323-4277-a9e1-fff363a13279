syntax = "proto3";
package ga.api.channel_music;

import "api/extension/extension.proto";
import "channel/channel_.proto";

option go_package = "golang.52tt.com/protocol/app/api/channel_music;channel_music";
option java_package = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";

service ChannelMusicLogicGo {
  option (ga.api.extension.logic_service_language) = "go";
  option (ga.api.extension.logic_service_name) = "channel-music-logic-go";

  rpc ChannelMusicHeartBeat(ga.channel.ChanneMusicV2HeartBeatReq) returns (ga.channel.ChanneMusicV2HeartBeatResp) {
    option (ga.api.extension.command) = {
      id: 2058
    };
  }
}