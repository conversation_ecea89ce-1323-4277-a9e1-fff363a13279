syntax = "proto3";
package ga.api.magic_expression;

import "magic_expression_logic/magic_expression_logic.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/magic_expression;magic_expression";

service MagicExpressionLogic {
  option (ga.api.extension.logic_service_name) = "magic-expression-logic";
  option (ga.api.extension.logic_service_language) = "go";

  rpc GetMagicExpressionInfoList(ga.magic_expression_logic.GetMagicExpressionInfoListRequest) returns (ga.magic_expression_logic.GetMagicExpressionInfoListResponse) {
    option (ga.api.extension.command) = {
      id: 51051;
    };
  }
  rpc ChannelMagicExpressionPush(ga.magic_expression_logic.ChannelMagicExpressionPushRequest) returns (ga.magic_expression_logic.ChannelMagicExpressionPushResponse) {
    option (ga.api.extension.command) = {
      id: 51052;
    };
  }
}



