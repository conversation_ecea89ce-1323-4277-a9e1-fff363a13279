// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v3.9.1

syntax = "proto3";
package ga.api.push;

import "auth/auth.proto";
import "push_logic/push-logic_.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/push;push";

service PushLogic {
    option (ga.api.extension.logic_service_name) = "push-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.push.PushLogic/";
    rpc RegisterPushDeviceToken(ga.auth.RegisterApnsDeviceTokenReq) returns (ga.auth.RegisterApnsDeviceTokenResp) {
        option (ga.api.extension.command) = {
             id: 401
        };
    }
    rpc GetPushToken(ga.push_logic.GetPushTokenReq) returns (ga.push_logic.GetPushTokenResp) {
        option (ga.api.extension.command) = {
             id: 900
        };
    }
}


