// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v3.9.1

syntax = "proto3";
package ga.api.channel_convene;

import "channel_convene/channel_convene.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/channel_convene;channel_convene";

service ChannelConveneLogicGo {
    option (ga.api.extension.logic_service_name) = "channel-convene-logic-go";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "";
    rpc BatchRemoveChannelCollect(ga.channel_convene.BatchRemoveChannelCollectRequest) returns (ga.channel_convene.BatchRemoveChannelCollectResponse) {
        option (ga.api.extension.command) = {
             id: 1158;
        };
    }
}


