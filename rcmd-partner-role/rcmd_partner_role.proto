syntax = "proto3";

package rcmd.rcmd_partner_role;

option go_package = "golang.52tt.com/protocol/services/rcmd/rcmd_partner_role";


service RCMDPartnerRole {
    rpc GetRCMDRoles(GetRCMDRolesReq) returns(GetRCMDRolesResp); // 获取推荐角色
    rpc ResetFilter(ResetFilterReq) returns(ResetFilterResp); // 主动重置过滤器
    rpc TransRoleIdEvent(TransRoleIdReq) returns (TransRoleIdResp);
    rpc ResetUserRoleFilter(ResetUserRoleFilterReq) returns (ResetUserRoleFilterResp);  // 记录用户访问role的曝光数据信息// 将uid关联的曝光的roleid信息传递给下游
}

message GetRCMDRolesReq {
    uint32 uid = 1;
    string category_id = 2;
    enum FetchMode{
        Invalid = 0;
        NextPage= 1; // 请求下一页
        Refresh = 2; // 刷新
    }
    FetchMode fetch_mode = 3;
    repeated uint32 browse_list = 4; //请求列表曝光信息, 用于支持曝光过滤功能
    repeated CategoryProperty category_property_list = 5; // 筛选条件分类列表
}

message RCMDEntity {
    enum EntityType {
        Invalid = 0;
        Role = 1;
        Group = 2;
    }
    uint32 id = 1;
    EntityType type = 2;
}
message GetRCMDRolesResp {
    repeated uint32 role_ids = 1;
    bool bottom_reached = 2; // 是否已经到底了，即下一页将没有数据
    string footprint = 3;// 推荐trace id
    repeated RCMDEntity entities = 4;  // 推荐的实体列表, 目前包含角色和群组
}

message ResetFilterReq {
    uint32 uid = 1;
    string category_id = 2; // 为空时清空所有分类的过滤器
}

message ResetFilterResp {
    uint32 code = 1;
    string msg = 2;
}

message CategoryProperty {
    string id = 1;
    uint32 type = 2; // CategoryProperty的类型
    repeated Label label_list = 3;
}

message Label {
    string id = 1;
    string name = 2;
}

message TransRoleIdReq{
    uint32 uid = 1;
    repeated uint32 role_ids = 2;
}

// 定义返回 UID 的消息
message TransRoleIdResp {
    int64 uid = 1; // UID 返回值
}


message ResetUserRoleFilterReq {
    uint32 uid = 1;
}

message ResetUserRoleFilterResp {
    uint32 code = 1;
    string msg = 2;
}
