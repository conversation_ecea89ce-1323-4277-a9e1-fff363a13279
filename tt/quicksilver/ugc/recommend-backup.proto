syntax = "proto3";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package ugc.recommendbackup;

option go_package = "golang.52tt.com/protocol/services/ugc/recommend-backup";

import "tt/quicksilver/ugc/content.proto";

service RecomomendBackUp {
    rpc GetRecommendBackup (GetRecommendBackupReq) returns (GetRecommendBackupRsp) {}
}

message GetRecommendBackupReq {
    uint32 limit = 1;
    ugc.content.ContentType content_type = 2;
}

message GetRecommendBackupRsp {
    repeated string post_ids = 1;
}
