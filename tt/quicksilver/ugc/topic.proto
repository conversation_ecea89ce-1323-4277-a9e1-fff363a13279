syntax = "proto3";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package ugc.topic;

option go_package = "golang.52tt.com/protocol/services/ugc/topic";

service Topic {

  rpc CreateTopic (CreateTopicReq) returns (CreateTopicResp) {
  }

  //删除
  rpc DeleteTopic (DeleteTopicReq) returns (DeleteTopicResp) {
  }
  rpc BatDeleteTopic (BatDeleteTopicReq) returns (BatDeleteTopicResp) {
  }

  //上下线主题
  rpc EnableTopic (EnableTopicReq) returns (EnableTopicResp) {
  }

  //更新主题资料
  rpc UpdateTopicInfo (UpdateTopicInfoReq) returns (UpdateTopicInfoResp) {
  }

  /*从自定义修改为官方话题*/
  rpc DIY2OfficialTopic (DIY2OfficialTopicReq) returns (DIY2OfficialTopicResp) {
  }

  /*批量修改提权*/
  rpc BatUpdateWeight (BatUpdateWeightReq) returns (BatUpdateWeightResp) {
  }
  /*批量获取所有提权话题*/
  rpc BatGetAllWeightTopic (BatGetAllWeightTopicReq) returns (BatGetAllWeightTopicResp) {
  }
  /*推荐流插入话题设置*/
  rpc UpdatePutTopicInRecommendConf (UpdatePutTopicInRecommendConfReq) returns (UpdatePutTopicInRecommendConfResp) {
  }

  /*删除推荐流插入话题配置*/
  rpc DelPutTopicInRecommendConf (DelPutTopicInRecommendConfReq) returns (DelPutTopicInRecommendConfResp) {
  }
  /*获取推荐流插入话题配置*/
  rpc GetPutTopicInRecommendConf (GetPutTopicInRecommendConfReq) returns (GetPutTopicInRecommendConfResp) {
  }
  /*更新推荐流插入其他分类话题配置*/
  rpc UpdateCatalogTopicInRcmdFeed (UpdateCatalogTopicInRcmdFeedReq) returns (UpdateCatalogTopicInRcmdFeedResp) {
  }
  /*删除推荐流插入其他分类话题配置*/
  rpc DelCatalogTopicInRcmdFeed (DelCatalogTopicInRcmdFeedReq) returns (DelCatalogTopicInRcmdFeedResp) {
  }
  /*获取推荐流插入其他分类话题配置*/
  rpc GetCatalogTopicInRcmdFeed (GetCatalogTopicInRcmdFeedReq) returns (GetCatalogTopicInRcmdFeedResp) {
  }
  /*获取24小时 3天 7天内 动态量最多的话题*/
  rpc GetTopicRank (GetTopicRankReq) returns (GetTopicRankResp) {
  }

  /*获取推荐流插入话题*/
  rpc GetTopicsInRecommend (GetTopicsInRecommendReq) returns (GetTopicsInRecommendResp) {
  }

  //返回指定主题详细资料
  rpc GetTopicInfo (GetTopicInfoReq) returns (GetTopicInfoResp) {
  }
  /*获取从自定义转官方近3天动态数多的话题*/
  rpc GetTopicByConvert2Official (GetTopicByConvert2OfficialReq) returns (GetTopicByConvert2OfficialResp) {
  }
  /*记录每个话题发动态的用户uid*/
  rpc AddUserIDByTopic (AddUserIDByTopicReq) returns (AddUserIDByTopicResp) {
  }
  /*获取话题发动态的用户uid列表*/
  rpc GetUserIDByTopic (GetUserIDByTopicReq) returns (GetUserIDByTopicResp) {
  }
  //返回所有主题
  rpc GetTopicList (GetTopicListReq) returns (GetTopicListResp) {

  }

  //根据指定的topicIds获取主题列表
  rpc GetTopics (GetTopicsReq) returns (GetTopicsResp) {

  }

  rpc GetTopicsCache (GetTopicsCacheReq) returns (GetTopicsCacheResp) {
  }

  //排序圈子
  rpc SortTopic (SortTopicReq) returns (SortTopicResp) {

  }

  //更新主题帖子数量
  rpc UpdateTopicPostCount (UpdateTopicPostCountReq) returns (UpdateTopicPostCountResp) {
  }
  // 调整话题归属
  rpc AdjustTopicBelong (AdjustTopicBelongReq) returns (AdjustTopicBelongResp) {
  }


  //订阅
  rpc SubscribeTopic (SubscribeTopicReq) returns (SubscribeTopicResp) {
  }

  //取消订阅
  rpc UnsubscribeTopic (UnsubscribeTopicReq) returns (UnsubscribeTopicResp) {
  }

  // //获取某个主题的所有订阅者
  // rpc GetSubscribers (GetSubscribersReq) returns (GetSubscribersResp) {
  // }


  /*

      //获取未订阅的主题列表
      rpc GetTopicListExcludeSubscribe (GetTopicListExcludeSubscribeReq) returns (GetTopicListExcludeSubscribeResp) {
      }




      //检查指定uid 是否是主题绑定官方号uid
      rpc CheckTopicBindUidExists (CheckTopicBindUidExistsReq) returns (CheckTopicBindUidExistsResp) {
      }

      //批量检查uids 是否未绑定主题绑定uids
      rpc BatchCheckTopicBindUid (BatchCheckTopicBindUidReq) returns (BatchCheckTopicBindUidResp) {
      }



      //是否订阅了所有主题
      rpc IsSubscribeAllTopic (IsSubscribeAllTopicReq) returns (IsSubscribeAllTopicResp) {
      }

      // 查找话题关系
      rpc GeTopicListRelationByTopic (GeTopicListRelationByTopicReq) returns (GetTopicListResp) {
      }

      //批量根据主题id去查找父级
      rpc BatchGetParentTopicInfo (BatchGetParentTopicInfoReq) returns (BatchGetParentTopicInfoResp) {
      }*/

  // 发布器 新增
  rpc InsertPublisherTopic (InsertPublisherTopicReq) returns (InsertPublisherTopicResp) {
  }

  // 发布器 修改
  //    rpc UpdatePublisherTopicWithoutUids (UpdatePublisherTopicWithoutUidsReq) returns (UpdatePublisherTopicWithoutUidsResp) {
  //    }

  // 发布器 获取配置
  rpc GetPublisherTopicInfo (GetPublisherTopicInfoReq) returns (GetPublisherTopicInfoResp) {
  }

  // 发布器 获取uids
  rpc GetPublisherTopicUids (GetPublisherTopicUidsReq) returns (GetPublisherTopicUidsResp) {
  }

  // 发布器 删除
  rpc DeletePublisherTopic (DeletePublisherTopicReq) returns (DeletePublisherTopicResp) {
  }

  rpc GetPublisherTopicByUid (GetPublisherTopicByUidReq) returns (GetPublisherTopicByUidResp) {
  }

  rpc GetPublisherTopicList (GetPublisherTopicListReq) returns (GetPublisherTopicListResp) {
  }



  /*记录话题下动态发布数量*/
  /*增加话题下动态发布记录*/
  rpc AddTopicStatistic (AddTopicStatisticReq) returns (AddTopicStatisticResp) {
  }

  //创建多个话题
  //搜索话题 支持模糊搜索
  rpc SearchTopic(SearchTopicReq) returns (SearchTopicResp){

  }


  rpc BatchUpdateTopicPostCount(BatchUpdateTopicPostCountReq) returns (BatchUpdateTopicPostCountResp){}

  rpc CreateEsTopic(CreateEsTopicReq) returns (CreateEsTopicResp){}
  rpc DeleteEsTopic (DeleteTopicReq) returns (DeleteTopicResp) {}

  rpc ReportTopicViewCount (ReportTopicViewCountReq) returns (ReportTopicViewCountRsp) {}

  rpc UpdateTopicRankWeight (UpdateTopicRankWeightReq) returns (UpdateTopicRankWeightResp) {}
  rpc GetTopicRankWeight (GetTopicRankWeightReq) returns (GetTopicRankWeightResp) {}
  rpc BatGetTopicRankWeight (BatGetTopicRankWeightReq) returns (BatGetTopicRankWeightResp) {}

  /* 话题详情页广告位 */
  rpc SetTopicAd (SetTopicAdReq) returns (SetTopicAdResp) {}
  rpc GetTopicAd (GetTopicAdReq) returns (GetTopicAdResp) {}
  rpc DelTopicAd (DelTopicAdReq) returns (DelTopicAdResp) {}

  /*话题绑定的游戏id*/
  rpc GetTopicBindGameID (GetTopicBindGameIDReq) returns (GetTopicBindGameIDResp) {}

  /* 新建 一个心情id对应的话题id */
  rpc CreateTopicIDForMood (CreateTopicIDForMoodReq) returns (CreateTopicIDForMoodResp) {}

  /* 删除话题流强插贴子配置 */
  rpc DelTopicStreamForceConf(DelTopicStreamForceConfReq) returns (DelTopicStreamForceConfResp) {}
  /* 新建或更新话题流强插贴子配置 */
  rpc SetTopicStreamForceConf(SetTopicStreamForceConfReq) returns (SetTopicStreamForceConfResp) {}
  /* 根据筛选条件获取话题流强插贴子配置列表 */
  rpc GetTopicStreamForceConfList(GetTopicStreamForceConfListReq) returns (GetTopicStreamForceConfListResp) {}
  /* 获取话题流指定强插位置范围并且生效中的贴子 */
  rpc GetTopicStreamForceActivePostList(GetTopicStreamForceActivePostListReq) returns (GetTopicStreamForceActivePostListResp) {}

  /* 反馈话题下的帖子无关 */
  rpc ReportTopicFeedback(ReportTopicFeedbackReq) returns (ReportTopicFeedbackResp) {}
  /* 获取用户在话题下反馈过的帖子 */
  rpc GetTopicFeedbacks(GetTopicFeedbacksReq) returns (GetTopicFeedbacksResp) {}

  rpc GetStepOnTopicIds(GetStepOnTopicIdsReq) returns (GetStepOnTopicIdsResp) {}

  rpc BatchGetTopicFollowCount(BatchGetTopicFollowCountReq)returns(BatchGetTopicFollowCountResp){}

  //发帖按钮引导配置 运营后台
  rpc SetPostButtonConfig(SetPostButtonConfigReq)returns(SetPostButtonConfigResp){}
  rpc GetPostButtonConfigs(GetPostButtonConfigsReq)returns(GetPostButtonConfigsResp){}
  rpc OfflinePostButtonConfig(OfflinePostButtonConfigReq)returns(OfflinePostButtonConfigResp){}
  rpc GetLastedPostButtonConfig(GetLastedPostButtonConfigReq)returns(GetLastedPostButtonConfigResp){}
}

enum TopicQueryOption {
  QueryAll = 0; //查询所有的
  QueryEnable = 1; //只查询上线的
  QueryDisable = 2; //只查询下线的
}

//兼容旧版本,GetTopics用，其它勿用
enum TopicQueryOptionEx {
  ExQueryEnable = 0; //只查询上线的
  ExQueryAll = 1; //查询所有的
  ExQueryDisable = 2; //只查询下线的
}

enum TopicType {
  TypeCollection = 0; //话题集合
  TypeTopic = 1; //话题
  TypeGeo = 2; //城市 地理位置
  TypeDIY = 3; // 自定义话题
  TypeMood = 4; /* 心情 */
  TypeTalk = 5;  //讨论话题
  TypeGameDistrict = 6; //游戏大区
}

message TopicBindGameInfo{
  uint32 game_id = 1;
  string game_name = 2;
}

message CreateTopicReq {
  string name = 1;
  string desc = 2;
  string icon_url = 3;
  uint32 bind_uid = 4 [deprecated = true]; //绑定一个账号作为主题的官方号
  TopicType topic_type = 5;
  string parent_topic_id = 6; //如果创建话题 指定一个父级主题id

  TopicWeightInfo weight_info = 7; /*提权*/
  TopicBindGameInfo game = 8; /* 话题 绑定一个游戏id 根据gamelist热门话题强插一个绑定的话题*/

  repeated RelatedTopic related_topic_list = 9; // 关联话题列表
  bool enable = 10; //是否上线
  repeated string multi_pic_url_list = 11; // 多图
}


message CreateTopicResp {
  string topic_id = 1;
  bool is_new = 2; // 是否是新的话题
}

message EnableTopicReq {
  string topic_id = 1;
  bool enable = 2;
}


message EnableTopicResp {

}

message UpdateTopicInfoReq {
  string topic_id = 1;
  string name = 2;
  string desc = 3;
  string icon_url = 4;
  uint32 bind_uid = 5 [deprecated = true]; //绑定一个账号作为主题的官方号

  TopicWeightInfo weight_info = 6; /*提权*/
  TopicBindGameInfo game = 7; /* 话题 绑定一个游戏id 根据gamelist热门话题强插一个绑定的话题*/

  repeated RelatedTopic related_topic_list = 8; // 关联话题列表
  repeated string multi_pic_url_list = 9; // 多图
}

message UpdateTopicInfoResp {

}

/*从自定义修改为官方话题*/
message DIY2OfficialTopicReq{
  string topic_id = 1;
  string desc = 2;
  string icon_url = 3;
  TopicType topic_type = 4;
  string parent_topic_id = 5; //如果创建话题 指定一个父级主题id

  TopicWeightInfo weight_info = 6; /*提权*/
  TopicBindGameInfo game = 7; /* 话题 绑定一个游戏id 根据gamelist热门话题强插一个绑定的话题*/
}
message DIY2OfficialTopicResp{
}

message BatUpdateWeightReq{
  repeated string topic_id_list = 1;
  TopicWeightInfo weight_info = 7; /*提权*/
}
message BatUpdateWeightResp{
  uint32 update_cnt = 1;
}

/*批量获取所有提权话题*/
message BatGetAllWeightTopicReq{
  uint32 offset = 1;
  uint32 limit = 2;
}
message TopicWithWeight{
  string topic_id = 1;
  string weight = 2;
  uint32 start_ts = 3;
  uint32 end_ts = 4;
}
message BatGetAllWeightTopicResp{
  repeated TopicWithWeight weight_list = 1;
  uint32 total_cnt = 2;
}

enum TopicSubscript{
  NoneSub = 0; /*没有*/
  New = 1; /*角标 新*/
  Recommend = 2; /*角标 荐*/
  Hot = 3; /*角标 HOT*/
  SecondNew = 4; /*角标 新2*/
  Up = 5; /*角标 Up*/
  Activity = 6; /*角标 活动*/
}

enum TopicPlatform {
  Android = 0;
  iOS = 1;
  All_Platform = 255;
}

enum PersonAttr{
  AllAttr = 0; /*全部人*/
  Newbie = 1; /*新用户*/
  Gender = 2; /*指定性别*/
}

enum TopicGender {
  Female = 0;
  Male = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message TopicRecommendInfo{
  string config_id = 1; /*唯一id*/
  uint32 index = 2; /*插入的位置*/
  string topic_id = 3; /*话题id*/
  string name = 4; /*话题名称*/
  string pic_url = 5; /*背景图*/
  uint32 subscript = 6; /*角标 TopicSubscript*/
  uint32 platform = 7; /*展示平台 TopicPlatform*/
  uint32 person_attr = 8; /*展示人群 PersonAttr*/
  uint32 start_ts = 9; /*生效开始时间*/
  uint32 end_ts = 10; /*生效结束时间*/
  uint32 status = 11; /*状态 同 WeightStatus*/
  uint32 RegDays = 12; /*新用户注册天数*/
  uint32 gender = 13 /*性别 TopicGender*/;
  uint32 topic_type = 14;
}

enum TopicInRecommendType{
  OLD_VERSION = 0; /*旧版本，在中间，5个*/
  NEW_VERSION_TOP_POSITION_NORMAL = 1; /*新版本，在顶部，20个 热门话题*/
  NEW_VERSION_TOP_POSITION_CATALOG = 2; /*新版本，在顶部，20个 其他分类话题*/
}
/*推荐流插入话题设置*/
message UpdatePutTopicInRecommendConfReq{
  TopicRecommendInfo info = 1;
  uint32 version_type = 2; /*TopicInRecommendType*/
}

message UpdatePutTopicInRecommendConfResp{
}

message DelPutTopicInRecommendConfReq{
  repeated string config_id_list = 1;
  uint32 version_type = 2; /*TopicInRecommendType*/
}

message DelPutTopicInRecommendConfResp{
  uint32 del_cnt = 1;
}

/*获取推荐流插入话题配置*/
message GetPutTopicInRecommendConfReq{
  uint32 index = 1; /*插入的位置*/
  string topic_id = 2; /*话题id*/
  string name = 3; /*话题名称*/
  uint32 offset = 4;
  uint32 limit = 5;
  uint32 version_type = 6; /*TopicInRecommendType*/

}
message GetPutTopicInRecommendConfResp{
  repeated TopicRecommendInfo info_list = 1;
  uint32 total_cnt = 2;
}

/*新运营配置推荐流话题类*/
message TopicAndSubscript{
  string topic_id = 1;
  string name = 2; /*话题名称*/
  uint32 subscript = 3; /*角标 TopicSubscript*/
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message CatalogTopicInRcmdFeed{
  string catalog_id = 1;
  string catalog_name = 2;
  uint32 index = 3; /*倒序 序号*/
  repeated TopicAndSubscript topic_list = 4;
  uint32 platform = 5; /*展示平台 TopicPlatform*/
  uint32 person_attr = 6; /*展示人群 PersonAttr*/
  uint32 start_ts = 7; /*生效开始时间*/
  uint32 end_ts = 8; /*生效结束时间*/
  uint32 status = 9; /*状态 同 WeightStatus*/
  uint32 RegDays = 10; /*新用户注册天数*/
  uint32 gender = 11; /*性别 TopicGender*/
  uint32 update_time = 12;
}

/*分类的话题 推荐流顶部 运营后台新增、修改*/
message UpdateCatalogTopicInRcmdFeedReq{
  repeated CatalogTopicInRcmdFeed catalogs = 1;
}
message UpdateCatalogTopicInRcmdFeedResp{
}
message DelCatalogTopicInRcmdFeedReq{
  repeated string config_id_list = 1;
}
message DelCatalogTopicInRcmdFeedResp{
  uint32 del_cnt = 1;
}
message GetCatalogTopicInRcmdFeedReq{
  uint32 offset = 1;
  uint32 limit = 2;
  bool valid = 3; /*是否仅返回生效的*/
}
message GetCatalogTopicInRcmdFeedResp{
  repeated CatalogTopicInRcmdFeed catalogs = 1;
  uint32 total_cnt = 2;
}

/*获取推荐流插入话题*/
message GetTopicsInRecommendReq{
  uint32 index_max = 1;
  uint32 version_type = 2; /*TopicInRecommendType*/
}
message IndexTopicConfs{
  repeated TopicRecommendInfo info = 1;
}
message GetTopicsInRecommendResp{
  map<uint32, IndexTopicConfs> conf_map = 1;
  repeated TopicInfo weight_list = 2;
}

/*获取24小时 3天 7天内 动态量最多的话题*/
message GetTopicRankReq{
  uint32 ndays = 1; /*自定义话题 根据近n天总动态数排序 DIYSortNDays*/
  uint32 cnt = 2; /*要几个*/
}

message GetTopicRankResp{
  repeated string topic_id_list = 1;
}
/*获取从自定义转官方近3天动态数多的话题*/
message GetTopicByConvert2OfficialReq{
  uint32 cnt = 1;
}

message GetTopicByConvert2OfficialResp{
  repeated string topic_id_list = 1;
}
/*记录每个话题发动态的用户uid*/
message AddUserIDByTopicReq{
  repeated string topic_id = 1;
  uint32 uid = 2;
  bool is_delete = 3;
}



message AddUserIDByTopicResp{
}

/*获取话题发动态的用户uid列表*/
message GetUserIDByTopicReq{
  string topic_id = 1;
  uint32 cnt = 2;
}
message GetUserIDByTopicResp{
  repeated string uid_list = 1;
}

message GetTopicInfoReq {
  string topic_id = 1;
  TopicQueryOption option = 2;
  string name = 3; //通过名称来查询
  //传入uid
  uint32 uid = 4;
  TopicType topic_type = 5; //指定获取类型
  //    uint32 weight_status = 6; /*提权状态 WeightStatus*/
  //    int32 weight = 7; /*提权指数*/
  //    uint32 ndays = 8; /*自定义话题 根据近n天总动态数排序 DIYSortNDays*/
}

message GetTopicInfoResp {
  TopicInfo info = 1;
}


enum WeightStatus{
  WeiStDefault = 0; /*默认*/
  WeiStValid = 1; /*配置中*/
  WeiStExpired = 2; /*过期*/
  WeiStFuture = 3; /*未来的*/
  WeiNoConfig = 4; /*无配置*/
}
enum DIYSortNDays{
  DefaultSort = 0; /*按创建时间排序*/
  OneDay = 1; /*24小时内*/
  ThreeDays = 3; /*3个自然日内*/
  SevenDays = 7; /*7个自然日内*/
}
message GetTopicListReq {
  TopicQueryOption option = 1 [deprecated = true];
  uint32 offset = 2; //分页拉取 偏移位置
  uint32 limit = 3; //分页拉取 拉取数量
  TopicType topic_type = 4; //指定获取类型
  bool query_parent_info = 5 [deprecated = true];
  string parent_topic_id = 6; // default: none get all topics  popular:get all popular topics
  uint32 uid = 7;//如果有uid 就查uid的
  uint32 weight_status = 8; /*提权状态 WeightStatus*/
  string weight = 9; /*提权指数*/
  uint32 ndays = 10; /*自定义话题 根据近n天总动态数排序 DIYSortNDays*/
  string name = 11; /*话题名称 精确搜索*/
  string topic_id = 12; /*话题ID 精确搜索*/
  string game_name = 13; /* 游戏name 精确搜索 */
}

message GeTopicListRelationByTopicReq {

  string topic_id = 1; //指定topicId
  uint32 offset = 2; //分页拉取 偏移位置
  uint32 limit = 3; //分页拉取 拉取数量
  TopicQueryOption option = 4;
  bool query_parent_topic = 5; //与topic_id的关系  true:查询父主题 false :查询子话题

}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetTopicListResp {
  repeated TopicInfo topicInfos = 1;
  uint32 total = 2; //总记录数
}

message SortTopicReq {
  map<string, string> sort = 1; //<topicId,index> 按照index升序排
}

message SortTopicResp {

}

message DeleteTopicReq {
  string topic_id = 1;
}

message DeleteTopicResp {

}

/*批量删除*/
message BatDeleteTopicReq {
  repeated string topic_id_list = 1;
}
//message BatDelResult{
//    string topic_id = 1;
//    bool is_deleted = 2;
//}
message BatDeleteTopicResp {
  uint32 deleted_cnt = 1;
}

/**/


message SubscribeTopicReq {
  uint32 uid = 1;
  string topic_id = 2;
  TopicType  topic_type = 3; //指定类型
}


message SubscribeTopicResp {

}

message UnsubscribeTopicReq {
  uint32 uid = 1;
  string topic_id = 2;
}

message UnsubscribeTopicResp {

}


message GetSubscribersReq {

}

message GetSubscribersResp {

}

message GetTopicsReq {
  repeated string topic_ids = 1;
  TopicQueryOptionEx option = 2;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetTopicsResp {
  repeated TopicInfo topicInfos = 1;
  uint32 total = 2; //总记录数

}

message TopicWeightInfo{
  bool has_weight = 1; /*是否提权*/
  string weight = 2;  /*提权指数*/
  uint32 weight_start_ts = 3;  /*提权开始时间*/
  uint32 weight_end_ts = 4;  /*提权结束时间*/
  uint32 weight_status = 5; /*提权状态 WeightStatus*/
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message TopicInfo {
  string topic_id = 1;
  string name = 2;
  string desc = 3;
  string icon_url = 4;
  //查询详细内容则包括一下字段
  uint32 member_count = 5 [deprecated = true]; //成员数
  uint32 post_count = 6; //动态数量
  uint32 update_at = 7; //最后一个帖子发布的时间
  uint32 bind_uid = 8 [deprecated = true]; //绑定一个账号作为主题的官方号
  uint32 create_at = 9; //主题创建时间

  TopicType topic_type = 10;

  string order = 11; //此字段仅用于排序


  repeated TopicInfo parent_infos = 12 [deprecated = true];
  //观看数量
  uint32 view_count = 13;

  TopicWeightInfo weight_info = 14; /*提权信息*/
  uint32 PostCntInDays = 15; /*n天内的总动态数*/

  bool is_valid = 16; // 5.5.6 自定义话题先创建再审核，需要标记话题是否有效
  TopicBindGameInfo game = 17; /* 话题 绑定一个游戏id 根据gamelist热门话题强插一个绑定的话题*/
  string mood_id = 18; /* 一个心情id对应的话题id */

  repeated RelatedTopic related_topic_list = 19; // 关联话题列表
  bool enable = 20; // 是否显示
  repeated string multi_pic_url_list = 21; // 多图
}

message RelatedTopic {
  string id = 1;
  string name = 2;
}

message CheckTopicBindUidExistsReq {
  uint32 bind_uid = 1;
  string topic_id = 2; //如果指定topicId 则匹配该主题的绑定uid 如果未指定topicId 则检查改uid是否为主题绑定uid
}

message CheckTopicBindUidExistsResp {
  bool exists = 1;
}

message LoadMore {
  uint32 last_page = 1;
}

message GetTopicListExcludeSubscribeReq {
  LoadMore load_more = 1;
  uint32 count = 2; //返回的count 可能会比这个count大
  repeated string subscriber_ids = 3; //已经订阅的主题ids
  TopicType topic_type = 4;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetTopicListExcludeSubscribeResp {
  repeated TopicInfo topicInfos = 1;
  LoadMore load_more = 2;
}


message BatchCheckTopicBindUidReq {
  repeated uint32 uids = 1;
}

message BatchCheckTopicBindUidResp {
  map<uint32, bool> result = 1;
}

message UpdateTopicPostCountReq {
  string topic_id = 1;
  string post_id = 2;
  bool delete = 3;
  uint32 create_at = 4; //帖子创建时间
  string sub_topic_id = 5;
}

message UpdateTopicPostCountResp {

}

message PostInfo{
  string topic_id = 1;
  string post_id = 2;
  bool delete = 3;
}
message BatchUpdateTopicPostCountReq{
  repeated PostInfo post_infos = 1;
}
message BatchUpdateTopicPostCountResp{}

message IsSubscribeAllTopicReq {
  repeated string topic_ids = 1;
}

message IsSubscribeAllTopicResp {
  bool result = 1;
}

message AdjustTopicBelongReq {
  string topic_id = 1;
  string parent_topic_id = 2;
  bool is_delete = 3;
}
message AdjustTopicBelongResp {
}

message BatchGetParentTopicInfoReq {
  repeated string topic_ids = 1;
}

message BatchGetParentTopicInfoResp {
  map<string, TopicInfoes> results = 1;
}

message TopicInfoes {
  repeated TopicInfo infoes = 1;
}

// 发布器 话题配置接口
enum PublisherTopicAppid{
  Appid_Default = 0;
  Appid_TT = 1;
  Appid_Huanyou = 2;
  Appid_TT_Huanyou = 3;
}

enum PublisherTopicPlatform{
  Platform_Default = 0;
  Platform_Android = 1;
  Platform_IOS = 2;
  Platform_Android_IOS = 3;
}

enum UserSex {
  User_Female = 0;
  User_Male = 1;
}

message SexAgeInfo{
  repeated UserSex sex = 1;
  bool is_age_unlimited = 2;
  uint32 age_year_min = 3;
  uint32 age_year_max = 4;
}

enum PublisherTopicShowUser{
  To_Default = 0;
  To_Total = 1;  // 对全局用户生效的topic
  To_Part = 2;  // 对指定用户生效的topic
  To_Newbie = 3; // 对新用户生效的topic 不需要新设备
  To_SexAge = 4; // 指定性别、年龄层用户
}

enum PublisherTopicNewInsert{
  Is_Default = 0;
  Is_New = 1;  // 新增
  Is_Continue = 2;  // 续传
}

message PublisherTopicInfo{
  string topic_id = 1;
  PublisherTopicAppid app_id = 2;
  PublisherTopicPlatform platform = 3;
  int64 begin_time = 4;
  int64 end_time = 5;
  PublisherTopicShowUser show = 6;
  uint32 days_reg = 7;
  SexAgeInfo age = 8;
}

// 新增接口：uids 对全局用户生效时，为空
message InsertPublisherTopicReq{
  PublisherTopicInfo info = 1;
  repeated uint32 uids = 2; // InsertPublisherTopicReq 中对全局用户生效时，为空; UpdatePublisherTopicWithUidsReq 中必填；
  PublisherTopicNewInsert new_insert = 3;
}
message InsertPublisherTopicResp{
}

// 修改接口  // 不填 uids
// PS: 废弃不用
//message UpdatePublisherTopicWithoutUidsReq{
//    PublisherTopicInfo info = 1;
//}
//message UpdatePublisherTopicWithoutUidsResp{
//}

// 获取topic配置（不包括uid）
message GetPublisherTopicInfoReq{
  string topic_id = 1;
}
message GetPublisherTopicInfoResp{
  PublisherTopicInfo info = 1;
}

// 获取topic配置（包含uid）
message GetPublisherTopicUidsReq{
  string topic_id = 1;
  uint32 offset = 2;
  uint32 limit = 3;
}
message GetPublisherTopicUidsResp{
  PublisherTopicInfo info = 1;
  repeated uint32 uids = 2;
}

// 删除
message DeletePublisherTopicReq{
  string topic_id = 1;
}
message DeletePublisherTopicResp{
}

// 客户端获取
message GetPublisherTopicByUidReq{
  uint32 uid = 1;
}
message GetPublisherTopicByUidResp{
  repeated PublisherTopicInfo attr = 1;
}

// 获取发布器话题列表
message GetPublisherTopicListReq{
}
message GetPublisherTopicListResp{
  repeated string topic_id_list = 1;
}

/*记录话题下动态发布数量*/
/*增加话题下动态发布记录*/
message AddTopicStatisticReq{
  string post_id = 1;
  bool is_del_post = 2;
  repeated string diy_topic_id = 3;//话题id列表
  string moon_topic_id = 4; //心情对应的话题id
}
message AddTopicStatisticResp{
}

//message AddTopicStatisticReq{
//    string topic_id = 1;
//    string post_id = 2;
//}
//message AddTopicStatisticResp{
//}

//同时创建多个话题
message CreateTopicV2Req {
  message TopicInfo {
    string name = 1;
    string desc = 2;
    string icon_url = 3;
    uint32 bind_uid = 4 [deprecated = true]; //绑定一个账号作为主题的官方号
    TopicType topic_type = 5;
    string parent_topic_id = 6; //如果创建话题 指定一个父级主题id
  }
  repeated TopicInfo topic_list = 1;
}

message CreateTopicV2Resp {
  repeated string topic_id_list = 1;
}


message SearchTopicReq {
  string name = 1;//模糊查询
  int32 last_count = 2;//最后分页item Count 初始为0
  string last_id = 3;//最后分页 页id 初始为空字符串
}

message SearchTopicResp {
  message TopicInfo {
    string name = 1;
    string topic_id = 2; //如果创建话题
    int32 count = 3;//数量
    bool is_new = 4;//是否是近x天的话题
    TopicType topic_type = 5;
  }
  repeated TopicInfo  topic_list = 1;//模糊查询返回名单
  int32 last_count = 2;//最后分页item Count 初始为0
  string last_id = 3;//最后分页 页id 初始为空字符串
  bool need_create = 4;//第一次请求会判断 精确匹配没有就返回true 后面都为false
}

message CreateEsTopicReq{
  string name = 1;
  string topic_id = 2;
}
message CreateEsTopicResp{}

message ReportTopicViewCountReq {
  uint32 uid = 1;
  repeated string topic_ids = 2;
}

message ReportTopicViewCountRsp {
}

message TopicRankWeightInfo{
  bool has_weight = 1; /*是否提权*/
  string weight = 2;  /*提权指数*/
  uint32 weight_start_ts = 3;  /*提权开始时间*/
  uint32 weight_end_ts = 4;  /*提权结束时间*/
  WeightStatus weight_status = 5; /*提权状态 WeightStatus*/
  string topic_id = 6; /*话题ID*/
}

message UpdateTopicRankWeightReq{
  TopicRankWeightInfo weight_info = 1;
}

message UpdateTopicRankWeightResp{
  uint32 update_cnt = 1;
}

message GetTopicRankWeightReq{
  string topic_id = 1;
}

message GetTopicRankWeightResp{
  TopicAllRankWeightInfo weight_info = 1;
}

message BatGetTopicRankWeightReq{
  uint32 offset = 1;
  uint32 limit = 2;
}

message TopicAllRankWeightInfo{
  TopicRankWeightInfo weight_info = 1;
  double realscore = 2;
  double totalscore = 3;
  int64 view_cnt = 4;
  int64 post_cnt = 5;
  string topic_name = 6;
  int64 create_time = 7;
  double downscore = 8;
}

message BatGetTopicRankWeightResp{
  repeated TopicAllRankWeightInfo topic_list = 1;
  uint32 total_cnt = 2;
}

/* 话题详情页广告位 && 心情详情页广告位 */
enum AdType{
  TotalAd = 0;
  TopicAd = 1;
  MoodAd = 2;
}
enum TopicConfigCommonStatus{
  Total = 0;
  NotBegin = 1;
  InConfig = 2;
  Expired = 3;
}
message TopicAdInfo{
  string config_id = 1;
  string topic_id = 2;
  uint32 index = 3; /* 序号 */
  uint32 platform = 4; /*展示平台 TopicPlatform*/
  uint32 start_ts = 5; /*生效开始时间*/
  uint32 end_ts = 6; /*生效结束时间*/
  string pic_url = 7; /* 入口图 */
  string link = 8; /* 活动链接 */
  uint32 update_time = 9;
  uint32 ad_type = 10; /* 分为话题广告和心情广告 AdType */
}
message SetTopicAdReq{
  TopicAdInfo info = 1;
}
message SetTopicAdResp{
}
message GetTopicAdReq{
  string topic_name = 1;
  string topic_id = 2;
  uint32 status = 4; /* TopicConfigCommonStatus */
  uint32 offset = 5;
  uint32 limit = 6;
  uint32 platform = 7;
  bool for_app = 8; /* 运营后台false，不需要过滤platform */
  uint32 ad_type = 9; /* 分为话题广告和心情广告 AdType */
}
message GetTopicAdResp{
  repeated TopicAdInfo infos = 1;
  uint32 total_cnt = 2;
}
message DelTopicAdReq{
  repeated string config_ids = 1;
}
message DelTopicAdResp{
}

/*话题绑定的游戏id*/
message GetTopicBindGameIDReq{
  repeated uint32 game_id = 1;
}
message TopicInfoBindGame{
  string topic_id = 1;
  string topic_name = 2;
  uint32 game_id = 3;
}
message GetTopicBindGameIDResp{
  repeated TopicInfoBindGame topics = 1;
}

/* 新建 一个心情id对应的话题id */
message CreateTopicIDForMoodReq{
  string mood_id = 1;
}
message CreateTopicIDForMoodResp{
  string topic_id = 1;
}

message GetTopicsCacheReq {
  repeated string topic_ids = 1;
}


//帖子缓存，帖子，用户数目没用到，暂时不准
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetTopicsCacheResp {
  repeated TopicInfo topicInfos = 1;
}

// 话题贴子推荐流强插配置的状态
enum TopicStreamForceConfStatus {
  TopicStreamForceConfStatusAll = 0; // 全部配置
  TopicStreamForceConfStatusInactive = 1; // 配置未生效
  TopicStreamForceConfStatusActive = 2; // 配置生效中
  TopicStreamForceConfStatusExpired = 3; // 配置已过期
}

message TopicStreamForceConf {
  string conf_id = 1;
  string topic_id = 2; // 强插话题名称
  string post_id = 3;
  uint32 position = 4; // 贴子插入位置
  uint32 begin_at = 5;
  uint32 end_at = 6;
}

message DelTopicStreamForceConfReq {
  string conf_id = 1;
}

message DelTopicStreamForceConfResp {
}

message SetTopicStreamForceConfReq {
  TopicStreamForceConf conf = 1;
}

message SetTopicStreamForceConfResp {
}

message GetTopicStreamForceConfListReq {
  uint32 offset = 1;
  uint32 limit = 2;

  string topic_id = 3;
  string post_id = 4;

  TopicStreamForceConfStatus status = 5;
}

message GetTopicStreamForceConfListResp {
  repeated TopicStreamForceConf conf_list = 1;
  uint32 total = 2;
}

message GetTopicStreamForceActivePostListReq {
  uint32 uid = 1;
  string topic_id = 2;
}

message GetTopicStreamForceActivePostListResp {
  message Post {
    string post_id = 1;
    uint32 pos = 2;
  }

  repeated Post post_list = 1;
}

message ReportTopicFeedbackReq {
  uint32 report_uid = 1; //哪个uid反馈的
  string post_id = 2; //帖子id
  string topic_id = 3; //反馈的是哪个话题id
}

message ReportTopicFeedbackResp {
  enum ReportResult {
    Success = 0; //成功上报
    Duplicate = 1; //重复上报
  }
  ReportResult report_result = 1;
}

message GetTopicFeedbacksReq {
  uint32 report_uid = 1;
  string topic_id = 2;
}

message GetTopicFeedbacksResp {
  message FeedbackInfo {
    string post_id = 1;
  }
  repeated FeedbackInfo feedback_list = 1;
}

message GetStepOnTopicIdsReq {
}

message GetStepOnTopicIdsResp {
  repeated string step_on_topic_ids = 1;
}


message BatchGetTopicFollowCountReq{
  repeated string topic_id = 1;
}
message BatchGetTopicFollowCountResp{
  map<string, uint32> map_list = 1;
}


//发帖按钮引导配置 运营后台
message PostButtonInfo{
  string id=1;
  string url=2;           //引导图片
  string topic_text=3;    //话题文案
  string reward_text=4;   //奖励文案
  string relation_topic=5;   //关联话题
  uint32 start_time=6;     //生效时间
  uint32 end_time=7;       //失效时间
  uint32 status=8;         //配置状态
  uint32 operate_time=9;    //操作时间
  string operator=10;       //操作人

}

enum PostButtonConfigStatus{
  Post_Button_Config_None=0;
  Post_Button_Config_Valid=1;    //生效中
  Post_Button_Config_Expired=2;   //已失效
  Post_Button_Config_NotYet=3;    //待生效
}

message SetPostButtonConfigReq{
  PostButtonInfo  info=1;
}

message SetPostButtonConfigResp{
}

message GetPostButtonConfigsReq{
  PostButtonConfigStatus status=1;
  string relation_topic=2;   //关联话题
  uint32 limit=3;
  uint32 offset=4;
}

message GetPostButtonConfigsResp{
  repeated PostButtonInfo info=1;
  uint32 total=2;
}

message OfflinePostButtonConfigReq{
  string id=1;
}
message OfflinePostButtonConfigResp{

}

message GetLastedPostButtonConfigReq{

}

message GetLastedPostButtonConfigResp{
  string topic_name=1;
  string url=2;           //引导图片
  string topic_text=3;    //话题文案
  string reward_text=4;   //奖励文案
  string relation_topic=5;   //关联话题

}