syntax = "proto3";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package ugc.user_online;

option go_package = "golang.52tt.com/protocol/services/ugc/user_online";

service UserOnline{
    rpc GetUserOnlineStatus(GetUserOnlineReq) returns(GetUserOnlineResp);
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetUserOnlineReq{
    repeated uint32 Uids = 1;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetUserOnlineResp{
    map<uint32,uint32> Status = 1;
}