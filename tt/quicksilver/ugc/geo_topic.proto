syntax = "proto3";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package ugc.geo_topic;

option go_package = "golang.52tt.com/protocol/services/ugc/geo_topic";

service GeoTopic {

    rpc CreateGeoTopic (CreateGeoTopicReq) returns (CreateGeoTopicResp) {}

    //删除
//    rpc DeleteGeoTopic (DeleteGeoTopicReq) returns (DeleteGeoTopicResp) {}

    //上下线主题
    rpc EnableGeoTopic (EnableGeoTopicReq) returns (EnableGeoTopicResp) {}

    //返回指定主题详细资料
    rpc GetGeoTopicById (GetGeoTopicByIdReq) returns (GetGeoTopicByIdResp) {}
    rpc GetGeoTopicByCode (GetGeoTopicByCityCodeReq) returns (GetGeoTopicByCityCodeResp) {}
    rpc GetGeoTopicsByIds (GetGeoTopicsByIdsReq) returns (GetGeoTopicsByIdsResp) {}

    //更新主题帖子数量
    rpc UpdateGeoTopicPostCount (UpdateGeoTopicPostCountReq) returns (UpdateGeoTopicPostCountResp) {}
}


message CreateGeoTopicReq {
    string city_code = 1;
    string city_name = 2;
}

message CreateGeoTopicResp {
    string geo_topic_id = 1;
}

message EnableGeoTopicReq {
    string geo_topic_id = 1;
    bool enable = 2;
}

message EnableGeoTopicResp {}

message GetGeoTopicByIdReq {
    string geo_topic_id = 1;
}

message GetGeoTopicByIdResp {
    GeoTopicInfo info = 1;
}

message GetGeoTopicByCityCodeReq {
    string city_code = 1;
}

message GetGeoTopicByCityCodeResp {
    GeoTopicInfo info = 1;
}

message GetGeoTopicsByIdsReq {
    repeated string geo_topic_ids = 1;
}

message GetGeoTopicsByIdsResp {
    map<string, GeoTopicInfo> infos = 1;
}


message DeleteGeoTopicReq {
    string geo_topic_id = 1;
}

message DeleteGeoTopicResp {}

message GeoTopicInfo {
    string geo_topic_id = 1;
    string city_code = 2;
    string city_name = 3;
    bool   enable = 4;
    uint32 post_count = 5; //动态数量
    int64  update_at = 6; // 最后一个帖子发布的时间
}


message UpdateGeoTopicPostCountReq {
    string geo_topic_id = 1;
    int64  delta = 2;
    int64  create_at = 3; // 帖子创建时间
}

message UpdateGeoTopicPostCountResp {}