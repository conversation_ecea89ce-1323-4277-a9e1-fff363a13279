syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-level";

package channel_level;

service ChannelLevel {
    // --------------- 基础接口 -----------------

    rpc GetChannelInfo (GetChannelInfoReq) returns (GetChannelInfoResp) {
    }

    // 仅返回活跃房间信息，单次查询超过20个将会被截断
    rpc BatchGetChannelInfo (BatchGetChannelInfoReq) returns (BatchGetChannelInfoResp) {
    }

    // --------------- 扩展接口 -----------------

    // 获取配置信息
    rpc GetSettings (GetSettingsReq) returns (GetSettingsResp) {
    }

    rpc GetChannelInfoByWeb (GetChannelInfoByWebReq) returns (GetChannelInfoByWebResp) {
    }

    // 获取房间管理员限制数
    rpc GetChannelAdminCountLimit (GetChannelAdminCountLimitReq) returns (GetChannelAdminCountLimitResp) {
    }

    // --------------- 维护接口 -----------------

    // 添加经验球
    rpc AddChannelExpOrb (AddChannelExpOrbReq) returns (AddChannelExpOrbResp) {
    }

    // 使用经验球
    rpc UseChannelExpOrb (UseChannelExpOrbReq) returns (UseChannelExpOrbResp) {
    }

    // 批量增加经验
    rpc BatchAddChannelExp (BatchAddChannelExpReq) returns (BatchAddChannelExpResp) {
    }

    // 查询经验增加记录
    rpc QueryChannelExpRecord (QueryChannelExpRecordReq) returns (QueryChannelExpRecordResp) {
    }
}

message Level {
    uint32 id = 1;

    enum Style {
        None = 0;
        Primary = 1; // 初级
        Middle = 2; // 中级
        High = 3; // 高级
    }

    // 等级特权样式
    Style welcome_style = 2; // 欢迎样式
    Style mic_style = 3; // 麦位样式
    Style team_style = 4; // 小队样式

    uint32 min_exp = 5; // 当前等级最小经验值
    uint32 max_exp = 6; // 当前等级最大经验值，为0则表示无上限
    uint32 next_id = 7; // 下一等级标识，等于0或者当前等级标识则标识满级
}

message GetChannelInfoReq {
    uint32 channel_id = 1;

    int64 settings_update_time = 2; // 配置更新时间戳
}
message GetChannelInfoResp {
    Level level = 1;

    uint32 owner = 2; // 房主标识
    uint32 current_exp = 3; // 当前经验值
    int64 update_time = 4; // 更新时间

    int64 settings_update_time = 5; // 配置更新时间戳
}
message BatchGetChannelInfoReq {
    repeated uint32 channel_ids = 1;

    int64 settings_update_time = 2; // 配置更新时间戳
}
message BatchGetChannelInfoResp {
    map<uint32, GetChannelInfoResp> info_map = 1;

    int64 settings_update_time = 2; // 配置更新时间戳
}

message GetSettingsReq {
    int64 settings_update_time = 1; // 配置更新时间戳
}
message GetSettingsResp {
    int64 settings_update_time = 1; // 配置更新时间戳，与参数时间一致则不返回配置信息

    repeated Level levels = 2;
}

message GetChannelInfoByWebReq {
    uint32 channel_id = 1;
    uint32 uid = 2;
}
message GetChannelInfoByWebResp {
    message Mission {
        uint32 type = 1; // 类型
        string desc = 2; // 描述
        int32 exp = 3; // 经验值
        uint32 order = 4; // 排序
        uint32 max_progress = 5; // 最大进度
        uint32 current_progress = 6; // 当前进度
    }

    uint32 level_id = 1; // 标识
    uint32 current_exp = 2; // 当前经验
    uint32 owner = 3; // 房主标识

    repeated Mission missions = 4; // 任务列表

    repeated ExpOrb orbs = 5; // 经验球
}

message GetChannelAdminCountLimitReq {
    uint32 channel_id = 1;
}
message GetChannelAdminCountLimitResp {
    uint32 limit = 1;
}

message ExpOrb {
    int32 exp = 1; // 经验值小于0，则为减少

    string seq = 2; // 唯一序号
    string reason = 3;

    int64 expire_at = 4; // 过期时间
}

message AddChannelExpOrbReq {
    uint32 channel_id = 1;

    ExpOrb orb = 2;

    bool use = 3;
}
message AddChannelExpOrbResp {
    bool is_repeat = 1; // 是否重复添加
    uint32 exp = 2; // 修改后的经验值
}

message UseChannelExpOrbReq {
    uint32 channel_id = 1;
    string seq = 3; // 唯一序号
}
message UseChannelExpOrbResp {
    bool is_repeat = 1; // 是否重复使用
    uint32 exp = 2; // 使用后的经验值
}

message AddExpInfo {
    uint32 channel_id = 1;
    int32 exp = 2;          // 经验值小于0，则为减少
    uint32 count_today = 3; // 这是今天添加的第几次
    uint32 owner = 4;       // 房主uid
}
message BatchAddChannelExpReq {
    string source = 1;     // 来源业务方
    string operator = 2;   // 操作者名字
    repeated AddExpInfo add_exp_infos = 3;
}
message AddExpResult {
    uint32 channel_id = 1;
    uint32 current_exp = 2;     // 修改后的经验值
    bool is_success = 3;        // 是否添加成功
    bool is_repeat = 4;         // 是否重复添加
}
message BatchAddChannelExpResp {
    repeated AddExpResult add_exp_results = 1;
}

message QueryChannelExpRecordReq{
    uint32 channel_id = 1;
    int64 date = 2;             // 查询哪天的记录，秒级时间戳
    uint32 offset = 3;
    uint32 limit = 4;
    string source = 5;          // 来源业务方
}
message AddExpRecord {
    uint32 channel_id = 1;
    uint32 create_uid = 2;
    int32 exp = 3;
    string operator = 4;
    int64 update_time = 5;
}
message QueryChannelExpRecordResp{
    repeated AddExpRecord add_exp_records = 1;
    uint32 total_records_cnt = 2;   // 记录总数目
}