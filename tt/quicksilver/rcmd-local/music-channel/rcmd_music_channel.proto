syntax="proto3";

import "tt/quicksilver/rcmd-local/common/common.proto";

package rcmd.music_channel;

option go_package = "golang.52tt.com/protocol/services/rcmd/music_channel";

enum RCMDLabel {	//后续再新增
    None = 0;
    GangUpWithHomeOwner = 1; // 一起玩过
    ChatWithHomeOwner = 2; // 互聊(混推会下发，垂直列表已废弃)
    FollowUserInChannel = 3; // 关注
    LocShow = 4; // 同城
    MtFollowUserInChannel = 5; // 有好友/单关在的房间
    MtEverEnterChannel = 6; // 过去30天进过的房间
    MtManyUsersInChannel = 7; // 超多人围观
    MtManyUsersFollowChannelOwner = 8; // 很多人关注房主
    MtGuessYouLikeChannelOwner = 9; // 最近关注人的相似房主推荐
    MtRecentLikeTabChannelDouDi = 10; // 最近进过房间同玩法兜底
    MtHotGameHotChannelDouDi = 11; // 热聊挑战上榜房间兜底
}

message ChannelInfo {
    uint32 tag_id                  = 1; // 所属tag_id
    uint32 recall_flag             = 2; // 召回标识 0：兜底，1：规则版
    rcmd.common.LocationInfo loc   = 3;
    repeated RCMDLabel rcmd_labels = 4; // 推荐标签
    string meta_id = 5;	//透传给客户端,客户端上报到数仓
    repeated uint32 relation_uids = 6; // 重逢的用户列表
}

message BlockOption {
    uint32 block_id = 1;           //块id
    uint32 elem_id = 2;            //块里面的元素id
}

enum FilterType {
    Invalid = 0;
    OutputFilter = 1;	//下发过滤器
    ExposeFilter = 2; //曝光过滤器
}

//房间主题
message ChannelTopic {
    uint32 tab_id = 1;
    repeated BlockOption block_options = 2;
}

message NotExposeChannels {
    string filter_id = 1;
    repeated uint32 not_expose_channel_id_list = 2; //未曝光的房间ID
}

message GetMusicChannelRecommendationReq {
    uint32 uid = 1;
    uint32 limit = 2;
    repeated BlockOption block_options = 3;  //废弃,改为使用channel_topics
    uint32 client_type = 4;             // 参考ServiceInfo中的client_type
    uint32 client_version = 5;          // 参考ServiceInfo中的client_version
    uint32 market_id = 6;               // 参考ServiceInfo中的market_id
    string channel_package_id = 7;		// 渠道包ID

    uint32 debug_flag = 8; //测试标志
    uint32 sex = 9; //性别过滤 0-不过滤 1-选择Male 2-选择Female

    uint32 category_id = 10; // 分类筛选
    repeated uint32 tab_ids = 11; // 分类筛选-多选子类型	//废弃,改为使用channel_topics
    bool is_first_get = 12; //是否首刷
    FilterType filter_type = 13;
    repeated uint32 expose_channel_id_list = 14; //上一刷曝光的房间id
    repeated uint32 follow_channel_id_list = 15; //跟随的房间id
    repeated ChannelTopic channel_topics =16;
    string enter_source = 17; //调用来源
    string filter_id = 18; // 页面调用区分
    bool is_user_location_auth_open = 19; // 用户定位授权是否开启
    NotExposeChannels not_expose_channels_list = 20; //未曝光的房间filterID及房间ID
}

message GetMusicChannelRecommendationResp {
    repeated uint32 channel_id_list           = 1;
    bool bottom_reached                       = 2;
    rcmd.common.LocationInfo self_loc         = 3;
    map<uint32, ChannelInfo> channel_info_map = 4;
}


message GetSelectPageMusicChannelRcmdReq {
    uint32 uid = 1;
    string tab_name = 2;				//一级分类标签
    string elem_name = 3;					//二级主题分类标签-废弃
    repeated string promotion_label = 4; 	//所有投放素材标签
    uint32 market_id = 5;        			// 参考ServiceInfo中的market_id,识别APP
    repeated string elem_names = 6;					//二级主题分类标签-数组
}

message GetSelectPageMusicChannelRcmdResp {
    uint32 channel_id = 1;
    string meta_id = 2;	 //透传给客户端,客户端上报到数仓
    uint32 err_code = 3;
    string err_msg = 4;
}

message GetMusicChannelPopupRcmdReq {
    uint32 uid = 1;
    uint32 market_id = 2;        			// 参考ServiceInfo中的market_id,识别APP
}

message GetMusicChannelPopupRcmdResp {
    uint32 channel_id = 1;
    string meta_id = 2;	 //透传给客户端,客户端上报到数仓
    uint32 friend_uid = 3; //为0时表示非关系链房间
    repeated uint32 relation_uids = 4; // 重逢的用户列表
}

message GetCollectedChannelReq {
    uint32 uid = 1;
    uint32 limit = 2;	//返回个数
    uint32 client_type = 3;             // 参考ServiceInfo中的client_type
    uint32 client_version = 4;          // 参考ServiceInfo中的client_version
    uint32 market_id = 5;               // 参考ServiceInfo中的market_id
    string channel_package_id = 6;		// 渠道包ID
    uint32 sex = 7; //性别过滤 0-不过滤 1-选择Male 2-选择Female
    bool is_first_get = 8; //是否首刷
    music_channel.FilterType filter_type = 9;
    repeated uint32 expose_channel_id_list = 10; //上一刷曝光的房间id
    repeated uint32 not_expose_channel_id_list = 11; //未曝光的房间ID
}

message GetCollectedChannelResp {
    repeated uint32 channel_id_list           = 1;
    bool bottom_reached                       = 2;
    rcmd.common.LocationInfo self_loc         = 3;
    map<uint32, music_channel.ChannelInfo> channel_info_map = 4;
}

// 获取房间内重逢用户列表
message GetRoomRelationUidsReq {
    uint32 req_uid = 1; // 请求id
    repeated uint32 room_uids = 2; // 房间内用户列表
    string enter_source = 3; //调用来源
}

message GetRoomRelationUidsResp {
    repeated RelationItem relation_items = 1; // 重逢用户列表
}

message RelationItem {
    uint32 uid = 1; //重逢用户
    string meta_id = 2;	 //透传给客户端,客户端上报到数仓
    string last_timestamp = 3; //最近一次重逢的时间戳
    string channel_topic = 4; //最近一次重逢的房间主题
    string song_name = 5; //最近一次重逢一起合唱的歌（一起K歌才有）
}

message GetThemeChannelPopupRcmdReq {
    uint32 uid = 1;
    uint32 market_id = 2;        			// 参考ServiceInfo中的market_id,识别APP
    string req_source = 3; //标识请求来源
}

message GetThemeChannelPopupRcmdResp {
    uint32 channel_id = 1;
    string meta_id = 2;	 //透传给客户端,客户端上报到数仓
    repeated uint32 relation_uids = 3; // 关注的用户列表
    repeated uint32 reunion_uids = 4; // 重逢的用户列表
    repeated RCMDLabel rcmd_labels = 5; // 推荐标签
}

message NotifyChannelLiveStatusReq {
    uint32 channel_id = 1;
    bool live_status = 2;
}

message NotifyChannelLiveStatusResp {
    bool is_success = 1;
}


message GetChannelsLocationReq {
    uint32 uid = 1;
    repeated uint32 channel_id_list = 2;
}

message ChannelLocationInfo {
    string channel_city = 1; // 外显城市名
    map<uint32, string> user_location_map = 2; // 房主+麦上 同一优先级城市名
}

message GetChannelsLocationResp {
    map<uint32, ChannelLocationInfo> channel_location_info = 1; //cid若不存在同城信息,则cid不在该map
}

message UserSetToVIPReq {
    repeated string uid_list = 1;
}

message UserSetToVIPResp {

}

enum STRATEGY {
    INVALID = 0;
    THEME_POP = 1; //主题房弹窗下发
    USER_INVITE = 2; //好友邀请进房
    QUEUE_PERSONA = 3; //查询画像
}

enum PERSONA {
    u_mt_probability_not_enter_7d = 0; //潜在非进房用户-算法规则
    u_mt_probability_not_enter_pred_score = 1; //潜在非进房用户预测-数分预测分数
    u_mt_pgc_not_enter_7d = 2; //潜在非进房用户并且最近7日未进过PGC房用户
}

message GetABTestStrategyReq {
    STRATEGY strategy_name = 1;
    uint32 uid = 2;

    repeated PERSONA persona_fields = 3; //需要查询的画像
    repeated string persona_keys = 4; //需要查询的画像keys
}

// buf:lint:ignore ENUM_PASCAL_CASE
enum THEME_POP_UI {
    OLD = 0;
    NEW = 1;
}

// buf:lint:ignore ENUM_PASCAL_CASE
enum USER_TYPE {
    OLD_USER = 0;
    RECALL_USER = 1;
    NEW_USER = 2;
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
message THEME_POP_STRATEGY {
    THEME_POP_UI ui_version = 1;
    bool if_req_in_im = 2; //IM req

    USER_TYPE user_type =3;
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
message USER_INVITE_STRATEGY {
    bool hit_experimental_group = 1; //是否命中实验组
}

message GetABTestStrategyResp {
    STRATEGY strategy_name = 1;
    THEME_POP_STRATEGY theme_pop_strategy = 2; //策略参数
    bool hit_group_rule = 3; //是否命中圈定用户人群

    USER_INVITE_STRATEGY user_invite_strategy = 4;

    bool if_sure_user_group = 5; //是否已可确定用户人群包， true则可提前停止轮询调用

    map<string, PersonaData> persona_data = 6; //uid: field:value
    message PersonaData {
        map<int32,string> data_map = 1;
    }
}


//音乐房间流推荐接口
service MusicChannel {
    rpc GetMusicChannelRecommendation (GetMusicChannelRecommendationReq) returns (GetMusicChannelRecommendationResp);	//音乐房推荐流，筛选流，快速匹配
    rpc GetSelectPageMusicChannelRcmd (GetSelectPageMusicChannelRcmdReq) returns (GetSelectPageMusicChannelRcmdResp);	// 新用户承接页，快速开玩页
    rpc GetMusicChannelPopupRcmd (GetMusicChannelPopupRcmdReq) returns (GetMusicChannelPopupRcmdResp); 	//音乐房间弹窗推荐
    rpc GetCollecttedChannel (GetCollectedChannelReq) returns (GetCollectedChannelResp);	//收藏页房间
    rpc GetRoomRelationUids (GetRoomRelationUidsReq) returns (GetRoomRelationUidsResp); // 获取房间内重逢的用户列表
    rpc GetThemeChannelPopupRcmd (GetThemeChannelPopupRcmdReq) returns (GetThemeChannelPopupRcmdResp); 	// 主题房弹窗推荐
    rpc NotifyChannelLiveStatus (NotifyChannelLiveStatusReq) returns (NotifyChannelLiveStatusResp);	// 通知房间live状态
    rpc GetChannelsLocation (GetChannelsLocationReq) returns (GetChannelsLocationResp); // 查询房间同城信息
    rpc UserSetToVIP (UserSetToVIPReq) returns (UserSetToVIPResp); //用户在运营后台被设置为VIP用户，服务端定期同步
    rpc GetABTestStrategy (GetABTestStrategyReq) returns (GetABTestStrategyResp); //用户AB策略接口
}
