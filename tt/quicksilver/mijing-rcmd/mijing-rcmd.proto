syntax = "proto3";

package mijing_rcmd;

option go_package = "golang.52tt.com/protocol/services/mijing_rcmd";

import "tt/quicksilver/extension/options/options.proto";

service MijingRcmd {
    option (service.options.service_ext) = {
        service_name: "mijing-rcmd"
    };

    // 根据uid获取搭子卡片
    rpc GetMijingPartnerCardListByUid(GetMijingPartnerCardListByUidRequest) returns (GetMijingPartnerCardListByUidResponse) {}
    // 根据id获取搭子卡片
    rpc GetMijingPartnerCardListById(GetMijingPartnerCardListByIdRequest) returns (GetMijingPartnerCardListByIdResponse) {}
    // 操作（新建、更新等）搭子卡片
    rpc OperateMijingPartnerCard(OperateMijingPartnerCardRequest) returns (OperateMijingPartnerCardResponse) {}
    // 卡片推荐规则获取
    rpc GetMijingPartnerCardRule(GetMijingPartnerCardRuleRequest) returns (GetMijingPartnerCardRuleResponse) {}
    // 卡片推荐规则新增或更新
    rpc UpdateMijingPartnerCardRule(UpdateMijingPartnerCardRuleRequest) returns (UpdateMijingPartnerCardRuleResponse) {}
    // 搭一下
    rpc MijingAccostPartnerCard(MijingAccostPartnerCardRequest) returns (MijingAccostPartnerCardResponse) {}
    // 搭子行为曝光
    rpc MijingExposurePartnerCard(MijingExposurePartnerCardRequest) returns (MijingExposurePartnerCardResponse) {}
    // 擦亮搭子卡片
    rpc MijingIncrExposurePartnerCard(MijingIncrExposurePartnerCardRequest) returns (MijingIncrExposurePartnerCardResponse) {}
    // 获得推荐的搭子卡片
    rpc GetMijingRcmdPartnerCardList(GetMijingRcmdPartnerCardListRequest) returns (GetMijingRcmdPartnerCardListResponse) {}
    // 设置用户登录事件
    rpc SetMijingRcmdPartnerCardLogin(SetMijingRcmdPartnerCardLoginRequest) returns (SetMijingRcmdPartnerCardLoginResponse) {}
    // 获取随机推荐的搭子卡片
    rpc GetMijingRandomRcmdPartnerCardList(GetMijingRandomRcmdPartnerCardListRequest) returns (GetMijingRandomRcmdPartnerCardListResponse) {}

    // 封禁搭子卡片
    rpc MijingBanPartnerCard(MijingBanPartnerCardRequest) returns (MijingBanPartnerCardResponse) {}
    // 卡片虚拟形象申请新增或更新
    rpc OperateMijingPartnerCardAvatar(OperateMijingPartnerCardAvatarRequest) returns (OperateMijingPartnerCardAvatarResponse) {}
    // 获取卡片虚拟形象申请
    rpc GetMijingPartnerCardAvatar(GetMijingPartnerCardAvatarRequest) returns (GetMijingPartnerCardAvatarResponse) {}

    // 泼墨体搭子形象生成CallBack
    rpc MijingGenerateAvatarCallBack(MijingGenerateAvatarCallBackRequest) returns (MijingGenerateAvatarCallBackResponse) {}
}

// = = = = = 「Begin」谜境搭子需求 = = = = =
// 搭子标签颜色
enum LabelColourType {
  LABEL_COLOUR_TYPE_UNSPECIFIED = 0;
  LABEL_COLOUR_TYPE_NOCOLOR = 1;  // 无颜色
  LABEL_COLOUR_TYPE_PINK = 2;  // 粉色
  LABEL_COLOUR_TYPE_GREEN = 3;  // 绿颜色
  LABEL_COLOUR_TYPE_PURPLE = 4;  // 紫色
  LABEL_COLOUR_TYPE_BLUE = 5;  // 蓝色
}

message LabelNameInfo{
    string name = 1;  //标签名字
    string img_url = 2;//标签图片路径
    LabelColourType colour_type = 3;  // 颜色
}

//搭子卡片的状态
enum CardStatusType {
  CARD_STATUS_TYPE_UNSPECIFIED = 0;
  CARD_STATUS_TYPE_INIT = 1;  // 初始化/送审中
  CARD_STATUS_TYPE_VIO = 11;  //审核违规
  CARD_STATUS_TYPE_SUCCESS = 20;  //卡片发布成功
}

//用户搭子卡片行为的状态
enum CardActionType {
  CARD_ACTION_TYPE_UNSPECIFIED = 0;
  CARD_ACTION_TYPE_OK = 1;  // 正常
  CARD_ACTION_TYPE_BAN = 10;  //拉黑名单
}

//搭子卡片信息
message MijingPartnerCardInfo{
    string card_id = 1; //搭子卡片id
    uint32 uid = 2;//卡片用户的uid
    repeated uint32 play_mode = 3;//寻找的玩本方式 1-语音交流，2-文字交流
    repeated LabelNameInfo label_info_list = 4; // 自选标签信息
    repeated LabelNameInfo partner_info_list = 5; //搭子的信息
    string custom_text = 6;// 自定义文案
    uint32 created_at = 7;//搭子卡片的发布时间
    CardStatusType status = 8;//搭子卡片状态
    CardActionType action = 9;//卡片行为状态
    uint32 exposure_count = 10;//曝光次数
    uint32 accost_count = 11;//被搭次数
    uint32 play_count = 12;//剩余滑动次数
    string avatar_img_url = 13;// 用户虚拟形象
}

//搭子卡片获取，运营后台查询卡片信息也是这个接口
message GetMijingPartnerCardListByUidRequest{
  repeated uint32 uid_list = 1;//uid
  uint32 limit = 2; //限制
  uint32 offset = 3; //偏移量
}

message GetMijingPartnerCardListByUidResponse{
  repeated MijingPartnerCardInfo records = 1;
  uint32 total = 2; //搭子卡片总条数
}

//搭子卡片获取
message GetMijingPartnerCardListByIdRequest{
  repeated string id_list = 1;             // 卡片id列表
  repeated CardStatusType card_status = 2; // 卡片状态
}

message GetMijingPartnerCardListByIdResponse{
  repeated MijingPartnerCardInfo records = 1;
}

//更新搭子卡片信息
message OperateMijingPartnerCardRequest{
  MijingPartnerCardInfo info = 1;//用户操作的搭子卡片信息
}

message OperateMijingPartnerCardResponse{
  string card_id = 1; //搭子卡片id
}

// 指标的类型
enum IndexType {
  INDEX_TYPE_UNSPECIFIED = 0;
  INDEX_TYPE_ACTIVE = 1;  // 活跃状态
  INDEX_TYPE_UPDATE = 2;  // 更新状态
  INDEX_TYPE_WELCOME = 3;  //受欢迎状态
  INDEX_TYPE_ACCOST = 4;  //搭讪状态
  INDEX_TYPE_ACTION = 5;  //被动行为
}

// 活跃指标的数值
enum MijingActiveType {
  MIJING_ACTIVE_TYPE_UNSPECIFIED = 0;
  MIJING_ACTIVE_TYPE_IN = 1;  // 在功能内
  MIJING_ACTIVE_TYPE_ONLINE = 2;  // 在线
  MIJING_ACTIVE_TYPE_0TO24 = 3;  //0-24小时登录过
  MIJING_ACTIVE_TYPE_24TO72 = 4;  //24-72小时登录过
  MIJING_ACTIVE_TYPE_72TO = 5;  //72-15*24小时
}

// 更新指标的数值
enum MijingUpdateType {
  MIJING_UPDATE_TYPE_UNSPECIFIED = 0;
  MIJING_UPDATE_TYPE_CREATE = 1;  // 当天首次创建
  MIJING_UPDATE_TYPE_0TO30 = 2;  // 0-30分钟更新
  MIJING_UPDATE_TYPE_30TO60 = 3;  //30-60分钟更新
  MIJING_UPDATE_TYPE_60TO180 = 4;  //60-180分钟更新
  MIJING_UPDATE_TYPE_H24 = 5;  //180分钟-24小时更新
  MIJING_UPDATE_TYPE_H24TO72 = 6;  //24小时-72小时更新
  MIJING_UPDATE_TYPE_H72 = 7;  //72小时之前更新
}

// 受欢迎程度的数值
enum MijingWelcomeType {
  MIJING_WELCOME_TYPE_UNSPECIFIED = 0;
  MIJING_WELCOME_TYPE_70 = 1;  // 100%-70%
  MIJING_WELCOME_TYPE_70TO40 = 2;  // 70%-40%
  MIJING_WELCOME_TYPE_40TO10 = 3;  //40%-10%
  MIJING_WELCOME_TYPE_10TO0 = 4;  //10%-0%
}

// 搭人意向的数值
enum MijingAccostType {
  MIJING_ACCOST_TYPE_UNSPECIFIED = 0;
  MIJING_ACCOST_TYPE_70 = 1;  // 100%-70%
  MIJING_ACCOST_TYPE_70TO40 = 2;  // 70%-40%
  MIJING_ACCOST_TYPE_40TO10 = 3;  //40%-10%
  MIJING_ACCOST_TYPE_10TO0 = 4;  //10%-0%
}

// 被动的数值
enum MijingActionType {
  MIJING_ACTION_TYPE_UNSPECIFIED = 0;
  MIJING_ACTION_TYPE_YES = 1;  // 搭过
  MIJING_ACTION_TYPE_NO = 2;  // 未搭过
}

//指标的属性
message MijingRuleInfo{
  string index_name = 1;//指标名字
  IndexType type = 2;//指标的类别IndexType，根据不同的指标类型，对应不同的值
  MijingActiveType active = 100;//指标的类别-INDEX_TYPE_ACTIVE
  MijingUpdateType update = 101;//指标的类别-INDEX_TYPE_UPDATE
  MijingWelcomeType welcome = 102;//指标的类别-INDEX_TYPE_WELCOME
  MijingAccostType accost = 103;//指标的类别-INDEX_TYPE_ACCOST
  MijingActionType action = 104;//指标的类别-INDEX_TYPE_ACTION
}

//搭子推荐位置的规则属性
message MijingPartnerCardRuleInfo{
  string id = 1; //规则唯一的id,用于更新操作使用
  uint32 rule_id = 2; //卡片位置id
  repeated MijingRuleInfo rule_list = 3; // 指标列表
  uint32 card_count = 4;//满足条件卡片数量
  uint32 provide_max = 5;//供需比最大值
  uint32 provide_min = 6;//供需比最小值
  uint32 rule_type = 7;//规则队列的类型：1-活跃用户，2-新用户
}

//卡片推荐规则获取
message GetMijingPartnerCardRuleRequest{
}

message GetMijingPartnerCardRuleResponse{
  repeated MijingPartnerCardRuleInfo infos = 1;
}

//卡片推荐规则新增或更新
message UpdateMijingPartnerCardRuleRequest{
   MijingPartnerCardRuleInfo info = 1;
}

message UpdateMijingPartnerCardRuleResponse{

}

message MijingAccostPartnerCardRequest{
  uint32 uid = 1; // from_uid
  repeated string card_ids = 2; //搭子卡片id
}

message MijingAccostPartnerCardResponse{}


// 用户行为
enum UserCardActionType {
  USER_CARD_ACTION_TYPE_UNSPECIFIED = 0;
  USER_CARD_ACTION_TYPE_LOOK = 1;  // 查看别人搭子卡片
  USER_CARD_ACTION_TYPE_ENTER = 2;  // 进入找搭子功能
  USER_CARD_ACTION_TYPE_LEAVE = 3;  // 离开找搭子功能
  USER_CARD_ACTION_TYPE_PASS = 4;  // 左滑别人搭子卡片（不匹配）
}

//搭子行为
message MijingExposurePartnerCardRequest{
  uint32 uid = 1; //uid
  UserCardActionType  action_type = 2;//行为代码
  string card_id = 3; //搭子卡片id：传入自己的卡片id
  string target_card_id = 4; //搭子卡片id：目标行为的卡片id
}

message MijingExposurePartnerCardResponse{
  
}

//擦亮搭子卡片
message MijingIncrExposurePartnerCardRequest{
  string card_id = 1; //搭子卡片id
}

message MijingIncrExposurePartnerCardResponse{
  bool result = 1;
}

//获取推荐搭子卡片id 列表
message GetMijingRcmdPartnerCardListRequest{
  uint32 uid = 1; //uid
  uint32 count = 2; //个数
  repeated uint32 filter_uid_list = 3;//需要过滤的uid
}

message GetMijingRcmdPartnerCardListResponse{
  repeated string card_id_list = 1; //搭子卡片id list
}

//谜境用户登录
message SetMijingRcmdPartnerCardLoginRequest{
  uint32 uid = 1; //uid
  uint32 login_type = 2;//用户登录的类别
  uint32 status = 3;//用户在线状态
}

message SetMijingRcmdPartnerCardLoginResponse{
  
}

//随机获取推荐搭子卡片id 列表
message GetMijingRandomRcmdPartnerCardListRequest{
  uint32 uid = 1; //uid
  uint32 random_count = 2; //随机获取的个数
}

message GetMijingRandomRcmdPartnerCardListResponse{
  repeated string card_id_list = 1; //搭子卡片id list
}

//封禁搭子卡片
message MijingBanPartnerCardRequest{
  string card_id = 1; //搭子卡片id
}

message MijingBanPartnerCardResponse{
 
}

// = = = = = 「End」谜境搭子需求 = = = = =


// = = = = = 「Begin」谜境搭子卡片虚拟形象需求 = = = = =
// 形象照片生成模式
enum PromptGenType {
  PROMPT_GEN_TYPE_UNSPECIFIED = 0;
  PROMPT_GEN_TYPE_INIT = 1;  // 首次生成
  PROMPT_GEN_TYPE_REPEAT = 2;  // 重复生成
}

// 形象照片生成状态
enum PromptGenAvatarStatus {
  PROMPT_GEN_AVATAR_STATUS_UNSPECIFIED = 0;
  PROMPT_GEN_AVATAR_STATUS_INIT = 1;  // 初始化
  PROMPT_GEN_AVATAR_STATUS_CHECKOK = 2;  //上传头像检测通过
  PROMPT_GEN_AVATAR_STATUS_DOING = 3;  // 正在生成中
  PROMPT_GEN_AVATAR_STATUS_CANCEL = 4;  // 生成操作用户取消
  PROMPT_GEN_AVATAR_STATUS_SUCCES = 5;  // 生成完成
  PROMPT_GEN_AVATAR_STATUS_FAIL = 6;  // 生成或检查失败
}

// 泼墨体产生虚拟形象错误原因
enum PromptErrorCode {
  PROMPT_ERROR_CODE_UNSPECIFIED = 0;
  PROMPT_ERROR_CODE_NOFACE = 1;  // 无人脸
  PROMPT_ERROR_CODE_BAN = 2;  // 上传的图片审核不过
}

//搭子卡片虚拟形象信息
message MijingPartnerCardAvatarInfo{  
  string avatar_id = 1;//唯一id
  uint32 uid = 2; // uid
  string native_img_url = 3;//原生图像
  PromptGenType gen_type = 4;//prompt生成图片的模式
  PromptGenAvatarStatus status = 5;//prompt生成图片的进度状态
  PromptErrorCode reason_code = 6;//当生成失败的话，会注明原因
  repeated string avatar_img_list = 7;//prompt返回的形象照片（最多四张）
  string img_tags = 8;//泼墨体返回的参数，暂时透传即可
  repeated float tags_probs = 9;//泼墨体返回的参数，暂时透传即可
  string img_id = 10;//泼墨体返回的参数，暂时透传即可
}

//更新或者新增搭子虚拟形象申请信息
message OperateMijingPartnerCardAvatarRequest{
  MijingPartnerCardAvatarInfo info = 1;//搭子卡片虚拟形象信息
  bool clear = 2;//再次生成需要清空上一次的虚拟形象照片
}

message OperateMijingPartnerCardAvatarResponse{
  string avatar_id = 1; //个人虚拟形象id
}

//搭子生成形象申请获取
message GetMijingPartnerCardAvatarRequest{
  string id_list = 1;             
}

message GetMijingPartnerCardAvatarResponse{
  MijingPartnerCardAvatarInfo info = 1;
}

//泼墨体搭子形象生成CallBack
message MijingGenerateAvatarCallBackRequest{
  string avatar_id = 1; //生成图像请求的id
  repeated string img_url = 2;//形象图片url
  uint32 code = 3; //异常的错误码
}

message MijingGenerateAvatarCallBackResponse{
 
}

// = = = = = 「End」谜境搭子卡片虚拟形象需求 = = = = =
