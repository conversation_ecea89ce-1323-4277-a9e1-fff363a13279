syntax = "proto3";

// buf:lint:ignore PACKAGE_DIRECTORY_MATCH
// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package com.quwan.dspiopother.proto;
// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/iop-iaa-ad";

option java_package = "com.quwan.dspiopother.proto";

service IAARevenueAdService {
  rpc GetAdPosInfos(GetAdPosInfosReq) returns (GetAdPosInfosResp) {}
  rpc GetDspAdInfos(GetAdPosInfosReq) returns (GetAdPosInfosResp) {}
}

// buf:lint:ignore ENUM_PASCAL_CASE
enum E_OsType {
  E_OsType_Unknown = 0;
  E_OsType_Android = 1;
  E_OsType_Ios = 2;
}

message GetAdPosInfosReq {
  string app_name = 1;
  string app_type = 2;
  uint32 uid = 3;
  string bundle_id = 4; //包名
  uint32 os = 5;    // E_OsType
  string client_ver = 6;    //客户端版本号
}

//广告位信息
message AdPosInfo {
  string cli_pos_id = 1;    //位置ID(ad_flash..)
  string ad_pos_id = 2;     //广告位ID
  bytes  params = 3;        //参数: 广场-信息流参数为：AdFeedSquareParam， 个人主页动态参数为：AdFeedHomePageDynamicParam
  string ad_app_id = 4;     //广告应用ID
  string iop_param = 5;     //iop param
}

message GetAdPosInfosResp{
  repeated AdPosInfo ad_pos_infos = 1;
  string   adn_configs = 2;
}

//信息流-广场 广告位参数(对应位置： ad_feed_square, ad_feed_square_other)
message AdFeedSquareParam {
  uint32 start_pos = 1;               //广告开始位置
  uint32 end_pos = 2;                 //广告结束位置
  uint32 interval_num = 3;            //广告展示间隔数量
  uint32 ad_num = 4;                  //连续插入广告数量
  string tab_name = 5;                //广场-推荐-其它TAB时(ad_feed_square_other) 的tab_name
}

//信息流-个人主页-动态 广告位参数(对应位置：ad_feed_home_dynamic)
message AdFeedHomePageDynamicParam {
  uint32 min_dynamic_num = 1;         //最小动态数量(动态数量大于等于3条)
  uint32 start_pos = 2;               //广告开始位置
  uint32 interval_num = 3;            //广告展示间隔数量
  uint32 ad_num = 4;                  //连续插入广告数量
}
