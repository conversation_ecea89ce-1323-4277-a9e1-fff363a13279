syntax = "proto3";

import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";

package yuyingold;
option go_package = "golang.52tt.com/protocol/services/yuyingold";


//百分数%的开始单位为0.01%
service YuyinGold {

  //通用接口

  //总大体情况
  rpc GetGeneralIncome(GetGeneralIncomeReq)returns(GetGeneralIncomeRsp){}
  //大体主播情况
  rpc GetAnchorInfo(GetGeneralIncomeReq)returns(GetAnchorInfoRsp){}
  //语音经营直播收益
  //通过时间算收益情况
  //今天，昨天收益情况
  rpc GetDateDimenIncome(GetGeneralIncomeReq)returns (GetDateDimenIncomeRsp){}
  //该周收益情况
  rpc GetWeekDimenIncome(GetGeneralIncomeReq)returns (GetWeekDimenIncomeRsp){}
  //该月收益情况
  rpc GetMonthDimenIncome(GetGeneralIncomeReq)returns (GetMonthDimenIncomeRsp){}
  //按月份额外收益
  rpc GetGuildExtraIncome(GetGuildsGeneralIncomeReq)returns (GetGuildExtraIncomeRsp){}

  //获取主播房间统计流水信息
  rpc GetInternelAnchorRoomList(GetInternelAnchorRoomListReq)returns (GetInternelAnchorRoomListRsp){}

  //工会任务入口
  rpc GetGuildTaskList(GetGeneralIncomeReq)returns (GetGuildTaskListRsp){}

  //新版本的v2....begin....................
  rpc GetGuildTaskListV2(GetGeneralIncomeReq)returns (GetGuildTaskListV2Rsp){}
  rpc GetGeneralIncomeV2(GetGeneralIncomeReq)returns (GetGeneralIncomeRsp){}
  rpc GetAnchorInfoV2(GetGeneralIncomeReq)returns (GetAnchorInfoRsp){}
  rpc GetValidAnchorTrendListV2(GetGeneralTrendListReq)returns (GetValidAnchorTrendListRsp){}
  //新版本的v2....end....................

  //收益趋势
  //日/月收益趋势
  rpc GetIncomeTrendList(GetGeneralTrendListReq)returns (GetIncomeTrendListRsp){}
  rpc GetLastIncomeTrendList(GetGeneralTrendListReq)returns (GetIncomeTrendListRsp){}

  //直播数趋势
  //直播数日/月趋势
  rpc GetValidAnchorTrendList(GetGeneralTrendListReq)returns (GetValidAnchorTrendListRsp){}

  //主播列表
  rpc GetAnchorList(GetGeneralIncomeReq)returns (GetAnchorListRsp){}

  // 高消费
  rpc GetConsumeRank(ConsumeRankReq)returns (ConsumeRankRsp){}

  // 详情
  rpc GetGuildUnSettlementSummary (GuildUnSettlementSummaryReq) returns (GuildUnSettlementSummaryRsp) {
  }

  rpc GetGuildsUnSettlementSummary (GetGuildsUnSettlementSummaryReq) returns (GetGuildsUnSettlementSummaryRsp) {
  }

  // PendingDetailHandler
  rpc GetUnSettlementDetals (GuildUnSettlementDetailReq) returns (GuildUnSettlementDetailRsp) {
  }

  // 二级页
  rpc GetGuildIncomeDetails (GuildIncomeDetailReq) returns (GuildIncomeDetailRsp) {
  }

  // anchorList 主播列表
  rpc GetGuildAnchorList (GetGuildAnchorListReq) returns (GetGuildAnchorListRsp) {
  }


  // 公会日流水收益对比
  rpc GuildConsumeDayQoq (GuildConsumeDayQoqReq) returns (GuildConsumeDayQoqRsp) {
  }


  // 今日收益
  rpc GetGuildTodayIncomeInfo (GuildTodayIncomeReq) returns (GuildTodayIncomeRsp) {
  }

  // 本月收益
  rpc GetGuildMonthTrendInfo (GuildMonthIncomeReq) returns (GuildMonthIncomeRsp) {
  }

  //对帐
  rpc GetAllHandlePresentCount (GetAllHandlePresentCountReq) returns (GetAllHandlePresentCountResp) {}

  rpc GetAllHandlePresentOrderList (GetAllHandlePresentOrderListReq) returns (GetAllHandlePresentOrderListResp) {}

  rpc AddOrder (AddOrderReq) returns (AddOrderResp) {}

  // 获取语音基础收益详情，通过时间段
  rpc GetGuildsBaseIncomeInfo (GetGuildsBaseIncomeInfoReq) returns (GetGuildsBaseIncomeInfoRsp) {}

  //获取时间范围内的语音房金钻订单数量和金额
  rpc GetGoldOrderCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
  //获取时间范围内的订单列表
  rpc GetGoldOrderList(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
  //补单
  rpc ReplaceOrder(ReconcileV2.ReplaceOrderReq) returns (ReconcileV2.EmptyResp) {}
}



/////////////////////////////////////////
message GetGeneralIncomeReq {
  uint32 uid = 1;
  uint32 guildid = 2;
  uint32 page_num = 3;
  uint32 page_size = 4;
  int64  begin_time = 5;
  int64  end_time = 6;
}

message GetGuildsGeneralIncomeReq {
  uint32 uid = 1;
  repeated uint32 guild_ids = 2;
  uint32 page_num = 3;
  uint32 page_size = 4;
  int64  begin_time = 5;
  int64  end_time = 6;
  ReqSettleStatus settle_status = 7; // 是否已结算
}

message GetGeneralIncomeRsp {
  GetAnchorInfoRsp anchor_info = 1;
  GetDateDimenIncomeRsp date_dimen_income = 2;
  GetWeekDimenIncomeRsp week_dimen_income = 3;
  GetMonthDimenIncomeRsp month_dimen_income = 4;
}





message GetAnchorInfoRsp {
  uint64 anchor_num = 1;//签约主播数
  uint64 valid_anchor_num = 2;//月有效主播数
  uint64 valid_anchor_dead_line = 3;
  uint64 month_valid_anchor_num = 4;//月新增有效主播数
  uint64 month_valid_anchor_dead_line = 5;
  uint64 star_anchor_num = 6;//星级主播数
  uint64 new_anchor_num = 7;//新主播数
  uint64 potential_anchor_num = 8;//潜力主播
}



message GetDateDimenIncomeRsp {
  uint64 today_income = 1;
  uint64 yesterday_income = 2;
  float lastday_qoq = 3;
}



message GetWeekDimenIncomeRsp {
  uint64 this_week_income = 1;
  uint64 last_week_income = 2;
  int64 current_time = 3;
  int64 week_start_time = 4;
  int64 week_end_time = 5;
}


message GetMonthDimenIncomeRsp {
  uint64 this_month_income = 1;//该月收入
  uint64 last_month_income = 2;//上一个月收入
  float month_qoq = 3;//上月同期
  uint64 same_last_month_income = 4;//
  uint64 last_month_valid_income_num = 5;
  uint64 month_six_income = 6;
}


message GetGeneralTrendListReq {
  uint32 uid = 1;
  uint32 guildid = 2;
  RangeType type_time = 3;
  int64 begin_time = 4;
  int64 end_time = 5;
}


message TrendInfo {
  uint64 value = 1;
  string key = 2;
}

message GetIncomeTrendListRsp {
  repeated TrendInfo list = 1;
}



message GetValidAnchorTrendListRsp {
  repeated TrendInfo list = 1;
}


message GetInternelAnchorRoomListReq {
  repeated uint32 uid_list = 1;
  uint32 guildid = 2;
  uint32 page_num = 3;
  uint32 page_size = 4;
  int64  begin_time = 5;
  int64  end_time = 6;
}

message InternelRoom {
  uint32 anchor_id = 1;
  uint32 fees = 2;
  uint32 incomes = 3;
  uint32 room_id = 4;
}


//获取统计的主播房间列表信息
message GetInternelAnchorRoomListRsp {
  repeated InternelRoom room_list = 1;
}



//额外收益+当月公会任务奖励分成比例 的记录
//额外收益记录
message ExtraIncome {
  uint64 month_record_income = 1;
  uint32 extra_income_ratio = 2;
  string key = 3;
  string extra_income_ratio_v2 = 4;
  uint32 guild_id = 5;
}

message GetGuildExtraIncomeRsp {
  repeated ExtraIncome list = 1;
}


message GuildTaskInfo {
  uint32 status = 1;
  uint32 finish = 4;
  uint32 current = 5;
}

message GetGuildTaskListRsp {
  repeated GuildTaskInfo list = 1;
  int64 remain_time = 2;
}


message GuildTaskInfoV2 {
  uint64 value = 6;//当前情况
  string ratio = 7;//当前比例
}

message GetGuildTaskListV2Rsp {
  repeated GuildTaskInfoV2 list = 1;
  int64 remain_time = 2;
  string buff_total = 3;//比例数目
}





message AnchorInfo {
  string account = 1;
  string nickname = 2;
  uint64 consume = 3;
  float month_to_now_qoq = 4;
  uint32 uid = 5;
  string alias = 6;
}

message GetAnchorListRsp {
  repeated AnchorInfo list = 1;
}


// form golddiamon
enum GoldIncomeType {
  UNKNOW_TYPE = 0;
  GAME_INCOME = 1;
  GAME_SETTLEMENT = 4;
  ANCHOR_INCOME = 6;
  ANCHOR_SETTLEMENT = 7;
};


enum RangeType {
  DAY_RANGE_TYPE = 0;
  MONTH_RANGE_TYPE = 1;
}

enum QueryType {
  NONE_TYPE = 0;
  ANCHOR_ID_TYPE = 1;
  PAID_UID_TYPE = 2;
}

enum TimeFilterUnit {
  BY_DAY = 0;
  BY_WEEK = 1;
  BY_MONTH = 2;
}

// 请求结算状态 (实际转换为 0 未结算 1 已结算)
enum ReqSettleStatus {
  ReqSettleStatusALL = 0; // 所有状态
  ReqSettleStatusWait = 1; // 待结算
  ReqSettleStatusFinished = 2; // 已结算
}

//enum IncomeType {
//    NONE_INDEX = 0;
//    GAME_RECHARGE_REBATE = 1; 			// 通过游戏充值返利 获得金钻
//    GUILD_SYS_TASK = 2;       			// 通过完成公会系统任务 获得金钻
//    GUILD_OPE_TASK = 3;       			// 通过完成公会运营任务 获得金钻
//    SETTLEMENT = 4;           			// 通过 系统结算 一般是扣除金钻
//    GUILD_WELFARE_FIRST_JOIN = 5;    	// 通过参加会长福利活动——新增有效成员 获得金钻
//    CHANNEL_SEND_GIFT = 6;				// 开黑房间送礼物 获得金钻
//    CHANNEL_SETTLEMENT = 7;				// 房间收益金钻结算
//}

//message AwardDiamondReq {
//    uint32 guild_id = 1;
//    uint32 paid_uid = 2;
//    uint64 bought_time = 3;
//    IncomeType source_type = 4;
//    int64 income = 5;
//    string order_id = 6;
//    string uniq_sign = 7;
//    string desc = 8;
//    string extand = 9;
//}
//
//message AwardDiamondRsp {
//}

message ConsumeRankItem {
  string alias = 1;
  string nick_name = 2;
  string account = 3;
  int64 consume = 4;
}

message ConsumeRankReq {
  RangeType consume_type = 1;
  uint32 guild_id = 2;
}

message ConsumeRankRsp {
  repeated ConsumeRankItem consume_rank_list = 1;
}

message GuildUnSettlementSummaryReq {
  uint32 guild_id = 1;
}

message GuildUnSettlementSummaryRsp {
  int64 paid_uid_cnt = 1;
  int64 total_income = 2;
  int64 total_fee = 3;
  int64 income_balance = 4;
  int64 begin_time = 5;
  int64 end_time = 6;
}

message GetGuildsUnSettlementSummaryReq {
  repeated uint32 guild_ids = 1;
}

message GetGuildsUnSettlementSummaryRsp {
  int64 today_fee = 1;
  int64 today_income = 2;
  int64 month_fee = 3;
  int64 month_income = 4;
}

message GuildUnSettlementDetailReq {
  uint32 guild_id = 1;
  QueryType query_type = 2;
  uint32 offset = 3;
  uint32 limit = 4;
}

message GuildUnSettlementDetail {
  uint32 anchor_id = 1;
  int64 fee = 2;
  int64 income = 3;
  uint32 paiduid = 4;
}

message GuildUnSettlementDetailRsp {
  repeated GuildUnSettlementDetail details = 1;
  bool next_page = 2;
}

message GuildIncomeDetailReq {
  uint32 guild_id = 1;
  uint32 offset = 2;
  uint32 limit = 3;
  int64 begin_time = 4;
  int64 end_time = 5;
  uint32 paid_uid = 7;
  uint32 anchor_id = 8;
}

message GuildIncomeDetail {
  int64 fee = 1;
  int64 income = 2;
  uint32 paiduid = 3;
  int64 bought_time = 4;
  uint32 anchor_uid = 5;
}

message GuildIncomeDetailRsp {
  repeated GuildIncomeDetail income_details = 1;
  bool next_page = 2;
}

message GetGuildAnchorListReq{
  uint32 guildid = 1;
}

message GuildAnchorSimpleInfo{
  uint32 uid = 1;
  string name = 2;
}
message GetGuildAnchorListRsp{
  repeated GuildAnchorSimpleInfo list = 1;
}

// 请求的是收益
message GuildConsumeDayQoqReq {
  uint32 guild_id = 2;
  int64 day = 4;
}

message GuildConsumeDayQoqRsp {
  int64 stat_time = 1;
  int64 consume = 2;
  int64 compare_stat_time = 3;
  int64 compare_consume = 4;
  float qoq = 5;
}

message GuildTodayIncomeReq {
  uint32 guild_id = 1;
}

message GetGuildsBaseIncomeInfoReq {
  repeated uint32 guild_ids = 1;
  uint32 begin_time = 2;
  uint32 end_time = 3;
  ReqSettleStatus settle_status = 4; // 是否已结算
  TimeFilterUnit unit = 5;
  uint32 offset = 6;
  uint32 limit = 7;
}

message BaseAnchorInfo {
  uint32 guild_id = 1;
  uint32 anchor_id = 2;
  int64 fee = 3;
  int64 income = 4;
  uint32 date = 5;
}
message GetGuildsBaseIncomeInfoRsp {
  repeated BaseAnchorInfo anchors = 1;
}

message StatTodayRoomIncomeInfo {
  uint32 anchor_id = 1;
  int64 members = 2;
  int64 incomes = 3;
  int64 fees = 4;
  uint32 guild_id = 5;
}

message GuildTodayIncomeRsp {
  repeated StatTodayRoomIncomeInfo stat_room_info = 1;
}

message GuildMonthIncomeReq {
  uint32 guild_id = 1;
  int64 month = 2;
}

message DayTrendMoreInfo {
  int64 day = 1;
  int64 fee = 2;
  int64 income = 3;
  int64 members = 4;
}

message GuildMonthIncomeRsp {
  repeated DayTrendMoreInfo stat_trend_info = 1;
}

// form golddiamon end


message GetAllHandlePresentCountReq
{
  int64 begin_time = 1;  //开始时间
  int64 end_time = 2;    //结束时间
}

message GetAllHandlePresentCountResp
{
  uint32 total_count = 1;  // 处理礼物总数量
}

message GetAllHandlePresentOrderListReq
{
  int64 begin_time = 1;
  int64 end_time = 2;
}

message GetAllHandlePresentOrderListResp
{
  repeated string orders = 1;
}

message OrderInfoT {
  uint32 uid = 1;
  uint32 target_uid = 2;
  string order_id = 3;
  uint32 channel_id = 4;
  uint32 channel_type = 5;  // ga::ChannelType
  uint32 guild_id = 6;
  uint32 send_time = 7;
  uint32 item_id = 8;
  uint32 item_count = 9;
  uint32 price = 10;
  uint32 price_type = 11;    // userpresent::PresentPriceType
}

message AddOrderReq
{
  OrderInfoT add_order_info = 1;
}

message AddOrderResp
{
  uint32 ret = 2;
}