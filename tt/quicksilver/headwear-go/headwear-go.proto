syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/headwear-go";
package headwear_go;
import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";


service HeadwearGo {
  rpc GetUserHeadwear(GetUserHeadwearReq) returns(GetUserHeadwearResp){
  }

  rpc SetUserHeadwearInUse(SetUserHeadwearInUseReq) returns(EmptyPb){
  }

  rpc GetUserHeadwearInUse(GetUserHeadwearInUseReq) returns(GetUserHeadwearInUseResp){
  }

  rpc GiveHeadwearToUser(GiveHeadwearToUserReq) returns(EmptyPb){
  }

  rpc AddHeadwearConfig(HeadwearConfig) returns(EmptyPb){
  }

  rpc GetHeadwearConfig(HeadwearConfigReq) returns(HeadwearConfig){
  }

  rpc DelHeadwearConfig(HeadwearConfigReq) returns(EmptyPb){
  }

  rpc GetHeadwearConfigAll(EmptyPb) returns(GetHeadwearConfigAllResp){
  }

  rpc GetUserHeadwearInUseList(GetUserHeadwearInUseListReq) returns(GetUserHeadwearInUseListResp){
  }

  rpc RemoveUserHeadwearInUse(RemoveUserHeadwearInUseReq) returns(EmptyPb){
  }

  rpc GetUserHeadwearBySuite(GetUserHeadwearBySuiteReq) returns(UserHeadwearInfo){
  }

  rpc AddUserHeadwearExtraTime(AddUserHeadwearExtraTimeReq) returns(EmptyPb){
  }

  rpc UpdateHeadwearConfig(HeadwearConfig) returns(EmptyPb){
  }

  rpc BatchGiveHeadwearToUser(BatchGiveHeadwearToUserReq) returns(EmptyPb){
  }

  rpc ChangeDecorationCustomText(ChangeDecorationCustomTextReq) returns(ChangeDecorationCustomTextResp){}

  rpc SetCpHeadwearConfig( SetCpHeadwearConfigReq ) returns( EmptyPb ){
  }

  rpc DelCpHeadwearConfig ( DelCpHeadwearConfigReq ) returns (EmptyPb){
  }

  rpc BatchGiveCpHeadwearToUser ( BatchGiveCpHeadwearToUserReq ) returns (EmptyPb){
  }

  //获取时间范围内的背包订单数量和物品数量
  rpc GetBackpackItemUseCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}

  //获取时间范围内的背包订单列表
  rpc GetBackpackItemUseOrder(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
}

message EmptyPb {}

// buf:lint:ignore ENUM_PASCAL_CASE
enum HEADWEAR_TYPE{
  HEADWEAR_TYPE_COMMON = 0;   //普通麦位框
  HEADWEAR_TYPE_CP = 1;   //cp麦位框
}

message HeadwearConfig{
  uint32 headwear_id = 1;
  uint32 suite_id = 2;
  string name = 3;
  string img = 4;           //img url
  uint32 level = 5;
  uint32 holding_time = 6;
  string static_img = 7;      // static img url
  string gif_img = 8;         // gif img url 动态图 不一定是gif格式

  uint32 headwear_type = 9;   // 麦位框类型 见 HEADWEAR_TYPE
  string cp_headwear_img = 10;// cp麦位框 上麦触发动效

  HeadwearCustomType custom_type = 11;
  string notes = 12; // 备注
  HeadwearSourceType source_type = 13; // 资源类型
  string text_color = 14; // 文案的色值
  string cp_notes = 15; //cp麦位备注
  string cp_name = 16; //cp麦位框关系
  string cp_connect_mic_img = 17; //连麦样式
  uint32 cp_id = 18;

  enum HeadwearExtendType{
    HeadwearExtendTypeDefault = 0;
    HeadwearExtendTypeVoice = 1;
  }
  HeadwearExtendType headwear_extend_type = 19;  // 麦位框的扩展类型
  string headwear_source_url = 20; // 新版麦位框资源的url
  string headwear_source_md5 = 21; // 新版麦位框资源的md5
}

enum HeadwearSourceType {
  Unknown = 0;
  Lottie = 1;
  Vap = 2;
}

enum HeadwearUseType {
  Common = 0;
  CPLoser = 1; // cp战失败方 强制佩戴
}

message UserHeadwearInfo{
  uint32 uid = 1;
  uint32 headwear_id = 2;
  uint32 expire_time = 3; //过期时间的timestamp
  uint32 extra_time = 4; //额外增加的秒数，未计入expire_time
  HeadwearConfig headwear_config = 5;
  uint32 cp_uid = 6; // cp对象uid

  uint32 use_type = 7; // see HeadwearUseType

  // 自定义装扮参数
  string custom_text = 8;
  string extend_json = 9; // 额外配置json，客户端用

  enum TextStatus{
    Default = 0;
    UnderReview = 1;  // 审核中
  }
  TextStatus text_status = 10;       //自定义文案状态
  string show_custom_text = 11;  // 用来展示的自定义文案
  uint32 remain_change_count = 12; // 剩余的修改次数
}

message GetUserHeadwearReq{
  uint32 uid = 1;
}

message GetUserHeadwearResp{
  repeated UserHeadwearInfo headwear_list = 1;
  string custom_headwear_text = 2;
}

message GetUserHeadwearBySuiteReq{
  uint32 uid = 1;
  uint32 suite_id = 2;
  uint32 cp_uid = 3;
  string custom_text = 4;
}

message GetHeadwearConfigReq{
  repeated uint32 id_list = 1;
}

message GetHeadwearConfigResp{
  repeated HeadwearConfig headwear_list = 1;
}

message SetUserHeadwearInUseReq{
  uint32 uid = 1;
  uint32 headwear_id = 2;
  uint32 cp_uid = 3;
  string custom_text = 4;
}

message GetUserHeadwearInUseReq{
  uint32 uid = 1;
}

message RemoveUserHeadwearInUseReq{
  uint32 uid = 1;
}

message GetUserHeadwearInUseResp{
  uint32 headwear_id = 1;
  uint32 cp_uid = 2;
  string extend_json =3;
  string custom_text = 4;
  string original_custom_text = 5;
}

message GetUserHeadwearInUseListReq{
  repeated uint32 uids = 1;
}

message GetUserHeadwearInUseListResp{
  repeated UserHeadwearInfo headwear_list = 1;
}

message GiveHeadwearToUserReq{
  // buf:lint:ignore ENUM_PASCAL_CASE
  enum GIVE_TYPE{
    NONE = 0;
    NEW = 1;      //new holding time
    APPEND = 2;   //append holding time, default
  }

  // buf:lint:ignore ENUM_PASCAL_CASE
  enum GIVE_EXPIRE_TYPE{
    CONFIG_HOLDING_TIME = 0;
    TIMESTAMP = 1;
    TIME_SEC_REAL = 2;
  }

  uint32 uid = 1;
  uint32 suite_id = 2;        //headwear suite
  uint32 level = 3;           //headwear level
  uint32 give_type = 4;       //see enum GIVE_TYPE
  uint32 give_expire_type = 5;        //see enum GIVE_EXPIRE_TYPE
  uint32 expire_time = 6;             // 绝对值 过期时间的时间戳（表示具体时间）
  int32 expire_time_rel = 7;          // 相对值 过期时间的时间戳 (表示秒 可正可负) expire_time_rel 和 expire_time 只能选一个填
  uint32 cp_uid = 8;                  // cp对象uid ，用于发放cp麦位框时填
  string order_id = 9;                //幂等orderID
  string push_msg_text_prefix = 10;   // TT助手推送文本前缀

  uint32 use_type = 11; // see HeadwearUseType

  string custom_text = 12; // 自定义麦位框的文案
  uint32 outside_time = 13; // 外部时间
  uint32 source_type = 14; // 来源
  bool without_push = 15; //  不需要发放麦位框的默认推送
  bool without_im = 16; // 是否不发送im消息
}

enum HeadwearCustomType {
  None = 0;
  CustomText = 1; // 自定义文案
}

message HeadwearConfigReq
{
  uint32 headwear_id = 1;
}

message GetHeadwearConfigAllResp
{
  repeated HeadwearConfig config_list = 1;
}

message AddUserHeadwearExtraTimeReq
{
  uint32 uid = 1;
  uint32 extra_time = 2;  //增加的时间
  repeated uint32 suite_ids = 3;
}

message BatchGiveHeadwearToUserReq{
  // buf:lint:ignore ENUM_PASCAL_CASE
  enum GIVE_TYPE{
    NONE = 0;
    NEW = 1;      //new holding time
    APPEND = 2;   //append holding time, default
  }

  // buf:lint:ignore ENUM_PASCAL_CASE
  enum GIVE_EXPIRE_TYPE{
    CONFIG_HOLDING_TIME = 0;
    TIMESTAMP = 1;
    TIME_SEC_REAL = 2;
  }

  repeated UserInfo uid_pair_list = 1;
  uint32 suite_id = 2;        //headwear suite
  uint32 level = 3;           //headwear level
  uint32 give_type = 4;       //see enum GIVE_TYPE
  uint32 give_expire_type = 5;        //see enum GIVE_EXPIRE_TYPE
  uint32 expire_time = 6;             // 绝对值 过期时间的时间戳（表示具体时间）
  int32 expire_time_rel = 7;          // 相对值 过期时间的时间戳 (表示秒 可正可负) expire_time_rel 和 expire_time 只能选一个填
  string order_id = 8;                //幂等orderID
  string push_msg_text_prefix = 9;   // TT助手推送文本前缀
  uint32 use_type = 10; // see HeadwearUseType
  bool without_im = 11; // 是否不发送im消息
}

message UserInfo{
  uint32 uid = 1;
  uint32 cp_uid = 2;          // cp对象uid ，用于发放cp麦位框时填
  string custom_text = 3;     // 自定义麦位框的文案
}



message ChangeDecorationCustomTextReq {
  enum DecorationType {
    Unknown = 0;
    HeadWear = 1;   // 麦位框
    ChannelPersonalization = 2;  // 坐骑
  }
  uint32 uid = 1; // uid
  DecorationType decoration_type = 2;  // 装扮类型
  string decoration_id = 3;
  string version = 4; // 麦位框可以不填
  string custom_text = 5; // 自定义文案（发放时的）
  string new_custom_text = 6; // 新的自定义文案
  uint32 cp_uid = 7; // cp麦位框的对方uid
}

message ChangeDecorationCustomTextResp{
  string extend_json = 1; // 额外的json
}

message CpHeadwearConfig{
    // buf:lint:ignore ENUM_PASCAL_CASE
    enum OPER_TYPE{
        MODIFY_CP = 0;   //修改
        ADD_CP = 1;      //添加
    }

    uint32 headwear_id_1 = 1; //headwear_id
    uint32 headwear_id_2 = 2; //headwear_id
    string cp_img = 3;
    string cp_name = 4;
    string cp_connect_mic_img = 5;
    string cp_notes = 6;
    OPER_TYPE oper_type = 7; //
}

message SetCpHeadwearConfigReq{
    CpHeadwearConfig cp_headwear_conf = 1;
}

message DelCpHeadwearConfigReq {
    uint32 headwear_id_1 = 1; //headwear_id
    uint32 headwear_id_2 = 2; //headwear_id
}

message GiveHeadwearItem{
  uint32 uid = 1;
  uint32 suite_id = 2;        //headwear suite
  uint32 level = 3;           //headwear level
  uint32 give_type = 4;       //see enum GIVE_TYPE
  uint32 give_expire_type = 5;        //see enum GIVE_EXPIRE_TYPE
  uint32 expire_time = 6;             // 绝对值 过期时间的时间戳（表示具体时间）
  int32 expire_time_rel = 7;          // 相对值 过期时间的时间戳 (表示秒 可正可负) expire_time_rel 和 expire_time 只能选一个填
  uint32 cp_uid = 8;                  // cp对象uid ，用于发放cp麦位框时填
  string order_id = 9;                //幂等orderID
  string push_msg_text_prefix = 10;   // TT助手推送文本前缀
  uint32 use_type = 11; // see HeadwearUseType
  string custom_text = 12; // 自定义麦位框的文案
}

message BatchGiveCpHeadwearToUserReq{
    repeated GiveHeadwearItem give_headwear_list = 1;
}

