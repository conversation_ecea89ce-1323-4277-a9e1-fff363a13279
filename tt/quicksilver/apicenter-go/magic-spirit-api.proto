syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/apicentergo";
// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package apicentergo;

message MagicSpirit {
  uint32 magic_spirit_id    = 1;
  string name               = 2;  // 名称
  string icon_url           = 3;  // 图标url
  uint32 price              = 4;  // 价格
  uint32 rank               = 5;  // 排名
  uint32 effect_begin       = 6;  // 上架时间
  uint32 effect_end         = 7;  // 下架时间
  string describe_image_url = 8;  // 礼物介绍浮层
  string describe           = 9;  // 礼物介绍
  uint32 junior_lighting    = 10; // 礼物触发初级光效个数
  uint32 middle_lighting    = 11; // 礼物触发中级光效个数
  string vfx_resource       = 12; // 特效资源
  string vfx_resource_md5   = 13; // 特效资源md5
  uint32 update_time        = 14; // 更新时间

  uint32 effect_time = 15;  // 生效时间
  uint32 id = 16;           // 未生效配置唯一id

  float rank_float = 17;    // float的排序，新版用，与rank不一致

   // 6.36.0 增加活动链接字段
   string desc_activity_url = 18;  // 礼物介绍活动链接

  // 6.56.3 新增
  bool show_effect_end = 19;    // 是否展示下架时间
  MagicSpiritActivityCfg activity_cfg = 20;  // 活动配置
}

message MagicSpiritActivityCfg {
  string activity_name = 1;  // 活动名称
  int64 begin_time = 2;  // 活动开始时间
  int64 end_time = 3;    // 活动结束时间
  string image_url = 4;  // 活动浮层

  string jump_url_tt = 5;  // tt活动跳转链接
  string jump_url_hc_android = 6;  // 欢游安卓跳转链接
  string jump_url_hc_ios = 7;  // 欢游ios跳转链接
  string jump_url_mike_android = 8;  // 麦可安卓跳转链接
  string jump_url_mike_ios = 9;  // 麦可ios跳转链接
}

message AddMagicSpiritReq {
  repeated MagicSpirit magic_spirit = 1;
}

message AddMagicSpiritResp {
  repeated uint32 magic_spirit_id = 1;
}

message DelMagicSpiritReq {
  repeated uint32 magic_spirit_ids = 1;
}

message DelMagicSpiritResp {
}

message GetMagicSpiritReq {
}

message GetMagicSpiritResp {
  repeated MagicSpirit magic_spirit = 1;
}

message UpdateMagicSpiritReq {
  repeated MagicSpirit magic_spirit = 1;
}

message UpdateMagicSpiritResp {
}

enum PrizeLeverConst {
  NONE_PRIZE        = 0; // 无特效
  SMALL_PRIZE       = 1; // 小奖特效
  NORMAL_PRIZE      = 2; // 中奖特效
  BIG_PRIZE         = 3; // 大奖特效
  FULL_SERVER_PRIZE = 4; // 全服特效
}

message MagicSpiritPondItem {
  uint32 item_id         = 1;
  uint32 magic_spirit_id = 2; // 魔法精灵ID
  uint32 present_id      = 3; // 礼物ID
  uint32 weight          = 4; // 权重
  uint32 prize_level     = 5; // 中奖等级
  uint32 update_time     = 8; // 更新时间
  uint32 effect_time     = 9;     // 生效时间
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message AddMagicSpiritPondReq {
  repeated MagicSpiritPondItem MagicSpiritPondItems = 1;
}

message AddMagicSpiritPondResp {
  repeated uint32 magic_spirit_ids = 1;
}

message UpdateMagicSpiritPondReq {
  uint32 item_id     = 1;
  uint32 present_id  = 2; // 礼物ID
  uint32 weight      = 3; // 权重
  uint32 prize_level = 5; // 中奖等级
}

message UpdateMagicSpiritPondResp {
}

message GetMagicSpiritPondReq {
  uint32 magic_spirit_id = 1;
}

message GetMagicSpiritPondResp {
  repeated MagicSpiritPondItem magic_spirit_pond = 1;
}

message DelMagicSpiritPondReq {
  uint32          magic_spirit_id = 1;
  repeated uint32 item_ids        = 2;
}

message DelMagicSpiritPondResp {
}

// ============= 新增未开始tab ====================
message GetMagicSpiritConfTmpReq{
}

message MagicSpiritConfTmp{
    MagicSpirit magic_spirit_tmp = 1;
    bool has_pool = 2;
    repeated MagicSpiritPondItem pool = 3;
}

message GetMagicSpiritConfTmpResp{
    repeated MagicSpiritConfTmp conf_list = 1;
}
// ==================================================

message MagicSpiritBlacklist {
  uint32 blacklist_id = 1;
  uint32 channel_id   = 2;
  uint32 room_id      = 3; // 房间ID
  string room_name    = 4; // 房间名
  uint32 ttid         = 5; // 房主ID
  string owner        = 6; // 房主名
  uint32 create_time  = 7; // 创建时间
}

message AddMagicSpiritBlacklistReq {
  repeated MagicSpiritBlacklist blacklist = 1;
}

message AddMagicSpiritBlacklistResp {
  repeated uint32 blacklist_id = 1;
}

message GetMagicSpiritBlackListReq {
  uint32 page_num   = 1;
  uint32 page_size  = 2;
  uint32 channel_id = 3;
}

message GetMagicSpiritBlackListResp {
  uint32                        total     = 1;
  repeated MagicSpiritBlacklist blacklist = 2;
}

message DelMagicSpiritBlacklistReq {
  repeated uint32 channel_ids = 1;
}

message DelMagicSpiritBlacklistResp {
}

enum CommonConfType {
  NONE                         = 0;
  DAILY_SEND_MONEY_LIMIT       = 1; // 每日送礼金额限制
  DAILY_PREVENT_EXCHANGE_LIMIT = 2; // 每日同账号送礼金额限制
  PER_ORDER_COUNT_LIMIT        = 3; // 单笔订单数量限制
}

message CommonConf {
  uint32 conf_type = 1;
  uint32 value     = 2;
}

message GetCommonConfReq {
}

message GetCommonConfResp {
  repeated CommonConf common_conf = 1;
}

message SetCommonConfReq {
  repeated CommonConf common_conf = 1;
}

message SetCommonConfResp {
}

// 连击信息
message CombInfo {
  uint32 duration   = 1;      // 倒计时光圈持续时长
  uint32 comb_level = 2;      // 根据次数触发的连击等级
  string level_name = 3;      // 等级名称
  bool   level_up   = 4;                // 是否升级
}

// 魔法精灵中奖类型
enum MagicSpiritEffectType
{
  MAGIC_SPIRIT_EFFECT_NONE = 0;
  MAGIC_SPIRIT_EFFECT_LV1  = 1;  // 小奖前置特效
  MAGIC_SPIRIT_EFFECT_LV2  = 2;  // 中奖前置特效
  MAGIC_SPIRIT_EFFECT_LV3  = 3;  // 大奖前置特效
  MAGIC_SPIRIT_EFFECT_LV4  = 4;  // 超级大奖带全服公告
}

// 实际送礼信息
message PresentSendInfo {
  string present_order_id = 1;
  uint32 target_uid       = 2;
  uint32 gift_id          = 3;
  uint32 cnt              = 4;
  uint32 award_effect     = 5;      // 中奖特效 see MagicSpiritEffectType
}

message SendMagicSpiritReq {
  uint32          uid             = 1;                           // 送礼方
  uint32          magic_spirit_id = 2;               // 魔法精灵ID
  repeated uint32 target_uid_list = 3;      // 收礼uid列表
  uint32          average_cnt     = 4;                   // 每个收礼方的礼物数量
  uint32          channel_id      = 5;                    // 房间ID
  uint32          comb_cnt        = 6;                      // 当前连击次数
}

message SendMagicSpiritResp {
  repeated PresentSendInfo send_info_list = 1;
  CombInfo                 comb_info      = 2;
  uint32                   send_time      = 3;    // 确认T豆订单的时间
  string                   magic_order_id = 4;    // 消费订单号
}

message MagicSpiritForCli {
  uint32          magic_spirit_id    = 1;
  string          name               = 2;  // 名称
  string          icon_url           = 3;  // 图标url
  uint32          price              = 4;  // 价格
  uint32          rank               = 5;  // 排名
  uint32          effect_begin       = 6;  // 上架时间
  uint32          effect_end         = 7;  // 下架时间
  string          describe_image_url = 8;  // 礼物介绍浮层
  string          describe_jump_url  = 9;  // 礼物介绍
  string          vfx_resource       = 10; // 特效资源
  string          vfx_resource_md5   = 11; // 特效资源md5
  repeated uint32 present_ids        = 12; // 礼物池中礼物id
  float           rank_float         = 13; // float的排序，新版用，与rank不一致
}

message GetMagicSpiritForCliReq {
  uint32 current_version = 1; // 当前版本
}

message GetMagicSpiritForCliResp {
  repeated MagicSpiritForCli magic_spirits   = 1;
  uint32                     current_version = 2;
}

message GetMagicSpiritUsableReq {
  uint32 channel_id = 1;
  uint32 uid        = 2;
}

message GetMagicSpiritUsableResp{
  uint32 channel_id   = 1;
  uint32 exp_level    = 2; // 使用这个礼物的最低经验等级
  uint32 wealth       = 3; // 使用这个礼物的最低财富值
  uint32 charm        = 4; // 使用这个礼物的最低魅力值
  uint32 pupil_usable = 5; // 未成年人是否可以使用这个礼物
  uint32 usable       = 6; // see MagicSpiritUsable  房间等其他服务器判断条件
}

message GetMagicSpiritOrderTotalReq {
  uint32 begin_time = 1;
  uint32 end_time   = 2;
}

message GetMagicSpiritOrderTotalResp {
  uint32 total_cnt   = 1;
  uint32 total_price = 2;
}

message GetMagicSpiritAwardTotalReq {
  uint32 begin_time = 1;
  uint32 end_time   = 2;
}

message GetMagicSpiritAwardTotalResp {
  uint32 total_cnt   = 1;
  uint32 total_price = 2;
}
