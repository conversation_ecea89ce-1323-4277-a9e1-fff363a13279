syntax="proto3";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package topic_channel.recommendation;

option go_package = "golang.52tt.com/protocol/services/topic_channel/recommendation";

service Recommendation {
    // 添加到推荐房
    rpc AddRecommendation (AddRecommendationReq) returns (AddRecommendationResp);
    // 更新推荐房配置信息
    rpc UpdateRecommendation (UpdateRecommendationReq) returns (UpdateRecommendationResp);
    // 移除推荐房
    rpc RemoveRecommendation (RemoveRecommendationReq) returns (RemoveRecommendationResp);
    // 获取推荐房列表
    rpc GetRecommendationList (GetRecommendationListReq) returns (GetRecommendationListResp);
    // 获取N个推荐房
    rpc PickRecommendations (PickRecommendationsReq) returns (PickRecommendationsResp);
    // 通过id获取推荐房，可用来判断是否是推荐房，非推荐房resp不返回
    rpc IsRecommendation (IsRecommendationReq) returns (IsRecommendationResp);
    // 更新推荐房状态
    rpc UpdateRecommendationStatus (UpdateRecommendationStatusReq) returns (UpdateRecommendationStatusResp);
}

enum ChannelType {
    PGC = 0;            //pgc推荐房, 默认
    UNION_TOP = 1;      //工会置顶
}

message RecommendationChannel { // 时间都是unix second

    uint32 channel_id = 1;
    int64 begin_at = 2; // 出现在推荐列表的时间区间, 左闭右开
    int64 end_at = 3; // 出现在推荐列表的时间区间, 左闭右开
    int64 update_at = 4; // 信息最后一次更新时间
    repeated uint32 slots = 5; // 出现在推荐列表的<小时>时间段, 如[9, 10, 14, 15, 20]
    uint32 user_id = 6; // 创建者用户id
    bool has_pwd = 7; //是否有密码
    uint32 admin_count = 8;//管理员数
    ChannelType channel_type = 9;
    uint32 tab_id = 10;
}

message AddRecommendationReq {
    RecommendationChannel channel_info = 1;
}

message AddRecommendationResp {}

message UpdateRecommendationReq {
    RecommendationChannel channel_info = 1;
}

message UpdateRecommendationResp {}

message RemoveRecommendationReq {
    uint32 channel_id = 1;
}

message RemoveRecommendationResp {}

message GetRecommendationListReq {
    enum Sort {
        NONE = 0;
        BEGIN_AT = 1;
        END_AT = 2;
        UPDATE_AT = 3;
    }
    uint32 limit = 1;
    int64 begin_at = 2;     // 按时间区间查询, 左闭右开, 传<=0就是不按时间过滤
    int64 end_at = 3;       // 按时间区间查询, 左闭右开, 传<=0就是不按时间过滤
    string load_more = 4;   // 分页上下文, 注意sort_by和load_more一定要是对应的
    uint32 channel_id = 5;  // 指定channel id获取
    uint32 uid = 6;         // 指定用户 id获取
    //Sort sort_by = 5; // 排序条件, 注意sort_by和load_more一定要是对应的
    //bool desc = 6; // 倒序排序(默认false, 即正序)
    ChannelType channel_type = 9;
}

message GetRecommendationListResp {
    repeated RecommendationChannel channel_list = 1;
    string load_more = 2; // 分页上下文, 注意sort_by和load_more一定要是对应的
}


message PickRecommendationsReq {
    message Filter {
        repeated uint32 exception_channel_id_list = 1; // 不拿这些channel_id
    }
    uint32 user_id = 1; // 不知道有什么用, 先留着吧
    uint32 count = 2; // 想要获取的数量
    Filter filter = 3; // condition

    ChannelType channel_type = 9;
    uint32 tab_id = 10;
}

message PickRecommendationsResp {
    repeated RecommendationChannel channel_list = 1;
}

message IsRecommendationReq {
    repeated uint32 channel_id = 1;
}

message IsRecommendationResp {
    map<uint32, bool> channel_list = 1;
}

message UpdateRecommendationStatusReq {
    uint32 channel_id = 1;

    message PwdEvent {
        bool had_pwd = 2;
    }
    message DismissEvent {
        bool is_dismiss = 3;
    }
    message InOutChannelEvent {
        uint32 admin_count = 4;
    }
    PwdEvent pwd_event = 5;
    DismissEvent dismiss_event = 6;
    InOutChannelEvent in_out_event = 7;
}

message UpdateRecommendationStatusResp {
    uint32 channel_id = 1;

    message PwdInfo {
        bool before_had_pwd = 2;
        bool after_had_pwd = 3;
    }
    message AdminCountInfo {
        uint32 before_admin_count = 4;
        uint32 after_admin_count = 5;
    }
    PwdInfo pwd_info = 6;
    AdminCountInfo admin_count_info = 7;
}