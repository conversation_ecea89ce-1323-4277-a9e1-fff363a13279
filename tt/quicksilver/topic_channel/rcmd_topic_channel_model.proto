syntax = "proto3";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package topic_channel.rcmd_topic_channel_model;
import "tt/quicksilver/rcmd-local/common/common.proto";

option go_package = "golang.52tt.com/protocol/services/topic_channel/rcmd_topic_channel_model";
import "tt/quicksilver/topic_channel/recommendation_gen.proto";
import "tt/quicksilver/topic_channel/rcmd_topic_channel.proto";

//模型打分服务  负责: 调取特征画像、特征处理、调用具体模型打分
service RcmdTopicChannelModel {
    // gbdt模型打分
     rpc ScoreTopicChannelWithGBDT(ScoreTopicChannelWithGBDTReq) returns(ScoreTopicChannelWithGBDTRsp);
    // 快速匹配
     rpc ScoreQuickMatchWithGBDT(ScoreQuickMatchWithGBDTReq) returns(ScoreuickMatchWithGBDTRsp);
     // 纯调用模型打分
     rpc Predict(PredictReq) returns(PredictRsp);
}

message InternalArray {
    repeated double internal_input = 1;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message PredictReq {
    uint32 uid = 1;
    string model_version = 2;
    repeated InternalArray inputData = 3;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message PredictRsp {
    repeated double Predictions = 1;
}

message ScoreTopicChannelWithGBDTReq{
    rcmd.common.RcmdBaseReq base_req = 1;
    repeated uint32 channel_id_list = 2; //要排序房间列表
    map<uint32, recommendation_gen.ChannelInfo> channel_info_map = 3;
}

message ChannelScoreItem{
    uint32 cid = 1;
    float  score = 2;
}

message ScoreTopicChannelWithGBDTRsp{
    repeated ChannelScoreItem scores = 1;
}


message ScoreQuickMatchWithGBDTReq{
    rcmd.common.RcmdBaseReq base_req = 1;
    repeated uint32 channel_id_list = 2; //要排序房间列表
    map<uint32, recommendation_gen.ChannelInfo> channel_info_map = 3;
    rcmd_topic_channel.Filter filter = 4;
}

message ScoreuickMatchWithGBDTRsp{
    repeated ChannelScoreItem scores = 1;
}
