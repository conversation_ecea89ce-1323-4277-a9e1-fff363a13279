syntax = "proto3";

package enigma.global.api.payproxy;

import "google/protobuf/timestamp.proto";

option go_package = "golang.52tt.com/protocol/services/pay-proxy";

// 内部服务, 仅用于适配旧版本的支付相关的逻辑
service PayProxy {
  // T 豆转密室券, 对应 DoRecharge 的冻结和提交的逻辑
  rpc ConvertTbeanToCoin (ConvertTbeanToCoinRequest) returns (ConvertTbeanToCoinResponse) {}
  // 上报支付结果, 对应 updatePayResult 的发放密室券部分的逻辑, 仅限于支付成功的情况, 支付失败就不要发了
  rpc ReportPayResult (ReportPayResultRequest) returns (ReportPayResultResponse) {}
  // 冻结游戏券, 对应 GameTicketMgr 的 FreezeUserGameTicket 的逻辑
  rpc FreezeUserGameTicket(FreezeUserGameTicketRequest) returns (FreezeUserGameTicketResponse);
  // 确认冻结游戏券, 对应 GameTicketMgr 的 ConfirmFreezeOrder 的提交逻辑
  rpc CommitFreezeOrder(CommitFreezeOrderRequest) returns (CommitFreezeOrderResponse);
  // 回滚冻结游戏券, 对应 GameTicketMgr 的 ConfirmFreezeOrder 的回滚逻辑
  rpc RollbackFreezeOrder(RollbackFreezeOrderRequest) returns (RollbackFreezeOrderResponse);
  // 获取游戏券余额
  rpc GetUserGameTicketBalance(GetUserGameTicketBalanceRequest) returns (GetUserGameTicketBalanceResponse);
}

// T 豆转密室券请求
message ConvertTbeanToCoinRequest {
  // 用户 ID
  uint64 uid = 1;
  // 消耗的 T 豆的数量
  int64 tbean_num = 2;
  // 获得的密室券的数量
  int64 coin_num = 3;
  // 订单 ID
  string order_id = 4;
}

// T 豆转密室券请求响应
message ConvertTbeanToCoinResponse {}

// 上报支付结果请求
message ReportPayResultRequest {
  // 货币系统订单号
  string outside_order_id = 1;
  // 支付订单记录
  ReportPayResultRequestPayOrder pay_order = 2;
  // 支付配置
  ReportPayResultRequestPayConfig pay_config = 3;
}

// 支付订单记录, 对应 store.PayOrder 的结构
message ReportPayResultRequestPayOrder {
  // 用户id
  uint64 uid = 1;
  // 订单号
  string order_id = 2;
  // 配置id
  uint32 cfg_id = 3;
  // 金额，单位：分
  uint32 price = 4;
  // 支付渠道
  string pay_channel = 5;
  // 支付状态
  uint32 pay_status = 6;
  // 货币系统订单号
  string outside_order_id = 7;
  // 第三方订单号
  string third_party_order_id = 8;
  // 外部时间
  google.protobuf.Timestamp outside_time = 9;
  // 创建时间
  google.protobuf.Timestamp create_time = 10;
  // 更新时间
  google.protobuf.Timestamp update_time = 11;
}

// 对应 store.PayCfg 的结构
message ReportPayResultRequestPayConfig {
  // ID
  uint32 id = 1;
  // 操作系统类型
  uint32 os_type = 2;
  // 苹果支付产品 ID
  string apple_pay_product_id = 3;
  // 金额，单位：分
  uint32 price = 4;
  // 礼物名称
  string ticket_name = 5;
  // 密室券图片url
  string game_tickets_url = 6;
  // 密室券 ID
  uint32 ticket_id = 7;
  // 密室券数量
  uint32 ticket_num = 8;
  // T 豆数量
  uint32 tbean_num = 9;
  // 礼物包裹 ID
  uint32 pack_id = 10;
  // 礼物数量
  uint32 pack_num = 11;
  // 礼物名称
  string gift_name = 12;
  // 礼物图片 url
  string gift_url = 13;
  // 密室券图片url
  string game_ticket_url = 14;
  // 创建时间
  google.protobuf.Timestamp create_time = 16;
  // 更新时间
  google.protobuf.Timestamp update_time = 17;
}

// 上报支付结果响应
message ReportPayResultResponse {}

// 确认冻结游戏券请求
message FreezeUserGameTicketRequest {
  // 用户 ID
  uint64 uid = 1;
  // 订单号
  string order_id = 2;
  //  uint32 ticket_id = 3; <- 不再指定密室券 ID
  uint32 num = 3;
  //  CostDestType cost_type = 5; <- 不指定消耗类型
  //  CostPriorityType cost_priority = 6; <- 不指定消耗优先级
  // 优惠券id
  string coupon_id = 4;
  // 优惠券所减免的密室券数量，用于成本核算
  uint32 coupon_reduce_num = 5;
  //使用场景
  uint32 usage_type = 6;
  // 使用时的上下文，用于校验优惠券条件，也方便查用户反馈
  UsageContext usage_context = 7;
}

// 冻结使用的上下文
message UsageContext{
  // 当前在哪个游戏下
  uint32 game_id = 1;
  // 章节
  repeated uint32 chapters = 2;
  // 玩伴id（仅使用优惠券时传）
  uint32 playmate_uid = 3;
  // 玩剧本的用户uid列表
  repeated uint64 player_uids = 4;
}

// 确认冻结游戏券响应
message FreezeUserGameTicketResponse {}

// 提交冻结游戏券请求
message CommitFreezeOrderRequest {
  // 用户 ID
  uint64 uid = 1;
  // 订单号
  string order_id = 2;
}

// 提交冻结游戏券响应
message CommitFreezeOrderResponse {}

// 回滚冻结游戏券请求
message RollbackFreezeOrderRequest {
  // 用户 ID
  uint64 uid = 1;
  // 订单号
  string order_id = 2;
}

// 回滚冻结游戏券响应
message RollbackFreezeOrderResponse {}

// 获取游戏券余额请求
message GetUserGameTicketBalanceRequest {
  // 用户 ID
  uint64 uid = 1;
}

// 获取游戏券余额响应
message GetUserGameTicketBalanceResponse {
  // 用户 ID
  uint64 uid = 1;
  // 用户游戏券余额
  int64 balance = 2;
}
