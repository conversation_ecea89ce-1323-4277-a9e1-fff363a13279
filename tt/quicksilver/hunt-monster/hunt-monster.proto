syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/huntmonster";
package huntmonster;


service HuntMonster {
    rpc GetMonsterActivityConf (GetMonsterActivityConfReq) returns (GetMonsterActivityConfResp) {
    }

    rpc GetMonsterChannel ( GetMonsterChannelReq ) returns ( GetMonsterChannelResp ) {
    }

    rpc GetMonsterList (GetMonsterListReq) returns (GetMonsterListResp) {
    }

    rpc AttackMonster (AttackMonsterReq) returns (AttackMonsterResp) {
    }

    rpc GetHuntMonsterItem( GetHuntMonsterItemReq ) returns ( GetHuntMonsterItemResp ) {
    }

    rpc AddHuntMonsterItem( AddHuntMonsterItemReq ) returns ( AddHuntMonsterItemResp ) {
    }

    rpc GetChannelActivateRank ( GetChannelActivateRankReq ) returns ( GetChannelActivateRankResp ) {
    }

    rpc GetUserAwardRecord ( GetUserAwardRecordReq ) returns ( GetUserAwardRecordResp ) {
    }

    // 获取屏蔽打龙房间列表
    rpc GetBanChannelIdList( GetBanChannelIdListReq ) returns ( GetBanChannelIdListResp ) {}
}

message User {
    uint32 uid = 1;
    string account = 2;
    string nickname = 3;
    string alias    = 4;
    int32 sex      = 5;
    int64 value    = 6;
}

enum MonsterStatus {
    ACTIVATE = 0; //当前房间正在打或者倒计时30秒后开始打的龙
    IN_QUEUE = 1; //在房间队列中，还没激活
    ESCAPE = 2; //时间到逃跑了
    KILLED = 3; //击败了
}

//怪物信息
message Monster {
    uint32 channel_id = 1;  //触发房间
    User   trigger_user = 2; //触发的用户
    int64 monster_id = 3; //服务端生成怪物唯一ID
    int64 curr_life_point = 4;  //当前生命值
    int64 max_life_point = 5;   //最大生命值
    int64 create_time = 6; //触发时间戳
    string icon_url = 7;
    MonsterStatus status = 8;//
    int64 count_down = 9; //boss从出现到开始打的倒计时
    int64 live_time  = 10; //boss从出现到结束的时间，包括count_down
}

message TimeRange {
    uint32 begin_time = 1;
    uint32 end_time = 2;
}

//取boss活动开启时间段
message GetMonsterActivityConfReq {
}

message GetMonsterActivityConfResp {
    repeated TimeRange time_range_list = 1;
    int64 count_down = 2; //boss从出现到开始打的倒计时
    int64 live_time  = 3; //boss从出现到结束的时间，包括count_down
}

//随机取一个前有boss的房间
message GetMonsterChannelReq {
}

message GetMonsterChannelResp {
    repeated uint32 monster_channel_list = 1;
}

message GetMonsterListReq {
    uint32 channel_id = 1;
    uint32 uid = 2;
}

message GetMonsterListResp {
    repeated Monster monster_list = 1;
    AttackResult attack_result = 2; //当前用户对怪物攻击效果
    int64 item_cnt = 3; //拥有道具数量
    repeated User rank_list = 4;
}

//攻击效果数据
message AttackResult {
    int64 attack_value = 1; //本次攻击有效值，包括暴击值
    int64 critical_value = 2; //暴击值
    int64 total_value = 3; //本人攻击当前怪物总值
    int64 attack_rank = 4; //对当前怪物造成伤害排名
    int64 left_item_cnt = 5; //剩余道具数量
}

message AttackMonsterReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
    int64 monster_id = 3;
    int64 cnt = 4; //使用道具数量
}

message AttackMonsterResp {
    Monster monster = 1;
    AttackResult atc_res = 2;
    int64 item_cnt = 3; //剩余数量
}

message AddHuntMonsterItemReq {
    uint32 uid = 1;
    int64 item_type = 2; //现在默认给0
    int64 incr_cnt = 3;  //增加的值，可以传负值
}

message AddHuntMonsterItemResp {
    int64 item_type = 1; //现在默认给0
    int64 final_cnt = 2; //增加完后的值
}

message GetHuntMonsterItemReq {
    uint32 uid = 1;
}

message GetHuntMonsterItemResp {
    int64 item_cnt = 1;
    uint64 attack_monster_cnt = 2; //打龙数量
    uint64 award_tbean_value  = 3; //获得奖励T豆价值
}

//房间信息
message ChannelInfo{
    uint32 channel_id = 1;
    uint32 display_id = 2;
    uint32 channel_type = 3; // 7是直播房
    bool is_on_live = 4; //是否直播中
    string channel_name = 5; //房间名
    uint32 activate_count = 6; //触发龙次数
    uint32 anchor_uid = 7; //如果是直播房，主播UID
}

//房间触发龙次数
message ChannelActivateRecord {
    ChannelInfo channel_info = 1;
    User  anchor_info  = 2;
}

//触发龙次数房间排行榜
message GetChannelActivateRankReq {
}

message GetChannelActivateRankResp {
    repeated ChannelActivateRecord ranks = 1;//排行榜
}

enum MonsterAwardType {
    INVALID    = 0;
    PRESENT    = 1; //包裹礼物
    HEAD_WEARA = 2; //头像框
    MIC_WEARA  = 3; //麦位框
    HORSE      = 4; //坐骑
    TBEAM      = 5; //T豆
}

message AwardItem {
    MonsterAwardType award_type = 1;
    string item_id = 2;
    string icon_url = 3; //图标链接
    int64 item_cnt = 4; //包裹礼物数量
    int64 hold_day = 5;// 麦位框天数
    string name = 6; //礼物名
    int64 timestamp = 7; //获得时间戳 //
}

message GetUserAwardRecordReq {
    uint32 uid = 1;
}

message GetUserAwardRecordResp {
    repeated AwardItem award_item_list = 1;
    uint64 attack_monster_cnt = 2; //打龙数量
    uint64 award_tbean_value  = 3; //获得奖励T豆价值
}


// 获取屏蔽打龙房间列表
message  GetBanChannelIdListReq {

}

message  GetBanChannelIdListResp {
    repeated  uint32 ban_channel_id_list = 1; // 不显示打龙房间列表
}