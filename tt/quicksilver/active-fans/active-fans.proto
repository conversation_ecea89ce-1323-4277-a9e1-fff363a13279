syntax = "proto3";
package active_fans;
option go_package = "golang.52tt.com/protocol/services/active-fans";

service ActiveFans {

    rpc RecordLoginEvent (RecordLoginEventReq) returns (RecordLoginEventResp) {
    }

    rpc GetActiveLoginRecordsBeforeTime (GetActiveLoginRecordsBeforeTimeReq) returns (GetActiveLoginRecordsResp) {
    }


    rpc RemoveActiveLogin (RemoveActiveLoginReq) returns (RemoveActiveLoginResp) {
    }

    rpc GetActiveFriendship (GetActiveFriendshipReq) returns (GetActiveFriendshipResp) {
    }

    rpc GetActiveFriendshipV2 (GetActiveFriendshipV2Req) returns (GetActiveFriendshipV2Resp) {
    }


    rpc BuildActiveFriendship (BuildActiveFriendshipReq) returns (BuildActiveFriendshipResp) {
    }


    rpc RemoveActiveFriendship (RemoveActiveFriendshipReq) returns (RemoveActiveFriendshipResp) {
    }

    //修改关系 先检查是否是活跃用户
    rpc UpdateFriendship (UpdateFriendshipReq) returns (UpdateFriendshipResp) {
    }

    // 记录在线状态
    rpc UpdateUserOLStatus(UpdateUserOLStatusReq) returns (UpdateUserOLStatusResp){}

    // 当大数量时只获取在线的粉丝/关注
    rpc GetOnlineActiveFriendship(GetOnlineActiveFriendshipReq) returns (GetOnlineActiveFriendshipResp){}
}

message UpdateUserOLStatusReq {
    uint32 uid = 1;
    bool is_online = 2;
}

message UpdateUserOLStatusResp {

}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetOnlineActiveFriendshipReq {
    uint32 uid = 1;
    GetActiveFriendshipV2Req.QueryType queryType = 2;
    uint32 big_num = 3; // 大于这个数才过滤非在线人数
}

message GetOnlineActiveFriendshipResp {
    repeated uint32 friendships = 1;
}

message RecordLoginEventReq {
    uint32 uid = 1;
}
message RecordLoginEventResp {
    bool is_recent_frist_login = 1; //true :非活跃登录 （重建关系（粉丝，关注）） false:活跃登录
}

message GetActiveFriendshipReq {
    uint32 uid = 1;
    bool query_fan = 2; //true:查活跃粉丝 false:查询活跃关注
    uint32 max_fetch_num = 3; //0:代表获取全量粉丝或关注 非0：最多返回的粉丝或关注数
}

message GetActiveFriendshipV2Req {
    enum QueryType {
        Follower = 0;
        Following = 1;
        Friend = 2;
    }
    uint32 uid = 1;
    QueryType query_type = 2;
}

message GetActiveFriendshipV2Resp {
    repeated uint32 friendships = 1;
}


message GetActiveFriendshipResp {
    repeated uint32 fans_uid_list = 1;
}

message BuildActiveFriendshipReq {
    uint32 uid = 1;
    repeated uint32 follower_uid_list = 2; //粉丝列表
    repeated uint32 following_uid_list = 3; //关注列表
}

message BuildActiveFriendshipResp {

}

message GetActiveLoginRecordsBeforeTimeReq {
    uint32 before_time = 1;

}

message LoadMore {
    uint32 time_sec = 1;
}
message GetActiveLoginRecordsByRangeReq {
    uint32 count = 1;
    LoadMore load_more = 2;
}

message GetActiveLoginRecordsResp {
    repeated LoginRecord records = 1;
    LoadMore load_more = 2;
}

message LoginRecord {
    uint32 uid = 1;
    uint32 last_login_at = 2; //uinx time sec
}

message RemoveActiveLoginReq {
    uint32 before = 1;
}


message RemoveActiveLoginResp {

}

message RemoveActiveFriendshipReq {
    repeated uint32 uid_list = 1;
    enum RemoveOption {
        NonSpecifiedUid = 0;
        SpecifiedUid = 1;
    }
    uint32 uid = 2;
    RemoveOption option = 3;
}


message RemoveActiveFriendshipResp {

}

message UpdateFriendshipReq {
    uint32 from_uid = 1;
    uint32 to_uid = 2;
    bool is_delete = 3;
}
message UpdateFriendshipResp {

}

