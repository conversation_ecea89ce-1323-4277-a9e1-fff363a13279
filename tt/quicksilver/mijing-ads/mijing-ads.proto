syntax = "proto3";

package mijing_ads;

option go_package = "golang.52tt.com/protocol/services/mijing-ads";

import "tt/quicksilver/extension/options/options.proto";

service MijingAds {
    option (service.options.service_ext) = {
        service_name: "mijing-ads"
    };

    // 新增 or 更新第三方广告配置
    rpc UpdateThirdPartyAds(UpdateThirdPartyAdsRequest) returns (UpdateThirdPartyAdsResponse) {}
    // 删除第三方广告配置
    rpc DeleteThirdPartyAds(DeleteThirdPartyAdsRequest) returns (DeleteThirdPartyAdsResponse) {}
    // 获取第三方广告配置列表
    rpc GetThirdPartyAdsConfigList(GetThirdPartyAdsConfigListRequest) returns (GetThirdPartyAdsConfigListResponse) {}
}

// = = = = = 「Begin」第三方广告 = = = = =
// 第三方广告开关
enum ThirdPartyAdsSwitch {
    THIRD_PARTY_ADS_SWITCH_UNSPECIFIED = 0;
    THIRD_PARTY_ADS_SWITCH_ON = 1;  // 开启
    THIRD_PARTY_ADS_SWITCH_OFF = 2;  // 关闭
}

// 第三方广告场景
enum ThirdPartyAdsScene {
    THIRD_PARTY_ADS_SCENE_UNSPECIFIED = 0;
    THIRD_PARTY_ADS_SCENE_CHAPTER_LOAD = 1;  // 章节加载
    THIRD_PARTY_ADS_SCENE_UNLOCK_CHAPTER_ROOM = 2;  // 解锁章节 - 房间
    THIRD_PARTY_ADS_SCENE_UNLOCK_CHAPTER_GAME = 3;  // 解锁章节 - 游戏
    THIRD_PARTY_ADS_SCENE_UNLOCK_CLUE = 4;  // 解锁线索
    THIRD_PARTY_ADS_SCENE_CLUE_POPUP = 5;  // 线索弹窗
    THIRD_PARTY_ADS_SCENE_GAME_MIDWAY = 6;  // 游戏中途
    THIRD_PARTY_ADS_SCENE_CHAPTER_END = 7;  // 章节结束
    THIRD_PARTY_ADS_SCENE_POST_LIST = 8;  // 帖子列表
    THIRD_PARTY_ADS_SCENE_CLUE_POPUP_146 = 9;  // 线索弹窗 p.s v1.46.0 新增仅可选「信息流」的广告类型
    THIRD_PARTY_ADS_SCENE_ADS_INTERACTION = 10;  // 广告交互
}

// 第三方广告类型
enum ThirdPartyAdsType {
    THIRD_PARTY_ADS_TYPE_UNSPECIFIED = 0;
    THIRD_PARTY_ADS_TYPE_FEED_STREAM = 1;  // 信息流
    THIRD_PARTY_ADS_TYPE_BANNER = 2;  // Banner
    THIRD_PARTY_ADS_TYPE_INTERSTITIAL = 3;  // 插屏
    THIRD_PARTY_ADS_TYPE_SPLASH = 4;  // 开屏
    THIRD_PARTY_ADS_TYPE_REWARD_VIDEO = 5;  // 激励视频
}

// 第三方广告下发人群类型
enum ThirdPartyTargetUserType {
    THIRD_PARTY_TARGET_USER_TYPE_UNSPECIFIED = 0;
    THIRD_PARTY_TARGET_USER_TYPE_ALL_USER = 1;  // 全部用户
    THIRD_PARTY_TARGET_USER_TYPE_USER_GROUP = 2;  // 智能运营平台
}

// 第三方广告剧本类型
enum ThirdPartyTargetScenarioType {
    THIRD_PARTY_TARGET_SCENARIO_TYPE_UNSPECIFIED = 0;
    THIRD_PARTY_TARGET_SCENARIO_TYPE_ALL = 1;  // 全部
    THIRD_PARTY_TARGET_SCENARIO_TYPE_PART = 2;  // 部分
}

// 第三方广告配置
message ThirdPartyAdsConfig {
    enum ClientType {
        CLIENT_TYPE_UNSPECIFIED = 0;
        CLIENT_TYPE_IOS = 1;  // ios
        CLIENT_TYPE_ANDROID = 2;  // Android
    }

    // 可见版本的比较运算符
    enum VersionOperator {
        VERSION_OPERATOR_UNSPECIFIED = 0;
        VERSION_OPERATOR_ALL = 1;  // 全部
        VERSION_OPERATOR_GT = 2;  // 大于
        VERSION_OPERATOR_EQ = 3;  // 等于
        VERSION_OPERATOR_LT = 4;  // 小于
        VERSION_OPERATOR_NE = 5;  // 不等于
    }

    string id = 1;  // 唯一 ID（新增时填空，更新时传递）
    ThirdPartyAdsSwitch switch =  2;  // 开关
    ThirdPartyAdsScene scene = 3;  // 场景
    string ads_name = 4;  // 广告位名称
    ThirdPartyAdsType type = 5;  // 广告类型
    string code_location_id = 6;  // 代码位 ID, p.s v1.46.0 deprecated，Use code_location_id_list
    ThirdPartyTargetUserType target_user_type = 7;  // 下发人群类型
    string ads_location_id = 8;  // 广告位 ID
    ClientType client_type = 9;  // 客户端类型
    repeated string code_location_id_list = 10;  // 代码位 ID 列表
    VersionOperator version_operator = 11;  // 可见版本的比较运算符

    // 以下类型时有效
    // THIRD_PARTY_TARGET_USER_TYPE_USER_GROUP - 智能运营平台
    uint32 user_group_id = 101;  // 智能运营平台 ID

    // 以下类型时有效
    // THIRD_PARTY_ADS_SCENE_GAME_MIDWAY - 游戏中途
    uint32 display_time_interval = 201;  // 展示间隔，每间隔 x 个分钟展示一次广告，单位：秒
    uint32 text_prompt_after_x_seconds = 202;  // x 秒后显示文字提示，单位：秒（右下角文案提示）

    // 以下类型时有效
    // THIRD_PARTY_ADS_SCENE_POST_LIST - 帖子列表
    uint32 display_count_interval = 301;  // 展示间隔，每间隔 x 个帖子展示一次广告，单位：个

    // 以下类型时有效
    // THIRD_PARTY_ADS_SCENE_CHAPTER_LOAD - 章节加载
    // THIRD_PARTY_ADS_SCENE_UNLOCK_CHAPTER_ROOM - 解锁章节（房间）
    // THIRD_PARTY_ADS_SCENE_UNLOCK_CHAPTER_GAME - 解锁章节（游戏）
    // THIRD_PARTY_ADS_SCENE_UNLOCK_CLUE - 解锁线索
    // THIRD_PARTY_ADS_SCENE_CLUE_POPUP - 线索弹窗
    // THIRD_PARTY_ADS_SCENE_GAME_MIDWAY - 游戏中途
    // THIRD_PARTY_ADS_SCENE_CHAPTER_END - 章节结束
    // THIRD_PARTY_ADS_SCENE_CLUE_POPUP_146 - 线索弹窗 p.s v1.46.0 新增仅可选「信息流」的广告类型
    // THIRD_PARTY_ADS_SCENE_ADS_INTERACTION - 广告交互
    ThirdPartyTargetScenarioType target_scenario_type = 401;  // 剧本类型
    repeated uint32 scenario_id_list = 402;  // 剧本 ID 列表，THIRD_PARTY_TARGET_SCENARIO_TYPE_PART 时生效

    // 以下类型时有效
    // THIRD_PARTY_ADS_TYPE_BANNER - Banner
    uint32 refresh_interval = 501;  // 刷新间隔，单个 Banner 展示 x 秒后刷新，单位：秒
    string bg_color = 502;  // 背景色
    string border_img_url = 503;  // Banner 边框图片地址

    // 以下类型时有效
    // version_operator != VERSION_OPERATOR_ALL
    string version = 601;  // 版本号
    uint32 int_version = 602;  // 整型版本号，p.s 根据 version 进行转换，运营后台无需填写

    // 以下类型时有效
    // THIRD_PARTY_ADS_SCENE_CLUE_POPUP_146 - 信息流
    string ads_border_url = 701;  // 广告边框图片 URL
    string poster_url = 702;  // 海报图片 URL
    string jump_url = 703;  // 跳转链接
}

// 新增 or 更新第三方广告配置
message UpdateThirdPartyAdsRequest {
    ThirdPartyAdsConfig config = 1;  // 广告配置
}

message UpdateThirdPartyAdsResponse {

}

// 删除第三方广告配置
message DeleteThirdPartyAdsRequest {
    repeated string id_list = 1;  // ThirdPartyAdsConfig.id 列表
}

message DeleteThirdPartyAdsResponse {

}

// 获取第三方广告配置列表
message GetThirdPartyAdsConfigListRequest {
    // 数据源
    enum DataSource {
        DATA_SOURCE_UNSPECIFIED = 0;
        DATA_SOURCE_LOCAL_CACHE_SWITCH_ON = 1;  // 本地缓存（只保存开启的数据）
        DATA_SOURCE_MONGODB = 2;  // MongoDB（运营后台请求用这个枚举类型）
    }

    DataSource data_source = 1;  // 数据源
    ThirdPartyAdsScene scene = 2;  // 场景
    ThirdPartyAdsType type = 3;  // 广告类型
    string ads_name = 4;  // 广告位名称
    ThirdPartyAdsSwitch switch = 5;  // 开关
    ThirdPartyAdsConfig.ClientType client_type = 6;  // 客户端类型
    repeated uint32 scenario_id_list = 7;  // 剧本 ID 列表

    uint32 limit = 101;  // 限制条数
    uint32 offset = 102;  // 偏移量
}

message GetThirdPartyAdsConfigListResponse {
    uint32 total_count = 1;  // 总条数（只有 offset 为 0，且数据源是 DATA_SOURCE_MONGODB 时才会返回）
    repeated ThirdPartyAdsConfig config_list = 2;  // 广告配置列表
}
// = = = = = 「End」第三方广告 = = = = =
