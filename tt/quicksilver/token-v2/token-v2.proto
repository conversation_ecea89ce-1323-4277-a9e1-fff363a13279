syntax = "proto3";

package token.v2;

option go_package = "golang.52tt.com/protocol/services/token.v2";

service Token {
  rpc Decode(DecodeReq) returns (DecodeResp) {}

  rpc GetWechatAccessToken(WechatTokenReq) returns (WechatTokenResp) {}
  rpc GetWechatApiTicket(WechatTokenReq) returns (WechatTokenResp) {}

  rpc GrantToken(GrantTokenReq) returns (GrantTokenResp) {}
  rpc ValidateToken(ValidateTokenReq) returns (ValidateTokenResp) {}
  rpc RevokeAllTokens(RevokeAllTokensReq) returns (RevokeAllTokensResp) {}

  rpc GetAccessTokenPublicKeys(GetAccessTokenPublicKeysReq) returns (GetAccessTokenPublicKeysResp) {}

  // token-cpp
  rpc CreateTokenSeed(CreateTokenSeedReq) returns (CreateTokenSeedResp) {}
  rpc DecodeToken(DecodeTokenReq) returns (DecodeTokenResp) {}
  rpc ClearToken(ClearTokenReq) returns (ClearTokenResp) {}
}

message WechatTokenReq {
  string wx_app_id = 1;
}

message WechatTokenResp {
  string wx_token = 1;
}

message TokenInfo {
  uint32 user_id = 1;
  uint32 terminal_type = 2;
  uint64 expires_at = 3;
  string user_name = 4;
  string session = 5;
  repeated string scopes = 6;
}

message DecodeReq {
  uint32 app_id = 1;
  string token = 2;
}

message DecodeResp {
  TokenInfo token_info = 1;
}

enum TokenType {
  UNDEFINED = 0;
  ACCESS_TOKEN = 1;
  REFRESH_TOKEN = 2;
}

// 不同命名空间下分开保存 refreshToken
enum TokenNamespace {
  // 未指定
  TOKEN_NAMESPACE_UNSPECIFIED = 0;
  // 登录
  TOKEN_NAMESPACE_AUTH = 1;
}

message TokenInfoV2 {
  uint32 uid = 1;
  bytes device_id = 2;
  uint32 client_type = 3;
  uint32 terminal_type = 4;
  repeated string scopes = 5;
  map<string, string> extra = 6;
  repeated string aud = 7;
  uint32 acc_ttl = 8;  // sec
  uint32 ref_ttl = 9;  // sec
  TokenNamespace namespace = 10;
}

message GrantedToken {
  TokenType type = 1;
  string token = 2;
  int64 expires_in = 3;
  string token_id = 4;
}

message GrantTokenReq {
  TokenInfoV2 token_info = 1;
}

message GrantTokenResp {
  GrantedToken access_token = 1;
  GrantedToken refresh_token = 2;
}

message ValidateTokenReq {
  TokenType type = 1;
  string token = 2;
}

message ValidateTokenResp {
  TokenInfoV2 token_info = 1;
}

message RevokeOptions {
  repeated string exclude_access_tokens = 1;
  repeated string exclude_refresh_tokens = 2;
  string device_id = 3;
  repeated bytes include_device_ids = 4;
  TokenNamespace namespace = 5;
}

message RevokeAllTokensReq {
  uint32 uid = 1;
  RevokeOptions opts = 2;
}

message RevokeAllTokensResp {
  // nothing
}

message GetAccessTokenPublicKeysReq {
  string key_id = 1;
}

message GetAccessTokenPublicKeysResp {
  string jwk_format_keys = 1;
}

enum TokenBizType {
  TokenBiz_UNKNOWN = 0;
  TokenBiz_SDK = 1;
  TokenBiz_TT_WEB = 2;
}

message TokenSeed {
  uint32 uid = 1;
  string seed = 2;
  uint32 expire_time = 3;
}

message CreateTokenSeedReq {
  uint32 uid = 1;
  uint32 biz = 2;
  bool replace_existed = 3;
}

message CreateTokenSeedResp {
  TokenSeed token_seed = 1;
}

message DecodeTokenReq {
  bytes token = 1;
  uint32 biz = 2;
  bool refresh_expire = 3;
}

message DecodeTokenResp {
  uint32 uid = 1;
}

message ClearTokenReq {
  uint32 uid = 1;
}

message ClearTokenResp {
  // nothing
}
