syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/anti-go";

package anti_go;

service AntiGO {

  rpc SetUserProfile (SetUserProfileReq) returns (SetUserProfileResp) {}
  rpc GetUserProfile (GetUserProfileReq) returns (GetUserProfileResp) {}
  rpc BatchSetUserProfile (BatchSetUserProfileReq) returns (BatchSetUserProfileResp) {}
  rpc BatchGetUserProfile (BatchGetUserProfileReq) returns (BatchGetUserProfileResp) {}

}

// buf:lint:ignore ENUM_PASCAL_CASE
enum USER_PROFILE {
  USER_PROFILE_NORMAL = 0; //大号
  USER_PROFILE_FAKE = 1;   //小号
  USER_PROFILE_SUSPICOUS = 2;  //可疑账号
}

message SetUserProfileReq {
  uint64 uid = 1;
  int32 profile	= 2;
  uint32 reason_code = 3;
  string reason	= 4;
}

message SetUserProfileResp {
}

message GetUserProfileReq {
  uint64 uid = 1;
  bool with_detail = 2;
}

message GetUserProfileResp {
  uint64 uid = 1;
  int32 profile = 2;
  uint32 reason_code = 3;
  string reason = 4;
}

message BatchSetUserProfileReq {
  repeated uint64 uid_list = 1;
  int32 profile	= 2;
  uint32 reason_code = 3;
  string reason	= 4;
}

message BatchSetUserProfileResp {
}

message BatchGetUserProfileReq {
  repeated uint64 uid_list = 1;
}

message BatchGetUserProfileResp {
  repeated GetUserProfileResp user_profile_list = 1;
}