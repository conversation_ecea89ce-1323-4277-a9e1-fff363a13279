syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/singaround";
// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package singaround;


service SingARound {
    /* 匹配 */
    rpc StartSingingGameMatching (StartSingingGameMatchingReq) returns (StartSingingGameMatchingResp) {}
    // 加入游戏（观众加入游戏时使用）
    rpc JoinSingingGame (JoinSingingGameReq) returns (JoinSingingGameResp) {}
    // 获取开始倒计时
    rpc GetSingingGameCountdown(GetSingingGameCountdownReq) returns (GetSingingGameCountdownResp) {}
    // 准备（开始游戏）
    rpc StartSingingGame(StartSingingGameReq) returns (StartSingingGameResp) {}
    // 抢唱
    rpc GrabSingingGameMic(GrabSingingGameMicReq) returns (GrabSingingGameMicResp) {}
    // 请求帮唱
    rpc AskForSingingHelp(AskForSingingHelpReq) returns (AskForSingingHelpResp) {}
    // 帮唱
    rpc AnswerSingingHelp(AnswerSingingHelpReq) returns (AnswerSingingHelpResp) {}
    // 完成演唱
    rpc AccomplishSingingGameSong(AccomplishSingingGameSongReq) returns (AccomplishSingingGameSongResp) {}
    // 上报积分
    rpc ReportSingingGameSongScore(ReportSingingGameSongScoreReq) returns (ReportSingingGameSongScoreResp) {}
    // 用户房间状态（客户端重连时使用）
    rpc GetSingingGameChannelInfo(GetSingingGameChannelInfoReq) returns (GetSingingGameChannelInfoResp) {}
    // 切换游戏类型
    rpc SwitchSingingGameType(SwitchSingingGameTypeReq) returns (SwitchSingingGameTypeResp) {}
    // 牛啊
    rpc ExpressSingingGameLike(ExpressSingingGameLikeReq) returns (ExpressSingingGameLikeResp) {}
    // 碰拳
    rpc SingingGameFistBump(SingingGameFistBumpReq) returns (SingingGameFistBumpResp) {}
    // 开始游戏（UGC房）
    rpc StartUgcChannelSingingGame(StartUgcChannelSingingGameReq) returns (StartUgcChannelSingingGameResp) {}
    // 取消准备（UGC房）
    rpc CancelSingingGamePreparation(CancelSingingGamePreparationReq) returns (CancelSingingGamePreparationResp) {}

    /* 拉全部形象 装饰配置 */
    rpc GetAllSingImageConf (GetAllSingImageConfReq) returns (GetAllSingImageConfResp) {}
    /* 用户可见形象 */
    rpc GetUserSingImage (GetUserSingImageReq) returns (GetUserSingImageResp) {}
    rpc AwardUserSingImage (AwardUserSingImageReq) returns (AwardUserSingImageResp) {}
    rpc SetUserSingImage (SetUserSingImageReq) returns (SetUserSingImageResp) {}
    rpc BatchAwardUser (BatchAwardUserReq) returns (BatchAwardUserResp) {}
    rpc GetAwardRecord (GetAwardRecordReq) returns (GetAwardRecordResp) {}
    rpc UpdateSingImageConf (UpdateSingImageConfReq) returns (UpdateSingImageConfResp) {}
    rpc DelSingImageConf (DelSingImageConfReq) returns (DelSingImageConfResp) {}
    rpc UpdateConfVersion (UpdateConfVersionReq) returns (UpdateConfVersionResp) {}

    /* 歌曲基础信息 */
    rpc SetSongBaseInfo (SetSongBaseInfoReq) returns (SetSongBaseInfoResp) {}
    /* 歌曲文件 */
    rpc CheckSongExist (CheckSongExistReq) returns (CheckSongExistResp) {}
    rpc SetSongFile (SetSongFileReq) returns (SetSongFileResp) {}
    rpc UpdateSongFile (UpdateSongFileReq) returns (UpdateSongFileResp) {}
    rpc GetSongList (GetSongListReq) returns (GetSongListResp) {}
    rpc DelSong (DelSongReq) returns (DelSongResp) {}
    rpc GetSongForApp (GetSongForAppReq) returns (GetSongForAppResp) {}

    /* 专区配置 */
    rpc AddSingingGame (AddSingingGameReq) returns (AddSingingGameResp) {}
    rpc UpdateSingingGame (UpdateSingingGameReq) returns (UpdateSingingGameResp) {}
    rpc GetSingingGame (GetSingingGameListReq) returns (GetSingingGameListResp) {}
    rpc SetSingingGameRank (SetSingingGameRankReq) returns (SetSingingGameRankResp) {}
    rpc DelSingingGame (DelSingingGameReq) returns (DelSingingGameResp) {}

    /* 推荐侧 */
    rpc GetEnableTags (GetEnableTagsReq) returns (GetEnableTagsResp) {}

    /* 客户端专区列表 */
    rpc GetSingGameForApp (GetSingingGameForAppReq) returns(GetSingingGameForAppResp) {}

    /* 获取轮次信息 */
    rpc GetSingingGameRoundInfo (GetSingingGameRoundInfoReq) returns (GetSingingGameRoundInfoResp) {}

    /* 闯关专区 */
    // 投票
    rpc SingingGameVote (SingingGameVoteReq) returns (SingingGameVoteResp) {}
    // 完成资源加载
    rpc AccomplishLoadingRes (AccomplishLoadingResReq) returns (AccomplishLoadingResResp) {}
    // 排行榜
    rpc GetSingingGameRankList (GetSingingGameRankListReq) returns (GetSingingGameRankListResp) {}
    // 通关模式游戏记录
    rpc GetSingingGameRecordList (GetSingingGameRecordListReq) returns (GetSingingGameRecordListResp) {}
    // 新增曲风
    rpc AddSongStyle (AddSongStyleReq) returns (AddSongStyleResp) {}
    // 删除曲风
    rpc DelSongStyle (DelSongStyleReq) returns (DelSongStyleResp) {}
    // 获取所有曲风
    rpc GetAllSongStyle (GetAllSongStyleReq) returns (GetAllSongStyleResp) {}
    // 低质用户行为记录
    rpc GetLessUser (GetLessUserReq) returns (GetLessUserResp) {}
    // 获取个人抢唱信息
    rpc GetSingingGameUserInfo (GetSingingGameUserInfoReq) returns (GetSingingGameUserInfoResp) {}
}

/* 匹配 */
message StartSingingGameMatchingReq{
    uint32 uid = 1;
    SingingGameType singing_game_type = 2; // 游戏类型
    uint32 new_singing_game_type = 3; // 可配置游戏类型
}
message StartSingingGameMatchingResp{
    uint32 channel_id = 1; // 房间id
}

// 加入游戏（观众加入游戏时使用）
message JoinSingingGameReq {
    uint32 channel_id = 1;
    uint32 uid = 2;
}
message JoinSingingGameResp {
    bool is_success = 1;
    bool is_playing = 2; /* 是否游戏中 */
    repeated uint32 uid_list = 3; // 玩家用户id
}

// 获取开始倒计时
message GetSingingGameCountdownReq{
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 round_id = 3;
}
message GetSingingGameCountdownResp{
    uint32 countdown = 1;
}

// 准备（开始游戏）
message StartSingingGameReq{
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 round_id = 3;
}
message StartSingingGameResp{
    SingingGameChannelInfo channel_info = 1;
}

// 抢唱
message GrabSingingGameMicReq{
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 round_id = 3;
    string song_id = 4;
}
message GrabSingingGameMicResp{
    SingingGameChannelInfo channel_info = 1;
    bool is_success = 2;
}

// 请求帮唱
message AskForSingingHelpReq{
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 round_id = 3;
    string song_id = 4;
    bool auto_req = 5; // 是否自动请求
}
message AskForSingingHelpResp{
    SingingGameChannelInfo channel_info = 1;
}

// 帮唱
message AnswerSingingHelpReq{
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 round_id = 3;
    string song_id = 4;
}
message AnswerSingingHelpResp{
    SingingGameChannelInfo channel_info = 1;
    bool is_success = 2;
}

// 完成演唱
message AccomplishSingingGameSongReq{
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 round_id = 3;
    string song_id = 4;
}
message AccomplishSingingGameSongResp{
    SingingGameChannelInfo channel_info = 1;
}

// 上报积分
message ReportSingingGameSongScoreReq{
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 round_id = 3;
    string song_id = 4;
    uint32 score = 5;
}
message ReportSingingGameSongScoreResp{
    SingingGameSongResult song_result = 1;
    bool is_report_audio = 2; /* 是否音频抽样上报 */
}

message SingingGameSongResult{
    SingingGameChannelInfo channel_info = 1;
    SingingGameScoreDetail score_detail = 2;
}

message SingingGameScoreDetail{
    bool success = 1; // 是否成功
    uint32 helper_score = 2; // 帮唱的人本次得分
    repeated string scores = 3; // 加分详情
    string account = 4; // 接唱/帮唱的人
}

// 用户房间状态（客户端重连时使用）
message GetSingingGameChannelInfoReq{
    uint32 channel_id = 1;
}
message GetSingingGameChannelInfoResp{
    SingingGameChannelInfo channel_info = 1;
    repeated SongInfo songs = 2;
    uint32 grab_mic_btn_hidden_at = 3; // 抢唱按钮隐藏时间（单位：秒）
    uint32 grab_mic_btn_display_time = 4; // 抢唱按钮持续展示时间（单位：秒）
    uint32 help_btn_hidden_at = 5; // 帮唱按钮隐藏时间（单位：秒）
    uint32 help_btn_display_time = 6; // 帮唱按钮持续展示时间（单位：秒）
    repeated SingingGameSongStyle styles = 7; // 随机曲风（投票阶段）
    SingingGameSongStyle selected_style = 8; // 曲风（预加载阶段）
    uint32 vote_countdown = 9; // 投票倒计时
    uint32 no_prepare_ban_duration = 11; // 不准备禁止时间（单位：分钟）
    uint32 exit_game_ban_duration = 12; // 退游禁止时间（单位：分钟）
}

// 切换游戏类型
message SwitchSingingGameTypeReq{
    uint32 uid = 1;
    uint32 channel_id = 2;
    SingingGameType singing_game_type = 3; // 游戏类型
    uint32 new_singing_game_type = 4; // 可配置游戏类型
}
message SwitchSingingGameTypeResp{
    SingingGameChannelInfo channel_info = 1;
    repeated SongInfo songs = 2;
}

// 房间全量状态信息
message SingingGameChannelInfo{
    uint32 channel_id = 1; // 房间id
    uint32 member_limit = 2; // 成员限制
    SingingGameType singing_game_type = 3; // 游戏类型
    uint32 song_countdown = 4; // 接唱/帮唱倒计时
    uint32 max_life_count = 5; // 最大生命值
    uint32 cur_round_id = 6; // 当前游戏id
    bool is_playing = 7; // 是否游戏中
    SingingGameRoundStage stage = 8; // 游戏状态
    uint32 stage_updated_at = 9; // 游戏状态修改时间
    string cur_song_id = 10; // 当前歌曲id
    uint32 cur_song_index = 11; // 当前是第几首歌（从1开始）
    repeated SingingGamePosition positions = 12; // 游戏位
    uint32 version = 13; // 版本
    uint32 minimum_player_num = 14; // 最少玩家数量
    uint32 controller_id = 15; // 管理游戏（开始游戏/修改游戏类型）的人
    uint32 new_singing_game_type = 16; // 新游戏(专区)类型,当新增字段类型是走音房时，旧字段类型是走音；当新增字段类型不是走音房时，旧字段类型需要赋值为歌神
    string official_group_info = 17; // 官方qq群
    oneof extra{ // 方便放各种专区的扩展字段
        PassThroughExtra pass_through_extra = 18;
    }
}

message PassThroughExtra{
    uint32 cur_skip_count = 1; // 当前弃歌数量
    uint32 max_skip_count = 2; // 最大弃歌数量
    uint32 cur_song_index = 3; // 当前关数（从1开始）
    uint32 pass_through_count = 4; // 已闯关数
}

enum SingingGameRoundStage{
    SingingGameRoundStageUndefined = 0; // 未定义
    SingingGameRoundStageGrabMic = 1; // 抢唱
    SingingGameRoundStageSinging = 2; // 接唱
    SingingGameRoundStageReqHelp1 = 3; // 请求帮唱1
    SingingGameRoundStageReqHelpFailed1 = 4; // 请求帮唱失败1
    SingingGameRoundStageComparing = 5; // 对比分数中
    SingingGameRoundStageResult = 6; // 比对结果
    SingingGameRoundStageReqHelp2 = 7; // 请求帮唱2
    SingingGameRoundStageReqHelpFailed2 = 8; // 请求帮唱失败2
    SingingGameRoundStageHelperSinging = 9; // 帮唱中
    SingingGameRoundStageHelperComparing = 10; //帮唱对比
    SingingGameRoundStageHelperResult = 11; // 帮唱的结果
    SingingGameRoundStagePartialResult = 12; // 本轮结果（闯关专区）
    SingingGameRoundStageVote = 13; // 投票（闯关专区）
    SingingGameRoundStagePreload = 14; // 预加载资源（闯关专区）
}

message SingingGamePosition{
    uint32 uid = 1; // 用户id
    string nickname = 2; // 昵称
    string account = 3; // 头像
    uint32 index = 4; // 位置（从1开始）
    bool prepared = 5; // 是否已准备
    uint32 left_life_count = 6; // 剩余生命数，为0时淘汰
    SingingGameUserType user_type = 7; // 身份类型
    uint32 score = 8; // 分数
    string user_set_image_id = 9; /* 用户设置形象后房间推送 */
    string decoration_id = 10;
    uint32 sex = 11; // 性别
    bool have_run_away = 12; // 是否逃跑
    string precentor_tag = 13; // 领唱人标识url
}

enum SingingGameUserType{
    SingingGameUserTypeUndefined = 0;
    SingingGameUserTypeSinger = 1; // 抢唱的人
    SingingGameUserTypeHelper = 2; // 帮唱的人
}

message SingingGameSongStyle{
    string id = 1;
    string desc = 2;
    uint32 supported_num = 3;
    bool is_open = 4;
}

// 投票
message SingingGameVoteReq{
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 round_id = 3;
    string style_id = 4;
}
message SingingGameVoteResp{
    SingingGameChannelInfo channel_info = 1;
    repeated SingingGameSongStyle styles = 2; // SingingGameRoundStageVote阶段返回
    SingingGameSongStyle style = 3; // SingingGameRoundStagePreload阶段返回
    repeated SongInfo songs = 4; // SingingGameRoundStagePreload阶段返回
    uint32 vote_countdown = 5; // 投票倒计时
}

// 完成资源加载
message AccomplishLoadingResReq{
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 round_id = 3;
}
message AccomplishLoadingResResp{
    SingingGameChannelInfo channel_info = 1;
}

enum RankListType{
    RankListTypeUndefined = 0;
    RankListTypeFriend = 1; // 好友榜
    RankListTypeAll = 2; // 总榜
    RankListTypeDay = 3; // 日版
}

// 排行榜
message GetSingingGameRankListReq{
    uint32 uid = 1;
    RankListType type = 2;
}
message GetSingingGameRankListResp{
    repeated SingingGameRankListItem list = 1;
    SingingGameRankListItem personal_rank = 2; // 个人排名
}

// 通关模式游戏记录
message GetSingingGameRecordListReq{
    uint32 uid = 1;
    uint32 count = 2;
    SingingGameLoadMore load_more = 3; // 首次拉取不传, 加载更多时原封不动地填入上一次GetSingingGameRecordListResp中的load_more字段
}
message GetSingingGameRecordListResp{
    repeated SingingGameRankListItem list = 1;
    SingingGameLoadMore load_more = 2;   // 下一次加载更多时, 将load_more原封不动地填入请求的load_more中; 如果不包含此字段, 表示已经拉完了
}

enum SingingGameResultType{
    SingingGameResultTypeUndefined = 0;
    SingingGameResultTypeFailed = 1; // 通关失败
    SingingGameResultTypeNormal = 2; // 普通通关
    SingingGameResultTypeCooperative = 3; // 抱团通关
    SingingGameResultTypePerfect = 4; // 完美通关
}

message SingingGameRankListItem{
    uint32 rank = 1;
    repeated string accounts = 2;
    uint32 pass_through_count = 3; // 闯关数量
    uint32 updated_at = 4;
    SingingGameResultType result_type = 5;
}

message SingingGameLoadMore {
    uint32 last_page = 1;
    uint32 last_count = 2;
}

// 添加曲风
message AddSongStyleReq{
    SingingGameSongStyle style = 1;
}
message AddSongStyleResp{
    SingingGameSongStyle style = 1;
}

// 删除曲风
message DelSongStyleReq{
    string style_id = 1;
}
message DelSongStyleResp{
}

// 获取所有曲风
message GetAllSongStyleReq{}
message GetAllSongStyleResp{
    repeated SingingGameSongStyle styles = 1;
}

enum SingingGameType{
    SingingGameTypeUndefined = 0;
    SingingGameTypeExpert = 1;        // 高手
    SingingGameTypeRookie = 2;        // 走音
    SingingGameTypePassThrough = 999; // 闯关
    SingingGameTypePassThrough2 = 1000; // 通关模式
}

// 牛啊
message ExpressSingingGameLikeReq{
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 round_id = 3;
    string song_id = 4;
    uint32 count = 5;
}
message ExpressSingingGameLikeResp{}

// 碰拳
message SingingGameFistBumpReq{
    uint32 uid = 1;
    uint32 channel_id = 2;
    repeated uint32 uids = 3;
}
message SingingGameFistBumpResp{}

// 开始游戏（UGC房）
message StartUgcChannelSingingGameReq{
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 round_id = 3;
}
message StartUgcChannelSingingGameResp{}

// 取消准备（UGC房）
message CancelSingingGamePreparationReq{
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 round_id = 3;
}
message CancelSingingGamePreparationResp{}


/*形象信息*/
enum UserImageType{
    NormalImage = 0;
    DefaultImage = 1;
    LockImage = 2;
}
message UserImageConf{
    string image_id = 1;
    string image_name = 2;
    string image_url = 3; /* 图片url */
    string md5 = 4;
    uint32 image_type = 5; /* UserImageType */
    uint32 index = 6;
    string default_icon_url = 7; /* 默认图 */
    string default_sing_avatar_url = 8; /* 默认大图 */
}
/* 唱歌时装饰 */
message DecorationInfo{
    string decoration_id = 1;
    string decoration_name = 2;
    string decoration_url = 3;
    string md5 = 4;
}
message GetAllSingImageConfReq{
    uint32 version = 1;
}
message GetAllSingImageConfResp{
    repeated UserImageConf user_image_conf_list = 1;
    repeated DecorationInfo decoration_list = 2;
    uint32 version = 3;
}
message GetUserSingImageReq{
    uint32 uid = 1;
}
message GetUserSingImageResp{
    repeated string image_id_list = 1;
    string user_set_image_id = 2;
    string decoration_id = 3;
    uint32 decoration_end_ts = 4;
    message LockImage{
        string image_id = 1;
        string tip = 2;
    }
    repeated LockImage lock_images = 5; /* 用户未拥有的形象列表 */
    bool is_precentor = 6; // 是否是领唱者
}
/* 解锁用户的形象 */
message AwardUserSingImageReq{
    uint32 uid = 1;
    string image_id = 2;
}
message AwardUserSingImageResp{
}
message UserDecorationInfo{
    string decoration_id = 1;
    uint32 start_ts = 2;
    uint32 end_ts = 3;
}
message PrecentorTag{
    uint32 start_ts = 1;
    uint32 end_ts = 2;
}
message SetUserSingImageReq{
    uint32 uid = 1;
    string image_id = 2;
    UserDecorationInfo decoration = 3;
    uint32 channel_id = 4;
    PrecentorTag precentor_tag = 5;
}
message SetUserSingImageResp{
}

message BatchAwardUserReq{
    repeated uint32 uids = 1;
    oneof award{
        string image_id = 2; // 形象id
        UserDecorationInfo decoration = 3; // 装饰
        PrecentorTag precentor_tag = 4; // 领唱者标识
    }
    uint32 day = 5; // 多少天
    string operator = 6;
}
message BatchAwardUserResp{}

message GetAwardRecordReq{
    uint32 page = 1;
    uint32 count = 2;
}
message GetAwardRecordResp{
    uint32 total_count = 1;
    repeated AwardRecord records = 2;
}

message AwardRecord{
    string ttid = 1;
    AwardType award_type = 2; // 奖励类型
    string award = 3; // 奖励
    uint32 awarded_at = 4; // 发放时间
    uint32 expired_at = 5; // 过期时间
    string operator = 6;
}

enum AwardType{
    AwardType_Undefined = 0;
    AwardType_Image = 1; // 形象
    AwardType_Decoration = 2; // 装饰
    AwardType_Precentor = 3; // 领唱者标识
}

message UpdateSingImageConfReq{
    repeated UserImageConf user_image_conf_list = 1;
    repeated DecorationInfo decoration_list = 2;
}
message UpdateSingImageConfResp{
    repeated string failed_list = 1; /* 失败的名称 */
}
message DelSingImageConfReq{
    repeated string image_id_list = 1;
    repeated string decoration_id_list = 2;
}
message DelSingImageConfResp{
}

message UpdateConfVersionReq{
}
message UpdateConfVersionResp{
}

/* 歌曲管理 */
message SongResInfo{

}
message SongInfo{
    string song_id = 1;
    string song_name = 2;
    string singer = 3;
    string lyricist = 4;
    string composer = 5;
    string origin_singer = 6;
    string upload_user = 7;
    uint32 upload_uid = 12; // 新版改为传uid
    string upload_ttid = 13; // 新版展示ttid
    uint32 gender = 8;
    uint32 lib_type = 9; /* SingingGameType */
    string file_url = 10; /* 歌曲歌词文件zip */
    string md5 = 11;
    uint32 section_id = 14;
    string style_id = 15; // 曲风id
}

message CheckSongExistReq{
    repeated SongInfo song_list = 1;
}
message CheckSongExistResp{
    repeated SongInfo exist_list = 1;
    repeated SongInfo non_exist_list = 2;
}
message SetSongBaseInfoReq{
    repeated SongInfo infos = 1;
}
message SetSongBaseInfoResp{
    repeated string duplicative_name_list = 1;
}
message SongFileInfo{
    string song_name = 1;
    string singer = 2;
    uint32 lib_type = 3; /* SingingGameType */
    string file_url = 4;
    string md5 = 5;
    uint32 section_id = 6;
}
message SetSongFileReq{
    repeated SongFileInfo song_list = 1;
}
message SetSongFileResp{
    repeated SongInfo duplicative_name_list = 1;
    repeated SongInfo non_existed_name_list = 2;
}
message GetSongListReq{
    string song_name = 1;
    string singer = 2;
    uint32 lib_type = 3; /* SingingGameType */
    uint32 offset = 4;
    uint32 limit = 5;
    enum FileExist{
        NoneSense = 0;
        OnlyNotExistFile = 1; /* 只返回不存在歌曲歌词文件的列表 */
        OnlyExistFile = 2; /* 只返回存在歌曲歌词文件的列表 */
    }
    FileExist file_exist = 6;
    string style_id = 7; // 曲风id
}
message GetSongListResp{
    repeated SongInfo infos = 1;
    uint32 total = 2;
}
message DelSongReq{
    repeated SongInfo song_list = 1;
}
message DelSongResp{
}
message GetSongForAppReq{
    uint32 tag = 1; /* SingingGameType */
    uint32 count = 2;
}
message GetSongForAppResp{
    repeated SongInfo song_id_list = 1;
}

message UpdateSongFileReq {
    repeated SongInfo infos = 1;
}

message UpdateSongFileResp {}



/* 专区配置 */
enum SingingZoneType {
    SingingZoneTypeUndefined = 0;
    SingingZoneTypeNormal    = 1;   // 普通专区
    SingingZoneTypeFestival  = 2;   // 节日专区
    SingingZoneTypePassThrough = 3; // 闯关专区
}

message SingingGameInfo {
    uint32 singing_game_type  = 1; // 专区类型
    string name               = 2; // 专区名称
    string descr              = 3; // 专区描述
    uint32 zone_type          = 4; // 普通/节日专区类型
    uint32 hp                 = 5; // 生命值
    uint32 same_sex_score     = 6; // 同性通过分数
    uint32 opposite_sex_score = 7; // 异性通过分数
    uint32 rank               = 8; // 排序
    uint32 rank_time          = 9; // 调整排序的时间, 同rank按这个倒排
    uint32 start_time         = 10; // 有效期起
    uint32 end_time           = 11; // 有效期止
    string inlet_icon         = 12; // 入口图标
    string inlet_color        = 13; // 入口色值
    string ready_cover        = 14; // 准备页封面
    string zone_button        = 15; // 分区按钮
    string zone_ready_bg      = 16; // 分区准备页背景
    string zone_bg            = 17; // 分区背景
    bool   is_open            = 18; // 是否上线
    uint32 member_limit       = 19; // 开局人数
}

message AddSingingGameReq {
    SingingGameInfo singing_game_info = 1;
}

message AddSingingGameResp {}

message UpdateSingingGameReq {
    SingingGameInfo singing_game_info = 1;
}

message UpdateSingingGameResp {}

message GetSingingGameListReq {}

message GetSingingGameListResp {
    repeated SingingGameInfo singing_game_infos = 2;
}

message SetSingingGameRankReq {
    repeated uint32 singing_game_types = 1;
}

message SetSingingGameRankResp {}

message DelSingingGameReq {
    repeated uint32 singing_game_types = 1;
}

message DelSingingGameResp {}

message GetEnableTagsReq {}

message GetEnableTagsResp {
    repeated Tag tags = 1;
}

message Tag {
    uint32 tag_id   = 1;
    string name      = 2; // 名称
    string descr     = 3; // 描述
    uint32 zone_type = 4; // 专区类型
}

message GetSingingGameForAppReq {
    uint32 current_game_type = 1;
}

message GetSingingGameForAppResp {
    repeated SingingGameInfoForApp singing_game_infos = 1;
}

message SpecialGameZoneInfo{
    uint32 style = 1; /* 置顶:1; */
    bool is_user_channel = 2; /* 是否进入个人房 */
    bool is_need_share = 3; /* 是否需要分享 */
    uint32 tab_id = 4;
    string tab_name = 5;
}
message SingingGameInfoForApp {
    uint32 type            = 1; // 专区类型
    string name            = 2; // 专区名称
    string descr           = 3; // 专区描述
    string inlet_icon      = 4; // 入口图标
    string inlet_color     = 5; // 入口色值
    string ready_cover     = 6; // 准备页封面
    string zone_button     = 7; // 专区按钮
    string zone_ready_bg   = 8; // 专区准备页背景
    string zone_bg         = 9; // 专区背景
    string time_limit_icon = 10; // 限时图标icon
    SpecialGameZoneInfo special_game_info = 11; /* 特殊的专区设置 */
}

message GetSingingGameRoundInfoReq {
    repeated uint32 channel_ids = 1; // 房间id
}

message GetSingingGameRoundInfoResp {
    repeated RoundInfo round_infos = 1;
}

message RoundInfo {
    uint32 channel_id = 1;
    uint32 song_index = 2; // 当前在唱歌曲下表
    uint32 song_total = 3; // 总歌曲数
    string singing_game_name = 4; // 专区名称
    string status = 5; // 状态
    string song_name = 6; // 歌曲名称
    string singer = 7; // 歌曲原唱
    SingingGameRoundStage stage = 8;
    uint32 tag_id = 9;
}

/*低质用户行为记录*/
message GetLessUserReq {
    enum GameType{
        SingARound = 0;
        KTV = 1;
    }
    uint32 uid = 1;
    uint32 game_type = 2; /* GameType */
    uint32 channel_id = 3;
}
message GetLessUserResp {
    bool is_limit = 1; /* true 被限制行为 */
}

message GetSingingGameUserInfoReq{
    uint32 uid = 1;
}
message GetSingingGameUserInfoResp{
    uint32 grab_mic_time = 1;
    uint32 grab_mic_success_time = 2;
    uint32 like_count = 3; // 牛啊数量
}