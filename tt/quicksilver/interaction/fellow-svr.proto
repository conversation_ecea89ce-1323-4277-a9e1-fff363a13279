syntax = "proto3";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package fellow_svr;

import "tt/quicksilver/unified_pay/cb/pay_callback.proto";
import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";


option go_package = "golang.52tt.com/protocol/services/fellow-svr";


service FellowSvr {
    // 获取挚友名单
    rpc GetFellowList (GetFellowListReq) returns (GetFellowListResp) {
    }

    // 获取可以申请挚友关系的用户名单
    rpc GetFellowCandidateList (GetFellowCandidateListReq) returns (GetFellowCandidateListResp) {
    }

    // 获取对应用户的挚友申请相关信息
    rpc GetFellowCandidateInfo (GetFellowCandidateInfoReq) returns (GetFellowCandidateInfoResp) {
    }

    // 发送挚友申请（邀请函）
    rpc SendFellowInvite (SendFellowInviteReq) returns (SendFellowInviteResp) {
    }

    // 获取对应id的邀请函信息
    rpc GetFellowInviteInfoById (GetFellowInviteInfoByIdReq) returns (GetFellowInviteInfoByIdResp) {
    }

    // 获取待处理的邀请函列表
    rpc GetFellowInviteList (GetFellowInviteListReq) returns (GetFellowInviteListResp) {
    }

    // 处理挚友申请
    rpc HandleFellowInvite (HandleFellowInviteReq) returns (HandleFellowInviteResp) {
    }

    // 获取web挚友信息
    rpc GetWebFellowInfo (GetWebFellowListReq) returns (GetWebFellowListResp) {
    }

    // 检查邀请坑位
    rpc CheckFellowInvite (CheckFellowInviteReq) returns (CheckFellowInviteResp) {
    }

    // 获取挚友值
    rpc GetFellowPoint (GetFellowPointReq) returns (GetFellowPointResp) {
    }

    // 撤销挚友邀请
    rpc CancelFellowInvite (CancelFellowInviteReq) returns (CancelFellowInviteResp) {

    }

    // 增加信物配置
    rpc AddFellowPresentConfig (AddFellowPresentConfigReq) returns (AddFellowPresentConfigResp) {

    }

    // 删除信物配置
    rpc DelFellowPresentConfig (DelFellowPresentConfigReq) returns (DelFellowPresentConfigResp) {

    }

    // 更新信物配置
    rpc UpdateFellowPresentConfig (UpdateFellowPresentConfigReq) returns (UpdateFellowPresentConfigResp) {

    }

    // 获取信物配置
    rpc GetAllFellowPresentConfig (GetAllFellowPresentConfigReq) returns (GetAllFellowPresentConfigResp) {

    }

    // 获取所有信物配置
    rpc GetFellowPresentConfigById (GetFellowPresentConfigByIdReq) returns (GetFellowPresentConfigByIdResp) {

    }

    // 解锁挚友位
    rpc UnlockFellowSite (UnlockFellowSiteReq) returns (UnlockFellowSiteResp) {

    }

    // 解绑关系申请
    rpc UnboundFellow (UnboundFellowReq) returns (UnboundFellowResp) {

    }

    // 取消解绑关系
    rpc CancelUnboundFellow (CancelUnboundFellowReq) returns (CancelUnboundFellowResp) {

    }

    // 赠送挚友信物
    rpc SendFellowPresent (SendFellowPresentReq) returns (SendFellowPresentResp) {

    }

    // 获取挚友信物赠送页信息
    rpc GetFellowPresentDetail (GetFellowPresentDetailReq) returns (GetFellowPresentDetailResp) {

    }

    rpc Notify (unified_pay.cb.PayNotify) returns (unified_pay.cb.PayNotifyResponse) {

    }

    // 获取挚友绑定类型
    rpc GetFellowType (GetFellowTypeReq) returns (GetFellowTypeResp) {

    }

    //　根据对方uid 获取挚友信息
    rpc GetFellowInfoByUid (GetFellowInfoByUidReq) returns (GetFellowInfoByUidResp) {

    }

    // 房间内直接赠送信物
    rpc ChannelSendFellowPresent (ChannelSendFellowPresentReq) returns (ChannelSendFellowPresentResp) {

    }

    // 在房间内发起挚友邀请
    rpc SendChannelFellowInvite (SendChannelFellowInviteReq) returns (SendChannelFellowInviteResp) {

    }

    // 处理房间内的挚友邀请
    rpc HandleChannelFellowInvite (HandleChannelFellowInviteReq) returns (HandleChannelFellowInviteResp) {

    }

    rpc GetRoomFellowList (GetRoomFellowListReq) returns (GetRoomFellowListResp) {

    }

    // 获取在该房间的所有挚友邀请信息
    rpc GetAllChannelFellowInvite (GetAllChannelFellowInviteReq) returns (GetAllChannelFellowInviteResp) {

    }

    // 立即解绑
    rpc DirectUnboundFellow (DirectUnboundFellowReq) returns (DirectUnboundFellowResp) {
    }

    // 获取房间申请的相关信息
    rpc GetChannelFellowCandidateInfo (GetChannelFellowCandidateInfoReq) returns (GetChannelFellowCandidateInfoResp) {
    }

    // 获取麦上挚友信息
    rpc GetOnMicFellowList (GetOnMicFellowListReq) returns (GetOnMicFellowListResp) {
    }


    // 获取麦上挚友信息
    rpc GetSendInviteList (GetSendInviteListReq) returns (GetSendInviteListResp) {
    }


    rpc ChangeFellowBindType (ChangeFellowBindTypeReq) returns (ChangeFellowBindTypeResp) {
    }

    // 获取挚友列表
    rpc GetFellowSimpleInfo (GetFellowSimpleInfoReq) returns (GetFellowSimpleInfoResp) {
    }

    // 批量获取挚友对 信息
    rpc BatchGetFellowSimpleInfoByPairList (BatchGetFellowSimpleInfoByPairListReq) returns (BatchGetFellowSimpleInfoByPairListResp) {
    }

    //=============================关系管理============================

    // 添加关系
    rpc AddRelationship (RelationshipAddReq) returns (RelationshipAddResp) {}
    // 编辑关系
    rpc UpdateRelationship (RelationshipUpdateReq) returns (RelationshipUpdateResp) {}
    // 删除关系
    rpc DelRelationship (RelationshipDeleteReq) returns (RelationshipDeleteResp) {}
    // 分页查询关系列表
    rpc GetRelationshipList (RelationshipListReq) returns (RelationshipListResp) {}
    // 根据条件查询关系
    rpc GetRelationship (RelationshipGetReq) returns (RelationshipGetResp) {}
    // 分页查询关系下发列表
    rpc GetChannelRelationshipBindingList (ChannelRelationshipBindingListReq) returns (ChannelRelationshipBindingListResp) {}
    // 下发关系
    rpc AddChannelRelationshipBinding (ChannelRelationshipBindingAddReq) returns (ChannelRelationshipBindingAddResp) {}
    // 编辑下发关系信息
    rpc UpdateChannelRelationshipBinding (ChannelRelationshipBindingUpdateReq) returns (ChannelRelationshipBindingUpdateResp) {}
    // 删除下发关系信息
    rpc DelChannelRelationshipBinding (ChannelRelationshipBindingDeleteReq) returns (ChannelRelationshipBindingDeleteResp) {}
    // 获取挚友值topN好友
    rpc GetTopNFellow (GetTopNFellowReq) returns (GetTopNFellowResp) {}

    //=============================关系管理 end============================


    // 获取所有稀缺关系配置
    rpc GetRareConfig (GetRareConfigReq) returns (GetRareConfigResp) {
    }

    // 根据ids获取稀缺关系配置,包括过期,软删
    rpc GetFromAllRelationshipByIds (GetFromAllRelationshipByIdsReq) returns (GetFromAllRelationshipByIdsResp) {
    }


    // 添加稀缺关系
    rpc AddRare (AddRareReq) returns (AddRareResp) {
    }

    // 切换绑定的稀缺关系
    rpc SetBindRelation (SetBindRelationReq) returns (SetBindRelationResp) {
    }

    // 获取房间对应的稀缺关系配置
    rpc GetChannelRareConfig (GetChannelRareConfigReq) returns (GetChannelRareConfigResp) {
    }

    // 获取挚友空间稀缺关系标签
    rpc GetRareTags (GetRareTagsReq) returns (GetRareTagsResp){
    }

    // 获取自己和某个UID的所有稀缺关系列表
    rpc GetRareList (GetRareListReq) returns (GetRareListResp){
    }

    // 获取历史挚友绑定类型
    rpc GetHistoryFellowType (GetHistoryFellowTypeReq) returns (GetHistoryFellowTypeResp) {

    }
    

    // 对账相关

    //补单
    rpc FixFellowOrder(ReconcileV2.ReplaceOrderReq) returns (ReconcileV2.EmptyResp) {}

    // 获取礼物kafka消费单数
    rpc CntPresentEvLogTotalCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}

    // 获取礼物kafka消费订单
    rpc GetPresentEvLogOrderIds(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}

    // 礼物Kafka消费补单
    rpc FixPresentEvLogOrder(ReconcileV2.ReplaceOrderReq) returns (ReconcileV2.EmptyResp) {}

    rpc DelRare(DelRareReq) returns (DelRareResp) {}

    // 神秘人延迟加分
    rpc AddFellowPointDelay(AddFellowPointDelayReq) returns(AddFellowPointDelayResp) {}


    // 测试升级消息
    rpc TestUpgradeImMsg(TestUpgradeImMsgReq) returns(TestUpgradeImMsgResp) {}

    // 测试设置等级
    rpc TestSetFellowLevel(TestSetFellowLevelReq) returns(TestSetFellowLevelResp) {}
}

enum FellowType {
    ENUM_FELLOW_TYPE_UNKNOWN = 0;  // 未知
    ENUM_FELLOW_TYPE_BRO = 1;  // 基友
    ENUM_FELLOW_TYPE_LADYBRO = 2;  // 闺蜜
    ENUM_FELLOW_TYPE_INTIMATE = 3;  // 知己
    ENUM_FELLOW_TYPE_PARTNER = 4;  // 搭子
}

enum FellowBindType {
    ENUM_FELLOW_BIND_TYPE_UNKNOWN = 0;  // 没有关系
    ENUM_FELLOW_BIND_TYPE_UNIQUE = 1;  // 唯一
    ENUM_FELLOW_BIND_TYPE_MULTI = 2;  // 非唯一
}

enum FellowBindStatus {
    ENUM_FELLOW_BIND_STATUS_NORMAL = 0;  // 正常状态
    ENUM_FELLOW_BIND_STATUS_APART = 1;  // 解绑中
}

// 铭牌信息
message PlateUrl {
    string cp_unique = 1;   // cp战铭牌URL
    string date_unique = 2;  // 相亲房铭牌URL
}

message FellowBackground {
    string background_url = 1; // 关系背景
    uint32 source_type = 2; // 关系背景的资源类型，FellowSourceType
    string md5 = 3; // 关系背景的md5
    string background_img = 4; // 动态关系背景的底图
}

enum BanType {
    BAN_TYPE_NORMAL = 0;
    BAN_TYPE_TMP = 1; // 临时封禁
    BAN_TYPE_PERMANENT = 2; // 永久封禁
}

message RareInfo {
  uint32 rare_id = 1;              //稀缺关系ID   
  uint32 sub_rare_id = 2;          //我的组合关系ID
  uint32 day_count = 3;            //在一起的天数
  uint32 remain_count = 4;         //剩余天数
  bool bind_status = 5;            //绑定状态
}

// 稀缺信息
message RareTabInfo {
  uint32 to_uid = 1;                 //对方uid
  string to_account = 2;            //账号
  string to_nick_name = 3;           //昵称
  uint32 bind_type = 4;             // 绑定类型 FellowBindType
  uint32 fellow_type = 5;           //see enum FellowType
  string fellow_name = 6;           //挚友关系名称
  RareInfo out_rare = 7;            //外显稀缺关系信息
  FellowBackground present_bg = 8; // 挚友信物背景
  uint32 rare_count = 9;           //可切换的稀缺关系列表数量
  uint32 to_sex = 10;               //性别
}


// 挚友信息
message FellowInfo {
    int64 uid = 1; //uid
    string account = 2; //账号
    string nick_name = 3; //昵称
    uint32 fellow_level = 4; //挚友等级
    uint32 fellow_type = 5; //挚友类型 see enum FellowType
    uint32 day_cnt = 6; // 在一起的天数
    string fellow_name = 7;
    uint32 bind_type = 8; // 绑定类型 FellowBindType
    uint32 fellow_point = 9; // 挚友值
    FellowBackground background = 10; // 关系背景
    string cp_present_icon = 17;  //信物 URL
    PlateUrl plate_url = 18;  // PlateUrl 铭牌信息
    uint32 bind_status = 19;  // 绑定状态  see FellowBindStatus
    BanType ban_type = 20; // 封禁情况
    RareInfo bind_rare = 21; //绑定的稀缺关系
    string fellow_icon = 22;  //对应的小icon
    string card_colour = 23;  //资料卡颜色
    repeated string room_msg_colour = 24;  //进房消息颜色
    string ligature_url = 25; //挚友连线资源
    GradingInfo grading_info = 26; //挚友段位信息
    uint32 sex = 27;  // 性别
}

enum InviteStatus {
    ENUM_INVITE_STATUS_NO_INVITE = 0;  // 未申请
    ENUM_INVITE_STATUS_INVITED = 1;  // 已申请
    ENUM_INVITE_STATUS_SUCCESS = 2;  // 申请成功
    ENUM_INVITE_STATUS_FAILED = 3;  // 申请失败
    ENUM_INVITE_STATUS_CANCELED = 4;  // 申请被撤回
    ENUM_INVITE_STATUS_TIMEOUT = 5;  // 申请超时退回
}

enum UnboundStatus {
    ENUM_UNBOUND_STATUS_NO_INVITE = 0;  // 未申请
    ENUM_UNBOUND_STATUS_PENDING = 1;  // 已发起
    ENUM_UNBOUND_STATUS_FINISHED = 2;  // 已完成
    ENUM_UNBOUND_STATUS_CANCELED = 3;  // 已撤回
}


// 邀请函信息
message FellowTypeInfo {
    uint32 multi_fellow_type = 1; // FellowType
    string multi_fellow_name = 2; // 关系名字
}


// 申请挚友页面提供的用户信息
message FellowInviteUser {
    int64 uid = 1; //uid
    string account = 2; //账号
    string nick_name = 3; //昵称
    uint32 fellow_point = 4; // 挚友值
    uint32 invite_status = 5; // 申请状态
    uint32 sex = 6; // 性别 0女1男
    string invite_id = 7;// 邀请函id
}

// 信物信息
message FellowPresentInfo {
    uint32 item_id = 1; //礼物id
    string name = 2; //礼物名
    uint32 value = 3; // 价格
    string icon = 4; // 图标
    uint32 price_type = 5; // 1红钻2t豆 , PRESENT_PRICE_TYPE
    FellowBackground unique_background = 6; // 唯一关系背景皮肤
    FellowBackground multi_background = 7; // 非唯一关系背景皮肤
    uint32 already_own = 8; // 是否已经拥有，1 已拥有
}


// 邀请函信息
message FellowInviteInfo {
    string invite_id = 1; //邀请函id
    int64 from_uid = 2; //发送人uid
    string from_account = 3; // 发送人账号
    string from_nickname = 4; // 发送人昵称
    uint32 bind_type = 5; // 绑定类型 FellowBindType
    uint32 fellow_type = 6; // 非唯一关系类型 FellowType
    uint32 fellow_point = 7; // 与邀请人的挚友值
    uint32 create_time = 8; // 邀请函发送时间
    string fellow_name = 9; // 非唯一关系类型的名字
    uint32 status = 10; // 邀请函当前状态，req不用填 InviteStatus
    int64 to_uid = 11; //接收人uid
    string to_account = 12; // 接收人账号
    string to_nickname = 13; // 接收人昵称
    bool with_unlock = 14;
    uint32 from_sex = 15; // 性别 0女1男
    uint32 to_sex = 16; // 性别 0女1男
    FellowPresentInfo present_info = 17; // 信物信息
    uint32 unlock_price = 18; // 解锁花费
    uint32 channel_id = 19; // 发起邀请的房间id
    uint32 end_time = 20; // 邀请的到期时间
    string from_fellow_name = 21; // 原来关系名称
}

message GetFellowListReq {
    int64 uid = 1; //uid
}

message GetFellowListResp {
    int64 uid = 1; //uid
    FellowInfo unique_fellow = 2; //唯一挚友
    repeated FellowInfo multi_fellow = 3; // 非唯一挚友列表
    uint32 fellow_position_cnt = 4;  //非唯一挚友坑位数量
    uint32 pending_invite_cnt = 5; // 待处理的邀请数量
    UnlockInfo unlock_info = 7; // 解锁信息
    uint32 send_invite_count = 8; //我发出的邀请数量
    repeated RareTabInfo rare_tab_list = 9; //稀缺关系tab列表
}

message GetFellowSimpleInfoReq {
    int64 uid = 1;
}

message GetFellowSimpleInfoResp {
    int64 uid = 1;
    repeated FellowInfo fellow_list = 2; //  挚友列表，只包含Uid，绑定类型，等级
}



// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message FellowPair {
    int64 uidA = 1; // uidA
    int64 uidB = 2; // uidB
}

message BatchGetFellowSimpleInfoByPairListReq {
    repeated FellowPair pair_list = 1; // uidA,uidB
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SimpleFellowInfo {
    uint32 uidA = 1; // uid
    uint32 uidB = 2; // uidB
    uint32 level = 3; // 等级
    string fellow_name = 4; // 关系名称
    uint32 point = 5; // 挚友值
}

message BatchGetFellowSimpleInfoByPairListResp {
    repeated SimpleFellowInfo fellow_list = 1; //  挚友info列表，只包含Uid，关系名称，等级
}


message UnlockInfo {
    uint32 unlock_price = 1; // 解锁自身下一个挚友位的价格
    uint32 unlock_level = 2; // 解锁自身下一个挚友位所需等级
    uint32 unlock_site = 3;  // 下一个挚友位是第几个
}

message GetFellowCandidateListReq {
    uint32 uid = 1;
    uint32 bind_type = 2;  // FellowBindType
    uint32 page = 3; // 页数
    uint32 count = 4; // 每页的个数
}

message GetFellowCandidateListResp {
    repeated FellowInviteUser fellow_list = 1; // 可申请的挚友列表
    bool is_reach_end = 2; // 是否到达末尾
}

message GetFellowCandidateInfoReq {
    uint32 uid = 1;
    uint32 target_uid = 2;
}

message GetFellowCandidateInfoResp {
    repeated FellowPresentInfo present_info = 1;  // 礼物信息
    FellowInviteUser user_info = 2;    // 用户信息
    repeated FellowTypeInfo multi_fellow_option_list = 3;  // 非绑定 - 可选的关系列表
    bool has_multi_fellow_field = 4;   // 非绑定 - 是否还有栏位
    uint32 unlock_price = 5;  // 非绑定 - 解锁花费
}

message SendFellowInviteReq {
    uint32 uid = 1;
    uint32 target_uid = 2;
    uint32 bind_type = 3; // FellowBindType
    uint32 present_id = 4;
    uint32 fellow_type = 5; // FellowType
    bool with_unlock = 6; // 是否带有解锁
}


message SendFellowInviteResp {
    int64 remain_currency = 1;
    int64 remain_tbean = 2;
}

message GetFellowInviteListReq {
    uint32 uid = 1;
}

message GetFellowInviteListResp {
    repeated FellowInviteInfo invite_list = 1;
}

message GetFellowInviteInfoByIdReq {
    string invite_id = 1; //邀请函id
}

message GetFellowInviteInfoByIdResp {
    FellowInviteInfo invite_list = 1;
}

message ServiceCtrlInfo {
    string client_ip = 1;
    uint32 client_port = 2;
    bytes device_id = 3;
    uint32 client_type = 4;
    uint32 terminal_type = 5;
    uint32 client_id = 6;
    uint32 client_version = 7;
}

message HandleFellowInviteReq {
    uint32 uid = 1;
    string invite_id = 2; //邀请函id
    bool is_accept_invite = 3; // 是否接受邀请
    uint32 market_id = 4; // 设备的marketId，上报用
    uint32 app_id = 5; // 平台id
    ServiceCtrlInfo service_info = 6; //service_info，部分逻辑会用到
}

message HandleFellowInviteResp {
}


// web接口
message GetWebFellowListReq {
    int64 uid = 1;
    int64 fellow_uid = 2;
}


message FellowTask {
    uint32 id = 1;  // 任务ID
    float add_point = 2;  // 完成一次加分
    uint32 max_point = 3;  // 每日最高分
    uint32 max_cnt = 4;  // 每日任务次数上限
    uint32 complete_cnt = 5;  // 当前完成
    string title = 6;  // 标题
    string jump_url = 7;
    string icon = 8;
    string desc = 9;  // 描述
    string button_text = 10;
}

message FellowLevelConfig {
    uint32 level = 1; // 等级
    uint32 point = 2; // 分数
    string desc = 3; //描述
    string award = 4; // 奖励
    string icon = 5; // 图标 url
}

message GetWebFellowListResp {
    int64 uid = 1;
    int64 fellow_uid = 2;
    uint32 fellow_point = 3; //当前挚友值
    uint32 bind_type = 4;  //绑定类型 see FellowBindType
    uint32 fellow_type = 5;  //挚友类型 see FellowType
    repeated FellowTask task = 6; // 每日任务
    repeated FellowLevelConfig level_config = 7;  //里程碑配置
    uint32 bind_day = 8;  //绑定天数
    uint32 unbound_time = 9; //解绑发起时间，0为未发起解绑
    FellowPresentInfo present_info = 10;  // 礼物信息
    GradingInfo grading_info = 11; //段位信息
}

message CheckFellowInviteReq {
    uint32 uid = 1;
    uint32 bind_type = 2;
}

message CheckFellowInviteResp {
    bool reach_limit = 1; // 达到上限则无法邀请
    string cp_name = 2; // 如果是唯一绑定关系，已绑定的用户昵称
    uint32 cp_sex = 3; // 如果是唯一绑定关系，已经绑定的用户性别 0女1男
    UnlockInfo unlock_info = 4; // 非绑定 - 自己的解锁信息
}


message GetFellowPointReq {
    uint32 uid = 1;  //自己UID
    uint32 fellow_uid = 2;  //挚友UID
}

message GetFellowPointResp {
    uint32 uid = 1;  //自己UID
    uint32 fellow_uid = 2;  //挚友UID
    uint32 fellow_point = 3; // 当前挚友值
    uint32 fellow_level = 4; //挚友等级
    uint32 current_level_point = 5; //当前等级分数
    uint32 next_level_point = 6; //下一等级分数
    uint32 bind_type = 7;  //绑定类型 see FellowBindType
}

// 段位信息（一个等级区间称为一个段位）
message GradingInfo {
    string icon = 1; // 图标
    string color = 2; // 颜色
    string grading_name = 3; // 段位名称
    uint32 level = 4; // 段位等级
}

message CancelFellowInviteReq {
    uint32 op_uid = 1;
    string invite_id = 2; //邀请函id
}


message CancelFellowInviteResp {
}


message FellowPresentConfig {
    uint32 gift_id = 1;
    string unique_background_url = 2; // 唯一关系背景
    uint32 unique_source_type = 3; // 唯一关系背景的资源类型，FellowSourceType
    string unique_md5 = 4; // 唯一关系背景的md5
    string multi_background_url = 5; // 非唯一关系背景
    uint32 multi_source_type = 6; // 非唯一关系背景的资源类型，FellowSourceType
    string multi_md5 = 7; // 非唯一关系背景的md5
    string unique_background_img = 8; // 唯一动态关系背景的底图
    string multi_background_img = 9; // 非唯一动态关系背景的底图
    string source_url = 10; // （运营后台用） 完整资源包url
}

message AddFellowPresentConfigReq {
    FellowPresentConfig config = 1;
}

message AddFellowPresentConfigResp {
}

message UpdateFellowPresentConfigReq {
    FellowPresentConfig config = 1;
}


message UpdateFellowPresentConfigResp {
}

message DelFellowPresentConfigReq {
    uint32 gift_id = 1;
}


message DelFellowPresentConfigResp {
}


message GetFellowPresentConfigByIdReq {
    uint32 gift_id = 1;
}


message GetFellowPresentConfigByIdResp {
    FellowPresentConfig config = 1;
}


message GetAllFellowPresentConfigReq {
}

message GetAllFellowPresentConfigResp {
    repeated FellowPresentConfig config = 1;
}

message UnlockFellowSiteReq {
    uint32 uid = 1;
}

message UnlockFellowSiteResp {
    int64 remain = 1;
}

message UnboundFellowReq {
    uint32 uid = 1;
    uint32 target_uid = 2;
}

message UnboundFellowResp {
}


message CancelUnboundFellowReq {
    uint32 uid = 1;
    uint32 target_uid = 2;
}

message CancelUnboundFellowResp {
}

message ChangeFellowBindTypeReq{
    uint32 op_uid = 1;
    uint32 target_uid = 2;
    uint32 from_bind_type = 3;      //  see FellowBindType
    uint32 from_fellow_type = 4;    //  see FellowType
    uint32 to_bind_type = 5;        //  see FellowBindType
    uint32 to_fellow_type = 6;      //  see FellowType
    uint32 present_id = 7;
    bool with_unlock = 8; // 是否带有解锁
}

message ChangeFellowBindTypeResp{
    int64 remain_tbean = 1;
}


message SendFellowPresentReq {
    uint32 uid = 1;
    uint32 target_uid = 2;
    uint32 gift_id = 3; // 礼物item_id
    uint32 market_id = 4; // 设备的marketId，上报用
    uint32 app_id = 5; // 平台id
    ServiceCtrlInfo service_info = 6; //service_info，部分逻辑会用到
}

message SendFellowPresentResp {
    int64 remain_currency = 1;
    int64 remain_tbean = 2;
    PresentSendItemSimpleInfo present_info = 3;
}

message PresentSendItemSimpleInfo
{
    uint32 item_id = 1;
    uint32 count = 2;
    uint32 show_effect = 3;  // 播放特效 ga::PRESENT_SHOW_EFFECT_TYPE
    uint32 show_effect_v2 = 4;  // 播放特效 ga::PRESENT_SHOW_EFFECT_TYPE_V2
    string present_icon = 5;
}

message GetFellowPresentDetailReq {
    uint32 uid = 1;
    uint32 target_uid = 2;
}

message GetFellowPresentDetailResp {
    repeated FellowPresentInfo present_list = 1;  // 礼物信息
    FellowPresentInfo fellow_present = 2; // 已绑定的信物
    uint32 fellow_level = 3;
    string fellow_name = 4;
    uint32 day_cnt = 5; // 在一起的天数
    uint32 bind_type = 6;
}


message GetFellowTypeReq {
    uint32 uid = 1;
    uint32 target_uid = 2;
}

message GetFellowTypeResp {
    uint32 uid = 1;
    uint32 target_uid = 2;
    uint32 fellow_type = 3; //see FellowType
    string fellow_type_str = 4;
}

message GetHistoryFellowTypeReq {
    uint32 uid = 1;
    uint32 target_uid = 2;
    string update_time = 3;
}

message GetHistoryFellowTypeResp {
    uint32 uid = 1;
    uint32 target_uid = 2;
    uint32 fellow_type = 3; //see FellowType
    string fellow_type_str = 4;
}

message GetFellowInfoByUidReq {
    uint32 uid = 1;
    uint32 target_uid = 2;
}

message GetFellowInfoByUidResp {
    FellowInfo fellow_info = 1;
}

message ChannelSendFellowPresentReq {
    uint32 uid = 1;
    uint32 target_uid = 2;
    uint32 present_id = 3;
    uint32 channel_id = 4;
}

message ChannelSendFellowPresentResp {
    int64 remain_currency = 1;
    int64 remain_tbean = 2;
}

message SendChannelFellowInviteReq {
    uint32 uid = 1;
    uint32 target_uid = 2;
    uint32 bind_type = 3; // FellowBindType
    uint32 present_id = 4;
    uint32 fellow_type = 5; // FellowType
    bool with_unlock = 6; // 是否带有解锁
    uint32 channel_id = 7; // 房间id
}

message SendChannelFellowInviteResp {
    int64 remain_currency = 1;
    int64 remain_tbean = 2;
}


message HandleChannelFellowInviteReq {
    uint32 uid = 1;
    string invite_id = 2; //邀请函id
    bool is_accept_invite = 3; // 是否接受邀请
    uint32 channel_id = 4; // 房间
    uint32 market_id = 5; // 设备的marketId，上报用
    uint32 app_id = 6; // 平台id
    ServiceCtrlInfo service_info = 7; //service_info，部分逻辑会用到
}

message HandleChannelFellowInviteResp {

}

// 邀请函信息
message ChannelFellowMsg {
    int64 from_uid = 1; //发送人uid
    string from_account = 2; // 发送人账号
    string from_nickname = 3; // 发送人昵称
    uint32 bind_type = 4; // 绑定类型 FellowBindType
    uint32 fellow_type = 5; // 非唯一绑定类型 FellowType
    string fellow_name = 6;
    int64 to_uid = 7; //接收人uid
    string to_account = 8; // 接收人账号
    string to_nickname = 9; // 接收人昵称
    uint32 from_sex = 10; // 性别 0女1男
    uint32 to_sex = 11; // 性别 0女1男
    FellowPresentInfo present_info = 12; // 信物信息
    uint32 status = 13; // see InviteStatus
}


message GetRoomFellowListReq {
    uint32 uid = 1; // 查看者 uid
    repeated uint32 next_uid = 2; // 相邻UID列表
    repeated uint32 on_mic_uid = 3; // 麦上不相邻UID列表
}

message GetRoomFellowListResp {
    uint32 uid = 1; //uid
    repeated FellowInfo fellow_list = 2; // 三个挚友信息
}


message GetAllChannelFellowInviteReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
}

message GetAllChannelFellowInviteResp {
    repeated FellowInviteInfo send_invite_list = 1;   // 我发出的邀请函
    repeated FellowInviteInfo received_invite_list = 2;   // 我收到的邀请函
}

message DirectUnboundFellowReq {
    uint32 uid = 1;
    uint32 target_uid = 2;
}

message DirectUnboundFellowResp {

}


message GetOnMicFellowListReq {
    uint32 channel_id = 1; //当前房间
}

// 麦位挚友信息
message MicFellowInfo {
    uint32 uid = 1; //uid
    uint32 fellow_uid = 2; //
    uint32 fellow_level = 3; //挚友等级
    uint32 fellow_type = 4; //挚友类型 see enum FellowType
    uint32 bind_type = 5; // 绑定类型 FellowBindType
    RareInfo current_rare = 6; // 当前绑定稀缺关系id
    string ligature_url = 7; //挚友连线资源
}

message MicFellowInfoChangeInfo {
    repeated MicFellowInfo mic_fellow = 1;
}


message GetOnMicFellowListResp {
    uint32 channel_id = 1; //当前房间
    repeated MicFellowInfo mic_fellow = 2;
}

message GetChannelFellowCandidateInfoReq {
    uint32 uid = 1;
    uint32 target_uid = 2;
    uint32 item_id = 3;
}

// 可选的关系信息
message FellowOptionInfo {
    uint32 bind_type = 1;  // FellowBindType
    uint32 fellow_type = 2; // FellowType
    string fellow_name = 3; // 关系名字
}

message GetChannelFellowCandidateInfoResp {
    FellowPresentInfo present_info = 1;  // 礼物信息
    FellowInviteUser user_info = 2;    // 用户信息
    repeated FellowOptionInfo fellow_option_list = 3;  // 可选的关系列表
    bool has_multi_fellow_field = 4;   // 非唯一 - 对方是否还有栏位
    uint32 unlock_price = 5;  // 非唯一 - 帮对方解锁花费
    bool has_cp_field = 6;  // 唯一 - 对方是否可以绑定唯一关系
    UnlockInfo self_unlock_info = 7; // 自己的解锁信息
}


//我发出的挚友邀请列表
message GetSendInviteListReq {
}

message GetSendInviteListResp {
    repeated FellowInviteInfo invite_list = 1;
}

// 资源
message Resource {
    string url = 1; // 资源地址
    ResourceType type = 2; // 资源类型
    string background_img_url = 3; // 背景图片地址
    string md5 = 4; // 资源md5
    string name = 5; // 资源名称
}

// 资源类型
enum ResourceType {
    RESOURCE_TYPE_UNKNOWN = 0; // 未知
    RESOURCE_TYPE_IMG = 1; // 图片
    RESOURCE_TYPE_VIP = 2; // 视频
    RESOURCE_TYPE_ZIP = 3; // zip
}

// 颜色资源
message GradientColor {
    string start_hex = 1; // 上渐变色值
    string end_hex = 2; // 下渐变色值，如果是单色，则为空
    string shadow_hex = 3; // 投影色值
}

// 关系盒子
message RelationshipBox {
    Resource background = 1; // 挚友柜背景图
    Resource big_background = 2; // 稀缺关系背景图
}

// 消息通知图片配置
message MsgNotifyImg {
    string origin = 1; // 原图
    string thumbnail = 2; // 缩略图
}

// 关系类型
enum RelationType {
    RELATION_TYPE_UNKNOWN = 0;
    RELATION_TYPE_RARE = 1;// 稀有关系
}

// 麦上连线样式
message ConnectedStringForMic {
    string left = 1; // 单方1在左边时的样式
    string right = 2; // 单方1在右边的样式
}


// 关系
message Relationship {
    uint32 id = 1;// id
    int64 create_time = 2; // 创建时间
    int64 update_time = 3; // 更新时间
    string name = 4;// 名称
    RelationType type = 5;// 类型
    Resource animation_of_settlement = 6;// CP战结算动画
    RelationshipBox relationship_box = 7;// 关系盒子
    ConnectedStringForMic connected_string_for_mic = 8; // 麦上连线样式
    string space_flag_url = 9; // 关系标签的图片
    GradientColor card_flag_color = 10; // 关系标签的颜色
    string im_flag_url = 11; // 关系标签进出房/im的图片
    string friend_space_background = 12; // 挚友空间背景
    MsgNotifyImg msg_notify_img = 13; // 消息通知图片
    int32 biz_type = 14;// 不同业务定义的类型
    repeated Relationship sub_relation = 15;// 子关系
    int32 sort_index = 16; // 排序索引
}

// 房间绑定的稀有关系
message ChannelRelationshipBinding {
    message RelationshipSimpleInfo {
        uint32 id = 1;// id
        string name = 2;// 名称
    }
    message Introduction {
        string tt_url = 1; // TT链接
        string happy_game_ios = 2; // 欢游ios链接
        string happy_game_android = 3; // 欢游安卓链接
    }
    int64 id = 1; // id
    int64 create_time = 2; // 创建时间
    int64 update_time = 3; // 更新时间
    uint32 channel_id = 4; // 房间id，0是所有的CP战房间
    repeated RelationshipSimpleInfo relationship_id_list = 5; // 关系id
    int64 start_time = 6; // 开始时间
    int64 end_time = 7; // 结束时间
    Introduction intro_url = 8; // 介绍URL
    string entrance_url = 9; // 入口样式url
}


//=======================分页查询稀缺关系列表========================

// 分页查询关系列表请求
message RelationshipListReq {
    int32 page_index = 1; // 当前页码
    int32 page_size = 2; // 每页大小
    uint32 condition_id = 3; // 查询条件-id
    string condition_name = 4; // 查询条件-名称
}

// 分页查询关系列表响应
message RelationshipListResp {
    repeated Relationship list = 1; // 查询条件
    int32 total_count = 2; // 总数
    int32 total_page = 3; // 总页数
}

//=======================根据条件查询稀缺关系========================

// 根据条件查询关系请求
message RelationshipGetReq {
    uint32 id = 1; // id
}

// 根据条件查询关系响应
message RelationshipGetResp {
    Relationship relationship = 1; // 稀有关系
}

//=======================添加稀缺关系========================

// 添加关系请求
message RelationshipAddReq {
    Relationship relationship = 1;// 稀有关系
}

// 添加稀缺关系响应
message RelationshipAddResp {
    uint32 id = 1;// id
}

//=======================编辑稀缺关系========================

// 编辑关系请求
message RelationshipUpdateReq {
    Relationship relationship = 1;// 稀有关系
}

// 编辑关系响应
message RelationshipUpdateResp {
    uint32 id = 1;// id
}

//=======================删除稀缺关系========================

// 删除关系请求
message RelationshipDeleteReq {
    uint32 id = 1;// id
}

// 删除关系响应
message RelationshipDeleteResp {
}

//=======================分页查询稀缺关系下发列表========================

// 分页查询关系下发列表请求
message ChannelRelationshipBindingListReq {
    int32 page_index = 1; // 当前页码
    int32 page_size = 2; // 每页大小
    uint32 channel_id = 3; // 房间id，0表示全部，-1表示所有CP战房间，其他表示指定CP战房间
}

// 分页查询关系下发列表响应
message ChannelRelationshipBindingListResp {
    repeated ChannelRelationshipBinding list = 1; // 下发信息列表
    int32 total_count = 2; // 总数
    int32 total_page = 3; // 总页数
}

//=======================下发稀缺关系========================

// 下发关系请求
message ChannelRelationshipBindingAddReq {
    ChannelRelationshipBinding channel_relationship_binding = 1;// 房间绑定的关系
}

// 下发关系响应
message ChannelRelationshipBindingAddResp {
    int64 id = 1;// id
}

//=======================编辑下发稀缺关系信息========================

// 编辑下发关系信息请求
message ChannelRelationshipBindingUpdateReq {
    ChannelRelationshipBinding channel_relationship_binding = 1;// 房间绑定的关系
}

// 编辑下发关系信息响应
message ChannelRelationshipBindingUpdateResp {
    int64 id = 1;// id
}

//=======================删除下发稀缺关系信息========================

// 删除下发关系信息请求
message ChannelRelationshipBindingDeleteReq {
    int64 id = 1;// id
}

// 删除下发关系信息响应
message ChannelRelationshipBindingDeleteResp {
}

// 获取稀缺关系配置
message GetRareConfigReq {
  uint32 day_config = 1; //只获取天数配置
}

message AnimationConfig {
  string resource_url = 1;  //动画资源地址 zip包
  string md5 = 2;  //动画MD5
}


// 稀缺关系CP值对应天数和动画
message RareDayConfig {
    uint32 day = 1;
    uint32 cp_value = 2;  //天数对应CP值
    AnimationConfig animation = 3;  //天数对应动画
    string background_url = 4;  //天数对应的背景url
}

message SubRareConfig {
    uint32 sub_rare_id = 1; // 组合ID
    string name = 2; // 组合稀缺关系名称
    string rare_flag = 3; // 组合关系进房/im标签
  }

message ConnectedForMic{
  string left = 1;  // 单方1在左边时的样式
  string right = 2; // 单方1在右边的样式
}

//MsgNotifyPictures 消息通知图片配置
message MsgNotifyPictures {
  string origin = 1;      // 原图
  string thumbnail = 2;   // 缩略图
}

message RareConfig {
  uint32 rare_id = 1;                         // rare_id 
  string name = 2;                            // 稀缺关系名称
  repeated SubRareConfig sub_rare_cfg = 3;    // 组合关系ID和名称
  AnimationConfig cp_animation = 4;           // CP战结算动画
  ConnectedForMic mic_connected = 5;          // 麦上连线样式
  string rare_flag = 6;                       // 非组合关系的进房/im标签
  string card_color = 7;                      // 资料卡挚友标签（单色值）
  FellowBackground cp_bg = 8;                 // 稀缺关系CP位背景
  FellowBackground mid_bg = 9;                // 稀缺关系非CP位背景
  MsgNotifyPictures msg_pictrures = 10;       // 消息通知图片配置
  string friend_space_background = 11;        // 挚友空间场景关系图
}

message GetRareConfigResp {
    repeated RareConfig rare_list = 1;
    repeated RareDayConfig day_config = 2; //天数对应动画
}


// 切换绑定的稀缺关系
message SetBindRelationReq {
    uint32 uid = 1;
    uint32 to_uid = 2;
    uint32 rare_id = 3;
    uint32 sub_rare_id = 4;
    bool bind = 5;   // 取消绑定false 绑定 true
}


// 切换绑定的稀缺关系
message SetBindRelationResp {
    uint32 uid = 1;
    uint32 to_uid = 2;
    uint32 rare_id = 3;
    uint32 sub_rare_id = 4;
    bool bind = 5;   // 取消绑定false 绑定 true
}


message ChoseRare {
  uint32 uid = 1;
  uint32 sub_rare_id = 2; //组合关系子ID
}


// 选择稀缺关系
message AddRareReq {
  uint32 uid = 1;
  uint32 to_uid = 2;
  uint32 rare_id = 3; //选择关系
  uint32 add_time = 4; //增加时间(秒)
  uint32 game_id = 5; //cp game_id
  uint32 channel_id = 6; //房间ID
  repeated ChoseRare user_rare = 7; //组合关系
  bool is_recorver = 8; // 是否延迟恢复的数据
  uint32 create_time = 9; // 获得时间
}


message AddRareResp {
  string rare_name = 1; //关系名称
}



// 获取房间可用稀缺关系配置
message GetChannelRareConfigReq {
    uint32 channel_id = 1;
}

message GetChannelRareConfigResp {
    uint32 channel_id = 1;
    string intro_url = 2; //房间介绍URL
    repeated RareConfig rare_list = 3;
    string entrance_url = 4; //房间入口图片URL
}

message GetFromAllRelationshipByIdsReq {
    repeated uint32 ids = 1;
}

message GetFromAllRelationshipByIdsResp {
    repeated Relationship relation_list = 1;
}

message GetRareTagsReq {
    uint32 uid = 1;
    uint32 to_uid = 2;
}

message GetRareTagsResp {
    repeated string tags = 1;
}


// 获取两个UID的所有稀缺关系列表
message GetRareListReq{
  uint32 uid = 1;
  uint32 to_uid = 2;
 }

message GetRareListResp{
  uint32 uid = 1;
  uint32 to_uid = 2;                 //对方uid
  string to_account = 3;            //账号
  string to_nick_name = 4;           //昵称
  uint32 bind_type = 5;             // 绑定类型 FellowBindType
  uint32 fellow_type = 6;           //see enum FellowType
  string fellow_name = 7;           //挚友关系名称
  repeated RareInfo rare_list = 8;  //可切换的稀缺关系列表
  FellowBackground present_bg = 9; // 挚友信物背景
 }

message DelRareReq {
    uint32 uid = 2;
    uint32 to_uid = 3;
    uint32 rare_id = 4;
    uint32 sub_rare_id = 5;
}

message DelRareResp {
}

message FellowPointDelayItem {
    uint32 uid = 1;
    uint32 to_uid = 2;
    float point = 3;
    string reason = 4;
    uint32 add_type = 5;
}

message AddFellowPointDelayReq {
    repeated FellowPointDelayItem delay_items = 1;
}

message AddFellowPointDelayResp {}

message TestUpgradeImMsgReq{
    uint32 uid = 1;
    uint32 to_uid = 2;
    uint32 level = 3;
    uint32 channel_id = 4;
}

message TestUpgradeImMsgResp{
}

message TestSetFellowLevelReq{
    uint32 uid = 1;
    uint32 to_uid = 2;
    uint32 level = 3;
}

message TestSetFellowLevelResp{
}

message GetTopNFellowReq {
    uint32 uid = 1;
}

message GetTopNFellowResp {
    repeated TopNFellowInfo top_n_fellow_list = 1;
}

message TopNFellowInfo {
    uint32 fellow_uid = 1;
    uint32 fellow_val = 2;
}