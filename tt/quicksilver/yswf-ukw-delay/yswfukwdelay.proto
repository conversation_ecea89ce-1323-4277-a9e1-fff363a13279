syntax = "proto3";

package yswfukwdelay;
option go_package = "golang.52tt.com/protocol/services/yswfukwdelay";


service YswfUkwDelay {
  rpc AddYswfUkwDelayData(AddYswfUkwDelayDataReq) returns (AddYswfUkwDelayDataResp) {}
  rpc ManualRecover(ManualRecoverReq) returns (ManualRecoverResp) {}
}

enum YswfBizType {
  Unknow = 0;
  RareRelation = 1000;
  FellowVal = 1001;
  CPVal = 2000;
  CPReport = 2001;
  CPValLog = 2002;
  DatingVal = 3000;
}

message AddYswfUkwDelayDataReq {
  uint32 uid_a = 1;
  uint32 uid_b = 2;
  uint32 biz_type = 3;
  bytes data = 4;
}

message AddYswfUkwDelayDataResp {

}

message DatingGameReportData {
  uint32 channel_id = 1;
  uint32 uid_a = 2;
  uint32 uid_b = 3;
  uint32 value_a = 4;
  uint32 value_b = 5;
  uint32 level = 6;
  uint32 level_score = 7;
  uint32 create_time = 8;
}

message ManualRecoverReq {
  uint32 uid = 1;
}

message ManualRecoverResp {

}