syntax = "proto3";

package muse_social_community_achieve;
option go_package = "golang.52tt.com/protocol/services/muse-social-community-achieve";

// buf:lint:ignore SERVICE_PASCAL_CASE
service muse_social_community_achieve {
  rpc GetLevelDetail(GetLevelDetailRequest)returns(GetLevelDetailResponse);
  rpc CheckIn(CheckInRequest)returns(CheckInResponse);
  rpc GetCheckInSimple(GetCheckInSimpleRequest)returns(GetCheckInSimpleResponse);
  rpc GetSocialCommunityUpdateLevelTip(GetSocialCommunityUpdateLevelTipRequest)returns(GetSocialCommunityUpdateLevelTipResponse);
  rpc GetRightMicList(GetRightMicListRequest)returns(GetRightMicListResponse);
  rpc GetRightMicTabList(GetRightMicTabListRequest)returns(GetRightMicTabListResponse);
  rpc SetChannelCurrentMic(SetChannelCurrentMicRequest)returns(SetChannelCurrentMicResponse);
  rpc GetChannelCurrentMic(GetChannelCurrentMicRequest)returns(GetChannelCurrentMicResponse);
  rpc ClearContentTaskInteraction(ClearContentTaskInteractionRequest)returns(ClearContentTaskInteractionResponse);
  rpc ClearExpireTaskRecord(ClearExpireTaskRecordRequest)returns(ClearExpireTaskRecordResponse);
}

message ClearExpireTaskRecordRequest{

}

message ClearExpireTaskRecordResponse{

}

message SetChannelCurrentMicRequest{
    uint32 channel_id=1;
    int32 mic_number=2;
    uint32 level=3;
}

message SetChannelCurrentMicResponse{

}

message GetChannelCurrentMicRequest{
    uint32 channel_id=1;
}

message GetChannelCurrentMicResponse{
  int32 mic_number=1;
  uint32 level=2;
}

message GetLevelDetailRequest{
  string social_community_id = 1;
  uint32 uid = 2;
  uint32 market_id = 3;
}

message GetLevelDetailResponse{
  string social_community_id = 1;
  string social_community_name = 2;
  string social_community_logo = 3;
  SocialCommunityLevelCard level_card = 4;
  SocialCommunityRightGroup right_group = 5;
  repeated SocialCommunityTaskGroup task_groups = 6;
  BrandMember member = 7;
  uint32 professionalism = 8;
}

message BrandMember{
  uint32 uid = 1;
  string role_text = 2;
  BrandMemberRole role = 3;
}

enum BrandMemberRole{
  Brand_Role_None = 0;
  Brand_Role_Captain = 1;//主理人
  Brand_Role_Kernel = 2;//核心成员
  Brand_Role_Vice_Captain = 3;//副主理人
  Brand_Producer = 4;//制作人
  Brand_Fans = 5;//粉丝
}

message SocialCommunityRightGroup{
  string title = 1;
  repeated SocialCommunityRight social_community_rights = 2;
}

message SocialCommunityLevelCard {
  uint32 level = 1;
  int64 cur_exp = 2;
  int64 next_level_exp = 3;
  string account = 4;
  int64 user_today_exp = 5;
  int64 user_total_exp = 6;
}

message SocialCommunityRight {
  string logo = 1;
  string title = 2;
  string desc = 3;
  SocialCommunityRightStatus status = 4;//1-生效中，0-上锁中
  bool new_flag = 5;
  string tips = 6;
}

enum SocialCommunityRightStatus{
  Status_Lock = 0;
  Status_Valid = 1;
}

message SocialCommunityTaskGroup{
  string title = 1;
  repeated SocialCommunityTask tasks = 7;
}

enum TaskViewType{
  TASK_VIEW_TYPE_NORMAL = 0;
  TASK_VIEW_TYPE_CHECK_IN = 1;
  TASK_VIEW_TYPE_STEP = 2;
}

enum TaskStatus{
  TASK_STATUS_DOING = 0;
  TASK_STATUS_DONE = 1;
}

message SocialCommunityTask {
  TaskViewType view_type = 1;//0-普通类型，1-签到样式，2-step类型

  oneof social_community_task_view{
    SocialCommunityNormalTaskView normal_task_view = 2;
    SocialCommunityCheckInTaskView check_in_task_view = 3;
    SocialCommunityStepTaskView step_task_view = 4;
  }

  TaskStatus status = 5;//0-未完成，1-已完成
  ActionType action_type = 6;//0-不需要点击，1-url,2-check_in
  string action_url = 7;
  repeated string bg_colors = 8;
}

enum ActionType{
  ActionTypeNone = 0;
  ActionTypeUrl = 1;
  ActionTypeCheckIn = 2;
}

message SocialCommunityNormalTaskView {
  int64 award_exp = 1;//奖励经验值，用于左侧图标显示
  string title = 2;
  string desc = 3;
  string active_desc = 4;
  string button_text = 5;
}

message SocialCommunityCheckInTaskView {
  int64 award_exp = 1;//奖励经验值，用于左侧图标显示
  string title = 2;
  string desc = 3;
  string active_desc = 4;
  map<uint32, string> status_button_text = 5;//已完成，和未完成文案
}

message SocialCommunityStepTaskView {
  repeated SocialCommunityStep steps = 1;
  string title = 2;
  string button_text = 3;
  int64 score = 4;//目前获得值
  string desc = 5;
  string tips = 6;
}

message SocialCommunityStep{
  int64 award_exp = 1;//奖励经验值
  string desc = 2;
  int64 score = 3;//到这step需要值
}

message CheckInRequest{
  string social_community_id = 1;
}

message CheckInResponse{

}

message GetCheckInSimpleRequest{
    string social_community_id=1;
}

message GetCheckInSimpleResponse{
    uint32 check_in_status=1;
    uint32 check_in_exp=2;
}

message GetSocialCommunityUpdateLevelTipRequest{
    string social_community_id=1;
}

message GetSocialCommunityUpdateLevelTipResponse{
  string social_community_id=1;
  string social_community_name=2;
  string logo=3;
  SocialCommunityRightGroup right_group = 4;
  uint32 captain=5;
  uint32 level=6;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetRightMicListRequest {
  string social_community_id = 1;
  uint32 tabId = 2;
}

message GetRightMicListResponse {
  repeated uint32 mic_list = 1;
}

message GetRightMicTabListRequest {
}

message GetRightMicTabListResponse {
  repeated uint32 tab_list = 1;
}

message ClearContentTaskInteractionRequest {
  uint32 uid = 1;
  string social_community_id = 2;
  bool clear_interaction = 3;
  bool clear_task = 4;
}

message ClearContentTaskInteractionResponse {
}