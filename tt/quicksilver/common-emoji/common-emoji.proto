syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/common-emoji";
package common_emoji;

service CommonEmoji {
  rpc GetHotEmojis(GetHotEmojisReq) returns(GetHotEmojisResp); // 获取热门表情
  rpc GetEmojisByKeyWord(GetEmojisByKeyWordReq) returns(GetEmojisByKeyWordResp); //关健词表情
  rpc CopyThirdPartyEmojiToObs(CopyThirdPartyEmojiToObsReq) returns(CopyThirdPartyEmojiToObsResp); // 复制第三方表情到obs
}

message GetHotEmojisReq {
  enum ReqSource {
    SOURCE_UNSPECIFIED = 0;
    SOURCE_GAME = 1;
    SOURCE_MT = 2;
  }
  ReqSource req_source = 1; // 请求来源
  bool user_cache = 2; // 是否使用缓存
}

// 表情信息
message BaseEmojiInfo {
  string url = 1; // 图url
  uint32 height = 2; // 高度
  uint32 width = 3; // 宽度
}

message CommonEmojiInfo {
  string emoji_id = 1; // 表情包id
  string md5 = 2; // 表情包md5
  BaseEmojiInfo origin_emoji_info = 3; // 原图表情
  BaseEmojiInfo thumb_emoji_info = 4; // 缩略图表情
  bool is_cache = 5; // 是否缓存
  string obs_key = 6; // obs key
}

message GetHotEmojisResp {
  repeated CommonEmojiInfo emojis = 1; // 表情列表
}

enum EmojiSource {
  EMOJI_SOURCE_UNSPECIFIED = 0;
  EMOJI_SOURCE_SHINE_API = 1; // 闪萌api
  EMOJI_SOURCE_CACHE_OR_SHINE = 2; // 缓存没有则走闪萌api
  EMOJI_SOURCE_CACHE = 3;  // 只走缓存
}

message GetEmojisByKeyWordReq {
  bool use_cache = 1; //deprecated 是否使用缓存
  string key_word = 2; // 关键词
  EmojiSource emoji_source = 3; // 表情来源
}

message GetEmojisByKeyWordResp {
  repeated CommonEmojiInfo emojis = 1; // 表情列表
  bool need_audit_to_cache = 2; // 是否需要审核入库
}

message CopyThirdPartyEmojiToObsReq {
  bytes emoji_msg = 1; // 表情包消息，见im.proto EmojiMsg
}

message CopyThirdPartyEmojiToObsResp {
  string obs_key = 1; // obs key
  string url = 2; // 表情包url
  bool is_obs = 3; // 是否obs
}

// 审核事件使用字段
enum AuditStage {
  AUDIT_STAGE_UNSPECIFIED = 0;
  AUDIT_STAGE_PULL_EMOJI = 1; // 拉取表情包
}
