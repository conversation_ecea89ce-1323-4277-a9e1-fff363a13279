syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/group-im-msg";
package group_im_msg;

message PreviewGroupImMsgReq {
  uint32 group_id = 1;
  uint32 limit = 2;
  string scene = 3;
}

message PreviewGroupImMsgResp {
  repeated bytes msg_list = 1;      // 一个 bytes 对应 一个 sync.NewMessageSync
}

service GroupImMsg {
  // 按时间倒序，最新的排在最前面
  rpc PreviewGroupImMsg(PreviewGroupImMsgReq) returns (PreviewGroupImMsgResp) {}
}