dir=$(pwd)
echo dir=$dir

#创建临时文件存储编译后的py文件
mkdir ./output

protoc --python_out=./output -I=$dir $dir/persona/tt/channel.proto
protoc --python_out=./output -I=$dir $dir/persona/tt/playmate.proto
protoc --python_out=./output -I=$dir $dir/persona/tt/user.proto
protoc --python_out=./output -I=$dir $dir/persona/tt/song.proto
protoc --python_out=./output -I=$dir $dir/persona/tt/cpid_channel_cpid.proto
protoc --python_out=./output -I=$dir $dir/persona/tt/post.proto
protoc --python_out=./output -I=$dir $dir/persona/tt/dimension.proto

protoc --python_out=./output -I=$dir $dir/common/common.proto
protoc --python_out=./output -I=$dir $dir/common/validate.proto
protoc --python_out=./output -I=$dir $dir/recall/recall_common.proto

protoc --python_out=./output -I=$dir $dir/persona/persona_collect.proto
protoc --python_out=./output -I=$dir $dir/persona/persona.proto
protoc --python_out=./output -I=$dir $dir/persona/options.proto


#打包编译文件后上传到obs
mv ./output ./rcmd
zip -r rcmd.zip rcmd
hdfs dfs -rm obs://bdp-datawarehouse/file-resource/tt/user_profile/rcmd.zip
hdfs dfs -put ./rcmd.zip obs://bdp-datawarehouse/file-resource/tt/user_profile/rcmd.zip

#删除临时文件
rm -rf ./rcmd
rm -rf ./rcmd.zip
