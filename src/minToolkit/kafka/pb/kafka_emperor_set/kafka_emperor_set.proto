syntax = "proto3";

package kafka_emperor_set;

message EmperorSetEvent
{
  uint32 uid = 1;
  uint32 target_uid = 2;
  uint32 set_id = 3;
  string order_id = 4;   // 帝王套的主订单id
  uint32 send_time = 5;
  string from_ukw_account = 6;  // 送礼神秘人账号，为空则不是神秘人
  string from_ukw_nickname = 7;  // 送礼神秘人昵称
  string to_ukw_account = 8;  // 收礼神秘人账号，为空则不是神秘人
  string to_ukw_nickname = 9;  // 收礼神秘人昵称
  uint32 channel_id = 10;  // 频道id
}
