syntax="proto3";

// ����import
import "common/tlvpickle/skbuiltintype.proto";

package authverifychecker;

message CheckReq {
  uint32 uid = 1;
  string client_ip = 2;
  string device_id = 3;
}


enum CheckResult {
  NEED_ALL_VERIFY = 0x0000;
  NEED_NO_SMS_VERIFY = 0x0001;
  NEED_NO_CAPTCHA_VERIFY = 0x0002;
  NEED_NO_BIND_PHONE = 0x0004;
  NEED_NO_VERIFY = 0x7fffffff;
}

message CheckResp {
  uint32 result = 1;    // CheckResult
}

// �°�ӿ�
message AuthCheckReq {
  uint32 uid = 1;
}

message AuthCheckResp {
  uint32 result = 1;
}

message AddWhitelistReq {
  repeated uint32 uid_list = 1;
  string description = 2;
  uint32 type = 3;
}

message AddWhitelistResp {
  bool result = 1;
}


service authVerifyChecker {
  option( tlvpickle.Magic ) = 15400;        // ��������˿ں�
  option( tlvpickle.ServerName ) = "authverifychecker";        // ������
  // �ɰ�ӿ�
  rpc Check( CheckReq ) returns ( CheckResp ) {
    option( tlvpickle.CmdID ) = 1;            // �����
    option( tlvpickle.OptString ) = "u:c:d:";     // ���Թ��ߵ�����Ų����� ע������ð��
    option( tlvpickle.Usage ) = "-u <uid> -c <client_ip> -d <device_id>";   // ���Թ��ߵ�����Ű���
  }

  // �ɰ�ӿ�
  rpc AddWhitelist( AddWhitelistReq ) returns ( AddWhitelistResp ) {
    option( tlvpickle.CmdID ) = 2;            // �����
    option( tlvpickle.OptString ) = "u:d:";     // ���Թ��ߵ�����Ų����� ע������ð��
    option( tlvpickle.Usage ) = "-u <uid1,uid2,uid3> -d <description>";   // ���Թ��ߵ�����Ű���
  }

  // �°��¼���ӿ�
  rpc AuthCheck( AuthCheckReq ) returns ( AuthCheckResp ) {
    option( tlvpickle.CmdID ) = 3;            // �����
    option( tlvpickle.OptString ) = "u:";     // ���Թ��ߵ�����Ų����� ע������ð��
    option( tlvpickle.Usage ) = "-u <uid>";   // ���Թ��ߵ�����Ű���
  }
  // �°�ӿ�
  rpc AddWhitelistWithType( AddWhitelistReq ) returns ( AddWhitelistResp ) {
    option( tlvpickle.CmdID ) = 4;            // �����
    option( tlvpickle.OptString ) = "u:d:t:";     // ���Թ��ߵ�����Ų����� ע������ð��
    option( tlvpickle.Usage ) = "-u <uid1,uid2,uid3> -t <WhitelistType��1-AuthVerify��2-SdkBindPhone> -d <description>";   // ���Թ��ߵ�����Ű���
  }

}
