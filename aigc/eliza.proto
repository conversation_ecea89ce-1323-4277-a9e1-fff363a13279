syntax = "proto3";

package aigc.eliza;

option go_package = "golang.52tt.com/protocol/services/aigc/eliza";
import "google/api/annotations.proto";
import "validate/validate.proto";
import "aigc/model.proto";
import "aigc/common.proto";

// 应用及场景管理
// api接口: Chat(含历史记录, context)

message AppCredential {
  string app_id = 1[(validate.rules).string = {min_len:4}];
  string app_key = 2[(validate.rules).string = {max_len:32}];
}

// 应用, 一个应用可以绑定多个场景(eliza)
message AppInfo {
  uint32 id = 1;

  uint32 owner_id = 7; // 对应T云的user_id, 应用所有者

  string app_id = 4; // 对应T云的service_account, 应用
  string app_name = 2;
  string biz_id = 5; // 对应T云的project, 业务
  string biz_name = 6;

  string app_key = 3; // 在eliza中创建app时生成
}
/*
 Eliza 是一个聊天机器人, 1964诞生于MIT.
 由<PERSON>开发, 以自然语言处理为基础, 通过模拟人类对话的方式, 与用户进行对话, 从而达到人工智能的目的.
 在api模型中，eliza id 和场景id 是一样的概念
 */
message ElizaInfo {
  uint32 id = 1;  // 场景id
  string name = 2;

  string biz_id = 6;
  string biz_name = 9;
  string app_id = 5;
  string app_name = 10;

  string model = 8; // 模型
  uint32 prompt_id = 7; // prompt
  string prompt_name = 11;
  // 描述tag, name:value
  map<string, string> tags = 3;
  // 占位符信息
  repeated string placeholders = 4;

  CostType cost_type = 12; // 扣费账号类型, 1个人; 2项目
  uint32 cost_account = 13; // 扣费账号ID
  string cost_account_name = 14; // 扣费账号名字

  model.ModelType model_type = 15; // 区分这个prompt是文生文还是文生图类型, 决定了prompt_id要从哪个服务取数据
  string QuotaCategory = 16; // 配额分类, 用于区分不同的配额, 例如toB, toC
}

message AppCallbackReq {
  string request_id = 1;
  string service_account = 2;
  string service_account_name = 3;
  string service_account_owner = 7;
  string project_id = 4 ;
  string project_name = 5;
  string creator = 6;
}
message AppCallbackRsp {
  message Status {
    uint32 code = 1;
    string message = 2;
  }
  Status status = 1;
}

message UpdateAppProjectReq {
  string app_id = 1;
  uint32 project_id = 2;
  string category = 3;
}
message UpdateAppProjectRsp {

}

message ListBizReq {
}
message ListBizRsp {
  message BizInfo {
    string biz_id = 1;
    string biz_name = 2;
  }
  repeated BizInfo list = 1;
}
message ListAppReq {
  string biz_id = 1;
}
message ListAppRsp {
  repeated AppInfo list = 1;
}

enum CostType {
  COST_TYPE_NONE = 0;
  COST_TYPE_PERSON = 1;
  COST_TYPE_PROJECT = 2;
}

message ListElizaReq {
  string biz_id = 3;
  string app_id = 1;
  string name_pattern = 2;  // 搜索
  CostType cost_type = 4; // 扣费账号类型, 1个人; 2项目
  uint32 cost_account = 5; // 扣费账号ID
  uint32 eliza_id = 6;   // eliza id
}

message ListElizaRsp {
  repeated ElizaInfo list = 1;
}


message AddElizaReq {
  string name = 2;
  string biz_id = 6;
  string biz_name = 9;
  string app_id = 5;
  string app_name = 10;

  string model = 8; // 模型, deprecated, 从prompt查询
  uint32 prompt_id = 7; // prompt
  string prompt_name = 11;
  // 描述tag, name:value
  map<string, string> tags = 3;

  CostType cost_type = 12; // 扣费账号类型, 1个人; 2项目
  uint32 cost_account = 13; // 扣费账号ID

  model.ModelType model_type = 14; // 区分这个prompt是文生文还是文生图类型, 决定了prompt_id要从哪个服务取数据
  string quota_category = 15; // 配额分类, 用于区分不同的配额, 例如toB, toC
}

message AddElizaRsp {
  ElizaInfo info = 1;
}

message UpdateElizaReq {
  uint32 id = 1;  // 场景id

  string name = 2;
  string biz_id = 6;
  string biz_name = 9;
  string app_id = 5;
  string app_name = 10;

  string model = 8;
  uint32 prompt_id = 7;
  string prompt_name = 11;
  map<string, string> tags = 3;

  CostType cost_type = 12; // 扣费账号类型, 1个人; 2项目
  uint32 cost_account = 13; // 扣费账号ID

  model.ModelType model_type = 14; // 区分这个prompt是文生文还是文生图类型, 决定了prompt_id要从哪个服务取数据
  string quota_category = 15; // 配额分类, 用于区分不同的配额, 例如toB, toC
}

message UpdateElizaRsp {
  ElizaInfo info = 1;
}
message DeleteElizaReq {
  uint32 id = 1;
}
message DeleteElizaRsp {

}

message Context {
  uint32 id = 1;
  string app_id = 2; // todo 暂时不返回
  uint32 uid = 3;
  uint32 prompt_id = 4;
  string prompt_name = 5;
  string model_bind = 6;
  uint32 model_max_token = 13;
  string tell_what = 7;
  uint32 prompt_token = 14;
  uint32 max_token = 8;
  float temperature = 9;
  uint32 n = 10;
  float presence_penalty = 11;
  float frequency_penalty = 12;
}
message History {
  uint32 id = 1;
  uint32 ctx_id = 2;
  string app_id = 3; // todo 暂时不返回
  uint32 uid = 4;
  string user_tell_what = 9;
  string q = 5;
  string a = 6;
  uint32 cost = 7;
  uint32 duration = 8;
}
message GetContextReq {
  uint32 ctx_id = 1;
}
message GetContextRsp {
  Context ctx = 1;
}
message GetHistoryReq {
  uint32 ctx_id = 1;
}
message GetHistoryRsp {
  repeated History history = 1;
}
message AddHistoryReq {
  uint32 ctx_id = 1;
  repeated History history = 2;
}
message AddHistoryRsp {

}

message ChatReq {
  AppCredential credential = 1[(validate.rules).message = {required:true}];
  uint32 eliza = 5[(validate.rules).uint32 = {gt:0}];

  // 当context_id为0时,会使用placeholder初始化新的context
  // 当context_id不为0时,直接使用已经存在的context。此时若placeholder字段不为空，会重新使用placeholder生成prompt
  uint32 context_id = 2;
  map<string, string> placeholder_info = 6;

  uint32 uid = 3;
  string q = 4;
  message Preset {
    string q = 1;
    string a = 2;
  }
  repeated Preset preset = 7;
  repeated Preset infix = 9;
  bool debug = 8;
}
message ChatRsp {
  uint32 context_id = 1;
  string a = 2;
  string model_used = 3;
  uint32 total_tokens = 5;      // 本次问答总token数
  uint32 completion_tokens = 6; // 本次问答响应token数
  uint32 model_max_token = 7;
  string req_id = 8;
  message Debug {
    uint32 duration_millis = 1;
    string json_req = 2;
  }
  Debug debug = 4;
}

message PaintParam {
  string user_prompt = 1;
}

message PaintOutputImage {
  string url = 1; //图片链接url
  string image_b64 = 2;
  string Path = 3; // 图片路径,不含域名
  string Type = 4; // 图片类型
  bool Censor = 5; // 图片鉴黄结果
  string CensorData = 6; // 图片审核失败提示
}

message PaintReq {
  AppCredential credential = 1[(validate.rules).message = {required:true}];
  uint32 eliza = 2[(validate.rules).uint32 = {gt:0}];
  uint32 uid = 3;
  PaintParam param = 4;
  bool debug = 5;
}

message PaintRsp {
  repeated PaintOutputImage images = 1;
  message Debug {
    uint32 duration_millis = 1;
    string json_req = 2;
  }
  Debug debug = 2;
  map<string, string> intermediate_images = 3;
  map<string, int32> controlnet_indexs = 4; // 需要感知条件顺序的业务方透传controlnet_indexs字段
}

service Eliza {
  rpc AppCallback(AppCallbackReq) returns (AppCallbackRsp) {
    // 【管理接口】T云开通promptt时回调
    option (google.api.http) = {
      post: "/eliza/app/callback",
      body: "*",
    };
  }

  rpc ListBiz(ListBizReq) returns (ListBizRsp) {
    // 获取业务列表
    option (google.api.http) = {
      post: "/eliza/biz/list",
      body: "*",
    };
  }
  rpc ListApp(ListAppReq) returns (ListAppRsp) {
    // 获取应用列表
    option (google.api.http) = {
      post: "/eliza/app/list",
      body: "*",
    };
  }

  rpc ListEliza (ListElizaReq) returns (ListElizaRsp) {
    // 查询app绑定的eliza列表
    option (google.api.http) = {
      post: "/eliza/eliza/get",
      body: "*",
    };
  }

  // add&update 在1.6.5之后，是promptt|painter发布时调用，前端不再调用
  rpc AddEliza(AddElizaReq) returns (AddElizaRsp) {
  }
  rpc UpdateEliza(UpdateElizaReq) returns (UpdateElizaRsp) {
  }
  rpc DeleteEliza(DeleteElizaReq) returns (DeleteElizaRsp) {
  }

  // --- query context && history, for debug ---
  rpc GetContext(GetContextReq) returns (GetContextRsp) {
    // 获取context
    option (google.api.http) = {
      post: "/eliza/context/get",
      body: "*",
    };
  }
  rpc GetHistory(GetHistoryReq) returns (GetHistoryRsp) {
    // 获取history
    option (google.api.http) = {
      post: "/eliza/history/get",
      body: "*",
    };
  }
  rpc AddHistory(AddHistoryReq) returns(AddHistoryRsp) {
    // 添加history
    option (google.api.http) = {
      post: "/eliza/history/add",
      body: "*",
    };
  }

  // --- api ---
  rpc Chat(ChatReq) returns (ChatRsp) {
    // 【应用接口】在指定的场景(eliza)中聊天
    // context_id 为0时，会创建一个新的context
    // context_id 不为0时，会使用指定的context
    option (google.api.http) = {
      post: "/eliza/chat",
      body: "*",
    };
  }

  rpc Paint(PaintReq) returns (PaintRsp) {
    // 【应用接口】在指定的场景(eliza)中作画
    option (google.api.http) = {
      post: "/eliza/paint",
      body: "*",
    };
  }

  // Deprecated, 为了支持function call, 使用raw http方式直接直接支持
  rpc ChatPro(ChatProReq) returns (stream ChatProRsp) {
    // 复刻openai接口，支持openai所有参数，支持非openai模型
    option (google.api.http) = {
      post: "/eliza/chat/completions",
      body: "*",
    };
  }

  rpc PaintPro(PaintProReq) returns (stream PaintRsp) {
    // 复刻sd接口，支持sd所有参数
    option (google.api.http) = {
      post: "/eliza/images/generations",
      body: "*",
    };
  }



  rpc Models(ModelsReq) returns (ModelsRsp) {
    // 获取模型列表
    option (google.api.http) = {
      get: "/eliza/v1/models",
    };
  }

  rpc Model(ModelReq) returns (ModelRsp) {
    // 获取模型列表
    option (google.api.http) = {
      get: "/eliza/models/{model}",
    };
  }
}

message ChatProMessage {
  string role = 1;
  string content = 2;
  string name = 3;
}
message ChatProReq {
  string model = 1;
  repeated ChatProMessage messages = 2;
  uint32 max_tokens = 3;
  float temperature = 4;
  float top_p = 5;
  uint32 n = 6;
  bool stream = 7;
  repeated string stop = 8;
  float presence_penalty = 9;
  float frequency_penalty = 10;
  map<string, int32> logit_bias = 11;
  string user = 12;
  bool debug = 15;
}

message ChatProChoice {
  int32 index = 1;
  ChatProMessage message = 2;
  ChatProMessage delta = 4; // stream模式返回，其中不带role
  string finish_reason = 3;
}
message ChatProRsp {
  string id = 1;
  string object = 2;
  int64 created = 3;
  string model = 4;
  repeated ChatProChoice choices = 5;
  message Usage {
    uint32 prompt_tokens = 1;
    uint32 completion_tokens = 2;
    uint32 total_tokens = 3;
  }
  Usage usage = 6; // stream模式最后一个相应带usage(扩展)
  uint32 context_id = 8;
  message Debug {
    uint32 duration_millis = 1;
    string json_req = 2;
  }
  Debug debug = 7;
}

message ModelsReq {
  AppCredential credential = 1;
}
message ModelsRsp {
  message Permission {
    string id = 1;
    string object = 2;
    uint32 created = 3;
  }
  message Model {
    string id = 3;
    string object = 1;
    uint32 created = 4;
    string owned_by = 5;
    string root = 6;
    repeated Permission Permission = 7;
  }
  string object = 2;
  repeated Model data = 1;
}

message ModelReq {
  AppCredential credential = 1;
  string model = 2;
}

message ModelRsp {
  string id = 1;
  int64 created = 2;
  string object = 3;
  string owned_by = 4;
}

message PaintProReq {
  string model = 1[(validate.rules).string = {min_len:1}];
  string prompt = 2[(validate.rules).string = {min_len:1}]; //输入的提示词
  string neg_prompt = 3; //不想要的提示词
  string init_image = 4; //初始图片
  string mask_image = 5;
  string sampler = 21; // 采样算法, 默认DPM++ 2M Karras
  uint32 n = 6[(validate.rules).uint32 = {lte:4,gte:1,ignore_empty:true}]; //生成图片的数量,默认1
  uint32 steps = 7[(validate.rules).uint32 = {lte:100,gte:1,ignore_empty:true}]; //生成图片的步数,默认30
  float cfg_scale = 8[(validate.rules).float = {lte:30,gt:0,ignore_empty:true}]; //图文相关性,默认7.5
  float strength = 9[(validate.rules).float = {lte:1,gt:0,ignore_empty:true}]; //图片重绘强度,默认0.75
  uint32 height = 10[(validate.rules).uint32 = {lte:1024,gte:512,ignore_empty:true}]; //生成图片的高度,默认512
  uint32 width = 11[(validate.rules).uint32 = {lte:1024,gte:512,ignore_empty:true}]; //生成图片的宽度,默认512
  int64 seed = 12[(validate.rules).int64 = {lte:4294967294,gte:-1,ignore_empty:true}]; //随机种子,默认-1
  bool stream = 13; //是否流式生成,默认false
  repeated string loras = 14; // lora模型
  repeated float lora_weights = 15[(validate.rules).repeated = {items:{float:{lte:10,gt:0,ignore_empty:true}}}]; // lora模型权重
  float upscale = 16[(validate.rules).float = {lte:4,gte:1,ignore_empty:true}]; // 图片超分比例,默认1.0
  string upscaler = 17; // 图片超分算法，默认realesr-general-x4v3
  float dni_strength = 18[(validate.rules).float = {lte:1,gte:0,ignore_empty:true}]; // 去噪强度,与超分算法配合使用,默认0.5
  //  bool gfpgan = 19; // 人脸修复, 调试中
  string ret_img_format = 20[(validate.rules).string = {in:["jpeg", "png"],ignore_empty:true}]; //返回图片格式,默认png
  string init_image_obs_path = 22;  // 填写image obs path，用于从obs获取图片
  string init_image_url = 23; // 填写外部的图片url，用于获取外部图片
  string init_image_base64 = 24;
  map<string, string> controlnet_images = 25;
  map<string, float> controlnet_conditioning_scale_map = 26;
  map<string, string> controlnet_result_images = 27;
  bool disable_init_image_url = 28;
  string mask_image_url = 29;
  string mask_image_base64 = 30;
  map<string, int32> controlnet_indexs = 31; // 需要感知条件顺序的业务方透传controlnet_indexs字段
  map<string, float> control_guidance_start = 32;
  map<string, float> control_guidance_end = 33;
  bool ret_base64 = 34; // 返回图片格式base64
  bool disable_intermediate = 35; // 禁用中间结果图的处理
  map<string, string> controlnet_raw_images = 36;
  float eta = 37[(validate.rules).float = {lte:1,gt:0,ignore_empty:true}]; // 默认0
  uint32 clip_skip = 38;        // 跳过clip模型计算层数，变更文本相关性
  bool hires_fix = 39;// 是否开启高清修复
  uint32 highresfix_steps = 40;  // 高清修复步数
  float highresfix_cfg_scale = 41; // 高清修复图文相关性
  float highresfix_ds = 42; // 高清修复强度
  string vae = 43;       // vae模型
  string sampler_index = 44;   // 采样算法
  common.JournalFrom journal_from = 45; //扣费方式,扣费来源 0未知, 1 web ,2 api
  common.AlwaysonScripts alwayson_scripts = 46;
  bool restore_faces = 47;
  float code_former_weight = 48;
  string face_restoration_model = 49;
}

message PaintProRsp {
  message Data {
    string model = 1;
    string prompt = 2;
    string neg_prompt = 3;
    string init_image = 4;
    string mask_image = 5;
    string sampler = 21;
    uint32 n = 6;
    uint32 steps = 7;
    float cfg_scale = 8;
    float strength = 9;
    uint32 height = 10;
    uint32 width = 11;
    int64 seed = 12;
    bool stream = 13;
    repeated string loras = 14;
    repeated float lora_weights = 15;
    float upscale = 16;
    string upscaler = 17;
    float dni_strength = 18;
    //  bool gfpgan = 19;
    string ret_img_format = 20;
    // 以上参数和输入一样,标识实际生效的参数值

    uint32 create_time = 30;
    float speed_time = 31;
    string image_name = 32;
    uint32 upscaled_width = 33;
    uint32 upscaled_height = 34; // 超分后的图片尺寸, 当upscale>1时有效
    string image_b64 = 35;
  }
  repeated Data data = 1;
  map<string, string> intermediate_images = 2;
  map<string, int32> controlnet_indexs = 3; // 需要感知条件顺序的业务方透传controlnet_indexs字段
}
message PaintProStreamRsp {
  uint32 stream_id = 1;
  PaintProRsp.Data data = 2;
  bool finish = 3;
}
