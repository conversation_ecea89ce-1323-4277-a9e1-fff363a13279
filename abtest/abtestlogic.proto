syntax="proto3";
package abtestlogic;

import "abtest/internal_common.proto";

option go_package="golang.52tt.com/protocol/services/rcmd/abtest/abtestlogic";

message PS_AbtestDomain {
    uint32 did = 1;                 //流量域id did=0 表示全部流量
    uint32 up_layer_id = 2;         //域所属的层id
    uint32 ld_ratio = 3;            //域流量占上一层域流量比例，同一层所有域流量比例和必须小于等于1000 
    uint32 ratio_ver = 4;           //流量版本号，每次流量调整会修改版本号值
    string up_layer_tag = 5;        //域所属的层 的 层标签
}

message PS_AbtestDomainRoute {
    repeated PS_AbtestDomain up_domain_list = 1;    //流量域层属路径，列表从左至右为 子域->父域 的关系，最左边是实验所在层使用的流量域，最右边是顶层流量域0
}

message PS_AbtestExptVer {
    uint32 exptv_id = 1;            // 实验版本ID 0表示未参与
    uint32 exptv_ratio = 2;         // 实验版本使用流量占实验流量比例，同一实验所有版本流量比例和必须小于等于1000
    uint32 expt_id = 3;             // 实验ID 0表示未参与
    uint32 expt_ratio = 4;          // 实验使用流量占层流量比例，同一层所有实验流量比例和必须小于等于1000
    uint32 expt_status = 5;         // 实验状态 use ps_db_enum.proto -> PS_ExperimentStatus 注：此字段不用上报
    uint32 layer_id = 6;            // 实验所在层ID
    uint32 expt_begin = 7;          // 实验开始时间戳
    uint32 expt_end = 8;            // 实验结束时间戳
    uint32 ratio_ver = 9;           // 流量版本号，每次流量调整会修改版本号值
    string layer_tag = 10;          // 层标签
    PS_AbtestDomainRoute domain_route = 11; // 层使用的流量域的从属路径
    uint32 client_type = 12;        // 用户标识类型 use ps_db_enum->PS_AbtestClientType
    uint32 terminal_type = 13;      // 实验终端类型 use ps_db_enum->PS_AbtestTerminalType
}

message PS_AbtestExpt {
    map<string, string> expt_argv = 1;  //实验参数值 map
    PS_AbtestExptVer expt_ver = 2;      //参数对应的实验版本信息，用于数据上报及统计分析
}

message PS_AbtestUser {
    uint32 client_type = 1;     //用户标识类型 use ps_db_enum->PS_AbtestClientType
    string client_id = 2;
}

message PS_UsersAbtest {
    PS_AbtestUser user = 1;
    repeated PS_AbtestExpt expt_list = 2;   //用户参与的实验，仅在 is_not_need_expt_ver = false 时返回
    map<string, string> default_argv = 3;   //默认参数值 map，用于兜底，用户未被分配参与、未开始、已结束的实验，可使用默认参数。仅在 is_not_need_expt_ver = false 时返回
                                            //default_argv 中的key与 expt_list 中 expt_argv 中的key互斥
    map<string, string> all_argv = 4;       //所有参数值 map，等于 default_argv + expt_argv 的并集，仅在 is_not_need_expt_ver = ture 时返回。
}

//按层标签搜索用户的实验配置信息
message PS_GetUsersAbtestByTagReq {
    string layer_tag = 1;                   //层标签
    repeated PS_AbtestUser user_list = 2;
    bool is_not_need_expt_ver = 3;          //是否不需要实验分组信息，default : false; 业务方只依靠ab试验平台上报分组信息，自己只关注具体的参数值时，设置为 true
    bool is_not_need_submit = 4;            //是否不需要上报分组信息到数据中台, default : false; 业务放想自己上报分组信息时，设置为 true，同时需要将 is_not_need_expt_ver 设置为 false
}

message PS_GetUsersAbtestByTagRsp {
    repeated PS_UsersAbtest user_test_list = 1;
}

// 根据一批层标签 批量搜索用户实验配置
message PS_BatchGetUsersAbtestByTagReq {
    repeated string layer_tag_list = 1;          //层标签列表
    repeated PS_AbtestUser user_list = 2; 
    bool is_not_need_expt_ver = 3;          //是否不需要实验分组信息，default : false; 业务方只依靠ab试验平台上报分组信息，自己只关注具体的参数值时，设置为 true
    bool is_not_need_submit = 4;            //是否不需要上报分组信息到数据中台, default : false; 业务放想自己上报分组信息时，设置为 true，同时需要将 is_not_need_expt_ver 设置为 false
    uint32 terminal_type = 5;                // 实验终端类型 use PS_AbtestTerminalType, 1代表服务端，2代表客户端，默认值0，不可组合使用，由于兼容性，0等同于1，即服务端
}

message PS_UsersAbtestWithTag {
    string layer_tag = 1;
    repeated PS_UsersAbtest user_test_list = 2;
}

message PS_BatchGetUsersAbtestByTagRsp {
    repeated PS_UsersAbtestWithTag tag_user_test_list = 1;
}

// 根据一批参数key 批量搜索用户实验配置
message PS_BatchGetUsersAbtestByArgKeyReq {
    repeated string arg_key_list = 1;          //参数key列表
    repeated PS_AbtestUser user_list = 2; 
    bool is_not_need_expt_ver = 3;          //是否不需要实验分组信息，default : false; 业务方只依靠ab试验平台上报分组信息，自己只关注具体的参数值时，设置为 true
    bool is_not_need_submit = 4;            //是否不需要上报分组信息到数据中台, default : false; 业务放想自己上报分组信息时，设置为 true，同时需要将 is_not_need_expt_ver 设置为 false
}

message PS_BatchGetUsersAbtestByArgKeyRsp {
    repeated PS_UsersAbtest user_test_list = 1;
}

//获取用户所有的实验配置信息 请求data字段内容 (用户id在 base_req 中传递)
message PS_GetUserAllAbtestReq {
    uint32 terminal_type = 1;       // 实验终端类型 use PS_AbtestTerminalType
}

//获取用户所有的实验配置信息  响应data字段内容
message PS_GetUserAllAbtestRsp {
    repeated PS_AbtestExpt expt_list = 1;   //用户参与的实验
    map<string, string> default_argv = 2;   //默认参数值 map，用于兜底，用户未被分配参与、未开始、已结束的实验，可使用默认参数。
                                            //default_argv 中的key与 expt_list 中 expt_argv 中的key互斥
    map<string, string> uid_default_argv = 3;   //按uid匹配到的 默认参数值 map，用于兜底，用户未被分配参与、未开始、已结束的实验，可使用默认参数。
    map<string, string> deviceid_default_argv = 4;   //按设备id匹配到的 默认参数值 map，用于兜底，用户未被分配参与、未开始、已结束的实验，可使用默认参数。
}

//查询用户指定参数的值 请求data字段内容  (用户id在 base_req 中传递)
message PS_GetUserAbtestByArgkeysReq {
    repeated string arg_key_list = 1;    //建议查询同一个实验中的参数key
}

//查询用户指定参数的值  响应data字段内容
message PS_GetUserAbtestByArgkeysRsp {
    repeated PS_AbtestExpt expt_list = 1;   //用户参与的实验
    map<string, string> default_argv = 2;   //默认参数值 map，用于兜底，用户未被分配参与、未开始、已结束的实验，可使用默认参数。
                                            //default_argv 中的key与 expt_list 中 expt_argv 中的key互斥
    map<string, string> uid_default_argv = 3;   //按uid匹配到的 默认参数值 map，用于兜底，用户未被分配参与、未开始、已结束的实验，可使用默认参数。
    map<string, string> deviceid_default_argv = 4;   //按设备id匹配到的 默认参数值 map，用于兜底，用户未被分配参与、未开始、已结束的实验，可使用默认参数。
}

//上报用户实验分组信息 请求data字段内容  (用户id在 base_req 中传递)
message PS_SubmitUserAbtestReq {
    PS_AbtestExptVer expt_ver = 1;   //用户参与的实验
}

//上报用户实验分组信息  响应data字段内容
message PS_SubmitUserAbtestRsp {
}

// 批量获取用户所有的实验配置信息
message PS_BatchGetUserAllAbtestReq {
    uint32 client_type = 1;               // 用户标识类型 use ps_db_enum->PS_AbtestClientType
    repeated string client_id_list = 2;     // 用户id列表
    bool is_not_need_expt_ver = 3;          // 是否不需要实验分组信息，default : false; 业务方只依靠ab试验平台上报分组信息，自己只关注具体的参数值时，设置为 true
}

message PS_BatchGetUserAllAbtestRsp {
    repeated PS_UsersAbtest user_test_list = 1;
}

service AbtestLogicService {
    // 多用户，服务端使用
    rpc GetUsersAbtestByTag(internal_common.PS_LogicCommonReq) returns (internal_common.PS_LogicCommonRsp);
    rpc BatchGetUserAllAbtest(internal_common.PS_LogicCommonReq) returns (internal_common.PS_LogicCommonRsp);
    rpc BatchGetUsersAbtestByTag(internal_common.PS_LogicCommonReq) returns (internal_common.PS_LogicCommonRsp);
    rpc BatchGetUsersAbtestByArgKey(internal_common.PS_LogicCommonReq) returns (internal_common.PS_LogicCommonRsp);

    // 单用户，一般是客户端使用，用户信息在base_req里
    rpc GetUserAllAbtest(internal_common.PS_LogicCommonReq) returns (internal_common.PS_LogicCommonRsp);
    rpc GetUserAbtestByArgkeys(internal_common.PS_LogicCommonReq) returns (internal_common.PS_LogicCommonRsp);
    rpc SubmitUserAbtest(internal_common.PS_LogicCommonReq) returns (internal_common.PS_LogicCommonRsp);
};