// Code generated by MockGen. DO NOT EDIT.
// Source: F:\griffin\clients\anchorcontract-go\iclient.go

// Package anchorcontract_go is a generated GoMock package.
package anchorcontract_go

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	anchorcontract_go "golang.52tt.com/protocol/services/anchorcontract-go"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AddSignWhiteUid mocks base method.
func (m *MockIClient) AddSignWhiteUid(ctx context.Context, req *anchorcontract_go.AddSignWhiteUidReq) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddSignWhiteUid", ctx, req)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// AddSignWhiteUid indicates an expected call of AddSignWhiteUid.
func (mr *MockIClientMockRecorder) AddSignWhiteUid(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddSignWhiteUid", reflect.TypeOf((*MockIClient)(nil).AddSignWhiteUid), ctx, req)
}

// ApplyCancelContractNew mocks base method.
func (m *MockIClient) ApplyCancelContractNew(ctx context.Context, req *anchorcontract_go.ApplyCancelContractNewReq) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplyCancelContractNew", ctx, req)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// ApplyCancelContractNew indicates an expected call of ApplyCancelContractNew.
func (mr *MockIClientMockRecorder) ApplyCancelContractNew(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplyCancelContractNew", reflect.TypeOf((*MockIClient)(nil).ApplyCancelContractNew), ctx, req)
}

// ApplySignContract mocks base method.
func (m *MockIClient) ApplySignContract(ctx context.Context, req *anchorcontract_go.ApplySignContractReq) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplySignContract", ctx, req)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// ApplySignContract indicates an expected call of ApplySignContract.
func (mr *MockIClientMockRecorder) ApplySignContract(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplySignContract", reflect.TypeOf((*MockIClient)(nil).ApplySignContract), ctx, req)
}

// ApplySignDoyen mocks base method.
func (m *MockIClient) ApplySignDoyen(ctx context.Context, req *anchorcontract_go.ApplySignDoyenReq) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplySignDoyen", ctx, req)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// ApplySignDoyen indicates an expected call of ApplySignDoyen.
func (mr *MockIClientMockRecorder) ApplySignDoyen(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplySignDoyen", reflect.TypeOf((*MockIClient)(nil).ApplySignDoyen), ctx, req)
}

// ApplySignEsport mocks base method.
func (m *MockIClient) ApplySignEsport(ctx context.Context, in *anchorcontract_go.ApplySignEsportReq) (*anchorcontract_go.ApplySignEsportResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplySignEsport", ctx, in)
	ret0, _ := ret[0].(*anchorcontract_go.ApplySignEsportResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ApplySignEsport indicates an expected call of ApplySignEsport.
func (mr *MockIClientMockRecorder) ApplySignEsport(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplySignEsport", reflect.TypeOf((*MockIClient)(nil).ApplySignEsport), ctx, in)
}

// BatchGetAnchorIdentity mocks base method.
func (m *MockIClient) BatchGetAnchorIdentity(ctx context.Context, uid uint32, in *anchorcontract_go.BatchGetAnchorIdentityReq) (*anchorcontract_go.BatchGetAnchorIdentityResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetAnchorIdentity", ctx, uid, in)
	ret0, _ := ret[0].(*anchorcontract_go.BatchGetAnchorIdentityResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetAnchorIdentity indicates an expected call of BatchGetAnchorIdentity.
func (mr *MockIClientMockRecorder) BatchGetAnchorIdentity(ctx, uid, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAnchorIdentity", reflect.TypeOf((*MockIClient)(nil).BatchGetAnchorIdentity), ctx, uid, in)
}

// BatchGetApplyBlacklist mocks base method.
func (m *MockIClient) BatchGetApplyBlacklist(ctx context.Context, uid uint32, in *anchorcontract_go.BatchGetApplyBlacklistReq) (*anchorcontract_go.BatchGetApplyBlacklistResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetApplyBlacklist", ctx, uid, in)
	ret0, _ := ret[0].(*anchorcontract_go.BatchGetApplyBlacklistResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetApplyBlacklist indicates an expected call of BatchGetApplyBlacklist.
func (mr *MockIClientMockRecorder) BatchGetApplyBlacklist(ctx, uid, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetApplyBlacklist", reflect.TypeOf((*MockIClient)(nil).BatchGetApplyBlacklist), ctx, uid, in)
}

// BatchGetContractInfo mocks base method.
func (m *MockIClient) BatchGetContractInfo(ctx context.Context, in *anchorcontract_go.BatchGetContractInfoReq) (*anchorcontract_go.BatchGetContractInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetContractInfo", ctx, in)
	ret0, _ := ret[0].(*anchorcontract_go.BatchGetContractInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetContractInfo indicates an expected call of BatchGetContractInfo.
func (mr *MockIClientMockRecorder) BatchGetContractInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetContractInfo", reflect.TypeOf((*MockIClient)(nil).BatchGetContractInfo), ctx, in)
}

// BatchGetLiveAnchorCert mocks base method.
func (m *MockIClient) BatchGetLiveAnchorCert(ctx context.Context, uids []uint32) (map[uint32]*anchorcontract_go.BatchGetLiveAnchorCertResp_LiveAnchorCertInfo, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetLiveAnchorCert", ctx, uids)
	ret0, _ := ret[0].(map[uint32]*anchorcontract_go.BatchGetLiveAnchorCertResp_LiveAnchorCertInfo)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetLiveAnchorCert indicates an expected call of BatchGetLiveAnchorCert.
func (mr *MockIClientMockRecorder) BatchGetLiveAnchorCert(ctx, uids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetLiveAnchorCert", reflect.TypeOf((*MockIClient)(nil).BatchGetLiveAnchorCert), ctx, uids)
}

// BatchGetUserAnchorIdentityLog mocks base method.
func (m *MockIClient) BatchGetUserAnchorIdentityLog(ctx context.Context, uid uint32, in *anchorcontract_go.BatchGetUserAnchorIdentityLogReq) (*anchorcontract_go.BatchGetUserAnchorIdentityLogResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserAnchorIdentityLog", ctx, uid, in)
	ret0, _ := ret[0].(*anchorcontract_go.BatchGetUserAnchorIdentityLogResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetUserAnchorIdentityLog indicates an expected call of BatchGetUserAnchorIdentityLog.
func (mr *MockIClientMockRecorder) BatchGetUserAnchorIdentityLog(ctx, uid, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserAnchorIdentityLog", reflect.TypeOf((*MockIClient)(nil).BatchGetUserAnchorIdentityLog), ctx, uid, in)
}

// BatchGetUserApplySignRecord mocks base method.
func (m *MockIClient) BatchGetUserApplySignRecord(ctx context.Context, uid uint32, in *anchorcontract_go.BatchGetUserApplySignRecordReq) (*anchorcontract_go.BatchGetUserApplySignRecordResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserApplySignRecord", ctx, uid, in)
	ret0, _ := ret[0].(*anchorcontract_go.BatchGetUserApplySignRecordResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetUserApplySignRecord indicates an expected call of BatchGetUserApplySignRecord.
func (mr *MockIClientMockRecorder) BatchGetUserApplySignRecord(ctx, uid, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserApplySignRecord", reflect.TypeOf((*MockIClient)(nil).BatchGetUserApplySignRecord), ctx, uid, in)
}

// BatchGetUserContract mocks base method.
func (m *MockIClient) BatchGetUserContract(ctx context.Context, uid uint32, in *anchorcontract_go.BatchGetUserContractReq) (*anchorcontract_go.BatchGetUserContractResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserContract", ctx, uid, in)
	ret0, _ := ret[0].(*anchorcontract_go.BatchGetUserContractResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetUserContract indicates an expected call of BatchGetUserContract.
func (mr *MockIClientMockRecorder) BatchGetUserContract(ctx, uid, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserContract", reflect.TypeOf((*MockIClient)(nil).BatchGetUserContract), ctx, uid, in)
}

// BatchGetUserContractCacheInfo mocks base method.
func (m *MockIClient) BatchGetUserContractCacheInfo(ctx context.Context, in *anchorcontract_go.BatchGetUserContractCacheInfoReq) (*anchorcontract_go.BatchGetUserContractCacheInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserContractCacheInfo", ctx, in)
	ret0, _ := ret[0].(*anchorcontract_go.BatchGetUserContractCacheInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetUserContractCacheInfo indicates an expected call of BatchGetUserContractCacheInfo.
func (mr *MockIClientMockRecorder) BatchGetUserContractCacheInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserContractCacheInfo", reflect.TypeOf((*MockIClient)(nil).BatchGetUserContractCacheInfo), ctx, in)
}

// BatchGetUserExamineCert mocks base method.
func (m *MockIClient) BatchGetUserExamineCert(ctx context.Context, uids []uint32) (*anchorcontract_go.BatchGetUserExamineCertResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserExamineCert", ctx, uids)
	ret0, _ := ret[0].(*anchorcontract_go.BatchGetUserExamineCertResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetUserExamineCert indicates an expected call of BatchGetUserExamineCert.
func (mr *MockIClientMockRecorder) BatchGetUserExamineCert(ctx, uids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserExamineCert", reflect.TypeOf((*MockIClient)(nil).BatchGetUserExamineCert), ctx, uids)
}

// BatchGetUserLiveAnchorExamine mocks base method.
func (m *MockIClient) BatchGetUserLiveAnchorExamine(ctx context.Context, uid uint32, in *anchorcontract_go.BatchGetUserLiveAnchorExamineReq) (*anchorcontract_go.BatchGetUserLiveAnchorExamineResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserLiveAnchorExamine", ctx, uid, in)
	ret0, _ := ret[0].(*anchorcontract_go.BatchGetUserLiveAnchorExamineResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetUserLiveAnchorExamine indicates an expected call of BatchGetUserLiveAnchorExamine.
func (mr *MockIClientMockRecorder) BatchGetUserLiveAnchorExamine(ctx, uid, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserLiveAnchorExamine", reflect.TypeOf((*MockIClient)(nil).BatchGetUserLiveAnchorExamine), ctx, uid, in)
}

// BatchGuildExtensionContract mocks base method.
func (m *MockIClient) BatchGuildExtensionContract(ctx context.Context, in *anchorcontract_go.BatchGuildExtensionContractReq) (*anchorcontract_go.BatchGuildExtensionContractResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGuildExtensionContract", ctx, in)
	ret0, _ := ret[0].(*anchorcontract_go.BatchGuildExtensionContractResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGuildExtensionContract indicates an expected call of BatchGuildExtensionContract.
func (mr *MockIClientMockRecorder) BatchGuildExtensionContract(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGuildExtensionContract", reflect.TypeOf((*MockIClient)(nil).BatchGuildExtensionContract), ctx, in)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// CancelContractByUid mocks base method.
func (m *MockIClient) CancelContractByUid(ctx context.Context, in *anchorcontract_go.CancelContractByUidReq) (*anchorcontract_go.CancelContractByUidResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelContractByUid", ctx, in)
	ret0, _ := ret[0].(*anchorcontract_go.CancelContractByUidResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CancelContractByUid indicates an expected call of CancelContractByUid.
func (mr *MockIClientMockRecorder) CancelContractByUid(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelContractByUid", reflect.TypeOf((*MockIClient)(nil).CancelContractByUid), ctx, in)
}

// CensorVideo mocks base method.
func (m *MockIClient) CensorVideo(ctx context.Context, req *anchorcontract_go.CensorVideoReq) (*anchorcontract_go.CensorVideoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CensorVideo", ctx, req)
	ret0, _ := ret[0].(*anchorcontract_go.CensorVideoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CensorVideo indicates an expected call of CensorVideo.
func (mr *MockIClientMockRecorder) CensorVideo(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CensorVideo", reflect.TypeOf((*MockIClient)(nil).CensorVideo), ctx, req)
}

// CheckCanApplyCancelContractV2 mocks base method.
func (m *MockIClient) CheckCanApplyCancelContractV2(ctx context.Context, req *anchorcontract_go.CheckCanApplyCancelContractV2Req) (*anchorcontract_go.CheckCanApplyCancelContractV2Resp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckCanApplyCancelContractV2", ctx, req)
	ret0, _ := ret[0].(*anchorcontract_go.CheckCanApplyCancelContractV2Resp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CheckCanApplyCancelContractV2 indicates an expected call of CheckCanApplyCancelContractV2.
func (mr *MockIClientMockRecorder) CheckCanApplyCancelContractV2(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckCanApplyCancelContractV2", reflect.TypeOf((*MockIClient)(nil).CheckCanApplyCancelContractV2), ctx, req)
}

// CheckCanApplySign mocks base method.
func (m *MockIClient) CheckCanApplySign(ctx context.Context, req *anchorcontract_go.CheckCanApplySignReq) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckCanApplySign", ctx, req)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// CheckCanApplySign indicates an expected call of CheckCanApplySign.
func (mr *MockIClientMockRecorder) CheckCanApplySign(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckCanApplySign", reflect.TypeOf((*MockIClient)(nil).CheckCanApplySign), ctx, req)
}

// CheckIfGreatLiveAnchor mocks base method.
func (m *MockIClient) CheckIfGreatLiveAnchor(ctx context.Context, req *anchorcontract_go.CheckIfGreatLiveAnchorReq) (*anchorcontract_go.CheckIfGreatLiveAnchorResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIfGreatLiveAnchor", ctx, req)
	ret0, _ := ret[0].(*anchorcontract_go.CheckIfGreatLiveAnchorResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CheckIfGreatLiveAnchor indicates an expected call of CheckIfGreatLiveAnchor.
func (mr *MockIClientMockRecorder) CheckIfGreatLiveAnchor(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIfGreatLiveAnchor", reflect.TypeOf((*MockIClient)(nil).CheckIfGreatLiveAnchor), ctx, req)
}

// CheckIsSignWhiteUid mocks base method.
func (m *MockIClient) CheckIsSignWhiteUid(ctx context.Context, req *anchorcontract_go.CheckIsSignWhiteUidReq) (*anchorcontract_go.CheckIsSignWhiteUidResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIsSignWhiteUid", ctx, req)
	ret0, _ := ret[0].(*anchorcontract_go.CheckIsSignWhiteUidResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CheckIsSignWhiteUid indicates an expected call of CheckIsSignWhiteUid.
func (mr *MockIClientMockRecorder) CheckIsSignWhiteUid(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIsSignWhiteUid", reflect.TypeOf((*MockIClient)(nil).CheckIsSignWhiteUid), ctx, req)
}

// CheckUserGreatLiveAnchor mocks base method.
func (m *MockIClient) CheckUserGreatLiveAnchor(ctx context.Context, req *anchorcontract_go.CheckUserGreatLiveAnchorReq) (*anchorcontract_go.CheckUserGreatLiveAnchorResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckUserGreatLiveAnchor", ctx, req)
	ret0, _ := ret[0].(*anchorcontract_go.CheckUserGreatLiveAnchorResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CheckUserGreatLiveAnchor indicates an expected call of CheckUserGreatLiveAnchor.
func (mr *MockIClientMockRecorder) CheckUserGreatLiveAnchor(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUserGreatLiveAnchor", reflect.TypeOf((*MockIClient)(nil).CheckUserGreatLiveAnchor), ctx, req)
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// ContractClaimObsToken mocks base method.
func (m *MockIClient) ContractClaimObsToken(ctx context.Context, req *anchorcontract_go.ContractClaimObsTokenReq) (*anchorcontract_go.ContractClaimObsTokenResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ContractClaimObsToken", ctx, req)
	ret0, _ := ret[0].(*anchorcontract_go.ContractClaimObsTokenResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ContractClaimObsToken indicates an expected call of ContractClaimObsToken.
func (mr *MockIClientMockRecorder) ContractClaimObsToken(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContractClaimObsToken", reflect.TypeOf((*MockIClient)(nil).ContractClaimObsToken), ctx, req)
}

// DelSignWhiteUid mocks base method.
func (m *MockIClient) DelSignWhiteUid(ctx context.Context, req *anchorcontract_go.DelSignWhiteUidReq) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelSignWhiteUid", ctx, req)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// DelSignWhiteUid indicates an expected call of DelSignWhiteUid.
func (mr *MockIClientMockRecorder) DelSignWhiteUid(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelSignWhiteUid", reflect.TypeOf((*MockIClient)(nil).DelSignWhiteUid), ctx, req)
}

// GetAllApplyBlacklist mocks base method.
func (m *MockIClient) GetAllApplyBlacklist(ctx context.Context, uid uint32, in *anchorcontract_go.GetAllApplyBlacklistReq) (*anchorcontract_go.GetAllApplyBlacklistResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllApplyBlacklist", ctx, uid, in)
	ret0, _ := ret[0].(*anchorcontract_go.GetAllApplyBlacklistResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAllApplyBlacklist indicates an expected call of GetAllApplyBlacklist.
func (mr *MockIClientMockRecorder) GetAllApplyBlacklist(ctx, uid, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllApplyBlacklist", reflect.TypeOf((*MockIClient)(nil).GetAllApplyBlacklist), ctx, uid, in)
}

// GetAllApplySignRecord mocks base method.
func (m *MockIClient) GetAllApplySignRecord(ctx context.Context, uid uint32, in *anchorcontract_go.GetAllApplySignRecordReq) (*anchorcontract_go.GetAllApplySignRecordResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllApplySignRecord", ctx, uid, in)
	ret0, _ := ret[0].(*anchorcontract_go.GetAllApplySignRecordResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAllApplySignRecord indicates an expected call of GetAllApplySignRecord.
func (mr *MockIClientMockRecorder) GetAllApplySignRecord(ctx, uid, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllApplySignRecord", reflect.TypeOf((*MockIClient)(nil).GetAllApplySignRecord), ctx, uid, in)
}

// GetAllLiveAnchorExamine mocks base method.
func (m *MockIClient) GetAllLiveAnchorExamine(ctx context.Context, uid uint32, in *anchorcontract_go.GetAllLiveAnchorExamineReq) (*anchorcontract_go.GetAllLiveAnchorExamineResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllLiveAnchorExamine", ctx, uid, in)
	ret0, _ := ret[0].(*anchorcontract_go.GetAllLiveAnchorExamineResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAllLiveAnchorExamine indicates an expected call of GetAllLiveAnchorExamine.
func (mr *MockIClientMockRecorder) GetAllLiveAnchorExamine(ctx, uid, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllLiveAnchorExamine", reflect.TypeOf((*MockIClient)(nil).GetAllLiveAnchorExamine), ctx, uid, in)
}

// GetAnchorAgentUid mocks base method.
func (m *MockIClient) GetAnchorAgentUid(ctx context.Context, uids []uint32) (*anchorcontract_go.GetAnchorAgentUidResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorAgentUid", ctx, uids)
	ret0, _ := ret[0].(*anchorcontract_go.GetAnchorAgentUidResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorAgentUid indicates an expected call of GetAnchorAgentUid.
func (mr *MockIClientMockRecorder) GetAnchorAgentUid(ctx, uids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorAgentUid", reflect.TypeOf((*MockIClient)(nil).GetAnchorAgentUid), ctx, uids)
}

// GetAnchorCertListByItemId mocks base method.
func (m *MockIClient) GetAnchorCertListByItemId(ctx context.Context, in *anchorcontract_go.GetAnchorCertListByItemIdReq) (*anchorcontract_go.GetAnchorCertListByItemIdResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorCertListByItemId", ctx, in)
	ret0, _ := ret[0].(*anchorcontract_go.GetAnchorCertListByItemIdResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAnchorCertListByItemId indicates an expected call of GetAnchorCertListByItemId.
func (mr *MockIClientMockRecorder) GetAnchorCertListByItemId(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorCertListByItemId", reflect.TypeOf((*MockIClient)(nil).GetAnchorCertListByItemId), ctx, in)
}

// GetAnchorCertTaskInfo mocks base method.
func (m *MockIClient) GetAnchorCertTaskInfo(ctx context.Context, uid uint32) (*anchorcontract_go.GetAnchorCertTaskInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorCertTaskInfo", ctx, uid)
	ret0, _ := ret[0].(*anchorcontract_go.GetAnchorCertTaskInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAnchorCertTaskInfo indicates an expected call of GetAnchorCertTaskInfo.
func (mr *MockIClientMockRecorder) GetAnchorCertTaskInfo(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorCertTaskInfo", reflect.TypeOf((*MockIClient)(nil).GetAnchorCertTaskInfo), ctx, uid)
}

// GetCancelContractApplyList mocks base method.
func (m *MockIClient) GetCancelContractApplyList(ctx context.Context, req *anchorcontract_go.GetCancelContractApplyListReq) (*anchorcontract_go.GetCancelContractApplyListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCancelContractApplyList", ctx, req)
	ret0, _ := ret[0].(*anchorcontract_go.GetCancelContractApplyListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetCancelContractApplyList indicates an expected call of GetCancelContractApplyList.
func (mr *MockIClientMockRecorder) GetCancelContractApplyList(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCancelContractApplyList", reflect.TypeOf((*MockIClient)(nil).GetCancelContractApplyList), ctx, req)
}

// GetCancelContractTypeList mocks base method.
func (m *MockIClient) GetCancelContractTypeList(ctx context.Context, req *anchorcontract_go.GetCancelContractTypeListReq) (*anchorcontract_go.GetCancelContractTypeListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCancelContractTypeList", ctx, req)
	ret0, _ := ret[0].(*anchorcontract_go.GetCancelContractTypeListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetCancelContractTypeList indicates an expected call of GetCancelContractTypeList.
func (mr *MockIClientMockRecorder) GetCancelContractTypeList(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCancelContractTypeList", reflect.TypeOf((*MockIClient)(nil).GetCancelContractTypeList), ctx, req)
}

// GetCancelPayAmount mocks base method.
func (m *MockIClient) GetCancelPayAmount(ctx context.Context, req *anchorcontract_go.GetCancelPayAmountReq) (*anchorcontract_go.GetCancelPayAmountResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCancelPayAmount", ctx, req)
	ret0, _ := ret[0].(*anchorcontract_go.GetCancelPayAmountResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetCancelPayAmount indicates an expected call of GetCancelPayAmount.
func (mr *MockIClientMockRecorder) GetCancelPayAmount(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCancelPayAmount", reflect.TypeOf((*MockIClient)(nil).GetCancelPayAmount), ctx, req)
}

// GetContract mocks base method.
func (m *MockIClient) GetContract(ctx context.Context, in *anchorcontract_go.GetContractReq) (*anchorcontract_go.GetContractResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetContract", ctx, in)
	ret0, _ := ret[0].(*anchorcontract_go.GetContractResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetContract indicates an expected call of GetContract.
func (mr *MockIClientMockRecorder) GetContract(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetContract", reflect.TypeOf((*MockIClient)(nil).GetContract), ctx, in)
}

// GetContractWorkerConfigs mocks base method.
func (m *MockIClient) GetContractWorkerConfigs(ctx context.Context, req *anchorcontract_go.GetContractWorkerConfigsReq) (*anchorcontract_go.GetContractWorkerConfigsResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetContractWorkerConfigs", ctx, req)
	ret0, _ := ret[0].(*anchorcontract_go.GetContractWorkerConfigsResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetContractWorkerConfigs indicates an expected call of GetContractWorkerConfigs.
func (mr *MockIClientMockRecorder) GetContractWorkerConfigs(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetContractWorkerConfigs", reflect.TypeOf((*MockIClient)(nil).GetContractWorkerConfigs), ctx, req)
}

// GetGuildAnchorExtInfoList mocks base method.
func (m *MockIClient) GetGuildAnchorExtInfoList(ctx context.Context, in *anchorcontract_go.GetGuildAnchorExtInfoListReq) (*anchorcontract_go.GetGuildAnchorExtInfoListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildAnchorExtInfoList", ctx, in)
	ret0, _ := ret[0].(*anchorcontract_go.GetGuildAnchorExtInfoListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildAnchorExtInfoList indicates an expected call of GetGuildAnchorExtInfoList.
func (mr *MockIClientMockRecorder) GetGuildAnchorExtInfoList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildAnchorExtInfoList", reflect.TypeOf((*MockIClient)(nil).GetGuildAnchorExtInfoList), ctx, in)
}

// GetGuildAnchorIdentity mocks base method.
func (m *MockIClient) GetGuildAnchorIdentity(ctx context.Context, uid uint32, in *anchorcontract_go.GetGuildAnchorIdentityReq) (*anchorcontract_go.GetGuildAnchorIdentityResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildAnchorIdentity", ctx, uid, in)
	ret0, _ := ret[0].(*anchorcontract_go.GetGuildAnchorIdentityResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildAnchorIdentity indicates an expected call of GetGuildAnchorIdentity.
func (mr *MockIClientMockRecorder) GetGuildAnchorIdentity(ctx, uid, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildAnchorIdentity", reflect.TypeOf((*MockIClient)(nil).GetGuildAnchorIdentity), ctx, uid, in)
}

// GetGuildAnchorIdentityLog mocks base method.
func (m *MockIClient) GetGuildAnchorIdentityLog(ctx context.Context, uid uint32, in *anchorcontract_go.GetGuildAnchorIdentityLogReq) (*anchorcontract_go.GetGuildAnchorIdentityLogResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildAnchorIdentityLog", ctx, uid, in)
	ret0, _ := ret[0].(*anchorcontract_go.GetGuildAnchorIdentityLogResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildAnchorIdentityLog indicates an expected call of GetGuildAnchorIdentityLog.
func (mr *MockIClientMockRecorder) GetGuildAnchorIdentityLog(ctx, uid, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildAnchorIdentityLog", reflect.TypeOf((*MockIClient)(nil).GetGuildAnchorIdentityLog), ctx, uid, in)
}

// GetGuildApplySignRecord mocks base method.
func (m *MockIClient) GetGuildApplySignRecord(ctx context.Context, uid uint32, in *anchorcontract_go.GetGuildApplySignRecordReq) (*anchorcontract_go.GetGuildApplySignRecordResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildApplySignRecord", ctx, uid, in)
	ret0, _ := ret[0].(*anchorcontract_go.GetGuildApplySignRecordResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildApplySignRecord indicates an expected call of GetGuildApplySignRecord.
func (mr *MockIClientMockRecorder) GetGuildApplySignRecord(ctx, uid, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildApplySignRecord", reflect.TypeOf((*MockIClient)(nil).GetGuildApplySignRecord), ctx, uid, in)
}

// GetGuildApplySignRecordCnt mocks base method.
func (m *MockIClient) GetGuildApplySignRecordCnt(ctx context.Context, uid uint32, in *anchorcontract_go.GetGuildApplySignRecordCntReq) (*anchorcontract_go.GetGuildApplySignRecordCntResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildApplySignRecordCnt", ctx, uid, in)
	ret0, _ := ret[0].(*anchorcontract_go.GetGuildApplySignRecordCntResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildApplySignRecordCnt indicates an expected call of GetGuildApplySignRecordCnt.
func (mr *MockIClientMockRecorder) GetGuildApplySignRecordCnt(ctx, uid, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildApplySignRecordCnt", reflect.TypeOf((*MockIClient)(nil).GetGuildApplySignRecordCnt), ctx, uid, in)
}

// GetGuildApplySignRecordList mocks base method.
func (m *MockIClient) GetGuildApplySignRecordList(ctx context.Context, in *anchorcontract_go.GetGuildApplySignRecordListReq) (*anchorcontract_go.GetGuildApplySignRecordListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildApplySignRecordList", ctx, in)
	ret0, _ := ret[0].(*anchorcontract_go.GetGuildApplySignRecordListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildApplySignRecordList indicates an expected call of GetGuildApplySignRecordList.
func (mr *MockIClientMockRecorder) GetGuildApplySignRecordList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildApplySignRecordList", reflect.TypeOf((*MockIClient)(nil).GetGuildApplySignRecordList), ctx, in)
}

// GetGuildCancelSignRecordList mocks base method.
func (m *MockIClient) GetGuildCancelSignRecordList(ctx context.Context, in *anchorcontract_go.GetGuildCancelSignRecordListReq) (*anchorcontract_go.GetGuildCancelSignRecordListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildCancelSignRecordList", ctx, in)
	ret0, _ := ret[0].(*anchorcontract_go.GetGuildCancelSignRecordListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildCancelSignRecordList indicates an expected call of GetGuildCancelSignRecordList.
func (mr *MockIClientMockRecorder) GetGuildCancelSignRecordList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildCancelSignRecordList", reflect.TypeOf((*MockIClient)(nil).GetGuildCancelSignRecordList), ctx, in)
}

// GetGuildContract mocks base method.
func (m *MockIClient) GetGuildContract(ctx context.Context, guildId, page, pageSize uint32) (*anchorcontract_go.GetGuildContractResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildContract", ctx, guildId, page, pageSize)
	ret0, _ := ret[0].(*anchorcontract_go.GetGuildContractResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildContract indicates an expected call of GetGuildContract.
func (mr *MockIClientMockRecorder) GetGuildContract(ctx, guildId, page, pageSize interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildContract", reflect.TypeOf((*MockIClient)(nil).GetGuildContract), ctx, guildId, page, pageSize)
}

// GetGuildContractByCond mocks base method.
func (m *MockIClient) GetGuildContractByCond(ctx context.Context, req *anchorcontract_go.GetGuildContractByCondReq) (*anchorcontract_go.GetGuildContractByCondResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildContractByCond", ctx, req)
	ret0, _ := ret[0].(*anchorcontract_go.GetGuildContractByCondResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildContractByCond indicates an expected call of GetGuildContractByCond.
func (mr *MockIClientMockRecorder) GetGuildContractByCond(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildContractByCond", reflect.TypeOf((*MockIClient)(nil).GetGuildContractByCond), ctx, req)
}

// GetGuildContractByIdentity mocks base method.
func (m *MockIClient) GetGuildContractByIdentity(ctx context.Context, in *anchorcontract_go.GetGuildContractByIdentityReq) (*anchorcontract_go.GetGuildContractByIdentityResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildContractByIdentity", ctx, in)
	ret0, _ := ret[0].(*anchorcontract_go.GetGuildContractByIdentityResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildContractByIdentity indicates an expected call of GetGuildContractByIdentity.
func (mr *MockIClientMockRecorder) GetGuildContractByIdentity(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildContractByIdentity", reflect.TypeOf((*MockIClient)(nil).GetGuildContractByIdentity), ctx, in)
}

// GetGuildContractSum mocks base method.
func (m *MockIClient) GetGuildContractSum(ctx context.Context, guildId uint32) (*anchorcontract_go.GetGuildContractSumResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildContractSum", ctx, guildId)
	ret0, _ := ret[0].(*anchorcontract_go.GetGuildContractSumResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildContractSum indicates an expected call of GetGuildContractSum.
func (mr *MockIClientMockRecorder) GetGuildContractSum(ctx, guildId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildContractSum", reflect.TypeOf((*MockIClient)(nil).GetGuildContractSum), ctx, guildId)
}

// GetGuildLiveAnchorExamine mocks base method.
func (m *MockIClient) GetGuildLiveAnchorExamine(ctx context.Context, uid uint32, in *anchorcontract_go.GetGuildLiveAnchorExamineReq) (*anchorcontract_go.GetGuildLiveAnchorExamineResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildLiveAnchorExamine", ctx, uid, in)
	ret0, _ := ret[0].(*anchorcontract_go.GetGuildLiveAnchorExamineResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildLiveAnchorExamine indicates an expected call of GetGuildLiveAnchorExamine.
func (mr *MockIClientMockRecorder) GetGuildLiveAnchorExamine(ctx, uid, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildLiveAnchorExamine", reflect.TypeOf((*MockIClient)(nil).GetGuildLiveAnchorExamine), ctx, uid, in)
}

// GetGuildSignRight mocks base method.
func (m *MockIClient) GetGuildSignRight(ctx context.Context, req *anchorcontract_go.GetGuildSignRightReq) (*anchorcontract_go.GetGuildSignRightResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildSignRight", ctx, req)
	ret0, _ := ret[0].(*anchorcontract_go.GetGuildSignRightResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildSignRight indicates an expected call of GetGuildSignRight.
func (mr *MockIClientMockRecorder) GetGuildSignRight(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildSignRight", reflect.TypeOf((*MockIClient)(nil).GetGuildSignRight), ctx, req)
}

// GetIdentityChangeHistory mocks base method.
func (m *MockIClient) GetIdentityChangeHistory(ctx context.Context, in *anchorcontract_go.GetIdentityChangeHistoryReq) (*anchorcontract_go.GetIdentityChangeHistoryResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIdentityChangeHistory", ctx, in)
	ret0, _ := ret[0].(*anchorcontract_go.GetIdentityChangeHistoryResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetIdentityChangeHistory indicates an expected call of GetIdentityChangeHistory.
func (mr *MockIClientMockRecorder) GetIdentityChangeHistory(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIdentityChangeHistory", reflect.TypeOf((*MockIClient)(nil).GetIdentityChangeHistory), ctx, in)
}

// GetMultiPlayerCenterEntry mocks base method.
func (m *MockIClient) GetMultiPlayerCenterEntry(ctx context.Context, uid uint32) (string, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMultiPlayerCenterEntry", ctx, uid)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetMultiPlayerCenterEntry indicates an expected call of GetMultiPlayerCenterEntry.
func (mr *MockIClientMockRecorder) GetMultiPlayerCenterEntry(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiPlayerCenterEntry", reflect.TypeOf((*MockIClient)(nil).GetMultiPlayerCenterEntry), ctx, uid)
}

// GetNeedConfirmWorkerType mocks base method.
func (m *MockIClient) GetNeedConfirmWorkerType(ctx context.Context, req *anchorcontract_go.GetNeedConfirmWorkerTypeReq) (*anchorcontract_go.GetNeedConfirmWorkerTypeResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNeedConfirmWorkerType", ctx, req)
	ret0, _ := ret[0].(*anchorcontract_go.GetNeedConfirmWorkerTypeResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetNeedConfirmWorkerType indicates an expected call of GetNeedConfirmWorkerType.
func (mr *MockIClientMockRecorder) GetNeedConfirmWorkerType(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNeedConfirmWorkerType", reflect.TypeOf((*MockIClient)(nil).GetNeedConfirmWorkerType), ctx, req)
}

// GetRadioLiveAnchorExamine mocks base method.
func (m *MockIClient) GetRadioLiveAnchorExamine(ctx context.Context, uid uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRadioLiveAnchorExamine", ctx, uid)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRadioLiveAnchorExamine indicates an expected call of GetRadioLiveAnchorExamine.
func (mr *MockIClientMockRecorder) GetRadioLiveAnchorExamine(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRadioLiveAnchorExamine", reflect.TypeOf((*MockIClient)(nil).GetRadioLiveAnchorExamine), ctx, uid)
}

// GetRecommendTopGuildList mocks base method.
func (m *MockIClient) GetRecommendTopGuildList(ctx context.Context, req *anchorcontract_go.GetRecommendTopGuildListReq) (*anchorcontract_go.GetRecommendTopGuildListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecommendTopGuildList", ctx, req)
	ret0, _ := ret[0].(*anchorcontract_go.GetRecommendTopGuildListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetRecommendTopGuildList indicates an expected call of GetRecommendTopGuildList.
func (mr *MockIClientMockRecorder) GetRecommendTopGuildList(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecommendTopGuildList", reflect.TypeOf((*MockIClient)(nil).GetRecommendTopGuildList), ctx, req)
}

// GetSignEsportAuditToken mocks base method.
func (m *MockIClient) GetSignEsportAuditToken(ctx context.Context, applyId uint32) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSignEsportAuditToken", ctx, applyId)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSignEsportAuditToken indicates an expected call of GetSignEsportAuditToken.
func (mr *MockIClientMockRecorder) GetSignEsportAuditToken(ctx, applyId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSignEsportAuditToken", reflect.TypeOf((*MockIClient)(nil).GetSignEsportAuditToken), ctx, applyId)
}

// GetUserAnchorIdentityLog mocks base method.
func (m *MockIClient) GetUserAnchorIdentityLog(ctx context.Context, uid uint32, in *anchorcontract_go.GetUserAnchorIdentityLogReq) (*anchorcontract_go.GetUserAnchorIdentityLogResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserAnchorIdentityLog", ctx, uid, in)
	ret0, _ := ret[0].(*anchorcontract_go.GetUserAnchorIdentityLogResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserAnchorIdentityLog indicates an expected call of GetUserAnchorIdentityLog.
func (mr *MockIClientMockRecorder) GetUserAnchorIdentityLog(ctx, uid, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAnchorIdentityLog", reflect.TypeOf((*MockIClient)(nil).GetUserAnchorIdentityLog), ctx, uid, in)
}

// GetUserApplySignRecord mocks base method.
func (m *MockIClient) GetUserApplySignRecord(ctx context.Context, uid uint32, in *anchorcontract_go.GetUserApplySignRecordReq) (*anchorcontract_go.GetUserApplySignRecordResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserApplySignRecord", ctx, uid, in)
	ret0, _ := ret[0].(*anchorcontract_go.GetUserApplySignRecordResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserApplySignRecord indicates an expected call of GetUserApplySignRecord.
func (mr *MockIClientMockRecorder) GetUserApplySignRecord(ctx, uid, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserApplySignRecord", reflect.TypeOf((*MockIClient)(nil).GetUserApplySignRecord), ctx, uid, in)
}

// GetUserContract mocks base method.
func (m *MockIClient) GetUserContract(ctx context.Context, uin, targetUid uint32) (*anchorcontract_go.GetUserContractResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserContract", ctx, uin, targetUid)
	ret0, _ := ret[0].(*anchorcontract_go.GetUserContractResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserContract indicates an expected call of GetUserContract.
func (mr *MockIClientMockRecorder) GetUserContract(ctx, uin, targetUid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserContract", reflect.TypeOf((*MockIClient)(nil).GetUserContract), ctx, uin, targetUid)
}

// GetUserContractCacheInfo mocks base method.
func (m *MockIClient) GetUserContractCacheInfo(ctx context.Context, uin, targetUid uint32) (*anchorcontract_go.ContractCacheInfo, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserContractCacheInfo", ctx, uin, targetUid)
	ret0, _ := ret[0].(*anchorcontract_go.ContractCacheInfo)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserContractCacheInfo indicates an expected call of GetUserContractCacheInfo.
func (mr *MockIClientMockRecorder) GetUserContractCacheInfo(ctx, uin, targetUid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserContractCacheInfo", reflect.TypeOf((*MockIClient)(nil).GetUserContractCacheInfo), ctx, uin, targetUid)
}

// GetUserExamineCert mocks base method.
func (m *MockIClient) GetUserExamineCert(ctx context.Context, uid uint32) (*anchorcontract_go.GetUserExamineCertResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserExamineCert", ctx, uid)
	ret0, _ := ret[0].(*anchorcontract_go.GetUserExamineCertResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserExamineCert indicates an expected call of GetUserExamineCert.
func (mr *MockIClientMockRecorder) GetUserExamineCert(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserExamineCert", reflect.TypeOf((*MockIClient)(nil).GetUserExamineCert), ctx, uid)
}

// GetUserPromoteInviteInfo mocks base method.
func (m *MockIClient) GetUserPromoteInviteInfo(ctx context.Context, req *anchorcontract_go.GetUserPromoteInviteInfoReq) (*anchorcontract_go.GetUserPromoteInviteInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPromoteInviteInfo", ctx, req)
	ret0, _ := ret[0].(*anchorcontract_go.GetUserPromoteInviteInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserPromoteInviteInfo indicates an expected call of GetUserPromoteInviteInfo.
func (mr *MockIClientMockRecorder) GetUserPromoteInviteInfo(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPromoteInviteInfo", reflect.TypeOf((*MockIClient)(nil).GetUserPromoteInviteInfo), ctx, req)
}

// GuildExtensionContract mocks base method.
func (m *MockIClient) GuildExtensionContract(ctx context.Context, in *anchorcontract_go.GuildExtensionContractReq) (*anchorcontract_go.GuildExtensionContractResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GuildExtensionContract", ctx, in)
	ret0, _ := ret[0].(*anchorcontract_go.GuildExtensionContractResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GuildExtensionContract indicates an expected call of GuildExtensionContract.
func (mr *MockIClientMockRecorder) GuildExtensionContract(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GuildExtensionContract", reflect.TypeOf((*MockIClient)(nil).GuildExtensionContract), ctx, in)
}

// HandleApplyBlackInfo mocks base method.
func (m *MockIClient) HandleApplyBlackInfo(ctx context.Context, uid uint32, in *anchorcontract_go.HandleApplyBlackInfoReq) (*anchorcontract_go.HandleApplyBlackInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleApplyBlackInfo", ctx, uid, in)
	ret0, _ := ret[0].(*anchorcontract_go.HandleApplyBlackInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// HandleApplyBlackInfo indicates an expected call of HandleApplyBlackInfo.
func (mr *MockIClientMockRecorder) HandleApplyBlackInfo(ctx, uid, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleApplyBlackInfo", reflect.TypeOf((*MockIClient)(nil).HandleApplyBlackInfo), ctx, uid, in)
}

// HandleFocusAnchor mocks base method.
func (m *MockIClient) HandleFocusAnchor(ctx context.Context, in *anchorcontract_go.HandleFocusAnchorReq) (*anchorcontract_go.HandleFocusAnchorResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleFocusAnchor", ctx, in)
	ret0, _ := ret[0].(*anchorcontract_go.HandleFocusAnchorResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// HandleFocusAnchor indicates an expected call of HandleFocusAnchor.
func (mr *MockIClientMockRecorder) HandleFocusAnchor(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleFocusAnchor", reflect.TypeOf((*MockIClient)(nil).HandleFocusAnchor), ctx, in)
}

// HandlerCancelContractApply mocks base method.
func (m *MockIClient) HandlerCancelContractApply(ctx context.Context, in *anchorcontract_go.HandlerCancelContractApplyReq) (*anchorcontract_go.HandlerCancelContractApplyResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandlerCancelContractApply", ctx, in)
	ret0, _ := ret[0].(*anchorcontract_go.HandlerCancelContractApplyResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// HandlerCancelContractApply indicates an expected call of HandlerCancelContractApply.
func (mr *MockIClientMockRecorder) HandlerCancelContractApply(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandlerCancelContractApply", reflect.TypeOf((*MockIClient)(nil).HandlerCancelContractApply), ctx, in)
}

// InvitePromote mocks base method.
func (m *MockIClient) InvitePromote(ctx context.Context, req *anchorcontract_go.InvitePromoteReq) (*anchorcontract_go.InvitePromoteResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InvitePromote", ctx, req)
	ret0, _ := ret[0].(*anchorcontract_go.InvitePromoteResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// InvitePromote indicates an expected call of InvitePromote.
func (mr *MockIClientMockRecorder) InvitePromote(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InvitePromote", reflect.TypeOf((*MockIClient)(nil).InvitePromote), ctx, req)
}

// LockCancelPayAmount mocks base method.
func (m *MockIClient) LockCancelPayAmount(ctx context.Context, req *anchorcontract_go.LockCancelPayAmountReq) (*anchorcontract_go.LockCancelPayAmountResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LockCancelPayAmount", ctx, req)
	ret0, _ := ret[0].(*anchorcontract_go.LockCancelPayAmountResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// LockCancelPayAmount indicates an expected call of LockCancelPayAmount.
func (mr *MockIClientMockRecorder) LockCancelPayAmount(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LockCancelPayAmount", reflect.TypeOf((*MockIClient)(nil).LockCancelPayAmount), ctx, req)
}

// ModifyWorkerType mocks base method.
func (m *MockIClient) ModifyWorkerType(ctx context.Context, req *anchorcontract_go.ModifyWorkerTypeReq) (*anchorcontract_go.ModifyWorkerTypeResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyWorkerType", ctx, req)
	ret0, _ := ret[0].(*anchorcontract_go.ModifyWorkerTypeResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ModifyWorkerType indicates an expected call of ModifyWorkerType.
func (mr *MockIClientMockRecorder) ModifyWorkerType(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyWorkerType", reflect.TypeOf((*MockIClient)(nil).ModifyWorkerType), ctx, req)
}

// OfficialHandleApplySign mocks base method.
func (m *MockIClient) OfficialHandleApplySign(ctx context.Context, uid uint32, in *anchorcontract_go.OfficialHandleApplySignReq) (*anchorcontract_go.OfficialHandleApplySignResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OfficialHandleApplySign", ctx, uid, in)
	ret0, _ := ret[0].(*anchorcontract_go.OfficialHandleApplySignResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// OfficialHandleApplySign indicates an expected call of OfficialHandleApplySign.
func (mr *MockIClientMockRecorder) OfficialHandleApplySign(ctx, uid, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OfficialHandleApplySign", reflect.TypeOf((*MockIClient)(nil).OfficialHandleApplySign), ctx, uid, in)
}

// OfficialHandleApplySignEsport mocks base method.
func (m *MockIClient) OfficialHandleApplySignEsport(ctx context.Context, in *anchorcontract_go.OfficialHandleApplySignEsportReq) (*anchorcontract_go.OfficialHandleApplySignEsportResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OfficialHandleApplySignEsport", ctx, in)
	ret0, _ := ret[0].(*anchorcontract_go.OfficialHandleApplySignEsportResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// OfficialHandleApplySignEsport indicates an expected call of OfficialHandleApplySignEsport.
func (mr *MockIClientMockRecorder) OfficialHandleApplySignEsport(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OfficialHandleApplySignEsport", reflect.TypeOf((*MockIClient)(nil).OfficialHandleApplySignEsport), ctx, in)
}

// PresidentHandleApplySign mocks base method.
func (m *MockIClient) PresidentHandleApplySign(ctx context.Context, in *anchorcontract_go.PresidentHandleApplySignReq) (*anchorcontract_go.PresidentHandleApplySignResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PresidentHandleApplySign", ctx, in)
	ret0, _ := ret[0].(*anchorcontract_go.PresidentHandleApplySignResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// PresidentHandleApplySign indicates an expected call of PresidentHandleApplySign.
func (mr *MockIClientMockRecorder) PresidentHandleApplySign(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PresidentHandleApplySign", reflect.TypeOf((*MockIClient)(nil).PresidentHandleApplySign), ctx, in)
}

// ProcPromoteInvite mocks base method.
func (m *MockIClient) ProcPromoteInvite(ctx context.Context, req *anchorcontract_go.ProcPromoteInviteReq) (*anchorcontract_go.ProcPromoteInviteResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcPromoteInvite", ctx, req)
	ret0, _ := ret[0].(*anchorcontract_go.ProcPromoteInviteResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ProcPromoteInvite indicates an expected call of ProcPromoteInvite.
func (mr *MockIClientMockRecorder) ProcPromoteInvite(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcPromoteInvite", reflect.TypeOf((*MockIClient)(nil).ProcPromoteInvite), ctx, req)
}

// ReclaimAnchorIdentity mocks base method.
func (m *MockIClient) ReclaimAnchorIdentity(ctx context.Context, uid uint32, in *anchorcontract_go.ReclaimAnchorIdentityReq) (*anchorcontract_go.ReclaimAnchorIdentityResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReclaimAnchorIdentity", ctx, uid, in)
	ret0, _ := ret[0].(*anchorcontract_go.ReclaimAnchorIdentityResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ReclaimAnchorIdentity indicates an expected call of ReclaimAnchorIdentity.
func (mr *MockIClientMockRecorder) ReclaimAnchorIdentity(ctx, uid, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReclaimAnchorIdentity", reflect.TypeOf((*MockIClient)(nil).ReclaimAnchorIdentity), ctx, uid, in)
}

// ReclaimGuildAllAnchorIdentity mocks base method.
func (m *MockIClient) ReclaimGuildAllAnchorIdentity(ctx context.Context, in *anchorcontract_go.ReclaimGuildAllAnchorIdentityReq) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReclaimGuildAllAnchorIdentity", ctx, in)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// ReclaimGuildAllAnchorIdentity indicates an expected call of ReclaimGuildAllAnchorIdentity.
func (mr *MockIClientMockRecorder) ReclaimGuildAllAnchorIdentity(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReclaimGuildAllAnchorIdentity", reflect.TypeOf((*MockIClient)(nil).ReclaimGuildAllAnchorIdentity), ctx, in)
}

// SetGuildCancelContractType mocks base method.
func (m *MockIClient) SetGuildCancelContractType(ctx context.Context, req *anchorcontract_go.SetGuildCancelContractTypeReq) (*anchorcontract_go.SetGuildCancelContractTypeResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetGuildCancelContractType", ctx, req)
	ret0, _ := ret[0].(*anchorcontract_go.SetGuildCancelContractTypeResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetGuildCancelContractType indicates an expected call of SetGuildCancelContractType.
func (mr *MockIClientMockRecorder) SetGuildCancelContractType(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetGuildCancelContractType", reflect.TypeOf((*MockIClient)(nil).SetGuildCancelContractType), ctx, req)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// UpdateGuildSignRight mocks base method.
func (m *MockIClient) UpdateGuildSignRight(ctx context.Context, req *anchorcontract_go.UpdateGuildSignRightReq) (*anchorcontract_go.UpdateGuildSignRightResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateGuildSignRight", ctx, req)
	ret0, _ := ret[0].(*anchorcontract_go.UpdateGuildSignRightResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpdateGuildSignRight indicates an expected call of UpdateGuildSignRight.
func (mr *MockIClientMockRecorder) UpdateGuildSignRight(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateGuildSignRight", reflect.TypeOf((*MockIClient)(nil).UpdateGuildSignRight), ctx, req)
}

// UpdateLiveAnchorExamineStatus mocks base method.
func (m *MockIClient) UpdateLiveAnchorExamineStatus(ctx context.Context, uid uint32, in *anchorcontract_go.UpdateLiveAnchorExamineStatusReq) (*anchorcontract_go.UpdateLiveAnchorExamineStatusResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateLiveAnchorExamineStatus", ctx, uid, in)
	ret0, _ := ret[0].(*anchorcontract_go.UpdateLiveAnchorExamineStatusResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpdateLiveAnchorExamineStatus indicates an expected call of UpdateLiveAnchorExamineStatus.
func (mr *MockIClientMockRecorder) UpdateLiveAnchorExamineStatus(ctx, uid, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLiveAnchorExamineStatus", reflect.TypeOf((*MockIClient)(nil).UpdateLiveAnchorExamineStatus), ctx, uid, in)
}

// UpdateLiveAnchorExamineTime mocks base method.
func (m *MockIClient) UpdateLiveAnchorExamineTime(ctx context.Context, uid uint32, in *anchorcontract_go.UpdateLiveAnchorExamineTimeReq) (*anchorcontract_go.UpdateLiveAnchorExamineTimeResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateLiveAnchorExamineTime", ctx, uid, in)
	ret0, _ := ret[0].(*anchorcontract_go.UpdateLiveAnchorExamineTimeResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpdateLiveAnchorExamineTime indicates an expected call of UpdateLiveAnchorExamineTime.
func (mr *MockIClientMockRecorder) UpdateLiveAnchorExamineTime(ctx, uid, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLiveAnchorExamineTime", reflect.TypeOf((*MockIClient)(nil).UpdateLiveAnchorExamineTime), ctx, uid, in)
}

// UpdateRadioLiveAnchorExamine mocks base method.
func (m *MockIClient) UpdateRadioLiveAnchorExamine(ctx context.Context, uid uint32) (*anchorcontract_go.UpdateRadioLiveAnchorExamineResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRadioLiveAnchorExamine", ctx, uid)
	ret0, _ := ret[0].(*anchorcontract_go.UpdateRadioLiveAnchorExamineResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateRadioLiveAnchorExamine indicates an expected call of UpdateRadioLiveAnchorExamine.
func (mr *MockIClientMockRecorder) UpdateRadioLiveAnchorExamine(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRadioLiveAnchorExamine", reflect.TypeOf((*MockIClient)(nil).UpdateRadioLiveAnchorExamine), ctx, uid)
}

// UpdateRemark mocks base method.
func (m *MockIClient) UpdateRemark(ctx context.Context, in *anchorcontract_go.UpdateRemarkReq) (*anchorcontract_go.UpdateRemarkResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRemark", ctx, in)
	ret0, _ := ret[0].(*anchorcontract_go.UpdateRemarkResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpdateRemark indicates an expected call of UpdateRemark.
func (mr *MockIClientMockRecorder) UpdateRemark(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRemark", reflect.TypeOf((*MockIClient)(nil).UpdateRemark), ctx, in)
}

// UpdateSignedAnchorAgentId mocks base method.
func (m *MockIClient) UpdateSignedAnchorAgentId(ctx context.Context, guildId, agentUid, identityType uint32, anchorList []uint32) (*anchorcontract_go.UpdateSignedAnchorAgentIdResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSignedAnchorAgentId", ctx, guildId, agentUid, identityType, anchorList)
	ret0, _ := ret[0].(*anchorcontract_go.UpdateSignedAnchorAgentIdResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpdateSignedAnchorAgentId indicates an expected call of UpdateSignedAnchorAgentId.
func (mr *MockIClientMockRecorder) UpdateSignedAnchorAgentId(ctx, guildId, agentUid, identityType, anchorList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSignedAnchorAgentId", reflect.TypeOf((*MockIClient)(nil).UpdateSignedAnchorAgentId), ctx, guildId, agentUid, identityType, anchorList)
}
