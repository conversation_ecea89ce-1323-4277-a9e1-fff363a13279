// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/clients/numeric-go (interfaces: IClient)

// Package numeric_go is a generated GoMock package.
package numeric_go

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	numeric_go "golang.52tt.com/protocol/services/numeric-go"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AddUserNumeric mocks base method.
func (m *MockIClient) AddUserNumeric(arg0 context.Context, arg1 *numeric_go.AddUserNumericReq) (*numeric_go.AddUserNumericResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUserNumeric", arg0, arg1)
	ret0, _ := ret[0].(*numeric_go.AddUserNumericResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddUserNumeric indicates an expected call of AddUserNumeric.
func (mr *MockIClientMockRecorder) AddUserNumeric(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserNumeric", reflect.TypeOf((*MockIClient)(nil).AddUserNumeric), arg0, arg1)
}

// BatchGetPersonalNumeric mocks base method.
func (m *MockIClient) BatchGetPersonalNumeric(arg0 context.Context, arg1 []uint32) (*numeric_go.BatchGetPersonalNumericResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetPersonalNumeric", arg0, arg1)
	ret0, _ := ret[0].(*numeric_go.BatchGetPersonalNumericResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetPersonalNumeric indicates an expected call of BatchGetPersonalNumeric.
func (mr *MockIClientMockRecorder) BatchGetPersonalNumeric(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetPersonalNumeric", reflect.TypeOf((*MockIClient)(nil).BatchGetPersonalNumeric), arg0, arg1)
}

// BatchRecordSendGiftEvent mocks base method.
func (m *MockIClient) BatchRecordSendGiftEvent(arg0 context.Context, arg1 *numeric_go.BatchRecordSendGiftEventReq) (*numeric_go.BatchRecordSendGiftEventResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchRecordSendGiftEvent", arg0, arg1)
	ret0, _ := ret[0].(*numeric_go.BatchRecordSendGiftEventResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchRecordSendGiftEvent indicates an expected call of BatchRecordSendGiftEvent.
func (mr *MockIClientMockRecorder) BatchRecordSendGiftEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchRecordSendGiftEvent", reflect.TypeOf((*MockIClient)(nil).BatchRecordSendGiftEvent), arg0, arg1)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetPersonalNumericV2 mocks base method.
func (m *MockIClient) GetPersonalNumericV2(arg0 context.Context, arg1 uint32) (*numeric_go.GetPersonalNumericV2Resp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPersonalNumericV2", arg0, arg1)
	ret0, _ := ret[0].(*numeric_go.GetPersonalNumericV2Resp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPersonalNumericV2 indicates an expected call of GetPersonalNumericV2.
func (mr *MockIClientMockRecorder) GetPersonalNumericV2(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPersonalNumericV2", reflect.TypeOf((*MockIClient)(nil).GetPersonalNumericV2), arg0, arg1)
}

// GetUserNumericLock mocks base method.
func (m *MockIClient) GetUserNumericLock(arg0 context.Context, arg1 uint32) (*numeric_go.GetUserNumericLockResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserNumericLock", arg0, arg1)
	ret0, _ := ret[0].(*numeric_go.GetUserNumericLockResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserNumericLock indicates an expected call of GetUserNumericLock.
func (mr *MockIClientMockRecorder) GetUserNumericLock(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserNumericLock", reflect.TypeOf((*MockIClient)(nil).GetUserNumericLock), arg0, arg1)
}

// GetUserRichSwitch mocks base method.
func (m *MockIClient) GetUserRichSwitch(arg0 context.Context, arg1 uint32) (*numeric_go.GetUserRichSwitchResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserRichSwitch", arg0, arg1)
	ret0, _ := ret[0].(*numeric_go.GetUserRichSwitchResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserRichSwitch indicates an expected call of GetUserRichSwitch.
func (mr *MockIClientMockRecorder) GetUserRichSwitch(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserRichSwitch", reflect.TypeOf((*MockIClient)(nil).GetUserRichSwitch), arg0, arg1)
}

// RecordSendGiftEvent mocks base method.
func (m *MockIClient) RecordSendGiftEvent(arg0 context.Context, arg1 *numeric_go.RecordSendGiftEventReq) (*numeric_go.RecordSendGiftEventResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordSendGiftEvent", arg0, arg1)
	ret0, _ := ret[0].(*numeric_go.RecordSendGiftEventResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// RecordSendGiftEvent indicates an expected call of RecordSendGiftEvent.
func (mr *MockIClientMockRecorder) RecordSendGiftEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordSendGiftEvent", reflect.TypeOf((*MockIClient)(nil).RecordSendGiftEvent), arg0, arg1)
}

// SetUserNumericLock mocks base method.
func (m *MockIClient) SetUserNumericLock(arg0 context.Context, arg1 *numeric_go.SetUserNumericLockReq) (*numeric_go.SetUserNumericLockResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserNumericLock", arg0, arg1)
	ret0, _ := ret[0].(*numeric_go.SetUserNumericLockResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetUserNumericLock indicates an expected call of SetUserNumericLock.
func (mr *MockIClientMockRecorder) SetUserNumericLock(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserNumericLock", reflect.TypeOf((*MockIClient)(nil).SetUserNumericLock), arg0, arg1)
}

// SetUserRichSwitch mocks base method.
func (m *MockIClient) SetUserRichSwitch(arg0 context.Context, arg1 uint32, arg2 bool) (*numeric_go.SetUserRichSwitchResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserRichSwitch", arg0, arg1, arg2)
	ret0, _ := ret[0].(*numeric_go.SetUserRichSwitchResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetUserRichSwitch indicates an expected call of SetUserRichSwitch.
func (mr *MockIClientMockRecorder) SetUserRichSwitch(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserRichSwitch", reflect.TypeOf((*MockIClient)(nil).SetUserRichSwitch), arg0, arg1, arg2)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// UseRichCard mocks base method.
func (m *MockIClient) UseRichCard(arg0 context.Context, arg1 *numeric_go.UseRichCardReq) (*numeric_go.UseRichCardResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UseRichCard", arg0, arg1)
	ret0, _ := ret[0].(*numeric_go.UseRichCardResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UseRichCard indicates an expected call of UseRichCard.
func (mr *MockIClientMockRecorder) UseRichCard(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UseRichCard", reflect.TypeOf((*MockIClient)(nil).UseRichCard), arg0, arg1)
}
