// Code generated by quicksilver-cli. DO NOT EDIT.
package anchorcontract_go

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	PB "golang.52tt.com/protocol/services/anchorcontract-go"
	context "context"
	protocol "golang.52tt.com/pkg/protocol"
)

type IClient interface {
	client.BaseClient
	AddSignWhiteUid(ctx context.Context, req *PB.AddSignWhiteUidReq) protocol.ServerError
	ApplyCancelContractNew(ctx context.Context, req *PB.ApplyCancelContractNewReq) protocol.ServerError
	ApplySignContract(ctx context.Context, req *PB.ApplySignContractReq) protocol.ServerError
	ApplySignDoyen(ctx context.Context, req *PB.ApplySignDoyenReq) protocol.ServerError
	ApplySignEsport(ctx context.Context, in *PB.ApplySignEsportReq) (*PB.ApplySignEsportResp,protocol.ServerError)
	BatchGetAnchorIdentity(ctx context.Context, uid uint32, in *PB.BatchGetAnchorIdentityReq) (*PB.BatchGetAnchorIdentityResp,protocol.ServerError)
	BatchGetApplyBlacklist(ctx context.Context, uid uint32, in *PB.BatchGetApplyBlacklistReq) (*PB.BatchGetApplyBlacklistResp,protocol.ServerError)
	BatchGetContractInfo(ctx context.Context, in *PB.BatchGetContractInfoReq) (*PB.BatchGetContractInfoResp,protocol.ServerError)
	BatchGetLiveAnchorCert(ctx context.Context, uids []uint32) (map[uint32]*PB.BatchGetLiveAnchorCertResp_LiveAnchorCertInfo,protocol.ServerError)
	BatchGetUserAnchorIdentityLog(ctx context.Context, uid uint32, in *PB.BatchGetUserAnchorIdentityLogReq) (*PB.BatchGetUserAnchorIdentityLogResp,protocol.ServerError)
	BatchGetUserApplySignRecord(ctx context.Context, uid uint32, in *PB.BatchGetUserApplySignRecordReq) (*PB.BatchGetUserApplySignRecordResp,protocol.ServerError)
	BatchGetUserContract(ctx context.Context, uid uint32, in *PB.BatchGetUserContractReq) (*PB.BatchGetUserContractResp,protocol.ServerError)
	BatchGetUserContractCacheInfo(ctx context.Context, in *PB.BatchGetUserContractCacheInfoReq) (*PB.BatchGetUserContractCacheInfoResp,protocol.ServerError)
	BatchGetUserExamineCert(ctx context.Context, uids []uint32) (*PB.BatchGetUserExamineCertResp,protocol.ServerError)
	BatchGetUserLiveAnchorExamine(ctx context.Context, uid uint32, in *PB.BatchGetUserLiveAnchorExamineReq) (*PB.BatchGetUserLiveAnchorExamineResp,protocol.ServerError)
	BatchGuildExtensionContract(ctx context.Context, in *PB.BatchGuildExtensionContractReq) (*PB.BatchGuildExtensionContractResp,protocol.ServerError)
	CancelContractByUid(ctx context.Context, in *PB.CancelContractByUidReq) (*PB.CancelContractByUidResp,protocol.ServerError)
	CensorVideo(ctx context.Context, req *PB.CensorVideoReq) (*PB.CensorVideoResp,protocol.ServerError)
	CheckCanApplyCancelContractV2(ctx context.Context, req *PB.CheckCanApplyCancelContractV2Req) (*PB.CheckCanApplyCancelContractV2Resp,protocol.ServerError)
	CheckCanApplySign(ctx context.Context, req *PB.CheckCanApplySignReq) protocol.ServerError
	CheckIfGreatLiveAnchor(ctx context.Context, req *PB.CheckIfGreatLiveAnchorReq) (*PB.CheckIfGreatLiveAnchorResp,protocol.ServerError)
	CheckIsSignWhiteUid(ctx context.Context, req *PB.CheckIsSignWhiteUidReq) (*PB.CheckIsSignWhiteUidResp,protocol.ServerError)
	CheckUserGreatLiveAnchor(ctx context.Context, req *PB.CheckUserGreatLiveAnchorReq) (*PB.CheckUserGreatLiveAnchorResp,protocol.ServerError)
	ContractClaimObsToken(ctx context.Context, req *PB.ContractClaimObsTokenReq) (*PB.ContractClaimObsTokenResp,protocol.ServerError)
	DelSignWhiteUid(ctx context.Context, req *PB.DelSignWhiteUidReq) protocol.ServerError
	GetAllApplyBlacklist(ctx context.Context, uid uint32, in *PB.GetAllApplyBlacklistReq) (*PB.GetAllApplyBlacklistResp,protocol.ServerError)
	GetAllApplySignRecord(ctx context.Context, uid uint32, in *PB.GetAllApplySignRecordReq) (*PB.GetAllApplySignRecordResp,protocol.ServerError)
	GetAllLiveAnchorExamine(ctx context.Context, uid uint32, in *PB.GetAllLiveAnchorExamineReq) (*PB.GetAllLiveAnchorExamineResp,protocol.ServerError)
	GetAnchorAgentUid(ctx context.Context, uids []uint32) (*PB.GetAnchorAgentUidResp,error)
	GetAnchorCertListByItemId(ctx context.Context, in *PB.GetAnchorCertListByItemIdReq) (*PB.GetAnchorCertListByItemIdResp,protocol.ServerError)
	GetAnchorCertTaskInfo(ctx context.Context, uid uint32) (*PB.GetAnchorCertTaskInfoResp,protocol.ServerError)
	GetCancelContractApplyList(ctx context.Context, req *PB.GetCancelContractApplyListReq) (*PB.GetCancelContractApplyListResp,protocol.ServerError)
	GetCancelContractTypeList(ctx context.Context, req *PB.GetCancelContractTypeListReq) (*PB.GetCancelContractTypeListResp,protocol.ServerError)
	GetCancelPayAmount(ctx context.Context, req *PB.GetCancelPayAmountReq) (*PB.GetCancelPayAmountResp,protocol.ServerError)
	GetContract(ctx context.Context, in *PB.GetContractReq) (*PB.GetContractResp,protocol.ServerError)
	GetContractWorkerConfigs(ctx context.Context, req *PB.GetContractWorkerConfigsReq) (*PB.GetContractWorkerConfigsResp,protocol.ServerError)
	GetGuildAnchorExtInfoList(ctx context.Context, in *PB.GetGuildAnchorExtInfoListReq) (*PB.GetGuildAnchorExtInfoListResp,protocol.ServerError)
	GetGuildAnchorIdentity(ctx context.Context, uid uint32, in *PB.GetGuildAnchorIdentityReq) (*PB.GetGuildAnchorIdentityResp,protocol.ServerError)
	GetGuildAnchorIdentityLog(ctx context.Context, uid uint32, in *PB.GetGuildAnchorIdentityLogReq) (*PB.GetGuildAnchorIdentityLogResp,protocol.ServerError)
	GetGuildApplySignRecord(ctx context.Context, uid uint32, in *PB.GetGuildApplySignRecordReq) (*PB.GetGuildApplySignRecordResp,protocol.ServerError)
	GetGuildApplySignRecordCnt(ctx context.Context, uid uint32, in *PB.GetGuildApplySignRecordCntReq) (*PB.GetGuildApplySignRecordCntResp,protocol.ServerError)
	GetGuildApplySignRecordList(ctx context.Context, in *PB.GetGuildApplySignRecordListReq) (*PB.GetGuildApplySignRecordListResp,protocol.ServerError)
	GetGuildCancelSignRecordList(ctx context.Context, in *PB.GetGuildCancelSignRecordListReq) (*PB.GetGuildCancelSignRecordListResp,protocol.ServerError)
	GetGuildContract(ctx context.Context, guildId, page, pageSize uint32) (*PB.GetGuildContractResp,protocol.ServerError)
	GetGuildContractByCond(ctx context.Context, req *PB.GetGuildContractByCondReq) (*PB.GetGuildContractByCondResp,protocol.ServerError)
	GetGuildContractByIdentity(ctx context.Context, in *PB.GetGuildContractByIdentityReq) (*PB.GetGuildContractByIdentityResp,protocol.ServerError)
	GetGuildContractSum(ctx context.Context, guildId uint32) (*PB.GetGuildContractSumResp,protocol.ServerError)
	GetGuildLiveAnchorExamine(ctx context.Context, uid uint32, in *PB.GetGuildLiveAnchorExamineReq) (*PB.GetGuildLiveAnchorExamineResp,protocol.ServerError)
	GetGuildSignRight(ctx context.Context, req *PB.GetGuildSignRightReq) (*PB.GetGuildSignRightResp,protocol.ServerError)
	GetIdentityChangeHistory(ctx context.Context, in *PB.GetIdentityChangeHistoryReq) (*PB.GetIdentityChangeHistoryResp,protocol.ServerError)
	GetMultiPlayerCenterEntry(ctx context.Context, uid uint32) (string,protocol.ServerError)
	GetNeedConfirmWorkerType(ctx context.Context, req *PB.GetNeedConfirmWorkerTypeReq) (*PB.GetNeedConfirmWorkerTypeResp,protocol.ServerError)
	GetRadioLiveAnchorExamine(ctx context.Context, uid uint32) (uint32,error)
	GetRecommendTopGuildList(ctx context.Context, req *PB.GetRecommendTopGuildListReq) (*PB.GetRecommendTopGuildListResp,protocol.ServerError)
	GetSignEsportAuditToken(ctx context.Context, applyId uint32) (string,error)
	GetUserAnchorIdentityLog(ctx context.Context, uid uint32, in *PB.GetUserAnchorIdentityLogReq) (*PB.GetUserAnchorIdentityLogResp,protocol.ServerError)
	GetUserApplySignRecord(ctx context.Context, uid uint32, in *PB.GetUserApplySignRecordReq) (*PB.GetUserApplySignRecordResp,protocol.ServerError)
	GetUserContract(ctx context.Context, uin, targetUid uint32) (*PB.GetUserContractResp,protocol.ServerError)
	GetUserContractCacheInfo(ctx context.Context, uin, targetUid uint32) (*PB.ContractCacheInfo,protocol.ServerError)
	GetUserExamineCert(ctx context.Context, uid uint32) (*PB.GetUserExamineCertResp,protocol.ServerError)
	GetUserPromoteInviteInfo(ctx context.Context, req *PB.GetUserPromoteInviteInfoReq) (*PB.GetUserPromoteInviteInfoResp,protocol.ServerError)
	GuildExtensionContract(ctx context.Context, in *PB.GuildExtensionContractReq) (*PB.GuildExtensionContractResp,protocol.ServerError)
	HandleApplyBlackInfo(ctx context.Context, uid uint32, in *PB.HandleApplyBlackInfoReq) (*PB.HandleApplyBlackInfoResp,protocol.ServerError)
	HandleFocusAnchor(ctx context.Context, in *PB.HandleFocusAnchorReq) (*PB.HandleFocusAnchorResp,protocol.ServerError)
	HandlerCancelContractApply(ctx context.Context, in *PB.HandlerCancelContractApplyReq) (*PB.HandlerCancelContractApplyResp,protocol.ServerError)
	InvitePromote(ctx context.Context, req *PB.InvitePromoteReq) (*PB.InvitePromoteResp,protocol.ServerError)
	LockCancelPayAmount(ctx context.Context, req *PB.LockCancelPayAmountReq) (*PB.LockCancelPayAmountResp,protocol.ServerError)
	ModifyWorkerType(ctx context.Context, req *PB.ModifyWorkerTypeReq) (*PB.ModifyWorkerTypeResp,protocol.ServerError)
	OfficialHandleApplySign(ctx context.Context, uid uint32, in *PB.OfficialHandleApplySignReq) (*PB.OfficialHandleApplySignResp,protocol.ServerError)
	OfficialHandleApplySignEsport(ctx context.Context, in *PB.OfficialHandleApplySignEsportReq) (*PB.OfficialHandleApplySignEsportResp,protocol.ServerError)
	PresidentHandleApplySign(ctx context.Context, in *PB.PresidentHandleApplySignReq) (*PB.PresidentHandleApplySignResp,protocol.ServerError)
	ProcPromoteInvite(ctx context.Context, req *PB.ProcPromoteInviteReq) (*PB.ProcPromoteInviteResp,protocol.ServerError)
	ReclaimAnchorIdentity(ctx context.Context, uid uint32, in *PB.ReclaimAnchorIdentityReq) (*PB.ReclaimAnchorIdentityResp,protocol.ServerError)
	ReclaimGuildAllAnchorIdentity(ctx context.Context, in *PB.ReclaimGuildAllAnchorIdentityReq) protocol.ServerError
	SetGuildCancelContractType(ctx context.Context, req *PB.SetGuildCancelContractTypeReq) (*PB.SetGuildCancelContractTypeResp,protocol.ServerError)
	UpdateGuildSignRight(ctx context.Context, req *PB.UpdateGuildSignRightReq) (*PB.UpdateGuildSignRightResp,protocol.ServerError)
	UpdateLiveAnchorExamineStatus(ctx context.Context, uid uint32, in *PB.UpdateLiveAnchorExamineStatusReq) (*PB.UpdateLiveAnchorExamineStatusResp,protocol.ServerError)
	UpdateLiveAnchorExamineTime(ctx context.Context, uid uint32, in *PB.UpdateLiveAnchorExamineTimeReq) (*PB.UpdateLiveAnchorExamineTimeResp,protocol.ServerError)
	UpdateRadioLiveAnchorExamine(ctx context.Context, uid uint32) (*PB.UpdateRadioLiveAnchorExamineResp,error)
	UpdateRemark(ctx context.Context, in *PB.UpdateRemarkReq) (*PB.UpdateRemarkResp,protocol.ServerError)
	UpdateSignedAnchorAgentId(ctx context.Context, guildId, agentUid, identityType uint32, anchorList []uint32) (*PB.UpdateSignedAnchorAgentIdResp,protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
