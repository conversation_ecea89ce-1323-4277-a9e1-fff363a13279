package numeric_go

import (
	"context"
	"golang.52tt.com/pkg/log"

	"github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/numeric-go"
	"google.golang.org/grpc"
)

const (
	serviceName = "numeric-go"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewNumericGoClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.NumericGoClient { return c.Stub().(pb.NumericGoClient) }

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetUserRichSwitch(ctx context.Context, uid uint32) (*pb.GetUserRichSwitchResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserRichSwitch(ctx, &pb.GetUserRichSwitchReq{Uid: uid})
	log.Debugf("GetUserRichSwitch uid:%d, resp:%+v", uid, resp)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetUserRichSwitch(ctx context.Context, uid uint32, enable bool) (*pb.SetUserRichSwitchResp, protocol.ServerError) {
	resp, err := c.typedStub().SetUserRichSwitch(ctx, &pb.SetUserRichSwitchReq{Uid: uid, Enable: enable})
	log.Debugf("SetUserRichSwitch uid:%d, enable:%+v, resp:%+v", uid, enable, resp)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetPersonalNumericV2(ctx context.Context, uid uint32) (*pb.GetPersonalNumericV2Resp, protocol.ServerError) {
	resp, err := c.typedStub().GetPersonalNumericV2(ctx, &pb.GetPersonalNumericV2Req{
		Uid: uid,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetPersonalNumeric(ctx context.Context, uidList []uint32) (*pb.BatchGetPersonalNumericResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchGetPersonalNumeric(ctx, &pb.BatchGetPersonalNumericReq{
		UidList: uidList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetPersonalNumeric uidList:%+v, err:%s", uidList, err)
		return nil, protocol.ToServerError(err)
	}
	return resp, protocol.ToServerError(err)
}

func (c *Client) AddUserNumeric(ctx context.Context, req *pb.AddUserNumericReq) (*pb.AddUserNumericResp, protocol.ServerError) {
	resp, err := c.typedStub().AddUserNumeric(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddUserNumeric req:%+v, err:%s", req, err)
		return nil, protocol.ToServerError(err)
	}
	return resp, protocol.ToServerError(err)
}

func (c *Client) RecordSendGiftEvent(ctx context.Context, req *pb.RecordSendGiftEventReq) (*pb.RecordSendGiftEventResp, protocol.ServerError) {
	resp, err := c.typedStub().RecordSendGiftEvent(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "RecordSendGiftEvent req:%+v, err:%s", req, err)
		return nil, protocol.ToServerError(err)
	}
	log.DebugWithCtx(ctx, "RecordSendGiftEvent req:%+v, resp:%+v", req, resp)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchRecordSendGiftEvent(ctx context.Context, req *pb.BatchRecordSendGiftEventReq) (*pb.BatchRecordSendGiftEventResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchRecordSendGiftEvent(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchRecordSendGiftEvent req:%+v, err:%s", req, err)
		return nil, protocol.ToServerError(err)
	}
	log.DebugWithCtx(ctx, "BatchRecordSendGiftEvent req:%+v, resp:%+v", req, resp)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UseRichCard(ctx context.Context, req *pb.UseRichCardReq) (*pb.UseRichCardResp, protocol.ServerError) {
	resp, err := c.typedStub().UseRichCard(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "UseRichCard req:%+v, err:%s", req, err)
		return nil, protocol.ToServerError(err)
	}
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserNumericLock(ctx context.Context, uid uint32) (*pb.GetUserNumericLockResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserNumericLock(ctx, &pb.GetUserNumericLockReq{Uid: uid})
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetUserNumericLock(ctx context.Context, req *pb.SetUserNumericLockReq) (*pb.SetUserNumericLockResp, protocol.ServerError) {
	resp, err := c.typedStub().SetUserNumericLock(ctx, req)
	return resp, protocol.ToServerError(err)
}
