#define CATCH_CONFIG_MAIN

#include <test/catch.hpp>
#include <cstdint>
#include <vector>
#include <memory>
#include <iostream>

#include "iLog.h"

#include "interactive/cpp/client.h"

using std::cout;
using std::endl;

using namespace ugc::interactive;

static std::shared_ptr<Client> client;


TEST_CASE("setup") {
    Comm::OpenLog ( "interactive-client-test", 16, "/home/<USER>/log");

    client = std::make_shared<Client>();
}

TEST_CASE("get-interactive-uinfo") {
    ugc::interactive::Uinfo info;
    int ret = client->GetInteractiveUinfo(500001, &info);
    REQUIRE(ret == 0);
    cout << "GetInteractiveUinfo 500001->" << info.ShortDebugString().c_str() << endl;
}