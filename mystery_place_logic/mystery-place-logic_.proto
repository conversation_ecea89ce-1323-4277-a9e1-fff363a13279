syntax = "proto3";

package ga.mystery_place_logic;

import "ga_base.proto";
import "mystery_place_logic/mystery_place_view.proto";
import "topic_channel/topic_channel_.proto";
import "mijing_label_logic/mijing_label_logic.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/mystery-place-logic";

message ScenarioInfo{
  uint32 id = 1;
  enum PlayMode{
    None = 0;//未设置
    Single = 1;//单人
    Multi = 2;//多人
  }
  PlayMode play_mode = 2;
  uint32 tab_id = 3;
  string tab_name = 4;
  string link = 5;
  string introduction = 6;
  repeated string labels = 7;
  string new_display_picture = 8;
  string old_display_picture = 9;
  string big_share_picture = 10;
  string small_share_picture = 11;
  string video = 12;
  string title = 13;
  string labels_color = 14;
  string rate = 15; //进度

  repeated uint32 tab_ids = 16;
  string scenario_small_picture = 17; // 剧本小图
  string new_scenario_small_picture = 18;
  repeated CharacterInfo character_info = 19; // 角色介绍
  float score = 20; //评分
  string recommend_video = 21;// 头部视频
  repeated string recommend_picture = 22;// 头部图片
  uint32 min_player_num = 23; // 开场人数
  string game_difficulty = 24; // 游戏难度
  string estimate_finish_time = 25; // 预计通关时间
  repeated string bind_topic_ids = 26; // 绑定话题
  GameOrientation game_orientation = 27; // 显示方向
  ScenarioDevInfo dev_info = 28; // 开发者信息
}

enum GameOrientation {
  GAME_ORIENTATION_UNDEFINED = 0;
  GAME_ORIENTATION_HORIZONTAL = 1; // 横
  GAME_ORIENTATION_VERTICAL = 2; // 竖
}

//获取列表接口
message ListScenarioInfoReq{
  ga.BaseReq base_req = 1;
}

//详情页面
message ListScenarioInfoResp{
  ga.BaseResp base_resp = 1;
  repeated ScenarioInfo infos = 2;
  bool old_user = 3;
  uint32 voucher_balance = 4;  //消费券余额
  string last_record_text = 5;  // 历史文案
  ScenarioInfo last_playing_scenario = 6; // 上次玩的剧本
}

message GetScenarioInfoReq {
  ga.BaseReq base_req = 1;
  uint32 id = 2;
}

message GetScenarioInfoResp {
  ga.BaseResp base_resp = 1;
  ScenarioInfo info = 2;
  bool old_user = 3;
}

// 登录任务
message LoginTask {
  enum State {
    StateNone = 0;
    StateUnreceived = 1; // 未领取
    StateReceived = 2; // 已领取
    StateEnded = 3; // 已结束
  }

  // 任务标题
  string title = 1;
  // 任务状态
  State state = 2;
  // 任务描述
  string desc = 3;
  // 任务背景图
  string background = 4;
  // 弹窗 ICON 图
  string popup_icon = 5;
}

message GetLoginTaskReq {
  ga.BaseReq base_req = 1;
}

message GetLoginTaskResp {
  ga.BaseResp base_resp = 1;
  LoginTask login_task = 2;
}

message ReceiveLoginTaskAwardReq {
  ga.BaseReq base_req = 1;
}

message ReceiveLoginTaskAwardResp {
  ga.BaseResp base_resp = 1;
}

message GetBalanceInfoReq {
  ga.BaseReq base_req = 1;
}

message GetBalanceInfoResp {
  ga.BaseResp base_resp = 1;
  // 消费券余额
  uint32 voucher_balance = 2;
  // 登录任务信息
  LoginTask login_task = 3;
}

message ShareScenarioMsg{
  ScenarioInfo  scenario_info = 1;
}

//密室逃脱房，组队状态
enum MysteryTeamStatus {
  STATUS_NONE = 0; //出错了
  OWNER_LEAVE = 1; //房主不在房，即房间没有人
  TAB_CHANGE = 2; //当前房间切换玩法
  SEAT_AVAILABLE = 3; //当前房间有空位
  SEAT_UNAVAILABLE = 4; //当前房间无空位
  CHANNEL_LOCKED = 5; //房间已上锁
}

//密室逃脱房，邀请进房结果
enum MysteryInviteResult {
  NOT_FOUND = 0; //未找到邀请记录
  NOT_RESPOND = 1; //未响应
  ACCEPT = 2; //接受
  REJECT = 3; //拒绝
}

message CheckScenarioRoomReq {
  ga.BaseReq base_req = 1;
  uint32 to_channel = 2; //要去的房间
  uint32 to_channel_owner_uid = 3; //要去的房间的房主uid
  //做校验，防止房间已经切换玩法了。首页传剧本下所有tabid，房间内传当前tabid
  repeated uint32 tab_list = 4;
}

message CheckScenarioRoomResp {
  ga.BaseResp base_resp = 1;
  MysteryTeamStatus team_status = 2;
}

message Invite2MyRoomReq {
  ga.BaseReq base_req = 1;
  uint32 to_channel = 2; //要去的房间，也就是我自己的房间
  uint32 tab_id = 3; //做校验，防止房间已经切换玩法了
  uint32 invite_uid = 4; //受邀人
  uint32 invite_channel = 5; //受邀人所在房间
}

message Invite2MyRoomResp {
  ga.BaseResp base_resp = 1;
  MysteryTeamStatus team_status = 2;
  string invite_id = 3; //邀请id，用于后续查询
}

message GetInvite2MyRoomResultReq {
  ga.BaseReq base_req = 1;
  string invite_id = 2; //邀请id
}

message GetInvite2MyRoomResultResp {
  ga.BaseResp base_resp = 1;
  MysteryInviteResult invite_result = 2; //邀请结果
}

//邀请别人来我的密逃房，结果通知
message Invite2MyRoomResultPush {
  uint32 invite_uid = 1; //受邀人
  uint32 to_channel = 2; //去哪个房间
  MysteryInviteResult invite_result = 3; //邀请结果
}

enum ScenarioTagType{
  ScenarioTagType_UNDEFINED = 0; // 未定义
  ScenarioTagType_SUBSCRIBE = 1; // 预约
  ScenarioTagType_NEW = 2; // New
  ScenarioTagType_HOT = 3; // Hot
}

message ScenarioSimpleInfo{
  uint32 id = 1;
  string title = 2;
  string scenario_small_picture = 3;
  string first_label = 4;
  string labels_color = 5;
  ScenarioTagType tag_type = 6; // 标签类型
}

message RecommendedScenarioDetailInfo{
  uint32 id = 1;
  string title = 2;
  string scenario_small_picture = 3;
  repeated ScenarioTabInfo tab_infos = 4;
  repeated string barrage = 5;
  string recommend_video = 6;
  repeated string recommend_picture = 7;
  string introduction = 8;
  string estimate_finish_time = 9;
  repeated string labels = 10;
  string labels_color = 11;
  uint32 first_tab_id = 12; //首关tab
  uint32 current_playing_room_count = 13; //当前剧本在玩房间数
  bool is_reservation = 14; //是否为预约本

  float score = 15; //评分
  uint32 min_player_num = 16; // 开场人数
  string game_difficulty = 17; // 游戏难度
  string start_btn = 18; // 开始文案
  string find_playmate_btn = 19; // 找人玩文案
  string role_picture = 20; // 人物图片
  string new_recommend_picture = 21; // 首页图
  string invite_playmate_scenario_small_picture = 22; // AB测：底部邀请好友玩剧本略缩图

  repeated CharacterInfo character_info = 23; // 角色介绍
  bool is_show_price = 38; // 价格显示开关
  string price_num = 39 [deprecated = true]; // 剧本价格 用42那个
  bool is_trial = 40;// 剧本是否限免（剧本价格为0）
  repeated string bind_topic_ids = 41; // 绑定话题
  uint32 price_num_int = 42; // 剧本价格
  uint32 rank = 43; // 剧本排名
  string rank_url = 44; // 剧本排名页面链接
  string rank_icon = 45; // 剧本排名图标
  GameOrientation game_orientation = 46; // 显示方向
  InvitationActivityInfo invitation_activity_info = 47; // 邀请玩本配置
  bool is_super_scenario = 48;//是否精品本
  bool is_subscribed = 49; // 是否已预约
  ScenarioDevInfo dev_info = 50; // 开发者信息
  bool is_rookie = 51;// 是否新手必玩
  repeated mijing_label_logic.ScenarioContentLabel scenario_content_label_list = 52;  // 剧本内容标签列表
}

enum ScenarioTabPlayMode{
  None = 0;//未设置
  Single = 1;//单人
  Multi = 2;//多人
}

enum ScenarioTabStatus{
  ScenarioTabStatus_UNDEFINED = 0;
  ScenarioTabStatus_NEW = 1;
  ScenarioTabStatus_PALYED = 2;
}

message ScenarioTabInfo{
  ScenarioTabPlayMode play_mode = 2;
  uint32 tab_id = 3;
  string icon = 4; // 主题图片
  ScenarioTabStatus tab_status = 5; // 玩法状态
  string tab_name = 6; // 玩法名称
}

// 角色介绍
message CharacterInfo {
  string name = 1;
  string img = 2;
  string info = 3;
}

enum RecommendedScenarioType{
  RecommendedScenarioType_UNDEFINED = 0;
  RecommendedScenarioType_ALL = 1; // 全部
  RecommendedScenarioType_NEW = 2; // 最新上架，不等同ScenarioTagType_NEW
  RecommendedScenarioType_SUBSCRIBE = 3; // 预约
  RecommendedScenarioType_PLAYMOD_SINGLE = 4; // 单人本
}
//获取推荐剧本列表缩略信息接口
message ListRecommendedScenarioSimpleInfoReq{
  ga.BaseReq base_req = 1;
  RecommendedScenarioType type = 2;
  uint32 count = 3; // 分页数量，不穿默认返回全部，兼容旧版
  ListRecommendedScenarioSimpleInfoLoadMore load_more =4; // 客户端回传就行了
}
message ListRecommendedScenarioSimpleInfoResp{
  ga.BaseResp base_resp = 1;
  repeated ScenarioSimpleInfo infos = 2;
  ListRecommendedScenarioSimpleInfoLoadMore load_more =3; // 客户端回传就行了
}

message ListRecommendedScenarioSimpleInfoLoadMore {
  uint32 last_id = 1; // 列表最后的ID
  RecommendedScenarioType type = 2; // 分类类型
}

//获取推荐剧本列表详情信息接口
message ListRecommendedScenarioDetailInfoLoadMore {
  uint32 last_id = 1; // 列表最后的ID
  RecommendedScenarioType type = 2; // 分类类型
}

//获取推荐剧本列表详情信息接口
message ListRecommendedScenarioDetailInfoReq{
  ga.BaseReq base_req = 1;
  RecommendedScenarioType type = 2;
  uint32 count = 3; // 分页数量，不穿默认返回全部，兼容旧版
  ListRecommendedScenarioDetailInfoLoadMore load_more =4; // 客户端回传就行了
  repeated RecommendedScenarioType exclude_types = 5; // 排除类型
  string scenario_content_label_id = 6;// 剧本内容标签ID
  string client_channel_id = 7; // 渠道号
}
message ListRecommendedScenarioDetailInfoResp{
  ga.BaseResp base_resp = 1;
  repeated RecommendedScenarioDetailInfo infos = 2;
  ListRecommendedScenarioDetailInfoLoadMore load_more =3; // 客户端回传就行了
}

message ScenarioDevInfo {
  string dev_name = 1; // 开发者名称
  string dev_avatar = 2; // 开发者头像
  string dev_greetings = 3; // 开发者问候语
}

//获取推荐剧本列表缩略信息接口
message GetRecommendedScenarioDetailInfoReq{
  ga.BaseReq base_req = 1;
  repeated uint32 scenario_id = 2;
}
message GetRecommendedScenarioDetailInfoResp{
  ga.BaseResp base_resp = 1;
  repeated RecommendedScenarioDetailInfo infos = 2;
}

message GetRoomShareLinkByTabIdReq{
  ga.BaseReq base_req = 1;
  uint32 tab_id = 2;
}

message GetRoomShareLinkByTabIdResp{
  ga.BaseResp base_resp = 1;
  string link = 2;
}

message MysteryPlaceChannelListReq {
  ga.BaseReq base_req = 1;
  repeated uint32 tab_ids = 2;
  repeated uint32 category_ids = 3; //分类id，用于全选小游戏 为了兼容以后万一有多选分类的，就设置成数组
  //  repeated BlockOption block_option = 4; //用户所选筛选信息, eg: [<block_id:1, elem_id:100>,<block_id:1, elem_id:101>,<block_id:2, elem_id:200>]

  enum Sex{
    UNDEFINED = 0;
    FEMALE = 1;
    MALE = 2;
  }

  Sex sex = 5; //性别过滤 0-不过滤 1-选择Female 2-选择Male

  uint32 get_mode = 6; //1代表下一页，2代表刷新。替换了旧版本的loadmore

  string channel_package_id = 7;  //渠道包id

  repeated uint32 no_browse_list = 8; //请求列表曝光channelid列表

  uint32 enter_source = 9; //  see ga::ChannelEnterReq::EChannelEnterSource 来源字段

  /*
    //谜境筛选字段
    message MysteryPlaceBlockOption{
      //筛选表头
      uint32 block_type = 1;  //see MysteryPlaceBlockEnum
      //筛选项
      repeated uint32 elem_type = 2; //see MysteryPlaceElemEnum
    }
    repeated MysteryPlaceBlockOption mystery_place_option = 10;
  */

  repeated MysteryPlaceBusinessBlockOption mystery_place_option = 10;

  uint32 count = 11;

  repeated uint32 scenario_ids = 12;
}

message MysteryPlaceBusinessBlockOption {
  //筛选表头
  uint32 business_block_id = 1;  //see MysteryPlaceBlockEnum
  //筛选项
  uint32 business_elem_id = 2; //see MysteryPlaceElemEnum
}

//筛选表头
enum MysteryPlaceBlockEnum {
  RoomMode = 0; //房间模式
  GameCondition = 1; //对局状态
}
//筛选项
enum MysteryPlaceElemEnum {
  NoLimit = 0; //不限
  MysteryPlaceRoomModeSingle = 1;  //单人
  MysteryPlaceRoomModeDouble = 2;  //双人
  Waiting = 3; //等待中
  Started = 4; // 开局中
}

message MysteryPlaceChannelListResp {
  ga.BaseResp base_resp = 1;
  repeated MysteryPlaceChannelItem items = 2;
  bool load_finish = 3;                        //返回true表示没有下一页了
  //仅用于客户端数据上报字段
  message DataReport{
    string footprint = 1; //推荐trace id
  }
  DataReport report_data = 4;
}

message MysteryPlaceChannelItem{
  uint32 channel_id = 1; //房间Id
  string channel_name = 2; //房间名

  mystery_place_logic.ChannelItemUserInfo owner = 3;

  MysteryPlaceChannelView view = 4;

  uint32 tab_id = 5;
  string tab_name = 6;

  uint32 member_count = 7; //人数

  MysteryPlaceRCMDLabelEnum rcmd_label = 8; //推荐行为标签
  string geo_info = 9; // 地理位置信息

  uint32 level_id = 10;      //房间等级id  大概率是没用的了，先不返回具体值，目前只保留字段

  // 用于推荐系统与数据中心跟踪统计用户行为，没具体业务意义
  message RecommendationTraceInfo {
    uint32 recall_flag = 1;
  }

  RecommendationTraceInfo trace_info = 11; // 用于推荐系统与数据中心跟踪统计用户行为，没具体业务意义(透穿)

  bool owner_mismatch_version = 12; // 房主是否不符合版本(服务端指定)，true表示房主不符合版本要求
}

enum MysteryPlaceRCMDLabelEnum {
  RCMDLabel_None = 0;
  RCMDLabel_GangUpWithHomeOwner = 1;
  RCMDLabel_ChatWithHomeOwner = 2;
}

message MysteryPlaceChannelView{
  oneof channel_view{
    mystery_place_logic.MysteryViewDefault mystery_view_default = 1;
    mystery_place_logic.MysteryMusicChannelView mystery_view_music = 2;
    mystery_place_logic.MysterySimpleView mystery_view_simple = 3;
  }
}

// 获取评论页参数
message GetCommentPageParamsReq{
  ga.BaseReq base_req = 1;
  uint32 tab_id = 2; // 玩法id
  bool have_completed = 3; // 是否完成剧本
}
message GetCommentPageParamsResp{
  ga.BaseResp base_resp = 1;
  bool already_comment = 2; //是否评论过
  repeated PlaymateTag good_playmate_tags = 3; // 好评标签
  repeated PlaymateTag bad_playmate_tags = 4;  // 差评标签
  ScenarioInfo info = 5; // 用于判断是否单人本
}

message PlaymateTag {
  string id = 1; // 标签id
  string content = 2; // 标签内容
}

message ScenarioComment{
  uint32 score = 1; // 分数
  string content = 2; // 评论内容
  bool have_completed = 3; // 是否完成剧本
}
// 评论剧本/玩伴
message CommentToScenarioReq{
  ga.BaseReq base_req = 1;
  uint32 tab_id = 2; // 玩法id
  ScenarioComment comment = 3; // 评论
  uint32 target_uid = 4; // 被评论的uid
  PlaymateTagType type = 5; // 是否好评
  repeated PlaymateTag playmate_tags = 6; // 标签
  uint32 channel_id = 7; // 房间id
  string set_id = 8; // 小游戏的对局id
  string post_id = 9; // 帖子id
  string comment_to_npc = 10 [deprecated = true];
  ScenarioComment comment_detail_to_npc = 11; // 评论
}
message CommentToScenarioResp{
  ga.BaseResp base_resp = 1;
}

enum PlaymateTagType{
  PlaymateTagType_UNDEFINED = 0;
  PlaymateTagType_GOOD = 1; // 好评
  PlaymateTagType_BAD = 2; // 差评
}

// 获取推荐剧本
message GetRcmdScenarioReq{
  ga.BaseReq base_req = 1;
}
message GetRcmdScenarioResp{
  ga.BaseResp base_resp = 1;
  ScenarioInfo info = 2;
}

// 邀请房主切换剧本
message RequestToSwitchScenarioReq{
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
  uint32 tab_id = 3;
}
message RequestToSwitchScenarioResp{
  ga.BaseResp base_resp = 1;
}

message GetNewScenarioTipReq{
  ga.BaseReq base_req = 1;
}
message GetNewScenarioTipResp{
  ga.BaseResp base_resp = 1;
  repeated NewScenarioTip tips = 2; // 提醒（每个剧本仅有一条）
  uint32 switch_interval = 3; // 切换间隔时间（单位：s）
}

enum NewScenarioTipType{
  NewScenarioTipType_UNDEFINED = 0; // 未定义
  NewScenarioTipType_NEW_TAB = 1; // 新主题
  NewScenarioTipType_SUBSCRIBE = 2; // 预约
  NewScenarioTipType_NEW = 3; // 新剧本
}

message NewScenarioTip{
  string tip_id = 1; // 提醒id
  uint32 scenario_id = 2; // 剧本id
  uint32 tab_id = 3; // 玩法id
  NewScenarioTipType tip_type = 4; // 提醒类型
  string content = 5; // 提醒内容
}

message MarkNewScenarioTipReadReq{
  ga.BaseReq base_req = 1;
  NewScenarioTip tip = 2; // 提醒（每个剧本仅有一条）
}
message MarkNewScenarioTipReadResp{
  ga.BaseResp base_resp = 1;
}

message MysteryPlaceClientConfigReq{
  ga.BaseReq base_req = 1;
  string version_name = 2; //谜境版本
  string channel_package_id = 3; //渠道包
}
message MysteryPlaceClientConfigResp{
  ga.BaseResp base_resp = 1;
  bool mj_hide_novice_guide = 2;
  bool is_close = 3;
  bool is_effective_regulation = 4;
  bool is_hide_scenario_rank = 5;
  bool mj_control_audit = 6;  // 谜境提审开关控制
  string scenario_rank_uri = 7; // 和5 榜单跳转地址
  bool hide_mj_ad = 8; // 谜境广告屏蔽
}

// 获取剧本记录
message GetPlayedScenarioRecordListReq{
  ga.BaseReq base_req = 1;
  MysteryPlaceLoadMore load_more = 2;
  uint32 count = 3; // 剧本数量
  uint32 uid =  4;
}
message GetPlayedScenarioRecordListResp{
  ga.BaseResp base_resp = 1;
  MysteryPlaceLoadMore load_more = 2;
  repeated ScenarioRecord played_records = 3;
}

// 根据ID获取剧本记录
message GetPlayedScenarioRecordListByIDReq{
  ga.BaseReq base_req = 1;
  uint32 uid =  2;
  repeated uint32 scenario_id_list = 3; // 剧本id
}
message GetPlayedScenarioRecordListByIDResp{
  ga.BaseResp base_resp = 1;
  repeated ScenarioRecord played_records = 2;
}

message ScenarioRecord{
  uint32 scenario_id = 1; // 剧本id
  string scenario_name = 2; // 剧本名称
  string bg = 3; // 剧本封面
  repeated uint32 passed_chapters = 4; // 已通关的章节
  uint32 all_chapter_num = 5; // 所有章节数量
  string tip = 6; // 提示
  repeated string recommend_picture = 7; // 剧本轮播图列表
}

message GetPlayedScenarioRecordDetailReq{
  ga.BaseReq base_req = 1;
  uint32 scenario_id = 2; // 剧本id
  uint32 uid = 3; // 用户uid
}
message GetPlayedScenarioRecordDetailResp{
  ga.BaseResp base_resp = 1;
  ScenarioRecordDetail detail = 2; // 记录详情
  bool is_visible = 3; // 记录是否可见
}

message ScenarioRecordDetail{
  uint32 scenario_id = 1; // 剧本id
  uint32 game_id = 2; // 小游戏id
  uint32 tab_id = 3; // 玩法id
  uint32 chapter = 4; // 章节
  MysteryPlacePlayer player1 = 5; // 玩家1
  MysteryPlacePlayer player2 = 6; // 玩家2
  string summary = 7; // 总览
  uint32 created_at = 8; // 通关时间
}

message MysteryPlacePlayer{
  uint32 uid = 1;
  string account = 2;
  string nickname = 3;
}

message MysteryPlaceLoadMore {
  uint32 last_page = 1;
  uint32 last_count = 2;
}

// 获取剧本章节概要
message GetScenarioChapterSummaryReq{
  ga.BaseReq base_req = 1;
  uint32 game_id = 2; // 游戏id
  uint32 chapter = 3; // 章节
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  uint32 playmateUid = 4; // 玩伴uid
}
message GetScenarioChapterSummaryResp{
  ga.BaseResp base_resp = 1;
  repeated string chapter_fragments = 2; // 回顾
  string summary = 3; // 总览
  bool is_visible = 4; // 记录是否可见
}

// 设置记录可见
message SetPlayedScenarioRecordVisibilityReq{
  ga.BaseReq base_req = 1;
  uint32 game_id = 2;
  uint32 playmate_uid = 3;
  bool is_visible = 4;
}
message SetPlayedScenarioRecordVisibilityResp{
  ga.BaseResp base_resp = 1;
  bool is_visible = 2;
}

// 获取推荐剧本
message GetRcmdMiJingTabReq{
  ga.BaseReq base_req = 1;
  uint32 tab_id = 2; // 玩法id
}
message ScenarioExtraInfo{
  float score = 1; // 评分
  string min = 2; // 预估通关时长
  repeated string labels = 3; // 标签
  uint32 scenario_id = 4; // 剧本id
}
message RcmdTabInfo{
  uint32 tab_id = 1; // 玩法id
  string icon = 2; // 图标
  string title = 3; // 标题
  string desc = 4; // 描述
  string tip = 5;
  ScenarioExtraInfo extra_info = 6; // 剧本扩展信息
}
message GetRcmdMiJingTabResp{
  ga.BaseResp base_resp = 1;
  repeated RcmdTabInfo tab_infos = 2; // 玩法信息
}

//首页豆腐块
message HomePageBigTofuReq{
  ga.BaseReq base_req = 1;
  uint32 last_time = 2;
}

message HomePageBigTofuResp{
  ga.BaseResp base_resp = 1;
  string sub_title = 2;
  repeated string rolling_img = 3; //轮播图
  bool has_new = 4;// 是否有新本
  string background_img = 5; // 豆腐块背景图
  string title = 6; // 豆腐块名称，未展示
}

message HomePageRightTofuReq{
  ga.BaseReq base_req = 1;
  int64 config_id = 2; // 客户端获取不同版本需求配置 预设最新配置号获取新版本配置6.39.0-》6390
}

message TofuItem{
  string img = 1 ;
  string sub_title = 2; // 展示用
  string link = 3;
  string title = 4; // 豆腐块名称，未展示
}

message HomePageRightTofuResp{
  ga.BaseResp base_resp = 1;
  repeated TofuItem item = 2;
}


message IsUserHasScenarioFreeCouponsReq {
  ga.BaseReq base_req = 1;
  uint32 uid = 2;
  uint32 scenario_id = 3;// 剧本ID
}

message IsUserHasScenarioFreeCouponsResp {
  ga.BaseResp base_resp = 1;
  bool is_had_free_coupon = 2; // 是否有免费券
}

// deprecated (写错名但又删不了的)
message GetChannelScenarioInfoReqReq {
  ga.BaseReq base_req = 1;
  uint32 tab_id = 2;
}

// deprecated (写错名但又删不了的)
message GetChannelScenarioInfoReqResp {
  ga.BaseResp base_resp = 1;
  ScenarioInfo info = 2; // 剧本数据
}


message GetChannelScenarioInfoReq {
  ga.BaseReq base_req = 1;
  uint32 tab_id = 2;
}

message GetChannelScenarioInfoResp {
  ga.BaseResp base_resp = 1;
  ScenarioInfo info = 2; // 剧本数据
  topic_channel.Tab tab_detail = 3;   // 玩法信息
}

// 邀请玩本的信息
message InvitationActivityInfo {
  string activity_id = 1; // 活动ID
  string title = 2; // 文本
  string uri = 3; // 活动地址
  string icon = 4; // 图标
}

// 获取密逃常用语请求
message GetEscapeCommonExpressionListReq {
  ga.BaseReq base_req = 1;
}

// 获取密逃常用语返回
message GetEscapeCommonExpressionListResp {
  ga.BaseResp base_resp = 1;
  repeated EscapeCommonExpression expression_list = 2;
}

// 密逃常用语
message EscapeCommonExpression {
  uint64 id = 1;  // ID
  string content = 2;  // 常用语文案
}

enum ScenarioSubscribeStatus{
  ScenarioSubscribeStatus_UNSPECIFIED = 0;
  ScenarioSubscribeStatus_SUBSCRIBED = 1; // 已预约
  ScenarioSubscribeStatus_UNSUBSCRIBED = 2; // 取消预约（2023-10-25 暂时用不上）
}

// 上报预约状态
message ReportScenarioSubscribeStatusReq{
  ga.BaseReq base_req = 1;
  ScenarioSubscribeStatus status = 2; // 预约状态
  uint32 scenario_id = 3; // 剧本id
}
message ReportScenarioSubscribeStatusResp{
  ga.BaseResp base_resp = 1;
}

enum InviteSource {
  InviteSource_UNSPECIFIED = 0; // NONE
  InviteSource_WECHAT_DISCOVER = 1; // 微信朋友圈
  InviteSource_WECHAT = 2; // 微信
  InviteSource_QQ = 3; // QQ
  InviteSource_QQ_SPACE = 4; // QQ空间
  InviteSource_COPY_PSW = 5; // 复制口令
  InviteSource_COPY_URL = 6; // 复制地址
}

// 接入邀请风控
message CheckRiskReq{
  ga.BaseReq base_req = 1;
  oneof risk_type {
    InviteToPlayShareRisk invite_risk = 2;
  }
}
message CheckRiskResp{
  ga.BaseResp base_resp = 1;
}

// 纸嫁衣活动 invite_to_play 分享时
message InviteToPlayShareRisk {
  InviteSource source = 1;  // 邀请方式，风控类型为邀请时填写
  int64 check_at = 2;       // ms 毫秒
}

//获取剧本热度榜单信息
message GetHotRankScenarioListReq{
  ga.BaseReq base_req = 1;
}

message GetHotRankScenarioListResp{
  ga.BaseResp base_resp = 1;
  repeated HotRankScenarioRankInfo infos = 2;
  uint32 limit_day = 3;//最近15天
}

message HotRankScenarioRankInfo {
  uint32 scenario_id = 1;
  string title = 2;//剧本名称
  repeated string labels = 3; //剧本标签
  string small_picture = 4;//小图图片
  uint32 min_player_num = 5; // 开场人数
  string introduction = 6; //剧本介绍
  uint32 hot_score = 7; //剧本的热度值
  string rail_bg = 8; // 横条背景图
  ScenarioDevInfo dev_info = 9;//剧本开发者信息
  bool is_super_scenario = 10;//是否精品本
  bool is_rookie = 11;// 是否新手必玩
  repeated mijing_label_logic.ScenarioContentLabel scenario_content_label_list = 12;  // 剧本内容标签列表
}

// 谜境剧本详情页是否可评价
message MijingShowCommentEntranceReq{
  ga.BaseReq base_req = 1;
  uint32 scenario_id = 2;
}

message MijingShowCommentEntranceResp{
  ga.BaseResp base_resp = 1;
  bool is_show = 2;
}