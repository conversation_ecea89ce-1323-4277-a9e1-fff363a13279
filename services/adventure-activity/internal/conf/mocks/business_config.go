// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/adventure-activity/internal/conf (interfaces: IBusinessConfManager)

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	conf "golang.52tt.com/services/adventure-activity/internal/conf"
)

// MockIBusinessConfManager is a mock of IBusinessConfManager interface.
type MockIBusinessConfManager struct {
	ctrl     *gomock.Controller
	recorder *MockIBusinessConfManagerMockRecorder
}

// MockIBusinessConfManagerMockRecorder is the mock recorder for MockIBusinessConfManager.
type MockIBusinessConfManagerMockRecorder struct {
	mock *MockIBusinessConfManager
}

// NewMockIBusinessConfManager creates a new mock instance.
func NewMockIBusinessConfManager(ctrl *gomock.Controller) *MockIBusinessConfManager {
	mock := &MockIBusinessConfManager{ctrl: ctrl}
	mock.recorder = &MockIBusinessConfManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIBusinessConfManager) EXPECT() *MockIBusinessConfManagerMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockIBusinessConfManager) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIBusinessConfManagerMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIBusinessConfManager)(nil).Close))
}

// GetActivityCfg mocks base method.
func (m *MockIBusinessConfManager) GetActivityCfg() *conf.ActivityConf {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActivityCfg")
	ret0, _ := ret[0].(*conf.ActivityConf)
	return ret0
}

// GetActivityCfg indicates an expected call of GetActivityCfg.
func (mr *MockIBusinessConfManagerMockRecorder) GetActivityCfg() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActivityCfg", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetActivityCfg))
}

// GetAwardCenterAppId mocks base method.
func (m *MockIBusinessConfManager) GetAwardCenterAppId() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAwardCenterAppId")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetAwardCenterAppId indicates an expected call of GetAwardCenterAppId.
func (mr *MockIBusinessConfManagerMockRecorder) GetAwardCenterAppId() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardCenterAppId", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetAwardCenterAppId))
}

// GetBackSenderInfo mocks base method.
func (m *MockIBusinessConfManager) GetBackSenderInfo() (uint32, string) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBackSenderInfo")
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(string)
	return ret0, ret1
}

// GetBackSenderInfo indicates an expected call of GetBackSenderInfo.
func (mr *MockIBusinessConfManagerMockRecorder) GetBackSenderInfo() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBackSenderInfo", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetBackSenderInfo))
}

// GetBingoTtMsg mocks base method.
func (m *MockIBusinessConfManager) GetBingoTtMsg() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBingoTtMsg")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetBingoTtMsg indicates an expected call of GetBingoTtMsg.
func (mr *MockIBusinessConfManagerMockRecorder) GetBingoTtMsg() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBingoTtMsg", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetBingoTtMsg))
}

// GetBingoUserSearchAble mocks base method.
func (m *MockIBusinessConfManager) GetBingoUserSearchAble() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBingoUserSearchAble")
	ret0, _ := ret[0].(bool)
	return ret0
}

// GetBingoUserSearchAble indicates an expected call of GetBingoUserSearchAble.
func (mr *MockIBusinessConfManagerMockRecorder) GetBingoUserSearchAble() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBingoUserSearchAble", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetBingoUserSearchAble))
}

// GetBreakingNewsLayBackSec mocks base method.
func (m *MockIBusinessConfManager) GetBreakingNewsLayBackSec() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBreakingNewsLayBackSec")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetBreakingNewsLayBackSec indicates an expected call of GetBreakingNewsLayBackSec.
func (mr *MockIBusinessConfManagerMockRecorder) GetBreakingNewsLayBackSec() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBreakingNewsLayBackSec", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetBreakingNewsLayBackSec))
}

// GetBuyAmountOptions mocks base method.
func (m *MockIBusinessConfManager) GetBuyAmountOptions() []uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBuyAmountOptions")
	ret0, _ := ret[0].([]uint32)
	return ret0
}

// GetBuyAmountOptions indicates an expected call of GetBuyAmountOptions.
func (mr *MockIBusinessConfManagerMockRecorder) GetBuyAmountOptions() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBuyAmountOptions", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetBuyAmountOptions))
}

// GetBuyLimitDesc mocks base method.
func (m *MockIBusinessConfManager) GetBuyLimitDesc() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBuyLimitDesc")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetBuyLimitDesc indicates an expected call of GetBuyLimitDesc.
func (mr *MockIBusinessConfManagerMockRecorder) GetBuyLimitDesc() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBuyLimitDesc", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetBuyLimitDesc))
}

// GetChancePackId mocks base method.
func (m *MockIBusinessConfManager) GetChancePackId() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChancePackId")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetChancePackId indicates an expected call of GetChancePackId.
func (mr *MockIBusinessConfManagerMockRecorder) GetChancePackId() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChancePackId", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetChancePackId))
}

// GetFeiShuRobotUrl mocks base method.
func (m *MockIBusinessConfManager) GetFeiShuRobotUrl() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFeiShuRobotUrl")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetFeiShuRobotUrl indicates an expected call of GetFeiShuRobotUrl.
func (mr *MockIBusinessConfManagerMockRecorder) GetFeiShuRobotUrl() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFeiShuRobotUrl", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetFeiShuRobotUrl))
}

// GetHistoryProfitExtract mocks base method.
func (m *MockIBusinessConfManager) GetHistoryProfitExtract() int64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHistoryProfitExtract")
	ret0, _ := ret[0].(int64)
	return ret0
}

// GetHistoryProfitExtract indicates an expected call of GetHistoryProfitExtract.
func (mr *MockIBusinessConfManagerMockRecorder) GetHistoryProfitExtract() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHistoryProfitExtract", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetHistoryProfitExtract))
}

// GetHistoryProfitFusingMin mocks base method.
func (m *MockIBusinessConfManager) GetHistoryProfitFusingMin() int64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHistoryProfitFusingMin")
	ret0, _ := ret[0].(int64)
	return ret0
}

// GetHistoryProfitFusingMin indicates an expected call of GetHistoryProfitFusingMin.
func (mr *MockIBusinessConfManagerMockRecorder) GetHistoryProfitFusingMin() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHistoryProfitFusingMin", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetHistoryProfitFusingMin))
}

// GetHistoryProfitUpperLimit mocks base method.
func (m *MockIBusinessConfManager) GetHistoryProfitUpperLimit() int64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHistoryProfitUpperLimit")
	ret0, _ := ret[0].(int64)
	return ret0
}

// GetHistoryProfitUpperLimit indicates an expected call of GetHistoryProfitUpperLimit.
func (mr *MockIBusinessConfManagerMockRecorder) GetHistoryProfitUpperLimit() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHistoryProfitUpperLimit", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetHistoryProfitUpperLimit))
}

// GetHistoryProfitWarnMin mocks base method.
func (m *MockIBusinessConfManager) GetHistoryProfitWarnMin() int64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHistoryProfitWarnMin")
	ret0, _ := ret[0].(int64)
	return ret0
}

// GetHistoryProfitWarnMin indicates an expected call of GetHistoryProfitWarnMin.
func (mr *MockIBusinessConfManagerMockRecorder) GetHistoryProfitWarnMin() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHistoryProfitWarnMin", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetHistoryProfitWarnMin))
}

// GetHourProfitFusingMin mocks base method.
func (m *MockIBusinessConfManager) GetHourProfitFusingMin() int64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHourProfitFusingMin")
	ret0, _ := ret[0].(int64)
	return ret0
}

// GetHourProfitFusingMin indicates an expected call of GetHourProfitFusingMin.
func (mr *MockIBusinessConfManagerMockRecorder) GetHourProfitFusingMin() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHourProfitFusingMin", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetHourProfitFusingMin))
}

// GetHourProfitWarnMin mocks base method.
func (m *MockIBusinessConfManager) GetHourProfitWarnMin() int64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHourProfitWarnMin")
	ret0, _ := ret[0].(int64)
	return ret0
}

// GetHourProfitWarnMin indicates an expected call of GetHourProfitWarnMin.
func (mr *MockIBusinessConfManagerMockRecorder) GetHourProfitWarnMin() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHourProfitWarnMin", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetHourProfitWarnMin))
}

// GetMinAvgProfit mocks base method.
func (m *MockIBusinessConfManager) GetMinAvgProfit() int {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMinAvgProfit")
	ret0, _ := ret[0].(int)
	return ret0
}

// GetMinAvgProfit indicates an expected call of GetMinAvgProfit.
func (mr *MockIBusinessConfManagerMockRecorder) GetMinAvgProfit() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMinAvgProfit", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetMinAvgProfit))
}

// GetNeedBreakingNewsValueLimit mocks base method.
func (m *MockIBusinessConfManager) GetNeedBreakingNewsValueLimit() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNeedBreakingNewsValueLimit")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetNeedBreakingNewsValueLimit indicates an expected call of GetNeedBreakingNewsValueLimit.
func (mr *MockIBusinessConfManagerMockRecorder) GetNeedBreakingNewsValueLimit() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNeedBreakingNewsValueLimit", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetNeedBreakingNewsValueLimit))
}

// GetNoPermissionText mocks base method.
func (m *MockIBusinessConfManager) GetNoPermissionText() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNoPermissionText")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetNoPermissionText indicates an expected call of GetNoPermissionText.
func (mr *MockIBusinessConfManagerMockRecorder) GetNoPermissionText() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNoPermissionText", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetNoPermissionText))
}

// GetPayAppId mocks base method.
func (m *MockIBusinessConfManager) GetPayAppId() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPayAppId")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetPayAppId indicates an expected call of GetPayAppId.
func (mr *MockIBusinessConfManagerMockRecorder) GetPayAppId() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPayAppId", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetPayAppId))
}

// GetPayReasonFormat mocks base method.
func (m *MockIBusinessConfManager) GetPayReasonFormat() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPayReasonFormat")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetPayReasonFormat indicates an expected call of GetPayReasonFormat.
func (mr *MockIBusinessConfManagerMockRecorder) GetPayReasonFormat() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPayReasonFormat", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetPayReasonFormat))
}

// GetPoolSimulateCnt mocks base method.
func (m *MockIBusinessConfManager) GetPoolSimulateCnt() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPoolSimulateCnt")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetPoolSimulateCnt indicates an expected call of GetPoolSimulateCnt.
func (mr *MockIBusinessConfManagerMockRecorder) GetPoolSimulateCnt() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPoolSimulateCnt", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetPoolSimulateCnt))
}

// GetReissueAwardHour mocks base method.
func (m *MockIBusinessConfManager) GetReissueAwardHour() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReissueAwardHour")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetReissueAwardHour indicates an expected call of GetReissueAwardHour.
func (mr *MockIBusinessConfManagerMockRecorder) GetReissueAwardHour() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReissueAwardHour", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetReissueAwardHour))
}

// GetTestMode mocks base method.
func (m *MockIBusinessConfManager) GetTestMode() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTestMode")
	ret0, _ := ret[0].(bool)
	return ret0
}

// GetTestMode indicates an expected call of GetTestMode.
func (mr *MockIBusinessConfManagerMockRecorder) GetTestMode() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTestMode", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetTestMode))
}

// GetTestPayUidList mocks base method.
func (m *MockIBusinessConfManager) GetTestPayUidList() []uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTestPayUidList")
	ret0, _ := ret[0].([]uint32)
	return ret0
}

// GetTestPayUidList indicates an expected call of GetTestPayUidList.
func (mr *MockIBusinessConfManagerMockRecorder) GetTestPayUidList() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTestPayUidList", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetTestPayUidList))
}

// GetTopDressAwardConf mocks base method.
func (m *MockIBusinessConfManager) GetTopDressAwardConf() *conf.TopDressAwardConf {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTopDressAwardConf")
	ret0, _ := ret[0].(*conf.TopDressAwardConf)
	return ret0
}

// GetTopDressAwardConf indicates an expected call of GetTopDressAwardConf.
func (mr *MockIBusinessConfManagerMockRecorder) GetTopDressAwardConf() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTopDressAwardConf", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetTopDressAwardConf))
}

// GetTopExtraAwardConf mocks base method.
func (m *MockIBusinessConfManager) GetTopExtraAwardConf() *conf.TopExtraAwardConf {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTopExtraAwardConf")
	ret0, _ := ret[0].(*conf.TopExtraAwardConf)
	return ret0
}

// GetTopExtraAwardConf indicates an expected call of GetTopExtraAwardConf.
func (mr *MockIBusinessConfManagerMockRecorder) GetTopExtraAwardConf() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTopExtraAwardConf", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetTopExtraAwardConf))
}

// NeedBingoBreakingNews mocks base method.
func (m *MockIBusinessConfManager) NeedBingoBreakingNews() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NeedBingoBreakingNews")
	ret0, _ := ret[0].(bool)
	return ret0
}

// NeedBingoBreakingNews indicates an expected call of NeedBingoBreakingNews.
func (mr *MockIBusinessConfManagerMockRecorder) NeedBingoBreakingNews() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NeedBingoBreakingNews", reflect.TypeOf((*MockIBusinessConfManager)(nil).NeedBingoBreakingNews))
}

// Reload mocks base method.
func (m *MockIBusinessConfManager) Reload(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Reload", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Reload indicates an expected call of Reload.
func (mr *MockIBusinessConfManagerMockRecorder) Reload(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Reload", reflect.TypeOf((*MockIBusinessConfManager)(nil).Reload), arg0)
}

// Watch mocks base method.
func (m *MockIBusinessConfManager) Watch(arg0 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Watch", arg0)
}

// Watch indicates an expected call of Watch.
func (mr *MockIBusinessConfManagerMockRecorder) Watch(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Watch", reflect.TypeOf((*MockIBusinessConfManager)(nil).Watch), arg0)
}
