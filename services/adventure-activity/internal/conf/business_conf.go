package conf

import (
    "crypto/md5"
    "encoding/json"
    "errors"
    "fmt"
    "io/ioutil"
    "time"

    "golang.52tt.com/pkg/log"
)

const BusinessConfFile = "/data/oss/conf-center/tt/adventure-island.json"

var LastConfMd5Sum [md5.Size]byte

type ActivityConf struct {
    ActivityId   uint32    `json:"activity_id"`
    ActivityName string    `json:"activity_name"`
    BeginTime    time.Time `json:"begin_time"`
    EndTime      time.Time `json:"end_time"`
}

type BusinessConf struct {
    PayAppId        string `json:"pay_app_id"`        // 支付系统appId
    PayReasonFormat string `json:"pay_reason_format"` // 支付描述format

    BackSenderAppId  uint32 `json:"back_sender_app_id"`  // 包裹发放业务id
    BackSenderSign   string `json:"back_sender_sign"`    // 包裹发放密钥业务id
    AwardCenterAppId uint32 `json:"award_center_app_id"` // 装扮发奖中心
    FeiShuRobotUrl   string `json:"fei_shu_robot_url"`   // 飞书机器人url

    ActivityCfg *ActivityConf `json:"activity_cfg"`

    HourProfitWarnMin       int64 `json:"hour_profit_warn_min"`       // 小时亏损告警阈值
    HourProfitFusingMin     int64 `json:"hour_profit_fusing_min"`     // 小时亏损停服阈值
    HistoryProfitWarnMin    int64 `json:"history_profit_warn_min"`    // 历史亏损告警阈值
    HistoryProfitFusingMin  int64 `json:"history_profit_fusing_min"`  // 历史亏损停服阈值
    HistoryProfitUpperLimit int64 `json:"history_profit_upper_limit"` // 历史利润上限值
    HistoryProfitExtract    int64 `json:"history_profit_extract"`     // 当利润达到上限值时扣除的“提取金额”

    ChancePackId     uint32   `json:"chance_pack_id"` // 购买机会的包裹id
    BuyAmountOptions []uint32 `json:"buy_amount_options"`
    BuyLimitDesc     string   `json:"buy_limit_desc"`
    MinAvgProfit     int      `json:"min_avg_profit"` // 期望最小平均收益值（元）

    NoPermissionText string `json:"no_permission_text"`

    BingoTtMsg                 string `json:"bingo_tt_msg"`             // 大奖tt助手消息
    BingoUserSearchAble        bool   `json:"bingo_user_search_switch"` // 中大奖用户搜索开关
    NeedBingoBreakingNews      bool   `json:"need_bingo_breaking_news"`
    NeedBreakingNewsValueLimit uint32 `json:"need_breaking_new_value_limit"` // 中奖全服T豆门槛
    BreakingNewsLayBackSec     uint32 `json:"breaking_news_lay_back_second"` // 中间全服延时推送时间（秒）

    PoolSimulateCnt uint32 `json:"pool_simulate_cnt"` // 奖池模拟次数

    TestMode         bool     `json:"test_mode"`
    TestPayUidList   []uint32 `json:"test_pay_uid_list"`
    ReissueAwardHour uint32   `json:"reissue_award_hour"`

    TopDressAwardConf  *TopDressAwardConf  `json:"top_dress_award_conf"`  // 登顶装扮奖励配置
    TopExtraAwardConf  *TopExtraAwardConf  `json:"top_extra_award_conf"`  // 登顶加码奖励配置
}

// DressAward 奖励装扮信息
type DressAward struct {
    DressId     string `json:"award_id"`     // 奖励id
    DressType   uint32 `json:"award_type"`   // 奖励类型
    DurationDay uint32 `json:"duration_sec"` // 奖励时长（天）
}

// TopDressAwardConf 登顶装扮奖励配置信息
type TopDressAwardConf struct {
    DressList  []DressAward `json:"dress_list"`  // 奖励装扮列表
    LimitCount uint32       `json:"limit_count"` // 每人获得次数限制
}

// TopExtraAwardConf 登顶加码奖励配置信息
type TopExtraAwardConf struct {
    LimitCount uint32 `json:"limit_count"` // 前N名限制
    AwardBgId  uint32 `json:"award_bg_id"` // 奖励背景id
}

func (c *BusinessConf) Parse(configFile string) (isChange bool, err error) {
    defer func() {
        if e := recover(); e != nil {
            err = fmt.Errorf("Failed to parse config: %v \n", e)
        }
    }()

    data, err := ioutil.ReadFile(configFile)
    if err != nil {
        return false, err
    }

    md5Sum := md5.Sum(data)
    if md5Sum == LastConfMd5Sum {
        isChange = false
        return
    }

    err = json.Unmarshal(data, &c)
    if err != nil {
        return false, err
    }

    err = c.CheckConf()
    if err != nil {
        return false, err
    }

    LastConfMd5Sum = md5Sum

    log.Infof("BusinessConf : %+v", c)
    return true, nil
}

func (c *BusinessConf) CheckConf() error {
    if c.PayAppId == "" {
        return errors.New("PayAppId conf err")
    }
    if c.BackSenderAppId == 0 || c.BackSenderSign == "" {
        return errors.New("BackSender conf err")
    }
    if c.ChancePackId == 0 {
        return errors.New("ChancePackId conf err")
    }

    return nil
}

type BusinessConfManager struct {
    Done chan interface{}
    //mutex sync.RWMutex
    conf *BusinessConf
}

func NewBusinessConfManager() (*BusinessConfManager, error) {
    businessConf := &BusinessConf{}

    _, err := businessConf.Parse(BusinessConfFile)
    if err != nil {
        log.Errorf("NewBusinessConfManager fail to Parse BusinessConf err:%v", err)
        return nil, err
    }

    confMgr := &BusinessConfManager{
        conf: businessConf,
        Done: make(chan interface{}),
    }

    go confMgr.Watch(BusinessConfFile)

    return confMgr, nil
}

func (bm *BusinessConfManager) Reload(file string) error {
    businessConf := &BusinessConf{}

    isChange, err := businessConf.Parse(file)
    if err != nil {
        log.Errorf("NewBusinessConfManager fail to Parse BusinessConf err:%v", err)
        return err
    }

    if isChange {
        //bm.mutex.Lock()
        bm.conf = businessConf
        //bm.mutex.Unlock()

        log.Infof("Reload %+v", businessConf)
    }

    return nil
}

func (bm *BusinessConfManager) Watch(file string) {
    log.Infof("Watch start. file:%s", file)

    for {
        select {
        case _, ok := <-bm.Done:
            if !ok {
                log.Infof("Watch done")
                return
            }

        case <-time.After(30 * time.Second):
            err := bm.Reload(file)
            if err != nil {
                log.Errorf("Watch Reload fail. file:%s, err:%v", file, err)
            }
        }
    }
}

func (bm *BusinessConfManager) GetReissueAwardHour() uint32 {
    if bm.conf.ReissueAwardHour == 0 {
        return 6
    }

    return bm.conf.ReissueAwardHour
}

func (bm *BusinessConfManager) GetPayAppId() string {
    return bm.conf.PayAppId
}

func (bm *BusinessConfManager) GetFeiShuRobotUrl() string {
    return bm.conf.FeiShuRobotUrl
}

func (bm *BusinessConfManager) GetAwardCenterAppId() uint32 {
    return bm.conf.AwardCenterAppId
}

func (bm *BusinessConfManager) GetNeedBreakingNewsValueLimit() uint32 {
    if bm.conf.NeedBreakingNewsValueLimit == 0 {
        return 500000
    }
    return bm.conf.NeedBreakingNewsValueLimit
}

func (bm *BusinessConfManager) GetBreakingNewsLayBackSec() uint32 {
    if bm.conf.NeedBreakingNewsValueLimit == 0 {
        return 1
    }
    return bm.conf.BreakingNewsLayBackSec
}

func (bm *BusinessConfManager) GetHourProfitFusingMin() int64 {
    if bm.conf.HourProfitFusingMin == 0 {
        return 10 * 10000 * 100
    }
    return bm.conf.HourProfitFusingMin
}

func (bm *BusinessConfManager) GetHourProfitWarnMin() int64 {
    if bm.conf.HourProfitWarnMin == 0 {
        return 3 * 10000 * 100
    }
    return bm.conf.HourProfitWarnMin
}

func (bm *BusinessConfManager) GetHistoryProfitFusingMin() int64 {
    return bm.conf.HistoryProfitFusingMin
}

func (bm *BusinessConfManager) GetHistoryProfitWarnMin() int64 {
    if bm.conf.HistoryProfitWarnMin == 0 {
        return 10 * 10000 * 100
    }
    return bm.conf.HistoryProfitWarnMin
}

func (bm *BusinessConfManager) GetHistoryProfitUpperLimit() int64 {
    if bm.conf.HistoryProfitUpperLimit == 0 {
        return 40 * 10000 * 100
    }
    return bm.conf.HistoryProfitUpperLimit
}

func (bm *BusinessConfManager) GetHistoryProfitExtract() int64 {
    return bm.conf.HistoryProfitExtract
}

func (bm *BusinessConfManager) GetBackSenderInfo() (uint32, string) {
    return bm.conf.BackSenderAppId, bm.conf.BackSenderSign
}

func (bm *BusinessConfManager) GetChancePackId() uint32 {
    return bm.conf.ChancePackId
}

func (bm *BusinessConfManager) GetMinAvgProfit() int {
    return bm.conf.MinAvgProfit
}

func (bm *BusinessConfManager) GetPoolSimulateCnt() uint32 {
    return bm.conf.PoolSimulateCnt
}

func (bm *BusinessConfManager) GetTestPayUidList() []uint32 {
    return bm.conf.TestPayUidList
}

func (bm *BusinessConfManager) GetBingoTtMsg() string {
    return bm.conf.BingoTtMsg
}

func (bm *BusinessConfManager) GetBingoUserSearchAble() bool {
    return bm.conf.BingoUserSearchAble
}

func (bm *BusinessConfManager) GetTestMode() bool {
    return bm.conf.TestMode
}

func (bm *BusinessConfManager) NeedBingoBreakingNews() bool {
    return bm.conf.NeedBingoBreakingNews
}

func (bm *BusinessConfManager) Close() {
    close(bm.Done)
}

func (bm *BusinessConfManager) GetPayReasonFormat() string {
    return bm.conf.PayReasonFormat
}

func (bm *BusinessConfManager) GetActivityCfg() *ActivityConf {
    if bm.conf == nil || bm.conf.ActivityCfg == nil {
        return &ActivityConf{}
    }
    return bm.conf.ActivityCfg
}

func (bm *BusinessConfManager) GetBuyAmountOptions() []uint32 {
    return bm.conf.BuyAmountOptions
}

func (bm *BusinessConfManager) GetBuyLimitDesc() string {
    return bm.conf.BuyLimitDesc
}

func (bm *BusinessConfManager) GetNoPermissionText() string {
    return bm.conf.NoPermissionText
}


func (bm *BusinessConfManager) GetTopDressAwardConf() *TopDressAwardConf {
    if bm.conf == nil || bm.conf.TopDressAwardConf == nil {
        return &TopDressAwardConf{}
    }
    return bm.conf.TopDressAwardConf
}

func (bm *BusinessConfManager) GetTopExtraAwardConf() *TopExtraAwardConf {
    if bm.conf == nil || bm.conf.TopExtraAwardConf == nil {
        return &TopExtraAwardConf{}
    }
    return bm.conf.TopExtraAwardConf
}