package conf

import(
)

type IBusinessConfManager interface {
	Close() 
	GetActivityCfg() *ActivityConf
	GetAwardCenterAppId() uint32
	GetBackSenderInfo() (uint32,string)
	GetBingoTtMsg() string
	GetBingoUserSearchAble() bool
	GetBreakingNewsLayBackSec() uint32
	GetBuyAmountOptions() []uint32
	GetBuyLimitDesc() string
	GetChancePackId() uint32
	GetFeiShuRobotUrl() string
	GetHistoryProfitExtract() int64
	GetHistoryProfitFusingMin() int64
	GetHistoryProfitUpperLimit() int64
	GetHistoryProfitWarnMin() int64
	GetHourProfitFusingMin() int64
	GetHourProfitWarnMin() int64
	GetMinAvgProfit() int
	GetNeedBreakingNewsValueLimit() uint32
	GetNoPermissionText() string
	GetPayAppId() string
	GetPayReasonFormat() string
	GetPoolSimulateCnt() uint32
	GetReissueAwardHour() uint32
	GetTestMode() bool
	GetTestPayUidList() []uint32
	GetTopDressAwardConf() *TopDressAwardConf
	GetTopExtraAwardConf() *TopExtraAwardConf
	NeedBingoBreakingNews() bool
	Reload(file string) error
	Watch(file string) 
}


type IBusinessConf interface {
	CheckConf() error
	Parse(configFile string) (isChange bool,err error)
}

