package activity_game

//go:generate quicksilver-cli test interface ../activity-game
//go:generate mockgen -destination=../mocks/activity-game.go -package=mocks golang.52tt.com/services/adventure-activity/internal/model/activity-game IActivityGame

import (
    "context"
    "fmt"
    "gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/protocol/common/status"
    "golang.52tt.com/services/adventure-activity/internal/conf"
    activity_conf "golang.52tt.com/services/adventure-activity/internal/model/activity-conf"
    "golang.52tt.com/services/adventure-activity/internal/model/activity-game/cache"
    "golang.52tt.com/services/adventure-activity/internal/model/activity-game/store"
    anti_corruption_layer "golang.52tt.com/services/adventure-activity/internal/model/anti-corruption-layer"
    "golang.52tt.com/services/adventure-activity/internal/model/comm"
    "golang.52tt.com/services/adventure-activity/internal/model/lottery"
    "sync"
    "time"
)

const (
    ChanceTBean = 2000
)

var (
    ErrTimeRangeOver       = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "time range over")
    ErrRequestParamInvalid = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    ErrDBError             = protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
    ErrCostDailyLimit      = protocol.NewExactServerError(nil, status.ErrCatCanteenUseChanceLimit, "今天营业已达到上限，注意休息哦")
)

type ActivityGame struct {
    store      store.IStore
    cache      cache.ICache
    bc         conf.IBusinessConfManager
    acLayerMgr anti_corruption_layer.IACLayer

    lottery    lottery.ILottery
    actConfMgr activity_conf.IActivityConf

    timerD   *timer.Timer
    wg       sync.WaitGroup
    shutDown chan struct{}
}

func NewMgr(s mysql.DBx, rds mysql.DBx, cacheClient redis.Cmdable, bc conf.IBusinessConfManager,
    acLayerMgr anti_corruption_layer.IACLayer, actConfMgr activity_conf.IActivityConf, lottery *lottery.Lottery) (*ActivityGame, error) {
    mysqlStore := store.NewStore(s, rds)
    redisCli := cache.NewCache(cacheClient)

    m := &ActivityGame{
        actConfMgr: actConfMgr,
        lottery:    lottery,
        store:      mysqlStore,
        cache:      redisCli,
        bc:         bc,
        shutDown:   make(chan struct{}),
        acLayerMgr: acLayerMgr,
    }

    err := m.startTimer()
    if err != nil {
        log.Errorf("NewMgr startTimer err:%v", err)
        return m, err
    }

    return m, nil
}

func (m *ActivityGame) Stop() {
    m.timerD.Stop()
    close(m.shutDown)
    m.wg.Wait()
    _ = m.cache.Close()
    _ = m.store.Close()
}

func (m *ActivityGame) genAwardOrderId(uid uint32, idx int, awardId string, isBuyPack bool) string {
    senderAppId, _ := m.bc.GetBackSenderInfo()

    if isBuyPack {
        return fmt.Sprintf("%d_P_%d_%s_%d_b", senderAppId, uid, awardId, time.Now().Unix())
    }
    return fmt.Sprintf("%d_P_%d_%s_%d_%d", senderAppId, uid, awardId, idx, time.Now().Unix())
}

func (m *ActivityGame) genDressOrderId(ty uint32, lotteryId, awardId string) string {
    senderAppId := m.bc.GetAwardCenterAppId()
    return fmt.Sprintf("%d_%s_%s_%d", senderAppId, lotteryId, awardId, ty)
}

// GetUserPlayFile 获取用户进度
func (m *ActivityGame) GetUserPlayFile(ctx context.Context, uid uint32) (*comm.UserPlayFile, uint32, error) {
    var userProgress *comm.UserPlayFile

    currLv, exist, err := m.store.GetUserCurrentLv(ctx, uid)
    if err != nil {
        return nil, 0, err
    }

    if !exist || currLv.LotteryId == "" {
        // 如果当前不存在用户记录，则为新玩家，返回首关默认值
        userProgress = &comm.UserPlayFile{
            UserN:     0,
            LevelId:   1,
            PlayCount: 0,
            PrizeMap:  make(map[string]uint32),
        }
        return userProgress, 0, nil
    }

    lotteryRecord, err := m.store.GetRecordByLotteryId(ctx, currLv.LotteryId, currLv.UpdateTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetRecordByLotteryId fail. lotteryId:%s, err:%v", currLv.LotteryId, err)
        return nil, 0, err
    }

    userProgress = &comm.UserPlayFile{}
    err = userProgress.FromJSONString(lotteryRecord.PlayFile)
    if err != nil {
        return nil, 0, err
    }

    return userProgress, currLv.UpdateVersion, nil
}

// GetUserPlayFileWithCache 获取用户进度
func (m *ActivityGame) GetUserPlayFileWithCache(ctx context.Context, uid uint32) (*comm.UserPlayFile, error) {
    // 从缓存中获取
    userPlayFile, exist, err := m.cache.GetUserPlayFile(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserPlayFileWithCache fail. uid:%d, err:%v", uid, err)
        return nil, err
    }
    if exist {
        return userPlayFile, nil
    }

    // 缓存不存在，从数据库中获取
    userProgress, _, err := m.GetUserPlayFile(ctx, uid)
    if err != nil {
        return nil, err
    }

    // 写入缓存
    _ = m.cache.SetUserPlayFile(ctx, uid, userProgress)
    return userProgress, nil
}

func (m *ActivityGame) checkRiskLimit(ctx context.Context, awardList []*comm.Prize) (map[string]string, error) {
    // todo
    return nil, nil
}

func (m *ActivityGame) checkChanceUseLimit(ctx context.Context, uid uint32, price uint32, id uint32) error {
    type2Limit, err := m.cache.GetPriceLimitCfg(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "checkChanceUseLimit fail to GetPriceLimitCfg. uid:%d, price:%d, err:%v", uid, price, err)
        return err
    }

    dailyLimit := type2Limit[comm.DailyUseLimitType]
    if dailyLimit > 0 {
        // 检查是否达到每日限制值
        currVal, err := m.cache.GetUserDailyPrice(ctx, uint32(time.Now().Day()), uid, comm.DailyUseLimitType)
        if err != nil {
            log.ErrorWithCtx(ctx, "checkChanceUseLimit fail to GetUserDailyPrice. uid:%d, price:%d, err:%v", uid, price, err)
            return ErrDBError
        }

        if currVal+price > dailyLimit {
            log.ErrorWithCtx(ctx, "checkChanceUseLimit fail. uid:%d, price:%d, currPrice(%d) > limit(%d)", uid, price, currVal+price, dailyLimit)
            return ErrCostDailyLimit
        }
    }

    return nil
}

func (m *ActivityGame) checkUserRemainChance(ctx context.Context, uid uint32, chanceAmount uint32) error {
    remain, err := m.GetUserRemain(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "checkUserRemainChance fail to GetUserRemainChance. uid:%+v, err:%v", uid, err)
        return err
    }

    if remain < chanceAmount {
        // 余额不足
        return protocol.NewExactServerError(nil, status.ErrCatCanteenChanceNotEnough) // 错误码
    }

    return nil
}

func (m *ActivityGame) GetUserRemain(ctx context.Context, uid uint32) (uint32, error) {
    remain, exist, err := m.cache.GetUserRemainChance(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserRemain fail. uid:%d, err:%v", uid, err)
        return 0, err
    }

    if exist {
        return remain, nil
    }

    remainStore, _, err := m.store.GetUserRemainChance(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserRemain fail. uid:%d, err:%v", uid, err)
        return 0, err
    }

    _ = m.cache.SetUserRemainChance(ctx, uid, remainStore.Amount)
    return remainStore.Amount, nil
}
