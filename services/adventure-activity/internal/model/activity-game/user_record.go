package activity_game

import (
    "context"
    pb "golang.52tt.com/protocol/services/adventure-activity"
    "golang.52tt.com/services/adventure-activity/internal/model/comm"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
)

func (m *ActivityGame) GetUserRecord(ctx context.Context, req *pb.GetUserAdventureRecordRequest) (*pb.GetUserAdventureRecordResponse, error) {
    out := &pb.GetUserAdventureRecordResponse{}
    sourceList := make([]uint32, 0)
    if req.GetRecordType() == uint32(pb.GetUserAdventureRecordRequest_RECORD_TYPE_CARD_LIGHTED) {
        sourceList = append(sourceList, comm.AwardTypeLighted)
    } else {
        sourceList = append(sourceList, comm.AwardTypeCompleted, comm.AwardTypeReachTop)
    }

    recordList, err := m.store.GetUserAwardRecords(ctx, req.GetUid(), req.GetLimit(), sourceList, req.GetOffset(), false)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserAwardRecords fail to GetUserAwardRecords. req:%+v, err:%v", req, err)
        return out, err
    }

    list := make([]*pb.DrawResult, 0)
    for _, v := range recordList {
        simpleInfo := m.actConfMgr.GetSimpleInfoByAwardId(v.AwardId)
        if simpleInfo == nil {
            log.WarnWithCtx(ctx, "GetSimpleInfoByAwardId. awardId:%s", v.AwardId)
            continue
        }
        list = append(list, &pb.DrawResult{
            LevelId: v.LevelId,
            AwardList: []*pb.AwardInfo{
                {
                    AwardId:      v.AwardId,
                    AwardType:    v.AwardType,
                    DressSubType: v.DressSubType,
                    Amount:       v.Amount,
                    AwardWorth:   v.AwardWorth,
                    AwardName:    simpleInfo.Name,
                    AwardDesc:    "",
                    AwardIcon:    simpleInfo.Icon,
                },
            },
            ResultType: v.Source,
        })
    }

    out.RecordList = list
    return out, nil
}

func (m *ActivityGame) GetPlatformWinningRecord(ctx context.Context, req *pb.GetPlatformWinningRecordRequest) (*pb.GetPlatformWinningRecordResponse, error) {
    out := &pb.GetPlatformWinningRecordResponse{}

    out = &pb.GetPlatformWinningRecordResponse{
        NewVersion:     "",
        RecordList:     []*pb.PlatformWinningRecord{},
        TopNUsed:       0,
        ReqDurationSec: 0,
    }
    return out, nil
}
