package cache

import(
	redis "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	time "time"
	context "context"
	comm "golang.52tt.com/services/adventure-activity/internal/model/comm"
)

type ICache interface {
	CheckIfFusing(ctx context.Context) (bool,error)
	CheckIfWarning(ctx context.Context, ty uint32) (bool,error)
	Close() error
	DelFusing(ctx context.Context) error
	DelHourProfit(ctx context.Context, hour int) error
	DelUserPlayFile(ctx context.Context, uid uint32) error
	DelUserRemainChance(ctx context.Context, uid uint32) error
	DelWarning(ctx context.Context, ty uint32) error
	GetHistoryProfit(ctx context.Context) (int64,error)
	GetHourProfit(ctx context.Context, hour int) (int64,error)
	GetPriceLimitCfg(ctx context.Context) (map[uint32]uint32,error)
	GetRedisClient() redis.Cmdable
	GetUserDailyPrice(ctx context.Context, day, uid, ty uint32) (uint32,error)
	GetUserPlayFile(ctx context.Context, uid uint32) (*comm.UserPlayFile,bool,error)
	GetUserRemainChance(ctx context.Context, uid uint32) (uint32,bool,error)
	IncrHistoryProfit(ctx context.Context, profit int64) (int64,error)
	IncrHourProfit(ctx context.Context, hour int, profit int64) (int64,error)
	IncrUserDailyPrice(ctx context.Context, day, uid, incr, ty uint32) error
	LockUserLottery(ctx context.Context, uid uint32, ttl time.Duration) (bool,error)
	SetFusing(ctx context.Context) error
	SetPriceLimitCfg(ctx context.Context, m map[uint32]uint32) error
	SetUserPlayFile(ctx context.Context, uid uint32, up *comm.UserPlayFile) error
	SetUserRemainChance(ctx context.Context, uid, remain uint32) error
	SetWarning(ctx context.Context, ty uint32) error
	UnlockUserLottery(ctx context.Context, uid uint32) error
}

