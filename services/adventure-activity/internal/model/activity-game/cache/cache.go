package cache

//go:generate quicksilver-cli test interface ../cache
//go:generate mockgen -destination=../mocks/cache.go -package=mocks golang.52tt.com/services/adventure-activity/internal/model/activity-game/cache ICache

import (
    "fmt"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
    "strconv"
    "time"
    "context"
)

const (
    ttl = 2 * 60 * 60
)

type Cache struct {
    cmder redis.Cmdable
}

func NewCache(client redis.Cmdable) *Cache {
    c := &Cache{
        cmder: client,
    }
    return c
}

func (c *Cache) Close() error { return nil }

func (c *Cache) GetRedisClient() redis.Cmdable {
    return c.cmder
}

func genUserRemainChanceKey(uid uint32) string {
    return fmt.Sprintf("adventure_act_remain_%d", uid)
}

func genPriceLimitConfKey() string {
    return "adventure_act_price_limit"
}

func genUserDailyPriceKey(uid, day uint32) string {
    return fmt.Sprintf("adventure_act_user_daily_price_%d_%d", uid, day)
}

func genUserLotteryLockKey(uid uint32) string {
    return fmt.Sprintf("adventure_act_lottery_lock_%d", uid)
}

func (c *Cache) SetUserRemainChance(ctx context.Context, uid, remain uint32) error {
    key := genUserRemainChanceKey(uid)
    return c.cmder.Set(ctx, key, remain, 24*time.Hour).Err()
}

func (c *Cache) DelUserRemainChance(ctx context.Context, uid uint32) error {
    key := genUserRemainChanceKey(uid)
    return c.cmder.Del(ctx, key).Err()
}

func (c *Cache) GetUserRemainChance(ctx context.Context, uid uint32) (uint32, bool, error) {
    key := genUserRemainChanceKey(uid)
    v, err := c.cmder.Get(ctx, key).Uint64()
    if err == redis.Nil {
        return 0, false, nil
    }
    return uint32(v), true, err
}

func (c *Cache) LockUserLottery(ctx context.Context, uid uint32, ttl time.Duration) (bool, error) {
    key := genUserLotteryLockKey(uid)
    return c.cmder.SetNX(ctx, key, "1", ttl).Result()
}

func (c *Cache) UnlockUserLottery(ctx context.Context, uid uint32) error {
    key := genUserLotteryLockKey(uid)
    return c.cmder.Del(ctx, key).Err()
}

func (c *Cache) SetPriceLimitCfg(ctx context.Context, m map[uint32]uint32) error {
    key := genPriceLimitConfKey()
    pam := make(map[string]interface{})
    for k, v := range m {
        pam[fmt.Sprint(k)] = v
    }
    return c.cmder.HMSet(ctx, key, pam).Err()
}

func (c *Cache) GetPriceLimitCfg(ctx context.Context) (map[uint32]uint32, error) {
    key := genPriceLimitConfKey()
    out := make(map[uint32]uint32)
    res, err := c.cmder.HGetAll(ctx, key).Result()
    if err != nil {
        return out, err
    }

    for k, v := range res {
        ty, _ := strconv.Atoi(k)
        val, _ := strconv.Atoi(v)
        out[uint32(ty)] = uint32(val)
    }

    return out, nil
}

func (c *Cache) IncrUserDailyPrice(ctx context.Context, day, uid, incr, ty uint32) error {
    key := genUserDailyPriceKey(uid, day)

    c.cmder.Expire(ctx, key, 24*time.Hour)
    return c.cmder.HIncrBy(ctx, key, fmt.Sprint(ty), int64(incr)).Err()
}

func (c *Cache) GetUserDailyPrice(ctx context.Context, day, uid, ty uint32) (uint32, error) {
    key := genUserDailyPriceKey(uid, day)
    v, err := c.cmder.HGet(ctx, key, fmt.Sprint(ty)).Uint64()
    if err == redis.Nil {
        return 0, nil
    }
    return uint32(v), err
}
