package cache

import (
    "golang.52tt.com/services/adventure-activity/internal/model/comm"
    "context"
    "fmt"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
)

func genUserPlayFileKey(uid uint32) string {
    return fmt.Sprintf("user_play_file_%d", uid)
}

func (c *Cache) SetUserPlayFile(ctx context.Context, uid uint32, up *comm.UserPlayFile) error {
    data, err := up.ToJSONString()
    if err != nil {
        return err
    }
    err = c.cmder.Set(ctx, genUserPlayFileKey(uid), data, ttl).Err()
    return err
}

func (c *Cache) GetUserPlayFile(ctx context.Context, uid uint32) (*comm.UserPlayFile, bool, error) {
    data, err := c.cmder.Get(ctx, genUserPlayFileKey(uid)).Result()
    if err != nil {
        if err == redis.Nil {
            return nil, false, nil
        }
        log.ErrorWithCtx(ctx, "GetUserPlayFile failed uid:%d", uid)
        return nil, false, err
    }
    up := &comm.UserPlayFile{}
    err = up.FromJSONString(data)
    if err != nil {
        return nil, false, err
    }
    return up, true, nil
}

// DelUserPlayFile 删除用户游戏数据
func (c *Cache) DelUserPlayFile(ctx context.Context, uid uint32) error {
    return c.cmder.Del(ctx, genUserPlayFileKey(uid)).Err()
}
