package store

import (
    "context"
    "fmt"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    "golang.52tt.com/pkg/log"
    "strings"
    "time"
)

const (
    AwardOrderStatusInit uint32 = iota
    AwardOrderStatusSuccess

    AwardPackSelectField = "id, lottery_id, order_id, uid, level_id,play_count, award_id, award_type,dress_sub_type, award_worth, amount, source, bingo_type, create_time"
)

func GenAwardPackTblIdx(t time.Time) string {
    return fmt.Sprintf("adventure_activity_award_%04d%02d", t.Year(), t.Month())
}

// CreateAwardPackTbl 包裹发放表
var CreateAwardPackTbl = `CREATE TABLE IF NOT EXISTS %s (
	id int unsigned NOT NULL AUTO_INCREMENT,
	lottery_id varchar(128) NOT NULL DEFAULT "" COMMENT '抽奖标识id',
	order_id varchar(128)  NOT NULL COMMENT '订单号',
	uid int(10) unsigned NOT NULL COMMENT 'uid',
	level_id int(10) unsigned NOT NULL COMMENT '关卡id',
    play_count int(10) unsigned NOT NULL COMMENT '游玩次数',
    
	award_id varchar(128) NOT NULL COMMENT '奖励id',
    award_type tinyint unsigned NOT NULL COMMENT '奖励类型',
    dress_sub_type int(10) unsigned NOT NULL COMMENT '装扮子类型，see award-center.proto EGiftType',
	award_worth int(10) unsigned NOT NULL COMMENT '奖励价值, 仅记录T豆价值',
	amount int(10) unsigned NOT NULL COMMENT '发放数量',
    
	status tinyint unsigned NOT NULL COMMENT '状态 0-未完成 1-完成',
	source tinyint unsigned NOT NULL COMMENT '来源 0-购买 1-点亮奖励 2-通关奖励 3-登顶奖励',
    bingo_type tinyint unsigned NOT NULL COMMENT '中奖类型 0- 未通关 1-概率通关 2-保底通关',
	create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

	PRIMARY KEY (id),
	UNIQUE KEY uniq_order_id (order_id),
	INDEX idx_uid_level (uid, level_id),
    INDEX idx_lottery_id (lottery_id),
	INDEX idx_create_time (create_time)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT "冒险岛奖励发放表";`

type AwardRecord struct {
    Id        uint32 `db:"id"`
    LotteryId string `db:"lottery_id"`
    OrderId   string `db:"order_id"`
    Uid       uint32 `db:"uid"`
    LevelId   uint32 `db:"level_id"`
    PlayCount uint32 `db:"play_count"`

    AwardId      string `db:"award_id"`
    AwardType    uint32 `db:"award_type"`
    DressSubType uint32 `db:"dress_sub_type"`
    AwardWorth   uint32 `db:"award_worth"`
    Amount       uint32 `db:"amount"`

    Status    uint32 `db:"status"`
    Source    uint32 `db:"source"`
    BingoType uint32 `db:"bingo_type"`

    CreateTime time.Time `db:"create_time"`
    UpdateTime time.Time `db:"update_time"`
}

func (s *Store) CreateAwardPackTbl(ctx context.Context, tx mysql.Txx, t time.Time) error {
    query := fmt.Sprintf(CreateAwardPackTbl, GenAwardPackTblIdx(t))
    _, err := tx.ExecContext(ctx, query)
    return err
}

func (s *Store) InsertAwardRecords(ctx context.Context, tx mysql.Txx, outsideTime time.Time, uid uint32, records []*AwardRecord) error {
    if len(records) == 0 {
        return nil
    }

    placeholder := make([]string, 0, len(records))
    params := make([]interface{}, 0)
    for _, r := range records {
        placeholder = append(placeholder, "(?,?,?,?,?,?,?,?,?,?,?,?,?)")
        params = append(params, r.LotteryId, r.OrderId, uid, r.LevelId, r.PlayCount, r.AwardId, r.AwardType, r.DressSubType, r.AwardWorth, r.Amount, r.Source, r.BingoType, outsideTime)
    }

    query := fmt.Sprintf("INSERT INTO %s(lottery_id, order_id, uid, level_id, play_count,award_id, award_type, dress_sub_type, award_worth, amount, source, bingo_type, create_time) VALUES %s",
        GenAwardPackTblIdx(outsideTime), strings.Join(placeholder, ","))

    _, err := tx.ExecContext(ctx, query, params...)
    if err != nil {
        if mysql.IsMySQLError(err, 1146) {
            // 表不存在，建表
            err = s.CreateAwardPackTbl(ctx, tx, outsideTime)
            if err != nil {
                log.ErrorWithCtx(ctx, "InsertAwardRecords fail to CreateAwardPackTbl. uid:%d, err:%v", uid, err)
                return err
            }

            _, err = tx.ExecContext(ctx, query, params...)
            if err == nil {
                return nil
            }
        }
        log.ErrorWithCtx(ctx, "InsertAwardRecords fail to insert. uid:%d, err:%v", uid, err)
        return err
    }

    return nil
}

func (s *Store) ChangeAwardRecordStatus(ctx context.Context, t time.Time, orderId string, status uint32) error {
    query := fmt.Sprintf(`UPDATE %s SET status=? WHERE order_id=?`, GenAwardPackTblIdx(t))
    _, err := s.db.ExecContext(ctx, query, status, orderId)
    if err != nil {
        log.ErrorWithCtx(ctx, "ChangeAwardRecordStatus db err:%s", err.Error())
        return err
    }
    log.InfoWithCtx(ctx, "ChangeAwardRecordStatus success. orderId:%s, status:%d", orderId, status)
    return nil
}

func (s *Store) GetAwardRecordByStatus(ctx context.Context, status, limit uint32, beginTime, endTime time.Time) ([]*AwardRecord, error) {
    query := fmt.Sprintf("SELECT %s FROM %s WHERE create_time >= ? AND create_time < ? AND status=? LIMIT ?",
        AwardPackSelectField, GenAwardPackTblIdx(beginTime))

    infos := make([]*AwardRecord, 0)
    err := s.readonlyDb.SelectContext(ctx, &infos, query, beginTime, endTime, status, limit)
    if err != nil {
        if mysql.IsMySQLError(err, 1146) && mysql.IsMySQLError(err, 1062) {
            return infos, nil
        }

        log.ErrorWithCtx(ctx, "GetAwardRecordByStatus db err:%s", err.Error())
        return infos, err
    }
    return infos, nil
}

func (s *Store) GetAwardRecordByLottery(ctx context.Context, uid uint32, lotteryIdList []string, t time.Time) ([]*AwardRecord, error) {
    infos := make([]*AwardRecord, 0)
    if len(lotteryIdList) == 0 {
        return infos, nil
    }
    strList := make([]string, 0, len(lotteryIdList))
    for _, str := range lotteryIdList {
        strList = append(strList, fmt.Sprintf("'%s'", str))
    }

    query := fmt.Sprintf("SELECT %s FROM %s WHERE lottery_id in (%s) AND uid=?",
        AwardPackSelectField, GenAwardPackTblIdx(t), strings.Join(strList, ","))

    err := s.readonlyDb.SelectContext(ctx, &infos, query, uid)
    if err != nil && !mysql.IsMySQLError(err, 1146) {
        log.ErrorWithCtx(ctx, "GetAwardRecordByLottery db err:%s", err.Error())
        return nil, err
    }
    return infos, nil
}

func (s *Store) GetUserAwardRecords(ctx context.Context, uid, limit uint32, sourceList []uint32, offset string, ascSort bool) ([]*AwardRecord, error) {
    now := time.Now()
    infos := make([]*AwardRecord, 0, limit)
    if limit == 0 {
        return infos, nil
    }

    restLen := limit
    logTime, logId, ok := ParseRecordOffsetId(offset)
    if !ok {
        logTime = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
        if ascSort {
            logTime = logTime.AddDate(0, -6, 0)
        }
    }

    sqlStr := "SELECT %s FROM %s WHERE uid = ?"

    for {
        params := make([]interface{}, 0)
        query := fmt.Sprintf(sqlStr, AwardPackSelectField, GenAwardPackTblIdx(logTime))
        params = append(params, uid)

        extQuery, extParams := genExtQuery(ascSort, logId, restLen)
        query += extQuery
        params = append(params, extParams...)

        query += fmt.Sprintf(" AND source in (%s)", genParamJoinStr(sourceList))

        err := s.readonlyDb.SelectContext(ctx, &infos, query, params...)
        if err != nil && !mysql.IsMySQLError(err, 1146) {
            log.ErrorWithCtx(ctx, "GetAwardRecordByStatus db err:%s", err.Error())
            return nil, err
        }

        if uint32(len(infos)) >= limit {
            // 数据取够了
            break
        }

        logId = 0
        logTime, ok = getNextLogTime(logTime, ascSort)
        if !ok {
            break
        }
    }

    return infos, nil
}

//
//type AwardStatistics struct {
//    LevelId      uint32 `db:"level_id"`
//    Source       uint32 `db:"source"`
//    PeopleCnt    int64  `db:"people_cnt"`
//    AwardCnt     int64  `db:"award_cnt"`
//    AwardWorth   int64  `db:"award_worth"`
//    BingoRandCnt int64  `db:"bingo_rand_cnt"`
//    BingoNCnt    int64  `db:"bingo_n_cnt"`
//    BingoWorth   int64  `db:"bingo_worth"`
//}
//
//// GetAwardStatistics 获取获奖数据统计
//func (s *Store) GetAwardStatistics(ctx context.Context, beginTime, endTime time.Time) (map[uint32]map[uint32]*AwardStatistics, error) {
//    out := make(map[uint32]map[uint32]*AwardStatistics)
//    list := make([]*AwardStatistics, 0, 15)
//
//    temp := "SELECT level_id, source, COUNT(DISTINCT uid) AS people_cnt, SUM(amount) AS award_cnt, SUM(award_worth*amount) AS award_worth, " +
//        "SUM(IF(bingo_type=1,amount,0)) AS bingo_rand_cnt, SUM(IF(bingo_type=2,amount,0)) AS bingo_n_cnt, " +
//        "SUM(IF(bingo_type>0,amount*award_worth,0)) AS bingo_worth FROM %s WHERE create_time >= ? AND create_time < ? GROUP BY level_id, source"
//
//    for i := uint32(0); i < AwardPackTblCnt; i++ {
//        query := fmt.Sprintf(temp, GenAwardPackTblIdx(beginTime, i))
//        err := s.readonlyDb.SelectContext(ctx, &list, query, beginTime, endTime)
//        if !mysql.IsMySQLError(err, 1146) {
//            log.ErrorWithCtx(ctx, "GetAwardStatistics fail. queryMonthTime:%v, err:%v", beginTime, err)
//            return out, err
//        }
//    }
//
//    for _, info := range list {
//        _, ok := out[info.LevelId]
//        if !ok {
//            out[info.LevelId] = make(map[uint32]*AwardStatistics)
//        }
//
//        stats, ok := out[info.LevelId][info.Source]
//        if !ok {
//            out[info.LevelId][info.Source] = info
//            continue
//        }
//
//        stats.PeopleCnt += info.PeopleCnt
//        stats.AwardCnt += info.AwardCnt
//        stats.AwardWorth += info.AwardWorth
//        stats.BingoRandCnt += info.BingoRandCnt
//        stats.BingoNCnt += info.BingoNCnt
//        stats.BingoWorth += info.BingoWorth
//    }
//
//    return out, nil
//}
//
//func (s *Store) GetAwardTotalCountInfo(ctx context.Context, beginTime, endTime time.Time) (*StCount, error) {
//    out := &StCount{}
//    temp := "SELECT COUNT(1) as count, IF(SUM(award_worth*amount),SUM(award_worth*amount),0) as worth FROM %s WHERE create_time >= ? AND create_time < ?"
//
//    for i := uint32(0); i < AwardPackTblCnt; i++ {
//        tmpCnt := &StCount{}
//        query := fmt.Sprintf(temp, GenAwardPackTblIdx(beginTime, i))
//        err := s.readonlyDb.GetContext(ctx, tmpCnt, query, beginTime, endTime)
//        if !mysql.IsMySQLError(err, 1146) {
//            log.ErrorWithCtx(ctx, "GetAwardTotalCountInfo fail. queryMonthTime:%v, err:%v", beginTime, err)
//            return out, err
//        }
//
//        out.Count += tmpCnt.Count
//        out.Worth += tmpCnt.Worth
//    }
//
//    return out, nil
//}
//
//func (s *Store) GetAwardOrderIds(ctx context.Context, beginTime, endTime time.Time) ([]string, error) {
//    list := make([]string, 0)
//    temp := "SELECT order_id FROM %s WHERE create_time >= ? AND create_time < ?"
//
//    for i := uint32(0); i < AwardPackTblCnt; i++ {
//        query := fmt.Sprintf(temp, GenAwardPackTblIdx(beginTime, i))
//        err := s.readonlyDb.SelectContext(ctx, &list, query, beginTime, endTime)
//        if !mysql.IsMySQLError(err, 1146) {
//            log.ErrorWithCtx(ctx, "GetAwardOrderIds fail. queryMonthTime:%v, err:%v", beginTime, err)
//            return list, err
//        }
//    }
//
//    return list, nil
//}
