package store

import (
    "fmt"
    "context"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    "time"
    "errors"
)

// 用户抽奖记录表
var createLotteryRecordTbl = `
CREATE TABLE  IF NOT EXISTS %s (
    id int unsigned NOT NULL AUTO_INCREMENT,
    uid int(10) unsigned NOT NULL COMMENT 'uid',
    lottery_id varchar(128) NOT NULL COMMENT '抽奖订单号',
    from_level_id int(10) unsigned NOT NULL COMMENT '关卡id',
    to_level_id int(10) unsigned NOT NULL COMMENT '关卡id',
    use_chance int(10) unsigned NOT NULL COMMENT '花费抽奖次数',
    user_n INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户冒险值',
    play_file varchar(1024) NOT NULL DEFAULT "" COMMENT '用户抽奖后的关卡进度，json格式', 
    
    create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY idx_lottery_id (lottery_id),
    KEY idx_uid (uid),
    KEY idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT "用户抽奖记录表";`

func GenLotteryRecordTblIdx(t time.Time) string {
    return fmt.Sprintf("adventure_activity_lottery_log_%04d%02d", t.Year(), t.Month())
}

// CreateLotteryRecordTbl 建表
func (s *Store) CreateLotteryRecordTbl(ctx context.Context, t time.Time) error {
    _, err := s.db.ExecContext(ctx, fmt.Sprintf(createLotteryRecordTbl, GenLotteryRecordTblIdx(t)))
    if err != nil {
        log.ErrorWithCtx(ctx, "CreateLotteryRecordTbl fail err %v", err)
        return err
    }
    return err
}

type LotteryRecord struct {
    Id          uint32 `db:"id"`
    Uid         uint32 `db:"uid"`
    LotteryId   string `db:"lottery_id"`
    FromLevelId uint32 `db:"from_level_id"`
    ToLevelId   uint32 `db:"to_level_id"`
    UseChance   uint32 `db:"use_chance"`
    UserN       uint32 `db:"user_n"`
    PlayFile    string `db:"play_file"`

    CreateTime time.Time `db:"create_time"`
}

// InsertLotteryRecord 创建抽奖记录
func (s *Store) InsertLotteryRecord(ctx context.Context, tx mysql.Txx, r *LotteryRecord, tbl time.Time) error {
    if tx == nil {
        log.ErrorWithCtx(ctx, "InsertLotteryRecord fail to insert. %+v, err: tx is nil", r)
        return errors.New("tx is nil")
    }

    query := fmt.Sprintf("INSERT INTO %s(uid,lottery_id,from_level_id,to_level_id,use_chance, user_n, play_file) "+
        "VALUES (?,?,?,?,?,?,?)", GenLotteryRecordTblIdx(tbl))

    _, err := tx.ExecContext(ctx, query, r.Uid, r.LotteryId, r.FromLevelId, r.ToLevelId, r.UseChance, r.UserN, r.PlayFile)
    if err != nil {
        if mysql.IsMySQLError(err, 1146) {
            // 表不存在，建表
            err = s.CreateLotteryRecordTbl(ctx, tbl)
            if err != nil {
                log.ErrorWithCtx(ctx, "InsertLotteryRecord fail to CreateLotteryRecordTbl. %+v, err:%v", r, err)
                return err
            }

            _, err = tx.ExecContext(ctx, query, r.Uid, r.LotteryId, r.FromLevelId, r.ToLevelId, r.UseChance, r.UserN, r.PlayFile)
            if err != nil {
                log.ErrorWithCtx(ctx, "InsertLotteryRecord fail to insert. %+v, err:%v", r, err)
                return err
            }

            return nil
        }

        log.ErrorWithCtx(ctx, "InsertLotteryRecord fail to insert. %+v, err:%v", r, err)
        return err
    }

    return nil
}

// GetRecordByLotteryId 根据LotteryId 获取记录
func (s *Store) GetRecordByLotteryId(ctx context.Context, lotteryId string, tbl time.Time) (*LotteryRecord, error) {
    r := &LotteryRecord{}

    query := fmt.Sprintf("SELECT id,uid,lottery_id,from_level_id,to_level_id,use_chance, user_n,play_file FROM %s "+
        "WHERE lottery_id=?", GenLotteryRecordTblIdx(tbl))
    err := s.db.GetContext(ctx, r, query, lotteryId)
    if err != nil {
        if mysql.IsNoRowsError(err) || mysql.IsMySQLError(err, 1146) {
            return r, nil
        }
        log.ErrorWithCtx(ctx, "GetLotteryRecordByLotteryId fail. lotteryId:%s, err:%v", lotteryId, err)
        return r, err
    }

    return r, nil
}
