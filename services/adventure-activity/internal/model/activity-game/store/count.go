package store

import (
    "context"
    "fmt"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
)

const (
    countTblName        = "adventure_activity_count"
    CountTypeTopReached = 1 // 登顶次数
)

var createCntTbl = `CREATE TABLE IF NOT EXISTS %s (
    id int unsigned NOT NULL AUTO_INCREMENT,
    count_type tinyint(1) NOT NULL DEFAULT 0 COMMENT '类型, 1-登顶次数',
    cnt int unsigned NOT NULL PRIMARY KEY COMMENT '次数',
   
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY idx_count_type (count_type),
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='次数记录表';`

func (s *Store) CreateCountTbl(ctx context.Context) error {
    _, err := s.db.ExecContext(ctx, createCntTbl, countTblName)
    if err != nil {
        log.ErrorWithCtx(ctx, "CreateCountTbl fail err %v", err)
        return err
    }

    return nil
}

type Count struct {
    CountType uint8  `db:"count_type"` // 类型, 1-登顶次数
    Cnt       uint32 `db:"cnt"`        // 登顶次数
}

// IncrCount 插入或更新登顶次数
func (s *Store) IncrCount(ctx context.Context, tx mysql.Txx, cntInfo *Count) (bool, error) {
    if cntInfo == nil || tx == nil {
        return false, fmt.Errorf("IncrCount failed, cntInfo or tx is nil")
    }

    query := fmt.Sprintf("INSERT INTO %s (count_type, cnt) VALUES (?, ?) ON DUPLICATE KEY UPDATE cnt = cnt + ?", countTblName)

    ret, err := tx.ExecContext(ctx, query, cntInfo.CountType, cntInfo.Cnt, cntInfo.Cnt)
    if err != nil {
        log.ErrorWithCtx(ctx, "IncrCount fail to insert. %+v, err:%v", cntInfo, err)
        return false, err
    }

    rowsAffected, _ := ret.RowsAffected()
    return rowsAffected > 0, nil
}

// GetCountByType 获取指定类型的计数
func (s *Store) GetCountByType(ctx context.Context, tx mysql.Txx, countType uint8) (*Count, error) {
    query := fmt.Sprintf("SELECT count_type, cnt FROM %s WHERE count_type = ?", countTblName)
    var err error
    var cnt Count

    if tx == nil {
        err = s.db.GetContext(ctx, &cnt, query, countType)
    } else {
        err = tx.GetContext(ctx, &cnt, query, countType)
    }

    if err != nil {
        if mysql.IsNoRowsError(err) {
            return &cnt, nil // 没有记录
        }
        log.ErrorWithCtx(ctx, "GetCountByType fail to GetContext. count_type: %d, err: %v", countType, err)
        return nil, err
    }

    return &cnt, nil
}
