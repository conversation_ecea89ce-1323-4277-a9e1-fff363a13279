package store

import (
    "time"
    "database/sql"
    "context"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "errors"
    "fmt"
)

const RemainChanceTblName = "adventure_activity_remain_chance"

var CreateRemainChanceTbl = `CREATE TABLE %s (
  id bigint unsigned NOT NULL AUTO_INCREMENT,
  uid int unsigned NOT NULL COMMENT 'uid',
  amount int unsigned NOT NULL COMMENT '数量',
  update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  PRIMARY KEY (id),
  UNIQUE KEY uid_key(uid)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;`

func (s *Store) CreateRemainChanceTbl(ctx context.Context) error {
    _, err := s.db.ExecContext(ctx, fmt.Sprintf(CreateRemainChanceTbl, RemainChanceTblName))
    return err
}

type Remain<PERSON>hance struct {
    Uid        uint32    `db:"uid"`
    Amount     uint32    `db:"amount"`
    UpdateTime time.Time `db:"update_time"`
}

func (s *Store) InsertUserRemainChance(ctx context.Context, remain *RemainChance) (bool, error) {

    query := fmt.Sprintf("INSERT INTO %s (uid,amount) VALUES (?,?)", RemainChanceTblName)

    _, err := s.db.ExecContext(ctx, query, remain.Uid, remain.Amount)
    if err != nil {
        // 数据表不存在,  尝试建表并重试
        if mysql.IsMySQLError(err, 1146) {
            err = s.CreateRemainChanceTbl(ctx)
            if err != nil {
                log.ErrorWithCtx(ctx, "InsertUserRemainChance fail to CreateRemainChanceTbl. %+v, err:%v", remain, err)
                return false, err
            }

            _, err = s.db.ExecContext(ctx, query, remain.Uid, remain.Amount)
        }

        if err != nil &&
            !mysql.IsDupEntryError(err) { // 记录已存在，不返回错误
            log.ErrorWithCtx(ctx, "InsertUserRemainChance fail to insert. %+v, err:%v", remain, err)
            return false, err
        }
    }

    return true, nil
}

var ErrNilTx = errors.New("sql.Tx is nil")

func (s *Store) IncrUserRemainChance(ctx context.Context, tx mysql.Txx, uid, incrAmount uint32) error {
    if tx == nil {
        return ErrNilTx
    }

    query := fmt.Sprintf("UPDATE %s SET amount=amount+? WHERE uid=?", RemainChanceTblName)

    _, err := tx.ExecContext(ctx, query, incrAmount, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "IncrUserRemainChance fail to insert. uid:%d, incrAmount:%d, err:%v", uid, incrAmount, err)
        return err
    }

    return nil
}

func (s *Store) DecrUserRemainChance(ctx context.Context, tx mysql.Txx, uid, decrAmount uint32) (bool, error) {
    if tx == nil {
        return false, ErrNilTx
    }

    query := fmt.Sprintf("UPDATE %s SET amount=amount-? WHERE uid=? AND amount>=?", RemainChanceTblName)

    r, err := tx.ExecContext(ctx, query, decrAmount, uid, decrAmount)
    if err != nil {
        log.ErrorWithCtx(ctx, "DecrUserRemainChance fail to update. uid:%d, decrAmount:%d, err:%v", uid, decrAmount, err)
        return false, err
    }

    rowsAffect, _ := r.RowsAffected()

    return rowsAffect > 0, nil
}

func (s *Store) GetUserRemainChanceForUpdate(ctx context.Context, tx mysql.Txx, uid uint32) (*RemainChance, bool, error) {
    if tx == nil {
        return nil, false, ErrNilTx
    }

    query := fmt.Sprintf("SELECT uid,amount,update_time FROM %s WHERE uid=? FOR UPDATE", RemainChanceTblName)

    remain := &RemainChance{}
    err := tx.GetContext(ctx, remain, query, uid)
    if err != nil {
        if mysql.IsMySQLError(err, 1146) || mysql.IsNoRowsError(err) {
            return remain, false, nil
        }
        log.ErrorWithCtx(ctx, "GetUserRemainChanceForUpdate fail. uid:%d, err:%v", uid, err)
        return remain, false, err
    }

    return remain, true, nil
}

func (s *Store) GetUserRemainChance(ctx context.Context, uid uint32) (*RemainChance, bool, error) {
    remain := &RemainChance{}

    query := fmt.Sprintf("SELECT uid,amount,update_time FROM %s WHERE uid=?", RemainChanceTblName)
    err := s.db.GetContext(ctx, remain, query, uid)
    if err != nil {
        if mysql.IsNoRowsError(err) {
            return remain, false, nil
        }
        log.ErrorWithCtx(ctx, "GetUserRemainChance fail. uid:%d, err:%v", uid, err)
        return remain, false, err
    }

    return remain, true, nil
}

func (s *Store) GetTotalRemainChance(ctx context.Context) (uint64, error) {
    cnt := uint64(0)
    query := fmt.Sprintf("SELECT count(1) FROM %s", RemainChanceTblName)
    err := s.readonlyDb.GetContext(ctx, &cnt, query)
    if err != nil {
        if err == sql.ErrNoRows {
            return 0, nil
        }
        log.ErrorWithCtx(ctx, "GetTotalRemainChance fail. err:%v", err)
        return 0, err
    }

    return cnt, nil
}
