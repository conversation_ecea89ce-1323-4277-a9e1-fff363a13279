package store

import(
	context "context"
	mysql "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	time "time"
)

type IStore interface {
	ChangeAwardRecordStatus(ctx context.Context, t time.Time, orderId string, status uint32) error
	ChangeConsumeRecordPayInfo(ctx context.Context, t time.Time, uid uint32, oldStatus []uint32, newStatus uint32, payOrderId, tBeanTimeStr string) (bool,error)
	ChangeConsumeRecordStatus(ctx context.Context, tx mysql.Txx, t time.Time, uid uint32, oldStatus []uint32, newStatus uint32, payOrderId string) (bool,error)
	Close() error
	CreateAwardPackTbl(ctx context.Context, tx mysql.Txx, t time.Time) error
	CreateConsumeLogTbl(ctx context.Context, t time.Time) error
	CreateCountTbl(ctx context.Context) error
	CreateLotteryRecordTbl(ctx context.Context, t time.Time) error
	CreateRemainChanceTbl(ctx context.Context) error
	CreateUserCurrentLvTbl(ctx context.Context) error
	DecrUserRemainChance(ctx context.Context, tx mysql.Txx, uid, decrAmount uint32) (bool,error)
	GetAwardRecordByLottery(ctx context.Context, uid uint32, lotteryIdList []string, t time.Time) ([]*AwardRecord,error)
	GetAwardRecordByStatus(ctx context.Context, status, limit uint32, beginTime, endTime time.Time) ([]*AwardRecord,error)
	GetConsumeOrderIds(ctx context.Context, beginTime, endTime time.Time) ([]string,error)
	GetConsumeRecordByPayId(ctx context.Context, t time.Time, payOrderId string) (*ConsumeRecord,bool,error)
	GetConsumeTotalCountInfo(ctx context.Context, beginTime, endTime time.Time) (*StCount,error)
	GetCountByType(ctx context.Context, tx mysql.Txx, countType uint8) (*Count,error)
	GetRecordByLotteryId(ctx context.Context, lotteryId string, tbl time.Time) (*LotteryRecord,error)
	GetTotalRemainChance(ctx context.Context) (uint64,error)
	GetUserAwardRecords(ctx context.Context, uid, limit uint32, sourceList []uint32, offset string, ascSort bool) ([]*AwardRecord,error)
	GetUserCurrentLv(ctx context.Context, uid uint32) (*UserCurrentLv,bool,error)
	GetUserRemainChance(ctx context.Context, uid uint32) (*RemainChance,bool,error)
	GetUserRemainChanceForUpdate(ctx context.Context, tx mysql.Txx, uid uint32) (*RemainChance,bool,error)
	IncrCount(ctx context.Context, tx mysql.Txx, cntInfo *Count) (bool,error)
	IncrUserRemainChance(ctx context.Context, tx mysql.Txx, uid, incrAmount uint32) error
	InsertAwardRecords(ctx context.Context, tx mysql.Txx, outsideTime time.Time, uid uint32, records []*AwardRecord) error
	InsertConsumeRecord(ctx context.Context, r *ConsumeRecord) error
	InsertLotteryRecord(ctx context.Context, tx mysql.Txx, r *LotteryRecord, tbl time.Time) error
	InsertUserCurrentLv(ctx context.Context, r *UserCurrentLv) error
	InsertUserRemainChance(ctx context.Context, remain *RemainChance) (bool,error)
	Transaction(ctx context.Context, f func(tx mysql.Txx) error) error
	UpdateUserCurrentLv(ctx context.Context, tx mysql.Txx, r *UserCurrentLv) (bool,error)
}

