package store

import (
    "context"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "fmt"
    "strings"
    "time"
    "strconv"
)

//go:generate quicksilver-cli test interface ../store
//go:generate mockgen -destination=../mocks/store.go -package=mocks golang.52tt.com/services/adventure-activity/internal/model/activity-game/store IStore

type Store struct {
    db, readonlyDb mysql.DBx
}

func NewStore(db, readonlyDb mysql.DBx) *Store {
    s := &Store{
        db:         db,
        readonlyDb: readonlyDb,
    }

    // 建表
    _ = s.CreateUserCurrentLvTbl(context.Background())
    _ = s.CreateRemainChanceTbl(context.Background())
    return s
}

func (s *Store) Close() error {
    return s.db.Close()
}

func (s *Store) Transaction(ctx context.Context, f func(tx mysql.Txx) error) error {
    tx, err := s.db.Beginx()
    if err != nil {
        log.ErrorWithCtx(ctx, "Transaction Beginx fail err %v", err)
        return err
    }

    err = f(tx)
    if err != nil {
        log.ErrorWithCtx(ctx, "Transaction fail err %v", err)
        _ = tx.Rollback()
        return err
    }

    return tx.Commit()
}

type StCount struct {
    Count int64 `db:"count"`
    Worth int64 `db:"worth"`
    Value int64 `db:"value"`
}

func genParamJoinStr(list []uint32) string {
    strList := make([]string, 0, len(list))
    for _, i := range list {
        strList = append(strList, fmt.Sprint(i))
    }

    return strings.Join(strList, ",")
}

func GenRecordOffsetId(t time.Time, id uint32) string {
    return fmt.Sprintf("%04d-%02d-%d", t.Year(), t.Month(), id)
}

func ParseRecordOffsetId(offset string) (t time.Time, id uint32, ok bool) {
    ok = false
    if offset == "" {
        return
    }

    strList := strings.Split(offset, "-")
    if len(strList) != 3 {
        return
    }

    year, _ := strconv.ParseUint(strList[0], 10, 32)
    month, _ := strconv.ParseUint(strList[1], 10, 32)
    logId, _ := strconv.ParseUint(strList[2], 10, 32)

    t = time.Date(int(year), time.Month(month), 1, 0, 0, 0, 0, time.Local)
    return t, uint32(logId), true
}

func genExtQuery(ascSort bool, logId, restLen uint32) (string, []interface{}) {
    params := make([]interface{}, 0)
    query := ""

    if ascSort {
        if logId > 0 {
            query += " AND id > ?"
            params = append(params, logId)
        }

        query += " ORDER BY id limit ?"
        params = append(params, restLen)

    } else {
        if logId > 0 {
            query += " AND id < ?"
            params = append(params, logId)
        }

        query += " ORDER BY id desc limit ?"
        params = append(params, restLen)
    }

    return query, params
}

func getNextLogTime(logTime time.Time, ascSort bool) (time.Time, bool) {
    now := time.Now()
    if ascSort {
        // 查下个月的
        logTime = logTime.AddDate(0, 1, 0)
    } else {
        // 查上个月的
        logTime = logTime.AddDate(0, -1, 0)
    }

    if logTime.AddDate(0, 6, 0).Before(now) || logTime.After(now) {
        // 只查近六个月的数据
        return logTime, false
    }

    return logTime, true
}
