package activity_game

import (
    "context"
    "fmt"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/protocol/common/status"
    pb "golang.52tt.com/protocol/services/adventure-activity"
    "golang.52tt.com/services/adventure-activity/internal/model/activity-game/store"
    "golang.52tt.com/services/adventure-activity/internal/model/comm"
    "golang.52tt.com/services/adventure-activity/internal/model/lottery"
    "golang.52tt.com/services/tt-rev/common/goroutineex"
    "google.golang.org/grpc/codes"
    "sort"
    "strconv"
    "time"
)

// LotteryDraw 抽奖
func (m *ActivityGame) LotteryDraw(ctx context.Context, in *pb.LotteryDrawRequest) (*pb.LotteryDrawResponse, error) {
    out := &pb.LotteryDrawResponse{}
    uid, levelId, amount := in.GetUid(), in.GetLevelId(), in.GetAmount()
    if uid == 0 || levelId == 0 || amount == 0 || amount > 100 {
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }

    pool, ok := m.lottery.GetPrizePool(levelId)
    if !ok || pool == nil {
        log.ErrorWithCtx(ctx, "LotteryDraw fail to GetPrizePool. in:%+v, err:pool not found", in)
        return out, protocol.NewExactServerError(codes.OK, status.ErrAdventureActivitySystemErr, "pool not found")
    }

    price := ChanceTBean * amount / 100

    // 防沉迷限制检查
    err := m.checkChanceUseLimit(ctx, uid, price, levelId)
    if err != nil {
        log.ErrorWithCtx(ctx, "LotteryDraw fail to checkChanceUseLimit. in:%+v, err:%v", in, err)
        return out, err
    }

    // 用户抽奖余额检查
    err = m.checkUserRemainChance(ctx, uid, amount)
    if err != nil {
        log.ErrorWithCtx(ctx, "LotteryDraw fail to checkUserRemainChance. in:%+v, err:%v", in, err)
        return out, err
    }

    // 全局锁
    ok, err = m.cache.LockUserLottery(ctx, uid, 8*time.Second)
    if err != nil {
        log.ErrorWithCtx(ctx, "LotteryDraw fail to LockUserLottery.  in:%+v, err:%v", in, err)
        return out, err
    }

    if !ok {
        return out, protocol.NewExactServerError(nil, status.ErrCatCanteenLotteryDrawTooQuick)
    }
    defer m.cache.UnlockUserLottery(ctx, uid)

    // 获取用户当前抽奖进度
    playFile, updateVersion, err := m.GetUserPlayFile(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "LotteryDraw fail to GetUserProgress.  in:%+v, err:%v", in, err)
        return out, err
    }

    if playFile.LevelId != in.GetLevelId() {
        log.ErrorWithCtx(ctx, "LotteryDraw fail to GetUserProgress.  in:%+v, err:levelId not match", in)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "levelId not match")
    }

    if updateVersion == 0 {
        // 记录不存在，先插入一条数据
        err = m.store.InsertUserCurrentLv(ctx, &store.UserCurrentLv{
            Uid:           uid,
            LevelId:       1,
            PlayCount:     1,
            UpdateVersion: updateVersion,
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "LotteryDraw fail to InsertUserCurrentLv.  in:%+v, err:%v", in, err)
            return out, err
        }
    }

    lotteryId := fmt.Sprintf("lot_%d_%d_%d", uid, time.Now().UnixMilli(), in.GetAmount())

    // 抽奖
    drawReq := lottery.PrizeDrawReq{
        Uid:          uid,
        LevelId:      playFile.LevelId,
        LotteryId:    lotteryId,
        ChannelId:    in.GetChannelId(),
        CurrPlayFile: playFile,
    }

    drawResp, err := m.lottery.PrizeDrawBatch(ctx, levelId, drawReq, amount)
    if err != nil {
        log.ErrorWithCtx(ctx, "LotteryDraw fail to PrizeDrawBatch.  in:%+v, err:%v", in, err)
        return out, err
    }

    // 风控检查
    _, err = m.checkRiskLimit(ctx, drawResp.AwardList)
    if err != nil {
        log.ErrorWithCtx(ctx, "LotteryDraw fail to checkRiskLimit. amount, err")
        return out, err
    }

    newPlayFileStr, err := drawResp.CurrPlayFile.ToJSONString()
    if err != nil {
        log.ErrorWithCtx(ctx, "LotteryDraw newPlayFileStr fail to ToJSONString.in:%+v err:%v", in, err)
        return out, err
    }

    now := time.Now()
    lotteryRecord := &store.LotteryRecord{
        Uid:         uid,
        LotteryId:   lotteryId,
        FromLevelId: playFile.LevelId,
        ToLevelId:   drawResp.CurrPlayFile.LevelId,
        UseChance:   in.GetAmount(),
        UserN:       drawResp.CurrPlayFile.UserN,
        PlayFile:    newPlayFileStr,
        CreateTime:  now,
    }

    awardList := make([]*store.AwardRecord, 0, len(drawResp.AwardList))
    for idx, award := range drawResp.AwardList {
        r := &store.AwardRecord{
            LotteryId:    lotteryId,
            OrderId:      m.genAwardOrderId(uid, idx, award.PrizeId, false),
            Uid:          uid,
            LevelId:      award.LevelId,
            PlayCount:    award.PlayCount,
            AwardId:      award.PrizeId,
            AwardType:    award.PrizeType,
            DressSubType: award.DressSubType,
            AwardWorth:   award.PrizePrice,
            Amount:       award.Amount, // 一般只是1，看看这里要不要直接写死
            Source:       award.Source,
            BingoType:    award.BingoType,
            CreateTime:   now,
        }
        log.DebugWithCtx(ctx, "LotteryDraw newPlayFileStr. r:%+v", r)
        awardList = append(awardList, r)
    }

    resultList := m.genLotteryDrawResultList(ctx, drawResp.AwardList)
    var topReachedRet *pb.DrawResult
    for _, res := range resultList {
        if res.ResultType == uint32(pb.DrawResultType_DRAW_RESULT_PEAK_REACHED) {
            topReachedRet = res // 登顶结果
        }
    }

    if topReachedRet != nil {
        // 处理登顶虚拟奖励
        topUpAwardList, err := m.handleTopUpDressAward(ctx, uid, lotteryId, topReachedRet, drawResp.CurrPlayFile)
        if err != nil {
            log.ErrorWithCtx(ctx, "LotteryDraw fail to handleTopUpDressAward. in:%+v, err:%v", in, err)
            return out, err
        }
        // 合并登顶奖励
        awardList = append(awardList, topUpAwardList...)
    }

    // 用户新进度
    newPlayFile := &store.UserCurrentLv{
        Uid:           uid,
        LevelId:       drawResp.CurrPlayFile.LevelId,
        PlayCount:     drawResp.CurrPlayFile.PlayCount,
        LotteryId:     lotteryId,
        UpdateVersion: updateVersion,
    }

    var remainChance uint32
    // 事务提交：扣除抽奖机会，插入奖励记录、更新用户进度
    err = m.store.Transaction(ctx, func(tx mysql.Txx) error {
        // 登顶了
        if topReachedRet != nil {
            // 处理登顶加码奖励
        }

        remainChance, err = m.lotteryTransactionFunc(ctx, tx, uid, amount, now, awardList, lotteryRecord, newPlayFile)
        return err
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "LotteryDraw fail to Transaction.  in:%+v, err:%v", in, err)
        return out, err
    }

    // 组装抽奖结果
    out = &pb.LotteryDrawResponse{
        ResultList: resultList,
        UserPlayFile: &pb.UserPlayFile{
            LevelId:        newPlayFile.LevelId,
            CurrentChance:  remainChance,
            UserN:          drawResp.CurrPlayFile.UserN,
            CardCollection: drawResp.CurrPlayFile.PrizeMap,
        },
    }

    // afterLotteryHandle ：全服、助手消息、大奖写入轮播缓存等异步处理

    goroutineex.GoroutineWithTimeoutCtx(ctx, time.Second*5, func(ctx context.Context) {
        _ = m.cache.DelUserPlayFile(ctx, uid)
        _ = m.cache.DelUserRemainChance(ctx, uid)
    })

    return out, nil
}

func (m *ActivityGame) genLotteryDrawResultList(ctx context.Context, prizeList []*comm.Prize) []*pb.DrawResult {
    resultList := make([]*pb.DrawResult, 0)
    if len(prizeList) == 0 {
        log.WarnWithCtx(ctx, "genLotteryDrawResultList fail. prizeList is empty")
        return resultList
    }

    lv2RetTy2Awards := make(map[uint32]map[uint32][]*pb.AwardInfo)
    for _, prize := range prizeList {
        awardInfo := m.genResultAwardInfo(ctx, prize)
        if _, ok := lv2RetTy2Awards[prize.LevelId]; !ok {
            lv2RetTy2Awards[prize.LevelId] = make(map[uint32][]*pb.AwardInfo)
        }
        if _, ok := lv2RetTy2Awards[prize.LevelId][prize.ResultType]; !ok {
            lv2RetTy2Awards[prize.LevelId][prize.ResultType] = make([]*pb.AwardInfo, 0)
        }
        lv2RetTy2Awards[prize.LevelId][prize.ResultType] = append(lv2RetTy2Awards[prize.LevelId][prize.ResultType], awardInfo)
    }

    for levelId, retTy2Awards := range lv2RetTy2Awards {
        for retTy, awards := range retTy2Awards {
            resultList = append(resultList, &pb.DrawResult{
                LevelId:    levelId,
                AwardList:  awards,
                ResultType: retTy,
            })
        }
    }

    // 按照LevelId, ResultType排序
    sort.SliceStable(resultList, func(i, j int) bool {
        if resultList[i].LevelId != resultList[j].LevelId {
            return resultList[i].LevelId < resultList[j].LevelId
        }
        return resultList[i].ResultType < resultList[j].ResultType
    })

    return resultList
}

// lotteryTransactionFunc 抽奖事务处理
func (m *ActivityGame) lotteryTransactionFunc(ctx context.Context, tx mysql.Txx, uid, amount uint32, outsideTime time.Time,
    awardList []*store.AwardRecord, lotteryRecord *store.LotteryRecord, userPlayFile *store.UserCurrentLv) (remain uint32, err error) {

    // 获取用户当余额
    remainChance, exist, err := m.store.GetUserRemainChanceForUpdate(ctx, tx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "lotteryTransactionFunc GetUserRemainChanceForUpdate fail. uid:%d, err:%v", uid, err)
        return 0, err
    }

    if !exist || remainChance.Amount < amount {
        log.ErrorWithCtx(ctx, "lotteryTransactionFunc fail. remain not enough. uid:%d, amount:%d", uid, amount)
        return remain, protocol.NewExactServerError(codes.OK, status.ErrOnePieceRemainChanceNotEnough, "剩余道具不足") // todo 错误码更新
    }

    // 更新用户进度
    ok, err := m.store.UpdateUserCurrentLv(ctx, tx, userPlayFile)
    if err != nil {
        log.ErrorWithCtx(ctx, "lotteryTransactionFunc fail. update user current lv fail. uid:%d, err:%+v", uid, err)
        return remain, protocol.NewExactServerError(codes.OK, status.ErrOnePieceNotAvailable, "系统错误") // todo 错误码
    }

    if !ok {
        log.ErrorWithCtx(ctx, "lotteryTransactionFunc fail. update user current lv fail. uid:%d", uid)
        return remain, protocol.NewExactServerError(codes.OK, status.ErrOnePieceNotAvailable, "操作太快啦~请重试") // todo 错误码
    }

    // 插入抽奖记录流水
    err = m.store.InsertLotteryRecord(ctx, tx, lotteryRecord, outsideTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "lotteryTransactionFunc fail. insert lottery record fail. uid:%d, err:%+v", uid, err)
        return remain, protocol.NewExactServerError(codes.OK, status.ErrOnePieceNotAvailable, "系统错误") // todo 错误码
    }

    // 插入奖励记录
    err = m.store.InsertAwardRecords(ctx, tx, outsideTime, uid, awardList)
    if err != nil {
        log.ErrorWithCtx(ctx, "lotteryTransactionFunc fail. insert award records fail. uid:%d, err:%+v", uid, err)
        return 0, protocol.NewExactServerError(codes.OK, status.ErrOnePieceNotAvailable, "系统错误") // todo 错误码
    }

    // 扣除抽奖机会
    ok, err = m.store.DecrUserRemainChance(ctx, tx, uid, amount)
    if err != nil {
        log.ErrorWithCtx(ctx, "lotteryTransactionFunc fail. decr user remain chance fail. uid:%d, err:%+v", uid, err)
        return remain, protocol.NewExactServerError(codes.OK, status.ErrOnePieceNotAvailable, "系统错误") // todo 错误码
    }

    if !ok {
        log.ErrorWithCtx(ctx, "lotteryTransactionFunc fail. decr user remain chance fail. uid:%d, err:%+v", uid, err)
        return remain, protocol.NewExactServerError(codes.OK, status.ErrOnePieceRemainChanceNotEnough, "剩余道具不足") // todo 错误码
    }

    return remainChance.Amount - amount, nil
}

// topReachedTxFunc 处理登顶事务
func (m *ActivityGame) topReachedTxFunc(ctx context.Context, tx mysql.Txx, uid uint32) error {

    return nil
}

func (m *ActivityGame) genResultAwardInfo(ctx context.Context, award *comm.Prize) *pb.AwardInfo {
    var finTime int64
    if award.PrizeType == uint32(pb.AwardType_AWARD_TYPE_PACKAGE) {
        bgId, _ := strconv.Atoi(award.PrizeId)
        finTime = m.actConfMgr.GetPackageFinTime(ctx, uint32(bgId))
    } else if award.PrizeType == uint32(pb.AwardType_AWARD_TYPE_DRESS) {
        // todo
        finTime = 0
    }
    return &pb.AwardInfo{
        AwardId:      award.PrizeId,
        AwardType:    award.PrizeType,
        DressSubType: award.DressSubType,
        Amount:       award.Amount,
        AwardWorth:   award.PrizePrice,
        AwardName:    award.PrizeName,
        AwardDesc:    "",
        AwardIcon:    award.PrizeIcon,
        ExpTime:      finTime,
        //Weight:       0,
        CardId: award.CardIdOrder,
    }
}

// handleTopUpDressAward 处理登顶额外装扮奖励
func (m *ActivityGame) handleTopUpDressAward(ctx context.Context, uid uint32, lotteryId string, topReachedRet *pb.DrawResult, curr *comm.UserPlayFile) ([]*store.AwardRecord, error) {
    if curr == nil || topReachedRet == nil {
        log.WarnWithCtx(ctx, "handleTopUpDressAward fail. currUserPlayFile or topReachedRet is nil")
        return nil, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "参数错误")
    }

    dressCfg := m.bc.GetTopDressAwardConf()
    if dressCfg == nil || len(dressCfg.DressList) == 0 {
        log.DebugWithCtx(ctx, "handleTopUpDressAward fail. dressCfg is nil")
        return nil, nil
    }

    if curr.PlayCount > dressCfg.LimitCount {
        log.DebugWithCtx(ctx, "handleTopUpDressAward fail. curr.PlayCount:%d > dressCfg.LimitCount:%d", curr.PlayCount, dressCfg.LimitCount)
        return nil, nil
    }

    // 生成登顶奖励
    awardList := make([]*store.AwardRecord, 0, len(dressCfg.DressList))
    for _, dress := range dressCfg.DressList {
        award := &store.AwardRecord{
            LotteryId:    lotteryId,
            OrderId:      m.genDressOrderId(dress.DressType, lotteryId, dress.DressId),
            Uid:          uid,
            LevelId:      topReachedRet.GetLevelId(),
            PlayCount:    curr.PlayCount - 1,
            AwardId:      dress.DressId,
            AwardType:    uint32(pb.AwardType_AWARD_TYPE_DRESS),
            DressSubType: dress.DressType,
            Amount:       dress.DurationDay,
            Source:       topReachedRet.GetResultType(),
            CreateTime:   time.Now(),
        }
        awardList = append(awardList, award)

        topReachedRet.AwardList = append(topReachedRet.AwardList, &pb.AwardInfo{
            AwardId:      award.AwardId,
            AwardType:    award.AwardType,
            DressSubType: award.DressSubType,
            Amount:       award.Amount,
        })
    }

    return awardList, nil
}
