package activity_game

import (
    "golang.52tt.com/pkg/protocol"
    "time"
    "golang.52tt.com/services/adventure-activity/internal/model/comm"
    "strconv"
    "fmt"
    "golang.52tt.com/services/adventure-activity/internal/model/activity-game/store"
    pb "golang.52tt.com/protocol/services/adventure-activity"
    "golang.52tt.com/services/tt-rev/common/goroutineex"
    "golang.52tt.com/pkg/log"
    "context"
    "github.com/pkg/errors"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    "golang.52tt.com/protocol/common/status"
    UnifiedPayCallback "golang.52tt.com/protocol/services/unified_pay/cb"
    "google.golang.org/grpc/codes"
)

// BuyChance 购买抽奖机会
// return remain_chance, balance, payOrderId, err
func (m *ActivityGame) BuyChance(ctx context.Context, in *pb.BuyChanceRequest) (*pb.BuyChanceResponse, string, error) {
    out := &pb.BuyChanceResponse{}
    uid := in.GetUid()
    if in.GetChanceAmount() == 0 || in.GetChanceAmount() > 10000 || in.GetFee() == 0 {
        return out, "", protocol.NewExactServerError(nil, status.ErrOnePieceBuyChanceLimit)
    }

    // 反向校验
    if in.GetFee()/in.GetChanceAmount() != ChanceTBean {
        log.ErrorWithCtx(ctx, "BuyChance fail. price ErrRequestParamInvalid, in:%+v", in)
        return out, "", protocol.NewExactServerError(nil, status.ErrOnePieceBuyChanceLimit)
    }

    now := time.Now()
    outsideTime := time.Unix(now.Unix(), 0) // 去除 nsec

    // 时间相差过大
    if outsideTime.Add(20 * time.Minute).Before(now) {
        log.ErrorWithCtx(ctx, "BuyChance fail. outsideTime ErrRequestParamInvalid, in:%+v", in)
        return out, "", protocol.NewExactServerError(nil, status.ErrOnePieceBuyChanceLimit)
    }

    price := in.GetFee() / 100

    type2Limit, err := m.cache.GetPriceLimitCfg(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "BuyChance fail to GetPriceLimitCfg. in:%+v, err:%v", in, err)
        return out, "", err
    }

    singleLimit := type2Limit[comm.SingleBuyLimitType]
    if singleLimit > 0 && price > singleLimit {
        log.ErrorWithCtx(ctx, "BuyChance fail. in:%+v, singlePrice(%d) > singleLimit(%d)", in, price, singleLimit)
        return out, "", protocol.NewExactServerError(nil, status.ErrOnePieceBuyChanceLimit, "触发单笔购买上限，请理性消费哦~")
    }

    dailyLimit := type2Limit[comm.DailyBuyLimitType]
    if dailyLimit > 0 {
        // 检查是否达到每日限制值
        currVal, err := m.cache.GetUserDailyPrice(ctx, uint32(now.Day()), uid, comm.DailyBuyLimitType)
        if err != nil {
            log.ErrorWithCtx(ctx, "BuyChance fail to GetUserDailyPrice. in:%+v, err:%v", in, err)
            return out, "", err
        }

        if currVal+price > dailyLimit {
            log.ErrorWithCtx(ctx, "BuyChance fail. in:%+v, currPrice(%d) > limit(%d)", in, currVal+price, dailyLimit)
            return out, "", protocol.NewExactServerError(nil, status.ErrOnePieceBuyChanceLimit, "今日购买航海船已达上限，请明日再来哦~")
        }
    }

    chancePackId := m.bc.GetChancePackId()
    awardOrderId := m.genAwardOrderId(uid, 0, strconv.Itoa(int(chancePackId)), true)
    payOrderId := fmt.Sprintf("AA_PAY_%d_%d", uid, now.UnixNano())

    // add chance
    r := &store.ConsumeRecord{
        Uid:          uid,
        Amount:       in.GetChanceAmount(),
        Fee:          in.GetFee(),
        PayOrderId:   payOrderId,
        AwardOrderId: awardOrderId,
        Status:       store.ConsumeStatusInit,
        CreateTime:   outsideTime,
    }
    // 插入消费购买记录
    err = m.store.InsertConsumeRecord(ctx, r)
    if err != nil {
        log.ErrorWithCtx(ctx, "BuyChance fail to InsertConsumeRecord. %+v, err:%v", r, err)
        return out, "", err
    }

    // 冻结
    restBalance, err := m.freezeTBean(ctx, payOrderId, uid, in.GetFee(), outsideTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "BuyChance fail to freezeTBean. %+v, err:%v", r, err)
        return out, "", err
    }

    finalAmount, err := m.addUserChance(ctx, r)
    if err != nil {
        log.ErrorWithCtx(ctx, "BuyChance fail to addUserChance. in:%+v, err:%v", in, err)
        return out, "", err
    }

    for _, id := range m.bc.GetTestPayUidList() {
        if id == uid {
            return out, "", errors.New("test pay err,uncommit rollback")
        }
    }

    // 确认消费
    err = m.commitPayment(ctx, payOrderId, uid, in.GetFee(), chancePackId, in.GetChanceAmount())
    if err != nil {
        log.ErrorWithCtx(ctx, "BuyChance fail to commitPayment. in:%+v, err:%v", in, err)
        return out, "", err
    }

    // 记录每次购买金额缓存
    _ = m.cache.IncrUserDailyPrice(ctx, uint32(now.Day()), uid, price, comm.DailyBuyLimitType)

    // 异步发包裹
    goroutineex.GoroutineWithTimeoutCtx(ctx, 3*time.Second, func(ctx context.Context) {
        m.awardPackage(ctx, uid, price, chancePackId, outsideTime, awardOrderId)
    })

    out.Balance = restBalance
    out.FinalChanceAmount = finalAmount

    log.Infof("BuyChance in:%+v, payOrderId:%s, awardOrderId:%s, out:%+v", in, payOrderId, awardOrderId, out)
    return out, payOrderId, nil
}

// freezeTBean 冻结 T豆并更新状态
func (m *ActivityGame) freezeTBean(ctx context.Context, payOrderId string, uid uint32, fee uint32, outsideTime time.Time) (uint64, error) {
    // 冻结 T豆
    restBalance, err := m.acLayerMgr.PayFreeze(ctx, payOrderId, uid, fee, outsideTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "BuyChance fail to freeze. payOrderId:%s, err:%v", payOrderId, err)
        return 0, err
    }

    // 更新订单状态至冻结状态
    err = m.store.Transaction(ctx, func(tx mysql.Txx) error {
        ok, e := m.store.ChangeConsumeRecordStatus(ctx, tx, outsideTime, uid, []uint32{store.ConsumeStatusInit}, store.ConsumeStatusFreezing, payOrderId)
        if !ok {
            log.ErrorWithCtx(ctx, "BuyChance fail to change consume record status. payOrderId:%s, err:%v", payOrderId, e)
            return errors.New("change consume order status is invalid")
        }
        return e
    })

    if err != nil {
        return 0, err
    }

    return uint64(restBalance), nil
}

// commitPayment 确认消费并更新状态
func (m *ActivityGame) commitPayment(ctx context.Context, payOrderId string, uid uint32, fee uint32, chancePackId uint32, amount uint32) error {
    // 确认消费
    timeStr, dealToken, err := m.acLayerMgr.PayTBeanCommit(ctx, payOrderId, uid, fee, chancePackId, amount)
    if err != nil {
        log.ErrorWithCtx(ctx, "BuyChance fail to commit. payOrderId:%s, err:%v", payOrderId, err)
        return err
    }

    for _, id := range m.bc.GetTestPayUidList() {
        if id == uid {
            return errors.New("test pay err,commited, try commit more")
        }
    }

    if dealToken != "" {
        // 更新消费订单状态为已完成
        _, err = m.store.ChangeConsumeRecordPayInfo(ctx, time.Now(), uid, []uint32{store.ConsumeStatusAddedChance}, store.ConsumeStatusDone, payOrderId, timeStr)
        if err != nil {
            log.ErrorWithCtx(ctx, "BuyChance fail to ChangeConsumeRecordPayInfo. payOrderId:%s, err:%v", payOrderId, err)
            return err
        }
    }

    return nil
}

// 发放包裹奖励并更新发奖订单状态
func (m *ActivityGame) awardPackage(ctx context.Context, uid, amount, packId uint32, awardTime time.Time, orderId string) {
    err := m.acLayerMgr.AwardPackage(ctx, uid, packId, amount, awardTime.Unix(), orderId)
    if err != nil {
        log.ErrorWithCtx(ctx, "ReissueAwardHandle fail to AwardPackage. err:%v", err)
        return
    }

    // 更新发奖订单状态
    err = m.store.ChangeAwardRecordStatus(ctx, awardTime, orderId, store.AwardOrderStatusSuccess)
    if err != nil {
        log.ErrorWithCtx(ctx, "ReissueAwardHandle fail to ChangeAwardRecordStatus. err:%v", err)
        return
    }
}

// return finalChanceAmount , err
func (m *ActivityGame) addUserChance(ctx context.Context, r *store.ConsumeRecord) (finalAmount uint32, err error) {
    now := time.Now()
    uid, amount := r.Uid, r.Amount
    if r.Amount == 0 || r.PayOrderId == "" || r.AwardOrderId == "" {
        return 0, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }
    if r.Fee/r.Amount != ChanceTBean {
        log.ErrorWithCtx(ctx, "addUserChance fail. ErrRequestParamInvalid, %+v", r)
        return 0, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }

    // 不管3721先 insert 一条数量为0的默认数据
    // 防止后面事务中 对不存在的数据执行 select...for update 造成死锁
    _, err = m.store.InsertUserRemainChance(ctx, &store.RemainChance{Uid: uid})
    if err != nil {
        log.ErrorWithCtx(ctx, "addUserChance fail to InsertUserRemainChance. %+v, err:%v", r, err)
        return 0, err
    }

    // 事务开始
    err = m.store.Transaction(ctx, func(tx mysql.Txx) error {
        // 获取用户剩余数量记录 （带锁  for update)
        remain, exist, err := m.store.GetUserRemainChanceForUpdate(ctx, tx, uid)
        if err != nil {
            log.ErrorWithCtx(ctx, "addUserChance fail to GetUserRemainChanceForUpdate. %+v, err:%v", r, err)
            return err
        }

        if !exist {
            // 数据记录不存在，报错
            log.ErrorWithCtx(ctx, "addUserChance fail to GetUserRemainChanceForUpdate. %+v, err: record not exist", r)
            return protocol.NewExactServerError(nil, status.ErrSys)

        } else {
            // 增加用户剩余数量
            err = m.store.IncrUserRemainChance(ctx, tx, uid, amount)
            if err != nil {
                log.ErrorWithCtx(ctx, "addUserChance fail to IncrUserRemainChance. %+v, err:%v", r, err)
                return err
            }
        }

        // 更新状态
        ok, err := m.store.ChangeConsumeRecordStatus(ctx, tx, r.CreateTime, uid, []uint32{store.ConsumeStatusFreezing}, store.ConsumeStatusAddedChance, r.PayOrderId)
        if err != nil {
            log.ErrorWithCtx(ctx, "addUserChance fail to ChangeConsumeRecordStatus. %+v, err:%v", r, err)
            return err
        }

        if !ok {
            return protocol.NewExactServerError(nil, status.ErrOnePieceBuyChanceLimit)
        }

        finalAmount = remain.Amount + amount
        // 插入奖励记录
        err = m.store.InsertAwardRecords(ctx, tx, r.CreateTime, uid, []*store.AwardRecord{
            {
                //LotteryId:    "",
                OrderId:   r.AwardOrderId,
                Uid:       uid,
                AwardId:   fmt.Sprintf("%d", m.bc.GetChancePackId()),
                AwardType: 1,
                Amount:    amount,
                Source:    comm.AwardTypeBuyChance,
            },
        })

        return err
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "addUserChance fail to Transaction. %+v, err:%v", r, err)
        return
    }

    // 清空缓存
    _ = m.cache.DelUserRemainChance(ctx, uid)

    log.InfoWithCtx(ctx, "addUserChance %+v, finalAmount:%d, cost:%v", r, finalAmount, time.Since(now))
    return finalAmount, nil
}

// PayCallBackHandler T豆回调handle
func (m *ActivityGame) PayCallBackHandler(ctx context.Context, orderId string) (*UnifiedPayCallback.PayNotifyResponse, error) {
    out := &UnifiedPayCallback.PayNotifyResponse{}

    now := time.Now()

    // 获取消费订单信息
    order, exist, err := m.store.GetConsumeRecordByPayId(ctx, now, orderId)
    if err != nil {
        log.ErrorWithCtx(ctx, "Callback fail to GetConsumeRecordByPayId. orderId:%v, err:%v", orderId, err)
        return out, err
    }

    if !exist {
        lastMonthTime := getLastMonthTime(now)
        // 查上月
        order, exist, err = m.store.GetConsumeRecordByPayId(ctx, lastMonthTime, orderId)
        if err != nil {
            log.ErrorWithCtx(ctx, "Callback fail to GetConsumeRecordByPayId. orderId:%v, err:%v", orderId, err)
            return out, err
        }
    }

    if !exist {
        // 不可能订单不存在的，有问题，返回错误
        log.ErrorWithCtx(ctx, "Callback fail to GetMagicSpiritOrder. orderId:%v, err:%v", orderId, "order not exist")
        return out, protocol.NewExactServerError(codes.OK, status.ErrMagicSpiritOrderNotExist)
    }

    if order.CreateTime.Add(5 * time.Minute).After(now) {
        // 回调时机有问题，返回错误
        log.ErrorWithCtx(ctx, "Callback fail. orderId:%v, err:%v", orderId, "callback too early")
        return out, protocol.NewExactServerError(nil, status.ErrMagicSpiritOrderNotExist)
    }

    outsideTime := order.CreateTime
    orderStatus := order.Status
    var op UnifiedPayCallback.Op
    if orderStatus == store.ConsumeStatusDone || orderStatus == store.ConsumeStatusAddedChance {
        err := m.commitPayment(ctx, orderId, order.Uid, order.Fee, m.bc.GetChancePackId(), order.Amount)
        if err != nil {
            log.ErrorWithCtx(ctx, "Callback fail to commit. orderId:%+v, err:%v", orderId, err)
            return out, err
        }

        op = UnifiedPayCallback.Op_COMMIT

    } else {
        needUpdateStatus := orderStatus != store.ConsumeStatusRollback
        err := m.rollback(ctx, orderId, order.Uid, outsideTime, needUpdateStatus)
        if err != nil {
            log.ErrorWithCtx(ctx, "Callback fail to rollback. orderId:%+v, err:%v", orderId, err)
            return out, err
        }

        op = UnifiedPayCallback.Op_ROLLBACK
    }

    out.Op = op
    log.InfoWithCtx(ctx, "Callback order:%v, orderId:%v", order, orderId)
    return out, nil
}

func getLastMonthTime(now time.Time) time.Time {
    return time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local).AddDate(0, -1, 0)
}

func (m *ActivityGame) rollback(ctx context.Context, orderId string, uid uint32, outsideTime time.Time, needUpdateStatus bool) error {
    var err error
    if needUpdateStatus {
        // 更新订单状态为失败
        sourceStatusList := []uint32{
            store.ConsumeStatusInit,
            store.ConsumeStatusFreezing,
        }
        ok, err := m.store.ChangeConsumeRecordPayInfo(ctx, outsideTime, uid, sourceStatusList, store.ConsumeStatusRollback, orderId, "")
        if err != nil {
            log.ErrorWithCtx(ctx, "rollback fail to ChangeConsumeRecordPayInfo. uid:%v, orderId:%v, outsideTime:%v, err:%v", uid, orderId, outsideTime, err)
            return err
        }

        if !ok {
            err = errors.New("订单状态更新失败")
            log.ErrorWithCtx(ctx, "rollback fail to ChangeConsumeRecordPayInfo. uid:%v, orderId:%v, outsideTime:%v, err:%v", uid, orderId, outsideTime, err)
            return err
        }
    }

    // unified-pay 解冻T豆
    err = m.acLayerMgr.PayRollback(ctx, orderId, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "rollback fail to PayRollback. uid:%v, orderId:%v, outsideTime:%v, err:%v", uid, orderId, outsideTime, err)
        return err
    }

    log.Infof("rollback uid:%v, orderId:%v, outsideTime:%v", uid, orderId, outsideTime)
    return nil
}
