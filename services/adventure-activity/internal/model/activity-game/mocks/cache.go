// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/adventure-activity/internal/model/activity-game/cache (interfaces: ICache)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	redis "github.com/go-redis/redis/v8"
	gomock "github.com/golang/mock/gomock"
	comm "golang.52tt.com/services/adventure-activity/internal/model/comm"
)

// MockICache is a mock of ICache interface.
type MockICache struct {
	ctrl     *gomock.Controller
	recorder *MockICacheMockRecorder
}

// MockICacheMockRecorder is the mock recorder for MockICache.
type MockICacheMockRecorder struct {
	mock *MockICache
}

// NewMockICache creates a new mock instance.
func NewMockICache(ctrl *gomock.Controller) *MockICache {
	mock := &MockICache{ctrl: ctrl}
	mock.recorder = &MockICacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICache) EXPECT() *MockICacheMockRecorder {
	return m.recorder
}

// CheckIfFusing mocks base method.
func (m *MockICache) CheckIfFusing(arg0 context.Context) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIfFusing", arg0)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIfFusing indicates an expected call of CheckIfFusing.
func (mr *MockICacheMockRecorder) CheckIfFusing(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIfFusing", reflect.TypeOf((*MockICache)(nil).CheckIfFusing), arg0)
}

// CheckIfWarning mocks base method.
func (m *MockICache) CheckIfWarning(arg0 context.Context, arg1 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIfWarning", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIfWarning indicates an expected call of CheckIfWarning.
func (mr *MockICacheMockRecorder) CheckIfWarning(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIfWarning", reflect.TypeOf((*MockICache)(nil).CheckIfWarning), arg0, arg1)
}

// Close mocks base method.
func (m *MockICache) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockICacheMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockICache)(nil).Close))
}

// DelFusing mocks base method.
func (m *MockICache) DelFusing(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelFusing", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelFusing indicates an expected call of DelFusing.
func (mr *MockICacheMockRecorder) DelFusing(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelFusing", reflect.TypeOf((*MockICache)(nil).DelFusing), arg0)
}

// DelHourProfit mocks base method.
func (m *MockICache) DelHourProfit(arg0 context.Context, arg1 int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelHourProfit", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelHourProfit indicates an expected call of DelHourProfit.
func (mr *MockICacheMockRecorder) DelHourProfit(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelHourProfit", reflect.TypeOf((*MockICache)(nil).DelHourProfit), arg0, arg1)
}

// DelUserPlayFile mocks base method.
func (m *MockICache) DelUserPlayFile(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelUserPlayFile", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelUserPlayFile indicates an expected call of DelUserPlayFile.
func (mr *MockICacheMockRecorder) DelUserPlayFile(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserPlayFile", reflect.TypeOf((*MockICache)(nil).DelUserPlayFile), arg0, arg1)
}

// DelUserRemainChance mocks base method.
func (m *MockICache) DelUserRemainChance(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelUserRemainChance", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelUserRemainChance indicates an expected call of DelUserRemainChance.
func (mr *MockICacheMockRecorder) DelUserRemainChance(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserRemainChance", reflect.TypeOf((*MockICache)(nil).DelUserRemainChance), arg0, arg1)
}

// DelWarning mocks base method.
func (m *MockICache) DelWarning(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelWarning", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelWarning indicates an expected call of DelWarning.
func (mr *MockICacheMockRecorder) DelWarning(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelWarning", reflect.TypeOf((*MockICache)(nil).DelWarning), arg0, arg1)
}

// GetHistoryProfit mocks base method.
func (m *MockICache) GetHistoryProfit(arg0 context.Context) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHistoryProfit", arg0)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHistoryProfit indicates an expected call of GetHistoryProfit.
func (mr *MockICacheMockRecorder) GetHistoryProfit(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHistoryProfit", reflect.TypeOf((*MockICache)(nil).GetHistoryProfit), arg0)
}

// GetHourProfit mocks base method.
func (m *MockICache) GetHourProfit(arg0 context.Context, arg1 int) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHourProfit", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHourProfit indicates an expected call of GetHourProfit.
func (mr *MockICacheMockRecorder) GetHourProfit(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHourProfit", reflect.TypeOf((*MockICache)(nil).GetHourProfit), arg0, arg1)
}

// GetPriceLimitCfg mocks base method.
func (m *MockICache) GetPriceLimitCfg(arg0 context.Context) (map[uint32]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPriceLimitCfg", arg0)
	ret0, _ := ret[0].(map[uint32]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPriceLimitCfg indicates an expected call of GetPriceLimitCfg.
func (mr *MockICacheMockRecorder) GetPriceLimitCfg(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPriceLimitCfg", reflect.TypeOf((*MockICache)(nil).GetPriceLimitCfg), arg0)
}

// GetRedisClient mocks base method.
func (m *MockICache) GetRedisClient() redis.Cmdable {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedisClient")
	ret0, _ := ret[0].(redis.Cmdable)
	return ret0
}

// GetRedisClient indicates an expected call of GetRedisClient.
func (mr *MockICacheMockRecorder) GetRedisClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedisClient", reflect.TypeOf((*MockICache)(nil).GetRedisClient))
}

// GetUserDailyPrice mocks base method.
func (m *MockICache) GetUserDailyPrice(arg0 context.Context, arg1, arg2, arg3 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserDailyPrice", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserDailyPrice indicates an expected call of GetUserDailyPrice.
func (mr *MockICacheMockRecorder) GetUserDailyPrice(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserDailyPrice", reflect.TypeOf((*MockICache)(nil).GetUserDailyPrice), arg0, arg1, arg2, arg3)
}

// GetUserPlayFile mocks base method.
func (m *MockICache) GetUserPlayFile(arg0 context.Context, arg1 uint32) (*comm.UserPlayFile, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPlayFile", arg0, arg1)
	ret0, _ := ret[0].(*comm.UserPlayFile)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserPlayFile indicates an expected call of GetUserPlayFile.
func (mr *MockICacheMockRecorder) GetUserPlayFile(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPlayFile", reflect.TypeOf((*MockICache)(nil).GetUserPlayFile), arg0, arg1)
}

// GetUserRemainChance mocks base method.
func (m *MockICache) GetUserRemainChance(arg0 context.Context, arg1 uint32) (uint32, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserRemainChance", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserRemainChance indicates an expected call of GetUserRemainChance.
func (mr *MockICacheMockRecorder) GetUserRemainChance(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserRemainChance", reflect.TypeOf((*MockICache)(nil).GetUserRemainChance), arg0, arg1)
}

// IncrHistoryProfit mocks base method.
func (m *MockICache) IncrHistoryProfit(arg0 context.Context, arg1 int64) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrHistoryProfit", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrHistoryProfit indicates an expected call of IncrHistoryProfit.
func (mr *MockICacheMockRecorder) IncrHistoryProfit(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrHistoryProfit", reflect.TypeOf((*MockICache)(nil).IncrHistoryProfit), arg0, arg1)
}

// IncrHourProfit mocks base method.
func (m *MockICache) IncrHourProfit(arg0 context.Context, arg1 int, arg2 int64) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrHourProfit", arg0, arg1, arg2)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrHourProfit indicates an expected call of IncrHourProfit.
func (mr *MockICacheMockRecorder) IncrHourProfit(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrHourProfit", reflect.TypeOf((*MockICache)(nil).IncrHourProfit), arg0, arg1, arg2)
}

// IncrUserDailyPrice mocks base method.
func (m *MockICache) IncrUserDailyPrice(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrUserDailyPrice", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// IncrUserDailyPrice indicates an expected call of IncrUserDailyPrice.
func (mr *MockICacheMockRecorder) IncrUserDailyPrice(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrUserDailyPrice", reflect.TypeOf((*MockICache)(nil).IncrUserDailyPrice), arg0, arg1, arg2, arg3, arg4)
}

// LockUserLottery mocks base method.
func (m *MockICache) LockUserLottery(arg0 context.Context, arg1 uint32, arg2 time.Duration) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LockUserLottery", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LockUserLottery indicates an expected call of LockUserLottery.
func (mr *MockICacheMockRecorder) LockUserLottery(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LockUserLottery", reflect.TypeOf((*MockICache)(nil).LockUserLottery), arg0, arg1, arg2)
}

// SetFusing mocks base method.
func (m *MockICache) SetFusing(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetFusing", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetFusing indicates an expected call of SetFusing.
func (mr *MockICacheMockRecorder) SetFusing(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetFusing", reflect.TypeOf((*MockICache)(nil).SetFusing), arg0)
}

// SetPriceLimitCfg mocks base method.
func (m *MockICache) SetPriceLimitCfg(arg0 context.Context, arg1 map[uint32]uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPriceLimitCfg", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetPriceLimitCfg indicates an expected call of SetPriceLimitCfg.
func (mr *MockICacheMockRecorder) SetPriceLimitCfg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPriceLimitCfg", reflect.TypeOf((*MockICache)(nil).SetPriceLimitCfg), arg0, arg1)
}

// SetUserPlayFile mocks base method.
func (m *MockICache) SetUserPlayFile(arg0 context.Context, arg1 uint32, arg2 *comm.UserPlayFile) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserPlayFile", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserPlayFile indicates an expected call of SetUserPlayFile.
func (mr *MockICacheMockRecorder) SetUserPlayFile(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserPlayFile", reflect.TypeOf((*MockICache)(nil).SetUserPlayFile), arg0, arg1, arg2)
}

// SetUserRemainChance mocks base method.
func (m *MockICache) SetUserRemainChance(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserRemainChance", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserRemainChance indicates an expected call of SetUserRemainChance.
func (mr *MockICacheMockRecorder) SetUserRemainChance(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserRemainChance", reflect.TypeOf((*MockICache)(nil).SetUserRemainChance), arg0, arg1, arg2)
}

// SetWarning mocks base method.
func (m *MockICache) SetWarning(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetWarning", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetWarning indicates an expected call of SetWarning.
func (mr *MockICacheMockRecorder) SetWarning(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetWarning", reflect.TypeOf((*MockICache)(nil).SetWarning), arg0, arg1)
}

// UnlockUserLottery mocks base method.
func (m *MockICache) UnlockUserLottery(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnlockUserLottery", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnlockUserLottery indicates an expected call of UnlockUserLottery.
func (mr *MockICacheMockRecorder) UnlockUserLottery(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnlockUserLottery", reflect.TypeOf((*MockICache)(nil).UnlockUserLottery), arg0, arg1)
}
