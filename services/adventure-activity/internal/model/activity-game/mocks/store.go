// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/adventure-activity/internal/model/activity-game/store (interfaces: IStore)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	mysql "gitlab.ttyuyin.com/tyr/x/middleware/mysql"
	store "golang.52tt.com/services/adventure-activity/internal/model/activity-game/store"
)

// MockIStore is a mock of IStore interface.
type MockIStore struct {
	ctrl     *gomock.Controller
	recorder *MockIStoreMockRecorder
}

// MockIStoreMockRecorder is the mock recorder for MockIStore.
type MockIStoreMockRecorder struct {
	mock *MockIStore
}

// NewMockIStore creates a new mock instance.
func NewMockIStore(ctrl *gomock.Controller) *MockIStore {
	mock := &MockIStore{ctrl: ctrl}
	mock.recorder = &MockIStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIStore) EXPECT() *MockIStoreMockRecorder {
	return m.recorder
}

// ChangeAwardRecordStatus mocks base method.
func (m *MockIStore) ChangeAwardRecordStatus(arg0 context.Context, arg1 time.Time, arg2 string, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChangeAwardRecordStatus", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// ChangeAwardRecordStatus indicates an expected call of ChangeAwardRecordStatus.
func (mr *MockIStoreMockRecorder) ChangeAwardRecordStatus(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChangeAwardRecordStatus", reflect.TypeOf((*MockIStore)(nil).ChangeAwardRecordStatus), arg0, arg1, arg2, arg3)
}

// ChangeConsumeRecordPayInfo mocks base method.
func (m *MockIStore) ChangeConsumeRecordPayInfo(arg0 context.Context, arg1 time.Time, arg2 uint32, arg3 []uint32, arg4 uint32, arg5, arg6 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChangeConsumeRecordPayInfo", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChangeConsumeRecordPayInfo indicates an expected call of ChangeConsumeRecordPayInfo.
func (mr *MockIStoreMockRecorder) ChangeConsumeRecordPayInfo(arg0, arg1, arg2, arg3, arg4, arg5, arg6 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChangeConsumeRecordPayInfo", reflect.TypeOf((*MockIStore)(nil).ChangeConsumeRecordPayInfo), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// ChangeConsumeRecordStatus mocks base method.
func (m *MockIStore) ChangeConsumeRecordStatus(arg0 context.Context, arg1 mysql.Txx, arg2 time.Time, arg3 uint32, arg4 []uint32, arg5 uint32, arg6 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChangeConsumeRecordStatus", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChangeConsumeRecordStatus indicates an expected call of ChangeConsumeRecordStatus.
func (mr *MockIStoreMockRecorder) ChangeConsumeRecordStatus(arg0, arg1, arg2, arg3, arg4, arg5, arg6 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChangeConsumeRecordStatus", reflect.TypeOf((*MockIStore)(nil).ChangeConsumeRecordStatus), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// Close mocks base method.
func (m *MockIStore) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockIStoreMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIStore)(nil).Close))
}

// CreateAwardPackTbl mocks base method.
func (m *MockIStore) CreateAwardPackTbl(arg0 context.Context, arg1 mysql.Txx, arg2 time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAwardPackTbl", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateAwardPackTbl indicates an expected call of CreateAwardPackTbl.
func (mr *MockIStoreMockRecorder) CreateAwardPackTbl(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAwardPackTbl", reflect.TypeOf((*MockIStore)(nil).CreateAwardPackTbl), arg0, arg1, arg2)
}

// CreateConsumeLogTbl mocks base method.
func (m *MockIStore) CreateConsumeLogTbl(arg0 context.Context, arg1 time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateConsumeLogTbl", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateConsumeLogTbl indicates an expected call of CreateConsumeLogTbl.
func (mr *MockIStoreMockRecorder) CreateConsumeLogTbl(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateConsumeLogTbl", reflect.TypeOf((*MockIStore)(nil).CreateConsumeLogTbl), arg0, arg1)
}

// CreateCountTbl mocks base method.
func (m *MockIStore) CreateCountTbl(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateCountTbl", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateCountTbl indicates an expected call of CreateCountTbl.
func (mr *MockIStoreMockRecorder) CreateCountTbl(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateCountTbl", reflect.TypeOf((*MockIStore)(nil).CreateCountTbl), arg0)
}

// CreateLotteryRecordTbl mocks base method.
func (m *MockIStore) CreateLotteryRecordTbl(arg0 context.Context, arg1 time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateLotteryRecordTbl", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateLotteryRecordTbl indicates an expected call of CreateLotteryRecordTbl.
func (mr *MockIStoreMockRecorder) CreateLotteryRecordTbl(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateLotteryRecordTbl", reflect.TypeOf((*MockIStore)(nil).CreateLotteryRecordTbl), arg0, arg1)
}

// CreateRemainChanceTbl mocks base method.
func (m *MockIStore) CreateRemainChanceTbl(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRemainChanceTbl", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateRemainChanceTbl indicates an expected call of CreateRemainChanceTbl.
func (mr *MockIStoreMockRecorder) CreateRemainChanceTbl(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRemainChanceTbl", reflect.TypeOf((*MockIStore)(nil).CreateRemainChanceTbl), arg0)
}

// CreateUserCurrentLvTbl mocks base method.
func (m *MockIStore) CreateUserCurrentLvTbl(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateUserCurrentLvTbl", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateUserCurrentLvTbl indicates an expected call of CreateUserCurrentLvTbl.
func (mr *MockIStoreMockRecorder) CreateUserCurrentLvTbl(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateUserCurrentLvTbl", reflect.TypeOf((*MockIStore)(nil).CreateUserCurrentLvTbl), arg0)
}

// DecrUserRemainChance mocks base method.
func (m *MockIStore) DecrUserRemainChance(arg0 context.Context, arg1 mysql.Txx, arg2, arg3 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DecrUserRemainChance", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DecrUserRemainChance indicates an expected call of DecrUserRemainChance.
func (mr *MockIStoreMockRecorder) DecrUserRemainChance(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DecrUserRemainChance", reflect.TypeOf((*MockIStore)(nil).DecrUserRemainChance), arg0, arg1, arg2, arg3)
}

// GetAwardRecordByLottery mocks base method.
func (m *MockIStore) GetAwardRecordByLottery(arg0 context.Context, arg1 uint32, arg2 []string, arg3 time.Time) ([]*store.AwardRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAwardRecordByLottery", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*store.AwardRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardRecordByLottery indicates an expected call of GetAwardRecordByLottery.
func (mr *MockIStoreMockRecorder) GetAwardRecordByLottery(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardRecordByLottery", reflect.TypeOf((*MockIStore)(nil).GetAwardRecordByLottery), arg0, arg1, arg2, arg3)
}

// GetAwardRecordByStatus mocks base method.
func (m *MockIStore) GetAwardRecordByStatus(arg0 context.Context, arg1, arg2 uint32, arg3, arg4 time.Time) ([]*store.AwardRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAwardRecordByStatus", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*store.AwardRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardRecordByStatus indicates an expected call of GetAwardRecordByStatus.
func (mr *MockIStoreMockRecorder) GetAwardRecordByStatus(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardRecordByStatus", reflect.TypeOf((*MockIStore)(nil).GetAwardRecordByStatus), arg0, arg1, arg2, arg3, arg4)
}

// GetConsumeOrderIds mocks base method.
func (m *MockIStore) GetConsumeOrderIds(arg0 context.Context, arg1, arg2 time.Time) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConsumeOrderIds", arg0, arg1, arg2)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConsumeOrderIds indicates an expected call of GetConsumeOrderIds.
func (mr *MockIStoreMockRecorder) GetConsumeOrderIds(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConsumeOrderIds", reflect.TypeOf((*MockIStore)(nil).GetConsumeOrderIds), arg0, arg1, arg2)
}

// GetConsumeRecordByPayId mocks base method.
func (m *MockIStore) GetConsumeRecordByPayId(arg0 context.Context, arg1 time.Time, arg2 string) (*store.ConsumeRecord, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConsumeRecordByPayId", arg0, arg1, arg2)
	ret0, _ := ret[0].(*store.ConsumeRecord)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetConsumeRecordByPayId indicates an expected call of GetConsumeRecordByPayId.
func (mr *MockIStoreMockRecorder) GetConsumeRecordByPayId(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConsumeRecordByPayId", reflect.TypeOf((*MockIStore)(nil).GetConsumeRecordByPayId), arg0, arg1, arg2)
}

// GetConsumeTotalCountInfo mocks base method.
func (m *MockIStore) GetConsumeTotalCountInfo(arg0 context.Context, arg1, arg2 time.Time) (*store.StCount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConsumeTotalCountInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(*store.StCount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConsumeTotalCountInfo indicates an expected call of GetConsumeTotalCountInfo.
func (mr *MockIStoreMockRecorder) GetConsumeTotalCountInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConsumeTotalCountInfo", reflect.TypeOf((*MockIStore)(nil).GetConsumeTotalCountInfo), arg0, arg1, arg2)
}

// GetCountByType mocks base method.
func (m *MockIStore) GetCountByType(arg0 context.Context, arg1 mysql.Txx, arg2 byte) (*store.Count, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCountByType", arg0, arg1, arg2)
	ret0, _ := ret[0].(*store.Count)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCountByType indicates an expected call of GetCountByType.
func (mr *MockIStoreMockRecorder) GetCountByType(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCountByType", reflect.TypeOf((*MockIStore)(nil).GetCountByType), arg0, arg1, arg2)
}

// GetRecordByLotteryId mocks base method.
func (m *MockIStore) GetRecordByLotteryId(arg0 context.Context, arg1 string, arg2 time.Time) (*store.LotteryRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecordByLotteryId", arg0, arg1, arg2)
	ret0, _ := ret[0].(*store.LotteryRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecordByLotteryId indicates an expected call of GetRecordByLotteryId.
func (mr *MockIStoreMockRecorder) GetRecordByLotteryId(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecordByLotteryId", reflect.TypeOf((*MockIStore)(nil).GetRecordByLotteryId), arg0, arg1, arg2)
}

// GetTotalRemainChance mocks base method.
func (m *MockIStore) GetTotalRemainChance(arg0 context.Context) (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTotalRemainChance", arg0)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTotalRemainChance indicates an expected call of GetTotalRemainChance.
func (mr *MockIStoreMockRecorder) GetTotalRemainChance(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTotalRemainChance", reflect.TypeOf((*MockIStore)(nil).GetTotalRemainChance), arg0)
}

// GetUserAwardRecords mocks base method.
func (m *MockIStore) GetUserAwardRecords(arg0 context.Context, arg1, arg2 uint32, arg3 []uint32, arg4 string, arg5 bool) ([]*store.AwardRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserAwardRecords", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].([]*store.AwardRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAwardRecords indicates an expected call of GetUserAwardRecords.
func (mr *MockIStoreMockRecorder) GetUserAwardRecords(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAwardRecords", reflect.TypeOf((*MockIStore)(nil).GetUserAwardRecords), arg0, arg1, arg2, arg3, arg4, arg5)
}

// GetUserCurrentLv mocks base method.
func (m *MockIStore) GetUserCurrentLv(arg0 context.Context, arg1 uint32) (*store.UserCurrentLv, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserCurrentLv", arg0, arg1)
	ret0, _ := ret[0].(*store.UserCurrentLv)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserCurrentLv indicates an expected call of GetUserCurrentLv.
func (mr *MockIStoreMockRecorder) GetUserCurrentLv(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserCurrentLv", reflect.TypeOf((*MockIStore)(nil).GetUserCurrentLv), arg0, arg1)
}

// GetUserRemainChance mocks base method.
func (m *MockIStore) GetUserRemainChance(arg0 context.Context, arg1 uint32) (*store.RemainChance, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserRemainChance", arg0, arg1)
	ret0, _ := ret[0].(*store.RemainChance)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserRemainChance indicates an expected call of GetUserRemainChance.
func (mr *MockIStoreMockRecorder) GetUserRemainChance(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserRemainChance", reflect.TypeOf((*MockIStore)(nil).GetUserRemainChance), arg0, arg1)
}

// GetUserRemainChanceForUpdate mocks base method.
func (m *MockIStore) GetUserRemainChanceForUpdate(arg0 context.Context, arg1 mysql.Txx, arg2 uint32) (*store.RemainChance, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserRemainChanceForUpdate", arg0, arg1, arg2)
	ret0, _ := ret[0].(*store.RemainChance)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserRemainChanceForUpdate indicates an expected call of GetUserRemainChanceForUpdate.
func (mr *MockIStoreMockRecorder) GetUserRemainChanceForUpdate(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserRemainChanceForUpdate", reflect.TypeOf((*MockIStore)(nil).GetUserRemainChanceForUpdate), arg0, arg1, arg2)
}

// IncrCount mocks base method.
func (m *MockIStore) IncrCount(arg0 context.Context, arg1 mysql.Txx, arg2 *store.Count) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrCount", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrCount indicates an expected call of IncrCount.
func (mr *MockIStoreMockRecorder) IncrCount(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrCount", reflect.TypeOf((*MockIStore)(nil).IncrCount), arg0, arg1, arg2)
}

// IncrUserRemainChance mocks base method.
func (m *MockIStore) IncrUserRemainChance(arg0 context.Context, arg1 mysql.Txx, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrUserRemainChance", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// IncrUserRemainChance indicates an expected call of IncrUserRemainChance.
func (mr *MockIStoreMockRecorder) IncrUserRemainChance(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrUserRemainChance", reflect.TypeOf((*MockIStore)(nil).IncrUserRemainChance), arg0, arg1, arg2, arg3)
}

// InsertAwardRecords mocks base method.
func (m *MockIStore) InsertAwardRecords(arg0 context.Context, arg1 mysql.Txx, arg2 time.Time, arg3 uint32, arg4 []*store.AwardRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertAwardRecords", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertAwardRecords indicates an expected call of InsertAwardRecords.
func (mr *MockIStoreMockRecorder) InsertAwardRecords(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertAwardRecords", reflect.TypeOf((*MockIStore)(nil).InsertAwardRecords), arg0, arg1, arg2, arg3, arg4)
}

// InsertConsumeRecord mocks base method.
func (m *MockIStore) InsertConsumeRecord(arg0 context.Context, arg1 *store.ConsumeRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertConsumeRecord", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertConsumeRecord indicates an expected call of InsertConsumeRecord.
func (mr *MockIStoreMockRecorder) InsertConsumeRecord(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertConsumeRecord", reflect.TypeOf((*MockIStore)(nil).InsertConsumeRecord), arg0, arg1)
}

// InsertLotteryRecord mocks base method.
func (m *MockIStore) InsertLotteryRecord(arg0 context.Context, arg1 mysql.Txx, arg2 *store.LotteryRecord, arg3 time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertLotteryRecord", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertLotteryRecord indicates an expected call of InsertLotteryRecord.
func (mr *MockIStoreMockRecorder) InsertLotteryRecord(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertLotteryRecord", reflect.TypeOf((*MockIStore)(nil).InsertLotteryRecord), arg0, arg1, arg2, arg3)
}

// InsertUserCurrentLv mocks base method.
func (m *MockIStore) InsertUserCurrentLv(arg0 context.Context, arg1 *store.UserCurrentLv) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertUserCurrentLv", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertUserCurrentLv indicates an expected call of InsertUserCurrentLv.
func (mr *MockIStoreMockRecorder) InsertUserCurrentLv(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertUserCurrentLv", reflect.TypeOf((*MockIStore)(nil).InsertUserCurrentLv), arg0, arg1)
}

// InsertUserRemainChance mocks base method.
func (m *MockIStore) InsertUserRemainChance(arg0 context.Context, arg1 *store.RemainChance) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertUserRemainChance", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertUserRemainChance indicates an expected call of InsertUserRemainChance.
func (mr *MockIStoreMockRecorder) InsertUserRemainChance(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertUserRemainChance", reflect.TypeOf((*MockIStore)(nil).InsertUserRemainChance), arg0, arg1)
}

// Transaction mocks base method.
func (m *MockIStore) Transaction(arg0 context.Context, arg1 func(mysql.Txx) error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Transaction", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Transaction indicates an expected call of Transaction.
func (mr *MockIStoreMockRecorder) Transaction(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Transaction", reflect.TypeOf((*MockIStore)(nil).Transaction), arg0, arg1)
}

// UpdateUserCurrentLv mocks base method.
func (m *MockIStore) UpdateUserCurrentLv(arg0 context.Context, arg1 mysql.Txx, arg2 *store.UserCurrentLv) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUserCurrentLv", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateUserCurrentLv indicates an expected call of UpdateUserCurrentLv.
func (mr *MockIStoreMockRecorder) UpdateUserCurrentLv(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserCurrentLv", reflect.TypeOf((*MockIStore)(nil).UpdateUserCurrentLv), arg0, arg1, arg2)
}
