package activity_game

import(
	context "context"
	pb "golang.52tt.com/protocol/services/adventure-activity"
	comm "golang.52tt.com/services/adventure-activity/internal/model/comm"
	UnifiedPayCallback "golang.52tt.com/protocol/services/unified_pay/cb"
)

type IActivityGame interface {
	BuyChance(ctx context.Context, in *pb.BuyChanceRequest) (*pb.BuyChanceResponse,string,error)
	GetPlatformWinningRecord(ctx context.Context, req *pb.GetPlatformWinningRecordRequest) (*pb.GetPlatformWinningRecordResponse,error)
	GetUserPlayFile(ctx context.Context, uid uint32) (*comm.UserPlayFile,uint32,error)
	GetUserPlayFileWithCache(ctx context.Context, uid uint32) (*comm.UserPlayFile,error)
	GetUserRecord(ctx context.Context, req *pb.GetUserAdventureRecordRequest) (*pb.GetUserAdventureRecordResponse,error)
	GetUserRemain(ctx context.Context, uid uint32) (uint32,error)
	LotteryDraw(ctx context.Context, in *pb.LotteryDrawRequest) (*pb.LotteryDrawResponse,error)
	PayCallBackHandler(ctx context.Context, orderId string) (*UnifiedPayCallback.PayNotifyResponse,error)
	Stop() 
}

