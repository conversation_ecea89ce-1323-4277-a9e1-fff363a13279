package lottery

import(
	context "context"
	comm "golang.52tt.com/services/adventure-activity/internal/model/comm"
	time "time"
)

type ILottery interface {
	CheckLotteryUpdate() error
	GetLevelCfgList() []*comm.LevelConf
	GetNextPrizePool(id, offset uint32) (*comm.Pool,bool)
	GetPrizePool(id uint32) (*comm.Pool,bool)
	PrizeDrawBatch(ctx context.Context, levelId uint32, req PrizeDrawReq, amount uint32) (drawResp PrizeDrawResp,err error)
	PrizeDrawOnce(ctx context.Context, p *comm.Pool, req *PrizeDrawReq) (prize *comm.Prize,err error)
	ShutDown() 
	StartCommTimer() 
	TimerHandle(d time.Duration, handle func() error) 
}

