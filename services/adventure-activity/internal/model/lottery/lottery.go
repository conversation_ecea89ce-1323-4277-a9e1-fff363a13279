package lottery

//go:generate quicksilver-cli test interface ../lottery
//go:generate mockgen -destination=../mocks/lottery.go -package=mocks golang.52tt.com/services/adventure-activity/internal/model/lottery ILottery

import (
    "context"
    "crypto/rand"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/protocol/common/status"
    "golang.52tt.com/services/adventure-activity/internal/conf"
    activity_conf "golang.52tt.com/services/adventure-activity/internal/model/activity-conf"
    "golang.52tt.com/services/adventure-activity/internal/model/comm"
    "math/big"
    "sort"
    "sync"
    "time"
)

const (
    BingoNone       = uint32(iota)
    BingoRandom     // 随机通关
    BingoGuaranteed // 保底通关
)

type Lottery struct {
    bc         conf.IBusinessConfManager
    ActConfMgr activity_conf.IActivityConf

    poolList        []comm.Pool // 奖池
    activityId      uint32      // 当前活动id
    cfgVersion      int64
    prizeVersion    int64
    historyPrizeMap map[uint32]*comm.Prize // 历史奖品信息
    rwMute          sync.RWMutex

    shutDown chan interface{}
    wg       sync.WaitGroup
}

func NewLottery(bc conf.IBusinessConfManager, actConfMgr activity_conf.IActivityConf) (*Lottery, error) {
    l := &Lottery{
        bc:              bc,
        ActConfMgr:      actConfMgr,
        poolList:        make([]comm.Pool, 0),
        historyPrizeMap: make(map[uint32]*comm.Prize),
        wg:              sync.WaitGroup{},
        shutDown:        make(chan interface{}),
    }

    err := l.CheckLotteryUpdate()
    if err != nil {
        log.Errorf("NewLottery fail to CheckLotteryUpdate. err:%v", err)
        return l, err
    }

    go l.StartCommTimer()

    return l, nil
}

func (l *Lottery) ShutDown() {
    close(l.shutDown)
    l.wg.Wait()
    l.bc.Close()
}

// CheckLotteryUpdate 检查配置更新
func (l *Lottery) CheckLotteryUpdate() error {
    ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
    defer cancel()

    now := time.Now()
    prizeVersion, cfgVersion, change := l.checkLotteryVersion(ctx)
    if !change {
        return nil
    }

    err := l.updatePools(ctx, prizeVersion, cfgVersion)
    if err != nil {
        log.ErrorWithCtx(ctx, "CheckLotteryUpdate fail to updatePools. now:%v, err:%v", now, err)
        return err
    }

    //err = l.updateAllPrizeInfo(ctx)
    //if err != nil {
    //    log.ErrorWithCtx(ctx, "CheckLotteryUpdate fail to updateAllPrizeInfo. now:%v, err:%v", now, err)
    //    return err
    //}

    log.InfoWithCtx(ctx, "CheckLotteryUpdate success. now:%+v", now)
    return nil
}

func (l *Lottery) GetLevelCfgList() []*comm.LevelConf {
    l.rwMute.RLock()
    defer l.rwMute.RUnlock()

    list := make([]*comm.LevelConf, 0, len(l.poolList))
    for _, pool := range l.poolList {
        if pool.TotalWeight == 0 || pool.Cfg.LevelId == 0 {
            continue
        }

        cfg := pool.Cfg
        list = append(list, &cfg)
    }

    return list
}

func (l *Lottery) GetPrizePool(id uint32) (*comm.Pool, bool) {
    return l.GetNextPrizePool(id, 0)
}

func (l *Lottery) GetNextPrizePool(id, offset uint32) (*comm.Pool, bool) {
    l.rwMute.RLock()
    defer l.rwMute.RUnlock()

    // 查找当前关卡的位置
    currentIndex := -1
    for i, pool := range l.poolList {
        if pool.Cfg.LevelId == id {
            currentIndex = i
            break
        }
    }

    if currentIndex == -1 {
        return &comm.Pool{}, false
    }

    // 计算目标索引（支持循环）
    targetIndex := (currentIndex + int(offset)) % len(l.poolList)
    return &l.poolList[targetIndex], true
}

func (l *Lottery) setPrizePoolList(poolList []comm.Pool) bool {
    l.rwMute.Lock()
    defer l.rwMute.Unlock()

    // 根据levelId 升序排序
    sort.Slice(poolList, func(i, j int) bool {
        return poolList[i].Cfg.LevelId < poolList[j].Cfg.LevelId
    })

    l.poolList = poolList
    return true
}

func (l *Lottery) checkLotteryVersion(ctx context.Context) (prizeVersion, cfgVersion int64, change bool) {
    var err error

    cfgVersion, err = l.ActConfMgr.GetLevelInfoUpdateVersion(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "checkLotteryVersion fail to GetLevelInfoUpdateVersion. err:%v", err)
        return
    }

    prizeVersion, err = l.ActConfMgr.GetPondUpdateVersion(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "checkLotteryVersion fail to GetPrizeUpdateVersion. err:%v", err)
        return
    }

    if l.cfgVersion >= cfgVersion || l.prizeVersion >= prizeVersion {
        return
    }

    change = true
    return
}

func GenPoolListV2(poolList []*comm.Pool) []comm.Pool {
    newPoolList := make([]comm.Pool, 0)

    for _, pool := range poolList {
        if len(pool.Cfg.CompletedPrize) == 0 || len(pool.PrizeList) == 0 {
            log.Warnf("GenPoolListV2 pool.cfg.completed_prize or pool.prize_list is empty. pool:%+v", pool)
            continue
        }

        newPool := InitPrizePool(pool.Cfg, pool.PrizeList)
        newPoolList = append(newPoolList, *newPool)
    }
    return newPoolList
}

func (l *Lottery) updatePools(ctx context.Context, prizeVersion, cfgVersion int64) error {
    levelWithPool, err := l.ActConfMgr.GetLevelConfWithPool(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "updatePools fail to GetLevelInfoEffectiveInOrder.  err:%v", err)
        return err
    }

    poolList := GenPoolListV2(levelWithPool)
    _ = l.setPrizePoolList(poolList)
    l.cfgVersion = cfgVersion
    l.prizeVersion = prizeVersion

    log.InfoWithCtx(ctx, "updatePools success. cfg_version:%v, prize_version:%v", cfgVersion, prizeVersion)
    return nil
}

func InitPrizePool(cfg comm.LevelConf, poolList []*comm.Prize) *comm.Pool {

    // 按权重大小降序排列
    sort.SliceStable(poolList, func(r, l int) bool {
        return poolList[r].Weight > poolList[l].Weight
    })

    totalWeight := uint64(0)
    prizeList := make([]*comm.Prize, 0, len(poolList))
    targetPrizeIdList := make([]string, 0, len(poolList))

    for _, info := range poolList {
        totalWeight += uint64(info.Weight)
        prizeList = append(prizeList, info)
        targetPrizeIdList = append(targetPrizeIdList, info.PrizeId)
    }

    return &comm.Pool{
        Cfg:               cfg,
        PrizeList:         prizeList,
        TargetPrizeIdList: targetPrizeIdList,
        TotalWeight:       totalWeight,
    }
}

// 抽奖
func (l *Lottery) prizeDraw(p *comm.Pool) (prize *comm.Prize, err error) {

    if p.TotalWeight == 0 || len(p.PrizeList) == 0 {
        return prize, protocol.NewExactServerError(nil, status.ErrSys)
    }

    // 生成随机数
    val, err := rand.Int(rand.Reader, big.NewInt(int64(p.TotalWeight)))
    if err != nil {
        return prize, err
    }
    r := val.Int64()
    curr := uint64(0)

    for _, info := range p.PrizeList {
        curr += uint64(info.Weight)

        if curr > uint64(r) {
            // 命中
            prize = info
            break
        }
    }

    return prize, nil
}

type PrizeDrawReq struct {
    Uid       uint32
    LevelId   uint32
    LotteryId string
    ChannelId uint32

    // 当前集卡进度
    CurrPlayFile *comm.UserPlayFile
}

type PrizeDrawResp struct {
    AwardList    []*comm.Prize
    CurrPlayFile *comm.UserPlayFile // 抽奖后用户当前关卡进度
    //CompletedProgress []*comm.UserPlayFile // 抽奖过程中用户完成关卡进度
}

func (l *Lottery) PrizeDrawBatch(ctx context.Context, levelId uint32, req PrizeDrawReq, amount uint32) (drawResp PrizeDrawResp, err error) {
    awardList := make([]*comm.Prize, 0, amount)

    drawResp = PrizeDrawResp{}
    p, ok := l.GetPrizePool(levelId)
    if !ok {
        err = protocol.NewExactServerError(nil, status.ErrAdventureActivitySystemErr)
        log.ErrorWithCtx(ctx, "PrizeDrawBatch fail to GetPrizePool. levelId:%d, pool not found", levelId)
        return
    }

    currProgress := &comm.UserPlayFile{
        LevelId:  levelId,
        PrizeMap: make(map[string]uint32),
        UserN:    0,
    }
    if req.CurrPlayFile != nil {
        currProgress = req.CurrPlayFile
        currProgress.UserN = req.CurrPlayFile.UserN
        for k, v := range req.CurrPlayFile.PrizeMap {
            currProgress.PrizeMap[k] = v
        }
    }

    targetPool := p
    log.DebugWithCtx(ctx, "PrizeDrawBatch targetPool:%+v", targetPool)
    for i := uint32(0); i < amount; i++ {
        var isCompleted bool
        var bingoType uint32
        prize := &comm.Prize{}

        prize, err = l.PrizeDrawOnce(ctx, targetPool, &req)
        if err != nil {
            log.ErrorWithCtx(ctx, "PrizeDrawBatch fail to PrizeDrawOnce. in:%+v, err:%v", req, err)
            return drawResp, err
        }
        // 先将本次抽到的卡牌礼物添加进列表
        awardList = append(awardList, getAwardInfo([]*comm.Prize{prize}, BingoNone, comm.AwardTypeLighted, comm.AwardTypeLighted)...)
        // 更新用户关卡进度
        l.updateUserProgress(currProgress, 1, prize.PrizeId)

        // 判断保底通关
        if currProgress.UserN >= targetPool.Cfg.ConstantN {
            isCompleted = true
            bingoType = BingoGuaranteed

        } else {
            // 判断是否通关，判断卡牌是否集齐，根据currProgress判断
            if l.checkIfCompleted(currProgress, targetPool) {
                isCompleted = true
                bingoType = BingoRandom
            }
        }

        if isCompleted {
            award := targetPool.Cfg.CompletedPrize
            awardType := comm.AwardTypeCompleted
            oldLevelId := currProgress.LevelId
            playCount := currProgress.PlayCount
            // 获取下一关奖池
            targetPool, ok = l.GetNextPrizePool(levelId, 1)
            if !ok {
                err = protocol.NewExactServerError(nil, status.ErrAdventureActivitySystemErr)
                log.ErrorWithCtx(ctx, "PrizeDrawBatch fail to GetNextPrizePool. levelId:%d, pool not found", levelId)
                return
            }

            log.DebugWithCtx(ctx, "PrizeDrawBatch targetPool:%+v", targetPool)

            if oldLevelId > targetPool.Cfg.LevelId {
                playCount++
                awardType = comm.AwardTypeReachTop
            }

            // 加上通关奖励
            awardList = append(awardList, getAwardInfo(award, bingoType, awardType, awardType)...)

            // 重置用户当前进度
            currProgress = &comm.UserPlayFile{
                LevelId:   targetPool.Cfg.LevelId,
                PlayCount: playCount,
                UserN:     0,
                PrizeMap:  make(map[string]uint32),
            }
        }

        //reportLottery(ctx, req, i, award.ResultType, guaranteedVal, goalUserN)
    }

    drawResp.CurrPlayFile = currProgress
    drawResp.AwardList = awardList
    return drawResp, nil
}

func getAwardInfo(prizeList []*comm.Prize, bingoType, awardType, source uint32) []*comm.Prize {
    if len(prizeList) == 0 {
        return nil
    }

    list := make([]*comm.Prize, 0, len(prizeList))

    for _, v := range prizeList {

        list = append(list, &comm.Prize{
            CardIdOrder:  v.CardIdOrder,
            LevelId:      v.LevelId,
            PlayCount:    v.PlayCount,
            ResultType:   awardType,
            PrizeId:      v.PrizeId,
            PrizeType:    v.PrizeType,
            DressSubType: v.DressSubType,
            Amount:       v.Amount,
            Weight:       v.Weight,
            PrizeName:    v.PrizeName,
            PrizeIcon:    v.PrizeIcon,
            PrizePrice:   v.PrizePrice,
            PriceType:    v.PriceType,
            GiftId:       v.GiftId,
            BingoType:    bingoType,
            Source:       source,
        })
    }

    return list
}

func (l *Lottery) PrizeDrawOnce(ctx context.Context, p *comm.Pool, req *PrizeDrawReq) (prize *comm.Prize, err error) {
    if p == nil {
        err = protocol.NewExactServerError(nil, status.ErrAdventureActivitySystemErr)
        log.ErrorWithCtx(ctx, "PrizeDrawOnce fail. %+v, err:%v", req, err)
        return
    }

    prize, err = l.prizeDraw(p)
    if err != nil {
        log.ErrorWithCtx(ctx, "PrizeDrawOnce fail to prizeDraw. %+v, err:%v", req, err)
        return
    }

    if prize == nil {
        err = protocol.NewExactServerError(nil, status.ErrAdventureActivitySystemErr)
        log.ErrorWithCtx(ctx, "PrizeDrawOnce fail. pool not found %+v, err:%v", req, err)
        return
    }

    return
}

// 检查是否通关
func (l *Lottery) checkIfCompleted(progress *comm.UserPlayFile, p *comm.Pool) bool {
    if len(progress.PrizeMap) < len(p.TargetPrizeIdList) {
        return false
    }

    for _, v := range p.TargetPrizeIdList {
        if _, ok := progress.PrizeMap[v]; !ok {
            return false
        }
    }

    return true
}

// 更新用户关卡进度
func (l *Lottery) updateUserProgress(progress *comm.UserPlayFile, step uint32, prizeId string) {
    if progress.PrizeMap == nil {
        progress.PrizeMap = make(map[string]uint32)
    }

    progress.PrizeMap[prizeId] += step
    progress.UserN += step

    return
}

//func GenPoolList(levelCfgList []*comm.LevelConf, prizeList []*comm.Prize) []comm.Pool {
//    level2PrizeList := make(map[uint32][]*comm.Prize)
//    for _, prize := range prizeList {
//        if _, ok := level2PrizeList[prize.LevelId]; !ok {
//            level2PrizeList[prize.LevelId] = make([]*comm.Prize, 0)
//        }
//
//        level2PrizeList[prize.LevelId] = append(level2PrizeList[prize.LevelId], prize)
//    }
//
//    poolList := make([]comm.Pool, 0)
//    for _, cfg := range levelCfgList {
//        prizeList, ok := level2PrizeList[cfg.LevelId]
//        if !ok {
//            log.Errorf("GenPoolList fail. levelId:%d, miss PrizeList", cfg.LevelId)
//            continue
//        }
//
//        newPool := InitPrizePool(*cfg, prizeList)
//        poolList = append(poolList, *newPool)
//    }
//
//    return poolList
//}

//func reportLottery(ctx context.Context, req PrizeDrawReq, idx, result, guaranteedVal, goalUserN uint32) {
//    if req.Uid == 0 {
//        return
//    }
//
//    err := bylink.TrackMap(ctx, 0, "cat_canteen_lottery_guaranteed_log", map[string]interface{}{
//        "uid":             req.Uid,
//        "round_id":        idx,
//        "room_id":         req.ChannelId,
//        "level":           req.LevelId,
//        "game_result":     result,
//        "lottery_id":      req.LotteryId,
//        "play_value":      guaranteedVal,
//        "goal_play_value": goalUserN,
//    }, true)
//    if err != nil {
//        log.ErrorWithCtx(ctx, "reportLottery fail to TrackMap. err:%v", err)
//    }
//
//    bylink.Flush()
//}

//func (l *Lottery) PrizeDraw(ctx context.Context, req PrizeDrawReq, amount uint32) (
//    awardList []*AwardInfo, guaranteedVal, goalUserN uint32, err error) {
//    pool, ok := l.GetPrizePool(req.LevelId)
//    if !ok {
//        log.ErrorWithCtx(ctx, "pool not found. req:%+v", req)
//        err = protocol.NewExactServerError(nil, status.ErrSys)
//        return
//    }
//
//    var awardPackMap map[uint32]*AwardPack
//    awardPackMap, guaranteedVal, goalUserN, err = pool.PrizeDrawBatch(ctx, req, amount)
//    if err != nil {
//        log.ErrorWithCtx(ctx, "PrizeDraw fail to PrizeDrawBatch. in:%+v, err:%v", req, err)
//        return
//    }
//
//    awardList = make([]*AwardInfo, 0)
//    for _, awardPack := range awardPackMap {
//        awardInfo := &AwardInfo{
//            ResultType: awardPack.ResultType,
//        }
//        if awardPack.PackId > 0 {
//            awardInfo.Pack = awardPack
//        }
//
//        var mount *AwardMount
//        if IfIsBingo(awardInfo.ResultType) {
//            targetPool := pool
//            if awardInfo.ResultType == uint32(pb.ResultType_RESULT_CROSS_LEVEL_UPGRADE) {
//                nextPool, ok := l.GetNextPrizePool(req.LevelId, 1)
//                if ok {
//                    targetPool = nextPool
//                }
//            }
//            mount = &AwardMount{
//                MountId:   targetPool.Cfg.MountsId,
//                MountName: targetPool.Cfg.MountsName,
//                MountPic:  targetPool.Cfg.MountsPic,
//                Duration:  targetPool.Cfg.MountsKeepDay,
//                Amount:    awardPack.Amount,
//            }
//
//            // 发下一关的经营凭证
//            if nextPool, ok := l.GetNextPrizePool(targetPool.Cfg.LevelId, 1); ok {
//                awardInfo.Prop = &AwardProp{
//                    PropId:     nextPool.Cfg.LevelId,
//                    Amount:     awardPack.Amount,
//                    ResultType: awardInfo.ResultType,
//                }
//            }
//        }
//
//        if mount != nil && mount.MountId != "" {
//            awardInfo.Mount = mount
//        }
//
//        awardList = append(awardList, awardInfo)
//    }
//
//    return
//}

//func (l *Lottery) GetPrizeInfo(packId uint32) (*comm.Prize, bool) {
//    l.rwMute.RLock()
//    defer l.rwMute.RUnlock()
//
//    info, ok := l.historyPrizeMap[packId]
//    if ok {
//        return info, ok
//    }
//
//    return &comm.Prize{}, ok
//}
//
//func (l *Lottery) setAllPrizeInfo(tmpMap map[uint32]*comm.Prize) {
//    l.rwMute.Lock()
//    defer l.rwMute.Unlock()
//
//    l.historyPrizeMap = tmpMap // 更新历史奖品信息
//}

//
//func (l *Lottery) updateAllPrizeInfo(ctx context.Context) error {
//    allPrize, err := l.store.GetAllPrizeList(ctx)
//    if err != nil {
//        log.ErrorWithCtx(ctx, "updateAllPrizeInfo fail to GetAllPrizeList. err:%v", err)
//        return err
//    }
//
//    tmpMap := make(map[uint32]*Prize)
//    for _, prize := range allPrize {
//        tmpMap[prize.PackId] = &Prize{
//            PackId:         prize.PackId,
//            PackWorth:      prize.PackWorth,
//            PackName:       prize.PackName,
//            PackGiftAmount: prize.PackGiftAmount,
//            PackPic:        prize.PackPic,
//            GiftId:         prize.GiftId,
//        }
//    }
//
//    l.setAllPrizeInfo(tmpMap)
//    return nil
//}
