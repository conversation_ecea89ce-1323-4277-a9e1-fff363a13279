package lottery

import (
    "github.com/golang/mock/gomock"
    "testing"
    "golang.52tt.com/services/adventure-activity/internal/model/comm"
    "golang.52tt.com/services/adventure-activity/internal/conf/mocks"
)

var mockBc *mocks.MockIBusinessConfManager

func newTestLottery(t *testing.T) *Lottery {
    ctl := gomock.NewController(t)
    mockBc = mocks.NewMockIBusinessConfManager(ctl)

    return &Lottery{
        bc:              mockBc,
        poolList:        make([]comm.Pool, 0),
        historyPrizeMap: make(map[uint32]*comm.Prize),
    }
}
