package lottery

import (
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "time"
    "runtime/debug"
    "fmt"
    "runtime"
    "reflect"
    "bytes"
)

func (l *Lottery) StartCommTimer() {
    // 奖池配置
    go l.<PERSON>r<PERSON><PERSON><PERSON>(time.Second, l.CheckLotteryUpdate)
}

func (l *Lottery) TimerHandle(d time.Duration, handle func() error) {
    l.wg.Add(1)
    defer l.wg.Done()

    delay := time.NewTicker(d)
    for {
        select {
        case <-l.shutDown:
            return
        case <-delay.C:
            l.handleWithPanicCatch(handle)
        }
    }
}

func (l *Lottery) handleWithPanicCatch(handle func() error) {
    defer func() {
        if err := recover(); err != nil {
            var stack string
            var buf bytes.Buffer
            buf.Write(debug.Stack())
            stack = buf.String()

            nowTime := time.Now().Format("2006-01-02 15:04:05")
            fmt.Printf("%s %v %s %s", nowTime, err, "\n", stack)

            funcName := runtime.FuncForPC(reflect.ValueOf(handle).Pointer()).Name()
            log.Errorf("handleWithPanicCatch panic func:%s, err:%v", funcName, err)
        }
    }()

    _ = handle()
}
