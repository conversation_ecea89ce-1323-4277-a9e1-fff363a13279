package anti_corruption_layer

import(
	context "context"
	time "time"
	app "golang.52tt.com/protocol/app"
	channel_wedding "golang.52tt.com/protocol/services/channel-wedding"
	probgamecenter2 "golang.52tt.com/protocol/services/probgamecenter"
	backpackbasepb "golang.52tt.com/protocol/services/backpack-base"
	userpresent "golang.52tt.com/protocol/services/userpresent"
)

type IACLayer interface {
	AwardDress(ctx context.Context, uid, cpUid, holdingDay, outsideTs, dressType uint32, giftId, orderId string) error
	AwardPackage(ctx context.Context, uid, packId, amount uint32, outsideTs int64, orderId string) error
	CheckProbGameCenter(ctx context.Context, probReq *probgamecenter2.CheckFuseReq) (map[string]string,error)
	GetPackageItemCfg(ctx context.Context, bgIdList []uint32) (map[uint32]*userpresent.StPresentItemConfig,map[uint32][]*backpackbasepb.PackageItemCfg,map[uint32]*backpackbasepb.LotteryFragmentCfg,error)
	GetUserProfile(ctx context.Context, uid uint32) (*app.UserProfile,error)
	GetUserProfileMap(ctx context.Context, uidList []uint32, replace bool) (map[uint32]*app.UserProfile,error)
	PayFreeze(ctx context.Context, orderId string, uid, totalPrice uint32, outsideTime time.Time) (uint32,error)
	PayRollback(ctx context.Context, orderId string, uid uint32) error
	PayTBeanCommit(ctx context.Context, orderId string, uid, totalPrice, packId uint32, amount uint32) (string,string,error)
	PushBreakingNews(ctx context.Context, channelId, newsId uint32, weddingInfo *channel_wedding.WeddingInfo) error
	SimpleSendTTAssistantText(ctx context.Context, toUid uint32, content, highlight, url string) error
	Stop() 
}

