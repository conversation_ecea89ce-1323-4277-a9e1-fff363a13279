package anti_corruption_layer

//go:generate quicksilver-cli test interface ../anti-corruption-layer
//go:generate mockgen -destination=../mocks/anti_corruption_layer.go -package=mocks golang.52tt.com/services/adventure-activity/internal/model/anti-corruption-layer IACLayer

import (
    "context"
    "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channelol_go"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    award_center "golang.52tt.com/clients/award-center"
    backpackBase "golang.52tt.com/clients/backpack-base"
    backpackSender "golang.52tt.com/clients/backpack-sender"
    "golang.52tt.com/clients/channel"
    publicnotice "golang.52tt.com/clients/public-notice"
    push "golang.52tt.com/clients/push-notification/v2"
    userProfile "golang.52tt.com/clients/user-profile-api"
    pushPb "golang.52tt.com/protocol/app/push"
    channel_wedding "golang.52tt.com/protocol/services/channel-wedding"
    publicNoticePb "golang.52tt.com/protocol/services/public-notice"
    "golang.52tt.com/services/adventure-activity/internal/conf"
    unifiedPay "golang.52tt.com/clients/unified_pay"
    "golang.52tt.com/clients/account"
    imApi "golang.52tt.com/clients/im-api"
    probgamecenter2 "golang.52tt.com/protocol/services/probgamecenter"
    "time"
    "golang.52tt.com/pkg/protocol"
    probgamecenter "golang.52tt.com/clients/prob-game-center"
    "golang.52tt.com/protocol/common/status"
    backpackbasepb "golang.52tt.com/protocol/services/backpack-base"
    "golang.52tt.com/protocol/services/userpresent"
    presentclient "golang.52tt.com/clients/userpresent"
    "google.golang.org/grpc/codes"
    "fmt"
    "gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
)

type ACLayer struct {
    shutDown chan struct{}
    bc       conf.IBusinessConfManager

    imApiCli          imApi.IClient
    userProfileCli    userProfile.IClient
    accountCli        account.IClient
    publicNoticeCli   publicnotice.IClient
    channelCli        channel.IClient
    pushCli           push.IClient
    backpackSenderCli backpackSender.IClient
    backpackCli       backpackBase.IClient
    channelOlCli      channelol_go.ChannelolGoClient
    awardCenter       award_center.IClient
    unifiedPayCli     unifiedPay.IClient

    presentCli presentclient.IClient

    probGameCli probgamecenter.IClient
}

// NewMgr 活动配置模块
func NewMgr(ctx context.Context, bc conf.IBusinessConfManager) (*ACLayer, error) {
    userProfileCli, _ := userProfile.NewClient()
    publicNoticeCli := publicnotice.NewIClient()
    channelCli := channel.NewIClient()
    pushCli, _ := push.NewClient()
    backpackSenderCli, _ := backpackSender.NewClient()
    backpackCli, _ := backpackBase.NewClient()
    channelOlCli, _ := channelol_go.NewClient(ctx)

    awardCenter, _ := award_center.NewClient()

    unifiedPayCli, _ := unifiedPay.NewClient()
    accountCli := account.NewIClient()
    imApiCli, _ := imApi.NewClient()

    probgameCli, _ := probgamecenter.NewClient()

    presentCli := presentclient.NewClient()

    m := &ACLayer{
        shutDown:          make(chan struct{}),
        bc:                bc,
        imApiCli:          imApiCli,
        userProfileCli:    userProfileCli,
        accountCli:        accountCli,
        publicNoticeCli:   publicNoticeCli,
        channelCli:        channelCli,
        pushCli:           pushCli,
        backpackSenderCli: backpackSenderCli,
        backpackCli:       backpackCli,
        channelOlCli:      channelOlCli,

        awardCenter:   awardCenter,
        unifiedPayCli: unifiedPayCli,
        probGameCli:   probgameCli,

        presentCli: presentCli,
    }

    return m, nil
}

func (m *ACLayer) Stop() {
    close(m.shutDown)
}

func (m *ACLayer) CheckProbGameCenter(ctx context.Context, probReq *probgamecenter2.CheckFuseReq) (map[string]string, error) {
    tokenMap := make(map[string]string)
    quickCtx, cancel := context.WithTimeout(ctx, 500*time.Millisecond)
    defer cancel()

    probResp, err := m.probGameCli.CheckFuse(quickCtx, probReq)
    if err != nil {
        if err.Code() == status.ErrRiskControlPlobGameFuse {
            // 熔断了
            return tokenMap, protocol.NewExactServerError(nil, status.ErrOnePieceNotAvailable)
        }
        log.ErrorWithCtx(ctx, "checkProbGameCenter fail to CheckFuse. %+v, err:%v", probReq, err)
        return tokenMap, err
    }

    for _, ret := range probResp.GetCheckResultList() {
        if ret.IsFuse {
            // 熔断了
            return tokenMap, protocol.NewExactServerError(nil, status.ErrOnePieceNotAvailable)
        }

        tokenMap[ret.GetOrderId()] = ret.GetDealToken()
    }

    return tokenMap, nil
}

func (m *ACLayer) PushBreakingNews(ctx context.Context, channelId, newsId uint32, weddingInfo *channel_wedding.WeddingInfo) error {
    userMap, err := m.userProfileCli.BatchGetUserProfileV2(ctx, []uint32{weddingInfo.GetGroom().GetUid(), weddingInfo.GetBride().GetUid()}, false)
    if err != nil {
        log.ErrorWithCtx(ctx, "PushBreakingNews failed to BatchGetUserProfileV2. channelId:%d, err:%v", channelId)
        return err
    }

    cSimpleInfo, err := m.channelCli.GetChannelSimpleInfo(ctx, weddingInfo.GetGroom().GetUid(), channelId)
    if err != nil {
        log.ErrorWithCtx(ctx, "PushBreakingNews.GetChannelSimpleInfo, channelId: %d, weddingInfo:%v:, err: %v", channelId, weddingInfo, err)
        return err
    }

    fromUser := userMap[weddingInfo.GetGroom().GetUid()]
    targetUser := userMap[weddingInfo.GetBride().GetUid()]
    breakingNewsMessage := &publicNoticePb.CommonBreakingNewsV3{
        FromUid: fromUser.GetUid(),
        FromUserInfo: &publicNoticePb.UserInfo{
            Account: fromUser.GetAccount(),
            Nick:    fromUser.GetNickname(),
        },
        TargetUid: targetUser.GetUid(),
        TargetUserInfo: &publicNoticePb.UserInfo{
            Account: targetUser.GetAccount(),
            Nick:    targetUser.GetNickname(),
        },
        ChannelId: channelId,
        ChannelInfo: &publicNoticePb.ChannelInfo{
            ChannelDisplayid: cSimpleInfo.GetDisplayId(),
            ChannelBindid:    cSimpleInfo.GetBindId(),
            ChannelType:      cSimpleInfo.GetChannelType(),
            ChannelName:      cSimpleInfo.GetName(),
        },
        BreakingNewsBaseOpt: &publicNoticePb.CommBreakingNewsBaseOpt{
            TriggerType: uint32(pushPb.CommBreakingNewsBaseOpt_COMMON_RICH_TEXT_NEWS),
        },
    }
    req := &publicNoticePb.PushBreakingNewsReq{
        BreakingCmdType:    publicNoticePb.PushBreakingNewsReq_COMMON_BREAKING_EVENT_V3,
        CommonBreakingNews: breakingNewsMessage,
    }
    req.RichTextNews = &publicNoticePb.RichTextNews{
        NewsId: newsId,
    }

    _, pushErr := m.publicNoticeCli.PushBreakingNews(ctx, req)
    if pushErr != nil {
        log.ErrorWithCtx(ctx, "pushBreakingNews fail to PushMulticast. uid:%d, err:%v", channelId, pushErr)
        return pushErr
    }
    log.InfoWithCtx(ctx, "pushBreakingNews success, uid:%d, BreakingNewsMessage:%v", channelId, req)
    return nil
}

// GetPackageItemCfg 获取包裹配置
func (m *ACLayer) GetPackageItemCfg(ctx context.Context, bgIdList []uint32) (
    map[uint32]*userpresent.StPresentItemConfig,
    map[uint32][]*backpackbasepb.PackageItemCfg,
    map[uint32]*backpackbasepb.LotteryFragmentCfg,
    error) {

    giftIds := make([]uint32, 0)
    fragmentIdMap := make(map[uint32]struct{})
    mapGiftConf := make(map[uint32]*userpresent.StPresentItemConfig)
    bg2PackageItemCfg := map[uint32][]*backpackbasepb.PackageItemCfg{}
    mapFragmentItemCfg := make(map[uint32]*backpackbasepb.LotteryFragmentCfg)

    if len(bgIdList) > 0 {
        itemCfgRsp, err := m.backpackCli.GetPackageItemCfgV2(ctx, &backpackbasepb.GetPackageItemCfgReq{
            BgIdList: bgIdList,
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "getPackageItemCfg GetPackageItemCfgV2 fail err:%v", err)
            return mapGiftConf, bg2PackageItemCfg, mapFragmentItemCfg, err
        }

        for _, cfgList := range itemCfgRsp.GetPackageItemCfgList() {

            for _, itemCfg := range cfgList.GetItemCfgList() {
                if itemCfg.GetSourceId() > 0 {
                    switch itemCfg.GetItemType() {
                    case uint32(backpackbasepb.PackageItemType_BACKPACK_PRESENT):
                        giftIds = append(giftIds, itemCfg.GetSourceId())
                    case uint32(backpackbasepb.PackageItemType_BACKPACK_LOTTERY_FRAGMENT):
                        fragmentIdMap[itemCfg.GetSourceId()] = struct{}{}
                    default:
                        log.ErrorWithCtx(ctx, "getItemVal type bgId:%d sourceId:%v type:%v", itemCfg.BgId, itemCfg.SourceId, itemCfg.GetItemType())
                        err = protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, fmt.Sprintf("bgId:%d sourceId:%v 不支持的礼物类型", itemCfg.BgId, itemCfg.SourceId))
                    }

                    if _, ok := bg2PackageItemCfg[itemCfg.BgId]; !ok {
                        bg2PackageItemCfg[itemCfg.BgId] = make([]*backpackbasepb.PackageItemCfg, 0)
                    }
                    bg2PackageItemCfg[itemCfg.BgId] = append(bg2PackageItemCfg[itemCfg.BgId], itemCfg)
                }
            }
        }

    }

    //获取礼物配置
    if len(giftIds) > 0 {
        presentResp, err := m.presentCli.GetPresentConfigByIdList(ctx, 0, giftIds,
            uint32(userpresent.ConfigListTypeBitMap_CONFIG_UNLIMIT))
        if err != nil {
            log.Errorf("getPackageItemCfg GetPresentConfigByIdList fail err: %v", err)
            return mapGiftConf, bg2PackageItemCfg, mapFragmentItemCfg, err
        }
        for _, itemConf := range presentResp.GetItemList() {
            mapGiftConf[itemConf.GetItemId()] = itemConf
        }
    }

    // 获取碎片配置
    if len(fragmentIdMap) > 0 {
        fragmentResp, err := m.backpackCli.GetItemCfg(ctx, &backpackbasepb.GetItemCfgReq{
            ItemType: 4, // 4为碎片类型
            GetAll:   true,
        })
        if err != nil {
            log.Errorf("getPackageItemCfg GetPresentConfigByIdList fail err: %v", err)
            return mapGiftConf, bg2PackageItemCfg, mapFragmentItemCfg, nil
        }

        // 组装数据
        for _, cfg := range fragmentResp.GetItemCfgList() {
            itemConfig := &backpackbasepb.LotteryFragmentCfg{}
            e := proto.Unmarshal(cfg, itemConfig)
            if e != nil {
                log.ErrorWithCtx(ctx, "getFragmentConfMap proto.Unmarshal failed, itemCfg:[%+v], err:[%+v]", string(cfg), err)
            }
            mapFragmentItemCfg[itemConfig.GetFragmentId()] = itemConfig
        }

    }

    return mapGiftConf, bg2PackageItemCfg, mapFragmentItemCfg, nil
}
