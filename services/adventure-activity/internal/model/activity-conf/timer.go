package activity_conf

import (
    "context"
    "gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    adventure_activity "golang.52tt.com/protocol/services/adventure-activity"
    "strconv"
    "golang.52tt.com/services/adventure-activity/internal/model/activity-conf/store"
)

func (m *ActivityConf) startTimer() error {
    var err error
    m.timerD, err = timer.NewTimerD(context.Background(),
        "adventure-island-conf",
        timer.WithV8RedisCmdable(m.cache.GetRedisClient()))
    if err != nil {
        log.Errorf("startTimer NewTimerD err:%v", err)
        return err
    }

    // 定时刷新奖励配置信息

    m.timerD.Start()
    return nil
}

func (m *ActivityConf) refreshLocalPackageInfo(ctx context.Context) error {
    // 获取奖池所有信息
    simplePondItems, err := m.store.GetAllSimplePondItems(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "refreshLocalPackageInfo GetAllSimplePondItems error: %v", err)
        return err
    }

    if len(simplePondItems) == 0 {
        return nil
    }

    allPrizeMap := make(map[string]*store.SimplePondItem)
    bgIdList := make([]uint32, 0)
    for _, v := range simplePondItems {
        if v.AwardType != uint32(adventure_activity.AwardType_AWARD_TYPE_PACKAGE) {
            continue
        }
        bgId, _ := strconv.Atoi(v.AwardId)
        bgIdList = append(bgIdList, uint32(bgId))
        allPrizeMap[v.AwardId] = v
    }

    _, packageItemMap, _, err := m.acLayerMgr.GetPackageItemCfg(ctx, bgIdList)
    if err != nil {
        log.ErrorWithCtx(ctx, "refreshLocalPackageInfo GetPackageItemCfg error: %v", err)
        return err
    }

    // 上锁
    m.rwMutex.Lock()
    m.bg2PackageItemCfg = packageItemMap
    m.allPrizeMap = allPrizeMap
    m.rwMutex.Unlock()

    return nil
}
