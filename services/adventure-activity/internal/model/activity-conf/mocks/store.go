// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/adventure-activity/internal/model/activity-conf/store (interfaces: IStore)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	mysql "gitlab.ttyuyin.com/tyr/x/middleware/mysql"
	store "golang.52tt.com/services/adventure-activity/internal/model/activity-conf/store"
)

// MockIStore is a mock of IStore interface.
type MockIStore struct {
	ctrl     *gomock.Controller
	recorder *MockIStoreMockRecorder
}

// MockIStoreMockRecorder is the mock recorder for MockIStore.
type MockIStoreMockRecorder struct {
	mock *MockIStore
}

// NewMockIStore creates a new mock instance.
func NewMockIStore(ctrl *gomock.Controller) *MockIStore {
	mock := &MockIStore{ctrl: ctrl}
	mock.recorder = &MockIStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIStore) EXPECT() *MockIStoreMockRecorder {
	return m.recorder
}

// AddLevelConf mocks base method.
func (m *MockIStore) AddLevelConf(arg0 context.Context, arg1 mysql.Txx, arg2 []*store.LevelConf) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddLevelConf", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddLevelConf indicates an expected call of AddLevelConf.
func (mr *MockIStoreMockRecorder) AddLevelConf(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddLevelConf", reflect.TypeOf((*MockIStore)(nil).AddLevelConf), arg0, arg1, arg2)
}

// BatchUpdateLevelPond mocks base method.
func (m *MockIStore) BatchUpdateLevelPond(arg0 context.Context, arg1 mysql.Txx, arg2 []*store.LevelPond, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdateLevelPond", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchUpdateLevelPond indicates an expected call of BatchUpdateLevelPond.
func (mr *MockIStoreMockRecorder) BatchUpdateLevelPond(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateLevelPond", reflect.TypeOf((*MockIStore)(nil).BatchUpdateLevelPond), arg0, arg1, arg2, arg3)
}

// Close mocks base method.
func (m *MockIStore) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockIStoreMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIStore)(nil).Close))
}

// CreateLevelConfTbl mocks base method.
func (m *MockIStore) CreateLevelConfTbl(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateLevelConfTbl", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateLevelConfTbl indicates an expected call of CreateLevelConfTbl.
func (mr *MockIStoreMockRecorder) CreateLevelConfTbl(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateLevelConfTbl", reflect.TypeOf((*MockIStore)(nil).CreateLevelConfTbl), arg0)
}

// CreateLevelPondTbl mocks base method.
func (m *MockIStore) CreateLevelPondTbl(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateLevelPondTbl", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateLevelPondTbl indicates an expected call of CreateLevelPondTbl.
func (mr *MockIStoreMockRecorder) CreateLevelPondTbl(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateLevelPondTbl", reflect.TypeOf((*MockIStore)(nil).CreateLevelPondTbl), arg0)
}

// CreateLimitConfTbl mocks base method.
func (m *MockIStore) CreateLimitConfTbl(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateLimitConfTbl", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateLimitConfTbl indicates an expected call of CreateLimitConfTbl.
func (mr *MockIStoreMockRecorder) CreateLimitConfTbl(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateLimitConfTbl", reflect.TypeOf((*MockIStore)(nil).CreateLimitConfTbl), arg0)
}

// GetAllSimplePondItems mocks base method.
func (m *MockIStore) GetAllSimplePondItems(arg0 context.Context) ([]*store.SimplePondItem, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllSimplePondItems", arg0)
	ret0, _ := ret[0].([]*store.SimplePondItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllSimplePondItems indicates an expected call of GetAllSimplePondItems.
func (mr *MockIStoreMockRecorder) GetAllSimplePondItems(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllSimplePondItems", reflect.TypeOf((*MockIStore)(nil).GetAllSimplePondItems), arg0)
}

// GetLevelConfList mocks base method.
func (m *MockIStore) GetLevelConfList(arg0 context.Context) ([]*store.LevelConf, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLevelConfList", arg0)
	ret0, _ := ret[0].([]*store.LevelConf)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLevelConfList indicates an expected call of GetLevelConfList.
func (mr *MockIStoreMockRecorder) GetLevelConfList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLevelConfList", reflect.TypeOf((*MockIStore)(nil).GetLevelConfList), arg0)
}

// GetLevelConfMaxUpdate mocks base method.
func (m *MockIStore) GetLevelConfMaxUpdate(arg0 context.Context) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLevelConfMaxUpdate", arg0)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLevelConfMaxUpdate indicates an expected call of GetLevelConfMaxUpdate.
func (mr *MockIStoreMockRecorder) GetLevelConfMaxUpdate(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLevelConfMaxUpdate", reflect.TypeOf((*MockIStore)(nil).GetLevelConfMaxUpdate), arg0)
}

// GetLevelPondList mocks base method.
func (m *MockIStore) GetLevelPondList(arg0 context.Context) (map[uint32][]*store.LevelPond, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLevelPondList", arg0)
	ret0, _ := ret[0].(map[uint32][]*store.LevelPond)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLevelPondList indicates an expected call of GetLevelPondList.
func (mr *MockIStoreMockRecorder) GetLevelPondList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLevelPondList", reflect.TypeOf((*MockIStore)(nil).GetLevelPondList), arg0)
}

// GetLimitConfList mocks base method.
func (m *MockIStore) GetLimitConfList(arg0 context.Context) ([]*store.UserLimitCfg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLimitConfList", arg0)
	ret0, _ := ret[0].([]*store.UserLimitCfg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLimitConfList indicates an expected call of GetLimitConfList.
func (mr *MockIStoreMockRecorder) GetLimitConfList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLimitConfList", reflect.TypeOf((*MockIStore)(nil).GetLimitConfList), arg0)
}

// GetPondMaxUpdateTime mocks base method.
func (m *MockIStore) GetPondMaxUpdateTime(arg0 context.Context) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPondMaxUpdateTime", arg0)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPondMaxUpdateTime indicates an expected call of GetPondMaxUpdateTime.
func (mr *MockIStoreMockRecorder) GetPondMaxUpdateTime(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPondMaxUpdateTime", reflect.TypeOf((*MockIStore)(nil).GetPondMaxUpdateTime), arg0)
}

// SetLimitConfList mocks base method.
func (m *MockIStore) SetLimitConfList(arg0 context.Context, arg1 []*store.UserLimitCfg) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetLimitConfList", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetLimitConfList indicates an expected call of SetLimitConfList.
func (mr *MockIStoreMockRecorder) SetLimitConfList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetLimitConfList", reflect.TypeOf((*MockIStore)(nil).SetLimitConfList), arg0, arg1)
}

// Transaction mocks base method.
func (m *MockIStore) Transaction(arg0 context.Context, arg1 func(mysql.Txx) error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Transaction", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Transaction indicates an expected call of Transaction.
func (mr *MockIStoreMockRecorder) Transaction(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Transaction", reflect.TypeOf((*MockIStore)(nil).Transaction), arg0, arg1)
}
