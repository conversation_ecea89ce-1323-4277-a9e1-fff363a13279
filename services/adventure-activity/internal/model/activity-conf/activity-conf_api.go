package activity_conf

import(
	context "context"
	adventure_activity "golang.52tt.com/protocol/services/adventure-activity"
	comm "golang.52tt.com/services/adventure-activity/internal/model/comm"
)

type IActivityConf interface {
	GetActiveLevelConf(ctx context.Context) ([]*adventure_activity.LevelConfWithPool,error)
	GetLevelConfWithPool(ctx context.Context) ([]*comm.Pool,error)
	GetLevelInfoUpdateVersion(ctx context.Context) (int64,error)
	GetLimitConf(ctx context.Context) (*adventure_activity.GetAdventureIslandLimitConfResp,error)
	GetPackageFinTime(ctx context.Context, bgId uint32) (expireTs int64)
	GetPondUpdateVersion(ctx context.Context) (int64,error)
	GetSimpleInfoByAwardId(awardId string) *comm.SimpleAwardInfo
	SetLevelCfg(ctx context.Context, req *adventure_activity.SetLevelConfRequest, prizeList []*comm.Prize) error
	SetLevelPond(ctx context.Context, pondList []*comm.Prize) error
	SetLimitConf(ctx context.Context, req *adventure_activity.SetAdventureIslandLimitConfReq) error
	Stop() 
}

