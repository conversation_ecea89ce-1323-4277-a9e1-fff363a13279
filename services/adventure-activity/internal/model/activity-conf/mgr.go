package activity_conf

//go:generate quicksilver-cli test interface ../activity-conf
//go:generate mockgen -destination=../mocks/lottery.go -package=mocks golang.52tt.com/services/adventure-activity/internal/model/activity-conf IActivityConf

import (
    "gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    anti_corruption_layer "golang.52tt.com/services/adventure-activity/internal/model/anti-corruption-layer"
    "sync"
    "golang.52tt.com/services/adventure-activity/internal/model/activity-conf/store"
    "golang.52tt.com/services/adventure-activity/internal/model/activity-conf/cache"
    "golang.52tt.com/services/adventure-activity/internal/conf"
    "context"
    "golang.52tt.com/services/adventure-activity/internal/model/comm"
    pb "golang.52tt.com/protocol/services/adventure-activity"
    "time"
    "sort"
    backpackbasepb "golang.52tt.com/protocol/services/backpack-base"
    "golang.52tt.com/services/tt-rev/common/goroutineex"
)

type ActivityConf struct {
    store      store.IStore
    cache      cache.ICache
    bc         conf.IBusinessConfManager
    acLayerMgr anti_corruption_layer.IACLayer

    timerD   *timer.Timer
    wg       sync.WaitGroup
    shutDown chan struct{}

    rwMutex           sync.RWMutex
    bg2PackageItemCfg map[uint32][]*backpackbasepb.PackageItemCfg
    allPrizeMap       map[string]*store.SimplePondItem
}

func NewMgr(s mysql.DBx, rds mysql.DBx, cacheClient redis.Cmdable, bc conf.IBusinessConfManager,
    acLayerMgr anti_corruption_layer.IACLayer) (*ActivityConf, error) {
    mysqlStore := store.NewStore(s, rds)
    redisCli := cache.NewCache(cacheClient)

    m := &ActivityConf{
        store:      mysqlStore,
        cache:      redisCli,
        bc:         bc,
        acLayerMgr: acLayerMgr,
        wg:         sync.WaitGroup{},
        shutDown:   make(chan struct{}),

        rwMutex:           sync.RWMutex{},
        bg2PackageItemCfg: make(map[uint32][]*backpackbasepb.PackageItemCfg),
        allPrizeMap:       make(map[string]*store.SimplePondItem),
    }

    // 首先执行一遍
    err := m.refreshLocalPackageInfo(context.Background())
    if err != nil {
        log.Errorf("NewMgr refreshLocalPackageInfo err:%v", err)
        return m, err
    }

    err = m.startTimer()
    if err != nil {
        log.Errorf("NewMgr startTimer err:%v", err)
        return m, err
    }

    return m, nil
}

func (m *ActivityConf) Stop() {
    m.timerD.Stop()
    close(m.shutDown)
    m.wg.Wait()
    _ = m.cache.Close()
    _ = m.store.Close()
}

// getPackageCfgLocal
func (m *ActivityConf) getBgCfgLocal(bgId uint32) ([]*backpackbasepb.PackageItemCfg, bool) {
    m.rwMutex.RLock()
    defer m.rwMutex.RUnlock()
    bgCf, ok := m.bg2PackageItemCfg[bgId]
    return bgCf, ok
}

func (m *ActivityConf) GetSimpleInfoByAwardId(awardId string) *comm.SimpleAwardInfo {
    award, ok := m.allPrizeMap[awardId]
    if !ok {
        return nil
    }

    return &comm.SimpleAwardInfo{
        AwardId:   award.AwardId,
        Name:      award.AwardName,
        Icon:      award.AwardIcon,
        Price:     award.AwardPrice,
        PriceType: award.AwardType,
    }
}

func (m *ActivityConf) GetPackageFinTime(ctx context.Context, bgId uint32) (expireTs int64) {
    if bgId == 0 {
        return 0
    }
    itemList, ok := m.getBgCfgLocal(bgId)
    if !ok {
        _, packageItemMap, _, err := m.acLayerMgr.GetPackageItemCfg(ctx, []uint32{bgId})
        if err != nil {
            log.ErrorWithCtx(ctx, "refreshLocalPackageInfo GetPackageItemCfg error: %v", err)
            return 0
        }
        _, ok := packageItemMap[bgId]
        if !ok {
            log.ErrorWithCtx(ctx, "GetPackageItemCfg error: %v", err)
            return 0
        }

        m.rwMutex.Lock()
        m.bg2PackageItemCfg[bgId] = packageItemMap[bgId]
        m.rwMutex.Unlock()
    }
    item := itemList[0]
    expireTs = int64(item.GetFinTime())
    if item.GetDynamicFinTime() != 0 {
        expireTs = time.Now().Unix() + int64(item.GetDynamicFinTime())
    } else {
        if item.GetMonths() > 0 {
            now := time.Now()
            t := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
            expireTs = t.AddDate(0, int(item.GetMonths()), 0).Unix()
        }
    }

    return
}

//
//// GetAwardInfoByBgId 根据包裹id获取礼物信息
//func (m *ActivityConf) GetAwardInfoByBgId(ctx context.Context, bgId uint32) *SimpleAwardInfo {
//    itemList, ok := m.getBgCfgLocal(bgId)
//    if ok {
//        item := itemList[0]
//        if item.GetItemType() == uint32(backpackbasepb.PackageItemType_BACKPACK_PRESENT) {
//            giftCfg, _ := m.getGiftCfgLocal(item.GetSourceId())
//            return &SimpleAwardInfo{
//                GiftId:   giftCfg.GetItemId(),
//                GiftName: giftCfg.GetName(),
//                GiftIcon: giftCfg.GetIconUrl(),
//                Amount:   item.GetItemCount(),
//                FinTime:  item.GetDynamicFinTime(),
//            }
//        } else if item.GetItemType() == uint32(backpackbasepb.PackageItemType_BACKPACK_LOTTERY_FRAGMENT) {
//        }
//
//    }
//
//    presentCfgMap, packageItemMap, fragmentCfgMap, err := m.acLayerMgr.GetPackageItemCfg(ctx, []uint32{bgId})
//    if err != nil {
//        log.ErrorWithCtx(ctx, "refreshLocalPackageInfo GetPackageItemCfg error: %v", err)
//        return nil
//    }
//    if len(presentCfgMap) == 0 {
//        return nil
//    }
//
//    return nil
//}

func (m *ActivityConf) SetLevelCfg(ctx context.Context, req *pb.SetLevelConfRequest, prizeList []*comm.Prize) error {
    now := time.Now()
    levelCfgList := make([]*store.LevelConf, 0, len(req.GetLevelConfList()))

    for _, levelConf := range req.GetLevelConfList() {
        levelCfgList = append(levelCfgList, &store.LevelConf{
            LevelId:      levelConf.GetLevelId(),
            LevelName:    levelConf.GetLevelName(),
            GuaranteeCnt: levelConf.GetMaxNFixed(),
            CreateTime:   now,
        })
    }

    levelAwardList := make([]*store.LevelPond, 0, len(req.GetLevelConfList()))
    for _, v := range prizeList {
        award, err := genStoreLevelPond(ctx, v, now, true)
        if err != nil {
            log.ErrorWithCtx(ctx, "genStoreLevelPond error: %v", err)
            return err
        }

        levelAwardList = append(levelAwardList, award)
    }

    // 事务插入关卡配置和关卡奖励配置
    err := m.store.Transaction(ctx, func(tx mysql.Txx) error {
        err := m.store.AddLevelConf(ctx, tx, levelCfgList)
        if err != nil {
            log.ErrorWithCtx(ctx, "add level conf error: %v", err)
            return err
        }

        err = m.store.BatchUpdateLevelPond(ctx, tx, levelAwardList, store.UpdateTypeCompletedPrize)
        if err != nil {
            log.ErrorWithCtx(ctx, "update level pond error: %v", err)
            return err
        }

        return nil
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "InsertUserCurrentLv transaction error: %v", err)
        return err
    }

    _ = m.cache.DelCurLevelConf(ctx)

    goroutineex.GoroutineWithTimeoutCtx(ctx, 30*time.Second, func(ctx context.Context) {
        _ = m.refreshLocalPackageInfo(ctx)
    })

    return nil
}

// SetLevelPond 设置关卡奖池
func (m *ActivityConf) SetLevelPond(ctx context.Context, pondList []*comm.Prize) error {
    now := time.Now()
    storeList := make([]*store.LevelPond, 0, len(pondList))

    for _, r := range pondList {
        award, err := genStoreLevelPond(ctx, r, now, false)
        if err != nil {
            log.ErrorWithCtx(ctx, "genStoreLevelPond error: %v", err)
            return err
        }
        storeList = append(storeList, award)
    }

    err := m.store.Transaction(ctx, func(tx mysql.Txx) error {
        return m.store.BatchUpdateLevelPond(ctx, tx, storeList, store.UpdateTypePond)
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "UpdateLevelPond fail. err:%v", err)
        return err
    }

    _ = m.cache.DelCurLevelConf(ctx)
    goroutineex.GoroutineWithTimeoutCtx(ctx, 30*time.Second, func(ctx context.Context) {
        _ = m.refreshLocalPackageInfo(ctx)
    })
    return nil
}

func genStoreLevelPond(ctx context.Context, v *comm.Prize, t time.Time, isCompletedPrize bool) (*store.LevelPond, error) {

    return &store.LevelPond{
        IsCompletedPrize: isCompletedPrize, // 通关奖励标识
        LevelId:          v.LevelId,
        Amount:           v.Amount,
        AwardId:          v.PrizeId,
        AwardType:        v.PrizeType,
        DressSubType:     v.DressSubType,
        AwardPrice:       v.PrizePrice,
        PriceType:        v.PriceType,
        AwardIcon:        v.PrizeIcon,
        AwardName:        v.PrizeName,
        GiftId:           v.GiftId,
        Weight:           v.Weight,
        CardIdOrder:      v.CardIdOrder,
        CreateTime:       t,
    }, nil
}

// GetLevelConfWithPool 获取等级配置及其奖池
func (m *ActivityConf) GetLevelConfWithPool(ctx context.Context) ([]*comm.Pool, error) {
    out := make([]*comm.Pool, 0)

    // 获取所有关卡配置
    confList, err := m.store.GetLevelConfList(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetLevelConfList error: %v", err)
        return out, err
    }

    // 获取所有奖池配置
    pondMap, err := m.store.GetLevelPondList(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetLevelPondList error: %v", err)
        return out, err
    }

    for _, v := range confList {
        pondList := pondMap[v.LevelId]

        completedPrize := make([]*comm.Prize, 0)
        poolPrize := make([]*comm.Prize, 0)
        for _, vv := range pondList {
            if vv.IsCompletedPrize {
                completedPrize = append(completedPrize, transLevelPond2CommPrize(vv))
            } else {
                poolPrize = append(poolPrize, transLevelPond2CommPrize(vv))
            }
        }

        levelWithPond := &comm.Pool{
            Cfg: comm.LevelConf{
                LevelId:        v.LevelId,
                LevelName:      v.LevelName,
                ConstantN:      v.GuaranteeCnt,
                CompletedPrize: completedPrize,
            },
            PrizeList:         poolPrize,
            TargetPrizeIdList: nil,
            TotalWeight:       0,
        }
        out = append(out, levelWithPond)
    }

    return out, nil
}

func transLevelPond2CommPrize(v *store.LevelPond) *comm.Prize {

    return &comm.Prize{
        CardIdOrder:  v.CardIdOrder,
        LevelId:      v.LevelId,
        PrizeId:      v.AwardId,
        PrizeType:    v.AwardType,
        DressSubType: v.DressSubType,
        Amount:       v.Amount,
        Weight:       v.Weight,
        PrizeName:    v.AwardName,
        PrizeIcon:    v.AwardIcon,
        PrizePrice:   v.AwardPrice,
        PriceType:    v.PriceType,
        GiftId:       v.GiftId,
    }
}

//GetActiveLevelConf 获取当前的活动配置(with 缓存)
func (m *ActivityConf) GetActiveLevelConf(ctx context.Context) ([]*pb.LevelConfWithPool, error) {
    exist, levelList, err := m.cache.GetCurLevelConf(ctx)
    if err != nil {
        return levelList, err
    }

    if exist {
        return levelList, nil
    }

    // 从存储中获取
    // 获取所有关卡配置
    confList, err := m.store.GetLevelConfList(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetLevelConfList error: %v", err)
        return levelList, err
    }

    // 获取所有奖池配置
    pondMap, err := m.store.GetLevelPondList(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetLevelPondList error: %v", err)
        return levelList, err
    }

    if levelList == nil {
        levelList = make([]*pb.LevelConfWithPool, 0, len(confList))
    }
    for _, v := range confList {
        var completePrize *store.LevelPond
        awardList := make([]*pb.AwardInfo, 0, len(pondMap[v.LevelId]))
        for _, vv := range pondMap[v.LevelId] {
            if vv.IsDel {
                continue
            }
            if vv.IsCompletedPrize {
                completePrize = vv
            } else {
                awardList = append(awardList, transLevelPond2PbAwardInfo(vv))
            }
        }
        if completePrize == nil || len(awardList) == 0 {
            log.ErrorWithCtx(ctx, "GetActiveLevelConf error: %v", err)
            continue
        }

        // awardList 按cardIdOrder 升序
        sort.SliceStable(awardList, func(i, j int) bool {
            return awardList[i].CardId < awardList[j].CardId
        })

        levelList = append(levelList, &pb.LevelConfWithPool{
            LevelCfg: &pb.LevelCfg{
                LevelId:    v.LevelId,
                LevelName:  v.LevelName,
                MaxNFixed:  v.GuaranteeCnt,
                LevelAward: transLevelPond2PbAwardInfo(completePrize),
            },
            AwardList: awardList,
        })
    }

    // 设置缓存
    _ = m.cache.SetCurLevelConf(ctx, levelList)
    return levelList, nil
}

func transLevelPond2PbAwardInfo(v *store.LevelPond) *pb.AwardInfo {
    return &pb.AwardInfo{
        AwardId:      v.AwardId,
        AwardType:    v.AwardType,
        DressSubType: v.DressSubType,
        Amount:       v.Amount,
        AwardWorth:   v.AwardPrice,
        AwardName:    v.AwardName,
        AwardDesc:    "", // todo
        AwardIcon:    v.AwardIcon,
        //ExpTime:      0,
        //Weight:       0,
        CardId: v.CardIdOrder,
    }
}

// GetLevelInfoUpdateVersion 获取活动配置更新版本
// return update_version, error
func (m *ActivityConf) GetLevelInfoUpdateVersion(ctx context.Context) (int64, error) {
    updateVersion, err := m.store.GetLevelConfMaxUpdate(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetLevelInfoUpdateVersion error: %v", err)
        return 0, err
    }

    return updateVersion, nil
}

// GetPondUpdateVersion 获取活动奖池的更新版本
func (m *ActivityConf) GetPondUpdateVersion(ctx context.Context) (int64, error) {
    maxUpdateTime, err := m.store.GetPondMaxUpdateTime(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetPondUpdateVersion err: %v", err)
        return 0, err
    }

    return maxUpdateTime, nil
}

func (m *ActivityConf) SetLimitConf(ctx context.Context, req *pb.SetAdventureIslandLimitConfReq) error {

    err := m.store.SetLimitConfList(ctx, []*store.UserLimitCfg{
        {
            LimitType: comm.DailyBuyLimitType,
            LimitVal:  req.GetDailyBuyChanceLimit(),
        },
        {
            LimitType: comm.DailyUseLimitType,
            LimitVal:  req.GetDailyUseChanceLimit(),
        },
        {
            LimitType: comm.SingleBuyLimitType,
            LimitVal:  req.GetSingleBuyChanceLimit(),
        },
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SetLimitConf fail to SetLimitConf req:%+v err:%v", req, err)
        return err
    }

    return nil
}

func (m *ActivityConf) GetLimitConf(ctx context.Context) (*pb.GetAdventureIslandLimitConfResp, error) {
    out := &pb.GetAdventureIslandLimitConfResp{}

    confList, err := m.store.GetLimitConfList(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetLimitConf fail to GetLimitConfList err:%v", err)
        return nil, err
    }

    for _, v := range confList {
        switch v.LimitType {
        case comm.SingleBuyLimitType:
            out.SingleBuyChanceLimit = v.LimitVal
            continue
        case comm.DailyBuyLimitType:
            out.DailyBuyChanceLimit = v.LimitVal
            continue
        case comm.DailyUseLimitType:
            out.DailyUseChanceLimit = v.LimitVal
            continue
        default:
            continue
        }
    }

    return out, nil
}
