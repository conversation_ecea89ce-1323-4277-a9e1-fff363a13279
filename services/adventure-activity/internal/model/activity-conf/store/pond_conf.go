package store

import (
    "fmt"
    "context"
    "time"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "strings"
)

const (
    LevelPondTblName = "adventure_activity_level_pond"
    pondQueryField   = "id, level_id, is_completed_prize, award_id,amount, award_type, dress_sub_type, award_price, price_type, award_icon, award_name, gift_id, weight,is_del, card_id_order"
)

const (
    UpdateTypePond           = uint32(iota) // 奖池
    UpdateTypeCompletedPrize                // 通关奖励
)

// CreateLevelPondTbl 关卡奖池配置
var CreateLevelPondTbl = `CREATE Table %s(
    id int(10) unsigned NOT NULL AUTO_INCREMENT,
    level_id int(10) unsigned NOT NULL DEFAULT '0' COMMENT '关卡id',
    is_completed_prize tinyint unsigned NOT NULL COMMENT '是否是通关奖励 0-否 1-是',
    
    award_id varchar(128) NOT NULL COMMENT '奖励id',
    amount tinyint unsigned NOT NULL COMMENT '奖励数量',
    award_type tinyint unsigned NOT NULL COMMENT '奖励类型',
    dress_sub_type int(10) unsigned NOT NULL COMMENT '装扮子类型，see award-center.proto EGiftType',
	award_price int(10) unsigned NOT NULL COMMENT '奖励价值',
    price_type int(10) unsigned NOT NULL COMMENT '奖励价值类型；1-红钻，2-T豆',
    award_icon varchar(128) NOT NULL COMMENT '奖励icon',
    award_name varchar(128) NOT NULL COMMENT '奖励名称',
    gift_id int(10) unsigned NOT NULL COMMENT '礼物id,包裹奖励中的礼物id',
    weight int(10) unsigned NOT NULL DEFAULT '0' COMMENT '权重',
    
    card_id_order tinyint unsigned NOT NULL default 0 COMMENT '卡牌id顺序',
    
    is_del tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
    
    create_time timestamp NOT NULL default CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time timestamp NOT NULL default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    primary key (id),
    key idx_level_id_award_id(level_id,award_id)
    
)engine=InnoDB default charset=utf8 COMMENT "冒险岛关卡奖池配置表"`

// CreateLevelPondTbl 建表
func (s *Store) CreateLevelPondTbl(ctx context.Context) error {
    _, err := s.db.ExecContext(ctx, fmt.Sprintf(CreateLevelPondTbl, LevelPondTblName))
    return err
}

type LevelPond struct {
    Id               uint32 `db:"id"`
    IsCompletedPrize bool   `db:"is_completed_prize"`

    LevelId      uint32 `db:"level_id"`
    Amount       uint32 `db:"amount"`
    AwardId      string `db:"award_id"`
    AwardType    uint32 `db:"award_type"`
    DressSubType uint32 `db:"dress_sub_type"`
    AwardPrice   uint32 `db:"award_price"`
    PriceType    uint32 `db:"price_type"`
    AwardIcon    string `db:"award_icon"`
    AwardName    string `db:"award_name"`
    GiftId       uint32 `db:"gift_id"`
    Weight       uint32 `db:"weight"`
    IsDel        bool   `db:"is_del"`

    CardIdOrder uint32 `db:"card_id_order"`

    CreateTime time.Time `db:"create_time"`
    UpdateTime time.Time `db:"update_time"`
}

// BatchUpdateLevelPond 批量更新数据
func (s *Store) BatchUpdateLevelPond(ctx context.Context, tx mysql.Txx, pondList []*LevelPond, updateType uint32) error {
    if len(pondList) == 0 {
        return nil
    }
    delLevelList := make([]uint32, 0)
    delLevelMap := make(map[uint32]struct{})
    for _, v := range pondList {
        if _, ok := delLevelMap[v.LevelId]; ok {
            continue
        }
        delLevelList = append(delLevelList, v.LevelId)
        delLevelMap[v.LevelId] = struct{}{}
    }

    placeholder := make([]string, 0, len(pondList))
    params := make([]interface{}, 0)
    for _, r := range pondList {
        placeholder = append(placeholder, "(?,?,?,?,?,?,?,?,?,?,?,?,?,?)")
        params = append(params, r.LevelId, r.IsCompletedPrize, r.AwardId, r.Amount, r.AwardType, r.DressSubType, r.AwardPrice, r.PriceType, r.AwardIcon, r.AwardName, r.Weight, r.GiftId, r.CardIdOrder, r.CreateTime)
    }

    // 事务更新，删除旧数据， 插入新数据
    delSql := fmt.Sprintf(`UPDATE %s SET is_del = 1 WHERE level_id IN (%s) AND is_completed_prize = ?`, LevelPondTblName, genParamJoinStr(delLevelList))
    _, err := tx.ExecContext(ctx, delSql, updateType)
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchUpdateLevelPond delete error: %v", err)
        return err
    }

    query := fmt.Sprintf("insert into %s (level_id,is_completed_prize, award_id, amount,award_type,dress_sub_type, award_price,price_type, award_icon, award_name, weight, gift_id,card_id_order, create_time) values %s", LevelPondTblName, strings.Join(placeholder, ","))
    // 插入新数据
    _, err = tx.ExecContext(ctx, query, params...)
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchUpdateLevelPond insert error: %v", err)
        return err
    }

    return err
}

// GetLevelPondList 获取奖池
func (s *Store) GetLevelPondList(ctx context.Context) (map[uint32][]*LevelPond, error) {
    query := fmt.Sprintf(`SELECT %s FROM %s WHERE is_del=0`, pondQueryField, LevelPondTblName)
    var list []*LevelPond
    err := s.db.SelectContext(ctx, &list, query)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetLevelPond error: %v", err)
        return nil, err
    }

    levelPoolMap := make(map[uint32][]*LevelPond)
    for _, v := range list {
        if _, ok := levelPoolMap[v.LevelId]; !ok {
            levelPoolMap[v.LevelId] = make([]*LevelPond, 0)
        }

        levelPoolMap[v.LevelId] = append(levelPoolMap[v.LevelId], v)
    }

    return levelPoolMap, err
}

// GetPondMaxUpdateTime 获取奖池最大更新时间
func (s *Store) GetPondMaxUpdateTime(ctx context.Context) (int64, error) {
    query := fmt.Sprintf(`SELECT IFNULL(max(UNIX_TIMESTAMP(create_time)), 0) FROM %s`, LevelPondTblName)
    var maxUpdateTime int64
    err := s.db.GetContext(ctx, &maxUpdateTime, query)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetMaxUpdateTime error: %v", err)
        return 0, err
    }
    return maxUpdateTime, nil
}

type SimplePondItem struct {
    Id           uint32 `db:"id"`
    AwardId      string `db:"award_id"`
    AwardType    uint32 `db:"award_type"`
    DressSubType uint32 `db:"dress_sub_type"`
    AwardPrice   uint32 `db:"award_price"`
    PriceType    uint32 `db:"price_type"`
    AwardIcon    string `db:"award_icon"`
    AwardName    string `db:"award_name"`
}

//GetAllSimplePondItems 获取所有奖池礼物简单信息
func (s *Store) GetAllSimplePondItems(ctx context.Context) ([]*SimplePondItem, error) {
    query := fmt.Sprintf("SELECT id, award_id, award_type,dress_sub_type,award_price,price_type,award_icon,award_name from %s", LevelPondTblName)
    list := make([]*SimplePondItem, 0)

    err := s.db.SelectContext(ctx, &list, query)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetAllSimplePondItems error: %v", err)
        return nil, err
    }
    return list, err
}
