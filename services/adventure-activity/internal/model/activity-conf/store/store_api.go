package store

import(
	context "context"
	mysql "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
)

type IStore interface {
	AddLevelConf(ctx context.Context, tx mysql.Txx, confList []*LevelConf) error
	BatchUpdateLevelPond(ctx context.Context, tx mysql.Txx, pondList []*LevelPond, updateType uint32) error
	Close() error
	CreateLevelConfTbl(ctx context.Context) error
	CreateLevelPondTbl(ctx context.Context) error
	CreateLimitConfTbl(ctx context.Context) error
	GetAllSimplePondItems(ctx context.Context) ([]*SimplePondItem,error)
	GetLevelConfList(ctx context.Context) ([]*LevelConf,error)
	GetLevelConfMaxUpdate(ctx context.Context) (int64,error)
	GetLevelPondList(ctx context.Context) (map[uint32][]*LevelPond,error)
	GetLimitConfList(ctx context.Context) ([]*UserLimitCfg,error)
	GetPondMaxUpdateTime(ctx context.Context) (int64,error)
	SetLimitConfList(ctx context.Context, list []*UserLimitCfg) error
	Transaction(ctx context.Context, f func(tx mysql.Txx) error) error
}

