// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/adventure-activity/internal/model/activity-game (interfaces: IActivityGame)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	adventure_activity "golang.52tt.com/protocol/services/adventure-activity"
	cb "golang.52tt.com/protocol/services/unified_pay/cb"
	comm "golang.52tt.com/services/adventure-activity/internal/model/comm"
)

// MockIActivityGame is a mock of IActivityGame interface.
type MockIActivityGame struct {
	ctrl     *gomock.Controller
	recorder *MockIActivityGameMockRecorder
}

// MockIActivityGameMockRecorder is the mock recorder for MockIActivityGame.
type MockIActivityGameMockRecorder struct {
	mock *MockIActivityGame
}

// NewMockIActivityGame creates a new mock instance.
func NewMockIActivityGame(ctrl *gomock.Controller) *MockIActivityGame {
	mock := &MockIActivityGame{ctrl: ctrl}
	mock.recorder = &MockIActivityGameMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIActivityGame) EXPECT() *MockIActivityGameMockRecorder {
	return m.recorder
}

// BuyChance mocks base method.
func (m *MockIActivityGame) BuyChance(arg0 context.Context, arg1 *adventure_activity.BuyChanceRequest) (*adventure_activity.BuyChanceResponse, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BuyChance", arg0, arg1)
	ret0, _ := ret[0].(*adventure_activity.BuyChanceResponse)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// BuyChance indicates an expected call of BuyChance.
func (mr *MockIActivityGameMockRecorder) BuyChance(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BuyChance", reflect.TypeOf((*MockIActivityGame)(nil).BuyChance), arg0, arg1)
}

// GetPlatformWinningRecord mocks base method.
func (m *MockIActivityGame) GetPlatformWinningRecord(arg0 context.Context, arg1 *adventure_activity.GetPlatformWinningRecordRequest) (*adventure_activity.GetPlatformWinningRecordResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPlatformWinningRecord", arg0, arg1)
	ret0, _ := ret[0].(*adventure_activity.GetPlatformWinningRecordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPlatformWinningRecord indicates an expected call of GetPlatformWinningRecord.
func (mr *MockIActivityGameMockRecorder) GetPlatformWinningRecord(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlatformWinningRecord", reflect.TypeOf((*MockIActivityGame)(nil).GetPlatformWinningRecord), arg0, arg1)
}

// GetUserPlayFile mocks base method.
func (m *MockIActivityGame) GetUserPlayFile(arg0 context.Context, arg1 uint32) (*comm.UserPlayFile, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPlayFile", arg0, arg1)
	ret0, _ := ret[0].(*comm.UserPlayFile)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserPlayFile indicates an expected call of GetUserPlayFile.
func (mr *MockIActivityGameMockRecorder) GetUserPlayFile(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPlayFile", reflect.TypeOf((*MockIActivityGame)(nil).GetUserPlayFile), arg0, arg1)
}

// GetUserPlayFileWithCache mocks base method.
func (m *MockIActivityGame) GetUserPlayFileWithCache(arg0 context.Context, arg1 uint32) (*comm.UserPlayFile, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPlayFileWithCache", arg0, arg1)
	ret0, _ := ret[0].(*comm.UserPlayFile)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPlayFileWithCache indicates an expected call of GetUserPlayFileWithCache.
func (mr *MockIActivityGameMockRecorder) GetUserPlayFileWithCache(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPlayFileWithCache", reflect.TypeOf((*MockIActivityGame)(nil).GetUserPlayFileWithCache), arg0, arg1)
}

// GetUserRecord mocks base method.
func (m *MockIActivityGame) GetUserRecord(arg0 context.Context, arg1 *adventure_activity.GetUserAdventureRecordRequest) (*adventure_activity.GetUserAdventureRecordResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserRecord", arg0, arg1)
	ret0, _ := ret[0].(*adventure_activity.GetUserAdventureRecordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserRecord indicates an expected call of GetUserRecord.
func (mr *MockIActivityGameMockRecorder) GetUserRecord(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserRecord", reflect.TypeOf((*MockIActivityGame)(nil).GetUserRecord), arg0, arg1)
}

// GetUserRemain mocks base method.
func (m *MockIActivityGame) GetUserRemain(arg0 context.Context, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserRemain", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserRemain indicates an expected call of GetUserRemain.
func (mr *MockIActivityGameMockRecorder) GetUserRemain(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserRemain", reflect.TypeOf((*MockIActivityGame)(nil).GetUserRemain), arg0, arg1)
}

// LotteryDraw mocks base method.
func (m *MockIActivityGame) LotteryDraw(arg0 context.Context, arg1 *adventure_activity.LotteryDrawRequest) (*adventure_activity.LotteryDrawResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LotteryDraw", arg0, arg1)
	ret0, _ := ret[0].(*adventure_activity.LotteryDrawResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LotteryDraw indicates an expected call of LotteryDraw.
func (mr *MockIActivityGameMockRecorder) LotteryDraw(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LotteryDraw", reflect.TypeOf((*MockIActivityGame)(nil).LotteryDraw), arg0, arg1)
}

// PayCallBackHandler mocks base method.
func (m *MockIActivityGame) PayCallBackHandler(arg0 context.Context, arg1 string) (*cb.PayNotifyResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PayCallBackHandler", arg0, arg1)
	ret0, _ := ret[0].(*cb.PayNotifyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PayCallBackHandler indicates an expected call of PayCallBackHandler.
func (mr *MockIActivityGameMockRecorder) PayCallBackHandler(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayCallBackHandler", reflect.TypeOf((*MockIActivityGame)(nil).PayCallBackHandler), arg0, arg1)
}

// Stop mocks base method.
func (m *MockIActivityGame) Stop() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Stop")
}

// Stop indicates an expected call of Stop.
func (mr *MockIActivityGameMockRecorder) Stop() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stop", reflect.TypeOf((*MockIActivityGame)(nil).Stop))
}
