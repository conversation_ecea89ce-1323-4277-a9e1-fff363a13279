// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/adventure-activity/internal/model/lottery (interfaces: ILottery)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	comm "golang.52tt.com/services/adventure-activity/internal/model/comm"
	lottery "golang.52tt.com/services/adventure-activity/internal/model/lottery"
)

// MockILottery is a mock of ILottery interface.
type MockILottery struct {
	ctrl     *gomock.Controller
	recorder *MockILotteryMockRecorder
}

// MockILotteryMockRecorder is the mock recorder for MockILottery.
type MockILotteryMockRecorder struct {
	mock *MockILottery
}

// NewMockILottery creates a new mock instance.
func NewMockILottery(ctrl *gomock.Controller) *MockILottery {
	mock := &MockILottery{ctrl: ctrl}
	mock.recorder = &MockILotteryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockILottery) EXPECT() *MockILotteryMockRecorder {
	return m.recorder
}

// CheckLotteryUpdate mocks base method.
func (m *MockILottery) CheckLotteryUpdate() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckLotteryUpdate")
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckLotteryUpdate indicates an expected call of CheckLotteryUpdate.
func (mr *MockILotteryMockRecorder) CheckLotteryUpdate() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckLotteryUpdate", reflect.TypeOf((*MockILottery)(nil).CheckLotteryUpdate))
}

// GetLevelCfgList mocks base method.
func (m *MockILottery) GetLevelCfgList() []*comm.LevelConf {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLevelCfgList")
	ret0, _ := ret[0].([]*comm.LevelConf)
	return ret0
}

// GetLevelCfgList indicates an expected call of GetLevelCfgList.
func (mr *MockILotteryMockRecorder) GetLevelCfgList() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLevelCfgList", reflect.TypeOf((*MockILottery)(nil).GetLevelCfgList))
}

// GetNextPrizePool mocks base method.
func (m *MockILottery) GetNextPrizePool(arg0, arg1 uint32) (*comm.Pool, bool) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNextPrizePool", arg0, arg1)
	ret0, _ := ret[0].(*comm.Pool)
	ret1, _ := ret[1].(bool)
	return ret0, ret1
}

// GetNextPrizePool indicates an expected call of GetNextPrizePool.
func (mr *MockILotteryMockRecorder) GetNextPrizePool(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNextPrizePool", reflect.TypeOf((*MockILottery)(nil).GetNextPrizePool), arg0, arg1)
}

// GetPrizePool mocks base method.
func (m *MockILottery) GetPrizePool(arg0 uint32) (*comm.Pool, bool) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPrizePool", arg0)
	ret0, _ := ret[0].(*comm.Pool)
	ret1, _ := ret[1].(bool)
	return ret0, ret1
}

// GetPrizePool indicates an expected call of GetPrizePool.
func (mr *MockILotteryMockRecorder) GetPrizePool(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPrizePool", reflect.TypeOf((*MockILottery)(nil).GetPrizePool), arg0)
}

// PrizeDrawBatch mocks base method.
func (m *MockILottery) PrizeDrawBatch(arg0 context.Context, arg1 uint32, arg2 lottery.PrizeDrawReq, arg3 uint32) (lottery.PrizeDrawResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PrizeDrawBatch", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(lottery.PrizeDrawResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PrizeDrawBatch indicates an expected call of PrizeDrawBatch.
func (mr *MockILotteryMockRecorder) PrizeDrawBatch(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PrizeDrawBatch", reflect.TypeOf((*MockILottery)(nil).PrizeDrawBatch), arg0, arg1, arg2, arg3)
}

// PrizeDrawOnce mocks base method.
func (m *MockILottery) PrizeDrawOnce(arg0 context.Context, arg1 *comm.Pool, arg2 *lottery.PrizeDrawReq) (*comm.Prize, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PrizeDrawOnce", arg0, arg1, arg2)
	ret0, _ := ret[0].(*comm.Prize)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PrizeDrawOnce indicates an expected call of PrizeDrawOnce.
func (mr *MockILotteryMockRecorder) PrizeDrawOnce(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PrizeDrawOnce", reflect.TypeOf((*MockILottery)(nil).PrizeDrawOnce), arg0, arg1, arg2)
}

// ShutDown mocks base method.
func (m *MockILottery) ShutDown() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ShutDown")
}

// ShutDown indicates an expected call of ShutDown.
func (mr *MockILotteryMockRecorder) ShutDown() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ShutDown", reflect.TypeOf((*MockILottery)(nil).ShutDown))
}

// StartCommTimer mocks base method.
func (m *MockILottery) StartCommTimer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "StartCommTimer")
}

// StartCommTimer indicates an expected call of StartCommTimer.
func (mr *MockILotteryMockRecorder) StartCommTimer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartCommTimer", reflect.TypeOf((*MockILottery)(nil).StartCommTimer))
}

// TimerHandle mocks base method.
func (m *MockILottery) TimerHandle(arg0 time.Duration, arg1 func() error) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "TimerHandle", arg0, arg1)
}

// TimerHandle indicates an expected call of TimerHandle.
func (mr *MockILotteryMockRecorder) TimerHandle(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TimerHandle", reflect.TypeOf((*MockILottery)(nil).TimerHandle), arg0, arg1)
}
