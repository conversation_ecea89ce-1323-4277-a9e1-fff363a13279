package comm

import (
    "encoding/json"
    "github.com/pkg/errors"
)

const (
    PriceTypeTBean = uint32(2)
)

const (
    ChanceTBean = uint32(2000)
)

const (
    AwardTypeBuyChance uint32 = uint32(iota)
    AwardTypeLighted
    AwardTypeCompleted
    AwardTypeReachTop
    AwardTypeBonus // 加码奖励
)

const (
    DailyBuyLimitType  = uint32(iota + 1) // 单日购买限制
    DailyUseLimitType                     // 单日使用限制
    SingleBuyLimitType                    // 单次购买限制
)

type LevelConf struct {
    LevelId   uint32
    LevelName string
    ConstantN uint32

    CompletedPrize []*Prize // 通关奖励（升级奖励）
}

type SimpleAwardInfo struct {
    AwardId   string
    Name      string
    Icon      string
    Price     uint32
    PriceType uint32
}

type Prize struct {
    CardIdOrder  uint32
    LevelId      uint32
    PlayCount    uint32
    ResultType   uint32 // 奖励类型, see LotteryResultType
    PrizeId      string
    PrizeType    uint32
    DressSubType uint32 // 装扮奖励子类型，see award-center.proto EGiftType
    Amount       uint32
    Weight       uint32
    PrizeName    string
    PrizeIcon    string
    PrizePrice   uint32
    PriceType    uint32

    GiftId uint32 // 为包裹礼物时的礼物id

    BingoType uint32
    Source    uint32 // 奖励类型

    //DressInfo *DressInfo  // 装扮奖励时有值
    //PackItem  []*PackItem // 为包裹奖励时有值
}

type Pool struct {
    Cfg               LevelConf
    PrizeList         []*Prize
    TargetPrizeIdList []string // 目标需要集齐的奖品id
    TotalWeight       uint64
}

type UserPlayFile struct {
    LevelId   uint32            `json:"level_id"`
    PlayCount uint32            `json:"play_count"`
    UserN     uint32            `json:"user_n"`
    PrizeMap  map[string]uint32 `json:"prize_map,omitempty"`
}

// ToJSONString 将结构体序列化为 JSON 字符串（辅助方法）
func (up *UserPlayFile) ToJSONString() (string, error) {
    bytes, err := json.Marshal(up)
    if err != nil {
        return "", err
    }
    return string(bytes), nil
}

// FromJSONString 从 JSON 字符串反序列化（辅助方法）
func (up *UserPlayFile) FromJSONString(data string) error {
    if data == "" {
        return errors.New("empty JSON data")
    }
    return json.Unmarshal([]byte(data), up)
}
