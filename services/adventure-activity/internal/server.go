package internal

import (
    "context"
    "gitlab.ttyuyin.com/avengers/tyr/core/log"
    "golang.52tt.com/protocol/services/demo/echo"
    "google.golang.org/grpc/codes"

    "golang.52tt.com/protocol/services/reconcile-v2"
    "golang.52tt.com/services/adventure-activity/internal/conf"
    kfk_producer "golang.52tt.com/services/adventure-activity/internal/kfk-producer"
    UnifiedPayCallback "golang.52tt.com/protocol/services/unified_pay/cb"
    adventure_activity "golang.52tt.com/protocol/services/adventure-activity"
    anti_corruption_layer "golang.52tt.com/services/adventure-activity/internal/model/anti-corruption-layer"
    activity_conf "golang.52tt.com/services/adventure-activity/internal/model/activity-conf"
    activity_game "golang.52tt.com/services/adventure-activity/internal/model/activity-game"
    redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
    mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
    "golang.52tt.com/services/adventure-activity/internal/model/lottery"
    "time"
    "golang.52tt.com/services/adventure-activity/internal/model/comm"
    "strconv"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/protocol/common/status"
    backpackbasepb "golang.52tt.com/protocol/services/backpack-base"
    "fmt"
)

var (
    errUnimplemented = protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
)

func NewServer(ctx context.Context, cfg *conf.StartConfig) (*Server, error) {
    log.Infof("server startup with cfg: %+v", *cfg)

    kfkPub, err := kfk_producer.NewKafkaProducer(cfg)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to kfk_producer.NewKafkaProducer, err:%v", err)
        return nil, err
    }

    bc, err := conf.NewBusinessConfManager()
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to conf.NewBusinessConfManager, err:%v", err)
        return nil, err
    }

    redisClient, err := redisConnect.NewClient(ctx, cfg.RedisConfig)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to redisConnect.NewClient, %+v, err:%v", cfg.RedisConfig, err)
        return nil, err
    }

    mysqlDBCli, err := mysqlConnect.NewClient(ctx, cfg.MysqlConfig)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to mysqlConnect.NewClient, %+v, err:%v", cfg.MysqlConfig, err)
        return nil, err
    }

    mysqlReadOnlyDBCli, err := mysqlConnect.NewClient(ctx, cfg.MysqlReadOnlyConfig)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to mysqlConnect.NewClient, %+v, err:%v", cfg.MysqlReadOnlyConfig, err)
        return nil, err
    }

    aclMgr, err := anti_corruption_layer.NewMgr(ctx, bc)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to anti_corruption_layer.NewMgr, err:%v", err)
        return nil, err
    }

    actConfMgr, err := activity_conf.NewMgr(mysqlDBCli, mysqlReadOnlyDBCli, redisClient, bc, aclMgr)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to activity_conf.NewMgr, err:%v", err)
        return nil, err
    }

    lotteryMgr, err := lottery.NewLottery(bc, actConfMgr)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to lottery.NewLottery, err:%v", err)
        return nil, err
    }

    actGameMgr, err := activity_game.NewMgr(mysqlDBCli, mysqlReadOnlyDBCli, redisClient, bc, aclMgr, actConfMgr, lotteryMgr)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to activity_game.NewMgr, err:%v", err)
        return nil, err
    }

    s := &Server{
        bc:         bc,
        acLayerMgr: aclMgr,
        actConfMgr: actConfMgr,
        actGameMgr: actGameMgr,
        kfkPub:     kfkPub,
    }

    return s, nil
}

type Server struct {
    bc         conf.IBusinessConfManager
    acLayerMgr anti_corruption_layer.IACLayer
    actConfMgr activity_conf.IActivityConf
    actGameMgr activity_game.IActivityGame

    kfkPub kfk_producer.IKafkaProducer
}

func (s *Server) ShutDown() {

}

// Notify T豆回调
func (s *Server) Notify(ctx context.Context, notify *UnifiedPayCallback.PayNotify) (*UnifiedPayCallback.PayNotifyResponse, error) {
    return s.actGameMgr.PayCallBackHandler(ctx, notify.GetOutTradeNo())
}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
    return req, nil
}

func (s *Server) BuyChance(ctx context.Context, in *adventure_activity.BuyChanceRequest) (*adventure_activity.BuyChanceResponse, error) {
    out, payOrderId, err := s.actGameMgr.BuyChance(ctx, in)
    if err != nil {
        log.Errorf("BuyChance fail to BuyChance. in:%+v,err:%v", in, err)
        return out, err
    }

    s.kfkPub.ProduceTBeanConsumeEvent(in.GetUid(), in.GetFee(), uint32(time.Now().Unix()), payOrderId)
    return out, nil
}

func (s *Server) LotteryDraw(ctx context.Context, request *adventure_activity.LotteryDrawRequest) (*adventure_activity.LotteryDrawResponse, error) {
    log.InfoWithCtx(ctx, "LotteryDraw in:%+v", request)
    return s.actGameMgr.LotteryDraw(ctx, request)
}

func (s *Server) GetCurrGameInfo(ctx context.Context, request *adventure_activity.GetCurrGameInfoRequest) (*adventure_activity.GetCurrGameInfoResponse, error) {
    out := &adventure_activity.GetCurrGameInfoResponse{}

    // 获取当前游戏关卡配置信息
    levelConfList, err := s.actConfMgr.GetActiveLevelConf(ctx)
    if err != nil {
        log.Errorf("GetCurrGameInfo fail to GetActiveLevelConf. err:%v", err)
        return out, err
    }

    // 获取用户游戏进度
    playFile, err := s.actGameMgr.GetUserPlayFileWithCache(ctx, request.GetUid())
    if err != nil {
        log.Errorf("GetCurrGameInfo fail to GetUserPlayFileWithCache. err:%v", err)
        return out, err
    }
    userRemain, err := s.actGameMgr.GetUserRemain(ctx, request.GetUid())
    if err != nil {
        log.Errorf("GetCurrGameInfo fail to GetUserRemain. err:%v", err)
        return out, err
    }

    userPlayFile := &adventure_activity.UserPlayFile{
        LevelId:        playFile.LevelId,
        CurrentChance:  userRemain,
        UserN:          playFile.UserN,
        CardCollection: playFile.PrizeMap,
    }

    // 获取活动其他配置信息
    activityConf := s.bc.GetActivityCfg()

    out = &adventure_activity.GetCurrGameInfoResponse{
        ActivityId:   activityConf.ActivityId,
        ActivityName: activityConf.ActivityName,
        BeginTime:    activityConf.BeginTime.Unix(),
        EndTime:      activityConf.EndTime.Unix(),
        BuyPropCfg: &adventure_activity.BuyPropCfg{
            GiftInfo: &adventure_activity.AwardInfo{
                Amount:    1,
                AwardName: "111",
                AwardDesc: "111",
                AwardIcon: "111",
            },
            BuyAmountOptions: s.bc.GetBuyAmountOptions(),
            UnitPrice:        comm.ChanceTBean,
            BuyLimitDesc:     s.bc.GetBuyLimitDesc(),
        },
        LevelList:    levelConfList,
        UserPlayFile: userPlayFile,
        TopNAwardInfo: &adventure_activity.TopNAwardCfg{
            TopNLimit: 100,
            AwardList: []*adventure_activity.AwardInfo{
                {
                    Amount:    1,
                    AwardName: "111",
                    AwardDesc: "111",
                    AwardIcon: "111",
                },
            },
        },
        NoPermissionText: s.bc.GetNoPermissionText(),
        //HasAccess:        false,
    }

    return out, nil
}

func (s *Server) GetPlatformWinningRecord(ctx context.Context, request *adventure_activity.GetPlatformWinningRecordRequest) (*adventure_activity.GetPlatformWinningRecordResponse, error) {
    return s.actGameMgr.GetPlatformWinningRecord(ctx, request)
}

func (s *Server) GetUserAdventureRecord(ctx context.Context, request *adventure_activity.GetUserAdventureRecordRequest) (*adventure_activity.GetUserAdventureRecordResponse, error) {
    return s.actGameMgr.GetUserRecord(ctx, request)
}

func (s *Server) SetAdventureIslandLimitConf(ctx context.Context, req *adventure_activity.SetAdventureIslandLimitConfReq) (*adventure_activity.SetAdventureIslandLimitConfResp, error) {
    out := &adventure_activity.SetAdventureIslandLimitConfResp{}
    log.InfoWithCtx(ctx, "SetAdventureIslandLimitConf req:%v", req)

    return out, s.actConfMgr.SetLimitConf(ctx, req)
}

func (s *Server) GetAdventureIslandLimitConf(ctx context.Context, req *adventure_activity.GetAdventureIslandLimitConfReq) (*adventure_activity.GetAdventureIslandLimitConfResp, error) {
    return s.actConfMgr.GetLimitConf(ctx)
}

func (s *Server) SetLevelConf(ctx context.Context, request *adventure_activity.SetLevelConfRequest) (*adventure_activity.SetLevelConfResponse, error) {
    out := &adventure_activity.SetLevelConfResponse{}
    log.InfoWithCtx(ctx, "SetLevelConf begin request: %+v", request)

    if len(request.GetLevelConfList()) == 0 || len(request.GetLevelConfList()) > 6 {
        log.ErrorWithCtx(ctx, "SetLevelConf failed, request:%+v", request)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "LevelConfList is empty or too long")
    }

    prizeList := make([]*adventure_activity.PrizePool, 0, len(request.GetLevelConfList()))

    for _, levelConf := range request.GetLevelConfList() {
        if levelConf.GetLevelId() == 0 || levelConf.LevelAward == nil {
            log.ErrorWithCtx(ctx, "SetLevelConf failed, request:%+v", request)
            return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "LevelId or LevelAward is empty")
        }
        prizeList = append(prizeList, &adventure_activity.PrizePool{
            LevelId: levelConf.GetLevelId(),
            AwardList: []*adventure_activity.AwardInfo{
                levelConf.GetLevelAward(),
            },
        })
    }

    levelPrizeList, err := s.getCommPrizeInfo(ctx, prizeList, true)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetLevelConfWithPool error: %v", err)
        return out, err
    }

    err = s.actConfMgr.SetLevelCfg(ctx, request, levelPrizeList)
    if err != nil {
        log.ErrorWithCtx(ctx, "SetLevelCfg error: %v", err)
        return out, err
    }

    log.InfoWithCtx(ctx, "SetLevelConf success request: %+v", request)
    return out, nil
}

func (s *Server) SetLevelPrizePool(ctx context.Context, request *adventure_activity.SetLevelPrizePoolRequest) (*adventure_activity.SetLevelPrizePoolResponse, error) {
    out := &adventure_activity.SetLevelPrizePoolResponse{}
    log.InfoWithCtx(ctx, "SetLevelPrizePool begin request: %+v", request)

    levelPrizeList, err := s.getCommPrizeInfo(ctx, request.GetPrizePoolList(), false)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetLevelConfWithPool error: %v", err)
        return out, err
    }

    err = s.actConfMgr.SetLevelPond(ctx, levelPrizeList)
    if err != nil {
        log.ErrorWithCtx(ctx, "SetLevelPond error: %v", err)
        return out, err
    }

    log.InfoWithCtx(ctx, "SetLevelPrizePool success request: %+v", request)
    return out, nil
}

func (s *Server) GetLevelConfWithPool(ctx context.Context, request *adventure_activity.GetLevelConfWithPoolRequest) (*adventure_activity.GetLevelConfWithPoolResponse, error) {
    out := &adventure_activity.GetLevelConfWithPoolResponse{}

    confList, err := s.actConfMgr.GetLevelConfWithPool(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetLevelConfWithPool error: %v", err)
        return out, err
    }

    levelConfList := make([]*adventure_activity.LevelConfWithPool, 0, len(confList))

    for _, v := range confList {
        if len(v.Cfg.CompletedPrize) == 0 || len(v.PrizeList) == 0 {
            continue
        }
        awardList := make([]*adventure_activity.AwardInfo, 0, len(v.PrizeList))
        for _, vv := range v.PrizeList {
            awardList = append(awardList, transPrizeInfo2Pb(ctx, vv))
        }
        levelConf := &adventure_activity.LevelConfWithPool{
            LevelCfg: &adventure_activity.LevelCfg{
                LevelId:    v.Cfg.LevelId,
                LevelName:  v.Cfg.LevelName,
                MaxNFixed:  v.Cfg.ConstantN,
                LevelAward: transPrizeInfo2Pb(ctx, v.Cfg.CompletedPrize[0]),
            },
            AwardList: awardList,
        }

        levelConfList = append(levelConfList, levelConf)
    }

    out.LevelList = levelConfList
    return out, nil
}

func transPrizeInfo2Pb(ctx context.Context, v *comm.Prize) *adventure_activity.AwardInfo {

    return &adventure_activity.AwardInfo{
        AwardId:      v.PrizeId,
        AwardType:    v.PrizeType,
        DressSubType: v.DressSubType,
        Amount:       v.Amount,
        AwardWorth:   v.PrizePrice,
        AwardName:    v.PrizeName,
        AwardDesc:    "",
        AwardIcon:    v.PrizeIcon,
        //ExpTime:      0,
        Weight: v.Weight,
        CardId: v.CardIdOrder,
    }
}

func (s *Server) getCommPrizeInfo(ctx context.Context, awardList []*adventure_activity.PrizePool, isCompletePrize bool) ([]*comm.Prize, error) {
    bgIdList := make([]uint32, 0, len(awardList))
    bgIdMap := make(map[int]struct{})
    prizeList := make([]*comm.Prize, 0, len(awardList))

    resultType := uint32(adventure_activity.DrawResultType_DRAW_RESULT_CARD_LIGHTED)
    if isCompletePrize {
        resultType = uint32(adventure_activity.DrawResultType_DRAW_RESULT_LEVEL_COMPLETED)
    }

    for _, v := range awardList {
        for _, vv := range v.GetAwardList() {
            if vv.GetAwardType() == uint32(adventure_activity.AwardType_AWARD_TYPE_PACKAGE) {
                bgId, _ := strconv.Atoi(vv.GetAwardId())
                if _, ok := bgIdMap[bgId]; ok {
                    continue
                }
                bgIdList = append(bgIdList, uint32(bgId))
                bgIdMap[bgId] = struct{}{}
            }
        }
    }

    presentCfgMap, packageItemMap, fragmentCfgMap, err := s.acLayerMgr.GetPackageItemCfg(ctx, bgIdList)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetTBeanTotalCount GetPackageItemCfg err %v", err)
        return nil, err
    }

    for _, v := range awardList {
        for _, vv := range v.GetAwardList() {
            var price, priceType, giftId uint32
            var name, icon string

            if vv.GetAwardType() == uint32(adventure_activity.AwardType_AWARD_TYPE_PACKAGE) {
                bgId, _ := strconv.Atoi(vv.GetAwardId())
                itemList := packageItemMap[uint32(bgId)]
                if len(itemList) == 0 {
                    log.ErrorWithCtx(ctx, "getCommPrizeInfo level:%d, v:%v, bg conf not exist", v.LevelId, vv)
                    return nil, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
                }
                if len(itemList) > 1 {
                    // 第一期只支持每个包裹一种礼物，超过1种礼物的报错
                    for _, vvv := range itemList {
                        log.DebugWithCtx(ctx, "getCommPrizeInfo level:%d, v:%v, vvv:%v", v.LevelId, vv, vvv)
                    }
                    return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "包裹中礼物数量物品数量大于1")
                }

                if itemList[0].GetItemType() == uint32(backpackbasepb.PackageItemType_BACKPACK_PRESENT) {
                    sourceId := itemList[0].GetSourceId()
                    if presentCfgMap[sourceId] == nil {
                        log.ErrorWithCtx(ctx, "getCommPrizeInfo level:%d, v:%v, present conf not exist", v.LevelId, vv)
                        return nil, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
                    }
                    price = presentCfgMap[sourceId].GetPrice() * itemList[0].GetItemCount()
                    priceType = presentCfgMap[sourceId].GetPriceType()
                    giftId = sourceId
                    name = fmt.Sprintf("%s * %d", presentCfgMap[sourceId].GetName(), itemList[0].GetItemCount())
                    icon = presentCfgMap[sourceId].GetIconUrl()
                }

                if itemList[0].GetItemType() == uint32(backpackbasepb.PackageItemType_BACKPACK_LOTTERY_FRAGMENT) {
                    sourceId := itemList[0].GetSourceId()
                    if fragmentCfgMap[sourceId] == nil {
                        log.ErrorWithCtx(ctx, "getCommPrizeInfo level:%d, v:%v, fragmentCfg not exist", v.LevelId, vv)
                        return nil, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
                    }
                    price = fragmentCfgMap[sourceId].GetFragmentPrice() * itemList[0].GetItemCount()
                    priceType = comm.PriceTypeTBean
                    name = fmt.Sprintf("%s * %d", fragmentCfgMap[sourceId].GetFragmentName(), itemList[0].GetItemCount())
                    icon = fragmentCfgMap[sourceId].GetFragmentUrl()
                }
            } else {
                // 装扮礼物
                name = vv.GetAwardName()
                icon = vv.GetAwardIcon()
            }

            prizeList = append(prizeList, &comm.Prize{
                CardIdOrder:  vv.GetCardId(),
                LevelId:      v.GetLevelId(),
                ResultType:   resultType,
                PrizeId:      vv.GetAwardId(),
                PrizeType:    vv.GetAwardType(), // 装扮没有价值
                DressSubType: vv.GetDressSubType(),
                Amount:       vv.GetAmount(),
                Weight:       vv.GetWeight(),
                PrizeName:    name,
                PrizeIcon:    icon,
                PrizePrice:   price,
                PriceType:    priceType,
                GiftId:       giftId,
            })
        }
    }

    return prizeList, nil
}

// GetTBeanTotalCount
// ==========对账接口==========
// T豆对账
func (s *Server) GetTBeanTotalCount(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
    out := &reconcile_v2.CountResp{}
    return out, errUnimplemented
}

func (s *Server) GetTBeanOrderIds(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
    out := &reconcile_v2.OrderIdsResp{}
    return out, errUnimplemented
}

// GetAwardTotalCount 奖励数据对账
func (s *Server) GetAwardTotalCount(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
    out := &reconcile_v2.CountResp{}
    return out, errUnimplemented
}

func (s *Server) GetAwardOrderIds(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
    out := &reconcile_v2.OrderIdsResp{}
    return out, errUnimplemented
}

//func (s *Server) getCommPrizeInfo(ctx context.Context, awardList []*adventure_activity.PrizePool, isCompletePrize bool) ([]*comm.Prize, error) {
//    bgIdList := make([]uint32, 0, len(awardList))
//    prizeList := make([]*comm.Prize, 0, len(awardList))
//
//    resultType := uint32(adventure_activity.DrawResultType_DRAW_RESULT_CARD_LIGHTED)
//    if isCompletePrize {
//        resultType = uint32(adventure_activity.DrawResultType_DRAW_RESULT_LEVEL_COMPLETED)
//    }
//
//    for _, v := range awardList {
//        for _, vv := range v.GetAwardList() {
//            if vv.GetAwardType() == uint32(adventure_activity.AwardType_AWARD_TYPE_PACKAGE) {
//                bgId, _ := strconv.Atoi(vv.GetAwardId())
//                bgIdList = append(bgIdList, uint32(bgId))
//            }
//        }
//    }
//
//    presentCfgMap, packageItemMap, fragmentCfgMap, err := s.acLayerMgr.GetPackageItemCfg(ctx, bgIdList)
//    if err != nil {
//        log.ErrorWithCtx(ctx, "GetTBeanTotalCount GetPackageItemCfg err %v", err)
//        return nil, err
//    }
//
//    for _, v := range awardList {
//        for _, vv := range v.GetAwardList() {
//            if vv.GetAwardType() == uint32(adventure_activity.AwardType_AWARD_TYPE_PACKAGE) {
//                bgId, _ := strconv.Atoi(vv.GetAwardId())
//                itemList := packageItemMap[uint32(bgId)]
//                if len(itemList) == 0 {
//                    log.ErrorWithCtx(ctx, "getCommPrizeInfo level:%d, v:%v, bg conf not exist", v.LevelId, vv)
//                    return nil, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
//                }
//                if len(itemList) > 1 {
//                    // 第一期只支持每个包裹一种礼物，超过1种礼物的报错
//                    log.ErrorWithCtx(ctx, "getCommPrizeInfo level:%d, v:%v, 包裹种的物品数量大于1", v.LevelId, vv)
//                    return nil, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
//                }
//
//                var itemInfo *comm.PackItem
//                if itemList[0].GetItemType() == uint32(backpackbasepb.PackageItemType_BACKPACK_PRESENT) {
//                    sourceId := itemList[0].GetSourceId()
//                    if presentCfgMap[sourceId] == nil {
//                        log.ErrorWithCtx(ctx, "getCommPrizeInfo level:%d, v:%v, present conf not exist", v.LevelId, vv)
//                        return nil, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
//                    }
//                    itemInfo = &comm.PackItem{
//                        GiftId:    sourceId,
//                        GiftName:  presentCfgMap[sourceId].GetName(),
//                        GiftPrice: presentCfgMap[sourceId].GetPrice(),
//                        PriceType: presentCfgMap[sourceId].GetPriceType(),
//                        //GiftDesc:  "",
//                        GiftIcon: presentCfgMap[sourceId].GetIconUrl(),
//                        Amount:   itemList[0].GetItemCount(),
//                    }
//                }
//
//                if itemList[0].GetItemType() == uint32(backpackbasepb.PackageItemType_BACKPACK_LOTTERY_FRAGMENT) {
//                    sourceId := itemList[0].GetSourceId()
//                    if fragmentCfgMap[sourceId] == nil {
//                        log.ErrorWithCtx(ctx, "getCommPrizeInfo level:%d, v:%v, fragmentCfg not exist", v.LevelId, vv)
//                        return nil, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
//                    }
//                    itemInfo = &comm.PackItem{
//                        GiftId:    sourceId,
//                        GiftName:  fragmentCfgMap[sourceId].GetFragmentName(),
//                        GiftPrice: fragmentCfgMap[sourceId].GetFragmentPrice(),
//                        PriceType: uint32(wishlistlogic.PriceType_PRICE_TBEAN),
//                        GiftIcon:  fragmentCfgMap[sourceId].GetFragmentUrl(),
//                        Amount:    itemList[0].GetItemCount(),
//                    }
//                }
//
//                prizeList = append(prizeList, &comm.Prize{
//                    LevelId:    v.GetLevelId(),
//                    ResultType: resultType,
//                    PrizeId:    vv.GetAwardId(),
//                    PrizeType:  uint32(adventure_activity.AwardType_AWARD_TYPE_PACKAGE),
//                    Amount:     1, // 包裹默认1个数量
//                    Weight:     vv.GetWeight(),
//                    PackItem: []*comm.PackItem{
//                        itemInfo, // 第一期仅支持配置一种礼物
//                    },
//                })
//            }
//
//            if vv.GetAwardType() == uint32(adventure_activity.AwardType_AWARD_TYPE_DRESS) {
//                prizeList = append(prizeList, &comm.Prize{
//                    LevelId:      v.GetLevelId(),
//                    ResultType:   resultType,
//                    PrizeId:      vv.GetAwardId(),
//                    PrizeType:    0, // 装扮没有价值
//                    DressSubType: vv.GetDressSubType(),
//                    Amount:       vv.GetAmount(),
//                    Weight:       vv.GetWeight(),
//                    DressInfo: &comm.DressInfo{
//                        DressId:   vv.GetAwardId(),
//                        DressName: vv.GetAwardName(),
//                        DressIcon: vv.GetAwardIcon(),
//                    },
//                })
//            }
//        }
//    }
//
//    return prizeList, nil
//}
