##

mock: mock-cache mock-store mock-conf mock-event
	echo "mock gen done"

mock-cache:
	echo "mock gen cache"
	cd ./cache && quicksilver-cli test interface && del cache_api.go && ren _api.go cache_api.go && mockgen -destination=../mock/mock_cache.go -package=mock . ICache,ISceneCache,IPresentMemCache

mock-store:
	echo "mock gen store"
	cd ./store && quicksilver-cli test interface && del store_api.go && ren _api.go store_api.go && mockgen -destination=../store/mock_db.go -package=store . IStore,ISceneStore,IDetailStore

#mock-rpc:
#	echo "mock gen rpc"
#	cd ./rpc && quicksilver-cli test && mockgen -destination=../mock/mock_rpc.go -package=mock . IClient
#

mock-event:
	echo "mock gen producer"
	cd ./producer && quicksilver-cli test interface && del producer_api.go && ren _api.go producer_api.go && mockgen -destination=../mock/mock_producer.go -package=mock . IKafkaProduceMgr


mock-conf:
	echo "mock gen conf"
	cd ./config/ttconfig/userpresent_go && quicksilver-cli test interface && del config_api.go && ren _api.go config_api.go&& mockgen -destination=../../../mock/mock_conf.go -package=mock . UserPresentGoConf