package manager

import (
	"context"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	"golang.52tt.com/pkg/datacenter"
	"golang.52tt.com/pkg/deal_token"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/marketid_helper"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	ga_base "golang.52tt.com/protocol/app"
	"golang.52tt.com/protocol/app/userpresent"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkapresent"
	pb "golang.52tt.com/protocol/services/userpresent-go"
	"golang.52tt.com/services/userpresent-go/internal/rpc"
	"golang.52tt.com/services/userpresent-go/internal/store"
	"strconv"
	"strings"
	"time"
)

func (m *UserPresentGoMgr) SendPresent(ctx context.Context, req *pb.SendPresentReq) (resp *pb.SendPresentResp, err error) {
	resp = &pb.SendPresentResp{}
	// 兼容极旧版本
	itemCount := req.GetItemCount()
	if req.GetItemCount() == 0 {
		itemCount = 1
	}

	// 某些礼物不记录Detail
	recordDetail := true
	if m.config.CheckIgnoreDetailItems(req.GetItemId()) {
		recordDetail = false
	}

	itemConfig := m.presentCache.GetConfigById(req.GetItemId())
	if itemConfig == nil {
		return resp, protocol.NewExactServerError(nil, status.ErrUserPresentConfigNotExist, "礼物配置不存在")
	}

	// dealToken校验和填充
	dealToken := req.GetDealToken()
	if itemConfig.GetPriceType() == uint32(pb.PresentPriceType_PRESENT_PRICE_TBEAN) {
		giftPrice := itemConfig.GetPrice() * uint32(itemCount)
		newDealToken, _ := deal_token.AddDealToken(req.GetDealToken(), deal_token.NewDealTokenData(req.OrderId, req.GetOrderId(), "userpresent",
			int64(req.GetUid()), int64(giftPrice)))

		if m.config.GetIsCheckDealToken() && deal_token.HandleDealToken(newDealToken) != 0 {
			log.ErrorWithCtx(ctx, "SendPresent -- CheckDealToken failed , req: %v", req)
			return resp, protocol.NewExactServerError(nil, status.ErrUserScoreInvalidDealtoken)
		}

		dealToken = newDealToken
	}

	// 是否记录财富
	enableResp, err := rpc.NumericCli.GetUserRichSwitch(ctx, req.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "SendPresent -- GetUserRichSwitch failed, err: %v", err)
	}
	isRecordRich := !enableResp.GetEnable()

	// 送礼物

	// 从这里开始使用一个新的过期时间为5s的ctx，防止出现由于上游耗时过长，送礼部分执行完成后，ctx超时的情况

	ctx, cancel := NewContextWithInfoTimeout(ctx, time.Second*5)
	defer cancel()

	err = m.RecordSendPresent(ctx, req, itemCount, itemConfig)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendPresent -- RecordSendPresent failed, err: %v", err)
		return resp, err
	}

	// 记录明细 （写新库）
	if recordDetail {
		err = m.RecordPresentDetail(ctx, req, itemCount, itemConfig, isRecordRich, dealToken)
		if err != nil {
			log.ErrorWithCtx(ctx, "SendPresent -- RecordPresentDetail failed, err: %v", err)
			return resp, protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
		}

		// 清除缓存
		_ = m.cache.ClearUserPresentDetail(ctx, req.GetTargetUid())
		_ = m.cache.ClearUserPresentSendDetail(ctx, req.GetUid())

		m.produceKafkaMsg(ctx, req, dealToken)

		// oss上报

		appName := marketid_helper.GetAppName(req.GetMarketId())

		datacenter.StdReportKV(ctx, "************", map[string]interface{}{
			"totalDate":        time.Now().Format("2006-01-02 15:04:05"),                                     // 日期
			"sendUid":          strconv.Itoa(int(req.GetUid())),                                              // 送礼人
			"targetUid":        strconv.Itoa(int(req.GetTargetUid())),                                        // 收礼人
			"itemId":           strconv.Itoa(int(req.GetItemId())),                                           // 礼物id
			"itemCount":        strconv.Itoa(int(itemCount)),                                                 // 数量
			"price":            strconv.Itoa(int(itemConfig.GetPrice())),                                     // 价格
			"priceType":        strconv.Itoa(int(itemConfig.GetPriceType())),                                 // 红钻/t豆
			"channelId":        strconv.Itoa(int(req.GetChannelId())),                                        // 送礼房间
			"channelDisplayId": strconv.Itoa(int(req.GetChannelDisplayId())),                                 // 房间显示id
			"sendSource":       strconv.Itoa(int(req.GetSendSource())),                                       // 公会短id
			"platform":         strconv.Itoa(int(req.GetSendPlatform())),                                     // 平台类型
			"itemSource":       strconv.Itoa(int(req.GetItemSource())),                                       // 礼物来源
			"batchType":        strconv.Itoa(int(req.GetBatchType())),                                        //批量类型
			"orderId":          req.GetOrderId(),                                                             //订单id
			"eventTime":        time.Unix(int64(req.GetSendTime()), 0).Local().Format("2006-01-02 15:04:05"), //事件发生时间（外部）
			"appId":            appName,                                                                      //应用id
		})

		log.DebugWithCtx(ctx, "SendPresent success, req: %v", req)
	}

	return resp, nil
}

func (m *UserPresentGoMgr) RecordSendPresent(ctx context.Context, req *pb.SendPresentReq, itemCount uint32, itemConfig *pb.StPresentItemConfig) (err error) {

	// 防止用户A和B同时互相送礼导致死锁
	// index大的先处理送礼的记录
	// index相同时，uid大的先处理送礼的记录

	err = m.RunWithTransaction(ctx, m.store, false, func(tx mysql.Txx) error {
		if req.GetUid()%100 > req.GetTargetUid()%100 || (req.GetUid()%100 == req.GetTargetUid()%100 && req.GetUid() > req.GetTargetUid()) {
			err = m.store.SendPresent(ctx, tx, req, itemCount, itemConfig.GetPrice()*itemCount)
			if err != nil {
				log.ErrorWithCtx(ctx, "SendPresent -- SendPresent failed, err: %v", err)
				return protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
			}

			err = m.store.ReceivePresent(ctx, tx, req, itemCount, itemConfig.GetPrice()*itemCount)
			if err != nil {
				log.ErrorWithCtx(ctx, "SendPresent -- ReceivePresent failed, err: %v", err)
				return protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
			}
		} else {
			err = m.store.ReceivePresent(ctx, tx, req, itemCount, itemConfig.GetPrice()*itemCount)
			if err != nil {
				log.ErrorWithCtx(ctx, "SendPresent -- ReceivePresent failed, err: %v", err)
				return protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
			}

			err = m.store.SendPresent(ctx, tx, req, itemCount, itemConfig.GetPrice()*itemCount)
			if err != nil {
				log.ErrorWithCtx(ctx, "SendPresent -- SendPresent failed, err: %v", err)
				return protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
			}
		}

		// 如果是t豆送礼，要记月表
		if itemConfig.GetPriceType() == uint32(pb.PresentPriceType_PRESENT_PRICE_TBEAN) {
			err := m.store.RecordPresentMonthlyHistory(ctx, tx, req, itemConfig, itemCount)
			if err != nil {
				log.ErrorWithCtx(ctx, "SendPresent -- SendPresent failed, err: %v", err)
				return protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
			}
		}

		// 如果是大神带飞券，要记单独的月表
		if itemConfig.GetExtend().GetTag() == uint32(ga_base.PresentTagType_PRESENT_TAG_TICKET) {
			err := m.store.RecordTicketPresentMonthlyHistory(ctx, tx, req, itemConfig, itemCount)
			if err != nil {
				log.ErrorWithCtx(ctx, "SendPresent -- SendPresent failed, err: %v", err)
				return protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
			}
		}

		return nil
	})

	return err
}

func (m *UserPresentGoMgr) RecordPresentDetail(ctx context.Context, req *pb.SendPresentReq, itemCount uint32, itemConfig *pb.StPresentItemConfig, isRecordRich bool, dealToken string) (err error) {
	// 事务处理用户送礼
	err = m.RunWithTransaction(ctx, m.detailStore, false, func(tx mysql.Txx) error {
		err = m.detailStore.SendPresentDetailRecord(ctx, tx, req, *itemConfig, itemCount, isRecordRich)
		if err != nil {
			log.ErrorWithCtx(ctx, "SendPresent -- SendPresentDetailRecord failed, err: %v", err)
			return protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
		}

		err = m.detailStore.ReceivePresentDetailRecord(ctx, tx, req, *itemConfig, itemCount, isRecordRich)
		if err != nil {
			log.ErrorWithCtx(ctx, "SendPresent -- ReceivePresentDetailRecord failed, err: %v", err)
			return protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
		}

		err = m.detailStore.RecordPresentDealToken(ctx, tx, req.GetOrderId(), dealToken, req.GetSendTime())
		if err != nil {
			log.ErrorWithCtx(ctx, "SendPresent -- RecordPresentDealToken failed, err: %v", err)
			return protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
		}

		return nil
	})

	return err
}

func (m *UserPresentGoMgr) CheckDealToken(price uint32, sToken string, orderId string) bool {
	if sToken == "" {
		log.ErrorWithCtx(context.Background(), "CheckDealToken -- sToken is empty")
	}
	if sToken == "" && m.config.GetIsCheckDealToken() {
		return false
	}

	isTokenOk := true
	currentToken := sToken
	for currentToken == "" {
		dtContent, err := deal_token.Decode(sToken)
		if err != nil {
			log.ErrorWithCtx(context.Background(), "CheckDealToken -- deal_token.Decode err:%v", err)
			isTokenOk = false
			break
		}

		//链表上每个节点的价值都一样
		if price != uint32(dtContent.TotalPrice) {
			isTokenOk = false
			break
		}

		// 存在可能性：链表里是总订单，而传入的是子订单，所以用contain而不是=进行比较
		if !strings.Contains(orderId, dtContent.OrderID) {
			isTokenOk = false
			break
		}

		currentToken = dtContent.PrevToken
	}

	if !isTokenOk && m.config.GetIsCheckDealToken() {
		return false
	}

	return true
}

func (m *UserPresentGoMgr) produceKafkaMsg(ctx context.Context, req *pb.SendPresentReq, dealToken string) {
	event := &kafkapresent.PresentEvent{
		Uid:             req.GetUid(),
		TargetUid:       req.GetTargetUid(),
		OrderId:         req.GetOrderId(),
		ChannelId:       req.GetChannelId(),
		ChannelType:     req.GetChannelType(),
		GuildId:         req.GetGuildId(),
		SendTime:        req.GetSendTime(),
		ItemId:          req.GetItemId(),
		ItemCount:       req.GetItemCount(),
		Price:           req.GetItemConfig().GetPrice(),
		PriceType:       req.GetItemConfig().GetPriceType(),
		ItemSource:      req.GetItemSource(),
		RankingRatio:    100,
		AddRich:         req.GetAddRich(),
		AddCharm:        req.GetAddCharm(),
		ReceiverGuildId: req.GetReceiverGuildId(),
		GiverGuildId:    req.GetGiverGuildId(),
		TagType:         req.ItemConfig.GetExtend().GetTag(),
		SendMethod:      req.GetSendMethod(),
		MsgType:         1,
		BindChannelId:   req.GetBindChannelId(),
		DealToken:       dealToken,
		FromUkwAccount:  req.GetFromUkwAccount(),
		FromUkwNickname: req.GetFromUkwNickname(),
		ToUkwAccount:    req.GetToUkwAccount(),
		ToUkwNickname:   req.GetToUkwNickname(),
		BatchType:       req.GetBatchType(),
		ChannelGameId:   req.GetChannelGameId(),
		IsVirtualLive:   req.GetIsVirtualLive(),
		ScoreType:       req.GetScoreType(),
		SendChannelId:   req.GetSendChannelId(),
	}

	if req.GetItemSource() == uint32(userpresent.PresentSourceType_PRESENT_SOURCE_PACKAGE) && m.config.GetRankingRation(req.GetItemId()) != 0 {
		event.RankingRatio = m.config.GetRankingRation(req.GetItemId())
	}

	if req.GetItemConfig().GetScore() > 0 {
		event.Score = req.GetItemConfig().GetScore() * req.GetItemCount()
	}

	tmpCtx, cancel := NewContextWithInfoTimeout(ctx, time.Second*5)
	defer cancel()

	_ = m.kafkaProducer.PublishPresentEvent(tmpCtx, event)
	_ = m.kafkaProducer.PublishPresentEventV2(tmpCtx, event)
}

func (m *UserPresentGoMgr) produceKafkaMsgByItem(ctx context.Context, req *store.SendPresentReqWithPrice, dealToken string) {
	event := &kafkapresent.PresentEvent{
		Uid:             req.GetUid(),
		TargetUid:       req.GetTargetUid(),
		OrderId:         req.GetOrderId(),
		ChannelId:       req.GetChannelId(),
		ChannelType:     req.GetChannelType(),
		GuildId:         req.GetGuildId(),
		SendTime:        req.GetSendTime(),
		ItemId:          req.GetItemId(),
		ItemCount:       req.GetItemCount(),
		Price:           req.GetItemConfig().GetPrice(),
		PriceType:       req.GetItemConfig().GetPriceType(),
		ItemSource:      req.GetItemSource(),
		RankingRatio:    100,
		AddRich:         req.GetAddRich(),
		AddCharm:        req.GetAddCharm(),
		ReceiverGuildId: req.GetReceiverGuildId(),
		GiverGuildId:    req.GetGiverGuildId(),
		TagType:         req.ItemConfig.GetExtend().GetTag(),
		SendMethod:      req.GetSendMethod(),
		MsgType:         1,
		BindChannelId:   req.GetBindChannelId(),
		DealToken:       dealToken,
		FromUkwAccount:  req.GetFromUkwAccount(),
		FromUkwNickname: req.GetFromUkwNickname(),
		ToUkwAccount:    req.GetToUkwAccount(),
		ToUkwNickname:   req.GetToUkwNickname(),
		BatchType:       req.GetBatchType(),
		ChannelGameId:   req.GetChannelGameId(),
		IsVirtualLive:   req.GetIsVirtualLive(),
		ScoreType:       req.GetScoreType(),
	}

	if req.GetItemSource() == uint32(userpresent.PresentSourceType_PRESENT_SOURCE_PACKAGE) && m.config.GetRankingRation(req.GetItemId()) != 0 {
		event.RankingRatio = m.config.GetRankingRation(req.GetItemId())
	}

	if req.GetItemConfig().GetScore() > 0 {
		event.Score = req.GetItemConfig().GetScore() * req.GetItemCount()
	}

	_ = m.kafkaProducer.PublishPresentEvent(ctx, event)
	_ = m.kafkaProducer.PublishPresentEventV2(ctx, event)
}

func NewContextWithInfoTimeout(ctx context.Context, timeOut time.Duration) (newCtx context.Context, cancel context.CancelFunc) {
	newCtx, cancel = context.WithTimeout(context.Background(), timeOut)
	sv, _ := protogrpc.ServiceInfoFromContext(ctx)
	newCtx = protogrpc.WithServiceInfo(newCtx, sv)
	return newCtx, cancel
}

func (m *UserPresentGoMgr) BatchSendPresent(ctx context.Context, req *pb.BatchSendPresentReq) (resp *pb.BatchSendPresentResp, err error) {
	resp = &pb.BatchSendPresentResp{}
	recordDetailMap := make(map[uint32]bool)
	dealTokenMap := make(map[string]string)

	if len(req.GetPresentList()) == 0 {
		return resp, protocol.NewExactServerError(nil, status.ErrRevenueSvrErr, "订单列表为空")
	}

	// 兼容极旧版本
	tmpReqList := make([]*store.SendPresentReqWithPrice, 0)
	uid := req.GetPresentList()[0].GetUid()
	toUid := req.GetPresentList()[0].GetTargetUid()

	// 是否记录财富
	isRecordRich := true
	enableResp, err := rpc.NumericCli.GetUserRichSwitch(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendPresent -- GetUserRichSwitch failed, err: %v", err)
	}

	isRecordRich = !enableResp.GetEnable()

	for _, item := range req.GetPresentList() {
		itemCount := item.GetItemCount()
		if item.GetItemCount() == 0 {
			itemCount = 1
		}

		// 某些礼物不记录Detail
		if _, ok := recordDetailMap[item.GetItemId()]; !ok {
			recordDetailMap[item.GetItemId()] = true
			if m.config.CheckIgnoreDetailItems(item.GetItemId()) {
				recordDetailMap[item.GetItemId()] = false
			}
		}

		itemConfig := m.presentCache.GetConfigById(item.GetItemId())
		if itemConfig == nil {
			return resp, protocol.NewExactServerError(nil, status.ErrUserPresentConfigNotExist, "礼物配置不存在")
		}

		// dealToken校验和填充
		if itemConfig.GetPriceType() == uint32(pb.PresentPriceType_PRESENT_PRICE_TBEAN) {
			giftPrice := itemConfig.GetPrice() * uint32(itemCount)
			newDealToken, _ := deal_token.AddDealToken(item.GetDealToken(), deal_token.NewDealTokenData(item.OrderId, item.GetOrderId(), "userpresent",
				int64(item.GetUid()), int64(giftPrice)))

			if m.config.GetIsCheckDealToken() && deal_token.HandleDealToken(newDealToken) != 0 {
				log.ErrorWithCtx(ctx, "SendPresent -- CheckDealToken failed , item: %v", item)
				return resp, protocol.NewExactServerError(nil, status.ErrUserScoreInvalidDealtoken)
			}

			dealTokenMap[item.GetOrderId()] = newDealToken
		}

		tmpReq := &store.SendPresentReqWithPrice{
			SendPresentItem: item,
			Price:           itemConfig.GetPrice() * item.GetItemCount(),
			Score:           itemConfig.GetScore() * item.GetItemCount(),
			PriceType:       itemConfig.GetPriceType(),
		}

		tmpReqList = append(tmpReqList, tmpReq)
	}

	// 送礼物

	// 从这里开始使用一个新的过期时间为5s的ctx，防止出现由于上游耗时过长，送礼部分执行完成后，ctx超时的情况

	ctx, cancel := NewContextWithInfoTimeout(ctx, time.Second*5)
	defer cancel()

	err = m.BatchRecordSendPresent(ctx, tmpReqList, uid, toUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchSendPresent -- BatchRecordSendPresent failed, err: %v", err)
		return resp, err
	}

	// 记录明细 （写新库）

	err = m.BatchRecordPresentDetail(ctx, uid, toUid, tmpReqList, isRecordRich, dealTokenMap)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendPresent -- RecordPresentDetail failed, err: %v", err)
		return resp, protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
	}

	// 清除缓存
	_ = m.cache.ClearUserPresentDetail(ctx, toUid)
	_ = m.cache.ClearUserPresentSendDetail(ctx, uid)

	for _, tmpReq := range tmpReqList {
		m.produceKafkaMsgByItem(ctx, tmpReq, dealTokenMap[tmpReq.GetOrderId()])

		// oss上报

		appName := marketid_helper.GetAppName(tmpReq.GetMarketId())

		datacenter.StdReportKV(ctx, "************", map[string]interface{}{
			"totalDate":        time.Now().Format("2006-01-02 15:04:05"),                                        // 日期
			"sendUid":          strconv.Itoa(int(tmpReq.GetUid())),                                              // 送礼人
			"targetUid":        strconv.Itoa(int(tmpReq.GetTargetUid())),                                        // 收礼人
			"itemId":           strconv.Itoa(int(tmpReq.GetItemId())),                                           // 礼物id
			"itemCount":        strconv.Itoa(int(tmpReq.GetItemCount())),                                        // 数量
			"price":            strconv.Itoa(int(tmpReq.Price)),                                                 // 价格
			"priceType":        strconv.Itoa(int(tmpReq.PriceType)),                                             // 红钻/t豆
			"channelId":        strconv.Itoa(int(tmpReq.GetChannelId())),                                        // 送礼房间
			"channelDisplayId": strconv.Itoa(int(tmpReq.GetChannelDisplayId())),                                 // 房间显示id
			"sendSource":       strconv.Itoa(int(tmpReq.GetSendSource())),                                       // 公会短id
			"platform":         strconv.Itoa(int(tmpReq.GetSendPlatform())),                                     // 平台类型
			"itemSource":       strconv.Itoa(int(tmpReq.GetItemSource())),                                       // 礼物来源
			"batchType":        strconv.Itoa(int(tmpReq.GetBatchType())),                                        //批量类型
			"orderId":          tmpReq.GetOrderId(),                                                             //订单id
			"eventTime":        time.Unix(int64(tmpReq.GetSendTime()), 0).Local().Format("2006-01-02 15:04:05"), //事件发生时间（外部）
			"appId":            appName,                                                                         //应用id
		})
	}

	log.DebugWithCtx(ctx, "SendPresent success, item: %v", req)
	return resp, nil
}

func (m *UserPresentGoMgr) BatchRecordSendPresent(ctx context.Context, req []*store.SendPresentReqWithPrice, uid, toUid uint32) (err error) {

	// 防止用户A和B同时互相送礼导致死锁
	// index大的先处理送礼的记录
	// index相同时，uid大的先处理送礼的记录

	err = m.RunWithTransaction(ctx, m.store, false, func(tx mysql.Txx) error {
		if uid%100 > toUid%100 || (uid%100 == toUid%100 && uid > toUid) {
			err = m.store.BatchSendPresent(ctx, tx, req, uid)
			if err != nil {
				log.ErrorWithCtx(ctx, "SendPresent -- SendPresent failed, err: %v", err)
				return protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
			}

			err = m.store.BatchReceivePresent(ctx, tx, req, toUid)
			if err != nil {
				log.ErrorWithCtx(ctx, "SendPresent -- ReceivePresent failed, err: %v", err)
				return protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
			}
		} else {
			err = m.store.BatchReceivePresent(ctx, tx, req, toUid)
			if err != nil {
				log.ErrorWithCtx(ctx, "SendPresent -- ReceivePresent failed, err: %v", err)
				return protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
			}

			err = m.store.BatchSendPresent(ctx, tx, req, uid)
			if err != nil {
				log.ErrorWithCtx(ctx, "SendPresent -- SendPresent failed, err: %v", err)
				return protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
			}
		}
		for _, item := range req {
			if m.config.CheckBanIdForTest(item.GetItemId()) {
				return protocol.NewExactServerError(nil, status.ErrRevenueSvrErr, "测试中，该礼物禁止送出")
			}
		}

		tbeanReqList := make([]*store.SendPresentReqWithPrice, 0)
		for _, item := range req {
			itemConfig := m.presentCache.GetConfigById(item.GetItemId())
			if itemConfig == nil {
				return protocol.NewExactServerError(nil, status.ErrUserPresentConfigNotExist, "礼物配置不存在")
			}
			// 如果是大神带飞券，要记单独的月表
			if itemConfig.GetPriceType() == uint32(pb.PresentPriceType_PRESENT_PRICE_TBEAN) {
				tbeanReqList = append(tbeanReqList, item)
			}
		}

		// 如果是t豆送礼，要记月表
		if len(tbeanReqList) > 0 {
			err := m.store.BatchRecordPresentMonthlyHistory(ctx, tx, tbeanReqList)
			if err != nil {
				log.ErrorWithCtx(ctx, "SendPresent -- SendPresent failed, err: %v", err)
				return protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
			}
		}

		return nil
	})

	return err
}

func (m *UserPresentGoMgr) BatchRecordPresentDetail(ctx context.Context, uid, targetUid uint32, req []*store.SendPresentReqWithPrice, isRecordRich bool, dealTokenMap map[string]string) (err error) {
	// 事务处理用户送礼
	err = m.RunWithTransaction(ctx, m.detailStore, false, func(tx mysql.Txx) error {
		err = m.detailStore.BatchSendPresentDetailRecord(ctx, tx, req, uid, isRecordRich)
		if err != nil {
			log.ErrorWithCtx(ctx, "SendPresent -- SendPresentDetailRecord failed, err: %v", err)
			return protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
		}

		err = m.detailStore.BatchReceivePresentDetailRecord(ctx, tx, req, targetUid, isRecordRich)
		if err != nil {
			log.ErrorWithCtx(ctx, "SendPresent -- ReceivePresentDetailRecord failed, err: %v", err)
			return protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
		}

		dealTokens := make([]*store.DealInfo, 0)
		for _, item := range req {
			dealTokens = append(dealTokens, &store.DealInfo{
				OrderID:   item.GetOrderId(),
				DealToken: dealTokenMap[item.GetOrderId()],
				SendTime:  item.GetSendTime(),
			})
		}

		err = m.detailStore.RecordPresentDealTokens(ctx, tx, dealTokens)
		if err != nil {
			log.ErrorWithCtx(ctx, "SendPresent -- RecordPresentDealToken failed, err: %v", err)
			return protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
		}

		return nil
	})

	return err
}
