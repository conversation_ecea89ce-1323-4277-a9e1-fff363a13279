package rpc

import (
	"golang.52tt.com/clients/channel"
	numeric_go "golang.52tt.com/clients/numeric-go"
	"golang.52tt.com/clients/sendim"
	sentinel_destructive_verify "golang.52tt.com/pkg/sentinel/destructive-verify"
	sentinel_interceptor "golang.52tt.com/pkg/sentinel/interceptor"
	"google.golang.org/grpc"
)

var (
	ChannelCli channel.IClient
	SendImCli  sendim.IClient
	NumericCli numeric_go.IClient
)

func Setup() error {
	opts := []grpc.DialOption{
		grpc.WithChainUnaryInterceptor(sentinel_interceptor.UnaryClientInterceptor(),
			sentinel_destructive_verify.UnarySlowCallClientInterceptor("present-middleware"),
		),
	}

	ChannelCli = channel.NewClient(opts...)
	SendImCli = sendim.NewClient(opts...)
	//NumericCli = numeric_go.NewIClient(opts...)

	return nil
}
