package exchangeguild

import (
	"encoding/json"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/services/exchange-logic/controllers"
	"golang.52tt.com/services/exchange-logic/models"
	"golang.52tt.com/services/exchange/esign/beans"
)


type EsignMasterNotifyController struct {
	controllers.APIController
}

func (c *EsignMasterNotifyController) Post() {
	ctx := c.Context()
	res := NotifyRes{
		Code: "200",
		Msg:  "success",
	}
	resData, _ := json.Marshal(res)
	//signature := c.Ctx.Input.Header("X-Tsign-Open-SIGNATURE")
	var baseAction beans.NotifyBaseConfig
	req := c.Ctx.Input.RequestBody
	log.InfoWithCtx(ctx, "req=%s", string(req))
	err := json.Unmarshal(req, &baseAction)
	if err != nil {
		log.ErrorWithCtx(ctx, "json.Unmarshal %+v", err)
		c.ServeAPIJsonWithError(status.ErrExchangeParamErr)
	}
	switch baseAction.Action {
	case "SIGN_FLOW_UPDATE":
		var notify beans.NotifySignFlowUpdate
		err = json.Unmarshal(req, &notify)
		if err != nil {
			log.ErrorWithCtx(ctx, "json.Unmarshal %+v", err)
			c.ServeAPIJsonWithError(status.ErrExchangeParamErr)
		}
		log.InfoWithCtx(ctx, "SIGN_FLOW_UPDATE = %+v", notify)
		_, err := models.ExchangeGuildClient.OnSignMasterFlowUpdate(ctx, notify.FlowId, notify.ThirdOrderNo, uint32(notify.SignResult))
		if err != nil {
			log.ErrorWithCtx(ctx, "OnSignFlowUpdate failed: %+v", err)
			c.ServeAPIJsonWithError(status.ErrExchangeSignErr)
		}
	case "SIGN_FLOW_FINISH":
		var notify beans.NotifySignFlowFinish
		err = json.Unmarshal(req, &notify)
		if err != nil {
			log.ErrorWithCtx(ctx, "json.Unmarshal %+v", err)
			c.ServeAPIJsonWithError(status.ErrExchangeParamErr)
		}
		log.InfoWithCtx(ctx, "SIGN_FLOW_FINISH = %+v", notify)
		_, err := models.ExchangeGuildClient.OnSignMasterFlowFinish(ctx, notify.FlowId, notify.FlowStatus)
		if err != nil {
			log.ErrorWithCtx(ctx, "OnSignFlowFinish failed: %+v", err)
			c.ServeAPIJsonWithError(status.ErrExchangeSignErr)
		}
	case "SIGN_DOC_EXPIRE":
		var notify beans.NotifySignDocExpire
		err = json.Unmarshal(req, &notify)
		if err != nil {
			log.ErrorWithCtx(ctx, "json.Unmarshal %+v", err)
			c.ServeAPIJsonWithError(status.ErrExchangeParamErr)
		}
		log.InfoWithCtx(ctx, "SIGN_DOC_EXPIRE = %+v", notify)
		_, err := models.ExchangeGuildClient.OnSignMasterDocExpire(ctx, notify.FlowId, notify.FileId)
		if err != nil {
			log.ErrorWithCtx(ctx, "OnSignDocExpire failed: %+v", err)
			c.ServeAPIJsonWithError(status.ErrExchangeSignErr)
		}
	}

	c.Ctx.WriteString(string(resData))
}
