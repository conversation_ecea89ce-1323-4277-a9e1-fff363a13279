syntax = "proto3";

import "google/protobuf/any.proto";

package api;
option go_package = ".";

message Response {
    int32 code = 1;
    string msg = 2;
    uint32 server_time = 3;
    google.protobuf.Any data = 4;
}

enum CurrencyType {
    Unspecified = 0; // 未指定
    TBean = 1; // T豆
    Points = 2; // 积分
    RedDiamond = 3; // 红钻
    Commission = 4; // 佣金
}

message RedDiamondExchangeItem {
    uint32 item_id = 1; // 商品id
    string name = 2; // 名称
    string desc = 3; // 描述
    uint32 amount = 4; // 按基础比例获得的红钻数量
    uint32 bonus_amount = 5; // 额外赠送的红钻数量
    CurrencyType currency_type = 6; // 货币类型, see CurrencyType
    uint32 price = 7; // 价格
}

message GetRedDiamondExchangeItemListResp {
    CurrencyType currency_type = 1; // 货币类型, see CurrencyType
    repeated RedDiamondExchangeItem item_list = 2; // 商品价格列表
}

message ExchangeResult {
    //    CurrencyType currency_type = 1;
    uint32 amount = 2;
    uint32 bonus_amount = 3;
    uint32 price = 4;
}

message ExchangeReq {
    CurrencyType currency_type = 1;
    ExchangeResult expected_result = 2;
}

message ExchangeResp {
    Transaction record = 1;
    bool Done = 2;
}

message ProfileResp {
    int32 red_diamonds = 1;
    int32 tbeans = 2;
    int32 points = 3;
    ExchangeResult max_tbean_to_red_diamond = 4;
    ExchangeResult max_points_to_red_diamond = 5;
}

message Transaction {
    uint32 id = 1;
    uint32 uid = 2;
    CurrencyType target_type = 3;
    uint32 amount = 4;
    CurrencyType currency_type = 5;
    uint32 price = 6;
    uint32 create_at = 7;
    string desc = 8;
}

message TransactionV2 {
    uint32 id = 1;
    uint32 uid = 2;
    CurrencyType target_type = 3;
    int32 amount = 4;
    CurrencyType currency_type = 5;
    int32 price = 6;
    uint32 create_at = 7;
    string desc = 8;
    string order_id = 9;
}

message GetTransactionsResp {
    repeated TransactionV2 list = 1;
}

message EstimateResp {
    ExchangeResult expected_result = 1;
}

message PointsToTBeanReq {
    string order_id = 1;
    uint32 points = 2;
    string market_id = 3;
    string request_id = 4;
    string device_id = 5;
    uint32 client_version = 6;
    uint32 client_type = 7;
    string face_auth_token = 8;
    uint32 face_auth_scene = 9;
    uint32 source_type = 10;
    string face_auth_provider_code = 11;
    string face_auth_provider_result_code = 12;
    string face_auth_result_token = 13;
}

message PointsToTBeanResp {
    Transaction record = 1;
    bool Done = 2;
    string request_id = 3;
    uint32 face_auth_scene = 4;
    string face_auth_context_json = 5;
}

message VerifyCodeResp {
    uint32 cooldown = 1;
    int32 code = 2;
    string msg = 3;
    int32 optype = 4;
}

message PointsToCashRecord {
    string id = 1;
    string bank_name = 2;
    string card_no = 3;
    float income = 4;
    uint32 status = 5;
    uint32 time = 6;
    string remark = 7;
}

message PointsToCashRecordResult {
    repeated PointsToCashRecord list = 1;
    uint32 total = 2;
    uint32 server_time = 3;
}

message PointsToCashInfo {
    uint32 income = 1;
    uint32 points = 2;
    uint32 withdraw_status = 3;
}

message PointsToCashResp {
    PointsToCashInfo points_info = 1;
    PointsToCashRecordResult drawing_record = 2;
}

message CheckEncashmentResult {
    uint32 withdraw_status = 1; //当前周期内能不能提现
    uint32 date_status = 2; //是否在提现周期内
    uint32 withdraw_type = 3; // 0:月结 1：周结
}

message PointsBalance {
    uint32 balance = 1;
    bool hidden = 2;
    bool has_withdraw = 3;
}

message TransactionV3 {
    string order_id = 1;
    int32 amount = 2;
    uint32 create_at = 3;
    string desc = 4;
}

message GetTransactionsV3Resp {
    repeated TransactionV3 list = 1;
}

message PointsToCashLimitResp {
    uint32 limit_remain = 1;
}

enum SettlementBillType
{
    UnKnownBillType = 0;
    GiftScore = 1;          // 对公礼物积分
    AwardScore = 2;         // 对公奖励积分
    MaskPKScore = 3;        // 对公蒙面PK积分
    AmuseCommission = 4;    // 多人互动会长佣金 (娱乐房佣金)
    YuyinBaseCommission = 5;// 语音直播会长佣金 (语音基础佣金)
    MonthMiddle = 6;        // 语音直播会长额外奖励
    DeepCoop = 7;           // 多人互动会长额外奖励 (旧深度合作)
    YuyinSubsidy = 8;       // 语音直播新公会补贴&主播补贴
    KnightScore = 9;        // 对公骑士积分
    AmuseExtra = 10;        // 多人互动会长额外奖励 (新自动结算 2022.11)
}

message AnchorScoreWithdrawRecord {
    uint32 anchor_uid = 1; // 主播UID
    uint32 guild_owner = 2; // 对公会长UID
    string bill_id = 3; // 结算单ID
    uint64 guild_owner_wd_money = 4; // 会长提现金额（分）
    string guild_owner_wd_money_cny = 5; // 会长提现金额（元）
    uint64 anchor_wd_money = 6; // 主播提现金额（分）
    string anchor_wd_money_cny = 7; // 主播提现金额（元）
    uint64 settle_start = 8; // 结算起始时间
    uint64 settle_end = 9; // 结算结束时间
    uint64 withdraw_time = 10; // 发起提现时间
}

message GetAnchorScoreWithdrawRecordsResp {
    repeated AnchorScoreWithdrawRecord anchor_withdraw_list = 1;
    uint32 total = 2;
}

message UserBankInfo {
    int32 is_adult = 1;
    string tt_mobile = 2;
    uint32 uid = 3;
    string name = 4;
    string identification_card = 5;
    string mobile = 6;
    uint32 status = 7;
    uint32 realname_auth_status = 8;
    string card_no = 9;
    string bank_name = 10;
    string bank_province = 11;
    string bank_city = 12;
    string opening_bank = 13;
    uint32 account_type = 14;
    string company_name = 15;
    uint32 invalidBankInfo = 16;
    uint32 exchange_invisible = 17;
}

message DistributeContract {
    string name = 1;
    string url = 2;
}

message DistributeContractResp {
    repeated DistributeContract list = 1;
}




