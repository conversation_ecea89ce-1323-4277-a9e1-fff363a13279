package mgr

import (
    "context"
    "crypto/rand"
    "errors"
    "fmt"
    "golang.52tt.com/protocol/common/status"
    "golang.52tt.com/services/public-notice/internal/conf"
    "google.golang.org/grpc/codes"
    "math/big"
    "strings"
    "time"

    "gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
    pushNotification "golang.52tt.com/clients/push-notification/v2"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    ga "golang.52tt.com/protocol/app"
    pushPb "golang.52tt.com/protocol/app/push"
    public_notice "golang.52tt.com/protocol/services/public-notice"
    pushPB "golang.52tt.com/protocol/services/push-notification/v2"
)

// 通用全服前缀
var msgList []string

const (
    UnableNone    = iota
    UnableAll     // 全部不可见
    UnableOutSize // 房间外不可见
)

type AnnounceScope uint32

const (
    announceScopeInvalid AnnounceScope = iota
    announceScopeAllAcount
    announceScopeAllChannel
)

func (m *BreakingNewsMgr) CommBreakingEventV3(ctx context.Context, req *public_notice.PushBreakingNewsReq) error {
    if req.CommonBreakingNews == nil {
        log.ErrorWithCtx(ctx, "CommBreakingEventV3 invalid parameter")
        return errors.New("CommBreakingEventV3 invalid parameter")
    }
    breakingNewsMessage := &pushPb.CommonBreakingNewsV3{}

    // 全服公告 不可见处理
    unable, unableType := conf.GetUnableConf()
    if unable {
        switch unableType {
        // 全部不可见，直接return
        case UnableAll:
            log.InfoWithCtx(ctx, "CommBreakingEventV3 unable push breakingNews")
            return nil
        // 仅房间外不可见，屏蔽该位
        case UnableOutSize:
            req.GetCommonBreakingNews().GetBreakingNewsBaseOpt().AnnounceScope &= (^uint32(pushPb.CommBreakingNewsBaseOpt_OUTSIDE_CHANNEL))
            log.InfoWithCtx(ctx, "CommBreakingEventV3 AnnounceScope:%d",
                req.GetCommonBreakingNews().GetBreakingNewsBaseOpt().GetAnnounceScope())
        default:
            log.InfoWithCtx(ctx, "CommBreakingEventV3 got invalid type:%d", unableType)
        }
    }

    uidList := make([]uint32, 0, 2)
    // 构造用户信息
    if req.CommonBreakingNews.GetFromUid() != 0 {
        breakingNewsMessage.FromUid = req.CommonBreakingNews.GetFromUid()
        // 请求中带用户信息则无需再次请求 && 过滤ukw用户信息,重新请求
        if req.CommonBreakingNews.FromUserInfo != nil && !strings.HasPrefix(req.CommonBreakingNews.FromUserInfo.GetAccount(), "ukw") {
            breakingNewsMessage.FromAccount = req.CommonBreakingNews.FromUserInfo.GetAccount()
            breakingNewsMessage.FromNick = req.CommonBreakingNews.FromUserInfo.GetNick()
        } else {
            uidList = append(uidList, breakingNewsMessage.FromUid)
        }

        // 概率玩法传入uid为0
    } else if req.CommonBreakingNews.FromUserInfo != nil {
        breakingNewsMessage.FromAccount = req.CommonBreakingNews.FromUserInfo.GetAccount()
        breakingNewsMessage.FromNick = req.CommonBreakingNews.FromUserInfo.GetNick()
    } else {
        log.ErrorWithCtx(ctx, "CommBreakingEventV3 get invalid from uid info, req:%+v", req)
    }

    if req.CommonBreakingNews.GetTargetUid() != 0 {
        // 请求中带用户信息则无需再次请求 && 过滤ukw用户信息,重新请求
        breakingNewsMessage.TargetUid = req.CommonBreakingNews.GetTargetUid()
        if req.CommonBreakingNews.TargetUserInfo != nil && !strings.HasPrefix(req.CommonBreakingNews.TargetUserInfo.GetAccount(), "ukw") {
            breakingNewsMessage.TargetAccount = req.CommonBreakingNews.TargetUserInfo.GetAccount()
            breakingNewsMessage.TargetNick = req.CommonBreakingNews.TargetUserInfo.GetNick()
        } else {
            uidList = append(uidList, breakingNewsMessage.TargetUid)
        }

        // 概率玩法传入uid为0
    } else if req.CommonBreakingNews.TargetUserInfo != nil {
        breakingNewsMessage.TargetAccount = req.CommonBreakingNews.TargetUserInfo.GetAccount()
        breakingNewsMessage.TargetNick = req.CommonBreakingNews.TargetUserInfo.GetNick()
    } else {
        log.WarnWithCtx(ctx, "CommBreakingEventV3 get invalid target uid info, req:%+v")
    }

    // 构造房间信息
    if req.CommonBreakingNews.GetChannelId() != 0 {
        err := m.fillChannelInfo(ctx, req.CommonBreakingNews.GetChannelId(), breakingNewsMessage)
        if err != nil {
            log.ErrorWithCtx(ctx, "CommBreakingEventV3 failed to fillChannelInfo, err:%v", err)
            return err
        }
    }

    // 构建公会信息
    if req.CommonBreakingNews.GetGuildId() != 0 {
        err := m.fillGuildInfo(ctx, req, breakingNewsMessage)
        if err != nil {
            log.ErrorWithCtx(ctx, "CommBreakingEventV3 failed to fillGuildInfo, err:%v", err)
            return err
        }
    }

    // 批量获取用户信息
    if len(uidList) != 0 {
        userInfoMap, err := m.accountCli.BatGetUserByUid(ctx, uidList...)
        if err != nil {
            log.ErrorWithCtx(ctx, "CommBreakingEventV3 failed to BatGetUserByUid, uidList:%v, err:%v", uidList, err)
            return err
        }
        fromUserInfo := userInfoMap[breakingNewsMessage.FromUid]
        targetUserInfo := userInfoMap[breakingNewsMessage.TargetUid]
        if fromUserInfo != nil {
            breakingNewsMessage.FromAccount = fromUserInfo.GetUsername()
            breakingNewsMessage.FromNick = fromUserInfo.GetNickname()
        }
        if targetUserInfo != nil {
            breakingNewsMessage.TargetAccount = targetUserInfo.GetUsername()
            breakingNewsMessage.TargetNick = targetUserInfo.GetNickname()
        }
    }

    // 填充头像信息
    err := m.fillHeadImage(ctx, breakingNewsMessage)
    if err != nil {
        log.ErrorWithCtx(ctx, "CommBreakingEventV3 failed to fillHeadImage, err:%v", err)
        return err
    }

    // 公告前缀
    if req.CommonBreakingNews.GetNewsPrefix() != "" {
        breakingNewsMessage.NewsPrefix = req.CommonBreakingNews.GetNewsPrefix()
    } else {
        breakingNewsMessage.NewsPrefix = m.GetNewsPrefix(ctx, req.CommonBreakingNews.BreakingNewsBaseOpt.GetTriggerType())
    }

    needMonster := conf.CheckNeedMonster(req.GetCommonBreakingNews().GetPresentNewsBaseOpt().GetGiftName())
    log.DebugWithCtx(ctx, "CommBreakingEventV3 needMonster:%v, giftName:%s", needMonster, req.GetCommonBreakingNews().GetPresentNewsBaseOpt().GetGiftName())
    if req.CommonBreakingNews.GetNeedMonsterInfo() || needMonster {
        // 打龙配置
        monsterConf := m.GetHuntMonsterConf()
        if monsterConf != nil && monsterConf.IsActivity && !m.isBanHuntMonsterChannel(breakingNewsMessage.GetChannelId()) {
            breakingNewsMessage.MonsterInfoV2 = &pushPb.MonsterInfoV2{MonsterId: 0, CountDown: int64(120)}
            breakingNewsMessage.RushInfo = &ga.RushInfo{RushType: monsterConf.RushType, RushMaxRandTs: monsterConf.RushMaxRandTs, RushWaitTs: monsterConf.RushWaitTs}
        }
    }

    // 透传其他信息
    m.fillCommonInfo(req, breakingNewsMessage)

    // 保留真实uid，用于后续单推 和 上报
    fromTrueUid := breakingNewsMessage.GetFromUid()
    targetTrueUid := breakingNewsMessage.GetTargetUid()
    // 神秘人处理 (需注意channelId为0的情况)
    if m.NeedMysteryInfo(ctx, breakingNewsMessage.GetBreakingNewsBaseOpt().GetTriggerType(), breakingNewsMessage.GetChannelType(),
        breakingNewsMessage.GetChannelId(), breakingNewsMessage.GetFromUid()) {
        m.fillMysteryInfo(ctx, req, breakingNewsMessage)
    }

    if conf.IsMaskNicknameType(breakingNewsMessage.GetBreakingNewsBaseOpt().GetTriggerType()) ||
        conf.IsRichTextMaskNicknameType(req.GetRichTextNews().GetNewsId()) {
        err = m.maskMessage(ctx, breakingNewsMessage)
        if err != nil {
            log.ErrorWithCtx(ctx, "CommBreakingEventV3 failed to maskMessage, err:%v", err)
            return err
        }
    }

    // 全文本类型全服处理
    if /*req.GetCommonBreakingNews().GetBreakingNewsBaseOpt().GetTriggerType() ==
      uint32(pushPb.CommBreakingNewsBaseOpt_TRIGGER_TYPE(999)) && */// 6.47.0 富文本类型全服不由triggerType决定
    req.GetRichTextNews().GetNewsId() != 0 {
        err := m.HandleRichTextBreakingNews(ctx, req.GetRichTextNews(), breakingNewsMessage)
        if err != nil {
            log.ErrorWithCtx(ctx, "CommBreakingEventV3 failed to HandleRichTextBreakingNews, err:%v", err)
            return err
        }
    }
    log.InfoWithCtx(ctx, "CommBreakingEventV3, msg: %+v", breakingNewsMessage)
    log.DebugWithCtx(ctx, "Push breakingNews, TriggerType:%d, newsId:%d, rank:%f", breakingNewsMessage.GetBreakingNewsBaseOpt().GetTriggerType(), breakingNewsMessage.GetNewsId(), breakingNewsMessage.GetRank())
    if breakingNewsMessage.BreakingNewsBaseOpt.TriggerType == 999 && (breakingNewsMessage.NewsId == 0 || breakingNewsMessage.NewsContent == "") {
        log.ErrorWithCtx(ctx, "CommBreakingEventV3 invalid breakingNewsMessage, breakingNewsMessage:%+v, req:%+v", breakingNewsMessage, req)
        return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "全服公告内容/newsId为空")
    }

    seqId := time.Now().Unix()
    // 开关开启时仅推送 中奖用户
    if conf.IsTargetUserOnlyMessage(breakingNewsMessage.GetBreakingNewsBaseOpt().GetTriggerType()) ||
        conf.IsRichTextTargetUserOnlyMessage(breakingNewsMessage.NewsId) {
        err := m.PushToUsers(ctx, breakingNewsMessage, pushPb.PushMessage_COMMON_BREAKING_EVENT_V3, []uint32{fromTrueUid}, seqId)
        if err != nil {
            log.ErrorWithCtx(ctx, "CommBreakingEventV3 failed to PushToUsers, uid:%d, err:%v", breakingNewsMessage.GetFromUid(), err)
            return err
        }
    } else {
        // 推送范围优化， 房间内可见全服使用全房间推送
        scope := announceScopeAllAcount
        if (breakingNewsMessage.BreakingNewsBaseOpt.AnnounceScope & uint32(pushPb.CommBreakingNewsBaseOpt_OUTSIDE_CHANNEL)) == 0 {
            scope = announceScopeAllChannel
        }

        // 正常推送
        err = m.PushHelper(ctx, breakingNewsMessage, pushPb.PushMessage_COMMON_BREAKING_EVENT_V3, []uint32{}, seqId, scope)
        if err != nil {
            log.ErrorWithCtx(ctx, "CommBreakingEventV3 failed to PushHelper, err:%v", err)
            return err
        }
    }

    // 百灵上报
    go m.reporter.ReportBreakingNews(ctx, fromTrueUid, []uint32{fromTrueUid, targetTrueUid}, breakingNewsMessage.GetBreakingNewsBaseOpt().GetTriggerType())

    log.InfoWithCtx(ctx, "CommBreakingEventV3 Push finish. FromUid:%d, ChannelId:%d, TriggerType:%d", breakingNewsMessage.GetFromUid(), breakingNewsMessage.GetChannelId(), breakingNewsMessage.GetBreakingNewsBaseOpt().GetTriggerType())
    return nil
}

func (m *BreakingNewsMgr) maskMessage(ctx context.Context, breakingNewsMessage *pushPb.CommonBreakingNewsV3) error {
    // 推送真实nickname ==》中奖用户
    /*
    	log.DebugWithCtx(ctx, "maskMessage msg:%+v", breakingNewsMessage)
    	err := m.PushToUsers(ctx, breakingNewsMessage, pushPb.PushMessage_COMMON_BREAKING_EVENT_V3, []uint32{breakingNewsMessage.FromUid}, seqId)
    	if err != nil {
    		log.ErrorWithCtx(ctx, "CommBreakingEventV3 failed to PushToUsers, err:%v", err)
    		return err
    	}
    */

    // 全服 ==》 (pushv2尚未支持)跳过中奖用户
    //skipUidList := []uint32{}
    // 处理nickname
    maskName := m.maskName(breakingNewsMessage.FromNick)
    // 如果不是神秘人，替换nickname
    if !(breakingNewsMessage.FromUserProfile.GetPrivilege().GetType() == uint32(ga.EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW)) {
        breakingNewsMessage.FromNick = maskName
        if breakingNewsMessage.FromUserProfile != nil {
            breakingNewsMessage.FromUserProfile.Nickname = maskName
        }
    }
    log.DebugWithCtx(ctx, "maskMessage msg:%+v", breakingNewsMessage)
    //err := m.PushHelper(ctx, breakingNewsMessage, pushPb.PushMessage_COMMON_BREAKING_EVENT_V3, skipUidList, seqId, scope)
    //if err != nil {
    //	log.ErrorWithCtx(ctx, "CommBreakingEventV3 failed to PushHelper, err:%v", err)
    //	return err
    //}
    return nil
}

func (m *BreakingNewsMgr) SmashEggBreaking(ctx context.Context, req *public_notice.PushBreakingNewsReq) error {
    if req.SmashEggBreakingNews == nil {
        log.ErrorWithCtx(ctx, "SmashEggBreaking invalid parameter")
        return errors.New("SmashEggBreaking invalid parameter")
    }
    breakingNewsMessage := &pushPb.SmashEggBreakingNews{
        CurrentHits:  req.SmashEggBreakingNews.GetCurrentHits(),
        MorphHits:    req.SmashEggBreakingNews.GetMorphHits(),
        MorphFlag:    req.SmashEggBreakingNews.GetMorphFlag(),
        MorphEndTime: req.SmashEggBreakingNews.GetMorphEndTime(),
    }
    seqId := time.Now().Unix()
    err := m.PushHelper(ctx, breakingNewsMessage, pushPb.PushMessage_SMASH_EGGS_BREAKING_EVENT, []uint32{}, seqId, announceScopeAllAcount)
    if err != nil {
        log.ErrorWithCtx(ctx, "SmashEggBreaking failed to PushHelper, err:%v", err)
        return err
    }
    log.InfoWithCtx(ctx, "SmashEggBreaking PushHelper finish.")
    return nil
}

func (m *BreakingNewsMgr) PushHelper(ctx context.Context, breakingNewsMessage proto.MessageV1, cmd pushPb.PushMessage_CMD_TYPE, skipUidList []uint32, seqId int64, scope AnnounceScope) error {
    log.DebugWithCtx(ctx, "cmd:%d, breakingNewsMessage:%v", cmd, breakingNewsMessage)
    BreakingNewsContent, _ := proto.Marshal(breakingNewsMessage)
    notification := m.notificationBuilder(BreakingNewsContent, cmd, seqId)
    mapMultiAccount := make(map[uint64]string, 0)
    switch scope {
    case announceScopeAllAcount, announceScopeInvalid:
        mapMultiAccount[0] = "0@group"
    case announceScopeAllChannel:
        mapMultiAccount[1] = "1@channel"
    // 默认全服推送
    default:
        mapMultiAccount[0] = "0@group"
    }

    err := m.pushCli.PushMulticasts(ctx, mapMultiAccount, skipUidList, notification)
    if err != nil {
        log.ErrorWithCtx(ctx, "PushBreakingNews failed to PushMulticast. breakingNewsMessage:%v, err:%v", breakingNewsMessage, err)
        return err
    } else {
        log.InfoWithCtx(ctx, "PushBreakingNews PushMulticast finish. breakingNewsMessage:%v, mapMultiAccount:%+v", breakingNewsMessage, mapMultiAccount)
    }
    return nil
}

func (m *BreakingNewsMgr) PushToUsers(ctx context.Context, breakingNewsMessage proto.MessageV1, cmd pushPb.PushMessage_CMD_TYPE, uidList []uint32, seqId int64) error {
    breakingNewsContent, _ := proto.Marshal(breakingNewsMessage)
    notification := m.notificationBuilder(breakingNewsContent, cmd, seqId)
    err := m.pushCli.PushToUsers(ctx, uidList, notification)
    if err != nil {
        log.ErrorWithCtx(ctx, "PushToUsers failed, breakingNewsMessage:%v, err:%v", breakingNewsMessage, err)
        return err
    } else {
        log.InfoWithCtx(ctx, "PushToUsers finish. breakingNewsMessage:%v", breakingNewsMessage)
    }
    return nil
}

func (m *BreakingNewsMgr) notificationBuilder(breakingNewsContent []byte, cmd pushPb.PushMessage_CMD_TYPE, seqId int64) *pushPB.CompositiveNotification {

    pushMessage := pushPb.PushMessage{Cmd: uint32(cmd), Content: breakingNewsContent, SeqId: uint32(seqId)}

    pushMessageContent, _ := proto.Marshal(&pushMessage)
    notification := &pushPB.CompositiveNotification{
        Sequence: uint32(seqId),
        TerminalTypeList: []uint32{
            protocol.MobileAndroidTT,
            protocol.MobileIPhoneTT,
        },
        AppId:              uint32(protocol.TT),
        TerminalTypePolicy: pushNotification.DefaultPolicy,
        ProxyNotification: &pushPB.ProxyNotification{
            Type:       uint32(pushPB.ProxyNotification_PUSH),
            Payload:    pushMessageContent,
            Policy:     pushPB.ProxyNotification_DEFAULT,
            ExpireTime: 86400,
        },
    }
    return notification
}

func (m *BreakingNewsMgr) CommonPublicMsgPush(ctx context.Context, cmdType uint32, msg []byte, inputScope AnnounceScope) error {
    seqId := time.Now().Unix()
    notification := m.notificationBuilder(msg, pushPb.PushMessage_CMD_TYPE(cmdType), seqId)
    mapMultiAccount := make(map[uint64]string, 0)

    // 只需房间内可见
    if (inputScope & AnnounceScope(pushPb.CommBreakingNewsBaseOpt_OUTSIDE_CHANNEL)) == 0 {
        mapMultiAccount[1] = "1@channel"
    } else {
        mapMultiAccount[0] = "0@group"
    }
    err := m.pushCli.PushMulticasts(ctx, mapMultiAccount, []uint32{}, notification)
    if err != nil {
        log.ErrorWithCtx(ctx, "CommonPublicMsgPush failed to PushMulticast. cmdType:%d, msg:%v, err:%v", cmdType, msg, err)
        return err
    }
    log.InfoWithCtx(ctx, "CommonPublicMsgPush PushMulticast finish. cmdType:%d, msg:%v, mapMultiAccount:%+v", cmdType, msg, mapMultiAccount)
    return nil

}

func (m *BreakingNewsMgr) GetNewsPrefix(ctx context.Context, triggerType uint32) string {
    roll, _ := rand.Int(rand.Reader, big.NewInt(int64(len(msgList))))
    strNewsPrefix := msgList[roll.Int64()]
    return strNewsPrefix
}

func (m *BreakingNewsMgr) NeedMysteryInfo(ctx context.Context, triggerType, channelType, channelId, uid uint32) bool {
    // 属于神秘人全服类型
    if conf.IsMysteryMessage(triggerType) {
        // 传入channelId为0，则查询channelol服务
        if channelId == 0 {
            channelId, err := m.channelolCli.GetUserChannelId(ctx, uid, uid)
            // 请求出错或者用户不在房默认false
            if err != nil || channelId == 0 {
                log.ErrorWithCtx(ctx, "NeedMysteryInfo failed to GetUserChannelId, uid:%d, channelId:%v, err:%v", uid, channelId, err)
                return false
            } else {
                // 请求出错默认false
                channelInfo, err := m.channelCli.GetChannelSimpleInfo(ctx, channelId, channelId)
                if err != nil {
                    log.ErrorWithCtx(ctx, "NeedMysteryInfo failed to GetChannelSimpleInfo, channelId:%d, err:%v", channelId, err)
                    return false
                }
                return conf.IsMysteryChannelType(channelInfo.GetChannelType())
            }
        }
        return conf.IsMysteryChannelType(channelType)
    }
    // 非神秘人全服公告类型直接return
    return false
}

// 透传其他信息
func (m *BreakingNewsMgr) fillCommonInfo(req *public_notice.PushBreakingNewsReq, breakingNewsMessage *pushPb.CommonBreakingNewsV3) {
    breakingNewsMessage.NewsContent = req.CommonBreakingNews.GetNewsContent()
    breakingNewsMessage.JumpUrl = req.CommonBreakingNews.GetJumpUrl()
    breakingNewsMessage.DelaySecs = req.CommonBreakingNews.GetDelaySecs()
    breakingNewsMessage.DatingSceneName = req.CommonBreakingNews.GetDatingSceneName()
    breakingNewsMessage.RichLevel = req.CommonBreakingNews.GetRichLevel()
    breakingNewsMessage.IsOldDeal = req.CommonBreakingNews.GetIsOldDeal()
    breakingNewsMessage.OldNewsContent = req.CommonBreakingNews.GetOldNewsContent()
    breakingNewsMessage.TagId = req.CommonBreakingNews.GetTagId()
    breakingNewsMessage.NobilityLevel = req.CommonBreakingNews.GetNobilityLevel()
    breakingNewsMessage.NobilityLevelName = req.CommonBreakingNews.GetNobilityLevelName()
    breakingNewsMessage.NobilityExtentCnt = req.CommonBreakingNews.GetNobilityExtentCnt()
    breakingNewsMessage.OptData = req.CommonBreakingNews.GetOptData()
    breakingNewsMessage.HardUrl = req.GetCommonBreakingNews().GetHardUrl()

    commBreakingNewsBaseOpt := &pushPb.CommBreakingNewsBaseOpt{
        TriggerType:      req.CommonBreakingNews.GetBreakingNewsBaseOpt().GetTriggerType(),
        RollingCount:     req.CommonBreakingNews.GetBreakingNewsBaseOpt().GetRollingCount(),
        RollingTime:      req.CommonBreakingNews.GetBreakingNewsBaseOpt().GetRollingTime(),
        AnnounceScope:    req.CommonBreakingNews.GetBreakingNewsBaseOpt().GetAnnounceScope(),
        AnnouncePosition: req.CommonBreakingNews.GetBreakingNewsBaseOpt().GetAnnouncePosition(),
        JumpType:         req.CommonBreakingNews.GetBreakingNewsBaseOpt().GetJumpType(),
        JumpPosition:     req.CommonBreakingNews.GetBreakingNewsBaseOpt().GetJumpPosition(),
    }
    presentBreakingNewsBaseOpt := &pushPb.PresentBreakingNewsBaseOpt{
        GiftName:    req.CommonBreakingNews.GetPresentNewsBaseOpt().GetGiftName(),
        GiftId:      req.CommonBreakingNews.GetPresentNewsBaseOpt().GetGiftId(),
        GiftCount:   req.CommonBreakingNews.GetPresentNewsBaseOpt().GetGiftCount(),
        GiftIconUrl: req.CommonBreakingNews.GetPresentNewsBaseOpt().GetGiftIconUrl(),
        GiftWorth:   req.CommonBreakingNews.GetPresentNewsBaseOpt().GetGiftWorth(),
        MagicId:     req.CommonBreakingNews.GetPresentNewsBaseOpt().GetMagicId(),
        MagicName:   req.CommonBreakingNews.GetPresentNewsBaseOpt().GetMagicName(),
        MagicIcon:   req.CommonBreakingNews.GetPresentNewsBaseOpt().GetMagicIcon(),
    }
    breakingNewsMessage.BreakingNewsBaseOpt = commBreakingNewsBaseOpt
    breakingNewsMessage.PresentNewsBaseOpt = presentBreakingNewsBaseOpt
}

// 填充房间信息
func (m *BreakingNewsMgr) fillChannelInfo(ctx context.Context, channelId uint32, breakingNewsMessage *pushPb.CommonBreakingNewsV3) error {
    breakingNewsMessage.ChannelId = channelId
    channelInfo, err := m.channelCli.GetChannelSimpleInfo(ctx, channelId, channelId)
    if err != nil {
        log.ErrorWithCtx(ctx, "CommBreakingEventV3 failed to GetChannelSimpleInfo, channelId:%d, err:%v", channelId, err)
        return err
    }
    breakingNewsMessage.ChannelDisplayid = channelInfo.GetDisplayId()
    breakingNewsMessage.ChannelBindid = channelInfo.GetBindId()
    breakingNewsMessage.ChannelType = channelInfo.GetChannelType()
    breakingNewsMessage.ChannelName = channelInfo.GetName()
    breakingNewsMessage.ChannelIconMd5 = channelInfo.GetIconMd5()
    // 添加channelViewId
    breakingNewsMessage.ChannelViewId = channelInfo.GetChannelViewId()
    return nil
}

func (m *BreakingNewsMgr) fillMysteryInfo(ctx context.Context, req *public_notice.PushBreakingNewsReq, breakingNewsMessage *pushPb.CommonBreakingNewsV3) {
    mysteryUidList := []uint32{}
    if breakingNewsMessage.GetFromUid() != 0 {
        mysteryUidList = append(mysteryUidList, breakingNewsMessage.GetFromUid())
    }
    if breakingNewsMessage.GetTargetUid() != 0 {
        mysteryUidList = append(mysteryUidList, breakingNewsMessage.GetTargetUid())
    }
    UKWInfoMap, sErr := m.userProfileCli.BatchGetUserProfileV2(ctx, mysteryUidList, true)
    if sErr != nil {
        log.ErrorWithCtx(ctx, "CommBreakingEventV3 failed to BatchGetUKWPersonInfoOnly.mysteryUidList:%v, err:%v", mysteryUidList, sErr)
    } else {
        log.InfoWithCtx(ctx, "BatchGetUserProfile success, UKWInfoMap:%v", UKWInfoMap)
        if ukwInfo, ok := UKWInfoMap[breakingNewsMessage.GetFromUid()]; ok {
            breakingNewsMessage.FromUserProfile = ukwInfo
            // 特权为神秘人
            if ukwInfo.GetPrivilege().GetType() == uint32(ga.EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW) {
                // 神秘人设置一个无效uid，避免旧版客户端跳转
                breakingNewsMessage.FromUid = 0
                // 神秘人头像不能为空，ios会有异常
                breakingNewsMessage.FromFaceMd5 = "ver-**************"
                breakingNewsMessage.FromAccount = ukwInfo.GetPrivilege().GetAccount()
                breakingNewsMessage.FromNick = ukwInfo.GetPrivilege().GetNickname()
            }
        }
        if ukwInfo, ok := UKWInfoMap[breakingNewsMessage.GetTargetUid()]; ok {
            breakingNewsMessage.TargetUserProfile = ukwInfo
            // 特权为神秘人
            if ukwInfo.GetPrivilege().GetType() == uint32(ga.EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW) {
                // 神秘人设置一个无效uid，避免旧版客户端跳转
                breakingNewsMessage.TargetUid = 0
                // 神秘人头像不能为空，ios会有异常
                breakingNewsMessage.TargetFaceMd5 = "ver-**************"
                breakingNewsMessage.TargetAccount = ukwInfo.GetPrivilege().GetAccount()
                breakingNewsMessage.TargetNick = ukwInfo.GetPrivilege().GetNickname()
            }
        }
    }
}

func (m *BreakingNewsMgr) fillGuildInfo(ctx context.Context, req *public_notice.PushBreakingNewsReq, breakingNewsMessage *pushPb.CommonBreakingNewsV3) error {
    breakingNewsMessage.GuildId = req.CommonBreakingNews.GetGuildId()
    if req.CommonBreakingNews.GuildInfo != nil {
        breakingNewsMessage.GuildName = req.CommonBreakingNews.GuildInfo.GetGuildName()
        breakingNewsMessage.GuildDisplayId = req.CommonBreakingNews.GuildInfo.GetGuildDisplayId()
    } else {
        guildInfo, err := m.guildCli.GetGuild(ctx, req.CommonBreakingNews.GetGuildId())
        if err != nil {
            log.ErrorWithCtx(ctx, "CommBreakingEventV3 failed to GetGuild, guildId:%d, err:%v", req.CommonBreakingNews.GetGuildId(), err)
            return err
        }
        breakingNewsMessage.GuildName = guildInfo.GetName()
        breakingNewsMessage.GuildDisplayId = guildInfo.GetShortId()
    }
    return nil
}

func (m *BreakingNewsMgr) fillHeadImage(ctx context.Context, breakingNewsMessage *pushPb.CommonBreakingNewsV3) error {
    // 批量获取头像
    accountList := make([]string, 0, 3)
    if breakingNewsMessage.GetFromAccount() != "" {
        accountList = append(accountList, breakingNewsMessage.GetFromAccount())
    }
    if breakingNewsMessage.GetTargetAccount() != "" {
        accountList = append(accountList, breakingNewsMessage.GetTargetAccount())
    }
    var guildAccount string
    if breakingNewsMessage.GetGuildId() != 0 {
        guildAccount = fmt.Sprintf("%d@guild", breakingNewsMessage.GetGuildId())
        accountList = append(accountList, guildAccount)
    }
    if len(accountList) != 0 {
        headMap, err := m.headImageCli.BatchGetHeadImageMd5(ctx, breakingNewsMessage.GetFromUid(), accountList)
        if err != nil {
            log.ErrorWithCtx(ctx, "CommBreakingEventV3 failed to BatchGetHeadImageMd5, accountList:%v, err:%v", accountList, err)
            return err
        }
        breakingNewsMessage.FromFaceMd5 = headMap[breakingNewsMessage.GetFromAccount()]
        breakingNewsMessage.TargetFaceMd5 = headMap[breakingNewsMessage.GetTargetAccount()]
        breakingNewsMessage.GuildFaceMd5 = headMap[guildAccount]
    }
    return nil
}

// 使用rune处理中文字符
func (m *BreakingNewsMgr) maskName(name string) string {
    if name == "" {
        return "*"
    }
    return string([]rune(name)[0:1]) + "*"
}

func init() {
    msgList = []string{"妈耶！", "哇塞！", "啊，碉堡了~", "牛批！", "号外~号外~", "敲厉害的！", "爆炸新闻！", "啊啊啊啊！", "哇~人生赢家！"}
}
