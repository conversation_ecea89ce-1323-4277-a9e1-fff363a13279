package server

import (
    "context"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/protocol/common/status"
    public_notice "golang.52tt.com/protocol/services/public-notice"
    "golang.52tt.com/services/public-notice/internal/mgr"
    "golang.52tt.com/services/public-notice/internal/timer"
    context0 "golang.org/x/net/context"
    "google.golang.org/grpc/codes"
)

type publicNoticeServer struct {
    breakingNewsMgr *mgr.BreakingNewsMgr
}

func (s *publicNoticeServer) CommonPublicMsgPush(c context0.Context, req *public_notice.CommonPublicMsgPushReq) (*public_notice.CommonPublicMsgPushResp, error) {
    out := &public_notice.CommonPublicMsgPushResp{}
    defer func() {
        log.InfoWithCtx(c, "CommonPublicMsgPush req:%+v, out:%+v", req, out)
    }()

    err := s.breakingNewsMgr.CommonPublicMsgPush(c, req.GetCmdType(), req.GetOptData(), mgr.AnnounceScope(req.GetAnnounceScope()))
    if err != nil {
        log.ErrorWithCtx(c, "CommonPublicMsgPush failed, err:%v", err)
        return out, err
    }
    return out, nil
}

func (s *publicNoticeServer) AddStickBreakingNews(c context.Context, req *public_notice.AddStickBreakingNewsReq) (*public_notice.AddStickBreakingNewsResp, error) {
    out := &public_notice.AddStickBreakingNewsResp{}
    defer func() {
        log.InfoWithCtx(c, "AddStickBreakingNews req:%+v, out:%+v", req, out)
    }()

    err := s.breakingNewsMgr.AddBreakingNewsPriorityRecord(c, req)
    if err != nil {
        log.ErrorWithCtx(c, "AddStickBreakingNews failed, err:%v", err)
        return out, err
    }
    return out, nil
}

func (s *publicNoticeServer) DelStickBreakingNews(c context.Context, req *public_notice.DelStickBreakingNewsReq) (*public_notice.DelStickBreakingNewsResp, error) {
    out := &public_notice.DelStickBreakingNewsResp{}
    defer func() {
        log.InfoWithCtx(c, "DelStickBreakingNews req:%+v, out:%+v", req, out)
    }()

    err := s.breakingNewsMgr.DeleteBreakingNewsPriorityRecord(c, req)
    if err != nil {
        log.ErrorWithCtx(c, "DelStickBreakingNews failed, err:%v", err)
        return out, err

    }
    return out, nil
}

func (s *publicNoticeServer) UpdateStickBreakingNews(c context.Context, req *public_notice.UpdateStickBreakingNewsReq) (*public_notice.UpdateStickBreakingNewsResp, error) {
    out := &public_notice.UpdateStickBreakingNewsResp{}
    defer func() {
        log.InfoWithCtx(c, "UpdateStickBreakingNews req:%+v, out:%+v", req, out)
    }()
    err := s.breakingNewsMgr.UpdateBreakingNewsPriorityRecord(c, req)
    if err != nil {
        log.ErrorWithCtx(c, "UpdateStickBreakingNews failed, err:%v", err)
        return out, err
    }
    return out, nil
}

func (s *publicNoticeServer) GetAllStickBreakingNews(c context.Context, req *public_notice.GetAllStickBreakingNewsReq) (*public_notice.GetAllStickBreakingNewsResp, error) {
    out, err := s.breakingNewsMgr.GetAllStickBreakingNews(c)
    if err != nil {
        log.ErrorWithCtx(c, "GetAllStickBreakingNews failed, err:%v", err)
        return out, err
    }
    return out, nil
}

func (s *publicNoticeServer) CheckIfCouldOperateStickBreakingNews(c context.Context, req *public_notice.CheckIfCouldOperateStickBreakingNewsReq) (*public_notice.CheckIfCouldOperateStickBreakingNewsResp, error) {
    out := &public_notice.CheckIfCouldOperateStickBreakingNewsResp{}
    defer func() {
        log.DebugWithCtx(c, "CheckIfCouldAddStickBreakingNews req:%+v, out:%+v", req, out)
    }()
    if req.GetNewsId() != 0 {
        err := s.breakingNewsMgr.CheckIfCouldAddStickBreakingNews(c, req.GetNewsId(), req.GetBeginTime(), req.GetEndTime())
        if err != nil {
            log.ErrorWithCtx(c, "CheckIfCouldAddStickBreakingNews failed, err:%v", err)
            return out, err
        }
    } else if req.GetRecordId() != 0 {
        err := s.breakingNewsMgr.CheckIfCouldUpdateStickRecord(c, req.GetRecordId(), req.GetBeginTime(), req.GetEndTime())
        if err != nil {
            log.ErrorWithCtx(c, "CheckIfCouldAddStickBreakingNews failed, err:%v", err)
            return out, err
        }
    } else {
        log.ErrorWithCtx(c, "CheckIfCouldAddStickBreakingNews failed, newsId and recordId is 0")
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "全服公告Id和全服公告优先级记录Id不能同时为0")
    }

    return out, nil
}

func (s *publicNoticeServer) TestPushRichTextBreakingNews(ctx context.Context, req *public_notice.TestPushRichTextBreakingNewsReq) (*public_notice.TestPushRichTextBreakingNewsResp, error) {
    s.breakingNewsMgr.TestPushRichTextBreakingNews(ctx, req)
    return &public_notice.TestPushRichTextBreakingNewsResp{}, nil
}

func NewPublicNoticeServer(breakingNewsMgr *mgr.BreakingNewsMgr, timerMgr *timer.Timer) public_notice.PublicNoticeServer {
    c := &publicNoticeServer{
        breakingNewsMgr: breakingNewsMgr,
    }
    return c
}

func (s *publicNoticeServer) PushBreakingNews(ctx context.Context, req *public_notice.PushBreakingNewsReq) (resp *public_notice.PushBreakingNewsResp, err error) {
    resp = &public_notice.PushBreakingNewsResp{}
    log.InfoWithCtx(ctx, "PushBreakingNews req:%+v", req)
    err = s.breakingNewsMgr.PushBreakingNews(ctx, req)
    if err != nil {
        log.ErrorWithCtx(ctx, "PushBreakingNews failed, err:%v", err)
        return resp, err
    }
    return resp, nil
}

func (s *publicNoticeServer) AddBreakingNewsConfig(ctx context.Context, req *public_notice.AddBreakingNewsConfigReq) (*public_notice.AddBreakingNewsConfigResp, error) {
    log.DebugWithCtx(ctx, "BatchGetBreakingNewsConfig in:%+v", req)
    return s.breakingNewsMgr.AddBreakingNewsConfig(ctx, req)
}

func (s *publicNoticeServer) UpdateBreakingNewsConfig(ctx context.Context, req *public_notice.UpdateBreakingNewsConfigReq) (*public_notice.UpdateBreakingNewsConfigResp, error) {
    return s.breakingNewsMgr.UpdateBreakingNewsConfig(ctx, req)
}

func (s *publicNoticeServer) BatchDelBreakingNewsConfig(ctx context.Context, req *public_notice.BatchDelBreakingNewsConfigReq) (*public_notice.BatchDelBreakingNewsConfigResp, error) {
    return s.breakingNewsMgr.BatchDelBreakingNewsConfig(ctx, req)
}

func (s *publicNoticeServer) BatchGetBreakingNewsConfig(ctx context.Context, req *public_notice.BatchGetBreakingNewsConfigReq) (*public_notice.BatchGetBreakingNewsConfigResp, error) {
    log.DebugWithCtx(ctx, "BatchGetBreakingNewsConfig in:%+v", req)
    return s.breakingNewsMgr.BatchGetBreakingNewsConfig(ctx, req)
}
