package manager

import (
	"context"
	"fmt"
	anchorcontractgo "golang.52tt.com/protocol/services/anchorcontract-go"
	channel_live_stats "golang.52tt.com/protocol/services/channel-live-stats"
	"time"

	utils2 "golang.52tt.com/pkg/foundation/utils"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/anchor-level"
	channel_recommend_svr "golang.52tt.com/protocol/services/channel-recommend-svr"
	"golang.52tt.com/protocol/services/channellivemgr"
	entertainmentRecommendBack "golang.52tt.com/protocol/services/entertainmentRecommendBackSvr"
	"golang.52tt.com/services/anchor-level/store"
	"golang.52tt.com/services/anchor-level/utils"
)

const (
	NeedWeekActiveDays = 5

	NewTaskTotalTimeSec      = 86400*7*4 + 3600*8 //第五周8点消失
	TotalNewTaskTotalTimeSec = 86400*7*8 + 3600*8 //第九周8点消失
	MonthSec                 = 86400 * 30

	weekTaskLevel_1 = 1
	weekTaskLevel_2 = 2
	weekTaskLevel_3 = 3

	taskTypeTotalNew = 1 // 纯新达人
	taskTypeComeback = 2 // 回归达人
	taskTypeNew      = 3 // 新达人
)

const SendVoiceLiveMsgErr = "SendVoiceLiveMsg err=%v"
const GrantFlowCardErr = "channelRecommendSvr.GrantFlowCard fail uid=%d, err=%v"

func getTaskName(taskType int) string {
	if taskType == taskTypeNew {
		return "新达人"
	}
	if taskType == taskTypeTotalNew {
		return "纯新达人"
	}
	if taskType == taskTypeComeback {
		return "回归达人"
	}
	return ""
}

func (m *AnchorLevelMgr) sendNewAnchorTaskNotFinishMsg(ctx context.Context, uid uint32, lastWeekBeginTime time.Time, lastWeekEndTime time.Time, taskType int) {
	if taskType == taskTypeNew {
		log.InfoWithCtx(ctx, "sendNewAnchorTaskNotFinishMsg taskType is new, no need to send msg %d ", uid)
		return
	}

	err := m.SendVoiceLiveMsg(ctx, uid, fmt.Sprintf("【周任务未达成】很遗憾你在上周（%d月%d日-%d月%d日）未达成%s周任务，本周继续努力吧~",
		lastWeekBeginTime.Month(), lastWeekBeginTime.Day(), lastWeekEndTime.Month(), lastWeekEndTime.Day(), getTaskName(taskType)),
		"", true)
	if err != nil {
		log.Errorf(SendVoiceLiveMsgErr, err)
	}
}

// 发放新主播任务奖励
func (m *AnchorLevelMgr) SendNewAnchorTaskReward() error {
	ctx := context.Background()
	log.InfoWithCtx(ctx, "SendNewAnchorTaskReward start")
	err := m.reporter.SendInfo("SendNewAnchorTaskReward start")
	if err != nil {
		log.Errorf("SendNewAnchorTaskReward reporter.SendInfo err=%v", err)
	}

	nowTime := time.Now()
	if m.dyconfig.GetFakeNowTime() != 0 {
		nowTime = time.Unix(int64(m.dyconfig.GetFakeNowTime()), 0)
	}
	weekBeginTime := utils.GetThisWeekBeginTime(nowTime)
	beginTime := weekBeginTime.AddDate(0, 0, -7*8-4) //8周前，且前4天，从周四开始

	checkList, err := m.store.GetAnchorLevelNewTaskWithTimeRange(ctx, beginTime.Unix(), weekBeginTime.Unix())
	if err != nil {
		log.Errorf("SendNewAnchorTaskReward store.GetAnchorLevelNewTaskWithTimeRange err=%v", err)
		return err
	}
	log.Infof("SendNewAnchorTaskReward weekBeginTime=%v, beginTime=%v, checkList total=%d", weekBeginTime, beginTime, len(checkList))

	for _, elem := range checkList {
		log.Infof("SendNewAnchorTaskReward beginInfo=%+v", elem)
		uid := elem.Uid
		lastWeekBeginTime := weekBeginTime.AddDate(0, 0, -7)
		lastWeekEndTime := weekBeginTime.Add(-time.Second)
		taskType := elem.TaskType
		if elem.TaskType == 0 {
			info, serverError := m.channelLiveStats.GetAnchorBaseInfo(ctx, 0, uid)
			if serverError != nil {
				log.ErrorWithCtx(ctx, "SendNewAnchorTaskReward .GetAnchorBaseInfo fail err=%v uid=%d", serverError, uid)
				return serverError
			}

			taskType, err = m.GetTaskType(ctx, uid, nowTime, info)
			if err != nil {
				log.Errorf("SendNewAnchorTaskReward GetTaskType err=%v , uid %d", err, uid)
				return err
			}

			log.InfoWithCtx(ctx, "SendNewAnchorTaskReward uid=%d taskType=%d", uid, taskType)

			if m.dyconfig.GetFakeTaskType(uid) != 0 {
				log.InfoWithCtx(ctx, "GetTaskType GetFakeTaskType=%d", m.dyconfig.GetFakeTaskType(uid))
				taskType = int32(m.dyconfig.GetFakeTaskType(uid))
			}

			// 顺便写到数据库
			err = m.store.InsertAnchorLevelNewTask(ctx, uid, uint32(taskType))
			if err != nil {
				log.Errorf("SendNewAnchorTaskReward InsertAnchorLevelNewTask err=%v , uid %d", err, uid)
			}
		}

		// 查直播数据，判断是否完成直播任务
		activeDaysResp, serverError := m.channelLiveStats.GetAnchorDailyRecordWithDateList(ctx, 0, uid, uint32(lastWeekBeginTime.Unix()), uint32(lastWeekEndTime.Unix()-1)) //防止包含
		if serverError != nil {
			log.ErrorWithCtx(ctx, "SendNewAnchorTaskReward channelLiveStats.GetAnchorDailyRecordWithDateList fail uid=%d, err=%v", uid, serverError)
			return serverError
		}

		weekActiveDays := uint32(0)
		weekGiftValue := uint32(0)
		for _, elem := range activeDaysResp.GetList() {
			if elem.GetIsLiveActiveDay() {
				weekActiveDays++
			}
			weekGiftValue += elem.GetAnchorIncome()
		}

		log.Infof("SendNewAnchorTaskReward step1 GetAnchorDailyRecordWithDateList lastWeekBeginTime=%v,lastWeekEndTime=%v, uid=%d, weekActiveDays=%d, weekGiftValue=%d",
			lastWeekBeginTime, lastWeekEndTime, elem.Uid, weekActiveDays, weekGiftValue)

		// 如果不是纯新/回归，且第四周以后，就没有任务了，直接返回
		isSecond := elem.CreateTime < weekBeginTime.AddDate(0, 0, -7*4-4).Unix() // 4周前的时间
		if taskType != taskTypeTotalNew && taskType != taskTypeComeback && isSecond {
			log.Infof("SendNewAnchorTaskReward uid=%d taskType=%d not new/回归, weekActiveDays=%d,  weekGiftValue=%d",
				uid, taskType, weekActiveDays, weekGiftValue)
			continue
		}

		// 最低条件：每周活跃天数达标周收入>=100000或者周收入大于50000且创建时间在第四周之前
		if weekActiveDays >= NeedWeekActiveDays && ((weekGiftValue >= 50000 && !isSecond) || weekGiftValue >= 100000) {
			log.InfoWithCtx(ctx, "SendNewAnchorTaskReward uid=%d  start reward weekActiveDays=%d,  weekGiftValue=%d",
				uid, weekActiveDays, weekGiftValue)
			err = m.sendNewAnchorTaskRewardStep2(uid, weekBeginTime, weekGiftValue, weekActiveDays, lastWeekBeginTime, lastWeekEndTime, elem.CreateTime, isSecond, uint32(taskType))
			if err != nil {
				_ = m.reporter.SendWarning(fmt.Sprintf("sendNewAnchorTaskRewardStep2 err=%v", err))
				continue
			}
		} else {
			// 查考核等级
			checkResp, err := m.anchorCheckCli.GetLastCreateScore(ctx, uid)
			if err != nil {
				log.ErrorWithCtx(ctx, "SendNewAnchorTaskReward GetLastCreateScore fail %v, uid=%d", err, uid)
				continue
				//return err
			}
			anchorCheckLevel := checkResp.GetLevel()

			if m.dyconfig.GetFakeLevel(uid) != "" {
				anchorCheckLevel = m.dyconfig.GetFakeLevel(uid)
			}

			// 没有完成任务的也要统计
			if err := m.store.InsertAnchorLevelNewTaskRecord(&store.AnchorLevelNewTaskRecord{
				TimeKey: fmt.Sprintf("%d%02d%02d-%d%02d%02d",
					lastWeekBeginTime.Year(), lastWeekBeginTime.Month(), lastWeekBeginTime.Day(),
					lastWeekEndTime.Year(), lastWeekEndTime.Month(), lastWeekEndTime.Day()),
				Uid: uid, CheckLevel: anchorCheckLevel, WeekActiveDay: weekActiveDays, WeekIncome: weekGiftValue, WeekTaskLevel: 0, CreateTime: time.Now(),
			}); err != nil {
				log.Errorf("SendNewAnchorTaskReward InsertAnchorLevelNewTaskRecord fail %v, uid=%d", err, uid)
			}
			log.InfoWithCtx(ctx, "SendNewAnchorTaskReward uid=%d anchorCheckLevel=%s, weekActiveDays=%d weekGiftValue=%d not enough",
				uid, anchorCheckLevel, weekActiveDays, weekGiftValue)
			m.sendNewAnchorTaskNotFinishMsg(ctx, uid, lastWeekBeginTime, lastWeekEndTime, int(taskType))
		}
	}

	err = m.reporter.SendInfo("SendNewAnchorTaskReward end")
	if err != nil {
		log.Errorf("SendNewAnchorTaskReward reporter.SendInfo err=%v", err)
	}
	log.InfoWithCtx(ctx, "SendNewAnchorTaskReward end")
	return nil
}

// 发放新主播任务奖励
func (m *AnchorLevelMgr) SendNewAnchorTaskRewardTest(uid uint32) error {
	ctx := context.Background()
	//log.InfoWithCtx(ctx, "SendNewAnchorTaskReward start")

	weekBeginTime := utils.GetThisWeekBeginTime(time.Now())
	createTime := weekBeginTime.AddDate(0, 1, 0).Unix()

	// 改成本周的
	lastWeekBeginTime := weekBeginTime
	lastWeekEndTime := weekBeginTime.AddDate(0, 0, 6)
	log.Infof("SendNewAnchorTaskRewardTest lastWeekBeginTime=%v lastWeekEndTime=%v", lastWeekBeginTime, lastWeekEndTime)

	//	log.Infof("SendNewAnchorTaskReward beginInfo=%+v", elem)
	//	uid := elem.Uid

	activeDaysResp, serverError := m.channelLiveStats.GetAnchorDailyRecordWithDateList(ctx, 0, uid, uint32(lastWeekBeginTime.Unix()), uint32(lastWeekEndTime.Unix()-1)) //防止包含
	if serverError != nil {
		log.ErrorWithCtx(ctx, "SendNewAnchorTaskReward channelLiveStats.GetAnchorDailyRecordWithDateList fail uid=%d, err=%v", uid, serverError)
		return serverError
	}

	weekActiveDays := uint32(0)
	weekGiftValue := uint32(0)
	for _, elem := range activeDaysResp.GetList() {
		if elem.GetIsLiveActiveDay() {
			weekActiveDays++
		}
		weekGiftValue += elem.GetAnchorIncome()
	}

	log.Infof("SendNewAnchorTaskReward step1 GetAnchorDailyRecordWithDateList lastWeekBeginTime=%v,lastWeekEndTime=%v, uid=%d, weekActiveDays=%d, weekGiftValue=%d",
		lastWeekBeginTime, lastWeekEndTime, uid, weekActiveDays, weekGiftValue)

	if weekActiveDays >= NeedWeekActiveDays && weekGiftValue >= 50000 { // 满足最低条件
		log.InfoWithCtx(ctx, "SendNewAnchorTaskReward uid=%d  start reward weekActiveDays=%d,  weekGiftValue=%d",
			uid, weekActiveDays, weekGiftValue)
		err := m.sendNewAnchorTaskRewardStep2(uid, weekBeginTime, weekGiftValue, weekActiveDays, lastWeekBeginTime, lastWeekEndTime, createTime, false, uint32(taskTypeTotalNew))
		if err != nil {
			_ = m.reporter.SendWarning(fmt.Sprintf("sendNewAnchorTaskRewardStep2 err=%v", err))
			return nil
		}
	}

	return nil
}

/*
低级新人流量：列表进房S等级，快速进房等级无（生效日期：周一08:00-次周一07:59:59，生效时间段：00:00-23:59）
中级新人流量：列表进房S等级，快速进房等级D（生效日期：周一08:00-次周一07:59:59，生效时间段：00:00-23:59）
高级新人流量：列表进房S等级，快速进房等级C（生效日期：周一08:00-次周一07:59:59，生效时间段：00:00-23:59）
*/

func (m *AnchorLevelMgr) getAnchorExtraInfo(ctx context.Context, uid uint32) (
	anchorCheckLevel string, channelId, tagId, signGuildId uint32, err error,
) {
	// 查考核等级
	checkResp, err := m.anchorCheckCli.GetLastCreateScore(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorLevelNewTask GetLastCreateScore fail %v, uid=%d", err, uid)
		return
	}
	anchorCheckLevel = checkResp.GetLevel()
	channelLiveInfo, serverError := m.channelLiveMgr.GetChannelLiveInfo(ctx, uid, false)
	if serverError != nil {
		log.ErrorWithCtx(ctx, "channelLiveMgr.GetChannelLiveInfo fail uid=%d, err=%v", uid, serverError)
		return
	}
	channelId = channelLiveInfo.GetChannelLiveInfo().GetChannelId()
	tagId = channelLiveInfo.GetChannelLiveInfo().GetTagId()

	contractInfo, err := m.anchorcontractGoClient.GetUserContractCacheInfo(ctx, 0, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "anchorcontractGoClient.GetUserContractCacheInfo fail uid=%d, err=%v", uid, err)
		return
	}
	signGuildId = contractInfo.GetContract().GetGuildId()
	return
}

func (m *AnchorLevelMgr) sendNewAnchorTaskRewardStep2(uid uint32, weekBeginTime time.Time, weekGiftValue, weekActiveDays uint32,
	lastWeekBeginTime time.Time, lastWeekEndTime time.Time, elemCreateTime int64, isSecond bool, taskType uint32) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*30)
	defer cancel()
	anchorCheckLevel, channelId, tagId, guildId, err := m.getAnchorExtraInfo(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendNewAnchorTaskReward sendNewAnchorTaskRewardStep2 getAnchorExtraInfo fail uid=%d, err=%v", uid, err)
		return err
	}
	log.Infof("SendNewAnchorTaskReward sendNewAnchorTaskRewardStep2 getAnchorExtraInfo uid=%d, anchorCheckLevel=%s, channelId=%d, tagId=%d, guildId=%d",
		uid, anchorCheckLevel, channelId, tagId, guildId)
	if m.dyconfig.GetFakeLevel(uid) != "" {
		anchorCheckLevel = m.dyconfig.GetFakeLevel(uid)
	}

	channelLevel := uint32(entertainmentRecommendBack.ChannelLevel_Channel_Level_S) //列表进房S等级
	confType := uint32(entertainmentRecommendBack.ELivePrepareConfType_EPREPARE_CONF_CONSTANT)
	rewardBeginTime := weekBeginTime.Add(time.Hour * 8)                //周一08:00
	rewardEndTime := weekBeginTime.AddDate(0, 0, 7).Add(time.Hour * 8) //次周一07:59:59
	rewardBeginTimeStamp := uint32(rewardBeginTime.Unix())
	rewardEndTimeStamp := uint32(rewardEndTime.Unix())
	timeSection := entertainmentRecommendBack.TimeSection{BeginTime: &rewardBeginTimeStamp, EndTime: &rewardEndTimeStamp}
	dayTimeBegin := uint32(0)
	dayTimeEnd := uint32(86400 - 1)

	// 快速进房
	quickEnterInfo := entertainmentRecommendBack.LiveQuickEnterPrepareInfo{
		Type: &confType, QuickLevel: nil, Sections: nil,
		DaySectionList: &entertainmentRecommendBack.DayTimeSectionList{SectList: []*entertainmentRecommendBack.DayTimeSection{{BeginTs: &dayTimeBegin, EndTs: &dayTimeEnd}}},
	}

	//次月最后一天24点，也就是次次月第一天
	weekTaskLevel := uint32(0)

	if !isSecond {
		if weekGiftValue >= 200000 {
			weekTaskLevel = weekTaskLevel_3
			log.InfoWithCtx(ctx, "SendNewAnchorTaskReward sendNewAnchorTaskRewardStep2 uid=%d weekGiftValue >= 150000", uid)
			quickLevel := uint32(entertainmentRecommendBack.ChannelLevel_Channel_Invalid)
			if taskType == taskTypeTotalNew || taskType == taskTypeComeback {
				quickLevel = uint32(entertainmentRecommendBack.ChannelLevel_Channel_Level_D) //快速进房D等级
			}

			quickEnterInfo.QuickLevel = &quickLevel
			quickEnterInfo.Sections = &timeSection
			log.Infof("SendNewAnchorTaskReward uid=%d weekTaskLevel_3", uid)

		} else if weekGiftValue >= 100000 {
			weekTaskLevel = weekTaskLevel_2
			log.InfoWithCtx(ctx, "SendNewAnchorTaskReward sendNewAnchorTaskRewardStep2 uid=%d weekGiftValue >= 100000", uid)

			channelLevel = uint32(entertainmentRecommendBack.ChannelLevel_Channel_Level_S)
			quickLevel := uint32(entertainmentRecommendBack.ChannelLevel_Channel_Invalid)
			if taskType == taskTypeTotalNew || taskType == taskTypeComeback {
				quickLevel = uint32(entertainmentRecommendBack.ChannelLevel_Channel_Level_D) //快速进房D等级
				channelLevel = uint32(entertainmentRecommendBack.ChannelLevel_Channel_Invalid)
			}

			quickEnterInfo.QuickLevel = &quickLevel
			quickEnterInfo.Sections = &timeSection
		} else {
			weekTaskLevel = weekTaskLevel_1
			quickLevel := uint32(entertainmentRecommendBack.ChannelLevel_Channel_Invalid) //快速进房D等级

			channelLevel = uint32(entertainmentRecommendBack.ChannelLevel_Channel_Level_A)
			if taskType == taskTypeTotalNew || taskType == taskTypeComeback {
				channelLevel = uint32(entertainmentRecommendBack.ChannelLevel_Channel_Level_S)
			}

			quickEnterInfo.QuickLevel = &quickLevel
			quickEnterInfo.Sections = &timeSection
			log.InfoWithCtx(ctx, "SendNewAnchorTaskReward sendNewAnchorTaskRewardStep2 uid=%d else", uid)
		}
	} else {
		if weekGiftValue >= 200000 {
			weekTaskLevel = weekTaskLevel_3
			log.InfoWithCtx(ctx, "SendNewAnchorTaskReward sendNewAnchorTaskRewardStep2 uid=%d weekGiftValue >= 100000", uid)
			quickLevel := uint32(entertainmentRecommendBack.ChannelLevel_Channel_Level_C) //快速进房C等级
			channelLevel = uint32(entertainmentRecommendBack.ChannelLevel_Channel_Level_S)

			quickEnterInfo.QuickLevel = &quickLevel
			quickEnterInfo.Sections = &timeSection
		} else if weekGiftValue >= 100000 {
			weekTaskLevel = weekTaskLevel_2
			log.InfoWithCtx(ctx, "SendNewAnchorTaskReward sendNewAnchorTaskRewardStep2 uid=%d weekGiftValue >= 100000", uid)
			quickLevel := uint32(entertainmentRecommendBack.ChannelLevel_Channel_Level_D) //快速进房D等级

			quickEnterInfo.QuickLevel = &quickLevel
			quickEnterInfo.Sections = &timeSection
		}
	}

	m.handleNewTaskPushMsg(uid, weekTaskLevel, anchorCheckLevel, lastWeekBeginTime, lastWeekEndTime, int(taskType), isSecond)
	log.Infof("SendNewAnchorTaskReward sendNewAnchorTaskRewardStep2 uid=%d, anchorCheckLevel=%s, weekActiveDays=%d, weekGiftValue=%d, weekTaskLevel=%d, quickEnterInfo.QuickLevel=%v",
		uid, anchorCheckLevel, weekActiveDays, weekGiftValue, weekTaskLevel, quickEnterInfo.GetQuickLevel())

	liveId := uint32(0)
	_, err3 := m.entertainmentRecommendBack.AddLivePrepareChannel(ctx, 0, &entertainmentRecommendBack.AddLivePrepareChannelReq{
		ChannelList: []*entertainmentRecommendBack.LivePrepareChannelInfo{
			{Id: &liveId, Uid: &uid, ChannelId: &channelId, GuildId: &guildId, TagId: &tagId, Level: &channelLevel, TagSections: &timeSection,
				ConfType: &confType, QuickEnterInfo: &quickEnterInfo, TagSectionList: &entertainmentRecommendBack.DayTimeSectionList{
				SectList: []*entertainmentRecommendBack.DayTimeSection{{BeginTs: &dayTimeBegin, EndTs: &dayTimeEnd}}}}}})
	if err3 != nil {
		log.ErrorWithCtx(ctx, "SendNewAnchorTaskReward entertainmentRecommendBack.AddLivePrepareChannel fail uid=%d, err=%v", uid, err3)
		return err3
	}
	log.Infof("SendNewAnchorTaskReward AddLivePrepareChannel uid=%d Level=%d QuickEnterInfo=%s", uid, channelLevel, quickEnterInfo.String())

	if taskType == taskTypeTotalNew || taskType == taskTypeComeback {
		lastTaskBeginTime := weekBeginTime.AddDate(0, 0, -7*7-4) //本周是最后一次任务的最晚时间，七周前，且是周四前
		if elemCreateTime < lastTaskBeginTime.Unix() {
			if err := m.SendVoiceLiveMsg(ctx, uid, fmt.Sprintf("【周任务结束】亲爱的达人，%s周任务期已全部结束，"+
				"继续完成“达人星级任务”将获得达人专属等级标识、流量卡、精美房间背景和粉丝铭牌奖励哦~去完成>>>", getTaskName(int(taskType))), hligth, true); err != nil {
				log.Errorf(SendVoiceLiveMsgErr, err)
			}
		}
	}

	if err := m.store.InsertAnchorLevelNewTaskRecord(&store.AnchorLevelNewTaskRecord{
		TimeKey: fmt.Sprintf("%d%02d%02d-%d%02d%02d",
			lastWeekBeginTime.Year(), lastWeekBeginTime.Month(), lastWeekBeginTime.Day(),
			lastWeekEndTime.Year(), lastWeekEndTime.Month(), lastWeekEndTime.Day()),
		Uid: uid, CheckLevel: anchorCheckLevel, WeekActiveDay: weekActiveDays, WeekIncome: weekGiftValue, WeekTaskLevel: weekTaskLevel, CreateTime: time.Now(),
	}); err != nil {
		log.Errorf("SendNewAnchorTaskReward sendNewAnchorTaskRewardStep2 InsertAnchorLevelNewTaskRecord fail %v, uid=%d", err, uid)
	}
	return nil
}

func (m *AnchorLevelMgr) handleNewTaskPushMsg(uid uint32, weekTaskLevel uint32, anchorCheckLevel string, lastWeekBeginTime time.Time, lastWeekEndTime time.Time, taskType int, isSecond bool) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()
	if taskType == taskTypeNew {
		log.InfoWithCtx(ctx, "handleNewTaskPushMsg taskType is new, no need to send msg %d ", uid)
		return
	}

	pushMsg := ""

	if !isSecond {
		if weekTaskLevel == weekTaskLevel_3 {
			pushMsg = fmt.Sprintf("【周任务达成】恭喜你在上周（%d月%d日-%d月%d日）完成%s周任务三，本周获得高级基础流量包（有效期至本周日），继续完成任务吧~",
				lastWeekBeginTime.Month(), lastWeekBeginTime.Day(), lastWeekEndTime.Month(), lastWeekEndTime.Day(), getTaskName(taskType))
		}
		if weekTaskLevel == weekTaskLevel_2 {
			pushMsg = fmt.Sprintf("【周任务达成】恭喜你在上周（%d月%d日-%d月%d日）完成%s周任务二，本周获得中级基础流量包（有效期至本周日），继续完成更高任务可获得更多流量扶持哦~",
				lastWeekBeginTime.Month(), lastWeekBeginTime.Day(), lastWeekEndTime.Month(), lastWeekEndTime.Day(), getTaskName(taskType))
		}
		if weekTaskLevel == weekTaskLevel_1 {
			pushMsg = fmt.Sprintf("【周任务达成】恭喜你在上周（%d月%d日-%d月%d日）完成%s周任务一，本周获得低级基础流量包（有效期至本周日），继续完成更高任务可获得更多流量扶持哦~",
				lastWeekBeginTime.Month(), lastWeekBeginTime.Day(), lastWeekEndTime.Month(), lastWeekEndTime.Day(), getTaskName(taskType))
		}

	} else {
		if weekTaskLevel == weekTaskLevel_3 {
			pushMsg = fmt.Sprintf("【周任务达成】恭喜你在上周（%d月%d日-%d月%d日）完成%s周任务二，本周获得高级基础流量包（有效期至本周日），继续完成任务吧~",
				lastWeekBeginTime.Month(), lastWeekBeginTime.Day(), lastWeekEndTime.Month(), lastWeekEndTime.Day(), getTaskName(taskType))
		}
		if weekTaskLevel == weekTaskLevel_2 {
			pushMsg = fmt.Sprintf("【周任务达成】恭喜你在上周（%d月%d日-%d月%d日）完成%s周任务一，本周获得中级基础流量包（有效期至本周日），继续完成更高任务可获得更多流量扶持哦~",
				lastWeekBeginTime.Month(), lastWeekBeginTime.Day(), lastWeekEndTime.Month(), lastWeekEndTime.Day(), getTaskName(taskType))
		}
	}

	if err := m.SendVoiceLiveMsg(ctx, uid, pushMsg, "", true); err != nil {
		log.Errorf(SendVoiceLiveMsgErr, err)
	}

	log.Infof("SendNewAnchorTaskReward handleNewTaskPushMsg uid=%d,weekTaskLevel=%d,anchorCheckLevel=%s,msg=%s", uid, weekTaskLevel, anchorCheckLevel, pushMsg)
}

func (m *AnchorLevelMgr) GetAnchorLevelNewTask(ctx context.Context, uid uint32) (resp *pb.AnchorLevelNewTask, err error) {
	resp = &pb.AnchorLevelNewTask{
		NeedWeekActiveDays: NeedWeekActiveDays,
		//NeedWeekPlatformDays: NeedWeekPlatformDays,
	}
	defer func() {
		log.DebugWithCtx(ctx, "GetAnchorLevelNewTask uid=%d, resp=%+v, err=%v", uid, resp, err)

		if resp.TaskType == uint32(0) {
			resp.TaskType = uint32(taskTypeNew)
		}
	}()

	checkData, _ := m.store.GetAnchorLevelNewTask(ctx, uid)
	log.DebugWithCtx(ctx, "GetAnchorLevelNewTask checkData=%+v", checkData)
	if checkData.Uid == 0 || checkData.CreateTime == 0 {
		return resp, nil
	}

	taskBeginTime := utils.GetAnchorNewBeginTime(checkData.CreateTime)
	tNow := time.Now()

	if checkData.TaskType == 0 {
		info, serverError := m.channelLiveStats.GetAnchorBaseInfo(ctx, 0, uid)
		if serverError != nil {
			log.ErrorWithCtx(ctx, "GetAnchorLevelNewTask .GetAnchorBaseInfo fail err=%v uid=%d", serverError, uid)
			return resp, serverError
		}

		taskType, err := m.GetTaskType(ctx, uid, tNow, info)
		if err != nil {
			log.Errorf("GetAnchorLevelNewTask GetTaskType err=%v , uid %d", err, uid)
			return resp, serverError
		}

		log.InfoWithCtx(ctx, "SendNewAnchorTaskReward uid=%d taskType=%d", uid, taskType)

		if m.dyconfig.GetFakeTaskType(uid) != 0 {
			log.InfoWithCtx(ctx, "GetTaskType GetFakeTaskType=%d", m.dyconfig.GetFakeTaskType(uid))
			taskType = int32(m.dyconfig.GetFakeTaskType(uid))
		}

		// 顺便写到数据库
		err = m.store.InsertAnchorLevelNewTask(ctx, uid, uint32(taskType))
		if err != nil {
			log.Errorf("SendNewAnchorTaskReward InsertAnchorLevelNewTask err=%v , uid %d", err, uid)
		}

		checkData.TaskType = taskType
	}

	fakeTime := m.dyconfig.GetFakeNowTime()
	if fakeTime != 0 {
		tNow = time.Unix(fakeTime, 0)
		log.InfoWithCtx(ctx, "GetAnchorLevelNewTask GetFakeNowTime=%d,%v", fakeTime, tNow)
	}

	if tNow.Unix() > taskBeginTime.Unix()+TotalNewTaskTotalTimeSec {
		log.InfoWithCtx(ctx, "GetAnchorLevelNewTask ignore. uid=%d CreateTime=%d taskBeginTime=%d tNow=%d",
			uid, checkData.CreateTime, taskBeginTime.Unix(), tNow.Unix())
		return resp, nil
	}
	resp.RemainTime = uint32(taskBeginTime.Unix() + TotalNewTaskTotalTimeSec - tNow.Unix())

	weekBeginTime := utils.GetThisWeekBeginTime(tNow)
	weekEndTime := tNow
	if tNow.Weekday() == time.Monday && tNow.Hour() < 8 && tNow.Sub(taskBeginTime) > time.Hour*8 { //周一8点前并且不是当周才开始任务，还是显示上周的数据
		weekEndTime = weekBeginTime
		weekBeginTime = weekBeginTime.AddDate(0, 0, -7)
	}

	activeDaysResp, serverError := m.channelLiveStats.GetAnchorDailyRecordWithDateList(ctx, 0, uid, uint32(weekBeginTime.Unix()), uint32(weekEndTime.Unix()-1)) //防止包含
	if serverError != nil {
		log.ErrorWithCtx(ctx, "channelLiveStats.GetAnchorDailyRecordWithDateList uid=%d, err=%v", uid, serverError)
		return resp, serverError
	}

	weekActiveDays := uint32(0)
	for _, elem := range activeDaysResp.GetList() {
		if elem.GetIsLiveActiveDay() {
			weekActiveDays++
		}
		resp.WeekGiftValue += elem.GetAnchorIncome()
	}
	if weekActiveDays < NeedWeekActiveDays {
		resp.RemainWeekActiveDays = NeedWeekActiveDays - weekActiveDays
	}

	log.DebugWithCtx(ctx, "GetAnchorLevelNewTask uid=%d weekBeginTime=%v,weekEndTime=%v weekActiveDays=%d WeekGiftValue=%d",
		uid, weekBeginTime, weekEndTime, weekActiveDays, resp.WeekGiftValue)

	checkResp, err := m.anchorCheckCli.GetLastCreateScore(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorLevelNewTask GetLastCreateScore fail %v, uid=%d", err, uid)
		return resp, err
	}
	resp.AnchorCheckLevel = checkResp.GetLevel()

	if m.dyconfig.GetFakeLevel(uid) != "" {
		resp.AnchorCheckLevel = m.dyconfig.GetFakeLevel(uid)
		log.InfoWithCtx(ctx, "GetAnchorLevelNewTask GetFakeLevel=%s", resp.AnchorCheckLevel)
	}

	resp.TaskType = uint32(checkData.TaskType)

	resp.WeekNum = uint32(getWeekNum(tNow, taskBeginTime))

	// 尚未开始的
	if tNow.Unix() < taskBeginTime.Unix() {
		resp.WeekNum = 0
	}

	return resp, nil
}

func (m *AnchorLevelMgr) SetAnchorCheckPass(ctx context.Context, uid uint32, checkLevel string) error { //nolint:golint
	log.InfoWithCtx(ctx, "SetAnchorCheckPass begin uid=%d, checkLevel=%s", uid, checkLevel)
	tNow := time.Now()
	if m.dyconfig.GetFakeNowTime() != 0 {
		tNow = time.Unix(int64(m.dyconfig.GetFakeNowTime()), 0)
		log.InfoWithCtx(ctx, "SetAnchorCheckPass GetFakeNowTime=%d,%v", m.dyconfig.GetFakeNowTime(), tNow)
	}

	info, serverError := m.channelLiveStats.GetAnchorBaseInfo(ctx, 0, uid)
	if serverError != nil {
		log.ErrorWithCtx(ctx, "SetAnchorCheckPass .GetAnchorBaseInfo fail err=%v uid=%d", serverError, uid)
		return serverError
	}

	//当前时间距离主播在平台内首次开播日≤30天或最近一次开播时间距离当前≥180天，且考核等级D级及以上的主播 为新主播
	//那么当前时间距离主播在平台内首次开播日>30天并最近一次开播时间距离当前<180天 为老主播
	if uint32(tNow.Unix()) > info.GetInfo().GetFirstLiveTs()+MonthSec && uint32(tNow.Unix()) < info.GetInfo().GetLastLiveAt()+6*MonthSec { //老主播
		log.InfoWithCtx(ctx, "SetAnchorCheckPass uid=%d is old. GetAnchorBaseInfo=%+v", uid, info.GetInfo())
		return nil
	}

	taskType, err := m.GetTaskType(ctx, uid, tNow, info)
	if err != nil {
		log.Errorf("SendNewAnchorTaskReward GetTaskType err=%v , uid %d", err, uid)
		return err
	}

	log.InfoWithCtx(ctx, "SetAnchorCheckPass uid=%d taskType=%d , info=%+v", uid, taskType, info.GetInfo())

	if m.dyconfig.GetFakeTaskType(uid) != 0 {
		log.InfoWithCtx(ctx, "GetTaskType GetFakeTaskType=%d", m.dyconfig.GetFakeTaskType(uid))
		taskType = int32(m.dyconfig.GetFakeTaskType(uid))
	}

	//从考核通过开始获得列表进房S等级
	channelLiveInfo, serverError := m.channelLiveMgr.GetChannelLiveInfo(ctx, uid, false)
	if serverError != nil {
		log.ErrorWithCtx(ctx, "SetAnchorCheckPass .GetChannelLiveInfo fail uid=%d, err=%v", uid, serverError)
		return serverError
	}

	channelId := channelLiveInfo.GetChannelLiveInfo().GetChannelId()
	tagId := channelLiveInfo.GetChannelLiveInfo().GetTagId()

	contractInfo, err := m.anchorcontractGoClient.GetUserContractCacheInfo(ctx, 0, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetAnchorCheckPass .GetUserContractCacheInfo fail uid=%d, err=%v", uid, err)
		return err
	}
	guildId := contractInfo.GetContract().GetGuildId()
	channelLevel := uint32(entertainmentRecommendBack.ChannelLevel_Channel_Level_S) //列表进房S等级
	confType := uint32(entertainmentRecommendBack.ELivePrepareConfType_EPREPARE_CONF_CONSTANT)
	rewardBeginTime := time.Now()
	rewardEndTime := utils.GetThisWeekBeginTime(rewardBeginTime).AddDate(0, 0, 14) //次周日23:59:59，也就是次次周一0点
	rewardBeginTimeStamp := uint32(rewardBeginTime.Unix())
	rewardEndTimeStamp := uint32(rewardEndTime.Unix())
	timeSection := entertainmentRecommendBack.TimeSection{BeginTime: &rewardBeginTimeStamp, EndTime: &rewardEndTimeStamp}

	dayTimeBegin := uint32(0)
	dayTimeEnd := uint32(86400 - 1)
	quickEnterInfo := entertainmentRecommendBack.LiveQuickEnterPrepareInfo{Type: &confType, Sections: &timeSection, DaySectionList: &entertainmentRecommendBack.DayTimeSectionList{
		SectList: []*entertainmentRecommendBack.DayTimeSection{{BeginTs: &dayTimeBegin, EndTs: &dayTimeEnd}}}}

	//
	oldTask, _ := m.store.GetAnchorLevelNewTask(ctx, uid)

	log.InfoWithCtx(ctx, "SetAnchorCheckPass uid=%d, oldTask=%+v", uid, oldTask)

	// 考核高等级直接发奖
	if checkLevel == "S" || checkLevel == "A" { //考核A级以及以上的主播，自动获得麦位开通权和2张热门流量，列表进房S等级+快速进房C等级（有效期：首次考核通过时至第30天的23:59:59）

		//考核等级为S或A等级的新主播在考核通过日自动获得麦位开通权
		flagResp, err := m.channelLiveMgr.SetAuthFlag(ctx, &channellivemgr.SetAuthFlagReq{UidList: []uint32{uid}, AuthFlag: channellivemgr.ChannelLiveAuthFlag_MQ_Auth,
			Flag: true,
		})
		if err != nil {
			log.Errorf("SetAnchorCheckPass channelLiveMgr.SetAuthFlag fail err=%v uid=%d", err, uid)
			return err
		}
		if len(flagResp.FailUidList) > 0 {
			log.Warnf("SetAnchorCheckPass flagResp.FailUidList = %v", flagResp.FailUidList)
		}

		//列表进房S等级+快速进房C等级（有效期：首次考核通过时至次周日的23:59:59）
		quickLevel := uint32(entertainmentRecommendBack.ChannelLevel_Channel_Level_D) //快速进房D等级
		quickEnterInfo.QuickLevel = &quickLevel

		firstPassTimeStamp := oldTask.CreateTime
		if firstPassTimeStamp == 0 {
			firstPassTimeStamp = tNow.Unix()
		}

		firstPassTime := time.Unix(firstPassTimeStamp, 0)
		fastEnterTimeEnd := utils.GetThisWeekBeginTime(firstPassTime).AddDate(0, 0, 14) //次周日23:59:59，也就是次次周一0点
		fastEnterTimeEndUnit32 := uint32(fastEnterTimeEnd.Unix())
		firstPassTimeStampUint32 := uint32(firstPassTimeStamp)
		newTimeSection := entertainmentRecommendBack.TimeSection{BeginTime: &firstPassTimeStampUint32, EndTime: &fastEnterTimeEndUnit32}
		quickEnterInfo.Sections = &newTimeSection

		//如果是纯新主播/回归，热门流量卡*2（有效期至首次考核通过的次月最后一天23:59:59）
		firstPassTimeMonthOne := time.Date(firstPassTime.Year(), firstPassTime.Month(), 1, 0, 0, 0, 0, time.Local)
		flowCardExpireTime := firstPassTimeMonthOne.AddDate(0, 2, 0) //次月最后一天23:59:59，就是两个月后的1号
		if taskType == taskTypeTotalNew || taskType == taskTypeComeback {
			_, err = m.channelRecommendSvr.GrantFlowCard(ctx, &channel_recommend_svr.GrantFlowCardReq{ //热门流量卡
				GrantType: uint32(channel_recommend_svr.FlowCardGrantType_GrantAnchor),
				Info: &channel_recommend_svr.FlowCardGrantInfo{Id: uid, Level: uint32(channel_recommend_svr.RecommendLevel_Recommend_Level_A),
					ExpirtTs: uint32(flowCardExpireTime.Unix()), Cnt: 2, UpdateTs: uint32(time.Now().Unix()), OrderId: fmt.Sprintf("ANCHOR_NEW_SA_%d", uid), //通过orderId限制只给用户发一次
				},
			})
			if err != nil {
				log.ErrorWithCtx(ctx, GrantFlowCardErr, uid, err)
				return err
			}
		}

		// 新主播这里要做任务
		if taskType == taskTypeNew {
			err := m.store.InitAnchorLevelNewTaskAward(ctx, uid, checkLevel, uint32(taskType))
			if err != nil {
				log.ErrorWithCtx(ctx, "SetAnchorCheckPass InitAnchorLevelNewTaskAward fail %v, uid=%d checkLevel=%s", err, uid, checkLevel)
			}
		} else {
			liveId := uint32(0)
			awardReq := &entertainmentRecommendBack.AddLivePrepareChannelReq{
				ChannelList: []*entertainmentRecommendBack.LivePrepareChannelInfo{
					{Id: &liveId, Uid: &uid, ChannelId: &channelId, GuildId: &guildId, TagId: &tagId, Level: &channelLevel, TagSections: &timeSection,
						ConfType: &confType, QuickEnterInfo: &quickEnterInfo, TagSectionList: &entertainmentRecommendBack.DayTimeSectionList{
						SectList: []*entertainmentRecommendBack.DayTimeSection{{BeginTs: &dayTimeBegin, EndTs: &dayTimeEnd}}},
					},
				},
			}
			_, err2 := m.entertainmentRecommendBack.AddLivePrepareChannel(ctx, 0, awardReq)
			if err2 != nil {
				log.ErrorWithCtx(ctx, "SetAnchorCheckPass .AddLivePrepareChannel fail uid=%d, err=%v", uid, err2)
				return err2
			}
			log.InfoWithCtx(ctx, "SetAnchorCheckPass AddLivePrepareChannel uid=%d checkLevel=%s req=%s", uid, checkLevel, utils2.ToJson(awardReq))
		}

	} else {

		// 考核B,C，更新记录
		log.InfoWithCtx(ctx, "SetAnchorCheckPass InitAnchorLevelNewTaskAward uid=%d checkLevel=%s", uid, checkLevel)

		firstPassTimeStamp := oldTask.CreateTime
		if firstPassTimeStamp == 0 {
			firstPassTimeStamp = tNow.Unix()
		}
		firstPassTime := time.Unix(firstPassTimeStamp, 0)
		//如果是B级且为纯新/回归，普通流量卡*1（有效期至首次考核通过的次月最后一天23:59:59）
		firstPassTimeMonthOne := time.Date(firstPassTime.Year(), firstPassTime.Month(), 1, 0, 0, 0, 0, time.Local)
		flowCardExpireTime := firstPassTimeMonthOne.AddDate(0, 2, 0) //次月最后一天23:59:59，就是两个月后的1号
		if (taskType == taskTypeTotalNew || taskType == taskTypeComeback) && checkLevel == "B" {
			_, err = m.channelRecommendSvr.GrantFlowCard(ctx, &channel_recommend_svr.GrantFlowCardReq{ //热门流量卡
				GrantType: uint32(channel_recommend_svr.FlowCardGrantType_GrantAnchor),
				Info: &channel_recommend_svr.FlowCardGrantInfo{Id: uid, Level: uint32(channel_recommend_svr.RecommendLevel_Recommend_Level_B),
					ExpirtTs: uint32(flowCardExpireTime.Unix()), Cnt: 1, UpdateTs: uint32(time.Now().Unix()), OrderId: fmt.Sprintf("ANCHOR_NEW_B_%d", uid), //通过orderId限制只给用户发一次
				},
			})
			if err != nil {
				log.ErrorWithCtx(ctx, GrantFlowCardErr, uid, err)
				return err
			}
		}

		// 改了等级也会重复发奖，奖励周期还是按照首次的
		// on duplicate key update create_time=create_time,check_level=?,award_status=0
		err := m.store.InitAnchorLevelNewTaskAward(ctx, uid, checkLevel, uint32(taskType))
		if err != nil {
			log.ErrorWithCtx(ctx, "SetAnchorCheckPass InitAnchorLevelNewTaskAward fail %v, uid=%d checkLevel=%s", err, uid, checkLevel)
		}
	}

	err2 := m.store.InsertAnchorLevelNewTask(ctx, uid, uint32(taskType))
	if err2 != nil {
		log.ErrorWithCtx(ctx, "SetAnchorCheckPass InsertAnchorLevelNewTask fail %v, uid=%d", err2, uid)
		return err2
	}

	if oldTask.CreateTime != 0 {
		log.InfoWithCtx(ctx, "SetAnchorCheckPass uid=%d has new task, not send msg", uid)
		return nil
	}

	m.handleSetAnchorCheckPassPushMsg(ctx, uid, checkLevel, tNow, uint32(taskType))

	return nil
}

func (m *AnchorLevelMgr) handleSetAnchorCheckPassPushMsg(ctx context.Context, uid uint32, checkLevel string, tNow time.Time, taskType uint32) {
	if taskType == taskTypeNew {
		log.InfoWithCtx(ctx, "SetAnchorCheckPass handleSetAnchorCheckPassPushMsg uid=%d taskType=%d", uid, taskType)
		return
	}

	if checkLevel == "S" || checkLevel == "A" {
		taskBeginTime := utils.GetAnchorNewBeginTime(tNow.Unix())
		if taskBeginTime.Unix() > tNow.Unix() {
			err := m.SendVoiceLiveMsg(ctx, uid, fmt.Sprintf("恭喜你考核通过正式成为达人，由于你的考核等级为%s，已自动获得热门流量卡2张+高级新人流量包2周（有效期为本日-次周日23:59）；\n温馨提醒：你的%s周任务将在下周一开启，完成可以获得更多流量哦，点击前往达人中心查看>>", checkLevel, getTaskName(int(taskType))),
				"点击前往达人中心查看>>", true)
			if err != nil {
				log.Errorf(SendVoiceLiveMsgErr, err)
				//return err
			}
		} else {
			err := m.SendVoiceLiveMsg(ctx, uid, fmt.Sprintf("恭喜你考核通过正式成为达人，由于你的考核等级为%s，已自动获得热门流量卡2张+高级新人流量包2周，（有效期为本日-次周日23:59）；\n温馨提醒：你的%s周任务已开启，完成可以获得更多流量哦，点击前往达人中心查看>>", checkLevel, getTaskName(int(taskType))), "点击前往达人中心查看>>", true)
			if err != nil {
				log.Errorf(SendVoiceLiveMsgErr, err)
				//return err
			}
		}

	} else {
		taskBeginTime := utils.GetAnchorNewBeginTime(tNow.Unix())
		if taskBeginTime.Unix() > tNow.Unix() {
			err := m.SendVoiceLiveMsg(ctx, uid, fmt.Sprintf("恭喜你考核通过正式成为达人，由于你的考核等级为%s，已自动获得基础新人流量扶持(每天有效听听2小时第二天即可获得，有效期至下周日)；\n温馨提醒：你的%s周任务将在下周一开启，完成可以获得更多流量哦，点击前往达人中心查看>>", checkLevel, getTaskName(int(taskType))),
				"点击前往达人中心查看>>", true)
			if err != nil {
				log.Errorf(SendVoiceLiveMsgErr, err)
				//return err
			}
		} else {
			err := m.SendVoiceLiveMsg(ctx, uid,
				fmt.Sprintf("恭喜你考核通过正式成为达人，由于你的考核等级为%s，已自动获得基础新人流量扶持(每天有效听听2小时第二天即可获得，有效期至下周日)；\n温馨提醒：你的%s周任务已开启，完成可以获得更多流量哦，点击前往达人中心查看>>", checkLevel, getTaskName(int(taskType))),
				"点击前往达人中心查看>>", true)
			if err != nil {
				log.Errorf(SendVoiceLiveMsgErr, err)
				//return err
			}
		}

	}
}

func (m *AnchorLevelMgr) SettleNewTaskAward() error {

	/*
		考核C级的新主播：从考核通过开始到次周日23:59期间内，若当日直播有效时长满2小时获得列表进房S等级（有效期：当天-次日23:59:59）
		考核B级的新主播：从考核通过开始到次周日23:59期间内，若当日直播有效时长满2小时获得列表S+快速进房D（有效期：当天-次日23:59:59），以及1张普通流量卡（有效期至首次考核通过的次月最后一天23:59:59
	*/
	err := m.reporter.SendInfo("考核新主播奖励发放 start")
	if err != nil {
		log.Errorf("SettleNewTaskAward reporter.SendInfo err=%v", err)
	}

	defer func() {
		if err != nil {
			log.Errorln(err)
			msg := fmt.Sprintf("【考核新主播奖励发放】失败，err %+v", err)
			m.reporter.SendError(msg)
		} else {
			m.reporter.SendInfo("【考核新主播奖励发放】结束")
		}
	}()

	now := time.Now()
	nowDayZero := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)

	tm := now.AddDate(0, 0, -1)
	YestedayZero := time.Date(tm.Year(), tm.Month(), tm.Day(), 0, 0, 0, 0, time.Local)
	startTs := utils.GetThisWeekBeginTime(tm).AddDate(0, 0, -7).Unix()

	liveValidMinutes := m.dyconfig.GetLiveValidMinutes()

	log.Infof("SettleNewTaskAward startTs=%d liveValidMinutes=%d", startTs, liveValidMinutes)

	list, err := m.store.GetAnchorLevelNewTaskAward(context.Background(), uint32(startTs))
	if err != nil {
		log.Errorf("SettleNewTaskAward GetAnchorLevelNewTaskMission fail %v", err)
		return err
	}
	log.Infof("SettleNewTaskAward GetAnchorLevelNewTaskAward size=%d startTs=%d", len(list), startTs)

	ctx := context.Background()

	for _, info := range list {
		if uint32(info.CreateTime) > uint32(nowDayZero.Unix()) {
			log.Warnf("SettleNewTaskAward info=%+v nowDayZero=%+v", info, nowDayZero.Unix())
			continue
		}
		if info.TaskType == 0 {
			uid := info.Uid
			liveInfo, serverError := m.channelLiveStats.GetAnchorBaseInfo(ctx, 0, uid)
			if serverError != nil {
				log.ErrorWithCtx(ctx, "SendNewAnchorTaskReward .GetAnchorBaseInfo fail err=%v uid=%d", serverError, uid)
				return serverError
			}

			info.TaskType, err = m.GetTaskType(ctx, uid, now, liveInfo)
			if err != nil {
				log.Errorf("SendNewAnchorTaskReward GetTaskType err=%v , uid %d", err, uid)
				return err
			}

			log.InfoWithCtx(ctx, "SendNewAnchorTaskReward uid=%d taskType=%d", uid, info.TaskType)

			if m.dyconfig.GetFakeTaskType(uid) != 0 {
				log.InfoWithCtx(ctx, "GetTaskType GetFakeTaskType=%d", m.dyconfig.GetFakeTaskType(uid))
				info.TaskType = int32(m.dyconfig.GetFakeTaskType(uid))
			}

			// 顺便写到数据库
			err = m.store.InsertAnchorLevelNewTask(ctx, uid, uint32(info.TaskType))
			if err != nil {
				log.Errorf("SendNewAnchorTaskReward InsertAnchorLevelNewTask err=%v , uid %d", err, uid)
			}
		}

		activeDaysResp, serr := m.channelLiveStats.GetAnchorDailyRecordWithDateList(ctx, 0, info.Uid, uint32(YestedayZero.Unix()), uint32(nowDayZero.Unix()))
		if serr != nil {
			log.Errorf("SettleNewTaskAward channelLiveStats.GetAnchorDailyRecordWithDateList fail %v, info=%+v", serr, info)
			continue
			//return serr
		}
		log.Infof("SettleNewTaskAward info=%+v GetAnchorDailyRecordWithDateList=%+v", info, activeDaysResp)
		if len(activeDaysResp.GetList()) == 0 {
			continue
		}

		for _, stats := range activeDaysResp.GetList() {

			if stats.GetLiveValidMinutes() < liveValidMinutes {
				continue
			}

			if info.TaskType == int32(taskTypeTotalNew) || info.TaskType == int32(taskTypeComeback) {
				channelLevel := uint32(entertainmentRecommendBack.ChannelLevel_Channel_Invalid)
				quickLevel := uint32(entertainmentRecommendBack.ChannelLevel_Channel_Invalid)
				if info.CheckLevel == "B" {
					channelLevel = uint32(entertainmentRecommendBack.ChannelLevel_Channel_Level_S)
					quickLevel = uint32(entertainmentRecommendBack.ChannelLevel_Channel_Level_D) //快速进房D
				} else if info.CheckLevel == "C" {
					channelLevel = uint32(entertainmentRecommendBack.ChannelLevel_Channel_Level_S)
				}

				// 发放快速进房流量
				var retry uint32
				for retry = 0; retry < 3; retry++ {
					log.Infof("SettleNewTaskAward awardPrepareChannel uid=%d channelLevel=%d quickLevel=%d", info.Uid, channelLevel, quickLevel)
					err = m.awardPrepareChannel(ctx, info.Uid, channelLevel, quickLevel, uint32(now.Unix()), uint32(nowDayZero.AddDate(0, 0, 1).Unix()))
					if err == nil {
						break
					}
					log.Errorf("SettleNewTaskAward awardPrepareChannel fail %v, retry=%d uid=%d", err, retry, info.Uid)
					time.Sleep(time.Second * 3)
				}
				if retry == 3 {
					log.Errorf("SettleNewTaskAward awardPrepareChannel fail %v, retry=%d uid=%d", err, retry, info.Uid)
					//return err
				}
				break
			} else {
				channelLevel := uint32(entertainmentRecommendBack.ChannelLevel_Channel_Level_A)
				quickLevel := uint32(entertainmentRecommendBack.ChannelLevel_Channel_Invalid)

				// 发放快速进房流量
				var retry uint32
				for retry = 0; retry < 3; retry++ {
					log.Infof("SettleNewTaskAward awardPrepareChannel uid=%d channelLevel=%d quickLevel=%d", info.Uid, channelLevel, quickLevel)
					err = m.awardPrepareChannel(ctx, info.Uid, channelLevel, quickLevel, uint32(now.Unix()), uint32(nowDayZero.AddDate(0, 0, 1).Unix()))
					if err == nil {
						break
					}
					log.Errorf("SettleNewTaskAward awardPrepareChannel fail %v, retry=%d uid=%d", err, retry, info.Uid)
					time.Sleep(time.Second * 3)
				}
				if retry == 3 {
					log.Errorf("SettleNewTaskAward awardPrepareChannel fail %v, retry=%d uid=%d", err, retry, info.Uid)
					//return err
				}
				break
			}

		}
	}
	return nil
}

func (m *AnchorLevelMgr) awardPrepareChannel(ctx context.Context, uid, channelLevel, quickLevel, awardBeginTs, awardEndTs uint32) error {

	channelLiveInfo, serverError := m.channelLiveMgr.GetChannelLiveInfo(ctx, uid, false)
	if serverError != nil {
		log.ErrorWithCtx(ctx, "awardPrepareChannel .GetChannelLiveInfo fail uid=%d, err=%v", uid, serverError)
		if serverError.Code() == status.ErrChannelLiveNotAuthority {
			return nil
		}
		return serverError
	}
	contractInfo, err := m.anchorcontractGoClient.GetUserContractCacheInfo(ctx, 0, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "awardPrepareChannel .GetUserContractCacheInfo fail uid=%d, err=%v", uid, err)
		return err
	}

	guildId := contractInfo.GetContract().GetGuildId()
	channelId := channelLiveInfo.GetChannelLiveInfo().GetChannelId()
	tagId := channelLiveInfo.GetChannelLiveInfo().GetTagId()

	liveId := uint32(0)
	confType := uint32(entertainmentRecommendBack.ELivePrepareConfType_EPREPARE_CONF_CONSTANT)
	dayTimeBegin := uint32(0)
	dayTimeEnd := uint32(86400 - 1)

	awardReq := &entertainmentRecommendBack.AddLivePrepareChannelReq{
		ChannelList: []*entertainmentRecommendBack.LivePrepareChannelInfo{
			{
				Id:        &liveId,
				Uid:       &uid,
				ChannelId: &channelId,
				GuildId:   &guildId,
				TagId:     &tagId,
				Level:     &channelLevel,
				TagSections: &entertainmentRecommendBack.TimeSection{
					BeginTime: &awardBeginTs,
					EndTime:   &awardEndTs,
				},
				TagSectionList: &entertainmentRecommendBack.DayTimeSectionList{
					SectList: []*entertainmentRecommendBack.DayTimeSection{
						{
							BeginTs: &dayTimeBegin,
							EndTs:   &dayTimeEnd,
						},
					},
				},
				ConfType: &confType,
			},
		},
	}
	if quickLevel > 0 {
		awardReq.ChannelList[0].QuickEnterInfo = &entertainmentRecommendBack.LiveQuickEnterPrepareInfo{
			Type:       &confType,
			QuickLevel: &quickLevel,
			Sections: &entertainmentRecommendBack.TimeSection{
				BeginTime: &awardBeginTs,
				EndTime:   &awardEndTs,
			},
			DaySectionList: &entertainmentRecommendBack.DayTimeSectionList{
				SectList: []*entertainmentRecommendBack.DayTimeSection{
					{
						BeginTs: &dayTimeBegin,
						EndTs:   &dayTimeEnd,
					},
				},
			},
		}
	}

	log.InfoWithCtx(ctx, "awardPrepareChannel uid=%d req=%s", uid, awardReq.String())
	_, err2 := m.entertainmentRecommendBack.AddLivePrepareChannel(ctx, 0, awardReq)
	if err2 != nil {
		log.ErrorWithCtx(ctx, "awardPrepareChannel .AddLivePrepareChannel fail uid=%d, err=%v", uid, err2)
		return err2
	}

	return nil
}

// CheckIsTotalNewAnchor 检查是否是纯新主播
func (m *AnchorLevelMgr) CheckIsTotalNewAnchor(ctx context.Context, uid uint32) (bool, error) {
	// 纯新主播定义：同实名下所有账号，第一次考核通过
	// 1. 获取实名下所有账号
	realNameUidList, err := m.ttcProxyClient.GetTheSameRealNameUserList(ctx, uint64(uid))
	if err != nil {
		log.Errorf("CheckIsTotalNewAnchor GetTheSameRealNameUserList fail %v, uid=%d", err, uid)
		return false, err
	}

	log.InfoWithCtx(ctx, "CheckIsTotalNewAnchor GetTheSameRealNameUserList uid=%d, realNameUidList=%s", uid, utils2.ToJson(realNameUidList))

	// 1.1 去除本uid
	uidList := make([]uint32, 0)
	for _, elem := range realNameUidList.GetUids() {
		if elem != uid {
			uidList = append(uidList, uint32(elem))
		}
	}

	// 2. 获取实名下所有账号的签约记录

	for _, anchorUid := range uidList {
		resp, err := m.anchorcontractGoClient.GetIdentityChangeHistory(ctx, &anchorcontractgo.GetIdentityChangeHistoryReq{
			AnchorIdentity: anchorcontractgo.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE,
			Uid:            anchorUid,
		})

		if err != nil {
			log.Errorf("CheckIsTotalNewAnchor BatchGetUserLiveAnchorExamine fail %v, uid=%d", err, uid)
			continue
		}

		for _, item := range resp.GetList() {
			if item.GetGuildId() != 0 {
				log.Infof("CheckIsTotalNewAnchor uid=%d, has other examine record", uid)
				return false, nil
			}
		}
	}

	return true, nil
}

func getWeekNum(tNow time.Time, taskBeginTime time.Time) (week int) {
	timeRange := tNow.Sub(taskBeginTime)
	if timeRange.Hours()/24/7 >= 0 {
		week = int(timeRange.Hours()/24/7) + 1
	} else {
		week = 0
	}

	return week
}

func (m *AnchorLevelMgr) GetTaskType(ctx context.Context, uid uint32, nowTime time.Time, info *channel_live_stats.GetAnchorBaseInfoResp) (int32, error) {
	taskType := taskTypeNew
	if info.GetInfo().GetLastLiveAt() != 0 && uint32(nowTime.Unix()) >= info.GetInfo().GetLastLiveAt()+6*MonthSec {
		//当前时间距离主播在平台内最后开播日≥6个月，那就是回归主播
		taskType = taskTypeComeback
	} else {
		//不是回归, 判断是不是纯新
		isTotalNew, err := m.CheckIsTotalNewAnchor(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "SetAnchorCheckPass CheckIsTotalNewAnchor fail err=%v uid=%d", err, uid)
			return int32(taskType), err
		}
		if isTotalNew {
			taskType = taskTypeTotalNew
		} else {
			taskType = taskTypeNew
		}
	}

	return int32(taskType), nil
}
