result=`cp /mnt/hgfs/quicksilver/src/golang.52tt.com/services/channel-deeplink-recommend-logic/proto/channel-deeplink-recommend-logic_.proto /mnt/hgfs/quicksilver/src/golang.52tt.com/third-party/tt-protocol/app/`
if [ -n "$result" ] 
then 
echo "cp err:" 
echo $result 
 exit  
fi 
result=`protoc -I=/mnt/hgfs/quicksilver/src/golang.52tt.com/third-party/tt-protocol/app/  --go_out=plugins=grpc:/mnt/hgfs/quicksilver/src channel-deeplink-recommend-logic_.proto`
if [ -n "$result" ] 
then  
echo "protoc err:"  
echo $result  
exit  
fi 
