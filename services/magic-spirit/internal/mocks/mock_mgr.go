// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/magic-spirit/internal/manager (interfaces: IMagicSpiritMgr)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	magic_spirit "golang.52tt.com/protocol/services/magic-spirit"
	present_middleware "golang.52tt.com/protocol/services/present-middleware"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	cb "golang.52tt.com/protocol/services/unified_pay/cb"
	define "golang.52tt.com/services/magic-spirit/internal/define"
	mysql "golang.52tt.com/services/magic-spirit/internal/mysql"
)

// MockIMagicSpiritMgr is a mock of IMagicSpiritMgr interface.
type MockIMagicSpiritMgr struct {
	ctrl     *gomock.Controller
	recorder *MockIMagicSpiritMgrMockRecorder
}

// MockIMagicSpiritMgrMockRecorder is the mock recorder for MockIMagicSpiritMgr.
type MockIMagicSpiritMgrMockRecorder struct {
	mock *MockIMagicSpiritMgr
}

// NewMockIMagicSpiritMgr creates a new mock instance.
func NewMockIMagicSpiritMgr(ctrl *gomock.Controller) *MockIMagicSpiritMgr {
	mock := &MockIMagicSpiritMgr{ctrl: ctrl}
	mock.recorder = &MockIMagicSpiritMgrMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIMagicSpiritMgr) EXPECT() *MockIMagicSpiritMgrMockRecorder {
	return m.recorder
}

// AddMagicSpirit mocks base method.
func (m *MockIMagicSpiritMgr) AddMagicSpirit(arg0 context.Context, arg1 *magic_spirit.MagicSpirit) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddMagicSpirit", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddMagicSpirit indicates an expected call of AddMagicSpirit.
func (mr *MockIMagicSpiritMgrMockRecorder) AddMagicSpirit(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddMagicSpirit", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).AddMagicSpirit), arg0, arg1)
}

// AddMagicSpiritBlacklist mocks base method.
func (m *MockIMagicSpiritMgr) AddMagicSpiritBlacklist(arg0 context.Context, arg1 []*mysql.MagicSpiritBlacklist) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddMagicSpiritBlacklist", arg0, arg1)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddMagicSpiritBlacklist indicates an expected call of AddMagicSpiritBlacklist.
func (mr *MockIMagicSpiritMgrMockRecorder) AddMagicSpiritBlacklist(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddMagicSpiritBlacklist", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).AddMagicSpiritBlacklist), arg0, arg1)
}

// AddMagicSpiritPond mocks base method.
func (m *MockIMagicSpiritMgr) AddMagicSpiritPond(arg0 context.Context, arg1 []*mysql.MagicSpiritPond) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddMagicSpiritPond", arg0, arg1)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddMagicSpiritPond indicates an expected call of AddMagicSpiritPond.
func (mr *MockIMagicSpiritMgrMockRecorder) AddMagicSpiritPond(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddMagicSpiritPond", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).AddMagicSpiritPond), arg0, arg1)
}

// AddUnpackList mocks base method.
func (m *MockIMagicSpiritMgr) AddUnpackList(arg0 context.Context, arg1, arg2 uint32, arg3 []*magic_spirit.UnpackGiftInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUnpackList", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddUnpackList indicates an expected call of AddUnpackList.
func (mr *MockIMagicSpiritMgrMockRecorder) AddUnpackList(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUnpackList", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).AddUnpackList), arg0, arg1, arg2, arg3)
}

// Callback mocks base method.
func (m *MockIMagicSpiritMgr) Callback(arg0 context.Context, arg1 string) (cb.Op, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Callback", arg0, arg1)
	ret0, _ := ret[0].(cb.Op)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Callback indicates an expected call of Callback.
func (mr *MockIMagicSpiritMgrMockRecorder) Callback(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Callback", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).Callback), arg0, arg1)
}

// CheckAndUpdateMagicSpirit mocks base method.
func (m *MockIMagicSpiritMgr) CheckAndUpdateMagicSpirit(arg0 context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "CheckAndUpdateMagicSpirit", arg0)
}

// CheckAndUpdateMagicSpirit indicates an expected call of CheckAndUpdateMagicSpirit.
func (mr *MockIMagicSpiritMgrMockRecorder) CheckAndUpdateMagicSpirit(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckAndUpdateMagicSpirit", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).CheckAndUpdateMagicSpirit), arg0)
}

// CheckIfSendMagicSpirit mocks base method.
func (m *MockIMagicSpiritMgr) CheckIfSendMagicSpirit(arg0 context.Context, arg1 *define.SendInfo, arg2 *magic_spirit.MagicSpirit) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIfSendMagicSpirit", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckIfSendMagicSpirit indicates an expected call of CheckIfSendMagicSpirit.
func (mr *MockIMagicSpiritMgrMockRecorder) CheckIfSendMagicSpirit(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIfSendMagicSpirit", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).CheckIfSendMagicSpirit), arg0, arg1, arg2)
}

// CheckIfSendMagicWithSource mocks base method.
func (m *MockIMagicSpiritMgr) CheckIfSendMagicWithSource(arg0 context.Context, arg1 *magic_spirit.CheckIfSendMagicWithSourceReq) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIfSendMagicWithSource", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIfSendMagicWithSource indicates an expected call of CheckIfSendMagicWithSource.
func (mr *MockIMagicSpiritMgrMockRecorder) CheckIfSendMagicWithSource(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIfSendMagicWithSource", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).CheckIfSendMagicWithSource), arg0, arg1)
}

// CheckMagicPondUpdate mocks base method.
func (m *MockIMagicSpiritMgr) CheckMagicPondUpdate(arg0 context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "CheckMagicPondUpdate", arg0)
}

// CheckMagicPondUpdate indicates an expected call of CheckMagicPondUpdate.
func (mr *MockIMagicSpiritMgrMockRecorder) CheckMagicPondUpdate(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckMagicPondUpdate", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).CheckMagicPondUpdate), arg0)
}

// CheckProfit mocks base method.
func (m *MockIMagicSpiritMgr) CheckProfit(arg0 context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "CheckProfit", arg0)
}

// CheckProfit indicates an expected call of CheckProfit.
func (mr *MockIMagicSpiritMgrMockRecorder) CheckProfit(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckProfit", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).CheckProfit), arg0)
}

// DayReport mocks base method.
func (m *MockIMagicSpiritMgr) DayReport(arg0 context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "DayReport", arg0)
}

// DayReport indicates an expected call of DayReport.
func (mr *MockIMagicSpiritMgrMockRecorder) DayReport(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DayReport", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).DayReport), arg0)
}

// DelMagicSpirit mocks base method.
func (m *MockIMagicSpiritMgr) DelMagicSpirit(arg0 context.Context, arg1 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelMagicSpirit", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelMagicSpirit indicates an expected call of DelMagicSpirit.
func (mr *MockIMagicSpiritMgrMockRecorder) DelMagicSpirit(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelMagicSpirit", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).DelMagicSpirit), arg0, arg1)
}

// DelMagicSpiritBlacklist mocks base method.
func (m *MockIMagicSpiritMgr) DelMagicSpiritBlacklist(arg0 context.Context, arg1 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelMagicSpiritBlacklist", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelMagicSpiritBlacklist indicates an expected call of DelMagicSpiritBlacklist.
func (mr *MockIMagicSpiritMgrMockRecorder) DelMagicSpiritBlacklist(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelMagicSpiritBlacklist", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).DelMagicSpiritBlacklist), arg0, arg1)
}

// GenFinancialFile mocks base method.
func (m *MockIMagicSpiritMgr) GenFinancialFile(arg0 context.Context, arg1 *reconcile_v2.GenFinancialFileReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenFinancialFile", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// GenFinancialFile indicates an expected call of GenFinancialFile.
func (mr *MockIMagicSpiritMgrMockRecorder) GenFinancialFile(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenFinancialFile", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).GenFinancialFile), arg0, arg1)
}

// GetAccessResult mocks base method.
func (m *MockIMagicSpiritMgr) GetAccessResult(arg0 context.Context, arg1, arg2, arg3 uint32, arg4 bool) (uint32, map[uint32]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccessResult", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(map[uint32]uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetAccessResult indicates an expected call of GetAccessResult.
func (mr *MockIMagicSpiritMgrMockRecorder) GetAccessResult(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccessResult", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).GetAccessResult), arg0, arg1, arg2, arg3, arg4)
}

// GetChannelAllUnpackGift mocks base method.
func (m *MockIMagicSpiritMgr) GetChannelAllUnpackGift(arg0 context.Context, arg1, arg2 uint32) (*magic_spirit.GetChannelAllUnpackGiftResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelAllUnpackGift", arg0, arg1, arg2)
	ret0, _ := ret[0].(*magic_spirit.GetChannelAllUnpackGiftResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelAllUnpackGift indicates an expected call of GetChannelAllUnpackGift.
func (mr *MockIMagicSpiritMgrMockRecorder) GetChannelAllUnpackGift(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelAllUnpackGift", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).GetChannelAllUnpackGift), arg0, arg1, arg2)
}

// GetCommonConf mocks base method.
func (m *MockIMagicSpiritMgr) GetCommonConf(arg0 context.Context) ([]*mysql.MagicSpiritCommonConf, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCommonConf", arg0)
	ret0, _ := ret[0].([]*mysql.MagicSpiritCommonConf)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCommonConf indicates an expected call of GetCommonConf.
func (mr *MockIMagicSpiritMgrMockRecorder) GetCommonConf(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCommonConf", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).GetCommonConf), arg0)
}

// GetCommonConfWithCache mocks base method.
func (m *MockIMagicSpiritMgr) GetCommonConfWithCache(arg0 context.Context) (map[uint32]*mysql.MagicSpiritCommonConf, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCommonConfWithCache", arg0)
	ret0, _ := ret[0].(map[uint32]*mysql.MagicSpiritCommonConf)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCommonConfWithCache indicates an expected call of GetCommonConfWithCache.
func (mr *MockIMagicSpiritMgrMockRecorder) GetCommonConfWithCache(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCommonConfWithCache", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).GetCommonConfWithCache), arg0)
}

// GetMagicAwardOrderIdList mocks base method.
func (m *MockIMagicSpiritMgr) GetMagicAwardOrderIdList(arg0 context.Context, arg1, arg2 time.Time) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicAwardOrderIdList", arg0, arg1, arg2)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMagicAwardOrderIdList indicates an expected call of GetMagicAwardOrderIdList.
func (mr *MockIMagicSpiritMgrMockRecorder) GetMagicAwardOrderIdList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicAwardOrderIdList", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).GetMagicAwardOrderIdList), arg0, arg1, arg2)
}

// GetMagicAwardTotal mocks base method.
func (m *MockIMagicSpiritMgr) GetMagicAwardTotal(arg0 context.Context, arg1, arg2 time.Time) (uint32, uint64, uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicAwardTotal", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(uint64)
	ret2, _ := ret[2].(uint64)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// GetMagicAwardTotal indicates an expected call of GetMagicAwardTotal.
func (mr *MockIMagicSpiritMgrMockRecorder) GetMagicAwardTotal(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicAwardTotal", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).GetMagicAwardTotal), arg0, arg1, arg2)
}

// GetMagicConsumeOrderIdList mocks base method.
func (m *MockIMagicSpiritMgr) GetMagicConsumeOrderIdList(arg0 context.Context, arg1, arg2 time.Time, arg3 uint32) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicConsumeOrderIdList", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMagicConsumeOrderIdList indicates an expected call of GetMagicConsumeOrderIdList.
func (mr *MockIMagicSpiritMgrMockRecorder) GetMagicConsumeOrderIdList(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicConsumeOrderIdList", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).GetMagicConsumeOrderIdList), arg0, arg1, arg2, arg3)
}

// GetMagicOrderTotal mocks base method.
func (m *MockIMagicSpiritMgr) GetMagicOrderTotal(arg0 context.Context, arg1, arg2 time.Time, arg3 uint32) (uint32, uint64, uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicOrderTotal", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(uint64)
	ret2, _ := ret[2].(uint64)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// GetMagicOrderTotal indicates an expected call of GetMagicOrderTotal.
func (mr *MockIMagicSpiritMgrMockRecorder) GetMagicOrderTotal(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicOrderTotal", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).GetMagicOrderTotal), arg0, arg1, arg2, arg3)
}

// GetMagicSpirit mocks base method.
func (m *MockIMagicSpiritMgr) GetMagicSpirit(arg0 context.Context) ([]*mysql.MagicSpirit, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicSpirit", arg0)
	ret0, _ := ret[0].([]*mysql.MagicSpirit)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMagicSpirit indicates an expected call of GetMagicSpirit.
func (mr *MockIMagicSpiritMgrMockRecorder) GetMagicSpirit(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicSpirit", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).GetMagicSpirit), arg0)
}

// GetMagicSpiritBlacklist mocks base method.
func (m *MockIMagicSpiritMgr) GetMagicSpiritBlacklist(arg0 context.Context, arg1, arg2, arg3 uint32) (uint32, []*mysql.MagicSpiritBlacklist, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicSpiritBlacklist", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].([]*mysql.MagicSpiritBlacklist)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetMagicSpiritBlacklist indicates an expected call of GetMagicSpiritBlacklist.
func (mr *MockIMagicSpiritMgrMockRecorder) GetMagicSpiritBlacklist(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicSpiritBlacklist", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).GetMagicSpiritBlacklist), arg0, arg1, arg2, arg3)
}

// GetMagicSpiritById mocks base method.
func (m *MockIMagicSpiritMgr) GetMagicSpiritById(arg0 context.Context, arg1 uint32) (*magic_spirit.MagicSpirit, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicSpiritById", arg0, arg1)
	ret0, _ := ret[0].(*magic_spirit.MagicSpirit)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMagicSpiritById indicates an expected call of GetMagicSpiritById.
func (mr *MockIMagicSpiritMgrMockRecorder) GetMagicSpiritById(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicSpiritById", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).GetMagicSpiritById), arg0, arg1)
}

// GetMagicSpiritByIds mocks base method.
func (m *MockIMagicSpiritMgr) GetMagicSpiritByIds(arg0 context.Context, arg1 []uint32) (map[uint32]*magic_spirit.MagicSpirit, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicSpiritByIds", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]*magic_spirit.MagicSpirit)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMagicSpiritByIds indicates an expected call of GetMagicSpiritByIds.
func (mr *MockIMagicSpiritMgrMockRecorder) GetMagicSpiritByIds(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicSpiritByIds", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).GetMagicSpiritByIds), arg0, arg1)
}

// GetMagicSpiritPond mocks base method.
func (m *MockIMagicSpiritMgr) GetMagicSpiritPond(arg0 context.Context, arg1 uint32) ([]*mysql.MagicSpiritPond, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicSpiritPond", arg0, arg1)
	ret0, _ := ret[0].([]*mysql.MagicSpiritPond)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMagicSpiritPond indicates an expected call of GetMagicSpiritPond.
func (mr *MockIMagicSpiritMgrMockRecorder) GetMagicSpiritPond(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicSpiritPond", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).GetMagicSpiritPond), arg0, arg1)
}

// GetMagicSpiritPondTmp mocks base method.
func (m *MockIMagicSpiritMgr) GetMagicSpiritPondTmp(arg0 context.Context, arg1 time.Time) (map[uint32][]*mysql.MagicSpiritPond, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicSpiritPondTmp", arg0, arg1)
	ret0, _ := ret[0].(map[uint32][]*mysql.MagicSpiritPond)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMagicSpiritPondTmp indicates an expected call of GetMagicSpiritPondTmp.
func (mr *MockIMagicSpiritMgrMockRecorder) GetMagicSpiritPondTmp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicSpiritPondTmp", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).GetMagicSpiritPondTmp), arg0, arg1)
}

// GetMagicSpiritPondWithCache mocks base method.
func (m *MockIMagicSpiritMgr) GetMagicSpiritPondWithCache(arg0 context.Context, arg1 uint32) ([]*mysql.MagicSpiritPond, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicSpiritPondWithCache", arg0, arg1)
	ret0, _ := ret[0].([]*mysql.MagicSpiritPond)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMagicSpiritPondWithCache indicates an expected call of GetMagicSpiritPondWithCache.
func (mr *MockIMagicSpiritMgrMockRecorder) GetMagicSpiritPondWithCache(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicSpiritPondWithCache", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).GetMagicSpiritPondWithCache), arg0, arg1)
}

// GetMagicSpiritTmp mocks base method.
func (m *MockIMagicSpiritMgr) GetMagicSpiritTmp(arg0 context.Context) ([]*magic_spirit.MagicSpiritTmp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicSpiritTmp", arg0)
	ret0, _ := ret[0].([]*magic_spirit.MagicSpiritTmp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMagicSpiritTmp indicates an expected call of GetMagicSpiritTmp.
func (mr *MockIMagicSpiritMgrMockRecorder) GetMagicSpiritTmp(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicSpiritTmp", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).GetMagicSpiritTmp), arg0)
}

// GetMagicSpiritUsable mocks base method.
func (m *MockIMagicSpiritMgr) GetMagicSpiritUsable(arg0 context.Context, arg1 *magic_spirit.GetMagicSpiritUsableReq) (*magic_spirit.GetMagicSpiritUsableResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicSpiritUsable", arg0, arg1)
	ret0, _ := ret[0].(*magic_spirit.GetMagicSpiritUsableResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMagicSpiritUsable indicates an expected call of GetMagicSpiritUsable.
func (mr *MockIMagicSpiritMgrMockRecorder) GetMagicSpiritUsable(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicSpiritUsable", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).GetMagicSpiritUsable), arg0, arg1)
}

// GetMagicSpiritVersion mocks base method.
func (m *MockIMagicSpiritMgr) GetMagicSpiritVersion(arg0 context.Context) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicSpiritVersion", arg0)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMagicSpiritVersion indicates an expected call of GetMagicSpiritVersion.
func (mr *MockIMagicSpiritMgrMockRecorder) GetMagicSpiritVersion(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicSpiritVersion", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).GetMagicSpiritVersion), arg0)
}

// GetMagicSpiritWithCache mocks base method.
func (m *MockIMagicSpiritMgr) GetMagicSpiritWithCache(arg0 context.Context) ([]*mysql.MagicSpirit, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicSpiritWithCache", arg0)
	ret0, _ := ret[0].([]*mysql.MagicSpirit)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMagicSpiritWithCache indicates an expected call of GetMagicSpiritWithCache.
func (mr *MockIMagicSpiritMgrMockRecorder) GetMagicSpiritWithCache(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicSpiritWithCache", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).GetMagicSpiritWithCache), arg0)
}

// GetUserExemptCondVal mocks base method.
func (m *MockIMagicSpiritMgr) GetUserExemptCondVal(arg0 context.Context, arg1 uint32, arg2 ...uint32) (map[uint32]bool, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserExemptCondVal", varargs...)
	ret0, _ := ret[0].(map[uint32]bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserExemptCondVal indicates an expected call of GetUserExemptCondVal.
func (mr *MockIMagicSpiritMgrMockRecorder) GetUserExemptCondVal(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserExemptCondVal", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).GetUserExemptCondVal), varargs...)
}

// HourReport mocks base method.
func (m *MockIMagicSpiritMgr) HourReport(arg0 context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "HourReport", arg0)
}

// HourReport indicates an expected call of HourReport.
func (mr *MockIMagicSpiritMgrMockRecorder) HourReport(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HourReport", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).HourReport), arg0)
}

// JudgeExpectProfit mocks base method.
func (m *MockIMagicSpiritMgr) JudgeExpectProfit(arg0 []*mysql.MagicSpiritPond, arg1 uint32) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JudgeExpectProfit", arg0, arg1)
	ret0, _ := ret[0].(bool)
	return ret0
}

// JudgeExpectProfit indicates an expected call of JudgeExpectProfit.
func (mr *MockIMagicSpiritMgrMockRecorder) JudgeExpectProfit(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JudgeExpectProfit", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).JudgeExpectProfit), arg0, arg1)
}

// LotteryDraw mocks base method.
func (m *MockIMagicSpiritMgr) LotteryDraw(arg0 context.Context, arg1 string, arg2 *define.SendInfo, arg3 *magic_spirit.MagicSpirit, arg4 time.Time) ([]*magic_spirit.PresentSendInfo, []*magic_spirit.UnpackGiftInfo, map[string]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LotteryDraw", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*magic_spirit.PresentSendInfo)
	ret1, _ := ret[1].([]*magic_spirit.UnpackGiftInfo)
	ret2, _ := ret[2].(map[string]string)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// LotteryDraw indicates an expected call of LotteryDraw.
func (mr *MockIMagicSpiritMgrMockRecorder) LotteryDraw(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LotteryDraw", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).LotteryDraw), arg0, arg1, arg2, arg3, arg4)
}

// OpenUnpackGift mocks base method.
func (m *MockIMagicSpiritMgr) OpenUnpackGift(arg0 context.Context, arg1, arg2 uint32, arg3 string, arg4 bool) (*present_middleware.SendMagicSpiritOpt, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OpenUnpackGift", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*present_middleware.SendMagicSpiritOpt)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OpenUnpackGift indicates an expected call of OpenUnpackGift.
func (mr *MockIMagicSpiritMgrMockRecorder) OpenUnpackGift(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OpenUnpackGift", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).OpenUnpackGift), arg0, arg1, arg2, arg3, arg4)
}

// ReissueMagicOrder mocks base method.
func (m *MockIMagicSpiritMgr) ReissueMagicOrder(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReissueMagicOrder", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReissueMagicOrder indicates an expected call of ReissueMagicOrder.
func (mr *MockIMagicSpiritMgrMockRecorder) ReissueMagicOrder(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReissueMagicOrder", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).ReissueMagicOrder), arg0, arg1)
}

// SendMagicSpirit mocks base method.
func (m *MockIMagicSpiritMgr) SendMagicSpirit(arg0 context.Context, arg1 *magic_spirit.SendMagicSpiritReq) (*magic_spirit.SendMagicSpiritResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendMagicSpirit", arg0, arg1)
	ret0, _ := ret[0].(*magic_spirit.SendMagicSpiritResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendMagicSpirit indicates an expected call of SendMagicSpirit.
func (mr *MockIMagicSpiritMgrMockRecorder) SendMagicSpirit(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMagicSpirit", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).SendMagicSpirit), arg0, arg1)
}

// SendMagicWithSource mocks base method.
func (m *MockIMagicSpiritMgr) SendMagicWithSource(arg0 context.Context, arg1 *magic_spirit.SendMagicWithSourceReq) (*magic_spirit.SendMagicWithSourceResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendMagicWithSource", arg0, arg1)
	ret0, _ := ret[0].(*magic_spirit.SendMagicWithSourceResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendMagicWithSource indicates an expected call of SendMagicWithSource.
func (mr *MockIMagicSpiritMgrMockRecorder) SendMagicWithSource(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMagicWithSource", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).SendMagicWithSource), arg0, arg1)
}

// SendPresent mocks base method.
func (m *MockIMagicSpiritMgr) SendPresent(arg0 context.Context, arg1 string, arg2 int64, arg3 *magic_spirit.MagicSpirit, arg4 []*magic_spirit.PresentSendInfo, arg5 *define.SendInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendPresent", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendPresent indicates an expected call of SendPresent.
func (mr *MockIMagicSpiritMgrMockRecorder) SendPresent(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendPresent", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).SendPresent), arg0, arg1, arg2, arg3, arg4, arg5)
}

// SetCommonConf mocks base method.
func (m *MockIMagicSpiritMgr) SetCommonConf(arg0 context.Context, arg1 []*mysql.MagicSpiritCommonConf) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetCommonConf", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetCommonConf indicates an expected call of SetCommonConf.
func (mr *MockIMagicSpiritMgrMockRecorder) SetCommonConf(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetCommonConf", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).SetCommonConf), arg0, arg1)
}

// SetMagicSpiritTemporary mocks base method.
func (m *MockIMagicSpiritMgr) SetMagicSpiritTemporary(arg0 context.Context, arg1 *magic_spirit.MagicSpiritTmp) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetMagicSpiritTemporary", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetMagicSpiritTemporary indicates an expected call of SetMagicSpiritTemporary.
func (mr *MockIMagicSpiritMgrMockRecorder) SetMagicSpiritTemporary(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMagicSpiritTemporary", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).SetMagicSpiritTemporary), arg0, arg1)
}

// ShutDown mocks base method.
func (m *MockIMagicSpiritMgr) ShutDown() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ShutDown")
}

// ShutDown indicates an expected call of ShutDown.
func (mr *MockIMagicSpiritMgrMockRecorder) ShutDown() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ShutDown", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).ShutDown))
}

// StartTimer mocks base method.
func (m *MockIMagicSpiritMgr) StartTimer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "StartTimer")
}

// StartTimer indicates an expected call of StartTimer.
func (mr *MockIMagicSpiritMgrMockRecorder) StartTimer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartTimer", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).StartTimer))
}

// TimerHandle mocks base method.
func (m *MockIMagicSpiritMgr) TimerHandle(arg0 time.Duration, arg1 func()) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "TimerHandle", arg0, arg1)
}

// TimerHandle indicates an expected call of TimerHandle.
func (mr *MockIMagicSpiritMgrMockRecorder) TimerHandle(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TimerHandle", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).TimerHandle), arg0, arg1)
}

// UnpackListChangeNotify mocks base method.
func (m *MockIMagicSpiritMgr) UnpackListChangeNotify(arg0 context.Context, arg1, arg2 uint32, arg3 []*magic_spirit.UnpackGiftInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnpackListChangeNotify", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnpackListChangeNotify indicates an expected call of UnpackListChangeNotify.
func (mr *MockIMagicSpiritMgrMockRecorder) UnpackListChangeNotify(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnpackListChangeNotify", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).UnpackListChangeNotify), arg0, arg1, arg2, arg3)
}

// UpdateMagicSpirit mocks base method.
func (m *MockIMagicSpiritMgr) UpdateMagicSpirit(arg0 context.Context, arg1 *magic_spirit.MagicSpirit, arg2 time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateMagicSpirit", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateMagicSpirit indicates an expected call of UpdateMagicSpirit.
func (mr *MockIMagicSpiritMgrMockRecorder) UpdateMagicSpirit(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateMagicSpirit", reflect.TypeOf((*MockIMagicSpiritMgr)(nil).UpdateMagicSpirit), arg0, arg1, arg2)
}
