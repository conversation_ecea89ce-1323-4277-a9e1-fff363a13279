// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/magic-spirit/internal/cache (interfaces: ICache)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	redis "github.com/go-redis/redis/v8"
	gomock "github.com/golang/mock/gomock"
	magic_spirit "golang.52tt.com/protocol/services/magic-spirit"
	mysql "golang.52tt.com/services/magic-spirit/internal/mysql"
)

// MockICache is a mock of ICache interface.
type MockICache struct {
	ctrl     *gomock.Controller
	recorder *MockICacheMockRecorder
}

// MockICacheMockRecorder is the mock recorder for MockICache.
type MockICacheMockRecorder struct {
	mock *MockICache
}

// NewMockICache creates a new mock instance.
func NewMockICache(ctrl *gomock.Controller) *MockICache {
	mock := &MockICache{ctrl: ctrl}
	mock.recorder = &MockICacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICache) EXPECT() *MockICacheMockRecorder {
	return m.recorder
}

// AddUnpackOrderList mocks base method.
func (m *MockICache) AddUnpackOrderList(arg0 context.Context, arg1, arg2 uint32, arg3 []*magic_spirit.UnpackGiftInfo, arg4 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUnpackOrderList", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddUnpackOrderList indicates an expected call of AddUnpackOrderList.
func (mr *MockICacheMockRecorder) AddUnpackOrderList(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUnpackOrderList", reflect.TypeOf((*MockICache)(nil).AddUnpackOrderList), arg0, arg1, arg2, arg3, arg4)
}

// BatGetUserSendFlag mocks base method.
func (m *MockICache) BatGetUserSendFlag(arg0 context.Context, arg1 uint32, arg2 ...uint32) (map[uint32]bool, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatGetUserSendFlag", varargs...)
	ret0, _ := ret[0].(map[uint32]bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetUserSendFlag indicates an expected call of BatGetUserSendFlag.
func (mr *MockICacheMockRecorder) BatGetUserSendFlag(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetUserSendFlag", reflect.TypeOf((*MockICache)(nil).BatGetUserSendFlag), varargs...)
}

// BatchGetUnpackOrderInfo mocks base method.
func (m *MockICache) BatchGetUnpackOrderInfo(arg0 context.Context, arg1 []string) (map[string]*magic_spirit.UnpackGiftInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUnpackOrderInfo", arg0, arg1)
	ret0, _ := ret[0].(map[string]*magic_spirit.UnpackGiftInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUnpackOrderInfo indicates an expected call of BatchGetUnpackOrderInfo.
func (mr *MockICacheMockRecorder) BatchGetUnpackOrderInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUnpackOrderInfo", reflect.TypeOf((*MockICache)(nil).BatchGetUnpackOrderInfo), arg0, arg1)
}

// CheckIfFusing mocks base method.
func (m *MockICache) CheckIfFusing(arg0 context.Context) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIfFusing", arg0)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIfFusing indicates an expected call of CheckIfFusing.
func (mr *MockICacheMockRecorder) CheckIfFusing(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIfFusing", reflect.TypeOf((*MockICache)(nil).CheckIfFusing), arg0)
}

// CheckIfWarning mocks base method.
func (m *MockICache) CheckIfWarning(arg0 context.Context) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIfWarning", arg0)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIfWarning indicates an expected call of CheckIfWarning.
func (mr *MockICacheMockRecorder) CheckIfWarning(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIfWarning", reflect.TypeOf((*MockICache)(nil).CheckIfWarning), arg0)
}

// Close mocks base method.
func (m *MockICache) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockICacheMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockICache)(nil).Close))
}

// DelBlacklist mocks base method.
func (m *MockICache) DelBlacklist(arg0 context.Context, arg1 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelBlacklist", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelBlacklist indicates an expected call of DelBlacklist.
func (mr *MockICacheMockRecorder) DelBlacklist(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelBlacklist", reflect.TypeOf((*MockICache)(nil).DelBlacklist), arg0, arg1)
}

// DelCommonConf mocks base method.
func (m *MockICache) DelCommonConf(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelCommonConf", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelCommonConf indicates an expected call of DelCommonConf.
func (mr *MockICacheMockRecorder) DelCommonConf(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelCommonConf", reflect.TypeOf((*MockICache)(nil).DelCommonConf), arg0)
}

// DelFusing mocks base method.
func (m *MockICache) DelFusing(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelFusing", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelFusing indicates an expected call of DelFusing.
func (mr *MockICacheMockRecorder) DelFusing(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelFusing", reflect.TypeOf((*MockICache)(nil).DelFusing), arg0)
}

// DelMagicSpiritCache mocks base method.
func (m *MockICache) DelMagicSpiritCache(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelMagicSpiritCache", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelMagicSpiritCache indicates an expected call of DelMagicSpiritCache.
func (mr *MockICacheMockRecorder) DelMagicSpiritCache(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelMagicSpiritCache", reflect.TypeOf((*MockICache)(nil).DelMagicSpiritCache), arg0)
}

// DelMagicSpiritPond mocks base method.
func (m *MockICache) DelMagicSpiritPond(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelMagicSpiritPond", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelMagicSpiritPond indicates an expected call of DelMagicSpiritPond.
func (mr *MockICacheMockRecorder) DelMagicSpiritPond(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelMagicSpiritPond", reflect.TypeOf((*MockICache)(nil).DelMagicSpiritPond), arg0)
}

// DelWarning mocks base method.
func (m *MockICache) DelWarning(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelWarning", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelWarning indicates an expected call of DelWarning.
func (mr *MockICacheMockRecorder) DelWarning(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelWarning", reflect.TypeOf((*MockICache)(nil).DelWarning), arg0)
}

// ExistsBlacklist mocks base method.
func (m *MockICache) ExistsBlacklist(arg0 context.Context) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExistsBlacklist", arg0)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExistsBlacklist indicates an expected call of ExistsBlacklist.
func (mr *MockICacheMockRecorder) ExistsBlacklist(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExistsBlacklist", reflect.TypeOf((*MockICache)(nil).ExistsBlacklist), arg0)
}

// ExpireMagicSpirit mocks base method.
func (m *MockICache) ExpireMagicSpirit(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExpireMagicSpirit", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// ExpireMagicSpirit indicates an expected call of ExpireMagicSpirit.
func (mr *MockICacheMockRecorder) ExpireMagicSpirit(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExpireMagicSpirit", reflect.TypeOf((*MockICache)(nil).ExpireMagicSpirit), arg0)
}

// ExpireMagicSpiritPond mocks base method.
func (m *MockICache) ExpireMagicSpiritPond(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExpireMagicSpiritPond", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// ExpireMagicSpiritPond indicates an expected call of ExpireMagicSpiritPond.
func (mr *MockICacheMockRecorder) ExpireMagicSpiritPond(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExpireMagicSpiritPond", reflect.TypeOf((*MockICache)(nil).ExpireMagicSpiritPond), arg0)
}

// GetChannelUnpackOrderIdList mocks base method.
func (m *MockICache) GetChannelUnpackOrderIdList(arg0 context.Context, arg1, arg2, arg3 uint32) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelUnpackOrderIdList", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelUnpackOrderIdList indicates an expected call of GetChannelUnpackOrderIdList.
func (mr *MockICacheMockRecorder) GetChannelUnpackOrderIdList(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelUnpackOrderIdList", reflect.TypeOf((*MockICache)(nil).GetChannelUnpackOrderIdList), arg0, arg1, arg2, arg3)
}

// GetCombCnt mocks base method.
func (m *MockICache) GetCombCnt(arg0 context.Context, arg1, arg2, arg3 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCombCnt", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCombCnt indicates an expected call of GetCombCnt.
func (mr *MockICacheMockRecorder) GetCombCnt(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCombCnt", reflect.TypeOf((*MockICache)(nil).GetCombCnt), arg0, arg1, arg2, arg3)
}

// GetCommonConf mocks base method.
func (m *MockICache) GetCommonConf(arg0 context.Context) ([]*mysql.MagicSpiritCommonConf, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCommonConf", arg0)
	ret0, _ := ret[0].([]*mysql.MagicSpiritCommonConf)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCommonConf indicates an expected call of GetCommonConf.
func (mr *MockICacheMockRecorder) GetCommonConf(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCommonConf", reflect.TypeOf((*MockICache)(nil).GetCommonConf), arg0)
}

// GetHourProfit mocks base method.
func (m *MockICache) GetHourProfit(arg0 context.Context, arg1 int) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHourProfit", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHourProfit indicates an expected call of GetHourProfit.
func (mr *MockICacheMockRecorder) GetHourProfit(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHourProfit", reflect.TypeOf((*MockICache)(nil).GetHourProfit), arg0, arg1)
}

// GetMagicPondVersion mocks base method.
func (m *MockICache) GetMagicPondVersion(arg0 context.Context, arg1 bool) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicPondVersion", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMagicPondVersion indicates an expected call of GetMagicPondVersion.
func (mr *MockICacheMockRecorder) GetMagicPondVersion(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicPondVersion", reflect.TypeOf((*MockICache)(nil).GetMagicPondVersion), arg0, arg1)
}

// GetMagicSpirit mocks base method.
func (m *MockICache) GetMagicSpirit(arg0 context.Context) ([]*mysql.MagicSpirit, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicSpirit", arg0)
	ret0, _ := ret[0].([]*mysql.MagicSpirit)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMagicSpirit indicates an expected call of GetMagicSpirit.
func (mr *MockICacheMockRecorder) GetMagicSpirit(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicSpirit", reflect.TypeOf((*MockICache)(nil).GetMagicSpirit), arg0)
}

// GetMagicSpiritById mocks base method.
func (m *MockICache) GetMagicSpiritById(arg0 context.Context, arg1 uint32) (*mysql.MagicSpirit, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicSpiritById", arg0, arg1)
	ret0, _ := ret[0].(*mysql.MagicSpirit)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetMagicSpiritById indicates an expected call of GetMagicSpiritById.
func (mr *MockICacheMockRecorder) GetMagicSpiritById(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicSpiritById", reflect.TypeOf((*MockICache)(nil).GetMagicSpiritById), arg0, arg1)
}

// GetMagicSpiritPond mocks base method.
func (m *MockICache) GetMagicSpiritPond(arg0 context.Context, arg1, arg2 uint32) ([]*mysql.MagicSpiritPond, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicSpiritPond", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*mysql.MagicSpiritPond)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetMagicSpiritPond indicates an expected call of GetMagicSpiritPond.
func (mr *MockICacheMockRecorder) GetMagicSpiritPond(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicSpiritPond", reflect.TypeOf((*MockICache)(nil).GetMagicSpiritPond), arg0, arg1, arg2)
}

// GetRealNameCheck mocks base method.
func (m *MockICache) GetRealNameCheck(arg0 context.Context, arg1 uint32) (bool, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRealNameCheck", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetRealNameCheck indicates an expected call of GetRealNameCheck.
func (mr *MockICacheMockRecorder) GetRealNameCheck(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRealNameCheck", reflect.TypeOf((*MockICache)(nil).GetRealNameCheck), arg0, arg1)
}

// GetRedisClient mocks base method.
func (m *MockICache) GetRedisClient() redis.Cmdable {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedisClient")
	ret0, _ := ret[0].(redis.Cmdable)
	return ret0
}

// GetRedisClient indicates an expected call of GetRedisClient.
func (mr *MockICacheMockRecorder) GetRedisClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedisClient", reflect.TypeOf((*MockICache)(nil).GetRedisClient))
}

// GetSendDailyPrice mocks base method.
func (m *MockICache) GetSendDailyPrice(arg0 context.Context, arg1, arg2 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSendDailyPrice", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSendDailyPrice indicates an expected call of GetSendDailyPrice.
func (mr *MockICacheMockRecorder) GetSendDailyPrice(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSendDailyPrice", reflect.TypeOf((*MockICache)(nil).GetSendDailyPrice), arg0, arg1, arg2)
}

// GetSendToOtherDailyPriceList mocks base method.
func (m *MockICache) GetSendToOtherDailyPriceList(arg0 context.Context, arg1, arg2 uint32, arg3 []uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSendToOtherDailyPriceList", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSendToOtherDailyPriceList indicates an expected call of GetSendToOtherDailyPriceList.
func (mr *MockICacheMockRecorder) GetSendToOtherDailyPriceList(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSendToOtherDailyPriceList", reflect.TypeOf((*MockICache)(nil).GetSendToOtherDailyPriceList), arg0, arg1, arg2, arg3)
}

// GetSendValid mocks base method.
func (m *MockICache) GetSendValid(arg0 context.Context, arg1 uint32) (bool, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSendValid", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetSendValid indicates an expected call of GetSendValid.
func (mr *MockICacheMockRecorder) GetSendValid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSendValid", reflect.TypeOf((*MockICache)(nil).GetSendValid), arg0, arg1)
}

// GetUnpackOrderInfo mocks base method.
func (m *MockICache) GetUnpackOrderInfo(arg0 context.Context, arg1 string) (*magic_spirit.UnpackGiftInfo, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUnpackOrderInfo", arg0, arg1)
	ret0, _ := ret[0].(*magic_spirit.UnpackGiftInfo)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUnpackOrderInfo indicates an expected call of GetUnpackOrderInfo.
func (mr *MockICacheMockRecorder) GetUnpackOrderInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUnpackOrderInfo", reflect.TypeOf((*MockICache)(nil).GetUnpackOrderInfo), arg0, arg1)
}

// GetUpdateMagicSpiritVersion mocks base method.
func (m *MockICache) GetUpdateMagicSpiritVersion(arg0 context.Context) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUpdateMagicSpiritVersion", arg0)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUpdateMagicSpiritVersion indicates an expected call of GetUpdateMagicSpiritVersion.
func (mr *MockICacheMockRecorder) GetUpdateMagicSpiritVersion(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUpdateMagicSpiritVersion", reflect.TypeOf((*MockICache)(nil).GetUpdateMagicSpiritVersion), arg0)
}

// GetUserSendFlag mocks base method.
func (m *MockICache) GetUserSendFlag(arg0 context.Context, arg1 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserSendFlag", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserSendFlag indicates an expected call of GetUserSendFlag.
func (mr *MockICacheMockRecorder) GetUserSendFlag(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSendFlag", reflect.TypeOf((*MockICache)(nil).GetUserSendFlag), arg0, arg1)
}

// GetUserUnpackOrderIdList mocks base method.
func (m *MockICache) GetUserUnpackOrderIdList(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserUnpackOrderIdList", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserUnpackOrderIdList indicates an expected call of GetUserUnpackOrderIdList.
func (mr *MockICacheMockRecorder) GetUserUnpackOrderIdList(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserUnpackOrderIdList", reflect.TypeOf((*MockICache)(nil).GetUserUnpackOrderIdList), arg0, arg1, arg2, arg3, arg4)
}

// IncrCombCnt mocks base method.
func (m *MockICache) IncrCombCnt(arg0 context.Context, arg1, arg2, arg3, arg4 uint32, arg5 time.Duration) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrCombCnt", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrCombCnt indicates an expected call of IncrCombCnt.
func (mr *MockICacheMockRecorder) IncrCombCnt(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrCombCnt", reflect.TypeOf((*MockICache)(nil).IncrCombCnt), arg0, arg1, arg2, arg3, arg4, arg5)
}

// IncrHourProfit mocks base method.
func (m *MockICache) IncrHourProfit(arg0 context.Context, arg1 int, arg2 int64) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrHourProfit", arg0, arg1, arg2)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrHourProfit indicates an expected call of IncrHourProfit.
func (mr *MockICacheMockRecorder) IncrHourProfit(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrHourProfit", reflect.TypeOf((*MockICache)(nil).IncrHourProfit), arg0, arg1, arg2)
}

// IncrSendDailyPrice mocks base method.
func (m *MockICache) IncrSendDailyPrice(arg0 context.Context, arg1, arg2, arg3 uint32, arg4 time.Duration) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrSendDailyPrice", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrSendDailyPrice indicates an expected call of IncrSendDailyPrice.
func (mr *MockICacheMockRecorder) IncrSendDailyPrice(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrSendDailyPrice", reflect.TypeOf((*MockICache)(nil).IncrSendDailyPrice), arg0, arg1, arg2, arg3, arg4)
}

// IncrSendToOtherDailyPrice mocks base method.
func (m *MockICache) IncrSendToOtherDailyPrice(arg0 context.Context, arg1, arg2 uint32, arg3 map[uint32]uint32, arg4 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrSendToOtherDailyPrice", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// IncrSendToOtherDailyPrice indicates an expected call of IncrSendToOtherDailyPrice.
func (mr *MockICacheMockRecorder) IncrSendToOtherDailyPrice(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrSendToOtherDailyPrice", reflect.TypeOf((*MockICache)(nil).IncrSendToOtherDailyPrice), arg0, arg1, arg2, arg3, arg4)
}

// InitBlacklist mocks base method.
func (m *MockICache) InitBlacklist(arg0 context.Context, arg1 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitBlacklist", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// InitBlacklist indicates an expected call of InitBlacklist.
func (mr *MockICacheMockRecorder) InitBlacklist(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitBlacklist", reflect.TypeOf((*MockICache)(nil).InitBlacklist), arg0, arg1)
}

// IsBlacklist mocks base method.
func (m *MockICache) IsBlacklist(arg0 context.Context, arg1 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsBlacklist", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsBlacklist indicates an expected call of IsBlacklist.
func (mr *MockICacheMockRecorder) IsBlacklist(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsBlacklist", reflect.TypeOf((*MockICache)(nil).IsBlacklist), arg0, arg1)
}

// Lock mocks base method.
func (m *MockICache) Lock(arg0 context.Context, arg1 string, arg2 time.Duration) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Lock", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Lock indicates an expected call of Lock.
func (mr *MockICacheMockRecorder) Lock(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Lock", reflect.TypeOf((*MockICache)(nil).Lock), arg0, arg1, arg2)
}

// PopExpireUnpackOrderId mocks base method.
func (m *MockICache) PopExpireUnpackOrderId(arg0 context.Context) (string, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PopExpireUnpackOrderId", arg0)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// PopExpireUnpackOrderId indicates an expected call of PopExpireUnpackOrderId.
func (mr *MockICacheMockRecorder) PopExpireUnpackOrderId(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PopExpireUnpackOrderId", reflect.TypeOf((*MockICache)(nil).PopExpireUnpackOrderId), arg0)
}

// RemoveUnpackOrder mocks base method.
func (m *MockICache) RemoveUnpackOrder(arg0 context.Context, arg1, arg2 uint32, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveUnpackOrder", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveUnpackOrder indicates an expected call of RemoveUnpackOrder.
func (mr *MockICacheMockRecorder) RemoveUnpackOrder(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveUnpackOrder", reflect.TypeOf((*MockICache)(nil).RemoveUnpackOrder), arg0, arg1, arg2, arg3)
}

// ScriptLoad mocks base method.
func (m *MockICache) ScriptLoad(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ScriptLoad", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// ScriptLoad indicates an expected call of ScriptLoad.
func (mr *MockICacheMockRecorder) ScriptLoad(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScriptLoad", reflect.TypeOf((*MockICache)(nil).ScriptLoad), arg0)
}

// SetBlacklist mocks base method.
func (m *MockICache) SetBlacklist(arg0 context.Context, arg1 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetBlacklist", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetBlacklist indicates an expected call of SetBlacklist.
func (mr *MockICacheMockRecorder) SetBlacklist(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetBlacklist", reflect.TypeOf((*MockICache)(nil).SetBlacklist), arg0, arg1)
}

// SetCommonConf mocks base method.
func (m *MockICache) SetCommonConf(arg0 context.Context, arg1 []*mysql.MagicSpiritCommonConf) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetCommonConf", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetCommonConf indicates an expected call of SetCommonConf.
func (mr *MockICacheMockRecorder) SetCommonConf(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetCommonConf", reflect.TypeOf((*MockICache)(nil).SetCommonConf), arg0, arg1)
}

// SetFusing mocks base method.
func (m *MockICache) SetFusing(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetFusing", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetFusing indicates an expected call of SetFusing.
func (mr *MockICacheMockRecorder) SetFusing(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetFusing", reflect.TypeOf((*MockICache)(nil).SetFusing), arg0)
}

// SetMagicPondVersion mocks base method.
func (m *MockICache) SetMagicPondVersion(arg0 context.Context, arg1 int64, arg2 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetMagicPondVersion", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetMagicPondVersion indicates an expected call of SetMagicPondVersion.
func (mr *MockICacheMockRecorder) SetMagicPondVersion(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMagicPondVersion", reflect.TypeOf((*MockICache)(nil).SetMagicPondVersion), arg0, arg1, arg2)
}

// SetMagicSpirit mocks base method.
func (m *MockICache) SetMagicSpirit(arg0 context.Context, arg1 []*mysql.MagicSpirit) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetMagicSpirit", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetMagicSpirit indicates an expected call of SetMagicSpirit.
func (mr *MockICacheMockRecorder) SetMagicSpirit(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMagicSpirit", reflect.TypeOf((*MockICache)(nil).SetMagicSpirit), arg0, arg1)
}

// SetMagicSpiritPond mocks base method.
func (m *MockICache) SetMagicSpiritPond(arg0 context.Context, arg1, arg2 uint32, arg3 []*mysql.MagicSpiritPond) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetMagicSpiritPond", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetMagicSpiritPond indicates an expected call of SetMagicSpiritPond.
func (mr *MockICacheMockRecorder) SetMagicSpiritPond(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMagicSpiritPond", reflect.TypeOf((*MockICache)(nil).SetMagicSpiritPond), arg0, arg1, arg2, arg3)
}

// SetRealNameCheck mocks base method.
func (m *MockICache) SetRealNameCheck(arg0 context.Context, arg1 uint32, arg2 bool, arg3 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetRealNameCheck", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetRealNameCheck indicates an expected call of SetRealNameCheck.
func (mr *MockICacheMockRecorder) SetRealNameCheck(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetRealNameCheck", reflect.TypeOf((*MockICache)(nil).SetRealNameCheck), arg0, arg1, arg2, arg3)
}

// SetSendValid mocks base method.
func (m *MockICache) SetSendValid(arg0 context.Context, arg1 uint32, arg2 bool, arg3 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetSendValid", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetSendValid indicates an expected call of SetSendValid.
func (mr *MockICacheMockRecorder) SetSendValid(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetSendValid", reflect.TypeOf((*MockICache)(nil).SetSendValid), arg0, arg1, arg2, arg3)
}

// SetUserSendFlagWithExpire mocks base method.
func (m *MockICache) SetUserSendFlagWithExpire(arg0 context.Context, arg1, arg2 uint32, arg3 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserSendFlagWithExpire", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserSendFlagWithExpire indicates an expected call of SetUserSendFlagWithExpire.
func (mr *MockICacheMockRecorder) SetUserSendFlagWithExpire(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserSendFlagWithExpire", reflect.TypeOf((*MockICache)(nil).SetUserSendFlagWithExpire), arg0, arg1, arg2, arg3)
}

// SetWarning mocks base method.
func (m *MockICache) SetWarning(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetWarning", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetWarning indicates an expected call of SetWarning.
func (mr *MockICacheMockRecorder) SetWarning(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetWarning", reflect.TypeOf((*MockICache)(nil).SetWarning), arg0)
}

// Unlock mocks base method.
func (m *MockICache) Unlock(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Unlock", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Unlock indicates an expected call of Unlock.
func (mr *MockICacheMockRecorder) Unlock(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Unlock", reflect.TypeOf((*MockICache)(nil).Unlock), arg0, arg1)
}

// UpdateMagicSpiritVersion mocks base method.
func (m *MockICache) UpdateMagicSpiritVersion(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateMagicSpiritVersion", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateMagicSpiritVersion indicates an expected call of UpdateMagicSpiritVersion.
func (mr *MockICacheMockRecorder) UpdateMagicSpiritVersion(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateMagicSpiritVersion", reflect.TypeOf((*MockICache)(nil).UpdateMagicSpiritVersion), arg0)
}
