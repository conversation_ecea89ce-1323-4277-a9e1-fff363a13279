package cache

import (
	"time"
	"context"
)

// 简单的分部署锁

func gen<PERSON><PERSON><PERSON><PERSON>(key string) string {
	return "magic:lock:" + key
}

func (c *Cache) Lock(ctx context.Context, key string, ttl time.Duration) (bool, error) {
	return c.redisCli.SetNX(ctx, gen<PERSON><PERSON><PERSON><PERSON>(key), 1, ttl).Result()
}

func (c *Cache) Unlock(ctx context.Context, key string) error {
	return c.redisCli.Del(ctx, gen<PERSON><PERSON><PERSON><PERSON>(key)).Err()
}
