package cache

import (
    "context"
    redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
    "strconv"
)

type Cache struct {
	redisCli    redis.Cmdable
	popQueueSha string
}

func NewCache(ctx context.Context, cfg *redisConnect.RedisConfig) (*Cache, error) {
	client, err := redisConnect.NewClient(ctx, cfg)
	if err != nil {
		return nil, err
	}

	c := &Cache{
		redisCli: client,
	}

	return c, c.ScriptLoad(ctx)
}

func (c *Cache) Close() error {
	return c.redisCli.(redis.Client).Close()
}

func (c *Cache) GetRedisClient() redis.Cmdable {
    return c.redisCli
}

func genMagicSpiritKey() string {
	return "magic_spirit"
}

func genMagicSpiritKeyVersionKey() string {
	return "magic_spirit_version"
}

func genMagicSpiritPondKey() string {
	return "magic_spirit_pond"
}

func genMagicSpiritBlacklistKey() string {
	return "magic_spirit_blacklist"
}

func genMagicSpiritBlacklistInitFlagKey() string {
	return "magic_spirit_blacklist_init"
}

func genCommonConfKey() string {
	return "magic_spirit_common_conf"
}

func uint322String(id uint32) string {
	return strconv.FormatInt(int64(id), 10)
}
