package cache

//go:generate quicksilver-cli test interface ../cache
//go:generate mockgen -destination=../mocks/mock_cache.go -package=mocks golang.52tt.com/services/magic-spirit/internal/cache ICache

import (
	"context"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
	"strconv"
)

type Cache struct {
	redisCli    redis.Cmdable
	popQueueSha string
}

func NewCache(ctx context.Context, cfg *redisConnect.RedisConfig) (*Cache, error) {
	client, err := redisConnect.NewClient(ctx, cfg)
	if err != nil {
		return nil, err
	}

	c := &Cache{
		redisCli: client,
	}

	return c, c.ScriptLoad(ctx)
}

func (c *Cache) Close() error {
	return c.redisCli.(redis.Client).Close()
}

func (c *Cache) GetRedisClient() redis.Cmdable {
	return c.redisCli
}

func genMagicSpiritKey() string {
	return "magic_spirit_v2"
}

func genMagicSpiritKeyVersionKey() string {
	return "magic_spirit_version_v2"
}

func genMagicSpiritPondKey() string {
	return "magic_spirit_pond_v2"
}

func genMagicSpiritBlacklistKey() string {
	return "magic_spirit_blacklist"
}

func genMagicSpiritBlacklistInitFlagKey() string {
	return "magic_spirit_blacklist_init"
}

func genCommonConfKey() string {
	return "magic_spirit_common_conf"
}

func uint322String(id uint32) string {
	return strconv.FormatInt(int64(id), 10)
}
