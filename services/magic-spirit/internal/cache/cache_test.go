package cache

import (
	"context"
	"fmt"
	"github.com/alicebob/miniredis/v2"
	"golang.52tt.com/pkg/log"
	redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
	pb "golang.52tt.com/protocol/services/magic-spirit"
	"golang.52tt.com/services/magic-spirit/internal/mysql"
	"strconv"
	"testing"
	"time"
)

var cacheCli *Cache

func init() {
	log.SetLevel(log.DebugLevel)
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}

	// 连接mock的redis server
	port, _ := strconv.ParseInt(s.Port(), 10, 32)
	redisClient, _ := redisConnect.NewClient(context.Background(), &redisConnect.RedisConfig{
		Host: s.Host(),
		Port: uint32(port),
	})

	cacheCli = &Cache{redisCli: redisClient}
}

func TestHGet(t *testing.T) {
	re, err := cacheCli.redisCli.Del(context.Background(), "666666").Result()
	fmt.Printf("%+v, %+v", re, err)

}

func TestCache_GetMagicSpiritById(t *testing.T) {
	rs, b, err := cacheCli.GetMagicSpiritById(context.Background(), 3)
	if err != nil {
		t.Error(err)
	}

	fmt.Printf("%v, %+v", b, rs)
}

func TestCache_GetMagicSpirit(t *testing.T) {
	t.Log(cacheCli.redisCli.HGetAll(context.Background(), "testgetalll").Result())
	//t.Log(cache.GetMagicSpirit(context.Background()))
}

func TestCache_IsBlacklist(t *testing.T) {
	t.Log(cacheCli.IsBlacklist(context.Background(), 10086))
}

func TestCache_SetFusing(t *testing.T) {
	t.Run("SetFusing", func(t *testing.T) {
		err := cacheCli.SetFusing(context.Background())
		if err != nil {
			t.Errorf("TestCache_SetFusing SetFusing fail. err:%v", err)
		}
	})

	t.Run("CheckIfFusing", func(t *testing.T) {
		b, err := cacheCli.CheckIfFusing(context.Background())
		if err != nil || !b {
			t.Errorf("TestCache_SetFusing SetFusing fail. err:%v b:%v", err, b)
		}
	})

	t.Run("DelFusing", func(t *testing.T) {
		err := cacheCli.DelFusing(context.Background())
		if err != nil {
			t.Errorf("TestCache_SetFusing DelFusing fail. err:%v", err)
		}
	})

	t.Run("CheckIfFusing", func(t *testing.T) {
		b, err := cacheCli.CheckIfFusing(context.Background())
		if err != nil || b {
			t.Errorf("TestCache_SetFusing SetFusing fail. err:%v b:%v", err, b)
		}
	})
}

func TestCache_SetWarning(t *testing.T) {
	t.Run("SetWarning", func(t *testing.T) {
		err := cacheCli.SetWarning(context.Background())
		if err != nil {
			t.Errorf("TestCache_SetWarning SetWarning fail. err:%v", err)
		}
	})

	t.Run("CheckIfWarning", func(t *testing.T) {
		b, err := cacheCli.CheckIfWarning(context.Background())
		if err != nil || !b {
			t.Errorf("TestCache_SetWarning CheckIfWarning fail. err:%v b:%v", err, b)
		}
	})

	t.Run("DelWarning", func(t *testing.T) {
		err := cacheCli.DelWarning(context.Background())
		if err != nil {
			t.Errorf("TestCache_SetWarning DelWarning fail. err:%v", err)
		}
	})

	t.Run("CheckIfWarning", func(t *testing.T) {
		b, err := cacheCli.CheckIfWarning(context.Background())
		if err != nil || b {
			t.Errorf("TestCache_SetWarning CheckIfWarning fail. err:%v b:%v", err, b)
		}
	})
}

func TestCache_IncrHourProfit(t *testing.T) {
	t.Run("GetHourProfit", func(t *testing.T) {
		p, err := cacheCli.GetHourProfit(context.Background(), time.Now().Hour())
		if err != nil || p != 0 {
			t.Errorf("TestCache_GetHourProfit GetHourProfit fail. err:%v", err)
		}
	})

	t.Run("IncrHourProfit", func(t *testing.T) {
		_, err := cacheCli.IncrHourProfit(context.Background(), time.Now().Hour(), 1)
		if err != nil {
			t.Errorf("TestCache_IncrHourProfit IncrHourProfit fail. err:%v", err)
		}
	})

	t.Run("GetHourProfit", func(t *testing.T) {
		p, err := cacheCli.GetHourProfit(context.Background(), time.Now().Hour())
		if err != nil || p == 0 {
			t.Errorf("TestCache_GetHourProfit GetHourProfit fail. err:%v", err)
		}
	})
}

func TestCache_AddUnpackOrderList(t *testing.T) {
	cid := uint32(1)
	uid := uint32(1)
	orderId := "test"

	t.Run("AddUnpackOrderList", func(t *testing.T) {
		err := cacheCli.AddUnpackOrderList(context.Background(), cid, uid, []*pb.UnpackGiftInfo{
			{ItemOrderId: orderId, EndTs: uint32(time.Now().Add(-time.Hour).Unix())},
		}, 0)
		if err != nil {
			t.Errorf("TestCache_AddUnpackOrderList AddUnpackOrderList fail. err:%v", err)
		}
	})

	t.Run("GetUnpackOrderInfo", func(t *testing.T) {
		_, exist, err := cacheCli.GetUnpackOrderInfo(context.Background(), orderId)
		if err != nil || !exist {
			t.Errorf("TestCache_AddUnpackOrderList AddUnpackOrderList fail. err:%v", err)
		}
	})

	t.Run("BatchGetUnpackOrderInfo", func(t *testing.T) {
		_, err := cacheCli.BatchGetUnpackOrderInfo(context.Background(), []string{orderId})
		if err != nil {
			t.Errorf("TestCache_AddUnpackOrderList BatchGetUnpackOrderInfo fail. err:%v", err)
		}
	})

	t.Run("GetChannelUnpackOrderIdList", func(t *testing.T) {
		_, err := cacheCli.GetChannelUnpackOrderIdList(context.Background(), cid, 0, 2)
		if err != nil {
			t.Errorf("TestCache_AddUnpackOrderList GetChannelUnpackOrderIdList fail. err:%v", err)
		}
	})

	t.Run("GetUserUnpackOrderIdList", func(t *testing.T) {
		_, err := cacheCli.GetUserUnpackOrderIdList(context.Background(), uid, cid, 0, 2)
		if err != nil {
			t.Errorf("TestCache_AddUnpackOrderList GetUserUnpackOrderIdList fail. err:%v", err)
		}
	})

	t.Run("ScriptLoad", func(t *testing.T) {
		err := cacheCli.ScriptLoad(context.Background())
		if err != nil {
			t.Errorf("TestCache_AddUnpackOrderList ScriptLoad fail. err:%v", err)
		}
	})

	t.Run("PopExpireUnpackOrderId", func(t *testing.T) {
		_, exist, err := cacheCli.PopExpireUnpackOrderId(context.Background())
		if err != nil || !exist {
			t.Errorf("TestCache_AddUnpackOrderList PopExpireUnpackOrderId fail. err:%v", err)
		}
	})

	t.Run("RemoveUnpackOrder", func(t *testing.T) {
		err := cacheCli.RemoveUnpackOrder(context.Background(), uid, cid, orderId)
		if err != nil {
			t.Errorf("TestCache_AddUnpackOrderList RemoveUnpackOrder fail. err:%v", err)
		}
	})
}

func TestCache_SetCommonConf(t *testing.T) {
	confId := uint32(1)
	t.Run("SetCommonConf", func(t *testing.T) {
		err := cacheCli.SetCommonConf(context.Background(), []*mysql.MagicSpiritCommonConf{
			{ConfId: confId},
		})
		if err != nil {
			t.Errorf("TestCache_SetCommonConf SetCommonConf fail. err:%v", err)
		}
	})

	t.Run("GetCommonConf", func(t *testing.T) {
		_, err := cacheCli.GetCommonConf(context.Background())
		if err != nil {
			t.Errorf("TestCache_SetCommonConf GetCommonConf fail. err:%v", err)
		}
	})

	t.Run("SetCommonConf", func(t *testing.T) {
		err := cacheCli.DelCommonConf(context.Background())
		if err != nil {
			t.Errorf("TestCache_SetCommonConf DelCommonConf fail. err:%v", err)
		}
	})
}

func TestCache_SetBlacklist(t *testing.T) {
	cid := uint32(1)
	t.Run("InitBlacklist", func(t *testing.T) {
		err := cacheCli.InitBlacklist(context.Background(), []uint32{cid})
		if err != nil {
			t.Errorf("TestCache_SetBlacklist SetBlacklist fail. err:%v", err)
		}
	})

	t.Run("IsBlacklist", func(t *testing.T) {
		_, err := cacheCli.IsBlacklist(context.Background(), cid)
		if err != nil {
			t.Errorf("TestCache_SetBlacklist IsBlacklist fail. err:%v", err)
		}
	})

	t.Run("DelBlacklist", func(t *testing.T) {
		err := cacheCli.DelBlacklist(context.Background(), []uint32{cid})
		if err != nil {
			t.Errorf("TestCache_SetBlacklist DelBlacklist fail. err:%v", err)
		}
	})

	t.Run("ExistsBlacklist", func(t *testing.T) {
		_, err := cacheCli.ExistsBlacklist(context.Background())
		if err != nil {
			t.Errorf("TestCache_SetBlacklist ExistsBlacklist fail. err:%v", err)
		}
	})
}

func TestCache_IncrCombCnt(t *testing.T) {
	cid := uint32(1)
	uid := uint32(1)

	t.Run("GetCombCnt", func(t *testing.T) {
		_, err := cacheCli.GetCombCnt(context.Background(), cid, uid, 1)
		if err != nil {
			t.Errorf("TestCache_IncrCombCnt GetCombCnt fail. err:%v", err)
		}
	})

	t.Run("IncrCombCnt", func(t *testing.T) {
		_, err := cacheCli.IncrCombCnt(context.Background(), cid, uid, 1, 1, 0)
		if err != nil {
			t.Errorf("TestCache_IncrCombCnt IncrCombCnt fail. err:%v", err)
		}
	})

	t.Run("GetCombCnt", func(t *testing.T) {
		_, err := cacheCli.GetCombCnt(context.Background(), cid, uid, 1)
		if err != nil {
			t.Errorf("TestCache_IncrCombCnt GetCombCnt fail. err:%v", err)
		}
	})
}

func TestCache_SetMagicSpirit(t *testing.T) {
	t.Run("SetMagicSpirit", func(t *testing.T) {
		err := cacheCli.SetMagicSpirit(context.Background(), []*mysql.MagicSpirit{{MagicSpiritId: 1}})
		if err != nil {
			t.Errorf("TestCache_SetMagicSpirit SetMagicSpirit fail. err:%v", err)
		}
	})

	t.Run("GetMagicSpirit", func(t *testing.T) {
		_, err := cacheCli.GetMagicSpirit(context.Background())
		if err != nil {
			t.Errorf("TestCache_SetMagicSpirit GetMagicSpirit fail. err:%v", err)
		}
	})

	t.Run("GetMagicSpiritById", func(t *testing.T) {
		_, exist, err := cacheCli.GetMagicSpiritById(context.Background(), 1)
		if err != nil || !exist {
			t.Errorf("TestCache_SetMagicSpirit GetMagicSpiritById fail. err:%v exist:%v", err, exist)
		}
	})

	t.Run("DelMagicSpiritCache", func(t *testing.T) {
		err := cacheCli.DelMagicSpiritCache(context.Background())
		if err != nil {
			t.Errorf("TestCache_SetMagicSpirit DelMagicSpiritCache fail. err:%v", err)
		}
	})

	t.Run("ExpireMagicSpirit", func(t *testing.T) {
		err := cacheCli.ExpireMagicSpirit(context.Background())
		if err != nil {
			t.Errorf("TestCache_SetMagicSpirit ExpireMagicSpirit fail. err:%v", err)
		}
	})
}

func TestCache_UpdateMagicSpiritVersion(t *testing.T) {
	t.Run("UpdateMagicSpiritVersion", func(t *testing.T) {
		err := cacheCli.UpdateMagicSpiritVersion(context.Background())
		if err != nil {
			t.Errorf("TestCache_UpdateMagicSpiritVersion UpdateMagicSpiritVersion fail. err:%v", err)
		}
	})

	t.Run("GetUpdateMagicSpiritVersion", func(t *testing.T) {
		_, err := cacheCli.GetUpdateMagicSpiritVersion(context.Background())
		if err != nil {
			t.Errorf("TestCache_UpdateMagicSpiritVersion GetUpdateMagicSpiritVersion fail. err:%v", err)
		}
	})
}

func TestCache_SetSendValid(t *testing.T) {
	t.Run("GetSendValid", func(t *testing.T) {
		_, _, err := cacheCli.GetSendValid(context.Background(), 1)
		if err != nil {
			t.Errorf("TestCache_SetSendValid GetSendValid fail. err:%v", err)
		}
	})

	t.Run("SetSendValid", func(t *testing.T) {
		err := cacheCli.SetSendValid(context.Background(), 1, true, 0)
		if err != nil {
			t.Errorf("TestCache_SetSendValid SetSendValid fail. err:%v", err)
		}
	})

	t.Run("GetSendValid", func(t *testing.T) {
		_, _, err := cacheCli.GetSendValid(context.Background(), 1)
		if err != nil {
			t.Errorf("TestCache_SetSendValid GetSendValid fail. err:%v", err)
		}
	})
}

func TestCache_SetRealNameCheck(t *testing.T) {
	t.Run("GetRealNameCheck", func(t *testing.T) {
		_, _, err := cacheCli.GetRealNameCheck(context.Background(), 1)
		if err != nil {
			t.Errorf("TestCache_SetRealNameCheck GetRealNameCheck fail. err:%v", err)
		}
	})

	t.Run("SetRealNameCheck", func(t *testing.T) {
		err := cacheCli.SetRealNameCheck(context.Background(), 1, true, 0)
		if err != nil {
			t.Errorf("TestCache_SetRealNameCheck SetRealNameCheck fail. err:%v", err)
		}
	})

	t.Run("GetRealNameCheck", func(t *testing.T) {
		_, _, err := cacheCli.GetRealNameCheck(context.Background(), 1)
		if err != nil {
			t.Errorf("TestCache_SetRealNameCheck GetRealNameCheck fail. err:%v", err)
		}
	})
}

func TestCache_IncrSendDailyPrice(t *testing.T) {
	uid := uint32(1)

	t.Run("IncrSendDailyPrice", func(t *testing.T) {
		_, err := cacheCli.IncrSendDailyPrice(context.Background(), uid, 1, 1, 0)
		if err != nil {
			t.Errorf("TestCache_IncrSendDailyPrice IncrSendDailyPrice fail. err:%v", err)
		}
	})

	t.Run("GetSendDailyPrice", func(t *testing.T) {
		_, err := cacheCli.GetSendDailyPrice(context.Background(), uid, 1)
		if err != nil {
			t.Errorf("TestCache_IncrSendDailyPrice GetSendDailyPrice fail. err:%v", err)
		}
	})
}

func TestCache_IncrSendToOtherDailyPrice(t *testing.T) {
	uid := uint32(1)

	t.Run("IncrSendDailyPrice", func(t *testing.T) {
		err := cacheCli.IncrSendToOtherDailyPrice(context.Background(), uid, 1, map[uint32]uint32{1: 1}, 0)
		if err != nil {
			t.Errorf("TestCache_IncrSendToOtherDailyPrice IncrSendToOtherDailyPrice fail. err:%v", err)
		}
	})

	t.Run("GetSendToOtherDailyPriceList", func(t *testing.T) {
		_, err := cacheCli.GetSendToOtherDailyPriceList(context.Background(), uid, 1, []uint32{1})
		if err != nil {
			t.Errorf("TestCache_IncrSendToOtherDailyPrice GetSendToOtherDailyPriceList fail. err:%v", err)
		}
	})
}

func TestCache_SetMagicSpiritPond(t *testing.T) {
	t.Run("SetMagicSpiritPond", func(t *testing.T) {
		err := cacheCli.SetMagicSpiritPond(context.Background(), 1, []*mysql.MagicSpiritPond{
			{MagicSpiritId: 1},
		})
		if err != nil {
			t.Errorf("TestCache_SetMagicSpiritPond SetMagicSpiritPond fail. err:%v", err)
		}
	})

	t.Run("GetMagicSpiritPond", func(t *testing.T) {
		_, _, err := cacheCli.GetMagicSpiritPond(context.Background(), 1)
		if err != nil {
			t.Errorf("TestCache_SetMagicSpiritPond GetMagicSpiritPond fail. err:%v", err)
		}
	})

	t.Run("ExpireMagicSpiritPond", func(t *testing.T) {
		err := cacheCli.ExpireMagicSpiritPond(context.Background())
		if err != nil {
			t.Errorf("TestCache_SetMagicSpiritPond ExpireMagicSpiritPond fail. err:%v", err)
		}
	})
}

func TestCache_DelMagicSpiritPond(t *testing.T) {
	t.Log(cacheCli.DelMagicSpiritPond(context.Background()))
}

func TestCache_SetMagicPondVersion(t *testing.T) {
	t.Log(cacheCli.SetMagicPondVersion(context.Background(), time.Now().Unix()))
}

func TestCache_GetMagicPondVersion(t *testing.T) {
	t.Log(cacheCli.GetMagicPondVersion(context.Background()))
}

func TestCache_SetUserSendFlagWithExpire(t *testing.T) {
	t.Log(cacheCli.SetUserSendFlagWithExpire(context.Background(), 1, 1, 0))
}

func TestCache_BatGetUserSendFlag(t *testing.T) {
	t.Log(cacheCli.BatGetUserSendFlag(context.Background(), 1))
}
