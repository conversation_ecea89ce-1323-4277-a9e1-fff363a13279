package cache

import(
	context "context"
	time "time"
	redis "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	mysql "golang.52tt.com/services/magic-spirit/internal/mysql"
	pb "golang.52tt.com/protocol/services/magic-spirit"
)

type ICache interface {
	AddUnpackOrderList(ctx context.Context, channelId, sendUid uint32, list []*pb.UnpackGiftInfo, expire time.Duration) error
	BatGetUserSendFlag(ctx context.Context, uid uint32, magicIds ...uint32) (map[uint32]bool,error)
	BatchGetUnpackOrderInfo(ctx context.Context, orderIdList []string) (map[string]*pb.UnpackGiftInfo,error)
	CheckIfFusing(ctx context.Context) (bool,error)
	CheckIfWarning(ctx context.Context) (bool,error)
	Close() error
	DelBlacklist(ctx context.Context, channelIds []uint32) error
	DelCommonConf(ctx context.Context) error
	DelFusing(ctx context.Context) error
	DelMagicSpiritCache(ctx context.Context) error
	DelMagicSpiritPond(ctx context.Context) error
	DelWarning(ctx context.Context) error
	ExistsBlacklist(ctx context.Context) (bool,error)
	ExpireMagicSpirit(ctx context.Context) error
	ExpireMagicSpiritPond(ctx context.Context) error
	GetChannelUnpackOrderIdList(ctx context.Context, channelId, begin, limit uint32) ([]string,error)
	GetCombCnt(ctx context.Context, cid, uid, magicId uint32) (uint32,error)
	GetCommonConf(ctx context.Context) ([]*mysql.MagicSpiritCommonConf,error)
	GetHourProfit(ctx context.Context, hour int) (int64,error)
	GetMagicPondVersion(ctx context.Context, isSpecial bool) (int64,error)
	GetMagicSpirit(ctx context.Context) ([]*mysql.MagicSpirit,error)
	GetMagicSpiritById(ctx context.Context, magicSpiritId uint32) (*mysql.MagicSpirit,bool,error)
	GetMagicSpiritPond(ctx context.Context, magicSpiritId uint32, pondType uint32) ([]*mysql.MagicSpiritPond,bool,error)
	GetRealNameCheck(ctx context.Context, uid uint32) (bool,bool,error)
	GetRedisClient() redis.Cmdable
	GetSendDailyPrice(ctx context.Context, uid, day uint32) (uint32,error)
	GetSendToOtherDailyPriceList(ctx context.Context, uid, day uint32, toUidList []uint32) ([]uint32,error)
	GetSendValid(ctx context.Context, uid uint32) (bool,bool,error)
	GetUnpackOrderInfo(ctx context.Context, orderId string) (*pb.UnpackGiftInfo,bool,error)
	GetUpdateMagicSpiritVersion(ctx context.Context) (uint32,error)
	GetUserSendFlag(ctx context.Context, uid uint32) (bool,error)
	GetUserUnpackOrderIdList(ctx context.Context, uid, channelId, begin, limit uint32) ([]string,error)
	IncrCombCnt(ctx context.Context, cid, uid, magicId, cnt uint32, expire time.Duration) (uint32,error)
	IncrHourProfit(ctx context.Context, hour int, profit int64) (int64,error)
	IncrSendDailyPrice(ctx context.Context, uid, day, price uint32, expire time.Duration) (uint32,error)
	IncrSendToOtherDailyPrice(ctx context.Context, uid, day uint32, mapToUid2Price map[uint32]uint32, expire time.Duration) error
	InitBlacklist(ctx context.Context, channelIds []uint32) error
	IsBlacklist(ctx context.Context, channelId uint32) (bool,error)
	Lock(ctx context.Context, key string, ttl time.Duration) (bool,error)
	PopExpireUnpackOrderId(ctx context.Context) (string,bool,error)
	RemoveUnpackOrder(ctx context.Context, uid, cid uint32, orderId string) error
	ScriptLoad(ctx context.Context) (err error)
	SetBlacklist(ctx context.Context, channelIds []uint32) error
	SetCommonConf(ctx context.Context, conf []*mysql.MagicSpiritCommonConf) error
	SetFusing(ctx context.Context) error
	SetMagicPondVersion(ctx context.Context, updateVersion int64, isSpecial bool) error
	SetMagicSpirit(ctx context.Context, data []*mysql.MagicSpirit) error
	SetMagicSpiritPond(ctx context.Context, magicSpiritId uint32, pondType uint32, pond []*mysql.MagicSpiritPond) error
	SetRealNameCheck(ctx context.Context, uid uint32, valid bool, expire time.Duration) error
	SetSendValid(ctx context.Context, uid uint32, valid bool, expire time.Duration) error
	SetUserSendFlagWithExpire(ctx context.Context, uid uint32, magicId uint32, expire time.Duration) error
	SetWarning(ctx context.Context) error
	Unlock(ctx context.Context, key string) error
	UpdateMagicSpiritVersion(ctx context.Context) error
}

