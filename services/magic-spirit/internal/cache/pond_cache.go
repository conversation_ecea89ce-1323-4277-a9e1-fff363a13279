package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/services/magic-spirit/internal/mysql"
)

const (
	PondExpireTime = 2 * time.Second
)

var magicPondUpdateVersionKey = "magic_spirit_pond_version"

func (c *Cache) SetMagicSpiritPond(ctx context.Context, magicSpiritId uint32, pond []*mysql.MagicSpiritPond) error {
	bytes, err := json.Marshal(pond)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetMagicSpiritPond, marshal error: %v", err)
		return err
	}
	err = c.redisCli.HSet(ctx, genMagicSpiritPondKey(), uint322String(magicSpiritId), string(bytes)).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "SetMagicSpiritCache cache error； %v", err)
		return err
	}

	return nil
}

func (c *Cache) GetMagicSpiritPond(ctx context.Context, magicSpiritId uint32) ([]*mysql.MagicSpiritPond, bool, error) {
	cache := make([]*mysql.MagicSpiritPond, 0, 8)

	rs, err := c.redisCli.HGet(ctx, genMagicSpiritPondKey(), strconv.FormatInt(int64(magicSpiritId), 10)).Result()
	if err != nil {
		if err == redis.Nil {
			return cache, false, nil
		}

		log.ErrorWithCtx(ctx, "GetMagicSpiritPond error: %v", err)
		return cache, false, err
	}

	err = json.Unmarshal([]byte(rs), &cache)
	if err != nil {
		return cache, false, err
	}

	return cache, true, nil
}

func (c *Cache) ExpireMagicSpiritPond(ctx context.Context) error {
	err := c.redisCli.Expire(ctx, genMagicSpiritPondKey(), PondExpireTime).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "ExpireMagicSpiritPond error: %v", err)
		return err
	}

	return nil
}

func (c *Cache) DelMagicSpiritPond(ctx context.Context) error {
	err := c.redisCli.Del(ctx, genMagicSpiritPondKey()).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "DelMagicSpiritPond error: %v", err)
		return err
	}
	return nil
}

func (c *Cache) SetMagicPondVersion(ctx context.Context, updateVersion int64) error {

	err := c.redisCli.Set(ctx, magicPondUpdateVersionKey, fmt.Sprintf("%d", updateVersion), 0).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "cache SetMagicPondVersion err:%v", err)
		return err
	}
	return nil
}

func (c *Cache) GetMagicPondVersion(ctx context.Context) (int64, error) {
	val, err := c.redisCli.Get(ctx, magicPondUpdateVersionKey).Int64()
	if err != nil {
		if err == redis.Nil {
			return 0, nil
		}
		log.ErrorWithCtx(ctx, "cache SetMagicPondVersion err:%v", err)
		return 0, err
	}
	return val, nil
}
