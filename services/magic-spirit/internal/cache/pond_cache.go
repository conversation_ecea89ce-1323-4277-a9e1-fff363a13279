package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/services/magic-spirit/internal/mysql"
)

const (
	PondExpireTime                   = 2 * time.Second
	magicPondUpdateVersionKey        = "magic_spirit_pond_version_v2"
	magicPondUpdateVersionKeySpecial = "magic_spirit_pond_version_special_v2"
)

func genPondFiled(magicSpiritId uint32, pondType uint32) string {
	return fmt.Sprintf("%d_%d", magicSpiritId, pondType)
}

func (c *Cache) SetMagicSpiritPond(ctx context.Context, magicSpiritId uint32, pondType uint32, pond []*mysql.MagicSpiritPond) error {
	bytes, err := json.Marshal(pond)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetMagicSpiritPond, marshal error: %v", err)
		return err
	}
	err = c.redisCli.HSet(ctx, genMagicSpiritPondKey(), genPondFiled(magicSpiritId, pondType), string(bytes)).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "SetMagicSpiritCache cache error； %v", err)
		return err
	}

	return nil
}

func (c *Cache) GetMagicSpiritPond(ctx context.Context, magicSpiritId uint32, pondType uint32) ([]*mysql.MagicSpiritPond, bool, error) {
	cache := make([]*mysql.MagicSpiritPond, 0, 8)

	rs, err := c.redisCli.HGet(ctx, genMagicSpiritPondKey(), genPondFiled(magicSpiritId, pondType)).Result()
	if err != nil {
		if err == redis.Nil {
			return cache, false, nil
		}

		log.ErrorWithCtx(ctx, "GetMagicSpiritPond error: %v", err)
		return cache, false, err
	}

	err = json.Unmarshal([]byte(rs), &cache)
	if err != nil {
		return cache, false, err
	}

	return cache, true, nil
}

func (c *Cache) ExpireMagicSpiritPond(ctx context.Context) error {
	err := c.redisCli.Expire(ctx, genMagicSpiritPondKey(), PondExpireTime).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "ExpireMagicSpiritPond error: %v", err)
		return err
	}

	return nil
}

func (c *Cache) DelMagicSpiritPond(ctx context.Context) error {
	err := c.redisCli.Del(ctx, genMagicSpiritPondKey()).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "DelMagicSpiritPond error: %v", err)
		return err
	}
	return nil
}

func (c *Cache) SetMagicPondVersion(ctx context.Context, updateVersion int64, isSpecial bool) error {
	key := magicPondUpdateVersionKey
	if isSpecial {
		key = magicPondUpdateVersionKeySpecial
	}

	err := c.redisCli.Set(ctx, key, fmt.Sprintf("%d", updateVersion), 0).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "cache SetMagicPondVersion err:%v", err)
		return err
	}
	return nil
}

func (c *Cache) GetMagicPondVersion(ctx context.Context, isSpecial bool) (int64, error) {
	key := magicPondUpdateVersionKey
	if isSpecial {
		key = magicPondUpdateVersionKeySpecial
	}
	val, err := c.redisCli.Get(ctx, key).Int64()
	if err != nil {
		if err == redis.Nil {
			return 0, nil
		}
		log.ErrorWithCtx(ctx, "cache SetMagicPondVersion err:%v", err)
		return 0, err
	}
	return val, nil
}
