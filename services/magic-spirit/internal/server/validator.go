package server

import (
	"errors"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/magic-spirit"
)

func validateAddMagicSpiritReq(req *pb.AddMagicSpiritReq) error {
	if len(req.MagicSpirit) == 0 {
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "empty slice: magic")
	}
	for _, item := range req.GetMagicSpirit() {
		if item.Price == 0 {
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "魔法精灵价值不能为0")
		}
	}
	return nil
}

func validateUpdateMagicSpiritReq(req *pb.UpdateMagicSpiritReq) error {
	if len(req.GetMagicSpirit()) == 0 {
		return errors.New("empty slice: magic")
	}
	for _, item := range req.GetMagicSpirit() {
		if item.GetConf().GetPrice() == 0 {
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "魔法精灵价值不能为0")
		}
	}
	return nil
}
