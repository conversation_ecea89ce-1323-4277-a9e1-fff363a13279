package server

import (
	"errors"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/magic-spirit"
	"sort"
	"google.golang.org/grpc/codes"
)

func validateAddMagicSpiritReq(req *pb.AddMagicSpiritReq) error {
	if len(req.MagicSpirit) == 0 {
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "empty slice: magic")
	}
	for _, item := range req.GetMagicSpirit() {
		if item.Price == 0 {
			return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "幸运礼物的价值不能为0")
		}
		if len(item.GetMagicEffectTimeList()) == 0 {
			return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "幸运礼物的时间段不能为空")
		}

		if len(item.GetMagicEffectTimeList()) > 10 || len(item.GetSpecialPondConf().GetEffectTimeList()) > 10 {
			return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "间歇上下架时间段不能超过10个")
		}

		// 首先检查时间段内是否有时间冲突
		if checkIfTimeRangeConflict(item.GetMagicEffectTimeList()) {
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "礼物上下架时间段有冲突")
		}

		if checkIfTimeRangeConflict(item.GetSpecialPondConf().GetEffectTimeList()) {
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "特殊奖池时间段有冲突")
		}

		magicEffectTimeList := item.GetMagicEffectTimeList()
		specialPondTimeList := item.GetSpecialPondConf().GetEffectTimeList()

		// 检查特殊奖池时间段是否在普通奖池内
		if len(specialPondTimeList) > 0 {
			if !checkBInA(magicEffectTimeList, specialPondTimeList) {
				return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "特殊奖池时间段必须在普通奖池时间段内")
			}

			if item.GetActivityCfg().GetBeginTime() > 0 {
				tmpTimeList := append([]*pb.TimeRange{}, &pb.TimeRange{
					StartTime: item.GetActivityCfg().GetBeginTime(),
					EndTime:   item.GetActivityCfg().GetEndTime(),
				})
				tmpTimeList = append(tmpTimeList, specialPondTimeList...)
				checkIfTimeRangeConflict(tmpTimeList)
			}
		}
	}

	return nil
}

func validateUpdateMagicSpiritReq(req *pb.UpdateMagicSpiritReq) error {
	if len(req.GetMagicSpirit()) == 0 {
		return errors.New("empty slice: magic")
	}
	for _, item := range req.GetMagicSpirit() {
		if item.GetConf() == nil {
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "empty slice: magic")
		}
		if item.GetConf().GetPrice() == 0 {
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "幸运礼物的价值不能为0")
		}
		if len(item.GetConf().GetMagicEffectTimeList()) == 0 {
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "幸运礼物的时间段不能为空")
		}

		if len(item.GetConf().GetMagicEffectTimeList()) > 10 || len(item.GetConf().GetSpecialPondConf().GetEffectTimeList()) > 10 {
			return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "间歇上下架时间段不能超过10个")
		}

		// 首先检查时间段内是否有时间冲突
		if checkIfTimeRangeConflict(item.GetConf().GetMagicEffectTimeList()) {
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "礼物上下架时间段有冲突")
		}

		if checkIfTimeRangeConflict(item.GetConf().GetSpecialPondConf().GetEffectTimeList()) {
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "特殊奖池时间段有冲突")
		}

		magicEffectTimeList := item.GetConf().GetMagicEffectTimeList()
		specialPondTimeList := item.GetConf().GetSpecialPondConf().GetEffectTimeList()
		activityConf := item.GetConf().GetActivityCfg()

		// 检查特殊奖池时间段是否在普通奖池内
		if len(specialPondTimeList) > 0 {
			if !checkBInA(magicEffectTimeList, specialPondTimeList) {
				return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "特殊奖池时间段必须在普通奖池时间段内")
			}

			if activityConf.GetBeginTime() > 0 {
				tmpTimeList := append([]*pb.TimeRange{}, &pb.TimeRange{
					StartTime: activityConf.GetBeginTime(),
					EndTime:   activityConf.GetEndTime(),
				})
				tmpTimeList = append(tmpTimeList, specialPondTimeList...)
				checkIfTimeRangeConflict(tmpTimeList)
			}
		}
	}

	return nil
}

// 时间段冲突的检查
func checkIfTimeRangeConflict(timeRangeList []*pb.TimeRange) bool {
	if len(timeRangeList) == 0 {
		return false
	}
	// 先按开始时间排序
	sort.SliceStable(timeRangeList, func(i, j int) bool {
		return timeRangeList[i].StartTime < timeRangeList[j].StartTime
	})

	// 检查相邻时间段是否有重叠
	for i := 1; i < len(timeRangeList); i++ {
		if timeRangeList[i-1].EndTime >= timeRangeList[i].StartTime {
			return true // 存在冲突
		}
	}

	return false // 无冲突
}

// CheckBInA 检查B的所有时间段是否都在A的某个时间段内
func checkBInA(A, B []*pb.TimeRange) bool {
	// 1. 对A按Start排序
	sort.SliceStable(A, func(i, j int) bool {
		return A[i].StartTime < A[j].StartTime
	})

	// 2. 检查B中的每个时间段
	for _, b := range B {
		if !isIntervalCovered(b, A) {
			return false
		}
	}
	return true
}

// isIntervalCovered 检查单个时间段b是否被A的某个时间段包含
func isIntervalCovered(b *pb.TimeRange, A []*pb.TimeRange) bool {
	// 二分查找找到A中第一个Start <= b.Start的区间
	idx := sort.Search(len(A), func(i int) bool {
		return A[i].StartTime > b.StartTime
	})
	idx-- // 调整为最后一个A[i].Start <= b.Start的区间

	// 检查可能包含b的A区间
	if idx >= 0 && A[idx].EndTime >= b.EndTime {
		return true
	}

	// 检查下一个A区间（可能b.Start在A[idx]和A[idx+1]之间）
	if idx+1 < len(A) && A[idx+1].StartTime <= b.StartTime && A[idx+1].EndTime >= b.EndTime {
		return true
	}

	return false
}
