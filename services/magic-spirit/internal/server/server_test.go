package server

import (
	"context"
	"fmt"
	"github.com/golang/mock/gomock"
	pb "golang.52tt.com/protocol/services/magic-spirit"
	reconcileV2 "golang.52tt.com/protocol/services/reconcile-v2"
	"golang.52tt.com/protocol/services/unified_pay/cb"
	UnifiedPayCallback "golang.52tt.com/protocol/services/unified_pay/cb"
	"golang.52tt.com/services/magic-spirit/internal/manager"
	"golang.52tt.com/services/magic-spirit/internal/mocks"
	"golang.52tt.com/services/magic-spirit/internal/mysql"
	"strconv"
	"strings"
	"testing"
	"time"
)

func TestMagicSpirit_Separate(t *testing.T) {
	splitElements := strings.Split("", ",")
	channelTypeList := make([]uint32, 0, len(splitElements))
	fmt.Println(channelTypeList, splitElements[0], len(splitElements))
	for _, element := range splitElements {
		channelType, err := strconv.ParseUint(element, 10, 32)
		if err != nil {
			fmt.Println("parse channel type fail. err:", err)
			continue
		}
		channelTypeList = append(channelTypeList, uint32(channelType))
	}
	fmt.Println(channelTypeList)
}

func TestMagicSpirit_Join(t *testing.T) {
	fmt.Println(strings.Join(nil, ","))
}

func TestMagicSpirit_AddMagicSpirit(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMagicSpiritMgr(ctl)

	s := &MagicSpirit{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().AddMagicSpirit(gomock.Any(), gomock.Any()).Return(uint32(1), nil),
	)

	t.Run("TestMagicSpirit_AddMagicSpirit", func(t *testing.T) {
		_, err := s.AddMagicSpirit(context.Background(), &pb.AddMagicSpiritReq{
			MagicSpirit: []*pb.MagicSpirit{
				{MagicSpiritId: 1, Price: 100, MagicEffectTimeList: []*pb.TimeRange{
					{StartTime: time.Now().Unix(), EndTime: time.Now().Add(time.Hour).Unix()},
				}},
			},
		})
		if err != nil {
			t.Errorf("AddMagicSpirit fail. err:%v", err)
		}
	})
}

func TestMagicSpirit_DelMagicSpirit(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMagicSpiritMgr(ctl)

	s := &MagicSpirit{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().DelMagicSpirit(gomock.Any(), gomock.Any()).Return(nil),
	)

	t.Run("TestMagicSpirit_DelMagicSpirit", func(t *testing.T) {
		_, err := s.DelMagicSpirit(context.Background(), &pb.DelMagicSpiritReq{
			MagicSpiritIds: []uint32{1},
		})
		if err != nil {
			t.Errorf("DelMagicSpirit fail. err:%v", err)
		}
	})
}

func TestMagicSpirit_UpdateMagicSpirit(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMagicSpiritMgr(ctl)

	s := &MagicSpirit{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().SetMagicSpiritTemporary(gomock.Any(), gomock.Any()).Return(nil),
	)

	t.Run("TestMagicSpirit_UpdateMagicSpirit", func(t *testing.T) {
		_, err := s.UpdateMagicSpirit(context.Background(), &pb.UpdateMagicSpiritReq{
			MagicSpirit: []*pb.MagicSpiritTmp{
				{
					EffectTime: uint32(time.Now().Unix()),
					Conf: &pb.MagicSpirit{
						MagicSpiritId: 1,
						Price:         100,
						MagicEffectTimeList: []*pb.TimeRange{
							{StartTime: time.Now().Unix(), EndTime: time.Now().Add(time.Hour).Unix()},
						},
					},
				},
			},
		})
		if err != nil {
			t.Errorf("UpdateMagicSpirit fail. err:%v", err)
		}
	})
}

func TestMagicSpirit_GetMagicSpirit(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMagicSpiritMgr(ctl)

	s := &MagicSpirit{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().GetMagicSpirit(gomock.Any()).Return([]*mysql.MagicSpirit{{MagicSpiritId: 1}}, nil),
	)

	t.Run("TestMagicSpirit_GetMagicSpirit", func(t *testing.T) {
		_, err := s.GetMagicSpirit(context.Background(), &pb.GetMagicSpiritReq{})
		if err != nil {
			t.Errorf("GetMagicSpirit fail. err:%v", err)
		}
	})
}

func TestMagicSpirit_AddMagicSpiritPond(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMagicSpiritMgr(ctl)

	s := &MagicSpirit{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().AddMagicSpiritPond(gomock.Any(), gomock.Any(), gomock.Any()).Return([]uint32{1}, nil),
	)

	t.Run("TestMagicSpirit_AddMagicSpiritPond", func(t *testing.T) {
		_, err := s.AddMagicSpiritPond(context.Background(), &pb.AddMagicSpiritPondReq{
			MagicSpiritPondItems: []*pb.MagicSpiritPondItem{
				{MagicSpiritId: 1},
			},
		})
		if err != nil {
			t.Errorf("AddMagicSpiritPond fail. err:%v", err)
		}
	})
}

func TestMagicSpirit_GetMagicSpiritPond(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMagicSpiritMgr(ctl)

	s := &MagicSpirit{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().GetMagicSpiritPond(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*mysql.MagicSpiritPond{{MagicSpiritId: 1}}, nil).AnyTimes(),
	)

	t.Run("TestMagicSpirit_GetMagicSpiritPond", func(t *testing.T) {
		_, err := s.GetMagicSpiritPond(context.Background(), &pb.GetMagicSpiritPondReq{
			MagicSpiritId: 1,
		})
		if err != nil {
			t.Errorf("GetMagicSpiritPond fail. err:%v", err)
		}
	})
}

func TestMagicSpirit_SetCommonConf(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMagicSpiritMgr(ctl)

	s := &MagicSpirit{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().SetCommonConf(gomock.Any(), gomock.Any()).Return(nil),
	)

	t.Run("TestMagicSpirit_SetCommonConf", func(t *testing.T) {
		_, err := s.SetCommonConf(context.Background(), &pb.SetCommonConfReq{
			CommonConf: []*pb.CommonConf{{ConfType: 1}},
		})
		if err != nil {
			t.Errorf("SetCommonConf fail. err:%v", err)
		}
	})
}

func TestMagicSpirit_GetCommonConf(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMagicSpiritMgr(ctl)

	s := &MagicSpirit{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().GetCommonConf(gomock.Any()).Return([]*mysql.MagicSpiritCommonConf{
			{ConfId: 1, ValueType: manager.CONF_VAL_TYPE_INT, Value: "100"},
		}, nil),
	)

	t.Run("TestMagicSpirit_GetCommonConf", func(t *testing.T) {
		_, err := s.GetCommonConf(context.Background(), &pb.GetCommonConfReq{})
		if err != nil {
			t.Errorf("GetCommonConf fail. err:%v", err)
		}
	})
}

func TestMagicSpirit_AddMagicSpiritBlacklist(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMagicSpiritMgr(ctl)

	s := &MagicSpirit{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().AddMagicSpiritBlacklist(gomock.Any(), gomock.Any()).Return([]uint32{1}, nil),
	)

	t.Run("TestMagicSpirit_AddMagicSpiritBlacklist", func(t *testing.T) {
		_, err := s.AddMagicSpiritBlacklist(context.Background(), &pb.AddMagicSpiritBlacklistReq{
			Blacklist: []*pb.MagicSpiritBlacklist{
				{BlacklistId: 1},
			},
		})
		if err != nil {
			t.Errorf("AddMagicSpiritBlacklist fail. err:%v", err)
		}
	})
}

func TestMagicSpirit_GetMagicSpiritBlacklist(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMagicSpiritMgr(ctl)

	s := &MagicSpirit{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().GetMagicSpiritBlacklist(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(0), []*mysql.MagicSpiritBlacklist{
			{ChannelId: 1},
		}, nil),
	)

	t.Run("TestMagicSpirit_GetMagicSpiritBlacklist", func(t *testing.T) {
		_, err := s.GetMagicSpiritBlacklist(context.Background(), &pb.GetMagicSpiritBlackListReq{
			ChannelId: 1, PageSize: 1,
		})
		if err != nil {
			t.Errorf("GetMagicSpiritBlacklist fail. err:%v", err)
		}
	})
}

func TestMagicSpirit_DelMagicSpiritBlacklist(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMagicSpiritMgr(ctl)

	s := &MagicSpirit{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().DelMagicSpiritBlacklist(gomock.Any(), gomock.Any()).Return(nil),
	)

	t.Run("TestMagicSpirit_DelMagicSpiritBlacklist", func(t *testing.T) {
		_, err := s.DelMagicSpiritBlacklist(context.Background(), &pb.DelMagicSpiritBlacklistReq{
			ChannelIds: []uint32{1},
		})
		if err != nil {
			t.Errorf("DelMagicSpiritBlacklist fail. err:%v", err)
		}
	})
}

func TestMagicSpirit_GetAwardOrderIds(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMagicSpiritMgr(ctl)

	s := &MagicSpirit{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().GetMagicAwardOrderIdList(gomock.Any(), gomock.Any(), gomock.Any()).Return([]string{}, nil),
	)

	t.Run("TestMagicSpirit_GetAwardOrderIds", func(t *testing.T) {
		_, err := s.GetAwardOrderIds(context.Background(), &reconcileV2.TimeRangeReq{
			BeginTime: time.Now().Unix() - 60, EndTime: time.Now().Unix(),
		})
		if err != nil {
			t.Errorf("GetAwardOrderIds fail. err:%v", err)
		}
	})
}

func TestMagicSpirit_GetAwardTotalCount(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMagicSpiritMgr(ctl)

	s := &MagicSpirit{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().GetMagicAwardTotal(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(0), uint64(0), uint64(0), nil),
	)

	t.Run("TestMagicSpirit_GetAwardTotalCount", func(t *testing.T) {
		_, err := s.GetAwardTotalCount(context.Background(), &reconcileV2.TimeRangeReq{
			BeginTime: time.Now().Unix() - 60, EndTime: time.Now().Unix(),
		})
		if err != nil {
			t.Errorf("GetAwardTotalCount fail. err:%v", err)
		}
	})
}

func TestMagicSpirit_GetConsumeOrderIds(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMagicSpiritMgr(ctl)

	s := &MagicSpirit{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().GetMagicConsumeOrderIdList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]string{}, nil),
	)

	t.Run("TestMagicSpirit_GetConsumeOrderIds", func(t *testing.T) {
		_, err := s.GetConsumeOrderIds(context.Background(), &reconcileV2.TimeRangeReq{
			BeginTime: time.Now().Unix() - 60, EndTime: time.Now().Unix(),
		})
		if err != nil {
			t.Errorf("GetConsumeOrderIds fail. err:%v", err)
		}
	})
}

func TestMagicSpirit_GetConsumeTotalCount(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMagicSpiritMgr(ctl)

	s := &MagicSpirit{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().GetMagicOrderTotal(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(0), uint64(0), uint64(0), nil),
	)

	t.Run("TestMagicSpirit_GetConsumeTotalCount", func(t *testing.T) {
		_, err := s.GetConsumeTotalCount(context.Background(), &reconcileV2.TimeRangeReq{
			BeginTime: time.Now().Unix() - 60, EndTime: time.Now().Unix(),
		})
		if err != nil {
			t.Errorf("GetConsumeTotalCount fail. err:%v", err)
		}
	})
}

func TestMagicSpirit_GetChannelAllUnpackGift(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMagicSpiritMgr(ctl)

	s := &MagicSpirit{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().GetChannelAllUnpackGift(gomock.Any(), gomock.Any(), gomock.Any()).Return(&pb.GetChannelAllUnpackGiftResp{}, nil),
	)

	t.Run("TestMagicSpirit_GetChannelAllUnpackGift", func(t *testing.T) {
		_, err := s.GetChannelAllUnpackGift(context.Background(), &pb.GetChannelAllUnpackGiftReq{
			Uid: 1, ChannelId: 1,
		})
		if err != nil {
			t.Errorf("GetChannelAllUnpackGift fail. err:%v", err)
		}
	})
}

func TestMagicSpirit_GetMagicSpiritAwardTotal(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMagicSpiritMgr(ctl)

	s := &MagicSpirit{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().GetMagicAwardTotal(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(0), uint64(0), uint64(0), nil),
	)

	t.Run("TestMagicSpirit_GetMagicSpiritAwardTotal", func(t *testing.T) {
		_, err := s.GetMagicSpiritAwardTotal(context.Background(), &pb.GetMagicSpiritAwardTotalReq{
			BeginTime: uint32(time.Now().Unix() - 60), EndTime: uint32(time.Now().Unix()),
		})
		if err != nil {
			t.Errorf("GetMagicSpiritAwardTotal fail. err:%v", err)
		}
	})
}

func TestMagicSpirit_GetMagicSpiritOrderTotal(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMagicSpiritMgr(ctl)

	s := &MagicSpirit{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().GetMagicOrderTotal(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(0), uint64(0), uint64(0), nil),
	)

	t.Run("TestMagicSpirit_GetMagicSpiritOrderTotal", func(t *testing.T) {
		_, err := s.GetMagicSpiritOrderTotal(context.Background(), &pb.GetMagicSpiritOrderTotalReq{
			BeginTime: uint32(time.Now().Unix() - 60), EndTime: uint32(time.Now().Unix()),
		})
		if err != nil {
			t.Errorf("GetMagicSpiritOrderTotal fail. err:%v", err)
		}
	})
}

func TestMagicSpirit_GetMagicSpiritForCli(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMagicSpiritMgr(ctl)

	s := &MagicSpirit{
		mgr: mockMgr,
	}

	gomock.InOrder(
		//mockMgr.EXPECT().GetMagicSpiritVersion(gomock.Any()).Return(uint32(1), nil),
		mockMgr.EXPECT().GetMagicSpiritWithCache(gomock.Any()).Return([]*mysql.MagicSpirit{
			{MagicSpiritId: 1, EffectEnd: uint32(time.Now().Unix() + 60)},
		}, nil),
		mockMgr.EXPECT().GetMagicSpiritPondWithCache(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*mysql.MagicSpiritPond{
			{MagicSpiritId: 1},
		}, nil),
	)

	t.Run("TestMagicSpirit_GetMagicSpiritForCli", func(t *testing.T) {
		_, err := s.GetMagicSpiritForCli(context.Background(), &pb.GetMagicSpiritForCliReq{})
		if err != nil {
			t.Errorf("GetMagicSpiritForCli fail. err:%v", err)
		}
	})
}

//func TestMagicSpirit_GetMagicSpiritUsable(t *testing.T) {
//	ctl := gomock.NewController(t)
//	defer ctl.Finish()
//
//	mockMgr := mocks.NewMockIMagicSpiritMgr(ctl)
//
//	s := &MagicSpirit{
//		mgr: mockMgr,
//	}
//
//	gomock.InOrder(
//		mockMgr.EXPECT().GetMagicSpiritUsable(gomock.Any(), gomock.Any()).Return(&pb.GetMagicSpiritUsableResp{}, nil),
//	)
//
//	t.Run("TestMagicSpirit_GetMagicSpiritUsable", func(t *testing.T) {
//		_, err := s.GetMagicSpiritUsable(context.Background(), &pb.GetMagicSpiritUsableReq{ChannelId: 1, Uid: 1})
//		if err != nil {
//			t.Errorf("GetMagicSpiritUsable fail. err:%v", err)
//		}
//	})
//}

func TestMagicSpirit_Notify(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMagicSpiritMgr(ctl)

	s := &MagicSpirit{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().Callback(gomock.Any(), gomock.Any()).Return(cb.Op_COMMIT, nil),
	)

	t.Run("TestMagicSpirit_Notify", func(t *testing.T) {
		_, err := s.Notify(context.Background(), &UnifiedPayCallback.PayNotify{OutTradeNo: "test"})
		if err != nil {
			t.Errorf("Notify fail. err:%v", err)
		}
	})
}

func TestMagicSpirit_SendMagicSpirit(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMagicSpiritMgr(ctl)

	s := &MagicSpirit{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().SendMagicSpirit(gomock.Any(), gomock.Any()).Return(&pb.SendMagicSpiritResp{}, nil),
	)

	t.Run("TestMagicSpirit_SendMagicSpirit", func(t *testing.T) {
		_, err := s.SendMagicSpirit(context.Background(), &pb.SendMagicSpiritReq{})
		if err != nil {
			t.Errorf("SendMagicSpirit fail. err:%v", err)
		}
	})
}

func TestMagicSpirit_SendUnpackGift(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMagicSpiritMgr(ctl)

	s := &MagicSpirit{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().OpenUnpackGift(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
	)

	t.Run("TestMagicSpirit_SendUnpackGift", func(t *testing.T) {
		_, err := s.SendUnpackGift(context.Background(), &pb.SendUnpackGiftReq{Uid: 1, ChannelId: 1, ItemOrderId: "test"})
		if err != nil {
			t.Errorf("SendUnpackGift fail. err:%v", err)
		}
	})
}

func TestMagicSpirit_ShutDown(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMagicSpiritMgr(ctl)

	s := &MagicSpirit{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().ShutDown(),
	)

	t.Run("TestMagicSpirit_ShutDown", func(t *testing.T) {
		s.ShutDown()
	})
}

func TestMagicSpirit_GetMagicSpiritConfTmp(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIMagicSpiritMgr(ctl)

	s := &MagicSpirit{
		mgr: mockMgr,
	}

	gomock.InOrder(
		mockMgr.EXPECT().GetMagicSpiritTmp(gomock.Any()).Return([]*pb.MagicSpiritTmp{
			{
				//Id:                   0,
				EffectTime: uint32(time.Now().Unix()),
				Conf: &pb.MagicSpirit{
					MagicSpiritId: 1,
					Name:          "1",
					IconUrl:       "1",
				},
			},
		}, nil),
		mockMgr.EXPECT().GetMagicSpiritPondTmp(gomock.Any()).Return(map[uint32][]*mysql.MagicSpiritPond{
			1: []*mysql.MagicSpiritPond{
				{
					ItemId:        1,
					MagicSpiritId: 1,
					Weight:        0,
					PresentId:     1,
					BeginTime:     time.Now(),
				},
			},
		}, nil),
		mockMgr.EXPECT().GetMagicSpiritByIds(gomock.Any(), gomock.Any()).Return(map[uint32]*pb.MagicSpirit{
			1: &pb.MagicSpirit{
				MagicSpiritId: 1,
				Name:          "2",
			},
		}, nil),
		mockMgr.EXPECT().GetMagicSpiritPond(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes(),
	)

	t.Run("TestMagicSpirit_SendUnpackGift", func(t *testing.T) {
		_, err := s.GetMagicSpiritConfTmp(context.Background(), &pb.GetMagicSpiritConfTmpReq{})
		if err != nil {
			t.Errorf("SendUnpackGift fail. err:%v", err)
		}
	})
}
