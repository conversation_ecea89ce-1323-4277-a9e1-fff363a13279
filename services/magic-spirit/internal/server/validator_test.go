package server

import (
	"testing"
	pb "golang.52tt.com/protocol/services/magic-spirit"
)

func TestCheckBInA(t *testing.T) {
	type args struct {
		A []*pb.TimeRange
		B []*pb.TimeRange
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "test1",
			args: args{
				A: []*pb.TimeRange{
					{
						StartTime: 1,
						EndTime:   2,
					},
					{
						StartTime: 3,
						EndTime:   5,
					},
				},
				B: []*pb.TimeRange{
					{
						StartTime: 3,
						EndTime:   4,
					},
				},
			},
			want: true,
		},

		{
			name: "test2",
			args: args{
				A: []*pb.TimeRange{
					{
						StartTime: 1,
						EndTime:   2,
					},
					{
						StartTime: 3,
						EndTime:   4,
					},
				},
				B: []*pb.TimeRange{
					{
						StartTime: 2,
						EndTime:   3,
					},
				},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := checkBInA(tt.args.A, tt.args.B); got != tt.want {
				t.E<PERSON>rf("CheckBInA() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_checkIfTimeRangeConflict(t *testing.T) {
	type args struct {
		timeRangeList []*pb.TimeRange
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "test1",
			args: args{
				timeRangeList: []*pb.TimeRange{
					{
						StartTime: 1,
						EndTime:   2,
					},
					{
						StartTime: 3,
						EndTime:   4,
					},
				},
			},
			want: false,
		},
		{
			name: "test2",
			args: args{
				timeRangeList: []*pb.TimeRange{
					{
						StartTime: 1,
						EndTime:   2,
					},
					{
						StartTime: 2,
						EndTime:   3,
					},
				},
			},
			want: true,
		},
		{
			name: "test3",
			args: args{
				timeRangeList: []*pb.TimeRange{
					{
						StartTime: 1,
						EndTime:   3,
					},
					{
						StartTime: 2,
						EndTime:   4,
					},
				},
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := checkIfTimeRangeConflict(tt.args.timeRangeList); got != tt.want {
				t.Errorf("checkIfTimeRangeConflict() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSliceCopyAndMove(t *testing.T) {
	tmpTimeList := append([]*pb.TimeRange{}, &pb.TimeRange{
		StartTime: 5,
		EndTime:   6,
	})
	timeListB := []*pb.TimeRange{
		&pb.TimeRange{
			StartTime: 3,
			EndTime:   4,
		},
		&pb.TimeRange{
			StartTime: 1,
			EndTime:   2,
		},
		&pb.TimeRange{
			StartTime: 7,
			EndTime:   8,
		},
	}

	tmpTimeList = append(tmpTimeList, timeListB...)
	checkRes := checkIfTimeRangeConflict(tmpTimeList)

	t.Logf("checkRes: %v", checkRes)
	t.Logf("tmpTimeList: %v", tmpTimeList)
	t.Logf("timeListB:%v", timeListB)
}
