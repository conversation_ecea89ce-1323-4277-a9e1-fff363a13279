package server

import (
	"context"
	"testing"
	pb "golang.52tt.com/protocol/services/magic-spirit"
	"reflect"
	"golang.52tt.com/services/magic-spirit/internal/conf"
)

func Test_timeRange2TimeListStr(t *testing.T) {
	type args struct {
		ctx      context.Context
		timeList []*pb.TimeRange
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			// ["1747726774-1747813174","1747899574-1747985974"]
			name: "test1",
			args: args{
				ctx: context.Background(),
				timeList: []*pb.TimeRange{
					{
						StartTime: 1747726774,
						EndTime:   1747813174,
					},
					{
						StartTime: 1747899574,
						EndTime:   1747985974,
					},
				},
			},
			want: "1747726774-1747813174,1747899574-1747985974",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := conf.TimeRange2TimeListStr(tt.args.timeList); got != tt.want {
				t.Errorf("timeRange2TimeListStr() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_str2TimeRangeListPb(t *testing.T) {
	type args struct {
		ctx         context.Context
		timeListStr string
	}
	tests := []struct {
		name string
		args args
		want []*pb.TimeRange
	}{
		{
			name: "test1",
			args: args{
				ctx:         context.Background(),
				timeListStr: "1747726774-1747813174,1747899574-1747985974",
			},
			want: []*pb.TimeRange{
				{
					StartTime: 1747726774,
					EndTime:   1747813174,
				},
				{
					StartTime: 1747899574,
					EndTime:   1747985974,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := conf.Str2TimeRangeListPb(tt.args.ctx, tt.args.timeListStr); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("str2TimeRangeListPb() = %v, want %v", got, tt.want)
			}
		})
	}
}
