package server

import (
	"context"
	"encoding/json"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/protocol/services/demo/echo"
	"golang.52tt.com/services/magic-spirit/internal/event/producer"
	"sort"
	"strconv"
	"strings"
	"time"

	"golang.52tt.com/pkg/log"
	logicPb "golang.52tt.com/protocol/app/magic-spirit-logic"
	pb "golang.52tt.com/protocol/services/magic-spirit"
	UnifiedPayCallback "golang.52tt.com/protocol/services/unified_pay/cb"
	"golang.52tt.com/services/magic-spirit/internal/cache"
	"golang.52tt.com/services/magic-spirit/internal/conf"
	"golang.52tt.com/services/magic-spirit/internal/manager"
	"golang.52tt.com/services/magic-spirit/internal/mysql"
)

type MagicSpirit struct {
	mgr manager.IMagicSpiritMgr
}

func NewServer(ctx context.Context, sc *conf.ServiceConfigT) (*MagicSpirit, error) {
	//sc := &conf.ServiceConfigT{}
	//cfgPath := ctx.Value("configfile").(string)
	//if cfgPath == "" {
	//	return nil, errors.New("configfile not exist")
	//}
	//err := sc.Parse(cfgPath)
	//if err != nil {
	//	log.Errorf("config Parse fail err:%v", err)
	//	return nil, err
	//}

	cache_, err := cache.NewCache(ctx, sc.GetRedisConfig())
	if nil != err {
		log.ErrorWithCtx(ctx, "init redis fail, err: %v", err)
		return nil, err
	}

	mysqlStore, err := mysql.NewMysql(sc.GetMysqlConfig(), sc.GetMysqlReadOnlyConfig())
	if err != nil {
		log.Errorf("NewMysql fail %+v, %+v, err:%v", sc.GetMysqlConfig(), sc.GetMysqlReadOnlyConfig(), err)
		return nil, err
	}

	magicSpiritKfk, err := producer.NewMagicSpiritEventProducer(sc.MagicSpiritPresentKFK)
	if err != nil {
		log.Errorf("NewMagicSpiritEventProducer fail %+v, err:%v", sc.MagicSpiritPresentKFK, err)
		return nil, err
	}

	mgr, err := manager.NewMagicSpiritMgr(mysqlStore, cache_, sc, magicSpiritKfk)
	if err != nil {
		log.Errorf("NewMagicSpiritMgr fail %+v, %+v, err:%v", sc.GetMysqlConfig(), sc.GetMysqlReadOnlyConfig(), err)
		return nil, err
	}

	return &MagicSpirit{
		mgr: mgr,
	}, nil
}

func (m *MagicSpirit) ShutDown() {
	m.mgr.ShutDown()
}

func (m *MagicSpirit) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

func (m *MagicSpirit) AddMagicSpirit(c context.Context, req *pb.AddMagicSpiritReq) (*pb.AddMagicSpiritResp, error) {
	resp := &pb.AddMagicSpiritResp{}
	defer func() {
		log.InfoWithCtx(c, "AddMagicSpirit req:%+v, resp: %+v", req, resp)
	}()
	var err error

	err = validateAddMagicSpiritReq(req)
	if err != nil {
		log.ErrorWithCtx(c, "SetMagicSpirit invalid params: %v", err)
		return resp, err
	}

	data := req.MagicSpirit[0]
	id, err := m.mgr.AddMagicSpirit(c, data)
	if err != nil {
		log.ErrorWithCtx(c, "SetMagicSpirit add db fail, req:%+v, error:%v", req, err)
		return resp, err
	}
	resp.MagicSpiritId = []uint32{id}

	return resp, nil
}

func (m *MagicSpirit) DelMagicSpirit(c context.Context, req *pb.DelMagicSpiritReq) (*pb.DelMagicSpiritResp, error) {
	resp := &pb.DelMagicSpiritResp{}

	err := m.mgr.DelMagicSpirit(c, req.MagicSpiritIds)
	if err != nil {
		log.ErrorWithCtx(c, "DelMagicSpirit error: %v", err)
		return resp, err
	}

	return resp, nil
}

func (m *MagicSpirit) GetMagicSpirit(c context.Context, req *pb.GetMagicSpiritReq) (*pb.GetMagicSpiritResp, error) {
	resp := &pb.GetMagicSpiritResp{}

	data, err := m.mgr.GetMagicSpirit(c)
	if err != nil {
		log.ErrorWithCtx(c, "GetMagicSpirit get data fail, error: %v", err)
		return resp, err
	}

	respMagicSpirit := make([]*pb.MagicSpirit, 0, 8)
	for _, item := range data {
		cfg := &pb.MagicSpirit{
			MagicSpiritId:    item.MagicSpiritId,
			Name:             item.Name,
			IconUrl:          item.IconUrl,
			Price:            item.Price,
			Rank:             item.Rank,
			EffectBegin:      item.EffectBegin,
			EffectEnd:        item.EffectEnd,
			DescribeImageUrl: item.DescribeImageUrl,
			Describe:         item.Describe,
			JuniorLighting:   item.JuniorLighting,
			MiddleLighting:   item.MiddleLighting,
			VfxResource:      item.VfxResource,
			VfxResourceMd5:   item.VfxResourceMd5,
			UpdateTime:       uint32(item.UpdateTime.Unix()),
			RankFloat:        item.RankFloat,
			DescActivityUrl:  item.ActivityJumpUrl,
            ShowEffectEnd:    item.ShowEffectEnd,
            ActivityCfg: &pb.MagicSpiritActivityCfg{
                ActivityName:       item.ActName,
                BeginTime:          item.ActBeginTime.Unix(),
                EndTime:            item.ActEndTime.Unix(),
                ImageUrl:           item.ActImageUrl,
                JumpUrlTt:          item.ActivityJumpUrl,
                JumpUrlHcAndroid:   item.ActJumpUrlHcAndroid,
                JumpUrlHcIos:       item.ActJumpUrlHcIos,
                JumpUrlMikeAndroid: item.ActJumpUrlMikeAndroid,
                JumpUrlMikeIos:     item.ActJumpUrlMikeIos,
            },
		}

		if item.ChannelTypeList == "" {
			cfg.ChannelTypeList = nil
			respMagicSpirit = append(respMagicSpirit, cfg)
			continue
		}
		splitElements := strings.Split(item.ChannelTypeList, ",")
		channelTypeList := make([]uint32, 0, len(splitElements))
		for _, element := range splitElements {
			channelType, err := strconv.ParseUint(element, 10, 32)
			if err != nil {
				log.WarnWithCtx(c, "GetMagicSpirit convert channelType error: %v", err)
				continue
			}
			channelTypeList = append(channelTypeList, uint32(channelType))
		}
		cfg.ChannelTypeList = channelTypeList
		respMagicSpirit = append(respMagicSpirit, cfg)
	}
	resp.MagicSpirit = respMagicSpirit

	return resp, nil
}

func (m *MagicSpirit) UpdateMagicSpirit(c context.Context, req *pb.UpdateMagicSpiritReq) (*pb.UpdateMagicSpiritResp, error) {
	resp := &pb.UpdateMagicSpiritResp{}
	defer func() {
		log.InfoWithCtx(c, "UpdateMagicSpirit req:%+v, resp: %+v", req, resp)
	}()
	var err error

	err = validateUpdateMagicSpiritReq(req)
	if err != nil {
		log.ErrorWithCtx(c, "UpdateMagicSpirit invalid params: %v", err)
		return resp, err
	}
	magicSpirit := req.GetMagicSpirit()[0]
	err = m.mgr.SetMagicSpiritTemporary(c, magicSpirit)
	if err != nil {
		log.ErrorWithCtx(c, "UpdateMagicSpirit error: %v", err)
		return resp, err
	}

	return resp, nil
}

func (m *MagicSpirit) AddMagicSpiritPond(c context.Context, req *pb.AddMagicSpiritPondReq) (*pb.AddMagicSpiritPondResp, error) {
	resp := &pb.AddMagicSpiritPondResp{}
	defer func() {
		log.InfoWithCtx(c, "AddMagicSpiritPond req:%+v, resp: %+v", req, resp)
	}()
	data := make([]*mysql.MagicSpiritPond, 0, 8)
	for _, item := range req.MagicSpiritPondItems {
		if item.GetPrizeLevel() == uint32(logicPb.MagicSpiritEffectType_MAGIC_SPIRIT_EFFECT_LV4) && item.GetBreakingNewId() == 0 {
			log.ErrorWithCtx(c, "AddMagicSpiritPond error: 全服公告大奖Id不能为0")
			return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "全服公告大奖Id不能为0")
		}
		data = append(data, &mysql.MagicSpiritPond{
			MagicSpiritId:  item.MagicSpiritId,
			Weight:         item.Weight,
			PresentId:      item.PresentId,
			Price:          item.ItemId,
			PrizeLevel:     item.PrizeLevel,
			BeginTime:      time.Unix(int64(item.GetEffectTime()), 0),
			EndTime:        time.Date(2038, 01, 01, 0, 0, 0, 0, time.Local), // 先设置成一个较大的数
			BreakingNewsId: item.GetBreakingNewId(),
		})
	}

	ids, err := m.mgr.AddMagicSpiritPond(c, data)
	if err != nil {
		log.ErrorWithCtx(c, "AddMagicSpiritPond error: %v", err)
		return resp, err
	}

	resp.MagicSpiritIds = ids
	return resp, nil
}

func (m *MagicSpirit) GetMagicSpiritPond(c context.Context, req *pb.GetMagicSpiritPondReq) (*pb.GetMagicSpiritPondResp, error) {
	resp := &pb.GetMagicSpiritPondResp{}

	data, err := m.mgr.GetMagicSpiritPond(c, req.MagicSpiritId)
	if err != nil {
		log.ErrorWithCtx(c, "GetMagicSpiritPond error: %v", err)
		return resp, nil
	}

	msp := make([]*pb.MagicSpiritPondItem, 0, 8)
	for _, item := range data {
		msp = append(msp, &pb.MagicSpiritPondItem{
			ItemId:        item.ItemId,
			MagicSpiritId: item.MagicSpiritId,
			PresentId:     item.PresentId,
			Weight:        item.Weight,
			PrizeLevel:    item.PrizeLevel,
			UpdateTime:    uint32(item.CreateTime.Unix()),
			BreakingNewId: item.BreakingNewsId,
		})
	}

	resp.MagicSpiritPond = msp
	return resp, nil
}

func (m *MagicSpirit) GetCommonConf(c context.Context, req *pb.GetCommonConfReq) (*pb.GetCommonConfResp, error) {
	resp := &pb.GetCommonConfResp{}

	conf, err := m.mgr.GetCommonConf(c)
	if err != nil {
		log.ErrorWithCtx(c, "GetCommonConf mgr get error: %v", err)
		return resp, err
	}

	rsConf := make([]*pb.CommonConf, 0, 8)
	for _, item := range conf {
		val := uint32(0)
		if item.ValueType == manager.CONF_VAL_TYPE_INT {
			tmpVal, err := strconv.ParseInt(item.Value, 10, 64)
			if err != nil {
				log.ErrorWithCtx(c, "GetCommonConf convert conf type error: %v, confId: %v", item.ConfId)
				continue
			}
			val = uint32(tmpVal)
		}

		rsConf = append(rsConf, &pb.CommonConf{
			ConfType: item.ConfId,
			Value:    val,
		})
	}

	resp.CommonConf = rsConf
	return resp, nil
}

func (m *MagicSpirit) AddMagicSpiritBlacklist(c context.Context, req *pb.AddMagicSpiritBlacklistReq) (*pb.AddMagicSpiritBlacklistResp, error) {
	resp := &pb.AddMagicSpiritBlacklistResp{}

	data := make([]*mysql.MagicSpiritBlacklist, 0, 8)
	for _, item := range req.Blacklist {
		data = append(data, &mysql.MagicSpiritBlacklist{
			ChannelId: item.ChannelId,
			RoomId:    item.RoomId,
			RoomName:  item.RoomName,
			Ttid:      item.Ttid,
			RoomOwner: item.Owner,
		})
	}
	ids, err := m.mgr.AddMagicSpiritBlacklist(c, data)
	if err != nil {
		log.ErrorWithCtx(c, "AddMagicSpiritBlacklist error: %v", err)
		return resp, err
	}

	resp.BlacklistId = ids
	return resp, nil
}

func (m *MagicSpirit) GetMagicSpiritBlacklist(c context.Context, req *pb.GetMagicSpiritBlackListReq) (*pb.GetMagicSpiritBlackListResp, error) {
	resp := &pb.GetMagicSpiritBlackListResp{}

	total, data, err := m.mgr.GetMagicSpiritBlacklist(c, req.ChannelId, req.PageNum, req.PageSize)
	if err != nil {
		log.ErrorWithCtx(c, "GetMagicSpiritBlacklist error: %v", err)
		return resp, err
	}

	rsData := make([]*pb.MagicSpiritBlacklist, 0, 8)
	for _, item := range data {
		rsData = append(rsData, &pb.MagicSpiritBlacklist{
			BlacklistId: item.BlacklistId,
			ChannelId:   item.ChannelId,
			RoomId:      item.RoomId,
			RoomName:    item.RoomName,
			Ttid:        item.Ttid,
			Owner:       item.RoomOwner,
			CreateTime:  uint32(item.CreateTime.Unix()),
		})
	}
	resp.Total = total
	resp.Blacklist = rsData

	return resp, nil
}

func (m *MagicSpirit) DelMagicSpiritBlacklist(c context.Context, req *pb.DelMagicSpiritBlacklistReq) (*pb.DelMagicSpiritBlacklistResp, error) {
	resp := &pb.DelMagicSpiritBlacklistResp{}

	err := m.mgr.DelMagicSpiritBlacklist(c, req.ChannelIds)
	if err != nil {
		log.ErrorWithCtx(c, "DelMagicSpiritBlacklist error: %v", err)
		return resp, err
	}

	return resp, nil
}

func (m *MagicSpirit) SetCommonConf(c context.Context, req *pb.SetCommonConfReq) (*pb.SetCommonConfResp, error) {
	resp := &pb.SetCommonConfResp{}

	data := make([]*mysql.MagicSpiritCommonConf, 0, 8)
	for _, item := range req.CommonConf {
		data = append(data, &mysql.MagicSpiritCommonConf{
			ConfId:    item.ConfType,
			Value:     strconv.FormatInt(int64(item.Value), 10),
			ValueType: manager.CONF_VAL_TYPE_INT,
		})
	}
	err := m.mgr.SetCommonConf(c, data)
	if err != nil {
		log.ErrorWithCtx(c, "SetCommonConf error: %v", err)
		return resp, err
	}

	return resp, nil
}

func (m *MagicSpirit) SendMagicSpirit(ctx context.Context, in *pb.SendMagicSpiritReq) (*pb.SendMagicSpiritResp, error) {
	out := &pb.SendMagicSpiritResp{}
	defer func() {
		log.InfoWithCtx(ctx, "SendMagicSpirit in:%+v, out:%+v", in, out)
	}()
	var err error
	out, err = m.mgr.SendMagicSpirit(ctx, in)
	return out, err
}

func (m *MagicSpirit) Notify(ctx context.Context, req *UnifiedPayCallback.PayNotify) (*UnifiedPayCallback.PayNotifyResponse, error) {
	out := &UnifiedPayCallback.PayNotifyResponse{}
	op, err := m.mgr.Callback(ctx, req.GetOutTradeNo())
	if err != nil {
		log.Errorf("Notify fail to Callback. in:%+v, err:%v", req, err)
		return out, err
	}

	out.Confirmed = true
	out.Op = op

	return out, nil
}

func (m *MagicSpirit) GetMagicSpiritForCli(ctx context.Context, in *pb.GetMagicSpiritForCliReq) (*pb.GetMagicSpiritForCliResp, error) {
	resp := &pb.GetMagicSpiritForCliResp{}

	lastUpdateTime, err := m.mgr.GetMagicSpiritVersion(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMagicSpiritForCli get last version error: %v", err)
		// 如果版本缓存失效， 默认拉取
		lastUpdateTime = uint32(time.Now().Unix())
	}
	if lastUpdateTime > in.GetCurrentVersion() {
		resp.CurrentVersion = lastUpdateTime
	} else {
		log.Debugf("GetMagicSpiritForCli magic_spirit config no update, sversion: %d, cversion: %v", lastUpdateTime, resp.CurrentVersion)
		resp.CurrentVersion = lastUpdateTime
		resp.MagicSpirits = make([]*pb.MagicSpiritForCli, 0)
		return resp, nil
	}

	data, err := m.mgr.GetMagicSpiritWithCache(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMagicSpiritForCli find db error: %v", err)
		return nil, err
	}

	// 排序, 先按rankFloat排, rankFloat相同updateTime倒序
	sort.Slice(data, func(i, j int) bool {
		if data[i].RankFloat == data[j].RankFloat {
			return data[i].UpdateTime.Unix() > data[j].UpdateTime.Unix()
		}

		return data[i].RankFloat < data[j].RankFloat
	})

	respMagicSpiritForCLi := make([]*pb.MagicSpiritForCli, 0, 8)
	for _, item := range data {
		// 判断上下架时间, 只要为下架的都查出
		now := uint32(time.Now().Unix())
		if item.EffectEnd < now {
			continue
		}

		respMagicSpiritForCLiItem := &pb.MagicSpiritForCli{
			MagicSpiritId:    item.MagicSpiritId,
			Name:             item.Name,
			IconUrl:          item.IconUrl,
			Price:            item.Price,
			Rank:             item.Rank,
			EffectBegin:      item.EffectBegin,
			EffectEnd:        item.EffectEnd,
			DescribeImageUrl: item.DescribeImageUrl,
			DescribeJumpUrl:  item.Describe,
			//JuniorLighting:   item.JuniorLighting,
			//MiddleLighting:   item.MiddleLighting,
			VfxResource:     item.VfxResource,
			VfxResourceMd5:  item.VfxResourceMd5,
			PresentIds:      nil,
			RankFloat:       item.RankFloat,
			DescActivityUrl: item.ActivityJumpUrl,
            ShowEffectEnd:    item.ShowEffectEnd,
            ActivityCfg: &pb.MagicSpiritActivityCfg{
                ActivityName:       item.ActName,
                BeginTime:          item.ActBeginTime.Unix(),
                EndTime:            item.ActEndTime.Unix(),
                ImageUrl:           item.ActImageUrl,
                JumpUrlTt:          item.ActivityJumpUrl,
                JumpUrlHcAndroid:   item.ActJumpUrlHcAndroid,
                JumpUrlHcIos:       item.ActJumpUrlHcIos,
                JumpUrlMikeAndroid: item.ActJumpUrlMikeAndroid,
                JumpUrlMikeIos:     item.ActJumpUrlMikeIos,
            },
		}
		pondData, err := m.mgr.GetMagicSpiritPondWithCache(ctx, item.MagicSpiritId)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetMagicSpiritForCli get pond error: %v", err)
			return nil, err
		}
		if len(pondData) == 0 {
			continue
		}

		presentIds := make([]uint32, 0, 8)
		for _, pondItem := range pondData {
			presentIds = append(presentIds, pondItem.PresentId)
		}

		respMagicSpiritForCLiItem.PresentIds = presentIds
		respMagicSpiritForCLi = append(respMagicSpiritForCLi, respMagicSpiritForCLiItem)
	}

	resp.MagicSpirits = respMagicSpiritForCLi
	return resp, nil
}

func (m *MagicSpirit) GetMagicSpiritUsable(ctx context.Context, in *pb.GetMagicSpiritUsableReq) (*pb.GetMagicSpiritUsableResp, error) {
	resp, err := m.mgr.GetMagicSpiritUsable(ctx, in)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMagicSpiritUsable error: %v", err)
		return resp, err
	}

	log.Debugf("GetMagicSpiritUsable in:%+v, out:%+v", in, resp)
	return resp, nil
}

func (m *MagicSpirit) GetMagicSpiritOrderTotal(ctx context.Context, req *pb.GetMagicSpiritOrderTotalReq) (*pb.GetMagicSpiritOrderTotalResp, error) {
	out := &pb.GetMagicSpiritOrderTotalResp{}
	if req.GetBeginTime() == 0 || req.GetEndTime() == 0 {
		return out, nil
	}

	beginTime := time.Unix(int64(req.GetBeginTime()), 0)
	endTime := time.Unix(int64(req.GetEndTime()), 0)

	orderCnt, totalPrice, totalNum, err := m.mgr.GetMagicOrderTotal(ctx, beginTime, endTime, 0)
	if err != nil {
		log.Errorf("GetMagicSpiritOrderTotal fail to GetMagicOrderTotal. in:%+v, err:%v", req, err)
		return out, err
	}

	out.OrderCnt = orderCnt
	out.TotalPrice = totalPrice
	out.TotalNum = totalNum

	return out, nil
}

func (m *MagicSpirit) GetMagicSpiritAwardTotal(ctx context.Context, req *pb.GetMagicSpiritAwardTotalReq) (*pb.GetMagicSpiritAwardTotalResp, error) {
	out := &pb.GetMagicSpiritAwardTotalResp{}
	if req.GetBeginTime() == 0 || req.GetEndTime() == 0 {
		return out, nil
	}

	beginTime := time.Unix(int64(req.GetBeginTime()), 0)
	endTime := time.Unix(int64(req.GetEndTime()), 0)

	orderCnt, totalPrice, totalNum, err := m.mgr.GetMagicAwardTotal(ctx, beginTime, endTime)
	if err != nil {
		log.Errorf("GetMagicSpiritAwardTotal fail to GetMagicOrderTotal. in:%+v, err:%v", req, err)
		return out, err
	}

	out.OrderCnt = orderCnt
	out.TotalPrice = totalPrice
	out.TotalNum = totalNum

	return out, nil
}

type ReconcileParams struct {
	Source uint32 `json:"source"`
}

func unmarshalReconcileParams(str string) *ReconcileParams {
	param := &ReconcileParams{}
	if str == "" {
		return param
	}

	err := json.Unmarshal([]byte(str), param)
	if err != nil {
		log.Errorf("unmarshalReconcileParams fail. str:%s, err:%v", str, err)
	}

	return param
}

func (m *MagicSpirit) SendUnpackGift(ctx context.Context, in *pb.SendUnpackGiftReq) (*pb.SendUnpackGiftResp, error) {
	out := &pb.SendUnpackGiftResp{}

	if in.GetUid() == 0 || in.GetChannelId() == 0 || in.GetItemOrderId() == "" {
		return out, protocol.NewExactServerError(nil, status.ErrMagicSpiritSendLimit, "参数有误")
	}

	opt, err := m.mgr.OpenUnpackGift(ctx, in.GetUid(), in.GetChannelId(), in.GetItemOrderId(), false)
	if err != nil {
		log.Errorf("SendUnpackGift fail to OpenUnpackGift. in:%+v, err:%v", in, err)
		return out, err
	}

	out.SendOpt = opt

	log.Infof("SendUnpackGift in:%+v, out:%+v", in, out)
	return out, nil
}

func (m *MagicSpirit) CheckIfSendMagicWithSource(ctx context.Context, in *pb.CheckIfSendMagicWithSourceReq) (*pb.CheckIfSendMagicWithSourceResp, error) {
	out := &pb.CheckIfSendMagicWithSourceResp{}
	var err error
	_, err = m.mgr.CheckIfSendMagicWithSource(ctx, in)
	if err != nil {
		log.Errorf("CheckIfSendMagicWithSource fail to CheckIfSendMagicWithSource. in:%+v, err:%v", in, err)
		return out, err
	}

	return out, nil
}

func (m *MagicSpirit) SendMagicWithSource(ctx context.Context, in *pb.SendMagicWithSourceReq) (*pb.SendMagicWithSourceResp, error) {
	out := &pb.SendMagicWithSourceResp{}
	defer func() {
		log.InfoWithCtx(ctx, "SendMagicWithSource in:%+v, out:%+v", in, out)
	}()
	var err error
	out, err = m.mgr.SendMagicWithSource(ctx, in)
	return out, err
}

func (m *MagicSpirit) GetChannelAllUnpackGift(ctx context.Context, in *pb.GetChannelAllUnpackGiftReq) (*pb.GetChannelAllUnpackGiftResp, error) {
	return m.mgr.GetChannelAllUnpackGift(ctx, in.GetUid(), in.GetChannelId())
}

func (m *MagicSpirit) GetMagicSpiritConfTmp(ctx context.Context, req *pb.GetMagicSpiritConfTmpReq) (*pb.GetMagicSpiritConfTmpResp, error) {
	out := &pb.GetMagicSpiritConfTmpResp{}

	beginTime := time.Now()
	// 获取幸运礼物配置
	magicTmps, err := m.mgr.GetMagicSpiritTmp(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMagicSpiritConfTmp fail to GetMagicSpiritTmp,err:%v", err)
		return out, err
	}

	for _, v := range magicTmps {
		out.ConfList = append(out.ConfList, &pb.MagicSpiritConfTmp{
			MagicSpiritTmp: v,
			HasPool:        false,
		})
	}

	// 获取奖池配置
	magicPonds, err := m.mgr.GetMagicSpiritPondTmp(ctx, beginTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMagicSpiritConfTmp fail to GetMagicSpiritPondTmp,err:%v", err)
		return out, err
	}
	magicIds := make([]uint32, 0)
	for k := range magicPonds {
		magicIds = append(magicIds, k)
	}
	if len(magicIds) == 0 {
		return out, nil
	}
	// 获取幸运礼物by magicSpiritIds
	mapMagicConf, err := m.mgr.GetMagicSpiritByIds(ctx, magicIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMagicSpiritConfTmp fail to GetMagicSpiritByIds,err:%v, magicIds:%v", err, magicIds)
		return out, err
	}

	for k, v := range mapMagicConf {
		out.ConfList = append(out.ConfList, &pb.MagicSpiritConfTmp{
			MagicSpiritTmp: &pb.MagicSpiritTmp{
				//EffectTime:           0,
				Conf: v,
			},
			HasPool: true,
			Pool:    fillMagicPondList2PB(magicPonds[k]),
		})
	}

	return out, nil
}

func fillMagicPondList2PB(list []*mysql.MagicSpiritPond) []*pb.MagicSpiritPondItem {
	res := make([]*pb.MagicSpiritPondItem, 0)
	if len(list) == 0 {
		return res
	}

	for _, v := range list {
		res = append(res, &pb.MagicSpiritPondItem{
			ItemId:        v.ItemId,
			MagicSpiritId: v.MagicSpiritId,
			PresentId:     v.PresentId,
			Weight:        v.Weight,
			PrizeLevel:    v.PrizeLevel,
			UpdateTime:    uint32(v.CreateTime.Unix()),
			EffectTime:    uint32(v.BeginTime.Unix()),
			BreakingNewId: v.BreakingNewsId,
		})
	}
	return res
}

func (m *MagicSpirit) GetMagicSpiritExemptValue(ctx context.Context, req *pb.GetMagicSpiritExemptValueReq) (*pb.GetMagicSpiritExemptValueResp, error) {
	out := &pb.GetMagicSpiritExemptValueResp{}
	magicSendFlagList := make([]*pb.MagicSpiritExemptValue, 0)

	magicIdMap, err := m.mgr.GetUserExemptCondVal(ctx, req.GetUid(), req.GetMagicSpiritId()...)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMagicSpiritExemptValue fail to GetUserExemptCondVal,err:%v", err)
		return out, err
	}

	for magicId, value := range magicIdMap {
		magicSendFlagList = append(magicSendFlagList, &pb.MagicSpiritExemptValue{
			MagicSpiritId: magicId,
			SendFlag:      value,
		})
	}

	out.ValueList = magicSendFlagList
	return out, err
}
