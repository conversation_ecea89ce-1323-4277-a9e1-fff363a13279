package mysql

import (
	"testing"
	"time"
	"context"
)

func TestStore_UpdateSpecialPond(t *testing.T) {

	pondList := []*MagicSpiritPond{
		{
			MagicSpiritId: 1,
			PresentId:     1,
			Price:         1,
			Weight:        1,
			PrizeLevel:    1,
			BeginTime:     time.Now(),
			EndTime:       time.Now(),
		},
		{
			MagicSpiritId: 1,
			PresentId:     2,
			Price:         2,
			Weight:        2,
			PrizeLevel:    2,
			BeginTime:     time.Now(),
			EndTime:       time.Now(),
		},
		{
			MagicSpiritId: 1,
			PresentId:     3,
			Price:         2,
			Weight:        2,
			PrizeLevel:    2,
			BeginTime:     time.Now(),
			EndTime:       time.Now(),
		},
	}

	err := mysqlStore.UpdateSpecialPond(context.Background(), 1, pondList)
	if err != nil {
		t.Fatal(err)
	}
}

func TestStore_GetSpecialPondMaxUpdateTime(t *testing.T) {
	maxUpdateTime, err := mysqlStore.GetSpecialPondMaxUpdateTime(context.Background())
	if err != nil {
		t.Fatal(err)
	}
	t.Log(maxUpdateTime)
}
