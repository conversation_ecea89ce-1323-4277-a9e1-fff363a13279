package mysql

import (
	"context"
	"database/sql"
	"strconv"
	"strings"
	"time"

	"golang.52tt.com/pkg/log"
	"github.com/jinzhu/gorm"
)

type MagicSpirit struct {
	MagicSpiritId    uint32    `gorm:"primary_key;autoIncrement"`
	Name             string    `gorm:"type:varchar(255) not null;default:'';comment:'礼物名称'"`
	IconUrl          string    `gorm:"type:varchar(255) not null;default:'';comment:'礼物图标'"`
	Price            uint32    `gorm:"type:int(10) unsigned not null;default:0;comment:'礼物价格'"`
	Rank             uint32    `gorm:"type:int(10) unsigned not null;default:0;comment:'礼物排名'"`
	EffectBegin      uint32    `gorm:"type:int(10) unsigned not null;default:0;comment:'上架时间'"`
	EffectEnd        uint32    `gorm:"type:int(10) unsigned not null;default:0;comment:'下架时间'"`
	DescribeImageUrl string    `gorm:"type:varchar(255) not null;default:'';comment:'礼物介绍浮层图'"`
	Describe         string    `gorm:"type:varchar(255) not null;default:'';comment:'礼物介绍'"`
	ActivityJumpUrl  string    `gorm:"type:varchar(255) not null;default:'';comment:'礼物活动链接跳转url'"`
	JuniorLighting   uint32    `gorm:"type:int(10) unsigned not null;default:0;comment:'初级光效个数'"`
	MiddleLighting   uint32    `gorm:"type:int(10) unsigned not null;default:0;comment:'高级光效个数'"`
	VfxResource      string    `gorm:"type:varchar(255) not null;default:'';comment:'特效资源地址'"`
	VfxResourceMd5   string    `gorm:"type:varchar(32) not null;default:'';comment:'特效资源md5'"`
	HasPond          bool      `gorm:"type:bool not null;default:false;comment:'是否配置奖池'"`
	IsDel            bool      `gorm:"type:bool not null;default:false;comment:'已删除'"`
	CreateTime       time.Time `gorm:"type:timestamp not null;default:current_timestamp;"`
	UpdateTime       time.Time `gorm:"type:timestamp on update current_timestamp;omitempty;default:current_timestamp;"`
	RankFloat        float32   `gorm:"type:double unsigned not null;default:0;comment:'礼物排名'"`
	ChannelTypeList  string    `gorm:"type:varchar(255) not null;default:'';comment:'渠道类型列表'"`

	ShowEffectEnd         bool      `gorm:"type:bool not null;default:false;comment:'是否展示下架时间'"`
	ActName               string    `gorm:"type:varchar(255) not null;default:'';comment:'活动名称'"`
	ActBeginTime          time.Time `gorm:"type:datetime not null;default:0;comment:'活动开始时间'"`
	ActEndTime            time.Time `gorm:"type:datetime not null;default:0;comment:'活动结束时间'"`
	ActImageUrl           string    `gorm:"type:varchar(255) not null;default:'';comment:'活动图片地址'"`
	ActJumpUrlHcAndroid   string    `gorm:"type:varchar(255) not null;default:'';comment:'欢游安卓活动跳转链接'"`
	ActJumpUrlHcIos       string    `gorm:"type:varchar(255) not null;default:'';comment:'欢游ios活动跳转链接'"`
	ActJumpUrlMikeAndroid string    `gorm:"type:varchar(255) not null;default:'';comment:'麦可安卓活动跳转链接'"`
	ActJumpUrlMikeIos     string    `gorm:"type:varchar(255) not null;default:'';comment:'麦可ios活动跳转链接'"`

	SpDescImg      string `gorm:"type:varchar(255) not null;default:'';comment:'礼物介绍浮层'"`
	SpDescJumpUrl  string `gorm:"type:varchar(255) not null;default:'';comment:'礼物介绍详情跳转链接'"`
	MagicTimeList  string `gorm:"type:varchar(512) not null;default:'';comment:'幸运礼物上线时间段'"`
	SpPondTimeList string `gorm:"type:varchar(512) not null;default:'';comment:'特殊奖池上线时间段'"`
}

/*
alter table magic_spirit add column magic_time_list varchar(512) not null default '' comment '幸运礼物上线时间段';
alter table magic_spirit add column sp_pond_time_list varchar(512) not null default '' comment '特殊奖池上线时间段';
alter table magic_spirit add column sp_desc_img varchar(255) not null default '' comment '礼物介绍浮层';
alter table magic_spirit add column sp_desc_jump_url varchar(255) not null default '' comment '礼物介绍详情跳转链接';
*/

func (m *MagicSpirit) TableName() string {
	return "magic_spirit"
}

func (s *Store) AddMagicSpirit(ctx context.Context, spirit *MagicSpirit) (uint32, error) {
	// 超时ctx
	tx := s.db.BeginTx(ctx, &sql.TxOptions{})
	err := tx.Create(spirit).Error
	if err != nil {
		tx.Rollback()
		return 0, err
	}
	tx.Commit()
	log.InfoWithCtx(ctx, "AddMagicSpirit success, spirit:%+v", spirit)
	return spirit.MagicSpiritId, nil
}

func (s *Store) GetMagicSpirit(ctx context.Context) ([]*MagicSpirit, error) {
	rs := make([]*MagicSpirit, 0)
	err := s.db.Model(&MagicSpirit{}).Where("is_del = 0").Order("magic_spirit_id DESC").Find(&rs).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMagic find error: %v", err)
		return nil, err
	}

	return rs, nil
}

func (s *Store) GetMagicSpiritById(ctx context.Context, magicSpiritId uint32) (*MagicSpirit, error) {
	rs := &MagicSpirit{}
	err := s.db.Model(&MagicSpirit{}).Where("magic_spirit_id = ?", magicSpiritId).Where("is_del = 0").Scan(&rs).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMagicSpiritById find db error: %v", err)
		return rs, err
	}

	return rs, nil
}

func (s *Store) GetMagicSpiritByIds(ctx context.Context, magicSpiritIds []uint32) (map[uint32]*MagicSpirit, error) {
	out := make(map[uint32]*MagicSpirit)

	rs := make([]*MagicSpirit, 0)
	err := s.db.Model(&MagicSpirit{}).Where("magic_spirit_id in (?) AND is_del =0", magicSpiritIds).Scan(&rs).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMagicSpiritById find db error: %v", err)
		return out, err
	}

	for _, v := range rs {
		out[v.MagicSpiritId] = v
	}

	return out, nil
}

func (s *Store) UpdateMagicSpirit(ctx context.Context, tx *gorm.DB, spirit *MagicSpirit) error {
	err := tx.Debug().Model(&MagicSpirit{}).
		Where("magic_spirit_id = ?", spirit.MagicSpiritId).
		Update(fillMagicMap(spirit)).
		Error
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateMagic error: %v", err)
		return err
	}
	log.InfoWithCtx(ctx, "UpdateMagic success, spirit:%+v", spirit)
	return nil
}

func (s *Store) DelMagicSpirit(ctx context.Context, magicIds []uint32) error {
	err := s.db.Model(&MagicSpirit{}).
		Where("magic_spirit_id in (?)", magicIds).
		Where("is_del = ?", false).
		Update("is_del", true).
		Update("update_time", time.Now().Unix()).Error
	if err != nil {
		return err
	}
	log.InfoWithCtx(ctx, "DelMagicSpirit success, magicIds:%+v", magicIds)
	return err
}

// ==============================internal=============================
func fillMagicMap(magic *MagicSpirit) map[string]interface{} {
	rs := make(map[string]interface{})

	rs["name"] = magic.Name
	rs["icon_url"] = magic.IconUrl
	rs["price"] = magic.Price
	rs["rank"] = magic.Rank
	rs["effect_begin"] = magic.EffectBegin
	rs["effect_end"] = magic.EffectEnd
	rs["describe_image_url"] = magic.DescribeImageUrl
	rs["describe"] = magic.Describe
	rs["activity_jump_url"] = magic.ActivityJumpUrl // add
	rs["junior_lighting"] = magic.JuniorLighting
	rs["middle_lighting"] = magic.MiddleLighting
	rs["vfx_resource"] = magic.VfxResource
	rs["vfx_resource_md5"] = magic.VfxResourceMd5
	rs["rank_float"] = magic.RankFloat
	rs["channel_type_list"] = magic.ChannelTypeList

	rs["show_effect_end"] = magic.ShowEffectEnd
	rs["act_name"] = magic.ActName
	rs["act_begin_time"] = magic.ActBeginTime
	rs["act_end_time"] = magic.ActEndTime
	rs["act_image_url"] = magic.ActImageUrl
	rs["act_jump_url_hc_android"] = magic.ActJumpUrlHcAndroid
	rs["act_jump_url_hc_ios"] = magic.ActJumpUrlHcIos
	rs["act_jump_url_mike_android"] = magic.ActJumpUrlMikeAndroid
	rs["act_jump_url_mike_ios"] = magic.ActJumpUrlMikeIos

	rs["sp_desc_img"] = magic.SpDescImg
	rs["sp_desc_jump_url"] = magic.SpDescJumpUrl
	rs["magic_time_list"] = magic.MagicTimeList
	rs["sp_pond_time_list"] = magic.SpPondTimeList

	return rs
}

func (s *Store) BatchGetMagicSpiritLimitChannelTypeList(ctx context.Context, magicSpiritIds []uint32) (map[uint32][]uint32, error) {
	rs := make(map[uint32][]uint32)

	type MagicSpiritSimpleItem struct {
		MagicSpiritId   uint32 `gorm:"column:magic_spirit_id"`
		ChannelTypeList string `gorm:"column:channel_type_list"`
	}
	itemList := make([]*MagicSpiritSimpleItem, 0)

	// select magic_spirit_id,channel_type_list from magic_spirit where magic_spirit_id in (?) and is_del = 0
	err := s.db.Debug().Model(&MagicSpirit{}).Select("magic_spirit_id,channel_type_list").Where("magic_spirit_id in (?)", magicSpiritIds).Where("is_del = 0").Scan(&itemList).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetMagicSpiritLimitChannelTypeList find db, magicSpiritIds:%+v, error: %v", magicSpiritIds, err)
		return nil, err
	}
	for _, item := range itemList {
		if item.ChannelTypeList == "" {
			continue
		}
		splitSegments := strings.Split(item.ChannelTypeList, ",")
		channelTypeList := make([]uint32, 0, len(splitSegments))
		for _, str := range splitSegments {
			channelType, err := strconv.ParseUint(str, 10, 32)
			if err != nil {
				log.WarnWithCtx(ctx, "BatchGetMagicSpiritLimitChannelTypeList parse channelTypeList, magicSpiritId:%d, channelTypeList:%s, error:%v", item.MagicSpiritId, item.ChannelTypeList, err)
				continue
			}
			channelTypeList = append(channelTypeList, uint32(channelType))
		}
		rs[item.MagicSpiritId] = channelTypeList
	}
	return rs, nil

}
