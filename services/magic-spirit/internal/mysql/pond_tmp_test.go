package mysql

import (
	"testing"
	"time"
	"context"
	"database/sql"
)

func TestStore_AddPondTmp(t *testing.T) {
	beginTime := time.Now()
	pondList := []*MagicSpiritPondTmp{
		{
			MagicSpiritId: 1,
			PresentId:     1,
			Price:         1,
			PrizeLevel:    1,
			Weight:        1,
			BeginTime:     beginTime,
		},
		{
			MagicSpiritId: 1,
			PresentId:     2,
			Price:         2,
			PrizeLevel:    2,
			Weight:        2,
			BeginTime:     beginTime,
		},
	}

	err := mysqlStore.AddNormalPondTmp(context.Background(), pondList)
	if err != nil {
		t.Fatal(err)
	}
}

// 获取临时表数据
func TestStore_GetNormalPondTmp(t *testing.T) {
	beginTime := time.Now().Add(1 * time.Hour)
	pondList := []*MagicSpiritPondTmp{
		{
			MagicSpiritId: 1,
			PresentId:     1,
			Price:         1,
			PrizeLevel:    1,
			Weight:        1,
			BeginTime:     beginTime,
		},
		{
			MagicSpiritId: 1,
			PresentId:     2,
			Price:         2,
			PrizeLevel:    2,
			Weight:        2,
			BeginTime:     beginTime,
		},
	}

	err := mysqlStore.AddNormalPondTmp(context.Background(), pondList)
	if err != nil {
		t.Fatal(err)
	}
	pondList, err = mysqlStore.GetNormalPondTmp(context.Background())
	if err != nil {
		t.Fatal(err)
	}

	for _, pond := range pondList {
		t.Logf("%+v", pond)
	}
}

func TestStore_GetEffectivePondTmp(t *testing.T) {
	beginTime := time.Now().Add(-3 * time.Second)
	pondList := []*MagicSpiritPondTmp{
		{
			MagicSpiritId: 1,
			PresentId:     1,
			Price:         1,
			PrizeLevel:    1,
			Weight:        1,
			BeginTime:     beginTime,
		},
		{
			MagicSpiritId: 1,
			PresentId:     2,
			Price:         2,
			PrizeLevel:    2,
			Weight:        2,
			BeginTime:     beginTime,
		},
	}

	err := mysqlStore.AddNormalPondTmp(context.Background(), pondList)
	if err != nil {
		t.Fatal(err)
	}

	// 获取有效的奖池
	pondList, err = mysqlStore.GetEffectivePondTmp(context.Background())
	if err != nil {
		t.Fatal(err)
	}

	ids := make([]uint32, 0)
	for _, pond := range pondList {
		t.Logf("%+v", pond)
		ids = append(ids, pond.ItemId)
	}
	t.Logf("%+v", ids)
	ctx := context.Background()
	tx := mysqlStore.db.BeginTx(ctx, &sql.TxOptions{})
	// update update_flag
	rows, err := mysqlStore.UpdatePondTmpFlag(ctx, tx, ids)
	if err != nil {
		tx.Rollback()
		t.Fatal(err)
	}
	t.Log(rows)
}
