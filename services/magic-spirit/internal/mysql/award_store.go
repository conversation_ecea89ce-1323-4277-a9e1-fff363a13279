package mysql

import (
	"fmt"
	"github.com/go-sql-driver/mysql"
	"github.com/jinzhu/gorm"
	"golang.52tt.com/pkg/log"
	"strings"
	"time"
)

// 奖励送礼记录表
type MagicSpiritAwardLog struct {
	Id              uint64    `gorm:"primary_key;AUTO_INCREMENT"`
	OrderId         string    `gorm:"unique_index:order_idx"`
	MagicOrderId    string    `gorm:"index:magic_order_idx"`
	MagicId         uint32    `gorm:"index:magic_id_idx" sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0"`
	MagicTotalPrice uint32    `sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0"`
	ChannelId       uint32    `gorm:"index:cid_idx" sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0"`
	FromUid         uint32    `gorm:"index:uid_idx" sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0"`
	ToUid           uint32    `gorm:"index:to_uid_idx" sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0"`
	GiftId          uint32    `gorm:"index:gift_id_idx" sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0"`
	Num             uint32    `sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0"`
	GiftTotalPrice  uint32    `sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0"`
	TBeanTimeStr    string    `gorm:"index:tbean_time_idx"`
	TBeanDealToken  string    `sql:"type:varchar(1024) DEFAULT ''"`
	OutsideTime     time.Time `gorm:"index:outside_ts_idx" sql:"type:TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP"`
	CreateTime      time.Time `sql:"type:TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP"`
	UpdateTime      time.Time `sql:"type:TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`
	AwardTime       time.Time `gorm:"index:award_ts_idx" sql:"type:TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP"`
	IsDone          bool
	Source          uint32 `gorm:"index:source_idx" sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0"`

	BreakingNewsId uint32 `sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0"`
}

// 增加全服id字段
// alter table tbl_magic_spirit_award_202506 add column breaking_news_id int(10) unsigned not null default 0;

func (t *MagicSpiritAwardLog) TableName() string {
	return fmt.Sprintf("tbl_magic_spirit_award_%04d%02d", t.OutsideTime.Year(), t.OutsideTime.Month())
}

func (s *Store) RecordMagicSpiritAwardLogs(tx *gorm.DB, list []*MagicSpiritAwardLog) error {
	if len(list) == 0 {
		return nil
	}

	values := make([]interface{}, 0)
	strList := make([]string, 0)
	for _, info := range list {
		strList = append(strList, "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")
		values = append(values, info.OrderId, info.MagicOrderId, info.MagicId, info.MagicTotalPrice,
			info.ChannelId, info.FromUid, info.ToUid,
			info.GiftId, info.Num, info.GiftTotalPrice, info.TBeanTimeStr, info.TBeanDealToken,
			info.OutsideTime, info.AwardTime, info.IsDone, info.Source, info.BreakingNewsId)
	}

	valStr := strings.Join(strList, ",")
	insertSql := fmt.Sprintf("insert into %s ("+
		"order_id, magic_order_id, magic_id, magic_total_price, "+
		"channel_id, from_uid, to_uid,"+
		"gift_id, num, gift_total_price, t_bean_time_str, t_bean_deal_token, "+
		"outside_time, award_time, is_done, source, breaking_news_id) values %s", list[0].TableName(), valStr)

	err := s.getDb(tx).Exec(insertSql, values...).Error
	if err != nil {
		mysqlErr, ok := err.(*mysql.MySQLError)
		if !ok {
			log.Errorf("RecordMagicSpiritAwardLogs fail to Exec. list:%+v, err:%v", list, err)
			return err
		}

		// 记录已存在
		if mysqlErr.Number == 1062 {
			log.Debugf("RecordMagicSpiritAwardLogs fail. list:%+v, err:%v", list, err)
			return nil
		}

		// 不是表不存在的错误
		if mysqlErr.Number != 1146 {
			log.Errorf("RecordMagicSpiritAwardLogs fail. list:%+v, err:%v", list, err)
			return err
		}

		// 表不存在建表
		err = s.getDb(tx).CreateTable(list[0]).Error
		if err != nil {
			log.Errorf("RecordMagicSpiritAwardLogs fail to CreateTable.list:%+v, err:%v", list, err)
			return err
		}

		// 重新插入
		err = s.getDb(tx).Exec(insertSql, values...).Error
		if err != nil {
			log.Errorf("RecordMagicSpiritAwardLogs fail to Exec.list:%+v, err:%v", list, err)
			return err
		}
	}

	log.Infof("RecordMagicSpiritAwardLogs list:%+v", list)
	return nil
}

func (s *Store) RecordMagicSpiritAwardLog(tx *gorm.DB, info *MagicSpiritAwardLog) error {
	err := s.getDb(tx).Create(info).Error
	if err != nil {
		mysqlErr, ok := err.(*mysql.MySQLError)
		if !ok {
			log.Errorf("RecordMagicSpiritAwardLogs fail. order:%+v, err:%v", info, err)
			return err
		}

		// 记录已存在
		if mysqlErr.Number == 1062 {
			log.Debugf("RecordMagicSpiritAwardLogs fail 1062. order:%+v, err:%v", info, err)
			return nil
		}

		// 不是表不存在的错误
		if mysqlErr.Number != 1146 {
			log.Errorf("RecordMagicSpiritAwardLogs fail 1146. order:%+v, err:%v", info, err)
			return err
		}

		// 表不存在建表
		err = s.getDb(tx).CreateTable(info).Error
		if err != nil {
			log.Errorf("RecordMagicSpiritAwardLogs fail to CreateTable.order:%+v, err:%v", info, err)
			return err
		}

		// 重新插入
		err = s.getDb(tx).Create(info).Error
		if err != nil {
			log.Errorf("RecordMagicSpiritAwardLogs fail to Create.order:%+v, err:%v", info, err)
			return err
		}
	}

	log.Infof("RecordMagicSpiritAwardLogs info:%+v", info)
	return nil
}

func (s *Store) GetMagicSpiritAwardLogs(tx *gorm.DB, beginTime, endTime time.Time, limit uint32) ([]*MagicSpiritAwardLog, error) {
	list := make([]*MagicSpiritAwardLog, 0)

	err := s.getDb(tx).Model(&MagicSpiritAwardLog{OutsideTime: endTime}).Select("*").
		Where("outside_time >= ? and outside_time <= ?", beginTime, endTime).
		Limit(limit).Scan(&list).Error

	if err != nil {
		mysqlErr, ok := err.(*mysql.MySQLError)
		if !ok || mysqlErr.Number != 1146 {
			log.Errorf("GetMagicSpiritAwardLogs fail. err:%v", err)
			return list, err
		}
	}

	return list, nil
}

func (s *Store) GetMagicSpiritAwardLog(tx *gorm.DB, orderId string, queryMonthTime time.Time) (*MagicSpiritAwardLog, bool, error) {
	order := &MagicSpiritAwardLog{OutsideTime: queryMonthTime}

	err := s.getDb(tx).First(order, "order_id=?", orderId).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return order, false, nil
		}

		mysqlErr, ok := err.(*mysql.MySQLError)
		if ok && mysqlErr.Number == 1146 {
			return order, false, nil
		}

		log.Errorf("GetMagicSpiritAwardLog fail. orderId:%v, err:%v", orderId, err)
		return order, false, err
	}

	return order, true, nil
}

func (s *Store) UpdateMagicSpiritAwardDone(tx *gorm.DB, orderId string, queryMonthTime, awardTime time.Time) (bool, error) {
	r := s.getDb(tx).Model(&MagicSpiritAwardLog{OutsideTime: queryMonthTime}).
		Where("order_id=? and is_done=?", orderId, false).
		Update(map[string]interface{}{"award_time": awardTime, "is_done": true})

	if r.Error != nil {
		log.Errorf("UpdateMagicSpiritAwardDone fail to Update. orderId:%s, queryMonthTime:%v, err:%v",
			orderId, queryMonthTime, r.Error)
		return false, r.Error
	}

	if r.RowsAffected == 0 {
		log.Debugf("UpdateMagicSpiritAwardDone fail to Update. orderId:%s, queryMonthTime:%v, err:RowsAffected == 0",
			orderId, queryMonthTime, r.Error)
		return false, nil
	}

	log.Infof("UpdateMagicSpiritAwardDone orderId:%s, awardTime:%v, queryMonthTime:%v", orderId, awardTime, queryMonthTime)
	return true, nil
}

func (s *Store) UpdateMagicSpiritAwardTBeanTime(tx *gorm.DB, magicOrderId, tBeanTime, dealToken string, queryMonthTime time.Time) (bool, error) {
	r := s.getDb(tx).Model(&MagicSpiritAwardLog{OutsideTime: queryMonthTime}).
		Where("magic_order_id=?", magicOrderId).
		Update(map[string]interface{}{"t_bean_time_str": tBeanTime, "t_bean_deal_token": dealToken})

	if r.Error != nil {
		log.Errorf("UpdateMagicSpiritAwardTBeanTime fail to Update. orderId:%+v, queryMonthTime:%v, tBeanTime:%v, err:%v",
			magicOrderId, queryMonthTime, tBeanTime, r.Error)
		return false, r.Error
	}

	if r.RowsAffected == 0 {
		log.Debugf("UpdateMagicSpiritAwardTBeanTime fail to Update. orderId:%+v, queryMonthTime:%v, tBeanTime:%v, err:RowsAffected == 0",
			magicOrderId, queryMonthTime, tBeanTime)
		return false, nil
	}

	log.Infof("UpdateMagicSpiritAwardTBeanTime orderId:%+v, queryMonthTime:%v, tBeanTime:%v, dealToken:%s",
		magicOrderId, queryMonthTime, tBeanTime, dealToken)
	return true, nil
}

func (s *Store) GetAwardTotalInfo(queryMonthTime, beginTime, endTime time.Time) (*TotalInfo, error) {
	out := &TotalInfo{}

	err := s.readonlyDb.Model(&MagicSpiritAwardLog{OutsideTime: queryMonthTime}).
		Select("count(1) as order_cnt, sum(gift_total_price) as total_price, sum(num) as total_num").
		Where("award_time >= ? and award_time < ? ",
			beginTime, endTime).Scan(out).Error

	if err != nil {
		mysqlErr, ok := err.(*mysql.MySQLError)
		if !ok || mysqlErr.Number != 1146 {
			log.Errorf("GetAwardTotalInfo fail. queryMonthTime:%v, err:%v", queryMonthTime, err)
			return out, err
		}
	}

	return out, nil
}

func (s *Store) GetMagicAwardOrderIdList(queryTime, begin, end time.Time) ([]string, error) {
	list := make([]string, 0)

	rows, err := s.readonlyDb.Model(&MagicSpiritAwardLog{OutsideTime: queryTime}).
		Select("order_id").
		Where("award_time >= ? and award_time < ? ", begin, end).Rows()
	if err != nil {
		log.Errorf("GetMagicAwardOrderIdList fail. begin:%v, end:%v, err:%v", begin, end, err)
		return list, err
	}
	defer rows.Close()

	if rows.Err() != nil {
		log.Errorf("GetMagicConsumeOrderIdList fail. begin:%v, end:%v, err:%v", begin, end, err)
		// 异味处理
	}

	for rows.Next() {
		orderId := ""
		err = rows.Scan(&orderId)
		if err == nil {
			list = append(list, orderId)
		}
	}

	return list, nil
}

type Statistics struct {
	MagicTotalPrice int64 `db:"magic_total_price"`
	GiftTotalPrice  int64 `db:"gift_total_price"`
	ConsumeCnt      int64 `db:"consume_cnt"`
	TotalNum        int64 `db:"total_num"`
}

func (s *Store) GetAwardStatistics(beginTime, endTime time.Time) (*Statistics, error) {
	out := &Statistics{}

	err := s.readonlyDb.Model(&MagicSpiritAwardLog{OutsideTime: beginTime}).
		Select("count(distinct from_uid) as consume_cnt, sum(gift_total_price) as gift_total_price, "+
			"sum(magic_total_price) as magic_total_price, sum(num) as total_num").
		Where("outside_time >= ? and outside_time < ? ", beginTime, endTime).Scan(out).Error

	if err != nil {
		mysqlErr, ok := err.(*mysql.MySQLError)
		if !ok || mysqlErr.Number != 1146 {
			log.Errorf("GetAwardStatistics fail. queryMonthTime:%v, err:%v", beginTime, err)
			return out, err
		}
	}

	return out, nil
}

type SumInfo struct {
	Cnt        uint64 `db:"cnt"`
	ConsumeCnt uint64 `db:"consume_cnt"`
}

func (s *Store) GetAwardCntSumById(giftIdList []uint32, beginTime, endTime time.Time) (*SumInfo, error) {
	out := &SumInfo{}
	if len(giftIdList) == 0 {
		return out, nil
	}

	err := s.readonlyDb.Model(&MagicSpiritAwardLog{OutsideTime: beginTime}).
		Select("sum(num) as cnt, count(distinct from_uid) as consume_cnt").
		Where("outside_time >= ? and outside_time < ? and gift_id in (?)", beginTime, endTime, giftIdList).Scan(out).Error

	if err != nil {
		mysqlErr, ok := err.(*mysql.MySQLError)
		if !ok || mysqlErr.Number != 1146 {
			log.Errorf("GetAwardCntSumById fail. queryMonthTime:%v, err:%v", beginTime, err)
			return out, err
		}
	}

	return out, nil
}
