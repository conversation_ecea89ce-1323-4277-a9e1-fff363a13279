package mysql

import (
	"context"
	"fmt"
	"strings"
	"time"

	"golang.52tt.com/pkg/log"
	"github.com/jinzhu/gorm"
	"errors"
)

const (
	PondTableName = "magic_spirit_pond"
)

type MagicSpiritPond struct {
	ItemId         uint32    `gorm:"primary_key;autoIncrement"`
	MagicSpiritId  uint32    `gorm:"index:magic_id_idx;type:int(10) unsigned not null;default:0;comment:'礼物id'"`
	Weight         uint32    `gorm:"type:int(10) unsigned not null;default:0;comment:'礼物权重'"`
	PresentId      uint32    `gorm:"type:int(10) unsigned not null;default:0;comment:'原礼物id'"`
	Price          uint32    `gorm:"type:int(10) unsigned not null;default:0;comment:'原礼物价值'"`
	PrizeLevel     uint32    `gorm:"type:int(10) unsigned not null;default:0;comment:'中奖特效等级'"`
	BreakingNewsId uint32    `gorm:"type:int(10) unsigned not null;default:0;comment:'全服公告id'"`
	CreateTime     time.Time `gorm:"type:timestamp not null;default:current_timestamp;"`
	UpdateTime     time.Time `gorm:"type:timestamp not null;default:current_timestamp;comment:'修改时间'"`
	BeginTime      time.Time `gorm:"type:timestamp not null;default:current_timestamp;comment:'生效时间'"`
	EndTime        time.Time `gorm:"type:timestamp not null;default:'2038-01-01 00:00:00';comment:'失效时间'"`
	IsDel          uint32    `gorm:"type:tinyint(1) NOT NULL;default:0;comment:'已删除'"`
	// 礼物价值字段
}

/*
2022-10-14
alter table magic_spirit_pond add column update_time timestamp not null default current_timestamp comment "修改时间";
alter table magic_spirit_pond add column begin_time timestamp not null default current_timestamp comment "生效时间";
alter table magic_spirit_pond add column end_time timestamp not null default '2038-01-01 00:00:00' comment "失效时间";
alter table magic_spirit_pond add column is_del tinyint(1) NOT NULL DEFAULT '0';
ALTER TABLE magic_spirit_pond ADD COLUMN breaking_news_id INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '全服公告id' ;
alter table magic_spirit_pond add index idx_magic_present_begin(magic_spirit_id,present_id,begin_time,is_del);
*/

func (m *MagicSpiritPond) TableName() string {
	return "magic_spirit_pond"
}

// magic_pond op
func (s *Store) AddMagicSpiritPond(ctx context.Context, tx *gorm.DB, pond []*MagicSpiritPond) ([]uint32, error) {
	if len(pond) == 0 {
		return nil, nil
	}

	if tx == nil {
		return nil, errors.New("tx is nil")
	}

	var err error

	delMagicIdList := make([]uint32, 0)
	delMagicIdMap := make(map[uint32]struct{})
	for _, item := range pond {
		if _, ok := delMagicIdMap[item.MagicSpiritId]; !ok {
			delMagicIdMap[item.MagicSpiritId] = struct{}{}
			delMagicIdList = append(delMagicIdList, item.MagicSpiritId)
		}
	}

	// 删除旧数据
	//magicSpiritId := pond[0].MagicSpiritId
	err = tx.Table(PondTableName).Where("magic_spirit_id in (?)", delMagicIdList).Delete(&MagicSpiritPond{}).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "AddMagicSpiritPond remove old data error: %v", err)
		return nil, err
	}

	for _, item := range pond {
		err = tx.Table(PondTableName).Create(item).Error
		if err != nil {
			log.ErrorWithCtx(ctx, "AddMagicSpiritPond error: %s", err)
			return nil, err
		}
	}

	// 标记已设置礼物池
	err = tx.Model(&MagicSpirit{}).Where("magic_spirit_id in (?)", delMagicIdList).
		Update("has_pond", true).
		Update("update_time", time.Now()).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "MarkIsSetPond error: %v", err)
		return nil, err
	}

	ids := make([]uint32, 0, 8)
	for _, item := range pond {
		ids = append(ids, item.ItemId)
	}
	log.InfoWithCtx(ctx, "AddMagicSpiritPond success, magic_id_list: %v, item_id: %v", delMagicIdList, ids)
	return ids, nil
}

func (s *Store) GetMagicSpiritPond(ctx context.Context, magicSpiritId uint32) ([]*MagicSpiritPond, error) {
	mp := make([]*MagicSpiritPond, 0)

	nowTime := time.Now()
	err := s.db.Table(PondTableName).Model(&MagicSpiritPond{}).
		Where("magic_spirit_id= ? and end_time>? and begin_time<=? and is_del=0", magicSpiritId, nowTime, nowTime).
		Find(&mp).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMagicSpiritPond error: %v", err)
		return nil, err
	}

	return mp, nil
}

func (s *Store) DelMagicSpiritPond(ctx context.Context, magicId uint32, itemIds []uint32) error {

	query := "update magic_spirit_pond set is_del = 1 where magic_spirit_id=? and item_id in(?)"

	err := s.db.Table(PondTableName).Exec(query, magicId, genParamJoinStr(itemIds)).Error

	if err != nil {
		log.ErrorWithCtx(ctx, "DelMagicSpiritPond err: %v", err)
		return err
	}

	return nil
}

func genParamJoinStr(list []uint32) string {
	strList := make([]string, 0, len(list))
	for _, i := range list {
		strList = append(strList, fmt.Sprint(i))
	}

	return strings.Join(strList, ",")
}

func (s *Store) GetNormalPondMaxUpdateTime(ctx context.Context) (int64, error) {
	var maxUpdateTime int64

	// 使用 Row() 获取单个字段
	row := s.db.Table(PondTableName).
		Select("UNIX_TIMESTAMP(MAX(update_time))").
		Row()

	err := row.Scan(&maxUpdateTime)
	if err != nil {
		return 0, err
	}
	return maxUpdateTime, nil
}
