package mysql

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strings"
	"time"

	"golang.52tt.com/pkg/log"
)

type MagicSpiritPond struct {
	ItemId         uint32    `gorm:"primary_key;autoIncrement"`
	MagicSpiritId  uint32    `gorm:"index:magic_id_idx;type:int(10) unsigned not null;default:0;comment:'礼物id'"`
	Weight         uint32    `gorm:"type:int(10) unsigned not null;default:0;comment:'礼物权重'"`
	PresentId      uint32    `gorm:"type:int(10) unsigned not null;default:0;comment:'原礼物id'"`
	Price          uint32    `gorm:"type:int(10) unsigned not null;default:0;comment:'原礼物价值'"`
	PrizeLevel     uint32    `gorm:"type:int(10) unsigned not null;default:0;comment:'中奖特效等级'"`
	BreakingNewsId uint32    `gorm:"type:int(10) unsigned not null;default:0;comment:'全服公告id'"`
	CreateTime     time.Time `gorm:"type:timestamp not null;default:current_timestamp;"`
	UpdateTime     time.Time `gorm:"type:timestamp not null;default:current_timestamp;comment:'修改时间'"`
	BeginTime      time.Time `gorm:"type:timestamp not null;default:current_timestamp;comment:'生效时间'"`
	EndTime        time.Time `gorm:"type:timestamp not null;default:'2038-01-01 00:00:00';comment:'失效时间'"`
	IsDel          uint32    `gorm:"type:tinyint(1) NOT NULL;default:0;comment:'已删除'"`
	// 礼物价值字段
}

/*
2022-10-14
alter table magic_spirit_pond add column update_time timestamp not null default current_timestamp comment "修改时间";
alter table magic_spirit_pond add column begin_time timestamp not null default current_timestamp comment "生效时间";
alter table magic_spirit_pond add column end_time timestamp not null default '2038-01-01 00:00:00' comment "失效时间";
alter table magic_spirit_pond add column is_del tinyint(1) NOT NULL DEFAULT '0';
ALTER TABLE magic_spirit_pond ADD COLUMN breaking_news_id INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '全服公告id' ;
alter table magic_spirit_pond add index idx_magic_present_begin(magic_spirit_id,present_id,begin_time,is_del);
*/

type PondVersion struct {
	UpdateVersion int64 `json:"update_version"`
}

func (m *MagicSpiritPond) TableName() string {
	return "magic_spirit_pond"
}

// magic_pond op
func (s *Store) AddMagicSpiritPond(ctx context.Context, pond []*MagicSpiritPond) ([]uint32, error) {
	if len(pond) == 0 {
		return nil, nil
	}
	tx := s.db.BeginTx(ctx, &sql.TxOptions{})
	var err error

	// 删除旧数据
	magicSpiritId := pond[0].MagicSpiritId
	err = tx.Where("magic_spirit_id=?", magicSpiritId).Delete(&MagicSpiritPond{}).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "AddMagicSpiritPond remove old data error: %v", err)
		tx.Rollback()
		return nil, err
	}

	for _, item := range pond {
		err = tx.Create(item).Error
		if err != nil {
			log.ErrorWithCtx(ctx, "AddMagicSpiritPond error: %s", err)
			tx.Rollback()
			return nil, err
		}
	}

	// 标记已设置礼物池
	err = tx.Model(&MagicSpirit{}).Where("magic_spirit_id = ?", magicSpiritId).
		Update("has_pond", true).
		Update("update_time", time.Now()).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "MarkIsSetPond error: %v", err)
		tx.Rollback()
		return nil, err
	}

	tx.Commit()

	ids := make([]uint32, 0, 8)
	for _, item := range pond {
		ids = append(ids, item.ItemId)
	}
	return ids, nil
}

// 新配置奖池
func (s *Store) AddMagicSpiritPondV2(ctx context.Context, pond []*MagicSpiritPond, endTime time.Time) ([]uint32, error) {
	ids := make([]uint32, 0, 8)
	beginTime := pond[0].BeginTime
	nowTime := time.Now()

	if len(pond) == 0 {
		log.Errorf("SetPrizeList fail len(PrizeList) == 0")
		return ids, errors.New("len(PrizeList) == 0")
	}

	tx := s.db.BeginTx(ctx, &sql.TxOptions{})
	var err error
	// 把其他未过期的配置的下架时间更新为新配置的上架时间，这里的create_time因为作为展示的顺序条件，所以这里保持不变
	query := "update magic_spirit_pond set end_time=? where magic_spirit_id=? and end_time>? and is_del=0"
	err = s.db.Exec(query, beginTime, pond[0].MagicSpiritId, nowTime).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "Update magic_spirit endTime error: %v,pond:%v", err, pond)
		tx.Rollback()
		return nil, err
	}

	query = "update magic_spirit_pond set end_time=begin_time where magic_spirit_id=? and begin_time>?"
	err = s.db.Exec(query, pond[0].MagicSpiritId, nowTime).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "Update magic_spirit endTime error: %v,pond:%v", err, pond)
		tx.Rollback()
		return nil, err
	}

	query2 := "replace into magic_spirit_pond (magic_spirit_id,weight,present_id,price,prize_level,breaking_news_id,begin_time,end_time) values %s"
	placeholder := make([]string, 0, len(pond))
	params := make([]interface{}, 0)
	for _, v := range pond {
		placeholder = append(placeholder, "(?,?,?,?,?,?,?,?)")
		params = append(params, v.MagicSpiritId, v.Weight, v.PresentId, v.Price, v.PrizeLevel, v.BreakingNewsId, beginTime, v.EndTime)
	}
	query2 = fmt.Sprintf(query2, strings.Join(placeholder, ","))

	err = s.db.Exec(query2, params...).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "replace into magic_spirit error: %v,pond:%v", err, pond)
		tx.Rollback()
		return nil, err
	}

	// 标记已设置礼物池
	err = tx.Model(&MagicSpirit{}).Where("magic_spirit_id = ?", pond[0].MagicSpiritId).
		Update("has_pond", true).
		Update("update_time", time.Now()).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "MarkIsSetPond error: %v", err)
		tx.Rollback()
		return nil, err
	}

	tx.Commit()

	return ids, nil
}

func (s *Store) GetMagicSpiritPond(ctx context.Context, magicSpiritId uint32, effectTime time.Time) ([]*MagicSpiritPond, error) {
	mp := make([]*MagicSpiritPond, 0)

	nowTime := time.Now()
	err := s.db.Model(&MagicSpiritPond{}).
		Where("magic_spirit_id= ? and end_time>? and begin_time<=? and is_del=0", magicSpiritId, effectTime, nowTime).
		Find(&mp).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMagicSpiritPond error: %v", err)
		return nil, err
	}

	return mp, nil
}

func (s *Store) GetMagicSpiritPondTmp(ctx context.Context) ([]*MagicSpiritPond, error) {
	mp := make([]*MagicSpiritPond, 0)

	nowTime := time.Now()
	endTime := time.Date(2038, 01, 01, 0, 0, 0, 0, time.Local)
	err := s.db.Model(&MagicSpiritPond{}).
		Where("begin_time>? and end_time=? and is_del=0", nowTime, endTime).
		Find(&mp).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMagicSpiritPond error: %v", err)
		return nil, err
	}

	return mp, nil
}

func (s *Store) GetMagicPondUpdateVersion(ctx context.Context, now time.Time) (int64, error) {
	query := "SELECT IFNULL(max(UNIX_TIMESTAMP(update_time)),0) as update_version FROM magic_spirit_pond WHERE end_time>? AND begin_time<=? AND " +
		"is_del=0"
	val := &PondVersion{UpdateVersion: int64(0)}
	err := s.db.Raw(query, now, now).Scan(val).Error
	log.Infof("%+v", val)
	if err != nil {
		if err == sql.ErrNoRows {
			return 0, nil
		}
		log.ErrorWithCtx(ctx, "GetMagicPondUpdateVersion err: %v", err)
		return 0, err
	}

	return val.UpdateVersion, nil
}

func (s *Store) DelMagicSpiritPond(ctx context.Context, magicId uint32, itemIds []uint32) error {

	query := "update magic_spirit_pond set is_del = 1 where magic_spirit_id=? and item_id in(?)"

	err := s.db.Exec(query, magicId, genParamJoinStr(itemIds)).Error

	if err != nil {
		log.ErrorWithCtx(ctx, "DelMagicSpiritPond err: %v", err)
		return err
	}

	return nil
}

func genParamJoinStr(list []uint32) string {
	strList := make([]string, 0, len(list))
	for _, i := range list {
		strList = append(strList, fmt.Sprint(i))
	}

	return strings.Join(strList, ",")
}
