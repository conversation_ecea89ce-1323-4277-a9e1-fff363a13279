package mysql

import (
	"context"
	"database/sql"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/jinzhu/gorm"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
)

//go:generate mockgen -destination=../mocks/mock_store.go -package=mocks golang.52tt.com/services/magic-spirit/internal/mysql IStore

type Store struct {
	db, readonlyDb *gorm.DB
}

func NewMysql(c, rc *config.MysqlConfig) (*Store, error) {
	mysqlDb, err := gorm.Open("mysql", c.ConnectionString())
	if err != nil {
		log.Errorf("Failed to create mysql %v", err)
		return nil, err
	}

	mysqlReadonlyDb, err := gorm.Open("mysql", rc.ConnectionString())
	if err != nil {
		log.Errorf("Failed to create mysql %v", err)
		return nil, err
	}

	if c.MaxIdleConns > 0 {
		mysqlDb.DB().SetMaxIdleConns(c.MaxIdleConns)
		mysqlReadonlyDb.DB().SetMaxIdleConns(c.MaxIdleConns)
	}
	if c.MaxOpenConns > 0 {
		mysqlDb.DB().SetMaxOpenConns(c.MaxOpenConns)
		mysqlReadonlyDb.DB().SetMaxOpenConns(c.MaxOpenConns)
	}
	mysqlDb.DB().SetConnMaxLifetime(time.Minute * 5)
	mysqlReadonlyDb.DB().SetConnMaxLifetime(time.Minute * 5)

	st := &Store{
		db:         mysqlDb,
		readonlyDb: mysqlReadonlyDb,
	}

	st.CreateTable()

	return st, nil
}

func (s *Store) getDb(tx *gorm.DB) *gorm.DB {
	if tx == nil {
		return s.db
	}

	return tx
}

func (s *Store) CreateTable() {
	if !s.db.HasTable(&MagicSpirit{}) {
		s.db.CreateTable(&MagicSpirit{})
	}

	if !s.db.HasTable(&MagicSpiritPond{}) {
		s.db.CreateTable(&MagicSpiritPond{})
	}

	if !s.db.HasTable(&MagicSpiritCommonConf{}) {
		s.db.CreateTable(&MagicSpiritCommonConf{})
	}

	if !s.db.HasTable(&MagicSpiritBlacklist{}) {
		s.db.CreateTable(&MagicSpiritBlacklist{})
	}

	if !s.db.HasTable(&ReconcileDataLog{}) {
		s.db.CreateTable(&ReconcileDataLog{})
	}

	err := s.db.Exec(createMagicSpiritTmpTbl).Error
	if err != nil {
		log.Infof("CreateTable fail at createMagicSpiritTmpTbl,err:%v", err.Error())
	}
}

func (s *Store) Transaction(ctx context.Context, f func(tx *gorm.DB) error) error {
	tx := s.db.BeginTx(ctx, &sql.TxOptions{})

	err := f(tx)
	if err != nil {
		log.Errorf("Transaction fail err %v", err)
		_ = tx.Rollback()
		return err
	}

	return tx.Commit().Error
}
