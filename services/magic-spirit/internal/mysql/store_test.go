package mysql

import (
	"context"
	"fmt"
	"github.com/jinzhu/gorm"
	"golang.52tt.com/pkg/config"
	"testing"
	"time"

	pb "golang.52tt.com/protocol/services/apicentergo"
	"golang.52tt.com/services/magic-spirit/internal/conf"
	"k8s.io/apimachinery/pkg/util/rand"
)

var mysqlStore *Store

func init() {
	sc := &conf.ServiceConfigT{
		MysqlConfig: &config.MysqlConfig{
			Host:     "*************",
			Port:     3306,
			UserName: "godman",
			Password: "thegodofman",
			Database: "appsvr",
			Protocol: "tcp",
			Charset:  "utf8mb4",
		},
	}

	mysqlStore, _ = NewMysql(sc.GetMysqlConfig(), sc.GetMysqlConfig())
}

func TestStore_CreateTable(t *testing.T) {
	mysqlStore.CreateTable()
}

func TestStore_AddMagicSpirit(t *testing.T) {
	_, err := mysqlStore.AddMagicSpirit(context.Background(), &MagicSpirit{
		Name:             rand.String(3),
		IconUrl:          "http://" + rand.String(3) + ".com",
		Price:            2,
		Rank:             1,
		EffectBegin:      0,
		EffectEnd:        0,
		DescribeImageUrl: "",
		Describe:         "",
		JuniorLighting:   0,
		MiddleLighting:   0,
        ShowEffectEnd: true,
        ActName: "test",
        ActBeginTime: time.Now(),
        ActEndTime: time.Now(),
        ActImageUrl: "http://test.com1",
        ActJumpUrlHcAndroid: "http://test.com2",
        ActJumpUrlHcIos: "http://test.com3",
        ActJumpUrlMikeAndroid: "http://test.com4",
        ActJumpUrlMikeIos: "http://test.com5",
	})
	t.Log(err)
}

func TestStore_DelMagicSpirit(t *testing.T) {
	_ = mysqlStore.DelMagicSpirit(context.Background(), []uint32{5, 6})
}

func TestStore_GetMagicSpirit(t *testing.T) {
	rs, err := mysqlStore.GetMagicSpirit(context.Background())
	if err != nil {
		t.Error(err)
	}
	t.Log(err, rs)
}

func TestStore_GetMagicSpiritByIds(t *testing.T) {
	rs, err := mysqlStore.GetMagicSpiritByIds(context.Background(), []uint32{1, 2, 3})
	t.Log(err)

	t.Logf("%+v", rs)
}

func TestStore_GetMagicSpiritById(t *testing.T) {
	rs, err := mysqlStore.GetMagicSpiritById(context.Background(), uint32(1))
	t.Log(err)

	t.Logf("%+v", rs)
}

func TestStore_UpdateMagicSpirit(t *testing.T) {
	err := mysqlStore.UpdateMagicSpirit(context.Background(), &MagicSpirit{
		MagicSpiritId:    1,
		Name:             "",
		IconUrl:          "",
		Price:            123,
		Rank:             123,
		EffectBegin:      123,
		EffectEnd:        123,
		DescribeImageUrl: "123",
		Describe:         "123",
		JuniorLighting:   123,
		MiddleLighting:   123,
		CreateTime:       time.Time{},
		UpdateTime:       time.Time{},
	})
	t.Log(err)
}

func TestStore_AddMagicSpiritPond(t *testing.T) {
	magicPondItems := []*MagicSpiritPond{
		{
			MagicSpiritId: 1,
			Weight:        100,
		},
		{
			MagicSpiritId: 1,
			Weight:        300,
		},
		{
			MagicSpiritId: 1,
			Weight:        500,
		},
		{
			MagicSpiritId: 1,
			Weight:        200,
		},
	}

	_, err := mysqlStore.AddMagicSpiritPond(context.Background(), magicPondItems)
	t.Log(err)
}

func TestStore_GetMagicSpiritPond(t *testing.T) {
	rs, err := mysqlStore.GetMagicSpiritPond(context.Background(), 1, time.Now())
	t.Log(err)

	for _, item := range rs {
		println(item.Weight)
	}
}

func TestStore_DelMagicSpiritPond(t *testing.T) {
	err := mysqlStore.DelMagicSpiritPond(context.Background(), 1, []uint32{43, 45})
	t.Log(err)
}

func TestStore_SetCommonConf(t *testing.T) {
	err := mysqlStore.SetCommonConf(context.Background(), []*MagicSpiritCommonConf{
		{
			ConfId:    uint32(pb.CommonConfType_DAILY_SEND_MONEY_LIMIT),
			Value:     "555",
			ValueType: 1,
		},
		{
			ConfId:    uint32(pb.CommonConfType_DAILY_PREVENT_EXCHANGE_LIMIT),
			Value:     "105",
			ValueType: 1,
		},
		{
			ConfId:    uint32(pb.CommonConfType_PER_ORDER_COUNT_LIMIT),
			Value:     "109",
			ValueType: 1,
		},
	})
	t.Log(err)
}

func TestStore_GetCommonConf(t *testing.T) {
	list, err := mysqlStore.GetCommonConf(context.Background())
	t.Log(err)

	for _, info := range list {
		t.Logf("%+v", info)
	}
}

func TestStore_AddBlacklist(t *testing.T) {
	_, err := mysqlStore.AddBlacklist(context.Background(), []*MagicSpiritBlacklist{
		{
			RoomId:    uint32(rand.Intn(999)),
			RoomName:  rand.String(5),
			Ttid:      uint32(rand.Int63nRange(100000, 999999)),
			RoomOwner: rand.String(3),
		},
	})
	t.Log(err)
}

func TestStore_GetBlacklist(t *testing.T) {
	total, data, err := mysqlStore.GetBlacklist(context.Background(), 3, 1, 1)
	t.Log(err)

	println(total)
	for _, item := range data {
		fmt.Printf("%+v", item)
		println()
	}
}

func TestStore_GetBlacklistCIds(t *testing.T) {
	_, err := mysqlStore.GetBlacklistCIds(context.Background())
	t.Log(err)
}

func TestStore_DelBlacklist(t *testing.T) {
	err := mysqlStore.DelBlacklist(context.Background(), []uint32{1, 2, 3, 4, 7})
	t.Log(err)
}

func TestStore_CreateMagicSpiritOrder(t *testing.T) {
	now := time.Now().AddDate(0, 0, 1)
	t.Log(mysqlStore.CreateMagicSpiritOrder(nil, &MagicSpiritOrder{
		OrderId:      "test1",
		Status:       0,
		ChannelId:    1,
		Uid:          10000,
		MagicId:      1,
		Num:          2,
		Price:        20,
		OutsideTime:  now,
		CreateTime:   now,
		UpdateTime:   now,
		TBeanTimeStr: "",
	}))
}

func TestStore_GetMagicSpiritOrder(t *testing.T) {
	order, exist, err := mysqlStore.GetMagicSpiritOrder(nil, "test1", time.Now())
	t.Log(exist, err)
	t.Logf("%+v", order)
}

func TestStore_GetChannelOrderListByStatus(t *testing.T) {
	list, err := mysqlStore.GetChannelOrderListByStatus(nil, 1, 0, time.Now())
	t.Log(err)
	for _, info := range list {
		t.Logf("%+v", info)
	}
}

func TestStore_UpdateMagicSpiritOrderStatus(t *testing.T) {
	t.Log(mysqlStore.UpdateMagicSpiritOrderStatus(nil, "test1", time.Now(), []uint32{0}, 1))
}

func TestStore_UpdateMagicSpiritOrderTBeanTime(t *testing.T) {
	t.Log(mysqlStore.UpdateMagicSpiritOrderTBeanTime(nil, "test1", time.Now().Format("2006-01-02 15:04:05"), time.Now()))
}

func TestStore_GetOrderTotalInfo(t *testing.T) {
	t.Log(mysqlStore.GetOrderTotalInfo(time.Now(), time.Now().Add(time.Minute), time.Now(), 0, 0))
}

func TestStore_GetMagicConsumeOrderIdList(t *testing.T) {
	t.Log(mysqlStore.GetMagicConsumeOrderIdList(time.Now(), time.Now().Add(time.Minute), time.Now(), 0, 0))
}

func TestStore_RecordMagicSpiritAwardLog(t *testing.T) {
	now := time.Now().AddDate(0, 0, 1)
	t.Log(mysqlStore.RecordMagicSpiritAwardLog(nil, &MagicSpiritAwardLog{
		OrderId:         "test1_1056",
		MagicOrderId:    "test1",
		MagicTotalPrice: 10,
		ChannelId:       1,
		FromUid:         10000,
		ToUid:           2000,
		MagicId:         1,
		Num:             2,
		GiftTotalPrice:  100,
		OutsideTime:     now,
		CreateTime:      now,
		UpdateTime:      now,
		TBeanTimeStr:    "2021-12-04 14:17:19",
	}))

	t.Log(mysqlStore.RecordMagicSpiritAwardLog(nil, &MagicSpiritAwardLog{
		OrderId:      "test1_1056",
		MagicOrderId: "test1",
	}))
}

func TestStore_RecordMagicSpiritAwardLogs(t *testing.T) {
	now := time.Now()
	t.Log(mysqlStore.RecordMagicSpiritAwardLogs(nil, []*MagicSpiritAwardLog{{
		OrderId:         "test1_1060",
		MagicOrderId:    "test1",
		MagicTotalPrice: 10,
		ChannelId:       1,
		FromUid:         10000,
		ToUid:           2000,
		MagicId:         1,
		Num:             2,
		GiftTotalPrice:  100,
		OutsideTime:     now,
		CreateTime:      now,
		UpdateTime:      now,
		AwardTime:       now,
		TBeanTimeStr:    "2021-12-04 14:17:19",
	},
		{
			OrderId:         "test1_1059",
			MagicOrderId:    "test1",
			MagicTotalPrice: 10,
			ChannelId:       1,
			FromUid:         10000,
			ToUid:           2000,
			MagicId:         1,
			Num:             2,
			GiftTotalPrice:  100,
			OutsideTime:     now,
			CreateTime:      now,
			UpdateTime:      now,
			AwardTime:       now,
			TBeanTimeStr:    "2021-12-04 14:17:19",
		},
	}))
}

func TestStore_GetMagicSpiritAwardLog(t *testing.T) {
	t.Log(mysqlStore.GetMagicSpiritAwardLog(nil, "test1_1059", time.Now()))
}

func TestStore_GetMagicSpiritAwardLogs(t *testing.T) {
	list, err := mysqlStore.GetMagicSpiritAwardLogs(nil, time.Now().Add(-time.Hour), time.Now(), 5)
	t.Log(err)
	for _, info := range list {
		t.Logf("%+v", info)
	}
}

func TestStore_UpdateMagicSpiritAwardTBeanTime(t *testing.T) {
	t.Log(mysqlStore.UpdateMagicSpiritAwardTBeanTime(nil, "test1", time.Now().Format("2006-01-02 15:04:05"), "deal", time.Now()))
}

func TestStore_UpdateMagicSpiritAwardDone(t *testing.T) {
	t.Log(mysqlStore.UpdateMagicSpiritAwardDone(nil, "test1", time.Now(), time.Now()))
}

func TestStore_GetAwardTotalInfo(t *testing.T) {
	t.Log(mysqlStore.GetAwardTotalInfo(time.Now(), time.Now().Add(-time.Hour), time.Now()))
}

func TestStore_GetMagicAwardOrderIdList(t *testing.T) {
	t.Log(mysqlStore.GetMagicAwardOrderIdList(time.Now(), time.Now().Add(-time.Hour), time.Now()))
}

func TestStore_GetAwardStatistics(t *testing.T) {
	t.Log(mysqlStore.GetAwardStatistics(time.Now().Add(-time.Hour), time.Now()))
}

func TestStore_GetAwardCntSumById(t *testing.T) {
	t.Log(mysqlStore.GetAwardCntSumById([]uint32{0}, time.Now().Add(-time.Hour), time.Now()))
}

func TestStore_Transaction(t *testing.T) {
	t.Log(mysqlStore.Transaction(context.Background(), func(tx *gorm.DB) error {
		return nil
	}))
}

func TestStore_AddMagicSpiritTmp(t *testing.T) {
	info := &MagicSpiritTemporary{
		//Id:               0,
		MagicSpiritId:    1,
		Name:             "111",
		IconUrl:          "1111",
		Price:            100,
		Ranking:          100,
		EffectBegin:      1666062715,
		EffectEnd:        1666062915,
		DescribeImageUrl: "1111111",
		GiftDescribe:     "11111111",
		JuniorLighting:   1,
		MiddleLighting:   1,
		VfxResource:      "111111111",
		VfxResourceMd5:   "11111111",
		//IsDel:            0,
		BeginTime:  time.Now().Add(10 * time.Minute),
		UpdateFlag: 0,
        ShowEffectEnd: true,
        ActName: "test",
        ActBeginTime: time.Now(),
        ActEndTime: time.Now(),
        ActImageUrl: "http://test.com1",
        ActJumpUrlHcAndroid: "http://test.com2",
        ActJumpUrlHcIos: "http://test.com3",
        ActJumpUrlMikeAndroid: "http://test.com4",
        ActJumpUrlMikeIos: "http://test.com5",
	}
	t.Log(mysqlStore.AddMagicSpiritTmp(context.Background(), info))
}

func TestStore_AddMagicSpiritTmp2(t *testing.T) {
	info := &MagicSpiritTemporary{
		//Id:               0,
		MagicSpiritId:    2,
		Name:             "111",
		IconUrl:          "1111",
		Price:            100,
		Ranking:          100,
		EffectBegin:      1666062715,
		EffectEnd:        1666062915,
		DescribeImageUrl: "1111111",
		GiftDescribe:     "11111111",
		JuniorLighting:   1,
		MiddleLighting:   1,
		VfxResource:      "111111111",
		VfxResourceMd5:   "11111111",
		//IsDel:            0,
		BeginTime:  time.Now().Add(-10 * time.Minute),
		UpdateFlag: 0,
	}
	t.Log(mysqlStore.AddMagicSpiritTmp(context.Background(), info))
}

func TestStore_GetMagicSpiritTmpEffective(t *testing.T) {
	out, err := mysqlStore.GetMagicSpiritTmpEffective(context.Background(), time.Now())
	t.Log(err)
	for _, v := range out {
		t.Logf("%+v", v)
	}
}

func TestStore_SetMagicSpiritUpdateFlag(t *testing.T) {
	magicId := uint32(2)
	beginTime := time.Unix(1666064391, 0)

	t.Log(mysqlStore.SetMagicSpiritUpdateFlag(context.Background(), magicId, beginTime))
}

func TestStore_DelMagicSpiritTmp(t *testing.T) {
	t.Log(mysqlStore.DelMagicSpiritTmp(context.Background(), []uint32{2}))
}

func TestStore_AddMagicSpiritPondV2(t *testing.T) {

	ponds := []*MagicSpiritPond{
		{
			//ItemId:        0,
			MagicSpiritId: 1,
			Weight:        1,
			PresentId:     1,
			Price:         10,
			PrizeLevel:    1,
			BeginTime:     time.Unix(1666150791, 0),
			EndTime:       time.Date(2038, 01, 01, 0, 0, 0, 0, time.Local),
		},
		{
			//ItemId:        0,
			MagicSpiritId: 1,
			Weight:        1,
			PresentId:     2,
			Price:         10,
			PrizeLevel:    1,
			BeginTime:     time.Unix(1666150791, 0),
			EndTime:       time.Date(2038, 01, 01, 0, 0, 0, 0, time.Local),
		},
	}
	t.Log(mysqlStore.AddMagicSpiritPondV2(context.Background(), ponds, time.Now()))
}

func TestStore_AddMagicSpiritPondV22(t *testing.T) {
	ponds := []*MagicSpiritPond{
		{
			//ItemId:        0,
			MagicSpiritId: 2,
			Weight:        1,
			PresentId:     1,
			Price:         10,
			PrizeLevel:    1,
			BeginTime:     time.Unix(1666064391, 0),
			EndTime:       time.Date(2038, 01, 01, 0, 0, 0, 0, time.Local),
		},
		{
			//ItemId:        0,
			MagicSpiritId: 2,
			Weight:        1,
			PresentId:     2,
			Price:         10,
			PrizeLevel:    1,
			BeginTime:     time.Unix(1666064391, 0),
			EndTime:       time.Date(2038, 01, 01, 0, 0, 0, 0, time.Local),
		},
	}
	t.Log(mysqlStore.AddMagicSpiritPondV2(context.Background(), ponds, time.Now()))
}

func TestStore_GetMagicSpiritPond2(t *testing.T) {
	info, err := mysqlStore.GetMagicSpiritPond(context.Background(), uint32(2), time.Now())
	t.Log(err)
	for _, v := range info {
		t.Logf("%+v", v)
	}
}

func TestStore_GetMagicSpiritPondTmp(t *testing.T) {
	info, err := mysqlStore.GetMagicSpiritPondTmp(context.Background())
	t.Log(err)
	for _, v := range info {
		t.Logf("%+v", v)
	}
}

func TestStore_GetMagicPondUpdateVersion(t *testing.T) {
	t.Log(mysqlStore.GetMagicPondUpdateVersion(context.Background(), time.Now()))
}

func TestStore_GetMagicSpiritTmp(t *testing.T) {
	info, err := mysqlStore.GetMagicSpiritTmp(context.Background(), time.Now())
	t.Log(err)
	for _, v := range info {
		t.Logf("%+v", v)
	}
}

func TestStore_GetReconcileSumStats(t *testing.T) {
	list, err := mysqlStore.GetReconcileSumStats(time.Now().AddDate(0, 0, -1), time.Now())
	if err != nil {
		t.Error(err)
	}

	err = mysqlStore.RecordReconcileDataLogs(list)
	if err != nil {
		t.Error(err)
	}
}

func TestStore_BatchGetMagicSpiritLimitChannelTypeList(t *testing.T) {
	list, err := mysqlStore.BatchGetMagicSpiritLimitChannelTypeList(context.Background(), []uint32{1, 2})
	if err != nil {
		t.Error(err)
	}
	for k, v := range list {
		t.Logf("%d:%+v", k, v)
	}
}
