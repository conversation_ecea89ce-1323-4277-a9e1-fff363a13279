package define

import pb "golang.52tt.com/protocol/services/magic-spirit"

type SendInfo struct {
	Uid                uint32
	MagicSpiritId      uint32
	TargetUidList      []uint32
	AverageCnt         uint32
	ChannelId          uint32
	SeparateUnpackGift bool
	Source             uint32
	DealToken          string
}

const (
	NormalPondType  = uint32(pb.AddMagicSpiritPondReq_NONE_POND)
	SpecialPondType = uint32(pb.AddMagicSpiritPondReq_SPECIAL_POND)
)
