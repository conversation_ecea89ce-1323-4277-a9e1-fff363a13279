package conf

import (
    "reflect"
    "testing"
    pb "golang.52tt.com/protocol/services/magic-spirit"
)

func TestGetEffectTimeRange(t *testing.T) {
    type args struct {
        timeList []*pb.TimeRange
        nowUnix  int64
    }
    tests := []struct {
        name string
        args args
        want *pb.TimeRange
    }{
        {
            name: "TestGetEffectTimeRange 无生效中时间段",
            args: args{
                timeList: []*pb.TimeRange{
                    {
                        StartTime: 1640000000,
                        EndTime:   1640001000,
                    },
                    {
                        StartTime: 1640002000,
                        EndTime:   1640003000,
                    },
                },
                nowUnix: 1640005000,
            },
            want: nil,
        },
        {
            name: "多个时间段，第一个时间段生效中",
            args: args{
                timeList: []*pb.TimeRange{
                    {
                        StartTime: 1640000000,
                        EndTime:   1640001000,
                    },
                    {
                        StartTime: 1640002000,
                        EndTime:   1640003000,
                    },
                },
                nowUnix: 1640000500,
            },
            want: &pb.TimeRange{
                StartTime: 1640000000,
                EndTime:   1640001000,
            },
        },
        {
            name: "没有生效中时间段，返回第一个待生效时间段",
            args: args{
                timeList: []*pb.TimeRange{
                    {
                        StartTime: 1640000000,
                        EndTime:   1640001000,
                    },
                    {
                        StartTime: 1640002000,
                        EndTime:   1640003000,
                    },
                    {
                        StartTime: 1640006000,
                        EndTime:   1640007000,
                    },
                },
                nowUnix: 1640005000,
            },
            want: &pb.TimeRange{
                StartTime: 1640006000,
                EndTime:   1640007000,
            },
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            if got := GetEffectTimeRange(tt.args.timeList, tt.args.nowUnix); !reflect.DeepEqual(got, tt.want) {
                t.Errorf("GetEffectTimeRange() = %v, want %v", got, tt.want)
            }
        })
    }
}
