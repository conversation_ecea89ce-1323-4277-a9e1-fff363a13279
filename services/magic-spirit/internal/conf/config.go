package conf

import (
	"encoding/json"
	"fmt"
	redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
	"io/ioutil"

	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"strings"
	"strconv"
	"context"
	pb "golang.52tt.com/protocol/services/magic-spirit"
)

type ServiceConfigT struct {
	MysqlConfig           *config.MysqlConfig       `json:"mysql"`
	MysqlReadOnlyConfig   *config.MysqlConfig       `json:"readonly_mysql"`
	RedisConfig           *redisConnect.RedisConfig `json:"redis"`
	MagicSpiritPresentKFK *config.KafkaConfig       `json:"magic_spirit_present_kfk"` // 幸运礼物kafka
}

func (sc *ServiceConfigT) Parse(configFile string) (err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("Failed to parse config: %v \n", e)
		}
	}()

	data, err := ioutil.ReadFile(configFile)
	if err != nil {
		return err
	}
	err = json.Unmarshal(data, &sc)
	if err != nil {
		return err
	}

	log.Infof("ServiceConfigT:MysqlConfig:%+v\n", sc.MysqlConfig)
	return
}

func (sc *ServiceConfigT) GetMysqlConfig() *config.MysqlConfig {
	return sc.MysqlConfig
}

func (sc *ServiceConfigT) GetMysqlReadOnlyConfig() *config.MysqlConfig {
	return sc.MysqlReadOnlyConfig
}

func (sc *ServiceConfigT) GetRedisConfig() *redisConnect.RedisConfig {
	return sc.RedisConfig
}

func Str2TimeRangeListPb(ctx context.Context, timeListStr string) []*pb.TimeRange {
	if timeListStr == "" {
		return nil
	}
	timeList := make([]*pb.TimeRange, 0)

	for _, element := range strings.Split(timeListStr, ",") {
		if element == "" {
			continue
		}
		splitElements := strings.Split(element, "-")
		if len(splitElements) != 2 {
			log.WarnWithCtx(ctx, "GetMagicSpirit invalid timeListStr: %v", timeListStr)
			continue
		}
		beginTime, err := strconv.ParseInt(splitElements[0], 10, 32)
		if err != nil {
			log.WarnWithCtx(ctx, "GetMagicSpirit convert beginTime error: %v", err)
			continue
		}
		endTime, err := strconv.ParseInt(splitElements[1], 10, 32)
		if err != nil {
			log.WarnWithCtx(ctx, "GetMagicSpirit convert endTime error: %v", err)
			continue
		}
		timeList = append(timeList, &pb.TimeRange{
			StartTime: beginTime,
			EndTime:   endTime,
		})
	}

	return timeList
}

func TimeRange2TimeListStr(timeList []*pb.TimeRange) string {
	if len(timeList) == 0 {
		return ""
	}
	timeListStr := ""
	for _, item := range timeList {
		timeListStr += strconv.FormatInt(item.StartTime, 10) + "-" + strconv.FormatInt(item.EndTime, 10) + ","
	}
	return timeListStr[:len(timeListStr)-1]
}

// GetEffectTimeRange 返回生效中或者，最近即将生效的时间段
func GetEffectTimeRange(timeList []*pb.TimeRange, nowUnix int64) *pb.TimeRange {
	if len(timeList) == 0 {
		return nil
	}

	// 返回生效中的时间段
	for _, item := range timeList {
		if nowUnix >= item.StartTime && nowUnix <= item.EndTime {
			return item
		}
	}

	// 没有生效中的时间段，返回最近即将生效的
	for _, item := range timeList {
		if item.StartTime > nowUnix {
			return item
		}
	}

	// 没有待生效的时间段
	return nil
}
