package conf

import (
	"encoding/json"
	"fmt"
	redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
	"io/ioutil"

	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
)

type ServiceConfigT struct {
	MysqlConfig           *config.MysqlConfig       `json:"mysql"`
	MysqlReadOnlyConfig   *config.MysqlConfig       `json:"readonly_mysql"`
	RedisConfig           *redisConnect.RedisConfig `json:"redis"`
	MagicSpiritPresentKFK *config.KafkaConfig       `json:"magic_spirit_present_kfk"` // 幸运礼物kafka
}

func (sc *ServiceConfigT) Parse(configFile string) (err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("Failed to parse config: %v \n", e)
		}
	}()

	data, err := ioutil.ReadFile(configFile)
	if err != nil {
		return err
	}
	err = json.Unmarshal(data, &sc)
	if err != nil {
		return err
	}

	log.Infof("ServiceConfigT:MysqlConfig:%+v\n", sc.MysqlConfig)
	return
}

func (sc *ServiceConfigT) GetMysqlConfig() *config.MysqlConfig {
	return sc.MysqlConfig
}

func (sc *ServiceConfigT) GetMysqlReadOnlyConfig() *config.MysqlConfig {
	return sc.MysqlReadOnlyConfig
}

func (sc *ServiceConfigT) GetRedisConfig() *redisConnect.RedisConfig {
	return sc.RedisConfig
}
