package manager

import (
	"context"
	"fmt"
	publicNoticePB "golang.52tt.com/protocol/services/public-notice"
	"google.golang.org/grpc/codes"
	"time"

	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/protocol/services/apicentergo"
	upb "golang.52tt.com/protocol/services/userpresent"
	"golang.52tt.com/services/magic-spirit/internal/mysql"
)

const (
	WriteDBTimeout = 2 * time.Second
)

func (m *MagicSpiritMgr) JudgeExpectProfit(data []*mysql.MagicSpiritPond, spiritPrice uint32) bool {
	sumWeight := uint32(0)
	for _, item := range data {
		sumWeight += item.Weight
	}
	if sumWeight == 0 {
		return false
	}

	expectProfit := float64(0)
	for _, item := range data {
		prob := float64(item.Weight) / float64(sumWeight)
		expectProfit += prob * float64(int32(spiritPrice)-int32(item.Price))
	}
	expectProfit = expectProfit / float64(spiritPrice) * 100

	log.Debugf("MinExpectProfit: %v", m.bc.GetMinExpectProfit())
	return expectProfit >= m.bc.GetMinExpectProfit()
}

func (m *MagicSpiritMgr) AddMagicSpiritPond(ctx context.Context, data []*mysql.MagicSpiritPond) ([]uint32, error) {
	if len(data) == 0 {
		return nil, nil
	}

	if len(data) >= 100 {
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "奖池礼物数量过多")
	}

	weightLimit := uint32(1000000)
	totalWeight := uint32(0)
	for _, item := range data {
		if item.Weight > weightLimit {
			return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "权重值过大")
		}
		resp, err := m.publicNoticeCli.BatchGetBreakingNewsConfig(ctx, &publicNoticePB.BatchGetBreakingNewsConfigReq{
			SearchNewsId: item.BreakingNewsId,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "AddMagicSpiritPond get breaking news error: %v", err)
			return nil, err
		}
		if len(resp.BreakingNewsConfigList) == 0 {
			log.ErrorWithCtx(ctx, "AddMagicSpiritPond invalid breaking news id: %d", item.BreakingNewsId)
			return nil, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "全服ID不存在")
		}
		totalWeight += item.Weight
	}

	if totalWeight > weightLimit {
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "权重值过大")
	}

	// 补充礼物价钱
	for _, item := range data {
		present, err := m.apiCenterGoCli.GetPresentConfigById(ctx, &apicentergo.GetPresentConfigByIdReq{
			ItemId: item.PresentId,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "AddMagicSpiritPond fill price error: %v", err)
			return nil, err
		}
		if upb.PresentPriceType(present.ItemConfig.PriceType) != upb.PresentPriceType_PRESENT_PRICE_TBEAN {
			log.ErrorWithCtx(ctx, "AddMagicSpiritPond invalid price type present selected, presentId: %d", item.PresentId)
			return nil, fmt.Errorf("invalid price type present: %d", item.PresentId)
		}

		if present.ItemConfig.Price == 0 {
			return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "礼物价格不应为0")
		}
		item.Price = present.ItemConfig.Price
	}

	// 判断期望收益率是否>=0
	msid := data[0].MagicSpiritId
	ms, err := m.mysql.GetMagicSpiritById(ctx, msid)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddMagicSpiritPond find magic_spirit info error: %v", err)
		return nil, err
	}
	if !m.JudgeExpectProfit(data, ms.Price) {
		log.ErrorWithCtx(ctx, "AddMagicSpiritPond judge expect profit fail")
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, fmt.Sprintf("期望利润率<%.2f%%", m.bc.GetMinExpectProfit()))
	}

	// 设置缓存3s过期
	err = m.cache.ExpireMagicSpiritPond(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddMagicSpiritPond pre delete cache error: %v", err)
		return nil, err
	}

	// 写库2s失败
	timeoutCtx, cancel := context.WithTimeout(ctx, WriteDBTimeout)
	defer cancel()
	ids, err := m.mysql.AddMagicSpiritPondV2(timeoutCtx, data, time.Now())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddMagicSpiritPond error: %v")
		return nil, err
	}

	err = m.cache.DelMagicSpiritCache(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddMagicSpiritPond post delete cache error: %v", err)
	}

	return ids, nil
}

func (m *MagicSpiritMgr) GetMagicSpiritPondWithCache(ctx context.Context, magicSpiritId uint32) ([]*mysql.MagicSpiritPond, error) {
	data, exist, err := m.cache.GetMagicSpiritPond(ctx, magicSpiritId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMagicSpiritPondWithCache get cache error: %v, magicSpiritId: %v", err, magicSpiritId)
		return data, err
	}

	if !exist {
		data, err = m.GetMagicSpiritPond(ctx, magicSpiritId)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetMagicSpiritPondWithCache find db error: %v, magicSpiritId: %v", err, magicSpiritId)
			return nil, err
		}

		err = m.cache.SetMagicSpiritPond(ctx, magicSpiritId, data)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetMagicSpiritPondWithCache set cache error: %v", err)
			return nil, err
		}

		log.Debugf("GetMagicSpiritPondWithCache reload cache, magicSpiritId: %v", magicSpiritId)
	}

	return data, nil
}

func (m *MagicSpiritMgr) GetMagicSpiritPond(ctx context.Context, magicSpiritId uint32) ([]*mysql.MagicSpiritPond, error) {
	data, err := m.mysql.GetMagicSpiritPond(ctx, magicSpiritId, time.Now())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMagicSpiritPond error: %v", err)
		return nil, err
	}

	return data, nil
}

func (m *MagicSpiritMgr) GetMagicSpiritPondTmp(ctx context.Context, effectTime time.Time) (map[uint32][]*mysql.MagicSpiritPond, error) {
	out := make(map[uint32][]*mysql.MagicSpiritPond)

	list, err := m.mysql.GetMagicSpiritPondTmp(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMagicSpiritPond error: %v", err)
		return out, err
	}

	for _, v := range list {
		_, ok := out[v.MagicSpiritId]
		if !ok {
			out[v.MagicSpiritId] = make([]*mysql.MagicSpiritPond, 0)
		}
		out[v.MagicSpiritId] = append(out[v.MagicSpiritId], &mysql.MagicSpiritPond{
			ItemId:         v.ItemId,
			MagicSpiritId:  v.MagicSpiritId,
			Weight:         v.Weight,
			PresentId:      v.PresentId,
			Price:          v.Price,
			PrizeLevel:     v.PrizeLevel,
			CreateTime:     v.CreateTime,
			BeginTime:      v.BeginTime,
			BreakingNewsId: v.BreakingNewsId,
		})
	}

	return out, nil
}
