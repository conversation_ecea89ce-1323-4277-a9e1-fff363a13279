package manager

import (
	"context"
	"errors"
	"fmt"
	"github.com/golang/mock/gomock"
	accountCli "golang.52tt.com/clients/account"
	"golang.52tt.com/clients/mocks/account"
	apicenter_go "golang.52tt.com/clients/mocks/apicenter-go"
	chancegameentry "golang.52tt.com/clients/mocks/chance-game-entry"
	channel2 "golang.52tt.com/clients/mocks/channel"
	present_middleware "golang.52tt.com/clients/mocks/present-middleware"
	probgamecenter "golang.52tt.com/clients/mocks/prob-game-center"
	pushNotificationv2 "golang.52tt.com/clients/mocks/push-notification/v2"
	unifiedPay "golang.52tt.com/clients/mocks/unified_pay"
	"golang.52tt.com/pkg/deal_token"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/protocol/services/apicentergo"
	entryPb "golang.52tt.com/protocol/services/chance-game-entry"
	channelsvr "golang.52tt.com/protocol/services/channelsvr"
	pb "golang.52tt.com/protocol/services/magic-spirit"
	probgamecenter2 "golang.52tt.com/protocol/services/probgamecenter"
	public_notice2 "golang.52tt.com/protocol/services/public-notice"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	upb "golang.52tt.com/protocol/services/userpresent"
	"golang.52tt.com/services/magic-spirit/internal/conf"
	"golang.52tt.com/services/magic-spirit/internal/mocks"
	"golang.52tt.com/services/magic-spirit/internal/mysql"
	"testing"
	"time"
)

func TestMagicSpiritMgr_AddMagicSpirit(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)

	m := &MagicSpiritMgr{
		mysql: mockStore,
		cache: mockCache,
	}

	gomock.InOrder(
		mockCache.EXPECT().ExpireMagicSpirit(gomock.Any()).Return(nil),
		mockStore.EXPECT().AddMagicSpirit(gomock.Any(), gomock.Any()).Return(uint32(1), nil),
		mockCache.EXPECT().DelMagicSpiritCache(gomock.Any()).Return(nil),
	)

	t.Run("TestMagicSpiritMgr_AddMagicSpirit", func(t *testing.T) {
		_, err := m.AddMagicSpirit(context.Background(), &pb.MagicSpirit{
			Name: "test",
		})
		if err != nil {
			t.Errorf("AddMagicSpirit fail. err:%v", err)
		}
	})
}

func TestMagicSpiritMgr_GetMagicSpirit(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)

	m := &MagicSpiritMgr{
		mysql: mockStore,
		cache: mockCache,
	}

	gomock.InOrder(
		mockStore.EXPECT().GetMagicSpirit(gomock.Any()).Return([]*mysql.MagicSpirit{}, nil),
	)

	t.Run("TestMagicSpiritMgr_GetMagicSpirit", func(t *testing.T) {
		_, err := m.GetMagicSpirit(context.Background())
		if err != nil {
			t.Errorf("GetMagicSpirit fail. err:%v", err)
		}
	})
}

func TestMagicSpiritMgr_GetMagicSpiritWithCache(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)

	m := &MagicSpiritMgr{
		mysql: mockStore,
		cache: mockCache,
	}

	gomock.InOrder(
		mockCache.EXPECT().GetMagicSpirit(gomock.Any()).Return([]*mysql.MagicSpirit{}, nil),
		mockStore.EXPECT().GetMagicSpirit(gomock.Any()).Return([]*mysql.MagicSpirit{}, nil),
		mockCache.EXPECT().SetMagicSpirit(gomock.Any(), gomock.Any()).Return(nil),
	)

	t.Run("TestMagicSpiritMgr_GetMagicSpiritWithCache", func(t *testing.T) {
		_, err := m.GetMagicSpiritWithCache(context.Background())
		if err != nil {
			t.Errorf("GetMagicSpiritWithCache fail. err:%v", err)
		}
	})
}

func TestMagicSpiritMgr_GetMagicSpiritVersion(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)

	m := &MagicSpiritMgr{
		mysql: mockStore,
		cache: mockCache,
	}

	gomock.InOrder(
		mockCache.EXPECT().GetUpdateMagicSpiritVersion(gomock.Any()).Return(uint32(0), fmt.Errorf("test")),
	)

	t.Run("TestMagicSpiritMgr_GetMagicSpiritVersion", func(t *testing.T) {
		_, err := m.GetMagicSpiritVersion(context.Background())
		if err == nil {
			t.Errorf("GetMagicSpiritVersion fail. err:%v", err)
		}
	})

	gomock.InOrder(
		mockCache.EXPECT().GetUpdateMagicSpiritVersion(gomock.Any()).Return(uint32(0), nil),
	)

	t.Run("TestMagicSpiritMgr_GetMagicSpiritVersion", func(t *testing.T) {
		_, err := m.GetMagicSpiritVersion(context.Background())
		if err != nil {
			t.Errorf("GetMagicSpiritVersion fail. err:%v", err)
		}
	})
}

func TestMagicSpiritMgr_GetMagicSpiritById(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)

	m := &MagicSpiritMgr{
		mysql: mockStore,
		cache: mockCache,
	}

	gomock.InOrder(
		mockCache.EXPECT().GetMagicSpiritById(gomock.Any(), gomock.Any()).Return(nil, false, nil),
		mockStore.EXPECT().GetMagicSpiritById(gomock.Any(), gomock.Any()).Return(&mysql.MagicSpirit{}, nil),
	)

	t.Run("TestMagicSpiritMgr_GetMagicSpiritById", func(t *testing.T) {
		_, err := m.GetMagicSpiritById(context.Background(), 1)
		if err != nil {
			t.Errorf("GetMagicSpiritById fail. err:%v", err)
		}
	})
}

func TestMagicSpiritMgr_UpdateMagicSpirit(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)

	m := &MagicSpiritMgr{
		mysql: mockStore,
		cache: mockCache,
	}

	gomock.InOrder(
		mockCache.EXPECT().ExpireMagicSpirit(gomock.Any()).Return(nil),
		mockStore.EXPECT().Transaction(gomock.Any(), gomock.Any()).Return(nil),
		mockCache.EXPECT().DelMagicSpiritCache(gomock.Any()).Return(nil),
	)

	t.Run("TestMagicSpiritMgr_UpdateMagicSpirit", func(t *testing.T) {
		err := m.UpdateMagicSpirit(context.Background(), &pb.MagicSpirit{
			Name: "test",
		}, time.Now())
		if err != nil {
			t.Errorf("UpdateMagicSpirit fail. err:%v", err)
		}
	})
}

func TestMagicSpiritMgr_DelMagicSpirit(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)

	m := &MagicSpiritMgr{
		mysql: mockStore,
		cache: mockCache,
	}

	gomock.InOrder(
		mockStore.EXPECT().DelMagicSpirit(gomock.Any(), gomock.Any()).Return(nil),
		mockStore.EXPECT().DelMagicSpiritTmp(gomock.Any(), gomock.Any()).Return(nil),
		mockCache.EXPECT().DelMagicSpiritCache(gomock.Any()).Return(nil),
	)

	t.Run("TestMagicSpiritMgr_DelMagicSpirit", func(t *testing.T) {
		err := m.DelMagicSpirit(context.Background(), []uint32{1})
		if err != nil {
			t.Errorf("DelMagicSpirit fail. err:%v", err)
		}
	})
}

//func TestMagicSpiritMgr_GetMagicSpiritUsable(t *testing.T) {
//	ctl := gomock.NewController(t)
//	defer ctl.Finish()
//
//	mockStore := mocks.NewMockIStore(ctl)
//	mockCache := mocks.NewMockICache(ctl)
//	mockCfg := mocks.NewMockIBusinessConfManager(ctl)
//	entryMock := chancegameentry.NewMockIClient(ctl)
//
//	m := &MagicSpiritMgr{
//		mysql:              mockStore,
//		cache:              mockCache,
//		bc:                 mockCfg,
//		chanceGameEntryCli: entryMock,
//	}
//
//	gomock.InOrder(
//		mockCfg.EXPECT().GetShowCheckIntervalSec().Return(uint32(10)),
//		//mockCache.EXPECT().ExistsBlacklist(gomock.Any()).Return(false, nil),
//		//mockStore.EXPECT().GetBlacklistCIds(gomock.Any()).Return([]uint32{0}, nil),
//		//mockCache.EXPECT().InitBlacklist(gomock.Any(), gomock.Any()).Return(nil),
//		//
//		//mockCache.EXPECT().GetRealNameCheck(gomock.Any(), gomock.Any()).Return(true, true, nil),
//		//mockCfg.EXPECT().CheckIfInWhiteList(gomock.Any()).Return(false),
//		//mockCfg.EXPECT().GetDisableSwitch().Return(false),
//		//
//		//mockCache.EXPECT().GetSendValid(gomock.Any(), gomock.Any()).Return(true, true, nil),
//		entryMock.EXPECT().CheckGameEntryAccess(gomock.Any(), gomock.Any(), gomock.Any()).Return(
//			&entryPb.CheckGameEntryAccessResp{
//				ConfList: []*entryPb.GameEntryAccess{
//					&entryPb.GameEntryAccess{
//						Access: false,
//						Switch: true,
//						ConditionList: []*entryPb.AccessCondition{
//							&entryPb.AccessCondition{
//								SubList: []*entryPb.SubAccessCondition{
//									&entryPb.SubAccessCondition{
//										ConditionType: 4,
//										Threshold:     10,
//									},
//								},
//								RelateType: 1,
//							},
//							&entryPb.AccessCondition{
//								SubList: []*entryPb.SubAccessCondition{
//									&entryPb.SubAccessCondition{
//										ConditionType: 7,
//										Threshold:     0,
//									},
//								},
//								RelateType: 1,
//							},
//						},
//						RelateType: 2,
//					},
//				},
//			}, nil),
//		mockCache.EXPECT().BatGetUserSendFlag(gomock.Any(), gomock.Any(), gomock.Any()).Return(map[uint32]bool{1: true}, nil),
//	)
//
//	t.Run("TestMagicSpiritMgr_GetMagicSpiritUsable", func(t *testing.T) {
//		_, err := m.GetMagicSpiritUsable(context.Background(), &pb.GetMagicSpiritUsableReq{
//			ChannelId:  1,
//			Uid:        1,
//			OldVersion: false,
//		})
//		if err != nil {
//			t.Errorf("GetMagicSpiritUsable fail. err:%v", err)
//		}
//	})
//}

func TestMagicSpiritMgr_AddMagicSpiritBlacklist(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)

	m := &MagicSpiritMgr{
		mysql: mockStore,
		cache: mockCache,
	}

	gomock.InOrder(
		mockStore.EXPECT().AddBlacklist(gomock.Any(), gomock.Any()).Return([]uint32{1}, nil),
		mockCache.EXPECT().SetBlacklist(gomock.Any(), gomock.Any()).Return(nil),
	)

	t.Run("TestMagicSpiritMgr_AddMagicSpiritBlacklist", func(t *testing.T) {
		_, err := m.AddMagicSpiritBlacklist(context.Background(), []*mysql.MagicSpiritBlacklist{
			{ChannelId: 1},
		})
		if err != nil {
			t.Errorf("AddMagicSpiritBlacklist fail. err:%v", err)
		}
	})
}

func TestMagicSpiritMgr_GetMagicSpiritBlacklist(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)

	m := &MagicSpiritMgr{
		mysql: mockStore,
		cache: mockCache,
	}

	gomock.InOrder(
		mockStore.EXPECT().GetBlacklist(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(1), []*mysql.MagicSpiritBlacklist{
			{ChannelId: 1},
		}, nil),
	)

	t.Run("TestMagicSpiritMgr_GetMagicSpiritBlacklist", func(t *testing.T) {
		total, list, err := m.GetMagicSpiritBlacklist(context.Background(), 1, 0, 10)
		if err != nil || total == 0 || len(list) == 0 {
			t.Errorf("GetMagicSpiritBlacklist fail. err:%v", err)
		}
	})
}

func TestMagicSpiritMgr_DelMagicSpiritBlacklist(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)

	m := &MagicSpiritMgr{
		mysql: mockStore,
		cache: mockCache,
	}

	gomock.InOrder(
		mockCache.EXPECT().DelBlacklist(gomock.Any(), gomock.Any()).Return(nil),
		mockStore.EXPECT().DelBlacklist(gomock.Any(), gomock.Any()).Return(nil),
		mockCache.EXPECT().DelBlacklist(gomock.Any(), gomock.Any()).Return(nil),
	)

	t.Run("TestMagicSpiritMgr_DelMagicSpiritBlacklist", func(t *testing.T) {
		err := m.DelMagicSpiritBlacklist(context.Background(), []uint32{1})
		if err != nil {
			t.Errorf("DelMagicSpiritBlacklist fail. err:%v", err)
		}
	})
}

func TestMagicSpiritMgr_SetCommonConf(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)

	m := &MagicSpiritMgr{
		mysql: mockStore,
		cache: mockCache,
	}

	gomock.InOrder(
		mockCache.EXPECT().DelCommonConf(gomock.Any()).Return(nil),
		mockStore.EXPECT().SetCommonConf(gomock.Any(), gomock.Any()).Return(nil),
		mockCache.EXPECT().DelCommonConf(gomock.Any()).Return(nil),
	)

	t.Run("TestMagicSpiritMgr_SetCommonConf", func(t *testing.T) {
		err := m.SetCommonConf(context.Background(), []*mysql.MagicSpiritCommonConf{
			{ValueType: 1, Value: "100"},
		})
		if err != nil {
			t.Errorf("SetCommonConf fail. err:%v", err)
		}
	})
}

func TestMagicSpiritMgr_GetCommonConf(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)

	m := &MagicSpiritMgr{
		mysql: mockStore,
		cache: mockCache,
	}

	gomock.InOrder(
		mockStore.EXPECT().GetCommonConf(gomock.Any()).Return([]*mysql.MagicSpiritCommonConf{
			{ValueType: 1, Value: "100"},
		}, nil),
	)

	t.Run("TestMagicSpiritMgr_GetCommonConf", func(t *testing.T) {
		list, err := m.GetCommonConf(context.Background())
		if err != nil || len(list) == 0 {
			t.Errorf("GetCommonConf fail. err:%v", err)
		}
	})
}

func TestMagicSpiritMgr_GetCommonConfWithCache(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)

	m := &MagicSpiritMgr{
		mysql: mockStore,
		cache: mockCache,
	}

	gomock.InOrder(
		mockCache.EXPECT().GetCommonConf(gomock.Any()).Return(nil, nil),
		mockStore.EXPECT().GetCommonConf(gomock.Any()).Return([]*mysql.MagicSpiritCommonConf{
			{ValueType: 1, Value: "100"},
		}, nil),
		mockCache.EXPECT().SetCommonConf(gomock.Any(), gomock.Any()).Return(nil),
	)

	t.Run("TestMagicSpiritMgr_GetCommonConfWithCache", func(t *testing.T) {
		confMap, err := m.GetCommonConfWithCache(context.Background())
		if err != nil || len(confMap) == 0 {
			t.Errorf("GetCommonConfWithCache fail. err:%v", err)
		}
	})
}

func TestMagicSpiritMgr_AddMagicSpiritPond(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)
	mockApicenterGoCli := apicenter_go.NewMockIClient(ctl)
	publicNoticeCli := mocks.NewMockPublicNoticeClient(ctl)

	m := &MagicSpiritMgr{
		mysql:           mockStore,
		cache:           mockCache,
		bc:              mockCfg,
		apiCenterGoCli:  mockApicenterGoCli,
		publicNoticeCli: publicNoticeCli,
	}

	gomock.InOrder(
		publicNoticeCli.EXPECT().BatchGetBreakingNewsConfig(gomock.Any(), gomock.Any()).Return(&public_notice2.BatchGetBreakingNewsConfigResp{
			BreakingNewsConfigList: []*public_notice2.BreakingNewsConfig{
				{NewsId: 1},
			},
		}, nil).AnyTimes(),
		mockApicenterGoCli.EXPECT().GetPresentConfigById(gomock.Any(), gomock.Any()).Return(&apicentergo.GetPresentConfigByIdResp{
			ItemConfig: &apicentergo.StPresentItemConfig{
				Price:     100,
				PriceType: uint32(upb.PresentPriceType_PRESENT_PRICE_TBEAN),
			}}, nil),
		mockStore.EXPECT().GetMagicSpiritById(gomock.Any(), gomock.Any()).Return(&mysql.MagicSpirit{MagicSpiritId: 1, Price: 10000}, nil),
		mockCfg.EXPECT().GetMinExpectProfit().AnyTimes().Return(float64(0)),

		mockCache.EXPECT().ExpireMagicSpiritPond(gomock.Any()).Return(nil),
		mockStore.EXPECT().AddMagicSpiritPondV2(gomock.Any(), gomock.Any(), gomock.Any()).Return([]uint32{1}, nil),
		mockCache.EXPECT().DelMagicSpiritCache(gomock.Any()).Return(nil),
	)

	t.Run("TestMagicSpiritMgr_AddMagicSpiritPond", func(t *testing.T) {
		_, err := m.AddMagicSpiritPond(context.Background(), []*mysql.MagicSpiritPond{
			{MagicSpiritId: 1, Price: 10, PresentId: 1, Weight: 10},
		})
		if err != nil {
			t.Errorf("AddMagicSpiritPond fail. err:%v", err)
		}
	})
}

func TestMagicSpiritMgr_GetMagicSpiritPond(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)

	m := &MagicSpiritMgr{
		mysql: mockStore,
		cache: mockCache,
		bc:    mockCfg,
	}

	gomock.InOrder(
		mockStore.EXPECT().GetMagicSpiritPond(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*mysql.MagicSpiritPond{{MagicSpiritId: 1, Price: 10000}}, nil),
	)

	t.Run("TestMagicSpiritMgr_GetMagicSpiritPond", func(t *testing.T) {
		_, err := m.GetMagicSpiritPond(context.Background(), 1)
		if err != nil {
			t.Errorf("GetMagicSpiritPond fail. err:%v", err)
		}
	})
}

func TestMagicSpiritMgr_GetMagicSpiritPondWithCache(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)

	m := &MagicSpiritMgr{
		mysql: mockStore,
		cache: mockCache,
		bc:    mockCfg,
	}

	gomock.InOrder(
		mockCache.EXPECT().GetMagicSpiritPond(gomock.Any(), gomock.Any()).Return(nil, false, nil),
		mockStore.EXPECT().GetMagicSpiritPond(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*mysql.MagicSpiritPond{{MagicSpiritId: 1, Price: 10000}}, nil),
		mockCache.EXPECT().SetMagicSpiritPond(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
	)

	t.Run("TestMagicSpiritMgr_GetMagicSpiritPondWithCache", func(t *testing.T) {
		_, err := m.GetMagicSpiritPondWithCache(context.Background(), 1)
		if err != nil {
			t.Errorf("GetMagicSpiritPondWithCache fail. err:%v", err)
		}
	})
}

func TestMagicSpiritMgr_AddUnpackList(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)
	mockAccount := account.NewMockIClient(ctl)
	mockChanel := channel2.NewMockIClient(ctl)
	mockPush := pushNotificationv2.NewMockIClient(ctl)

	m := &MagicSpiritMgr{
		mysql:      mockStore,
		cache:      mockCache,
		bc:         mockCfg,
		accountCli: mockAccount,
		channelCli: mockChanel,
		pushCli:    mockPush,
	}

	gomock.InOrder(
		mockCfg.EXPECT().GetUnpackGiftDelaySec().Return(uint32(10)),
		mockCache.EXPECT().AddUnpackOrderList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
		mockCache.EXPECT().GetChannelUnpackOrderIdList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]string{"test1"}, nil),
		mockCache.EXPECT().BatchGetUnpackOrderInfo(gomock.Any(), gomock.Any()).Return(map[string]*pb.UnpackGiftInfo{
			"test1": {ItemOrderId: "test1"},
		}, nil),
		mockAccount.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(map[uint32]*accountCli.User{}, nil),
		mockChanel.EXPECT().GetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channelsvr.ChannelSimpleInfo{}, nil),
		mockPush.EXPECT().PushMulticasts(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
	)

	t.Run("TestMagicSpiritMgr_AddUnpackList", func(t *testing.T) {
		err := m.AddUnpackList(context.Background(), 1, 1, []*pb.UnpackGiftInfo{
			{ItemOrderId: "test"},
		})
		if err != nil {
			t.Errorf("AddUnpackList fail. err:%v", err)
		}
	})
}

func TestMagicSpiritMgr_GetChannelAllUnpackGift(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)

	m := &MagicSpiritMgr{
		mysql: mockStore,
		cache: mockCache,
		bc:    mockCfg,
	}

	gomock.InOrder(
		mockCache.EXPECT().GetChannelUnpackOrderIdList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]string{"test"}, nil),
		mockCache.EXPECT().GetUserUnpackOrderIdList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]string{"test"}, nil),
		mockCache.EXPECT().BatchGetUnpackOrderInfo(gomock.Any(), gomock.Any()).Return(map[string]*pb.UnpackGiftInfo{
			"test1": {ItemOrderId: "test1", EndTs: uint32(time.Now().Unix() + 60)},
		}, nil),
	)

	t.Run("TestMagicSpiritMgr_GetChannelAllUnpackGift", func(t *testing.T) {
		_, err := m.GetChannelAllUnpackGift(context.Background(), 1, 1)
		if err != nil {
			t.Errorf("GetChannelAllUnpackGift fail. err:%v", err)
		}
	})
}

func TestMagicSpiritMgr_OpenUnpackGift(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)
	mockAccount := account.NewMockIClient(ctl)
	mockChanel := channel2.NewMockIClient(ctl)
	mockPush := pushNotificationv2.NewMockIClient(ctl)

	m := &MagicSpiritMgr{
		mysql:      mockStore,
		cache:      mockCache,
		bc:         mockCfg,
		accountCli: mockAccount,
		channelCli: mockChanel,
		pushCli:    mockPush,
	}

	gomock.InOrder(
		mockStore.EXPECT().GetMagicSpiritAwardLog(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, false, nil),
		mockStore.EXPECT().GetMagicSpiritAwardLog(gomock.Any(), gomock.Any(), gomock.Any()).Return(&mysql.MagicSpiritAwardLog{
			OrderId: "test", FromUid: 1, ChannelId: 1,
		}, true, nil),
		mockCache.EXPECT().RemoveUnpackOrder(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
		mockStore.EXPECT().UpdateMagicSpiritAwardDone(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil),
	)

	t.Run("TestMagicSpiritMgr_OpenUnpackGift", func(t *testing.T) {
		_, err := m.OpenUnpackGift(context.Background(), 1, 1, "test", false)
		if err != nil && protocol.ToServerError(err).Code() != status.ErrMagicSpiritOrderNotExist {
			t.Errorf("OpenUnpackGift fail. err:%v", err)
		}
	})
}

func TestMagicSpiritMgr_handleTimeoutUnpackGift(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)
	mockAccount := account.NewMockIClient(ctl)
	mockChanel := channel2.NewMockIClient(ctl)
	mockPush := pushNotificationv2.NewMockIClient(ctl)

	m := &MagicSpiritMgr{
		mysql:      mockStore,
		cache:      mockCache,
		bc:         mockCfg,
		accountCli: mockAccount,
		channelCli: mockChanel,
		pushCli:    mockPush,
	}

	gomock.InOrder(
		mockCache.EXPECT().PopExpireUnpackOrderId(gomock.Any()).Return("test", true, nil),
		mockStore.EXPECT().GetMagicSpiritAwardLog(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, false, nil),
		mockStore.EXPECT().GetMagicSpiritAwardLog(gomock.Any(), gomock.Any(), gomock.Any()).Return(&mysql.MagicSpiritAwardLog{
			OrderId: "test", FromUid: 1, ChannelId: 1,
		}, true, nil),
		mockCache.EXPECT().RemoveUnpackOrder(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
		mockStore.EXPECT().UpdateMagicSpiritAwardDone(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil),
	)

	t.Run("TestMagicSpiritMgr_handleTimeoutUnpackGift", func(t *testing.T) {
		m.handleTimeoutUnpackGift()
	})
}

func TestMagicSpiritMgr_sendUnpackGift(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)
	mockAccount := account.NewMockIClient(ctl)
	mockChanel := channel2.NewMockIClient(ctl)
	mockPush := pushNotificationv2.NewMockIClient(ctl)
	mockProbGame := probgamecenter.NewMockIClient(ctl)
	mockPresentM := present_middleware.NewMockIClient(ctl)

	m := &MagicSpiritMgr{
		mysql:        mockStore,
		cache:        mockCache,
		bc:           mockCfg,
		accountCli:   mockAccount,
		channelCli:   mockChanel,
		pushCli:      mockPush,
		probGameCli:  mockProbGame,
		presentMWCli: mockPresentM,
	}

	gomock.InOrder(
		mockCache.EXPECT().GetMagicSpiritById(gomock.Any(), gomock.Any()).Return(&mysql.MagicSpirit{MagicSpiritId: 1, Price: 10000}, true, nil),
		mockCache.EXPECT().GetMagicSpiritPond(gomock.Any(), gomock.Any()).Return([]*mysql.MagicSpiritPond{{MagicSpiritId: 1, Price: 10000, ItemId: 1}}, true, nil),
		mockChanel.EXPECT().GetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channelsvr.ChannelSimpleInfo{}, nil),
		mockProbGame.EXPECT().CheckFuse(gomock.Any(), gomock.Any()).Return(&probgamecenter2.CheckFuseResp{
			CheckResultList: []*probgamecenter2.CheckFuseResult{
				{OrderId: "test", DealToken: "test"},
			},
		}, nil),
		mockPresentM.EXPECT().MagicSendPresent(gomock.Any(), gomock.Any()).Return(nil, nil),
	)

	t.Run("TestMagicSpiritMgr_sendUnpackGift", func(t *testing.T) {
		_, err := m.sendUnpackGift(context.Background(), &mysql.MagicSpiritAwardLog{OrderId: "test"}, false)
		if err != nil {
			t.Errorf("sendUnpackGift fail. err:%v", err)
		}
	})
}

func TestMagicSpiritMgr_GetMagicAwardOrderIdList(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)

	m := &MagicSpiritMgr{
		mysql: mockStore,
	}

	gomock.InOrder(
		mockStore.EXPECT().GetMagicAwardOrderIdList(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return([]string{"test"}, nil),
	)

	now := time.Now()
	endTime := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)

	t.Run("TestMagicSpiritMgr_GetMagicAwardOrderIdList", func(t *testing.T) {
		_, err := m.GetMagicAwardOrderIdList(context.Background(), endTime.Add(-time.Hour), endTime)
		if err != nil {
			t.Errorf("GetMagicAwardOrderIdList fail. err:%v", err)
		}
	})
}

func TestMagicSpiritMgr_GetMagicAwardTotal(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)

	m := &MagicSpiritMgr{
		mysql: mockStore,
	}

	gomock.InOrder(
		mockStore.EXPECT().GetAwardTotalInfo(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&mysql.TotalInfo{}, nil),
	)

	now := time.Now()
	endTime := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
	t.Run("TestMagicSpiritMgr_GetMagicAwardTotal", func(t *testing.T) {
		_, _, _, err := m.GetMagicAwardTotal(context.Background(), endTime.Add(-time.Hour), endTime)
		if err != nil {
			t.Errorf("GetMagicAwardTotal fail. err:%v", err)
		}
	})
}

func TestMagicSpiritMgr_GetMagicConsumeOrderIdList(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)

	m := &MagicSpiritMgr{
		mysql: mockStore,
	}

	gomock.InOrder(
		mockStore.EXPECT().GetMagicConsumeOrderIdList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return([]string{"test"}, nil),
	)

	now := time.Now()
	endTime := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)

	t.Run("TestMagicSpiritMgr_GetMagicConsumeOrderIdList", func(t *testing.T) {
		_, err := m.GetMagicConsumeOrderIdList(context.Background(), endTime.Add(-time.Hour), endTime, 0)
		if err != nil {
			t.Errorf("GetMagicConsumeOrderIdList fail. err:%v", err)
		}
	})
}

func TestMagicSpiritMgr_GetMagicOrderTotal(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)

	m := &MagicSpiritMgr{
		mysql: mockStore,
	}

	gomock.InOrder(
		mockStore.EXPECT().GetOrderTotalInfo(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&mysql.TotalInfo{}, nil),
	)

	now := time.Now()
	endTime := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)

	t.Run("TestMagicSpiritMgr_GetMagicAwardTotal", func(t *testing.T) {
		_, _, _, err := m.GetMagicOrderTotal(context.Background(), endTime.Add(-time.Hour), endTime, 0)
		if err != nil {
			t.Errorf("GetMagicAwardTotal fail. err:%v", err)
		}
	})
}

func TestMagicSpiritMgr_Callback(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)
	mockAccount := account.NewMockIClient(ctl)
	mockPay := unifiedPay.NewMockIClient(ctl)

	m := &MagicSpiritMgr{
		mysql:         mockStore,
		cache:         mockCache,
		bc:            mockCfg,
		accountCli:    mockAccount,
		unifiedPayCli: mockPay,
	}

	gomock.InOrder(
		mockStore.EXPECT().GetMagicSpiritOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(&mysql.MagicSpiritOrder{}, false, nil),
		mockStore.EXPECT().GetMagicSpiritOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(&mysql.MagicSpiritOrder{
			OrderId:    "test",
			Status:     mysql.OrderStatusFinish,
			CreateTime: time.Now().Add(-5 * time.Minute),
		}, true, nil),
		mockCache.EXPECT().GetMagicSpiritById(gomock.Any(), gomock.Any()).Return(&mysql.MagicSpirit{}, true, nil),

		mockAccount.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&accountCli.User{}, nil),
		mockCfg.EXPECT().GetPayAppId().Return(""),
		mockPay.EXPECT().UnfreezeAndConsume(gomock.Any(), gomock.Any()).Return("2006-01-02 15:04:05", "test", nil),

		mockStore.EXPECT().UpdateMagicSpiritAwardTBeanTime(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil),
		mockStore.EXPECT().UpdateMagicSpiritOrderTBeanTime(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil),
	)

	t.Run("TestMagicSpiritMgr_Callback", func(t *testing.T) {
		_, err := m.Callback(context.Background(), "test")
		if err != nil {
			t.Errorf("Callback fail. err:%v", err)
		}
	})

	gomock.InOrder(
		mockStore.EXPECT().GetMagicSpiritOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(&mysql.MagicSpiritOrder{}, false, nil),
		mockStore.EXPECT().GetMagicSpiritOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(&mysql.MagicSpiritOrder{
			OrderId:    "test",
			Status:     mysql.OrderStatusHandling,
			CreateTime: time.Now().Add(-5 * time.Minute),
		}, true, nil),
		mockCache.EXPECT().GetMagicSpiritById(gomock.Any(), gomock.Any()).Return(&mysql.MagicSpirit{}, true, nil),

		mockStore.EXPECT().UpdateMagicSpiritOrderStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil),
		mockCfg.EXPECT().GetPayAppId().Return(""),
		mockPay.EXPECT().UnFreezeAndRefund(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
	)

	t.Run("TestMagicSpiritMgr_Callback", func(t *testing.T) {
		_, err := m.Callback(context.Background(), "test")
		if err != nil {
			t.Errorf("Callback fail. err:%v", err)
		}
	})
}

func TestMagicSpiritMgr_SendMagicSpirit(t *testing.T) {
	t.Skip()
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)
	mockAccount := account.NewMockIClient(ctl)
	mockPay := unifiedPay.NewMockIClient(ctl)
	mockProbGame := probgamecenter.NewMockIClient(ctl)
	mockChanel := channel2.NewMockIClient(ctl)
	mockPush := pushNotificationv2.NewMockIClient(ctl)
	entryMock := chancegameentry.NewMockIClient(ctl)
	mockProducer := mocks.NewMockMagicSpiritEventProducer(ctl)

	m := &MagicSpiritMgr{
		mysql: mockStore,
		cache: mockCache,
		bc:    mockCfg,
		lotteryPond: &MagicPondLocalCache{
			pondMemCache: [2]*AllMagicPond{
				{map[uint32]*MagicPond{
					1: {PondGiftList: []*mysql.MagicSpiritPond{
						{ItemId: 1, Price: 10, Weight: 10, PrizeLevel: uint32(pb.MagicSpiritEffectType_MAGIC_SPIRIT_EFFECT_LV3_MORE_500)},
						{ItemId: 2, Price: 10, Weight: 10, PrizeLevel: uint32(pb.MagicSpiritEffectType_MAGIC_SPIRIT_EFFECT_LV1)},
					}, TotalWeight: 20,
					},
				},
				},
			},
		},
		accountCli:          mockAccount,
		unifiedPayCli:       mockPay,
		probGameCli:         mockProbGame,
		channelCli:          mockChanel,
		pushCli:             mockPush,
		chanceGameEntryCli:  entryMock,
		magicSpiritProducer: mockProducer,
	}

	now := time.Now()

	gomock.InOrder(
		mockCache.EXPECT().GetMagicSpiritById(gomock.Any(), gomock.Any()).Return(&mysql.MagicSpirit{
			EffectBegin: uint32(now.Unix()), EffectEnd: uint32(now.Add(time.Hour).Unix()), Price: 100,
		}, true, nil),
		mockCache.EXPECT().CheckIfFusing(gomock.Any()).Return(false, nil),

		mockCfg.EXPECT().GetOldVersionAppAccessCheck().Return(false),
		entryMock.EXPECT().CheckMagicSpiritAccess(gomock.Any(), gomock.Any(), gomock.Any()).Return(
			&entryPb.CheckMagicSpiritAccessResp{
				Switch: true,
				ConfList: []*entryPb.MagicSpiritAccess{
					&entryPb.MagicSpiritAccess{
						MagicSpiritId: 1,
						Access:        true,
						ConditionList: []*entryPb.AccessCondition{
							&entryPb.AccessCondition{
								SubList: []*entryPb.SubAccessCondition{
									&entryPb.SubAccessCondition{
										ConditionType: 7,
										Threshold:     0,
									},
								},
							}},
						RelateType: 0,
					}},
			}, nil),

		//mockCache.EXPECT().BatGetUserSendFlag(gomock.Any(), gomock.Any(), gomock.Any()).Return(map[uint32]bool{1: true}, nil),
		mockCache.EXPECT().GetCommonConf(gomock.Any()).Return([]*mysql.MagicSpiritCommonConf{
			{ConfId: uint32(pb.CommonConfType_PER_ORDER_COUNT_LIMIT), Value: "1000", ValueType: 1},
			{ConfId: uint32(pb.CommonConfType_DAILY_SEND_MONEY_LIMIT), Value: "1000", ValueType: 1},
			{ConfId: uint32(pb.CommonConfType_DAILY_PREVENT_EXCHANGE_LIMIT), Value: "1000", ValueType: 1},
		}, nil),
		mockCache.EXPECT().GetSendDailyPrice(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(uint32(100), nil),
		mockCache.EXPECT().GetSendToOtherDailyPriceList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return([]uint32{100}, nil),
		//mockCache.EXPECT().GetRealNameCheck(gomock.Any(), gomock.Any()).Return(true, true, nil),
		//mockCfg.EXPECT().CheckIfInWhiteList(gomock.Any()).Return(false),
		//mockCfg.EXPECT().GetDisableSwitch().Return(false),
		//mockCache.EXPECT().GetSendValid(gomock.Any(), gomock.Any()).Return(true, true, nil),

		mockStore.EXPECT().CreateMagicSpiritOrder(gomock.Any(), gomock.Any()).Return(nil),

		mockCfg.EXPECT().GetPayAppId().Return(""),
		mockPay.EXPECT().PresetFreeze(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(0), nil),
		mockStore.EXPECT().UpdateMagicSpiritOrderStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil),

		mockCfg.EXPECT().GetUnpackGiftDelaySec().AnyTimes().Return(uint32(10)),
		mockProbGame.EXPECT().CheckFuse(gomock.Any(), gomock.Any()).Return(&probgamecenter2.CheckFuseResp{
			CheckResultList: []*probgamecenter2.CheckFuseResult{
				{OrderId: "test", DealToken: "test"},
			},
		}, nil),
		mockCache.EXPECT().IncrHourProfit(gomock.Any(), gomock.Any(), gomock.Any()).Return(int64(10), nil),
		mockCfg.EXPECT().GetProfitFusingMin().Return(int64(100)),
		mockStore.EXPECT().Transaction(gomock.Any(), gomock.Any()).Return(nil),
		mockCache.EXPECT().IncrSendDailyPrice(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(uint32(0), nil),
		mockCache.EXPECT().IncrSendToOtherDailyPrice(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(nil),
		mockCfg.EXPECT().GetUserInvestFlagExpireDay().Return(uint32(30)),
		mockCache.EXPECT().SetUserSendFlagWithExpire(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),

		mockCfg.EXPECT().GetCallbackTestList().Return([]uint32{}),

		mockAccount.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&accountCli.User{}, nil),
		mockCfg.EXPECT().GetPayAppId().Return(""),
		mockPay.EXPECT().UnfreezeAndConsume(gomock.Any(), gomock.Any()).Return("2006-01-02 15:04:05", "test", nil),
		mockStore.EXPECT().UpdateMagicSpiritAwardTBeanTime(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil),
		mockStore.EXPECT().UpdateMagicSpiritOrderTBeanTime(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil),

		mockCfg.EXPECT().GetCombMaxIntervalSec().Return(uint32(10)),
		mockCache.EXPECT().GetCombCnt(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(0), nil),
		mockCfg.EXPECT().GetCombMaxIntervalSec().Return(uint32(10)),
		mockCache.EXPECT().IncrCombCnt(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(0), nil),
		mockCfg.EXPECT().GetCombValidCnt().Return(uint32(1)),
		mockCfg.EXPECT().GetCombLevelConfList().Return([]*conf.CombConf{
			{Level: 1, Score: 1}, {Level: 2, Score: 3},
		}),

		mockCfg.EXPECT().GetUnpackGiftDelaySec().AnyTimes().Return(uint32(10)),
		mockCache.EXPECT().AddUnpackOrderList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(nil),
		mockCache.EXPECT().GetChannelUnpackOrderIdList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return([]string{"test1"}, nil),
		mockCache.EXPECT().BatchGetUnpackOrderInfo(gomock.Any(), gomock.Any()).AnyTimes().Return(map[string]*pb.UnpackGiftInfo{
			"test1": {ItemOrderId: "test1"},
		}, nil),
		mockAccount.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).AnyTimes().Return(map[uint32]*accountCli.User{}, nil),
		mockChanel.EXPECT().GetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&channelsvr.ChannelSimpleInfo{}, nil),
		mockPush.EXPECT().PushMulticasts(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(nil),
		mockProducer.EXPECT().SendMsg(gomock.Any(), gomock.Any()).Return(),
	)

	t.Run("TestMagicSpiritMgr_SendMagicSpirit", func(t *testing.T) {
		_, err := m.SendMagicSpirit(context.Background(), &pb.SendMagicSpiritReq{
			Uid: 1, ChannelId: 1, MagicSpiritId: 1, TargetUidList: []uint32{1}, AverageCnt: 10, SeparateUnpackGift: true,
		})
		if err != nil {
			t.Errorf("SendMagicSpirit fail. err:%v", err)
		}
	})
}

func TestMagicSpiritMgr_SetMagicSpiritTemporary(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)

	m := &MagicSpiritMgr{
		mysql: mockStore,
	}

	in := &pb.MagicSpiritTmp{
		//Id:                   0,
		EffectTime: uint32(time.Now().Unix()),
		Conf: &pb.MagicSpirit{
			MagicSpiritId: 1,
		},
	}

	gomock.InOrder(
		mockStore.EXPECT().AddMagicSpiritTmp(gomock.Any(), gomock.Any()),
	)

	t.Run("TestMagicSpiritMgr_SetMagicSpiritTemporary", func(t *testing.T) {
		err := m.SetMagicSpiritTemporary(context.Background(), in)
		if err != nil {
			t.Errorf("SetMagicSpiritTemporary fail. err:%v", err)
		}
	})
}

func TestMagicSpiritMgr_GetMagicSpiritByIds(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)

	m := &MagicSpiritMgr{
		mysql: mockStore,
		cache: mockCache,
	}

	gomock.InOrder(
		mockCache.EXPECT().GetMagicSpiritById(gomock.Any(), gomock.Any()).AnyTimes(),
		mockStore.EXPECT().GetMagicSpiritByIds(gomock.Any(), gomock.Any()).Return(map[uint32]*mysql.MagicSpirit{
			1: {
				MagicSpiritId: 1,
			},
		}, nil),
	)

	t.Run("TestMagicSpiritMgr_GetMagicSpiritByIds", func(t *testing.T) {
		_, err := m.GetMagicSpiritByIds(context.Background(), []uint32{1, 2})
		if err != nil {
			t.Errorf("GetMagicSpiritByIds fail. err:%v", err)
		}
	})
}

func TestMagicSpiritMgr_GetMagicSpiritPondTmp(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)

	m := &MagicSpiritMgr{
		mysql: mockStore,
	}

	gomock.InOrder(
		mockStore.EXPECT().GetMagicSpiritPondTmp(gomock.Any()).Return([]*mysql.MagicSpiritPond{
			{
				ItemId:        1,
				MagicSpiritId: 1,
				Weight:        1,
				PresentId:     1,
				Price:         10,
				BeginTime:     time.Now().Add(-10 * time.Second),
			},
		}, nil),
	)

	t.Run("TestMagicSpiritMgr_GetMagicSpiritPondTmp", func(t *testing.T) {
		_, err := m.GetMagicSpiritPondTmp(context.Background(), time.Now())
		if err != nil {
			t.Errorf("GetMagicSpiritPondTmp fail. err:%v", err)
		}
	})
}

func TestMagicSpiritMgr_GetMagicSpiritTmp(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)

	m := &MagicSpiritMgr{
		mysql: mockStore,
	}

	gomock.InOrder(
		mockStore.EXPECT().GetMagicSpiritTmp(gomock.Any(), gomock.Any()).Return([]*mysql.MagicSpiritTemporary{
			{
				Id:            1,
				MagicSpiritId: 2,
				BeginTime:     time.Now().Add(10 * time.Minute),
				UpdateFlag:    0,
			},
		}, nil),
	)

	t.Run("GetMagicSpiritTmp", func(t *testing.T) {
		_, err := m.GetMagicSpiritTmp(context.Background())
		if err != nil {
			t.Errorf("GetMagicSpiritTmp fail. err:%v", err)
		}
	})
}

//func Test_getVerifyInfo(t *testing.T) {
//	ctl := gomock.NewController(t)
//	defer ctl.Finish()
//
//	mockStore := mocks.NewMockIStore(ctl)
//	mockConf := mocks.NewMockIBusinessConfManager(ctl)
//
//	m := &MagicSpiritMgr{
//		mysql: mockStore,
//		bc:    mockConf,
//	}
//
//	gomock.InOrder(
//		mockConf.EXPECT().GetRealNameAccess().Return(conf.RealNameAccessCfg{
//			ServerHost:   "127.0.0.1",
//			ClientSecret: "fsadgasg",
//			ClientCaller: "sgasdg",
//		}),
//	)
//
//	t.Run("TestMagicSpiritMgr_getVerifyInfo", func(t *testing.T) {
//		_, _, _, _ = m.getVerifyInfo(context.Background(), uint32(194235))
//	})
//}
//
//func TestNewMagicPondLocalCache(t *testing.T) {
//	ctrl := gomock.NewController(t)
//	defer ctrl.Finish()
//	log.SetLevel(log.DebugLevel)
//	sc := &conf.ServiceConfigT{}
//	err := sc.Parse("../magic-spirit.json")
//	if err != nil {
//		log.Errorf("config Parse fail err:%v", err)
//		return
//	}
//
//	redisClient := redis.NewClient(&redis.Options{
//		Network:            sc.GetRedisConfig().Protocol,
//		Addr:               sc.GetRedisConfig().Addr(),
//		PoolSize:           sc.GetRedisConfig().PoolSize,
//		IdleCheckFrequency: sc.GetRedisConfig().IdleCheckFrequency(),
//		DB:                 sc.GetRedisConfig().DB,
//	})
//	log.Debugf("Initialized redis %s", sc.GetRedisConfig().Print())
//	redisTracer := tracing.Init("star-trek-redis")
//
//	cacheClient, err := cache.NewCache(redisClient, redisTracer)
//	if err != nil {
//		log.Errorf("NewCache fail %+v, err:%v", sc.GetRedisConfig(), err)
//		return
//	}
//
//	mysqlStore, err := mysql.NewMysql(sc.GetMysqlConfig(), sc.GetMysqlReadOnlyConfig())
//	if err != nil {
//		log.Errorf("NewMysql fail %+v, %+v, err:%v", sc.GetMysqlConfig(), sc.GetMysqlReadOnlyConfig(), err)
//		return
//	}
//
//	_, err = NewMagicPondLocalCache(mysqlStore, cacheClient)
//	t.Log(err)
//}

//func Test_checkUserPersonalInfo(t *testing.T) {
//	ctl := gomock.NewController(t)
//	defer ctl.Finish()
//
//	mockStore := mocks.NewMockIStore(ctl)
//	mockNumeric := numeric.NewMockIClient(ctl)
//	mockExp := exp.NewMockIClient(ctl)
//	mockBc := mocks.NewMockIBusinessConfManager(ctl)
//
//	m := &MagicSpiritMgr{
//		mysql:      mockStore,
//		numericCli: mockNumeric,
//		expCli:     mockExp,
//		bc:         mockBc,
//	}
//
//	mockNumeric.EXPECT().GetPersonalNumericData(gomock.Any(), gomock.Any()).Return(&numericsvr.GetPersonalNumericResp{
//		ConsumeNumeric:   1,
//		CharmNumeric:     1,
//		ConsumeNumeric64: 1,
//		CharmNumeric64:   1,
//	}, nil)
//	mockExp.EXPECT().GetUserExp(gomock.Any(), gomock.Any()).Return(int32(100000), uint32(10000), nil)
//	mockBc.EXPECT().GetMagicSpiritWealthLv().Return(uint32(1000))
//	mockBc.EXPECT().GetMagicSpiritCharmLv().Return(uint32(100))
//	mockBc.EXPECT().GetMagicSpiritExpLvLimit().Return(uint32(1))
//
//	_, err := m.checkUserPersonalInfo(context.Background(), uint32(111))
//	if err != nil {
//		t.Errorf("checkUserPersonalInfo fail. err:%v", err)
//	}
//	time.Sleep(1 * time.Second)
//}

func TestMagicSpiritMgr_ReissueMagicOrder(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockBc := mocks.NewMockIBusinessConfManager(ctl)
	mockAccount := account.NewMockIClient(ctl)
	mockPay := unifiedPay.NewMockIClient(ctl)
	mockProbGame := probgamecenter.NewMockIClient(ctl)
	mockPresentM := present_middleware.NewMockIClient(ctl)

	m := &MagicSpiritMgr{
		mysql:         mockStore,
		cache:         mockCache,
		bc:            mockBc,
		accountCli:    mockAccount,
		unifiedPayCli: mockPay,
		probGameCli:   mockProbGame,
		presentMWCli:  mockPresentM,
	}

	type args struct {
		c       context.Context
		orderId string
	}
	orderId := "test"
	OutsideTime := time.Now().Add(-2 * time.Minute)
	tbeanToken := "{\"tradeNo\":\"202211031630221670000f676aa\",\"orderID\":\"magic_2520915_23_1667459418018829315\",\"sign\":\"6d0aa293a52bfb8d77b881e5505fc4fa\"," +
		"\"ctime\":\"2022-11-03 16:30:22\",\"serverName\":\"tbean\",\"buyerId\":2520915,\"totalPirce\":1000,\"prevToken\":\"\",\"prevMd5\":\"\"}"
	someErr := errors.New("some errs")

	tests := []struct {
		name     string
		initFunc func()
		args     args
		want     error
		wantErr  bool
	}{
		{
			name: "补单成功",
			initFunc: func() {
				mockStore.EXPECT().GetMagicSpiritAwardLog(gomock.Any(), gomock.Any(), gomock.Any()).Return(&mysql.MagicSpiritAwardLog{}, false, nil)
				mockStore.EXPECT().GetMagicSpiritAwardLog(gomock.Any(), gomock.Any(), gomock.Any()).Return(&mysql.MagicSpiritAwardLog{
					Id:              1,
					OrderId:         "test",
					MagicOrderId:    "test",
					MagicId:         1,
					MagicTotalPrice: 1000,
					ChannelId:       1,
					FromUid:         1,
					ToUid:           2,
					GiftId:          1,
					Num:             10,
					GiftTotalPrice:  10000,
					TBeanTimeStr:    "1",
					TBeanDealToken:  "", // 需要重新commit
					OutsideTime:     OutsideTime,
					CreateTime:      OutsideTime,
					UpdateTime:      OutsideTime,
					AwardTime:       OutsideTime,
					IsDone:          true,
				}, true, nil)
				mockCache.EXPECT().GetMagicSpiritById(gomock.Any(), gomock.Any()).Return(&mysql.MagicSpirit{}, false, nil)
				mockStore.EXPECT().GetMagicSpiritById(gomock.Any(), gomock.Any()).Return(&mysql.MagicSpirit{
					MagicSpiritId:    1,
					Name:             "11",
					IconUrl:          "11",
					Price:            100,
					Rank:             1,
					EffectBegin:      1,
					EffectEnd:        1,
					DescribeImageUrl: "11",
					Describe:         "11",
					HasPond:          true,
				}, nil)
				// 获取消费order
				mockStore.EXPECT().GetMagicSpiritOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(&mysql.MagicSpiritOrder{}, false, nil)
				mockStore.EXPECT().GetMagicSpiritOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(&mysql.MagicSpiritOrder{
					OrderId:    "test",
					Status:     mysql.OrderStatusFinish,
					CreateTime: time.Now().Add(-5 * time.Minute),
				}, true, nil)
				// commit
				mockAccount.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&accountCli.User{}, nil)
				mockBc.EXPECT().GetPayAppId().Return("")
				mockPay.EXPECT().UnfreezeAndConsume(gomock.Any(), gomock.Any()).Return("2006-01-02 15:04:05", tbeanToken, nil)
				mockStore.EXPECT().UpdateMagicSpiritAwardTBeanTime(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil)
				mockStore.EXPECT().UpdateMagicSpiritOrderTBeanTime(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil)
				// 获取token
				mockProbGame.EXPECT().CheckFuse(gomock.Any(), gomock.Any()).Return(&probgamecenter2.CheckFuseResp{
					CheckResultList: []*probgamecenter2.CheckFuseResult{
						{OrderId: "test", DealToken: tbeanToken},
					},
				}, nil)
				mockPresentM.EXPECT().MagicSendPresent(gomock.Any(), gomock.Any()).Return(nil, nil)

			},
			args: args{
				c:       context.Background(),
				orderId: orderId,
			},
			want:    nil,
			wantErr: false},

		{
			name: "下游服务失败1",
			initFunc: func() {
				mockStore.EXPECT().GetMagicSpiritAwardLog(gomock.Any(), gomock.Any(), gomock.Any()).Return(&mysql.MagicSpiritAwardLog{}, false, someErr)
			},
			args: args{
				c:       context.Background(),
				orderId: orderId,
			},
			want:    someErr,
			wantErr: true,
		},
		{
			name: "下游服务失败2",
			initFunc: func() {
				mockStore.EXPECT().GetMagicSpiritAwardLog(gomock.Any(), gomock.Any(), gomock.Any()).Return(&mysql.MagicSpiritAwardLog{}, false, nil)
				mockStore.EXPECT().GetMagicSpiritAwardLog(gomock.Any(), gomock.Any(), gomock.Any()).Return(&mysql.MagicSpiritAwardLog{}, false, someErr)
			},
			args: args{
				c:       context.Background(),
				orderId: orderId,
			},
			want:    someErr,
			wantErr: true,
		},
		{
			name: "下游服务失败3",
			initFunc: func() {
				mockStore.EXPECT().GetMagicSpiritAwardLog(gomock.Any(), gomock.Any(), gomock.Any()).Return(&mysql.MagicSpiritAwardLog{}, false, nil)
				mockStore.EXPECT().GetMagicSpiritAwardLog(gomock.Any(), gomock.Any(), gomock.Any()).Return(&mysql.MagicSpiritAwardLog{
					Id:              1,
					OrderId:         "test",
					MagicOrderId:    "test",
					MagicId:         1,
					MagicTotalPrice: 1000,
					ChannelId:       1,
					FromUid:         1,
					ToUid:           2,
					GiftId:          1,
					Num:             10,
					GiftTotalPrice:  10000,
					TBeanTimeStr:    "1",
					TBeanDealToken:  "", // 需要重新commit
					OutsideTime:     OutsideTime,
					CreateTime:      OutsideTime,
					UpdateTime:      OutsideTime,
					AwardTime:       OutsideTime,
					IsDone:          true,
				}, true, nil)
				mockCache.EXPECT().GetMagicSpiritById(gomock.Any(), gomock.Any()).Return(&mysql.MagicSpirit{}, false, nil)
				mockStore.EXPECT().GetMagicSpiritById(gomock.Any(), gomock.Any()).Return(&mysql.MagicSpirit{}, someErr)
			},
			args: args{
				c:       context.Background(),
				orderId: orderId,
			},
			want:    someErr,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.initFunc != nil {
				tt.initFunc()
			}
			err := m.ReissueMagicOrder(tt.args.c, tt.args.orderId)
			if (err != nil) != tt.wantErr {
				t.Errorf("ReissueMagicOrder() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestGenMagicDealToken(t *testing.T) {
	dt := deal_token.NewDealTokenData("1", "1", "1", 1, 1)
	str, _ := deal_token.Encode(dt)
	str, _ = deal_token.AddDealToken(str, deal_token.NewDealTokenData("2", "1", "2", 1, 1))

	str, _ = deal_token.AddDealToken(str, deal_token.NewDealTokenData("3", "1", "3", 1, 1))

	newStr := updateDealTokenOrderId(str, "2")
	dtStr := newStr
	for {
		dt, _ = deal_token.Decode(dtStr)
		if dt.OrderID != "2" {
			t.Errorf("fail. %v", dt)
			break
		}

		t.Log(dt.TradeNo, dt.OrderID)
		if dt.PrevToken == "" {
			break
		}

		dtStr = dt.PrevToken
	}
}

func TestMagicSpiritMgr_CheckIfSendMagicWithSource(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)
	entryMock := chancegameentry.NewMockIClient(ctl)
	mockStore := mocks.NewMockIStore(ctl)

	m := &MagicSpiritMgr{
		cache:              mockCache,
		bc:                 mockCfg,
		chanceGameEntryCli: entryMock,
		mysql:              mockStore,
	}

	type args struct {
		ctx context.Context
		in  *pb.CheckIfSendMagicWithSourceReq
	}
	tests := []struct {
		name     string
		initFunc func()
		args     args
		want     bool
		wantErr  bool
	}{
		{
			name: "TestMagicSpiritMgr_CheckIfSendMagicWithSource",
			initFunc: func() {
				mockCache.EXPECT().GetMagicSpiritById(gomock.Any(), gomock.Any()).Return(nil, false, nil)
				mockStore.EXPECT().GetMagicSpiritById(gomock.Any(), gomock.Any()).Return(&mysql.MagicSpirit{}, nil)
				mockCache.EXPECT().CheckIfFusing(gomock.Any()).Return(false, nil)
				//mockCache.EXPECT().ExistsBlacklist(gomock.Any()).Return(true, nil),
				//mockCache.EXPECT().IsBlacklist(gomock.Any(), gomock.Any()).Return(false, nil),
				mockCfg.EXPECT().GetOldVersionAppAccessCheck().Return(false)
				entryMock.EXPECT().CheckMagicSpiritAccess(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&entryPb.CheckMagicSpiritAccessResp{
						Switch: true,
						ConfList: []*entryPb.MagicSpiritAccess{
							&entryPb.MagicSpiritAccess{
								MagicSpiritId: 1,
								Access:        true,
								ConditionList: []*entryPb.AccessCondition{
									&entryPb.AccessCondition{
										SubList: []*entryPb.SubAccessCondition{
											&entryPb.SubAccessCondition{
												ConditionType: 7,
												Threshold:     0,
											},
										},
									}},
								RelateType: 0,
							}},
					}, nil)
				//mockCache.EXPECT().BatGetUserSendFlag(gomock.Any(), gomock.Any(), gomock.Any()).Return(map[uint32]bool{1: true}, nil)
				mockCache.EXPECT().GetCommonConf(gomock.Any()).Return([]*mysql.MagicSpiritCommonConf{
					{ConfId: uint32(pb.CommonConfType_PER_ORDER_COUNT_LIMIT), Value: "1000", ValueType: 1},
					{ConfId: uint32(pb.CommonConfType_DAILY_SEND_MONEY_LIMIT), Value: "1000", ValueType: 1},
					{ConfId: uint32(pb.CommonConfType_DAILY_PREVENT_EXCHANGE_LIMIT), Value: "1000", ValueType: 1},
				}, nil)
				mockCache.EXPECT().GetSendDailyPrice(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(uint32(100), nil)
				mockCache.EXPECT().GetSendToOtherDailyPriceList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return([]uint32{100}, nil)
			},
			args: args{
				ctx: context.Background(),
				in: &pb.CheckIfSendMagicWithSourceReq{
					Uid:           1,
					ChannelId:     1,
					MagicSpiritId: 1,
					Amount:        1,
					Source:        1,
				},
			},
			want:    true,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := m
			if tt.initFunc != nil {
				tt.initFunc()
			}
			got, err := m.CheckIfSendMagicWithSource(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckIfSendMagicWithSource() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("CheckIfSendMagicWithSource() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMagicSpiritMgr_GenFinancialFile(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)
	entryMock := chancegameentry.NewMockIClient(ctl)
	mockStore := mocks.NewMockIStore(ctl)

	m := &MagicSpiritMgr{
		cache:              mockCache,
		bc:                 mockCfg,
		chanceGameEntryCli: entryMock,
		mysql:              mockStore,
	}

	type args struct {
		ctx context.Context
		req *reconcile_v2.GenFinancialFileReq
	}
	tests := []struct {
		name     string
		initFunc func()
		args     args
		wantErr  bool
	}{
		{
			name:     "param err",
			initFunc: nil,
			args: args{
				ctx: context.Background(),
				req: &reconcile_v2.GenFinancialFileReq{
					BeginTime: time.Now().Unix(),
					EndTime:   time.Now().Add(-10 * time.Minute).Unix(),
				},
			},
			wantErr: true,
		},
		{
			name: "success",
			initFunc: func() {
				mockStore.EXPECT().GetReconcileSumStats(gomock.Any(), gomock.Any()).Return([]*mysql.ReconcileDataLog{
					{
						Id:                1,
						LogTime:           time.Now(),
						MagicId:           1,
						Source:            0,
						BuyPrice:          100,
						BuyPriceTBeanTime: 100,
						Num:               1,
						GiftTotalPrice:    100,
						PeopleCnt:         1,
					},
				}, nil)
				mockStore.EXPECT().RecordReconcileDataLogs(gomock.Any()).Return(nil)
			},
			args: args{
				ctx: context.Background(),
				req: &reconcile_v2.GenFinancialFileReq{
					BeginTime:  time.Now().Unix(),
					EndTime:    time.Now().Add(10 * time.Minute).Unix(),
					TbeanPrice: 100,
				},
			},
			wantErr: false,
		},
		{
			name: "mysql error1",
			initFunc: func() {
				mockStore.EXPECT().GetReconcileSumStats(gomock.Any(), gomock.Any()).Return([]*mysql.ReconcileDataLog{
					{
						Id:                1,
						LogTime:           time.Now(),
						MagicId:           1,
						Source:            0,
						BuyPrice:          100,
						BuyPriceTBeanTime: 100,
						Num:               1,
						GiftTotalPrice:    100,
						PeopleCnt:         1,
					},
				}, errors.New("mysql error"))
			},
			args: args{
				ctx: context.Background(),
				req: &reconcile_v2.GenFinancialFileReq{
					BeginTime:  time.Now().Unix(),
					EndTime:    time.Now().Add(10 * time.Minute).Unix(),
					TbeanPrice: 100,
				},
			},
			wantErr: true,
		},
		{
			name: "mysql error2",
			initFunc: func() {
				mockStore.EXPECT().GetReconcileSumStats(gomock.Any(), gomock.Any()).Return([]*mysql.ReconcileDataLog{
					{
						Id:                1,
						LogTime:           time.Now(),
						MagicId:           1,
						Source:            0,
						BuyPrice:          100,
						BuyPriceTBeanTime: 100,
						Num:               1,
						GiftTotalPrice:    100,
						PeopleCnt:         1,
					},
				}, nil)
				mockStore.EXPECT().RecordReconcileDataLogs(gomock.Any()).Return(errors.New("mysql error2"))
			},
			args: args{
				ctx: context.Background(),
				req: &reconcile_v2.GenFinancialFileReq{
					BeginTime:  time.Now().Unix(),
					EndTime:    time.Now().Add(10 * time.Minute).Unix(),
					TbeanPrice: 100,
				},
			},
			wantErr: true,
		},
		{
			name: "提前结束",
			initFunc: func() {
				mockStore.EXPECT().GetReconcileSumStats(gomock.Any(), gomock.Any()).Return([]*mysql.ReconcileDataLog{
					{
						Id:                1,
						LogTime:           time.Now(),
						MagicId:           1,
						Source:            0,
						BuyPrice:          100,
						BuyPriceTBeanTime: 100,
						Num:               1,
						GiftTotalPrice:    100,
						PeopleCnt:         1,
					},
					{
						Id:                1,
						LogTime:           time.Now(),
						MagicId:           1,
						Source:            1,
						BuyPrice:          100,
						BuyPriceTBeanTime: 100,
						Num:               1,
						GiftTotalPrice:    100,
						PeopleCnt:         1,
					},
				}, nil)
			},
			args: args{
				ctx: context.Background(),
				req: &reconcile_v2.GenFinancialFileReq{
					BeginTime:  time.Now().Unix(),
					EndTime:    time.Now().Add(10 * time.Minute).Unix(),
					TbeanPrice: 0,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.initFunc != nil {
				tt.initFunc()
			}
			if err := m.GenFinancialFile(tt.args.ctx, tt.args.req); (err != nil) != tt.wantErr {
				t.Errorf("GenFinancialFile() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
