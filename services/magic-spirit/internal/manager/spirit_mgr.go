package manager

import (
	"context"
	"fmt"
	"github.com/jinzhu/gorm"
	"golang.52tt.com/protocol/app/channel"
	"strconv"
	"strings"
	"time"

	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	pb "golang.52tt.com/protocol/services/magic-spirit"
	"golang.52tt.com/services/magic-spirit/internal/mysql"
)

const MAGICSPIRIT_USABLE = uint32(1)

func (m *MagicSpiritMgr) AddMagicSpirit(ctx context.Context, in *pb.MagicSpirit) (uint32, error) {
	// 设置缓存2s过期
	err := m.cache.ExpireMagicSpirit(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetMagicSpirit pre del cache error: %v", err)
		return 0, err
	}

	data := pbMagicSpirit2Db(in)

	// 写库1s超时, 确保缓存失效前完成变更
	timeoutCtx, cancel := context.WithTimeout(ctx, WriteDBTimeout)
	defer cancel()
	id, err := m.mysql.AddMagicSpirit(timeoutCtx, data)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetMagicSpirit insert db error: %v", err)
		return 0, err
	}

	err = m.cache.DelMagicSpiritCache(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetMagicSpirit post del cache error: %v", err)
		return 0, err
	}

	return id, nil
}

func (m *MagicSpiritMgr) SetMagicSpiritTemporary(ctx context.Context, in *pb.MagicSpiritTmp) error {
	magicSpirit := in.GetConf()

	channelTypeStrList := make([]string, 0, len(magicSpirit.GetChannelTypeList()))
	for _, v := range magicSpirit.GetChannelTypeList() {
		channelTypeStrList = append(channelTypeStrList, fmt.Sprintf("%d", v))
	}

    actCfg := in.GetConf().GetActivityCfg()
	data := &mysql.MagicSpiritTemporary{
		MagicSpiritId:    in.GetId(),
		Name:             magicSpirit.GetName(),
		IconUrl:          magicSpirit.GetIconUrl(),
		Price:            magicSpirit.GetPrice(),
		Ranking:          magicSpirit.GetRank(),
		EffectBegin:      magicSpirit.GetEffectBegin(),
		EffectEnd:        magicSpirit.GetEffectEnd(),
		DescribeImageUrl: magicSpirit.GetDescribeImageUrl(),
		GiftDescribe:     magicSpirit.GetDescribe(),
		JuniorLighting:   magicSpirit.GetJuniorLighting(),
		MiddleLighting:   magicSpirit.GetMiddleLighting(),
		VfxResource:      magicSpirit.GetVfxResource(),
		VfxResourceMd5:   magicSpirit.GetVfxResourceMd5(),
		UpdateTime:       time.Now(),
		BeginTime:        time.Unix(int64(in.GetEffectTime()), 0),
		RankFloat:        magicSpirit.GetRankFloat(),
		//ActivityJumpUrl:  magicSpirit.GetDescActivityUrl(),
		ChannelTypeList:  strings.Join(channelTypeStrList, ","),

        ShowEffectEnd:          in.GetConf().GetShowEffectEnd(),
        ActName:                actCfg.GetActivityName(),
        ActBeginTime:           time.Unix(actCfg.GetBeginTime(), 0),
        ActEndTime:             time.Unix(actCfg.GetEndTime(), 0),
        ActivityJumpUrl:        actCfg.GetJumpUrlTt(),
        ActImageUrl:            actCfg.GetImageUrl(),
        ActJumpUrlHcAndroid:    actCfg.GetJumpUrlHcAndroid(),
        ActJumpUrlHcIos:        actCfg.GetJumpUrlHcIos(),
        ActJumpUrlMikeAndroid:  actCfg.GetJumpUrlMikeAndroid(),
        ActJumpUrlMikeIos:      actCfg.GetJumpUrlMikeIos(),
	}

	_, err := m.mysql.AddMagicSpiritTmp(ctx, data)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddMagicSpiritTmp error: %v", err)
		return err
	}

	return nil
}

func (m *MagicSpiritMgr) UpdateMagicSpirit(ctx context.Context, in *pb.MagicSpirit, beginTime time.Time) error {
	// 设置缓存2s过期
	err := m.cache.ExpireMagicSpirit(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateMagicSpirit fail to DelMagicSpiritCache. err:%v", err)
		return err
	}

	channelTypeStrList := make([]string, 0, len(in.GetChannelTypeList()))
	for _, v := range in.GetChannelTypeList() {
		channelTypeStrList = append(channelTypeStrList, fmt.Sprintf("%d", v))
	}

	data := pbMagicSpirit2Db(in)

	err = m.mysql.Transaction(ctx, func(tx *gorm.DB) error {
		err = m.mysql.UpdateMagicSpirit(ctx, data)
		if err != nil {
			log.ErrorWithCtx(ctx, "store UpdateMagicSpirit error: %v", err)
			return err
		}

		err = m.mysql.SetMagicSpiritUpdateFlag(ctx, data.MagicSpiritId, beginTime)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateMagicSpirit SetMagicSpiritUpdateFlag error: %v", err)
			return err
		}

		return nil
	})
	if err != nil {
		log.Errorf("UpdateMagicSpirit fail to Transaction. err:%v, data:%v", err, data)
		return err
	}

	err = m.cache.DelMagicSpiritCache(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateMagicSpirit pre del cache err： %v", err)
		return err
	}

	return nil
}

func (m *MagicSpiritMgr) GetMagicSpirit(ctx context.Context) ([]*mysql.MagicSpirit, error) {
	data, err := m.mysql.GetMagicSpirit(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMagicSpirit db find error: %v", err)
		return nil, err
	}

	return data, nil
}

func (m *MagicSpiritMgr) GetMagicSpiritWithCache(ctx context.Context) ([]*mysql.MagicSpirit, error) {
	var err error

	cache, err := m.cache.GetMagicSpirit(ctx)
	if len(cache) == 0 {
		if err != nil {
			log.ErrorWithCtx(ctx, "GetMagicSpiritWithCache get by cache error: %v", err)
		}
		data, err := m.GetMagicSpirit(ctx)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetMagicSpiritWithCache error: %v", err)
			return nil, err
		}
		err = m.cache.SetMagicSpirit(ctx, data)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetMagicSpiritWithCache set cache error: %v", err)
			return nil, err
		}

		return data, nil
	}

	return cache, nil
}

func (m *MagicSpiritMgr) DelMagicSpirit(ctx context.Context, ids []uint32) error {
	err := m.mysql.DelMagicSpirit(ctx, ids)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelMagicSpirit error: %v", err)
		return err
	}

	// 未生效的礼物配置也需要del
	err = m.mysql.DelMagicSpiritTmp(ctx, ids)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelMagicSpiritTmp error: %v", err)
		return err
	}

	err = m.cache.DelMagicSpiritCache(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelMagicSpirit pre del cache error： %v", err)
		return err
	}

	return nil
}

func (m *MagicSpiritMgr) GetMagicSpiritById(ctx context.Context, magicSpiritId uint32) (*pb.MagicSpirit, error) {
	info := &pb.MagicSpirit{}

	data, exist, err := m.cache.GetMagicSpiritById(ctx, magicSpiritId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMagicSpiritById get cache error: %v", err)
		return info, err
	}

	if !exist {
		data, err = m.mysql.GetMagicSpiritById(ctx, magicSpiritId)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetMagicSpiritById find db error: %v", err)
			return info, err
		}

		log.Debugf("GetMagicSpiritById get from db, magicSpiritId: %v", magicSpiritId)
	}

	return fillMagicSpiritTmp2PB(data), nil
}

func (m *MagicSpiritMgr) GetMagicSpiritByIds(ctx context.Context, magicSpiritIds []uint32) (map[uint32]*pb.MagicSpirit, error) {
	info := make(map[uint32]*pb.MagicSpirit)

	searchFromDB := make([]uint32, 0)
	for _, v := range magicSpiritIds {
		data, exist, err := m.cache.GetMagicSpiritById(ctx, v)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetMagicSpiritById get cache error: %v", err)
			return info, err
		}
		if !exist {
			searchFromDB = append(searchFromDB, v)
			continue
		}
		info[v] = fillMagicSpiritTmp2PB(data)
	}

	if len(searchFromDB) == 0 {
		return info, nil
	}

	magicMap, err := m.mysql.GetMagicSpiritByIds(ctx, searchFromDB)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMagicSpiritById find db error: %v", err)
		return info, err
	}

	log.Debugf("GetMagicSpiritByIds get from db, magicSpiritIds: %v", searchFromDB)
	for k, v := range magicMap {
		info[k] = fillMagicSpiritTmp2PB(v)
	}

	return info, nil
}

func fillMagicSpiritTmp2PB(data *mysql.MagicSpirit) *pb.MagicSpirit {
	if data == nil {
		return nil
	}

	splitSegment := strings.Split(data.ChannelTypeList, ",")
	channelTypeList := make([]uint32, 0, len(splitSegment))
	for _, v := range splitSegment {
		n, err := strconv.Atoi(v)
		if err != nil {
			log.Warnf("fillMagicSpiritTmp2PB fail strconv.Atoi, err:%v", err)
			continue
		}
		channelTypeList = append(channelTypeList, uint32(n))
	}
	return &pb.MagicSpirit{
		MagicSpiritId:    data.MagicSpiritId,
		Name:             data.Name,
		IconUrl:          data.IconUrl,
		Price:            data.Price,
		Rank:             data.Rank,
		EffectBegin:      data.EffectBegin,
		EffectEnd:        data.EffectEnd,
		DescribeImageUrl: data.DescribeImageUrl,
		Describe:         data.Describe,
		JuniorLighting:   data.JuniorLighting,
		MiddleLighting:   data.MiddleLighting,
		VfxResource:      data.VfxResource,
		VfxResourceMd5:   data.VfxResourceMd5,
		UpdateTime:       uint32(data.UpdateTime.Unix()),
		RankFloat:        data.RankFloat,
		DescActivityUrl:  data.ActivityJumpUrl,
		ChannelTypeList:  channelTypeList,
        ShowEffectEnd:    data.ShowEffectEnd,
        ActivityCfg: &pb.MagicSpiritActivityCfg{
            ActivityName:       data.ActName,
            BeginTime:          data.ActBeginTime.Unix(),
            EndTime:            data.ActEndTime.Unix(),
            ImageUrl:           data.ActImageUrl,
            JumpUrlTt:          data.ActivityJumpUrl,
            JumpUrlHcAndroid:   data.ActJumpUrlHcAndroid,
            JumpUrlHcIos:       data.ActJumpUrlHcIos,
            JumpUrlMikeAndroid: data.ActJumpUrlMikeAndroid,
            JumpUrlMikeIos:     data.ActJumpUrlMikeIos,
        },
	}
}

func (m *MagicSpiritMgr) GetMagicSpiritTmp(ctx context.Context) ([]*pb.MagicSpiritTmp, error) {
	out := make([]*pb.MagicSpiritTmp, 0)

	beginTime := time.Now()
	dbRes, err := m.mysql.GetMagicSpiritTmp(ctx, beginTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "mgr GetMagicSpiritTmp fail, err:%v", err)
		return out, err
	}

	for _, v := range dbRes {
		cfg := &pb.MagicSpiritTmp{
			Id:         v.Id,
			EffectTime: uint32(v.BeginTime.Unix()),
			Conf: &pb.MagicSpirit{
				MagicSpiritId:    v.MagicSpiritId,
				Name:             v.Name,
				IconUrl:          v.IconUrl,
				Price:            v.Price,
				Rank:             v.Ranking,
				EffectBegin:      v.EffectBegin,
				EffectEnd:        v.EffectEnd,
				DescribeImageUrl: v.DescribeImageUrl,
				Describe:         v.GiftDescribe,
				JuniorLighting:   v.JuniorLighting,
				MiddleLighting:   v.MiddleLighting,
				VfxResource:      v.VfxResource,
				VfxResourceMd5:   v.VfxResourceMd5,
				UpdateTime:       uint32(v.UpdateTime.Unix()),
				RankFloat:        v.RankFloat,
				DescActivityUrl:  v.ActivityJumpUrl,
                ShowEffectEnd:    v.ShowEffectEnd,
                ActivityCfg: &pb.MagicSpiritActivityCfg{
                    ActivityName:       v.ActName,
                    BeginTime:          v.ActBeginTime.Unix(),
                    EndTime:            v.ActEndTime.Unix(),
                    ImageUrl:           v.ActImageUrl,
                    JumpUrlTt:          v.ActivityJumpUrl,
                    JumpUrlHcAndroid:   v.ActJumpUrlHcAndroid,
                    JumpUrlHcIos:       v.ActJumpUrlHcIos,
                    JumpUrlMikeAndroid: v.ActJumpUrlMikeAndroid,
                    JumpUrlMikeIos:     v.ActJumpUrlMikeIos,
                },
			},
		}

		splitElements := strings.Split(v.ChannelTypeList, ",")
		channelTypeList := make([]uint32, 0, len(splitElements))
		for _, v := range splitElements {
			n, err := strconv.Atoi(v)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetMagicSpiritTmp fail strconv.Atoi, err:%v", err)
				continue
			}
			channelTypeList = append(channelTypeList, uint32(n))
		}
		cfg.Conf.ChannelTypeList = channelTypeList

		out = append(out, cfg)
	}

	return out, nil
}

func (m *MagicSpiritMgr) GetMagicSpiritUsable(ctx context.Context, req *pb.GetMagicSpiritUsableReq) (*pb.GetMagicSpiritUsableResp, error) {
	resp := &pb.GetMagicSpiritUsableResp{
		CheckIntervalSec: m.bc.GetShowCheckIntervalSec(),
		ChannelId:        req.GetChannelId(),
		Usable:           0,
	}

	// 在开关开启时，旧版客户端走旧逻辑，新版客户端走新逻辑
	if m.bc.GetOldVersionAppAccessCheck() && req.GetOldVersion() {
		access, err := m.getSmashAccessResult(ctx, req.GetUid(), req.GetChannelId(), 0, 0)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetMagicSpiritUsable fail getSmashAccessResult")
			return resp, nil //不把错误返回给客户端
		}
		if access {
			resp.Usable = uint32(1)
		}
		return resp, nil
	}

	// 开关关闭时/新版请求 都走新逻辑
	confNum, magicIdAccessMap, err := m.GetAccessResult(ctx, req.GetUid(), req.GetChannelId(), 0, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMagicSpiritUsable fail GetAccessResult,req:%v,err:%v", req, err)
		return resp, err //不把错误返回给客户端
	}

	accessibleMagicIds := make([]uint32, 0, len(magicIdAccessMap))
	for magicId := range magicIdAccessMap {
		accessibleMagicIds = append(accessibleMagicIds, magicId)
	}

	if confNum == uint32(len(accessibleMagicIds)) {
		resp.Usable = MAGICSPIRIT_USABLE
	}

	validMap := make(map[uint32]bool)
	channelInfo, err := m.channelCli.GetChannelSimpleInfo(ctx, req.GetChannelId(), req.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMagicSpiritUsable fail GetChannelSimpleInfo,req:%v,err:%v", req, err)
		return resp, nil
	}
	magicMap, err := m.mysql.BatchGetMagicSpiritLimitChannelTypeList(ctx, accessibleMagicIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMagicSpiritUsable fail BatchGetMagicSpiritLimitChannelTypeList,req:%v,err:%v", req, err)
		return resp, nil
	}

	for magicId, channelTypeList := range magicMap {
		var isPassType bool
		for _, channelType := range channelTypeList {
			if channelType == channelInfo.GetChannelType() {
				isPassType = true
				break
			}
		}
		validMap[magicId] = isPassType
	}

	finalUsableMagicIds := make([]uint32, 0, len(accessibleMagicIds))
	for _, magicId := range accessibleMagicIds {
		// 配置了房间类型 且 类型匹配
		if (magicMap[magicId] != nil && validMap[magicId]) ||
			// 没配置类型 默认限制cpl房间
			(magicMap[magicId] == nil && channelInfo.GetChannelType() != uint32(channel.ChannelType_CPL_SUPER_CHANNEL_TYPE)) {
			finalUsableMagicIds = append(finalUsableMagicIds, magicId)
		}
	}
	resp.MagicSpiritIds = finalUsableMagicIds
	return resp, nil
}

func (m *MagicSpiritMgr) GetMagicSpiritVersion(ctx context.Context) (uint32, error) {
	version, err := m.cache.GetUpdateMagicSpiritVersion(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMagicSpiritVersion by cache error: %v", err)
		return 0, err
	}

	return version, nil
}

// ================================internal===========================
func pbMagicSpirit2Db(in *pb.MagicSpirit) *mysql.MagicSpirit {
	channelTypeStrList := make([]string, 0, len(in.GetChannelTypeList()))
	for _, v := range in.GetChannelTypeList() {
		channelTypeStrList = append(channelTypeStrList, fmt.Sprintf("%d", v))
	}

	return &mysql.MagicSpirit{
		MagicSpiritId:    in.MagicSpiritId,
		Name:             in.Name,
		IconUrl:          in.IconUrl,
		Price:            in.Price,
		Rank:             in.Rank,
		EffectBegin:      in.EffectBegin,
		EffectEnd:        in.EffectEnd,
		DescribeImageUrl: in.DescribeImageUrl,
		Describe:         in.Describe,
		JuniorLighting:   in.JuniorLighting,
		MiddleLighting:   in.MiddleLighting,
		VfxResource:      in.VfxResource,
		VfxResourceMd5:   in.VfxResourceMd5,
		RankFloat:        in.GetRankFloat(),
		//ActivityJumpUrl:  in.GetDescActivityUrl(),
		ChannelTypeList:  strings.Join(channelTypeStrList, ","),

        ShowEffectEnd:          in.GetShowEffectEnd(),
        ActName:                in.GetActivityCfg().GetActivityName(),
        ActBeginTime:           time.Unix(in.GetActivityCfg().GetBeginTime(), 0),
        ActEndTime:             time.Unix(in.GetActivityCfg().GetEndTime(), 0),
        ActivityJumpUrl:        in.GetActivityCfg().GetJumpUrlTt(),
        ActImageUrl:            in.GetActivityCfg().GetImageUrl(),
        ActJumpUrlHcAndroid:    in.GetActivityCfg().GetJumpUrlHcAndroid(),
        ActJumpUrlHcIos:        in.GetActivityCfg().GetJumpUrlHcIos(),
        ActJumpUrlMikeAndroid:  in.GetActivityCfg().GetJumpUrlMikeAndroid(),
        ActJumpUrlMikeIos:      in.GetActivityCfg().GetJumpUrlMikeIos(),
    }
}
