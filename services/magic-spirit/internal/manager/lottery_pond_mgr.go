package manager

import (
	"context"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	"math/rand"
	"sort"
	"sync"
	"sync/atomic"
	"time"

	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/services/magic-spirit/internal/cache"
	"golang.52tt.com/services/magic-spirit/internal/mysql"
	"fmt"
)

// 无锁内存缓存
type MagicPondLocalCache struct {
	cache cache.ICache
	store mysql.IStore
	stop  chan interface{}
	wg    sync.WaitGroup

	pondMemCache [2]*AllMagicPond
	pondCacheIdx int32
}

type AllMagicPond struct {
	mapPondGift map[uint32]*MagicPond
}

type MagicPond struct {
	PondGiftList []*mysql.MagicSpiritPond
	TotalWeight  uint64

	// 特殊奖池
	SpecialPondGiftList []*mysql.MagicSpiritPond
	SpecialTotalWeight  uint64
}

func NewMagicPondLocalCache(st mysql.IStore, cache cache.ICache) (*MagicPondLocalCache, error) {
	//设置随机种子
	rand.Seed(time.Now().UnixNano())

	mc := &MagicPondLocalCache{
		store: st,
		cache: cache,
		stop:  make(chan interface{}),
	}

	err := mc.initMagicPond()
	if err != nil {
		log.Errorf("NewMagicPondLocalCache fail to initMagicPond. err:%v", err)
		return mc, err
	}

	mc.checkUpdateTimer()

	return mc, nil
}

func (mc *MagicPondLocalCache) Stop() {
	close(mc.stop)
	mc.wg.Wait()
}

func (mc *MagicPondLocalCache) checkUpdateTimer() {
	mc.wg.Add(1)

	go func() {
		var (
			updateAt uint32
		)

		for {
			select {
			case <-mc.stop:
				mc.wg.Done()
				return

			case <-time.After(time.Second * 3):
				// get update ts
				ts, err := mc.cache.GetUpdateMagicSpiritVersion(context.Background())
				if err != nil {
					log.Errorf("checkUpdateTimer GetUpdateMagicSpiritVersion fail: %v", err)
					continue
				}

				if ts == updateAt {
					break
				}

				err = mc.initMagicPond()
				if err != nil {
					log.Errorf("checkUpdateTimer initMagicPond fail: %v", err)
				} else {
					updateAt = ts
				}
			}
		}
	}()
}

func (mc *MagicPondLocalCache) initMagicPond() error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	magicList, err := mc.store.GetMagicSpirit(ctx)
	if err != nil {
		log.Errorf("initMagicPond fail to GetMagicSpirit. err:%v", err)
		return err
	}

	mapPondGift := make(map[uint32]*MagicPond)

	for _, magicConf := range magicList {
		pondList, err := mc.store.GetMagicSpiritPond(ctx, magicConf.MagicSpiritId)
		if err != nil {
			log.Errorf("initMagicPond fail to GetMagicSpiritPond. MagicSpiritId:%v, err:%v", magicConf.MagicSpiritId, err)
			continue
		}

		if len(pondList) == 0 {
			continue
		}

		// 特殊奖池
		specialPondList, err := mc.store.GetSpecialPondByMagicId(ctx, magicConf.MagicSpiritId)
		if err != nil {
			log.Errorf("initMagicPond fail to GetSpecialPondByMagicId. MagicSpiritId:%v, err:%v", magicConf.MagicSpiritId, err)
			continue
		}

		mapPondGift[magicConf.MagicSpiritId] = mc.initPond(pondList, specialPondList)
	}

	index := 1 - atomic.LoadInt32(&mc.pondCacheIdx)
	mc.pondMemCache[index] = &AllMagicPond{
		mapPondGift: mapPondGift,
	}
	atomic.StoreInt32(&mc.pondCacheIdx, index)

	var initPondLog string
	for magicId, pond := range mc.pondMemCache[index].mapPondGift {
		var normalPond string
		for _, info := range pond.PondGiftList {
			normalPond += fmt.Sprintf("%d:gift%d:price:%d:w%d, ", info.ItemId, info.PresentId, info.Price, info.Weight)
		}
		var specialPond string
		for _, info := range pond.SpecialPondGiftList {
			specialPond += fmt.Sprintf("%d:gift%d:price:%d:w%d, ", info.ItemId, info.PresentId, info.Price, info.Weight)
		}
		initPondLog += fmt.Sprintf("magicId:%d, normalPond:%s, specialPond:%s\n", magicId, normalPond, specialPond)
	}
	log.Infof("initMagicPond initPondLog:%s", initPondLog)
	return nil
}

func (mc *MagicPondLocalCache) initPond(list, specialList []*mysql.MagicSpiritPond) *MagicPond {
	var totalWeight uint64
	for _, info := range list {
		totalWeight += uint64(info.Weight)
	}

	var specialTotalWeight uint64
	for _, info := range specialList {
		specialTotalWeight += uint64(info.Weight)
	}

	/*index := 0
	  pond := make([]*MagicPond, 0)

	  for i := 0; i < len(list); i++ {
	  	for j := uint32(0); j < list[i].Weight; j++ {
	  		pond[index] = list[i]
	  		index++
	  	}
	  }*/

	// 按 weight 从大到小排序
	sort.SliceStable(list, func(right, left int) bool {
		return list[right].Weight > list[left].Weight
	})

	sort.SliceStable(specialList, func(right, left int) bool {
		return specialList[right].Weight > specialList[left].Weight
	})

	return &MagicPond{
		PondGiftList: list,
		TotalWeight:  totalWeight,

		SpecialPondGiftList: specialList,
		SpecialTotalWeight:  specialTotalWeight,
	}
}

// 开奖
func (mc *MagicPondLocalCache) LotteryDraw(magicId, num uint32, useSpecialPool bool) ([]*mysql.MagicSpiritPond, error) {
	list := make([]*mysql.MagicSpiritPond, 0, num)
	index := atomic.LoadInt32(&mc.pondCacheIdx)

	pond, ok := mc.pondMemCache[index].mapPondGift[magicId]
	if !ok || pond == nil {
		log.Errorf("LotteryDraw fail. magicId:%v, num:%v, pond:%+v", magicId, num, pond)
		return list, protocol.NewExactServerError(nil, status.ErrMagicSpiritSendLimit, "该礼物还未上架哦~")
	}

	// 如果在特殊奖池时间，优先使用特殊奖池
	if useSpecialPool && len(pond.SpecialPondGiftList) > 0 && pond.SpecialTotalWeight > 0 {
		for i := 0; i < int(num); i++ {
			// 生成随机数
			r := rand.Int63n(int64(pond.SpecialTotalWeight)) //#nosec
			curr := uint64(0)

			for _, info := range pond.SpecialPondGiftList {
				curr += uint64(info.Weight)

				if curr > uint64(r) {
					// 命中
					list = append(list, info)
					break
				}
			}
		}
		return list, nil
	}

	if len(pond.PondGiftList) == 0 || pond.TotalWeight == 0 {
		log.Errorf("LotteryDraw fail. magicId:%v, num:%v, pond:%+v", magicId, num, pond)
		return list, protocol.NewExactServerError(nil, status.ErrMagicSpiritSendLimit, "该礼物还未上架哦~")
	}

	pondGiftList := pond.PondGiftList

	for i := 0; i < int(num); i++ {
		// 生成随机数
		r := rand.Int63n(int64(pond.TotalWeight)) //#nosec
		curr := uint64(0)

		for _, info := range pondGiftList {
			curr += uint64(info.Weight)

			if curr > uint64(r) {
				// 命中
				list = append(list, info)
				break
			}
		}
	}

	return list, nil
}
