package manager

import(
	context "context"
	pb "golang.52tt.com/protocol/services/magic-spirit"
	pmPB "golang.52tt.com/protocol/services/present-middleware"
	time "time"
	mysql "golang.52tt.com/services/magic-spirit/internal/mysql"
	define "golang.52tt.com/services/magic-spirit/internal/define"
	UnifiedPayCallback "golang.52tt.com/protocol/services/unified_pay/cb"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
)

type IMagicSpiritMgr interface {
	AddMagicSpirit(ctx context.Context, in *pb.MagicSpirit) (uint32,error)
	AddMagicSpiritBlacklist(ctx context.Context, data []*mysql.MagicSpiritBlacklist) ([]uint32,error)
	AddMagicSpiritPond(ctx context.Context, data []*mysql.MagicSpiritPond, isSpecialPool bool) ([]uint32,error)
	AddUnpackList(ctx context.Context, uid, channelId uint32, unpackPbList []*pb.UnpackGiftInfo) error
	Callback(ctx context.Context, orderId string) (op UnifiedPayCallback.Op,err error)
	CheckAndUpdateMagicSpirit() 
	CheckIfSendMagicSpirit(ctx context.Context, sendInfo *define.SendInfo, magicInfo *pb.MagicSpirit) error
	CheckIfSendMagicWithSource(ctx context.Context, in *pb.CheckIfSendMagicWithSourceReq) (bool,error)
	CheckMagicPondUpdate() 
	CheckProfit(c context.Context) 
	DayReport(c context.Context) 
	DelMagicSpirit(ctx context.Context, ids []uint32) error
	DelMagicSpiritBlacklist(ctx context.Context, ids []uint32) error
	GenFinancialFile(ctx context.Context, req *reconcile_v2.GenFinancialFileReq) error
	GetAccessResult(ctx context.Context, uid, cid, magicId uint32, checkAll bool) (uint32,map[uint32]uint32,error)
	GetChannelAllUnpackGift(ctx context.Context, uid, channelId uint32) (*pb.GetChannelAllUnpackGiftResp,error)
	GetCommonConf(ctx context.Context) ([]*mysql.MagicSpiritCommonConf,error)
	GetCommonConfWithCache(ctx context.Context) (map[uint32]*mysql.MagicSpiritCommonConf,error)
	GetMagicAwardOrderIdList(_ context.Context, beginTime, endTime time.Time) ([]string,error)
	GetMagicAwardTotal(_ context.Context, beginTime, endTime time.Time) (orderCnt uint32,totalPrice, totalNum uint64,err error)
	GetMagicConsumeOrderIdList(_ context.Context, beginTime, endTime time.Time, source uint32) ([]string,error)
	GetMagicOrderTotal(_ context.Context, beginTime, endTime time.Time, source uint32) (orderCnt uint32,totalPrice, totalNum uint64,err error)
	GetMagicSpirit(ctx context.Context) ([]*mysql.MagicSpirit,error)
	GetMagicSpiritBlacklist(ctx context.Context, channelId, pageNum, pageSize uint32) (uint32,[]*mysql.MagicSpiritBlacklist,error)
	GetMagicSpiritById(ctx context.Context, magicSpiritId uint32) (*pb.MagicSpirit,error)
	GetMagicSpiritByIds(ctx context.Context, magicSpiritIds []uint32) (map[uint32]*pb.MagicSpirit,error)
	GetMagicSpiritPond(ctx context.Context, magicSpiritId, pondType uint32) ([]*mysql.MagicSpiritPond,error)
	GetMagicSpiritPondTmp(ctx context.Context) (map[uint32][]*mysql.MagicSpiritPond,error)
	GetMagicSpiritPondWithCache(ctx context.Context, magicSpiritId uint32, pondType uint32) ([]*mysql.MagicSpiritPond,error)
	GetMagicSpiritTmp(ctx context.Context) ([]*pb.MagicSpiritTmp,error)
	GetMagicSpiritUsable(ctx context.Context, req *pb.GetMagicSpiritUsableReq) (*pb.GetMagicSpiritUsableResp,error)
	GetMagicSpiritVersion(ctx context.Context) (uint32,error)
	GetMagicSpiritWithCache(ctx context.Context) ([]*mysql.MagicSpirit,error)
	GetUserExemptCondVal(ctx context.Context, uid uint32, magicIds ...uint32) (map[uint32]bool,error)
	HourReport(c context.Context) 
	JudgeExpectProfit(data []*mysql.MagicSpiritPond, spiritPrice uint32) bool
	LotteryDraw(ctx context.Context, magicSpiritOrderId string, sendInfo *define.SendInfo, magicSpirit *pb.MagicSpirit, outsideTime time.Time, useSpecialPool bool) ([]*pb.PresentSendInfo,[]*pb.UnpackGiftInfo,map[string]string,error)
	OpenUnpackGift(ctx context.Context, uid, channelId uint32, orderId string, sysAutoOpen bool) (*pmPB.SendMagicSpiritOpt,error)
	ReissueMagicOrder(ctx context.Context, orderId string) error
	SendMagicSpirit(ctx context.Context, in *pb.SendMagicSpiritReq) (*pb.SendMagicSpiritResp,error)
	SendMagicWithSource(ctx context.Context, in *pb.SendMagicWithSourceReq) (*pb.SendMagicWithSourceResp,error)
	SendPresent(ctx context.Context, magicOrderId string, outsideTime int64, magicConf *pb.MagicSpirit, list []*pb.PresentSendInfo, sendInfo *define.SendInfo) error
	SetCommonConf(ctx context.Context, data []*mysql.MagicSpiritCommonConf) error
	SetMagicSpiritTemporary(ctx context.Context, in *pb.MagicSpiritTmp) error
	ShutDown() 
	StartTimer() 
	TimerHandle(d time.Duration, handle func()) 
	UnpackListChangeNotify(ctx context.Context, uid, channelId uint32, newUnpackPbList []*pb.UnpackGiftInfo) error
	UpdateMagicSpirit(ctx context.Context, in *pb.MagicSpirit, beginTime time.Time) error
}


type IMagicPondLocalCache interface {
	LotteryDraw(magicId, num uint32, useSpecialPool bool) ([]*mysql.MagicSpiritPond,error)
	Stop() 
}

