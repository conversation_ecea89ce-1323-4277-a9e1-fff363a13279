package server

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
	"golang.52tt.com/clients/configserver"
	"golang.52tt.com/pkg/abtest"
	"golang.52tt.com/pkg/bylink"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	channelPB "golang.52tt.com/protocol/app/channel"
	pb "golang.52tt.com/protocol/services/channel-recommend-svr"
	configServerPb "golang.52tt.com/protocol/services/configserver"
	"golang.52tt.com/services/channel-recommend/channel-recommend-svr/client"
	"golang.52tt.com/services/channel-recommend/channel-recommend-svr/conf"
	"golang.52tt.com/services/channel-recommend/channel-recommend-svr/event"
	"golang.52tt.com/services/channel-recommend/channel-recommend-svr/manager"
	qualityUserMgr "golang.52tt.com/services/channel-recommend/channel-recommend-svr/manager/quality-user/mgr"
	"golang.52tt.com/services/channel-recommend/channel-recommend-svr/model"
	"sync"
	"time"
)

type ChannelRecommendServer struct {
	dyconf            conf.IConfDynamic
	mgr               *manager.ChannelRecommendManager
	qucickRecMgr      *manager.QuickRecChannelMgr
	pgcListPopMgr     *manager.PgcRecommendListAndPopMgr
	presentKfkSub     *event.PresentEventSub
	channelLotteryKfk *event.ChannelLotteryEventSub
	channelKfk        *event.ChannelEventSub
	authKfk           *event.AuthKafkaSub
	deepLinkKfk       *event.DeepLinkKafkaSub
	tagKfk            *event.UserTagKafkaSub
	emperorKfkSub     *event.EmperorEventSub
	qualityUserMgr    *qualityUserMgr.QualityUserMgr
	timerD            *timer.Timer
	cliPool           *model.SClientPool
}

func NewChannelRecommendServer(configer config.Configer) (*ChannelRecommendServer, error) {
	sc := &model.ServerConfig{}
	err := sc.Parse(configer)
	if err != nil {
		log.Errorf("NewChannelRecommendServer Failed to parse config  %+v", err)
		return nil, err
	}

	dycfg, err := conf.NewRecChannelDynamicConf()
	if err != nil {
		return nil, err
	}

	clientPool, err := model.NewClientPool(configer)
	if err != nil {
		log.Errorf("NewClientPool failed err:%v", err)
		return nil, err
	}

	err = client.InitClient(sc.GetEnv())
	if err != nil {
		log.Errorf("InitClient failed err:%v", err)
		return nil, err
	}

	// 百灵数据统计 初始化
	bylinkCollect, err := bylink.NewKfkCollector()
	if err != nil {
		log.Errorf("bylink.NewKfkCollector failed err:%v", err)
		return nil, err
	}
	bylink.InitGlobalCollector(bylinkCollect)

	// 大小进程的定时器，同一个任务只会在一个节点上执行
	timerD_, err := timer.NewTimerD(context.Background(), "channel-recommend-svr", timer.WithV6RedisCmdable(clientPool.RedisCli))
	if err != nil {
		log.Errorf("NewTimerD failed err:%v", err)
		return nil, err
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*60)
	defer cancel()

	qucickRecMgr, err := manager.NewQuickRecChannelMgr(ctx, clientPool, dycfg)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewQuickRecChannelMgr err:%v", err)
		return nil, err
	}

	mgr, err := manager.NewChannelRecommendManager(clientPool, dycfg, timerD_)
	if err != nil {
		return nil, err
	}

	qualityUserMgr_, err := qualityUserMgr.NewQualityUserMgr(sc, clientPool, dycfg)
	if err != nil {
		log.Errorf("NewQualityUserMgr failed err:%v", err)
		return nil, err
	}
	pgcListPopMgr, err := manager.NewPgcRecommendListAndPopMgr(ctx, dycfg, clientPool, mgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewPgcRecommendListAndPopMgr err:%v", err)
		return nil, err
	}

	channelLotteryKfk, err := event.NewChannelLotteryEventSub(clientPool.Sc.GetChannelLotteryKfkConf(), clientPool)
	if err != nil {
		log.Errorf("Failed to NewChannelLotteryEventSub conf:%v err:%s", clientPool.Sc.GetChannelLotteryKfkConf(), err.Error())
		return nil, err
	}

	channelKfk, err := event.NewChannelEventSub(clientPool.Sc.GetChannelKfkConf(), clientPool)
	if err != nil {
		log.Errorf("Failed to NewChannelLotteryEventSub conf:%v err:%s", clientPool.Sc.GetChannelKfkConf(), err.Error())
		return nil, err
	}

	presentKfkSub, err := event.NewPresentEventSub(clientPool.Sc.GetPresentKfkConf(), mgr)
	if err != nil {
		log.Errorf("Failed to NewChannelLotteryEventSub conf:%v err:%s", clientPool.Sc.GetChannelKfkConf(), err.Error())
		return nil, err
	}

	emperorKfkSub, err := event.NewEmperorEventSub(clientPool.Sc.GetEmperorKfkConf(), mgr)
	if err != nil {
		log.Errorf("Failed to NewEmperorEventSub conf:%v err:%s", clientPool.Sc.GetChannelKfkConf(), err.Error())
		return nil, err
	}

	authKfk_, err := event.NewAuthKafkaSubscriber(clientPool.Sc.GetAuthKfkConf(), mgr)
	if err != nil {
		log.Errorf("Failed to NewAuthKafkaSubscriber conf:%v err:%s", clientPool.Sc.GetChannelKfkConf(), err.Error())
		return nil, err
	}

	deepLinkKfk_, err := event.NewDeepLinkKafkaSubscriber(clientPool.Sc.GetDeepLinkKfkConf().ClientID, clientPool.Sc.GetDeepLinkKfkConf().GroupID,
		clientPool.Sc.GetDeepLinkKfkConf().TopicList()[0], clientPool.Sc.GetDeepLinkKfkConf().BrokerList(), mgr)
	if err != nil {
		log.Errorf("Failed to NewDeepLinkKafkaSubscriber conf:%v err:%s", clientPool.Sc.GetChannelKfkConf(), err.Error())
		return nil, err
	}

	tagKfk_, err := event.NewUserTagKafkaSubscriber(clientPool.Sc.GetTagKfkConf().ClientID, clientPool.Sc.GetTagKfkConf().GroupID,
		clientPool.Sc.GetTagKfkConf().TopicList()[0], clientPool.Sc.GetTagKfkConf().BrokerList(), mgr)
	if err != nil {
		log.Errorf("Failed to NewUserTagKafkaSubscriber conf:%v err:%s", clientPool.Sc.GetChannelKfkConf(), err.Error())
		return nil, err
	}

	timerD_.Start()

	return &ChannelRecommendServer{
		dyconf:            dycfg,
		mgr:               mgr,
		qucickRecMgr:      qucickRecMgr,
		pgcListPopMgr:     pgcListPopMgr,
		presentKfkSub:     presentKfkSub,
		channelLotteryKfk: channelLotteryKfk,
		channelKfk:        channelKfk,
		authKfk:           authKfk_,
		deepLinkKfk:       deepLinkKfk_,
		tagKfk:            tagKfk_,
		qualityUserMgr:    qualityUserMgr_,
		emperorKfkSub:     emperorKfkSub,
		timerD:            timerD_,
		cliPool:           clientPool,
	}, nil
}

func (s *ChannelRecommendServer) ShutDown() {
	s.mgr.Close()
	s.presentKfkSub.Close()
	s.channelLotteryKfk.Close()
	s.channelKfk.Close()
	s.authKfk.Close()
	s.deepLinkKfk.Close()
	s.tagKfk.Close()
	s.qualityUserMgr.ShutDown()
	bylink.Close()
	s.timerD.Stop()
	s.cliPool.Close()
}

func (s *ChannelRecommendServer) GetFlowCardLimitConfList(ctx context.Context, req *pb.GetFlowCardLimitConfListReq) (*pb.GetFlowCardLimitConfListResp, error) {
	return s.mgr.GetFlowCardLimitConfList(ctx, req)
}

func (s *ChannelRecommendServer) AddFlowCardLimitConf(ctx context.Context, req *pb.AddFlowCardLimitConfReq) (*pb.AddFlowCardLimitConfResp, error) {
	return s.mgr.AddFlowCardLimitConf(ctx, req)
}

func (s *ChannelRecommendServer) UpdateFlowCardLimitConf(ctx context.Context, req *pb.UpdateFlowCardLimitConfReq) (*pb.UpdateFlowCardLimitConfResp, error) {
	return s.mgr.UpdateFlowCardLimitConf(ctx, req)
}

func (s *ChannelRecommendServer) DelFlowCardLimitConf(ctx context.Context, req *pb.DelFlowCardLimitConfReq) (*pb.DelFlowCardLimitConfResp, error) {
	return s.mgr.DelFlowCardLimitConf(ctx, req)
}

func (s *ChannelRecommendServer) ReclaimGrantedFlowCard(ctx context.Context, req *pb.ReclaimGrantedFlowCardReq) (*pb.ReclaimGrantedFlowCardResp, error) {
	return s.mgr.ReclaimGrantedFlowCard(ctx, req)
}

func (s *ChannelRecommendServer) GetGrantFlowCardList(ctx context.Context, req *pb.GetGrantFlowCardListReq) (*pb.GetGrantFlowCardListResp, error) {
	return s.mgr.GetGrantFlowCardList(ctx, req)
}

func (s *ChannelRecommendServer) GrantFlowCard(ctx context.Context, req *pb.GrantFlowCardReq) (*pb.GrantFlowCardResp, error) {
	return s.mgr.GrantFlowCard(ctx, req)
}

func (s *ChannelRecommendServer) BatGrantFlowCard(ctx context.Context, req *pb.BatGrantFlowCardReq) (*pb.BatGrantFlowCardResp, error) {
	return s.mgr.BatGrantFlowCard(ctx, req)
}

func (s *ChannelRecommendServer) BanGrantedFlowCard(ctx context.Context, req *pb.BanGrantedFlowCardReq) (*pb.BanGrantedFlowCardResp, error) {
	return s.mgr.BanGrantedFlowCard(ctx, req)
}

func (s *ChannelRecommendServer) GrantAnchorFlowCardByGuild(ctx context.Context, req *pb.GrantAnchorFlowCardByGuildReq) (*pb.GrantAnchorFlowCardByGuildResp, error) {
	return s.mgr.GrantAnchorFlowCardByGuild(ctx, req)
}

func (s *ChannelRecommendServer) GetFlowCardHourRemainCnt(ctx context.Context, req *pb.GetFlowCardHourRemainCntReq) (*pb.GetFlowCardHourRemainCntResp, error) {
	return s.mgr.GetFlowCardHourRemainCnt(ctx, req)
}

func (s *ChannelRecommendServer) UseFlowCard(ctx context.Context, req *pb.UseFlowCardReq) (*pb.UseFlowCardResp, error) {
	return s.mgr.UseFlowCard(ctx, req)
}

func (s *ChannelRecommendServer) GetFlowCardListByType(ctx context.Context, req *pb.GetFlowCardListByTypeReq) (*pb.GetFlowCardListByTypeResp, error) {
	return s.mgr.GetFlowCardListByType(ctx, req)
}

func (s *ChannelRecommendServer) GetAllUseFlowCardAnchor(ctx context.Context, req *pb.GetAllUseFlowCardAnchorReq) (*pb.GetAllUseFlowCardAnchorResp, error) {
	return s.mgr.GetAllUseFlowCardAnchor(ctx, req)
}

func (s *ChannelRecommendServer) GetRecLotteryChList(ctx context.Context, req *pb.GetRecLotteryChListReq) (*pb.GetRecLotteryChListResp, error) {
	return s.mgr.GetRecLotteryChList(ctx, req)
}

func (s *ChannelRecommendServer) GetLotteryChannelRecInfo(ctx context.Context, req *pb.GetLotteryChannelRecInfoReq) (*pb.GetLotteryChannelRecInfoResp, error) {
	return s.mgr.GetLotteryChannelRecInfo(ctx, req)
}

func (s *ChannelRecommendServer) GetRecommendChannel(ctx context.Context, req *pb.GetRecommendChannelReq) (*pb.GetRecommendChannelResp, error) {
	switch channelPB.GetRecommonChannelListReq_HomeType(req.GetHomeType()) {
	case channelPB.GetRecommonChannelListReq_HOME_ENTERTAIN:
		roiType, err := s.mgr.CheckIsRoi(ctx, req.GetUid(), req.GetDeviceId(), uint32(model.DeviceStatusQueryUser))
		if err != nil {
			log.ErrorWithCtx(ctx, "GetRecommendChannel CheckIsRoi failed req:%v err:%v", req, err)
		}

		if roiType != model.RoiUserTypeInvalid {
			// roi用户
			return s.mgr.GetRoiRecommendChannel(ctx, req, roiType, false)
		}

		// 非roi用户，走AB测拿到身份类型（好用户/坏用户/普通用户），然后决定走好坏房间的逻辑还是原逻辑
		userType := s.mgr.GetAbtestUserType(ctx, req.GetUid(), uint32(abtest.APPID_TTyuyin))
		log.DebugWithCtx(ctx, "GetRecommendChannel uid %d , userType:%v", req.GetUid(), userType)
		// 拿最偏爱的房间品类标签
		tagType, err := s.mgr.GetUserPreferChannelTagValue(ctx, req.GetUid())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetRecommendChannel GetUserPreferChannelTagValue failed req:%v err:%v", req, err)
		}
		// 获取AB测试推荐房间
		if userType != 0 {
			return s.mgr.GetAbtestRecommendChannel(ctx, req, userType, tagType, false)
		}

		// 如果既不是好也不是坏，直接走原逻辑
		return s.mgr.GetRecommendChannel(ctx, req)
	default:
		return &pb.GetRecommendChannelResp{}, nil
	}
}

func (s *ChannelRecommendServer) GetPrepareChannelList(ctx context.Context, req *pb.GetPrepareChannelListReq) (*pb.GetPrepareChannelListResp, error) {
	return s.mgr.GetPrepareChannelList(ctx, req)
}

func (s *ChannelRecommendServer) SetPrepareChannel(ctx context.Context, req *pb.SetPrepareChannelReq) (*pb.SetPrepareChannelResp, error) {
	return s.mgr.SetPrepareChannel(ctx, req)
}

func (s *ChannelRecommendServer) DelPrepareChannel(ctx context.Context, req *pb.DelPrepareChannelReq) (*pb.DelPrepareChannelResp, error) {
	return s.mgr.DelPrepareChannel(ctx, req)
}

func (s *ChannelRecommendServer) GetPrepareBackupList(ctx context.Context, req *pb.GetPrepareBackupListReq) (*pb.GetPrepareBackupListResp, error) {
	return s.mgr.GetPrepareBackupList(ctx, req)
}

func (s *ChannelRecommendServer) GetPrepareOperRecordList(ctx context.Context, req *pb.GetPrepareOperRecordListReq) (*pb.GetPrepareOperRecordListResp, error) {
	return s.mgr.GetPrepareOperRecordList(ctx, req)
}

func (s *ChannelRecommendServer) GetDisplaySceneInfoList(ctx context.Context, req *pb.GetDisplaySceneInfoListReq) (*pb.GetDisplaySceneInfoListResp, error) {
	return s.mgr.GetDisplaySceneInfoList(ctx, req)
}

func (s *ChannelRecommendServer) AddDisplaySceneInfo(ctx context.Context, req *pb.AddDisplaySceneInfoReq) (*pb.AddDisplaySceneInfoResp, error) {
	return s.mgr.AddDisplaySceneInfo(ctx, req)
}

func (s *ChannelRecommendServer) DelDisplaySceneInfo(ctx context.Context, req *pb.DelDisplaySceneInfoReq) (*pb.DelDisplaySceneInfoResp, error) {
	return s.mgr.DelDisplaySceneInfo(ctx, req)
}

func (s *ChannelRecommendServer) GetQuickEntryConfigList(ctx context.Context, req *pb.GetQuickEntryConfigListReq) (*pb.GetQuickEntryConfigListResp, error) {
	return s.mgr.GetQuickEntryConfigList(ctx, req)
}

func (s *ChannelRecommendServer) BatGetChannelSoundLabel(ctx context.Context, req *pb.BatGetChannelSoundLabelReq) (*pb.BatGetChannelSoundLabelResp, error) {
	return s.mgr.BatGetChannelSoundLabel(ctx, req)
}

func (s *ChannelRecommendServer) TriggerTimer(ctx context.Context, req *pb.TriggerTimerReq) (*pb.TriggerTimerResp, error) {
	return s.mgr.TriggerTimer(ctx, req)
}

func (s *ChannelRecommendServer) CheckUidIsInWeddingGroup(ctx context.Context, req *pb.CheckUidIsInWeddingGroupReq) (*pb.CheckUidIsInWeddingGroupResp, error) {
	return &pb.CheckUidIsInWeddingGroupResp{IsInWeddingGroup: s.mgr.CheckUidIsInGroup(ctx, req.GetUid())}, nil
}

func (s *ChannelRecommendServer) GetQuickRecChannelByTagId(ctx context.Context, req *pb.GetQuickRecChannelByTagIdReq) (*pb.GetQuickRecChannelByTagIdResp, error) {
	log.DebugWithCtx(ctx, "GetQuickRecChannelByTagId req:%v", req)
	if !s.mgr.CheckIsNewShortLink(req.GetTagId()) {
		// 先走iop智能运营平台获取
		channelId := s.qucickRecMgr.GetQuickRecChannelFromIop(ctx, req.GetUid(), req.GetTagId())
		if channelId != 0 {
			return &pb.GetQuickRecChannelByTagIdResp{
				ChannelId: channelId,
			}, nil
		}

		// 走AB测拿到身份类型（好用户/坏用户/普通用户），然后决定走好坏房间的逻辑还是原逻辑
		userType := s.mgr.GetAbtestUserType(ctx, req.GetUid(), uint32(abtest.APPID_TTyuyin))
		log.DebugWithCtx(ctx, "GetRecommendChannel uid %d , userType:%v", req.GetUid(), userType)
		// 获取AB测试推荐房间
		if userType != 0 {
			channelId, err := s.mgr.GetAbtestQuickEnterChannel(ctx, req.GetUid(), userType, req.GetTagId())
			if err != nil {
				log.ErrorWithCtx(ctx, "GetAbtestQuickEnterChannel failed err:%v", err)
			}

			if channelId != 0 {
				return &pb.GetQuickRecChannelByTagIdResp{
					ChannelId: channelId,
				}, nil
			}
		}
	}

	// 以下是旧的推荐逻辑
	return s.qucickRecMgr.GetQuickRecChannelByTagId(ctx, req.Uid, req.TagId)
}

func (s *ChannelRecommendServer) GetUserRoiInfo(ctx context.Context, req *pb.GetUserRoiInfoReq) (*pb.GetUserRoiInfoResp, error) {
	resp, err := s.mgr.GetUserRoiInfo(ctx, req)
	return resp, err
}

func (s *ChannelRecommendServer) ConfirmRoiHighPotentail(ctx context.Context, req *pb.ConfirmRoiHighPotentailReq) (*pb.ConfirmRoiHighPotentailResp, error) {
	return s.mgr.ConfirmRoiHighPotentail(ctx, req)
}

func (s *ChannelRecommendServer) GetRecommendChannelV2(ctx context.Context, req *pb.GetRecommendChannelV2Req) (*pb.GetRecommendChannelV2Resp, error) {
	group := &sync.WaitGroup{}
	roiType := model.RoiUserTypeInvalid
	userType := manager.UserType(0)
	isPrecisePgcUser := false
	isPrecisePriorityTop := s.dyconf.GetSvrDyConf().PgcRecommendListConf.IsPriorityTop
	isDiyRecommend := true
	group.Add(4)
	go func() { // 检查是否是roi用户
		defer group.Done()
		roiType, _ = s.mgr.CheckIsRoi(ctx, req.GetUid(), req.GetDeviceId(), uint32(model.DeviceStatusQueryUser))
	}()
	go func() { // 非roi用户，走AB测拿到身份类型（好用户/坏用户/普通用户），然后决定走好坏房间的逻辑还是原逻辑
		defer group.Done()
		userType = s.mgr.GetAbtestUserType(ctx, req.GetUid(), uint32(abtest.APPID_TTyuyin))
	}()
	go func() { //检查是否精准PGC推荐
		defer group.Done()
		isPrecisePgcUser = s.pgcListPopMgr.CheckRecListPrecisePgcUser(ctx, req.GetUid())
	}()
	go func() { // 检查个性化推荐开关
		defer group.Done()
		configResp, err := s.cliPool.ConfigServerCli.GetConfig(ctx, configServerPb.GetConfigReq{
			ConfigKey: string(configserver.DiyRecommendType),
			Uid:       req.GetUid(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "GetRecommendChannelV2 GetConfig failed req:%v err:%v", req, err)
		} else {
			if configResp.GetConfigInfo().GetValue() == "false" {
				isDiyRecommend = false
			}
		}
	}()
	group.Wait()
	log.DebugWithCtx(ctx, "GetRecommendChannelV2 uid %d , userType:%v, roiType:%v isPrecisePgcUser：%v isDiyRecommend:%v", req.GetUid(), userType, roiType, isPrecisePgcUser, isDiyRecommend)

	// 如果配置了精准PGC推荐优先，则先走精准PGC推荐
	if isPrecisePriorityTop && isPrecisePgcUser && isDiyRecommend {
		resp, err := s.pgcListPopMgr.GetChannelRecommendList(ctx, req)
		resp.ChannelList = s.mgr.FilterUserNegativeFeedbackChannel(ctx, req.GetUid(), resp.GetChannelList())
		return resp, err
	}

	//1.优先ROI
	if roiType != model.RoiUserTypeInvalid && isDiyRecommend {
		// roi用户
		roiRsp, err := s.mgr.GetRoiRecommendChannel(ctx, &pb.GetRecommendChannelReq{
			Uid:          req.GetUid(),
			UserCategory: req.UserCategory,
			Start:        req.Start,
			Count:        req.Count,
			HomeType:     uint32(channelPB.GetRecommonChannelListReq_HOME_ENTERTAIN),
			DeviceId:     req.GetDeviceId(),
		}, roiType, true)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetRecommendChannelV2 GetRoiRecommendChannel failed req:%v err:%v", req, err)
			return &pb.GetRecommendChannelV2Resp{}, err
		}
		return &pb.GetRecommendChannelV2Resp{
			ChannelList: roiRsp.ChannelList,
			IsEnd:       roiRsp.IsEnd,
		}, nil
	}

	// 2. 获取AB测试推荐房间
	if userType != 0 && isDiyRecommend {
		// 拿最偏爱的房间品类标签
		tagType, err := s.mgr.GetUserPreferChannelTagValue(ctx, req.GetUid())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetRecommendChannelV2 GetUserPreferChannelTagValue failed req:%v err:%v", req, err)
		}
		abtestRsp, err := s.mgr.GetAbtestRecommendChannel(ctx, &pb.GetRecommendChannelReq{
			Uid:          req.GetUid(),
			UserCategory: req.UserCategory,
			Start:        req.Start,
			Count:        req.Count,
			HomeType:     uint32(channelPB.GetRecommonChannelListReq_HOME_ENTERTAIN),
			DeviceId:     req.GetDeviceId(),
		}, userType, tagType, true)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetRecommendChannelV2 GetAbtestRecommendChannel failed req:%v err:%v", req, err)
			return &pb.GetRecommendChannelV2Resp{}, err
		}
		return &pb.GetRecommendChannelV2Resp{
			ChannelList: abtestRsp.ChannelList,
			IsEnd:       abtestRsp.IsEnd,
		}, nil
	}

	// 3. 推荐精准PGC
	if isPrecisePgcUser && isDiyRecommend {
		resp, err := s.pgcListPopMgr.GetChannelRecommendList(ctx, req)
		resp.ChannelList = s.mgr.FilterUserNegativeFeedbackChannel(ctx, req.GetUid(), resp.GetChannelList())
		return resp, err
	}

	// 4.直接走原SABC逻辑
	return s.mgr.GetRecommendChannelV2(ctx, req)
}
func (s *ChannelRecommendServer) GetChannelByTagId(ctx context.Context, req *pb.GetChannelByTagIdReq) (*pb.GetChannelByTagIdResp, error) {
	return s.mgr.GetChannelByTagId(ctx, req)
}
func (s *ChannelRecommendServer) GetRecChListByPerTagId(ctx context.Context, req *pb.GetRecChListByPerTagIdReq) (*pb.GetRecChListByPerTagIdResp, error) {
	return s.mgr.GetRecChListByPerTagId(ctx, req)
}

// GetChannelGroupResourceList 获取房间组资源位配置列表
func (s *ChannelRecommendServer) GetChannelGroupResourceList(ctx context.Context, req *pb.GetChannelGroupResourceListReq) (*pb.GetChannelGroupResourceListResp, error) {
	return s.mgr.GetAllChannelGroupResource(ctx, req)
}

// AddChannelGroupResource 增加房间组资源位配置
func (s *ChannelRecommendServer) AddChannelGroupResource(ctx context.Context, req *pb.AddChannelGroupResourceReq) (*pb.AddChannelGroupResourceResp, error) {
	return s.mgr.AddChannelGroupResource(ctx, req)
}

// UpdateChannelGroupResource 更新房间组资源位配置
func (s *ChannelRecommendServer) UpdateChannelGroupResource(ctx context.Context, req *pb.UpdateChannelGroupResourceReq) (*pb.UpdateChannelGroupResourceResp, error) {
	return s.mgr.UpdateChannelGroupResource(ctx, req)
}

// DelChannelGroupResource 删除房间组资源位配置
func (s *ChannelRecommendServer) DelChannelGroupResource(ctx context.Context, req *pb.DelChannelGroupResourceReq) (*pb.DelChannelGroupResourceResp, error) {
	return s.mgr.DelChannelGroupResource(ctx, req)
}

func (s *ChannelRecommendServer) CheckChannelGroupResource(ctx context.Context, req *pb.CheckChannelGroupResourceReq) (*pb.CheckChannelGroupResourceResp, error) {
	return s.mgr.CheckChannelGroupResource(ctx, req)
}

// BatchGetChannelBigGiftInfo 批量获取房间大礼物信息
func (s *ChannelRecommendServer) BatchGetChannelBigGiftInfo(ctx context.Context, req *pb.BatchGetChannelBigGiftInfoReq) (*pb.BatchGetChannelBigGiftInfoResp, error) {
	return s.mgr.BatchGetChannelBigGiftInfo(ctx, req)
}

func (s *ChannelRecommendServer) GetUserQualityInfo(ctx context.Context, req *pb.GetUserQualityInfoReq) (*pb.GetUserQualityInfoResp, error) {
	return s.qualityUserMgr.GetUserQualityInfo(ctx, req)
}

// SetRevenueSwitchHub 批量获取房间大礼物信息
func (s *ChannelRecommendServer) SetRevenueSwitchHub(ctx context.Context, req *pb.SetRevenueSwitchHubReq) (*pb.SetRevenueSwitchHubResp, error) {
	return s.mgr.SetRevenueSwitchHub(ctx, req)
}

// GetRevenueSwitchHub 批量获取房间大礼物信息
func (s *ChannelRecommendServer) GetRevenueSwitchHub(ctx context.Context, req *pb.GetRevenueSwitchHubReq) (*pb.GetRevenueSwitchHubResp, error) {
	return s.mgr.GetRevenueSwitchHub(ctx, req)
}

// BatchGetRevenueSwitchHub 批量获取房间大礼物信息
func (s *ChannelRecommendServer) BatchGetRevenueSwitchHub(ctx context.Context, req *pb.BatchGetRevenueSwitchHubReq) (*pb.BatchGetRevenueSwitchHubResp, error) {
	return s.mgr.BatchGetRevenueSwitchHub(ctx, req)
}

func (s *ChannelRecommendServer) ConfirmQualityHighPop(ctx context.Context, req *pb.ConfirmQualityHighPopReq) (*pb.ConfirmQualityHighPopResp, error) {
	return s.qualityUserMgr.ConfirmQualityHighPop(ctx, req)
}

func (s *ChannelRecommendServer) GetTopWinChannelInfo(ctx context.Context, req *pb.GetTopWinChannelInfoReq) (*pb.GetTopWinChannelInfoResp, error) {
	return s.pgcListPopMgr.GetTopWinChannelInfo(ctx, req)
}

func (s *ChannelRecommendServer) CleanTopWinFilter(ctx context.Context, req *pb.CleanTopWinFilterReq) (*pb.CleanTopWinFilterResp, error) {
	err := s.pgcListPopMgr.CleanTopWinFilter(ctx, req.Uid)
	return &pb.CleanTopWinFilterResp{}, err
}

func (s *ChannelRecommendServer) GetQuickWeightConfList(ctx context.Context, req *pb.GetQuickWeightConfListReq) (*pb.GetQuickWeightConfListResp, error) {
	return s.qucickRecMgr.GetQuickWeightConfList(ctx, req)
}

func (s *ChannelRecommendServer) GetQuickRecChannelList(ctx context.Context, req *pb.GetQuickRecChannelListReq) (*pb.GetQuickRecChannelListResp, error) {
	return s.qucickRecMgr.GetQuickRecChannelList(ctx, req)
}

func (s *ChannelRecommendServer) GetTagConfigInfoList(ctx context.Context, req *pb.GetTagConfigInfoListReq) (*pb.GetTagConfigInfoListResp, error) {
	return s.mgr.GetTagConfigInfoList(ctx, req)
}

// GetTopOverLayChannel 获取顶部浮层房间（提供给智能运营平台使用）
func (s *ChannelRecommendServer) GetTopOverLayChannel(ctx context.Context, req *pb.GetTopOverLayChannelReq) (*pb.GetTopOverLayChannelResp, error) {
	return s.pgcListPopMgr.GetTopOverLayChannel(ctx, req.GetUid())
}

func (s *ChannelRecommendServer) GetRecFeedbackConfig(ctx context.Context, req *pb.GetRecFeedbackConfigReq) (*pb.GetRecFeedbackConfigResp, error) {
	return s.mgr.GetRecFeedbackConfig(ctx, req)
}

func (s *ChannelRecommendServer) DoRecFeedback(ctx context.Context, req *pb.DoRecFeedbackReq) (*pb.DoRecFeedbackResp, error) {
	return s.mgr.DoRecFeedback(ctx, req)
}
