package main

import (
	"context"
	"fmt"
	beego "github.com/astaxie/beego/config"
	_ "github.com/go-sql-driver/mysql" // MySQL驱动。
	"github.com/jmoiron/sqlx"
	"golang.52tt.com/pkg/config"
	"strconv"
	"strings"

	"crypto/tls"
	"github.com/go-gomail/gomail"
	"github.com/tealeg/xlsx"
	recPb "golang.52tt.com/protocol/services/channel-recommend-svr"
	"os"
	"time"
)

var MapLv2Type = map[uint32]string{
	1: "超级流量卡",
	2: "热门流量卡",
	3: "普通流量卡",
}

type FlowCardConsumeLog struct {
	ConsumeType uint32    `db:"consume_type"`
	GuildId     uint32    `db:"guild_id"`
	AnchorUid   uint32    `db:"anchor_uid"`
	RecLevel    uint32    `db:"rec_level"`
	Cnt         uint32    `db:"cnt"`
	ExpireTs    uint32    `db:"expire_ts"`
	EffectTs    uint32    `db:"effect_ts"`
	CreateTime  time.Time `db:"create_time"`
	RemainCnt   uint32    `db:"remain_cnt"`
}

// 流量卡发放记录
type FlowCardGrantLog struct {
	OrderId      string    `db:"order_id"`
	GrantType    uint32    `db:"grant_type"`
	Id           uint32    `db:"id"`
	RecLevel     uint32    `db:"rec_level"`
	Cnt          uint32    `db:"cnt"`
	ExpireTs     uint32    `db:"expire_ts"`
	GuildGrantId uint32    `db:"guild_grant_id"`
	UpdateTime   time.Time `db:"update_time"`
	CreateTime   time.Time `db:"create_time"`
}

// 主播流量卡
type AnchorFlowCard struct {
	GrantId      uint32    `db:"grant_id"`
	AnchorUid    uint32    `db:"anchor_uid"`
	RecLevel     uint32    `db:"rec_level"`
	ExpireTime   time.Time `db:"expire_time"`
	GuildGrantId uint32    `db:"guild_grant_id"`
	TotalCnt     uint32    `db:"total_cnt"`
	UsedCnt      uint32    `db:"used_cnt"`
	BanBeginTs   uint32    `db:"ban_begin_ts"`
	BanEndTs     uint32    `db:"ban_end_ts"`
	Remark       string    `db:"remark"`
	UpdateTime   time.Time `db:"update_time"`
	CreateTime   time.Time `db:"create_time"`
}

// 公会流量卡
type GuildFlowCard struct {
	GrantId    uint32    `db:"grant_id"`
	GuildId    uint32    `db:"guild_id"`
	RecLevel   uint32    `db:"rec_level"`
	ExpireTime time.Time `db:"expire_time"`
	TotalCnt   uint32    `db:"total_cnt"`
	UsedCnt    uint32    `db:"used_cnt"`
	BanBeginTs uint32    `db:"ban_begin_ts"`
	BanEndTs   uint32    `db:"ban_end_ts"`
	Remark     string    `db:"remark"`
	UpdateTime time.Time `db:"update_time"`
	CreateTime time.Time `db:"create_time"`
}

func SendMail(filePathList []string, value string) error {
	m := gomail.NewMessage()
	m.SetHeader("From", "<EMAIL>")

	m.SetHeader("To", "<EMAIL>", "<EMAIL>")
	//m.SetHeader("To", "<EMAIL>")
	m.SetHeader("Subject", value)

	m.SetBody("text/html", "数据表在附件")
	for _, filePath := range filePathList {
		m.Attach(filePath) //附件
	}

	d := gomail.NewDialer("mail.52tt.com", 465, "<EMAIL>", "V7.D.sTy@%bDB50t#n.r$A4h+-FnXA")

	d.TLSConfig = &tls.Config{InsecureSkipVerify: true}
	if err := d.DialAndSend(m); err != nil {
		fmt.Println(err)
		return err
	}
	return nil
}

func main() {
	nowTm := time.Now()
	pathFileList := make([]string, 0)
	if nowTm.Day() == 1 {
		monthBegin := time.Date(nowTm.Year(), nowTm.Month(), 1, 0, 0, 0, 0, time.Local)
		lastMonthEndTm := time.Unix(monthBegin.Unix()-1, 0)
		lastMonthBeginTm := time.Date(lastMonthEndTm.Year(), lastMonthEndTm.Month(), 1, 0, 0, 0, 0, time.Local)

		file := xlsx.NewFile()
		sheet, err := file.AddSheet("流量卡发放汇总月表")
		if err != nil {
			return
		}

		row := sheet.AddRow()
		slice := []string{"日期", "发放类型", "流量卡类型", "流量卡到期时间", "发放数量"}
		row.WriteSlice(&slice, -1)

		GetFlowCardGrantLog(lastMonthBeginTm, lastMonthEndTm, sheet)

		path := fmt.Sprintf("流量卡发放汇总月表%04d%02d.xlsx", lastMonthBeginTm.Year(), lastMonthBeginTm.Month())
		file.Save(path)
		pathFileList = append(pathFileList, path)

		file1 := xlsx.NewFile()
		sheet1, err1 := file1.AddSheet("主播维度公会发放汇总")
		if err1 != nil {
			return
		}

		row1 := sheet1.AddRow()
		slice1 := []string{"主播uid", "数量"}
		row1.WriteSlice(&slice1, -1)

		file2 := xlsx.NewFile()
		sheet2, err2 := file2.AddSheet("主播维度非公会发放汇总")
		if err2 != nil {
			return
		}

		row2 := sheet2.AddRow()
		slice2 := []string{"主播uid", "数量"}
		row2.WriteSlice(&slice2, -1)

		GetAnchorFlowCardGrant(lastMonthBeginTm, lastMonthEndTm, sheet1, sheet2)

		path1 := fmt.Sprintf("主播维度的公会发放汇总%04d%02d.xlsx", lastMonthBeginTm.Year(), lastMonthBeginTm.Month())
		file1.Save(path1)
		pathFileList = append(pathFileList, path1)

		path2 := fmt.Sprintf("主播维度的非公会发放汇总%04d%02d.xlsx", lastMonthBeginTm.Year(), lastMonthBeginTm.Month())
		file2.Save(path2)
		pathFileList = append(pathFileList, path2)
	}

	if nowTm.Day() == 1 {
		monthBegin := time.Date(nowTm.Year(), nowTm.Month(), 1, 0, 0, 0, 0, time.Local)
		lastMonthEndTm := time.Unix(monthBegin.Unix()-1, 0)
		monthFirstDayTm := time.Date(lastMonthEndTm.Year(), lastMonthEndTm.Month(), 1, 0, 0, 0, 0, time.Local)
		lastMonthBeginTm := monthFirstDayTm.AddDate(0, 0, -1)

		file := xlsx.NewFile()
		sheet, err := file.AddSheet("流量卡整体使用汇总月表")
		if err != nil {
			return
		}

		row := sheet.AddRow()
		slice := []string{"日期", "流量卡类型", "使用数量", "过期数量"}
		row.WriteSlice(&slice, -1)

		file1 := xlsx.NewFile()
		sheet1, err := file1.AddSheet("流量卡主播使用汇总月表")
		if err != nil {
			return
		}

		row1 := sheet1.AddRow()
		slice1 := []string{"日期", "主播uid", "流量卡类型", "拥有未过期的数量", "使用数量", "过期数量"}
		row1.WriteSlice(&slice1, -1)

		file2 := xlsx.NewFile()
		sheet2, err := file2.AddSheet("流量卡公会使用汇总月表")
		if err != nil {
			return
		}

		row2 := sheet2.AddRow()
		slice2 := []string{"日期", "公会id", "流量卡类型", "拥有未过期的数量", "使用数量", "发放数量", "过期数量"}
		row2.WriteSlice(&slice2, -1)

		GetFlowCardMonthConsumeLog(monthFirstDayTm, lastMonthBeginTm, lastMonthEndTm, sheet, sheet1, sheet2)

		path := fmt.Sprintf("流量卡整体使用汇总月表%04d%02d.xlsx", monthFirstDayTm.Year(), monthFirstDayTm.Month())
		file.Save(path)
		pathFileList = append(pathFileList, path)

		path1 := fmt.Sprintf("主播流量卡使用汇总月表%04d%02d.xlsx", monthFirstDayTm.Year(), monthFirstDayTm.Month())
		file1.Save(path1)
		pathFileList = append(pathFileList, path1)

		path2 := fmt.Sprintf("公会流量卡使用汇总月表%04d%02d.xlsx", monthFirstDayTm.Year(), monthFirstDayTm.Month())
		file2.Save(path2)
		pathFileList = append(pathFileList, path2)

		file3 := xlsx.NewFile()
		sheet3, err := file3.AddSheet("主播维度的公会使用汇总")
		if err != nil {
			return
		}

		row3 := sheet3.AddRow()
		slice3 := []string{"主播uid", "数量"}
		row3.WriteSlice(&slice3, -1)
		GetAnchorFlowCardConsume(monthFirstDayTm, lastMonthEndTm, sheet3)

		path3 := fmt.Sprintf("主播维度的公会使用汇总%04d%02d.xlsx", monthFirstDayTm.Year(), monthFirstDayTm.Month())
		file3.Save(path3)
		pathFileList = append(pathFileList, path3)
	}

	if nowTm.Day() == 1 {
		file := xlsx.NewFile()
		sheet, err := file.AddSheet("流量卡使用日记录月表")
		if err != nil {
			return
		}

		row := sheet.AddRow()
		slice := []string{"消耗类型", "主播uid", "公会id", "流量卡类型", "使用时间段", "流量卡到期时间", "数量", "发放/使用日期", "流量卡生效日期", "剩余数量"}
		row.WriteSlice(&slice, -1)

		file1 := xlsx.NewFile()
		sheet1, err := file1.AddSheet("流量卡使用日汇总月表")
		if err != nil {
			return
		}

		row1 := sheet1.AddRow()
		slice1 := []string{"日期", "每小时段", "流量卡类型", "使用数量"}
		row1.WriteSlice(&slice1, -1)

		lastMonthTm := time.Date(nowTm.Year(), nowTm.Month(), nowTm.Day()-1, 0, 0, 0, 0, time.Local)
		monthBeginTm := time.Date(lastMonthTm.Year(), lastMonthTm.Month(), 1, 0, 0, 0, 0, time.Local)
		var loopCnt uint32
		for tm := monthBeginTm; tm.Month() != nowTm.Month(); tm = tm.AddDate(0, 0, 1) {
			beginTm := time.Date(tm.Year(), tm.Month(), tm.Day()-1, 0, 0, 0, 0, time.Local)
			endTm := time.Date(tm.Year(), tm.Month(), tm.Day()+1, 0, 0, 0, 0, time.Local)
			GetFlowCardDayConsumeLog(tm, beginTm, endTm, sheet, sheet1)
			loopCnt++
			//兜底
			if loopCnt >= 60 {
				break
			}
		}

		path := fmt.Sprintf("流量卡使用日记录月表%04d%02d.xlsx", lastMonthTm.Year(), lastMonthTm.Month())
		file.Save(path)
		pathFileList = append(pathFileList, path)

		path1 := fmt.Sprintf("流量卡使用日汇总月表%04d%02d.xlsx", lastMonthTm.Year(), lastMonthTm.Month())
		file1.Save(path1)
		pathFileList = append(pathFileList, path1)
	}

	{
		file := xlsx.NewFile()
		sheet, err := file.AddSheet("流量卡使用记录日表")
		if err != nil {
			return
		}

		row := sheet.AddRow()
		slice := []string{"消耗类型", "主播uid", "公会id", "流量卡类型", "使用时间段", "流量卡到期时间", "数量", "发放/使用日期", "流量卡生效日期", "剩余数量"}
		row.WriteSlice(&slice, -1)

		file1 := xlsx.NewFile()
		sheet1, err := file1.AddSheet("流量卡使用汇总日表")
		if err != nil {
			return
		}

		row1 := sheet1.AddRow()
		slice1 := []string{"日期", "每小时段", "流量卡类型", "使用数量"}
		row1.WriteSlice(&slice1, -1)

		lastDay := time.Date(nowTm.Year(), nowTm.Month(), nowTm.Day()-1, 0, 0, 0, 0, time.Local)
		beginTm := time.Date(nowTm.Year(), nowTm.Month(), nowTm.Day()-2, 0, 0, 0, 0, time.Local)
		endTm := time.Date(nowTm.Year(), nowTm.Month(), nowTm.Day(), 0, 0, 0, 0, time.Local)
		GetFlowCardDayConsumeLog(lastDay, beginTm, endTm, sheet, sheet1)

		path := fmt.Sprintf("流量卡使用记录日表%04d%02d%02d.xlsx", lastDay.Year(), lastDay.Month(), lastDay.Day())
		file.Save(path)
		pathFileList = append(pathFileList, path)

		path1 := fmt.Sprintf("流量卡使用汇总日表%04d%02d%02d.xlsx", lastDay.Year(), lastDay.Month(), lastDay.Day())
		file1.Save(path1)
		pathFileList = append(pathFileList, path1)
	}

	for i := 1; i < 3; i++ {
		err := SendMail(pathFileList, "流量卡统计")
		if err == nil {
			break
		}
	}

	for _, path := range pathFileList {
		os.Remove(path)
	}
}

func InitSqlx(mysqlName string) *sqlx.DB {
	cfg, _ := beego.NewConfig("json", "entertainment.json")

	mysqlConfig := new(config.MysqlConfig)
	mysqlConfig.Read(cfg, mysqlName)
	mysqlAddr := mysqlConfig.ConnectionString()
	fmt.Println(mysqlAddr)

	mysqlDB, err := sqlx.Connect("mysql", mysqlAddr)
	if err != nil {
		panic(err)
	}
	return mysqlDB
}

// 获取流量卡消耗记录
func GetFlowCardDayConsumeLog(lastDay, beginTm, endTm time.Time, sheet, sheet1 *xlsx.Sheet) {
	ctx := context.Background()

	fmt.Printf("GetFlowCardDayConsumeLog begin \n")

	var err error
	liveRecDb := InitSqlx("mysqlLiveRec")

	mapKey2Cnt := make(map[string]uint32, 0)
	totalCnt := 0
	offset := 0
	for {
		infoList := make([]FlowCardConsumeLog, 0)
		querySql := fmt.Sprintf("select consume_type, guild_id, anchor_uid, rec_level, cnt, expire_ts, effect_ts, create_time, remain_cnt from flow_card_consume_log where create_time >= ? and create_time < ? limit ?, 500")
		err = liveRecDb.SelectContext(ctx, &infoList, querySql, beginTm, endTm, offset)
		if err != nil {
			fmt.Printf("GetFlowCardDayConsumeLog SelectContext failed err:%v", err)
			break
		}

		for _, info := range infoList {
			conSumeStr := ""
			if info.ConsumeType == 2 {
				if info.CreateTime.Unix() < lastDay.Unix() {
					continue
				}
				conSumeStr = "公会发放"
			}

			if info.ConsumeType == 1 {
				if info.EffectTs < uint32(lastDay.Unix()) || info.EffectTs >= uint32(endTm.Unix()) {
					continue
				}

				conSumeStr = "主播使用"
				if info.GuildId != 0 {
					conSumeStr = "公会使用"
				}

				effectTm := time.Unix(int64(info.EffectTs), 0)
				key := fmt.Sprintf("%02d_%d", effectTm.Hour(), info.RecLevel)
				mapKey2Cnt[key] += info.Cnt
			}
			row := sheet.AddRow()
			cell := row.AddCell()
			cell.Value = conSumeStr
			cell = row.AddCell()
			cell.Value = strconv.Itoa(int(info.AnchorUid))
			cell = row.AddCell()
			if info.GuildId != 0 {
				cell.Value = strconv.Itoa(int(info.GuildId))
			} else {
				cell.Value = ""
			}

			cell = row.AddCell()
			cell.Value = MapLv2Type[info.RecLevel]
			cell = row.AddCell()
			effectDate := ""
			if info.EffectTs != 0 {
				effectTm := time.Unix(int64(info.EffectTs), 0)
				cell.Value = fmt.Sprintf("%02d:00", effectTm.Hour())
				effectDate = fmt.Sprintf("%04d-%02d-%02d %02d:%02d:%02d", effectTm.Year(), effectTm.Month(), effectTm.Day(), effectTm.Hour(), effectTm.Minute(), effectTm.Second())
			} else {
				cell.Value = ""
			}

			cell = row.AddCell()
			if info.ExpireTs != 0 {
				cell.Value = time.Unix(int64(info.ExpireTs), 0).String()
			} else {
				cell.Value = ""
			}

			cell = row.AddCell()
			cell.Value = strconv.Itoa(int(info.Cnt))

			cell = row.AddCell()
			cell.Value = fmt.Sprintf("%04d-%02d-%02d %02d:%02d:%02d", info.CreateTime.Year(), info.CreateTime.Month(),
				info.CreateTime.Day(), info.CreateTime.Hour(), info.CreateTime.Minute(), info.CreateTime.Second())

			cell = row.AddCell()
			cell.Value = effectDate

			cell = row.AddCell()
			cell.Value = strconv.Itoa(int(info.RemainCnt))

			fmt.Printf("GetFlowCardDayConsumeLog success info:%v \n", info)
		}

		totalCnt += len(infoList)

		if len(infoList) < 500 {
			break
		}

		offset = offset + 500
	}

	for k, v := range mapKey2Cnt {
		arrList := strings.Split(k, "_")
		if len(arrList) != 2 {
			fmt.Sprintf("GetFlowCardDayConsumeLog arrList err k:%v\n", k)
			return
		}

		recLevel, _ := strconv.Atoi(arrList[1])

		row := sheet1.AddRow()
		cell := row.AddCell()
		cell.Value = fmt.Sprintf("%04d-%02d-%02d", lastDay.Year(), lastDay.Month(), lastDay.Day())
		cell = row.AddCell()
		cell.Value = fmt.Sprintf("%s:00", arrList[0])
		cell = row.AddCell()
		cell.Value = MapLv2Type[uint32(recLevel)]
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(v))
	}

	fmt.Printf("GetFlowCardDayConsumeLog end size %v\n", totalCnt)
}

// 获取流量卡消耗记录
func GetFlowCardMonthConsumeLog(monthFirstDayTm, beginTm, endTm time.Time, sheet, sheet1, sheet2 *xlsx.Sheet) {
	ctx := context.Background()

	fmt.Printf("GetFlowCardMonthConsumeLog begin monthFirstDayTm:%v beginTm:%v endTm:%v\n", monthFirstDayTm, beginTm, endTm)

	var err error
	liveRecDb := InitSqlx("mysqlLiveRec")

	type ConsumeData struct {
		TotalCnt      uint32
		UsedCnt       uint32
		MonthUsedCnt  uint32
		MonthGrantCnt uint32
		ExpireCnt     uint32
	}

	mapUid2FlowCard := make(map[uint32][]AnchorFlowCard)
	mapUidLv2Consume := make(map[string]ConsumeData, 0)
	mapGuild2FlowCard := make(map[uint32][]GuildFlowCard, 0)
	mapGuildLv2Consume := make(map[string]ConsumeData, 0)
	mapLv2Consume := make(map[uint32]ConsumeData, 0)

	offset := 0
	for {
		infoList := make([]AnchorFlowCard, 0)
		querySql := fmt.Sprintf("select * from anchor_flow_card where expire_time >= ? limit ?, 500")
		err = liveRecDb.SelectContext(ctx, &infoList, querySql, monthFirstDayTm, offset)
		if err != nil {
			fmt.Printf("GetFlowCardMonthConsumeLog SelectContext failed err:%v", err)
			break
		}

		for _, info := range infoList {
			key := fmt.Sprintf("%d_%d", info.AnchorUid, info.RecLevel)
			var expireCnt uint32
			if info.ExpireTime.Unix() <= endTm.Unix() {
				expireCnt = info.TotalCnt - info.UsedCnt
			}
			if consume, ok := mapUidLv2Consume[key]; ok {
				consume.TotalCnt += info.TotalCnt
				consume.UsedCnt += info.UsedCnt
				consume.ExpireCnt += expireCnt
				mapUidLv2Consume[key] = consume
			} else {
				mapUidLv2Consume[key] = ConsumeData{
					TotalCnt:  info.TotalCnt,
					UsedCnt:   info.UsedCnt,
					ExpireCnt: expireCnt,
				}
			}

			if consume, ok := mapLv2Consume[info.RecLevel]; ok {
				consume.TotalCnt += info.TotalCnt
				consume.UsedCnt += info.UsedCnt
				consume.ExpireCnt += expireCnt
				mapLv2Consume[info.RecLevel] = consume
			} else {
				mapLv2Consume[info.RecLevel] = ConsumeData{
					TotalCnt:  info.TotalCnt,
					UsedCnt:   info.UsedCnt,
					ExpireCnt: expireCnt,
				}
			}

			mapUid2FlowCard[info.AnchorUid] = append(mapUid2FlowCard[info.AnchorUid], info)
		}

		if len(infoList) < 500 {
			break
		}

		offset = offset + 500
	}

	offset = 0
	for {
		infoList := make([]GuildFlowCard, 0)
		querySql := fmt.Sprintf("select * from guild_flow_card where expire_time >= ? limit ?, 500")
		err = liveRecDb.SelectContext(ctx, &infoList, querySql, monthFirstDayTm, offset)
		if err != nil {
			fmt.Printf("GetFlowCardMonthConsumeLog SelectContext failed err:%v", err)
			break
		}

		for _, info := range infoList {
			key := fmt.Sprintf("%d_%d", info.GuildId, info.RecLevel)
			var expireCnt uint32
			if info.ExpireTime.Unix() <= endTm.Unix() {
				expireCnt = info.TotalCnt - info.UsedCnt
			}
			if consume, ok := mapGuildLv2Consume[key]; ok {
				consume.TotalCnt += info.TotalCnt
				consume.UsedCnt += info.UsedCnt
				consume.ExpireCnt += expireCnt
				mapGuildLv2Consume[key] = consume
			} else {
				mapGuildLv2Consume[key] = ConsumeData{
					TotalCnt:  info.TotalCnt,
					UsedCnt:   info.UsedCnt,
					ExpireCnt: expireCnt,
				}
			}

			if consume, ok := mapLv2Consume[info.RecLevel]; ok {
				consume.TotalCnt += info.TotalCnt
				consume.UsedCnt += info.UsedCnt
				consume.ExpireCnt += expireCnt
				mapLv2Consume[info.RecLevel] = consume
			} else {
				mapLv2Consume[info.RecLevel] = ConsumeData{
					TotalCnt:  info.TotalCnt,
					UsedCnt:   info.UsedCnt,
					ExpireCnt: expireCnt,
				}
			}

			mapGuild2FlowCard[info.GuildId] = append(mapGuild2FlowCard[info.GuildId], info)
		}

		if len(infoList) < 500 {
			break
		}

		offset = offset + 500
	}

	{
		infoList := make([]FlowCardConsumeLog, 0)
		offset = 0
		for {
			tmpInfoList := make([]FlowCardConsumeLog, 0)
			querySql := fmt.Sprintf("select consume_type, guild_id, anchor_uid, rec_level, cnt, expire_ts, effect_ts, create_time, remain_cnt from flow_card_consume_log " +
				"where guild_id = 0 and create_time >= ? and create_time <= ? limit ?, 500")
			err = liveRecDb.SelectContext(ctx, &tmpInfoList, querySql, beginTm, endTm, offset)
			if err != nil {
				fmt.Printf("GetFlowCardMonthConsumeLog SelectContext failed err:%v", err)
				break
			}

			for _, info := range tmpInfoList {
				infoList = append(infoList, info)
			}

			if len(tmpInfoList) < 500 {
				break
			}

			offset = offset + 500
		}

		for _, info := range infoList {
			if info.ConsumeType == 1 {
				if info.EffectTs < uint32(monthFirstDayTm.Unix()) || info.EffectTs > uint32(endTm.Unix()) {
					continue
				}
				key := fmt.Sprintf("%d_%d", info.AnchorUid, info.RecLevel)
				if consume, ok := mapUidLv2Consume[key]; ok {
					consume.MonthUsedCnt += info.Cnt
					mapUidLv2Consume[key] = consume
				}

				if consume, ok := mapLv2Consume[info.RecLevel]; ok {
					consume.MonthUsedCnt += info.Cnt
					mapLv2Consume[info.RecLevel] = consume
				}
			}
		}
	}

	{
		infoList := make([]FlowCardConsumeLog, 0)
		offset = 0
		for {
			tmpInfoList := make([]FlowCardConsumeLog, 0)
			querySql := fmt.Sprintf("select consume_type, guild_id, anchor_uid, rec_level, cnt, expire_ts, effect_ts, create_time, remain_cnt from flow_card_consume_log " +
				"where guild_id != 0  and create_time >= ? and create_time <= ? limit ?, 500")
			err = liveRecDb.SelectContext(ctx, &tmpInfoList, querySql, beginTm, endTm, offset)
			if err != nil {
				fmt.Printf("GetFlowCardMonthConsumeLog SelectContext failed err:%v", err)
				break
			}

			for _, info := range tmpInfoList {
				infoList = append(infoList, info)
			}

			if len(tmpInfoList) < 500 {
				break
			}

			offset = offset + 500
		}

		for _, info := range infoList {
			if info.ConsumeType == 1 {
				if info.EffectTs < uint32(monthFirstDayTm.Unix()) || info.EffectTs > uint32(endTm.Unix()) {
					continue
				}
				key := fmt.Sprintf("%d_%d", info.GuildId, info.RecLevel)
				if consume, ok := mapGuildLv2Consume[key]; ok {
					consume.MonthUsedCnt += info.Cnt
					mapGuildLv2Consume[key] = consume
				}

				if consume, ok := mapLv2Consume[info.RecLevel]; ok {
					consume.MonthUsedCnt += info.Cnt
					mapLv2Consume[info.RecLevel] = consume
				}
			}

			if info.ConsumeType == 2 {
				if info.CreateTime.Unix() < monthFirstDayTm.Unix() {
					continue
				}

				key := fmt.Sprintf("%d_%d", info.GuildId, info.RecLevel)
				if consume, ok := mapGuildLv2Consume[key]; ok {
					consume.MonthGrantCnt += info.Cnt
					mapGuildLv2Consume[key] = consume
				}
			}
		}
	}

	fmt.Printf("GetFlowCardMonthConsumeLog mapUidLv2Consume len:%d \n", len(mapUidLv2Consume))
	for k, v := range mapUidLv2Consume {
		arrList := strings.Split(k, "_")
		if len(arrList) != 2 {
			fmt.Sprintf("GetFlowCardMonthConsumeLog arrList err k:%v\n", k)
			return
		}

		anchorUid, _ := strconv.Atoi(arrList[0])
		recLevel, _ := strconv.Atoi(arrList[1])

		totalCnt := v.TotalCnt - v.UsedCnt + v.MonthUsedCnt

		row := sheet1.AddRow()
		cell := row.AddCell()
		cell.Value = fmt.Sprintf("%04d-%02d", monthFirstDayTm.Year(), monthFirstDayTm.Month())
		cell = row.AddCell()
		cell.Value = strconv.Itoa(anchorUid)
		cell = row.AddCell()
		cell.Value = MapLv2Type[uint32(recLevel)]
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(totalCnt))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(v.MonthUsedCnt))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(v.ExpireCnt))
	}

	fmt.Printf("GetFlowCardMonthConsumeLog mapGuildLv2Consume:%d\n", len(mapGuildLv2Consume))

	for k, v := range mapGuildLv2Consume {
		arrList := strings.Split(k, "_")
		if len(arrList) != 2 {
			fmt.Sprintf("GetFlowCardMonthConsumeLog arrList err k:%v\n", k)
			return
		}

		anchorUid, _ := strconv.Atoi(arrList[0])
		recLevel, _ := strconv.Atoi(arrList[1])

		totalCnt := v.TotalCnt - v.UsedCnt + v.MonthUsedCnt + v.MonthGrantCnt

		row := sheet2.AddRow()
		cell := row.AddCell()
		cell.Value = fmt.Sprintf("%04d-%02d", monthFirstDayTm.Year(), monthFirstDayTm.Month())
		cell = row.AddCell()
		cell.Value = strconv.Itoa(anchorUid)
		cell = row.AddCell()
		cell.Value = MapLv2Type[uint32(recLevel)]
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(totalCnt))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(v.MonthUsedCnt))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(v.MonthGrantCnt))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(v.ExpireCnt))
	}

	fmt.Printf("GetFlowCardMonthConsumeLog mapLv2Consume:%d\n", len(mapLv2Consume))

	for k, v := range mapLv2Consume {
		fmt.Printf("GetFlowCardMonthConsumeLog k:%d mapLv2Consume:%v\n", k, v)
		row := sheet.AddRow()
		cell := row.AddCell()
		cell.Value = fmt.Sprintf("%04d-%02d", monthFirstDayTm.Year(), monthFirstDayTm.Month())
		cell = row.AddCell()
		cell.Value = MapLv2Type[k]
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(v.MonthUsedCnt))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(v.ExpireCnt))
	}

	fmt.Printf("GetFlowCardMonthConsumeLog end\n")
}

// 获取流量卡发放记录
func GetFlowCardGrantLog(lastMonthBeginTm, lastMonthEndTm time.Time, sheet *xlsx.Sheet) {
	ctx := context.Background()

	fmt.Printf("GetFlowCardGrantLog begin \n")

	var err error
	liveRecDb := InitSqlx("mysqlLiveRec")

	totalCnt := 0
	offset := 0
	mapKey2Cnt := make(map[string]uint32, 0)
	for {
		infoList := make([]FlowCardGrantLog, 0)
		querySql := fmt.Sprintf("select * from flow_card_grant_log where create_time >= ? and create_time <= ? limit ?, 500")
		err = liveRecDb.SelectContext(ctx, &infoList, querySql, lastMonthBeginTm, lastMonthEndTm, offset)
		if err != nil {
			fmt.Printf("GetFlowCardGrantLog SelectContext failed err:%v", err)
			break
		}

		for _, info := range infoList {
			key := fmt.Sprintf("%d_%d_%d", info.GrantType, info.RecLevel, info.ExpireTs)
			mapKey2Cnt[key] += info.Cnt
			fmt.Printf("GetFlowCardGrantLog success info:%v \n", info)
		}

		if len(infoList) < 500 {
			break
		}

		offset = offset + 500
	}

	for k, v := range mapKey2Cnt {
		arrList := strings.Split(k, "_")
		if len(arrList) != 3 {
			fmt.Printf("GetFlowCardGrantLog key err k:%v \n", k)
			return
		}

		grantType, _ := strconv.Atoi(arrList[0])
		recLevel, _ := strconv.Atoi(arrList[1])
		expireTs, _ := strconv.Atoi(arrList[2])

		strGrantType := "发给公会"
		if grantType == int(recPb.FlowCardGrantType_GrantAnchor) {
			strGrantType = "发给主播"
		}

		expireTm := time.Unix(int64(expireTs), 0)
		expireTmStr := fmt.Sprintf("%04d-%02d-%02d %02d:%02d:%02d", expireTm.Year(), expireTm.Month(), expireTm.Day(), expireTm.Hour(), expireTm.Minute(), expireTm.Second())

		row := sheet.AddRow()
		cell := row.AddCell()
		cell.Value = fmt.Sprintf("%04d-%02d", lastMonthBeginTm.Year(), lastMonthBeginTm.Month())
		cell = row.AddCell()
		cell.Value = strGrantType
		cell = row.AddCell()
		cell.Value = MapLv2Type[uint32(recLevel)]
		cell = row.AddCell()
		cell.Value = expireTmStr
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(v))
	}

	fmt.Printf("GetFlowCardGrantLog end size %v\n", totalCnt)
}

// 获取主播维度的发放记录
func GetAnchorFlowCardGrant(lastMonthBeginTm, lastMonthEndTm time.Time, sheet, sheet1 *xlsx.Sheet) {
	ctx := context.Background()

	fmt.Printf("GetAnchorFlowCardGrant begin \n")

	var err error
	liveRecDb := InitSqlx("mysqlLiveRec")

	totalCnt := 0
	offset := 0
	mapUid2GuildCnt := make(map[uint32]uint32, 0)
	mapUid2NoGuildCnt := make(map[uint32]uint32, 0)
	for {
		infoList := make([]AnchorFlowCard, 0)
		querySql := fmt.Sprintf("select * from anchor_flow_card where create_time >= ? and create_time <= ? limit ?, 500")
		err = liveRecDb.SelectContext(ctx, &infoList, querySql, lastMonthBeginTm, lastMonthEndTm, offset)
		if err != nil {
			fmt.Printf("GetAnchorFlowCardGrant SelectContext failed err:%v", err)
			break
		}

		for _, info := range infoList {
			if info.GuildGrantId != 0 {
				mapUid2GuildCnt[info.AnchorUid] += info.TotalCnt
			} else {
				mapUid2NoGuildCnt[info.AnchorUid] += info.TotalCnt
			}

			fmt.Printf("GetAnchorFlowCardGrant success info:%v \n", info)
		}

		if len(infoList) < 500 {
			break
		}

		offset = offset + 500
	}

	for k, v := range mapUid2GuildCnt {
		row := sheet.AddRow()
		cell := row.AddCell()
		cell.Value = strconv.Itoa(int(k))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(v))
	}

	for k, v := range mapUid2NoGuildCnt {
		row := sheet1.AddRow()
		cell := row.AddCell()
		cell.Value = strconv.Itoa(int(k))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(v))
	}

	fmt.Printf("GetAnchorFlowCardGrant end size %v\n", totalCnt)
}

// 获取流量卡消耗记录
func GetFlowCardConsumeLogAll() {
	ctx := context.Background()

	fmt.Printf("GetFlowCardConsumeLog begin \n")

	var err error
	liveRecDb := InitSqlx("mysqlLiveRec")

	file := xlsx.NewFile()
	sheet, err := file.AddSheet("全部流量卡消耗记录表")
	if err != nil {
		return
	}

	row := sheet.AddRow()
	slice := []string{"消耗类型", "主播uid", "公会id", "流量卡类型", "使用时间段", "流量卡到期时间", "数量", "发放/使用日期", "流量卡生效日期"}
	row.WriteSlice(&slice, -1)

	totalCnt := 0
	offset := 0
	for {
		infoList := make([]FlowCardConsumeLog, 0)
		querySql := fmt.Sprintf("select consume_type, guild_id, anchor_uid, rec_level, cnt, expire_ts, effect_ts, create_time from flow_card_consume_log limit ?, 500")
		err = liveRecDb.SelectContext(ctx, &infoList, querySql, offset)
		if err != nil {
			fmt.Printf("GetFlowCardConsumeLog SelectContext failed err:%v", err)
			break
		}

		for _, info := range infoList {
			conSumeStr := ""
			if info.ConsumeType == 2 {
				conSumeStr = "公会发放"
			}

			if info.ConsumeType == 1 {
				conSumeStr = "主播使用"
				if info.GuildId != 0 {
					conSumeStr = "公会使用"
				}
			}
			row := sheet.AddRow()
			cell := row.AddCell()
			cell.Value = conSumeStr
			cell = row.AddCell()
			cell.Value = strconv.Itoa(int(info.AnchorUid))
			cell = row.AddCell()
			if info.GuildId != 0 {
				cell.Value = strconv.Itoa(int(info.GuildId))
			} else {
				cell.Value = ""
			}

			cell = row.AddCell()
			cell.Value = MapLv2Type[info.RecLevel]
			cell = row.AddCell()
			effectDate := ""
			if info.EffectTs != 0 {
				effectTm := time.Unix(int64(info.EffectTs), 0)
				cell.Value = fmt.Sprintf("%02d:00", effectTm.Hour())
				effectDate = fmt.Sprintf("%04d-%02d-%02d %02d:%02d:%02d", effectTm.Year(), effectTm.Month(), effectTm.Day(), effectTm.Hour(), effectTm.Minute(), effectTm.Second())
			} else {
				cell.Value = ""
			}

			cell = row.AddCell()
			if info.ExpireTs != 0 {
				cell.Value = time.Unix(int64(info.ExpireTs), 0).String()
			} else {
				cell.Value = ""
			}

			cell = row.AddCell()
			cell.Value = strconv.Itoa(int(info.Cnt))

			cell = row.AddCell()
			cell.Value = fmt.Sprintf("%04d-%02d-%02d %02d:%02d:%02d", info.CreateTime.Year(), info.CreateTime.Month(),
				info.CreateTime.Day(), info.CreateTime.Hour(), info.CreateTime.Minute(), info.CreateTime.Second())

			cell = row.AddCell()
			cell.Value = effectDate

			fmt.Printf("GetFlowCardConsumeLog success info:%v \n", info)
		}

		totalCnt += len(infoList)

		if len(infoList) < 500 {
			break
		}

		offset = offset + 500
	}

	fmt.Printf("GetFlowCardConsumeLog end size %v\n", totalCnt)

	path := fmt.Sprintf("全部流量卡消耗记录表.xlsx")
	file.Save(path)

	for i := 1; i < 3; i++ {
		err := SendMail([]string{path}, path)
		if err == nil {
			break
		}
	}
	os.Remove(path)
}

// 获取主播维度公会使用数据
func GetAnchorFlowCardConsume(beginTm, endTm time.Time, sheet *xlsx.Sheet) {
	ctx := context.Background()

	fmt.Printf("GetAnchorFlowCardConsume begin \n")

	var err error
	liveRecDb := InitSqlx("mysqlLiveRec")

	totalCnt := 0
	offset := 0
	mapUid2Cnt := make(map[uint32]uint32, 0)
	for {
		infoList := make([]FlowCardConsumeLog, 0)
		querySql := fmt.Sprintf("select consume_type, guild_id, anchor_uid, rec_level, cnt, expire_ts, effect_ts, create_time, " +
			"remain_cnt from flow_card_consume_log where create_time >= ? and create_time <= ? limit ?, 500")
		err = liveRecDb.SelectContext(ctx, &infoList, querySql, beginTm, endTm, offset)
		if err != nil {
			fmt.Printf("GetAnchorFlowCardConsume SelectContext failed err:%v", err)
			break
		}

		for _, info := range infoList {
			if info.ConsumeType == 1 && info.GuildId != 0 {
				mapUid2Cnt[info.AnchorUid] += info.Cnt
			}

			fmt.Printf("GetAnchorFlowCardConsume success info:%v \n", info)
		}

		totalCnt += len(infoList)

		if len(infoList) < 500 {
			break
		}

		offset = offset + 500
	}

	for k, v := range mapUid2Cnt {
		row := sheet.AddRow()
		cell := row.AddCell()
		cell.Value = strconv.Itoa(int(k))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(v))
		cell = row.AddCell()
	}

	fmt.Printf("GetAnchorFlowCardConsume end size %v\n", totalCnt)
}
