package manager

import (
	"context"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	"gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer/task/tasks"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	presentPb "golang.52tt.com/protocol/services/userpresent"
	"golang.52tt.com/services/channel-recommend/channel-recommend-svr/conf"
	"math/rand"
	"net"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"golang.52tt.com/pkg/coroutine"
	"golang.52tt.com/pkg/log"
	pbapi "golang.52tt.com/protocol/app/channel-recommend-logic"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/channel-recommend-svr"

	"gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
	anchorContractPb "golang.52tt.com/protocol/services/anchorcontract-go"
	"golang.52tt.com/services/channel-recommend/channel-recommend-svr/model"
)

var (
	LimitConfError = errors.New("配置错误")

	ReclaimUseCntTypeCommon     = 1 // 回收公用使用数量
	ReclaimUseHourCntTypeAnchor = 2 // 回收主播小时使用数量
	ReclaimUseDayCntTypeAnchor  = 4 // 回收主播每天使用数量
	ReclaimUseCntTypeGuild      = 8 // 回收公会使用数量
)

type ChannelRecommendManager struct {
	clientPool *model.SClientPool

	mapType2ActChannelList map[uint32][]uint32
	mapId2ActInfo          map[uint32]model.ActivityChannelInfo
	actChannelRwLock       sync.RWMutex

	mapKey2LevelChannel map[string][]uint32
	levelChannelRwLock  sync.RWMutex

	//分标签的等级房间库
	mapKey2TagLevelChannel map[string][]uint32
	tagLevelChannelRwLock  sync.RWMutex
	//个性化标签等级房间库
	mapKey2PersonalLvChannel map[string][]uint32
	personalLvChannelRwLock  sync.RWMutex
	personalUpdateIndex      uint32

	mapType2SceneList map[uint32][]*model.DisplaySceneInfo
	sceneInfoRwLock   sync.RWMutex

	mapCid2Label   map[uint32]string
	cidLabelRwLock sync.RWMutex

	dynamicCfg conf.IConfDynamic
	httpCli    *http.Client

	mapTagName2TagId map[string]uint32
	tagNameRwLock    sync.RWMutex

	PresentMemCache *model.PresentMemCache
	EmperorMemCache *model.EmperorSetCache
	switchTypeList  []uint32
}

func NewChannelRecommendManager(clientPool *model.SClientPool, dyConf conf.IConfDynamic, timerD *timer.Timer) (*ChannelRecommendManager, error) {

	httpCli := &http.Client{
		Transport: &http.Transport{
			DialContext: func(ctx context.Context, network, addr string) (conn net.Conn, e error) {
				host, _, err := net.SplitHostPort(addr)
				if err != nil {
					return nil, err
				}
				addrs, err := net.LookupHost(host)
				if err != nil {
					return nil, err
				}
				fmt.Printf("resolved %s to %v\n", host, addrs)
				return net.Dial(network, addr)
			},
			MaxIdleConns:          500,              // 最大空闲连接
			MaxConnsPerHost:       500,              // 每个pod最多多少链接
			IdleConnTimeout:       60 * time.Second, // 空闲连接的超时时间
			ExpectContinueTimeout: 10 * time.Second, // 等待服务第一个响应的超时时间
			MaxIdleConnsPerHost:   100,              // 每个host保持的空闲连接数
		},
	}

	mgr := &ChannelRecommendManager{
		clientPool:               clientPool,
		mapType2ActChannelList:   make(map[uint32][]uint32, 0),
		mapId2ActInfo:            make(map[uint32]model.ActivityChannelInfo, 0),
		actChannelRwLock:         sync.RWMutex{},
		mapKey2LevelChannel:      make(map[string][]uint32, 0),
		levelChannelRwLock:       sync.RWMutex{},
		mapKey2TagLevelChannel:   make(map[string][]uint32, 0),
		tagLevelChannelRwLock:    sync.RWMutex{},
		mapKey2PersonalLvChannel: make(map[string][]uint32, 0),
		personalLvChannelRwLock:  sync.RWMutex{},
		mapType2SceneList:        make(map[uint32][]*model.DisplaySceneInfo, 0),
		sceneInfoRwLock:          sync.RWMutex{},
		mapCid2Label:             make(map[uint32]string),
		cidLabelRwLock:           sync.RWMutex{},
		mapTagName2TagId:         make(map[string]uint32),
		tagNameRwLock:            sync.RWMutex{},
		dynamicCfg:               dyConf,
		httpCli:                  httpCli,
	}
	mgr.switchTypeList = []uint32{uint32(pbapi.RevenueSwitchHubType_REVENUE_SWITCH_HUB_TYPE_SNACKBAR)}

	// 先加载
	for i := 0; i < model.MaxBucketCnt; i++ {
		mgr.UpdateLevelChannelList(uint32(i))
	}

	//个性化推荐池
	for i := 0; i < model.MaxBucketPersonalCnt; i++ {
		mgr.updatePersonalTagLevelTb(uint32(i))
	}

	mgr.PresentMemCache = model.NewPresentMemCache()
	mgr.EmperorMemCache = model.NewEmperorMemCache()

	// 加载
	mgr.TimerQuickEntryConfig()
	mgr.TimerLoadLiveChannelLabel()
	//mgr.TimerLoadRecommendChannelList()
	//mgr.TimerLoadOldRecommendChannelList()
	mgr.TimerLoadTagName2TagId()

	rand.Seed(time.Now().UnixNano())

	timerD.AddIntervalTask("TimerProcSoonExpireFlowCard", time.Minute*60, tasks.FuncTask(mgr.TimerProcSoonExpireFlowCard))
	//coroutine.FixIntervalExec(mgr.TimerProcSoonExpireFlowCard, time.Minute*60)
	coroutine.FixIntervalExec(mgr.TimerLoadActChannelFromMysql, time.Minute)
	coroutine.FixIntervalExec(mgr.TimerUpdateLevelChannelList, time.Second*5)
	coroutine.FixIntervalExec(mgr.TimerPersonalTagLevel, time.Second*5) //个性化推荐池加到内存

	timerD.AddIntervalTask("TimerAutoGenPgcPrepareLevel", time.Minute*15, tasks.FuncTask(mgr.TimerAutoGenPgcPrepareLevel))
	//coroutine.FixIntervalExec(mgr.TimerAutoGenPgcPrepareLevel, time.Minute*15)
	timerD.AddIntervalTask("TimerLoadPgcPrepareLevel", time.Minute*15, tasks.FuncTask(mgr.TimerLoadPgcPrepareLevel))
	//coroutine.FixIntervalExec(mgr.TimerLoadPgcPrepareLevel, time.Minute*15)
	coroutine.FixIntervalExec(mgr.TimerQuickEntryConfig, time.Minute*1)
	coroutine.FixIntervalExec(mgr.TimerLoadLiveChannelLabel, time.Minute*10)
	timerD.AddIntervalTask("TimerLoadRoiChannelList", time.Minute*1, tasks.FuncTask(mgr.TimerLoadRoiChannelList))
	//coroutine.FixIntervalExec(mgr.TimerLoadRoiChannelList, time.Minute*1)
	timerD.AddIntervalTask("TimerLoadRecommendChannelList", time.Minute*1, tasks.FuncTask(mgr.TimerLoadRecommendChannelList))
	//coroutine.FixIntervalExec(mgr.TimerLoadRecommendChannelList, time.Minute*1)
	timerD.AddIntervalTask("TimerLoadOldRecommendChannelList", time.Minute*1, tasks.FuncTask(mgr.TimerLoadOldRecommendChannelList))
	//coroutine.FixIntervalExec(mgr.TimerLoadOldRecommendChannelList, time.Minute*1)
	coroutine.FixIntervalExec(mgr.TimerLoadTagName2TagId, time.Minute*1)

	// 定时删除过期的大礼物
	coroutine.FixIntervalExec(func() {
		mgr.clientPool.Cache.DelChannelBigGiftInfo(dyConf.Get().BigGiftTimeDur)
	}, time.Minute*1)

	// 抽奖推荐
	lotteryRecConf := clientPool.BusinessConfMgr.GetLotteryRecConf()
	if lotteryRecConf.UpdateTimerTs > 0 && lotteryRecConf.RemoveTimerTs > 0 {
		timerD.AddIntervalTask("TimerUpdateRecLotteryChList", time.Second*time.Duration(lotteryRecConf.UpdateTimerTs), tasks.FuncTask(mgr.TimerUpdateRecLotteryChList))
		//coroutine.FixIntervalExec(mgr.TimerUpdateRecLotteryChList, time.Second*time.Duration(lotteryRecConf.UpdateTimerTs))
		timerD.AddIntervalTask("TimerRemoveExpiredLotteryChList", time.Second*time.Duration(lotteryRecConf.RemoveTimerTs), tasks.FuncTask(mgr.TimerRemoveExpiredLotteryChList))
		//coroutine.FixIntervalExec(mgr.TimerRemoveExpiredLotteryChList, time.Second*time.Duration(lotteryRecConf.RemoveTimerTs))
	}

	return mgr, nil
}

func (m *ChannelRecommendManager) Close() {
	coroutine.StopAll()
}

func transLimitConfListToString(confList []*pb.LimitConf) (string, error) {
	tmpLimitConfList := confList
	mapHour2Lv := make(map[int]map[int]bool, 0)

	mapHour2Index := make(map[int]int, 0)
	for index, conf := range tmpLimitConfList {
		for _, lv := range conf.ConfList {
			if mapHour2Lv[int(conf.HourCnt)] == nil {
				mapHour2Lv[int(conf.HourCnt)] = make(map[int]bool, 0)
			}

			mapHour2Lv[int(conf.HourCnt)][int(lv.Level)] = true
			mapHour2Index[int(conf.HourCnt)] = index
		}
	}

	// 运营后台没有配置的小时配置，系统自动填充
	for i := 0; i < model.FlowCardLimitConfHourCnt; i++ {
		if _, hourOk := mapHour2Lv[i]; hourOk {
			for j := pb.RecommendLevel_Recommend_Level_S; j <= pb.RecommendLevel_Recommend_Level_B; j++ {
				if _, lvOk := mapHour2Lv[i][int(j)]; !lvOk {
					tmpLimitConfList[mapHour2Index[i]].ConfList = append(tmpLimitConfList[mapHour2Index[i]].ConfList, &pb.LevelConf{Level: uint32(j),
						Cnt: model.MapLv2DefaultLimitCnt[j]})
				}
			}
		} else {
			// 小时配置不存在，直接填默认
			tmpConf := &pb.LimitConf{
				HourCnt:  uint32(i),
				ConfList: make([]*pb.LevelConf, 0),
			}
			tmpConf.ConfList = append(tmpConf.ConfList, &pb.LevelConf{Level: uint32(pb.RecommendLevel_Recommend_Level_S),
				Cnt: model.MapLv2DefaultLimitCnt[pb.RecommendLevel_Recommend_Level_S]})
			tmpConf.ConfList = append(tmpConf.ConfList, &pb.LevelConf{Level: uint32(pb.RecommendLevel_Recommend_Level_A),
				Cnt: model.MapLv2DefaultLimitCnt[pb.RecommendLevel_Recommend_Level_A]})
			tmpConf.ConfList = append(tmpConf.ConfList, &pb.LevelConf{Level: uint32(pb.RecommendLevel_Recommend_Level_B),
				Cnt: model.MapLv2DefaultLimitCnt[pb.RecommendLevel_Recommend_Level_B]})

			tmpLimitConfList = append(tmpLimitConfList, tmpConf)
		}
	}

	if len(tmpLimitConfList) != model.FlowCardLimitConfHourCnt {
		log.Errorf("transLimitConfListToString hour cnt invalid len:%d list:%v", len(tmpLimitConfList), tmpLimitConfList)
		return "", LimitConfError
	}

	for _, conf := range tmpLimitConfList {
		if len(conf.ConfList) != model.FlowCardLimitConfLvCnt {
			log.Errorf("transLimitConfListToString lv cnt invalid len:%d conf:%v", len(conf.ConfList), conf)
			return "", LimitConfError
		}
	}

	sort.Slice(tmpLimitConfList, func(i, j int) bool { return tmpLimitConfList[i].HourCnt < tmpLimitConfList[j].HourCnt })
	tmpStringList := make([]string, 0)

	for _, conf := range tmpLimitConfList {
		sort.Slice(conf.ConfList, func(i, j int) bool { return conf.ConfList[i].Level < conf.ConfList[j].Level })
		cntList := make([]string, 0)
		for _, lvConf := range conf.ConfList {
			cntList = append(cntList, fmt.Sprintf("%d", lvConf.Cnt))
		}

		tmpStringList = append(tmpStringList, strings.Join(cntList, ","))
	}

	return strings.Join(tmpStringList, ":"), nil
}

func transStringToLimitConfList(limitConf string) ([]*pb.LimitConf, error) {
	confList := make([]*pb.LimitConf, 0)

	tmpStringList := strings.Split(limitConf, ":")
	if len(tmpStringList) != model.FlowCardLimitConfHourCnt {
		log.Errorf("transStringToLimitConfList hour cnt invalid len:%d conf:%v", len(tmpStringList), limitConf)
		return confList, LimitConfError
	}

	for index, tmpString := range tmpStringList {
		lvConfList := strings.Split(tmpString, ",")
		if len(lvConfList) != model.FlowCardLimitConfLvCnt {
			log.Errorf("transStringToLimitConfList lv cnt invalid len:%d string:%s conf:%v", len(lvConfList), tmpString, limitConf)
			return confList, LimitConfError
		}
		tmpConf := &pb.LimitConf{
			HourCnt:  uint32(index),
			ConfList: make([]*pb.LevelConf, 0),
		}

		for i, lvConf := range lvConfList {
			cnt, _ := strconv.Atoi(lvConf)
			tmpConf.ConfList = append(tmpConf.ConfList, &pb.LevelConf{Level: uint32(i + 1), Cnt: uint32(cnt)})
		}

		confList = append(confList, tmpConf)
	}

	return confList, nil
}

func (m *ChannelRecommendManager) delFlowCardLimitCache(beginTs, endTs uint32) {
	log.Debugf("delFlowCardLimitCache begin %d %d", beginTs, endTs)
	tsList := make([]uint32, 0)
	for ts := beginTs; ts < endTs; ts = ts + 24*3600 {
		tsList = append(tsList, ts)
	}
	tsList = append(tsList, endTs)
	err := m.clientPool.Cache.DelFlowCardLimitConf(tsList)
	if err != nil {
		log.Errorf("delFlowCardLimitCache failed %d %d list:%v err:%v", beginTs, endTs, tsList, err)
	}

	log.Debugf("delFlowCardLimitCache end %d %d list:%v", beginTs, endTs, tsList)
}

func (m *ChannelRecommendManager) AddFlowCardLimitConf(ctx context.Context, req *pb.AddFlowCardLimitConfReq) (*pb.AddFlowCardLimitConfResp, error) {
	resp := &pb.AddFlowCardLimitConfResp{}

	log.Infof("AddFlowCardLimitConf being req:%v", req)

	// 获取限额配置的字符串形式
	strLimitConf, err := transLimitConfListToString(req.GetConf().GetConfList())
	if err != nil {
		log.Errorf("AddFlowCardLimitConf transLimitConfListToString failed req:%v err:%v", req, err)
		return resp, err
	}

	err = m.clientPool.Store.AddFlowCardLimit(&model.FlowCardLimit{
		LimitConf:  strLimitConf,
		BeginTime:  time.Unix(int64(req.GetConf().GetBeginTs()), 0),
		EndTime:    time.Unix(int64(req.GetConf().GetEndTs()), 0),
		UpdateTime: time.Now(),
		CreateTime: time.Now(),
	})
	if err != nil {
		log.Errorf("AddFlowCardLimitConf AddFlowCardLimit failed req:%v error:%v", req, err)
		return resp, err
	}

	m.delFlowCardLimitCache(req.GetConf().GetBeginTs(), req.GetConf().GetEndTs())

	log.Infof("AddFlowCardLimitConf end req:%v resp:%v", req, resp)
	return resp, nil
}

func (m *ChannelRecommendManager) UpdateFlowCardLimitConf(ctx context.Context, req *pb.UpdateFlowCardLimitConfReq) (*pb.UpdateFlowCardLimitConfResp, error) {
	resp := &pb.UpdateFlowCardLimitConfResp{}

	log.Infof("UpdateFlowCardLimitConf begin req:%v", req)

	// 获取限额配置的字符串形式
	strLimitConf, err := transLimitConfListToString(req.GetConf().GetConfList())
	if err != nil {
		log.Errorf("UpdateFlowCardLimitConf transLimitConfListToString failed req:%v err:%v", req, err)
		return resp, err
	}

	err = m.clientPool.Store.UpdateFlowCardLimitCnt(req.GetConf().GetId(), strLimitConf)
	if err != nil {
		log.Errorf("UpdateFlowCardLimitConf UpdateFlowCardLimitCnt failed req:%v err:%v", req, err)
		return resp, err
	}

	m.delFlowCardLimitCache(req.GetConf().GetBeginTs(), req.GetConf().GetEndTs())

	log.Infof("UpdateFlowCardLimitConf end req:%v resp:%v", req, resp)
	return resp, nil
}

func (m *ChannelRecommendManager) DelFlowCardLimitConf(ctx context.Context, req *pb.DelFlowCardLimitConfReq) (*pb.DelFlowCardLimitConfResp, error) {
	resp := &pb.DelFlowCardLimitConfResp{}

	log.Infof("DelFlowCardLimitConf begin req:%v", req)

	flowCardLimit, err := m.clientPool.Store.GetFlowCardLimitById(req.GetId())
	if err != nil {
		log.Errorf("DelFlowCardLimitConf GetFlowCardLimitById failed req:%v err:%v", req, err)
		return resp, err
	}

	err = m.clientPool.Store.DelFlowCardLimit(req.GetId())
	if err != nil {
		log.Errorf("DelFlowCardLimitConf DelFlowCardLimit failed req:%v err:%v", req, err)
		return resp, err
	}

	m.delFlowCardLimitCache(uint32(flowCardLimit.BeginTime.Unix()), uint32(flowCardLimit.EndTime.Unix()))

	log.Infof("DelFlowCardLimitConf end req:%v resp:%v", req, resp)
	return resp, nil
}

func (m *ChannelRecommendManager) GetFlowCardLimitConfList(ctx context.Context, req *pb.GetFlowCardLimitConfListReq) (*pb.GetFlowCardLimitConfListResp, error) {
	resp := &pb.GetFlowCardLimitConfListResp{}

	log.Debugf("GetFlowCardLimitConfList begin req:%v", req)

	infoList, err := m.clientPool.Store.GetFlowCardLimitList(req.GetBeginTs(), req.GetEndTs(), (req.GetPage()-1)*req.GetPageSize(), req.GetPageSize())
	if err != nil {
		log.Errorf("GetFlowCardLimitConfList GetFlowCardLimitList failed req:%v err:%v", req, err)
		return resp, err
	}

	totalCnt, err := m.clientPool.Store.GetFlowCardLimitTotalCnt(req.GetBeginTs(), req.GetEndTs())
	if err != nil {
		log.Errorf("GetFlowCardLimitConfList GetFlowCardLimitTotalCnt failed req:%v err:%v", req, err)
		return resp, err
	}

	for _, info := range infoList {
		limitConfList, err := transStringToLimitConfList(info.LimitConf)
		if err != nil {
			log.Errorf("GetFlowCardLimitConfList transStringToLimitConfList failed req:%v info:%v err:%v", req, info, err)
			return resp, err
		}

		resp.ConfList = append(resp.ConfList, &pb.FlowCardLimitConf{
			Id:       info.Id,
			BeginTs:  uint32(info.BeginTime.Unix()),
			EndTs:    uint32(info.EndTime.Unix()),
			ConfList: limitConfList,
		})
	}

	resp.NextPage = req.GetPage() + 1
	resp.TotalCnt = totalCnt
	if len(resp.ConfList) < int(req.GetPageSize()) {
		resp.NextPage = 0
	}

	log.Debugf("GetFlowCardLimitConfList end req:%v resp:%v", req, resp)
	return resp, nil
}

func (m *ChannelRecommendManager) GetGrantFlowCardList(ctx context.Context, req *pb.GetGrantFlowCardListReq) (*pb.GetGrantFlowCardListResp, error) {
	resp := &pb.GetGrantFlowCardListResp{}

	log.Debugf("GetGrantFlowCardList begin req:%v", req)
	var totalCnt uint32 = 0
	if req.GetGrantType() == uint32(pb.FlowCardGrantType_GrantGuild) {
		infoList, err := m.clientPool.Store.GetGuildFlowCardList(req.GetId(), req.GetLevel(), (req.GetPage()-1)*req.GetPageSize(), req.GetPageSize())
		if err != nil {
			log.Errorf("GetGrantFlowCardList GetGuildFlowCardList failed req:%v err:%v", req, err)
			return resp, err
		}

		totalCnt, err = m.clientPool.Store.GetGuildFlowCardTotalCnt(req.GetId(), req.GetLevel())
		if err != nil {
			log.Errorf("GetGrantFlowCardList GetGuildFlowCardTotalCnt failed req:%v err:%v", req, err)
			return resp, err
		}

		for _, info := range infoList {
			resp.InfoList = append(resp.InfoList, &pb.FlowCardGrantInfo{
				GrantId:    info.GrantId,
				Id:         info.GuildId,
				Level:      info.RecLevel,
				ExpirtTs:   uint32(info.ExpireTime.Unix()),
				Cnt:        info.TotalCnt,
				Remark:     info.Remark,
				BanBeginTs: info.BanBeginTs,
				BanEndTs:   info.BanEndTs,
				UsedCnt:    info.UsedCnt,
				UpdateTs:   uint32(info.UpdateTime.Unix()),
				CreateTs:   uint32(info.CreateTime.Unix()),
			})
		}
	}
	if req.GetGrantType() == uint32(pb.FlowCardGrantType_GrantAnchor) {
		infoList, err := m.clientPool.Store.GetAnchorFlowCardList(req.GetId(), req.GetGuildGrantId(), req.GetLevel(), (req.GetPage()-1)*req.GetPageSize(), req.GetPageSize())
		if err != nil {
			log.Errorf("GetGrantFlowCardList GetAnchorFlowCardList failed req:%v err:%v", req, err)
			return resp, err
		}

		totalCnt, err = m.clientPool.Store.GetAnchorFlowCardTotalCnt(req.GetId(), req.GetGuildGrantId(), req.GetLevel())
		if err != nil {
			log.Errorf("GetGrantFlowCardList GetAnchorFlowCardTotalCnt failed req:%v err:%v", req, err)
			return resp, err
		}

		for _, info := range infoList {
			resp.InfoList = append(resp.InfoList, &pb.FlowCardGrantInfo{
				GrantId:      info.GrantId,
				Id:           info.AnchorUid,
				Level:        info.RecLevel,
				ExpirtTs:     uint32(info.ExpireTime.Unix()),
				Cnt:          info.TotalCnt,
				Remark:       info.Remark,
				BanBeginTs:   info.BanBeginTs,
				BanEndTs:     info.BanEndTs,
				UsedCnt:      info.UsedCnt,
				UpdateTs:     uint32(info.UpdateTime.Unix()),
				CreateTs:     uint32(info.CreateTime.Unix()),
				GuildGrantId: info.GuildGrantId,
			})
		}
	}

	resp.TotalCnt = totalCnt
	resp.NextPage = req.GetPage() + 1
	if len(resp.GetInfoList()) < int(req.GetPageSize()) {
		resp.NextPage = 0
	}

	log.Debugf("GetGrantFlowCardList end req:%v resp:%v", req, resp)
	return resp, nil
}

func (m *ChannelRecommendManager) GrantFlowCard(ctx context.Context, req *pb.GrantFlowCardReq) (*pb.GrantFlowCardResp, error) {
	resp := &pb.GrantFlowCardResp{}

	log.Infof("GrantFlowCard begin req:%v", req)
	expireTm := time.Unix(int64(req.GetInfo().GetExpirtTs()), 0)
	if req.GetGrantType() == uint32(pb.FlowCardGrantType_GrantGuild) {
		err := m.clientPool.Store.Transaction(ctx, func(tx *gorm.DB) error {
			err := m.clientPool.Store.GrantGuildFlowCard(tx, req.GetInfo().GetId(), req.GetInfo().GetLevel(), req.GetInfo().GetCnt(), expireTm)
			if err != nil {
				log.Errorf("GrantFlowCard GrantGuildFlowCard failed req:%v err:%v", req, err)
				return err
			}

			err = m.clientPool.Store.RecordFlowCardGrantLog(tx, model.FlowCardGrantLog{
				OrderId:      req.GetInfo().GetOrderId(),
				GrantType:    req.GetGrantType(),
				Id:           req.GetInfo().GetId(),
				RecLevel:     req.GetInfo().GetLevel(),
				Cnt:          req.GetInfo().GetCnt(),
				ExpireTs:     req.GetInfo().GetExpirtTs(),
				GuildGrantId: req.GetInfo().GetGuildGrantId(),
			})
			if err != nil {
				log.Errorf("GrantFlowCard RecordFlowCardGrantLog failed req:%v err:%v", req, err)
				return err
			}

			return nil
		})
		if err != nil {
			if err == model.DuplicateKeyErr {
				return resp, nil
			} else {
				return resp, protocol.NewServerError(status.ErrGrantFlowCardDatabaseErr)
			}
		}

		msg := fmt.Sprintf("尊敬的会长，您的公会获得%d张%s，有效期至%04d年%02d月%02d日，请前往【会长经营后台】查看使用。",
			req.GetInfo().GetCnt(), model.MapLv2LvName[req.GetInfo().GetLevel()], expireTm.Year(), expireTm.Month(), expireTm.Day())

		m.PushFuWuHaoMsgWithOlInfo(req.GetInfo().GetId(), model.FuWuHaoGuild, msg, "")
	}
	if req.GetGrantType() == uint32(pb.FlowCardGrantType_GrantAnchor) {
		err := m.clientPool.Store.Transaction(ctx, func(tx *gorm.DB) error {
			err := m.clientPool.Store.GrantAnchorFlowCard(tx, req.GetInfo().GetId(), req.GetInfo().GetLevel(), 0, req.GetInfo().GetCnt(), expireTm)
			if err != nil {
				log.Errorf("GrantFlowCard GrantAnchorFlowCard failed req:%v err:%v", req, err)
				return err
			}

			err = m.clientPool.Store.RecordFlowCardGrantLog(tx, model.FlowCardGrantLog{
				OrderId:      req.GetInfo().GetOrderId(),
				GrantType:    req.GetGrantType(),
				Id:           req.GetInfo().GetId(),
				RecLevel:     req.GetInfo().GetLevel(),
				Cnt:          req.GetInfo().GetCnt(),
				ExpireTs:     req.GetInfo().GetExpirtTs(),
				GuildGrantId: req.GetInfo().GetGuildGrantId(),
			})
			if err != nil {
				log.Errorf("GrantFlowCard RecordFlowCardGrantLog failed req:%v err:%v", req, err)
				return err
			}

			return nil
		})
		if err != nil {
			if err == model.DuplicateKeyErr {
				return resp, nil
			} else {
				return resp, protocol.NewServerError(status.ErrGrantFlowCardDatabaseErr)
			}
		}

		msg := fmt.Sprintf(model.AnchorFuWuHaoPushMsg,
			req.GetInfo().GetCnt(), model.MapLv2LvName[req.GetInfo().GetLevel()], expireTm.Year(), expireTm.Month(), expireTm.Day())

		m.PushFuWuHaoMsgWithOlInfo(req.GetInfo().GetId(), model.FuWuHaoAnchor, msg, model.AnchorFuWuHaoHighLight)
	}

	log.Infof("GrantFlowCard end req:%v resp:%v", req, resp)
	return resp, nil
}

func (m *ChannelRecommendManager) grantGuildFlowCard(ctx context.Context, info *pb.FlowCardGrantInfo) *pb.GrantErrorMsg {
	err := m.clientPool.Store.Transaction(ctx, func(tx *gorm.DB) error {
		err := m.clientPool.Store.GrantGuildFlowCard(tx, info.GetId(), info.GetLevel(), info.GetCnt(), time.Unix(int64(info.GetExpirtTs()), 0))
		if err != nil {
			log.ErrorWithCtx(ctx, "grantGuildFlowCard GrantGuildFlowCard failed info:%v err:%v", info, err)
			return err
		}

		err = m.clientPool.Store.RecordFlowCardGrantLog(tx, model.FlowCardGrantLog{
			OrderId:      info.GetOrderId(),
			GrantType:    uint32(pb.FlowCardGrantType_GrantGuild),
			Id:           info.GetId(),
			RecLevel:     info.GetLevel(),
			Cnt:          info.GetCnt(),
			ExpireTs:     info.GetExpirtTs(),
			GuildGrantId: info.GetGuildGrantId(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "grantGuildFlowCard RecordFlowCardGrantLog failed info:%v err:%v", info, err)
			return err
		}

		return nil
	})
	if err != nil && err != model.DuplicateKeyErr {
		return &pb.GrantErrorMsg{
			OrderId: info.GetOrderId(),
			ErrCode: status.ErrGrantFlowCardDatabaseErr,
			ErrMsg:  status.MessageFromCode(status.ErrGrantFlowCardDatabaseErr),
		}

	}

	log.DebugWithCtx(ctx, "grantGuildFlowCard end info:%v", info)
	return &pb.GrantErrorMsg{}
}

func (m *ChannelRecommendManager) grantAnchorFlowCard(ctx context.Context, info *pb.FlowCardGrantInfo) *pb.GrantErrorMsg {
	err := m.clientPool.Store.Transaction(ctx, func(tx *gorm.DB) error {
		err := m.clientPool.Store.GrantAnchorFlowCard(tx, info.GetId(), info.GetLevel(), 0, info.GetCnt(), time.Unix(int64(info.GetExpirtTs()), 0))
		if err != nil {
			log.Errorf("grantAnchorFlowCard GrantAnchorFlowCard failed info:%v err:%v", info, err)
			return err
		}

		err = m.clientPool.Store.RecordFlowCardGrantLog(tx, model.FlowCardGrantLog{
			OrderId:      info.GetOrderId(),
			GrantType:    uint32(pb.FlowCardGrantType_GrantAnchor),
			Id:           info.GetId(),
			RecLevel:     info.GetLevel(),
			Cnt:          info.GetCnt(),
			ExpireTs:     info.GetExpirtTs(),
			GuildGrantId: info.GetGuildGrantId(),
		})
		if err != nil {
			log.Errorf("grantAnchorFlowCard RecordFlowCardGrantLog failed info:%v err:%v", info, err)
			return err
		}

		return nil
	})
	if err != nil && err != model.DuplicateKeyErr {
		return &pb.GrantErrorMsg{
			OrderId: info.GetOrderId(),
			ErrCode: status.ErrGrantFlowCardDatabaseErr,
			ErrMsg:  status.MessageFromCode(status.ErrGrantFlowCardDatabaseErr),
		}
	}
	log.DebugWithCtx(ctx, "grantAnchorFlowCard end info:%v", info)
	return &pb.GrantErrorMsg{}
}

func (m *ChannelRecommendManager) BatGrantFlowCard(ctx context.Context, req *pb.BatGrantFlowCardReq) (*pb.BatGrantFlowCardResp, error) {
	resp := &pb.BatGrantFlowCardResp{}

	log.Infof("BatGrantFlowCard begin req:%v", req)

	mapId2IsFail := make(map[uint32]bool, 0)
	if req.GetGrantType() == uint32(pb.FlowCardGrantType_GrantGuild) {
		for _, info := range req.GetInfoList() {
			errMsg := m.grantGuildFlowCard(ctx, info)
			if errMsg.ErrCode != 0 {
				mapId2IsFail[info.GetId()] = true
				resp.ErrList = append(resp.ErrList, errMsg)
			}
		}

		for _, info := range req.GetInfoList() {
			if mapId2IsFail[info.GetId()] {
				continue
			}

			expireTm := time.Unix(int64(info.GetExpirtTs()), 0)
			msg := fmt.Sprintf("尊敬的会长，您的公会获得%d张%s，有效期至%04d年%02d月%02d日，请前往【会长经营后台】查看使用。",
				info.GetCnt(), model.MapLv2LvName[info.GetLevel()], expireTm.Year(), expireTm.Month(), expireTm.Day())

			m.PushFuWuHaoMsgWithOlInfo(info.GetId(), model.FuWuHaoGuild, msg, "")
		}
	}
	if req.GetGrantType() == uint32(pb.FlowCardGrantType_GrantAnchor) {
		for _, info := range req.GetInfoList() {
			errMsg := m.grantAnchorFlowCard(ctx, info)
			if errMsg.ErrCode != 0 {
				mapId2IsFail[info.GetId()] = true
				resp.ErrList = append(resp.ErrList, errMsg)
			}
		}

		for _, info := range req.GetInfoList() {
			if mapId2IsFail[info.GetId()] {
				continue
			}

			expireTm := time.Unix(int64(info.GetExpirtTs()), 0)
			msg := fmt.Sprintf(model.AnchorFuWuHaoPushMsg,
				info.GetCnt(), model.MapLv2LvName[info.GetLevel()], expireTm.Year(), expireTm.Month(), expireTm.Day())

			m.PushFuWuHaoMsgWithOlInfo(info.GetId(), model.FuWuHaoAnchor, msg, model.AnchorFuWuHaoHighLight)
		}
	}

	log.Infof("BatGrantFlowCard end req:%v resp:%v", req, resp)
	return resp, nil

}

func (m *ChannelRecommendManager) ReclaimGrantedFlowCard(ctx context.Context, req *pb.ReclaimGrantedFlowCardReq) (*pb.ReclaimGrantedFlowCardResp, error) {
	resp := &pb.ReclaimGrantedFlowCardResp{}

	log.Infof("ReclaimGrantedFlowCard begin req:%v", req)
	if req.GetGrantType() == uint32(pb.FlowCardGrantType_GrantGuild) {
		err := m.clientPool.Store.ReclaimGuildFlowCardCnt(req.GetGrantId(), req.GetReclaimCnt())
		if err != nil {
			log.Errorf("ReclaimGrantedFlowCard ReclaimGuildFlowCardCnt failed req:%v err:%v", req, err)
			return resp, err
		}
	}
	if req.GetGrantType() == uint32(pb.FlowCardGrantType_GrantAnchor) {
		err := m.clientPool.Store.ReclaimAnchorFlowCardCnt(req.GetGrantId(), req.GetReclaimCnt())
		if err != nil {
			log.Errorf("ReclaimGrantedFlowCard ReclaimGuildFlowCardCnt failed req:%v err:%v", req, err)
			return resp, err
		}
	}

	log.Infof("ReclaimGrantedFlowCard end req:%v resp:%v", req, resp)
	return resp, nil
}

func (m *ChannelRecommendManager) BanGrantedFlowCard(ctx context.Context, req *pb.BanGrantedFlowCardReq) (*pb.BanGrantedFlowCardResp, error) {
	resp := &pb.BanGrantedFlowCardResp{}

	log.Infof("BanGrantedFlowCard begin req:%v", req)
	if req.GetGrantType() == uint32(pb.FlowCardGrantType_GrantGuild) {
		err := m.clientPool.Store.BanGuildFlowCard(req.GetGrantId(), req.GetBeginTs(), req.GetEndTs())
		if err != nil {
			log.Errorf("BanGrantedFlowCard BanGuildFlowCard failed req:%v err:%v", req, err)
			return resp, err
		}
	}
	if req.GetGrantType() == uint32(pb.FlowCardGrantType_GrantAnchor) {
		err := m.clientPool.Store.BanAnchorFlowCard(req.GetGrantId(), req.GetBeginTs(), req.GetEndTs())
		if err != nil {
			log.Errorf("BanGrantedFlowCard BanAnchorFlowCard failed req:%v err:%v", req, err)
			return resp, err
		}
	}

	log.Infof("BanGrantedFlowCard end req:%v resp:%v", req, resp)
	return resp, nil
}

func (m *ChannelRecommendManager) GrantAnchorFlowCardByGuild(ctx context.Context, req *pb.GrantAnchorFlowCardByGuildReq) (*pb.GrantAnchorFlowCardByGuildResp, error) {
	resp := &pb.GrantAnchorFlowCardByGuildResp{}

	log.Infof("GrantAnchorFlowCardByGuild begin req:%v", req)

	guildFlowCard, err := m.clientPool.Store.GetGuildFlowCardById(req.GetGrantId())
	if err != nil {
		log.Errorf("GrantAnchorFlowCardByGuild GetGuildFlowCardById failed req:%v err:%v", req, err)
		return resp, err
	}

	nowTs := uint32(time.Now().Unix())
	if guildFlowCard.BanBeginTs <= nowTs && guildFlowCard.BanEndTs >= nowTs {
		log.Errorf("GrantAnchorFlowCardByGuild card is baned req:%v", req)
		banEndTm := time.Unix(int64(guildFlowCard.BanEndTs), 0)
		return resp, protocol.NewServerError(-2, fmt.Sprintf(model.GrantFlowCardIsBannedMsg, banEndTm.Year(), banEndTm.Month(), banEndTm.Day()))
	}

	if uint32(guildFlowCard.ExpireTime.Unix()) < nowTs {
		log.Errorf("GrantAnchorFlowCardByGuild card is expire req:%v info:%v", req, guildFlowCard)
		return resp, protocol.NewServerError(-2, "流量卡已经过期")
	}

	mapId2IsFail := make(map[uint32]bool, 0)
	for _, info := range req.GetInfoList() {
		err = m.clientPool.Store.Transaction(ctx, func(tx *gorm.DB) error {
			updateRowCnt, err := m.clientPool.Store.UseGuildFlowCard(tx, req.GetGrantId(), req.GetGuildId(), info.GetCnt())
			if err != nil {
				log.Errorf("GrantAnchorFlowCardByGuild UseGuildFlowCard failed req:%v err:%v", req, err)
				return err
			}

			if updateRowCnt == 0 {
				log.Errorf("GrantAnchorFlowCardByGuild UseGuildFlowCard no enough cnt to use req:%v", req)
				return protocol.NewServerError(-2, "已有的流量卡不足，请检查后重新填写")
			}

			err = m.clientPool.Store.GrantAnchorFlowCard(tx, info.GetId(), guildFlowCard.RecLevel, req.GetGrantId(), info.GetCnt(), guildFlowCard.ExpireTime)
			if err != nil {
				log.Errorf("GrantAnchorFlowCardByGuild GrantAnchorFlowCard failed req:%v err:%v", req, err)
				return err
			}

			// 流水记录
			err = m.clientPool.Store.RecordFlowCardConsumeLog(tx, model.FlowCardConsumeLog{
				ConsumeType: model.FlowCardConsumeTypeGrant,
				GrantId:     req.GetGrantId(),
				GuildId:     req.GetGuildId(),
				AnchorUid:   info.GetId(),
				RecLevel:    guildFlowCard.RecLevel,
				Cnt:         info.GetCnt(),
				ExpireTs:    uint32(guildFlowCard.ExpireTime.Unix()),
				EffectTs:    0,
				RemainCnt:   guildFlowCard.TotalCnt - guildFlowCard.UsedCnt - info.GetCnt(),
			})
			if err != nil {
				log.Errorf("GrantAnchorFlowCardByGuild RecordFlowCardConsumeLog failed req:%v info:%v err:%v", req, info, err)
				return err
			}

			return nil
		})
		if err != nil {
			resp.ErrList = append(resp.ErrList, &pb.GrantErrorMsg{
				Id:      info.GetId(),
				ErrCode: status.ErrGrantFlowCardDatabaseErr,
				ErrMsg:  status.MessageFromCode(status.ErrGrantFlowCardDatabaseErr),
			})
			mapId2IsFail[info.GetId()] = true
		}
	}

	for _, info := range req.GetInfoList() {
		if mapId2IsFail[info.GetId()] {
			continue
		}

		msg := fmt.Sprintf("亲爱的达人，你获得%d张%s，有效期至%04d年%02d月%02d日，请前往【达人中心】查看使用。去查看>>>",
			info.GetCnt(), model.MapLv2LvName[guildFlowCard.RecLevel], guildFlowCard.ExpireTime.Year(), guildFlowCard.ExpireTime.Month(), guildFlowCard.ExpireTime.Day())

		m.PushFuWuHaoMsgWithOlInfo(info.GetId(), model.FuWuHaoAnchor, msg, "去查看>>>")
	}

	log.Infof("GrantAnchorFlowCardByGuild end req:%v resp:%v", req, resp)
	return resp, nil
}

func (m *ChannelRecommendManager) getHourLimitCnt(level, dayHourCnt uint32, limitConf *pb.LimitConf) (uint32, bool) {
	log.Debugf("getHourLimitCnt limitConf:%v cnt:%d", limitConf, dayHourCnt)

	if dayHourCnt == limitConf.HourCnt {
		for _, lvConf := range limitConf.ConfList {
			if level == lvConf.Level {
				return lvConf.Cnt, true
			}
		}
	}

	return model.MapLv2DefaultLimitCnt[pb.RecommendLevel(level)], false
}

func (m *ChannelRecommendManager) getFlowCardHourLimitCnt(level, ts uint32) (uint32, error) {
	limitTotalCnt := model.MapLv2DefaultLimitCnt[pb.RecommendLevel(level)]

	// 获取限额配置
	strLimitConf, err := m.clientPool.Cache.GetFlowCardLimitConf(ts)
	if err != nil {
		log.Errorf("getFlowCardHourLimitCnt GetFlowCardLimitConf failed level:%d ts:%d err:%v", level, ts, err)
		return limitTotalCnt, err
	}

	if strLimitConf == "" {
		// 需要读取mysql
		limitList, err := m.clientPool.Store.GetFlowCardLimitListByTs(ts)
		if err != nil {
			log.Errorf("getFlowCardHourLimitCnt GetFlowCardLimitListByTs level:%d ts:%d err:%v", level, ts, err)
			return limitTotalCnt, err
		}

		if len(limitList) <= 0 {
			// 没有配置, 直接默认
			strLimitConf = model.FlowCardLimitConfDefaultStr
		} else {
			strLimitConf = limitList[0].LimitConf
		}

		// 写缓存
		err = m.clientPool.Cache.SetFlowCardLimitConf(ts, strLimitConf)
		if err != nil {
			log.Errorf("getFlowCardHourLimitCnt SetFlowCardLimitConf failed level:%d ts:%d err:%v", level, ts, err)
		}
	}

	if strLimitConf != model.FlowCardLimitConfDefaultStr {
		limitConfList, err := transStringToLimitConfList(strLimitConf)
		if err != nil {
			log.Errorf("getFlowCardHourLimitCnt transStringToLimitConfList failed level:%d ts:%d err:%v", level, ts, err)
			return limitTotalCnt, err
		}

		tm := time.Unix(int64(ts), 0)
		dayBeginTm := time.Date(tm.Year(), tm.Month(), tm.Day(), 0, 0, 0, 0, time.Local)
		dayHourCnt := uint32((tm.Unix() - dayBeginTm.Unix()) / 3600)

		for _, limitConf := range limitConfList {
			tmpLimitTotalCnt, isExist := m.getHourLimitCnt(level, dayHourCnt, limitConf)
			if isExist {
				limitTotalCnt = tmpLimitTotalCnt
				break
			}

		}
	}

	log.Infof("getFlowCardHourLimitCnt conf:%s %d", strLimitConf, limitTotalCnt)
	return limitTotalCnt, nil
}

func (m *ChannelRecommendManager) GetFlowCardHourRemainCnt(ctx context.Context, req *pb.GetFlowCardHourRemainCntReq) (*pb.GetFlowCardHourRemainCntResp, error) {
	resp := &pb.GetFlowCardHourRemainCntResp{}

	log.Debugf("GetFlowCardHourRemainCnt begin req:%v", req)

	// 获取已使用的数量
	useCnt, err := m.clientPool.Cache.GetFlowCardHourUseCnt(req.GetLevel(), req.GetTs())
	if err != nil {
		log.Errorf("GetFlowCardHourRemainCnt GetFlowCardHourUseCnt failed req:%v err:%v", req, err)
		return resp, err
	}

	// 获取限额配置
	limitTotalCnt, err := m.getFlowCardHourLimitCnt(req.GetLevel(), req.GetTs())
	if err != nil {
		log.Errorf("GetFlowCardHourRemainCnt getFlowCardHourLimitCnt failed req:%v err:%v", req, err)
		return resp, err
	}

	var remainCnt uint32 = 0
	if limitTotalCnt > useCnt {
		remainCnt = limitTotalCnt - useCnt
	}

	resp.RemainCnt = remainCnt

	log.Infof("GetFlowCardHourRemainCnt end req:%v resp:%v %d %d", req, resp, limitTotalCnt, useCnt)
	return resp, nil
}

type CheckParam struct {
	anchorUid  uint32
	guildId    uint32
	recLevel   uint32
	beginTs    uint32
	expireTs   uint32
	banBeginTs uint32
	banEndTs   uint32
}

func (m *ChannelRecommendManager) checkFlowCardTimeLimit(anchorUid, beginTs, expireTs, banBeginTs, banEndTs uint32, nowTm time.Time) protocol.ServerError {
	maxBeginTs := uint32(time.Date(nowTm.Year(), nowTm.Month(), nowTm.Day(), nowTm.Hour()+2, 0, 0, 0, time.Local).Unix())
	nextHourBeginTs := uint32(time.Date(nowTm.Year(), nowTm.Month(), nowTm.Day(), nowTm.Hour()+1, 0, 0, 0, time.Local).Unix())
	beginTm := time.Unix(int64(beginTs), 0)

	if beginTs >= maxBeginTs {
		log.Infof("checkFlowCardTimeLimit more than maxBeginTs %d %d %d", anchorUid, beginTs, maxBeginTs)
		return protocol.NewServerError(-2, "无效的流量卡生效时间")
	}

	nowTs := uint32(nowTm.Unix())
	// 现在使用和下一个时间段使用
	if (banBeginTs <= nowTs && nowTs <= banEndTs) || (banBeginTs <= beginTs && beginTs <= banEndTs) {
		log.Infof("checkFlowCardTimeLimit card is baned %d %d %d %d", anchorUid, beginTs, banBeginTs, banEndTs)
		banEndTm := time.Unix(int64(banEndTs), 0)
		return protocol.NewServerError(-2, fmt.Sprintf(model.UseFlowCardIsBannedMsg, banEndTm.Year(), banEndTm.Month(), banEndTm.Day()))
	}

	if expireTs < nowTs {
		log.Infof("checkFlowCardTimeLimit card is expire %d %d", anchorUid, expireTs)
		return protocol.NewServerError(-2, "流量卡已经过期")
	}

	if beginTm.Hour() == nowTm.Hour() && nowTs > (nextHourBeginTs-model.UseFlowCardToastTs) {
		toastCnt, err := m.clientPool.Cache.IncrUseFlowCardToastFlag(anchorUid, nowTs, 1)
		if err != nil {
			log.Errorf("checkFlowCardTimeLimit IncrUseFlowCardToastFlag failed  %d  %d",
				anchorUid, beginTs)
		}

		// 本时段只提示一次
		if toastCnt == 1 {
			log.Infof("checkFlowCardTimeLimit toast ts %d %d", anchorUid, nextHourBeginTs)
			return protocol.NewServerError(-2, model.UseFlowCardToastMsg)
		}
	}

	log.Debugf("checkFlowCardTimeLimit end %d", anchorUid)
	return nil
}

func (m *ChannelRecommendManager) checkFlowCardUseLimit(ctx context.Context, checkParam CheckParam) (uint32, protocol.ServerError) {
	anchorUid, guildId, recLevel, beginTs, expireTs, banBeginTs, banEndTs := checkParam.anchorUid, checkParam.guildId,
		checkParam.recLevel, checkParam.beginTs, checkParam.expireTs, checkParam.banBeginTs, checkParam.banEndTs

	log.Debugf("checkFlowCardUserLimit begin %d %d %d %d %d %d",
		anchorUid, guildId, recLevel, beginTs, banBeginTs, banEndTs)

	nowTm := time.Now()
	nowTs := uint32(nowTm.Unix())

	sErr := m.checkFlowCardTimeLimit(anchorUid, beginTs, expireTs, banBeginTs, banEndTs, nowTm)
	if sErr != nil {
		log.Infof("checkFlowCardUserLimit checkFlowCardTimeLimit limit %d %d %d sErr:%v", anchorUid, guildId, beginTs, sErr)
		return guildId, sErr
	}

	newUseCnt, err := m.clientPool.Cache.IncrFlowCardHourUseCnt(recLevel, beginTs, 1)
	if err != nil {
		log.Errorf("checkFlowCardUseLimit IncrFlowCardHourUseCnt failed %d %d %d %d %d %d err:%v", anchorUid, guildId, recLevel, beginTs, banBeginTs, banEndTs, err)
		return guildId, protocol.ToServerError(err)
	}

	// 获取限额配置
	limitTotalCnt, err := m.getFlowCardHourLimitCnt(recLevel, beginTs)
	if err != nil {
		log.Errorf("checkFlowCardUseLimit getFlowCardHourLimitCnt failed %d %d %d %d %d %d err:%v", anchorUid, guildId, recLevel, beginTs, banBeginTs, banEndTs, err)
		return guildId, protocol.ToServerError(err)
	}

	if limitTotalCnt < newUseCnt {
		log.Infof("checkFlowCardUseLimit more limit cnt to use %d %d %d %d %d %d %d %d", anchorUid, guildId, recLevel, beginTs, banBeginTs, banEndTs, limitTotalCnt, newUseCnt)
		return guildId, protocol.NewServerError(-2, "当前时段流量卡已没有名额了哦，换个时间吧")
	}

	// 主播小时限额
	anchorNewHourUseCnt, err := m.clientPool.Cache.IncrAnchorFlowCardHourUseCnt(anchorUid, beginTs, 1)
	if err != nil {
		log.Errorf("checkFlowCardUseLimit IncrAnchorFlowCardHourUseCnt failed %d %d %d %d %d %d err:%v", anchorUid, guildId, recLevel, beginTs, banBeginTs, banEndTs, err)
		return guildId, protocol.ToServerError(err)
	}

	if model.AnchorFlowCardHourUseCntLimit < anchorNewHourUseCnt {
		m.reclaimUseCnt(anchorUid, guildId, recLevel, beginTs, ReclaimUseCntTypeCommon)
		log.Infof("checkFlowCardUseLimit more than anchor hour limit cnt to use %d %d %d %d %d %d %d",
			anchorUid, guildId, recLevel, beginTs, banBeginTs, banEndTs, model.AnchorFlowCardDayUseCntLimit, anchorNewHourUseCnt)
		return guildId, protocol.NewServerError(-2, "当前时段已使用流量卡，不支持再次使用哦")
	}

	//主播每天限额
	anchorNewDayUseCnt, err := m.clientPool.Cache.IncrAnchorFlowCardDayUseCnt(anchorUid, beginTs, 1)
	if err != nil {
		log.Errorf("checkFlowCardUseLimit IncrAnchorFlowCardDayUseCnt failed %d %d %d %d %d %d err:%v", anchorUid, guildId, recLevel, beginTs, banBeginTs, banEndTs, err)
		return guildId, protocol.ToServerError(err)
	}

	if model.AnchorFlowCardDayUseCntLimit < anchorNewDayUseCnt {
		m.reclaimUseCnt(anchorUid, guildId, recLevel, beginTs, ReclaimUseCntTypeCommon+ReclaimUseHourCntTypeAnchor)
		log.Infof("checkFlowCardUseLimit more anchor limit cnt to use %d %d %d %d %d %d %d",
			anchorUid, guildId, recLevel, beginTs, banBeginTs, banEndTs, model.AnchorFlowCardDayUseCntLimit, anchorNewDayUseCnt)
		return guildId, protocol.NewServerError(-2, "每个达人每日最多使用两张流量卡哦，明天再来吧")
	}

	// 公会限额
	if guildId == 0 {
		// 从签约服务获取
		contractInfo, err := m.clientPool.AnchorContractCli.GetUserContractCacheInfo(ctx, anchorUid, anchorUid)
		if err != nil {
			log.Errorf("checkFlowCardUseLimit GetUserContractCacheInfo failed uid:%d err:%v", anchorUid, err)
			return guildId, err
		}

		if contractInfo.GetContract().GetGuildId() != 0 && contractInfo.GetContract().GetExpireTime() >= nowTs {
			for _, identify := range contractInfo.GetAnchorIdentityList() {
				if identify == uint32(anchorContractPb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE) {
					guildId = contractInfo.GetContract().GetGuildId()
				}
			}
		}
	}

	if guildId == 0 {
		m.reclaimUseCnt(anchorUid, guildId, recLevel, beginTs, ReclaimUseCntTypeCommon+ReclaimUseHourCntTypeAnchor+ReclaimUseDayCntTypeAnchor)
		log.Infof("checkFlowCardUseLimit no sign guild uid:%d", anchorUid)
		return guildId, protocol.NewServerError(-2, "没有签约听听身份，暂不能使用流量卡")
	}

	guildNewUseCnt, err := m.clientPool.Cache.IncrGuildFlowCardDayUseCnt(guildId, recLevel, beginTs, 1)
	if err != nil {
		log.Errorf("checkFlowCardUseLimit IncrGuildFlowCardDayUseCnt %d %d %d %d err:%v", anchorUid, guildId, recLevel, beginTs, err)
		return guildId, protocol.ToServerError(err)
	}

	guildUseLimit, err := m.clientPool.Cache.GetGuildFlowCardUseLimit(guildId, recLevel)
	if err != nil {
		log.Errorf("checkFlowCardUseLimit GetGuildFlowCardUseLimit %d %d %d %d err:%v", anchorUid, guildId, recLevel, beginTs, err)
		return guildId, protocol.ToServerError(err)
	}

	if guildUseLimit < guildNewUseCnt {
		m.reclaimUseCnt(anchorUid, guildId, recLevel, beginTs, ReclaimUseCntTypeCommon+ReclaimUseHourCntTypeAnchor+ReclaimUseDayCntTypeAnchor)
		log.Infof("checkFlowCardUseLimit GetGuildFlowCardUseLimit %d %d %d %d %d %d err:%v", anchorUid, guildId, recLevel, beginTs, err)
		return guildId, protocol.NewServerError(-2, "你所在的公会今日已没有流量卡可用名额了哦，换个时间吧")
	}

	log.Debugf("checkFlowCardUseLimit end %d %d %d %d %d", anchorUid, recLevel, beginTs, banBeginTs, banEndTs)
	return guildId, nil
}

func (m *ChannelRecommendManager) effectAnchorFlowCardLv(ctx context.Context, anchorUid, recLevel, ts uint32) protocol.ServerError {
	log.Debugf("effectAnchorFlowCardLv begin %d %d %d", anchorUid, recLevel, ts)

	// 获取房间id
	liveResp, err := m.clientPool.LiveMgrCli.GetChannelLiveInfo(ctx, anchorUid, false)
	if err != nil {
		log.Errorf("effectAnchorFlowCardLv GetChannelLiveInfo failed %d %d %d err:%v", anchorUid, recLevel, ts, err)
		return err
	}

	if liveResp.GetChannelLiveInfo() == nil {
		log.Errorf("effectAnchorFlowCardLv no live permission %d %d %d", anchorUid, recLevel, ts)
		return protocol.NewServerError(-2, "没有听听权限")
	}

	cErr := m.clientPool.Cache.SetChannelFlowCardRecommendLv(liveResp.GetChannelLiveInfo().GetChannelId(), recLevel, ts)
	if cErr != nil {
		log.Errorf("effectAnchorFlowCardLv SetChannelFlowCardRecommendLv failed %d %d %d %d err:%v", liveResp.GetChannelLiveInfo().GetChannelId(), anchorUid, recLevel, ts, err)
		return protocol.ToServerError(cErr)
	}

	log.Infof("effectAnchorFlowCardLv end %d %d %d %d", liveResp.GetChannelLiveInfo().GetChannelId(), anchorUid, recLevel, ts)
	return nil
}

func (m *ChannelRecommendManager) reclaimUseCnt(anchorUid, guildId, recLevel, beginTs uint32, reclaimType int) {
	log.Debugf("reclaimUseCnt begin %d %d %d %d %d ", anchorUid, guildId, recLevel, beginTs, reclaimType)

	if reclaimType&ReclaimUseCntTypeCommon != 0 {
		_, err := m.clientPool.Cache.IncrFlowCardHourUseCnt(recLevel, beginTs, -1)
		if err != nil {
			log.Errorf("reclaimUseCnt IncrFlowCardHourUseCnt failed %d %d %d %d err:%v", anchorUid, guildId, recLevel, beginTs, err)
		}
	}

	if reclaimType&ReclaimUseHourCntTypeAnchor != 0 {
		_, err := m.clientPool.Cache.IncrAnchorFlowCardHourUseCnt(anchorUid, beginTs, -1)
		if err != nil {
			log.Errorf("reclaimUseCnt IncrAnchorFlowCardHourUseCnt failed %d %d %d %d err:%v", anchorUid, guildId, recLevel, beginTs, err)
		}
	}

	if reclaimType&ReclaimUseDayCntTypeAnchor != 0 {
		_, err := m.clientPool.Cache.IncrAnchorFlowCardDayUseCnt(anchorUid, beginTs, -1)
		if err != nil {
			log.Errorf("reclaimUseCnt IncrAnchorFlowCardDayUseCnt failed %d %d %d %d err:%v", anchorUid, guildId, recLevel, beginTs, err)
		}
	}

	if reclaimType&ReclaimUseCntTypeGuild != 0 {
		_, err := m.clientPool.Cache.IncrGuildFlowCardDayUseCnt(guildId, recLevel, beginTs, -1)
		if err != nil {
			log.Errorf("reclaimUseCnt IncrGuildFlowCardDayUseCnt failed %d %d %d %d err:%v", anchorUid, guildId, recLevel, beginTs, err)
		}
	}

	log.Infof("reclaimUseCnt end  %d %d %d %d %d", anchorUid, guildId, recLevel, beginTs, reclaimType)
}

func (m *ChannelRecommendManager) guildUseFlowCard(ctx context.Context, req *pb.UseFlowCardReq) (uint32, error) {
	log.DebugfWithCtx(ctx, "guildUseFlowCard begin req:%v", req)

	var flowCardLv uint32 = 0
	guildFlowCard, err := m.clientPool.Store.GetGuildFlowCardById(req.GetGrantId())
	if err != nil {
		log.Errorf("guildUseFlowCard GetGuildFlowCardById failed req:%v err:%v", req, err)
		return flowCardLv, err
	}

	flowCardLv = guildFlowCard.RecLevel
	signGuildId, sErr := m.checkFlowCardUseLimit(ctx, CheckParam{
		anchorUid:  req.GetAnchorUid(),
		guildId:    req.GetGuildId(),
		recLevel:   guildFlowCard.RecLevel,
		beginTs:    req.GetBeginTs(),
		expireTs:   uint32(guildFlowCard.ExpireTime.Unix()),
		banBeginTs: guildFlowCard.BanBeginTs,
		banEndTs:   guildFlowCard.BanEndTs,
	})
	if sErr != nil {
		log.Errorf("guildUseFlowCard checkFlowCardUserLimit unable to user req:%v card:%v", req, guildFlowCard)
		return flowCardLv, sErr
	}

	// 公会使用
	err = m.clientPool.Store.Transaction(ctx, func(tx *gorm.DB) error {
		updateRowCnt, err := m.clientPool.Store.UseGuildFlowCard(tx, req.GetGrantId(), req.GetGuildId(), 1)
		if err != nil {
			log.Errorf("guildUseFlowCard UseGuildFlowCard failed req:%v err:%v", req, err)
			m.reclaimUseCnt(req.GetAnchorUid(), signGuildId, guildFlowCard.RecLevel, req.GetBeginTs(),
				ReclaimUseCntTypeCommon+ReclaimUseHourCntTypeAnchor+ReclaimUseHourCntTypeAnchor+ReclaimUseCntTypeGuild)
			return err
		}

		if updateRowCnt == 0 {
			log.Errorf("guildUseFlowCard UseGuildFlowCard no enough cnt to use req:%v", req)
			m.reclaimUseCnt(req.GetAnchorUid(), signGuildId, guildFlowCard.RecLevel, req.GetBeginTs(),
				ReclaimUseCntTypeCommon+ReclaimUseHourCntTypeAnchor+ReclaimUseHourCntTypeAnchor+ReclaimUseCntTypeGuild)
			return protocol.NewServerError(-2, "已有的流量卡不足")
		}

		// 流水记录
		err = m.clientPool.Store.RecordFlowCardConsumeLog(tx, model.FlowCardConsumeLog{
			ConsumeType: model.FlowCardConsumeTypeUse,
			GrantId:     req.GetGrantId(),
			GuildId:     req.GetGuildId(),
			AnchorUid:   req.GetAnchorUid(),
			RecLevel:    guildFlowCard.RecLevel,
			Cnt:         1,
			ExpireTs:    0,
			EffectTs:    req.GetBeginTs(),
			RemainCnt:   guildFlowCard.TotalCnt - guildFlowCard.UsedCnt - 1,
		})
		if err != nil {
			log.Errorf("guildUseFlowCard RecordFlowCardConsumeLog failed req:%v err:%v", req, err)
			m.reclaimUseCnt(req.GetAnchorUid(), signGuildId, guildFlowCard.RecLevel, req.GetBeginTs(),
				ReclaimUseCntTypeCommon+ReclaimUseHourCntTypeAnchor+ReclaimUseHourCntTypeAnchor+ReclaimUseCntTypeGuild)
			return err
		}

		err = m.effectAnchorFlowCardLv(ctx, req.GetAnchorUid(), guildFlowCard.RecLevel, req.GetBeginTs())
		if err != nil {
			log.Errorf("guildUseFlowCard effectAnchorFlowCardLv failed req:%v card:%v err:%v", req, guildFlowCard, err)
			m.reclaimUseCnt(req.GetAnchorUid(), signGuildId, guildFlowCard.RecLevel, req.GetBeginTs(),
				ReclaimUseCntTypeCommon+ReclaimUseHourCntTypeAnchor+ReclaimUseHourCntTypeAnchor+ReclaimUseCntTypeGuild)
			return err
		}
		return nil
	})
	if err != nil {
		return flowCardLv, err
	}

	log.DebugfWithCtx(ctx, "guildUseFlowCard end req:%v flowCardLv:%d", req, flowCardLv)
	return flowCardLv, nil
}

func (m *ChannelRecommendManager) anchorUseFlowCard(ctx context.Context, req *pb.UseFlowCardReq) (uint32, error) {
	log.DebugfWithCtx(ctx, "anchorUseFlowCard begin req:%v", req)

	var flowCardLv uint32 = 0
	anchorFlowCard, err := m.clientPool.Store.GetAnchorFlowCardById(req.GetGrantId())
	if err != nil {
		log.Errorf("anchorUseFlowCard GetAnchorFlowCardById failed req:%v err:%v", req, err)
		return flowCardLv, err
	}

	flowCardLv = anchorFlowCard.RecLevel
	signGuildId, sErr := m.checkFlowCardUseLimit(ctx, CheckParam{
		anchorUid:  req.GetAnchorUid(),
		guildId:    0,
		recLevel:   anchorFlowCard.RecLevel,
		beginTs:    req.GetBeginTs(),
		expireTs:   uint32(anchorFlowCard.ExpireTime.Unix()),
		banBeginTs: anchorFlowCard.BanBeginTs,
		banEndTs:   anchorFlowCard.BanEndTs,
	})
	if sErr != nil {
		log.Errorf("anchorUseFlowCard checkFlowCardUserLimit unable to user req:%v card:%v", req, anchorFlowCard)
		return flowCardLv, sErr
	}

	// 主播使用
	err = m.clientPool.Store.Transaction(ctx, func(tx *gorm.DB) error {
		updateRowCnt, err := m.clientPool.Store.UseAnchorFlowCard(tx, req.GetGrantId(), req.GetAnchorUid(), 1)
		if err != nil {
			m.reclaimUseCnt(req.GetAnchorUid(), signGuildId, anchorFlowCard.RecLevel, req.GetBeginTs(),
				ReclaimUseCntTypeCommon+ReclaimUseHourCntTypeAnchor+ReclaimUseHourCntTypeAnchor+ReclaimUseCntTypeGuild)
			log.Errorf("anchorUseFlowCard UseAnchorFlowCard failed req:%v err:%v", req, err)
			return err
		}

		if updateRowCnt == 0 {
			m.reclaimUseCnt(req.GetAnchorUid(), signGuildId, anchorFlowCard.RecLevel, req.GetBeginTs(),
				ReclaimUseCntTypeCommon+ReclaimUseHourCntTypeAnchor+ReclaimUseHourCntTypeAnchor+ReclaimUseCntTypeGuild)
			log.Errorf("anchorUseFlowCard UseAnchorFlowCard no enough cnt to use req:%v", req)
			return protocol.NewServerError(-2, "已有的流量卡不足")
		}

		// 流水记录
		err = m.clientPool.Store.RecordFlowCardConsumeLog(tx, model.FlowCardConsumeLog{
			ConsumeType: model.FlowCardConsumeTypeUse,
			GrantId:     req.GetGrantId(),
			GuildId:     req.GetGuildId(),
			AnchorUid:   req.GetAnchorUid(),
			RecLevel:    anchorFlowCard.RecLevel,
			Cnt:         1,
			ExpireTs:    0,
			EffectTs:    req.GetBeginTs(),
			RemainCnt:   anchorFlowCard.TotalCnt - anchorFlowCard.UsedCnt - 1,
		})
		if err != nil {
			log.Errorf("anchorUseFlowCard RecordFlowCardConsumeLog failed req:%v err:%v", req, err)
			m.reclaimUseCnt(req.GetAnchorUid(), signGuildId, anchorFlowCard.RecLevel, req.GetBeginTs(),
				ReclaimUseCntTypeCommon+ReclaimUseHourCntTypeAnchor+ReclaimUseHourCntTypeAnchor+ReclaimUseCntTypeGuild)
			return err
		}

		err = m.effectAnchorFlowCardLv(ctx, req.GetAnchorUid(), anchorFlowCard.RecLevel, req.GetBeginTs())
		if err != nil {
			m.reclaimUseCnt(req.GetAnchorUid(), signGuildId, anchorFlowCard.RecLevel, req.GetBeginTs(),
				ReclaimUseCntTypeCommon+ReclaimUseHourCntTypeAnchor+ReclaimUseHourCntTypeAnchor+ReclaimUseCntTypeGuild)
			log.Errorf("anchorUseFlowCard effectAnchorFlowCardLv failed req:%v card:%v err:%v", req, anchorFlowCard, err)
			return err
		}

		return nil
	})
	if err != nil {
		return flowCardLv, err
	}

	log.DebugfWithCtx(ctx, "anchorUseFlowCard end req:%v flowCardLv:%d", req, flowCardLv)
	return flowCardLv, nil
}

func (m *ChannelRecommendManager) UseFlowCard(ctx context.Context, req *pb.UseFlowCardReq) (*pb.UseFlowCardResp, error) {
	resp := &pb.UseFlowCardResp{}

	log.Infof("UseFlowCard begin req:%v", req)
	var flowCardLv uint32
	var err error
	if req.GetGuildId() != 0 {
		flowCardLv, err = m.guildUseFlowCard(ctx, req)
		if err != nil {
			return resp, err
		}
	} else {
		flowCardLv, err = m.anchorUseFlowCard(ctx, req)
		if err != nil {
			return resp, err
		}
	}

	if flowCardLv != 0 {
		beginTm := time.Unix(int64(req.GetBeginTs()), 0)
		msg := fmt.Sprintf("亲爱的达人，你已在%02d月%02d日%02d：00-%02d：59使用1张%s，将于开始时间10分钟后生效，请认真开启听听展示才艺吸引更多听众吧~",
			beginTm.Month(), beginTm.Day(), beginTm.Hour(), beginTm.Hour(), model.MapLv2LvName[flowCardLv])
		m.PushFuWuHaoMsgWithOlInfo(req.GetAnchorUid(), model.FuWuHaoAnchor, msg, "")
	}

	log.Infof("UseFlowCard end req:%v resp:%v", req, resp)
	return resp, nil
}

func (m *ChannelRecommendManager) GetFlowCardListByType(ctx context.Context, req *pb.GetFlowCardListByTypeReq) (*pb.GetFlowCardListByTypeResp, error) {
	resp := &pb.GetFlowCardListByTypeResp{}

	log.Debugf("GetFlowCardListByType begin req:%v", req)

	if req.GetGrantType() == uint32(pb.FlowCardGrantType_GrantAnchor) {
		flowCardList, err := m.clientPool.Store.GetAnchorFlowCardListByType(req.GetId(), req.GetLevel(), req.GetType())
		if err != nil {
			log.Errorf("GetFlowCardListByType GetAnchorFlowCardListByType failed req:%v err:%v", req, err)
			return resp, err
		}

		for _, flowCard := range flowCardList {
			resp.InfoList = append(resp.InfoList, &pb.FlowCardGrantInfo{
				GrantId:      flowCard.GrantId,
				Id:           flowCard.AnchorUid,
				Level:        flowCard.RecLevel,
				ExpirtTs:     uint32(flowCard.ExpireTime.Unix()),
				Cnt:          flowCard.TotalCnt,
				Remark:       flowCard.Remark,
				BanBeginTs:   flowCard.BanBeginTs,
				BanEndTs:     flowCard.BanEndTs,
				UsedCnt:      flowCard.UsedCnt,
				UpdateTs:     uint32(flowCard.UpdateTime.Unix()),
				CreateTs:     uint32(flowCard.CreateTime.Unix()),
				GuildGrantId: flowCard.GuildGrantId,
			})
		}
	}

	log.Debugf("GetFlowCardListByType end req:%v resp:%v", req, resp)
	return resp, nil
}

func (m *ChannelRecommendManager) GetAllUseFlowCardAnchor(ctx context.Context, req *pb.GetAllUseFlowCardAnchorReq) (*pb.GetAllUseFlowCardAnchorResp, error) {
	resp := &pb.GetAllUseFlowCardAnchorResp{
		MapCidLv: make(map[uint32]uint32, 0),
	}

	log.Debugf("GetAllUseFlowCardAnchor begin req:%v", req)

	mapCid2Lv, err := m.clientPool.Cache.GetAllUseFlowCardChByTs(req.GetTs())
	if err != nil {
		log.Errorf("GetAllUseFlowCardAnchor GetAllUseFlowCardChByTs failed req:%v err:%v", req, err)
		return resp, err
	}

	resp.MapCidLv = mapCid2Lv

	log.Debugf("GetAllUseFlowCardAnchor end req:%v resp:%v", req, resp)
	return resp, nil
}

func (m *ChannelRecommendManager) GetRecLotteryChList(ctx context.Context, req *pb.GetRecLotteryChListReq) (*pb.GetRecLotteryChListResp, error) {
	resp := &pb.GetRecLotteryChListResp{}

	if m.clientPool.BusinessConfMgr.GetLotteryRecConf().IsRec == 0 {
		log.DebugWithCtx(ctx, "GetRecLotteryChList is not open rec req:%v", req)
		return resp, nil
	}

	sv, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetRecLotteryChList ServiceInfoFromContext get fail in:%v", req)
		return resp, protocol.NewServerError(status.ErrRequestParamInvalid)
	}

	// 版本控制
	if m.clientPool.BusinessConfMgr.GetLotteryRecConf().MinVersion != "" &&
		protocol.ClientVersion(sv.ClientVersion).String() < m.clientPool.BusinessConfMgr.GetLotteryRecConf().MinVersion {
		log.DebugWithCtx(ctx, "GetRecLotteryChList is min version limit req:%v sv:%v min:%s", req, sv, m.clientPool.BusinessConfMgr.GetLotteryRecConf().MinVersion)
		return resp, nil
	}

	//从1开始
	if req.GetPage() < 1 {
		log.ErrorWithCtx(ctx, "GetRecLotteryChList page error req:%+v", req)
		return resp, nil
	}

	recChList, err := m.clientPool.Cache.GetLotteryRecChList((req.GetPage()-1)*req.GetPageSize(), req.GetPageSize())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLotteryRecChList failed req:%+v err:%v", req, err)
		return resp, err
	}

	mapId2Info, err := m.clientPool.Cache.BatchGetChLotteryInfo(recChList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLotteryRecChList BatchGetChLotteryInfo failed req:%+v err:%v", req, err)
		return resp, err
	}

	for _, cid := range recChList {
		if _, ok := mapId2Info[cid]; ok {
			if mapId2Info[cid].ChannelId > 0 && mapId2Info[cid].ChannelId == cid {
				resp.ChList = append(resp.ChList, &pb.RecLotteryChInfo{
					ChannelId:    mapId2Info[cid].ChannelId,
					LotteryEndTs: uint32(mapId2Info[cid].EndTs),
					AwardCnt:     mapId2Info[cid].AwardCnt,
					GiftInfo: &pb.GiftInfo{
						GiftId:        mapId2Info[cid].GiftInfo.GiftId,
						GiftName:      mapId2Info[cid].GiftInfo.GiftName,
						GiftIcon:      mapId2Info[cid].GiftInfo.GiftImg,
						GiftPrice:     mapId2Info[cid].GiftInfo.GiftPrice,
						GiftPriceType: mapId2Info[cid].GiftInfo.GiftPriceType,
					},
				})
			}
		} else {
			err = m.clientPool.Cache.DelLotteryRecCh(cid)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetLotteryRecChList DelLotteryRecCh failed req:%+v cid:%d err:%v", req, cid, err)
			}
		}

	}

	resp.NextPage = req.GetPage() + 1
	if uint32(len(recChList)) < req.GetPageSize() {
		resp.NextPage = 0
	}

	log.DebugWithCtx(ctx, "GetRecLotteryChList end req:%v resp:%v", req, resp)
	return resp, nil
}

func (m *ChannelRecommendManager) GetLotteryChannelRecInfo(ctx context.Context, in *pb.GetLotteryChannelRecInfoReq) (*pb.GetLotteryChannelRecInfoResp, error) {
	out := &pb.GetLotteryChannelRecInfoResp{}

	if m.clientPool.BusinessConfMgr.GetLotteryRecConf().IsRec == 0 {
		log.DebugWithCtx(ctx, "GetLotteryChannelRecInfo is not open rec req:%v", in)
		return out, nil
	}

	sv, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetLotteryChannelRecInfo ServiceInfoFromContext get fail in:%v", in)
		return out, protocol.NewServerError(status.ErrRequestParamInvalid)
	}

	// 版本控制
	if m.clientPool.BusinessConfMgr.GetLotteryRecConf().MinVersion != "" &&
		protocol.ClientVersion(sv.ClientVersion).String() < m.clientPool.BusinessConfMgr.GetLotteryRecConf().MinVersion {
		log.DebugWithCtx(ctx, "GetLotteryChannelRecInfo is min version limit req:%v sv:%v min:%s", in, sv, m.clientPool.BusinessConfMgr.GetLotteryRecConf().MinVersion)
		return out, nil
	}

	isRec, err := m.clientPool.Cache.CheckLotteryChIsRec(in.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLotteryChannelRecInfo CheckLotteryChIsRec failed in:%v err:%v", in, err)
		return out, err
	}

	out.IsRec = isRec
	log.DebugWithCtx(ctx, "GetLotteryChannelRecInfo end in:%v out:%v", in, out)
	return out, nil
}

func (m *ChannelRecommendManager) TriggerTimer(ctx context.Context, req *pb.TriggerTimerReq) (*pb.TriggerTimerResp, error) {
	resp := &pb.TriggerTimerResp{}

	switch req.GetTimerType() {
	case pb.TriggerTimerReq_Timer_Type_AutoGenPgcPrepareLevel:
		go m.AutoGenPgcPrepareLevel()
	case pb.TriggerTimerReq_Timer_Type_LoadPgcPrepareLevel:
		go m.LoadPgcPrepareLevel()
	default:
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	log.DebugWithCtx(ctx, "TriggerTimer end in:%v resp:%v", req, resp)
	return resp, nil
}

func (m *ChannelRecommendManager) HandleChannelBigGift(ctx context.Context, cid, giftId, sendTime uint32) error {

	// 获取大礼物信息
	giftInfo := m.PresentMemCache.GetConfigById(giftId)
	if isEmperorGift(giftId) {
		emperor := m.EmperorMemCache.GetConfigById(giftId)
		giftInfo = &presentPb.StPresentItemConfig{
			Price:     emperor.GetPresentsTotalPrice(),
			PriceType: uint32(presentPb.PresentPriceType_PRESENT_PRICE_TBEAN),
		}
	}
	giftLimit := m.dynamicCfg.Get().BigGiftPriceLimit

	// t豆价值足够才记录
	if (!isEmperorGift(giftId) && giftInfo.GetPriceType() != uint32(presentPb.PresentPriceType_PRESENT_PRICE_TBEAN)) || giftInfo.GetPrice() < giftLimit {
		return nil
	}

	err := m.clientPool.Cache.SetChannelGiftInfo(cid, giftId, sendTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleChannelBigGift SetChannelGiftInfo failed cid:%d giftId:%d err:%v", cid, giftId, err)
		return err
	}

	log.DebugWithCtx(ctx, "HandleChannelBigGift end cid:%d giftId:%d", cid, giftId)
	return nil
}

func (m *ChannelRecommendManager) BatchGetChannelBigGiftInfo(ctx context.Context, req *pb.BatchGetChannelBigGiftInfoReq) (*pb.BatchGetChannelBigGiftInfoResp, error) {
	resp := &pb.BatchGetChannelBigGiftInfoResp{}

	giftIdMap, err := m.clientPool.Cache.BatchGetChannelGiftInfo(req.GetCidList(), m.dynamicCfg.Get().BigGiftTimeDur)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelGiftInfo GetChannelGiftInfo failed req:%v err:%v", req, err)
		return resp, err
	}

	highValueMap := make(map[uint32]uint32)
	for cid, item := range giftIdMap {
		// 先随机打乱item
		rand.Shuffle(len(item), func(i, j int) {
			item[i], item[j] = item[j], item[i]
		})

		for _, giftId := range item {
			price := m.PresentMemCache.GetConfigById(giftId).GetPrice()
			if isEmperorGift(giftId) {
				price = m.EmperorMemCache.GetConfigById(giftId).GetPresentsTotalPrice()
			}

			highPrice := m.PresentMemCache.GetConfigById(highValueMap[cid]).GetPrice()
			if isEmperorGift(highValueMap[cid]) {
				highPrice = m.EmperorMemCache.GetConfigById(highValueMap[cid]).GetPresentsTotalPrice()
			}

			if price > highPrice {
				highValueMap[cid] = giftId
			}
		}

	}

	resp.MapCidGift = make(map[uint32]*pb.BigGiftInfo)
	for cid, giftId := range highValueMap {
		giftName := m.PresentMemCache.GetConfigById(giftId).GetName()
		if isEmperorGift(giftId) {
			giftName = m.EmperorMemCache.GetConfigById(giftId).GetSetName()
		}

		giftIcon := m.PresentMemCache.GetConfigById(giftId).GetIconUrl()
		if isEmperorGift(giftId) {
			giftIcon = m.EmperorMemCache.GetConfigById(giftId).GetIconUrl()
		}

		resp.MapCidGift[cid] = &pb.BigGiftInfo{
			GiftId:   giftId,
			GiftName: giftName,
			GiftIcon: giftIcon,
		}
	}

	log.DebugWithCtx(ctx, "GetChannelGiftInfo end req:%v resp:%v", req, resp)
	return resp, nil
}

func isEmperorGift(giftId uint32) bool {
	if giftId >= 100000 {
		return true
	}
	return false
}

func (m *ChannelRecommendManager) SetRevenueSwitchHub(ctx context.Context, req *pb.SetRevenueSwitchHubReq) (*pb.SetRevenueSwitchHubResp, error) {
	resp := &pb.SetRevenueSwitchHubResp{}
	err := m.clientPool.Cache.SetRevenueSwitchHub(req.GetUid(), req.GetSwitchType(), req.GetIsOpen())
	if err != nil {
		log.ErrorWithCtx(ctx, "SetRevenueSwitchHub SetRevenueSwitchHub failed uid:%d SwitchType:%d IsOpen:%v err:%v", req.GetUid(), req.GetSwitchType(), req.GetIsOpen(), err)
		return nil, err
	}
	log.DebugWithCtx(ctx, "SetRevenueSwitchHub end req:%v resp:%v", req, resp)
	return resp, nil
}

func (m *ChannelRecommendManager) GetRevenueSwitchHub(ctx context.Context, req *pb.GetRevenueSwitchHubReq) (*pb.GetRevenueSwitchHubResp, error) {
	resp := &pb.GetRevenueSwitchHubResp{}
	resp.IsOpenMap = make(map[uint32]bool)
	for _, switchType := range m.switchTypeList {
		resp.IsOpenMap[switchType] = true
	}
	switchTypeList, err := m.clientPool.Cache.GetRevenueSwitchHub(req.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRevenueSwitchHub GetRevenueSwitchHub failed Uid:%d err:%v", req.GetUid(), err)
		return nil, err
	}
	log.DebugWithCtx(ctx, "GetRevenueSwitchHub end req:%v switchTypeList:%v", req, switchTypeList)
	for _, switchType := range switchTypeList {
		log.DebugWithCtx(ctx, "GetRevenueSwitchHub end req:%v switchType:%v", req, switchType)
		resp.IsOpenMap[switchType] = false
	}
	log.DebugWithCtx(ctx, "GetRevenueSwitchHub end req:%v resp:%v", req, resp)
	return resp, nil
}

func (m *ChannelRecommendManager) BatchGetRevenueSwitchHub(ctx context.Context, req *pb.BatchGetRevenueSwitchHubReq) (*pb.BatchGetRevenueSwitchHubResp, error) {
	resp := &pb.BatchGetRevenueSwitchHubResp{}
	for _, uid := range req.GetUid() {
		resp.IsOpenMap[uid] = true
	}
	uidList, err := m.clientPool.Cache.BatchGetRevenueSwitchHub(req.GetUid(), req.GetSwitchType())
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetRevenueSwitchHub BatchGetRevenueSwitchHub failed Uid:%d SwitchType:%d err:%v", req.GetUid(), req.GetSwitchType(), err)
		return nil, err
	}
	resp.IsOpenMap = make(map[uint32]bool)
	for _, uid := range uidList {
		resp.IsOpenMap[uid] = false
	}
	log.DebugWithCtx(ctx, "BatchGetRevenueSwitchHub end req:%v resp:%v", req, resp)
	return resp, nil
}

func (m *ChannelRecommendManager) GetRecFeedbackConfig(ctx context.Context, req *pb.GetRecFeedbackConfigReq) (*pb.GetRecFeedbackConfigResp, error) {
	resp := &pb.GetRecFeedbackConfigResp{}

	for _, reason := range m.dynamicCfg.GetSvrDyConf().FeedbackConf.NegativeReasonList {
		resp.ReasonList = append(resp.ReasonList, reason)
	}

	log.DebugWithCtx(ctx, "GetRecFeedbackConfig end req:%v resp:%v", req, resp)
	return resp, nil
}

func (m *ChannelRecommendManager) DoRecFeedback(ctx context.Context, req *pb.DoRecFeedbackReq) (*pb.DoRecFeedbackResp, error) {
	resp := &pb.DoRecFeedbackResp{}

	nowTs := time.Now().Unix()
	err := m.clientPool.Cache.AddUserFeedBack(ctx, req.GetUid(), req.GetChannelId(), uint32(nowTs), m.dynamicCfg.GetSvrDyConf().FeedbackConf.NegativeEffectTs)
	if err != nil {
		log.ErrorWithCtx(ctx, "DoRecFeedback AddUserFeedBack failed req:%v err:%v", req, err)
		return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
	}

	log.InfoWithCtx(ctx, "DoRecFeedback end req:%v resp:%v", req, resp)
	return resp, nil
}

// 过滤用户负反馈房间
func (m *ChannelRecommendManager) FilterUserNegativeFeedbackChannel(ctx context.Context, uid uint32, channelList []*pb.ChannelRecommendSimpleInfo) []*pb.ChannelRecommendSimpleInfo {
	tmpChannelList := make([]*pb.ChannelRecommendSimpleInfo, 0)

	mapCid2Ts, err := m.clientPool.Cache.GetUserFeedBackList(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "FilterUserNegativeFeedbackChannel GetUserFeedBackList failed uid:%d err:%v", uid, err)
		return channelList
	}

	nowTs := time.Now().Unix()
	mapCid2Is := make(map[uint32]bool)
	for cid, ts := range mapCid2Ts {
		if ts+m.dynamicCfg.GetSvrDyConf().FeedbackConf.NegativeEffectTs >= uint32(nowTs) {
			mapCid2Is[cid] = true
		}
	}

	for _, channel := range channelList {
		if !mapCid2Is[channel.GetChannelId()] {
			tmpChannelList = append(tmpChannelList, channel)
		}
	}

	log.DebugWithCtx(ctx, "FilterUserNegativeFeedbackChannel end uid:%d mapCid2Is:%v channelList:%v tmpChannelList:%v", uid, mapCid2Is, channelList, tmpChannelList)
	return tmpChannelList
}
