package server

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/clients/account"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app"
	pb "golang.52tt.com/protocol/app/channel-recommend-logic"
	"golang.52tt.com/protocol/app/esport_logic"
	"golang.52tt.com/protocol/common/status"
	svrPb "golang.52tt.com/protocol/services/channel-recommend-svr"
	channel "golang.52tt.com/protocol/services/channelsvr"
	esport_skill "golang.52tt.com/protocol/services/esport-skill"
	"golang.52tt.com/protocol/services/esport_hall"
	"golang.52tt.com/protocol/services/esport_internal"
	iopPb "golang.52tt.com/protocol/services/iop-top-overlay"
	revenueRecPb "golang.52tt.com/protocol/services/revenue-recommend-svr"
	"golang.52tt.com/services/channel-recommend/channel-recommend-logic/internal/rpc"
	"golang.52tt.com/services/tt-rev/common/goroutineex"
	"golang.52tt.com/services/tt-rev/esport/common"
	"golang.org/x/sync/errgroup"
	"text/template"
	"time"
)

const (
	SexUrlMale   = "https://obs-cdn.52tt.com/tt/fe-moss/tgame/20240927101114_30735137.png"
	SexUrlFemale = "https://obs-cdn.52tt.com/tt/fe-moss/tgame/20240927101329_74599394.png"
)

// GetGlobalTopOverLay 通用顶部浮层
func (s *ChannelRecommendLogicImpl) GetGlobalTopOverLay(ctx context.Context, req *pb.GetGlobalTopOverLayRequest) (*pb.GetGlobalTopOverLayResponse, error) {
	resp := &pb.GetGlobalTopOverLayResponse{}
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetGlobalTopOverLay ServiceInfoFromContext fail. req:%+v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	subCtx, cancel := protogrpc.NewContextWithInfoTimeout(ctx, time.Second*1)
	defer cancel()

	uid := serviceInfo.UserID
	log.InfoWithCtx(ctx, "GetGlobalTopOverLay req uid:%d", uid)
	switchRsp, sErr := s.channelRecCli.GetRevenueSwitchHub(subCtx, &svrPb.GetRevenueSwitchHubReq{
		Uid: uid,
	})
	if sErr != nil {
		log.ErrorWithCtx(ctx, "GetGlobalTopOverLay GetRevenueSwitchHub failed req:%v err:%v", uid, sErr)
		return resp, sErr
	}
	if !switchRsp.GetIsOpenMap()[uint32(pb.RevenueSwitchHubType_REVENUE_SWITCH_HUB_TYPE_SNACKBAR)] {
		log.ErrorWithCtx(ctx, "GetGlobalTopOverLay GetRevenueSwitchHub not open,uid:%d", uid)
		return resp, protocol.NewExactServerError(nil, status.ErrExternalSystemFailed)
	}

	dycf := s.topOverlayDyConf.Get()
	overLayRsp, err := s.iopCli.TopOverSiteStrategy(subCtx, &iopPb.GetGlobalOverLayReq{
		Uid:     uid,
		AppName: dycf.AppName,
		AppType: dycf.GetAppTypes(serviceInfo.MarketID),
		SiteId:  dycf.SiteId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGlobalTopOverLay TopOverSiteStrategy failed req:%v err:%v", uid, err)
		return resp, protocol.NewExactServerError(nil, status.ErrExternalSystemFailed)
	}
	if overLayRsp.ReportData == nil {
		overLayRsp.ReportData = make(map[string]string)
	}
	switch overLayRsp.Type {
	case uint32(iopPb.ETopOverLayType_E_TOP_OVER_LAY_TYPE_CHANNEL):
		err = s.fillTopOverlayChannel(ctx, uid, overLayRsp, resp)
	case uint32(iopPb.ETopOverLayType_E_TOP_OVER_LAY_TYPE_USER):
		err = s.fillTopOverlayUser(ctx, uid, overLayRsp.Data, resp)
	case uint32(iopPb.ETopOverLayType_E_TOP_OVER_LAY_TYPE_ESPORT_GOD):
		err = s.fillTopOverlayCoach(ctx, uid, overLayRsp, resp)
	default:
		log.ErrorWithCtx(ctx, "GetGlobalTopOverLay TopOverSiteStrategy type invalid,uid:%d, overLayRsp:%+v", uid, overLayRsp)
		return resp, nil
	}

	if err != nil {
		log.ErrorWithCtx(ctx, "GetGlobalTopOverLay fill failed uid:%v, type:%d, err:%v", uid, overLayRsp.Type, err)
		return resp, err
	}

	reportData, err := json.Marshal(overLayRsp.ReportData)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGlobalTopOverLay Marshal failed,uid:%d, err:%v", uid, err)
		resp.ReportJsonData = "{}"
	} else {
		resp.ReportJsonData = string(reportData)
	}
	log.InfoWithCtx(ctx, "GetGlobalTopOverLay success,uid:%d, type:%d", uid, resp.Type)
	return resp, nil
}

type ShowTopOverlayChannel struct {
	UserInfo    *app.UserProfile
	IopInfo     *iopPb.TopOverChannelData
	ChannelType uint32
	ChannelName string
	ChannelIcon string
	BgUrl       string
}

func (s *ChannelRecommendLogicImpl) fillTopOverlayChannel(ctx context.Context, uid uint32, iopRsp *iopPb.GetGlobalOverLayResp, out *pb.GetGlobalTopOverLayResponse) error {
	dataPb := &iopPb.TopOverChannelData{}
	if err := proto.Unmarshal(iopRsp.Data, dataPb); err != nil {
		log.ErrorWithCtx(ctx, "fillTopOverlayChannel Unmarshal failed,uid:%d, err:%v", uid, err)
		return protocol.NewExactServerError(nil, status.ErrChannelRecommendTopOverlayErr)
	}
	if dataPb.Cid == 0 || dataPb.FollowUid == 0 {
		log.ErrorWithCtx(ctx, "fillTopOverlayUser channelId invalid,uid:%d, dataPb:%+v", uid, dataPb)
		return nil
	}
	var channelInfo *channel.ChannelSimpleInfo
	var userInfo *app.UserProfile
	g, tmpCtx := errgroup.WithContext(ctx)
	g.Go(func() error {
		var err error
		channelInfo, err = s.channelCli.GetChannelSimpleInfo(tmpCtx, uid, dataPb.Cid)
		return err
	})
	g.Go(func() error {
		var err error
		userInfo, err = s.getUserInfo(tmpCtx, dataPb.FollowUid)
		if err != nil {
			log.ErrorWithCtx(tmpCtx, "fillTopOverlayChannel getUserInfo failed,uid:%d, dataPb:%+v, err:%v", uid, dataPb, err)
			return err
		}
		return nil
	})
	if err := g.Wait(); err != nil {
		log.ErrorWithCtx(tmpCtx, "fillTopOverlayChannel failed uid:%v data:%s, err:%v", uid, dataPb.String(), err)
		return nil
	}
	dycf := s.topOverlayDyConf.Get()
	showData := &ShowTopOverlayChannel{
		UserInfo:    userInfo,
		IopInfo:     dataPb,
		ChannelType: *channelInfo.ChannelType,
		ChannelName: *channelInfo.Name,
		ChannelIcon: *channelInfo.IconMd5,
		BgUrl:       dycf.GetChannelBgUrl(dataPb.TagId),
	}

	tpl := s.topOverlayTplMgr.ChannelTpl.GetTpl()
	var itemBuf bytes.Buffer
	iItemTpl := template.Must(template.New("item").Parse(tpl))
	err := iItemTpl.Execute(&itemBuf, showData)
	if err != nil {
		log.ErrorWithCtx(ctx, "fillTopOverlayUser render execute failed err:%v", err)
		return protocol.NewExactServerError(nil, status.ErrChannelRecommendTopOverlayErr)
	}
	out.Type = uint32(pb.ETopOverLayType_E_TOP_OVER_LAY_TYPE_CHANNEL)
	out.UiXml = itemBuf.String()
	out.FollowedUserInfo = &pb.UserBaseInfo{
		Uid:      userInfo.Uid,
		NickName: userInfo.Nickname,
		HeadImg:  userInfo.HeadImgMd5,
		Sex:      int32(userInfo.Sex),
		Account:  userInfo.Account,
	}
	iopRsp.ReportData["label"] = "优质"

	_, err = rpc.RevenueRecCli.AddTopOverlayFollowInfo(ctx, &revenueRecPb.AddTopOverlayFollowInfoReq{
		Uid:         uid,
		Cid:         dataPb.Cid,
		FollowUid:   dataPb.FollowUid,
		FollowerTxt: dataPb.InTxt,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "fillTopOverlayChannel AddTopOverlayFollowInfo failed,uid:%d, dataPb:%+v, err:%v", uid, dataPb, err)
	}

	return nil
}

type ShowTopOverlayUser struct {
	UserInfo    *app.UserProfile
	IopInfo     *iopPb.TopOverChannelUserData
	ChannelType uint32
	BgUrl       string
	SexUrl      string
}

func (s *ChannelRecommendLogicImpl) fillTopOverlayUser(ctx context.Context, uid uint32, data []byte, out *pb.GetGlobalTopOverLayResponse) error {
	dataPb := &iopPb.TopOverChannelUserData{}
	if err := proto.Unmarshal(data, dataPb); err != nil {
		log.ErrorWithCtx(ctx, "fillTopOverlayUser Unmarshal failed,uid:%d, err:%v", uid, err)
		return protocol.NewExactServerError(nil, status.ErrChannelRecommendTopOverlayErr)
	}
	if dataPb.Cid == 0 || dataPb.Uid == 0 {
		log.ErrorWithCtx(ctx, "fillTopOverlayUser channelId invalid,uid:%d, dataPb:%+v", uid, dataPb)
		return nil
	}
	var channelInfo *channel.ChannelSimpleInfo
	var userInfo *app.UserProfile
	g, tmpCtx := errgroup.WithContext(ctx)
	g.Go(func() error {
		var err error
		channelInfo, err = s.channelCli.GetChannelSimpleInfo(tmpCtx, uid, dataPb.Cid)
		return err
	})
	g.Go(func() error {
		var err error
		userInfo, err = s.getUserInfo(tmpCtx, dataPb.Uid)
		if err != nil {
			log.ErrorWithCtx(tmpCtx, "fillTopOverlayUser getUserInfo failed,uid:%d, dataPb:%+v, err:%v", uid, dataPb, err)
			return err
		}
		return nil
	})

	if err := g.Wait(); err != nil {
		log.ErrorWithCtx(tmpCtx, "fillTopOverlayUser failed uid:%v data:%s, err:%v", uid, dataPb.String(), err)
		return nil
	}
	dycf := s.topOverlayDyConf.Get()
	showData := &ShowTopOverlayUser{
		UserInfo:    userInfo,
		IopInfo:     dataPb,
		ChannelType: *channelInfo.ChannelType,
		BgUrl:       dycf.GetChannelBgUrl(dataPb.TagId),
		SexUrl:      SexUrlFemale,
	}
	if userInfo.Sex == uint32(account.Male) {
		showData.SexUrl = SexUrlMale
	}
	tpl := s.topOverlayTplMgr.UserTpl.GetTpl()

	var itemBuf bytes.Buffer
	iItemTpl := template.Must(template.New("item").Parse(tpl))
	err := iItemTpl.Execute(&itemBuf, showData)
	if err != nil {
		log.ErrorWithCtx(ctx, "fillTopOverlayUser render execute failed err:%v", err)
		return protocol.NewExactServerError(nil, status.ErrChannelRecommendTopOverlayErr)
	}
	out.Type = uint32(pb.ETopOverLayType_E_TOP_OVER_LAY_TYPE_USER)
	out.UiXml = itemBuf.String()
	out.FollowedUserInfo = &pb.UserBaseInfo{
		Uid:      userInfo.Uid,
		NickName: userInfo.Nickname,
		HeadImg:  userInfo.HeadImgMd5,
		Sex:      int32(userInfo.Sex),
		Account:  userInfo.Account,
	}

	// 添加房间跟随信息
	_, err = rpc.RevenueRecCli.AddTopOverlayFollowInfo(ctx, &revenueRecPb.AddTopOverlayFollowInfoReq{
		Uid:         uid,
		Cid:         dataPb.Cid,
		FollowUid:   dataPb.Uid,
		FollowerTxt: dataPb.InTxt,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "fillTopOverlayUser AddTopOverlayFollowInfo failed,uid:%d, dataPb:%+v, err:%v", uid, dataPb, err)
	}
	return nil
}

type ShowTopOverlayCoach struct {
	UserInfo        *app.UserProfile
	IopInfo         *iopPb.GlobalRcmdCoachData
	SexUrl          string
	TextDesc        string // 大神自我描述
	Price           uint32 // 大神价格
	PriceUnit       string // 价格单位
	DiscountIconUrl string // 优惠icon
	LabelXmlStr     string // 标签xml
}

const (
	DiscountIconUrlFirstRound  = "https://obs-cdn.52tt.com/tt/fe-moss/tgame/20240925102616_65790594.png"
	DiscountIconUrlCoupon      = "https://obs-cdn.52tt.com/tt/fe-moss/tgame/20240924162303_86031270.png"
	DiscountIconUrlNewCustomer = "https://obs-cdn.52tt.com/tt/fe-moss/tdesign/esports/20241111103814_82554379.png"
)

func (s *ChannelRecommendLogicImpl) fillTopOverlayCoach(ctx context.Context, uid uint32, iopRsp *iopPb.GetGlobalOverLayResp, out *pb.GetGlobalTopOverLayResponse) error {
	dataPb := &iopPb.GlobalRcmdCoachData{}
	if err := proto.Unmarshal(iopRsp.Data, dataPb); err != nil {
		log.ErrorWithCtx(ctx, "fillTopOverlayCoach Unmarshal err: %v", err)
		return err
	}
	if dataPb.CoachUid == 0 || dataPb.GameId == 0 {
		log.ErrorWithCtx(ctx, "fillTopOverlayCoach data invalid, dataPb: %+v", dataPb)
		return nil
	}
	log.DebugWithCtx(ctx, "fillTopOverlayCoach, dataPb: %+v", dataPb)

	var (
		esportSwitch     uint32
		userInfo         *app.UserProfile
		sexUrl           string
		textDesc         string
		price            uint32
		priceUnit        string
		discountIconUrl  string
		discountLabel    string
		guaranteeWinText string
		labelList        []*esport_skill.LabelInfo
	)

	g, tmpCtx := errgroup.WithContext(ctx)
	// 查电竞总开关
	g.Go(func() error {
		switchResp, err1 := rpc.ESportSkillCli.GetSwitch(ctx, &esport_skill.GetSwitchRequest{})
		if err1 != nil {
			log.ErrorWithCtx(tmpCtx, "fillTopOverlayCoach GetSwitch err: %v", err1)
			return nil // 不碍事
		}
		esportSwitch = uint32(switchResp.GetSwitchStatus().GetMainSwitchStatus())
		return nil
	})
	// 查大神账号信息
	g.Go(func() error {
		var err error
		userInfo, err = s.getUserInfo(tmpCtx, dataPb.CoachUid)
		if err != nil {
			log.ErrorWithCtx(tmpCtx, "fillTopOverlayCoach getUserInfo err: %v", err)
			return err
		}

		sexUrl = SexUrlFemale
		if userInfo.Sex == uint32(account.Male) {
			sexUrl = SexUrlMale
		}

		return nil
	})
	// 查大神自我描述
	g.Go(func() error {
		skillRsp, err1 := rpc.ESportSkillCli.GetUserSkillByGameId(tmpCtx, &esport_skill.GetUserSkillByGameIdRequest{
			Uid:    dataPb.CoachUid,
			GameId: dataPb.GameId,
			IsHost: false,
		})
		if err1 != nil {
			log.ErrorWithCtx(tmpCtx, "fillTopOverlayCoach GetUserSkillByGameId err: %v", err1)
			return err1
		}

		textDesc = skillRsp.GetCurrentSkill().GetTextDesc()
		return nil
	})
	// 查价格信息
	g.Go(func() error {
		productRsp, err1 := rpc.ESportHallCli.GetSkillProductByUidGameId(tmpCtx, &esport_hall.GetSkillProductByUidGameIdRequest{
			Uid:    dataPb.CoachUid,
			GameId: dataPb.GameId,
		})
		if err1 != nil {
			log.ErrorWithCtx(tmpCtx, "fillTopOverlayCoach GetSkillProductByUidGameId err: %v", err1)
			return err1
		}

		pricingResp, err1 := rpc.ESportInternalCli.BatchGetSkillPricingInfo(tmpCtx, &esport_internal.BatchGetSkillPricingInfoRequest{
			Uid:         uid,
			CoachUid:    dataPb.GetCoachUid(),
			GameId:      []uint32{dataPb.GetGameId()},
			BuyAmount:   1,
			QueryOption: &esport_internal.CouponQueryOption{},
		})
		if err1 != nil {
			log.ErrorWithCtx(tmpCtx, "fillTopOverlayCoach BatchGetSkillPricingInfo err: %v", err1)
			return err1
		}

		priceInfo, ok := pricingResp.GetPriceMap()[dataPb.GetGameId()]
		if !ok {
			log.ErrorWithCtx(tmpCtx, "fillTopOverlayCoach BatchGetSkillPricingInfo no price info")
			return nil
		}
		price = priceInfo.GetPrice()
		priceUnit = priceInfo.GetUnit()
		if len(priceInfo.GetDiscountList()) > 0 {
			// 因为目前业务只允许一个优惠，所以直接取第一个
			// 判断用户使用的客户端版本号，如果不是符合新客价的版本，并且有新客价优惠，则使用顺位第二的优惠
			discountInfo := priceInfo.GetDiscountList()[0]
			price = discountInfo.GetPrice()
			discountLabel = discountInfo.GetDesc()
			switch discountInfo.GetType() {
			case esport_internal.DiscountType_DISCOUNT_TYPE_FIRST_ORDER:
				discountIconUrl = DiscountIconUrlFirstRound
				discountLabel = "首局"
			case esport_internal.DiscountType_DISCOUNT_TYPE_COUPON:
				discountIconUrl = DiscountIconUrlCoupon
				discountLabel = "券后价"
			case esport_internal.DiscountType_DISCOUNT_TYPE_NEW_CUSTOMER:
				discountIconUrl = DiscountIconUrlNewCustomer
				discountLabel = "新客价"
			}
		}

		guaranteeWinText = common.GetGranteeWinText(productRsp.GetSkillProduct().GetIsGuaranteeWin(), productRsp.GetSkillProduct().GetGuaranteeWinTexts())

		return nil
	})
	// 查认证标识
	g.Go(func() error {
		labelRsp, err1 := rpc.ESportSkillCli.BatchGetCoachLabelsForGame(tmpCtx, &esport_skill.BatchGetCoachLabelsForGameRequest{
			GameId:   dataPb.GameId,
			CoachIds: []uint32{dataPb.CoachUid},
		})
		if err1 != nil {
			log.ErrorWithCtx(tmpCtx, "fillTopOverlayCoach BatchGetCoachLabelsForGame err: %v", err1)
			return err1
		}

		for _, item := range labelRsp.GetLabelList() {
			// 过滤掉没配图的标识，免得自找麻烦，虽然线上不太可能
			for _, label := range item.GetLabelMap()[uint32(esport_skill.LabelType_LABEL_TYPE_COACH)].GetLabelList() {
				if len(label.GetLabelImage()) > 0 {
					labelList = append(labelList, label)
				}
			}
			for _, label := range item.GetLabelMap()[uint32(esport_skill.LabelType_LABEL_TYPE_SKILL)].GetLabelList() {
				if len(label.GetLabelImage()) > 0 {
					labelList = append(labelList, label)
				}
			}
		}

		return nil
	})
	if err := g.Wait(); err != nil {
		return err
	}

	if esportSwitch == uint32(esport_skill.EsportSwitchStatus_SWITCH_STATUS_OFF) {
		log.DebugWithCtx(ctx, "fillTopOverlayCoach esportSwitch close, dataPb: %+v", dataPb)
		return nil
	}

	labelXml, labelReport := genLabelString(guaranteeWinText, labelList)
	showData := &ShowTopOverlayCoach{
		UserInfo:        userInfo,
		IopInfo:         dataPb,
		SexUrl:          sexUrl,
		TextDesc:        textDesc,
		Price:           price,
		PriceUnit:       priceUnit,
		DiscountIconUrl: discountIconUrl,
		LabelXmlStr:     labelXml,
	}

	// 构建模板
	var itemBuf bytes.Buffer
	iItemTpl := template.Must(template.New("item").Parse(s.topOverlayTplMgr.CoachTpl.GetTpl()))
	err := iItemTpl.Execute(&itemBuf, showData)
	if err != nil {
		log.ErrorWithCtx(ctx, "fillTopOverlayCoach render execute failed err: %v", err)
		return err
	}

	// 组装结果返回
	out.Type = uint32(pb.ETopOverLayType_E_TOP_OVER_LAY_TYPE_ESPORT_GOD)
	out.UiXml = itemBuf.String()

	// 补充埋点数据
	iopRsp.ReportData["content"] = textDesc
	iopRsp.ReportData["price"] = fmt.Sprintf("%d%s", price, priceUnit)
	iopRsp.ReportData["discount_label"] = discountLabel
	iopRsp.ReportData["label"] = labelReport

	// 异步上报已曝光
	goroutineex.GoroutineWithTimeoutCtx(ctx, 5*time.Second, func(ctx context.Context) {
		_, err1 := rpc.ESportHallCli.ReportExposeCoach(ctx, &esport_hall.ReportExposeCoachRequest{
			Uid:             uid,
			ExposeCoachList: []uint32{dataPb.CoachUid},
			GameId:          dataPb.GameId,
			ExposeType:      uint32(esport_logic.ReportExposeCoachRequest_EXPOSE_COACH_TYPE_ESPORT_GLOBAL_POPUP),
		})
		if err1 != nil {
			log.ErrorWithCtx(ctx, "fillTopOverlayCoach ReportExposeCoach err: %v", err1)
		}
	})

	return nil
}

func (s *ChannelRecommendLogicImpl) getUserInfo(ctx context.Context, uid uint32) (*app.UserProfile, error) {
	var err error
	userInfo, err := rpc.UserProfileCli.GetUserProfileV2(ctx, uid, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "%d getUserInfo GetUserProfileV2 err: %v", uid, err)
		return nil, err
	}

	headMd5, err := s.headImgCli.GetHeadImageMd5(ctx, uid, userInfo.Account)
	if err != nil {
		log.ErrorWithCtx(ctx, "%d getUserInfo GetHeadImageMd5 err: %v", uid, err)
		return nil, err
	}
	userInfo.HeadImgMd5 = headMd5

	dyHeadMap, err := s.headDyImgCli.GetHeadDynamicImageMd5(ctx, []uint32{uid})
	if err != nil {
		log.ErrorWithCtx(ctx, "%d getUserInfo GetHeadDynamicImageMd5 uidList: %+v, err: %v", uid, err)
		return nil, err
	}
	if dyHeadMap != nil && len(dyHeadMap[uid]) > 0 {
		userInfo.HeadImgMd5 = dyHeadMap[uid]
	}

	return userInfo, nil
}

func genLabelString(guaranteeWinText string, labelList []*esport_skill.LabelInfo) (string, string) {
	var labelXml, labelReport string
	if len(guaranteeWinText) > 0 && len(labelList) == 0 { // 只有1个包赢
		labelXml = fmt.Sprintf(xmlOnlyOneGuarantee, guaranteeWinText)
		labelReport = fmt.Sprintf("包赢-%s", guaranteeWinText)
	} else if len(guaranteeWinText) == 0 && len(labelList) == 1 { // 只有1个认证
		labelXml = fmt.Sprintf(xmlOnlyOneLabel, labelList[0].GetLabelImage())
		labelReport = labelList[0].GetLabelName()
	} else if len(guaranteeWinText) > 0 && len(labelList) > 0 { // 包赢+认证
		labelXml = fmt.Sprintf(xmlGuaranteeAndLabel, guaranteeWinText, labelList[0].GetLabelImage())
		labelReport = fmt.Sprintf("包赢-%s,%s", guaranteeWinText, labelList[0].GetLabelName())
	} else if len(guaranteeWinText) == 0 && len(labelList) >= 2 { // 2个认证
		labelXml = fmt.Sprintf(xmlTwoLabel, labelList[0].GetLabelImage(), labelList[1].GetLabelImage())
		labelReport = fmt.Sprintf("%s,%s", labelList[0].GetLabelName(), labelList[1].GetLabelName())
	} else {
		// nothing
	}
	return labelXml, labelReport
}

const (
	xmlOnlyOneGuarantee = `
<ttdiv height=15 flex-direction=row justify-content=center>
    <ttdiv height=15 padding-left=3 padding-right=3>
    <ttdiv height=15 background-color=#FFEBDC flex-direction=row corner-radius=4>
        <ttimage width=32 height=15 margin-left=0 url="https://obs-cdn.52tt.com/tt/fe-moss/tgame/20240924170157_52792588.png"></ttimage>
        <tttext padding-left=3 padding-right=3 font-size=10 font-color=#E46F12>%s</tttext>
    </ttdiv>
    </ttdiv>
</ttdiv>
`
	xmlOnlyOneLabel = `
<ttdiv height=15 flex-direction=row justify-content=center>
    <ttdiv height=15 padding-left=3 padding-right=3>
    <ttimage height=15 width=59 url="%s"></ttimage>
    </ttdiv>
</ttdiv>
`
	xmlGuaranteeAndLabel = `
<ttdiv height=15 flex-direction=row justify-content=center>
    <ttdiv height=15 padding-left=3 padding-right=3>
    <ttdiv height=15 background-color=#FFEBDC flex-direction=row corner-radius=4>
        <ttimage width=32 height=15 margin-left=0 url="https://obs-cdn.52tt.com/tt/fe-moss/tgame/20240924170157_52792588.png"></ttimage>
        <tttext padding-left=3 padding-right=3 font-size=10 font-color=#E46F12>%s</tttext>
    </ttdiv>
    </ttdiv>

    <ttdiv height=15 padding-left=3 padding-right=3>
    <ttimage height=15 width=59 url="%s"></ttimage>
    </ttdiv>
</ttdiv>
`
	xmlTwoLabel = `
<ttdiv height=15 flex-direction=row justify-content=center>
    <ttdiv height=15 padding-left=3 padding-right=3>
    <ttimage height=15 width=59 url="%s"></ttimage>
    </ttdiv>
    <ttdiv height=15 padding-left=3 padding-right=3>
    <ttimage height=15 width=59 url="%s"></ttimage>
    </ttdiv>
</ttdiv>
`
)
