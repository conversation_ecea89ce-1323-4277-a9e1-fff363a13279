package server

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/clients/account-go"
	"golang.52tt.com/clients/channel"
	"golang.52tt.com/clients/channel-live-mgr"
	"golang.52tt.com/clients/channel-lottery"
	"golang.52tt.com/clients/channel-recommend-svr"
	"golang.52tt.com/clients/channel-stats"
	"golang.52tt.com/clients/channelmic"
	"golang.52tt.com/clients/channelol-stat-go"
	HeadDynamicImage "golang.52tt.com/clients/head-dynamic-image-logic"
	"golang.52tt.com/clients/headimage"
	"golang.52tt.com/clients/official-live-channel"
	"golang.52tt.com/clients/revenue-ext-game"
	"golang.52tt.com/pkg/coroutine"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	cAppPb "golang.52tt.com/protocol/app/channel"
	pb "golang.52tt.com/protocol/app/channel-recommend-logic"
	"golang.52tt.com/protocol/common/status"
	svrPb "golang.52tt.com/protocol/services/channel-recommend-svr"
	"golang.52tt.com/protocol/services/demo/echo"
	"golang.52tt.com/protocol/services/iop-top-overlay"
	revenueRecPb "golang.52tt.com/protocol/services/revenue-recommend-svr"
	"golang.52tt.com/services/channel-recommend/channel-recommend-logic/internal/conf"
	"golang.52tt.com/services/channel-recommend/channel-recommend-logic/internal/manager"
	"golang.52tt.com/services/channel-recommend/channel-recommend-logic/internal/rpc"
	"sync"
)

type StartConfig struct {
	// [optional] from startup arguments

	// from config file
	*conf.ServiceConfigT
}

type ChannelRecommendLogicImpl struct {
	sc                *conf.ServiceConfigT
	channelRecCli     channel_recommend_svr.IClient
	channelCli        channel.IClient
	channelMicCli     channelmic.IClient
	liveMgrCli        channellivemgr.IClient
	accountCli        account_go.IClient
	officialLiveCli   official_live_channel.IClient
	channelLotteryCli channel_lottery.IClient
	channelOlStatCli  channelol_stat_go.IClient
	channelStatCli    channelstats.IClient
	headImgCli        HeadImage.IClient
	headDyImgCli      HeadDynamicImage.IClient
	confManager       *conf.BusinessConfManager
	extGameCli        revenue_ext_game.IClient
	iopCli            *iop_top_overlay.Client
	topOverlayTplMgr  *conf.TopOverlayTpl
	topOverlayDyConf  conf.ITopOverlayDyConfig

	mapKey2ChannelList map[string][]*cAppPb.RecommendChannelInfo
	key2ChannelRwLock  sync.RWMutex

	//本地兜底缓存
	channelsMemCache       map[string][]*cAppPb.RecommendChannelInfo
	channelsMemCacheRwLock sync.RWMutex

	quickEntryConfigMgr     manager.IQuickEntryConfigMgr
	recommendChannelInfoMgr *manager.RecommendChannelInfoFillerManager
	extInfoMgr              *manager.ExtInfoMgr
}

func NewChannelRecommendLogicImpl(ctx context.Context, config *StartConfig) (*ChannelRecommendLogicImpl, error) {
	sc := config.ServiceConfigT

	log.Infof("NewChannelRecommendLogicImpl sc:%v", sc)
	if sc == nil {
		return nil, fmt.Errorf("no find conf")
	}

	confManager, err := conf.NewBusinessConfManager()
	if err != nil {
		log.Errorf("conf.NewBusinessConfManager() failed err:%v", err)
		return nil, err
	}

	iopCli := iop_top_overlay.MustNewClientTo(ctx, sc.IopTopOverlayAddr)

	topOverlayTpl, err := conf.NewTplConfig(ctx)
	if err != nil {
		log.Errorf("NewTplConfig failed err:%v", err)
		return nil, err
	}
	topOverlayDyConf, err := conf.NewDynamicTopOverlayConfig(ctx)
	if err != nil {
		log.Errorf("NewDynamicTopOverlayConfig failed err:%v", err)
		return nil, err
	}

	channelRecCli := channel_recommend_svr.NewIClient()
	channelCli := channel.NewIClient()
	channelMicCli := channelmic.NewClient()
	liveMgrCli, _ := channellivemgr.NewClient()
	accountCli, _ := account_go.NewClient()
	officialLiveCli, _ := official_live_channel.NewClient()
	channelLotteryCli, _ := channel_lottery.NewClient()
	channelOlStatCli, _ := channelol_stat_go.NewClient()
	channelStatCli, _ := channelstats.NewClient()
	headImgCli := HeadImage.NewClient()
	headDyImgCli := HeadDynamicImage.NewClient()
	revenueExtGameCli := revenue_ext_game.NewIClient()

	quickEntryConfMgr, err := manager.NewQuickEntryConfigMgr(ctx, channelRecCli)
	if err != nil {
		log.Errorf("NewQuickEntryConfigMgr failed err:%v", err)
		return nil, err
	}

	rpc.Setup()

	recommendChannelInfoMgr, err := manager.NewRecommendChannelInfoFillerManager()
	if err != nil {
		log.Errorf("NewRecommendChannelInfoFillerManager failed err:%v", err)
		return nil, err
	}

	extInfoMgr := manager.NewExtInfoMgr(confManager)

	s := &ChannelRecommendLogicImpl{
		sc:                sc,
		channelRecCli:     channelRecCli,
		channelCli:        channelCli,
		channelMicCli:     channelMicCli,
		liveMgrCli:        liveMgrCli,
		accountCli:        accountCli,
		officialLiveCli:   officialLiveCli,
		channelLotteryCli: channelLotteryCli,
		channelOlStatCli:  channelOlStatCli,
		channelStatCli:    channelStatCli,
		headImgCli:        headImgCli,
		headDyImgCli:      headDyImgCli,
		confManager:       confManager,
		extGameCli:        revenueExtGameCli,
		iopCli:            iopCli,
		topOverlayTplMgr:  topOverlayTpl,
		topOverlayDyConf:  topOverlayDyConf,

		mapKey2ChannelList:     make(map[string][]*cAppPb.RecommendChannelInfo, 0),
		key2ChannelRwLock:      sync.RWMutex{},
		channelsMemCache:       make(map[string][]*cAppPb.RecommendChannelInfo, 0),
		channelsMemCacheRwLock: sync.RWMutex{},

		quickEntryConfigMgr:     quickEntryConfMgr,
		recommendChannelInfoMgr: recommendChannelInfoMgr,
		extInfoMgr:              extInfoMgr,
	}

	return s, nil
}

func (s *ChannelRecommendLogicImpl) ShutDown() {
	coroutine.StopAll()
	// now no thing to do
}

func (s *ChannelRecommendLogicImpl) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	log.DebugWithCtx(ctx, "Echo req:[%+v]", req)
	return req, nil
}

func (s *ChannelRecommendLogicImpl) GetRecLotteryChList(ctx context.Context, req *pb.GetRecLotteryChListRequest) (*pb.GetRecLotteryChListResponse, error) {
	resp := &pb.GetRecLotteryChListResponse{}

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetRecLotteryChList ServiceInfoFromContext fail. req:%+v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	opUid := serviceInfo.UserID

	recResp, err := s.channelRecCli.GetRecLotteryChList(ctx, &svrPb.GetRecLotteryChListReq{
		Page:     req.GetPage(),
		PageSize: req.GetPageSize(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRecLotteryChList failed req:%v err:%v", req, err)
		return resp, err
	}

	cidList := make([]uint32, 0)
	for _, chInfo := range recResp.GetChList() {
		cidList = append(cidList, chInfo.GetChannelId())
	}

	mapId2ChInfo, err := s.channelCli.BatchGetChannelSimpleInfo(ctx, opUid, cidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRecLotteryChList BatchGetChannelSimpleInfo failed req:%v len:%d err:%v", req, len(cidList), err)
		return resp, err
	}

	for _, chInfo := range recResp.GetChList() {
		resp.ChList = append(resp.ChList, &pb.RecLotteryChInfo{
			ChannelId:     chInfo.GetChannelId(),
			Name:          mapId2ChInfo[chInfo.GetChannelId()].GetName(),
			IconMd5:       mapId2ChInfo[chInfo.GetChannelId()].GetIconMd5(),
			LotteryEndTs:  chInfo.GetLotteryEndTs(),
			GiftName:      chInfo.GetGiftInfo().GetGiftName(),
			GiftIcon:      chInfo.GetGiftInfo().GetGiftIcon(),
			GiftCnt:       chInfo.GetAwardCnt(),
			GiftValueText: getGiftValueText(chInfo.GetAwardCnt(), chInfo.GetGiftInfo()),
			ChannelType:   mapId2ChInfo[chInfo.GetChannelId()].GetChannelType(),
			BindId:        mapId2ChInfo[chInfo.GetChannelId()].GetBindId(),
		})
	}

	resp.NextPage = recResp.NextPage
	log.DebugWithCtx(ctx, "GetRecLotteryChList end req:%v resp:%v size:%d", req, resp, len(resp.GetChList()))
	return resp, nil
}

func (s *ChannelRecommendLogicImpl) GetRevenueSwitchHubService(ctx context.Context, req *pb.GetRevenueSwitchHubRequest) (*pb.GetRevenueSwitchHubResponse, error) {
	resp := &pb.GetRevenueSwitchHubResponse{}
	log.DebugWithCtx(ctx, "GetRevenueSwitchHub req:%v ", req)
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetRecLotteryChList ServiceInfoFromContext fail. req:%+v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	opUid := serviceInfo.UserID

	recResp, err := s.channelRecCli.GetRevenueSwitchHub(ctx, &svrPb.GetRevenueSwitchHubReq{
		Uid: opUid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRecLotteryChList failed req:%v err:%v", req, err)
		return resp, err
	}
	resp.IsOpenMap = recResp.GetIsOpenMap()
	log.DebugWithCtx(ctx, "GetRevenueSwitchHub req:%v resp:%v", req, resp)
	return resp, nil
}

func (s *ChannelRecommendLogicImpl) SetRevenueSwitchHubService(ctx context.Context, req *pb.SetRevenueSwitchHubRequest) (*pb.SetRevenueSwitchHubResponse, error) {
	resp := &pb.SetRevenueSwitchHubResponse{}
	log.DebugWithCtx(ctx, "SetRevenueSwitchHub req:%v ", req)
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetRecLotteryChList ServiceInfoFromContext fail. req:%+v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	opUid := serviceInfo.UserID

	_, err := s.channelRecCli.SetRevenueSwitchHub(ctx, &svrPb.SetRevenueSwitchHubReq{
		Uid:        opUid,
		SwitchType: req.GetSwitchType(),
		IsOpen:     req.GetIsOpen(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRecLotteryChList failed req:%v err:%v", req, err)
		return resp, err
	}

	return resp, nil
}

func (s *ChannelRecommendLogicImpl) GetChannelTopOverLay(ctx context.Context, req *pb.GetChannelTopOverLayRequest) (*pb.GetChannelTopOverLayResponse, error) {
	resp := &pb.GetChannelTopOverLayResponse{}

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetChannelTopOverLay ServiceInfoFromContext fail. req:%+v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	opUid := serviceInfo.UserID

	revenueResp, err := rpc.RevenueRecCli.GetUserRevenueRec(ctx, &revenueRecPb.GetUserRevenueRecReq{
		RecType:    uint32(revenueRecPb.GetUserRevenueRecReq_Req_Type_Top_Over_Lay),
		Uid:        opUid,
		MapTypeCnt: req.GetMapTypeCnt(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelTopOverLay GetUserRevenueRec failed req:%v err:%v", req, err)
		return resp, err
	}

	if revenueResp.GetRecInfo() != nil {
		overLayInfo := &pb.ChannelTopOverLay{}
		err = proto.Unmarshal(revenueResp.GetRecInfo(), overLayInfo)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetChannelTopOverLay Unmarshal failed req:%v revenueResp:%v err:%v", req, revenueResp, err)
		} else {
			resp.TopOverLay = overLayInfo
		}
	}

	resp.IntervalTs = revenueResp.GetIntervalTs()

	log.DebugWithCtx(ctx, "GetChannelTopOverLay end req:%v resp:%v", req, resp)
	return resp, nil
}
