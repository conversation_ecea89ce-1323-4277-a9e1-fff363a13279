package conf

import (
	"fmt"
	"golang.52tt.com/pkg/config"
)

type ServerConf struct {
	Name string `json:"name"`
}

type ServiceConfigT struct {
	SConf             *ServerConf `json:"server"`
	IopTopOverlayAddr string      `json:"iop_top_overlay_addr"` //智能运营平台地址
}

func (sc *ServiceConfigT) Parse(configer config.Configer) (err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("Failed to parse config: %v \n", e)
		}
	}()

	return
}
