package conf

import (
    "gitlab.ttyuyin.com/avengers/tyr/core/config/ttconfig"
    "sync/atomic"
)

type LogicBusinessConf struct {
    ProposeTips              []string          `json:"propose_tips"` // 求婚tips
    WeddingHallConf          WeddingHallConfig `json:"wedding_hall_conf"`
    BlessText                string            `json:"bless_text"`                   // 祝福语
    BigScreenLimitChangeTime uint32            `json:"big_screen_limit_change_time"` // 大屏限制修改时间, 单位分钟
    DownloadDelaySec         uint32            `json:"download_delay_sec"`           // 下载延迟时间, 单位秒
    EnterRoomReminderXml     string            `json:"enter_room_reminder_xml"`      // 进房提醒消息
    PcNickNameLen            uint32            `json:"pc_nick_name_len"`             // PC端昵称长度限制
}

type WeddingHallConfig struct {
    ShowWeddingTab                    bool     `json:"show_wedding_tab"`
    ShowWeddingHallEntry              bool     `json:"show_wedding_hall_entry"`
    ShowWeddingHallFloatingEntry      bool     `json:"show_wedding_hall_floating_entry"`
    ShowWeddingGroupList              []int32  `json:"show_wedding_group_list"`
    ShowWeddingEffectVersionList      []string `json:"show_wedding_effect_version_list"`
    WeddingHallEntryBackground        string   `json:"wedding_hall_entry_background"`
    WeddingHallEntryLottie            string   `json:"wedding_hall_entry_lottie"`
    WeddingHallEntryLottieMD5         string   `json:"wedding_hall_entry_lottie_md5"`
    WeddingReserveEntryBackground     string   `json:"wedding_reserve_entry_background"`
    WeddingReserveEntryLottie         string   `json:"wedding_reserve_entry_lottie"`
    WeddingReserveEntryLottieMd5      string   `json:"wedding_reserve_entry_lottie_md5"`
    WeddingHallBackground             string   `json:"wedding_hall_background"`
    WeddingHallReserveEntryBackground string   `json:"wedding_hall_reserve_entry_background"`
    WeddingHallReserveEntryHintLeft   uint32   `json:"wedding_hall_reserve_entry_hint_left"`
    WeddingHallReserveEntryHintRight  uint32   `json:"wedding_hall_reserve_entry_hint_right"`
    WeddingHallGoingItemFrame         string   `json:"wedding_hall_going_item_frame"`
    WeddingHallComingItemFrame        string   `json:"wedding_hall_coming_item_frame"`
    WeddingHallComingListMaxSize      uint32   `json:"wedding_hall_coming_list_max_size"`
    WeddingHallHotLabelUrl            string   `json:"wedding_hall_hot_label_url"`
}

type BusinessDyConf struct {
    atomicConfig *atomic.Value
}

func NewBusinessDyConf() (*BusinessDyConf, error) {
    out := &BusinessDyConf{}
    cfg := &LogicBusinessConf{}
    atomCfg, err := ttconfig.AtomLoad("channel-wedding-logic", cfg)
    if nil != err {
        return out, err
    }

    out.atomicConfig = atomCfg
    return out, nil
}

func (c *BusinessDyConf) GetConfig() *LogicBusinessConf {
    if c == nil || c.atomicConfig == nil {
        return &LogicBusinessConf{}
    }

    if cfg, ok := c.atomicConfig.Load().(*LogicBusinessConf); ok {
        return cfg
    }

    return &LogicBusinessConf{}
}

func (c *BusinessDyConf) GetProposeTips() []string {
    return c.GetConfig().ProposeTips
}

func (c *BusinessDyConf) GetBigScreenLimitChangeTime() uint32 {
    if c.GetConfig().BigScreenLimitChangeTime > 0 {
        return c.GetConfig().BigScreenLimitChangeTime
    }
    return 6 // 默认6分钟
}

func (c *BusinessDyConf) GetPcNickNameLen() uint32 {
    if c.GetConfig().PcNickNameLen > 0 {
        return c.GetConfig().PcNickNameLen
    }
    return 5 // 默认5个字符
}
