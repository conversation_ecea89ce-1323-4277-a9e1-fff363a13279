package internal

import (
    "context"
    "fmt"
    "gitlab.ttyuyin.com/avengers/tyr/core/log"
    "gitlab.ttyuyin.com/bizFund/bizFund/pkg/lpm_proxy"
    channelMic "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_mic"
    "golang.52tt.com/clients/account"
    censoringProxy "golang.52tt.com/clients/censoring-proxy"
    channelstats "golang.52tt.com/clients/channel-stats"
    headdynamicimage "golang.52tt.com/clients/head-dynamic-image-logic"
    headImageCli "golang.52tt.com/clients/headimage"
    imApi "golang.52tt.com/clients/im-api"
    "golang.52tt.com/clients/nobility"
    pushNotification "golang.52tt.com/clients/push-notification/v2"
    riskMngApiClient "golang.52tt.com/clients/risk-mng-api"
    friendShipCli "golang.52tt.com/clients/ugc/friendship"
    useronline "golang.52tt.com/clients/user-online"
    userProfileApi "golang.52tt.com/clients/user-profile-api"
    "golang.52tt.com/protocol/app"
    "golang.52tt.com/protocol/app/channel_wedding_logic"
    accountpb "golang.52tt.com/protocol/services/accountsvr"
    channel_wedding "golang.52tt.com/protocol/services/channel-wedding"
    channel_wedding_conf "golang.52tt.com/protocol/services/channel-wedding-conf"
    channel_wedding_minigame "golang.52tt.com/protocol/services/channel-wedding-minigame"
    channel_wedding_plan "golang.52tt.com/protocol/services/channel-wedding-plan"
    "golang.52tt.com/protocol/services/demo/echo"
    fellow "golang.52tt.com/protocol/services/fellow-svr"
    virtual_image_resource "golang.52tt.com/protocol/services/virtual-image-resource"
    "golang.52tt.com/services/channel-wedding-logic/internal/local_cache"
    context0 "golang.org/x/net/context"
    "math/rand"

    "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_follow"
    //riskMngApiClient "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/risk_mng_api"
    micMiddle "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_mic_middle"
    micScheme "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_scheme_middle"
    "golang.52tt.com/clients/channel"
    "golang.52tt.com/clients/channelol"
    magic_spirit "golang.52tt.com/clients/magic-spirit"
    present_extra_conf "golang.52tt.com/clients/present-extra-conf"
    userPresent "golang.52tt.com/clients/userpresent-go"
    usual_device "golang.52tt.com/clients/usual-device"
    "golang.52tt.com/pkg/protocol"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/app/virtual_image_logic"
    "golang.52tt.com/protocol/common/status"
    virtual_image_user "golang.52tt.com/protocol/services/virtual-image-user"
    "golang.52tt.com/services/channel-wedding-logic/internal/conf"
    "google.golang.org/grpc/codes"
    "time"
)

type StartConfig struct {
}

const (
    HostMicId = 1
)

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
    log.Infof("server startup with cfg: %+v", *cfg)
    miniGameCli := channel_wedding_minigame.MustNewClient(context.Background())
    channelCli := channel.NewClient()
    riskMngApiCli, _ := riskMngApiClient.NewClient()
    micMiddleCli := micMiddle.MustNewClient(context.Background())
    micSchemeCli := micScheme.MustNewClient(context.Background())
    channelMicCli := channelMic.MustNewClient(context.Background())
    userProfile, _ := userProfileApi.NewClient()
    weddingCli := channel_wedding.MustNewClient(ctx)
    virtualImageCli := virtual_image_user.MustNewClient(ctx)
    channelOlCli := channelol.NewClient()
    channelStatCli, _ := channelstats.NewClient()
    weddingPlanCli, _ := channel_wedding_plan.NewClient(ctx)
    censorCli := censoringProxy.NewIClient()
    channelWeddingConfCli, _ := channel_wedding_conf.NewClient(ctx)

    dyConfig, err := conf.NewBusinessDyConf()
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to NewBusinessDyConf. err:%v", err)
        return nil, err
    }
    localCache := local_cache.NewLocalCache(dyConfig, weddingPlanCli, weddingCli, channelWeddingConfCli, channelStatCli)

    userPresentCli := userPresent.NewClient()
    magicSpiritCli, _ := magic_spirit.NewClient()
    presentExtraCli, _ := present_extra_conf.NewClient()
    usualDeviceCli := usual_device.NewIClient()
    userOnlineCli := useronline.NewIClient()
    fellowCli, _ := fellow.NewClient(ctx)
    pushCli := pushNotification.NewIClient()
    headDynamicImageCli := headdynamicimage.NewClient()
    headImageClient := headImageCli.NewClient()
    accountCli, _ := account.NewClient()

    lpmProxy, err := lpm_proxy.NewLpmProxy(ctx, "tt-channel-wedding")
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to lpm_proxy.NewLpmProxy, err:%v", err)
        return nil, err
    }

    virtualImageResource := virtual_image_resource.MustNewClient(ctx)
    nobilityCli := nobility.NewIClient()
    imApiCli := imApi.NewIClient()

    friendshipCli := friendShipCli.NewIClient()
    channelFollowCli, err := channel_follow.NewClient(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to channel_follow.NewClient, err:%v", err)
        return nil, err
    }
    return &Server{
        dyConfig:     dyConfig,
        headImageCli: headImageClient,

        localCache:             localCache,
        miniGameCli:            miniGameCli,
        channelCli:             channelCli,
        riskMngApiCli:          riskMngApiCli,
        micMiddleCli:           micMiddleCli,
        friendshipCli:          friendshipCli,
        userProfile:            userProfile,
        micSchemeCli:           micSchemeCli,
        channelMicCli:          channelMicCli,
        weddingCli:             weddingCli,
        headDynamicImageCli:    headDynamicImageCli,
        virtualImageCli:        virtualImageCli,
        channelOlCli:           channelOlCli,
        magicSpiritCli:         magicSpiritCli,
        userPresentCli:         userPresentCli,
        presentExtraConfClient: presentExtraCli,
        UsualDeviceClient:      usualDeviceCli,
        channelStatCli:         channelStatCli,
        weddingPlanCli:         weddingPlanCli,
        censorCli:              censorCli,
        userOnlineCli:          userOnlineCli,
        fellowCli:              fellowCli,
        channelWeddingConfCli:  channelWeddingConfCli,
        pushCli:                pushCli,
        accountCli:             accountCli,
        lpmProxy:               lpmProxy,
        virtualImageResource:   virtualImageResource,
        nobilityCli:            nobilityCli,
        imApiCli:               imApiCli,
        channelFollowCli:       channelFollowCli,
    }, nil
}

type Server struct {
    dyConfig               *conf.BusinessDyConf
    localCache             *local_cache.LocalCache
    friendshipCli          friendShipCli.IClient
    headImageCli           headImageCli.IClient
    miniGameCli            channel_wedding_minigame.ChannelWeddingMinigameClient
    channelCli             channel.IClient
    riskMngApiCli          riskMngApiClient.IClient
    micMiddleCli           micMiddle.ChannelMicMiddleClient
    userProfile            userProfileApi.IClient
    micSchemeCli           micScheme.ChannelSchemeMiddleClient
    channelMicCli          channelMic.ChannelMicClient
    weddingCli             channel_wedding.ChannelWeddingClient
    headDynamicImageCli    headdynamicimage.IClient
    virtualImageCli        virtual_image_user.VirtualImageUserClient
    channelOlCli           channelol.IClient
    magicSpiritCli         magic_spirit.IClient
    userPresentCli         userPresent.IClient
    presentExtraConfClient present_extra_conf.IClient
    UsualDeviceClient      usual_device.IClient
    channelStatCli         channelstats.IClient
    weddingPlanCli         channel_wedding_plan.ChannelWeddingPlanClient
    censorCli              censoringProxy.IClient
    userOnlineCli          useronline.IClient
    fellowCli              *fellow.Client
    channelWeddingConfCli  channel_wedding_conf.ChannelWeddingConfClient
    pushCli                pushNotification.IClient
    accountCli             *account.Client
    lpmProxy               lpm_proxy.LpmProxyApi
    virtualImageResource   virtual_image_resource.VirtualImageResourceClient
    nobilityCli            nobility.IClient
    imApiCli               imApi.IClient
    channelFollowCli       *channel_follow.Client
}

type simpleWeddingInfo struct {
    groomUid  uint32
    brideUid  uint32
    channelId uint32
    friendUid uint32 // 好友uid
}

// 获取婚礼列表
func (s *Server) getWeddingLists() ([]*local_cache.WeddingHallItemCache, []*local_cache.SimpleWeddingPlanCache) {
    goingList := s.localCache.GetWeddingHallList(true, true)
    goingList = append(goingList, s.localCache.GetWeddingHallList(true, false)...)
    comingList := s.localCache.GetTodayComingWeddingHallList()
    return goingList, comingList
}

// 提取 UID 列表
func extractUidListWithBridesmaid(goingList []*local_cache.WeddingHallItemCache, comingList []*local_cache.SimpleWeddingPlanCache) []uint32 {
    uidList := make([]uint32, 0)
    for _, item := range goingList {
        uidList = append(uidList, item.BrideUid, item.GroomUid)
        uidList = append(uidList, item.BridesmaidUidList...)
        uidList = append(uidList, item.GroomsmanUidList...)
    }
    for _, item := range comingList {
        uidList = append(uidList, item.BrideUid, item.GroomUid)
    }
    return uidList
}

// 获取关注权限信息
func (s *Server) getFollowAuthInfo(c context0.Context, uidList []uint32) (map[uint32]*channel_follow.FollowChannelAuthItem, error) {
    followResp, err := s.channelFollowCli.BatGetFollowChannelAuth(c, &channel_follow.BatGetFollowChannelAuthReq{
        UidList: uidList,
    })
    if err != nil {
        return nil, err
    }

    authInfoMap := make(map[uint32]*channel_follow.FollowChannelAuthItem, len(uidList))
    for i, item := range followResp.GetAuthInfos() {
        authInfoMap[uidList[i]] = item
    }
    return authInfoMap, nil
}

func (s *Server) GetGoingWeddingEntry(c context0.Context, request *channel_wedding_logic.GetGoingWeddingEntryRequest) (*channel_wedding_logic.GetGoingWeddingEntryResponse, error) {
    out := &channel_wedding_logic.GetGoingWeddingEntryResponse{
        BaseResp: &app.BaseResp{},
    }
    defer func() {
        log.DebugWithCtx(c, "GetGoingWeddingEntry request: %+v, response: %+v", request, out)
    }()
    serviceInfo, ok := protogrpc.ServiceInfoFromContext(c)
    if !ok {
        log.ErrorWithCtx(c, "GetGoingWeddingEntry fail to get serviceInfo from context")
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }
    uid := serviceInfo.UserID

    // 获取婚礼列表
    goingList, comingList := s.getWeddingLists()

    // 提取 UID 列表
    uidList := extractUidListWithBridesmaid(goingList, comingList)

    planIds := make([]uint32, 0, len(comingList))
    for _, item := range goingList {
        if item.BrideUid != 0 && item.GroomUid != 0 {
            planIds = append(planIds, item.WeddingPlanId)
        }
    }

    subsResp, err := s.weddingPlanCli.BatGetWeddingSubscribeStatus(c, &channel_wedding_plan.BatGetWeddingSubscribeStatusRequest{
        Uid:               serviceInfo.UserID,
        WeddingPlanIdList: planIds,
    })
    if err != nil {
        log.ErrorWithCtx(c, "GetGoingWeddingEntry fail to BatGetWeddingSubscribeStatus. err:%v", err)
        return out, err
    }
    log.DebugWithCtx(c, "GetGoingWeddingEntry BatGetWeddingSubscribeStatus response: %+v", subsResp.GetSubscribeStatusMap())

    // 获取跟随进房开关信息
    authInfoMap, err := s.getFollowAuthInfo(c, uidList)
    if err != nil {
        log.ErrorWithCtx(c, "GetGoingWeddingEntry fail to getFollowAuthInfo. err:%v", err)
        return out, err
    }

    // 判断是否是自己好友
    isFollowingUidMap := s.batchCheckUserFollowing(c, uid, uidList)
    var myPair *simpleWeddingInfo
    friendPairList := make([]*simpleWeddingInfo, 0, len(comingList))
    // 好友做伴郎伴娘列表
    bridesmaidAndGroomsmanList := make([]*simpleWeddingInfo, 0)

    for _, item := range goingList {
        if item.BrideUid == uid || item.GroomUid == uid {
            myPair = &simpleWeddingInfo{
                brideUid:  item.BrideUid,
                groomUid:  item.GroomUid,
                channelId: item.ChannelId,
            }
            break
        }
        // 如果好友是新娘的婚礼，且有跟随开关
        if (isFollowingUidMap[item.BrideUid] && authInfoMap[item.BrideUid].GetFollowAuth()) ||
            // 如果好友是新郎的婚礼，且有跟随开关开关
            (isFollowingUidMap[item.GroomUid] && authInfoMap[item.GroomUid].GetFollowAuth()) ||
            // 如果是订阅的婚礼，且任意一方有跟随开关
            subsResp.GetSubscribeStatusMap()[item.WeddingPlanId] && (authInfoMap[item.GroomUid].GetFollowAuth() || authInfoMap[item.BrideUid].GetFollowAuth()) {
            friendPairList = append(friendPairList, &simpleWeddingInfo{
                brideUid:  item.BrideUid,
                groomUid:  item.GroomUid,
                channelId: item.ChannelId,
            })
        }
    }
    if myPair == nil && len(friendPairList) == 0 {
        for _, item := range goingList {
            for _, uidItem := range item.BridesmaidUidList {
                if isFollowingUidMap[uidItem] && authInfoMap[uidItem].GetFollowAuth() {
                    bridesmaidAndGroomsmanList = append(bridesmaidAndGroomsmanList, &simpleWeddingInfo{
                        brideUid:  item.BrideUid,
                        groomUid:  item.GroomUid,
                        channelId: item.ChannelId,
                        friendUid: uidItem,
                    })
                    break
                }
            }
            for _, uidItem := range item.GroomsmanUidList {
                if isFollowingUidMap[uidItem] && authInfoMap[uidItem].GetFollowAuth() {
                    bridesmaidAndGroomsmanList = append(bridesmaidAndGroomsmanList, &simpleWeddingInfo{
                        brideUid:  item.BrideUid,
                        groomUid:  item.GroomUid,
                        channelId: item.ChannelId,
                        friendUid: uidItem,
                    })
                    break
                }
            }
        }
    }

    if myPair != nil {
        _, err := s.fillGoingEntryResp(c, myPair, out)
        if err != nil {
            log.ErrorWithCtx(c, "GetGoingWeddingEntry fail to fillGoingEntryResp. err:%v, request:%+v, myPair:%+v", err, request, myPair)
            return out, err
        }
        out.MainTitle = "我们结婚啦"
        out.SubTitle = "你的婚礼进行中"
        return out, nil
    } else if len(friendPairList) > 0 {
        page, nextPage := getPage(request.GetPage(), uint32(len(friendPairList)))
        out.NextPageNum = nextPage
        friendPair := friendPairList[page]
        userMap, err := s.fillGoingEntryResp(c, friendPair, out)
        if err != nil {
            log.ErrorWithCtx(c, "GetGoingWeddingEntry fail to fillGoingEntryResp. err:%v, request:%+v, friendPair:%+v", err, request, friendPair)
            return out, err
        }
        out.MainTitle = "好友结婚啦"
        // 两个中必有一个是好友
        if isFollowingUidMap[friendPair.brideUid] {
            out.SubTitle = fmt.Sprintf("%s 举办婚礼中...", s.getShortNickname(serviceInfo.ClientType, userMap[friendPair.brideUid].Nickname))
        } else {
            out.SubTitle = fmt.Sprintf("%s 举办婚礼中...", s.getShortNickname(serviceInfo.ClientType, userMap[friendPair.groomUid].Nickname))
        }
        return out, nil
    } else if len(bridesmaidAndGroomsmanList) > 0 {
        // 根据page取出一对
        page, nextPage := getPage(request.GetPage(), uint32(len(bridesmaidAndGroomsmanList)))
        out.NextPageNum = nextPage
        bridesmaidAndGroomsman := bridesmaidAndGroomsmanList[page%uint32(len(bridesmaidAndGroomsmanList))]
        userMap, err := s.userProfile.BatchGetUserProfileV2(c, []uint32{bridesmaidAndGroomsman.friendUid, bridesmaidAndGroomsman.brideUid, bridesmaidAndGroomsman.groomUid}, false)
        if err != nil {
            log.ErrorWithCtx(c, "GetGoingWeddingEntry fail to BatchGetUserProfileV2. err:%v, request:%+v, bridesmaidAndGroomsman:%+v", err, request, bridesmaidAndGroomsman)
            return out, err
        }
        out.Bride = &channel_wedding_logic.UserInfoWithChannel{
            ChannelId: bridesmaidAndGroomsman.channelId,
            UserInfo:  userMap[bridesmaidAndGroomsman.brideUid],
        }
        out.Groom = &channel_wedding_logic.UserInfoWithChannel{
            ChannelId: bridesmaidAndGroomsman.channelId,
            UserInfo:  userMap[bridesmaidAndGroomsman.groomUid],
        }
        out.MainTitle = "沾沾喜气"
        if userMap[bridesmaidAndGroomsman.friendUid].GetSex() == uint32(accountpb.USER_SEX_USER_SEX_FEMALE) {
            out.SubTitle = fmt.Sprintf("好友 %s 在做伴娘~", s.getShortNickname(serviceInfo.ClientType, userMap[bridesmaidAndGroomsman.friendUid].Nickname))
        } else {
            out.SubTitle = fmt.Sprintf("好友 %s 在做伴郎~", s.getShortNickname(serviceInfo.ClientType, userMap[bridesmaidAndGroomsman.friendUid].Nickname))
        }
        return out, nil
    } else if len(comingList) > 0 {
        // coming
        comingPairList := make([]*simpleWeddingInfo, 0, len(comingList))
        for _, item := range comingList {
            if (isFollowingUidMap[item.BrideUid] && authInfoMap[item.BrideUid].GetFollowAuth()) ||
                (isFollowingUidMap[item.GroomUid] && authInfoMap[item.GroomUid].GetFollowAuth()) {
                comingPairList = append(comingPairList, &simpleWeddingInfo{
                    brideUid:  item.BrideUid,
                    groomUid:  item.GroomUid,
                    channelId: item.ChannelId,
                })
            }
        }
        infoWithChannelList := make([]*uidWithChannel, 0, len(comingPairList))
        for _, pair := range comingPairList {
            if isFollowingUidMap[pair.brideUid] {
                infoWithChannelList = append(infoWithChannelList, &uidWithChannel{
                    channelId: pair.channelId,
                    uid:       pair.brideUid,
                })
            }
            if isFollowingUidMap[pair.groomUid] {
                infoWithChannelList = append(infoWithChannelList, &uidWithChannel{
                    channelId: pair.channelId,
                    uid:       pair.groomUid,
                })
            }
        }

        var returnInfoWithChannelList []*uidWithChannel
        // 随机从infoWithChannelList 取出三个，允许不足3个，不能重复，不考虑page
        if len(infoWithChannelList) > 3 {
            returnInfoWithChannelList = make([]*uidWithChannel, 0, 3)
            usedIndex := make(map[int]bool)
            for len(returnInfoWithChannelList) < 3 {
                index := rand.Intn(len(infoWithChannelList)) // 生成随机索引
                if !usedIndex[index] {
                    usedIndex[index] = true
                    returnInfoWithChannelList = append(returnInfoWithChannelList, infoWithChannelList[index])
                }
            }
        } else {
            returnInfoWithChannelList = infoWithChannelList
        }
        friendUidList := make([]uint32, 0, len(returnInfoWithChannelList))
        for _, info := range returnInfoWithChannelList {
            friendUidList = append(friendUidList, info.uid)
        }
        userInfoMap, err := s.userProfile.BatchGetUserProfileV2(c, friendUidList, false)
        if err != nil {
            log.ErrorWithCtx(c, "GetGoingWeddingEntry fail to GetUsersMap. err:%v, request:%+v, friendUidList:%+v", err, request, friendUidList)
            return out, err
        }
        for _, info := range returnInfoWithChannelList {
            out.OtherUserList = append(out.OtherUserList, &channel_wedding_logic.UserInfoWithChannel{
                ChannelId: info.channelId,
                UserInfo:  userInfoMap[info.uid],
            })
        }
        if len(comingPairList) > 0 {
            out.MainTitle = "沾沾喜气"
            out.SubTitle = fmt.Sprintf("今天有%d场婚礼可观礼", len(comingPairList))
        }
        return out, nil
    } else {
        return out, nil
    }
}

func getPage(page uint32, listLen uint32) (truePage, nextPage uint32) {
    // 限制 page 不超过最大索引
    maxPageIndex := listLen - 1
    truePage = page
    if truePage > maxPageIndex {
        truePage = 0
    }

    // 设置下一页页码
    if truePage < maxPageIndex {
        nextPage = truePage + 1
    } else {
        nextPage = 0 // 循环回第一页
    }
    return truePage, nextPage
}

type uidWithChannel struct {
    uid       uint32
    channelId uint32
}

func (s *Server) getShortNickname(clientType uint16, nickname string) string {
    wordCnt := 6
    if clientType == uint16(app.TT_CLIENT_TYPE_TT_CLIENT_TYPE_PC_TT) {
        wordCnt = int(s.dyConfig.GetPcNickNameLen())
    }
    runes := []rune(nickname) // 将字符串转换为字符切片
    if len(runes) <= wordCnt {
        return nickname
    }
    return string(runes[:wordCnt]) + "..."
}
func (s *Server) fillGoingEntryResp(c context0.Context, friendPair *simpleWeddingInfo, out *channel_wedding_logic.GetGoingWeddingEntryResponse) (map[uint32]*app.UserProfile, error) {
    userMap, err := s.userProfile.BatchGetUserProfileV2(c, []uint32{friendPair.brideUid, friendPair.groomUid}, false)
    if err != nil {
        log.ErrorWithCtx(c, "GetGoingWeddingEntry fail to GetUsersMap. err:%v", err)
        return nil, err
    }
    out.Bride = &channel_wedding_logic.UserInfoWithChannel{
        ChannelId: friendPair.channelId,
        UserInfo:  userMap[friendPair.brideUid],
    }
    out.Groom = &channel_wedding_logic.UserInfoWithChannel{
        ChannelId: friendPair.channelId,
        UserInfo:  userMap[friendPair.groomUid],
    }
    return userMap, nil
}

// BatchCheckPlayMate 批量判断是 关注用户
func (s *Server) batchCheckUserFollowing(ctx context.Context, uid uint32, checkUids []uint32) map[uint32]bool {
    isFollowingMap, _, err := s.friendshipCli.BatchGetBiFollowingWithCache(ctx, uid, checkUids, true, false)
    if err != nil {
        log.ErrorWithCtx(ctx, "batchCheckUserFollowing uid: %+v, checkUids: %+v, err: %v", uid, checkUids, err)
        return map[uint32]bool{}
    }
    return isFollowingMap
}

func (s *Server) ShutDown() {}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
    return req, nil
}

// 判断用户是否是主持人
func (s *Server) isHost(ctx context.Context, opUid, channelId uint32) (bool, error) {
    hostUid, err := s.getChannelHost(ctx, channelId)
    return hostUid == opUid, err
}

func (s *Server) getChannelHost(ctx context.Context, channelId uint32) (uint32, error) {
    // 获取房间当前麦位列表
    micResp, err := s.channelMicCli.GetMicrList(ctx, &channelMic.GetMicrListReq{
        ChannelId: channelId,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "TakePlayersToMicAndCheck fail to GetMicrList. channelId:%d, err:%v", channelId, err)
        return 0, err
    }

    for _, mic := range micResp.GetAllMicList() {
        if mic.GetMicId() == HostMicId { // 1号麦是主持人
            return mic.GetMicUid(), nil
        }
    }

    return 0, nil
}

// 批量获取用户虚拟形象 返回 map[uint32]*pb.UserInuseItemInfo
func (s *Server) batGetUserVirtualImage(ctx context.Context, uidList []uint32) (map[uint32]*virtual_image_user.UserInuseItemInfo, error) {
    if len(uidList) == 0 {
        return nil, nil
    }
    resp, err := s.virtualImageCli.BatchGetUserInuseItemInfo(ctx, &virtual_image_user.BatchGetUserInuseItemInfoReq{
        UidList: uidList,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetUserVirtualImageInuse fail to BatchGetUserInuseItemInfo uidList:%v, err:%v", uidList, err)
        return nil, err
    }

    imageMap := make(map[uint32]*virtual_image_user.UserInuseItemInfo)
    for _, userInuseInfo := range resp.GetUserInuseItemInfo() {
        imageMap[userInuseInfo.GetUid()] = userInuseInfo
    }

    return imageMap, nil
}

// checkUserVirtualImageMicDisplay 检查用户是否开启虚拟形象麦位展示
func (s *Server) checkUserVirtualImageMicDisplay(ctx context.Context, uid uint32) (bool, error) {
    userVIResp, err := s.virtualImageCli.GetUserDisplaySwitch(ctx, &virtual_image_user.GetUserDisplaySwitchRequest{
        Uid: uid,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "CheckUserVirtualImageMicDisplay failed to GetUserDisplaySwitch. uid:%v, err:%v", uid, err)
        return false, err
    }

    for _, v := range userVIResp.GetDisplaySwitch() {
        if v.GetType() == uint32(virtual_image_logic.VirtualImageDisplaySwitch_VIRTUAL_IMAGE_DISPLAY_SWITCH_MIC) &&
            v.GetSwitchOn() {
            return true, nil
        }
    }

    return false, nil
}

func (s *Server) isInRoom(ctx context.Context, uid uint32, cid uint32) bool {
    channelId, err := s.channelOlCli.GetUserChannelId(ctx, 0, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "isInRoom fail to GetUserChannelId uid:%d, err:%v", uid, err)
        return false
    }
    return channelId == cid
}

func ToChairGameCommonErr(msg string) protocol.ServerError {
    return protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingMinigameCommonErr, msg)
}

func GoroutineWithTimeoutCtx(ctx context.Context, timeout time.Duration, fn func(ctx context.Context)) {
    timeoutCtx, cancel := protogrpc.NewContextWithInfoTimeout(ctx, timeout)
    go func() {
        defer cancel()
        fn(timeoutCtx)
    }()
}

func (s *Server) CancelWedding(c context0.Context, request *channel_wedding_logic.CancelWeddingRequest) (*channel_wedding_logic.CancelWeddingResponse, error) {
    out := &channel_wedding_logic.CancelWeddingResponse{}
    defer func() {
        log.InfoWithCtx(c, "CancelWedding response: %+v, request: %+v", out, request)
    }()

    serviceInfo, ok := protogrpc.ServiceInfoFromContext(c)
    if !ok {
        log.ErrorWithCtx(c, "CancelWedding fail to get serviceInfo, req:%+v", request)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }
    _, err := s.weddingPlanCli.CancelWedding(c, &channel_wedding_plan.CancelWeddingRequest{
        Uid:           serviceInfo.UserID,
        WeddingPlanId: request.GetWeddingPlanId(),
    })
    if err != nil {
        log.ErrorWithCtx(c, "CancelWedding fail to CancelWedding. err:%v, req:%+v", err, request)
        return out, err
    }
    return out, nil
}

func (s *Server) ApplyEndWeddingRelationship(c context0.Context, request *channel_wedding_logic.ApplyEndWeddingRelationshipRequest) (*channel_wedding_logic.ApplyEndWeddingRelationshipResponse, error) {
    out := &channel_wedding_logic.ApplyEndWeddingRelationshipResponse{}
    defer func() {
        log.InfoWithCtx(c, "ApplyEndWeddingRelationship success. request:%+v, out:%+v", request, out)
    }()
    serviceInfo, ok := protogrpc.ServiceInfoFromContext(c)
    if !ok {
        log.ErrorWithCtx(c, "ApplyEndWeddingRelationship fail to get serviceInfo")
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }
    resp, err := s.weddingPlanCli.ApplyEndWeddingRelation(c, &channel_wedding_plan.ApplyEndWeddingRelationRequest{
        Uid:    serviceInfo.UserID,
        Source: request.GetSource(),
    })
    if err != nil {
        log.ErrorWithCtx(c, "ApplyEndWeddingRelationship fail to ApplyEndWeddingRelation. err:%v, req:%+v", err, request)
        return out, err
    }
    out.EndRelationshipDeadline = resp.GetEndRelationshipDeadline()
    return out, nil
}

func (s *Server) CancelEndWeddingRelationship(c context0.Context, request *channel_wedding_logic.CancelEndWeddingRelationshipRequest) (*channel_wedding_logic.CancelEndWeddingRelationshipResponse, error) {
    out := &channel_wedding_logic.CancelEndWeddingRelationshipResponse{}
    defer func() {
        log.InfoWithCtx(c, "CancelEndWeddingRelationship success. request:%+v, out:%+v", request, out)
    }()
    serviceInfo, ok := protogrpc.ServiceInfoFromContext(c)
    if !ok {
        log.ErrorWithCtx(c, "CancelEndWeddingRelationship fail to get serviceInfo")
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }
    _, err := s.weddingPlanCli.CancelEndWeddingRelation(c, &channel_wedding_plan.CancelEndWeddingRelationRequest{
        Uid: serviceInfo.UserID,
    })
    if err != nil {
        log.ErrorWithCtx(c, "CancelEndWeddingRelationship fail to CancelEndWeddingRelation. err:%v, req:%+v", err, request)
        return out, err
    }
    return out, nil
}

func (s *Server) DirectEndWeddingRelationship(c context0.Context, request *channel_wedding_logic.DirectEndWeddingRelationshipRequest) (*channel_wedding_logic.DirectEndWeddingRelationshipResponse, error) {
    return &channel_wedding_logic.DirectEndWeddingRelationshipResponse{}, nil
}

func (s *Server) GetWeddingThemeCfgList(ctx context.Context, in *channel_wedding_logic.GetWeddingThemeCfgListRequest) (*channel_wedding_logic.GetWeddingThemeCfgListResponse, error) {
    out := &channel_wedding_logic.GetWeddingThemeCfgListResponse{
        DownloadDelaySec: s.dyConfig.GetConfig().DownloadDelaySec,
    }

    resp, err := s.channelWeddingConfCli.GetThemeCfgList(ctx, &channel_wedding_conf.GetThemeCfgListReq{})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetWeddingThemeCfgList fail to GetThemeCfgList. err:%v", err)
        return out, err
    }

    if len(resp.GetThemeCfgList()) == 0 {
        return out, nil
    }

    //resource, err := s.virtualImageResource.GetDefaultResourceList(ctx, &virtual_image_resource.GetDefaultResourceListRequest{})
    //if err != nil {
    //    log.ErrorWithCtx(ctx, "GetWeddingThemeCfgList fail to GetDefaultResourceList. err:%v", err)
    //    return out, err
    //}

    out.ThemeCfgList = make([]*channel_wedding_logic.WeddingRoomThemeCfg, 0)
    for _, theme := range resp.GetThemeCfgList() {
        if theme.GetIsDeleted() {
            continue
        }

        isFree := theme.GetPriceInfo().GetPriceType() != uint32(channel_wedding_logic.WeddingPriceType_WEDDING_PRICE_TYPE_T_BEAN)
        if isFree {
            // 免费主题资源不需要预下载
            continue
        }

        cfg := fillWeddingRoomThemeCfg(theme, isFree)

        // 特殊过渡逻辑，将默认素体资源添加到其中一个等级服装中返回，方便客户端预下载，后续需由客户端发版自行处理
        //if i == 0 {
        //    for j, v := range cfg.GetLevelClothesList() {
        //        if j != 0 {
        //            continue
        //        }
        //        v.BrideClothes = append(v.BrideClothes, resource.GetFemaleResources()...)
        //        v.GroomClothes = append(v.GroomClothes, resource.GetMaleResources()...)
        //    }
        //}

        out.ThemeCfgList = append(out.ThemeCfgList, cfg)
    }

    return out, nil
}

func fillWeddingResource(resource *channel_wedding_conf.WeddingPreviewCfg) *channel_wedding_logic.WeddingResource {
    res := resource.GetResource()
    return &channel_wedding_logic.WeddingResource{
        ResourceUrl:  res.GetResourceUrl(),
        ResourceMd5:  res.GetResourceMd5(),
        CpBoneId:     resource.GetCpBoneId(),
        ItemIdList:   resource.GetItemIds(),
        BaseCpBoneId: resource.GetBaseCpBoneId(),
    }
}

func fillWeddingLevelClothes(cfg *channel_wedding_conf.ThemeLevelCfg) *channel_wedding_logic.WeddingLevelClothes {
    if cfg == nil {
        return nil
    }

    suitMap := cfg.GetGuestDressCfgMap()
    out := &channel_wedding_logic.WeddingLevelClothes{
        Level:             cfg.Level,
        GroomClothes:      suitMap[uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_GROOM)].GetSuitCfg().GetItemIds(),
        BrideClothes:      suitMap[uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDE)].GetSuitCfg().GetItemIds(),
        GroomsmanClothes:  suitMap[uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDES)].GetSuitCfg().GetItemIds(),
        BridesmaidClothes: suitMap[uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDESMAID)].GetSuitCfg().GetItemIds(),
    }

    return out
}

func fillWeddingRoomThemeCfg(cfg *channel_wedding_conf.ThemeCfg, isFreeWedding bool) *channel_wedding_logic.WeddingRoomThemeCfg {
    if cfg == nil {
        return nil
    }

    out := &channel_wedding_logic.WeddingRoomThemeCfg{
        ThemeId:                cfg.ThemeId,
        ThemeResource:          cfg.GetThemeRoomResource().GetResourceUrl(),
        ThemeResourceMd5:       cfg.GetThemeRoomResource().GetResourceMd5(),
        LevelClothesList:       make([]*channel_wedding_logic.WeddingLevelClothes, 0),
        LevelBackgroundList:    make([]*channel_wedding_logic.WeddingLevelBackgroundCfg, 0),
        SceneCfgList:           make([]*channel_wedding_logic.WeddingSceneCfg, 0, len(cfg.SceneCfgList)),
        WeddingPreviewResource: fillWeddingResource(cfg.GetWeddingPreviewCfg()),
        IsFreeTheme:            isFreeWedding,
    }

    for _, v := range cfg.GetThemeLevelCfgList() {
        log.Debugf("fillWeddingRoomThemeCfg level:%d, BackgroundMp4Url:%s, v.GetSpecialBackgroundPicture():%s", v.GetLevel(), v.GetRoomBackgroundMp4Url(), v.GetSpecialBackgroundPicture())
        out.LevelClothesList = append(out.LevelClothesList, fillWeddingLevelClothes(v))

        out.LevelBackgroundList = append(out.LevelBackgroundList, &channel_wedding_logic.WeddingLevelBackgroundCfg{
            Level:                    v.GetLevel(),
            BackgroundPicture:        v.GetRoomBackgroundPicture(),
            BackgroundMp4Url:         v.GetRoomBackgroundMp4Url(),
            SpecialBackgroundPicture: v.GetSpecialBackgroundPicture(),
            SpecialBackgroundMp4Url:  v.GetSpecialBackgroundMp4Url(),
        })
    }

    for _, v := range cfg.SceneCfgList {
        bonsList := make([]*channel_wedding_logic.WeddingSceneBoneCfg, 0)
        for _, bone := range v.GetBoneCfgList() {
            bonsList = append(bonsList, &channel_wedding_logic.WeddingSceneBoneCfg{
                Level:         bone.Level,
                SeqIndex:      bone.SeqIndex,
                AnimationName: bone.AnimationName,
                BoneId:        bone.BoneId,
                BaseBoneId:    bone.GetBaseBoneId(),
            })
        }

        out.SceneCfgList = append(out.SceneCfgList, &channel_wedding_logic.WeddingSceneCfg{
            Scene:            v.GetScene(),
            SceneResource:    v.GetResource().GetResourceUrl(),
            SceneResourceMd5: v.GetResource().GetResourceMd5(),
            BoneCfgList:      bonsList,
        })
    }

    if cfg.GetChairResCfg() != nil {
        res := cfg.GetChairResCfg()
        // 椅子资源
        out.ChairResCfg = &channel_wedding_logic.ChairGameResourceCfg{
            ChairPic:            res.ChairPic,
            SittingPoseFemaleId: res.SittingPoseFemaleId,
            SittingPoseMaleId:   res.SittingPoseMaleId,
            StandbyFemaleId:     res.StandbyFemaleId,
            StandbyMaleId:       res.StandbyMaleId,
            FailFemaleIds:       res.FailFemaleIds,
            FailMaleIds:         res.FailMaleIds,
        }
    }

    return out
}

func (s *Server) GetUserInRoomStatus(ctx context.Context, in *channel_wedding_logic.GetUserInRoomStatusRequest) (*channel_wedding_logic.GetUserInRoomStatusResponse, error) {
    out := &channel_wedding_logic.GetUserInRoomStatusResponse{
        UserList: make([]*channel_wedding_logic.UserInRoomStatus, 0),
    }

    if len(in.GetUidList()) == 0 {
        return out, nil
    }

    // 批量获取用户信息
    upMap, err := s.userProfile.BatchGetUserProfileV2(ctx, in.GetUidList(), true)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserInRoomStatus fail to BatchGetUserProfileV2. in:%+v, err:%v", in, err)
        return out, err
    }

    userMap := make(map[uint32]*app.UserProfile) // 普通用户信息
    ukwUidList := make([]uint32, 0)
    for uid, up := range upMap {
        if up.GetPrivilege().GetType() == uint32(app.EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW) {
            ukwUidList = append(ukwUidList, uid)
        } else {
            userMap[uid] = up
        }
    }

    if len(ukwUidList) > 0 {
        // 获取神秘人的用户信息
        ukwUserMap, err := s.userProfile.BatchGetUserProfileV2(ctx, ukwUidList, false)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetUserInRoomStatus fail to BatchGetUserProfileV2. in:%+v, err:%v", in, err)
            return out, err
        }

        for uid, up := range ukwUserMap {
            userMap[uid] = up
        }
    }

    // 获取用户在房状态
    uid2Cid, err := s.channelOlCli.BatchGetUserChannelId(ctx, 0, in.GetUidList()...)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserInRoomStatus fail to BatchGetUserChannelId. in:%+v, err:%v", in, err)
        return out, err
    }

    // 获取贵族隐身状态
    uid2Nobility, err := s.nobilityCli.BatchGetNobilityInfo(ctx, 0, in.GetUidList())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserInRoomStatus fail to BatchGetNobilityInfo. in:%+v, err:%v", in, err)
        return out, err
    }

    for _, uid := range in.GetUidList() {
        user, ok := userMap[uid]
        if !ok {
            continue
        }

        info := &channel_wedding_logic.UserInRoomStatus{
            User: user,
        }

        if uid2Cid[uid] == in.GetCid() && !uid2Nobility[uid].GetInvisible() &&
            upMap[uid].GetPrivilege().GetType() != uint32(app.EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW) {
            info.InRoom = true
        }

        out.UserList = append(out.UserList, info)
    }

    return out, nil
}
