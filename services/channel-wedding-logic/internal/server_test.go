package internal

import (
    "context"
    "fmt"
    "github.com/golang/mock/gomock"
    "gitlab.ttyuyin.com/bizFund/bizFund/pkg/protocol"
    channelMic "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_mic"
    "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_mic_middle"
    micScheme "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_scheme_middle"
    mocks_channel "golang.52tt.com/clients/mocks/channel"
    "golang.52tt.com/clients/mocks/channelol"
    mocks_magic_spirit "golang.52tt.com/clients/mocks/magic-spirit"
    present_extra_conf "golang.52tt.com/clients/mocks/present-extra-conf"
    mocks_risk "golang.52tt.com/clients/mocks/risk-mng-api"
    mocks_userBlackListService "golang.52tt.com/clients/mocks/user-black-list"
    mocks_profile "golang.52tt.com/clients/mocks/user-profile-api"
    mocks_userpresent "golang.52tt.com/clients/mocks/userpresent-go"
    mocks_usual_device "golang.52tt.com/clients/mocks/usual-device"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    channel_wedding "golang.52tt.com/protocol/services/channel-wedding"
    channel_wedding_conf "golang.52tt.com/protocol/services/channel-wedding-conf"
    channel_wedding_minigame "golang.52tt.com/protocol/services/channel-wedding-minigame"
    channel_wedding_plan "golang.52tt.com/protocol/services/channel-wedding-plan"
    virtual_image_user "golang.52tt.com/protocol/services/virtual-image-user"
    "math"
    "testing"
)

func Test_Ceil(t *testing.T) {
    fmt.Println(math.Ceil(float64(5) / 24))
    fmt.Println(math.Ceil(float64(0) / 24))
}

func TestServer_generateSignature(t *testing.T) {
    url := "https://testing-go-api.ttyuyin.com/obs-cdn/tt/wedding/2025-03-16-15-36-23.png"
    sign := generateSignature(url, 2641039)
    t.Log(sign)
}

var (
    testServer *Server

    testUid       = uint32(10083)
    testTargetUid = uint32(2)
    testCid       = uint32(1)
    ctx           = protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{
        UserID:        testUid,
        ClientType:    protocol.ClientTypeANDROID,
        ClientVersion: protocol.FormatClientVersion(6, 43, 0),
    })

    vUserClient           *virtual_image_user.MockVirtualImageUserClient
    userProfileCli        *mocks_profile.MockIClient
    userBlackCli          *mocks_userBlackListService.MockIClient
    channelCli            *mocks_channel.MockIClient
    weddingMiniGameCli    *channel_wedding_minigame.MockChannelWeddingMinigameClient
    channelWeddingConfCli *channel_wedding_conf.MockChannelWeddingConfClient
    weddingPlanCli        *channel_wedding_plan.MockChannelWeddingPlanClient
    channelWeddingCli     *channel_wedding.MockChannelWeddingClient
    userPresentCli        *mocks_userpresent.MockIClient
    riskMngApiCli         *mocks_risk.MockIClient
    middleMicCli          *channel_mic_middle.MockChannelMicMiddleClient
    channelMicCli         *channelMic.MockChannelMicClient
    micSchemeCli          *micScheme.MockChannelSchemeMiddleClient
    magicSpiritCli        *mocks_magic_spirit.MockIClient
    presentExtraConfCli   *present_extra_conf.MockIClient
    usualDeviceCli        *mocks_usual_device.MockIClient
    channelOlCli          *channelol.MockIClient
)

func initTestMgr(t *testing.T) {
    ctrl := gomock.NewController(t)

    vUserClient = virtual_image_user.NewMockVirtualImageUserClient(ctrl)
    userProfileCli = mocks_profile.NewMockIClient(ctrl)
    userBlackCli = mocks_userBlackListService.NewMockIClient(ctrl)
    channelCli = mocks_channel.NewMockIClient(ctrl)
    weddingMiniGameCli = channel_wedding_minigame.NewMockChannelWeddingMinigameClient(ctrl)
    channelWeddingConfCli = channel_wedding_conf.NewMockChannelWeddingConfClient(ctrl)
    weddingPlanCli = channel_wedding_plan.NewMockChannelWeddingPlanClient(ctrl)
    channelWeddingCli = channel_wedding.NewMockChannelWeddingClient(ctrl)
    userPresentCli = mocks_userpresent.NewMockIClient(ctrl)
    riskMngApiCli = mocks_risk.NewMockIClient(ctrl)
    middleMicCli = channel_mic_middle.NewMockChannelMicMiddleClient(ctrl)
    channelMicCli = channelMic.NewMockChannelMicClient(ctrl)
    micSchemeCli = micScheme.NewMockChannelSchemeMiddleClient(ctrl)
    magicSpiritCli = mocks_magic_spirit.NewMockIClient(ctrl)
    presentExtraConfCli = present_extra_conf.NewMockIClient(ctrl)
    usualDeviceCli = mocks_usual_device.NewMockIClient(ctrl)
    channelOlCli = channelol.NewMockIClient(ctrl)

    testServer = &Server{
        //dyConfig: nil,
        //localCache:             nil,
        headImageCli:           nil,
        miniGameCli:            weddingMiniGameCli,
        channelCli:             channelCli,
        riskMngApiCli:          riskMngApiCli,
        micMiddleCli:           middleMicCli,
        userProfile:            userProfileCli,
        micSchemeCli:           micSchemeCli,
        channelMicCli:          channelMicCli,
        weddingCli:             channelWeddingCli,
        headDynamicImageCli:    nil,
        virtualImageCli:        vUserClient,
        channelOlCli:           channelOlCli,
        magicSpiritCli:         magicSpiritCli,
        userPresentCli:         userPresentCli,
        presentExtraConfClient: presentExtraConfCli,
        UsualDeviceClient:      usualDeviceCli,
        channelStatCli:         nil,
        weddingPlanCli:         weddingPlanCli,
        censorCli:              nil,
        userOnlineCli:          nil,
        fellowCli:              nil,
        channelWeddingConfCli:  channelWeddingConfCli,
        pushCli:                nil,
    }
}

func Test_getPage(t *testing.T) {
    type args struct {
        page    uint32
        listLen uint32
    }
    tests := []struct {
        name         string
        args         args
        wantTruePage uint32
        wantNextPage uint32
    }{
        {
            name: "Test with page 1 and list length 3",
            args: args{
                page:    1,
                listLen: 3,
            },
            wantTruePage: 1,
            wantNextPage: 2,
        },
        {
            name: "Test with page 2 and list length 3",
            args: args{
                page:    2,
                listLen: 3,
            },
            wantTruePage: 2,
            wantNextPage: 0,
        },
        {
            name: "Test with page 3 and list length 3",
            args: args{
                page:    3,
                listLen: 3,
            },
            wantTruePage: 0,
            wantNextPage: 1,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            gotTruePage, gotNextPage := getPage(tt.args.page, tt.args.listLen)
            if gotTruePage != tt.wantTruePage {
                t.Errorf("getPage() gotTruePage = %v, want %v", gotTruePage, tt.wantTruePage)
            }
            if gotNextPage != tt.wantNextPage {
                t.Errorf("getPage() gotNextPage = %v, want %v", gotNextPage, tt.wantNextPage)
            }
        })
    }
}
