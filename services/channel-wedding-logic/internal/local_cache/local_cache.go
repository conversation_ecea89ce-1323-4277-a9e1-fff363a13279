package local_cache

import (
    "context"
    "golang.52tt.com/clients/channel-stats"
    "golang.52tt.com/pkg/log"
    channel_wedding "golang.52tt.com/protocol/services/channel-wedding"
    channel_wedding_conf "golang.52tt.com/protocol/services/channel-wedding-conf"
    channel_wedding_plan "golang.52tt.com/protocol/services/channel-wedding-plan"
    "golang.52tt.com/services/channel-wedding-logic/internal/conf"
    "golang.52tt.com/services/tt-rev/esport/common/collection/transform"
    "sort"
    "sync"
    "time"
)

type WeddingHallItemCache struct {
    WeddingPlanId       uint32
    StartTs             uint32
    WeddingId           uint32
    ThemeId             uint32
    ChannelId           uint32
    ChannelHotValue     int64
    ChannelWeddingLevel uint32
    BrideUid            uint32
    GroomUid            uint32
    BridesmaidUidList   []uint32
    GroomsmanUidList    []uint32
    JoinUidSet          map[uint32]struct{}
    IsHot               bool
}

type SimpleWeddingPlanCache struct {
    BrideUid  uint32
    GroomUid  uint32
    ChannelId uint32
    PlanId    uint32
}

type LocalCache struct {
    bc             *conf.BusinessDyConf
    weddingPlanCli channel_wedding_plan.ChannelWeddingPlanClient
    weddingCli     channel_wedding.ChannelWeddingClient
    weddingConfCli channel_wedding_conf.ChannelWeddingConfClient
    channelStatCli channelstats.IClient

    sync.RWMutex
    goingPaidList   []*WeddingHallItemCache
    goingFreeList   []*WeddingHallItemCache
    comingPaidList  []*WeddingHallItemCache
    comingFreeList  []*WeddingHallItemCache
    todayComingList []*SimpleWeddingPlanCache // 今天即将到来的婚礼列表
    themeCfgMap     map[uint32]*channel_wedding_conf.ThemeCfg
}

func NewLocalCache(bc *conf.BusinessDyConf,
    weddingPlanCli channel_wedding_plan.ChannelWeddingPlanClient,
    weddingCli channel_wedding.ChannelWeddingClient,
    weddingConfCli channel_wedding_conf.ChannelWeddingConfClient,
    channelStatCli channelstats.IClient) *LocalCache {
    s := &LocalCache{
        bc:             bc,
        weddingPlanCli: weddingPlanCli,
        weddingCli:     weddingCli,
        weddingConfCli: weddingConfCli,
        channelStatCli: channelStatCli,
        goingPaidList:  make([]*WeddingHallItemCache, 0),
        goingFreeList:  make([]*WeddingHallItemCache, 0),
        comingPaidList: make([]*WeddingHallItemCache, 0),
        comingFreeList: make([]*WeddingHallItemCache, 0),
        themeCfgMap:    make(map[uint32]*channel_wedding_conf.ThemeCfg),
    }

    s.updateGoingWeddingHallList()
    go intervalFunc(10*time.Second, s.updateGoingWeddingHallList)
    s.updateComingWeddingHallList()
    go intervalFunc(30*time.Second, s.updateComingWeddingHallList)
    s.updateThemeCfg()
    go intervalFunc(30*time.Second, s.updateThemeCfg)
    s.updateTodayComingWeddingHallList()
    go intervalFunc(10*time.Second, s.updateTodayComingWeddingHallList)
    return s
}

func intervalFunc(interval time.Duration, f func()) {
    ticker := time.NewTicker(interval)
    defer ticker.Stop()
    for range ticker.C {
        f()
    }
}

func (c *LocalCache) updateTodayComingWeddingHallList() {
    comingList := make([]*SimpleWeddingPlanCache, 0)
    ctx := context.Background()
    list, err := c.weddingPlanCli.GetTodayAllComingWeddingList(ctx, &channel_wedding_plan.GetTodayAllComingWeddingListRequest{})
    if err != nil {
        log.ErrorWithCtx(ctx, "updateTodayComingWeddingHallList GetTodayAllComingWeddingList err: %v", err)
        return
    }
    for _, item := range list.GetWeddingList() {
        comingList = append(comingList, &SimpleWeddingPlanCache{
            BrideUid:  item.GetBrideUid(),
            GroomUid:  item.GetGroomUid(),
            ChannelId: item.GetReserveInfo().GetChannelId(),
            PlanId:    item.GetWeddingPlanId(),
        })
    }

    // 原子替换不加锁
    c.todayComingList = comingList
}

func (c *LocalCache) updateGoingWeddingHallList() {
    ctx := context.Background()
    goingPaidList := make([]*WeddingHallItemCache, 0)
    goingFreeList := make([]*WeddingHallItemCache, 0)

    // 加载数据
    pageSize := 100
    weedingSet := make(map[uint32]bool)
    for pageNum := 1; pageNum <= 100; pageNum++ {
        pageRsp, err := c.weddingCli.PageGetGoingWeddingList(ctx, &channel_wedding.PageGetGoingWeddingListReq{
            PageNum:  uint32(pageNum),
            PageSize: uint32(pageSize),
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "updateGoingWeddingHallList PageGetGoingWeddingList err: %v", err)
            return
        }

        hotMap, err := c.getChannelHotValueMap(ctx, pageRsp.GetWeddingList())
        if err != nil {
            log.ErrorWithCtx(ctx, "updateGoingWeddingHallList getChannelHotValueMap err: %v", err)
            return
        }

        planInfoMap, err := c.getWeddingPlanInfoMap(ctx, pageRsp.GetWeddingList())
        if err != nil {
            log.ErrorWithCtx(ctx, "updateGoingWeddingHallList getWeddingPlanInfoMap err: %v", err)
            return
        }

        for _, item := range pageRsp.GetWeddingList() {
            planInfo, ok := planInfoMap[item.GetWeddingPlanId()]
            if !ok {
                continue
            }
            if weedingSet[item.GetWeddingPlanId()] {
                continue
            }
            weedingSet[item.GetWeddingPlanId()] = true

            joinUidSet := make(map[uint32]struct{})
            for _, uid := range append(planInfo.BridesmaidUidList, planInfo.GroomsmanUidList...) {
                joinUidSet[uid] = struct{}{}
            }
            result := &WeddingHallItemCache{
                WeddingPlanId:       item.WeddingPlanId,
                WeddingId:           item.WeddingId,
                ThemeId:             item.ThemeId,
                ChannelId:           item.ChannelId,
                ChannelHotValue:     hotMap[item.ChannelId],
                ChannelWeddingLevel: item.CurrLevel,
                BrideUid:            planInfo.BrideUid,
                GroomUid:            planInfo.GroomUid,
                JoinUidSet:          joinUidSet,
                BridesmaidUidList:   planInfo.GetBridesmaidUidList(),
                GroomsmanUidList:    planInfo.GetGroomsmanUidList(),
                IsHot:               planInfo.IsHot,
            }
            if item.GetThemeType() == uint32(channel_wedding.WeddingThemeType_WEDDING_THEME_TYPE_FREE) {
                goingFreeList = append(goingFreeList, result)
            } else {
                goingPaidList = append(goingPaidList, result)
            }
        }
        if !pageRsp.GetHasMore() {
            break
        }
    }

    // 粗排
    sort.Slice(goingPaidList, func(i, j int) bool {
        return compareGoingItem(goingPaidList[i], goingPaidList[j])
    })
    sort.Slice(goingFreeList, func(i, j int) bool {
        return compareGoingItem(goingFreeList[i], goingFreeList[j])
    })

    // debug日志
    log.DebugWithCtx(ctx, "updateGoingWeddingHallList len(goingPaidList): %d, len(goingFreeList): %d", len(goingPaidList), len(goingFreeList))

    c.Lock()
    defer c.Unlock()
    c.goingPaidList = goingPaidList
    c.goingFreeList = goingFreeList
}

func (c *LocalCache) getChannelHotValueMap(ctx context.Context, goingList []*channel_wedding.GoingWeddingInfo) (map[uint32]int64, error) {
    if len(goingList) == 0 {
        return nil, nil
    }
    channelList := transform.Map(goingList, func(item *channel_wedding.GoingWeddingInfo) uint32 { return item.ChannelId })
    return c.channelStatCli.BatchGetChannelHotValue(ctx, channelList)
}

func (c *LocalCache) getWeddingPlanInfoMap(ctx context.Context, goingList []*channel_wedding.GoingWeddingInfo) (map[uint32]*channel_wedding_plan.WeddingPlanInfo, error) {
    if len(goingList) == 0 {
        return nil, nil
    }
    planList := transform.Map(goingList, func(item *channel_wedding.GoingWeddingInfo) uint32 { return item.WeddingPlanId })
    rsp, err := c.weddingPlanCli.BatGetWeddingInfoById(ctx, &channel_wedding_plan.BatGetWeddingInfoByIdRequest{WeddingPlanIdList: planList})
    return rsp.GetWeddingInfoMap(), err
}

func compareGoingItem(a, b *WeddingHallItemCache) bool {
    // 先按照ChannelHotValue排序，相等时再按照ChannelWeddingLevel排序，相等时再按照WeddingPlanId排序
    if a.ChannelHotValue == b.ChannelHotValue {
        if a.ChannelWeddingLevel == b.ChannelWeddingLevel {
            return a.WeddingPlanId < b.WeddingPlanId
        }
        return a.ChannelWeddingLevel > b.ChannelWeddingLevel
    }
    return a.ChannelHotValue > b.ChannelHotValue
}

func (c *LocalCache) updateComingWeddingHallList() {
    ctx := context.Background()
    comingPaidList := make([]*WeddingHallItemCache, 0)
    comingFreeList := make([]*WeddingHallItemCache, 0)

    // 分页加载数据
    pageSize := 100
    nowTs := time.Now().Unix()
    planSet := make(map[uint32]bool)
    for pageNum := 1; pageNum <= 100; pageNum++ {
        pageRsp, err := c.weddingPlanCli.PageGetComingWeddingList(ctx, &channel_wedding_plan.PageGetComingWeddingListRequest{
            PageNum:  uint32(pageNum),
            PageSize: uint32(pageSize),
            NowTs:    nowTs,
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "updateComingWeddingHallList PageGetComingWeddingList err: %v", err)
            return
        }
        for _, item := range pageRsp.GetWeddingList() {
            if planSet[item.GetWeddingPlanId()] {
                continue
            }
            planSet[item.GetWeddingPlanId()] = true
            if item.GetIsThemeFree() {
                comingFreeList = append(comingFreeList, &WeddingHallItemCache{
                    WeddingPlanId: item.GetWeddingPlanId(),
                    StartTs:       item.GetReserveInfo().GetStartTs(),
                })
            } else {
                comingPaidList = append(comingPaidList, &WeddingHallItemCache{
                    WeddingPlanId: item.GetWeddingPlanId(),
                    StartTs:       item.GetReserveInfo().GetStartTs(),
                    IsHot:         item.GetIsHot(),
                })
            }
        }
        if !pageRsp.GetHasMore() {
            break
        }
    }

    // 粗排
    sort.Slice(comingPaidList, func(i, j int) bool {
        return compareComingItem(comingPaidList[i], comingPaidList[j])
    })
    sort.Slice(comingFreeList, func(i, j int) bool {
        return compareComingItem(comingFreeList[i], comingFreeList[j])
    })

    // debug日志
    log.DebugWithCtx(ctx, "updateComingWeddingHallList len(comingPaidList): %d, len(comingFreeList): %d", len(comingPaidList), len(comingFreeList))

    // 只保留前面N条数据
    comingListMaxSize := c.bc.GetConfig().WeddingHallConf.WeddingHallComingListMaxSize
    if len(comingPaidList) > int(comingListMaxSize) {
        comingPaidList = comingPaidList[:comingListMaxSize]
    }
    if len(comingFreeList) > int(comingListMaxSize) {
        comingFreeList = comingFreeList[:comingListMaxSize]
    }

    c.Lock()
    defer c.Unlock()
    c.comingPaidList = comingPaidList
    c.comingFreeList = comingFreeList
}

func compareComingItem(a, b *WeddingHallItemCache) bool {
    if a.IsHot == b.IsHot {
        if a.StartTs == b.StartTs {
            return a.WeddingPlanId < b.WeddingPlanId
        }
        return a.StartTs < b.StartTs
    }
    return a.IsHot
}

func (c *LocalCache) GetWeddingHallList(isGoing, isPaid bool) []*WeddingHallItemCache {
    c.RLock()
    defer c.RUnlock()

    if isGoing && isPaid {
        return c.goingPaidList
    } else if isGoing && !isPaid {
        return c.goingFreeList
    } else if !isGoing && isPaid {
        return c.comingPaidList
    } else {
        return c.comingFreeList
    }
}

func (c *LocalCache) GetTodayComingWeddingHallList() []*SimpleWeddingPlanCache {
    c.RLock()
    defer c.RUnlock()

    return c.todayComingList
}

func (c *LocalCache) updateThemeCfg() {
    ctx := context.Background()

    rsp, err := c.weddingConfCli.GetThemeCfgList(ctx, &channel_wedding_conf.GetThemeCfgListReq{})
    if err != nil {
        log.ErrorWithCtx(ctx, "updateThemeCfg GetThemeCfgList err: %v", err)
        return
    }
    themeCfgMap := make(map[uint32]*channel_wedding_conf.ThemeCfg)
    for _, item := range rsp.GetThemeCfgList() {
        themeCfgMap[item.GetThemeId()] = item
    }

    c.Lock()
    defer c.Unlock()
    c.themeCfgMap = themeCfgMap
}

func (c *LocalCache) GetThemeCfg(themeId uint32) *channel_wedding_conf.ThemeCfg {
    c.RLock()
    defer c.RUnlock()

    return c.themeCfgMap[themeId]
}
