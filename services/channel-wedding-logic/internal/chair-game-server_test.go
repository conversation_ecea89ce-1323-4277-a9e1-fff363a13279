package internal

import (
    "context"
    "github.com/golang/mock/gomock"
    "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_mic"
    micMiddle "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_mic_middle"
    micScheme "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_scheme_middle"
    "golang.52tt.com/protocol/app"
    "golang.52tt.com/protocol/app/channel_wedding_logic"
    channel_wedding "golang.52tt.com/protocol/services/channel-wedding"
    channel_wedding_minigame "golang.52tt.com/protocol/services/channel-wedding-minigame"
    channel_wedding_plan "golang.52tt.com/protocol/services/channel-wedding-plan"
    channel "golang.52tt.com/protocol/services/channelsvr"
    risk_mng_api "golang.52tt.com/protocol/services/risk-mng-api"
    userpresent_go "golang.52tt.com/protocol/services/userpresent-go"
    usual_device_svr "golang.52tt.com/protocol/services/usual-device-svr"
    virtual_image_user "golang.52tt.com/protocol/services/virtual-image-user"
    "reflect"
    "testing"
    "time"
)

func TestServer_ApplyToJoinChairGame(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx context.Context
        req *channel_wedding_logic.ApplyToJoinChairGameRequest
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        want     *channel_wedding_logic.ApplyToJoinChairGameResponse
        wantErr  bool
    }{
        {
            name:     "ctx invalid",
            initFunc: nil,
            args: args{
                ctx: context.Background(),
                req: &channel_wedding_logic.ApplyToJoinChairGameRequest{
                    ChannelId: testCid,
                    IsCancel:  true,
                },
            },
            want:    &channel_wedding_logic.ApplyToJoinChairGameResponse{},
            wantErr: true,
        },
        {
            name: "不在房间内",
            initFunc: func() {
                channelOlCli.EXPECT().GetUserChannelId(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(0), nil)
            },
            args: args{
                ctx: ctx,
                req: &channel_wedding_logic.ApplyToJoinChairGameRequest{
                    ChannelId: testCid,
                    IsCancel:  false,
                },
            },
            want:    &channel_wedding_logic.ApplyToJoinChairGameResponse{},
            wantErr: true,
        },
        {
            name: "未佩戴任何虚拟形象",
            initFunc: func() {
                channelOlCli.EXPECT().GetUserChannelId(gomock.Any(), gomock.Any(), gomock.Any()).Return(testCid, nil)
                vUserClient.EXPECT().BatchGetUserInuseItemInfo(gomock.Any(), gomock.Any()).Return(&virtual_image_user.BatchGetUserInuseItemInfoResp{}, nil)
            },
            args: args{
                ctx: ctx,
                req: &channel_wedding_logic.ApplyToJoinChairGameRequest{
                    ChannelId: testCid,
                    IsCancel:  false,
                },
            },
            want:    &channel_wedding_logic.ApplyToJoinChairGameResponse{},
            wantErr: true,
        },
        {
            name: "主持人不可报名",
            initFunc: func() {
                channelOlCli.EXPECT().GetUserChannelId(gomock.Any(), gomock.Any(), gomock.Any()).Return(testCid, nil)
                vUserClient.EXPECT().BatchGetUserInuseItemInfo(gomock.Any(), gomock.Any()).Return(&virtual_image_user.BatchGetUserInuseItemInfoResp{
                    UserInuseItemInfo: []*virtual_image_user.UserInuseItemInfo{
                        {
                            Uid: testUid,
                            Items: []*virtual_image_user.InuseItemInfo{
                                {
                                    CfgId: 1,
                                },
                            },
                        },
                    },
                }, nil)
                channelMicCli.EXPECT().GetMicrList(gomock.Any(), gomock.Any()).Return(&channel_mic.GetMicrListResp{
                    ChannelId: testCid,
                    AllMicList: []*channel_mic.MicrSpaceInfo{
                        {
                            MicId:  1,
                            MicUid: testUid, // 主持人
                        },
                    },
                }, nil)

            },
            args: args{
                ctx: ctx,
                req: &channel_wedding_logic.ApplyToJoinChairGameRequest{
                    ChannelId: testCid,
                    IsCancel:  false,
                },
            },
            want:    &channel_wedding_logic.ApplyToJoinChairGameResponse{},
            wantErr: true,
        },
        {
            name: "不在婚礼中",
            initFunc: func() {
                channelOlCli.EXPECT().GetUserChannelId(gomock.Any(), gomock.Any(), gomock.Any()).Return(testCid, nil)
                vUserClient.EXPECT().BatchGetUserInuseItemInfo(gomock.Any(), gomock.Any()).Return(&virtual_image_user.BatchGetUserInuseItemInfoResp{
                    UserInuseItemInfo: []*virtual_image_user.UserInuseItemInfo{
                        {
                            Uid: testUid,
                            Items: []*virtual_image_user.InuseItemInfo{
                                {
                                    CfgId: 1,
                                },
                            },
                        },
                    },
                }, nil)
                channelMicCli.EXPECT().GetMicrList(gomock.Any(), gomock.Any()).Return(&channel_mic.GetMicrListResp{
                    ChannelId: testCid,
                    AllMicList: []*channel_mic.MicrSpaceInfo{
                        {
                            MicId:  1,
                            MicUid: testUid + 1, // 主持人
                        },
                    },
                }, nil)
                channelWeddingCli.EXPECT().GetChannelWeddingInfo(gomock.Any(), gomock.Any()).Return(&channel_wedding.GetChannelWeddingInfoResp{}, nil)
            },
            args: args{
                ctx: ctx,
                req: &channel_wedding_logic.ApplyToJoinChairGameRequest{
                    ChannelId: testCid,
                    IsCancel:  false,
                },
            },
            want:    &channel_wedding_logic.ApplyToJoinChairGameResponse{},
            wantErr: true,
        },
        {
            name: "正常申请",
            initFunc: func() {
                channelOlCli.EXPECT().GetUserChannelId(gomock.Any(), gomock.Any(), gomock.Any()).Return(testCid, nil)
                vUserClient.EXPECT().BatchGetUserInuseItemInfo(gomock.Any(), gomock.Any()).Return(&virtual_image_user.BatchGetUserInuseItemInfoResp{
                    UserInuseItemInfo: []*virtual_image_user.UserInuseItemInfo{
                        {
                            Uid: testUid,
                            Items: []*virtual_image_user.InuseItemInfo{
                                {
                                    CfgId: 1,
                                },
                            },
                        },
                    },
                }, nil)
                channelMicCli.EXPECT().GetMicrList(gomock.Any(), gomock.Any()).Return(&channel_mic.GetMicrListResp{
                    ChannelId: testCid,
                    AllMicList: []*channel_mic.MicrSpaceInfo{
                        {
                            MicId:  1,
                            MicUid: testUid + 1, // 主持人
                        },
                    },
                }, nil)
                channelWeddingCli.EXPECT().GetChannelWeddingInfo(gomock.Any(), gomock.Any()).Return(&channel_wedding.GetChannelWeddingInfoResp{
                    WeddingInfo: &channel_wedding.WeddingInfo{
                        Cid:       testCid,
                        WeddingId: 1,
                        Bride: &channel_wedding.WeddingCpMemInfo{
                            Uid: testUid + 1,
                        },
                        Groom: &channel_wedding.WeddingCpMemInfo{
                            Uid: testUid + 2,
                        },
                    },
                }, nil)

                channelCli.EXPECT().GetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channel.ChannelSimpleInfo{
                    ChannelId: &testCid,
                }, nil)

                // 风控检查
                riskMngApiCli.EXPECT().CheckHelper(gomock.Any(), gomock.Any(), gomock.Any()).Return(&risk_mng_api.CheckResp{
                    ErrCode: 0,
                }, nil)

                weddingMiniGameCli.EXPECT().ApplyToJoinChairGame(gomock.Any(), gomock.Any()).Return(&channel_wedding_minigame.ApplyToJoinChairGameResponse{}, nil)
            },
            args: args{
                ctx: ctx,
                req: &channel_wedding_logic.ApplyToJoinChairGameRequest{
                    ChannelId: testCid,
                    IsCancel:  false,
                },
            },
            want:    &channel_wedding_logic.ApplyToJoinChairGameResponse{},
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            s := testServer
            if tt.initFunc != nil {
                tt.initFunc()
            }
            got, err := s.ApplyToJoinChairGame(tt.args.ctx, tt.args.req)
            if (err != nil) != tt.wantErr {
                t.Errorf("ApplyToJoinChairGame() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("ApplyToJoinChairGame() got = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestServer_GetChairGameApplyList(t *testing.T) {
    initTestMgr(t)

    userA := &app.UserProfile{
        Uid: testUid + 1,
    }

    type args struct {
        ctx context.Context
        req *channel_wedding_logic.GetChairGameApplyListRequest
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        want     *channel_wedding_logic.GetChairGameApplyListResponse
        wantErr  bool
    }{
        {
            name: "ctx invalid",
            args: args{
                ctx: context.Background(),
                req: &channel_wedding_logic.GetChairGameApplyListRequest{},
            },
            want:    &channel_wedding_logic.GetChairGameApplyListResponse{},
            wantErr: true,
        },
        {
            name: "不在婚礼中",
            initFunc: func() {
                channelWeddingCli.EXPECT().GetChannelWeddingInfo(gomock.Any(), gomock.Any()).Return(&channel_wedding.GetChannelWeddingInfoResp{}, nil)
            },
            args: args{
                ctx: ctx,
                req: &channel_wedding_logic.GetChairGameApplyListRequest{
                    ChannelId: testCid,
                },
            },
            want:    &channel_wedding_logic.GetChairGameApplyListResponse{},
            wantErr: false,
        },
        {
            name: "applyList is nil",
            initFunc: func() {
                channelWeddingCli.EXPECT().GetChannelWeddingInfo(gomock.Any(), gomock.Any()).Return(&channel_wedding.GetChannelWeddingInfoResp{
                    WeddingInfo: &channel_wedding.WeddingInfo{
                        Cid:       testCid,
                        WeddingId: 1,
                    },
                }, nil)
                weddingMiniGameCli.EXPECT().GetChairGameApplyList(gomock.Any(), gomock.Any()).Return(&channel_wedding_minigame.GetChairGameApplyListResponse{}, nil)
            },
            args: args{
                ctx: ctx,
                req: &channel_wedding_logic.GetChairGameApplyListRequest{
                    ChannelId: testCid,
                },
            },
            want:    &channel_wedding_logic.GetChairGameApplyListResponse{},
            wantErr: false,
        },
        {
            name: "common success",
            initFunc: func() {
                channelWeddingCli.EXPECT().GetChannelWeddingInfo(gomock.Any(), gomock.Any()).Return(&channel_wedding.GetChannelWeddingInfoResp{
                    WeddingInfo: &channel_wedding.WeddingInfo{
                        Cid:       testCid,
                        WeddingId: 1,
                    },
                }, nil)

                weddingMiniGameCli.EXPECT().GetChairGameApplyList(gomock.Any(), gomock.Any()).Return(&channel_wedding_minigame.GetChairGameApplyListResponse{
                    ApplyUserList: []uint32{
                        1,
                        2,
                        3,
                        4,
                        5,
                        6,
                        testUid + 1}}, nil)

                userProfileCli.EXPECT().BatchGetUserProfileV2(gomock.Any(), gomock.Any(), gomock.Any()).Return(map[uint32]*app.UserProfile{
                    testUid + 1: userA,
                    1: {
                        Uid: 1,
                    },
                    2: {
                        Uid: 2,
                    },
                    3: {
                        Uid: 3,
                    },
                    4: {
                        Uid: 4,
                    },
                    5: {
                        Uid: 5,
                    },
                    6: {
                        Uid: 6,
                    },
                }, nil)

                weddingPlanCli.EXPECT().BatchGetWeddingRole(gomock.Any(), gomock.Any()).Return(
                    &channel_wedding_plan.BatchGetWeddingRoleResponse{
                        WeddingRoleMap: map[uint32]uint32{
                            1: uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_FRIENDS),
                            2: uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDESMAID),
                            3: uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_GROOM),
                            4: uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDES),
                            5: uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDE),
                            6: uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_UNSPECIFIED),
                        },
                    }, nil,
                )
            },
            args: args{
                ctx: ctx,
                req: &channel_wedding_logic.GetChairGameApplyListRequest{
                    ChannelId: testCid,
                },
            },
            want: &channel_wedding_logic.GetChairGameApplyListResponse{
                ApplyUserList: []*channel_wedding_logic.ChairGameUserInfo{

                    {
                        UserInfo: &app.UserProfile{
                            Uid: 3,
                        },
                        WeddingGuestType: uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_GROOM),
                    },
                    {
                        UserInfo: &app.UserProfile{
                            Uid: 5,
                        },
                        WeddingGuestType: uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDE),
                    },
                    {
                        UserInfo: &app.UserProfile{
                            Uid: 4,
                        },
                        WeddingGuestType: uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDES),
                    },
                    {
                        UserInfo: &app.UserProfile{
                            Uid: 2,
                        },
                        WeddingGuestType: uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDESMAID),
                    },
                    {
                        UserInfo: &app.UserProfile{
                            Uid: 1,
                        },
                        WeddingGuestType: uint32(channel_wedding_logic.WeddingAnimationType_WEDDING_ANIMATION_TYPE_UNSPECIFIED),
                    },
                    {
                        UserInfo: &app.UserProfile{
                            Uid: 6,
                        },
                        WeddingGuestType: uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_FRIENDS),
                    },
                    {
                        UserInfo:         userA,
                        WeddingGuestType: uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_UNSPECIFIED),
                    },
                },
                TotalApplyNum: 7,
            },
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            s := testServer
            if tt.initFunc != nil {
                tt.initFunc()
            }
            got, err := s.GetChairGameApplyList(tt.args.ctx, tt.args.req)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetChairGameApplyList() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("GetChairGameApplyList() got = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestServer_GetChairGameInfo(t *testing.T) {
    initTestMgr(t)

    gameInfo := &channel_wedding_minigame.ChairGameInfo{
        GameId:            1,
        ShowGameBeginAnim: false,
        GameProgress: &channel_wedding_minigame.ChairGameProgress{
            GameId:          1,
            CurRound:        1,
            ChairNum:        5,
            RoundStatus:     1,
            RoundPalyerUids: []uint32{1, 2, 3, 4, 5},
        },
        RewardList: []*channel_wedding_minigame.ChairGameRewardInfo{
            {
                Icon: "11",
                Name: "11",
            },
        },
        Players: []*channel_wedding_minigame.PlayerInfo{
            {
                Uid:   1,
                MicId: 1,
            },
        },
    }
    userA := &app.UserProfile{
        Uid: 1,
    }

    type args struct {
        ctx context.Context
        req *channel_wedding_logic.GetChairGameInfoRequest
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        want     *channel_wedding_logic.GetChairGameInfoResponse
        wantErr  bool
    }{
        {
            name: "ctx invalid",
            args: args{
                ctx: context.Background(),
                req: &channel_wedding_logic.GetChairGameInfoRequest{},
            },
            want:    &channel_wedding_logic.GetChairGameInfoResponse{},
            wantErr: true,
        },
        {
            name: "common success",
            initFunc: func() {
                weddingMiniGameCli.EXPECT().GetChairGameInfo(gomock.Any(), gomock.Any()).Return(&channel_wedding_minigame.GetChairGameInfoResponse{
                    GameInfo: gameInfo,
                }, nil)
                userProfileCli.EXPECT().BatchGetUserProfileV2(gomock.Any(), gomock.Any(), gomock.Any()).Return(map[uint32]*app.UserProfile{
                    1: userA,
                }, nil)
            },
            args: args{
                ctx: ctx,
                req: &channel_wedding_logic.GetChairGameInfoRequest{
                    ChannelId: testCid,
                },
            },
            want: &channel_wedding_logic.GetChairGameInfoResponse{
                GameInfo: transformGameInfo2LogicPb(map[uint32]*app.UserProfile{1: userA}, gameInfo),
            },
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            s := testServer
            if tt.initFunc != nil {
                tt.initFunc()
            }
            got, err := s.GetChairGameInfo(tt.args.ctx, tt.args.req)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetChairGameInfo() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("GetChairGameInfo() got = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestServer_GetChairGameRewardSetting(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx context.Context
        req *channel_wedding_logic.GetChairGameRewardSettingRequest
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        want     *channel_wedding_logic.GetChairGameRewardSettingResponse
        wantErr  bool
    }{
        {
            name: "ctx invalid",
            args: args{
                ctx: context.Background(),
                req: &channel_wedding_logic.GetChairGameRewardSettingRequest{},
            },
            want:    &channel_wedding_logic.GetChairGameRewardSettingResponse{},
            wantErr: true,
        },
        {
            name: "不在婚礼中",
            initFunc: func() {
                channelWeddingCli.EXPECT().GetChannelWeddingInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channel_wedding.GetChannelWeddingInfoResp{}, nil)
            },
            args: args{
                ctx: ctx,
                req: &channel_wedding_logic.GetChairGameRewardSettingRequest{
                    ChannelId: testCid,
                },
            },
            want:    &channel_wedding_logic.GetChairGameRewardSettingResponse{},
            wantErr: false,
        },
        {
            name: "获取成功",
            initFunc: func() {
                channelWeddingCli.EXPECT().GetChannelWeddingInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channel_wedding.GetChannelWeddingInfoResp{
                    WeddingInfo: &channel_wedding.WeddingInfo{
                        Cid:       testCid,
                        WeddingId: 1,
                    },
                }, nil)
                weddingMiniGameCli.EXPECT().GetChairGameReward(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channel_wedding_minigame.GetChairGameRewardResponse{
                    RewardList: []*channel_wedding_minigame.ChairGameRewardInfo{
                        {
                            Icon:       "111",
                            Name:       "11",
                            RewardUnit: "1",
                        },
                    },
                    RewardSetting: &channel_wedding_minigame.ChairGameRewardSetting{
                        GiftType:         []uint32{0},
                        SupportMagicGift: false,
                        PriceLimit:       10000,
                    },
                    SponsorUid: testUid,
                }, nil)
            },
            args: args{
                ctx: ctx,
                req: &channel_wedding_logic.GetChairGameRewardSettingRequest{
                    ChannelId: testCid,
                },
            },
            want: &channel_wedding_logic.GetChairGameRewardSettingResponse{
                RewardConf: &channel_wedding_logic.ChairGameRewardSetting{
                    GiftType:         []uint32{0},
                    SupportMagicGift: false,
                    PriceLimit:       10000,
                },
                RewardList: []*channel_wedding_logic.ChairGameRewardInfo{
                    {
                        Icon:       "111",
                        Name:       "11",
                        RewardUnit: "1",
                    },
                },
                SponsorUid: testUid,
            },
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            s := testServer
            if tt.initFunc != nil {
                tt.initFunc()
            }
            got, err := s.GetChairGameRewardSetting(tt.args.ctx, tt.args.req)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetChairGameRewardSetting() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("GetChairGameRewardSetting() got = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestServer_GrabChair(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx context.Context
        req *channel_wedding_logic.GrabChairRequest
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        want     *channel_wedding_logic.GrabChairResponse
        wantErr  bool
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            s := testServer
            if tt.initFunc != nil {
                tt.initFunc()
            }
            got, err := s.GrabChair(tt.args.ctx, tt.args.req)
            if (err != nil) != tt.wantErr {
                t.Errorf("GrabChair() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("GrabChair() got = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestServer_PlayerFailToHoldMicHandle(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx       context.Context
        uid       uint32
        cid       uint32
        weddingId uint32
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            s := testServer
            if tt.initFunc != nil {
                tt.initFunc()
            }
            s.PlayerFailToHoldMicHandle(tt.args.ctx, tt.args.uid, tt.args.cid, tt.args.weddingId)
        })
    }
}

func TestServer_SetChairGameReward(t *testing.T) {
    initTestMgr(t)

    giftEffEnd := time.Now().AddDate(0, 0, 1)

    type args struct {
        ctx context.Context
        req *channel_wedding_logic.SetChairGameRewardRequest
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        want     *channel_wedding_logic.SetChairGameRewardResponse
        wantErr  bool
    }{
        {
            name: "ctx invalid",
            initFunc: func() {
            },
            args: args{
                ctx: context.Background(),
                req: &channel_wedding_logic.SetChairGameRewardRequest{
                    ChannelId: testCid,
                },
            },
            want:    &channel_wedding_logic.SetChairGameRewardResponse{},
            wantErr: true,
        },
        {
            name: "非新人设置奖励",
            initFunc: func() {
                channelWeddingCli.EXPECT().GetChannelWeddingInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channel_wedding.GetChannelWeddingInfoResp{}, nil)
            },
            args: args{
                ctx: ctx,
                req: &channel_wedding_logic.SetChairGameRewardRequest{
                    ChannelId: testCid,
                },
            },
            want:    &channel_wedding_logic.SetChairGameRewardResponse{},
            wantErr: true,
        },
        {
            name: "当前不支持配置幸运礼物",
            initFunc: func() {
                channelWeddingCli.EXPECT().GetChannelWeddingInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channel_wedding.GetChannelWeddingInfoResp{
                    WeddingInfo: &channel_wedding.WeddingInfo{
                        Cid:       testCid,
                        WeddingId: 1,
                        Bride: &channel_wedding.WeddingCpMemInfo{
                            Uid: testUid,
                        },
                        Groom: &channel_wedding.WeddingCpMemInfo{
                            Uid: testUid + 1,
                        },
                    },
                }, nil)
                weddingMiniGameCli.EXPECT().GetChairGameReward(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channel_wedding_minigame.GetChairGameRewardResponse{
                    RewardList: nil,
                    RewardSetting: &channel_wedding_minigame.ChairGameRewardSetting{
                        GiftType:         []uint32{0},
                        SupportMagicGift: false,
                        PriceLimit:       10000,
                    },
                    SponsorUid: 0,
                }, nil)
            },
            args: args{
                ctx: ctx,
                req: &channel_wedding_logic.SetChairGameRewardRequest{
                    ChannelId:   testCid,
                    IsMagicGift: true,
                },
            },
            want:    &channel_wedding_logic.SetChairGameRewardResponse{},
            wantErr: true,
        },
        {
            name: "普通礼物-设置成功",
            initFunc: func() {
                channelWeddingCli.EXPECT().GetChannelWeddingInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channel_wedding.GetChannelWeddingInfoResp{
                    WeddingInfo: &channel_wedding.WeddingInfo{
                        Cid:       testCid,
                        WeddingId: 1,
                        Bride: &channel_wedding.WeddingCpMemInfo{
                            Uid: testUid,
                        },
                    }}, nil)
                weddingMiniGameCli.EXPECT().GetChairGameReward(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channel_wedding_minigame.GetChairGameRewardResponse{
                    RewardList: nil,
                    RewardSetting: &channel_wedding_minigame.ChairGameRewardSetting{
                        GiftType:         []uint32{0},
                        SupportMagicGift: false,
                        PriceLimit:       10000,
                    },
                    SponsorUid: 0,
                }, nil)

                userPresentCli.EXPECT().GetPresentConfigById(gomock.Any(), gomock.Any()).Return(&userpresent_go.GetPresentConfigByIdOldResp{
                    ItemConfig: &userpresent_go.StPresentItemConfig{
                        ItemId:    1,
                        Name:      "1",
                        IconUrl:   "1",
                        Price:     10,
                        EffectEnd: uint32(giftEffEnd.Unix()),
                        PriceType: uint32(2), // T豆礼物
                    },
                }, nil)

                // 风控检查
                usualDeviceCli.EXPECT().CheckUsualDevice(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&usual_device_svr.CheckUsualDeviceResp{
                    Result: true,
                }, nil)
                riskMngApiCli.EXPECT().CheckHelper(gomock.Any(), gomock.Any(), gomock.Any()).Return(&risk_mng_api.CheckResp{
                    ErrCode: 0,
                }, nil)

                weddingMiniGameCli.EXPECT().SetChairGameReward(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channel_wedding_minigame.SetChairGameRewardResponse{}, nil)
            },
            args: args{
                ctx: ctx,
                req: &channel_wedding_logic.SetChairGameRewardRequest{
                    ChannelId:    testCid,
                    AwardGiftId:  1,
                    GiftType:     1,
                    AwardGiftNum: 1,
                    IsMagicGift:  false,
                },
            },
            want:    &channel_wedding_logic.SetChairGameRewardResponse{},
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            s := testServer
            if tt.initFunc != nil {
                tt.initFunc()
            }
            got, err := s.SetChairGameReward(tt.args.ctx, tt.args.req)
            if (err != nil) != tt.wantErr {
                t.Errorf("SetChairGameReward() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("SetChairGameReward() got = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestServer_SetChairGameToNextRound(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx context.Context
        req *channel_wedding_logic.SetChairGameToNextRoundRequest
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        want     *channel_wedding_logic.SetChairGameToNextRoundResponse
        wantErr  bool
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            s := testServer
            if tt.initFunc != nil {
                tt.initFunc()
            }
            got, err := s.SetChairGameToNextRound(tt.args.ctx, tt.args.req)
            if (err != nil) != tt.wantErr {
                t.Errorf("SetChairGameToNextRound() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("SetChairGameToNextRound() got = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestServer_StartChairGame(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx context.Context
        req *channel_wedding_logic.StartChairGameRequest
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        want     *channel_wedding_logic.StartChairGameResponse
        wantErr  bool
    }{
        {
            name: "ctx invalid",
            initFunc: func() {
            },
            args: args{
                ctx: context.Background(),
                req: &channel_wedding_logic.StartChairGameRequest{},
            },
            want:    &channel_wedding_logic.StartChairGameResponse{},
            wantErr: true,
        },
        {
            name:     "玩家数量不够",
            initFunc: func() {},
            args: args{
                ctx: ctx,
                req: &channel_wedding_logic.StartChairGameRequest{
                    ChannelId:  testCid,
                    PlayerUids: []uint32{1, 2},
                },
            },
            want:    &channel_wedding_logic.StartChairGameResponse{},
            wantErr: true,
        },
        {
            name: "当前婚礼不支持抢椅子",
            initFunc: func() {
                channelWeddingCli.EXPECT().GetChannelWeddingInfo(gomock.Any(), gomock.Any()).Return(&channel_wedding.GetChannelWeddingInfoResp{}, nil)
            },
            args: args{
                ctx: ctx,
                req: &channel_wedding_logic.StartChairGameRequest{
                    ChannelId:  testCid,
                    PlayerUids: []uint32{1, 2, 3, 4, 5},
                },
            },
            want:    &channel_wedding_logic.StartChairGameResponse{},
            wantErr: true,
        },
        {
            name: "已报名的用户不足3人",
            initFunc: func() {
                channelWeddingCli.EXPECT().GetChannelWeddingInfo(gomock.Any(), gomock.Any()).Return(&channel_wedding.GetChannelWeddingInfoResp{
                    WeddingInfo: &channel_wedding.WeddingInfo{
                        Cid:            testCid,
                        WeddingId:      1,
                        ChairGameEntry: true,
                    },
                }, nil)

                weddingMiniGameCli.EXPECT().StartNewGamePreCheck(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channel_wedding_minigame.StartChairGameResponse{
                    Players: []uint32{1, 2},
                }, nil)
            },
            args: args{
                ctx: ctx,
                req: &channel_wedding_logic.StartChairGameRequest{
                    ChannelId:  testCid,
                    PlayerUids: []uint32{1, 2, 3, 4, 5},
                },
            },
            want:    &channel_wedding_logic.StartChairGameResponse{},
            wantErr: true,
        },
        {
            name: "开始抢椅子成功",
            initFunc: func() {
                channelWeddingCli.EXPECT().GetChannelWeddingInfo(gomock.Any(), gomock.Any()).Return(&channel_wedding.GetChannelWeddingInfoResp{
                    WeddingInfo: &channel_wedding.WeddingInfo{
                        Cid:            testCid,
                        WeddingId:      1,
                        ChairGameEntry: true,
                    },
                }, nil)
                weddingMiniGameCli.EXPECT().StartNewGamePreCheck(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channel_wedding_minigame.StartChairGameResponse{
                    Players: []uint32{1, 2, 3, 4, 5},
                }, nil)

                micSchemeCli.EXPECT().GetCurMicSize(gomock.Any(), gomock.Any()).Return(&micScheme.GetCurMicSizeResp{
                    MicSize: 20,
                }, nil)
                channelMicCli.EXPECT().GetMicrList(gomock.Any(), gomock.Any()).Return(&channel_mic.GetMicrListResp{
                    ChannelId: testCid,
                    AllMicList: []*channel_mic.MicrSpaceInfo{
                        {
                            MicId:  2,
                            MicUid: 1,
                        },
                        {
                            MicId:    12,
                            MicState: 1,
                            MicUid:   100, // 需要被踢下麦的用户
                        },
                    },
                }, nil).AnyTimes()
                // 解锁游戏麦位
                middleMicCli.EXPECT().SetMicStatus(gomock.Any(), gomock.Any()).Return(&micMiddle.SetMicStatusResp{}, nil)
                middleMicCli.EXPECT().KickOutMic(gomock.Any(), gomock.Any()).Return(&micMiddle.KickOutMicResp{}, nil)
                middleMicCli.EXPECT().HoldMic(gomock.Any(), gomock.Any()).Return(&micMiddle.HoldMicResp{
                    AllMicList: []*micMiddle.MicSpaceInfo{
                        {
                            MicId:  2,
                            MicUid: 1,
                        },
                        {
                            MicId:  12,
                            MicUid: 2,
                        },
                        {
                            MicId:  13,
                            MicUid: 3,
                        },
                        {
                            MicId:  14,
                            MicUid: 4,
                        },
                    },
                }, nil).AnyTimes()

                weddingMiniGameCli.EXPECT().StartChairGame(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channel_wedding_minigame.StartChairGameResponse{}, nil)
            },
            args: args{
                ctx: ctx,
                req: &channel_wedding_logic.StartChairGameRequest{
                    ChannelId:  testCid,
                    PlayerUids: []uint32{1, 2, 3, 4, 5},
                },
            },
            want:    &channel_wedding_logic.StartChairGameResponse{},
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            s := testServer
            if tt.initFunc != nil {
                tt.initFunc()
            }
            got, err := s.StartChairGame(tt.args.ctx, tt.args.req)
            if (err != nil) != tt.wantErr {
                t.Errorf("StartChairGame() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("StartChairGame() got = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestServer_StartGrabChair(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx context.Context
        req *channel_wedding_logic.StartGrabChairRequest
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        want     *channel_wedding_logic.StartGrabChairResponse
        wantErr  bool
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            s := testServer
            if tt.initFunc != nil {
                tt.initFunc()
            }
            got, err := s.StartGrabChair(tt.args.ctx, tt.args.req)
            if (err != nil) != tt.wantErr {
                t.Errorf("StartGrabChair() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("StartGrabChair() got = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestServer_kickOutMicSpace(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx       context.Context
        channelId uint32
        uidList   []uint32
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        {
            name: "common success",
            initFunc: func() {
                channelMicCli.EXPECT().GetMicrList(gomock.Any(), gomock.Any()).Return(&channel_mic.GetMicrListResp{
                    ChannelId: testCid,
                    AllMicList: []*channel_mic.MicrSpaceInfo{
                        {
                            MicId:  12,
                            MicUid: 1,
                        },
                    },
                }, nil)
                middleMicCli.EXPECT().KickOutMic(gomock.Any(), gomock.Any()).Return(&micMiddle.KickOutMicResp{}, nil)
            },
            args: args{
                ctx:       ctx,
                channelId: testCid,
                uidList:   []uint32{1},
            },
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            s := testServer
            if tt.initFunc != nil {
                tt.initFunc()
            }
            if err := s.kickOutMicSpace(tt.args.ctx, tt.args.channelId, tt.args.uidList); (err != nil) != tt.wantErr {
                t.Errorf("kickOutMicSpace() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}
