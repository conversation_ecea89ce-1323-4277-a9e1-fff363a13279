#!/bin/bash

quicksilver-cli test interface ./cache
quicksilver-cli test interface ./store
quicksilver-cli test interface ./mgr
mockgen -destination=mocks/mock_store.go -package=mocks golang.52tt.com/services/fellow-level-award/internal/store IStore
mockgen -destination=mocks/mock_cache.go -package=mocks golang.52tt.com/services/fellow-level-award/internal/cache ICache
mockgen -destination=mocks/mock_conf.go -package=mocks golang.52tt.com/services/fellow-level-award/internal/conf IBusinessConfManager
mockgen -destination=mocks/mock_mgr.go -package=mocks golang.52tt.com/services/fellow-level-award/internal/mgr IMgr
mockgen -destination=mocks/mock_rpc.go -package=mocks golang.52tt.com/services/fellow-level-award/internal/rpc IRpcClients


