package kafka_prod

import (
	pbLogic "golang.52tt.com/protocol/app/channel-live-logic"
)

type IKafkaProduce interface {
	Close()
	Init(addr []string, clientId string) error
	ProduceChannelLiveEvent(uidList []uint32, mapUid2Cid map[uint32]uint32, liveStatus pbLogic.EnumChannelLiveStatus, pkStatus pbLogic.EnumChannelLivePKStatus) error
	ProduceMultiPkEvent(pkInfo *pbLogic.MultiPkInfo, mapCid2SendUserList map[uint32][]uint32, mapCid2SendUserScore map[string]float64, firstKillUid uint32, mapFirstUid2Cid map[uint32]uint32) error
}
