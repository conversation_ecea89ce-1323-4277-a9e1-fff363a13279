package internal

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"github.com/fullstorydev/grpcurl"
	"github.com/golang/protobuf/jsonpb"
	"github.com/jhump/protoreflect/grpcreflect"
	"github.com/mark3labs/mcp-go/mcp"
	mcpserver "github.com/mark3labs/mcp-go/server"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/http"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/web"
	"golang.52tt.com/protocol/common/status"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/reflection/grpc_reflection_v1alpha"
	"io"
	"time"
)

type InvokeGrpcurlReq struct {
	ReqJson string `json:"req_json"`
	Method  string `json:"method"`
	Address string `json:"address"`
	SubEnv  string `json:"sub_env"`
	Headers string `json:"headers"`
}

type InvokeGrpcurlRsp struct {
	RspJson string `json:"rsp_json"`
}

type customEventHandler struct {
	*grpcurl.DefaultEventHandler
	headers  metadata.MD
	trailers metadata.MD
}

type singleMessageSupplier struct {
	data []byte
	used bool
}

func (s *singleMessageSupplier) Supply(msg proto.MessageV1) error {
	if s.used {
		return io.EOF
	}
	s.used = true
	return jsonpb.Unmarshal(bytes.NewReader(s.data), msg)
}

func (s *Server) InvokeGrpcurl(ctx context.Context, in *InvokeGrpcurlReq) (out *InvokeGrpcurlRsp, code int32, msg string) {
	if in.Method == "" || in.Address == "" || in.ReqJson == "" {
		code = status.ErrSys
		msg = "invalid param"
		return
	}

	headers := make([]string, 0)
	if in.Headers != "" {
		kv := make(map[string]string)
		if err := json.Unmarshal([]byte(in.Headers), &kv); err != nil {
			code = status.ErrSys
			msg = fmt.Sprintf("Failed to parse headers JSON: %v", err)
			return
		}
		for k, v := range kv {
			headers = append(headers, fmt.Sprintf("%s: %s", k, v))
		}
	}
	if in.SubEnv != "" {
		headers = append(headers, fmt.Sprintf("x-qw-traffic-mark: %s", in.SubEnv))
	}

	cc, err := grpcurl.BlockingDial(ctx, "tcp", in.Address, nil,
		grpc.WithBlock(),
		grpc.WithTimeout(time.Minute),
		grpc.WithInsecure(),
	)
	if err != nil {
		code = status.ErrSys
		msg = fmt.Sprintf("Failed to create gRPC connection: %v", err)
		return
	}
	defer cc.Close()

	refClient := grpcreflect.NewClient(ctx, grpc_reflection_v1alpha.NewServerReflectionClient(cc))
	defer refClient.Reset()
	descSource := grpcurl.DescriptorSourceFromServer(ctx, refClient)

	var outputBuffer bytes.Buffer
	_, formatter, err := grpcurl.RequestParserAndFormatter(grpcurl.FormatJSON, descSource, &outputBuffer, grpcurl.FormatOptions{})
	if err != nil {
		code = status.ErrSys
		msg = fmt.Sprintf("Failed to create formatter: %v", err)
		return
	}

	handler := &customEventHandler{
		DefaultEventHandler: &grpcurl.DefaultEventHandler{
			Out:            &outputBuffer,
			Formatter:      formatter,
			VerbosityLevel: 0,
			NumResponses:   0,
			Status:         nil,
		},
	}
	reqSupplier := &singleMessageSupplier{
		data: []byte(in.ReqJson),
	}
	err = grpcurl.InvokeRPC(ctx, descSource, cc, in.Method, headers, handler, reqSupplier.Supply)
	if err != nil {
		code = status.ErrSys
		msg = fmt.Sprintf("Failed to invoke RPC: %v", err)
		return
	}

	code = int32(handler.Status.Code())
	msg = handler.Status.Message()
	out = &InvokeGrpcurlRsp{
		RspJson: outputBuffer.String(),
	}
	return
}

func (s *Server) McpWrapper4InvokeGrpcurl() (mcp.Tool, mcpserver.ToolHandlerFunc) {
	return mcp.NewTool("invoke_grpcurl",
			mcp.WithDescription(`Invokes a gRPC method using reflection`),
			mcp.WithString("req_json", mcp.Description(`JSON request payload`), mcp.Required()),
			mcp.WithString("method", mcp.Description(`Fully-qualified method name, format: package.service.method`), mcp.Required()),
			mcp.WithString("address", mcp.Description(`remote grpc server address, example: account.appsvr.svc.cluster.local:80`), mcp.Required()),
			mcp.WithString("sub_env", mcp.Description(`Sub environment for route, example: tt-qa / tt-dev-mars`)),
			mcp.WithString("headers", mcp.Description(`Optional JSON object for request-specific headers, example: {"h1":"v1","h2":"v2"}`)),
		),
		func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
			args := req.GetArguments()
			reqJson, _ := args["req_json"].(string)
			method, _ := args["method"].(string)
			address, _ := args["address"].(string)
			subEnv, _ := args["sub_env"].(string)
			headers, _ := args["headers"].(string)

			in := &InvokeGrpcurlReq{
				ReqJson: reqJson,
				Method:  method,
				Address: address,
				SubEnv:  subEnv,
				Headers: headers,
			}
			log.InfoWithCtx(ctx, "McpWrapper4InvokeGrpcurl in: %+v", in)

			out, code, msg := s.InvokeGrpcurl(ctx, in)
			log.InfoWithCtx(ctx, "McpWrapper4InvokeGrpcurl in: %+v, out: %+v, code: %d, msg: %s", in, out, code, msg)
			return mcp.NewToolResultText(encodeRsp2Json(code, msg, out)), nil
		}
}

func (s *Server) HttpWrapper4InvokeGrpcurl(ctx context.Context, w http.ResponseWriter, r *http.Request) {
	in := &InvokeGrpcurlReq{}
	if err := http.ReadJSON(r, in); err != nil {
		log.ErrorWithCtx(ctx, "json.Unmarshal err: %v", err)
		_ = web.ServeAPICodeJson(w, status.ErrSys, "unmarshal error", nil)
		return
	}
	log.InfoWithCtx(ctx, "HttpWrapper4InvokeGrpcurl in: %+v", in)

	out, code, msg := s.InvokeGrpcurl(ctx, in)
	log.InfoWithCtx(ctx, "HttpWrapper4InvokeGrpcurl in: %+v, out: %+v, code: %d, msg: %s", in, out, code, msg)
	_ = web.ServeAPICodeJsonV2(w, code, msg, out)
}
