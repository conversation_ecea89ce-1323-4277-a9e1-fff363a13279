package internal

import (
	"context"
	"encoding/json"
	"golang.52tt.com/pkg/web"
	"golang.52tt.com/services/revenue-mcp-logic/internal/config"
	"time"
)

type Server struct {
	cfg *config.StartConfig
}

func NewServer(ctx context.Context, cfg *config.StartConfig) (*Server, error) {
	svr := &Server{cfg: cfg}

	return svr, nil
}

func encodeRsp2Json(code int32, msg string, data interface{}) string {
	rsp := &web.JsonResponse{
		Code: code, Msg: msg, ServerTime: uint32(time.Now().Unix()), Data: data,
	}
	result, _ := json.Marshal(rsp)
	return string(result)
}
