package main

import (
	"context"
	mcpserver "github.com/mark3labs/mcp-go/server"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/http"
	startup "gitlab.ttyuyin.com/avengers/tyr/core/service/startup/suit/http"
	"golang.52tt.com/services/revenue-mcp-logic/internal"
	"golang.52tt.com/services/revenue-mcp-logic/internal/config"
)

func main() {
	var (
		server *internal.Server
		cfg    = &config.StartConfig{}
		err    error
	)

	initializeFunc := func(ctx context.Context, r *http.Router) error {
		// do something with cfg
		if server, err = internal.NewServer(ctx, cfg); err != nil {
			log.Errorf("NewServer fail, err: %v", err)
			return err
		}

		mcpServer := mcpserver.NewMCPServer("revenue-mcp-logic", "1.0.0")
		mcpServer.AddTool(server.McpWrapper4InvokeGrpcurl())
		sseServer := mcpserver.NewSSEServer(mcpServer, mcpserver.WithStaticBasePath("/revenue-mcp-logic"))
		// 绑定mcp sse处理路由
		r.Handle(sseServer.CompleteSsePath(), sseServer.SSEHandler())
		r.Handle(sseServer.CompleteMessagePath(), sseServer.MessageHandler())

		// 绑定普通http接口路由
		p := r.Child("/revenue-mcp-logic")
		p.POST("/invoke_grpcurl", server.HttpWrapper4InvokeGrpcurl)

		return nil
	}

	closeFunc := func(ctx context.Context) {
		// do something when server terminating
	}

	if err := startup.New("revenue-mcp-logic", cfg).
		AddHttpServer(
			http.NewBuildOption().WithInitializeFunc(initializeFunc),
		).
		WithCloseFunc(closeFunc).
		Start(); err != nil {
		log.Errorf("server start fail, err: %v", err)
	}
}
