# Channel-Live-Mgr API 接口文档

## 概述

Channel-Live-Mgr 服务提供了完整的直播管理功能，包括主播权限管理、直播状态控制、PK系统、数据统计等核心接口。所有接口均基于 gRPC 协议实现。

## 接口分类

### 1. 直播权限管理

#### SetChannelLiveInfo - 设置主播直播权限
**功能描述**：为主播开通或更新直播权限

**请求参数**：
```protobuf
message SetChannelLiveInfoReq {
    ChannelLiveInfo channel_live_info = 1;
}

message ChannelLiveInfo {
    uint32 uid = 1;                    // 主播UID
    uint32 channel_id = 2;             // 频道ID
    uint32 begin_time = 3;             // 权限开始时间
    uint32 end_time = 4;               // 权限结束时间
    string oper_name = 6;              // 操作人员
    uint32 tag_id = 7;                 // 标签ID
    uint32 authority = 10;             // 权限标志位
}
```

**响应参数**：
```protobuf
message SetChannelLiveInfoResp {
    // 空响应，成功时无返回数据
}
```

#### DelChannelLiveInfo - 回收主播直播权限
**功能描述**：回收指定主播的直播权限

**请求参数**：
```protobuf
message DelChannelLiveInfoReq {
    uint32 uid = 1;                   // 主播UID
    uint32 channel_id = 2;            // 频道ID
    string reason = 3;                // 回收原因
}
```

#### GetChannelLiveInfo - 获取主播权限信息
**功能描述**：查询主播的直播权限信息

**请求参数**：
```protobuf
message GetChannelLiveInfoReq {
    uint32 uid = 1;                   // 主播UID
    bool expire = 2;                  // 是否包含过期权限
    uint32 channel_id = 3;            // 频道ID（可选）
}
```

### 2. 直播状态管理

#### SetChannelLiveStatus - 设置直播状态
**功能描述**：控制直播状态（开播、关播、暂停、继续）

**请求参数**：
```protobuf
message SetChannelLiveStatusReq {
    uint32 uid = 1;                           // 主播UID
    string account = 2;                       // 主播账号
    string nick = 3;                          // 主播昵称
    uint32 sex = 4;                           // 性别
    uint32 channel_id = 5;                    // 频道ID
    uint64 channel_live_id = 6;               // 直播ID
    string channel_client_id = 7;             // 语音流ID
    uint32 fans_cnt = 8;                      // 粉丝数
    uint32 group_fans_cnt = 9;                // 团粉丝数
    EnumChannelLiveStatus status = 10;        // 直播状态
    EnumChannelLiveStatus orig_status = 11;   // 原始状态
}

enum EnumChannelLiveStatus {
    CLOSE = 0;      // 关播
    OPEN = 1;       // 开播
    PAUSE = 2;      // 暂停
    CONTINUE = 3;   // 继续
}
```

#### GetChannelLiveStatus - 获取直播状态
**功能描述**：获取主播当前的直播状态信息

**请求参数**：
```protobuf
message GetChannelLiveStatusReq {
    uint32 uid = 1;                   // 主播UID
    uint32 channel_id = 2;            // 频道ID
    bool ignore_mem_cache = 3;        // 是否忽略内存缓存
}
```

**响应参数**：
```protobuf
message GetChannelLiveStatusResp {
    ChannelLiveStatusInfo channel_live_info = 1;
}

message ChannelLiveStatusInfo {
    ChannelLiveStatus channel_live_status = 1;           // 主播直播状态
    ChannelLiveStatus pk_channel_live_status = 2;        // PK对手状态
    ChannelLivePkCommonInfo pk_common_info = 3;          // PK公共信息
    bool is_challenge = 4;                               // 是否为挑战方
}
```

#### ChannelLiveHeartbeat - 主播心跳
**功能描述**：主播心跳接口，维持直播状态

**请求参数**：
```protobuf
message ChannelLiveHeartbeatReq {
    uint32 my_uid = 1;                // 发起心跳用户UID
    uint32 uid = 2;                   // 主播UID
    string account = 3;               // 主播账号
    string nick = 4;                  // 主播昵称
    uint32 channel_id = 5;            // 频道ID
    uint64 channel_live_id = 6;       // 直播ID
    string channel_client_id = 7;     // 语音流ID
    uint32 mic_id = 8;                // 麦位ID
}
```

### 3. PK系统

#### ApplyPk - 申请PK
**功能描述**：向指定主播发起PK申请

**请求参数**：
```protobuf
message ApplyPkReq {
    uint32 uid = 1;                   // 发起方UID
    uint32 channel_id = 2;            // 发起方频道ID
    uint64 channel_live_id = 3;       // 发起方直播ID
    uint32 target_uid = 4;            // 目标主播UID
    uint32 target_channel_id = 5;     // 目标频道ID
}
```

#### HandlerApply - 处理PK申请
**功能描述**：接受或拒绝PK申请

**请求参数**：
```protobuf
message HandlerApplyReq {
    uint32 uid = 1;                   // 处理方UID
    uint32 channel_id = 2;            // 处理方频道ID
    string username = 3;              // 处理方用户名
    string nickname = 4;              // 处理方昵称
    uint32 apply_uid = 5;             // 申请方UID
    uint32 apply_channel_id = 6;      // 申请方频道ID
    string apply_username = 7;        // 申请方用户名
    string apply_nickname = 8;        // 申请方昵称
    EnumApply oper = 9;               // 操作类型
}

enum EnumApply {
    accept = 0;     // 接受
    reject = 1;     // 拒绝
    delete = 2;     // 删除
    cancel = 3;     // 取消
    apply = 4;      // 申请
}
```

#### StartPkMatch - 开始PK匹配
**功能描述**：开始PK匹配，系统自动寻找合适对手

**请求参数**：
```protobuf
message StartPkMatchReq {
    uint32 uid = 1;                   // 主播UID
    uint32 channel_id = 2;            // 频道ID
    uint32 match_type = 3;            // 匹配类型：1-随机，2-排位
}
```

### 4. 指定PK管理

#### AddAppointPkInfo - 添加指定PK
**功能描述**：运营配置指定PK信息

**请求参数**：
```protobuf
message AddAppointPkInfoReq {
    AppointPkInfo info = 1;
}

message AppointPkInfo {
    uint32 appoint_id = 1;                    // 指定PK ID
    uint32 uid = 2;                           // 主播UID
    uint32 begin_ts = 3;                      // 开始时间戳
    uint32 end_ts = 4;                        // 结束时间戳
    repeated PkRivalInfo rival_list = 5;      // 对手列表
    uint32 update_ts = 6;                     // 更新时间戳
    string operator = 7;                      // 操作人员
    AppointPkSourceType pk_source = 8;        // PK来源类型
}

enum AppointPkSourceType {
    APPOINT_PK_SOURCE_NORMAL = 0;     // 普通指定PK
    APPOINT_PK_SOURCE_MATCH = 1;      // 赛事PK
}

message PkRivalInfo {
    uint32 uid = 1;                   // 对手UID
    uint32 pk_begin_ts = 2;           // PK开始时间
}
```

#### GetAppointPkInfoList - 获取指定PK列表
**功能描述**：查询指定PK配置列表

**请求参数**：
```protobuf
message GetAppointPkInfoListReq {
    uint32 uid = 1;                   // 主播UID（可选）
    uint32 begin_ts = 2;              // 开始时间（可选）
    uint32 end_ts = 3;                // 结束时间（可选）
    uint32 offset = 4;                // 偏移量
    uint32 limit = 5;                 // 限制数量
    QueryType query_type = 6;         // 查询类型
}

enum QueryType {
    Query_All = 0;        // 查询所有
    Query_By_Ts = 1;      // 按时间查询
    Query_By_Uid = 2;     // 按UID查询
}
```

### 5. 数据统计

#### GetChannelLiveTotalData - 获取主播总数据
**功能描述**：获取主播的直播总数据统计

**请求参数**：
```protobuf
message GetChannelLiveTotalDataReq {
    uint32 uid = 1;                   // 主播UID
    uint32 begin_time = 2;            // 开始时间
    uint32 end_time = 3;              // 结束时间
}
```

**响应参数**：
```protobuf
message GetChannelLiveTotalDataResp {
    uint64 total_gift_value = 1;              // 总礼物流水
    repeated LiveDayData day_date_list = 2;   // 每日数据列表
}

message LiveDayData {
    string date = 1;                  // 日期
    uint32 gift_value = 2;            // 礼物流水
    uint32 send_gift_user_cnt = 3;    // 送礼人数
    uint32 live_time = 4;             // 直播时长
    uint32 audience_cnt = 5;          // 观众数
    uint32 anchor_gift_value = 6;     // 主播收礼值
    uint32 add_fans = 7;              // 新增粉丝
    uint32 add_group_fans = 8;        // 新增团粉丝
    uint32 begin_time = 9;            // 开始时间
}
```

#### GetChanneLivePkRankUser - 获取PK排行榜
**功能描述**：获取PK过程中的用户排行榜

**请求参数**：
```protobuf
message GetChanneLivePkRankUserReq {
    uint32 uid = 1;                   // PK参与方UID
    uint32 channel_id = 2;            // 频道ID
    uint32 target_uid = 3;            // PK对手UID
    uint32 off = 4;                   // 偏移量
    uint32 cnt = 5;                   // 数量
}
```

## 错误码说明

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 40001 | 无直播权限 | 主播没有直播权限或权限已过期 |
| 40002 | 正在PK中 | 主播当前正在进行PK，无法执行其他操作 |
| 40003 | 正在匹配中 | 主播当前正在匹配，无法重复匹配 |
| 40004 | 指定PK期间不能发起PK | 在指定PK时间段内不允许发起其他PK |
| 40005 | 指定PK期间不能接受PK | 在指定PK时间段内不允许接受其他PK |
| 40006 | 即将有赛事PK场次 | 赛事PK开始前10分钟内不能发起PK |

## 使用示例

### 开播流程示例
```go
// 1. 设置直播状态为开播
req := &pb.SetChannelLiveStatusReq{
    Uid:       12345,
    Account:   "anchor001",
    Nick:      "主播昵称",
    ChannelId: 67890,
    Status:    pb.EnumChannelLiveStatus_OPEN,
}
resp, err := client.SetChannelLiveStatus(ctx, req)

// 2. 开始心跳
heartbeatReq := &pb.ChannelLiveHeartbeatReq{
    MyUid:     12345,
    Uid:       12345,
    ChannelId: 67890,
}
// 定期发送心跳...
```

### PK流程示例
```go
// 1. 申请PK
applyReq := &pb.ApplyPkReq{
    Uid:             12345,
    ChannelId:       67890,
    TargetUid:       54321,
    TargetChannelId: 98765,
}
_, err := client.ApplyPk(ctx, applyReq)

// 2. 处理PK申请（对方接受）
handlerReq := &pb.HandlerApplyReq{
    Uid:             54321,
    ChannelId:       98765,
    ApplyUid:        12345,
    ApplyChannelId:  67890,
    Oper:            pb.EnumApply_accept,
}
_, err = client.HandlerApply(ctx, handlerReq)
```

## 注意事项

1. **权限验证**：所有接口都会验证主播权限，确保只有有效权限的主播才能进行直播相关操作
2. **状态检查**：PK相关接口会检查主播当前状态，避免状态冲突
3. **时间限制**：赛事PK有特殊的时间限制，需要注意相关规则
4. **心跳机制**：主播需要定期发送心跳，超时会自动下播
5. **数据一致性**：缓存和数据库的数据一致性由服务内部保证
6. **错误处理**：客户端需要根据错误码进行相应的错误处理
