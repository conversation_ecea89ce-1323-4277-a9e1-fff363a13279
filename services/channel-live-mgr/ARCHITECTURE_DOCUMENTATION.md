# Channel-Live-Mgr 服务架构文档

## 服务概述

`channel-live-mgr` 是一个综合性的直播管理服务，负责管理语音直播房间的生命周期、PK系统、主播权限、数据统计等核心功能。该服务是直播平台的核心组件之一，处理直播房间的状态管理、PK匹配、权限控制等关键业务逻辑。

## 核心功能模块

### 1. 直播权限管理
- **主播权限开通/回收**：管理主播的直播权限，包括开通时间、结束时间、操作人员等
- **权限验证**：验证主播是否有直播权限，支持按UID和ChannelID查询
- **权限标志位**：支持多种权限类型的位标志管理
- **批量权限管理**：支持批量获取和管理主播权限信息

### 2. 直播状态管理
- **直播状态控制**：开播、关播、暂停、继续等状态管理
- **心跳机制**：主播心跳检测，超时自动下播
- **直播房间信息**：管理直播房间的基本信息、麦位信息、观众数据等
- **直播历史记录**：记录主播的直播历史、时长统计等

### 3. PK系统
- **PK申请与匹配**：支持主播间的PK申请、接受、拒绝等操作
- **PK状态机**：管理PK的各个阶段（开局、道具、最后一分钟、惩罚、结束）
- **PK匹配算法**：支持随机匹配和排位匹配
- **指定PK**：支持运营配置的指定PK，包括赛事PK功能
- **PK数据统计**：PK过程中的分数统计、排行榜等

### 4. 数据统计与分析
- **直播数据统计**：观众数、礼物流水、直播时长等
- **主播数据分析**：主播的历史数据、月度数据、总数据等
- **观看时长统计**：用户观看时长排行榜
- **收入统计**：礼物收入、骑士团收入、游戏收入等

### 5. 事件处理系统
- **Kafka事件订阅**：订阅礼物、频道、麦位等相关事件
- **事件分发处理**：将事件分发到相应的处理模块
- **实时数据更新**：基于事件实时更新直播数据

## 技术架构

### 架构层次

```
┌─────────────────────────────────────────────────────────────┐
│                        gRPC API Layer                       │
│                    (Proto Interface)                        │
├─────────────────────────────────────────────────────────────┤
│                     Business Logic Layer                    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐│
│  │   Server    │ │   Manager   │ │  Appoint PK │ │  Match  ││
│  │   Handler   │ │   Core      │ │   Handler   │ │ System  ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘│
├─────────────────────────────────────────────────────────────┤
│                      Data Access Layer                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐│
│  │    Cache    │ │    MySQL    │ │    Redis    │ │  Kafka  ││
│  │   Client    │ │    Store    │ │   Client    │ │Producer ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘│
├─────────────────────────────────────────────────────────────┤
│                    External Services                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐│
│  │   Account   │ │   Channel   │ │    Push     │ │  Other  ││
│  │   Service   │ │   Service   │ │ Notification│ │Services ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘│
└─────────────────────────────────────────────────────────────┘
```

### 核心组件

#### 1. Server Layer (server/server.go)
- **ChannelLiveMgrServer**：主服务器结构，实现所有gRPC接口
- **接口实现**：实现proto文件中定义的所有服务接口
- **依赖注入**：管理所有外部服务客户端和内部组件

#### 2. Manager Layer (manager/manager.go)
- **Manager**：核心业务逻辑管理器
- **PK状态机处理**：处理PK的状态变化和定时任务
- **事件处理**：处理来自Kafka的各种事件
- **数据统计**：实时数据统计和排行榜计算

#### 3. Cache Layer (cache/cache.go)
- **ChannelLiveMgrCache**：Redis缓存客户端封装
- **数据缓存**：直播状态、PK信息、排行榜等数据缓存
- **分布式锁**：基于Redis的分布式锁实现
- **心跳管理**：主播心跳数据管理

#### 4. Storage Layer (mysql/store.go)
- **Store**：MySQL数据库访问层
- **数据持久化**：直播记录、PK记录、主播数据等持久化
- **事务管理**：数据库事务处理
- **数据查询**：复杂查询和统计功能

### 数据流向

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Client    │───▶│   gRPC      │───▶│   Server    │
│ Application │    │  Interface  │    │   Handler   │
└─────────────┘    └─────────────┘    └─────────────┘
                                              │
                                              ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   External  │◀───│   Manager   │◀───│  Business   │
│  Services   │    │    Core     │    │   Logic     │
└─────────────┘    └─────────────┘    └─────────────┘
                           │
                           ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    Kafka    │───▶│    Cache    │───▶│    MySQL    │
│   Events    │    │   (Redis)   │    │  Database   │
└─────────────┘    └─────────────┘    └─────────────┘
```

## 主要数据结构

### 1. 直播权限信息 (ChannelLiveInfo)
```protobuf
message ChannelLiveInfo {
    uint32 uid = 1;                    // 主播UID
    uint32 channel_id = 2;             // 频道ID
    uint32 begin_time = 3;             // 权限开始时间
    uint32 end_time = 4;               // 权限结束时间
    uint32 create_time = 5;            // 创建时间
    string oper_name = 6;              // 操作人员
    uint32 tag_id = 7;                 // 标签ID
    uint32 authority = 10;             // 权限标志位
}
```

### 2. 直播状态信息 (ChannelLiveStatus)
```protobuf
message ChannelLiveStatus {
    uint32 uid = 1;                           // 主播UID
    uint32 channel_id = 5;                    // 频道ID
    uint64 channel_live_id = 6;               // 直播ID
    repeated PkMicSpace channel_mic_list = 9; // 麦位信息
    EnumChannelLiveStatus status = 10;        // 直播状态
    EnumChannelLivePKStatus pk_status = 14;   // PK状态
    EnumPkMatch pk_match_state = 15;          // 匹配状态
}
```

### 3. 指定PK信息 (AppointPkInfo)
```protobuf
message AppointPkInfo {
    uint32 appoint_id = 1;                    // 指定PK ID
    uint32 uid = 2;                           // 主播UID
    uint32 begin_ts = 3;                      // 开始时间
    uint32 end_ts = 4;                        // 结束时间
    repeated PkRivalInfo rival_list = 5;      // 对手列表
    string operator = 7;                      // 操作人员
    AppointPkSourceType pk_source = 8;        // PK来源类型
}
```

## 关键业务流程

### 1. 直播开播流程
1. 客户端调用 `SetChannelLiveStatus` 接口
2. 验证主播权限和直播房间状态
3. 生成直播ID，更新缓存和数据库
4. 初始化直播数据统计
5. 启动心跳检测
6. 发送开播事件到Kafka

### 2. PK匹配流程
1. 主播调用 `StartPkMatch` 开始匹配
2. 系统根据匹配算法寻找合适对手
3. 匹配成功后调用 `StartPk` 开始PK
4. 启动PK状态机，管理PK各个阶段
5. 实时统计PK分数和排行榜
6. PK结束后清理状态和数据

### 3. 指定PK流程
1. 运营通过 `AddAppointPkInfo` 配置指定PK
2. 系统在指定时间自动触发PK
3. 检查主播状态和限制条件
4. 启动指定PK，特殊处理赛事PK逻辑
5. 管理PK过程，取消加时等特殊规则

## 性能优化

### 1. 缓存策略
- **多级缓存**：内存缓存 + Redis缓存
- **缓存预热**：启动时预加载热点数据
- **缓存更新**：事件驱动的缓存更新机制

### 2. 数据库优化
- **读写分离**：读操作使用只读数据库
- **索引优化**：关键查询字段建立索引
- **分表策略**：按时间分表存储历史数据

### 3. 异步处理
- **事件异步处理**：Kafka事件异步消费
- **定时任务**：PK状态机、数据统计等定时处理
- **批量操作**：批量更新和查询优化

## 监控与运维

### 1. 健康检查
- **服务健康检查**：gRPC健康检查接口
- **依赖服务检查**：数据库、Redis、Kafka连接检查
- **业务指标监控**：直播数量、PK数量等业务指标

### 2. 日志管理
- **结构化日志**：使用统一的日志格式
- **链路追踪**：支持分布式链路追踪
- **错误告警**：关键错误实时告警

### 3. 配置管理
- **动态配置**：支持运行时配置更新
- **环境隔离**：开发、测试、生产环境配置隔离
- **配置验证**：配置项合法性验证

## 扩展性设计

### 1. 水平扩展
- **无状态设计**：服务实例无状态，支持水平扩展
- **负载均衡**：支持多实例负载均衡
- **数据分片**：支持数据库和缓存分片

### 2. 功能扩展
- **插件化架构**：支持功能模块插件化扩展
- **事件驱动**：基于事件的松耦合架构
- **接口版本管理**：支持API版本兼容

## 安全性

### 1. 权限控制
- **接口权限**：基于角色的接口访问控制
- **数据权限**：用户只能访问自己的数据
- **操作审计**：记录关键操作日志

### 2. 数据安全
- **数据加密**：敏感数据加密存储
- **SQL注入防护**：使用参数化查询
- **输入验证**：严格的输入参数验证
