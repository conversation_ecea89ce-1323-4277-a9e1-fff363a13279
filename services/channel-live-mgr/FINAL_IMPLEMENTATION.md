# 赛事PK功能最终实现方案

## 需求总结

根据最新需求，实现以下功能：

1. **在指定PK AddAppointPkInfoReq 接口上增加活动PK支持**
   - 活动PK的operator为"admin"
   - 指定PK和活动PK都是赛事PK，不需要区分具体来源

2. **赛事PK开启前10min内主播不能接受或发起任何PK**
   - 包括双人PK、四人PK、指定id PK
   - 发起PK时提示：即将有赛事PK场次，不能发起PK
   - 被拉起PK时提示：对方即将有一场赛事PK场次，不能发起PK

3. **赛事PK场次取消加时逻辑**
   - 避免影响下一场赛事PK场次

4. **主播还在进行上一场PK且到赛事PK的时间时的处理**
   - 在主播房间拉起赛事PK弹窗邀约提示
   - 点击"点击应战"后提示"当前还有正在进行的PK，无法应战"
   - 返回错误原因、主播ID、赛事ID给活动组开发

## 实现方案

### 1. 数据模型设计

#### Proto定义
```protobuf
// PK来源类型
enum AppointPkSourceType {
   APPOINT_PK_SOURCE_NORMAL = 0;  // 普通指定PK
   APPOINT_PK_SOURCE_MATCH = 1;   // 赛事PK（包括指定PK和活动PK）
}
```

#### 数据库字段
```sql
ALTER TABLE `tbl_appoint_pk_info` 
ADD COLUMN `pk_source` int(11) NOT NULL DEFAULT '0' 
COMMENT 'PK来源：0-普通指定PK，1-赛事PK' 
AFTER `operator`;
```

### 2. 业务逻辑实现

#### 2.1 活动PK自动识别
在 `AddAppointPkInfo` 和 `UpdateAppointPkInfo` 接口中：
```go
// 根据operator判断PK来源：admin为活动PK（赛事PK），其他为指定PK（也是赛事PK）
pkSource := uint32(in.GetInfo().GetPkSource())
if in.GetInfo().GetOperator() == "admin" {
    pkSource = 1 // 活动PK设置为赛事PK
}
```

#### 2.2 赛事PK前10分钟限制
在 `StartPkMatch`、`ApplyPk` 接口中添加检查：
```go
// 检查是否在赛事PK开始前10分钟内
upcomingMatchPkList, err := s.mysqlStore.GetAnchorUpcomingMatchPkList(ctx, uid, nowTs, nowTs+600)
if len(upcomingMatchPkList) > 0 {
    return protocol.NewExactServerError(nil, status.ErrChannelLiveAppointPkIngNoLaunchPk, "即将有赛事PK场次，不能发起PK")
}
```

#### 2.3 赛事PK加时取消
在PK状态机中：
```go
// 检查是否为赛事PK，如果是则取消加时逻辑
isMatchPk := task.MatchSource == int32(pbLogic.ChannelLivePKMatchType_CPK_Match_Appoint) && m.isAppointPkFromMatch(task.AUid, task.BUid)

pushMsg := pbLogic.ChannelLivePkStatusPushMsg{
    CommonInfo: &pbLogic.PkCommonInfo{
        IsExtraTime:     pkinfo.IsExtra && !isMatchPk, // 赛事PK取消加时
        IsOpenExtraTime: pkinfo.IsOpenExtra && !isMatchPk, // 赛事PK取消加时
    },
}
```

#### 2.4 赛事PK冲突处理
在 `AcceptAppointPk` 接口中：
```go
if appointPkInfo.GetPkSource() == pb.AppointPkSourceType_APPOINT_PK_SOURCE_MATCH {
    // 检查主播是否正在进行其他PK
    if myPkState.IsPKing() {
        return protocol.NewExactServerError(nil, status.ErrChannelLiveAppointPkIngNoAcceptPk, 
            fmt.Sprintf("当前还有正在进行的PK，无法应战。主播ID:%d，赛事ID:%d，错误原因:主播正在其他PK中", 
                in.GetMyUid(), appointPkInfo.GetAppointId()))
    }
}
```

### 3. 四人PK支持

#### 3.1 新增检查接口
```protobuf
// 检查主播是否在赛事PK前10分钟内（供其他服务调用）
rpc CheckAnchorUpcomingMatchPk(CheckAnchorUpcomingMatchPkReq) returns (CheckAnchorUpcomingMatchPkResp);

message CheckAnchorUpcomingMatchPkReq {
    repeated uint32 uid_list = 1;  // 主播UID列表
}

message CheckAnchorUpcomingMatchPkResp {
    map<uint32, bool> uid_has_upcoming_match = 1;  // UID -> 是否有即将开始的赛事PK
    map<uint32, string> uid_error_msg = 2;         // UID -> 错误提示信息
}
```

#### 3.2 接口实现
```go
func (s *ChannelLiveMgrServer) CheckAnchorUpcomingMatchPk(ctx context.Context, in *pb.CheckAnchorUpcomingMatchPkReq) (out *pb.CheckAnchorUpcomingMatchPkResp, err error) {
    // 批量检查主播是否在赛事PK前10分钟内
    for _, uid := range in.GetUidList() {
        upcomingMatchPkList, err := s.mysqlStore.GetAnchorUpcomingMatchPkList(ctx, uid, nowTs, nowTs+600)
        if len(upcomingMatchPkList) > 0 {
            out.UidHasUpcomingMatch[uid] = true
            out.UidErrorMsg[uid] = "即将有赛事PK场次，不能发起PK"
        }
    }
}
```

### 4. 数据库查询优化

```go
// 获取主播即将开始的赛事PK信息（用于检查10分钟内限制）
func (s *Store) GetAnchorUpcomingMatchPkList(ctx context.Context, uid, startTs, endTs uint32) ([]*AppointPkInfo, error) {
    sql := fmt.Sprintf("select appoint_id, uid, begin_ts, end_ts, pk_source, operator from %s where uid = ? and pk_source = 1 and begin_ts >= ? and begin_ts <= ?", tblAppointPkInfo)
    err := s.db.SelectContext(ctx, &infoList, sql, uid, startTs, endTs)
    return infoList, err
}
```

## 关键特性

### 1. 统一的赛事PK处理
- 指定PK和活动PK都使用 `pk_source = 1`
- 通过 `operator` 字段区分：`admin` 为活动PK，其他为指定PK
- 所有赛事PK享受相同的特殊逻辑

### 2. 全面的PK限制
- **双人PK**：`StartPkMatch`、`ApplyPk` 接口检查
- **四人PK**：通过 `CheckAnchorUpcomingMatchPk` 接口检查
- **指定PK**：在指定PK处理逻辑中检查

### 3. 详细的错误信息
- 返回具体的错误原因
- 包含主播ID和赛事ID
- 便于活动组开发调试和处理

### 4. 性能优化
- 添加数据库索引：`idx_pk_source_begin_ts`、`idx_uid_pk_source_begin_ts`
- 批量查询接口支持
- 缓存友好的设计

## 部署清单

### 1. 数据库升级
```bash
# 执行SQL脚本
mysql < services/channel-live-mgr/sql/add_pk_source_field.sql
```

### 2. 代码部署
- 部署更新后的 `channel-live-mgr` 服务
- 确保 proto 文件同步到相关服务

### 3. 四人PK服务集成
- 四人PK服务需要调用 `CheckAnchorUpcomingMatchPk` 接口
- 在发起四人PK时进行限制检查

### 4. 配置验证
- 验证赛事PK配置正确
- 测试活动PK的自动识别

## 测试要点

### 1. 功能测试
- 测试活动PK（operator="admin"）的自动识别
- 测试赛事PK前10分钟的各种PK限制
- 测试赛事PK的加时取消功能
- 测试赛事PK冲突时的错误信息返回

### 2. 接口测试
- 测试 `CheckAnchorUpcomingMatchPk` 接口的批量查询
- 测试错误信息的格式和内容
- 测试接口的性能表现

### 3. 集成测试
- 测试与四人PK服务的集成
- 测试多种PK类型的限制生效
- 测试边界条件和异常情况

### 4. 兼容性测试
- 验证现有普通指定PK功能不受影响
- 验证数据库升级的兼容性
- 验证新老版本的接口兼容性

## 监控指标

### 1. 业务指标
- 赛事PK创建数量
- 活动PK识别准确率
- PK限制触发次数
- 冲突处理成功率

### 2. 技术指标
- 接口响应时间
- 数据库查询性能
- 错误率和成功率
- 缓存命中率

## 总结

本实现方案完全满足了新需求：

1. ✅ **活动PK支持**：operator为"admin"时自动设置为赛事PK
2. ✅ **全面PK限制**：支持双人PK、四人PK、指定id PK的限制
3. ✅ **加时取消**：赛事PK强制取消加时逻辑
4. ✅ **冲突处理**：详细的错误信息返回给活动组
5. ✅ **四人PK集成**：提供专门的检查接口

方案设计简洁明了，性能优化到位，具有良好的扩展性和维护性。
