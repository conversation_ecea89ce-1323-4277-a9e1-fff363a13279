# 指定PK功能最终实现方案

## 需求总结

根据最新需求，实现以下功能：

1. **协议和数据库不需要增加来源字段**
   - 所有PK处理逻辑相同
   - 不区分指定PK和活动PK的来源

2. **指定PK开启前10min内主播不能接受或发起任何PK**
   - 包括双人PK、四人PK、指定id PK
   - 发起PK时提示：即将有指定PK场次，不能发起PK
   - 被拉起PK时提示：对方即将有一场指定PK场次，不能发起PK

3. **指定PK场次取消加时逻辑**
   - 避免影响下一场指定PK场次

4. **主播还在进行上一场PK且到指定PK的时间时的处理**
   - 在主播房间拉起指定PK弹窗邀约提示
   - 点击"点击应战"后提示"当前还有正在进行的PK，无法应战"
   - 返回错误原因、主播ID、指定PK ID给活动组开发

## 实现方案

### 1. 数据模型设计

#### Proto定义
```protobuf
// 指定pk信息
message AppointPkInfo {
   uint32 appoint_id = 1;
   uint32 uid = 2;
   uint32 begin_ts = 3;
   uint32 end_ts = 4;
   repeated PkRivalInfo rival_list = 5;
   uint32 update_ts = 6;
   string operator = 7;  // 操作人员，admin表示活动PK
}
```

#### 数据库结构
```go
type AppointPkInfo struct {
    AppointId uint32 `db:"appoint_id"`
    Uid       uint32 `db:"uid"`
    BeginTs   uint32 `db:"begin_ts"`
    EndTs     uint32 `db:"end_ts"`
    UpdateTs  uint32 `db:"update_ts"`
    Operator  string `db:"operator"`  // 操作人员，admin表示活动PK
}
```

### 2. 业务逻辑实现

#### 2.1 指定PK前10分钟限制
在 `StartPkMatch`、`ApplyPk` 接口中添加检查：
```go
// 检查是否在指定PK开始前10分钟内
upcomingAppointPkList, err := s.mysqlStore.GetAnchorUpcomingAppointPkList(ctx, uid, nowTs, nowTs+600)
if len(upcomingAppointPkList) > 0 {
    return protocol.NewExactServerError(nil, status.ErrChannelLiveAppointPkIngNoLaunchPk, "即将有指定PK场次，不能发起PK")
}
```

#### 2.2 指定PK加时取消
在PK状态机中：
```go
// 检查是否为指定PK，如果是则取消加时逻辑
isAppointPk := task.MatchSource == int32(pbLogic.ChannelLivePKMatchType_CPK_Match_Appoint)

pushMsg := pbLogic.ChannelLivePkStatusPushMsg{
    CommonInfo: &pbLogic.PkCommonInfo{
        IsExtraTime:     pkinfo.IsExtra && !isAppointPk, // 指定PK取消加时
        IsOpenExtraTime: pkinfo.IsOpenExtra && !isAppointPk, // 指定PK取消加时
    },
}
```

#### 2.3 指定PK冲突处理
在 `AcceptAppointPk` 接口中：
```go
// 检查主播是否正在进行其他PK
if myPkState.IsPKing() {
    return protocol.NewExactServerError(nil, status.ErrChannelLiveAppointPkIngNoAcceptPk,
        fmt.Sprintf("当前还有正在进行的PK，无法应战。主播ID:%d，指定PK ID:%d，错误原因:主播正在其他PK中",
            in.GetMyUid(), appointPkInfo.GetAppointId()))
}
```

### 3. 四人PK支持

#### 3.1 新增检查接口
```protobuf
// 检查主播是否在指定PK前10分钟内（供其他服务调用）
rpc CheckAnchorUpcomingMatchPk(CheckAnchorUpcomingMatchPkReq) returns (CheckAnchorUpcomingMatchPkResp);

message CheckAnchorUpcomingMatchPkReq {
    repeated uint32 uid_list = 1;  // 主播UID列表
}

message CheckAnchorUpcomingMatchPkResp {
    map<uint32, bool> uid_has_upcoming_match = 1;  // UID -> 是否有即将开始的指定PK
    map<uint32, string> uid_error_msg = 2;         // UID -> 错误提示信息
}
```

#### 3.2 接口实现
```go
func (s *ChannelLiveMgrServer) CheckAnchorUpcomingMatchPk(ctx context.Context, in *pb.CheckAnchorUpcomingMatchPkReq) (out *pb.CheckAnchorUpcomingMatchPkResp, err error) {
    // 批量检查主播是否在指定PK前10分钟内
    for _, uid := range in.GetUidList() {
        upcomingAppointPkList, err := s.mysqlStore.GetAnchorUpcomingAppointPkList(ctx, uid, nowTs, nowTs+600)
        if len(upcomingAppointPkList) > 0 {
            out.UidHasUpcomingMatch[uid] = true
            out.UidErrorMsg[uid] = "即将有指定PK场次，不能发起PK"
        }
    }
}
```

### 4. 数据库查询优化

```go
// 获取主播即将开始的指定PK信息（用于检查10分钟内限制）
func (s *Store) GetAnchorUpcomingAppointPkList(ctx context.Context, uid, startTs, endTs uint32) ([]*AppointPkInfo, error) {
    sql := fmt.Sprintf("select appoint_id, uid, begin_ts, end_ts, operator from %s where uid = ? and begin_ts >= ? and begin_ts <= ?", tblAppointPkInfo)
    err := s.db.SelectContext(ctx, &infoList, sql, uid, startTs, endTs)
    return infoList, err
}
```

## 关键特性

### 1. 简化的PK处理
- 所有指定PK使用相同的处理逻辑
- 不区分PK来源，统一按指定PK处理
- 通过 `operator` 字段可以区分活动PK（admin）和普通指定PK

### 2. 全面的PK限制
- **双人PK**：`StartPkMatch`、`ApplyPk` 接口检查
- **四人PK**：通过 `CheckAnchorUpcomingMatchPk` 接口检查
- **指定PK**：在指定PK处理逻辑中检查

### 3. 详细的错误信息
- 返回具体的错误原因
- 包含主播ID和指定PK ID
- 便于活动组开发调试和处理

### 4. 性能优化
- 使用现有的数据库结构和索引
- 批量查询接口支持
- 缓存友好的设计

## 部署清单

### 1. 代码部署
- 部署更新后的 `channel-live-mgr` 服务
- 确保 proto 文件同步到相关服务

### 2. 四人PK服务集成
- 四人PK服务需要调用 `CheckAnchorUpcomingMatchPk` 接口
- 在发起四人PK时进行限制检查

### 3. 配置验证
- 验证指定PK配置正确
- 测试活动PK（operator="admin"）的处理

## 测试要点

### 1. 功能测试
- 测试活动PK（operator="admin"）的处理
- 测试指定PK前10分钟的各种PK限制
- 测试指定PK的加时取消功能
- 测试指定PK冲突时的错误信息返回

### 2. 接口测试
- 测试 `CheckAnchorUpcomingMatchPk` 接口的批量查询
- 测试错误信息的格式和内容
- 测试接口的性能表现

### 3. 集成测试
- 测试与四人PK服务的集成
- 测试多种PK类型的限制生效
- 测试边界条件和异常情况

### 4. 兼容性测试
- 验证现有指定PK功能不受影响
- 验证新老版本的接口兼容性

## 监控指标

### 1. 业务指标
- 指定PK创建数量
- 活动PK处理数量
- PK限制触发次数
- 冲突处理成功率

### 2. 技术指标
- 接口响应时间
- 数据库查询性能
- 错误率和成功率
- 缓存命中率

## 总结

本实现方案完全满足了新需求：

1. ✅ **简化设计**：协议和数据库不增加来源字段，所有PK处理逻辑相同
2. ✅ **全面PK限制**：支持双人PK、四人PK、指定id PK的限制
3. ✅ **加时取消**：指定PK强制取消加时逻辑
4. ✅ **冲突处理**：详细的错误信息返回给活动组
5. ✅ **四人PK集成**：提供专门的检查接口

方案设计简洁明了，不增加复杂性，具有良好的扩展性和维护性。
