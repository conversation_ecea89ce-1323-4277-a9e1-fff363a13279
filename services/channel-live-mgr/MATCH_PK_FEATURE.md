# 赛事PK功能实现说明

## 功能概述

在 channel-live-mgr 服务中新增赛事PK功能，主要包括以下特性：

1. **指定PK来源标识**：在指定PK信息中新增来源字段，区分普通指定PK、赛事PK和活动PK
2. **重要PK前10分钟限制**：主播在重要PK（赛事PK、活动PK）开始前10分钟内不能发起或接受任何PK
3. **取消重要PK加时逻辑**：重要PK场次不触发加时，避免影响下一场PK
4. **重要PK冲突处理**：主播在进行其他PK时，重要PK到时间会弹窗提示但无法应战

**注意**：活动PK也是赛事PK的一种，所有新增逻辑对赛事PK和活动PK都生效。

## 实现细节

### 1. 数据库修改

**文件**: `sql/add_pk_source_field.sql`

- 在 `tbl_appoint_pk_info` 表中新增 `pk_source` 字段
- 0: 普通指定PK
- 1: 赛事PK
- 添加相关索引优化查询性能

### 2. Proto文件修改

**文件**: `proto/channel-live-mgr.proto`

- 新增 `AppointPkSourceType` 枚举类型
- 在 `AppointPkInfo` 消息中新增 `pk_source` 字段

### 3. 数据库操作层修改

**文件**: `mysql/store.go`

- 修改 `AppointPkInfo` 结构体，新增 `PkSource` 字段
- 更新所有相关的数据库操作方法，包括增删改查
- 新增 `GetAnchorUpcomingMatchPkList` 方法，用于查询主播即将开始的赛事PK

### 4. 业务逻辑修改

**文件**: `server/backend.go`
- 修改 `AddAppointPkInfo` 和 `UpdateAppointPkInfo` 方法，支持PK来源参数

**文件**: `server/server.go`
- 在 `StartPkMatch` 方法中新增赛事PK前10分钟限制检查
- 在 `ApplyPk` 方法中新增赛事PK前10分钟限制检查

**文件**: `server/appoint_pk_svr.go`
- 修改 `AcceptAppointPk` 方法，新增赛事PK冲突检查
- 更新指定PK信息转换逻辑，包含PK来源字段

**文件**: `manager/manager.go`
- 修改 `HandlerTask` 方法，为赛事PK取消加时逻辑
- 新增 `isAppointPkFromMatch` 辅助方法，检查是否为赛事PK

## 主要功能点

### 1. 赛事PK前10分钟限制

当主播尝试发起或接受PK时，系统会检查：
- 是否有赛事PK在未来10分钟内开始
- 如果有，则阻止PK操作并返回相应提示

### 2. 赛事PK加时取消

在PK状态机处理中：
- 检查当前PK是否为赛事PK
- 如果是赛事PK，则强制关闭加时功能
- 确保赛事PK按时结束，不影响下一场

### 3. 赛事PK冲突处理

当赛事PK时间到达但主播还在其他PK中：
- 系统会弹出赛事PK邀约提示
- 主播点击应战时提示"当前还有正在进行的PK，无法应战"
- 系统判定该主播应战超时

## 错误处理

- 使用现有的错误码体系
- 主要错误码：`ErrChannelLiveAppointPkIngNoLaunchPk`、`ErrChannelLiveAppointPkIngNoAcceptPk`
- 提供清晰的错误提示信息

## 部署说明

1. **数据库升级**：执行 `sql/add_pk_source_field.sql` 脚本
2. **代码部署**：部署更新后的 channel-live-mgr 服务
3. **配置检查**：确认相关配置项正确设置

## 测试建议

1. **功能测试**：
   - 测试赛事PK的创建和管理
   - 测试10分钟限制逻辑
   - 测试加时取消功能
   - 测试冲突处理逻辑

2. **性能测试**：
   - 验证新增索引的查询性能
   - 测试高并发场景下的表现

3. **兼容性测试**：
   - 确保现有普通指定PK功能不受影响
   - 验证新老版本客户端的兼容性
