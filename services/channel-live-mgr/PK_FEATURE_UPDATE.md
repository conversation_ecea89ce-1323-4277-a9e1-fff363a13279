# PK功能需求变更说明

## 需求变更

**原需求**：新增赛事PK功能，包括10分钟限制、取消加时、冲突处理等逻辑。

**变更需求**：活动PK也是赛事PK的一种，新增的逻辑对所有PK生效。

## 实现变更

### 1. PK来源类型扩展

**原设计**：
```protobuf
enum AppointPkSourceType {
   APPOINT_PK_SOURCE_NORMAL = 0;  // 普通指定PK
   APPOINT_PK_SOURCE_MATCH = 1;   // 赛事PK
}
```

**新设计**：
```protobuf
enum AppointPkSourceType {
   APPOINT_PK_SOURCE_NORMAL = 0;  // 普通指定PK
   APPOINT_PK_SOURCE_MATCH = 1;   // 赛事PK
   APPOINT_PK_SOURCE_ACTIVITY = 2; // 活动PK
}
```

### 2. 数据库字段更新

**字段定义**：
```sql
ALTER TABLE `tbl_appoint_pk_info` 
ADD COLUMN `pk_source` int(11) NOT NULL DEFAULT '0' 
COMMENT 'PK来源：0-普通指定PK，1-赛事PK，2-活动PK' 
AFTER `operator`;
```

### 3. 业务逻辑调整

#### 3.1 查询逻辑更新

**原逻辑**：只查询赛事PK（pk_source = 1）
```sql
WHERE pk_source = 1
```

**新逻辑**：查询赛事PK和活动PK（pk_source in (1, 2)）
```sql
WHERE pk_source in (1, 2)
```

#### 3.2 判断逻辑更新

**原逻辑**：
```go
if pkInfo.PkSource == 1 { // 只检查赛事PK
    // 应用特殊逻辑
}
```

**新逻辑**：
```go
if pkInfo.PkSource == 1 || pkInfo.PkSource == 2 { // 检查赛事PK和活动PK
    // 应用特殊逻辑
}
```

#### 3.3 接口检查更新

**原逻辑**：
```go
if appointPkInfo.GetPkSource() == pb.AppointPkSourceType_APPOINT_PK_SOURCE_MATCH {
    // 特殊处理
}
```

**新逻辑**：
```go
if appointPkInfo.GetPkSource() == pb.AppointPkSourceType_APPOINT_PK_SOURCE_MATCH || 
   appointPkInfo.GetPkSource() == pb.AppointPkSourceType_APPOINT_PK_SOURCE_ACTIVITY {
    // 特殊处理
}
```

### 4. 错误提示优化

**原提示**：
- "即将有赛事PK场次，不能发起PK"
- "对方即将有一场赛事PK场次，不能发起PK"

**新提示**：
- "即将有重要PK场次，不能发起PK"
- "对方即将有一场重要PK场次，不能发起PK"

## 功能特性

### 1. 10分钟限制规则
- **适用范围**：赛事PK（pk_source=1）和活动PK（pk_source=2）
- **限制内容**：开始前10分钟内不能发起或接受任何PK
- **检查时机**：`StartPkMatch`、`ApplyPk` 接口调用时

### 2. 加时取消规则
- **适用范围**：赛事PK（pk_source=1）和活动PK（pk_source=2）
- **处理逻辑**：强制关闭加时功能，确保按时结束
- **实现位置**：PK状态机 `HandlerTask` 方法

### 3. 冲突处理规则
- **适用范围**：赛事PK（pk_source=1）和活动PK（pk_source=2）
- **处理逻辑**：主播在其他PK中时，无法应战重要PK
- **错误提示**："当前还有正在进行的PK，无法应战"

## 代码修改清单

### 1. Proto文件
- `proto/channel-live-mgr.proto`：新增 `APPOINT_PK_SOURCE_ACTIVITY = 2`

### 2. 数据库层
- `mysql/store.go`：
  - 更新 `GetAnchorUpcomingMatchPkList` 查询条件
  - 更新相关注释说明

### 3. 业务逻辑层
- `server/server.go`：
  - 更新错误提示信息
  - 保持10分钟限制检查逻辑不变

- `server/appoint_pk_svr.go`：
  - 更新冲突检查条件，包含活动PK

- `manager/manager.go`：
  - 更新 `isAppointPkFromMatch` 方法，包含活动PK检查
  - 更新加时取消逻辑的注释

### 4. SQL脚本
- `sql/add_pk_source_field.sql`：更新字段注释和索引注释

### 5. 文档
- `MATCH_PK_FEATURE.md`：更新功能说明，体现活动PK支持

## 兼容性说明

### 1. 数据兼容性
- 现有数据：`pk_source` 默认值为 0（普通指定PK），不受影响
- 新增数据：可以设置为 1（赛事PK）或 2（活动PK）

### 2. 接口兼容性
- 现有接口：继续支持，行为不变
- 新增字段：向后兼容，老版本客户端忽略新字段

### 3. 功能兼容性
- 普通指定PK：功能完全不变
- 赛事PK：功能保持不变
- 活动PK：享受与赛事PK相同的特殊逻辑

## 测试要点

### 1. 功能测试
- 验证活动PK的10分钟限制生效
- 验证活动PK的加时取消生效
- 验证活动PK的冲突处理生效
- 验证普通指定PK功能不受影响

### 2. 数据测试
- 验证 `pk_source=2` 的活动PK数据正确保存和查询
- 验证索引查询性能

### 3. 兼容性测试
- 验证老版本客户端正常工作
- 验证新老数据混合场景

## 部署说明

1. **数据库升级**：执行 `sql/add_pk_source_field.sql`
2. **代码部署**：部署更新后的服务
3. **功能验证**：验证活动PK功能正常
4. **监控观察**：观察系统运行状态和性能指标

## 总结

本次变更将活动PK纳入赛事PK的管理范畴，使其享受相同的特殊处理逻辑：
- 10分钟限制规则
- 加时取消规则  
- 冲突处理规则

变更保持了良好的向后兼容性，不影响现有功能，同时为活动PK提供了完整的支持。
