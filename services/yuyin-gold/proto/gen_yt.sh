protoName=yuyin-gold.proto
fileName=yuyin-gold.pb.go

result=`protoc -I=./ --go_out=plugins=grpc:./ ./$protoName`
if [ -n "$result" ] 
then  
echo "protoc err:"  
echo $result  
exit  
fi 
mv golang.52tt.com/protocol/services/yuyingold/$fileName  ../../../protocol/services/yuyingold/$fileName
rm -rf golang.52tt.com
result=`cp $protoName ../../../third-party/tt-protocol/service/quicksilver/yuyin-gold/$protoName`
if [ -n "$result" ] 
then 
echo "cp err:" 
echo $result 
 exit  
fi 
