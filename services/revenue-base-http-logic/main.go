// 货币组-RMB支付结果回调处理程序

package main

import (
	"fmt"
	"golang.52tt.com/services/revenue-base-http-logic/handler/numeric"
	"net/http"
	_ "net/http/pprof"
	"os"

	"golang.52tt.com/services/revenue-base-http-logic/handler"
	user_recall "golang.52tt.com/services/revenue-base-http-logic/handler/user-recall"
	vipprivilege "golang.52tt.com/services/revenue-base-http-logic/handler/vip-privilege"
	virtual_image "golang.52tt.com/services/revenue-base-http-logic/handler/virtual-image"
	you_know_who "golang.52tt.com/services/revenue-base-http-logic/handler/you-know-who"
	"golang.52tt.com/services/revenue-base-http-logic/mgr"

	"golang.52tt.com/pkg/log"

	"github.com/gorilla/mux"
	"github.com/urfave/cli"

	"golang.52tt.com/pkg/versioning/svn" // code revision
	"golang.52tt.com/pkg/web"

	_ "golang.52tt.com/pkg/hub/tyr/compatible/http" // 兼容tyr公共库
)

func main() {

	app := cli.NewApp()
	app.Version = svn.CodeRevision
	app.Flags = []cli.Flag{
		cli.StringFlag{
			Name:  "config",
			Value: "service-config.json",
			Usage: "config path",
		},
	}

	cli.VersionPrinter = func(c *cli.Context) {
		fmt.Fprintf(os.Stdout, "%s\n%s\n", c.App.Name, c.App.Version)
	}

	app.Action = func(c *cli.Context) error {
		log.SetLevel(log.DebugLevel)
		configPath := c.String("config")
		manager := mgr.GetMgr()
		err := manager.Init(configPath)
		if err != nil {
			log.Fatalln("Failed to manager init:", err)
			return err
		}

		r := mux.NewRouter()
		auth := web.NewAuth(&web.UidAuthVerify{}, manager.IsValidateToken())
		webhandler := web.NewHandler(auth, manager.IsCors(), true)
		noAuthHandler := web.NewHandler(nil, manager.IsCors(), true)
		getHandler := web.NewHandler(auth, manager.IsCors(), false)

		//noAuthGetHandler := web.NewHandler(nil, manager.IsCors(), false)
		_ = r.PathPrefix("/b").HandlerFunc(user_recall.GetLink) // .Handle("/", noAuthGetHandler.SetNoAuthHandler(user_recall.GetLink))
		//shortLinkRoute.PathPrefix("/").HandlerFunc(user_recall.GetLink)
		//shortLinkRoute2.Handle("/", noAuthGetHandler.SetNoAuthHandler(user_recall.GetLink))

		base := r.PathPrefix("/revenue-base").Subrouter()

		// 珍宝馆 -接口
		treasure := base.PathPrefix("/treasure-house").Subrouter()
		//名流周榜
		treasure.Handle("/GetTreasurePrivilegeHistory", webhandler.SetHandler(handler.GetTreasurePrivilegeHistory))

		// 虚拟形象
		virtualImage := base.PathPrefix("/virtual-image").Subrouter()
		virtualImage.Handle("/GetBuyCommodityDataSuccess", webhandler.SetHandler(virtual_image.GetBuyCommodityDataSuccess))

		// 用户召回
		userRecall := base.PathPrefix("/user-recall").Subrouter()
		userRecall.Handle("/getRecallList", webhandler.SetHandler(user_recall.GetReCallList))
		userRecall.Handle("/sendRecall", webhandler.SetHandler(user_recall.SendRecall))
		userRecall.Handle("/getRecallPrize", webhandler.SetHandler(user_recall.GetRecallPrize))

		userRecall.Handle("/getBind", webhandler.SetHandler(user_recall.GetBind))
		userRecall.Handle("/getUserInfo", webhandler.SetHandler(user_recall.GetUserInfo))
		userRecall.Handle("/bind", webhandler.SetHandler(user_recall.Bind))

		userRecall.Handle("/getInviteInfo", noAuthHandler.SetNoAuthHandler(user_recall.GetInviteInfo))
		userRecall.Handle("/bind_v2", noAuthHandler.SetNoAuthHandler(user_recall.BindV2))

		// VIP
		{
			vipPrivilege := base.PathPrefix("/vip-privilege").Subrouter()
			// 获取用户VIP礼包入口信息
			vipPrivilege.Handle("/GetUserVipGiftPackageEntrance", webhandler.SetHandler(vipprivilege.GetUserVipGiftPackageEntrance))
			// 获取用户VIP礼包信息
			vipPrivilege.Handle("/GetUserVipGiftPackageInfo", webhandler.SetHandler(vipprivilege.GetUserVipGiftPackageInfo))
			// 领取VIP礼包
			vipPrivilege.Handle("/SubmitReceiveVipGiftPackage", webhandler.SetHandler(vipprivilege.SubmitReceiveVipGiftPackage))
		}

		// 神秘人
		{
			ukwSvrRouter := base.PathPrefix("/youknowwho").Subrouter()
			// 购买神秘人
			ukwSvrRouter.Handle("/buyYKW", webhandler.SetHandler(you_know_who.BuyYKW))
			// 获取神秘人信息
			ukwSvrRouter.Handle("/getYKW", webhandler.SetHandler(you_know_who.GetYKWInfo))
		}

		//  财富魅力榜
		{
			numericRouter := base.PathPrefix("/numeric").Subrouter()
			numericRouter.Handle("/GetRank", getHandler.SetHandler(numeric.GetNumericRank))
		}

		addr := manager.GetHttpListenAddr()
		fmt.Printf("revenue-base-http-logic run at %s\n", addr)
		http.Handle("/", r)
		err = http.ListenAndServe(addr, nil)
		if err != nil {
			log.Errorf("http.ListenAndServe fail, addr:%s, err:%v", addr, err)
			return err
		}
		return nil
	}

	_ = app.Run(os.Args)
}
