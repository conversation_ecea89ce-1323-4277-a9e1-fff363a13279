package numeric

import (
	"context"
	"encoding/json"
	userpresent_go "golang.52tt.com/protocol/services/userpresent-go"
	"io"
	"net/http"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/web"
	"golang.52tt.com/services/revenue-base-http-logic/mgr"
)

const (
	LiveStatusNotInLiving = 0 // 未在直播
	LiveStatusInLiving    = 1 // 直播中
	LiveStatusInPk        = 2 // pk中
	LiveStatusWatchLiving = 3 // 观看直播中
)

type Req struct {
	Sign string `json:"sign"`
}

func GetPresentConfig(w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()
	var err error

	body, err := io.ReadAll(r.Body)
	if err != nil {
		log.Errorf("GetInviteInfo io.ReadAll fail %v", err)
		return
	}

	req := &Req{}
	err = json.Unmarshal(body, req)
	if err != nil {
		log.Errorf("GetInviteInfo Unmarshal fail %v", err)
		web.ServeBadReq(w)
		return
	}

	log.DebugWithCtx(r.Context(), "GetInviteInfo r=%+v req=%+v", r, req)

	if len(req.Sign) == 0 {
		log.Errorf("GetInviteInfo len(req.Token) == 0")
		web.ServeBadReq(w)
		return
	}

	if req.Sign != "52tt" {
		log.Errorf("GetInviteInfo req.Sign error, req.Sign=%s", req.Sign)
		web.ServeBadReq(w)
		return
	}

	//uid := authInfo.UserID
	m := mgr.GetMgr()

	resp, err := m.PresentCli.GetPresentConfigListV3(ctx, &userpresent_go.GetPresentConfigListV3Req{
		TypeBitmap: 0,
		UpdateTime: 0,
	})
	if err != nil {
		_ = web.ServeAPICodeJson(w, -2, "系统错误", nil)
		log.ErrorWithCtx(ctx, "GetPresentConfigListV3 err:%v", err)
	}

	log.InfoWithCtx(ctx, "GetNumericRank resp %+v , err: %+v", resp, err)

	_ = web.ServeAPIJson(w, resp)
}
