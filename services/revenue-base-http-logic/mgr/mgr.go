package mgr

import (
	"context"
	"fmt"
	channelFollow "golang.52tt.com/clients/channel-follow"
	channellivemgr "golang.52tt.com/clients/channel-live-mgr"
	headwear "golang.52tt.com/clients/headwear-go"
	"golang.52tt.com/clients/nobility"
	numericgo "golang.52tt.com/clients/numeric-go"
	numericrank "golang.52tt.com/clients/numeric-rank"
	parent_guardian "golang.52tt.com/clients/parent-guardian"
	perfectmatch "golang.52tt.com/clients/perfect-match"
	"golang.52tt.com/clients/realnameauth"
	risk_mng_api "golang.52tt.com/clients/risk-mng-api"
	unifyPay "golang.52tt.com/clients/unified_pay"
	userpresent_go "golang.52tt.com/clients/userpresent-go"
	usualDevice "golang.52tt.com/clients/usual-device"
	vipprivilege "golang.52tt.com/clients/vipprivilegesvr-go"
	youknowwho "golang.52tt.com/clients/you-know-who"
	youknowwhosettlement "golang.52tt.com/clients/you-know-who-settlement"
	virtual_image_mall "golang.52tt.com/protocol/services/virtual-image-mall"
	virtual_image_resource "golang.52tt.com/protocol/services/virtual-image-resource"
	virtual_image_user "golang.52tt.com/protocol/services/virtual-image-user"
	"time"

	"golang.52tt.com/clients/account"
	fellow "golang.52tt.com/clients/fellow-svr"
	presentprivilege "golang.52tt.com/clients/present-privilege"
	"golang.52tt.com/clients/ugc/friendship"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	userRecallPB "golang.52tt.com/protocol/services/user-recall"
	user_recall_award "golang.52tt.com/protocol/services/user-recall-award"
	"golang.52tt.com/services/glory-http-logic/conf"
	"google.golang.org/grpc"
)

type Manager struct {
	sc                      *config.ServerConfig
	cf                      *conf.StaticConfig
	PresentPrivilegeCli     presentprivilege.IClient
	UserRecallCli           userRecallPB.UserRecallClient
	ChannelFollowCli        channelFollow.IClient
	ChannelLiveMgrCli       channellivemgr.IClient
	FellowCli               fellow.IClient
	HeadWearCli             headwear.IClient
	AccountCli              account.IClient
	UserRecallAwardClient   user_recall_award.UserRecallAwardClient
	UgcfriendshipCli        friendship.IClient
	VipPrivilegeCl          vipprivilege.IClient
	NumericGoCli            numericgo.IClient
	NumericRankCli          numericrank.IClient
	YouKnowWhoClient        youknowwho.IClient
	YouKnowWhoSettlementCli youknowwhosettlement.IClient
	RiskMngApiCli           risk_mng_api.IClient
	NobilityClient          nobility.IClient
	RealNameAuthCli         realnameauth.IClient
	UsualDeviceCli          usualDevice.IClient
	PerfectMatchClient      perfectmatch.IClient
	PayClient               unifyPay.IClient
	VirtualImageResourceCli virtual_image_resource.VirtualImageResourceClient
	VirtualImageMallCli     virtual_image_mall.VirtualImageMallClient
	VirtualImageUserCli     virtual_image_user.VirtualImageUserClient
	ParentGuardianCli       parent_guardian.IClient
	PresentCli              userpresent_go.IClient
}

var mgrInstance = new(Manager)

func GetMgr() *Manager {
	return mgrInstance
}

func (m *Manager) Init(configFile string) error {
	m.sc = config.EmptyServerConfig()
	err := m.sc.InitWithPath("json", configFile)
	if err != nil {
		log.Errorf("Init config err %v", err)
		return err
	}

	m.cf = conf.NewStaticConfig(m.sc)

	dialOpts := make([]grpc.DialOption, 0)

	m.PresentPrivilegeCli = presentprivilege.NewIClient()
	m.FellowCli = fellow.NewIClient(dialOpts...)
	m.AccountCli = account.NewIClient(dialOpts...)
	m.ChannelFollowCli = channelFollow.NewIClient(dialOpts...)
	m.ChannelLiveMgrCli = channellivemgr.NewIClient(dialOpts...)
	m.HeadWearCli = headwear.NewIClient(dialOpts...)
	m.UgcfriendshipCli = friendship.NewIClient(dialOpts...)
	m.VipPrivilegeCl = vipprivilege.NewIClient(dialOpts...)
	m.NumericGoCli = numericgo.NewIClient(dialOpts...)
	m.NumericRankCli = numericrank.NewIClient(dialOpts...)
	m.YouKnowWhoClient = youknowwho.NewIClient(dialOpts...)
	m.YouKnowWhoSettlementCli = youknowwhosettlement.NewIClient(dialOpts...)
	m.RiskMngApiCli = risk_mng_api.NewIClient(dialOpts...)
	m.NobilityClient = nobility.NewIClient(dialOpts...)
	m.RealNameAuthCli = realnameauth.NewIClient(dialOpts...)
	m.UsualDeviceCli = usualDevice.NewIClient(dialOpts...)
	m.PerfectMatchClient = perfectmatch.NewIClient(dialOpts...)
	m.PayClient = unifyPay.NewIClient(dialOpts...)
	m.ParentGuardianCli = parent_guardian.NewIClient(dialOpts...)
	m.PresentCli = userpresent_go.NewIClient(dialOpts...)

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()
	virtualClient, err := virtual_image_resource.NewClient(ctx)
	if err != nil {
		log.Errorf("userRecallPB.NewClient fail %v", err)
		return err
	}
	m.VirtualImageResourceCli = virtualClient

	virtualImageMallCli, err := virtual_image_mall.NewClient(ctx)
	if err != nil {
		log.Errorf("userRecallPB.NewClient fail %v", err)
		return err
	}
	m.VirtualImageMallCli = virtualImageMallCli

	virtualImageUserCli, err := virtual_image_user.NewClient(ctx)
	if err != nil {
		log.Errorf("userRecallPB.NewClient fail %v", err)
		return err
	}
	m.VirtualImageUserCli = virtualImageUserCli

	userRecallCli, err := userRecallPB.NewClient(ctx, dialOpts...)
	if err != nil {
		log.Errorf("userRecallPB.NewClient fail %v", err)
		return err
	}
	m.UserRecallCli = userRecallCli

	UserRecallAwardClient, err := user_recall_award.NewClient(ctx, dialOpts...)
	if err != nil {
		log.Errorf("user_recall_award.NewClient fail %v", err)
		return err
	}
	m.UserRecallAwardClient = UserRecallAwardClient

	fmt.Printf("server config:%+v\n", m.sc)
	return nil
}
func (m *Manager) GetServerConfig() *config.ServerConfig {
	return m.sc
}

func (m *Manager) GetHttpListenAddr() string {
	return m.sc.Listen
}
func (m *Manager) IsValidateToken() bool {
	return m.cf.IsValidateToken
}
func (m *Manager) IsCors() bool {
	return m.cf.IsCors
}
