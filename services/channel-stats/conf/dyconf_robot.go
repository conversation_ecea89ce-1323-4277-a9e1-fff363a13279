package conf

import (
	"context"
	"encoding/json"
	"golang.52tt.com/pkg/files"
	"golang.52tt.com/pkg/log"
	"io/ioutil"
	"strconv"
	"time"
)

const robotConfigFile = "/data/oss/conf-center/tt/channel_robot.json"

var gRobotConf *RobotStu = nil
var gRatioMap = make(map[uint32]*RatioStuInt)
var CommonRatio int64 = 1

type RatioStu struct {
	BeginTime string `json:"begin_time"`
	EndTime string `json:"end_time"`
	ChannelId string `json:"channel_id"`
	Ratio string `json:"ratio"`
}

type RatioStuInt struct {
	BeginTime int64
	EndTime int64
	Ratio int64
}

type RobotStu struct {
	CommonRatio string `json:"common_ratio"`
	SpecialRatioList []*RatioStu `json:"special_ratio_list"`
	ChannelWhite []string `json:"channel_white"`
}

func loadx(){
	data, err := ioutil.ReadFile(robotConfigFile)
	if err != nil {
		return
	}

	ctx := context.Background()
	//log.ErrorWithCtx(ctx,"Parse Unmarshal data:%v", data)

	tmpConf := &RobotStu{}

	err = json.Unmarshal(data, &tmpConf)
	if err != nil {
		log.ErrorWithCtx(ctx,"load conf err:%v", err)
		return
	}

	gRatioMap = make(map[uint32]*RatioStuInt)
	for _, r := range tmpConf.SpecialRatioList {
		cid,_ := strconv.ParseUint(r.ChannelId, 10, 32)
		bts,_ := time.ParseInLocation("2006-01-02 15:04:05", r.BeginTime, time.Local)
		ets,_ := time.ParseInLocation("2006-01-02 15:04:05", r.EndTime, time.Local)
		ratio,_ := strconv.ParseUint(r.Ratio,10,32)
		gRatioMap[uint32(cid)] = &RatioStuInt{
			BeginTime: bts.Unix(),
			EndTime:   ets.Unix(),
			Ratio:     int64(ratio),
		}
	}

	cRatio,_ := strconv.ParseInt(tmpConf.CommonRatio,10,32)
	if cRatio > 0 {
		CommonRatio = cRatio
	}

	log.DebugfWithCtx(ctx, "load %+v", gRatioMap[0])


	gRobotConf = tmpConf
}

func init(){

	loadx()

	watch := files.NewFileModifyWatch(robotConfigFile, time.Second*5)
	go watch.Start(func() {
		loadx()
	})
}

func GetRatio( channelId uint32, sec int64 ) int64 {
	nowTs := time.Now()

	var ratio int64 = 1
/*	if gRobotConf == nil {
		return ratio
	}*/

	r,ok := gRatioMap[channelId]
	if ok {
		//TODO 平滑算法
		if r.BeginTime <= nowTs.Unix() && r.EndTime > nowTs.Unix() {
			smooth := GetSmooth( r.BeginTime, r.EndTime, sec )
			ratio = int64(float64(r.Ratio) * smooth)
			if ratio < 1 {
				ratio = 1
			}
		}
	}

	return ratio
}

func GetSmooth( beginTs,endTs,sec int64 ) float64{
	nowTs := time.Now()

	passSec := nowTs.Unix() - beginTs
	leftSec := endTs - nowTs.Unix()

	var smooth float64 = 1

	if sec <= 0 {
		return smooth
	}

	if passSec > 0 && passSec <= sec {
		smooth = float64(passSec) / float64(sec)
	}

	if leftSec > 0 &&  leftSec <= sec {
		smooth = float64(leftSec) / float64(sec)
	}

	return smooth
}