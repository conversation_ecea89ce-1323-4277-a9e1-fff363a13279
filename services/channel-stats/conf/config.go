package conf

import (
	"encoding/json"
	"fmt"
	"golang.52tt.com/pkg/config"
	"io/ioutil"
)

type ServiceConfigT struct {
	RedisConfig        *config.RedisConfig `json:"redis"`
	MysqlConfig        *config.MysqlConfig `json:"mysql"`
	PresentKafkaConfig *config.KafkaConfig `json:"present_kafka"`
}

func (sc *ServiceConfigT) Parse(configFile string) (err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("Failed to parse config: %v \n", e)
		}
	}()

	data, err := ioutil.ReadFile(configFile)
	if err != nil {
		return err
	}
	err = json.Unmarshal(data, &sc)
	if err != nil {
		return err
	}
	return
}

func (sc *ServiceConfigT) GetPresentKafkaConfig() *config.KafkaConfig {
	return sc.PresentKafkaConfig
}

func (sc *ServiceConfigT) GetMysqlConfig() *config.MysqlConfig {
	return sc.MysqlConfig
}

func (sc *ServiceConfigT) GetMysqlConnectionString() string {
	return sc.MysqlConfig.ConnectionString()
}

func (sc *ServiceConfigT) GetRedisConfig() *config.RedisConfig {
	return sc.RedisConfig
}
