package conf

import (
	"context"
	"encoding/json"
	"golang.52tt.com/pkg/files"
	"golang.52tt.com/pkg/log"
	"io/ioutil"
	"sync"
	"time"
)

const configFile = "/data/oss/conf-center/tt/channel-stats.json"

type ChannelStatsConf struct {
	RecordSec        int64    `json:"record_sec"` //取多少秒之前到现在的记录
	EasyCacheSec     int64    `json:"easy_cache_sec"`
	MemberCntFactor  int64    `json:"member_cnt_factor"`
	PresentFactor    float64  `json:"present_factor"`
	WhiteChannelList []uint32 `json:"white_channel_list"`
	IsPushCPLChannel bool     `json:"is_push_cpl_channel"`
	SmoothSec int64 `json:"smooth_sec"`//多少秒进行平滑
}

var pRWMutex = sync.RWMutex{}
var gConf *ChannelStatsConf
var mapWhiteChannelId = make(map[uint32]bool)

func init() {
	load()

	watch := files.NewFileModifyWatch(configFile, time.Second*5)
	go watch.Start(func() {
		load()
	})
}

func load() {

	data, err := ioutil.ReadFile(configFile)
	if err != nil {
		return
	}

	ctx := context.Background()

	log.ErrorWithCtx(ctx,"Parse Unmarshal data:%v", data)

	tmpConf := &ChannelStatsConf{}

	err = json.Unmarshal(data, &tmpConf)
	if err != nil {
		log.ErrorWithCtx(ctx,"load conf err:%v", err)
		return
	}

	pRWMutex.Lock()
	gConf = tmpConf
	mapWhiteChannelId = make(map[uint32]bool)
	for _, cid := range gConf.WhiteChannelList {
		mapWhiteChannelId[cid] = true
	}
	pRWMutex.Unlock()
}

func GetRecordSec() int64 {
	var sec int64 = 60

	pRWMutex.RLock()
	if nil != gConf && gConf.RecordSec > 0 {
		sec = gConf.RecordSec
	}
	pRWMutex.RUnlock()
	return sec
}

func GetEasyCacheSec() (int64, bool) {
	var sec int64 = 60
	var openCache = false

	pRWMutex.RLock()
	if nil != gConf && gConf.EasyCacheSec > 0 {
		sec = gConf.EasyCacheSec
		openCache = true
	}
	pRWMutex.RUnlock()
	return sec, openCache
}

func GetMemberCntFactor() (int64, float64) {
	var memberCntFactor int64 = 10
	var presentFactor = 0.01
	pRWMutex.RLock()
	if nil != gConf {
		if gConf.MemberCntFactor > 0 {
			memberCntFactor = gConf.MemberCntFactor
		}
		if gConf.PresentFactor > 0 {
			presentFactor = float64(gConf.PresentFactor / 100.0)
		}
	}

	pRWMutex.RUnlock()
	return memberCntFactor, presentFactor
}

func IsWithChannelList(cid uint32) bool {
	hit := false
	if len(mapWhiteChannelId) == 0 {
		return true
	}
	hit = mapWhiteChannelId[cid]

	return hit
}

func IsPushCPLChannel() bool {
	if nil != gConf {
		return gConf.IsPushCPLChannel
	}
	return true
}

func GetSmoothSec() int64 {
	var sec int64 = 0
	if gConf == nil {
		return sec
	}
	if gConf.SmoothSec > 0 {
		sec = gConf.SmoothSec
	}
	return sec
}
