package mysql

import (
	"github.com/golang/mock/gomock"
	"golang.52tt.com/pkg/config"
	"reflect"
	"testing"
)

func TestNewMysql(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()

	cfg := &config.MysqlConfig{
		Host:                      "localhost",
		Port:                      0,
		Protocol:                  "",
		Database:                  "",
		UserName:                  "",
		Password:                  "",
		Charset:                   "",
		PingInterval:              0,
		MaxIdleConns:              0,
		MaxOpenConns:              0,
		SupportPartitionSelection: 0,
		MysqlSDKConfig:            config.MysqlSDKConfig{
			EnableCircuitBreaking: false,
			EnableSlowlogAlarm:    false,
			SlowlogTimeMs:         0,
			EnableApolloConfig:    false,
			ApolloNamespace:       "",
		},
	}
	
	type args struct {
		cfg *config.MysqlConfig
	}
	tests := []struct {
		name    string
		args    args
		want    *Store
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name:    "TestNewMysql",
			args:    args{cfg:cfg},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewMysql(tt.args.cfg)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewMysql() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewMysql() got = %v, want %v", got, tt.want)
			}
		})
	}
}

