package mysql

import (
	"context"
	_ "github.com/go-sql-driver/mysql" // MySQL驱动。
	"github.com/jmoiron/sqlx"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
)



func NewMysql(cfg *config.MysqlConfig) (*Store, error) {
	mysqlDb, err := sqlx.Connect("mysql", cfg.ConnectionString())
	if err != nil {
		log.ErrorWithCtx(context.Background(),"Failed to create mysql: %v", err)
		return nil, err
	}
	return &Store{
		db: mysqlDb,
	}, nil
}

type Store struct {
	db                        *sqlx.DB
}

