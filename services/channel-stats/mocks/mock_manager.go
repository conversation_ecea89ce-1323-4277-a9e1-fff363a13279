// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-stats/manager (interfaces: IManager)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	sarama "github.com/Shopify/sarama"
	gomock "github.com/golang/mock/gomock"
)

// MockIManager is a mock of IManager interface.
type MockIManager struct {
	ctrl     *gomock.Controller
	recorder *MockIManagerMockRecorder
}

// MockIManagerMockRecorder is the mock recorder for MockIManager.
type MockIManagerMockRecorder struct {
	mock *MockIManager
}

// NewMockIManager creates a new mock instance.
func NewMockIManager(ctrl *gomock.Controller) *MockIManager {
	mock := &MockIManager{ctrl: ctrl}
	mock.recorder = &MockIManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIManager) EXPECT() *MockIManagerMockRecorder {
	return m.recorder
}

// BatchGetChannelHotValue mocks base method.
func (m *MockIManager) BatchGetChannelHotValue(arg0 context.Context, arg1 []uint32) (map[uint32]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetChannelHotValue", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetChannelHotValue indicates an expected call of BatchGetChannelHotValue.
func (mr *MockIManagerMockRecorder) BatchGetChannelHotValue(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetChannelHotValue", reflect.TypeOf((*MockIManager)(nil).BatchGetChannelHotValue), arg0, arg1)
}

// BatchGetPresentHotValue mocks base method.
func (m *MockIManager) BatchGetPresentHotValue(arg0 context.Context, arg1 []uint32) ([]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetPresentHotValue", arg0, arg1)
	ret0, _ := ret[0].([]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetPresentHotValue indicates an expected call of BatchGetPresentHotValue.
func (mr *MockIManagerMockRecorder) BatchGetPresentHotValue(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetPresentHotValue", reflect.TypeOf((*MockIManager)(nil).BatchGetPresentHotValue), arg0, arg1)
}

// GetChannelHotValue mocks base method.
func (m *MockIManager) GetChannelHotValue(arg0 context.Context, arg1, arg2 uint32) (int64, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelHotValue", arg0, arg1, arg2)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetChannelHotValue indicates an expected call of GetChannelHotValue.
func (mr *MockIManagerMockRecorder) GetChannelHotValue(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelHotValue", reflect.TypeOf((*MockIManager)(nil).GetChannelHotValue), arg0, arg1, arg2)
}

// GetPresentHotValue mocks base method.
func (m *MockIManager) GetPresentHotValue(arg0 context.Context, arg1 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentHotValue", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentHotValue indicates an expected call of GetPresentHotValue.
func (mr *MockIManagerMockRecorder) GetPresentHotValue(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentHotValue", reflect.TypeOf((*MockIManager)(nil).GetPresentHotValue), arg0, arg1)
}

// HandlerPresentEvent mocks base method.
func (m *MockIManager) HandlerPresentEvent(arg0 *sarama.ConsumerMessage) (error, bool) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandlerPresentEvent", arg0)
	ret0, _ := ret[0].(error)
	ret1, _ := ret[1].(bool)
	return ret0, ret1
}

// HandlerPresentEvent indicates an expected call of HandlerPresentEvent.
func (mr *MockIManagerMockRecorder) HandlerPresentEvent(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandlerPresentEvent", reflect.TypeOf((*MockIManager)(nil).HandlerPresentEvent), arg0)
}

// ShutDown mocks base method.
func (m *MockIManager) ShutDown() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ShutDown")
}

// ShutDown indicates an expected call of ShutDown.
func (mr *MockIManagerMockRecorder) ShutDown() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ShutDown", reflect.TypeOf((*MockIManager)(nil).ShutDown))
}
