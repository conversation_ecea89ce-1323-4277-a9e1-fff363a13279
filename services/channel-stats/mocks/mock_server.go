// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-stats/server (interfaces: IChannelStatsServer)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	channelstats "golang.52tt.com/protocol/services/channelstats"
)

// MockIChannelStatsServer is a mock of IChannelStatsServer interface.
type MockIChannelStatsServer struct {
	ctrl     *gomock.Controller
	recorder *MockIChannelStatsServerMockRecorder
}

// MockIChannelStatsServerMockRecorder is the mock recorder for MockIChannelStatsServer.
type MockIChannelStatsServerMockRecorder struct {
	mock *MockIChannelStatsServer
}

// NewMockIChannelStatsServer creates a new mock instance.
func NewMockIChannelStatsServer(ctrl *gomock.Controller) *MockIChannelStatsServer {
	mock := &MockIChannelStatsServer{ctrl: ctrl}
	mock.recorder = &MockIChannelStatsServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIChannelStatsServer) EXPECT() *MockIChannelStatsServerMockRecorder {
	return m.recorder
}

// BatchGetChannelHotValue mocks base method.
func (m *MockIChannelStatsServer) BatchGetChannelHotValue(arg0 context.Context, arg1 *channelstats.BatchGetChannelHotValueReq) (*channelstats.BatchGetChannelHotValueResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetChannelHotValue", arg0, arg1)
	ret0, _ := ret[0].(*channelstats.BatchGetChannelHotValueResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetChannelHotValue indicates an expected call of BatchGetChannelHotValue.
func (mr *MockIChannelStatsServerMockRecorder) BatchGetChannelHotValue(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetChannelHotValue", reflect.TypeOf((*MockIChannelStatsServer)(nil).BatchGetChannelHotValue), arg0, arg1)
}

// BatchGetPresentHotValue mocks base method.
func (m *MockIChannelStatsServer) BatchGetPresentHotValue(arg0 context.Context, arg1 *channelstats.BatchGetPresentHotValueReq) (*channelstats.BatchGetPresentHotValueResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetPresentHotValue", arg0, arg1)
	ret0, _ := ret[0].(*channelstats.BatchGetPresentHotValueResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetPresentHotValue indicates an expected call of BatchGetPresentHotValue.
func (mr *MockIChannelStatsServerMockRecorder) BatchGetPresentHotValue(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetPresentHotValue", reflect.TypeOf((*MockIChannelStatsServer)(nil).BatchGetPresentHotValue), arg0, arg1)
}

// GetChannelHotValue mocks base method.
func (m *MockIChannelStatsServer) GetChannelHotValue(arg0 context.Context, arg1 *channelstats.GetChannelHotValueReq) (*channelstats.GetChannelHotValueResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelHotValue", arg0, arg1)
	ret0, _ := ret[0].(*channelstats.GetChannelHotValueResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelHotValue indicates an expected call of GetChannelHotValue.
func (mr *MockIChannelStatsServerMockRecorder) GetChannelHotValue(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelHotValue", reflect.TypeOf((*MockIChannelStatsServer)(nil).GetChannelHotValue), arg0, arg1)
}

// GetPresentHotValue mocks base method.
func (m *MockIChannelStatsServer) GetPresentHotValue(arg0 context.Context, arg1 *channelstats.GetPresentHotValueReq) (*channelstats.GetPresentHotValueResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentHotValue", arg0, arg1)
	ret0, _ := ret[0].(*channelstats.GetPresentHotValueResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentHotValue indicates an expected call of GetPresentHotValue.
func (mr *MockIChannelStatsServerMockRecorder) GetPresentHotValue(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentHotValue", reflect.TypeOf((*MockIChannelStatsServer)(nil).GetPresentHotValue), arg0, arg1)
}

// ShutDown mocks base method.
func (m *MockIChannelStatsServer) ShutDown() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ShutDown")
}

// ShutDown indicates an expected call of ShutDown.
func (mr *MockIChannelStatsServerMockRecorder) ShutDown() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ShutDown", reflect.TypeOf((*MockIChannelStatsServer)(nil).ShutDown))
}
