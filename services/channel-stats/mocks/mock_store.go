// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-stats/mysql (interfaces: IStore)

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIStore is a mock of IStore interface.
type MockIStore struct {
	ctrl     *gomock.Controller
	recorder *MockIStoreMockRecorder
}

// MockIStoreMockRecorder is the mock recorder for MockIStore.
type MockIStoreMockRecorder struct {
	mock *MockIStore
}

// NewMockIStore creates a new mock instance.
func NewMockIStore(ctrl *gomock.Controller) *MockIStore {
	mock := &MockIStore{ctrl: ctrl}
	mock.recorder = &MockIStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIStore) EXPECT() *MockIStoreMockRecorder {
	return m.recorder
}

// CreateMysqlTable mocks base method.
func (m *MockIStore) CreateMysqlTable() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateMysqlTable")
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateMysqlTable indicates an expected call of CreateMysqlTable.
func (mr *MockIStoreMockRecorder) CreateMysqlTable() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateMysqlTable", reflect.TypeOf((*MockIStore)(nil).CreateMysqlTable))
}
