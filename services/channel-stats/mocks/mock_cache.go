// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-stats/cache (interfaces: IChannelStatsCache)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIChannelStatsCache is a mock of IChannelStatsCache interface.
type MockIChannelStatsCache struct {
	ctrl     *gomock.Controller
	recorder *MockIChannelStatsCacheMockRecorder
}

// MockIChannelStatsCacheMockRecorder is the mock recorder for MockIChannelStatsCache.
type MockIChannelStatsCacheMockRecorder struct {
	mock *MockIChannelStatsCache
}

// NewMockIChannelStatsCache creates a new mock instance.
func NewMockIChannelStatsCache(ctrl *gomock.Controller) *MockIChannelStatsCache {
	mock := &MockIChannelStatsCache{ctrl: ctrl}
	mock.recorder = &MockIChannelStatsCacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIChannelStatsCache) EXPECT() *MockIChannelStatsCacheMockRecorder {
	return m.recorder
}

// BatchGetPresentHotValue mocks base method.
func (m *MockIChannelStatsCache) BatchGetPresentHotValue(arg0 context.Context, arg1 []uint32) (map[uint32]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetPresentHotValue", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetPresentHotValue indicates an expected call of BatchGetPresentHotValue.
func (mr *MockIChannelStatsCacheMockRecorder) BatchGetPresentHotValue(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetPresentHotValue", reflect.TypeOf((*MockIChannelStatsCache)(nil).BatchGetPresentHotValue), arg0, arg1)
}

// GetPresentHotValue mocks base method.
func (m *MockIChannelStatsCache) GetPresentHotValue(arg0 context.Context, arg1 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentHotValue", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentHotValue indicates an expected call of GetPresentHotValue.
func (mr *MockIChannelStatsCacheMockRecorder) GetPresentHotValue(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentHotValue", reflect.TypeOf((*MockIChannelStatsCache)(nil).GetPresentHotValue), arg0, arg1)
}

// GetPushList mocks base method.
func (m *MockIChannelStatsCache) GetPushList(arg0 context.Context) []uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPushList", arg0)
	ret0, _ := ret[0].([]uint32)
	return ret0
}

// GetPushList indicates an expected call of GetPushList.
func (mr *MockIChannelStatsCacheMockRecorder) GetPushList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPushList", reflect.TypeOf((*MockIChannelStatsCache)(nil).GetPushList), arg0)
}

// Lock mocks base method.
func (m *MockIChannelStatsCache) Lock(arg0 string, arg1 int64) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Lock", arg0, arg1)
	ret0, _ := ret[0].(bool)
	return ret0
}

// Lock indicates an expected call of Lock.
func (mr *MockIChannelStatsCacheMockRecorder) Lock(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Lock", reflect.TypeOf((*MockIChannelStatsCache)(nil).Lock), arg0, arg1)
}

// PopTask mocks base method.
func (m *MockIChannelStatsCache) PopTask() (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PopTask")
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PopTask indicates an expected call of PopTask.
func (mr *MockIChannelStatsCacheMockRecorder) PopTask() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PopTask", reflect.TypeOf((*MockIChannelStatsCache)(nil).PopTask))
}

// PushTaskList mocks base method.
func (m *MockIChannelStatsCache) PushTaskList(arg0 []uint32) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "PushTaskList", arg0)
}

// PushTaskList indicates an expected call of PushTaskList.
func (mr *MockIChannelStatsCacheMockRecorder) PushTaskList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushTaskList", reflect.TypeOf((*MockIChannelStatsCache)(nil).PushTaskList), arg0)
}

// RecordPresent mocks base method.
func (m *MockIChannelStatsCache) RecordPresent(arg0 context.Context, arg1 string, arg2, arg3, arg4, arg5 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordPresent", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordPresent indicates an expected call of RecordPresent.
func (mr *MockIChannelStatsCacheMockRecorder) RecordPresent(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordPresent", reflect.TypeOf((*MockIChannelStatsCache)(nil).RecordPresent), arg0, arg1, arg2, arg3, arg4, arg5)
}
