package event

import (
	"context"
	"github.com/Shopify/sarama"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/ugc/common/event"
)

const (
	topicTypeName = "present_event_v2"
)

type KafkaPresentEvenSubscriber struct {
	*event.KafkaSub
	handler EventHandler
}

func NewPresentEventKafkaSubscriber(clientId, groupId string, topics, brokers []string, handler EventHandler) (*KafkaPresentEvenSubscriber, error) {

	conf := sarama.NewConfig()
	conf.ClientID = clientId
	conf.Consumer.Offsets.Initial = sarama.OffsetNewest
	conf.Consumer.Return.Errors = true

	kafkaSub, err := event.NewKafkaSub(topicTypeName, brokers, groupId, topics, conf)
	if err != nil {
		log.ErrorWithCtx(context.Background(),"Failed to create kafka-subscriber %+v", err)
		return nil, err
	}

	sub := &KafkaPresentEvenSubscriber{
		KafkaSub: kafkaSub,
		handler:  handler,
	}

	sub.SetMessageProcessor(sub.handlerEvent)
	return sub, nil
}

func (s *KafkaPresentEvenSubscriber) Close() {
	s.KafkaSub.Stop()
}

func (s *KafkaPresentEvenSubscriber) handlerEvent(msg *sarama.ConsumerMessage) (error, bool) {
	switch msg.Topic {
	case topicTypeName:
		return s.handlerPresentEvent(msg)
	default:

	}
	return nil, false
}

func (s *KafkaPresentEvenSubscriber) handlerPresentEvent(msg *sarama.ConsumerMessage) (error, bool) {

	err, ok := s.handler.HandlerPresentEvent(msg)
	if nil != err {
		log.ErrorWithCtx(context.Background(),"HandlerPresentEvent err:%v", err)
		return err, ok
	}
	return nil, false
}
