package manager

import(
	context "context"
	sarama "github.com/Shopify/sarama"
)

type IManager interface {
	BatchGetChannelHotValue(ctx context.Context, channelIdList []uint32) (map[uint32]int64, error)
	BatchGetPresentHotValue(ctx context.Context, channelIdList []uint32) ([]int64, error)
	GetChannelHotValue(ctx context.Context, channelId, channelType uint32) (int64, int64, error)
	GetPresentHotValue(ctx context.Context, channelId uint32) (int64, error)
	HandlerPresentEvent(msg *sarama.ConsumerMessage) (error, bool)
	ShutDown() ()
}

