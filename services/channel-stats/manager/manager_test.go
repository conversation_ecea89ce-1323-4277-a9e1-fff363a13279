package manager

import (
	"context"
	"fmt"
	"github.com/Shopify/sarama"
	"github.com/golang/mock/gomock"
	channelsvr "golang.52tt.com/clients/channel"
	"golang.52tt.com/clients/channelol-stat-go"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/app/channel"
	gaPush "golang.52tt.com/protocol/app/push"
	channelsvrpb "golang.52tt.com/protocol/services/channelsvr"
	"golang.52tt.com/clients/channelol"
	pushNotification "golang.52tt.com/clients/push-notification/v2"
	pushPb "golang.52tt.com/protocol/services/push-notification/v2"
	"time"

	"golang.52tt.com/services/channel-stats/cache"
	"golang.52tt.com/services/channel-stats/conf"
	"golang.52tt.com/services/channel-stats/event"
	"golang.52tt.com/services/channel-stats/mocks"

	mock_channelol_stat_go "golang.52tt.com/clients/mocks/channelol-stat-go"
	mock_channel_go "golang.52tt.com/clients/mocks/channel"
	mock_channelol_go "golang.52tt.com/clients/mocks/channelol"
	mock_pushNotification "golang.52tt.com/clients/mocks/push-notification/v2"

	"reflect"
	"testing"
)

func TestManager_BatchGetChannelHotValue(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()

	var cid uint32 = 1024
	presentHotValMap := map[uint32]int64{1024:1024}
	channelsizeMap := map[uint32]uint32{1024:1024}
	channelIdList := []uint32{1024}
	//presentHotVal := []int64{1024}

	csiminfo := &channelsvrpb.ChannelSimpleInfo{
		ChannelId:            &cid,
		DisplayId:            nil,
		AppId:                nil,
		HasPwd:               nil,
		ChannelType:          nil,
		BindId:               nil,
		SwitchFlag:           nil,
		CreaterUid:           nil,
		CreateTs:             nil,
		IconMd5:              nil,
		TopicTitle:           nil,
		Passwd:               nil,
		Name:                 nil,
		IsDel:                nil,
	}
	channelId2Info := map[uint32]*channelsvrpb.ChannelSimpleInfo{1024:csiminfo}

	mockCache := mocks.NewMockIChannelStatsCache(ctl)

	channelCli := mock_channel_go.NewMockIClient(ctl)
	channelOlCli := mock_channelol_go.NewMockIClient(ctl)
	pushCli := mock_pushNotification.NewMockIClient(ctl)
	channelolstaCli := mock_channelol_stat_go.NewMockIClient(ctl)


	//(ctx context.Context, uin uint32, channelIds []uint32) (map[uint32]uint32, protocol.ServerError)
	//(ctx context.Context,channelId uint32) (int64, error)
	gomock.InOrder(
		mockCache.EXPECT().BatchGetPresentHotValue(ctx, channelIdList).Return(presentHotValMap, nil),
		channelolstaCli.EXPECT().BatchGetChannelMemberSize(ctx, channelIdList[0], channelIdList).Return(channelsizeMap, nil),
		channelCli.EXPECT().BatchGetChannelSimpleInfo(ctx, channelIdList[0], channelIdList).Return(channelId2Info, nil),

	)

	type fields struct {
		presentEventSub  *event.KafkaPresentEvenSubscriber
		cache            cache.IChannelStatsCache
		channelCli       channelsvr.IClient
		channelOlCli     channelol.IClient
		pushCli          pushNotification.IClient
		channelOLStatCli channelol_stat_go.IClient
	}
	type args struct {
		ctx           context.Context
		channelIdList []uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    map[uint32]int64
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name:    "TestManager_BatchGetChannelHotValue",
			fields:  fields{
				presentEventSub:  nil,
				cache:            mockCache,
				channelCli:       channelCli,
				channelOlCli:     channelOlCli,
				pushCli:          pushCli,
				channelOLStatCli: channelolstaCli,
			},
			args:    args{
				ctx:           ctx,
				channelIdList: channelIdList,
			},
			want:    presentHotValMap,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				presentEventSub:  tt.fields.presentEventSub,
				cache:            tt.fields.cache,
				channelCli:       tt.fields.channelCli,
				channelOlCli:     tt.fields.channelOlCli,
				pushCli:          tt.fields.pushCli,
				channelOLStatCli: tt.fields.channelOLStatCli,
			}
			got, err := m.BatchGetChannelHotValue(tt.args.ctx, tt.args.channelIdList)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchGetChannelHotValue() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BatchGetChannelHotValue() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_BatchGetPresentHotValue(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()

	presentHotValMap := map[uint32]int64{1024:1024}
	channelIdList := []uint32{1024}
	presentHotVal := []int64{1024}

	mockCache := mocks.NewMockIChannelStatsCache(ctl)

	mockChannelCli := mock_channel_go.NewMockIClient(ctl)
	mockChannelOlCli := mock_channelol_go.NewMockIClient(ctl)
	mockPushCli := mock_pushNotification.NewMockIClient(ctl)
	mockChannelolstaCli := mock_channelol_stat_go.NewMockIClient(ctl)

	gomock.InOrder(
		mockCache.EXPECT().BatchGetPresentHotValue(ctx, channelIdList).Return(presentHotValMap, nil),
	)
	
	type fields struct {
		presentEventSub  *event.KafkaPresentEvenSubscriber
		cache            cache.IChannelStatsCache
		channelCli       channelsvr.IClient
		channelOlCli     channelol.IClient
		pushCli          pushNotification.IClient
		channelOLStatCli channelol_stat_go.IClient
	}
	type args struct {
		ctx           context.Context
		channelIdList []uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []int64
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name:    "TestManager_BatchGetPresentHotValue",
			fields:  fields{
				presentEventSub:  nil,
				cache:            mockCache,
				channelCli:       mockChannelCli,
				channelOlCli:     mockChannelOlCli,
				pushCli:          mockPushCli,
				channelOLStatCli: mockChannelolstaCli,
			},
			args:    args{
				ctx:           ctx,
				channelIdList: channelIdList,
			},
			want:    presentHotVal,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				presentEventSub:  tt.fields.presentEventSub,
				cache:            tt.fields.cache,
				channelCli:       tt.fields.channelCli,
				channelOlCli:     tt.fields.channelOlCli,
				pushCli:          tt.fields.pushCli,
				channelOLStatCli: tt.fields.channelOLStatCli,
			}
			got, err := m.BatchGetPresentHotValue(tt.args.ctx, tt.args.channelIdList)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchGetPresentHotValue() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BatchGetPresentHotValue() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_GetChannelHotValue(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()

	var onlineMemberCnt,channelId,channelType uint32 = 1,1024,7
	var channelHotVal,memRatio,presentHotVal int64 = 10, 10,0

	mockCache := mocks.NewMockIChannelStatsCache(ctl)

	mockChannelCli := mock_channel_go.NewMockIClient(ctl)
	mockChannelOlCli := mock_channelol_go.NewMockIClient(ctl)
	mockPushCli := mock_pushNotification.NewMockIClient(ctl)
	mockChannelolstaCli := mock_channelol_stat_go.NewMockIClient(ctl)

	gomock.InOrder(
		mockCache.EXPECT().GetPresentHotValue(ctx, channelId).Return(presentHotVal, nil),
		mockChannelOlCli.EXPECT().GetChannelMemberSize(ctx,channelId,channelId).Return(onlineMemberCnt,nil),
	)

	type fields struct {
		presentEventSub  *event.KafkaPresentEvenSubscriber
		cache            cache.IChannelStatsCache
		channelCli       channelsvr.IClient
		channelOlCli     channelol.IClient
		pushCli          pushNotification.IClient
		channelOLStatCli channelol_stat_go.IClient
	}
	type args struct {
		ctx         context.Context
		channelId   uint32
		channelType uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    int64
		want1   int64
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name:    "",
			fields:  fields{
				presentEventSub:  nil,
				cache:            mockCache,
				channelCli:       mockChannelCli,
				channelOlCli:     mockChannelOlCli,
				pushCli:          mockPushCli,
				channelOLStatCli: mockChannelolstaCli,
			},
			args:    args{
				ctx:         ctx,
				channelId:   channelId,
				channelType: channelType,
			},
			want:    channelHotVal,
			want1:   memRatio,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				presentEventSub:  tt.fields.presentEventSub,
				cache:            tt.fields.cache,
				channelCli:       tt.fields.channelCli,
				channelOlCli:     tt.fields.channelOlCli,
				pushCli:          tt.fields.pushCli,
				channelOLStatCli: tt.fields.channelOLStatCli,
			}
			got, got1, err := m.GetChannelHotValue(tt.args.ctx, tt.args.channelId, tt.args.channelType)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetChannelHotValue() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetChannelHotValue() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("GetChannelHotValue() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestManager_GetPresentHotValue(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()

	var channelId uint32 = 1024
	var presentHotVal int64 = 10

	mockCache := mocks.NewMockIChannelStatsCache(ctl)

	mockChannelCli := mock_channel_go.NewMockIClient(ctl)
	mockChannelOlCli := mock_channelol_go.NewMockIClient(ctl)
	mockPushCli := mock_pushNotification.NewMockIClient(ctl)
	mockChannelolstaCli := mock_channelol_stat_go.NewMockIClient(ctl)

	gomock.InOrder(
		mockCache.EXPECT().GetPresentHotValue(ctx, channelId).Return(presentHotVal, nil),
	)
	
	type fields struct {
		presentEventSub  *event.KafkaPresentEvenSubscriber
		cache            cache.IChannelStatsCache
		channelCli       channelsvr.IClient
		channelOlCli     channelol.IClient
		pushCli          pushNotification.IClient
		channelOLStatCli channelol_stat_go.IClient
	}
	type args struct {
		ctx       context.Context
		channelId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    int64
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name:    "TestManager_GetPresentHotValue",
			fields:  fields{
				presentEventSub:  nil,
				cache:            mockCache,
				channelCli:       mockChannelCli,
				channelOlCli:     mockChannelOlCli,
				pushCli:          mockPushCli,
				channelOLStatCli: mockChannelolstaCli,
			},
			args:    args{
				ctx:       ctx,
				channelId: channelId,
			},
			want:    presentHotVal,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				presentEventSub:  tt.fields.presentEventSub,
				cache:            tt.fields.cache,
				channelCli:       tt.fields.channelCli,
				channelOlCli:     tt.fields.channelOlCli,
				pushCli:          tt.fields.pushCli,
				channelOLStatCli: tt.fields.channelOLStatCli,
			}
			got, err := m.GetPresentHotValue(tt.args.ctx, tt.args.channelId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPresentHotValue() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetPresentHotValue() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_HandlerPresentEvent(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()

	//ctx := context.Background()

	//var channelId uint32 = 1024
	//var presentHotVal int64 = 10

	mockCache := mocks.NewMockIChannelStatsCache(ctl)

	mockChannelCli := mock_channel_go.NewMockIClient(ctl)
	mockChannelOlCli := mock_channelol_go.NewMockIClient(ctl)
	mockPushCli := mock_pushNotification.NewMockIClient(ctl)
	mockChannelolstaCli := mock_channelol_stat_go.NewMockIClient(ctl)

	gomock.InOrder(
		//mockCache.EXPECT().GetPresentHotValue(ctx, channelId).Return(presentHotVal, nil),
	)

	msg := &sarama.ConsumerMessage{
		Headers:        nil,
		Timestamp:      time.Time{},
		BlockTimestamp: time.Time{},
		Key:            nil,
		Value:          nil,
		Topic:          "",
		Partition:      0,
		Offset:         0,
	}

	type fields struct {
		presentEventSub  *event.KafkaPresentEvenSubscriber
		cache            cache.IChannelStatsCache
		channelCli       channelsvr.IClient
		channelOlCli     channelol.IClient
		pushCli          pushNotification.IClient
		channelOLStatCli channelol_stat_go.IClient
	}
	type args struct {
		msg *sarama.ConsumerMessage
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   error
		want1  bool
	}{
		// TODO: Add test cases.
		{
			name:   "TestManager_HandlerPresentEvent",
			fields: fields{
				presentEventSub:  nil,
				cache:            mockCache,
				channelCli:       mockChannelCli,
				channelOlCli:     mockChannelOlCli,
				pushCli:          mockPushCli,
				channelOLStatCli: mockChannelolstaCli,
			},
			args:   args{msg:msg},
			want:   nil,
			want1:  false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				presentEventSub:  tt.fields.presentEventSub,
				cache:            tt.fields.cache,
				channelCli:       tt.fields.channelCli,
				channelOlCli:     tt.fields.channelOlCli,
				pushCli:          tt.fields.pushCli,
				channelOLStatCli: tt.fields.channelOLStatCli,
			}
			got, got1 := m.HandlerPresentEvent(tt.args.msg)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("HandlerPresentEvent() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("HandlerPresentEvent() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestManager_ShutDown(t *testing.T) {
	type fields struct {
		presentEventSub  *event.KafkaPresentEvenSubscriber
		cache            *cache.ChannelStatsCache
		channelCli       channelsvr.IClient
		channelOlCli     channelol.IClient
		pushCli          pushNotification.IClient
		channelOLStatCli channelol_stat_go.IClient
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
/*			m := &Manager{
				presentEventSub:  tt.fields.presentEventSub,
				cache:            tt.fields.cache,
				store:            tt.fields.store,
				channelCli:       tt.fields.channelCli,
				channelOlCli:     tt.fields.channelOlCli,
				pushCli:          tt.fields.pushCli,
				channelOLStatCli: tt.fields.channelOLStatCli,
			}*/
		})
	}
}

func TestManager_dopush(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()

	//ctx := context.Background()
	//var channelId,memberSz uint32 = 1024,0
	//var presentHotVal int64 = 10

	//mockCache := mocks.NewMockIChannelStatsCache(ctl)

	//mockChannelCli := mock_channel_go.NewMockIClient(ctl)
	//mockChannelOlCli := mock_channelol_go.NewMockIClient(ctl)
/*	mockPushCli := mock_pushNotification.NewMockIClient(ctl)
	mockChannelolstaCli := mock_channelol_stat_go.NewMockIClient(ctl)*/

	gomock.InOrder(
		//mockCache.EXPECT().GetPresentHotValue(ctx, channelId).Return(presentHotVal, nil),
		//mockCache.EXPECT().Lock("channel_stats_loop_push_lock",3).Return(true),
		//mockChannelOlCli.EXPECT().GetChannelMemberSize(ctx, channelId, channelId).Return(memberSz, nil),
	)
	
	type fields struct {
		presentEventSub  *event.KafkaPresentEvenSubscriber
		cache            cache.IChannelStatsCache
		channelCli       channelsvr.IClient
		channelOlCli     channelol.IClient
		pushCli          pushNotification.IClient
		channelOLStatCli channelol_stat_go.IClient
	}
	type args struct {
		cid uint32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		// TODO: Add test cases.
/*		{
			name:   "TestManager_dopush",
			fields: fields{
				presentEventSub:  nil,
				cache:            mockCache,
				channelCli:       mockChannelCli,
				channelOlCli:     mockChannelOlCli,
				pushCli:          mockPushCli,
				channelOLStatCli: mockChannelolstaCli,
			},
			args:   args{},
		},*/
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
/*			m := &Manager{
				presentEventSub:  tt.fields.presentEventSub,
				cache:            tt.fields.cache,
				channelCli:       tt.fields.channelCli,
				channelOlCli:     tt.fields.channelOlCli,
				pushCli:          tt.fields.pushCli,
				channelOLStatCli: tt.fields.channelOLStatCli,
			}*/
			//m.dopush(ctx, channelId)
		})
	}
}

func TestManager_loopPush(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()

	//ctx := context.Background()

	/*var channelId uint32 = 1024
	var presentHotVal int64 = 10*/

	//key := "channel_stats_loop_push_lock"
	//var expire int64 = 3

	//channelList := []uint32{}

	mockCache := mocks.NewMockIChannelStatsCache(ctl)

	mockChannelCli := mock_channel_go.NewMockIClient(ctl)
	mockChannelOlCli := mock_channelol_go.NewMockIClient(ctl)
	mockPushCli := mock_pushNotification.NewMockIClient(ctl)
	mockChannelolstaCli := mock_channelol_stat_go.NewMockIClient(ctl)

	gomock.InOrder(
		////mockCache.EXPECT().GetPushList(ctx).Return(channelList),
		//mockCache.EXPECT().Lock(key,expire).Return(true),
	)

	type fields struct {
		presentEventSub  *event.KafkaPresentEvenSubscriber
		cache            cache.IChannelStatsCache
		channelCli       channelsvr.IClient
		channelOlCli     channelol.IClient
		pushCli          pushNotification.IClient
		channelOLStatCli channelol_stat_go.IClient
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// TODO: Add test cases.
		{
			name:   "TestManager_loopPush",
			fields: fields{
				presentEventSub:  nil,
				cache:            mockCache,
				channelCli:       mockChannelCli,
				channelOlCli:     mockChannelOlCli,
				pushCli:          mockPushCli,
				channelOLStatCli: mockChannelolstaCli,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
/*			m := &Manager{
				presentEventSub:  nil,
				cache:            mockCache,
				channelCli:       mockChannelCli,
				channelOlCli:     mockChannelOlCli,
				pushCli:          mockPushCli,
				channelOLStatCli: mockChannelolstaCli,
			}*/
			//m.loopPush(ctx,key,expire)
		})
	}
}

func TestManager_pushChannelMsg(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()

	var channelId,cmd uint32 = 1024,32

	msg := []byte{}

	mockCache := mocks.NewMockIChannelStatsCache(ctl)

	mockChannelCli := mock_channel_go.NewMockIClient(ctl)
	mockChannelOlCli := mock_channelol_go.NewMockIClient(ctl)
	mockPushCli := mock_pushNotification.NewMockIClient(ctl)
	mockChannelolstaCli := mock_channelol_stat_go.NewMockIClient(ctl)

	bMsg := channel.ChannelBroadcastMsg{
		FromUid:      10000,
		FromAccount:  "ttyuyinzhushou",
		FromNick:     "",
		ToChannelId:  channelId,
		Type:         cmd,
		Content:      []byte(""),
		Time:         uint64(time.Now().Unix()),
		PbOptContent: msg,
	}

	bMsgBin, _ := bMsg.Marshal()

	pmsg := &gaPush.PushMessage{
		Cmd:     uint32(gaPush.PushMessage_CHANNEL_MSG_BRO),
		Content: bMsgBin,
		SeqId:   uint32(time.Now().Unix()) + channelId,
	}

	pmsgbin, _ := pmsg.Marshal()

	pushPbmgg := &pushPb.CompositiveNotification{
		Sequence: uint32(time.Now().Unix()) + channelId,
		TerminalTypeList: []uint32{
			protocol.MobileAndroidTT,
			protocol.MobileIPhoneTT,
		},
		TerminalTypePolicy: pushNotification.DefaultPolicy,
		AppId:              uint32(protocol.TT),
		ProxyNotification: &pushPb.ProxyNotification{
			Type:       uint32(pushPb.ProxyNotification_PUSH),
			Payload:    pmsgbin,
			Policy:     pushPb.ProxyNotification_DEFAULT,
			ExpireTime: 600,
		},
	}

	gomock.InOrder(
		mockPushCli.EXPECT().PushMulticast(ctx, uint64(channelId), fmt.Sprintf("%d@channel", channelId),[]uint32{}, pushPbmgg).Return( nil),
	)

	type fields struct {
		presentEventSub  *event.KafkaPresentEvenSubscriber
		cache            cache.IChannelStatsCache
		channelCli       channelsvr.IClient
		channelOlCli     channelol.IClient
		pushCli          pushNotification.IClient
		channelOLStatCli channelol_stat_go.IClient
	}
	type args struct {
		ctx context.Context
		msg []byte
		cmd uint32
		cid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name:    "TestManager_pushChannelMsg",
			fields:  fields{
				presentEventSub:  nil,
				cache:            mockCache,
				channelCli:       mockChannelCli,
				channelOlCli:     mockChannelOlCli,
				pushCli:          mockPushCli,
				channelOLStatCli: mockChannelolstaCli,
			},
			args:    args{
				ctx: ctx,
				msg: msg,
				cmd: cmd,
				cid: channelId,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				presentEventSub:  tt.fields.presentEventSub,
				cache:            tt.fields.cache,
				channelCli:       tt.fields.channelCli,
				channelOlCli:     tt.fields.channelOlCli,
				pushCli:          tt.fields.pushCli,
				channelOLStatCli: tt.fields.channelOLStatCli,
			}
			if err := m.pushChannelMsg(tt.args.ctx, tt.args.msg, tt.args.cmd, tt.args.cid); (err != nil) != tt.wantErr {
				t.Errorf("pushChannelMsg() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestNewManager(t *testing.T) {

	ctx := context.Background()

	type args struct {
		ctx context.Context
		sc  *conf.ServiceConfigT
	}

	sc := &conf.ServiceConfigT{
		RedisConfig:        &config.RedisConfig{
			Host:           "",
			Port:           0,
			Protocol:       "",
			PingInterval:   0,
			PoolSize:       0,
			Password:       "",
			DB:             0,
			DialTimeout:    0,
			ReadTimeout:    0,
			WriteTimeout:   0,
			RedisSDKConfig: config.RedisSDKConfig{},
		},
		MysqlConfig:        &config.MysqlConfig{
			Host:                      "",
			Port:                      0,
			Protocol:                  "",
			Database:                  "",
			UserName:                  "",
			Password:                  "",
			Charset:                   "",
			PingInterval:              0,
			MaxIdleConns:              0,
			MaxOpenConns:              0,
			SupportPartitionSelection: 0,
			MysqlSDKConfig:            config.MysqlSDKConfig{},
		},
		PresentKafkaConfig: &config.KafkaConfig{
			Brokers:      "xx",
			GroupID:      "",
			Topics:       "",
			ClientID:     "",
			SASLEnable:   false,
			SASLUser:     "",
			SASLPassword: "",
		},
	}
	tests := []struct {
		name    string
		args    args
		want    *Manager
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name:    "TestNewManager",
			args:    args{
				ctx: ctx,
				sc:  sc,
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			NewManager(tt.args.ctx, tt.args.sc)
		})
	}
}

func Test_channelHotValKey(t *testing.T) {
	type args struct {
		cid uint32
	}
	tests := []struct {
		name  string
		args  args
		want  string
		want1 string
	}{
		// TODO: Add test cases.
		{
			name:  "Test_channelHotValKey",
			args:  args{cid: 10},
			want:  "get_channel_hot_value_10",
			want1: "get_channel_hot_memfactory_10",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := channelHotValKey(tt.args.cid)
			if got != tt.want {
				t.Errorf("channelHotValKey() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("channelHotValKey() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func Test_presentValKey(t *testing.T) {
	type args struct {
		channelId uint32
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
		{
			name: "Test_presentValKey",
			args: args{channelId: 10},
			want: "GetPresentHotValue_10",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := presentValKey(tt.args.channelId); got != tt.want {
				t.Errorf("presentValKey() = %v, want %v", got, tt.want)
			}
		})
	}
}