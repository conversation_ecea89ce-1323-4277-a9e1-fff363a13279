package manager

import (
	"context"
	"fmt"
	"github.com/Shopify/sarama"
	"github.com/go-redis/redis"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	channelsvr "golang.52tt.com/clients/channel"
	"golang.52tt.com/clients/channelol"
	channelol_stat_go "golang.52tt.com/clients/channelol-stat-go"
	pushNotification "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/pkg/coroutine"
	"golang.52tt.com/pkg/easycache"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	tracing "golang.52tt.com/pkg/tracing/jaeger"
	"golang.52tt.com/protocol/app/channel"
	channelga "golang.52tt.com/protocol/app/channel"
	gaPush "golang.52tt.com/protocol/app/push"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkapresent"
	pushPb "golang.52tt.com/protocol/services/push-notification/v2"
	"golang.52tt.com/protocol/services/userpresent"
	"golang.52tt.com/services/channel-stats/cache"
	"golang.52tt.com/services/channel-stats/conf"
	"golang.52tt.com/services/channel-stats/event"
	"golang.52tt.com/services/channel-stats/util"
	"math/rand"
	"time"
)

var easyCache = easycache.NewSmapCache()

type Manager struct {
	presentEventSub *event.KafkaPresentEvenSubscriber
	cache           cache.IChannelStatsCache

	channelCli  channelsvr.IClient
	channelOlCli channelol.IClient
	pushCli      pushNotification.IClient
	channelOLStatCli channelol_stat_go.IClient
}

func NewManager(ctx context.Context,sc *conf.ServiceConfigT) (IManager, error) {
	rand.Seed(time.Now().UnixNano())

	redisClient := redis.NewClient(&redis.Options{
		Network:            sc.GetRedisConfig().Protocol,
		Addr:               sc.GetRedisConfig().Addr(),
		PoolSize:           sc.GetRedisConfig().PoolSize,
		IdleCheckFrequency: sc.GetRedisConfig().IdleCheckFrequency(),
		DB:                 sc.GetRedisConfig().DB,
	})
	log.DebugfWithCtx(ctx,"Initialized redis connection pool to %s://%s/%d", sc.GetRedisConfig().Protocol, sc.GetRedisConfig().Addr(), sc.GetRedisConfig().DB)
	redisTracer := tracing.Init("channel-stats_redis")
	cacheClient := cache.NewChannelStatsCache(redisClient, redisTracer)

	channelOLStatCli, err := channelol_stat_go.NewClient()
	if nil != err {
		log.ErrorWithCtx(ctx, "channelol_stat_go NewClient err:%v",err)
		return nil,err
	}
	channelOlCli := channelol.NewClient()
	pushCli, err := pushNotification.NewClient()
	if nil != err {
		log.ErrorWithCtx(ctx,"pushNotification.NewClient err:%v", err)
		return nil, err
	}

	channelCli := channelsvr.NewClient()

	mgr := &Manager{
		cache:        cacheClient,
		channelOlCli: channelOlCli,
		pushCli:      pushCli,
		channelCli:channelCli,
		channelOLStatCli:channelOLStatCli,
	}

	presentEventSub, err := event.NewPresentEventKafkaSubscriber(sc.GetPresentKafkaConfig().ClientID, sc.GetPresentKafkaConfig().GroupID,
		sc.GetPresentKafkaConfig().TopicList(), sc.GetPresentKafkaConfig().BrokerList(), mgr)
	if err != nil {
		log.ErrorWithCtx(ctx,"Failed to NewPresentEventKafkaSubscriber err %s", err.Error())
		return nil, err
	}

	err = presentEventSub.Start()
	if err != nil {
		log.ErrorWithCtx(ctx,"Failed to Start presentEventSuberr %s", err.Error())
		return nil, err
	}

	mgr.presentEventSub = presentEventSub

	coroutine.FixIntervalExec(mgr.loopPush, time.Second*2)

	return mgr, nil
}

//定时推送
func (m *Manager) loopPush() {
	lock := m.cache.Lock("channel_stats_loop_push_lock", 3)
	if ! lock {
		return
	}
	ctx := context.Background()
	t := time.Now()

	channelIdList := m.cache.GetPushList(ctx)

	log.DebugfWithCtx(ctx,"loopPush begin channelIdList sz:%v", len(channelIdList))

	for _, cid := range channelIdList {
		m.dopush(ctx, cid)
	}
	elapsed := time.Since(t)

	if elapsed >= 200*time.Millisecond {
		log.InfoWithCtx(ctx,"loopPush end elapsed:%v", elapsed)
	}
}

func (m *Manager) pushChannelMsg(ctx context.Context, msg []byte, cmd uint32, cid uint32) error {
	log.DebugfWithCtx(ctx,"pushChannelMsg channelid %v cmd:%v", cid, cmd)

	bMsg := channel.ChannelBroadcastMsg{
		FromUid:      10000,
		FromAccount:  "ttyuyinzhushou",
		FromNick:     "",
		ToChannelId:  cid,
		Type:         cmd,
		Content:      []byte(""),
		Time:         uint64(time.Now().Unix()),
		PbOptContent: msg,
	}

	log.DebugfWithCtx(ctx,"PushChannelMsg len msg %d", len(msg))

	bMsgBin, err := bMsg.Marshal()

	if err != nil {
		log.ErrorWithCtx(ctx,"PushChannelMsg bMsg err:%v", err)
	}

	pmsg := &gaPush.PushMessage{
		Cmd:     uint32(gaPush.PushMessage_CHANNEL_MSG_BRO),
		Content: bMsgBin,
		SeqId:   uint32(time.Now().Unix()) + cid,
	}

	pmsgbin, err := pmsg.Marshal()

	if err != nil {
		log.ErrorWithCtx(ctx,"ChannelLiveLogic_ Marshal err:%v", err)
	}

	err = m.pushCli.PushMulticast(ctx, uint64(cid), fmt.Sprintf("%d@channel", cid), []uint32{}, &pushPb.CompositiveNotification{
		Sequence: uint32(time.Now().Unix()) + cid,
		TerminalTypeList: []uint32{
			protocol.MobileAndroidTT,
			protocol.MobileIPhoneTT,
		},
		TerminalTypePolicy: pushNotification.DefaultPolicy,
		AppId:              uint32(protocol.TT),
		ProxyNotification: &pushPb.ProxyNotification{
			Type:       uint32(pushPb.ProxyNotification_PUSH),
			Payload:    pmsgbin,
			Policy:     pushPb.ProxyNotification_DEFAULT,
			ExpireTime: 600,
		},
	})

	if err != nil {
		log.ErrorWithCtx(ctx,"ChannelLiveLogic_ PushChannelMsg fail err:%v", err)
	}

	return nil
}

func (m *Manager) dopush(ctx context.Context,cid uint32) {

	//添加白名单，灰度期间只推白名单房间
	if ! conf.IsWithChannelList(cid) {
		return
	}

	memberSz, err := m.channelOlCli.GetChannelMemberSize(ctx, cid, cid)
	if nil != err {
		log.InfoWithCtx(ctx, "dopush GetChannelMemberSize cid:%v err:%v",cid, err)
		return
	}
	memberCntFactor, _ := conf.GetMemberCntFactor()
	presentHotValue, _ := m.GetPresentHotValue(ctx,cid)
	channelHotValue,_ := util.GetChannelHotValueWithRatio(ctx,cid,memberSz, uint32(presentHotValue), uint32(channelga.ChannelType_RADIO_LIVE_CHANNEL_TYPE))

	channelHotValuePushMsg := &channel.ChannelHotValueNotifyMsg{
		ChannelId:       cid,
		HotValue:       uint32(channelHotValue) ,
		MemberCntFactor: uint32(memberCntFactor),
	}

	binMsg, perr := proto.Marshal(channelHotValuePushMsg)
	if nil != perr {
		log.ErrorWithCtx(ctx,"dopush Marshal err:%v", err)
		return
	}
	serr := m.pushChannelMsg(ctx, binMsg, uint32(channelga.ChannelMsgType_CHANNEL_HOT_VALUE_MSG), cid)
	if nil != serr {
		log.ErrorWithCtx(ctx,"dopush pushChannelMsg err:%v", err)
		return
	}

	log.InfoWithCtx(ctx,"dopush cid:%v memberSz:%v presentVal:%v channelHotValue:%v", cid, memberSz, presentHotValue, channelHotValue)
}

func (m *Manager) HandlerPresentEvent(msg *sarama.ConsumerMessage) (error, bool) {
	ctx := context.Background()
	presentEvent := &kafkapresent.PresentEvent{}
	err := proto.Unmarshal(msg.Value, presentEvent)
	if err != nil {
		log.ErrorWithCtx(ctx," handlerPresentEvent Failed to proto.Unmarshal err(%v)", err)
		return err, false
	}

	if userpresent.PresentPriceType(presentEvent.PriceType) != userpresent.PresentPriceType_PRESENT_PRICE_TBEAN {
		return nil, false
	}

	// 过滤房间
	channelType := channel.ChannelType(presentEvent.ChannelType)
	invalidChannelType := util.IsNeedPresentHotValue(uint32(channelType))
	if ! invalidChannelType {
		return nil, false
	}

	err = m.cache.RecordPresent(ctx,presentEvent.OrderId, presentEvent.ChannelId, presentEvent.ChannelType, presentEvent.SendTime, presentEvent.Price*presentEvent.ItemCount)
	if nil != err {
		log.ErrorWithCtx(ctx,"handlerPresentEvent RecordPresent presentEvent:%+v err:%v", presentEvent, err)
		return err, false
	}

	return nil, false
}

func presentValKey(channelId uint32) string {
	return fmt.Sprintf("GetPresentHotValue_%v", channelId)
}

func (m *Manager) GetPresentHotValue(ctx context.Context,channelId uint32) (int64, error) {
	sec, openCache := conf.GetEasyCacheSec()
	if openCache {
		v, ok := easyCache.Get(presentValKey(channelId))
		if ok {
			val, ok := v.(int64)
			if ok {
				return val, nil
			}
		}
	}

	val, err := m.cache.GetPresentHotValue(ctx,channelId)

	easyCache.Set(presentValKey(channelId), val, sec)

	return val, err
}

func (m *Manager) BatchGetPresentHotValue(ctx context.Context,channelIdList []uint32) ([]int64, error) {
	presentHotValMap := make(map[uint32]int64)
	cacheLackChannelIdList := make([]uint32, 0)

	sec, openCache := conf.GetEasyCacheSec()
	if openCache {
		for _, cid := range channelIdList {
			hit := false
			v, ok := easyCache.Get(presentValKey(cid))
			if ok {
				val, ok := v.(int64)
				if ok {
					presentHotValMap[cid] = val
				}
			}
			if ! hit {
				cacheLackChannelIdList = append(cacheLackChannelIdList, cid)
			}
		}
	} else {
		cacheLackChannelIdList = channelIdList
	}

	if len(cacheLackChannelIdList) > 0 {
		presentHotValMap,_ = m.cache.BatchGetPresentHotValue(ctx,cacheLackChannelIdList)
		for _, cid := range cacheLackChannelIdList {
			easyCache.Set(presentValKey(cid), presentHotValMap[cid], sec)
		}
	}

	presentHotValList := make([]int64, 0)
	for _, cid := range channelIdList {
		presentHotValList = append(presentHotValList, presentHotValMap[cid])
	}

	return presentHotValList, nil
}

func channelHotValKey(cid uint32) (string,string) {
	return fmt.Sprintf("get_channel_hot_value_%v", cid), fmt.Sprintf("get_channel_hot_memfactory_%v",cid)
}

func (m *Manager) GetChannelHotValue(ctx context.Context ,channelId,channelType uint32)(int64,int64,error) {
	vkey,fkey := channelHotValKey(channelId)

	hotVal,ok1 :=easyCache.Get(vkey)
	fVal,ok2 := easyCache.Get(fkey)
	if ok1 && ok2 {
		val, ok1 := hotVal.(int64)
		fval,ok2 := fVal.(int64)
		if ok1 && ok2 {
			return val,fval,nil
		}
	}

	presentVal, _ := m.cache.GetPresentHotValue(ctx,channelId)
	onlineMemberCnt, err := m.channelOlCli.GetChannelMemberSize(ctx, channelId, channelId)
	if nil != err {
		log.ErrorWithCtx(ctx, "GetChannelHotValue GetChannelMemberSize channelId:%v err:%v", channelId, err)
		return 0,0, err
	}

	if channelType == 0 {
		resp, err := m.channelCli.GetChannelSimpleInfo(ctx, channelId, channelId)
		if nil != err {
			log.ErrorWithCtx(ctx, "GetChannelHotValue GetChannelSimpleInfo channelId:%v err:%v", channelId, err)
		}else{
			channelType = resp.GetChannelType()
		}
	}

	channelHotVal,memRatio := util.GetChannelHotValueWithRatio(ctx,channelId,onlineMemberCnt, uint32(presentVal), channelType)

	sec, openCache := conf.GetEasyCacheSec()
	if openCache {
		easyCache.Set(vkey, channelHotVal, sec-1)
		easyCache.Set(fkey, memRatio, sec-1)
	}

	return channelHotVal,memRatio, nil
}

func (m *Manager) BatchGetChannelHotValue(ctx context.Context, channelIdList []uint32 ) (map[uint32]int64,error) {

	channelHotValMap := make(map[uint32]int64)

	lackChnanelIdList :=make([]uint32,0)

	sec, openCache := conf.GetEasyCacheSec()
	for _, cid := range channelIdList {
		ekey,_:=channelHotValKey(cid)
		v,ok := easyCache.Get(ekey)
		if ok {
			var val int64
			if val,ok = v.(int64);ok {
				channelHotValMap[cid] = val
			}
		}
		if ! ok {
			lackChnanelIdList = append(lackChnanelIdList, cid)
		}
	}

	if len(lackChnanelIdList)==0{
		return channelHotValMap,nil
	}

	presentHotValMap,err := m.cache.BatchGetPresentHotValue(ctx,lackChnanelIdList)
	if nil != err {
		log.ErrorWithCtx(ctx, "BatchGetPresentHotValue err:%v",err)
		return channelHotValMap, err
	}
	memberSizeMap, err := m.channelOLStatCli.BatchGetChannelMemberSize(ctx, lackChnanelIdList[0], lackChnanelIdList)
	if nil != err {
		log.ErrorWithCtx(ctx, "BatchGetChannelMemberSize err:%v",err)
		return channelHotValMap, err
	}

	log.DebugfWithCtx(ctx, "BatchGetChannelHotValue presentHotValMap:%+v memberSizeMap:%+v", presentHotValMap, memberSizeMap)

	channelId2Info, err := m.channelCli.BatchGetChannelSimpleInfo(ctx, lackChnanelIdList[0],lackChnanelIdList)
	if nil != err {
		log.ErrorWithCtx(ctx, "BatchGetChannelSimpleInfo err:%v", err)
	}

	for _, cid := range lackChnanelIdList {
		var channelType uint32 = 0
		if channelId2Info != nil {
			if info,ok := channelId2Info[cid];ok {
				channelType = info.GetChannelType()
			}
		}
		hotVal,_ := util.GetChannelHotValueWithRatio(ctx,cid, memberSizeMap[cid], uint32(presentHotValMap[cid]),channelType)
		channelHotValMap[cid] = hotVal
		if openCache {
			ekey,_ := channelHotValKey(cid)
			easyCache.Set(ekey, hotVal, sec)
		}
	}

	return channelHotValMap, nil
}

func (m *Manager) ShutDown() {
	m.presentEventSub.Stop()
}
