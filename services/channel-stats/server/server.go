package server

import (
	"context"
	"errors"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/channelstats"
	"golang.52tt.com/services/channel-stats/conf"
	"golang.52tt.com/services/channel-stats/manager"
)

type ChannelStatsServer struct {
	sc  *conf.ServiceConfigT
	mgr manager.IManager
}

func NewChannelStatsServer(ctx context.Context, cfg config.Configer) (*ChannelStatsServer, error) {

	sc := &conf.ServiceConfigT{}

	cfgPath := ctx.Value("configfile").(string)
	if cfgPath == "" {
		return nil, errors.New("configfile not exist")
	}
	err := sc.Parse(cfgPath)
	if err != nil {
		return nil, err
	}

	mgr, err := manager.NewManager(ctx, sc)
	if nil != err {
		log.ErrorWithCtx(ctx, "Failed to NewManager err:%v", err)
		return nil, err
	}

	return &ChannelStatsServer{
		sc:  sc,
		mgr: mgr,
	}, nil
}

//
func (s *ChannelStatsServer) GetPresentHotValue(ctx context.Context, req *pb.GetPresentHotValueReq) (*pb.GetPresentHotValueResp, error) {
	log.DebugfWithCtx(ctx, "GetPresentHotValue req:%v", req)
	rsp := &pb.GetPresentHotValueResp{}

	val, err := s.mgr.GetPresentHotValue(ctx, req.ChannelId)
	if nil != err {
		log.ErrorWithCtx(ctx, "GetPresentHotValue channelId:%v err:%v", req.ChannelId, err)
		return rsp, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, err.Error())
	}
	rsp.PresentHotValue = val

	log.DebugfWithCtx(ctx, "GetPresentHotValue req:%v resp:%v", rsp)
	return rsp, nil
}

func (s *ChannelStatsServer) BatchGetPresentHotValue(ctx context.Context, req *pb.BatchGetPresentHotValueReq) (*pb.BatchGetPresentHotValueResp, error) {
	log.DebugfWithCtx(ctx, "BatchGetPresentHotValue req:%v", req)
	rsp := &pb.BatchGetPresentHotValueResp{}
	sz := len(req.ChannelIdList)
	if sz == 0 {
		return rsp, nil
	}
	if sz >= 128 {
		log.InfoWithCtx(ctx, "BatchGetPresentHotValue sz:%v", sz)
	}

	valList, err := s.mgr.BatchGetPresentHotValue(ctx, req.ChannelIdList)
	if nil != err {
		log.ErrorWithCtx(ctx, "BatchGetPresentHotValue err:%v", err)
		return rsp, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, err.Error())
	}
	rsp.PresentHotValueList = valList

	log.DebugfWithCtx(ctx, "BatchGetPresentHotValue req:%v rsp:%v", req, rsp)

	return rsp, nil
}

func (s *ChannelStatsServer) GetChannelHotValue(ctx context.Context, req *pb.GetChannelHotValueReq) (*pb.GetChannelHotValueResp, error) {

	log.DebugfWithCtx(ctx, "GetChannelHotValue req:%v", req)

	rsp := &pb.GetChannelHotValueResp{}

	hotVal, memRatio, err := s.mgr.GetChannelHotValue(ctx, req.ChannelId, req.ChannelType)
	if nil != err {
		log.ErrorWithCtx(ctx, "GetChannelHotValue req:%v err:%v", req, err)
		return rsp, nil
	}
	rsp.ChannelHotValue = hotVal
	rsp.MemberCntFactor = memRatio

	log.DebugfWithCtx(ctx, "GetChannelHotValue req:%v rsp:%v", req, rsp)
	return rsp, nil

}

func (s *ChannelStatsServer) BatchGetChannelHotValue(ctx context.Context, req *pb.BatchGetChannelHotValueReq) (*pb.BatchGetChannelHotValueResp, error) {

	log.DebugfWithCtx(ctx, "BatchGetChannelHotValue req:%v", req)

	rsp := &pb.BatchGetChannelHotValueResp{}

	sz := len(req.GetChannelIdList())

	if sz == 0 {
		return rsp, nil
	}

	if sz > 120 {
		log.InfoWithCtx(ctx, "BatchGetChannelHotValue sz:%v", sz)
	}

	channelValMap, err := s.mgr.BatchGetChannelHotValue(ctx, req.GetChannelIdList())
	if nil != err {
		log.ErrorWithCtx(ctx, "BatchGetChannelHotValue err:%v", err)
		return rsp, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, err.Error())
	}
	rsp.ChannelHotValueMap = channelValMap

	log.DebugfWithCtx(ctx, "BatchGetChannelHotValue req:%v rsp:%v", req, rsp)
	return rsp, nil

}

func (s *ChannelStatsServer) ShutDown() {
	s.mgr.ShutDown()
}

//
