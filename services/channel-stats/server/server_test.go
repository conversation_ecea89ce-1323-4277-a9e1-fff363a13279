package server

import (
	"context"
	"github.com/golang/mock/gomock"
	"golang.52tt.com/pkg/config"
	pb "golang.52tt.com/protocol/services/channelstats"
	"golang.52tt.com/services/channel-stats/conf"
	"golang.52tt.com/services/channel-stats/manager"
	"golang.52tt.com/services/channel-stats/mocks"
	"reflect"
	"testing"
)

func TestChannelStatsServer_BatchGetChannelHotValue(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()

	channelIdList := []uint32{1024}
	ctx := context.Background()
	req := &pb.BatchGetChannelHotValueReq{
		ChannelIdList:        channelIdList,
	}


	want := map[uint32]int64{ 1024:0 }
	rsp := &pb.BatchGetChannelHotValueResp{
		ChannelHotValueMap:   want,
	}

	mockManager := mocks.NewMockIManager(ctl)

	gomock.InOrder(
		mockManager.EXPECT().BatchGetChannelHotValue(ctx, channelIdList).Return(want, nil),
	)

	type fields struct {
		sc  *conf.ServiceConfigT
		mgr manager.IManager
	}
	type args struct {
		ctx context.Context
		req *pb.BatchGetChannelHotValueReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.BatchGetChannelHotValueResp
		wantErr bool
	}{
		{
			name:    "BatchGetChannelHotValue",
			fields:  fields{
				sc:  nil,
				mgr: mockManager,
			},
			args:    args{
				ctx: ctx,
				req: req,
			},
			want:    rsp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelStatsServer{
				sc:  tt.fields.sc,
				mgr: tt.fields.mgr,
			}
			got, err := s.BatchGetChannelHotValue(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchGetChannelHotValue() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BatchGetChannelHotValue() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelStatsServer_BatchGetPresentHotValue(t *testing.T) {


	ctl := gomock.NewController(t)
	defer ctl.Finish()

	channelIdList := []uint32{1024}
	ctx := context.Background()
	req := &pb.BatchGetPresentHotValueReq{
		ChannelIdList:        channelIdList,
	}

	want := []int64{ 1024 }
	rsp := &pb.BatchGetPresentHotValueResp{
		PresentHotValueList: want ,
	}

	mockManager := mocks.NewMockIManager(ctl)

	gomock.InOrder(
		mockManager.EXPECT().BatchGetPresentHotValue(ctx, channelIdList).Return(want, nil),
	)

	type fields struct {
		sc  *conf.ServiceConfigT
		mgr manager.IManager
	}
	type args struct {
		ctx context.Context
		req *pb.BatchGetPresentHotValueReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.BatchGetPresentHotValueResp
		wantErr bool
	}{
		{
			name:    "BatchGetPresentHotValue",
			fields:  fields{
				sc:  nil,
				mgr: mockManager,
			},
			args:    args{
				ctx: ctx,
				req: req,
			},
			want:    rsp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelStatsServer{
				sc:  tt.fields.sc,
				mgr: tt.fields.mgr,
			}
			got, err := s.BatchGetPresentHotValue(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchGetPresentHotValue() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BatchGetPresentHotValue() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelStatsServer_GetChannelHotValue(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	
	ctx := context.Background()
	req := &pb.GetChannelHotValueReq{
		ChannelId:            1024,
		ChannelType:          7,
	}

	rsp := &pb.GetChannelHotValueResp{
		ChannelHotValue:      10,
		MemberCntFactor:      10,
	}

	mockManager := mocks.NewMockIManager(ctl)

	//(ctx context.Context, channelId, channelType uint32) (int64, int64, error)
	gomock.InOrder(
		mockManager.EXPECT().GetChannelHotValue(ctx, req.ChannelId, req.ChannelType).Return(rsp.ChannelHotValue, rsp.MemberCntFactor, nil),
	)


	type fields struct {
		sc  *conf.ServiceConfigT
		mgr manager.IManager
	}
	type args struct {
		ctx context.Context
		req *pb.GetChannelHotValueReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetChannelHotValueResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name:    "GetChannelHotValue",
			fields:  fields{
				sc:  nil,
				mgr: mockManager,
			},
			args:    args{
				ctx: ctx,
				req: req,
			},
			want:    rsp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelStatsServer{
				sc:  tt.fields.sc,
				mgr: tt.fields.mgr,
			}
			got, err := s.GetChannelHotValue(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetChannelHotValue() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetChannelHotValue() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelStatsServer_GetPresentHotValue(t *testing.T) {


	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()
	req := &pb.GetPresentHotValueReq{
		ChannelId:            1024,
	}

	rsp := &pb.GetPresentHotValueResp{
		PresentHotValue:      1024,
	}

	mockManager := mocks.NewMockIManager(ctl)

	//(ctx context.Context,channelId uint32) (int64, error)
	gomock.InOrder(
		mockManager.EXPECT().GetPresentHotValue(ctx, req.ChannelId).Return(rsp.PresentHotValue, nil),
	)

	type fields struct {
		sc  *conf.ServiceConfigT
		mgr manager.IManager
	}
	type args struct {
		ctx context.Context
		req *pb.GetPresentHotValueReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetPresentHotValueResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name:    "_GetPresentHotValue",
			fields:  fields{
				sc:  nil,
				mgr: mockManager,
			},
			args:    args{
				ctx: ctx,
				req: req,
			},
			want:    rsp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelStatsServer{
				sc:  tt.fields.sc,
				mgr: tt.fields.mgr,
			}
			got, err := s.GetPresentHotValue(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPresentHotValue() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPresentHotValue() got = %v, want %v", got, tt.want)
			}
		})
	}
}
//
func TestChannelStatsServer_ShutDown(t *testing.T) {
	type fields struct {
		sc  *conf.ServiceConfigT
		mgr *manager.Manager
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
	/*		s := &ChannelStatsServer{
				sc:  tt.fields.sc,
				mgr: tt.fields.mgr,
			}*/
		})
	}
}

func TestNewChannelStatsServer(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.WithValue(context.Background(), "configfile", "../channel-stats.json")

	type args struct {
		ctx context.Context
		cfg config.Configer
	}
	tests := []struct {
		name    string
		args    args
		want    *ChannelStatsServer
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name:    "NewChannelStatsServer",
			args:    args{
				ctx: ctx,
				cfg: nil ,
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
/*			got, err := NewChannelStatsServer(tt.args.ctx, tt.args.cfg)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewChannelStatsServer() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewChannelStatsServer() got = %v, want %v", got, tt.want)
			}*/
		})
	}
}