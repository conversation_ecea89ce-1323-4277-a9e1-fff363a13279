package server

import(
	channelstats "golang.52tt.com/protocol/services/channelstats"
	context "context"
)

type IChannelStatsServer interface {
	BatchGetChannelHotValue(ctx context.Context, req *channelstats.BatchGetChannelHotValueReq) (*channelstats.BatchGetChannelHotValueResp, error)
	BatchGetPresentHotValue(ctx context.Context, req *channelstats.BatchGetPresentHotValueReq) (*channelstats.BatchGetPresentHotValueResp, error)
	GetChannelHotValue(ctx context.Context, req *channelstats.GetChannelHotValueReq) (*channelstats.GetChannelHotValueResp, error)
	GetPresentHotValue(ctx context.Context, req *channelstats.GetPresentHotValueReq) (*channelstats.GetPresentHotValueResp, error)
	ShutDown() ()
}

