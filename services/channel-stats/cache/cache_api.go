package cache

import(
	context "context"
)

type IChannelStatsCache interface {
	BatchGetPresentHotValue(ctx context.Context, channelIdList []uint32) (map[uint32]int64, error)
	GetPresentHotValue(ctx context.Context, channelId uint32) (int64, error)
	GetPushList(ctx context.Context) ([]uint32)
	Lock(key string, expire int64) (bool)
	PopTask() (int64, error)
	PushTaskList(channelIdList []uint32) ()
	RecordPresent(ctx context.Context, orderId string, channelId, channelType, sendTs, value uint32) (error)
}

