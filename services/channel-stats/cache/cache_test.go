package cache

import (
	"context"
	"github.com/alicebob/miniredis/v2"
	"github.com/go-redis/redis"
	"github.com/opentracing/opentracing-go"
	"reflect"
	"testing"
	"golang.52tt.com/pkg/log"
)
var redisClient *redis.Client

func init() {
	log.SetLevel(log.DebugLevel)
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}

	// 连接mock的redis server
	redisClient = redis.NewClient(&redis.Options{
		Addr: s.Addr(), // mock redis server的地址
	})
}

func TestChannelStatsCache_BatchGetPresentHotValue(t *testing.T) {

	ctx := context.Background()

	channelIdList := []uint32{1024}
	presentMap := map[uint32]int64{1024:0}

	type fields struct {
		redisClient *redis.Client
		tracer      opentracing.Tracer
	}
	type args struct {
		ctx           context.Context
		channelIdList []uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    map[uint32]int64
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name:    "TestChannelStatsCache_BatchGetPresentHotValue",
			fields:  fields{
				redisClient: redisClient,
				tracer:      nil,
			},
			args:    args{
				ctx:           ctx,
				channelIdList: channelIdList,
			},
			want:    presentMap,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cache := &ChannelStatsCache{
				redisClient: tt.fields.redisClient,
				tracer:      tt.fields.tracer,
			}
			got, err := cache.BatchGetPresentHotValue(tt.args.ctx, tt.args.channelIdList)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchGetPresentHotValue() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BatchGetPresentHotValue() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelStatsCache_GetPresentHotValue(t *testing.T) {

	ctx := context.Background()
	var channelId uint32 = 1024

	type fields struct {
		redisClient *redis.Client
		tracer      opentracing.Tracer
	}
	type args struct {
		ctx       context.Context
		channelId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    int64
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name:    "TestChannelStatsCache_GetPresentHotValue",
			fields:  fields{
				redisClient: redisClient,
				tracer:      nil,
			},
			args:    args{
				ctx:       ctx,
				channelId: channelId,
			},
			want:    0,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cache := &ChannelStatsCache{
				redisClient: tt.fields.redisClient,
				tracer:      tt.fields.tracer,
			}
			got, err := cache.GetPresentHotValue(tt.args.ctx, tt.args.channelId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPresentHotValue() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetPresentHotValue() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelStatsCache_GetPushList(t *testing.T) {

	ctx := context.Background()
	channelList := []uint32{}

	type fields struct {
		redisClient *redis.Client
		tracer      opentracing.Tracer
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []uint32
	}{
		// TODO: Add test cases.
		{
			name:   "TestChannelStatsCache_GetPushList",
			fields: fields{
				redisClient: redisClient,
				tracer:      nil,
			},
			args:   args{ctx: ctx},
			want:   channelList,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cache := &ChannelStatsCache{
				redisClient: tt.fields.redisClient,
				tracer:      tt.fields.tracer,
			}
			if got := cache.GetPushList(tt.args.ctx); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPushList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelStatsCache_Lock(t *testing.T) {
	type fields struct {
		redisClient *redis.Client
		tracer      opentracing.Tracer
	}
	type args struct {
		key    string
		expire int64
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   bool
	}{
		// TODO: Add test cases.
		{
			name:   "TestChannelStatsCache_Lock",
			fields: fields{
				redisClient: redisClient,
				tracer:      nil,
			},
			args:   args{
				key:    "10x",
				expire: 0,
			},
			want:   true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cache := &ChannelStatsCache{
				redisClient: tt.fields.redisClient,
				tracer:      tt.fields.tracer,
			}
			if got := cache.Lock(tt.args.key, tt.args.expire); got != tt.want {
				t.Errorf("Lock() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelStatsCache_PopTask(t *testing.T) {
	type fields struct {
		redisClient *redis.Client
		tracer      opentracing.Tracer
	}
	tests := []struct {
		name    string
		fields  fields
		want    int64
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name:    "TestChannelStatsCache_PopTask",
			fields:  fields{
				redisClient: redisClient,
				tracer:      nil,
			},
			want:    0,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cache := &ChannelStatsCache{
				redisClient: tt.fields.redisClient,
				tracer:      tt.fields.tracer,
			}
			got, err := cache.PopTask()
			if (err != nil) != tt.wantErr {
				t.Errorf("PopTask() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("PopTask() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelStatsCache_PushTaskList(t *testing.T) {

	//ctx := context.Background()
	channelList := []uint32{}

	type fields struct {
		redisClient *redis.Client
		tracer      opentracing.Tracer
	}
	type args struct {
		channelIdList []uint32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		// TODO: Add test cases.
		{
			name:   "TestChannelStatsCache_PushTaskList",
			fields: fields{
				redisClient: redisClient,
				tracer:      nil,
			},
			args:   args{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cache := &ChannelStatsCache{
				redisClient: tt.fields.redisClient,
				tracer:      tt.fields.tracer,
			}
			cache.PushTaskList(channelList)
		})
	}
}

func TestChannelStatsCache_RecordPresent(t *testing.T) {

	ctx := context.Background()

	type fields struct {
		redisClient *redis.Client
		tracer      opentracing.Tracer
	}
	type args struct {
		ctx         context.Context
		orderId     string
		channelId   uint32
		channelType uint32
		sendTs      uint32
		value       uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name:    "TestChannelStatsCache_RecordPresent",
			fields:  fields{
				redisClient: redisClient,
				tracer:      nil,
			},
			args:    args{
				ctx:         ctx,
				orderId:     "",
				channelId:   0,
				channelType: 0,
				sendTs:      0,
				value:       0,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cache := &ChannelStatsCache{
				redisClient: tt.fields.redisClient,
				tracer:      tt.fields.tracer,
			}
			if err := cache.RecordPresent(tt.args.ctx, tt.args.orderId, tt.args.channelId, tt.args.channelType, tt.args.sendTs, tt.args.value); (err != nil) != tt.wantErr {
				t.Errorf("RecordPresent() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestNewChannelStatsCache(t *testing.T) {
	type args struct {
		r      *redis.Client
		tracer opentracing.Tracer
	}
	tests := []struct {
		name string
		args args
		want *ChannelStatsCache
	}{
		// TODO: Add test cases.
		{
			name: "TestNewChannelStatsCache",
			args: args{
				r:      redisClient,
				tracer: nil,
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := NewChannelStatsCache(tt.args.r, tt.args.tracer)
			tt.want = got
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewChannelStatsCache() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_channelHotValuePushList(t *testing.T) {
	tests := []struct {
		name string
		want string
	}{
		// TODO: Add test cases.
		{
			name: "Test_channelHotValuePushList",
			want: "channel_hot_value_push",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := channelHotValuePushList(); got != tt.want {
				t.Errorf("channelHotValuePushList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_channelPresentRecordKey(t *testing.T) {
	type args struct {
		channelId uint32
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
		{
			name: "Test_channelPresentRecordKey",
			args: args{channelId: 10},
			want: "channel_present_record_10",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := channelPresentRecordKey(tt.args.channelId); got != tt.want {
				t.Errorf("channelPresentRecordKey() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_channelPresentValueKey(t *testing.T) {
	type args struct {
		channelId uint32
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
		{
			name: "Test_channelPresentValueKey",
			args: args{channelId:10},
			want: "channel_present_value_10",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := channelPresentValueKey(tt.args.channelId); got != tt.want {
				t.Errorf("channelPresentValueKey() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_pushTaskListKey(t *testing.T) {
	tests := []struct {
		name string
		want string
	}{
		// TODO: Add test cases.
		{
			name: "Test_pushTaskListKey",
			want: "channel_stats_push_task_list",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := pushTaskListKey(); got != tt.want {
				t.Errorf("pushTaskListKey() = %v, want %v", got, tt.want)
			}
		})
	}
}