package util

import (
	"context"
	"golang.52tt.com/pkg/log"
	channelga "golang.52tt.com/protocol/app/channel"
)
import "golang.52tt.com/services/channel-stats/conf"

func IsNeedPresentHotValue(channelType uint32) bool {
	if channelType == uint32(channelga.ChannelType_RADIO_LIVE_CHANNEL_TYPE) ||
		channelType == uint32(channelga.ChannelType_OFFICIAL_LIVE_CHANNEL_TYPE) ||
		channelType == uint32(channelga.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) ||
		channelType == uint32(channelga.ChannelType_CPL_SUPER_CHANNEL_TYPE) {
		return true
	}
	return false
}

func GetChannelHotValueWithRatio(ctx context.Context,cid,realMemberCnt, presentHotValue, channelType uint32 )(int64,int64) {
	var channelhotVal,memRatio int64
	sec := conf.GetSmoothSec()
	ratio := conf.GetRatio(cid,sec)
	memberFactor, presentFactor := conf.GetMemberCntFactor()

	if ! IsNeedPresentHotValue(channelType) {
		channelhotVal = int64(realMemberCnt)
		memRatio = 1
	}else{
		channelhotVal = int64(float64(ratio) * ( float64(realMemberCnt)*float64(memberFactor) + float64(presentHotValue)*presentFactor ))
		memRatio = ratio * memberFactor
	}

	log.DebugWithCtx(ctx, "cid:%v channelType:%v ratio:%v memRatio:%v realMemberCnt:%v memberFactor:%v presentHotVal:%v presentFactor:%v channelhotVal:%v",
		cid, channelType,ratio, memRatio,realMemberCnt, memberFactor, presentHotValue,presentFactor, channelhotVal)

	return channelhotVal,memRatio
}

//CPL大房，配置是否需要推
func IsPushChannelType(channelType uint32) bool {
	if channelga.ChannelType(channelType) != channelga.ChannelType_CPL_SUPER_CHANNEL_TYPE {
		return true
	}
	isPushCPL := conf.IsPushCPLChannel()
	return isPushCPL
}
