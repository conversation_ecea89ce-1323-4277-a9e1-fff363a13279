package gwcontext

import (
	"encoding/base64"
	"golang.52tt.com/pkg/protocol"
	"strconv"

	"golang.org/x/net/context"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"

	"golang.52tt.com/services/logic-grpc-gateway/logicproto"
)

func MetadataFromServicePacketHeader(hdr *logicproto.ServicePacketHeader) metadata.MD {
	return metadata.Pairs(
		requestSessionKey, base64.StdEncoding.EncodeToString(hdr.SessionKey[:]),
		requestClientType, strconv.Itoa(int(hdr.ClientType)),
		requestTerminalType, strconv.Itoa(int(hdr.TerminalType)),
		requestUserID, strconv.Itoa(int(hdr.Uid)),
		requestChannelID, strconv.Itoa(int(hdr.ServiceRetCode)),
		requestClientIP, strconv.Itoa(int(hdr.IP)),
		requestClientPort, strconv.Itoa(int(hdr.ClientPort)),
		requestDeviceID, base64.StdEncoding.EncodeToString(hdr.DeviceID[:]),
		requestCommandID, strconv.Itoa(int(hdr.CommandID)),
		requestClientVersion, strconv.Itoa(int(hdr.Return)), // client -> svr, ret 填客户端版本号
	)
}

func ParseServiceInfo(md metadata.MD) (sv *ServiceInfo) {
	sv = new(ServiceInfo)
	sv.SessionKey, _ = base64.StdEncoding.DecodeString(firstInMD(md, requestSessionKey))
	sv.DeviceID, _ = base64.StdEncoding.DecodeString(firstInMD(md, requestDeviceID))
	sv.ClientType = uint16(firstInMDInt(md, requestClientType))
	sv.TerminalType = uint32(firstInMDInt(md, requestTerminalType))
	sv.UserID = uint32(firstInMDInt(md, requestUserID))
	sv.ClientIP = uint32(firstInMDInt(md, requestClientIP))
	sv.ClientPort = uint32(firstInMDInt(md, requestClientPort))
	sv.CommandID = uint32(firstInMDInt(md, requestCommandID))
	sv.ClientVersion = uint32(firstInMDInt(md, requestClientVersion))
	sv.MarketID = uint32(firstInMDInt(md, requestMarketID))
	return
}

func SetResponseCryptAlgorithmAndKey(ctx context.Context, alg int16, key []byte) {
	grpc.SetHeader(ctx, metadata.Pairs(
		responseCryptAlgorithm, strconv.Itoa(int(alg)),
		responseCryptKey, base64.StdEncoding.EncodeToString(key),
	))
}

func SetResponseServiceRetCode(ctx context.Context, code int32) {
	md := metadata.Pairs(
		respServiceRetCode, strconv.Itoa(int(code)),
		protocol.CommonStatusCodeKey, strconv.Itoa(int(code)),
	)
	
	if protocol.IsTrailerOnlyMode(ctx) {
		_ = grpc.SetTrailer(ctx, md)
	} else {
		_ = grpc.SetHeader(ctx, md)
	}
}
func SetResponseServiceRetCodeAndMsg(ctx context.Context, sv *ServiceInfo, code int32, msg string) {
	md := metadata.Pairs(
		respServiceRetCode, strconv.Itoa(int(code)),
		respServiceRetMsg, msg,
		protocol.CommonStatusCodeKey, strconv.Itoa(int(code)),
	)
	if sv != nil {
		md.Append(requestMarketID, strconv.Itoa(int(sv.MarketID)))
	}

	if protocol.IsTrailerOnlyMode(ctx) {
		_ = grpc.SetTrailer(ctx, md)
	} else {
		_ = grpc.SetHeader(ctx, md)
	}
}

func ResponseCryptAlgorithmAndKey(md metadata.MD) (alg *int16, key []byte) {
	if v, ok := md[responseCryptAlgorithm]; ok && len(v) > 0 {
		x, _ := strconv.Atoi(v[0])
		x16 := int16(x)
		alg = &x16
	}
	key, _ = base64.StdEncoding.DecodeString(firstInMD(md, responseCryptKey))
	return
}

func ResponseServiceRetCode(md metadata.MD) int {
	return firstInMDInt(md, respServiceRetCode)
}

func ResponseServiceSRetCode(md metadata.MD) int {
	return firstInMDInt(md, respServiceSRetCode)
}

func ResponseServiceRetMsg(md metadata.MD) string {
	return firstInMD(md, respServiceRetMsg)
}

func RequestIsAutoRobotFromContext(ctx context.Context) (isRobot bool, ok bool) {
	if md, ok2 := metadata.FromIncomingContext(ctx); ok2 {
		robotFlag := firstInMDInt(md, requestIsRobot)
		isRobot = robotFlag == 1
		return
	}
	ok = false
	return
}

const (
	requestSessionKey    = "req_sess_key"
	requestClientType    = "req_cli_type"
	requestTerminalType  = "req_term"
	requestUserID        = "req_uid"
	requestChannelID     = "req_channel_id"
	requestClientIP      = "req_cli_ip"
	requestClientPort    = "req_cli_port"
	requestDeviceID      = "req_dev_id"
	requestCommandID     = "req_cmd_id"
	requestClientVersion = "req_cli_ver"
	requestMarketID      = "req_market_id"

	respServiceRetCode     = "resp_service_ret_code"
	respServiceRetMsg      = "resp_service_ret_msg"
	respServiceSRetCode    = "resp_service_sret_code"
	responseCryptAlgorithm = "resp_crypt_alg"
	responseCryptKey       = "resp_crypt_key"
)

const (
	requestIsRobot = "req_robot"
)

func firstInMD(md metadata.MD, key string) string {
	if v, ok := md[key]; ok && len(v) > 0 {
		return v[0]
	}
	return ""
}

func firstInMDInt(md metadata.MD, key string) int {
	if v, ok := md[key]; ok && len(v) > 0 {
		x, _ := strconv.Atoi(v[0])
		return x
	}
	return 0
}
