package server

import (
	"context"
	"github.com/golang/protobuf/proto"
	"github.com/opentracing/opentracing-go"
	"golang.52tt.com/clients/present-extra-conf"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/marketid_helper"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/pkg/ttversion"
	gabase "golang.52tt.com/protocol/app"
	"golang.52tt.com/protocol/app/channel"
	channel_scheme "golang.52tt.com/protocol/app/channel-scheme"
	pb "golang.52tt.com/protocol/app/present-go-logic"
	"golang.52tt.com/protocol/app/redpacket"
	"golang.52tt.com/protocol/app/time_present"
	presentPB_ "golang.52tt.com/protocol/app/userpresent"
	"golang.52tt.com/protocol/common/status"
	channel_live_mgr "golang.52tt.com/protocol/services/channellivemgr"
	"golang.52tt.com/protocol/services/conversion"
	present_middleware "golang.52tt.com/protocol/services/present-middleware"
	"golang.52tt.com/protocol/services/presentextraconf"
	"golang.52tt.com/protocol/services/presentprivilege"
	revenue_ext_game "golang.52tt.com/protocol/services/revenue-ext-game"
	time_present2 "golang.52tt.com/protocol/services/time-present"
	"golang.52tt.com/protocol/services/userpresent"
	"golang.52tt.com/services/present-go-logic/internal/conf"
	"golang.52tt.com/services/present-go-logic/internal/model/breaker"
	"golang.52tt.com/services/present-go-logic/internal/model/emperor_set_cache"
	"golang.52tt.com/services/present-go-logic/internal/model/intimate_present_cache"
	"golang.52tt.com/services/present-go-logic/internal/model/present_config_cache"
	"golang.52tt.com/services/present-go-logic/internal/model/present_set_cache"
	"golang.52tt.com/services/present-go-logic/internal/rpc"
	context0 "golang.org/x/net/context"
	"sort"
	"sync"
	"time"
)

type PresentGoLogic_ struct {
	businessConf          *conf.BusinessConfManager
	sendPresentConf       *conf.SendPresentConfManager
	drawGameConf          *conf.DrawGameConf
	imPresentConf         *conf.ImPresentConf
	presentSetConf        *conf.PresentSetConf
	constellationUrl      string
	presentExtraConfCli   present_extra_conf.IClient
	presentFloatCache     *presentextraconf.GetPresentFloatLayerResp
	presentFlashCache     []*pb.PresentFlashEffect
	flashConfigCache      []*pb.FlashEffectConfig
	presentConfigCache    map[uint32]*userpresent.StPresentItemConfig
	mapLock               sync.RWMutex
	presentConfigMemCache *present_config_cache.PresentMemCache
	presentSetCache       *present_set_cache.PresentSetCache
	emperorSetCache       *emperor_set_cache.EmperorSetCache
	intimatePresentCache  *intimate_present_cache.IntimatePresentCache
	lastUpdateTime        uint32
	timePresentConfig     conf.TimePresent
}

func (s *PresentGoLogic_) GetTimePresentList(c context0.Context, req *time_present.GetTimePresentListReq) (*time_present.GetTimePresentListResp, error) {
	out := &time_present.GetTimePresentListResp{TimePresent: make([]*time_present.TimePresent, 0)}

	resp, err := client.TimePresentCli.GetChannelTimePresentList(c, &time_present2.GetChannelTimePresentListReq{
		ChannelId: req.GetChannelId(),
	})

	if err != nil {
		log.ErrorWithCtx(c, "GetTimePresentList GetChannelTimePresentList err :%v", err)
		return out, protocol.ToServerError(err)
	}

	for _, item := range resp.GetTimePresent() {
		fromUserProfile := &gabase.UserProfile{}
		toUserProfile := &gabase.UserProfile{}

		sErr := proto.Unmarshal(item.GetFromUser(), fromUserProfile)
		if sErr != nil {
			log.ErrorWithCtx(c, "GetTimePresentList Unmarshal fromUserProfile err :%v", sErr)
		}

		sErr = proto.Unmarshal(item.GetToUser(), toUserProfile)
		if sErr != nil {
			log.ErrorWithCtx(c, "GetTimePresentList Unmarshal toUserProfile err :%v item %v", sErr, item)
		}

		out.TimePresent = append(out.TimePresent, &time_present.TimePresent{
			Id:         item.GetId(),
			ItemId:     item.GetItemId(),
			FromUser:   fromUserProfile,
			ToUser:     toUserProfile,
			BeginTime:  item.GetBeginTime(),
			EndTime:    item.GetEndTime(),
			DuringTime: item.GetDuringTime(),
		})
	}

	return out, nil
}

func (s *PresentGoLogic_) GetTimePresentOnShelf(c context0.Context, req *time_present.GetTimePresentOnShelfReq) (*time_present.GetTimePresentOnShelfResp, error) {
	out := &time_present.GetTimePresentOnShelfResp{TimePresentList: make([]*time_present.TimePresentOnShelf, 0)}

	channelDetail, err := client.ChannelCli.GetChannelSimpleInfo(c, 0, req.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(c, "GetTimePresentOnShelf GetChannelSimpleInfo err :%v", err)
		return out, protocol.ToServerError(err)
	}

	s.mapLock.RLock()
	defer s.mapLock.RUnlock()

	if req.GetChannelSchemeLayoutType() == uint32(channel_scheme.SchemeLayoutType_SCHEME_LAYOUT_DEFAULT_FUN) && channelDetail.GetChannelType() == uint32(channel.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) {
		for _, item := range s.presentConfigCache {
			// 删除的不管
			if item.GetExtend().GetIsTest() || item.GetIsDel() {
				continue
			}

			// 下架的不管
			if item.EffectBegin > uint32(time.Now().Unix()) || item.EffectEnd < uint32(time.Now().Unix()) || item.GetExtend().GetUnshowPresentShelf() {
				continue
			}

			if item.GetExtend().GetTag() == uint32(gabase.PresentTagType_PRESENT_TAG_TIME) {
				out.TimePresentList = append(out.TimePresentList, &time_present.TimePresentOnShelf{
					PresentId: item.GetItemId(),
				})
			}
		}
	}

	// 最后按id升序，保证顺序一致
	sort.Slice(out.TimePresentList, func(i, j int) bool {
		return out.TimePresentList[i].PresentId < out.TimePresentList[j].PresentId
	})
	log.DebugWithCtx(c, "GetTimePresentOnShelf %+v", out)

	return out, nil
}

func (s *PresentGoLogic_) GetChannelLiveIntimatePresentList(c context0.Context, req *time_present.GetChannelLiveIntimatePresentListReq) (*time_present.GetChannelLiveIntimatePresentListResp, error) {
	out := &time_present.GetChannelLiveIntimatePresentListResp{Items: make([]*time_present.IntimatePresent, 0)}
	// 拿下开关
	switchOn, privilegeMap := s.timePresentConfig.GetIntimatePresentMap(req.GetChannelId())

	// 拿pk状态
	liveStatus, sErr := client.ChannelLiveMgrCli.BatchGetChannelLiveStatusSimple(c, channel_live_mgr.BatchGetChannelLiveStatusSimpleReq{
		ChannelList: []uint32{req.GetChannelId()},
	})
	if sErr != nil {
		log.ErrorWithCtx(c, "GetChannelLiveIntimatePresentList BatchGetChannelLiveStatusSimple err :%v", sErr)
	}

	livePkChannelList := make([]uint32, 0)
	for _, item := range liveStatus.GetChannelLiveStatusList() {
		livePkChannelList = append(livePkChannelList, item.GetPkChannelIdList()...)
	}

	resp, err := client.TimePresentCli.GetChannelLiveIntimatePresentList(c, &time_present2.GetChannelLiveIntimatePresentListReq{
		ChannelId:       req.GetChannelId(),
		PkChannelIdList: livePkChannelList,
	})

	if err != nil {
		log.ErrorWithCtx(c, "GetTimePresentList GetChannelTimePresentList err :%v", err)
		return out, protocol.ToServerError(err)
	}

	for _, item := range resp.GetItems() {

		fromUserProfile := &gabase.UserProfile{}
		toUserProfile := &gabase.UserProfile{}

		sErr := proto.Unmarshal(item.GetFromUser(), fromUserProfile)
		if sErr != nil {
			log.ErrorWithCtx(c, "GetTimePresentList Unmarshal fromUserProfile err :%v", err)
		}

		sErr = proto.Unmarshal(item.GetToUser(), toUserProfile)
		if sErr != nil {
			log.ErrorWithCtx(c, "GetTimePresentList Unmarshal toUserProfile err :%v", err)
		}

		if switchOn || privilegeMap[item.GetItemId()] {
			out.Items = append(out.Items, &time_present.IntimatePresent{
				Id:         item.GetId(),
				ItemId:     item.GetItemId(),
				FromUser:   fromUserProfile,
				ToUser:     toUserProfile,
				BeginTime:  item.GetBeginTime(),
				EndTime:    item.GetEndTime(),
				DuringTime: item.GetDuringTime(),
			})
		}
	}

	channelInfoList := make([]*time_present.LiveIntimatePresentChannelInfo, 0)
	for _, item := range resp.GetLiveIntimatePresentInfo().GetChannelInfo() {

		fromUserProfile := &gabase.UserProfile{}
		toUserProfile := &gabase.UserProfile{}

		sErr := proto.Unmarshal(item.GetFromUser(), fromUserProfile)
		if sErr != nil {
			log.ErrorWithCtx(c, "GetTimePresentList Unmarshal fromUserProfile err :%v", sErr)
		}

		sErr = proto.Unmarshal(item.GetToUser(), toUserProfile)
		if sErr != nil {
			log.ErrorWithCtx(c, "GetTimePresentList Unmarshal toUserProfile err :%v item: %v", sErr, item)
		}

		channelInfoList = append(channelInfoList, &time_present.LiveIntimatePresentChannelInfo{
			ChannelId:      item.GetChannelId(),
			PresentId:      item.GetPresentId(),
			FromUser:       fromUserProfile,
			ToUser:         toUserProfile,
			LastChangeTime: item.GetLastChangeTime(),
		})
	}

	out.LiveIntimatePresentInfo = &time_present.LiveIntimatePresentInfo{
		LastUpdateTime:   resp.GetLiveIntimatePresentInfo().GetLastUpdateTime(),
		TriggerChannelId: resp.GetLiveIntimatePresentInfo().GetTriggerChannelId(),
		ChannelInfo:      channelInfoList,
	}

	return out, nil
}

func (s *PresentGoLogic_) GetLiveIntimatePresentOnShelf(c context0.Context, req *time_present.GetLiveIntimatePresentOnShelfReq) (*time_present.GetLiveIntimatePresentOnShelfResp, error) {
	out := &time_present.GetLiveIntimatePresentOnShelfResp{IntimatePresentList: make([]*time_present.IntimatePresentOnShelf, 0)}
	// 先拿开关
	switchOn, privilegeMap := s.timePresentConfig.GetIntimatePresentMap(req.GetChannelId())

	channelDetail, err := client.ChannelCli.GetChannelSimpleInfo(c, 0, req.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(c, "GetTimePresentOnShelf GetChannelSimpleInfo err :%v", err)
		return out, protocol.ToServerError(err)
	}

	s.mapLock.RLock()
	defer s.mapLock.RUnlock()

	if channelDetail.GetChannelType() == uint32(channel.ChannelType_RADIO_LIVE_CHANNEL_TYPE) {

		// 先查直播间状态确认是不是虚拟主播
		liveStatus, err := client.ChannelLiveMgrCli.GetChannelLiveStatus(c, channel_live_mgr.GetChannelLiveStatusReq{
			Uid: channelDetail.GetBindId(),
		})
		if err != nil {
			log.ErrorWithCtx(c, "GetLiveIntimatePresentOnShelf GetChannelLiveStatus err :%v", err)
			return out, protocol.ToServerError(err)
		}

		// 是虚拟主播就返回空的
		if liveStatus.GetChannelLiveInfo().GetChannelLiveStatus().GetAnchorType() == uint32(channel_live_mgr.AnchorType_Anchor_Type_Virtual) {
			return out, nil
		}

		// 再看看弹幕游戏开没开

		gameResp, sErr := client.RevenueExtGameCli.GetMountExtGame(c, &revenue_ext_game.GetMountExtGameReq{ChannelId: req.GetChannelId()})
		if sErr != nil {
			log.ErrorWithCtx(c, "FaceAuthBeforeConsumeChecker err :%v", sErr)
			return out, protocol.ToServerError(sErr)
		}

		// 在游戏中，不返回
		if gameResp.GetGameType() != 0 {
			return out, nil
		}

		for _, item := range s.presentConfigCache {
			// 删除的不管
			if item.GetExtend().GetIsTest() || item.GetIsDel() {
				continue
			}

			// 下架的不管
			if item.EffectBegin > uint32(time.Now().Unix()) || item.EffectEnd < uint32(time.Now().Unix()) || item.GetExtend().GetUnshowPresentShelf() {
				continue
			}

			// 考虑开关状态
			if !switchOn && !privilegeMap[item.GetItemId()] {
				continue
			}

			if item.GetExtend().GetTag() == uint32(gabase.PresentTagType_PRESENT_TAG_LIVE_INTIMATE) {
				out.IntimatePresentList = append(out.IntimatePresentList, &time_present.IntimatePresentOnShelf{
					PresentId: item.GetItemId(),
				})
			}
		}
	}

	// 最后按id升序，保证顺序一致
	sort.Slice(out.IntimatePresentList, func(i, j int) bool {
		return out.IntimatePresentList[i].PresentId < out.IntimatePresentList[j].PresentId
	})
	log.DebugWithCtx(c, "GetLiveIntimatePresentOnShelf %+v", out)

	return out, nil
}

func (s *PresentGoLogic_) GetLiveIntimatePresentConfigList(c context0.Context, req *time_present.GetLiveIntimatePresentConfigListReq) (*time_present.GetLiveIntimatePresentConfigListResp, error) {
	out := &time_present.GetLiveIntimatePresentConfigListResp{LiveIntimatePresentConfig: make([]*time_present.LiveIntimatePresentConfig, 0)}

	lastUpdate, configMap := s.intimatePresentCache.GetConfigList()
	if lastUpdate <= req.GetLastUpdateTime() {
		out.LastUpdateTime = req.GetLastUpdateTime()
		return out, nil
	}

	for _, item := range configMap {
		out.LiveIntimatePresentConfig = append(out.LiveIntimatePresentConfig, item)
	}
	out.LastUpdateTime = lastUpdate

	return out, nil
}

func (s *PresentGoLogic_) UnpackPresentBox(ctx context.Context, req *presentPB_.UnpackPresentBoxReq) (*presentPB_.UnpackPresentBoxResp, error) {
	out := &presentPB_.UnpackPresentBoxResp{}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, RequestInitFail, ctx)
		return out, protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
	}

	resp, err := client.PresentMiddlewareCli.UnpackPresentBox(ctx, &present_middleware.UnpackPresentBoxReq{
		Uid:       serviceInfo.UserID,
		BoxId:     req.GetBoxId(),
		ChannelId: req.GetChannelId(),
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "UnpackPresentBox UnpackPresentBox,  err :%v", err)
		return out, protocol.ToServerError(err)
	}

	out.BoxInfo = tranPMRespToLogic(resp.GetBoxInfo())

	return out, err
}

func tranPMRespToLogic(pmResp *present_middleware.PresentBoxInfo) *presentPB_.PresentBoxInfo {
	boxInfo := &presentPB_.PresentBoxInfo{
		ItemMsg: &presentPB_.PresentSendMsg{
			ItemInfo: &presentPB_.PresentSendItemInfo{
				ItemId:            pmResp.GetItemMsg().GetItemInfo().GetItemId(),
				Count:             pmResp.GetItemMsg().GetItemInfo().GetCount(),
				ShowEffect:        pmResp.GetItemMsg().GetItemInfo().GetShowEffect(),
				ShowEffectV2:      pmResp.GetItemMsg().GetItemInfo().GetShowEffectV2(),
				FlowId:            pmResp.GetItemMsg().GetItemInfo().GetFlowId(),
				IsBatch:           pmResp.GetItemMsg().GetItemInfo().GetIsBatch(),
				ShowBatchEffect:   pmResp.GetItemMsg().GetItemInfo().GetShowBatchEffect(),
				SendType:          pmResp.GetItemMsg().GetItemInfo().GetSendType(),
				DynamicTemplateId: pmResp.GetItemMsg().GetItemInfo().GetDynamicTemplateId(),
				IsVisibleToSender: pmResp.GetItemMsg().GetItemInfo().GetIsVisibleToSender(),
				IsShowSurprise:    pmResp.GetItemMsg().GetItemInfo().GetIsShowSurprise(),
				SurpriseCount:     pmResp.GetItemMsg().GetItemInfo().GetSurpriseCount(),
				CustomTextJson:    pmResp.GetItemMsg().GetItemInfo().GetCustomTextJson(),
			},
			SendTime:        pmResp.GetItemMsg().GetSendTime(),
			ChannelId:       pmResp.GetItemMsg().GetChannelId(),
			SendUid:         pmResp.GetItemMsg().GetSendUid(),
			SendAccount:     pmResp.GetItemMsg().GetSendAccount(),
			SendNickname:    pmResp.GetItemMsg().GetSendNickname(),
			TargetUid:       pmResp.GetItemMsg().GetTargetUid(),
			TargetAccount:   pmResp.GetItemMsg().GetTargetAccount(),
			TargetNickname:  pmResp.GetItemMsg().GetTargetNickname(),
			ExtendJson:      pmResp.GetItemMsg().GetExtendJson(),
			FromUserProfile: genUserProfile(pmResp.GetItemMsg().GetFromUserProfile()),
			ToUserProfile:   genUserProfile(pmResp.GetItemMsg().GetTargetUserProfile()),
		},
		BoxDetail: &presentPB_.PresentBoxDetail{
			BoxId:             pmResp.GetBoxDetail().GetBoxId(),
			FromUserProfile:   genUserProfile(pmResp.GetBoxDetail().GetFromUserProfile()),
			ToUserProfile:     genUserProfile(pmResp.GetBoxDetail().GetToUserProfile()),
			ItemId:            pmResp.GetBoxDetail().GetItemId(),
			ItemName:          pmResp.GetBoxDetail().GetItemName(),
			SendTime:          pmResp.GetBoxDetail().GetSendTime(),
			ExtendJson:        pmResp.GetBoxDetail().GetExtendJson(),
			DelayTime:         pmResp.GetBoxDetail().GetDelayTime(),
			IsVisibleToSender: pmResp.GetBoxDetail().GetIsVisibleToSender(),
		},
	}

	log.DebugWithCtx(context.Background(), "PresentBoxInfo %+v", boxInfo)

	return boxInfo
}

func tranEmperorInfoToLogic(pmResp *present_middleware.SetBoxInfo) *pb.EmperorBoxInfo {
	presentMsg := &pb.EmperorSetPresentMsg{}
	err := proto.Unmarshal(pmResp.GetItemMsg(), presentMsg)
	if err != nil {
		log.ErrorWithCtx(context.Background(), "Unmarshal presentMsg err :%v", err)
		return &pb.EmperorBoxInfo{}
	}

	boxInfo := &pb.EmperorBoxInfo{
		ItemMsg: presentMsg,
		BoxDetail: &pb.EmperorBoxDetail{
			BoxId:             pmResp.GetBoxDetail().GetBoxId(),
			FromUserProfile:   genUserProfile(pmResp.GetBoxDetail().GetFromUserProfile()),
			ToUserProfile:     genUserProfile(pmResp.GetBoxDetail().GetToUserProfile()),
			EmperorSetId:      pmResp.GetBoxDetail().GetSetId(),
			EmperorSetName:    pmResp.GetBoxDetail().GetSetName(),
			SendTime:          pmResp.GetBoxDetail().GetSendTime(),
			ExtendJson:        pmResp.GetBoxDetail().GetExtendJson(),
			DelayTime:         pmResp.GetBoxDetail().GetDelayTime(),
			IsVisibleToSender: pmResp.GetBoxDetail().GetIsVisibleToSender(),
		},
	}

	log.DebugWithCtx(context.Background(), "EmperorBoxInfo %+v", boxInfo)

	return boxInfo
}

func genUserProfile(profile *present_middleware.UserProfile) *gabase.UserProfile {
	if profile == nil {
		return &gabase.UserProfile{}
	}
	resp := &gabase.UserProfile{
		Uid:          profile.GetUid(),
		Account:      profile.GetAccount(),
		Nickname:     profile.GetNickname(),
		AccountAlias: profile.GetAccountAlias(),
		Sex:          profile.GetSex(),
	}

	if profile.GetPrivilege() != nil {
		resp.Privilege = &gabase.UserPrivilege{
			Account:  profile.GetPrivilege().GetAccount(),
			Nickname: profile.GetPrivilege().GetNickname(),
			Type:     profile.GetPrivilege().GetType(),
			Options:  profile.GetPrivilege().GetOptions(),
		}
	}

	return resp
}

const RequestInitFail = "InitRequestBaseInfo ServiceInfoFromContext fail. ctx:%+v"

func (s *PresentGoLogic_) GetPresentEffectTimeDetail(c context.Context, req *pb.GetPresentEffectTimeDetailReq) (*pb.GetPresentEffectTimeDetailResp, error) {
	out := &pb.GetPresentEffectTimeDetailResp{LevelInfo: []*pb.PresentEffectTimeLevelInfo{}}
	serviceInfo, ok := grpc.ServiceInfoFromContext(c)
	if !ok {
		log.ErrorWithCtx(c, RequestInitFail, c)
		return out, protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
	}

	resp, err := client.PresentExtraConfigCli.GetPresentEffectTimeDetail(c, &presentextraconf.GetPresentEffectTimeDetailReq{
		GiftId: req.GetGiftId(),
		Uid:    serviceInfo.UserID,
	})
	if err != nil {
		log.ErrorWithCtx(c, "GetPresentEffectTimeDetail GetPresentEffectTimeDetail err :%v", err)
		return out, protocol.ToServerError(err)
	}

	out.GiftId = resp.GetGiftId()
	out.NowCount = resp.GetNowCount()
	out.NoLimitExpireDayCount = resp.GetNoLimitExpireDayCount()
	for _, item := range resp.GetLevelInfo() {
		out.LevelInfo = append(out.LevelInfo, &pb.PresentEffectTimeLevelInfo{
			Level:          item.GetLevel(),
			LevelSendCount: item.GetLevelSendCount(),
			LevelDayCount:  item.GetLevelDayCount(),
		})
	}
	return out, nil
}

func (s *PresentGoLogic_) GetFriendGift(ctx context.Context, req *redpacket.GetFriendGiftReq) (*redpacket.GetFriendGiftResp, error) {
	out := &redpacket.GetFriendGiftResp{}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, RequestInitFail, ctx)
		return out, protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)

	}

	_, err := client.ConversionCli.GetFriendGift(ctx, &conversion.GetFriendGiftReq{
		Uid:     serviceInfo.UserID,
		OrderId: req.GetOrderId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFriendGift GetFriendGift err :%v", err)
		return out, protocol.ToServerError(err)
	}

	return out, nil
}

func (s *PresentGoLogic_) InitTimer() {
	// 相关配置的本地缓存
	ticker := time.NewTicker(time.Second * 5)
	for range ticker.C {
		s.FillExtraConfigCache()
	}
}

func (s *PresentGoLogic_) FillExtraConfigCache() {
	ctx := context.Background()
	resp, err := s.presentExtraConfCli.GetPresentFloatLayer(ctx, &presentextraconf.GetPresentFloatLayerReq{})
	if err != nil {
		log.ErrorWithCtx(ctx, "FillExtraConfigCache GetFlashEffectConfig err :%v", err)
		return
	}
	s.mapLock.Lock()
	s.presentFloatCache = resp
	s.mapLock.Unlock()

	tmpLastUpdate := uint32(0)
	for _, item := range resp.GetLayerInfos() {
		if item.GetLayerInfo().GetEffectStatus() == uint32(presentextraconf.EffectStatus_EffectStatus_Effective) {
			if item.GetLayerInfo().GetEffectBegin() >= tmpLastUpdate {
				tmpLastUpdate = item.GetLayerInfo().GetEffectBegin()
			}
		}
		if item.GetLayerInfo().GetEffectStatus() == uint32(presentextraconf.EffectStatus_EffectStatus_Outdated) {
			if item.GetLayerInfo().GetEffectEnd() >= tmpLastUpdate {
				tmpLastUpdate = item.GetLayerInfo().GetEffectEnd()
			}
		}
	}

	flash, err := s.presentExtraConfCli.GetPresentFlashEffect(ctx, &presentextraconf.GetPresentFlashEffectReq{})
	if err != nil {
		log.ErrorWithCtx(ctx, "FillExtraConfigCache GetFlashEffectConfig err :%v", err)
		return
	}

	s.mapLock.Lock()
	s.presentFlashCache = make([]*pb.PresentFlashEffect, 0)
	s.flashConfigCache = make([]*pb.FlashEffectConfig, 0)
	flashConfigMap := make(map[uint32]*pb.FlashEffectConfig)
	for _, item := range flash.GetPresentEffects() {
		if item.GetEffectStatus() == uint32(presentextraconf.EffectStatus_EffectStatus_Effective) {
			s.presentFlashCache = append(s.presentFlashCache, &pb.PresentFlashEffect{
				GiftId:  item.GetPresentConfig().GetGiftId(),
				FlashId: item.GetFlashInfo().GetFlashId(),
			})

			flashConfigMap[item.GetFlashInfo().GetFlashId()] = &pb.FlashEffectConfig{
				FlashId:   item.GetFlashInfo().GetFlashId(),
				FlashMd5:  item.GetFlashInfo().GetFlashMd5(),
				FlashUrl:  item.GetFlashInfo().GetFlashUrl(),
				FlashName: item.GetFlashInfo().GetFlashName(),
			}

			if item.GetEffectBegin() >= tmpLastUpdate {
				tmpLastUpdate = item.GetEffectBegin()
			}
		}

		if item.GetEffectStatus() == uint32(presentextraconf.EffectStatus_EffectStatus_Outdated) {
			if item.GetEffectEnd() >= tmpLastUpdate {
				tmpLastUpdate = item.GetEffectEnd()
			}
		}
	}

	for _, item := range flashConfigMap {
		s.flashConfigCache = append(s.flashConfigCache, item)
	}
	s.mapLock.Unlock()

	s.lastUpdateTime = resp.GetLastUpdateTime()

	if tmpLastUpdate >= resp.GetLastUpdateTime() {
		s.lastUpdateTime = tmpLastUpdate
	}

	cfgResp, err := client.PresentCli.GetPresentConfigListV2ByUpdateTime(ctx, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "FillExtraConfigCache GetPresentConfigList err :%v", err)
		return
	}

	s.mapLock.Lock()
	s.presentConfigCache = make(map[uint32]*userpresent.StPresentItemConfig)
	for _, item := range cfgResp.GetItemList() {
		s.presentConfigCache[item.ItemId] = item
	}
	s.mapLock.Unlock()

	log.InfoWithCtx(ctx, "FillExtraConfigCache tmpLastUpdate: %d, len: %d , %d , %d",
		s.lastUpdateTime, len(s.flashConfigCache), len(s.presentFlashCache), len(s.presentFloatCache.GetLayerInfos()))

}

func (s *PresentGoLogic_) GetPresentExtraConfig(ctx context.Context, req *pb.GetPresentExtraConfigReq) (*pb.GetPresentExtraConfigResp, error) {

	s.mapLock.RLock()
	out := &pb.GetPresentExtraConfigResp{
		FlashEffects:           s.presentFlashCache,
		FlashEffectConfigs:     s.flashConfigCache,
		LastUpdateTime:         s.lastUpdateTime,
		PresentEffectTimeInfos: []*pb.PresentEffectTime{},
	}

	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, RequestInitFail, ctx)
		return out, protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
	}

	uid := serviceInfo.UserID
	marketId := serviceInfo.MarketID
	clientType := serviceInfo.ClientType

	out.LayerInfos = make([]*pb.PresentFloatLayer, 0)

	for _, item := range s.presentFloatCache.GetLayerInfos() {
		if !checkIsShowOnApp(ctx, item.GetLayerInfo().GetShowAppType(), uint32(clientType), marketId) {
			continue
		}

		// 开始结束时间不对也过滤掉
		if item.GetLayerInfo().GetEffectBegin() > uint32(time.Now().Unix()) || item.GetLayerInfo().GetEffectEnd() < uint32(time.Now().Unix()) {
			continue
		}

		out.LayerInfos = append(out.LayerInfos, &pb.PresentFloatLayer{
			GiftId:          item.GetLayerInfo().GetGiftId(),
			FloatImageUrl:   item.GetLayerInfo().GetFloatImageUrl(),
			JumpUrl:         GetUrlByApp(ctx, item.GetLayerInfo().GetAppJumpUrl(), uint32(clientType), marketId),
			IsActivityUrl:   item.GetLayerInfo().GetIsActivityUrl(),
			ShowChannelType: tranShowChannelTypeToUint32(item.GetLayerInfo().GetShowChannelType()),
			IsShowUgcLocked: getIsShowUgcLocked(item.GetLayerInfo().GetActivityType(), item.GetLayerInfo().GetSubActivityType()),
		})
	}
	s.mapLock.RUnlock()

	resp, err := client.PresentExtraConfigCli.GetPresentEffectTime(ctx, &presentextraconf.GetPresentEffectTimeReq{
		Uid: uid,
	})
	if err != nil {
		log.Errorf("GetPresentExtraConfig GetPresentEffectTime failed err %+v", err)
		return out, err
	}

	prResp, err := client.PresentPrivilegeCli.GetTreasurePrivilege(ctx, &presentprivilege.GetTreasurePrivilegeReq{
		Uid: uid,
	})
	if err != nil {
		log.Errorf("GetPresentExtraConfig GetTreasurePrivilege failed err %+v", err)
		//return nil, err
	}

	out.PrivilegePresentInfo = make([]*pb.PrivilegePresentInfo, 0)
	for _, item := range prResp.GetTreasurePrivilege() {
		if item.GetEndTime() <= uint32(time.Now().Unix()) {
			continue
		}
		out.PrivilegePresentInfo = append(out.PrivilegePresentInfo, &pb.PrivilegePresentInfo{
			GiftId:  item.GetGiftId(),
			EndTime: item.GetEndTime(),
		})
	}

	s.mapLock.RLock()
	defer s.mapLock.RUnlock()

	for _, item := range resp.GetPresentEffectTimeInfos() {
		if s.presentConfigCache[item.GetGiftId()].GetEffectBegin() >= uint32(time.Now().Unix()) {
			continue
		}

		out.PresentEffectTimeInfos = append(out.PresentEffectTimeInfos, &pb.PresentEffectTime{
			GiftId:    item.GetGiftId(),
			EffectEnd: item.GetEffectEnd(),
			EffectInfo: &pb.PresentEffectTimeInfo{
				NowCount:              item.GetEffectInfo().GetNowCount(),
				NextLevelSendCount:    item.GetEffectInfo().GetNextLevelSendCount(),
				NextLevelDayCount:     item.GetEffectInfo().GetNextLevelDayCount(),
				MaxLevelSendCount:     item.GetEffectInfo().GetMaxLevelSendCount(),
				IsMaxLevel:            item.GetEffectInfo().GetIsMaxLevel(),
				NoLimitExpireDayCount: item.GetEffectInfo().GetNoLimitExpireDayCount(),
				LastSendTs:            item.GetEffectInfo().GetLastSendTs(),
				MaxLevelDayCount:      item.GetEffectInfo().GetMaxLevelDayCount(),
				EffectEndOnShelf:      item.GetEffectInfo().GetEffectEndOnShelf(),
				NowLevelDayCount:      item.GetEffectInfo().GetNowLevelDayCount(),
				NoticeNoLimitExpire:   item.GetEffectInfo().GetNoticeNoLimitExpire(),
			},
		})
	}

	log.DebugWithCtx(ctx, "GetPresentExtraConfig resp: %+v", out)

	return out, nil
}

func getIsShowUgcLocked(activityType presentextraconf.PresentFloatLayer_ActivityType, subActivityType presentextraconf.PresentFloatLayer_SubActivityType) bool {
	if activityType == presentextraconf.PresentFloatLayer_ACTIVITY_TYPE_REVENUE && subActivityType == presentextraconf.PresentFloatLayer_SUB_ACTIVITY_TYPE_REVENUE_BENEFIT {
		return false
	}
	return true
}

type StartConfig struct {
	ConstellationUrl string `json:"constellation_url"`
}

func NewPresentGoLogic(ctx context.Context, cfg *StartConfig, tracer opentracing.Tracer) (*PresentGoLogic_, error) {
	log.DebugWithCtx(ctx, "NewPresentGoLogic_ start")
	presentExtraConfCli, err := present_extra_conf.NewClient()
	if err != nil {
		log.Errorf("NewPresentGoLogic_ present_extra_conf.NewClient failed err %+v", err)
		return nil, err
	}

	log.DebugWithCtx(ctx, "ConstellationUrl is %s", cfg.ConstellationUrl)

	svr := &PresentGoLogic_{
		businessConf:        conf.NewBusinessConfManager(ctx),
		sendPresentConf:     conf.NewSendPresentConf(ctx),
		drawGameConf:        conf.NewDrawConfigConf(ctx),
		imPresentConf:       conf.NewImPresentConf(ctx),
		presentSetConf:      conf.NewPresentSetConf(ctx),
		constellationUrl:    cfg.ConstellationUrl,
		presentExtraConfCli: presentExtraConfCli,
	}
	_ = client.Setup()

	log.InfoWithCtx(ctx, "NewPresentGoLogic_ setup client")

	err = breaker.Setup(ctx)
	if err != nil {
		log.Errorf("NewPresentGoLogic_ breaker.Setup failed err %+v", err)
		return nil, err
	}

	log.InfoWithCtx(ctx, "NewPresentGoLogic_ setup breaker")

	svr.FillExtraConfigCache()
	//chance_cfg.InitChanceCfgMgr()

	go svr.InitTimer()

	svr.presentConfigMemCache = present_config_cache.NewPresentMemCache()
	svr.presentSetCache = present_set_cache.NewPresentMemCache()
	svr.emperorSetCache = emperor_set_cache.NewEmperorMemCache()
	svr.intimatePresentCache = intimate_present_cache.NewIntimatePresentMemCache()

	svr.timePresentConfig, _ = conf.InitTimePresentConfig()

	return svr, nil
}

func (s *PresentGoLogic_) GetUserActPresentArea(ctx context.Context, in *pb.GetUserActPresentAreaReq) (out *pb.GetUserActPresentAreaResp, err error) {
	out = &pb.GetUserActPresentAreaResp{}

	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, RequestInitFail, ctx)
		return out, protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
	}

	// 总开关
	if !s.businessConf.GetAreaSwitch() {
		out.AreaSwitch = false

		log.DebugfWithCtx(ctx, "GetUserActPresentArea req:%+v, AreaSwitch is false", in)
		return out, nil
	}

	out.AreaSwitch = true

	presentIdList := make([]uint32, 0, 2)
	frameConf := s.businessConf.GetCurrFrameAreaConf()
	if frameConf == nil {
		log.ErrorWithCtx(ctx, "GetUserActPresentArea fail. frameConf is nil. req:%+v", in)
		return out, nil
	}

	out.Frame = &pb.ActFrameConf{
		Title:        frameConf.Title,
		ResourcesUrl: frameConf.FrameUrl,
	}

	frameUrl := marketid_helper.Get("constellation_url_resource", in.GetBaseReq().GetMarketId(), uint32(serviceInfo.ClientType))
	if frameUrl != "" && ttversion.MikeResourceFeature.Atleast(serviceInfo.ClientType, serviceInfo.ClientVersion) {
		out.Frame.ResourcesUrl = frameUrl
	}
	log.DebugWithCtx(ctx, "constellation_url_resource %s , %+v , %+v , %s", out.Frame.ResourcesUrl, in.GetBaseReq(), serviceInfo, frameUrl)

	for _, info := range frameConf.PresentList {
		presentIdList = append(presentIdList, info.PresentId)
	}

	summaryResp, serr := client.PresentCli.GetUserPresentSummaryByItemList(ctx, in.GetUid(), false, presentIdList)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetUserActPresentArea fail to GetUserPresentSummaryByItemList. req:%+v err:%v", in, serr)
		return out, serr
	}

	presentList := make([]*pb.ActPresentDetail, 0, 2)
	mapPresentDetail := make(map[uint32]*pb.ActPresentDetail)

	for _, s := range summaryResp.GetSummaryList() {
		mapPresentDetail[s.GetItemId()] = &pb.ActPresentDetail{
			ItemId: s.GetItemId(),
			Num:    s.GetCount(),
		}
	}

	for _, id := range presentIdList {
		if detail, ok := mapPresentDetail[id]; ok {
			presentList = append(presentList, detail)
		} else {
			presentList = append(presentList, &pb.ActPresentDetail{
				ItemId: id,
				Num:    0,
			})
		}
	}

	jumpUrl := marketid_helper.Get("constellation_url", in.GetBaseReq().GetMarketId(), uint32(serviceInfo.ClientType))
	out.PresentArea = &pb.ActPresentArea{
		Title:   "星座礼物",
		JumpUrl: jumpUrl,
		Detail:  presentList,
	}

	achievementConf := s.businessConf.GetCurrAchievementAreaConf()
	if achievementConf == nil {
		log.ErrorWithCtx(ctx, "GetUserActPresentArea fail. achievementConf is nil. req:%+v", in)
		return out, nil
	}

	status, err := GetUserActAchievementStatus(ctx, in.GetUid(), s.constellationUrl)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserActPresentArea fail to GetUserActAchievementStatus. req:%+v err:%v", in, err)
	}

	achievementJumpUrl := marketid_helper.Get("constellation_url_achieve", in.GetBaseReq().GetMarketId(), uint32(serviceInfo.ClientType))

	out.Achievement = &pb.AchievementArea{
		Title:   "星座成就",
		JumpUrl: achievementJumpUrl,
		PicUrl:  achievementConf.PicUrl,
		Status:  status,
	}

	remindConf := s.businessConf.GetCurrRemindConf()
	if remindConf != nil {
		out.Remind = &pb.ActRemindConf{
			BeginTs:    remindConf.BeginTs,
			EndTs:      remindConf.EndTs,
			RemindText: remindConf.RemindText,
			Duration:   remindConf.Duration,
		}
	}

	log.DebugfWithCtx(ctx, "GetUserActPresentArea req:%+v, resp:%+v", in, out)
	return out, nil
}

func (s *PresentGoLogic_) ShutDown() {
	s.businessConf.Close()
	s.presentConfigMemCache.Stop()
}

func tranShowChannelTypeToUint32(showType []presentextraconf.PresentFloatLayer_ChannelType) []uint32 {
	out := make([]uint32, 0)
	for _, item := range showType {
		out = append(out, uint32(item))
	}
	return out
}

func checkIsShowOnApp(ctx context.Context, showType []presentextraconf.PresentFloatLayer_AppType, clientType, marketId uint32) bool {
	defer log.DebugWithCtx(ctx, "checkIsShowOnApp showType:%v, marketId:%d, clientType:%d", showType, marketId, clientType)

	for _, item := range showType {
		ok := false
		switch item {
		case presentextraconf.PresentFloatLayer_APP_TYPE_PC:
			ok = clientType == uint32(protocol.ClientTypePcTT)
		case presentextraconf.PresentFloatLayer_APP_TYPE_TT_ANDROID:
			ok = (clientType == uint32(protocol.ClientTypeANDROID)) && (marketId == uint32(protocol.TT_MarketID))
		case presentextraconf.PresentFloatLayer_APP_TYPE_TT_IOS:
			ok = (clientType == uint32(protocol.ClientTypeIOS)) && (marketId == uint32(protocol.TT_MarketID))
		case presentextraconf.PresentFloatLayer_APP_TYPE_HUANYOU_ANDROID:
			ok = (clientType == uint32(protocol.ClientTypeANDROID)) && (marketId == uint32(protocol.HUANYOU_MarketID))
		case presentextraconf.PresentFloatLayer_APP_TYPE_HUANYOU_IOS:
			ok = (clientType == uint32(protocol.ClientTypeIOS)) && (marketId == uint32(protocol.HUANYOU_MarketID))
		case presentextraconf.PresentFloatLayer_APP_TYPE_MAIKE_ANDROID:
			ok = (clientType == uint32(protocol.ClientTypeANDROID)) && (marketId == uint32(protocol.MIC_MarketID))
		case presentextraconf.PresentFloatLayer_APP_TYPE_MAIKE_IOS:
			ok = (clientType == uint32(protocol.ClientTypeIOS)) && (marketId == uint32(protocol.MIC_MarketID))
		}
		if ok {
			return true
		}
	}

	return false
}

func GetUrlByApp(ctx context.Context, urls []*presentextraconf.AppUrl, clientType, marketId uint32) string {
	defer log.DebugWithCtx(ctx, " GetUrlByApp urls:%v, marketId:%d, clientType:%d", urls, marketId, clientType)
	// 先找到TT的URL
	ttUrl := ""
	for _, item := range urls {
		if item.GetUrlType() == presentextraconf.AppUrl_URL_TYPE_TT {
			ttUrl = item.GetUrl()
		}
	}

	for _, item := range urls {
		ok := false
		switch item.GetUrlType() {
		case presentextraconf.AppUrl_URL_TYPE_TT:
			ok = marketId == uint32(protocol.TT_MarketID)
		case presentextraconf.AppUrl_URL_TYPE_HUANYOU_ANDROID:
			ok = (clientType == uint32(protocol.ClientTypeANDROID)) && (marketId == uint32(protocol.HUANYOU_MarketID))
		case presentextraconf.AppUrl_URL_TYPE_HUANYOU_IOS:
			ok = (clientType == uint32(protocol.ClientTypeIOS)) && (marketId == uint32(protocol.HUANYOU_MarketID))
		case presentextraconf.AppUrl_URL_TYPE_MAIKE_ANDROID:
			ok = (clientType == uint32(protocol.ClientTypeANDROID)) && (marketId == uint32(protocol.MIC_MarketID))
		case presentextraconf.AppUrl_URL_TYPE_MAIKE_IOS:
			ok = (clientType == uint32(protocol.ClientTypeIOS)) && (marketId == uint32(protocol.MIC_MarketID))
		}
		if ok {
			return item.GetUrl()
		}
	}

	return ttUrl
}
