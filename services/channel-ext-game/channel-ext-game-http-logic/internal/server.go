package internal

import (
    "context"
    "encoding/hex"
    "fmt"
    "github.com/thinkeridea/go-extend/exnet"
    grpc "gitlab.ttyuyin.com/avengers/tyr/core/service/basepb/info"
    "gitlab.ttyuyin.com/avengers/tyr/core/service/http"
    "golang.52tt.com/protocol/common/status"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/clients/account"
    HeadImage "golang.52tt.com/clients/headimage"
    riskMngApiClient "golang.52tt.com/clients/risk-mng-api"
    usualDevice "golang.52tt.com/clients/usual-device"
    youknowwhoCli "golang.52tt.com/clients/you-know-who"
    "golang.52tt.com/pkg/protocol"
    apppb "golang.52tt.com/protocol/app"
    channel_ext_game "golang.52tt.com/protocol/services/channel-ext-game"
    "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_go"
    "golang.52tt.com/protocol/services/demo/echo"
    "golang.52tt.com/protocol/services/tt_rev_common"
    "golang.52tt.com/services/channel-ext-game/channel-ext-game-http-logic/internal/conf"
    "strings"
    "time"
    "golang.52tt.com/pkg/tbean"
    "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channelol_go"
    "github.com/google/uuid"
    riskMngApiPb "golang.52tt.com/protocol/services/risk-mng-api"
    "math"
)

const (
    BadRequestDesc  = "bad request param"
    BadAppidDesc    = "bad appid"
    BadSecretDesc   = "bad secret"
    InternalErrDesc = "internal error"
)

const (
    WebTokenJwtIssuer = "52tt.com"
    BasePath          = "/ext-game"
    GetSessionPath    = "/session/get"
    CheckSessionPath  = "/session/check"
    CoinConsumePath   = "/coin/consume"
    RewardsIssuePath  = "/rewards/issue"
    EchoPath          = "/echo"
    ReNewSessionPath  = "/session/renew"
    BatchGetUserPath  = "/user/batget"
    BatchGetRoomPath  = "/room/batget"
    SendMsgPath       = "/msg/send"
)

const minutesPerDay = 1440 // 一天的分钟数
const (
    MaxGiftAwardAmount  = 10                 // 一次最多发放10个
    MaxDressAwardMinute = minutesPerDay * 31 // 最多31天
)

const (
    ErrConsumeReject = 101
)

type StartConfig struct {
    TbeanContextPath string `json:"tbean_context_path"`
}

type Server struct {
    bc                  conf.IBusinessConfManager
    ChannelGoCli        channel_go.ChannelGoClient
    AccountCli          account.IClient
    YKWCli              youknowwhoCli.IClient
    ChannelExtGameCli   channel_ext_game.ChannelExtGameClient
    riskMngApiCli       riskMngApiClient.IClient
    UsualDeviceCli      usualDevice.IClient
    HeadImageCli        HeadImage.IClient
    r                   *http.Router
    passCheckPathMap    map[string]bool
    sessionCheckPathMap map[string]bool
    TbeanCli            tbean.Client
    channelOlCli        channelol_go.ChannelolGoClient
}

func NewServer(ctx context.Context, sc *StartConfig, r *http.Router) (*Server, error) {
    accountCli, _ := account.NewClient()
    YKWCli, _ := youknowwhoCli.NewClient()
    channelExtGameCli := channel_ext_game.MustNewClient(context.Background())

    riskMngApiCli, err := riskMngApiClient.NewClient()
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to NewRiskMngApiClient, err:%v", err)
        return nil, err
    }

    usualDeviceCli := usualDevice.NewClient()

    bc, err := conf.NewBusinessConfManager()
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to NewBusinessConfManager, err:%v", err)
        return nil, err
    }

    headImageCli := HeadImage.NewClient()
    channelGoCli, _ := channel_go.NewClient(ctx)

    TbeanCli := tbean.NewClient(sc.TbeanContextPath)
    channelOlGoCli := channelol_go.MustNewClient(context.Background())

    svr := &Server{
        bc:                  bc,
        ChannelGoCli:        channelGoCli,
        AccountCli:          accountCli,
        YKWCli:              YKWCli,
        ChannelExtGameCli:   channelExtGameCli,
        r:                   r,
        riskMngApiCli:       riskMngApiCli,
        UsualDeviceCli:      usualDeviceCli,
        passCheckPathMap:    make(map[string]bool),
        sessionCheckPathMap: make(map[string]bool),
        HeadImageCli:        headImageCli,
        TbeanCli:            TbeanCli,
        channelOlCli:        channelOlGoCli,
    }
    r.AddRequestInterceptor(svr.AuthInterceptor())

    return svr, nil
}

// RegisterWithNoCheck 不需要校验
func (s *Server) RegisterWithNoCheck(path string, handler http.HandleFunc) {
    s.passCheckPathMap[path] = true
    s.r.POST(path, handler)
}

// RegisterWithSessionCheck 需要通用校验
func (s *Server) RegisterWithSessionCheck(path string, handler http.HandleFunc) {
    s.sessionCheckPathMap[path] = true
    s.r.POST(path, handler)
}

func (s *Server) Echo(_ context.Context, w http.ResponseWriter, r *http.Request) {
    // try http.GetUrlParams for url.Values
    msg := &echo.StringMessage{}
    if err := http.ReadJSON(r, msg); err != nil {
        w.WriteHeader(http.StatusBadRequest)
        return
    }

    _ = http.WriteJSON(w, http.StatusOK, msg)
}

func (s *Server) getUidByOpenId(ctx context.Context, openid string) (uint32, error) {
    resp, err := s.ChannelExtGameCli.GetUidByOpenid(ctx, &channel_ext_game.GetUidByOpenidReq{Openid: openid})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUidByOpenid failed, err %v", err)
        return 0, err
    }

    return resp.GetUid(), nil
}

func (s *Server) CoinConsume(ctx context.Context, w http.ResponseWriter, r *http.Request) {
    req := &ConsumeReq{}
    err := http.RepeatableReadJSON(r, req)
    if err != nil {
        log.ErrorWithCtx(ctx, "CoinConsume failed to ReadJSON. body [%s], err %v", r.RequestURI, err)
        ServeAPIJsonWithError(w, ErrBadRequestParams, BadRequestDesc)
        return
    }
    log.InfoWithCtx(ctx, "CoinConsume req %+v", req)

    ip := exnet.ClientIP(r)

    // 签名校验
    if !s.ValidateSignature(ctx, r, req.Appid) {
        log.ErrorWithCtx(ctx, "CoinConsume failed to ValidateSignature. req %v, err %v", req, err)
        ServeAPIJsonWithError(w, ErrBadRequestParams, "signature error")
        return
    }

    if req.Amount <= 0 || req.Amount >= math.MaxUint32 {
        log.ErrorWithCtx(ctx, "CoinConsume failed. req.Amount:%d, req:%+v", req.Amount, req)
        ServeAPIJsonWithError(w, ErrBadRequestParams, BadRequestDesc)
        return
    }

    now := time.Now()
    uid, err := s.getUidByOpenId(ctx, req.OpenId)
    if err != nil {
        log.ErrorWithCtx(ctx, "CoinConsume failed to getUidByOpenId. req %v, err %v", req, err)
        ServeAPIJsonWithError(w, ErrSystemInternal, "系统错误")
        return
    }
    if uid == 0 {
        log.ErrorWithCtx(ctx, "CoinConsume failed to getUidByOpenId. req %v openid 无效", req)
        ServeAPIJsonWithError(w, ErrBadRequestParams, "用户不存在")
        return
    }

    // 先检查T豆余额
    balance, e := s.TbeanCli.GetBalance(ctx, tbean.AppID_TT_HZ, tbean.AccountTypeCustomer, uid)
    if e != nil {
        serverError := protocol.ToServerError(e)
        if serverError != tbean.ErrAccountNotExists {
            log.ErrorWithCtx(ctx, "GetUserTBeanBalance GetBalance err ,uid :%d , err : %v", uid, e)
            //_ = web.ServeAPICodeJson(w, ErrSystemInternal, "系统错误", nil)
            //return // 非关键路径错误
        }
    } else {
        if int64(balance) < req.Amount {
            log.ErrorWithCtx(ctx, "CoinConsume failed to GetBalance. uid:%d req %v, err %v", uid, req, err)
            ServeAPIJsonWithError(w, ErrBalanceNotEnough, "豆豆余额不足")
            return
        }
    }

    // 风控检查
    riskInfo, err := s.consumeRiskCheck(ctx, ip, uid, req)
    if err != nil {
        errMsg := err.Error()
        log.ErrorWithCtx(ctx, "CoinConsume failed to riskCheck. uid:%d req %v, err %v", uid, req, err)
        ServeAPIJsonWithError(w, ErrBadRequestParams, errMsg)
        return
    }

    // 风控信息不为空
    if riskInfo != nil {
        log.ErrorWithCtx(ctx, "CoinConsume failed to riskCheck. uid:%d req %v, riskInfo %+v", uid, req, riskInfo)
        ServeAPIJsonWithErrorAndData(w, ErrNeedRiskCheck, riskInfo.ErrMsg, &ConsumeResp{
            RiskAuthInfo: riskInfo,
        })
        return
    }

    _, err = s.ChannelExtGameCli.ExtGameConsume(ctx, &channel_ext_game.ExtGameConsumeReq{
        Openid:    req.OpenId,
        Appid:     req.Appid,
        OrderId:   req.OrderId,
        Amount:    uint32(req.Amount),
        OutsideTs: now.Unix(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "CoinConsume failed to ExtGameConsume.uid:%d req %v, err %v", uid, req, err)

        e := protocol.ToServerError(err)
        code := ErrSystemInternal
        msg := e.Message()
        switch e.Code() {
        case status.ErrTbeanNoEnoughBalance:
            code = ErrBalanceNotEnough
            msg = "豆豆余额不足"

        case status.ErrChannelExtGameAwardOrderExists:
            code = ErrOrderExist
            msg = "订单已存在"

        default:
            if msg == "" {
                var ok bool
                msg, ok = status.CodeMessageMap[e.Code()]
                if !ok {
                    msg = "系统错误"
                }
            }
        }

        // 关键字替换，"T豆"->"豆豆"
        msg = strings.ReplaceAll(msg, "T豆", "豆豆")
        ServeAPIJsonWithError(w, code, msg)
        return
    }

    log.InfoWithCtx(ctx, "CoinConsume uid:%d req:%+v", uid, req)
    ServePB2JSON(w, nil)
}

func (s *Server) consumeRiskCheck(ctx context.Context, ip string, uid uint32, req *ConsumeReq) (*RiskAuthInfo, error) {

    // clientInfo
    clientInfo, err := GetClientInfoByEncodedStr(ctx, uid, req.ClientInfo)
    if err != nil {
        return nil, fmt.Errorf("参数解析失败,请重新登陆游戏")
    }

    ipUint, err := exnet.IPString2Long(ip)
    if err != nil {
        log.WarnWithCtx(ctx, "riskCheck failed to parse ip, uid:%d, err %+v", uid, err)
    }

    svrInfo := genServiceInfoCtx(clientInfo, uid, uint32(ipUint))
    svrInfo.RequestID = uuid.New().String()
    ctx = grpc.WithServiceInfo(ctx, svrInfo)

    // 2. 非常用设备检查
    if err := s.checkIfCanOp(ctx, uid, clientInfo); err != nil {
        return &RiskAuthInfo{
            ErrCode: int32(err.Code()),
            ErrMsg:  err.Message(),
        }, nil // 非常用设备的检查，非关键路径错误忽略
    }

    cid := uint32(0)
    // 查询用户当前所在的房间
    channelResp, err := s.channelOlCli.GetUserChannelId(ctx, &channelol_go.GetUserChannelIdReq{
        Uid: uid,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "riskCheck failed to GetUserChannelId. req %v, err %v", req, err)
        return nil, fmt.Errorf("系统错误")
    }
    cid = channelResp.GetChannelId()

    feeRMB := float64(req.Amount) / 100.0
    // feeRMB to string
    feeStr := fmt.Sprintf("%.2f", feeRMB)

    checkReq := &riskMngApiPb.CheckReq{
        Scene: "THIRD_GAME_CONSUME_ZDXX",
        SourceEntity: &riskMngApiPb.Entity{
            // 必选
            Uid:       uid,
            ClientIp:  ip,
            ChannelId: cid,
        },
        // 通用参数传递
        CustomParams: map[string]string{
            "consume_type": "1", // 1-"普通消费"
            "scene_id":     fmt.Sprintf("%d", tt_rev_common.ConsumeSceneType_CONSUME_SCENE_TYPE_THIRDPART_GAME_ZDXX),
            "amount":       feeStr,
        },
    }

    resultToken := req.RiskToken
    if resultToken != "" {
        // req.ResultToken 解密
        resultTokenByte, err := getInfoByAESDecrypt(ctx, req.RiskToken)
        if err != nil {
            log.ErrorWithCtx(ctx, "riskCheck failed to getInfoByAESDecrypt. req %v, err %v", req, err)
            // 非关键错误
        }
        if resultTokenByte != nil {
            resultToken = string(resultTokenByte)
        }
    }

    if clientInfo.ClientType == uint32(apppb.TT_CLIENT_TYPE_TT_CLIENT_TYPE_PC_TT) {
        // PC端补充RiskCommInfo.Token
        checkReq.RiskCommInfo = &riskMngApiPb.RiskCommInfo{
            Token: resultToken,
        }

    } else {
        // 移动端：补充FaceAuthInfo.ResultToken
        checkReq.SourceEntity.FaceAuthInfo = &riskMngApiPb.FaceAuthInfo{
            ResultToken: resultToken,
        }
    }

    checkResp, err := s.riskMngApiCli.CheckHelper(ctx, checkReq, nil)
    if err != nil {
        // 系统错误，风控非关键路径，可忽略系统错误
        log.ErrorWithCtx(ctx, "riskCheck risk-mng-api.Check failed, err:%v, req:%+v", err, checkReq)
        return nil, nil
    }

    // 命中风控拦截
    if checkResp.ErrCode < 0 {
        // 建议打个 info 拦截日志，方便排查，风控拦截日志不会很多
        log.InfoWithCtx(ctx, "riskCheck risk-mng-api.Check hit, req:%+v, resp:%+v", checkReq, checkResp)
        // 返回错误码给客户端，并设置 gRPC 错误码为 OK
        riskAuthInfo := &RiskAuthInfo{
            ErrCode: checkResp.GetErrCode(),
            ErrMsg:  checkResp.GetErrMsg(),
            ErrInfo: string(checkResp.GetErrInfo()),
        }
        if riskAuthInfo.ErrMsg == "" {
            riskAuthInfo.ErrMsg = status.CodeMessageMap[int(checkResp.GetErrCode())]
        }

        return riskAuthInfo, nil
    }

    log.DebugWithCtx(ctx, "riskCheck risk-mng-api.Check not hit, req:%+v, resp:%+v", checkReq, checkResp)
    // 无拦截
    return nil, nil
}

func (s *Server) checkIfCanOp(ctx context.Context, uid uint32, clientInfo *ClientInfo) protocol.ServerError {

    newDeviceId := strings.Replace(clientInfo.DeviceId, "-", "", -1)

    clientType := clientInfo.ClientType
    if clientInfo.ClientType != uint32(protocol.ClientTypeANDROID) && clientInfo.ClientType != uint32(protocol.ClientTypeIOS) {
        clientType = protocol.ClientTypePcTT
    }

    hexDeviceId, _ := hex.DecodeString(newDeviceId)

    // 检查是否是常用设备，不是则报错
    res, sErr := s.UsualDeviceCli.CheckUsualDevice(ctx, string(hexDeviceId), uid, 1, clientType)
    if sErr != nil {
        e := protocol.ToServerError(sErr)
        log.ErrorWithCtx(ctx, "Fail to CheckUsualDevice err(%v)", sErr)
        return e
    }

    if !res.Result {
        // 这里web会帮忙判断版本，版本就随便填吧
        err := s.UsualDeviceCli.GetDeviceAuthError(ctx, uint64(uid), uint16(clientInfo.ClientType), clientInfo.AppVersion)
        if err != nil {
            e := protocol.ToServerError(err)
            log.ErrorWithCtx(ctx, "checkIfCanOp fail. CheckUsualDevice uid(%d) err:%v", uid, err)
            return e
        }
    }

    return nil
}

func (s *Server) RewardsIssue(ctx context.Context, w http.ResponseWriter, r *http.Request) {
    req := &RewardReq{}
    err := http.RepeatableReadJSON(r, req)
    if err != nil {
        log.ErrorWithCtx(ctx, "RewardsIssue failed to ReadJSON. body [%s], err %v", r.RequestURI, err)
        ServeAPIJsonWithError(w, ErrBadRequestParams, BadRequestDesc)
        return
    }
    log.DebugWithCtx(ctx, "RewardsIssue req %v", req)

    // 签名校验
    if !s.ValidateSignature(ctx, r, req.Appid) {
        log.ErrorWithCtx(ctx, "RewardsIssue failed to ValidateSignature. req %v, err %v", req, err)
        ServeAPIJsonWithError(w, ErrBadRequestParams, "signature error")
        return
    }

    uid, err := s.getUidByOpenId(ctx, req.OpenId)
    if err != nil {
        log.ErrorWithCtx(ctx, "RewardsIssue failed to getUidByOpenId. req %v, err %v", req, err)
        ServeAPIJsonWithError(w, ErrBadRequestParams, BadRequestDesc)
        return
    }

    ip := exnet.ClientIP(r)
    gameConf, exist := s.bc.GetGameAppConfByAppid(req.Appid)
    if !exist {
        log.ErrorWithCtx(ctx, "RewardsIssue failed to GetGameAppConfByAppid.uid:%d req:%+v, err:%v", uid, req)
        ServeAPIJsonWithError(w, ErrBadAppid, BadAppidDesc)
        return
    }

    award, exist := gameConf.GetAwardInfo(req.RewardId, req.RewardType)
    if !exist {
        log.ErrorWithCtx(ctx, "RewardsIssue fail. uid:%d appid:%s, rewardId:%s, rewardType:%d not exist", uid, req.Appid, req.RewardId, req.RewardType)
        ServeAPIJsonWithError(w, ErrBadRequestParams, "奖励不存在")
        return
    }

    amount := awardAmountTransform(req.RewardType, req.RewardNum)
    if amount == 0 {
        log.ErrorWithCtx(ctx, "RewardsIssue fail. uid:%d appid:%s, req:%+v amount is zero", uid, req)
        ServeAPIJsonWithError(w, ErrBadRequestParams, "奖励发放数量无效")
        return
    }

    //风控检查
    hitRisk, err := s.awardRiskCheck(ctx, ip, uid, req, award)
    if err != nil || hitRisk {
        log.ErrorWithCtx(ctx, "RewardsIssue failed to awardRiskCheck. uid:%d req %v, err %v", uid, req, err)
        ServeAPIJsonWithError(w, ErrCommonErrorWithToast, err.Error())
        return
    }

    _, err = s.ChannelExtGameCli.ExtGameAward(ctx, &channel_ext_game.ExtGameAwardReq{
        Openid:     req.OpenId,
        Appid:      req.Appid,
        OrderId:    req.OrderId,
        RewardId:   award.RewardId,
        RewardType: req.RewardType,
        Amount:     amount,
    })
    if err != nil {
        e := protocol.ToServerError(err)
        code := ErrSystemInternal
        msg := "系统错误"
        if e.Code() == status.ErrChannelExtGameAwardOrderExists {
            code = ErrOrderExist
            msg = "订单已存在"
        }
        log.ErrorWithCtx(ctx, "RewardsIssue failed to ExtGameAward. uid:%d req %v, err %v", uid, req, err)
        ServeAPIJsonWithError(w, code, msg)
        return
    }

    log.InfoWithCtx(ctx, "RewardsIssue uid:%d,req:%+v", uid, req)
    ServePB2JSON(w, nil)
}

func awardAmountTransform(awardType, awardAmount uint32) uint32 {
    if awardAmount == 0 {
        return awardAmount
    }
    switch awardType {

    case uint32(channel_ext_game.ExtGameAwardReq_REWARD_TYPE_TBEAN),
        uint32(channel_ext_game.ExtGameAwardReq_REWARD_TYPE_RED_DIAMONDD):
        if awardAmount > MaxGiftAwardAmount {
            return 0
        }
        return awardAmount
    case uint32(channel_ext_game.ExtGameAwardReq_REWARD_TYPE_HEADWEAR),
        uint32(channel_ext_game.ExtGameAwardReq_REWARD_TYPE_HORSE),
        uint32(channel_ext_game.ExtGameAwardReq_REWARD_TYPE_OFFICIAL_CERT),
        uint32(channel_ext_game.ExtGameAwardReq_REWARD_TYPE_NAMEPLATE),
        uint32(channel_ext_game.ExtGameAwardReq_REWARD_TYPE_USER_DECORATION),
        uint32(channel_ext_game.ExtGameAwardReq_REWARD_TYPE_MEDAL):
        if awardAmount > MaxDressAwardMinute {
            return 0
        }
        // 将分钟转换为天，向上取整： (minutes + minutesPerDay - 1) / minutesPerDay
        return (awardAmount + minutesPerDay - 1) / minutesPerDay

    default:
        return awardAmount
    }
}

func (s *Server) awardRiskCheck(ctx context.Context, ip string, uid uint32, req *RewardReq, award *conf.AwardInfo) (hit bool, err error) {
    worth := float64(award.Worth) * float64(req.RewardNum)
    feeRMB := worth
    if worth > 0 {
        feeRMB = feeRMB / 100.0
    }

    feeStr := fmt.Sprintf("%.2f", feeRMB)

    // clientInfo
    clientInfo, err := GetClientInfoByEncodedStr(ctx, uid, req.ClientInfo)
    if err != nil {
        log.ErrorWithCtx(ctx, "awardRiskCheck failed to getClientInfoByEncodedStr.  req %v, err %v", req, err)
        return false, fmt.Errorf("参数错误，请重新登陆游戏")
    }

    ipUint, err := exnet.IPString2Long(ip)
    if err != nil {
        log.WarnWithCtx(ctx, "awardRiskCheck failed to parse ip, uid:%d, err %+v", uid, err)
    }

    svrInfo := genServiceInfoCtx(clientInfo, uid, uint32(ipUint))
    svrInfo.RequestID = uuid.New().String()
    ctx = grpc.WithServiceInfo(ctx, svrInfo)

    checkReq := &riskMngApiPb.CheckReq{
        Scene: "THIRD_GAME_AWARD_ZDXX",
        SourceEntity: &riskMngApiPb.Entity{
            // 必选
            Uid:      uid,
            ClientIp: ip,
        },
        // 通用参数传递
        CustomParams: map[string]string{
            "activity_id": "THIRD_GAME_AWARD_ZDXX",
            "amount":      feeStr,
        },
    }
    checkResp, err := s.riskMngApiCli.CheckHelper(ctx, checkReq, nil)
    if err != nil {
        log.ErrorWithCtx(ctx, "riskCheck risk-mng-api.Check failed, err:%v, req:%+v", err, checkReq)
        return false, nil // 系统错误，风控非关键路径，可忽略系统错误
    }

    // 命中风控拦截
    if checkResp.ErrCode < 0 {
        // 建议打个 info 拦截日志，方便排查，风控拦截日志不会很多
        log.InfoWithCtx(ctx, "riskCheck risk-mng-api.Check hit, req:%+v, resp:%+v", checkReq, checkResp)
        return true, fmt.Errorf(checkResp.GetErrMsg()) // 返回特定的错误提示
    }

    log.DebugWithCtx(ctx, "riskCheck risk-mng-api.Check not hit, req:%+v, resp:%+v", checkReq, checkResp)
    // 无拦截
    return false, nil
}

// SendPlatformAssistantMessage 平台私信、房间公屏
func (s *Server) SendPlatformAssistantMessage(ctx context.Context, w http.ResponseWriter, r *http.Request) {
    req := &SendMsgReq{}
    err := http.RepeatableReadJSON(r, req)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendPlatformAssistantMessage failed to ReadJSON. body [%s], err %v", r.RequestURI, err)
        ServeAPIJsonWithError(w, ErrBadRequestParams, BadRequestDesc)
        return
    }

    // 签名校验
    if !s.ValidateSignature(ctx, r, req.Appid) {
        log.ErrorWithCtx(ctx, "SendPlatformAssistantMessage failed to ValidateSignature. req %v, err %v", req, err)
        ServeAPIJsonWithError(w, ErrBadRequestParams, "signature error")
        return
    }

    log.DebugWithCtx(ctx, "SendPlatformAssistantMessage req:%+v", req)

    _, exist := s.bc.GetGameAppConfByAppid(req.Appid)
    if !exist {
        log.ErrorWithCtx(ctx, "SendPlatformAssistantMessage failed to GetGameAppConfByAppid. req:%+v, err:%v", req)
        ServeAPIJsonWithError(w, ErrBadAppid, BadAppidDesc)
        return
    }

    _, err = s.ChannelExtGameCli.SendPlatformMsg(ctx, &channel_ext_game.SendPlatformMsgReq{
        AppId:      req.Appid,
        OpenIdList: req.OpenIdList,
        TemplateId: req.TemplateId,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SendPlatformAssistantMessage failed to SendPlatformMsg. req %v, err %v", req, err)
        ServeAPIJsonWithError(w, ErrSystemInternal, "系统错误")
        return
    }

    log.InfoWithCtx(ctx, "SendPlatformAssistantMessage uid:%d,appId:%s len(msgList)：%d, templateId:%d type:%d", req.Appid, len(req.OpenIdList), req.TemplateId)
    ServePB2JSON(w, nil)
}
