package internal

import (
    "context"
    "gitlab.ttyuyin.com/avengers/tyr/core/service/http"
    channel_ext_game "golang.52tt.com/protocol/services/channel-ext-game"
    "testing"
    http2 "net/http"
    "github.com/golang/mock/gomock"
    "strings"
    "golang.52tt.com/pkg/protocol"
    "gitlab.ttyuyin.com/bizFund/bizFund/protocol/common/status"
    "golang.52tt.com/services/channel-ext-game/channel-ext-game-http-logic/internal/conf"
    usualDeviceSvrPb "golang.52tt.com/protocol/services/usual-device-svr"
    riskMngApiPb "golang.52tt.com/protocol/services/risk-mng-api"
    "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_go"
    "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channelol_go"
)

var (
    testUid       = uint32(2400464)
    cid           = uint32(1)
    channelViewId = "1"
    worth         = uint32(1)
)

// 溢出检查
func TestServer_CoinOverflowCheck(t *testing.T) {
    amount := uint32(42949673) // maxUint32: 4294967295
    fee := amount * 100        // 对于uint32来说溢出
    t.Logf("fee: %d, uint32(fee):%d", fee, uint32(fee))
    t.Logf("amount:%d fee/1000: %v", amount, fee/1000)
}

/*
=== RUN   TestServer_CoinOverflowCheck
    server_test.go:30: fee: 4, uint32(fee):4
    server_test.go:31: amount:42949673 fee/1000: 0
用户可以仅花4金币，获得42949673个单价为100金币的物品，造成损失
*/

func TestServer_CoinConsume(t *testing.T) {
    s := newMockSvr(t)

    httpReq, err := http2.NewRequest("POST", "http://127.0.0.1/test", strings.NewReader(`{"appid":"tt_zuduixiuxian","open_id":"4640a2042f4e3edc442cbad86e042","amount":10,"order_id":"safafsdfdfgww15sfsg","session":"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJhcHBpZCI6InR0X3p1ZHVpeGl1eGlhbiIsImNsaWVudF90eXBlIjowLCJleHAiOjE3NDM0OTg5NTcsImlhdCI6MTc0MzQ5MTc1NywiaXNzIjoiNTJ0dC5jb20iLCJtYXJrZXRfaWQiOjAsIm9wZW5faWQiOiI0NjQwYTIwNDJmNGUzZWRjNDQyY2JhZDg2ZTA0MiIsInJvb21faWQiOiIyMTgwNjE4IiwidHlwIjoxLCJ0eXBlIjoiSldUIn0.uqveV8fro0uXZQTZvMwIWevygkgSyMOgAXgKX37wn8usS2bbHP9xPwmoXZI2R-C-J0SVEX7LuT7LZaowzAu4KA","face_auth_result_token":"","client_info":"zEWZ9iDSigJNstacV9EVpNMRw0NM7qT0qIWPoPph5A4GcA7JB3XjQgGzRJLHUvBaStltLEDoJ9P2/kJ88oxmL5oWHHcmkmNwBkapMmS+OfBTJFrdp32meXiUEZ0MJfd7/2fDzoOfX84cL/IHANUfztNurPW/0Z1+UFCvAKUReB8ZvSwk/fhaQN/yGL56JkUq"}`))
    if err != nil {
        t.Errorf("http.NewRequest post error %v", err)
        return
    }
    httpReq.Header.Set("Content-Type", "application/json")
    httpReq.Header.Set("x-signature", "AjB3In2ZlUxvm7pHPYzPWA==")
    httpReq.Header.Set("x-nonce-str", "123456")
    httpReq.Header.Set("x-timestamp", "1736217301")

    type args struct {
        in0 context.Context
        w   http.ResponseWriter
        r   *http.Request
    }
    tests := []struct {
        name     string
        args     args
        initFunc func()
    }{
        {name: "CoinConsume", args: args{
            in0: context.Background(),
            w:   &EmptyWriteResponse{},
            r:   httpReq,
        },
            initFunc: func() {
                MockCfg.EXPECT().GetGameAppConfByAppid(gomock.Any()).Return(&conf.GameAppConf{
                    AppID:     "tt_zuduixiuxian",
                    AppSecret: "123456zdxxsecret",
                }, true)
                channelExtGameCli.EXPECT().GetUidByOpenid(gomock.Any(), gomock.Any()).Return(&channel_ext_game.GetUidByOpenidResp{Uid: testUid}, nil)
                tbeanCli.EXPECT().GetBalance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(int32(1000), nil)

                // 非常用设备检查-pass
                usualDeviceCli.EXPECT().CheckUsualDevice(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&usualDeviceSvrPb.CheckUsualDeviceResp{Result: true}, nil)

                // 获取用户所在房间id
                channelolCli.EXPECT().GetUserChannelId(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channelol_go.GetUserChannelIdResp{
                    ChannelId: cid,
                }, nil)

                // 风控检查
                riskMngCli.EXPECT().CheckHelper(gomock.Any(), gomock.Any(), gomock.Any()).Return(&riskMngApiPb.CheckResp{
                    ErrCode: 0,
                }, nil)

                channelExtGameCli.EXPECT().ExtGameConsume(gomock.Any(), gomock.Any()).Return(&channel_ext_game.ExtGameConsumeResp{}, nil)
            },
        },
        {name: "GetSession T豆余额不足", args: args{
            in0: context.Background(),
            w:   &EmptyWriteResponse{},
            r:   httpReq,
        },
            initFunc: func() {
                MockCfg.EXPECT().GetGameAppConfByAppid(gomock.Any()).Return(&conf.GameAppConf{
                    AppID:     "tt_zuduixiuxian",
                    AppSecret: "123456zdxxsecret",
                }, true)
                channelExtGameCli.EXPECT().GetUidByOpenid(gomock.Any(), gomock.Any()).Return(&channel_ext_game.GetUidByOpenidResp{Uid: testUid}, nil)

                tbeanCli.EXPECT().GetBalance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(int32(10), nil)

                // 非常用设备检查-pass
                usualDeviceCli.EXPECT().CheckUsualDevice(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&usualDeviceSvrPb.CheckUsualDeviceResp{Result: true}, nil)

                // 获取用户所在房间id
                channelolCli.EXPECT().GetUserChannelId(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channelol_go.GetUserChannelIdResp{
                    ChannelId: cid,
                }, nil)

                // 风控检查
                riskMngCli.EXPECT().CheckHelper(gomock.Any(), gomock.Any(), gomock.Any()).Return(&riskMngApiPb.CheckResp{
                    ErrCode: 0,
                }, nil)
                channelExtGameCli.EXPECT().ExtGameConsume(gomock.Any(), gomock.Any()).Return(&channel_ext_game.ExtGameConsumeResp{}, protocol.NewExactServerError(nil, status.ErrTbeanNoEnoughBalance, "err"))
            },
        },
        {name: "CoinConsume 风控错误", args: args{
            in0: context.Background(),
            w:   &EmptyWriteResponse{},
            r:   httpReq,
        },
            initFunc: func() {
                MockCfg.EXPECT().GetGameAppConfByAppid(gomock.Any()).Return(&conf.GameAppConf{
                    AppID:     "tt_zuduixiuxian",
                    AppSecret: "123456zdxxsecret",
                }, true)
                channelExtGameCli.EXPECT().GetUidByOpenid(gomock.Any(), gomock.Any()).Return(&channel_ext_game.GetUidByOpenidResp{Uid: testUid}, nil)

                tbeanCli.EXPECT().GetBalance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(int32(1000), nil)

                // 非常用设备检查-pass
                usualDeviceCli.EXPECT().CheckUsualDevice(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&usualDeviceSvrPb.CheckUsualDeviceResp{Result: true}, nil)

                // 获取用户所在房间id
                channelolCli.EXPECT().GetUserChannelId(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channelol_go.GetUserChannelIdResp{
                    ChannelId: cid,
                }, nil)

                // 风控检查
                riskMngCli.EXPECT().CheckHelper(gomock.Any(), gomock.Any(), gomock.Any()).Return(&riskMngApiPb.CheckResp{
                    ErrCode: -5314,
                    ErrMsg:  "风控错误",
                }, nil)
            },
        },
        {name: "CoinConsume 其他错误", args: args{
            in0: context.Background(),
            w:   &EmptyWriteResponse{},
            r:   httpReq,
        },
            initFunc: func() {
                MockCfg.EXPECT().GetGameAppConfByAppid(gomock.Any()).Return(nil, false)
            },
        },
    }
    for _, tt := range tests {
        if tt.initFunc != nil {
            tt.initFunc()
        }

        t.Run(tt.name, func(t *testing.T) {
            s.CoinConsume(tt.args.in0, tt.args.w, tt.args.r)
        })
    }
}

func TestServer_RewardsIssue(t *testing.T) {
    s := newMockSvr(t)

    httpReq, err := http2.NewRequest("POST", "http://127.0.0.1/test", strings.NewReader(`{"appid":"tt_zuduixiuxian","open_id":"4640a2042f4e3edc442cbad86e042","order_id":"12312gdssdfh541","reward_id":2693,"reward_type":2,"reward_num":1,"session":"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJhcHBpZCI6InR0X3p1ZHVpeGl1eGlhbiIsImNsaWVudF90eXBlIjowLCJleHAiOjE3NDM0OTg5NTcsImlhdCI6MTc0MzQ5MTc1NywiaXNzIjoiNTJ0dC5jb20iLCJtYXJrZXRfaWQiOjAsIm9wZW5faWQiOiI0NjQwYTIwNDJmNGUzZWRjNDQyY2JhZDg2ZTA0MiIsInJvb21faWQiOiIyMTgwNjE4IiwidHlwIjoxLCJ0eXBlIjoiSldUIn0.uqveV8fro0uXZQTZvMwIWevygkgSyMOgAXgKX37wn8usS2bbHP9xPwmoXZI2R-C-J0SVEX7LuT7LZaowzAu4KA","client_info":"zEWZ9iDSigJNstacV9EVpNMRw0NM7qT0qIWPoPph5A4GcA7JB3XjQgGzRJLHUvBaStltLEDoJ9P2/kJ88oxmL5oWHHcmkmNwBkapMmS+OfBTJFrdp32meXiUEZ0MJfd7/2fDzoOfX84cL/IHANUfztNurPW/0Z1+UFCvAKUReB8ZvSwk/fhaQN/yGL56JkUq"}`))
    if err != nil {
        t.Errorf("http.NewRequest post error %v", err)
        return
    }
    httpReq.Header.Set("Content-Type", "application/json")
    httpReq.Header.Set("x-signature", "kg/605jbNHxjl+ojOjY9QQ==")
    httpReq.Header.Set("x-nonce-str", "123456")
    httpReq.Header.Set("x-timestamp", "1736217301")

    type args struct {
        in0 context.Context
        w   http.ResponseWriter
        r   *http.Request
    }
    tests := []struct {
        name     string
        args     args
        initFunc func()
    }{
        {name: "RewardsIssue success", args: args{
            in0: context.Background(),
            w:   &EmptyWriteResponse{},
            r:   httpReq,
        },
            initFunc: func() {
                MockCfg.EXPECT().GetGameAppConfByAppid(gomock.Any()).Return(&conf.GameAppConf{
                    AppID:     "tt_zuduixiuxian",
                    AppSecret: "123456zdxxsecret",
                    AwardList: []*conf.AwardInfo{
                        &conf.AwardInfo{
                            ID:         2693,
                            RewardId:   "2693",
                            RewardType: 2,
                            Worth:      worth,
                        },
                    },
                }, true)
                channelExtGameCli.EXPECT().GetUidByOpenid(gomock.Any(), gomock.Any()).Return(&channel_ext_game.GetUidByOpenidResp{Uid: testUid}, nil)

                MockCfg.EXPECT().GetGameAppConfByAppid(gomock.Any()).Return(&conf.GameAppConf{
                    AppID:     "tt_zuduixiuxian",
                    AppSecret: "123456zdxxsecret",
                    AwardList: []*conf.AwardInfo{
                        &conf.AwardInfo{
                            RewardId:   "2693",
                            RewardType: 2,
                            Worth:      worth,
                        },
                    },
                }, true)
                // 非常用设备检查-pass
                usualDeviceCli.EXPECT().CheckUsualDevice(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&usualDeviceSvrPb.CheckUsualDeviceResp{Result: true}, nil)

                // 获取用户所在房间id
                channelGoCli.EXPECT().BatchGetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channel_go.BatchGetChannelSimpleInfoResp{
                    ChannelSimpleList: []*channel_go.ChannelSimpleInfo{
                        {
                            ChannelId:     cid,
                            ChannelViewId: channelViewId,
                        },
                    },
                }, nil)

                // 风控检查
                riskMngCli.EXPECT().CheckHelper(gomock.Any(), gomock.Any(), gomock.Any()).Return(&riskMngApiPb.CheckResp{
                    ErrCode: 0,
                }, nil)

                channelExtGameCli.EXPECT().ExtGameAward(gomock.Any(), gomock.Any()).Return(&channel_ext_game.ExtGameAwardResp{}, nil)
            },
        },

        {name: "RewardsIssue risk hit", args: args{
            in0: context.Background(),
            w:   &EmptyWriteResponse{},
            r:   httpReq,
        },
            initFunc: func() {
                MockCfg.EXPECT().GetGameAppConfByAppid(gomock.Any()).Return(&conf.GameAppConf{
                    AppID:     "tt_zuduixiuxian",
                    AppSecret: "123456zdxxsecret",
                    AwardList: []*conf.AwardInfo{
                        &conf.AwardInfo{
                            ID:         2693,
                            RewardId:   "2693",
                            RewardType: 2,
                            Worth:      worth,
                        },
                    },
                }, true)
                channelExtGameCli.EXPECT().GetUidByOpenid(gomock.Any(), gomock.Any()).Return(&channel_ext_game.GetUidByOpenidResp{Uid: testUid}, nil)

                MockCfg.EXPECT().GetGameAppConfByAppid(gomock.Any()).Return(&conf.GameAppConf{
                    AppID:     "TT_ZDXX",
                    AppSecret: "123456",
                    AwardList: []*conf.AwardInfo{
                        &conf.AwardInfo{
                            ID:         2693,
                            RewardId:   "2693",
                            RewardType: 2,
                            Worth:      worth,
                        },
                    },
                }, true)
                // 非常用设备检查-pass
                usualDeviceCli.EXPECT().CheckUsualDevice(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&usualDeviceSvrPb.CheckUsualDeviceResp{Result: true}, nil)

                // 获取用户所在房间id
                channelGoCli.EXPECT().BatchGetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channel_go.BatchGetChannelSimpleInfoResp{
                    ChannelSimpleList: []*channel_go.ChannelSimpleInfo{
                        {
                            ChannelId:     cid,
                            ChannelViewId: channelViewId,
                        },
                    },
                }, nil)

                // 风控检查
                riskMngCli.EXPECT().CheckHelper(gomock.Any(), gomock.Any(), gomock.Any()).Return(&riskMngApiPb.CheckResp{
                    ErrCode: -177,
                    ErrMsg:  "风控检查不通过",
                }, nil)
                channelExtGameCli.EXPECT().ExtGameAward(gomock.Any(), gomock.Any()).Return(&channel_ext_game.ExtGameAwardResp{}, protocol.NewExactServerError(nil, status.ErrTbeanNoEnoughBalance, "err"))
            },
        },

        {name: "RewardsIssue reward not exist", args: args{
            in0: context.Background(),
            w:   &EmptyWriteResponse{},
            r:   httpReq,
        },
            initFunc: func() {
                MockCfg.EXPECT().GetGameAppConfByAppid(gomock.Any()).Return(&conf.GameAppConf{
                    AppID:     "tt_zuduixiuxian",
                    AppSecret: "123456zdxxsecret",
                }, true)
                channelExtGameCli.EXPECT().GetUidByOpenid(gomock.Any(), gomock.Any()).Return(&channel_ext_game.GetUidByOpenidResp{Uid: testUid}, nil)

                MockCfg.EXPECT().GetGameAppConfByAppid(gomock.Any()).Return(&conf.GameAppConf{
                    AppID:     "tt_zuduixiuxian",
                    AppSecret: "123456zdxxsecret",
                }, true)
            },
        },
    }
    for _, tt := range tests {
        if tt.initFunc != nil {
            tt.initFunc()
        }
        t.Run(tt.name, func(t *testing.T) {
            s.RewardsIssue(tt.args.in0, tt.args.w, tt.args.r)
        })
    }
}

func Test_awardAmountTransform(t *testing.T) {
    type args struct {
        awardType   uint32
        awardAmount uint32
    }
    tests := []struct {
        name string
        args args
        want uint32
    }{
        {
            name: "awardAmountTransform",
            args: args{
                awardType:   3,
                awardAmount: 3540,
            },
            want: 3,
        },
        {
            name: "awardAmountTransform",
            args: args{
                awardType:   1,
                awardAmount: 1,
            },
            want: 1,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            if got := awardAmountTransform(tt.args.awardType, tt.args.awardAmount); got != tt.want {
                t.Errorf("awardAmountTransform() = %v, want %v", got, tt.want)
            }
        })
    }
}
