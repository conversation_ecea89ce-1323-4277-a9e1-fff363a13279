package internal

import (
    "context"
    "crypto/md5"
    "encoding/base64"
    "encoding/json"
    "fmt"
    "github.com/golang/mock/gomock"
    "gitlab.ttyuyin.com/avengers/tyr/core/service/http"
    marketIdMocks "gitlab.ttyuyin.com/bizFund/bizFund/pkg/marketid_helper/mocks"
    mockaccount "golang.52tt.com/clients/mocks/account"
    headImageMock "golang.52tt.com/clients/mocks/headimage"
    mockUkw "golang.52tt.com/clients/mocks/you-know-who"
    channel_ext_game "golang.52tt.com/protocol/services/channel-ext-game"
    "strings"
    "testing"

    "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_go"
    "golang.52tt.com/services/channel-ext-game/channel-ext-game-http-logic/internal/conf"
    "golang.52tt.com/services/channel-ext-game/channel-ext-game-http-logic/internal/conf/mocks"

    mockusualdevice "golang.52tt.com/clients/mocks/usual-device"
    http2 "net/http"
    tbeanMocks "golang.52tt.com/pkg/tbean/mocks"
    "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channelol_go"
    risk_mng_api_mock "golang.52tt.com/clients/mocks/risk-mng-api"
)

type EmptyWriteResponse struct {
}

func (e *EmptyWriteResponse) Header() http2.Header {
    return http2.Header{}
}
func (e *EmptyWriteResponse) WriteHeader(int) {

}
func (e *EmptyWriteResponse) Write(x []byte) (int, error) {
    return len(x), nil
}

var (
    channelExtGameCli *channel_ext_game.MockChannelExtGameClient
    UkwCli            *mockUkw.MockIClient
    AccountCli        *mockaccount.MockIClient
    MockCfg           *mocks.MockIBusinessConfManager
    HeadImageCli      *headImageMock.MockIClient
    usualDeviceCli    *mockusualdevice.MockIClient
    riskMngCli        *risk_mng_api_mock.MockIClient
    channelGoCli      *channel_go.MockChannelGoClient
    channelolCli      *channelol_go.MockChannelolGoClient

    gameConf = &conf.GameAppConf{
        AppID:     "1",
        AppSecret: "1",
    }
    tbeanCli *tbeanMocks.MockClient
)

func newMockSvr(t *testing.T) *Server {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()
    marketIdMocks.RegisterDefaultMockConfig(ctrl)

    AccountCli = mockaccount.NewMockIClient(ctrl)
    channelExtGameCli = channel_ext_game.NewMockChannelExtGameClient(ctrl)
    UkwCli = mockUkw.NewMockIClient(ctrl)
    MockCfg = mocks.NewMockIBusinessConfManager(ctrl)
    HeadImageCli = headImageMock.NewMockIClient(ctrl)

    usualDeviceCli = mockusualdevice.NewMockIClient(ctrl)
    riskMngCli = risk_mng_api_mock.NewMockIClient(ctrl)
    channelGoCli = channel_go.NewMockChannelGoClient(ctrl)
    tbeanCli = tbeanMocks.NewMockClient(ctrl)
    channelolCli = channelol_go.NewMockChannelolGoClient(ctrl)

    return &Server{
        ChannelExtGameCli: channelExtGameCli,
        bc:                MockCfg,
        AccountCli:        AccountCli,
        YKWCli:            UkwCli,
        UsualDeviceCli:    usualDeviceCli,
        riskMngApiCli:     riskMngCli,
        ChannelGoCli:      channelGoCli,
        HeadImageCli:      HeadImageCli,
        TbeanCli:          tbeanCli,
        channelOlCli:      channelolCli,
    }
}

func TestServer_Echo(t *testing.T) {
    s := newMockSvr(t)

    httpReq, err := http2.NewRequest("POST", "http://127.0.0.1/test", strings.NewReader("{}"))
    if err != nil {
        t.Errorf("http.NewRequest post error %v", err)
        return
    }
    httpReq.Header.Set("Content-Type", "application/json")

    type args struct {
        in0 context.Context
        w   http.ResponseWriter
        r   *http.Request
    }
    tests := []struct {
        name string
        args args
    }{
        {name: "Echo", args: args{
            in0: context.Background(),
            w:   &EmptyWriteResponse{},
            r:   httpReq,
        }},
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            s.Echo(tt.args.in0, tt.args.w, tt.args.r)
        })
    }
}

func TestServer_GetSession(t *testing.T) {
    s := newMockSvr(t)

    httpReq, err := http2.NewRequest("POST", "http://127.0.0.1/test", strings.NewReader(`{"appid":"1","app_secret":"1","open_id":"1","js_code":"1","room_id":1}`))
    if err != nil {
        t.Errorf("http.NewRequest post error %v", err)
        return
    }
    httpReq.Header.Set("Content-Type", "application/json")

    type args struct {
        in0 context.Context
        w   http.ResponseWriter
        r   *http.Request
    }
    tests := []struct {
        name string
        args args
    }{
        {name: "GetSession", args: args{
            in0: context.Background(),
            w:   &EmptyWriteResponse{},
            r:   httpReq,
        }},
    }
    for _, tt := range tests {
        gomock.InOrder(
            MockCfg.EXPECT().GetGameAppConfByAppid(gomock.Any()).Return(gameConf, true),
            channelExtGameCli.EXPECT().GetAuthInfoByJsCode(gomock.Any(), gomock.Any()).Return(&channel_ext_game.GetAuthInfoByJsCodeResp{
                Openid:           "1",
                Uid:              "1",
                Appid:            "1",
                ChannelDisplayId: 1,
            }, nil),
            MockCfg.EXPECT().GetSessionKeepDuration().Return(uint32(100)),
            channelExtGameCli.EXPECT().CancelUserExtGameJsCode(gomock.Any(), gomock.Any()).Return(&channel_ext_game.CancelUserExtGameJsCodeResp{}, nil),
        )

        t.Run(tt.name, func(t *testing.T) {
            s.GetSession(tt.args.in0, tt.args.w, tt.args.r)
        })
    }
}

func Test_getSignature(t *testing.T) {

    headerMap := make(map[string]string)
    headerMap["x-nonce-str"] = "123456"
    headerMap["x-timestamp"] = "1736217301"

    data := "{\"appid\":\"tt_zuduixiuxian\",\"open_id\":\"4640a2042f4e3edc442cbad86e042\",\"amount\":10,\"order_id\":\"safafsdfdfgww15sfsg\",\"session\":\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJhcHBpZCI6InR0X3p1ZHVpeGl1eGlhbiIsImNsaWVudF90eXBlIjowLCJleHAiOjE3NDM0OTg5NTcsImlhdCI6MTc0MzQ5MTc1NywiaXNzIjoiNTJ0dC5jb20iLCJtYXJrZXRfaWQiOjAsIm9wZW5faWQiOiI0NjQwYTIwNDJmNGUzZWRjNDQyY2JhZDg2ZTA0MiIsInJvb21faWQiOiIyMTgwNjE4IiwidHlwIjoxLCJ0eXBlIjoiSldUIn0.uqveV8fro0uXZQTZvMwIWevygkgSyMOgAXgKX37wn8usS2bbHP9xPwmoXZI2R-C-J0SVEX7LuT7LZaowzAu4KA\",\"face_auth_result_token\":\"\",\"client_info\":\"zEWZ9iDSigJNstacV9EVpNMRw0NM7qT0qIWPoPph5A4GcA7JB3XjQgGzRJLHUvBaStltLEDoJ9P2/kJ88oxmL5oWHHcmkmNwBkapMmS+OfBTJFrdp32meXiUEZ0MJfd7/2fDzoOfX84cL/IHANUfztNurPW/0Z1+UFCvAKUReB8ZvSwk/fhaQN/yGL56JkUq\"}"
    sign := signature(headerMap, data, "123456zdxxsecret")
    t.Log(sign) // AjB3In2ZlUxvm7pHPYzPWA==

    data = "{\"appid\":\"tt_zuduixiuxian\",\"open_id\":\"80068822130deaea4ff1ad8cb2867\",\"amount\":10,\"order_id\":\"safafsssdss11fdf111fsg\",\"session\":\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJhcHBpZCI6InR0X3p1ZHVpeGl1eGlhbiIsImNsaWVudF90eXBlIjowLCJleHAiOjE3NDQwMzA1NDEsImlhdCI6MTc0NDAyMzM0MSwiaXNzIjoiNTJ0dC5jb20iLCJtYXJrZXRfaWQiOjAsIm9wZW5faWQiOiI4MDA2ODgyMjEzMGRlYWVhNGZmMWFkOGNiMjg2NyIsInJvb21faWQiOiIiLCJ0eXAiOjEsInR5cGUiOiJKV1QifQ.8TxMs9VugxZ96SIr9Bn2h-SPoq42HnzIvOctnW_DdtdJ6XOoTloMDz2MxWMqwOBaZl-U-PH3z20RsoYtlMJsbQ\",\"face_auth_result_token\":\"\",\"client_info\":\"zEWZ9iDSigJNstacV9EVpNMRw0NM7qT0qIWPoPph5A4GcA7JB3XjQgGzRJLHUvBaStltLEDoJ9P2/kJ88oxmL5oWHHcmkmNwBkapMmS+OfBTJFrdp32meXiUEZ0MJfd7IlYeHbPFXlcE3vEHm37RM/OWNZ7Fc38l93LnCgFlCFk6xMG5GK6Afp3s7UEBXvmP\",\"risk_token\":\"jXw2HmrBphxDBAih8COkb3Gs8pyXyzBnRkOSJ3ZLR6N3TKPWUzSKChStq1ZcDfbBNCCaAtJ6OkSXIIjRE1qpJcpudGIxOE1RODTWkhuOqGtKc5g5jiXOzvALW5qZjWeGZ6i4rOcheORHwiwhuP6IWm9rs+9L9lpDAdHv3w3MF+pvRmgXLYSXpEItTDLjWuee\"}"
    t.Log(signature(headerMap, data, "123456zdxxsecret"))
}

func Test_GrantToken(t *testing.T) {
    s := newMockSvr(t)
    t.Log(s.GrantToken("1", "1", "1", 100, 1, 1))
}

func Test_ClientInfoSignV2(t *testing.T) {
    // 用户信息
    deviceId := "001dbfd74933034f81397e0871d4a59f"
    clientType := 2
    marketId := 0
    appVersion := 104792069
    //uid := 2466008

    uid := 2662859
    //拼接字符串
    signStr := fmt.Sprintf("%s%d%d%d%d", deviceId, clientType, marketId, appVersion, uid)

    //signStr := "22CCD03D-97FF-4D24-98CE-D50FE6A5E7F8&1&0&104660992&2512094"

    // 计算MD5
    hash := md5.Sum([]byte(signStr))
    md5Str := fmt.Sprintf("%x", hash)

    // 查看hash对应的是什么字符编码

    // Base64编码
    base64Str := base64.StdEncoding.EncodeToString(hash[:])

    // 输出结果
    fmt.Println("签名字符串:", signStr)
    fmt.Println("MD5哈希:", md5Str)
    fmt.Println("Base64编码后的签名:", base64Str)
}

func Test_ClientInfoSignV4(t *testing.T) {
    sign2 := "cK1HxFfxqmT1pQKaleIMmjdoy14GFcgBuJH+BLlu3MR0pVnttrw5GvqwMFKBO3RlBf92XAFxpnVInuPdv4LGI5pMkYIVO9j/nrzPxG/kAwbuDbdbef0Sv8aue5ISioS+K/B24KxlGNZZfSqH9/LukCGwKhqLUmbnqJELzQxBL6sIL8mnlgF4FvOMcbbY5/JW"
    //sign2 = "yBi0s00YQaAkb9RFZ5x53EfolbwhUH/fd5nyjKMVFhEdvZdOGZvbCWcwLOAP6QtVBbi3bi4W19qyLbiiERbMOwpM3O6RPM+vF9IC+kw5Tu8yOeyxPaSnnLyw1Jjxa7psQ57Hl+/pqhrsnjicXOqDoSL4+SxZ/JlDwahTxetGWM3dDyygtYFnGSvl9T7AhWdg"
    sign2 = "jXw2HmrBphxDBAih8COkb3Gs8pyXyzBnRkOSJ3ZLR6N3TKPWUzSKChStq1ZcDfbBNCCaAtJ6OkSXIIjRE1qpJcpudGIxOE1RODTWkhuOqGtKc5g5jiXOzvALW5qZjWeGZ6i4rOcheORHwiwhuP6IWm9rs+9L9lpDAdHv3w3MF+pvRmgXLYSXpEItTDLjWuee"
    cipherText, err := base64.StdEncoding.DecodeString(sign2)
    if err != nil {
        t.Log(err)
        return
    }

    str := AESDecrypt(cipherText, []byte("e1WbX55dmzJsVzgW"))
    t.Log(string(str))

    var clientInfo2 ClientInfo

    err = json.Unmarshal(str, &clientInfo2)
    if err != nil {
        t.Log(err)
    }

    _ = clientInfo2.clientInfoSignCheck(context.Background(), 2466008)
}

type JsonData struct {
    BizScene            string `json:"biz_scene"`
    FaceAuthRequireText string `json:"face_auth_require_text"`
    FaceAuthSuccessText string `json:"face_auth_success_text"`
    FaceReportScene     string `json:"face_report_scene"`
    ClientType          int    `json:"client_type"`
    ChannelId           int    `json:"channel_id"`
    ProcessingText      string `json:"processing_text"`
    FailureText         string `json:"failure_text"`
    RequestId           string `json:"request_id"`
}

func TestJsonUnmarshal(t *testing.T) {
    //jsonData := "{\"biz_scene\":\"1-2\",\"face_auth_require_text\":\"为了保障您的财产安全，请先进行人脸识别验证，验证通过后即可进行\<span style='color:red'\>消费\</span\>\",\"face_auth_success_text\":\"人脸验证成功，成功解锁本次\<span style='color:red'\>消费\</span\>\\n是否继续操作\",\"face_report_scene\":\"4\",\"client_type\":1}"

    //jsonData := "{\"biz_scene\":\"1-2\",\"face_auth_require_text\":\"为了保障您的财产安全，请先进行人脸识别验证，验证通过后即可进行&gtspan style='color:red'&lt消费&gt/span&lt\",\"face_auth_success_text\":\"人脸验证成功，成功解锁本次&gtspan style='color:red'&lt消费&gt/span&lt\\n是否继续操作\",\"uid\":2466008,\"face_report_scene\":\"4\",\"client_type\":5}"

    //jsonData := "{\"biz_scene\":\"1-2\",\"face_auth_require_text\":\"为了保障您的财产安全，请先进行人脸识别验证，验证通过后即可进行\<span style='color:red'\>消费\</span\>\",\"face_auth_success_text\":\"人脸验证成功，成功解锁本次\<span style='color:red'\>消费\</span\>\\n是否继续操作\",\"face_report_scene\":\"4\",\"client_type\":1}"
    //var data JsonData
    //err := json.Unmarshal([]byte(jsonData), &data)
    //if err != nil {
    //    t.Log(err)
    //}

    //baseJsonData := "eyJiaXpfc2NlbmUiOiIxLTIiLCJmYWNlX2F1dGhfcmVxdWlyZV90ZXh0Ijoi5Li65LqG5L+d6Zqc5oKo55qE6LSi5Lqn5a6J5YWo77yM6K+35YWI6L+b6KGM5Lq66IS46K+G5Yir6aqM6K+B77yM6aqM6K+B6YCa6L+H5ZCO5Y2z5Y+v6L+b6KGMXHUwMDNjc3BhbiBzdHlsZT0nY29sb3I6cmVkJ1x1MDAzZea2iOi0uVx1MDAzYy9zcGFuXHUwMDNlIiwiZmFjZV9hdXRoX3N1Y2Nlc3NfdGV4dCI6IuS6uuiEuOmqjOivgeaIkOWKn++8jOaIkOWKn+ino+mUgeacrOasoVx1MDAzY3NwYW4gc3R5bGU9J2NvbG9yOnJlZCdcdTAwM2XmtojotLlcdTAwM2Mvc3Bhblx1MDAzZVxu5piv5ZCm57un57ut5pON5L2cIiwiZmFjZV9yZXBvcnRfc2NlbmUiOiI0IiwiY2xpZW50X3R5cGUiOjF9"
    //// base64解码
    //decodedData, err := base64.StdEncoding.DecodeString(baseJsonData)
    //if err != nil {
    //    t.Log(err)
    //    return
    //}
    //
    ////t.Log(string(decodedData))
    //// json 反序列化
    //var data JsonData
    //err = json.Unmarshal(decodedData, &data)
    //if err != nil {
    //    t.Log(err)
    //}
    //
    //t.Logf("%+v", data)
}
