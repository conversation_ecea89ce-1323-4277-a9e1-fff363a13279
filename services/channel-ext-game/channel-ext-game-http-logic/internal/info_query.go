package internal

import (
    "context"
    "fmt"
    "gitlab.ttyuyin.com/avengers/tyr/core/service/http"
    "gitlab.ttyuyin.com/bizFund/bizFund/pkg/avatar"
    "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_go"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    channelExtGame "golang.52tt.com/protocol/services/channel-ext-game"
    channel_ext_game "golang.52tt.com/services/channel-ext-game/channel-ext-game-http-logic/internal/model"
)

func (s *Server) BatchGetRoomInfo(ctx context.Context, w http.ResponseWriter, r *http.Request) {
    out := &channel_ext_game.BatchGetRoomInfoResp{}
    req := &channel_ext_game.BatchGetRoomInfoReq{}
    err := http.RepeatableReadJSON(r, &req)
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetRoomInfo fail to RepeatableReadJSON. err:%v", err)
        ServeAPIJsonWithError(w, ErrBadRequestParams, BadRequestDesc)
        return
    }
    defer func() {
        log.DebugWithCtx(ctx, "BatchGetRoomInfo req:%+v, resp:%+v", req, out)
    }()

    if len(req.GetRoomIdList()) == 0 {
        log.ErrorWithCtx(ctx, "BatchGetRoomInfo empty room id list, req:%+v", req)
        ServeAPIJsonWithError(w, ErrBadRequestParams, BadRequestDesc)
        return
    }

    infoResp, err := s.ChannelGoCli.BatchGetChannelSimpleInfo(ctx, &channel_go.BatchGetChannelSimpleInfoReq{
        OpUid:             0,
        ChannelIdList:     nil,
        ChannelViewIdList: req.GetRoomIdList(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetRoomInfo fail to BatchGetChannelSimpleInfoByViewId. req:%+v, err:%v", req, err)
        if e := protocol.ToServerError(err); e != nil {
            ServeAPIJsonWithError(w, e.Code(), e.Message())
            return
        }
        ServeAPIJsonWithError(w, ErrSystemInternal, InternalErrDesc)
        return
    }

    viewIdChannelInfoMap := make(map[string]*channel_go.ChannelSimpleInfo)
    for _, info := range infoResp.GetChannelSimpleList() {
        viewIdChannelInfoMap[info.GetChannelViewId()] = info
    }

    out.RoomInfos = make(map[string]*channel_ext_game.RoomInfo)
    for _, roomId := range req.GetRoomIdList() {
        if info, ok := viewIdChannelInfoMap[roomId]; ok {

            var roomAvatar string
            if info.GetIconMd5() != "" {
                roomAvatar = s.getChannelIcon(info.GetChannelId(), info.GetIconMd5())
            } else {
                var clientType, marketId uint32
                cliams, _, _ := parseSession(req.GetSession())
                if cliams == nil {
                    log.ErrorWithCtx(ctx, "BatchGetRoomInfo fail to parseSession. req:%+v", req)
                } else {
                    clientType = cliams.ClientType
                    marketId = cliams.MarketId
                }

                userInfo, _ := s.AccountCli.GetUser(ctx, info.GetCreaterUid())
                if userInfo != nil {
                    headImageInfo, _ := s.HeadImageCli.GetHeadImageMd5(ctx, info.GetCreaterUid(), userInfo.GetUsername())
                    if headImageInfo != "" {
                        roomAvatar = avatar.GetAvatarUrl(ctx, userInfo.GetUsername(), headImageInfo, marketId, clientType)
                        log.DebugWithCtx(ctx, "BatchGetRoomInfo channelIcon empty, channelId:%d, channelViewId:%s, avatar:%s",
                            info.GetChannelId(), info.GetChannelViewId(), roomAvatar)
                    }
                }
            }

            out.RoomInfos[roomId] = &channel_ext_game.RoomInfo{
                RoomName: info.GetName(),
                Avatar:   roomAvatar,
            }
        }
    }
    ServePB2JSON(w, out)
}

func (s *Server) BatchGetUserInfo(ctx context.Context, w http.ResponseWriter, r *http.Request) {
    out := &channel_ext_game.BatchGetUserInfoResp{}
    req := &channel_ext_game.BatchGetUserInfoReq{}
    err := http.RepeatableReadJSON(r, &req)
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetUserInfo fail to RepeatableReadJSON. err:%v", err)
        ServeAPIJsonWithError(w, ErrBadRequestParams, BadRequestDesc)
        return
    }
    defer func() {
        log.DebugWithCtx(ctx, "BatchGetUserInfo req:%+v, resp:%+v", req, out)
    }()

    if len(req.GetOpenIdList()) == 0 {
        log.ErrorWithCtx(ctx, "BatchGetUserInfo empty openId list, req:%+v", req)
        ServeAPIJsonWithError(w, ErrBadRequestParams, BadRequestDesc)
        return
    }
    openIdSet := make(map[string]struct{})
    for _, openId := range req.GetOpenIdList() {
        openIdSet[openId] = struct{}{}
    }
    openIdSet[req.GetOpenId()] = struct{}{}
    openIdList := make([]string, 0, len(openIdSet))
    for openId, _ := range openIdSet {
        openIdList = append(openIdList, openId)
    }
    infoResp, err := s.ChannelExtGameCli.BatchGetUidByOpenIds(ctx, &channelExtGame.BatchGetUidByOpenIdsReq{
        OpenIdList: openIdList,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetUserInfo fail to BatchGetUserInfo. req:%+v, err:%v", req, err)
        if e := protocol.ToServerError(err); e != nil {
            ServeAPIJsonWithError(w, e.Code(), e.Message())
            return
        }
        ServeAPIJsonWithError(w, ErrSystemInternal, InternalErrDesc)
        return
    }

    if len(infoResp.GetUidMap()) == 0 {
        log.ErrorWithCtx(ctx, "BatchGetUserInfo empty uid list, req:%+v", req)
        ServeAPIJsonWithError(w, ErrBadRequestParams, BadRequestDesc)
        return
    }

    var needQueryRoomIdList bool
    for _, openId := range req.GetOpenIdList() {
        if openId == req.GetOpenId() {
            needQueryRoomIdList = true
            break
        }
    }

    roomIdList := make([]string, 0)
    if needQueryRoomIdList {
        channelListResp, err := s.ChannelGoCli.BatchGetUserRoleChannelList(ctx, &channel_go.BatchGetUserRoleChannelListReq{
            UidList: []uint32{infoResp.GetUidMap()[req.GetOpenId()]},
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "BatchGetUserInfo fail to GetUserChannelRoleList. req:%+v, err:%v", req, err)
            if e := protocol.ToServerError(err); e != nil {
                ServeAPIJsonWithError(w, e.Code(), e.Message())
                return
            }
            ServeAPIJsonWithError(w, ErrSystemInternal, InternalErrDesc)
            return
        }
        if len(channelListResp.GetUserRoleChannelList()) != 0 {
            for _, role := range channelListResp.GetUserRoleChannelList() {
                if role.GetUid() == infoResp.GetUidMap()[req.GetOpenId()] {
                    roomIdList = append(roomIdList, role.GetChannelSimple().GetChannelViewId())
                }
            }
        }
    }

    uidList := make([]uint32, 0, len(infoResp.GetUidMap()))
    for _, info := range infoResp.GetUidMap() {
        uidList = append(uidList, info)
    }

    infoMap, err := s.AccountCli.BatGetUserByUid(ctx, uidList...)
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetUserInfo fail to BatGetUserByUid. req:%+v, err:%v", req, err)
        if e := protocol.ToServerError(err); e != nil {
            ServeAPIJsonWithError(w, e.Code(), e.Message())
            return
        }
        ServeAPIJsonWithError(w, ErrSystemInternal, InternalErrDesc)
        return
    }

    accountList := make([]string, 0, len(infoMap))
    for _, info := range infoMap {
        accountList = append(accountList, info.GetUsername())
    }
    imageMap, err := s.HeadImageCli.BatchGetHeadImageMd5(ctx, infoResp.GetUidMap()[req.GetOpenId()], accountList)
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetUserInfo fail to BatchGetHeadImageMd5. req:%+v, err:%v", req, err)
        if e := protocol.ToServerError(err); e != nil {
            ServeAPIJsonWithError(w, e.Code(), e.Message())
            return
        }
        ServeAPIJsonWithError(w, ErrSystemInternal, InternalErrDesc)
        return
    }

    var clientType, marketId uint32
    cliams, _, _ := parseSession(req.GetSession())
    if cliams == nil {
        log.ErrorWithCtx(ctx, "BatchGetUserInfo fail to parseSession. req:%+v", req)
    } else {
        clientType = cliams.ClientType
        marketId = cliams.MarketId
    }

    out.UserInfos = make(map[string]*channel_ext_game.UserInfo)
    for _, openId := range req.GetOpenIdList() {
        log.DebugWithCtx(ctx, "BatchGetUserInfo openId:%s, uid:%d, marketId:%d, clientType:%d", openId, infoResp.GetUidMap()[openId], marketId, clientType)
        if uid, ok := infoResp.GetUidMap()[openId]; ok {
            if info, ok := infoMap[uid]; ok {
                out.UserInfos[openId] = &channel_ext_game.UserInfo{
                    Nickname:        info.GetNickname(),
                    Avatar:          avatar.GetAvatarUrl(ctx, info.GetUsername(), imageMap[info.GetUsername()], marketId, clientType),
                    Gender:          transSex(uint32(info.GetSex())),
                    PossessRoomList: roomIdList,
                }
            }
        }
    }
    ServePB2JSON(w, out)
}

// account服务0为女，1为男，转换为外部1为女，2为男
func transSex(sex uint32) (outerSex uint32) {
    if sex == 1 {
        outerSex = 2
    } else {
        outerSex = 1
    }
    return
}

func (s *Server) getChannelIcon(channelId uint32, iconVersion string) string {
    if iconVersion == "" {
        return ""
    }
    return fmt.Sprintf(s.bc.GetChannelIconUrlFormat(), channelId, iconVersion)
}
