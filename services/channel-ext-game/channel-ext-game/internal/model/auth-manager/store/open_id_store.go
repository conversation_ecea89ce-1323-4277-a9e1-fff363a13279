package store

import (
    "context"
    "database/sql"
    "errors"
    "fmt"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/protocol/common/status"
    "google.golang.org/grpc/codes"
    "strings"
)

const (
    openIdTbl       = "ext_game_open_id"
    createOpenIdTbl = `
CREATE TABLE IF NOT EXISTS ext_game_open_id (
  	id int UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT "自增id",
	uid int UNSIGNED NOT NULL COMMENT "用户id",
	open_id varchar(128) NOT NULL COMMENT "open_id",
	create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间",
	update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT "更新时间",
	is_delete tinyint(1) NOT NULL DEFAULT 0 COMMENT "是否删除",
	UNIQUE KEY idx_uid (uid),
	UNIQUE KEY idx_open_id (open_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT="游戏扩展open_id表";
	
`
)

func (s *Store) createOpenIdTbl() error {
    _, err := s.db.Exec(createOpenIdTbl)
    if err != nil {
        log.Errorf("createOpenIdTbl fail err %v", err)
        return err
    }
    return err
}

func (s *Store) InsertOpenId(ctx context.Context, uid uint32, openId string) error {
    _, err := s.db.ExecContext(ctx, fmt.Sprintf("INSERT INTO %s (uid, open_id) VALUES (?, ?)", openIdTbl), uid, openId)
    if err != nil {
        log.ErrorWithCtx(ctx, "InsertOpenId fail err %v, uid %d, openId %s", err, uid, openId)
        return err
    }
    log.InfoWithCtx(ctx, "InsertOpenId success uid %d, openId %s", uid, openId)
    return nil
}

func (s *Store) GetOpenIdByUid(ctx context.Context, uid uint32) (string, error) {
    var openId string
    err := s.readOnlyDb.GetContext(ctx, &openId, fmt.Sprintf("SELECT open_id FROM %s WHERE uid = ?", openIdTbl), uid)
    if err != nil {
        if errors.Is(err, sql.ErrNoRows) {
            log.WarnWithCtx(ctx, "GetOpenIdByUid not found uid %d", uid)
            return "", nil
        }
        log.ErrorWithCtx(ctx, "GetOpenIdByUid fail err %v, uid %d", err, uid)
        return "", err
    }
    log.DebugWithCtx(ctx, "GetOpenIdByUid success uid %d, openId %s", uid, openId)
    return openId, nil
}

func (s *Store) GetUidByOpenId(ctx context.Context, openId string) (uint32, error) {
    var uid uint32
    err := s.readOnlyDb.GetContext(ctx, &uid, fmt.Sprintf("SELECT uid FROM %s WHERE open_id = ?", openIdTbl), openId)
    if err != nil {
        if errors.Is(err, sql.ErrNoRows) {
            log.WarnWithCtx(ctx, "GetUidByOpenId not found openId %s", openId)
            return 0, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
        }
        log.ErrorWithCtx(ctx, "GetUidByOpenId fail err %v, openId %s", err, openId)
        return 0, err
    }
    log.DebugWithCtx(ctx, "GetUidByOpenId success openId %s, uid %d", openId, uid)
    return uid, nil
}

func (s *Store) BatchGetUidByOpenIds(ctx context.Context, openIds []string) (map[string]uint32, error) {
    uidMap := make(map[string]uint32, len(openIds))
    
    // 手动构建 IN 子句中的占位符
    placeholders := make([]string, len(openIds))
    args := make([]interface{}, len(openIds))
    for i, openId := range openIds {
        placeholders[i] = "?"
        args[i] = openId
    }
    inClause := strings.Join(placeholders, ", ")
    sqlStr := fmt.Sprintf("SELECT uid, open_id FROM %s WHERE open_id IN (%s)", openIdTbl, inClause)
    rows, err := s.readOnlyDb.QueryxContext(ctx, sqlStr, args...)
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetUidByOpenIds fail to execute query err %v", err)
        return nil, err
    }
    
    defer rows.Close()
    for rows.Next() {
        var uid uint32
        var openId string
        err := rows.Scan(&uid, &openId)
        if err != nil {
            log.ErrorWithCtx(ctx, "BatchGetUidByOpenIds fail to scan err %v", err)
            return nil, err
        }
        uidMap[openId] = uid
    }
    log.DebugWithCtx(ctx, "BatchGetUidByOpenIds success openIds %v, uidMap %v", openIds, uidMap)
    return uidMap, nil
}
