package store

import (
    "fmt"
    "testing"
)

func TestStore_GetOpenIdByUid(t *testing.T) {
    openId, err := testStore.GetOpenIdByUid(ctx, testUid)
    fmt.Println(openId, err)
}

func TestStore_InsertOpenId(t *testing.T) {
    t.Skip()
    err := testStore.InsertOpenId(ctx, testUid, "abc")
    fmt.Println(err)
}

func TestStore_GetUidByOpenId(t *testing.T) {
    uid, err := testStore.GetUidByOpenId(ctx, "abc")
    fmt.Println(uid, err)
    uid, err = testStore.GetUidByOpenId(ctx, "abc1")
    fmt.Println(uid, err)
}

func TestStore_BatchGetUidByOpenIds(t *testing.T) {
    uidMap, err := testStore.BatchGetUidByOpenIds(ctx, []string{"abc", "abc1"})
    fmt.Println(uidMap, err)
}
