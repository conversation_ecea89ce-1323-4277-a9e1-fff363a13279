package auth_manager

import(
	context "context"
	channel_ext_game "golang.52tt.com/protocol/services/channel-ext-game"
	cache "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/auth-manager/cache"
	context0 "golang.org/x/net/context"
)

type ISessionManager interface {
	BatchGetUidByOpenIds(ctx context.Context, openIds []string) (map[string]uint32,error)
	CancelJsCode(ctx context.Context, jsCode string) error
	CreateOpenIdIfNotExists(ctx context.Context, uid uint32) (string,error)
	GenJsCode(ctx context.Context, req *channel_ext_game.GetUserExtGameJsCodeReq) (string,string,error)
	GetAuthInfoByJsCode(ctx context.Context, jsCode string) (*cache.JsCodeInfo,error)
	GetUidByOpenId(ctx context.Context, openId string) (uint32,error)
	SimpleQueryOpenid(c context0.Context, uid uint32, ttid string) (string,error)
}

