// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/auth-manager/cache (interfaces: ICache)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	redis "github.com/go-redis/redis/v8"
	gomock "github.com/golang/mock/gomock"
	cache "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/auth-manager/cache"
)

// MockICache is a mock of ICache interface.
type MockICache struct {
	ctrl     *gomock.Controller
	recorder *MockICacheMockRecorder
}

// MockICacheMockRecorder is the mock recorder for MockICache.
type MockICacheMockRecorder struct {
	mock *MockICache
}

// NewMockICache creates a new mock instance.
func NewMockICache(ctrl *gomock.Controller) *MockICache {
	mock := &MockICache{ctrl: ctrl}
	mock.recorder = &MockICacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICache) EXPECT() *MockICacheMockRecorder {
	return m.recorder
}

// BindJsCodeInfo mocks base method.
func (m *MockICache) BindJsCodeInfo(arg0 context.Context, arg1 *cache.JsCodeInfo, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BindJsCodeInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// BindJsCodeInfo indicates an expected call of BindJsCodeInfo.
func (mr *MockICacheMockRecorder) BindJsCodeInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BindJsCodeInfo", reflect.TypeOf((*MockICache)(nil).BindJsCodeInfo), arg0, arg1, arg2)
}

// CancelJsCode mocks base method.
func (m *MockICache) CancelJsCode(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelJsCode", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CancelJsCode indicates an expected call of CancelJsCode.
func (mr *MockICacheMockRecorder) CancelJsCode(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelJsCode", reflect.TypeOf((*MockICache)(nil).CancelJsCode), arg0, arg1)
}

// Close mocks base method.
func (m *MockICache) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockICacheMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockICache)(nil).Close))
}

// GetInfoByJsCode mocks base method.
func (m *MockICache) GetInfoByJsCode(arg0 context.Context, arg1 string) (*cache.JsCodeInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInfoByJsCode", arg0, arg1)
	ret0, _ := ret[0].(*cache.JsCodeInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInfoByJsCode indicates an expected call of GetInfoByJsCode.
func (mr *MockICacheMockRecorder) GetInfoByJsCode(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInfoByJsCode", reflect.TypeOf((*MockICache)(nil).GetInfoByJsCode), arg0, arg1)
}

// GetRedisClient mocks base method.
func (m *MockICache) GetRedisClient() redis.Cmdable {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedisClient")
	ret0, _ := ret[0].(redis.Cmdable)
	return ret0
}

// GetRedisClient indicates an expected call of GetRedisClient.
func (mr *MockICacheMockRecorder) GetRedisClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedisClient", reflect.TypeOf((*MockICache)(nil).GetRedisClient))
}
