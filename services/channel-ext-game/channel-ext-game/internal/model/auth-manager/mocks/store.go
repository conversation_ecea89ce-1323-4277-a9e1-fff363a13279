// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/auth-manager/store (interfaces: IStore)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIStore is a mock of IStore interface.
type MockIStore struct {
	ctrl     *gomock.Controller
	recorder *MockIStoreMockRecorder
}

// MockIStoreMockRecorder is the mock recorder for MockIStore.
type MockIStoreMockRecorder struct {
	mock *MockIStore
}

// NewMockIStore creates a new mock instance.
func NewMockIStore(ctrl *gomock.Controller) *MockIStore {
	mock := &MockIStore{ctrl: ctrl}
	mock.recorder = &MockIStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIStore) EXPECT() *MockIStoreMockRecorder {
	return m.recorder
}

// BatchGetUidByOpenIds mocks base method.
func (m *MockIStore) BatchGetUidByOpenIds(arg0 context.Context, arg1 []string) (map[string]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUidByOpenIds", arg0, arg1)
	ret0, _ := ret[0].(map[string]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUidByOpenIds indicates an expected call of BatchGetUidByOpenIds.
func (mr *MockIStoreMockRecorder) BatchGetUidByOpenIds(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUidByOpenIds", reflect.TypeOf((*MockIStore)(nil).BatchGetUidByOpenIds), arg0, arg1)
}

// Close mocks base method.
func (m *MockIStore) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockIStoreMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIStore)(nil).Close))
}

// GetOpenIdByUid mocks base method.
func (m *MockIStore) GetOpenIdByUid(arg0 context.Context, arg1 uint32) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOpenIdByUid", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOpenIdByUid indicates an expected call of GetOpenIdByUid.
func (mr *MockIStoreMockRecorder) GetOpenIdByUid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOpenIdByUid", reflect.TypeOf((*MockIStore)(nil).GetOpenIdByUid), arg0, arg1)
}

// GetUidByOpenId mocks base method.
func (m *MockIStore) GetUidByOpenId(arg0 context.Context, arg1 string) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUidByOpenId", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUidByOpenId indicates an expected call of GetUidByOpenId.
func (mr *MockIStoreMockRecorder) GetUidByOpenId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUidByOpenId", reflect.TypeOf((*MockIStore)(nil).GetUidByOpenId), arg0, arg1)
}

// InsertOpenId mocks base method.
func (m *MockIStore) InsertOpenId(arg0 context.Context, arg1 uint32, arg2 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertOpenId", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertOpenId indicates an expected call of InsertOpenId.
func (mr *MockIStoreMockRecorder) InsertOpenId(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertOpenId", reflect.TypeOf((*MockIStore)(nil).InsertOpenId), arg0, arg1, arg2)
}
