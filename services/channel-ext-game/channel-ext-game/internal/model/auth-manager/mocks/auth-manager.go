// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/auth-manager (interfaces: ISessionManager)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	channel_ext_game "golang.52tt.com/protocol/services/channel-ext-game"
	cache "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/auth-manager/cache"
)

// MockISessionManager is a mock of ISessionManager interface.
type MockISessionManager struct {
	ctrl     *gomock.Controller
	recorder *MockISessionManagerMockRecorder
}

// MockISessionManagerMockRecorder is the mock recorder for MockISessionManager.
type MockISessionManagerMockRecorder struct {
	mock *MockISessionManager
}

// NewMockISessionManager creates a new mock instance.
func NewMockISessionManager(ctrl *gomock.Controller) *MockISessionManager {
	mock := &MockISessionManager{ctrl: ctrl}
	mock.recorder = &MockISessionManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockISessionManager) EXPECT() *MockISessionManagerMockRecorder {
	return m.recorder
}

// BatchGetUidByOpenIds mocks base method.
func (m *MockISessionManager) BatchGetUidByOpenIds(arg0 context.Context, arg1 []string) (map[string]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUidByOpenIds", arg0, arg1)
	ret0, _ := ret[0].(map[string]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUidByOpenIds indicates an expected call of BatchGetUidByOpenIds.
func (mr *MockISessionManagerMockRecorder) BatchGetUidByOpenIds(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUidByOpenIds", reflect.TypeOf((*MockISessionManager)(nil).BatchGetUidByOpenIds), arg0, arg1)
}

// CancelJsCode mocks base method.
func (m *MockISessionManager) CancelJsCode(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelJsCode", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CancelJsCode indicates an expected call of CancelJsCode.
func (mr *MockISessionManagerMockRecorder) CancelJsCode(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelJsCode", reflect.TypeOf((*MockISessionManager)(nil).CancelJsCode), arg0, arg1)
}

// CreateOpenIdIfNotExists mocks base method.
func (m *MockISessionManager) CreateOpenIdIfNotExists(arg0 context.Context, arg1 uint32) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOpenIdIfNotExists", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateOpenIdIfNotExists indicates an expected call of CreateOpenIdIfNotExists.
func (mr *MockISessionManagerMockRecorder) CreateOpenIdIfNotExists(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOpenIdIfNotExists", reflect.TypeOf((*MockISessionManager)(nil).CreateOpenIdIfNotExists), arg0, arg1)
}

// GenJsCode mocks base method.
func (m *MockISessionManager) GenJsCode(arg0 context.Context, arg1 *channel_ext_game.GetUserExtGameJsCodeReq) (string, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenJsCode", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GenJsCode indicates an expected call of GenJsCode.
func (mr *MockISessionManagerMockRecorder) GenJsCode(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenJsCode", reflect.TypeOf((*MockISessionManager)(nil).GenJsCode), arg0, arg1)
}

// GetAuthInfoByJsCode mocks base method.
func (m *MockISessionManager) GetAuthInfoByJsCode(arg0 context.Context, arg1 string) (*cache.JsCodeInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAuthInfoByJsCode", arg0, arg1)
	ret0, _ := ret[0].(*cache.JsCodeInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAuthInfoByJsCode indicates an expected call of GetAuthInfoByJsCode.
func (mr *MockISessionManagerMockRecorder) GetAuthInfoByJsCode(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAuthInfoByJsCode", reflect.TypeOf((*MockISessionManager)(nil).GetAuthInfoByJsCode), arg0, arg1)
}

// GetUidByOpenId mocks base method.
func (m *MockISessionManager) GetUidByOpenId(arg0 context.Context, arg1 string) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUidByOpenId", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUidByOpenId indicates an expected call of GetUidByOpenId.
func (mr *MockISessionManagerMockRecorder) GetUidByOpenId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUidByOpenId", reflect.TypeOf((*MockISessionManager)(nil).GetUidByOpenId), arg0, arg1)
}

// SimpleQueryOpenid mocks base method.
func (m *MockISessionManager) SimpleQueryOpenid(arg0 context.Context, arg1 uint32, arg2 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SimpleQueryOpenid", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SimpleQueryOpenid indicates an expected call of SimpleQueryOpenid.
func (mr *MockISessionManagerMockRecorder) SimpleQueryOpenid(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SimpleQueryOpenid", reflect.TypeOf((*MockISessionManager)(nil).SimpleQueryOpenid), arg0, arg1, arg2)
}
