package auth_manager

import (
    "context"
    "crypto/sha256"
    "encoding/hex"
    "fmt"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/common/status"
    channel_ext_game "golang.52tt.com/protocol/services/channel-ext-game"
    "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/auth-manager/cache"
    context0 "golang.org/x/net/context"
    "google.golang.org/grpc/codes"
    "time"
)

// GenJsCode 生成jsCode
func (m *SessionManager) GenJsCode(ctx context.Context, req *channel_ext_game.GetUserExtGameJsCodeReq) (string, string, error) {
    // get openId
    uid := req.GetUid()
    openId, err := m.CreateOpenIdIfNotExists(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "CreateOpenIdIfNotExists fail err %v, uid %d", err, uid)
        return "", "", err
    }
    // 使用openId哈希一个字符串, 生成jsCode
    jsCode := generateJSCode(generateOpenID(fmt.Sprintf("%d%s", uid, req.GetChannelViewId())))
    var clientType, marketId uint32
    info, ok := grpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "GenJsCode fail to get service info")
    } else {
        clientType = uint32(info.ClientType)
        marketId = info.MarketID
    }

    // 缓存jsCode
    err = m.cache.BindJsCodeInfo(ctx, &cache.JsCodeInfo{
        Uid:           uid,
        ChannelViewId: req.GetChannelViewId(),
        AppId:         req.GetAppId(),
        ClientType:    clientType,
        MarketId:      marketId,
        OpenId:        openId,
    }, jsCode)
    if err != nil {
        log.ErrorWithCtx(ctx, "BindUid2JsCode fail err %v, uid %d, jsCode %s", err, uid, jsCode)
        return "", "", err
    }
    return jsCode, openId, nil

}

func (m *SessionManager) CreateOpenIdIfNotExists(ctx context.Context, uid uint32) (string, error) {
    openId, err := m.store.GetOpenIdByUid(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "CreateOpenIdIfNotExists GetOpenIdByUid fail err %v, uid %d", err, uid)
        return "", err
    }
    if openId == "" {
        openId = generateOpenID(fmt.Sprintf("%04d", uid%10000))
        // 持久化存储，保证openId不变
        success, err := m.store.InsertOpenId(ctx, uid, openId)
        if err != nil {
            log.ErrorWithCtx(ctx, "CreateOpenIdIfNotExists InsertOpenId fail err %v, uid %d, openId %s", err, uid, openId)
            return "", err
        }
        if !success {
            // 等待20毫秒，可能是并发插入导致的重复插入
            time.Sleep(time.Millisecond * 20)
            // 已经插入了，查询出来并返回
            openId, err = m.store.GetOpenIdByUid(ctx, uid)
            if err != nil {
                log.ErrorWithCtx(ctx, "CreateOpenIdIfNotExists GetOpenIdByUid after insert fail err %v, uid %d", err, uid)
                return "", err
            }
            if openId == "" {
                log.ErrorWithCtx(ctx, "CreateOpenIdIfNotExists GetOpenIdByUid after insert returned empty, uid %d", uid)
                return "", protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
            }
            log.InfoWithCtx(ctx, "CreateOpenIdIfNotExists GetOpenIdByUid after insert success uid %d, openId %s", uid, openId)
            return openId, nil
        }
    }
    return openId, nil
}

// CancelJsCode 注销jsCode
func (m *SessionManager) CancelJsCode(ctx context.Context, jsCode string) error {
    return m.cache.CancelJsCode(ctx, jsCode)
}

// 使用openId哈希一个字符串, 生成jsCode
func generateJSCode(openId string) string {
    hash := sha256.New()
    hash.Write([]byte(openId))
    jsCode := hex.EncodeToString(hash.Sum(nil))
    return jsCode
}

func (m *SessionManager) GetUidByOpenId(ctx context.Context, openId string) (uint32, error) {
    return m.store.GetUidByOpenId(ctx, openId)
}

func (m *SessionManager) GetAuthInfoByJsCode(ctx context.Context, jsCode string) (*cache.JsCodeInfo, error) {
    return m.cache.GetInfoByJsCode(ctx, jsCode)
}

func (m *SessionManager) BatchGetUidByOpenIds(ctx context.Context, openIds []string) (map[string]uint32, error) {
    return m.store.BatchGetUidByOpenIds(ctx, openIds)
}

func (m *SessionManager) SimpleQueryOpenid(c context0.Context, uid uint32, ttid string) (string, error) {
    if uid == 0 {
        if ttid == "" {
            log.ErrorWithCtx(c, "SimpleQueryOpenid fail to get uid")
            return "", protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
        }

        tmpUid, err := m.antiLayer.GetUidByTTid(c, ttid)
        if err != nil {
            log.ErrorWithCtx(c, "SimpleQueryOpenid fail to GetUidByTTid. err:%v", err)
            return "", err
        }
        uid = tmpUid
    }
    openId, err := m.store.GetOpenIdByUid(c, uid)
    if err != nil {
        log.ErrorWithCtx(c, "SimpleQueryOpenid fail to GetOpenIdByUid. err:%v", err)
        return "", err
    }
    return openId, nil
}
