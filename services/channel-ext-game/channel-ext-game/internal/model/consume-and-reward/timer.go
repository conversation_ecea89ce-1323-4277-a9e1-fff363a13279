package consume_and_reward

import (
    "gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
    "context"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "time"
    "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/consume-and-reward/store"
    "gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer/task/tasks"
    "golang.52tt.com/services/tt-rev/common/feishu"
    "fmt"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    "github.com/google/uuid"
)

const (
    ReissueAwardBatCount = 100
)

func (m *Mgr) startTimer() error {
    var err error
    m.timerD, err = timer.NewTimerD(context.Background(),
        "channel-ext-game",
        timer.WithV8RedisCmdable(m.cache.GetRedisClient()))
    if err != nil {
        log.Errorf("startTimer NewTimerD err:%v", err)
        return err
    }

    // 补单
    if err = m.timerD.AddCronTask("@every 5s", "ReissueAwardHandle", tasks.FuncTask(func(ctx context.Context) {
        m.ReissueAwardHandle()
    })); err != nil {
        log.Errorf("startTimer AddCronTask handleVAExpired err:%v", err)
        return err
    }

    // 每日定时推送前一日的财务数据
    if err = m.timerD.AddCronTask("0 * * * * *", "DailyReportConsume", tasks.FuncTask(func(ctx context.Context) {
        m.DailyReportConsume()
    })); err != nil {
        log.Errorf("startTimer AddCronTask handleVAExpired err:%v", err)
        return err
    }

    m.timerD.Start()
    return nil
}

// ReissueAwardHandle 奖励补发handle
func (m *Mgr) ReissueAwardHandle() {
    ctx, cancel := context.WithTimeout(protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{RequestID: uuid.New().String()}), 20*time.Second)
    defer cancel()

    now := time.Now()
    // 最早的检查时间
    intervalHour := time.Duration(m.bc.GetReissueMaxIntervalHour())
    timeAfter := time.Now().Add(-intervalHour * time.Hour)

    tblTime := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
    // 最早的tblTime
    earliestTblTime := time.Date(timeAfter.Year(), timeAfter.Month(), 1, 0, 0, 0, 0, time.Local)

    awardList := make([]*store.AwardRecord, 0)
    for {
        if tblTime.Before(earliestTblTime) {
            break
        }
        // 获取待补发的奖励列表
        list, err := m.store.GetAwardRecordByStatus(ctx, now, store.AwardStatusInit, ReissueAwardBatCount, timeAfter, now)
        if err != nil {
            log.ErrorWithCtx(ctx, "ReissueAwardHandle fail to GetAwardRecordByStatus. err:%v", err)
            return
        }

        awardList = append(awardList, list...)
        if len(list) >= ReissueAwardBatCount {
            break
        }

        // 本月数据不够，取上个月
        tblTime = tblTime.AddDate(0, -1, 0)
    }

    // 批量补发奖励
    err := m.BatSendAward(ctx, awardList)
    if err != nil {
        log.ErrorWithCtx(ctx, "ReissueAwardHandle fail to BatSendAward. err:%v", err)
        return
    }
}

func (m *Mgr) DailyReportConsume() {
    ctx, cancel := context.WithTimeout(protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{RequestID: uuid.New().String()}), 30*time.Second)
    defer cancel()

    now := time.Now()
    log.DebugWithCtx(ctx, "DailyReportConsume now:%v", now)
    if now.Hour() != 4 || now.Minute() != 0 {
        return
    }

    beginTime := time.Date(now.Year(), now.Month(), now.Day()-1, 0, 0, 0, 0, now.Location())
    endTime := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
    _, _, err := m.DailyReportConsumeHandle(ctx, beginTime, endTime, true)
    if err != nil {
        log.ErrorWithCtx(ctx, "DailyReportConsumeHandle fail to DailyReportConsumeHandle. err:%v", err)
        return
    }

    return
}

func (m *Mgr) DailyReportConsumeHandle(ctx context.Context, beginTime, endTime time.Time, pushLark bool) (uint32, uint32, error) {

    countResp, err := m.GetConsumeTotalCount(ctx, beginTime, endTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "DailyReportConsumeHandle fail to GetConsumeTotalCount. err:%v", err)
        return 0, 0, err
    }

    log.InfoWithCtx(ctx, "DailyReportConsumeHandle GetConsumeTotalCount. countResp:%+v", countResp)

    if pushLark {
        lineStr := []string{
            fmt.Sprintf("日期：%s", beginTime.Format("2006-01-02")),
            fmt.Sprintf("消费订单数：%d", countResp.GetCount()),
            fmt.Sprintf("消费豆豆数：%d", countResp.GetValue()),
        }
        var rmbValue float64
        if countResp.GetValue() > 0 {
            rmbValue = float64(countResp.GetValue()) / 100.0
        }
        lineStr = append(lineStr, fmt.Sprintf("消费金额：%.2f元", rmbValue))
        err = m.sendFeiShuMsg("每日汇总", lineStr, "")
        if err != nil {
            log.ErrorWithCtx(ctx, "DailyReportConsumeHandle fail to sendFeiShuMsg. err:%v", err)
            return 0, 0, err
        }
    }

    return countResp.GetCount(), countResp.GetValue(), nil
}

func (m *Mgr) sendFeiShuMsg(title string, textLines []string, atUser string) error {
    lineList := make([][]*feishu.LineMem, 0, len(textLines))
    for _, text := range textLines {
        line := []*feishu.LineMem{
            {Tag: "text", Text: text},
        }
        lineList = append(lineList, line)
    }

    if atUser != "" {
        line := []*feishu.LineMem{
            {Tag: "at", UserId: atUser},
        }
        lineList = append(lineList, line)
    }

    if m.bc.GetFeiShuRobotUrl() == "" {
        return nil
    }

    return feishu.SendFeiShuRichMsg(m.bc.GetFeiShuRobotUrl(), title, lineList)
}
