package consume_and_reward

import (
    "context"
    "golang.52tt.com/pkg/log"
    unifiedPayPB "golang.52tt.com/protocol/services/unified_pay"
    UnifiedPayCallback "golang.52tt.com/protocol/services/unified_pay/cb"
    "time"
    "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/consume-and-reward/store"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/protocol/common/status"
    "google.golang.org/grpc/codes"
)

func (m *Mgr) freeze(ctx context.Context, orderId, payAppId string, uid, totalPrice uint32, outsideTime time.Time) (uint32, error) {
    // unified-pay 冻结T豆
    timeStr := outsideTime.Format("2006-01-02 15:04:05")
    reason := "「闯关专家」游戏内消费"

    restBalance, sErr := m.unifiedPayCli.PresetFreeze(ctx, uid, totalPrice, payAppId, orderId, timeStr, reason)
    if sErr != nil {
        log.ErrorWithCtx(ctx, "freeze fail to PresetFreeze. uid:%v, orderId:%v, outsideTime:%v, err:%v", uid, orderId, outsideTime, sErr)
        return restBalance, sErr
    }

    log.Infof("freeze uid:%d, orderId:%s, totalPrice:%d, outsideTime:%v, restBalance:%d", uid, orderId, totalPrice, outsideTime, restBalance)
    return restBalance, nil
}

func (m *Mgr) commit(ctx context.Context, orderId string, uid uint32, payAppId string, totalPrice uint32, outsideTime time.Time) error {
    user, err := m.accountCli.GetUser(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "commit fail to GetUser. uid:%v, orderId:%v, outsideTime:%v, err:%v", uid, orderId, outsideTime, err)
        return err
    }

    req := &unifiedPayPB.UnfreezeAndConsumeReq{
        AppId:      payAppId,
        Uid:        uid,
        UserName:   user.GetUsername(),
        ItemId:     0,
        ItemName:   "「闯关专家」游戏内消费",
        ItemNum:    1,
        ItemPrice:  totalPrice,
        TotalPrice: totalPrice,
        Platform:   "0",
        OutTradeNo: orderId,
        Notes:      "channel-ext-game",
    }

    // unified-pay 确认扣除T豆
    timeStr, _, err := m.unifiedPayCli.UnfreezeAndConsume(ctx, req)
    if err != nil {
        // 货币组commit接口在并发时会因锁报错，在这里重试一次
        retryCtx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
        defer cancel()

        timeStr, _, err = m.unifiedPayCli.UnfreezeAndConsume(retryCtx, req)
        if err != nil {
            log.ErrorWithCtx(ctx, "commit fail to UnfreezeAndConsume. uid:%v, orderId:%v, outsideTime:%v, err:%v", uid, orderId, outsideTime, err)
            return err
        }
    }

    _, _ = m.store.ChangeConsumeRecordPayInfo(ctx, outsideTime, uid, []uint32{store.ConsumeOrderStatusFreezing}, store.ConsumeOrderStatusDone, orderId, timeStr)

    log.Infof("commit uid:%d, orderId:%s, outsideTime:%v, cTime:%v", uid, orderId, outsideTime, timeStr)
    return nil
}

func (m *Mgr) rollback(ctx context.Context, orderId string, payAppId string, uid uint32, outsideTime time.Time) error {
    // unified-pay 解冻T豆
    err := m.unifiedPayCli.UnFreezeAndRefund(ctx, uid, payAppId, orderId)
    if err != nil {
        log.ErrorWithCtx(ctx, "rollback fail to UnFreezeAndRefund. uid:%v, orderId:%v, outsideTime:%v, err:%v", uid, orderId, outsideTime, err)
        return err
    }

    log.Infof("rollback uid:%v, orderId:%v, outsideTime:%v", uid, orderId, outsideTime)
    return nil
}

func (m *Mgr) Callback(ctx context.Context, uid uint32, orderId string) (op UnifiedPayCallback.Op, err error) {
    now := time.Now()

    order, exist, err := m.store.GetConsumeRecordByPayOrderId(ctx, now, orderId)
    if err != nil {
        log.ErrorWithCtx(ctx, "Callback fail to GetConsumeRecordByPayId. orderId:%v, err:%v", orderId, err)
        return 0, err
    }

    if !exist {
        lastMonthTime := getLastMonthTime(now)
        // 查上月
        order, exist, err = m.store.GetConsumeRecordByPayOrderId(ctx, lastMonthTime, orderId)
        if err != nil {
            log.ErrorWithCtx(ctx, "Callback fail to GetConsumeRecordByPayId. orderId:%v, err:%v", orderId, err)
            return 0, err
        }
    }

    if order.CTime.Add(5 * time.Minute).After(now) {
        // 回调时机有问题，返回错误
        log.ErrorWithCtx(ctx, "Callback fail. orderId:%v, err:%v", orderId, "callback too early")
        return 0, protocol.NewExactServerError(nil, status.ErrFellowHouseOrderNotExist)
    }

    gameConf, exist := m.bc.GetGameAppConfByAppid(order.AppID)
    if !exist {
        log.ErrorWithCtx(ctx, "Callback fail to GetGameAppConfByAppid. orderId:%v, err:%v", orderId, "gameAppId not exist")
        return 0, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "gameAppId not exist")
    }
    payAppId := gameConf.PayAppId
    outsideTime := order.CTime

    if exist {
        err := m.commit(ctx, orderId, order.UID, payAppId, order.Price, outsideTime)
        if err != nil {
            log.ErrorWithCtx(ctx, "Callback fail to commit. orderId:%+v, err:%v", orderId, err)
            return 0, err
        }

        op = UnifiedPayCallback.Op_COMMIT

    } else {

        // 订单不存在则回滚该笔订单
        op = UnifiedPayCallback.Op_ROLLBACK
        err := m.rollback(ctx, orderId, payAppId, order.UID, outsideTime)
        if err != nil {
            log.ErrorWithCtx(ctx, "Callback fail to rollback. orderId:%+v, err:%v", orderId, err)
            return 0, err
        }
        op = UnifiedPayCallback.Op_ROLLBACK
    }

    log.Infof("Callback uid:%d, order:%v, orderId:%v", uid, order, orderId)
    return op, nil
}

func getLastMonthTime(now time.Time) time.Time {
    return time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local).AddDate(0, -1, 0)
}
