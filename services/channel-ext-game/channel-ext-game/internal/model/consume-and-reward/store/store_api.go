package store

import(
	context "context"
	mysql "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	time "time"
)

type IStore interface {
	BatchInsertAwardRecord(ctx context.Context, records []*AwardRecord, awardTime time.Time) error
	ChangeConsumeRecordPayInfo(ctx context.Context, t time.Time, uid uint32, oldStatus []uint32, newStatus uint32, payOrderId, tBeanTimeStr string) (bool,error)
	Close() error
	CreateAwardTable(ctx context.Context, roundTime time.Time) error
	GetAwardOrderIds(ctx context.Context, tblTime, beginTime, endTime time.Time, giftTypeList []uint32) ([]string,error)
	GetAwardRecordByStatus(ctx context.Context, tblTime time.Time, status, limit uint32, beginTime, endTime time.Time) ([]*AwardRecord,error)
	GetAwardTotalCountInfo(ctx context.Context, tblTime, beginTime, endTime time.Time, giftTypeList []uint32) (*StCount,error)
	GetConsumeOrderIds(ctx context.Context, beginTime, endTime time.Time) ([]string,error)
	GetConsumeRecordByPayOrderId(ctx context.Context, t time.Time, payOrderId string) (*ConsumeRecord,bool,error)
	GetConsumeTotalCountInfo(ctx context.Context, beginTime, endTime time.Time) (*StCount,error)
	InsertExtGameConsumeRecord(ctx context.Context, record *ConsumeRecord) error
	Transaction(ctx context.Context, f func(tx mysql.Txx) error) error
	UpdateAwardStatus(ctx context.Context, awardTime time.Time, orderID string, status uint32) error
}

