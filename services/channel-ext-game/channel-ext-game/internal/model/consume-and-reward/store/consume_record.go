package store

import (
    "time"
    "fmt"
    "context"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    "strings"
)

const (
    ConsumeOrderStatusFreezing = uint32(iota)
    ConsumeOrderStatusDone
)

var createExtGameConsumeTableSql = `
CREATE Table IF NOT EXISTS %s (
    id int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id 自增',
    app_id varchar(20) NOT NULL COMMENT '第三方游戏app_id',
    source_order_id varchar(100) NOT NULL COMMENT '外部订单号',
    pay_order_id varchar(100) NOT NULL COMMENT '支付订单号',
    
    uid int(10) unsigned NOT NULL default 0 COMMENT '购买用户id',
    openid varchar(100) NOT NULL COMMENT '用户openid',
    price int(10) unsigned NOT NULL default 0 COMMENT '购买数量',

    order_status int(10)  unsigned NOT NULL default 0 COMMENT '订单状态',
    t_bean_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 't豆系统时间',
    ctime DATETIME NOT NULL default CURRENT_TIMESTAMP COMMENT '创建时间',
    mtime DATETIME NOT NULL default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    
    primary key (id),
    unique key uniq_idx_order_id (pay_order_id),
    key idx_t_bean_time (t_bean_time),
    key idx_ctime (ctime)
)engine=InnoDB default charset=utf8 COMMENT "第三方游戏消费记录表";`

func genExtGameConsumeTblName(t time.Time) string {
    return fmt.Sprintf("channel_ext_game_consume_%04d%02d", t.Year(), t.Month())
}

// 建表
func (s *Store) createExtGameConsumeTable(ctx context.Context, tblTime time.Time) error {
    _, err := s.db.ExecContext(ctx, fmt.Sprintf(createExtGameConsumeTableSql, genExtGameConsumeTblName(tblTime)))
    if err != nil {
        log.ErrorWithCtx(ctx, "create table failed. err:%v", err)
        return err
    }
    return nil
}

type ConsumeRecord struct {
    ID            uint32    `db:"id"`
    AppID         string    `db:"app_id"`
    SourceOrderID string    `db:"source_order_id"`
    PayOrderID    string    `db:"pay_order_id"`
    UID           uint32    `db:"uid"`
    OpenId        string    `db:"openid"`
    Price         uint32    `db:"price"`
    OrderStatus   uint32    `db:"order_status"`
    TBeanTime     time.Time `db:"t_bean_time"`
    CTime         time.Time `db:"ctime"`
    MTime         time.Time `db:"mtime"`
}

// InsertExtGameConsumeRecord 新增消费记录
func (s *Store) InsertExtGameConsumeRecord(ctx context.Context, record *ConsumeRecord) error {
    query := fmt.Sprintf("insert into %s (app_id,source_order_id, pay_order_id, uid,openid, price, order_status, ctime) "+
        "values (?, ?, ?, ?, ?, ?, ?, ?)", genExtGameConsumeTblName(record.CTime))

    _, err := s.db.ExecContext(ctx, query, record.AppID, record.SourceOrderID, record.PayOrderID, record.UID, record.OpenId, record.Price, record.OrderStatus, record.CTime)
    if err != nil {
        // 唯一键重复
        if mysql.IsMySQLError(err, 1062) {
            log.ErrorWithCtx(ctx, "InsertExtGameConsumeRecord failed. err:%v", err)
            return err // 一般是由于操作过太快
        }

        // 表不存在
        if mysql.IsMySQLError(err, 1146) {
            // 尝试建表
            err = s.createExtGameConsumeTable(ctx, record.CTime)
            if err != nil {
                log.ErrorWithCtx(ctx, "createExtGameConsumeTable failed. err:%v", err)
                return err
            }
            // 重试
            _, err = s.db.ExecContext(ctx, query, record.AppID, record.SourceOrderID, record.PayOrderID, record.UID, record.OpenId, record.Price, record.OrderStatus, record.CTime)
            if err != nil {
                log.ErrorWithCtx(ctx, "InsertExtGameConsumeRecord failed. err:%v", err)
                return err
            }
            return nil
        }

        log.ErrorWithCtx(ctx, "InsertExtGameConsumeRecord. err:%v", err)
        return err
    }

    log.InfoWithCtx(ctx, "InsertExtGameConsumeRecord success. record:%+v", record)
    return nil
}

// ChangeConsumeRecordPayInfo 更新记录PayInfo
func (s *Store) ChangeConsumeRecordPayInfo(ctx context.Context, t time.Time, uid uint32, oldStatus []uint32, newStatus uint32, payOrderId, tBeanTimeStr string) (bool, error) {
    if len(oldStatus) == 0 {
        return false, nil
    }

    param := make([]interface{}, 0)
    query := fmt.Sprintf(`UPDATE %s SET order_status=? `, genExtGameConsumeTblName(t))
    param = append(param, newStatus)

    if tBeanTimeStr != "" {
        tBeanTime, _ := time.ParseInLocation("2006-01-02 15:04:05", tBeanTimeStr, time.Local)
        query += `, t_bean_time = ?`
        param = append(param, tBeanTime)
    }

    param = append(param, payOrderId)
    statusStrList := make([]string, 0)
    for _, status := range oldStatus {
        statusStrList = append(statusStrList, "?")
        param = append(param, status)
    }

    query += fmt.Sprintf(` WHERE pay_order_id=? AND order_status in (%s)`, strings.Join(statusStrList, ","))

    r, err := s.db.ExecContext(ctx, query, param...)
    if err != nil {
        log.ErrorWithCtx(ctx, "UpdateConsumeRecordPayInfo fail.uid:%d, payOrderId:%v, newStatus:%v, err:%v", uid, payOrderId, newStatus, err)
        return false, err
    }

    rowAffect, _ := r.RowsAffected()
    log.InfoWithCtx(ctx, "UpdateConsumeRecordPayInfo success. uid:%d, payOrderId:%v, newStatus:%v,rowAffect:%d", uid, payOrderId, newStatus, rowAffect)
    return rowAffect > 0, nil
}

// GetConsumeRecordByPayOrderId 根据payOrderId获取消费记录
func (s *Store) GetConsumeRecordByPayOrderId(ctx context.Context, t time.Time, payOrderId string) (*ConsumeRecord, bool, error) {
    query := fmt.Sprintf("select id,app_id,source_order_id, pay_order_id, uid,openid, price, order_status, t_bean_time, ctime, mtime "+
        "from %s where pay_order_id = ?", genExtGameConsumeTblName(t))
    record := &ConsumeRecord{}
    err := s.db.GetContext(ctx, record, query, payOrderId)
    if err != nil {
        // 表不存在/记录不存在
        if mysql.IsMySQLError(err, 1146) || mysql.IsNoRowsError(err) {
            return record, false, nil
        }

        log.ErrorWithCtx(ctx, "GetConsumeRecordByPayOrderId failed. payOrderId:%s err:%v", payOrderId, err)
        return nil, false, err
    }

    return record, true, nil
}

func (s *Store) GetConsumeTotalCountInfo(ctx context.Context, beginTime, endTime time.Time) (*StCount, error) {
    out := &StCount{}
    temp := "SELECT COUNT(1) as count, IF(SUM(price),SUM(price),0) as worth FROM %s WHERE t_bean_time >= ? AND t_bean_time < ?"

    tmpCnt := &StCount{}

    query := fmt.Sprintf(temp, genExtGameConsumeTblName(beginTime))
    err := s.db.GetContext(ctx, tmpCnt, query, beginTime, endTime)
    if err != nil {
        // 表不存在、记录不存在
        if mysql.IsMySQLError(err, 1146) || mysql.IsNoRowsError(err) {
            return out, nil
        }

        log.ErrorWithCtx(ctx, "GetConsumeTotalCountInfo failed. queryMonthTime:%v, err:%v", beginTime, err)
        return out, err
    }

    out.Count += tmpCnt.Count
    out.Worth += tmpCnt.Worth

    var monthTime time.Time
    //otherMonth := false
    if beginTime.Add(time.Hour).Month() != beginTime.Month() {
        monthTime = beginTime.Add(time.Hour)
        //otherMonth = true

    } else if beginTime.Add(-time.Hour).Month() != beginTime.Month() {
        monthTime = beginTime.Add(-time.Hour)
        //otherMonth = true
    } else {

        return out, nil
    }

    // 月尾，跨月查一次
    //if otherMonth {
    tmpCnt2 := &StCount{}

    query2 := fmt.Sprintf(temp, genExtGameConsumeTblName(monthTime))
    err = s.db.GetContext(ctx, tmpCnt2, query2, beginTime, endTime)
    if err != nil {
        // 表不存在、记录不存在
        if mysql.IsMySQLError(err, 1146) || mysql.IsNoRowsError(err) {
            return out, nil
        }

        log.ErrorWithCtx(ctx, "GetConsumeTotalCountInfo failed. queryMonthTime:%v, err:%v", beginTime, err)
        return out, err
    }

    out.Count += tmpCnt2.Count
    out.Worth += tmpCnt2.Worth
    //}

    return out, nil
}

func (s *Store) GetConsumeOrderIds(ctx context.Context, beginTime, endTime time.Time) ([]string, error) {
    list := make([]string, 0)
    temp := "SELECT pay_order_id FROM %s WHERE t_bean_time >= ? AND t_bean_time < ?"

    query := fmt.Sprintf(temp, genExtGameConsumeTblName(beginTime))
    err := s.db.SelectContext(ctx, &list, query, beginTime, endTime)
    if err != nil {
        // 表不存在、记录不存在
        if mysql.IsMySQLError(err, 1146) || mysql.IsNoRowsError(err) {
            return list, nil
        }
        log.ErrorWithCtx(ctx, "GetConsumeOrderIds fail. queryMonthTime:%v, err:%v", beginTime, err)
        return list, err
    }

    var monthTime time.Time
    //otherMonth := false
    if beginTime.Add(time.Hour).Month() != beginTime.Month() {
        monthTime = beginTime.Add(time.Hour)
        //otherMonth = true

    } else if beginTime.Add(-time.Hour).Month() != beginTime.Month() {
        monthTime = beginTime.Add(-time.Hour)
        //otherMonth = true
    } else {

        return list, nil
    }

    // 月尾，跨月查一次
    list2 := make([]string, 0)
    query2 := fmt.Sprintf(temp, genExtGameConsumeTblName(monthTime))
    err = s.db.SelectContext(ctx, &list2, query2, beginTime, endTime)
    if err != nil {
        // 表不存在、记录不存在
        if mysql.IsMySQLError(err, 1146) || mysql.IsNoRowsError(err) {
            return list2, nil
        }
        log.ErrorWithCtx(ctx, "GetConsumeOrderIds fail. queryMonthTime:%v, err:%v", beginTime, err)
        return list2, err
    }
    list = append(list, list2...)

    return list, nil
}
