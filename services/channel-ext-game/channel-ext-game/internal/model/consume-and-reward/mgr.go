package consume_and_reward

//go:generate quicksilver-cli test interface ../consume-and-reward
//go:generate mockgen -destination=./mocks/consume-and-reward.go -package=mocks golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/consume-and-reward IMgr

import (
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
    "sync"
    "gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
    "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/consume-and-reward/store"
    "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/consume-and-reward/cache"
    "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/conf"
    anti_corruption_layer "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/anti-corruption-layer"
    "context"
    unifiedPay "golang.52tt.com/clients/unified_pay"
    "golang.52tt.com/clients/account"
    award_center "golang.52tt.com/protocol/services/risk-control/award-center"
    pb "golang.52tt.com/protocol/services/channel-ext-game"
    "strconv"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "time"
    "fmt"
    "golang.52tt.com/pkg/protocol"
    "google.golang.org/grpc/codes"
    "golang.52tt.com/protocol/common/status"
    "strings"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
)

var ReconcileGiftType = []uint32{1, 2} // 对应T豆、红钻
const (
    AwardHandlerPackage = 0
    AwardHandlerDress   = 1
)

var (
    ErrGameAppIdInvalid = protocol.NewExactServerError(codes.OK, status.ErrChannelExtGameGameAppidInvalid, "无效的AppId")
)

type Mgr struct {
    store      store.IStore
    cache      cache.ICache
    bc         conf.IBusinessConfManager
    timerD     *timer.Timer
    acLayerMgr anti_corruption_layer.IMgr

    wg       sync.WaitGroup
    shutDown chan struct{}

    unifiedPayCli unifiedPay.IClient
    accountCli    account.IClient
}

func NewMgr(s mysql.DBx, cacheClient redis.Cmdable, bc conf.IBusinessConfManager, acLayerMgr anti_corruption_layer.IMgr) (*Mgr, error) {
    mysqlStore := store.NewStore(s)
    redisCli := cache.NewCache(cacheClient)
    unifiedPayCli, _ := unifiedPay.NewClient()
    accountCli, _ := account.NewClient()

    m := &Mgr{
        store:         mysqlStore,
        cache:         redisCli,
        bc:            bc,
        unifiedPayCli: unifiedPayCli,
        accountCli:    accountCli,
        shutDown:      make(chan struct{}),
        acLayerMgr:    acLayerMgr,
    }

    err := m.startTimer()
    if err != nil {
        log.Errorf("NewMgr startTimer err:%v", err)
        return m, err
    }

    return m, nil
}

func (m *Mgr) Stop() {
    _ = m.cache.Close()
    _ = m.store.Close()
    m.shutDown <- struct{}{}
    m.wg.Wait()
}

func (m *Mgr) Consume(ctx context.Context, uid uint32, in *pb.ExtGameConsumeReq) error {
    outsideTime := time.Now()
    if in.GetOutsideTs() != 0 {
        outsideTime = time.Unix(in.GetOutsideTs(), 0)
    }

    gameConf, exist := m.bc.GetGameAppConfByAppid(in.GetAppid())
    if !exist {
        log.ErrorWithCtx(ctx, "AddExtGameConsume fail. appid:%s not exist", in.GetAppid())
        return ErrGameAppIdInvalid
    }

    payOrderId := genPayOrderId(in.GetAppid(), uid, in.GetOrderId())

    // 判断orderId是否已存在
    _, exist, err := m.store.GetConsumeRecordByPayOrderId(ctx, outsideTime, payOrderId)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetConsumeRecordByPayOrderId fail. err:%v", err)
        return err
    }

    if !exist && outsideTime.Add(-5*time.Minute).Month() != outsideTime.Month() {
        // 如果刚好跨月，多查一个月
        _, exist, err = m.store.GetConsumeRecordByPayOrderId(ctx, outsideTime.Add(-5*time.Minute), payOrderId)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetConsumeRecordByPayOrderId fail. err:%v", err)
            return err
        }
    }

    if exist {
        log.ErrorWithCtx(ctx, "AddExtGameConsume fail. orderId:%s already exist", in.GetOrderId())
        return protocol.NewExactServerError(codes.OK, status.ErrChannelExtGameAwardOrderExists, "订单已存在")
    }

    // 冻结T豆
    restBalance, err := m.freeze(ctx, payOrderId, gameConf.PayAppId, uid, in.GetAmount(), outsideTime)
    if err != nil {
        // 具体的错误码在logic层处理
        log.ErrorWithCtx(ctx, "freeze fail. uid:%d, orderId:%s, totalPrice:%d, outsideTime:%v", uid, payOrderId, in.GetAmount(), outsideTime)
        return err
    }

    consumeRecord := &store.ConsumeRecord{
        AppID:         gameConf.AppID,
        SourceOrderID: in.GetOrderId(),
        PayOrderID:    payOrderId,
        UID:           uid,
        OpenId:        in.GetOpenid(),
        Price:         in.GetAmount(),
        CTime:         outsideTime,
    }

    err = m.store.InsertExtGameConsumeRecord(ctx, consumeRecord)
    if err != nil {
        log.ErrorWithCtx(ctx, "InsertExtGameConsumeRecord failed. err:%v", err)
        return err
    }

    GoroutineWithTimeoutCtx(ctx, 3*time.Second, func(ctx context.Context) {
        err = m.commit(ctx, payOrderId, uid, gameConf.PayAppId, in.GetAmount(), outsideTime)
        if err != nil {
            log.ErrorWithCtx(ctx, "commit fail. orderId:%s, err:%v", payOrderId, err)
        }
    })

    log.InfoWithCtx(ctx, "InsertExtGameConsumeRecord success. record:%+v restBalance：%d", consumeRecord, restBalance)
    return nil
}

func genPayOrderId(gameAppID string, uid uint32, sourceOrderId string) string {
    return fmt.Sprintf("%s_%d_%s", gameAppID, uid, sourceOrderId)
}

func (m *Mgr) SendAward(ctx context.Context, uid uint32, in *pb.ExtGameAwardReq) error {
    gameConf, exist := m.bc.GetGameAppConfByAppid(in.GetAppid())
    if !exist {
        log.ErrorWithCtx(ctx, "SendPlatformAward fail. appid:%s not exist", in.GetAppid())
        return ErrGameAppIdInvalid
    }

    // 奖励校验
    award, exist := gameConf.GetAwardInfo(in.GetRewardId(), in.GetRewardType())
    if !exist {
        log.ErrorWithCtx(ctx, "SendPlatformAward fail. appid:%s, rewardId:%s, rewardType:%d not exist", in.GetAppid(), in.GetRewardId(), in.GetRewardType())
        return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "奖励不存在")
    }

    now := time.Now()
    awardTime := time.Unix(now.Unix(), 0)
    orderId, err := m.genAwardOrderId(ctx, gameConf, uid, in.GetRewardType(), in.GetRewardId(), in.GetOrderId())
    if err != nil {
        return err
    }
    awardRecord := &store.AwardRecord{
        OrderID:       orderId,
        SourceOrderID: in.GetOrderId(),
        GameAppId:     gameConf.AppID,
        Uid:           uid,
        OpenId:        in.GetOpenid(),
        GiftID:        in.GetRewardId(),
        GiftType:      in.GetRewardType(),
        GiftWorth:     award.Worth * in.GetAmount(),
        Amount:        in.GetAmount(),
        AwardTime:     awardTime,
        CreateTime:    awardTime,
    }

    err = m.store.BatchInsertAwardRecord(ctx, []*store.AwardRecord{awardRecord}, awardTime)
    if err != nil {
        // 如果是主键重复
        if strings.Contains(err.Error(), "1062") {
            return protocol.NewExactServerError(codes.OK, status.ErrChannelExtGameAwardOrderExists)
        }
        log.ErrorWithCtx(ctx, "SendPlatformAward fail to InsertAwardRecord. %+v, err:%v", award, err)
        return err
    }

    GoroutineWithTimeoutCtx(ctx, 3*time.Second, func(ctx context.Context) {
        _ = m.sendAward(ctx, awardRecord)
    })

    log.InfoWithCtx(ctx, "SendPlatformAward success. award:%+v", awardRecord)
    return nil
}

func (m *Mgr) genAwardOrderId(ctx context.Context, gameConf *conf.GameAppConf, uid uint32, awardType uint32, awardId, sourceOrderId string) (string, error) {

    switch pb.ExtGameAwardReq_RewardType(awardType) {
    case pb.ExtGameAwardReq_REWARD_TYPE_TBEAN,
        pb.ExtGameAwardReq_REWARD_TYPE_RED_DIAMONDD:
        return fmt.Sprintf("%d_OP_%s_%d_%d_%s_%s", gameConf.BackSenderAppId, gameConf.AppID, uid, awardType, awardId, sourceOrderId), nil

    // 以下为装扮类型
    case pb.ExtGameAwardReq_REWARD_TYPE_HEADWEAR,
        pb.ExtGameAwardReq_REWARD_TYPE_HORSE,
        pb.ExtGameAwardReq_REWARD_TYPE_OFFICIAL_CERT,
        pb.ExtGameAwardReq_REWARD_TYPE_NAMEPLATE,
        pb.ExtGameAwardReq_REWARD_TYPE_USER_DECORATION,
        pb.ExtGameAwardReq_REWARD_TYPE_MEDAL:

        return fmt.Sprintf("%d_OP_%s_%d_%d_%s_%s", gameConf.DressSenderAppId, gameConf.AppID, uid, awardType, awardId, sourceOrderId), nil
    default:
        return "", protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "不支持的奖励类型")
    }

}

func (m *Mgr) BatSendAward(ctx context.Context, awardList []*store.AwardRecord) error {

    for _, award := range awardList {
        err := m.sendAward(ctx, award)
        if err != nil {
            continue
        }

        // 更新发奖状态
        _ = m.store.UpdateAwardStatus(ctx, award.AwardTime, award.OrderID, store.AwardStatusSuccess)
    }

    return nil
}

func (m *Mgr) sendAward(ctx context.Context, award *store.AwardRecord) error {
    handle, dressType := getAwardHandleAndAwardCenterDressType(award.GiftType)
    gameAppId := award.GameAppId
    switch handle {
    case AwardHandlerPackage:
        bgId, err := strconv.ParseInt(award.GiftID, 10, 64)
        if err != nil {
            log.ErrorWithCtx(ctx, "awardByRecord fail to ParseInt, %+v, err:%v", award, err)
            return err
        }
        err = m.acLayerMgr.AwardPackage(ctx, award.Uid, uint32(bgId), award.Amount, award.AwardTime.Unix(), award.OrderID, gameAppId)
        if err != nil {
            log.ErrorWithCtx(ctx, "sendAward fail to AwardPackage, err:%v", err)
            return err
        }

    case AwardHandlerDress:
        err := m.acLayerMgr.AwardDress(ctx, award.Uid, award.Amount, uint32(award.AwardTime.Unix()), dressType, award.GiftID, award.OrderID, gameAppId)
        if err != nil {
            log.ErrorWithCtx(ctx, "sendAward fail to AwardDress, err:%v", err)
            return err
        }

    default:
        log.ErrorWithCtx(ctx, "awardByRecord unknown giftType:%d", award.GiftType)
        return nil
    }
    return nil
}

func getAwardHandleAndAwardCenterDressType(giftType uint32) (uint32, uint32) {
    switch pb.ExtGameAwardReq_RewardType(giftType) {
    case pb.ExtGameAwardReq_REWARD_TYPE_TBEAN,
        pb.ExtGameAwardReq_REWARD_TYPE_RED_DIAMONDD:
        return AwardHandlerPackage, 0

    case pb.ExtGameAwardReq_REWARD_TYPE_HEADWEAR:
        return AwardHandlerDress, uint32(award_center.EGiftType_Headwear)

    case pb.ExtGameAwardReq_REWARD_TYPE_HORSE:
        return AwardHandlerDress, uint32(award_center.EGiftType_Horse)

    case pb.ExtGameAwardReq_REWARD_TYPE_OFFICIAL_CERT:
        return AwardHandlerDress, uint32(award_center.EGiftType_OfficialCert)

    case pb.ExtGameAwardReq_REWARD_TYPE_NAMEPLATE:
        return AwardHandlerDress, uint32(award_center.EGiftType_Nameplate)

    case pb.ExtGameAwardReq_REWARD_TYPE_USER_DECORATION:
        return AwardHandlerDress, uint32(award_center.EGiftType_UserDecoration)

    case pb.ExtGameAwardReq_REWARD_TYPE_MEDAL:
        return AwardHandlerDress, uint32(award_center.EGiftType_Medal)

    default:
        return 0, 0
    }
}

func GoroutineWithTimeoutCtx(ctx context.Context, timeout time.Duration, fn func(ctx context.Context)) {
    timeoutCtx, cancel := protogrpc.NewContextWithInfoTimeout(ctx, timeout)
    go func() {
        defer cancel()
        fn(timeoutCtx)
    }()
}

// GetConsumeTotalCount 获取T豆消费数量
func (m *Mgr) GetConsumeTotalCount(ctx context.Context, beginTime, endTime time.Time) (*reconcile_v2.CountResp, error) {
    out := &reconcile_v2.CountResp{}
    info, err := m.store.GetConsumeTotalCountInfo(ctx, beginTime, endTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetConsumeTotalCount fail to GetConsumeTotalCountInfo. beginTime:%v, err:%v", beginTime, err)
        return out, err
    }

    out.Count = uint32(info.Count)
    out.Value = uint32(info.Worth)
    return out, nil
}

// GetConsumeOrderIds 获取T豆消费订单号列表
func (m *Mgr) GetConsumeOrderIds(ctx context.Context, beginTime, endTime time.Time) (*reconcile_v2.OrderIdsResp, error) {
    out := &reconcile_v2.OrderIdsResp{}
    var err error

    out.OrderIds, err = m.store.GetConsumeOrderIds(ctx, beginTime, endTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetConsumeOrderIds fail to GetConsumeOrderIds. beginTime:%v, err:%v", beginTime, err)
        return out, err
    }

    return out, nil
}

// GetAwardTotalCount 获取发奖总数
func (m *Mgr) GetAwardTotalCount(ctx context.Context, beginTime, endTime time.Time) (*reconcile_v2.CountResp, error) {
    out := &reconcile_v2.CountResp{}
    tblTime := beginTime

    info, err := m.store.GetAwardTotalCountInfo(ctx, tblTime, beginTime, endTime, ReconcileGiftType)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetAwardTotalCount fail to GetAwardTotalCountInfo. beginTime:%v, err:%v", beginTime, err)
        return out, err
    }

    out.Count += uint32(info.Count)
    out.Value += uint32(info.Value)

    if beginTime.Add(time.Hour).Month() != beginTime.Month() {
        tblTime = beginTime.Add(time.Hour)
    } else if beginTime.Add(-time.Hour).Month() != beginTime.Month() {
        tblTime = beginTime.Add(-time.Hour)
    } else {
        return out, nil
    }

    // 如果跨月，再次查询
    info, err = m.store.GetAwardTotalCountInfo(ctx, tblTime, beginTime, endTime, ReconcileGiftType)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetAwardTotalCount fail to GetAwardTotalCountInfo. beginTime:%v, err:%v", beginTime, err)
        return out, err
    }

    out.Count += uint32(info.Count)
    out.Value += uint32(info.Value)
    return out, nil
}

// GetAwardOrderIds 获取发奖订单号列表
func (m *Mgr) GetAwardOrderIds(ctx context.Context, beginTime, endTime time.Time) (*reconcile_v2.OrderIdsResp, error) {
    out := &reconcile_v2.OrderIdsResp{}
    var err error
    tblTime := beginTime

    out.OrderIds, err = m.store.GetAwardOrderIds(ctx, tblTime, beginTime, endTime, ReconcileGiftType)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetAwardOrderIds fail to GetAwardOrderIds. beginTime:%v, err:%v", beginTime, err)
        return out, err
    }

    if beginTime.Add(time.Hour).Month() != beginTime.Month() {
        tblTime = beginTime.Add(time.Hour)
    } else if beginTime.Add(-time.Hour).Month() != beginTime.Month() {
        tblTime = beginTime.Add(-time.Hour)
    } else {
        return out, nil
    }

    // 如果跨月，再次查询
    orderIds, err := m.store.GetAwardOrderIds(ctx, tblTime, beginTime, endTime, ReconcileGiftType)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetAwardOrderIds fail to GetAwardOrderIds. beginTime:%v, err:%v", beginTime, err)
        return out, err
    }

    out.OrderIds = append(out.OrderIds, orderIds...)
    log.DebugWithCtx(ctx, "GetAwardOrderIds orderIds:%v", out.OrderIds)
    return out, nil
}
