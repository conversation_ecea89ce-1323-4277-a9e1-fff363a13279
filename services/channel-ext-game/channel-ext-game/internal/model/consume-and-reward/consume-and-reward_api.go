package consume_and_reward

import(
	context "context"
	time "time"
	store "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/consume-and-reward/store"
	pb "golang.52tt.com/protocol/services/channel-ext-game"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	UnifiedPayCallback "golang.52tt.com/protocol/services/unified_pay/cb"
)

type IMgr interface {
	BatSendAward(ctx context.Context, awardList []*store.AwardRecord) error
	Callback(ctx context.Context, uid uint32, orderId string) (op UnifiedPayCallback.Op,err error)
	Consume(ctx context.Context, uid uint32, in *pb.ExtGameConsumeReq) error
	DailyReportConsume() 
	DailyReportConsumeHandle(ctx context.Context, beginTime, endTime time.Time, pushLark bool) (uint32,uint32,error)
	GetAwardOrderIds(ctx context.Context, beginTime, endTime time.Time) (*reconcile_v2.OrderIdsResp,error)
	GetAwardTotalCount(ctx context.Context, beginTime, endTime time.Time) (*reconcile_v2.CountResp,error)
	GetConsumeOrderIds(ctx context.Context, beginTime, endTime time.Time) (*reconcile_v2.OrderIdsResp,error)
	GetConsumeTotalCount(ctx context.Context, beginTime, endTime time.Time) (*reconcile_v2.CountResp,error)
	ReissueAwardHandle() 
	SendAward(ctx context.Context, uid uint32, in *pb.ExtGameAwardReq) error
	Stop() 
}

