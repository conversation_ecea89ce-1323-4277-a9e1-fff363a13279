// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/common-mgr/cache (interfaces: ICache)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	redis "github.com/go-redis/redis/v8"
	gomock "github.com/golang/mock/gomock"
	cache "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/common-mgr/cache"
)

// MockICache is a mock of ICache interface.
type MockICache struct {
	ctrl     *gomock.Controller
	recorder *MockICacheMockRecorder
}

// MockICacheMockRecorder is the mock recorder for MockICache.
type MockICacheMockRecorder struct {
	mock *MockICache
}

// NewMockICache creates a new mock instance.
func NewMockICache(ctrl *gomock.Controller) *MockICache {
	mock := &MockICache{ctrl: ctrl}
	mock.recorder = &MockICacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICache) EXPECT() *MockICacheMockRecorder {
	return m.recorder
}

// AddToWhiteList mocks base method.
func (m *MockICache) AddToWhiteList(arg0 context.Context, arg1 string, arg2 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddToWhiteList", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddToWhiteList indicates an expected call of AddToWhiteList.
func (mr *MockICacheMockRecorder) AddToWhiteList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddToWhiteList", reflect.TypeOf((*MockICache)(nil).AddToWhiteList), arg0, arg1, arg2)
}

// BatAddSendMsgQueue mocks base method.
func (m *MockICache) BatAddSendMsgQueue(arg0 context.Context, arg1 []*cache.SendMsg) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatAddSendMsgQueue", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatAddSendMsgQueue indicates an expected call of BatAddSendMsgQueue.
func (mr *MockICacheMockRecorder) BatAddSendMsgQueue(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatAddSendMsgQueue", reflect.TypeOf((*MockICache)(nil).BatAddSendMsgQueue), arg0, arg1)
}

// CheckIfInWhiteList mocks base method.
func (m *MockICache) CheckIfInWhiteList(arg0 context.Context, arg1 string, arg2 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIfInWhiteList", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIfInWhiteList indicates an expected call of CheckIfInWhiteList.
func (mr *MockICacheMockRecorder) CheckIfInWhiteList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIfInWhiteList", reflect.TypeOf((*MockICache)(nil).CheckIfInWhiteList), arg0, arg1, arg2)
}

// Close mocks base method.
func (m *MockICache) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockICacheMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockICache)(nil).Close))
}

// GetGameDownloadInfo mocks base method.
func (m *MockICache) GetGameDownloadInfo(arg0 context.Context, arg1 string) (*cache.GameDownloadInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameDownloadInfo", arg0, arg1)
	ret0, _ := ret[0].(*cache.GameDownloadInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameDownloadInfo indicates an expected call of GetGameDownloadInfo.
func (mr *MockICacheMockRecorder) GetGameDownloadInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameDownloadInfo", reflect.TypeOf((*MockICache)(nil).GetGameDownloadInfo), arg0, arg1)
}

// GetRedisClient mocks base method.
func (m *MockICache) GetRedisClient() redis.Cmdable {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedisClient")
	ret0, _ := ret[0].(redis.Cmdable)
	return ret0
}

// GetRedisClient indicates an expected call of GetRedisClient.
func (mr *MockICacheMockRecorder) GetRedisClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedisClient", reflect.TypeOf((*MockICache)(nil).GetRedisClient))
}

// GetUserEntry mocks base method.
func (m *MockICache) GetUserEntry(arg0 context.Context, arg1 uint32) (bool, *cache.UserEntry, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserEntry", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(*cache.UserEntry)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserEntry indicates an expected call of GetUserEntry.
func (mr *MockICacheMockRecorder) GetUserEntry(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserEntry", reflect.TypeOf((*MockICache)(nil).GetUserEntry), arg0, arg1)
}

// GetUserSendDailyLimit mocks base method.
func (m *MockICache) GetUserSendDailyLimit(arg0 context.Context, arg1, arg2, arg3 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserSendDailyLimit", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserSendDailyLimit indicates an expected call of GetUserSendDailyLimit.
func (mr *MockICacheMockRecorder) GetUserSendDailyLimit(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSendDailyLimit", reflect.TypeOf((*MockICache)(nil).GetUserSendDailyLimit), arg0, arg1, arg2, arg3)
}

// GetWhiteListRandomly mocks base method.
func (m *MockICache) GetWhiteListRandomly(arg0 context.Context, arg1 string, arg2 uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWhiteListRandomly", arg0, arg1, arg2)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWhiteListRandomly indicates an expected call of GetWhiteListRandomly.
func (mr *MockICacheMockRecorder) GetWhiteListRandomly(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWhiteListRandomly", reflect.TypeOf((*MockICache)(nil).GetWhiteListRandomly), arg0, arg1, arg2)
}

// HIncrUserSendDailyLimit mocks base method.
func (m *MockICache) HIncrUserSendDailyLimit(arg0 context.Context, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HIncrUserSendDailyLimit", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// HIncrUserSendDailyLimit indicates an expected call of HIncrUserSendDailyLimit.
func (mr *MockICacheMockRecorder) HIncrUserSendDailyLimit(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HIncrUserSendDailyLimit", reflect.TypeOf((*MockICache)(nil).HIncrUserSendDailyLimit), arg0, arg1, arg2, arg3)
}

// RPopOneSendMsgQueue mocks base method.
func (m *MockICache) RPopOneSendMsgQueue(arg0 context.Context) (*cache.SendMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RPopOneSendMsgQueue", arg0)
	ret0, _ := ret[0].(*cache.SendMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RPopOneSendMsgQueue indicates an expected call of RPopOneSendMsgQueue.
func (mr *MockICacheMockRecorder) RPopOneSendMsgQueue(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RPopOneSendMsgQueue", reflect.TypeOf((*MockICache)(nil).RPopOneSendMsgQueue), arg0)
}

// RemoveFromWhiteList mocks base method.
func (m *MockICache) RemoveFromWhiteList(arg0 context.Context, arg1 string, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveFromWhiteList", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveFromWhiteList indicates an expected call of RemoveFromWhiteList.
func (mr *MockICacheMockRecorder) RemoveFromWhiteList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveFromWhiteList", reflect.TypeOf((*MockICache)(nil).RemoveFromWhiteList), arg0, arg1, arg2)
}

// SetUserEntry mocks base method.
func (m *MockICache) SetUserEntry(arg0 context.Context, arg1 uint32, arg2, arg3 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserEntry", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserEntry indicates an expected call of SetUserEntry.
func (mr *MockICacheMockRecorder) SetUserEntry(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserEntry", reflect.TypeOf((*MockICache)(nil).SetUserEntry), arg0, arg1, arg2, arg3)
}

// UpdateGameDownloadInfo mocks base method.
func (m *MockICache) UpdateGameDownloadInfo(arg0 context.Context, arg1 string, arg2 *cache.GameDownloadInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateGameDownloadInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateGameDownloadInfo indicates an expected call of UpdateGameDownloadInfo.
func (mr *MockICacheMockRecorder) UpdateGameDownloadInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateGameDownloadInfo", reflect.TypeOf((*MockICache)(nil).UpdateGameDownloadInfo), arg0, arg1, arg2)
}
