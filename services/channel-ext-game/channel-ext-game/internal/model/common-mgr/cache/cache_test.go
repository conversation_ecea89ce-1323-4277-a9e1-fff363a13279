package cache

import (
    redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
    "context"
    "testing"
    "strconv"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "github.com/alicebob/miniredis/v2"
)

var cacheCli *Cache

var (
    testUid uint32 = 123456
)

func init() {
    log.SetLevel(log.DebugLevel)
    s, err := miniredis.Run()
    if err != nil {
        panic(err)
    }

    port, _ := strconv.ParseInt(s.Port(), 10, 32)
    redisClient, _ := redisConnect.NewClient(context.Background(), &redisConnect.RedisConfig{
        Host: s.Host(),
        Port: uint32(port),
    })

    cacheCli = NewCache(redisClient)
}

func TestCache_GetUserEntry(t *testing.T) {
    exist, userEntry, err := cacheCli.GetUserEntry(context.Background(), testUid)
    if err != nil {
        t.Logf("get user entry failed, err: %v", err)
    }
    t.Logf("user entry: %+v", userEntry)
    if exist {
        t.Errorf("user entry should not exist")
    }

    err = cacheCli.SetUserEntry(context.Background(), testUid, true, true)
    if err != nil {
        t.Logf("set user entry failed, err: %v", err)
    }

    exist, userEntry, err = cacheCli.GetUserEntry(context.Background(), testUid)
    if err != nil {
        t.Logf("get user entry failed, err: %v", err)
    }

    if !exist {
        t.Errorf("user entry should exist")
    }

    t.Logf("user entry: %+v", userEntry)
}

func TestCache_CheckIfCidInWhiteList(t *testing.T) {
    ChannelWhiteKeyPrefix := "ext_game_cid_white"
    cid := uint32(123456)
    exist, err := cacheCli.CheckIfInWhiteList(context.Background(), ChannelWhiteKeyPrefix, cid)
    if err != nil {
        t.Logf("check if cid in white list failed, err: %v", err)
    }
    if exist {
        t.Errorf("cid should not exist in white list")
    }

    err = cacheCli.AddToWhiteList(context.Background(), ChannelWhiteKeyPrefix, []uint32{cid, cid + 1, cid + 2, cid + 3})

    exist, err = cacheCli.CheckIfInWhiteList(context.Background(), ChannelWhiteKeyPrefix, cid)
    if err != nil {
        t.Logf("check if cid in white list failed, err: %v", err)
    }

    if !exist {
        t.Errorf("cid should exist in white list")
    }

    // 随机获取n个白名单房间
    n := uint32(5)
    cids, err := cacheCli.GetWhiteListRandomly(context.Background(), ChannelWhiteKeyPrefix, n)
    if err != nil {
        t.Logf("get white list randomly failed, err: %v", err)
    }
    for _, cid := range cids {
        t.Logf("cid: %v", cid)
    }

    // 移除房间白名单
    err = cacheCli.RemoveFromWhiteList(context.Background(), ChannelWhiteKeyPrefix, cid)
    if err != nil {
        t.Logf("remove cid from white list failed, err: %v", err)
    }

    exist, err = cacheCli.CheckIfInWhiteList(context.Background(), ChannelWhiteKeyPrefix, cid)
    if err != nil {
        t.Logf("check if cid in white list failed, err: %v", err)
    }
    if exist {
        t.Errorf("cid should not exist in white list")
    }
}

func TestCache_GetUserSendDailyLimit(t *testing.T) {
    uid := uint32(123456)
    day := uint32(1)
    sendType := uint32(1)
    cnt, err := cacheCli.GetUserSendDailyLimit(context.Background(), uid, day, sendType)
    if err != nil {
        t.Logf("get user send daily limit failed, err: %v", err)
    }
    if cnt != 0 {
        t.Errorf("cnt should be 0")
    }

    err = cacheCli.HIncrUserSendDailyLimit(context.Background(), uid, day, sendType)
    if err != nil {
        t.Logf("incr user send daily limit failed, err: %v", err)
    }

    cnt, err = cacheCli.GetUserSendDailyLimit(context.Background(), uid, day, sendType)
    if err != nil {
        t.Logf("get user send daily limit failed, err: %v", err)
    }
    if cnt != 1 {
        t.Errorf("cnt should be 1")
    }
}

func TestCache_RPopOneSendMsgQueue(t *testing.T) {
    msg := &SendMsg{
        AppId: "test",
        //SendType:   1,
        TemplateId: 1,
        Uid:        1,
    }

    err := cacheCli.BatAddSendMsgQueue(context.Background(), []*SendMsg{msg})
    if err != nil {
        t.Logf("bat add send msg queue failed, err: %v", err)
    }

    m, err := cacheCli.RPopOneSendMsgQueue(context.Background())
    if err != nil {
        t.Logf("rpop one send msg queue failed, err: %v", err)
    }

    t.Logf("msg: %+v", m)

    m2, err := cacheCli.RPopOneSendMsgQueue(context.Background())
    if err != nil {
        t.Logf("rpop one send msg queue failed, err: %v", err)
    }

    t.Logf("msg2: %+v", m2)

    m2, err = cacheCli.RPopOneSendMsgQueue(context.Background())
    if err != nil {
        t.Logf("rpop one send msg queue failed, err: %v", err)
    }

    t.Logf("msg2: %+v", m2)
}

func TestCache_GetWhiteListRandomly(t *testing.T) {
    // 随机获取n个白名单房间
    n := uint32(5)
    cids, err := cacheCli.GetWhiteListRandomly(context.Background(), "ext_game_cid_white_", n)
    if err != nil {
        t.Logf("get white list randomly failed, err: %v", err)
    }
    for _, cid := range cids {
        t.Logf("cid: %v", cid)
    }
}
