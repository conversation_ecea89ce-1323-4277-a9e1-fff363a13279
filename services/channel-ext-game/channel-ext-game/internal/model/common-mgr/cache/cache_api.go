package cache

import(
	context "context"
	redis "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
)

type ICache interface {
	AddToWhiteList(ctx context.Context, keyPrefix string, cidList []uint32) error
	BatAddSendMsgQueue(ctx context.Context, msgList []*SendMsg) error
	CheckIfInWhiteList(ctx context.Context, keyPrefix string, cid uint32) (bool,error)
	Close() error
	GetGameDownloadInfo(ctx context.Context, appid string) (*GameDownloadInfo,error)
	GetRedisClient() redis.Cmdable
	GetUserEntry(ctx context.Context, uid uint32) (bool,*UserEntry,error)
	GetUserSendDailyLimit(ctx context.Context, uid, day, sendType uint32) (uint32,error)
	GetWhiteListRandomly(ctx context.Context, keyPrefix string, n uint32) ([]uint32,error)
	HIncrUserSendDailyLimit(ctx context.Context, uid, day, sendType uint32) error
	RPopOneSendMsgQueue(ctx context.Context) (*SendMsg,error)
	RemoveFromWhiteList(ctx context.Context, keyPrefix string, cid uint32) error
	SetUserEntry(ctx context.Context, uid uint32, inGroup, faceAuth bool) error
	UpdateGameDownloadInfo(ctx context.Context, appid string, info *GameDownloadInfo) error
}

