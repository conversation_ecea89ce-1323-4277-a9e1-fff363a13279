package common_mgr

import (
    "context"
    "testing"
    "github.com/golang/mock/gomock"
    "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/common-mgr/mocks"
    bcMocks "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/conf/mocks"
    "net/http"
    "time"
    "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/common-mgr/cache"
    ttc_proxy_mocks "golang.52tt.com/clients/mocks/ttc-proxy"
)

var (
    testMgr *Mgr

    ctx       = context.Background()
    mockStore *mocks.MockIStore
    mockCache *mocks.MockICache
    mockBc    *bcMocks.MockIBusinessConfManager

    ttcProxyMock *ttc_proxy_mocks.MockIClient

    testUid = uint32(1)
)

func initTestMgr(t *testing.T) {
    ctrl := gomock.NewController(t)
    mockStore = mocks.NewMockIStore(ctrl)
    mockCache = mocks.NewMockICache(ctrl)
    mockBc = bcMocks.NewMockIBusinessConfManager(ctrl)
    ttcProxyMock = ttc_proxy_mocks.NewMockIClient(ctrl)

    httpCli := &http.Client{
        Timeout: time.Second * 5, // 请求超时时间
        Transport: &http.Transport{
            MaxIdleConns:        2,                // 最大空闲连接
            MaxConnsPerHost:     2,                // 每个pod最多多少链接
            IdleConnTimeout:     60 * time.Second, // 空闲连接的超时时间
            MaxIdleConnsPerHost: 2,                // 每个host保持的空闲连接数
        },
    }

    testMgr = &Mgr{
        store:    mockStore,
        cache:    mockCache,
        bc:       mockBc,
        httpCli:  httpCli,
        shutDown: make(chan struct{}),

        ttcProxyCli: ttcProxyMock,
    }
}

func TestMgr_CheckIfUserInUserGroup(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx context.Context
        uid uint32
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        want1    bool
        want2    bool
        wantErr  bool
    }{
        {
            name: "hit cache",
            initFunc: func() {
                mockBc.EXPECT().GetUserGroupList().Return([]string{"test_group"})
                mockCache.EXPECT().GetUserEntry(gomock.Any(), gomock.Any()).Return(true, &cache.UserEntry{
                    InGroup:  true,
                    FaceAuth: false,
                }, nil)
            },
            args: args{
                ctx: ctx,
                uid: testUid,
            },
            want1:   true,
            wantErr: false,
        },
        {
            name: "miss cache",
            initFunc: func() {
                mockBc.EXPECT().GetUserGroupList().Return([]string{"9774"}).AnyTimes()
                mockCache.EXPECT().GetUserEntry(gomock.Any(), gomock.Any()).Return(false, &cache.UserEntry{
                    InGroup:  false,
                    FaceAuth: false,
                }, nil)

                mockBc.EXPECT().GetDspLpmApiServerHost().Return("http://dataservice.test.in.skyengine.com.cn/dsp-lpm-apiserver")
                ttcProxyMock.EXPECT().GetUserRealNameAuthInfoV2(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)

                mockCache.EXPECT().SetUserEntry(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
            },
            args: args{
                ctx: ctx,
                uid: 2466008,
            },
            want1:   false,
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            got1, got2, err := m.CheckIfUserInUserGroup(tt.args.ctx, tt.args.uid)
            if (err != nil) != tt.wantErr {
                t.Errorf("CheckIfUserInUserGroup() error = %v, wantErr %v", err, tt.wantErr)
                return
            }

            if got1 != tt.want1 {
                t.Errorf("CheckIfUserInUserGroup() got1 = %v, want %v", got1, tt.want1)
            }

            if got2 != tt.want2 {
                t.Errorf("CheckIfUserInUserGroup() got2 = %v, want %v", got2, tt.want2)
            }
        })
    }
}
