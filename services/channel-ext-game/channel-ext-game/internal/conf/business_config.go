package conf

//go:generate quicksilver-cli test interface ../conf
//go:generate mockgen -destination=./mocks/business_config.go -package=mocks golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/conf IBusinessConfManager

import (
    "crypto/md5"
    "encoding/json"
    "fmt"
    "io/ioutil"
    "os"
    "time"

    "gitlab.ttyuyin.com/bizFund/bizFund/pkg/marketid_helper"
    "gitlab.ttyuyin.com/bizFund/bizFund/pkg/protocol"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "math/rand"
    "strconv"
    "strings"
)

const (
    BusinessConfPath = "/data/oss/conf-center/tt/"
    BusinessConfFile = "channel-ext-game.json"
)

var LastConfMd5Sum [md5.Size]byte

type AwardInfo struct {
    RewardId   string `json:"reward_id"`
    RewardType uint32 `json:"reward_type"`
    Worth      uint32 `json:"worth"` // T豆
}

type MsgConf struct {
    Id       uint32 `json:"id"`
    Text     string `json:"text"`
    SendType uint32 `json:"send_type"`
}

type GameAppConf struct {
    AppID     string `json:"app_id"`
    AppSecret string `json:"app_secret"`

    PayAppId         string `json:"pay_app_id"`          // 支付系统appId
    BackSenderAppId  uint32 `json:"back_sender_app_id"`  // 包裹发放业务id
    BackSenderSign   string `json:"back_sender_sign"`    // 包裹发放密钥
    DressSenderAppId uint32 `json:"dress_sender_app_id"` // 装扮发放业务id

    Switch bool `json:"switch"` // 游戏开关

    // 奖励列表
    AwardList       []*AwardInfo `json:"award_list"`        // 奖品列表
    DownloadInfoUrl string       `json:"download_info_url"` // 下载信息地址
    IsUseLinkLoad   bool         `json:"is_use_link_load"`  // 是否直接使用链接加载方式
    H5URL           string       `json:"h5_url"`

    TemplateId2Msg     map[uint32]string   `json:"template_id_2_msg"` // 消息模板id映射
    TemplateId2MsgConf map[uint32]*MsgConf `json:"template_id_2_msg_conf"`
    MsgFrequencyLimit  map[uint32]uint32   `json:"msg_frequency_limit"` // 消息发送频率限制 map[sendType]cnt
}

type BusinessConf struct {
    GameAppConfMap         map[string]*GameAppConf `json:"game_app_conf_map"`
    ReissueMaxIntervalHour uint32                  `json:"reissue_max_interval_hour"` // 补发奖励最大时间间隔(小时)
    DspLpmApiServerHost    string                  `json:"dsp_lpm_apiserver_host"`    // 人群包接口地址

    ChannelBlackList     []uint32 `json:"channel_black_list"`       // 房间黑名单
    ChannelWhiteList     []uint32 `json:"channel_white_list"`       // 房间白名单
    LiveChannelWhiteList []uint32 `json:"channel_white_list_radio"` // 直播间白名单列表

    UserGroupList []string `json:"user_group_list"` // 用户人群包列表

    TestCallBackUidList []uint32 `json:"test_callback_uid_list"`

    ChannelWhiteKeyPrefix string `json:"channel_white_key_prefix"` // 房间白名单key前缀
    UserWhiteKeyPrefix    string `json:"user_white_key_prefix"`    // 用户白名单key前缀

    FeishuRobotUrl          string `json:"feishu_robot_url"`
    NeedToCheckChannelWithe bool   `json:"need_to_check_channel_white"`
}

func (c *GameAppConf) GetAwardInfo(RewardId string, RewardType uint32) (*AwardInfo, bool) {
    for _, award := range c.AwardList {
        if award.RewardId == RewardId && award.RewardType == RewardType {
            return award, true
        }
    }
    return nil, false
}
func (c *BusinessConf) Parse(configFile string) (isChange bool, err error) {
    defer func() {
        if e := recover(); e != nil {
            err = fmt.Errorf("Failed to parse config: %v \n", e)
        }
    }()

    data, err := ioutil.ReadFile(configFile)
    if err != nil {
        return false, err
    }

    md5Sum := md5.Sum(data)
    if md5Sum == LastConfMd5Sum {
        isChange = false
        return
    }

    err = json.Unmarshal(data, &c)
    if err != nil {
        return false, err
    }

    err = c.CheckConf()
    if err != nil {
        return false, err
    }

    LastConfMd5Sum = md5Sum

    log.Infof("BusinessConf : %+v", c)
    return true, nil
}

type BusinessConfManager struct {
    Done chan interface{}
    //mutex sync.RWMutex
    conf *BusinessConf
}

func NewBusinessConfManager() (*BusinessConfManager, error) {
    businessConf := &BusinessConf{}

    businessConfFilePath := BusinessConfPath + BusinessConfFile
    if devBusinessConfPath := os.Getenv("DEV_BUSINESS_CONF_PATH"); devBusinessConfPath != "" {
        businessConfFilePath = devBusinessConfPath + BusinessConfFile
    }
    _, err := businessConf.Parse(businessConfFilePath)
    if err != nil {
        log.Errorf("NewBusinessConfManager fail to Parse BusinessConf err:%v", err)
        return nil, err
    }

    confMgr := &BusinessConfManager{
        conf: businessConf,
        Done: make(chan interface{}),
    }

    go confMgr.Watch(businessConfFilePath)

    return confMgr, nil
}

func (bm *BusinessConfManager) Reload(file string) error {
    businessConf := &BusinessConf{}

    isChange, err := businessConf.Parse(file)
    if err != nil {
        log.Errorf("NewBusinessConfManager fail to Parse BusinessConf err:%v", err)
        return err
    }

    if isChange {
        //bm.mutex.Lock()
        bm.conf = businessConf
        //bm.mutex.Unlock()

        log.Infof("Reload %+v", businessConf)
    }

    return nil
}

func (bm *BusinessConfManager) Watch(file string) {
    log.Infof("Watch start. file:%s", file)

    for {
        select {
        case _, ok := <-bm.Done:
            if !ok {
                log.Infof("Watch done")
                return
            }

        case <-time.After(30 * time.Second):
            log.Debugf("Watch check change")

            err := bm.Reload(file)
            if err != nil {
                log.Errorf("Watch Reload fail. file:%s, err:%v", file, err)
            }
        }
    }
}

func (bm *BusinessConfManager) Close() {
    close(bm.Done)
}

func (c *BusinessConf) CheckConf() error {
    if c.DspLpmApiServerHost == "" {
        return fmt.Errorf("DspLpmApiServerHost is empty")
    }
    return nil
}

// GetGameAppConfByAppid return conf, exist
func (bm *BusinessConfManager) GetGameAppConfByAppid(appid string) (*GameAppConf, bool) {
    if bm.conf == nil || bm.conf.GameAppConfMap == nil {
        return nil, false
    }
    if appConf, ok := bm.conf.GameAppConfMap[appid]; ok {
        return appConf, true
    }
    return nil, false
}

func (bm *BusinessConfManager) GetReissueMaxIntervalHour() uint32 {
    if bm.conf.ReissueMaxIntervalHour == 0 {
        return 24
    }
    return bm.conf.ReissueMaxIntervalHour
}

// GetExtGameVersionSwitch 查询外部游戏版本控制开关状态
func (bm *BusinessConfManager) GetExtGameVersionSwitch(marketId, clientVersion uint32) (bool, error) {
    // 通过马甲包获取最低可见入口的版本号
    minVersion := marketid_helper.Get("channel_ext_game_min_version", marketId, 0)
    if version, ok := parseCliVersion(minVersion); !ok ||
        protocol.ClientVersion(clientVersion) < version {

        // 未达到指定客户端版本，不可见入口
        return false, nil
    }

    return true, nil
}

func parseCliVersion(version string) (protocol.ClientVersion, bool) {
    subStrings := strings.Split(version, ".")
    subVers := make([]uint16, 0)
    for _, subString := range subStrings {
        t, err := strconv.ParseUint(subString, 10, 32)
        if err != nil {
            log.Debugf("parse version fail, version: %s, err: %v", version, err)
            continue
        }
        subVers = append(subVers, uint16(t))
    }
    if 3 != len(subVers) {
        return protocol.ClientVersion(0), false
    }

    iVersion := protocol.ClientVersion(protocol.FormatClientVersion(uint8(subVers[0]), uint8(subVers[1]), subVers[2]))
    return iVersion, true
}

func (bm *BusinessConfManager) GetDspLpmApiServerHost() string {
    return bm.conf.DspLpmApiServerHost
}

func (bm *BusinessConfManager) CheckIfInBlackChannel(channelId uint32) bool {
    if channelId == 0 {
        return false
    }
    for _, id := range bm.conf.ChannelBlackList {
        if channelId == id {
            return true
        }
    }

    return false
}

func (bm *BusinessConfManager) GetWhiteChannelRandomly() uint32 {
    if len(bm.conf.ChannelWhiteList) == 0 {
        return 0
    }
    return bm.conf.ChannelWhiteList[rand.Intn(len(bm.conf.ChannelWhiteList))]
}

func (bm *BusinessConfManager) GetUserGroupList() []string {
    return bm.conf.UserGroupList
}

func (bm *BusinessConfManager) GetDownloadInfoUrl(appid string) string {
    if appConf, ok := bm.GetGameAppConfByAppid(appid); ok {
        return appConf.DownloadInfoUrl
    }
    return ""
}

func (bm *BusinessConfManager) GetGameAppConfMap() map[string]*GameAppConf {
    return bm.conf.GameAppConfMap
}

// GetMsgByTemplateId 根据消息模板id获取msg 文案
func (bm *BusinessConfManager) GetMsgByTemplateId(appId string, templateId uint32) string {
    if appConf, ok := bm.GetGameAppConfByAppid(appId); ok {
        if appConf.TemplateId2Msg == nil {
            return ""
        }
        if msg, ok := appConf.TemplateId2Msg[templateId]; ok {
            return msg
        }
    }
    return ""
}

// GetTemplateId2MsgConf TemplateId2MsgConf
func (bm *BusinessConfManager) GetTemplateId2MsgConf(appId string, templateId uint32) *MsgConf {
    if appConf, ok := bm.GetGameAppConfByAppid(appId); ok {
        if appConf.TemplateId2MsgConf == nil {
            return nil
        }
        if msg, ok := appConf.TemplateId2MsgConf[templateId]; ok {
            return msg
        }
    }
    return nil
}

func (bm *BusinessConfManager) GetChannelWhiteKeyPrefix() string {
    if bm.conf == nil || bm.conf.ChannelWhiteKeyPrefix == "" {
        return "ext_game_cid_white_"
    }
    return bm.conf.ChannelWhiteKeyPrefix
}

func (bm *BusinessConfManager) GetUserWhiteKeyPrefix() string {
    if bm.conf == nil || bm.conf.UserWhiteKeyPrefix == "" {
        return "ext_game_user_white"
    }
    return bm.conf.UserWhiteKeyPrefix
}

func (bm *BusinessConfManager) GetMsgFrequencyLimit(appId string, sendType uint32) uint32 {
    if appConf, ok := bm.GetGameAppConfByAppid(appId); ok {
        if appConf.MsgFrequencyLimit == nil {
            return 0
        }

        if msg, ok := appConf.MsgFrequencyLimit[sendType]; ok {
            return msg
        }
    }

    return 0
}

func (bm *BusinessConfManager) GetFeiShuRobotUrl() string {
    return bm.conf.FeishuRobotUrl
}

func (bm *BusinessConfManager) NeedToCheckChannelWithe() bool {
    return bm.conf.NeedToCheckChannelWithe
}
