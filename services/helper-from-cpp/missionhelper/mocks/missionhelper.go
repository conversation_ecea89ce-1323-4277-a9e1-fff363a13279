// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/helper-from-cpp/missionhelper (interfaces: IMissionHelper)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	app "golang.52tt.com/protocol/app"
)

// MockIMissionHelper is a mock of IMissionHelper interface.
type MockIMissionHelper struct {
	ctrl     *gomock.Controller
	recorder *MockIMissionHelperMockRecorder
}

// MockIMissionHelperMockRecorder is the mock recorder for MockIMissionHelper.
type MockIMissionHelperMockRecorder struct {
	mock *MockIMissionHelper
}

// NewMockIMissionHelper creates a new mock instance.
func NewMockIMissionHelper(ctrl *gomock.Controller) *MockIMissionHelper {
	mock := &MockIMissionHelper{ctrl: ctrl}
	mock.recorder = &MockIMissionHelperMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIMissionHelper) EXPECT() *MockIMissionHelperMockRecorder {
	return m.recorder
}

// GetUserGrowInfo mocks base method.
func (m *MockIMissionHelper) GetUserGrowInfo(arg0 context.Context, arg1 uint32) (*app.GrowInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserGrowInfo", arg0, arg1)
	ret0, _ := ret[0].(*app.GrowInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserGrowInfo indicates an expected call of GetUserGrowInfo.
func (mr *MockIMissionHelperMockRecorder) GetUserGrowInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserGrowInfo", reflect.TypeOf((*MockIMissionHelper)(nil).GetUserGrowInfo), arg0, arg1)
}

// GrowInfoFillRichAndCharm mocks base method.
func (m *MockIMissionHelper) GrowInfoFillRichAndCharm(arg0 context.Context, arg1 uint32, arg2 *app.GrowInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GrowInfoFillRichAndCharm", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// GrowInfoFillRichAndCharm indicates an expected call of GrowInfoFillRichAndCharm.
func (mr *MockIMissionHelperMockRecorder) GrowInfoFillRichAndCharm(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GrowInfoFillRichAndCharm", reflect.TypeOf((*MockIMissionHelper)(nil).GrowInfoFillRichAndCharm), arg0, arg1, arg2)
}
