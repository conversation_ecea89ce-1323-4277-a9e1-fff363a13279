package smashegg

import (
	"context"
	"errors"
	"flag"
	"github.com/tealeg/xlsx"
	"golang.52tt.com/clients/backpack"
	userPresent "golang.52tt.com/clients/userpresent"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	backpackPB "golang.52tt.com/protocol/services/backpacksvr"
	"google.golang.org/grpc"
	"reflect"
	"time"
)

type Gift struct {
	Id    uint32
	Name  string
	Count uint32
	Worth uint32
	Type  string
}

type Statistics struct {
	dao *Dao

	files []string

	backpack *backpack.Client
	present  *userPresent.Client

	gifts map[int64]Gift
}

var statistics *Statistics

// for test
var startTime, endTime string

func init() {
	flag.StringVar(&startTime, "st", "", "start time")
	flag.StringVar(&endTime, "et", "", "end time")
	flag.Parse()

	mysqlConfig := &config.MysqlConfig{
		Host:     "*************",
		Port:     3306,
		Database: "smash_egg",
		UserName: "godman",
		Password: "thegodofman",
		Protocol: "tcp",
		Charset:  "utf8",
	}
	dao, err := newDao(mysqlConfig)
	if err != nil {
		log.Errorf("Failed to init db %v", err)
		return
	}

	statistics = &Statistics{
		dao:   dao,
		gifts: map[int64]Gift{},
	}

	statistics.backpack = backpack.NewClient(grpc.WithBlock())
	statistics.present = userPresent.NewClient(grpc.WithBlock())
	//
	//	//manager.Register("转转数据", statistics)
}

func (s *Statistics) GenExelFile(begin, end time.Time) {
	start := time.Now()

	log.Infof("smash egg GenExelFile")

	ctx := context.Background()

	gens := map[string]func(ctx context.Context, begin, end time.Time) (interface{}, error){
		"转转魔力球购买":     s.genRechargeExelFile,
		"转转中奖":        s.genWinningExelFile,
		"每月1号0点魔力球剩余": s.genRemainExelFile,
	}

	log.Infof("smash egg gens %v", gens)

	file := xlsx.NewFile()

	for name, gen := range gens {
		log.Infof("smash egg start gen %s", name)

		data, err := gen(ctx, begin, end)
		if err != nil {
			log.Errorf("smash egg fail gen %s, err: %v", name, err)
			continue
		}

		log.Infof("smash egg complete gen %s, takes: %v", name, time.Since(start))

		if sheet, err := file.AddSheet(name); err != nil {
			log.Errorf("AddSheet %s fail, err:%v", name, err)
			return
		} else if err = s.addArray(sheet, data); err != nil {
			log.Errorf("addArray %s fail, err:%v", name, err)
			return
		}

		log.Infof("smash egg complete gen %s into file", name)
	}

	//filePath := manager.GetSubFile(begin, "smashegg")

	filePath := time.Now().Format("20060102") + ".xlsx"

	err := file.Save(filePath)
	if err != nil {
		log.Errorf("smash egg file save fail err:%v\n", err)
		return
	}

	s.files = append(s.files, filePath)

	log.Infof("smash egg file has been written, takes: %v", time.Since(start))
}

func (s *Statistics) CheckBaipiao(begin, end time.Time) {
	start := time.Now()

	log.Infof("smash egg CheckBaipiao")

	ctx := context.Background()

	log.Infof("GetRechargeDataMap start!")
	beginRemain, endRemain, err := s.dao.GetRemainDataMap(ctx, begin, end)
	if err != nil {
		log.Errorf("GetRemainData error:%v", err)
		return
	}
	log.Infof("GetRemainDataMap takes: %v", time.Since(start))
	rm, err := s.dao.GetRechargeDataMap(ctx, begin, end.Add(-time.Second))
	if err != nil {
		log.Errorf("GetRechargeDataMap error:%v", err)
		return
	}
	log.Infof("GetRechargeDataMap and GetRemainDataMap takes: %v", time.Since(start))
	wm, err := s.dao.GetWinningDataMap(ctx, begin, end.Add(-time.Second))
	if err != nil {
		log.Errorf("GetWinningDataMap error:%v", err)
		return
	}
	log.Infof("GetRechargeDataMap and GetWinningDataMap and GetRemainData takes: %v", time.Since(start))

	log.Infof("db takes: %v", time.Since(start))
	var beginRemainSum, endRemainSum, totalMiss int64
	for i := range endRemain {
		if beginRemain[i]+rm[i]-wm[i] != endRemain[i] {
			miss := beginRemain[i] + rm[i] - wm[i] - endRemain[i]
			log.Errorf("BaiPiao uid(%d) MayRemain(%d) Recharge(%d) Winning(%d) JuneRemain(%d) miss(%d)\n", i, beginRemain[i], rm[i], wm[i], endRemain[i], miss)
			totalMiss += miss
		}
		beginRemainSum += beginRemain[i]
		endRemainSum += endRemain[i]
	}
	log.Infof("beginRemainSum (%d) endRemainSum (%d) totalMiss(%d)", beginRemainSum, endRemainSum, totalMiss)

	log.Infof("smash egg has been CheckBaipiao, takes: %v", time.Since(start))
}

func (s *Statistics) GetFiles(ts time.Time) []string {
	return s.files
}

func (s *Statistics) genRechargeExelFile(ctx context.Context, begin, end time.Time) (interface{}, error) {
	end = end.Add(-time.Second)

	return s.dao.GetRechargeData(ctx, begin, end)
}

func (s *Statistics) genWinningExelFile(ctx context.Context, begin, end time.Time) (interface{}, error) {
	end = end.Add(-time.Second)

	type giftData struct {
		GiftId    int64  `json:"gift_id" col:"礼物id"`
		GiftName  string `json:"gift_name" col:"礼物名"`
		GiftCount int64  `json:"gift_count" col:"礼物总数量"`
		GiftWorth int64  `json:"gift_worth" col:"礼物总价值（元）"`
		PlayCount int64  `json:"play_count" col:"抽奖总次数"`
	}

	winnings, err := s.dao.GetWinningData(ctx, begin, end)
	if err != nil {
		return nil, err
	}

	// 加载礼物配置
	for _, winning := range winnings {
		if _, ok := s.gifts[winning.PackId]; !ok {
			gift, err := s.loadGift(ctx, uint32(winning.PackId))
			if err != nil {
				return nil, err
			}
			s.gifts[winning.PackId] = *gift
		}
	}

	statistic := make(map[int64]*giftData)
	for _, winning := range winnings {
		gift := s.gifts[winning.PackId]
		if data, ok := statistic[int64(gift.Id)]; !ok {
			statistic[int64(gift.Id)] = &giftData{
				GiftId:    int64(gift.Id),
				GiftName:  gift.Name,
				GiftCount: winning.Num * int64(gift.Count),
				GiftWorth: winning.Num * int64(gift.Count) * int64(gift.Worth),
				PlayCount: winning.Num,
			}
		} else {
			data.GiftCount += winning.Num * int64(gift.Count)
			data.GiftWorth += winning.Num * int64(gift.Count) * int64(gift.Worth)
			data.PlayCount += winning.Num
		}
	}

	gifts := make([]giftData, 0)
	for _, v1 := range statistic {
		gifts = append(gifts, *v1)
	}
	return gifts, nil
}

func (s *Statistics) genRemainExelFile(ctx context.Context, begin, end time.Time) (interface{}, error) {
	return s.dao.GetRemainData(ctx, end)
}

func (s *Statistics) loadGift(ctx context.Context, packId uint32) (gift *Gift, err error) {
	cfg, err := s.backpack.GetPackageItemCfg(ctx, 0, &backpackPB.GetPackageItemCfgReq{
		BgId: packId,
	})
	if err != nil {
		return nil, err
	}

	for i := 0; i < len(cfg.ItemCfgList); i++ {
		pkgItemCfg := cfg.ItemCfgList[0]
		if pkgItemCfg.IsDel {
			continue
		}

		switch backpackPB.PackageItemType(pkgItemCfg.ItemType) {
		case backpackPB.PackageItemType_BACKPACK_PRESENT:
			presentCfg, err := s.present.GetPresentConfigById(ctx, pkgItemCfg.SourceId)
			if err != nil {
				return nil, err
			}
			gift = &Gift{
				Id:    presentCfg.ItemConfig.ItemId,
				Name:  presentCfg.ItemConfig.Name,
				Count: pkgItemCfg.ItemCount,
				Worth: presentCfg.ItemConfig.Price,
				Type:  "礼物",
			}
		case backpackPB.PackageItemType_BACKPACK_LOTTERY_FRAGMENT:
			itemCfgs, err := s.backpack.GetItemCfg(ctx, 0, &backpackPB.GetItemCfgReq{
				ItemType:         uint32(backpackPB.PackageItemType_BACKPACK_LOTTERY_FRAGMENT),
				ItemSourceIdList: []uint32{pkgItemCfg.SourceId},
				GetAll:           false,
			})
			if err != nil {
				return nil, err
			}
			if nil == itemCfgs.ItemCfgList || 1 != len(itemCfgs.ItemCfgList) {
				return nil, errors.New("invalid fragment cfg")
			}

			fragment := backpackPB.LotteryFragmentCfg{}
			err = fragment.Unmarshal(itemCfgs.ItemCfgList[0])
			if err != nil {
				return nil, err
			}
			gift = &Gift{
				Id:    fragment.FragmentId,
				Name:  fragment.FragmentName,
				Count: pkgItemCfg.ItemCount,
				Worth: fragment.FragmentPrice,
				Type:  "碎片",
			}
		default:
			return nil, errors.New("invalid package item type")
		}
	}
	return gift, nil
}

func (s *Statistics) addArray(sheet *xlsx.Sheet, data interface{}) error {
	rv := reflect.ValueOf(data)
	if rv.Kind() != reflect.Slice {
		return errors.New("invalid kind: " + rv.Kind().String())
	}

	if 0 == rv.Len() {
		return nil
	}

	// 获取表头
	{
		head := rv.Index(0)
		if head.Kind() == reflect.Ptr {
			head = head.Elem()
		}

		for i := 0; i < head.Type().NumField(); i++ {
			col := head.Type().Field(i).Tag.Get("col")
			sheet.Cell(0, i).SetValue(col)
		}
	}

	// 获取数据
	for i := 0; i < rv.Len(); i++ {
		tv := rv.Index(i)

		if tv.Kind() == reflect.Ptr {
			tv = tv.Elem()
		}

		if tv.Kind() != reflect.Struct {
			return errors.New("invalid kind: " + tv.Kind().String())
		}

		for j := 0; j < tv.NumField(); j++ {
			sheet.Cell(i+1, j).SetValue(tv.Field(j))
		}
	}
	return nil
}
