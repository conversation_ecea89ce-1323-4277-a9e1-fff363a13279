package model

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/settlement-bill"
	"golang.52tt.com/services/settlement-bill/mysql"
	"golang.52tt.com/services/settlement-bill/utils"
	"google.golang.org/grpc/codes"
	"strings"
)

type Receipt struct {
	db          mysql.IStore
	ReceiptSign string
	ReceiptFile *mysql.SettleReceipt
	ReceiptInfo *mysql.SettleReceiptInfo
}

// 发票购买方信息、纳税人识别号
const (
	ReceiptPurchaserName  = QuWanCorpName
	ReceiptPurchaserTaxNo = "91440106321010429K"
)

// ValidReceiptItems 有效发票项目名称
var ValidReceiptItems = []string{
	"*现代服务*服务费",
	"*现代服务*文化创意服务",
	"*生活服务*文化策划费",
	"*信息技术服务*信息服务费",
	"*信息技术服务费*信息技术服务费",
	"*信息技术服务*服务费",
	"*现代服务*文化服务",
	"*现代服务*文化创意策划",
	"*文化服务*服务费",
	"*文化服务*文化创意服务",
	"*文化服务*文化创意策划",
	"*生活服务*文化服务",
	"*生活服务*文化创意策划",
	"*生活服务*文化创意服务费",
}

// VerifyReceiptItemName 校验发票项目名称
func VerifyReceiptItemName(itemName string) bool {
	return utils.SliceExistString(itemName, ValidReceiptItems)
}

// CreateReceiptSign 创建发票唯一标识 代码+号码
func CreateReceiptSign(receiptCode, receiptNo string) string {
	return strings.Join([]string{receiptCode, receiptNo}, "_")
}

// CalculateReceiptTotalAmount 计算一组发票总金额
func CalculateReceiptTotalAmount(receiptsInfo []*Receipt) (uint64, uint64) {
	receiptAmount := uint64(0)      // 总金额
	receiptValidAmount := uint64(0) // 有效金额，重复提交的发票视为无效金额
	for _, r := range receiptsInfo {
		i := r.ReceiptInfo
		if i == nil {
			continue
		}
		receiptAmount += i.Amount
		if i.VerifyStatus == 1 && i.AuditStatus == pb.ReceiptStatus_ReceiptWaitSubmit {
			receiptValidAmount += i.Amount
		}
	}
	return receiptAmount, receiptValidAmount
}

func NewReceiptInst(db mysql.IStore) *Receipt {
	return &Receipt{
		db: db,
	}
}

// NewReceipt 初始化发票
func NewReceipt(db mysql.IStore, req *pb.RecordReceiptFileReq) (*Receipt, error) {
	receiptSign := CreateReceiptSign(req.GetReceiptCode(), req.GetReceiptNo())
	file := &mysql.SettleReceipt{
		ReceiptId:   req.GetReceiptId(),
		ReceiptSign: receiptSign,
		FileName:    req.GetFileName(),
		GuildOwner:  req.GetUid(),
		Size:        uint32(req.GetSize()),
		ShowSize:    req.GetShowSize(),
		FileClass:   req.GetFileClass(),
	}
	info := &mysql.SettleReceiptInfo{
		ReceiptSign:    receiptSign,
		ReceiptCode:    req.GetReceiptCode(),
		ReceiptNo:      req.GetReceiptNo(),
		TaxRate:        req.GetTaxRate(),
		TaxAmount:      req.GetTaxAmount(),
		Amount:         req.GetAmount(),
		ExTaxAmount:    req.GetExTaxAmount(),
		VCode:          req.GetVCode(),
		Content:        req.GetContent(),
		SellerName:     req.GetSellerName(),
		SellerTaxNo:    req.GetSellerTaxNo(),
		PurchaserName:  req.GetPurchaserName(),
		PurchaserTaxNo: req.GetPurchaserTaxNo(),
		VerifyResult:   req.GetVerifyResult(),
		VerifyStatus:   uint8(1),
		AuditStatus:    pb.ReceiptStatus_ReceiptWaitSubmit,
	}
	log.Infof("NewReceipt sign:%s, file:%+v, info:%+v", receiptSign, file, info)

	return &Receipt{
		db:          db,
		ReceiptSign: receiptSign,
		ReceiptFile: file,
		ReceiptInfo: info,
	}, nil
}

// Save 保存发票
func (r *Receipt) Save(ctx context.Context) error {
	if r == nil || r.db == nil || r.ReceiptSign == "" {
		return nil
	}
	// 记录发票文件
	return r.db.RecordReceiptFile(ctx, r.ReceiptFile, r.ReceiptInfo)
}

// GetReceiptsByFileIds 以文件ID获取发票
func (r *Receipt) GetReceiptsByFileIds(ctx context.Context, fileIds []string) ([]*Receipt, []string, error) {
	if r == nil || r.db == nil {
		return nil, nil, protocol.NewExactServerError(codes.OK, status.ErrSettleDbErr)
	}

	receiptSigns := make([]string, 0)
	receiptFileMap := make(map[string]*mysql.SettleReceipt)
	receiptSignMap := make(map[string]struct{})
	receipts := make([]*Receipt, 0)

	// 白名单允许不提交发票
	if len(fileIds) == 0 {
		return receipts, receiptSigns, nil
	}
	// 以文件ID获取发票文件
	receiptFiles, err := r.db.GetReceipts(ctx, fileIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetReceiptsByFileIds GetReceipts err:%v", err)
		return nil, nil, protocol.NewExactServerError(codes.OK, status.ErrSettleDbErr)
	}

	for _, f := range receiptFiles {
		if f.ReceiptSign != "" {
			if _, ok := receiptSignMap[f.ReceiptSign]; ok {
				// _, receiptNo := ParseReceiptSign(f.ReceiptSign)
				return nil, nil, protocol.NewExactServerError(codes.OK, status.ErrSettleReceiptRepeatErr)
			}
		}
		receiptFileMap[f.ReceiptSign] = f
		receiptSignMap[f.ReceiptSign] = struct{}{}
		receiptSigns = append(receiptSigns, f.ReceiptSign)
	}

	// 以发票唯一标识获取发票信息
	receiptsInfo, err := r.db.GetReceiptsInfo(ctx, receiptSigns)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetReceiptsByFileIds GetReceiptsInfo err:%v", err)
		return nil, nil, protocol.NewExactServerError(codes.OK, status.ErrSettleDbErr)
	}

	for _, i := range receiptsInfo {
		// 检查重复提交发票
		if i.AuditStatus == pb.ReceiptStatus_ReceiptSubmitted {
			return nil, nil, protocol.NewExactServerError(codes.OK, status.ErrSettleReceiptRepeatErr)
		}
		receipts = append(receipts, &Receipt{
			db:          r.db,
			ReceiptSign: i.ReceiptSign,
			ReceiptFile: receiptFileMap[i.ReceiptSign],
			ReceiptInfo: i,
		})
	}
	return receipts, receiptSigns, nil
}

// GetReceiptsSignByFileIds 以文件ID获取发票Sign
func (r *Receipt) GetReceiptsSignByFileIds(ctx context.Context, fileIds []string) ([]string, error) {
	if r == nil || r.db == nil {
		return nil, protocol.NewExactServerError(codes.OK, status.ErrSettleDbErr)
	}

	receiptSigns := make([]string, 0)

	// 白名单允许不提交发票
	if len(fileIds) == 0 {
		return receiptSigns, nil
	}
	// 以文件ID获取发票文件
	receiptFiles, err := r.db.GetReceipts(ctx, fileIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetReceiptsByFileIds GetReceipts err:%v", err)
		return nil, err
	}

	for _, f := range receiptFiles {
		if f.ReceiptSign != "" {
			receiptSigns = append(receiptSigns, f.ReceiptSign)
		}
	}
	return receiptSigns, nil
}
