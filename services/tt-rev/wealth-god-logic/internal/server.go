package internal

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"golang.52tt.com/pkg/protocol"
	protocolgrpc "golang.52tt.com/pkg/protocol/grpc"
	ga "golang.52tt.com/protocol/app"
	"golang.52tt.com/protocol/app/wealth_god_logic"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/protocol/services/demo/echo"
	wealth_god "golang.52tt.com/protocol/services/wealth-god"
	conf2 "golang.52tt.com/services/tt-rev/wealth-god-logic/internal/conf"
	context0 "golang.org/x/net/context"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"time"
)

var (
	errUnimplemented = protocol.NewExactServerError(codes.OK, status.ErrGrpcUnimplemented)
)

type StartConfig struct {
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)
	grpcOpts := []grpc.DialOption{
		//grpc.WithBlock(),
	}
	weathGodCli, _ := wealth_god.NewClient(ctx, grpcOpts...)

	bc, err := conf2.NewBusinessConfManager()
	if err != nil {
		return nil, err
	}

	return &Server{
		bc:           bc,
		wealthGodCli: weathGodCli,
	}, nil
}

type Server struct {
	bc conf2.IBusinessConfManager

	wealthGodCli wealth_god.WealthGodServiceClient
}

func (s *Server) GetWealthGodEntry(c context0.Context, request *wealth_god_logic.GetWealthGodEntryRequest) (*wealth_god_logic.GetWealthGodEntryResponse, error) {
	out := &wealth_god_logic.GetWealthGodEntryResponse{}
	defer func() {
		log.DebugWithCtx(c, "GetWealthGodEntry req:%+v, out:%+v", request, out)
	}()
	commonResp, err := s.wealthGodCli.GetWealthGodCommonCfg(c, &wealth_god.GetWealthGodCommonCfgRequest{})
	if err != nil {
		log.ErrorWithCtx(c, "GetWealthGodEntry call wealthGodCli.GetWealthGodCommonCfg failed, err:%+v", err)
		return out, nil
	}
	svrInfo, ok := protocolgrpc.ServiceInfoFromContext(c)
	if !ok {
		log.ErrorWithCtx(c, "GetWealthGodEntry fail to get serviceInfo, request:%+v", request)
		return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
	}
	now := time.Now()
	if now.Unix() >= commonResp.GetActivityStartTs() && now.Unix() < commonResp.GetActivityEndTs() {
		out.ActivityIsOpen = true
		entryResp, err := s.wealthGodCli.GetWealthGodEntry(c, &wealth_god.GetWealthGodEntryRequest{
			ChannelId: request.GetChannelId(),
			Uid:       svrInfo.UserID,
		})
		if err != nil {
			log.ErrorWithCtx(c, "GetWealthGodEntry call wealthGodCli.GetWealthGodEntry failed, err:%+v", err)
			return out, nil
		}
		out.LastGodEndTs = entryResp.GetLastGodEndTs()
		for _, god := range entryResp.GetCurrentChannelGodList() {
			out.CurrentChannelGodList = append(out.CurrentChannelGodList, &wealth_god_logic.WealthGod{
				GodId:                     god.GetGodId(),
				ChannelId:                 god.GetChannelId(),
				StartTs:                   god.GetStartTs(),
				EndTs:                     god.GetEndTs(),
				MissionEndTs:              god.GetMissionEndTs(),
				IsStayRoomMissionFinished: god.GetIsStayRoomMissionFinished(),
			})
		}

		return out, nil
	}
	return out, nil
}

func (s *Server) GetOneWealthGodChannel(c context0.Context, req *wealth_god_logic.GetOneWealthGodChannelReq) (*wealth_god_logic.GetOneWealthGodChannelResp, error) {
	out := &wealth_god_logic.GetOneWealthGodChannelResp{}
	defer func() {
		log.DebugWithCtx(c, "GetWealthGodActivityInfo req:%+v, out:%+v", req, out)
	}()
	channelResp, err := s.wealthGodCli.GetOneWealthGodChannel(c, &wealth_god.GetOneWealthGodChannelRequest{})
	if err != nil {
		log.ErrorWithCtx(c, "GetWealthGodActivityInfo call wealthGodCli.GetWealthGodCommonCfg failed, err:%+v", err)
		return out, nil
	}
	out.ChannelId = channelResp.GetChannelId()
	return out, nil
}

func (s *Server) GetWealthGodActivityInfo(c context0.Context, request *wealth_god_logic.GetWealthGodActivityInfoRequest) (*wealth_god_logic.GetWealthGodActivityInfoResponse, error) {
	out := &wealth_god_logic.GetWealthGodActivityInfoResponse{}
	defer func() {
		log.DebugWithCtx(c, "GetWealthGodActivityInfo req:%+v, out:%+v", request, out)
	}()
	commonResp, err := s.wealthGodCli.GetWealthGodCommonCfg(c, &wealth_god.GetWealthGodCommonCfgRequest{})
	if err != nil {
		log.ErrorWithCtx(c, "GetWealthGodActivityInfo call wealthGodCli.GetWealthGodCommonCfg failed, err:%+v", err)
		return out, nil
	}
	for _, item := range commonResp.GetTriggerList() {
		out.TriggerList = append(out.TriggerList, &wealth_god_logic.GetWealthGodActivityInfoResponse_WealthGodTrigger{
			TriggerId:   item.TriggerId,
			TriggerType: item.TriggerType,
		})
	}
	conf := s.bc.GetBusinessConf()
	if conf.RuleDesc != nil {
		out.GodBoxTitle = conf.RuleDesc.GodBoxTitle
		out.GodBoxDesc = conf.RuleDesc.GodBoxDesc
		out.GodBoxIcon = conf.RuleDesc.GodBoxIcon
		out.PlayingInstruction = conf.RuleDesc.PlayingInstruction
	}
	return out, nil
}

func (s *Server) GetWealthGodDetail(ctx context.Context, in *wealth_god_logic.GetWealthGodDetailRequest) (*wealth_god_logic.GetWealthGodDetailResponse, error) {
	out := &wealth_god_logic.GetWealthGodDetailResponse{}
	defer func() {
		log.DebugWithCtx(ctx, "GetWealthGodDetail in:%+v, out:%+v", in, out)
	}()

	svrInfo, ok := protocolgrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
	}

	rsp, err := s.wealthGodCli.GetWealthGodDetail(ctx, &wealth_god.GetWealthGodDetailRequest{
		Uid:       svrInfo.UserID,
		ChannelId: in.GetChannelId(),
		GodId:     in.GetGodId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetWealthGodDetail call wealthGodCli.GetWealthGodDetail failed, err:%+v", err)
		return out, err
	}

	out.GodInfo = &wealth_god_logic.WealthGod{
		GodId:        rsp.GetGodInfo().GetGodId(),
		ChannelId:    rsp.GetGodInfo().GetChannelId(),
		StartTs:      rsp.GetGodInfo().GetStartTs(),
		EndTs:        rsp.GetGodInfo().GetEndTs(),
		MissionEndTs: rsp.GetGodInfo().GetMissionEndTs(),
	}
	out.AvailableOpenCnt = rsp.GetAvailableOpenCnt()
	out.GetOpenCnt = rsp.GetGetOpenCnt()
	if rsp.BoxInfo != nil {
		var rewardList []*wealth_god_logic.WealthGodBoxRewardPreview
		for _, reward := range rsp.GetBoxInfo().GetRewardList() {
			rewardList = append(rewardList, &wealth_god_logic.WealthGodBoxRewardPreview{
				IsRare:     reward.IsRare,
				Title:      reward.Title,
				SubTitle:   reward.SubTitle,
				IconUrl:    reward.IconUrl,
				CornerText: reward.CornerText,
			})
		}
		out.BoxInfo = &wealth_god_logic.WealthGodBox{
			BoxType:    rsp.GetBoxInfo().GetBoxType(),
			RewardList: rewardList,
		}
	}
	return out, nil
}

func (s *Server) OpenWealthGodBoxReward(ctx context.Context, in *wealth_god_logic.OpenWealthGodBoxRewardRequest) (*wealth_god_logic.OpenWealthGodBoxRewardResponse, error) {
	out := &wealth_god_logic.OpenWealthGodBoxRewardResponse{}
	defer func() {
		log.InfoWithCtx(ctx, "OpenWealthGodBoxReward in:%+v, out:%+v", in, out)
	}()

	svrInfo, ok := protocolgrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
	}

	rsp, err := s.wealthGodCli.OpenWealthGodBoxReward(ctx, &wealth_god.OpenWealthGodBoxRewardRequest{
		Uid:       svrInfo.UserID,
		ChannelId: in.GetChannelId(),
		GodId:     in.GetGodId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "OpenWealthGodBoxReward call wealthGodCli.OpenWealthGodBoxReward failed, err:%+v", err)
		return out, err
	}

	for _, item := range rsp.GetRewardList() {
		out.RewardList = append(out.RewardList, &wealth_god_logic.WealthGodBoxRewardPreview{
			IsRare:     item.IsRare,
			Title:      item.Title,
			SubTitle:   item.SubTitle,
			IconUrl:    item.IconUrl,
			CornerText: item.CornerText,
		})
	}
	return out, nil
}

func (s *Server) ReportStayRoomMissionFinish(c context0.Context, request *wealth_god_logic.ReportStayRoomMissionFinishRequest) (*wealth_god_logic.ReportStayRoomMissionFinishResponse, error) {
	out := &wealth_god_logic.ReportStayRoomMissionFinishResponse{}
	defer func() {
		log.InfoWithCtx(c, "ReportStayRoomMissionFinish req:%+v, out:%+v", request, out)
	}()

	serviceInfo, ok := protocolgrpc.ServiceInfoFromContext(c)
	if !ok {
		log.ErrorWithCtx(c, "ReportStayRoomMissionFinish get serviceInfo from context failed")
		return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
	}
	if request.GetGodId() == "" {
		log.ErrorWithCtx(c, "ReportStayRoomMissionFinish get godId from request failed, request:%+v", request)
		return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
	}
	_, err := s.wealthGodCli.ReportStayRoomMissionFinish(c, &wealth_god.ReportStayRoomMissionFinishRequest{
		Uid:   serviceInfo.UserID,
		GodId: request.GetGodId(),
	})
	if err != nil {
		log.ErrorWithCtx(c, "ReportStayRoomMissionFinish call wealthGodCli.ReportStayRoomMissionFinish failed, err:%+v", err)
		return out, err
	}
	return out, nil
}

func (s *Server) GetWealthGodCommonCfg(c context0.Context, request *wealth_god_logic.GetWealthGodCommonCfgRequest) (*wealth_god_logic.GetWealthGodCommonCfgResponse, error) {
	out := &wealth_god_logic.GetWealthGodCommonCfgResponse{}
	defer func() {
		log.DebugWithCtx(c, "GetWealthGodCommonCfg req:%+v, out:%+v", request, out)
	}()
	conf := s.bc.GetBusinessConf()
	out.BoxRewardWindowShowDuration = conf.BoxRewardWindowShowDuration
	out.FixedEntryShowDuration = conf.FixedEntryShowDuration
	out.FixedEntryIconUrl = conf.FixedEntryIcon

	if conf.RushInfo != nil {
		out.RushInfo = &ga.RushInfo{
			RushMaxRandTs: conf.RushInfo.RushMaxRandTs,
			RushWaitTs:    conf.RushInfo.RushWaitTs,
			RushType:      uint32(ga.RushInfo_E_RUSH_WEALTH_GOD),
		}
	}
	if conf.DividingGodEntryIcon != nil {
		out.DividingGiftEntryIcon = &wealth_god_logic.WealthEntryIcon{
			BottomEntryIcon:        conf.DividingGodEntryIcon.BottomEntryIcon,
			DynamicWidgetEntryIcon: conf.DividingGodEntryIcon.DynamicWidgetEntryIcon,
		}
	}

	if conf.DoingMissionEntryIcon != nil {
		out.DoingMissionEntryIcon = &wealth_god_logic.WealthEntryIcon{
			BottomEntryIcon:        conf.DoingMissionEntryIcon.BottomEntryIcon,
			DynamicWidgetEntryIcon: conf.DoingMissionEntryIcon.DynamicWidgetEntryIcon,
		}
	}

	if conf.NormalEntryIcon != nil {
		out.NormalEntryIcon = &wealth_god_logic.WealthEntryIcon{
			BottomEntryIcon:        conf.NormalEntryIcon.BottomEntryIcon,
			DynamicWidgetEntryIcon: conf.NormalEntryIcon.DynamicWidgetEntryIcon,
		}
	}

	if conf.OtherRoomGodEntryIcon != nil {
		out.OtherRoomGodEntryIcon = &wealth_god_logic.WealthEntryIcon{
			BottomEntryIcon:        conf.OtherRoomGodEntryIcon.BottomEntryIcon,
			DynamicWidgetEntryIcon: conf.OtherRoomGodEntryIcon.DynamicWidgetEntryIcon,
		}
	}

	for k, v := range conf.InnerWealthBoxOpenAnimationMap {
		if out.BoxOpenAnimationMap == nil {
			out.BoxOpenAnimationMap = make(map[uint32]*wealth_god_logic.WealthGodCommonAnimation)
		}
		if openAnimation := v.OpenAnimation; openAnimation != nil {
			out.BoxOpenAnimationMap[k] = &wealth_god_logic.WealthGodCommonAnimation{
				AnimationUrl: openAnimation.AnimationUrl,
				AnimationMd5: openAnimation.AnimationMd5,
			}
		}

		if out.BoxWaitOpenAnimationMap == nil {
			out.BoxWaitOpenAnimationMap = make(map[uint32]*wealth_god_logic.WealthGodCommonAnimation)
		}
		if waitOpenAnimation := v.WaitOpenAnimation; waitOpenAnimation != nil {
			out.BoxWaitOpenAnimationMap[k] = &wealth_god_logic.WealthGodCommonAnimation{
				AnimationUrl: waitOpenAnimation.AnimationUrl,
				AnimationMd5: waitOpenAnimation.AnimationMd5,
			}
		}
	}

	if conf.WealthGodShowAnimation != nil {
		out.GodShowAnimation = &wealth_god_logic.WealthGodCommonAnimation{
			AnimationUrl: conf.WealthGodShowAnimation.AnimationUrl,
			AnimationMd5: conf.WealthGodShowAnimation.AnimationMd5,
		}
	}
	if conf.WealthBoxChangeAnimation != nil {
		out.BoxChangingAnimation = &wealth_god_logic.WealthGodCommonAnimation{
			AnimationUrl: conf.WealthBoxChangeAnimation.AnimationUrl,
			AnimationMd5: conf.WealthBoxChangeAnimation.AnimationMd5,
		}
	}

	if conf.BoxCountDownAnimation != nil {
		out.BoxCountDownAnimation = &wealth_god_logic.WealthGodCommonAnimation{
			AnimationUrl: conf.BoxCountDownAnimation.AnimationUrl,
			AnimationMd5: conf.BoxCountDownAnimation.AnimationMd5,
		}
	}

	out.GodEndPictureUrl = conf.GodEndPictureUrl

	resp, err := s.wealthGodCli.GetWealthGodCommonCfg(c, &wealth_god.GetWealthGodCommonCfgRequest{})
	if err != nil {
		log.ErrorWithCtx(c, "GetWealthGodCommonCfg call wealthGodCli.GetWealthGodCommonCfg failed, err:%+v, req:%+v", err, request)
		return out, err
	}
	out.NeedStayRoomSecond = resp.GetNeedStayRoomSecond()

	return out, nil
}

func (s *Server) ShutDown() {}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}
