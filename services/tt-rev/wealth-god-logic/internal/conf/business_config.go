package conf

//go:generate quicksilver-cli test interface ../conf
//go:generate mockgen -destination=./mocks/business_config.go -package=mocks golang.52tt.com/services/tt-rev/wealth-god-logic/internal/conf IBusinessConfManager

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"strconv"
	"time"

	"gitlab.ttyuyin.com/tt-infra/tyr/log"
)

const (
	BusinessConfPath = "/data/oss/conf-center/tt/"
	BusinessConfFile = "wealth-god-logic.json"
)

var LastConfMd5Sum [md5.Size]byte

type RushInfo struct {
	RushMaxRandTs uint32 `json:"rush_max_rand_ts"` // rush 最大随机值 秒
	RushWaitTs    uint32 `json:"rush_wait_ts"`     // rush 最大等待时间 秒
}

type WealthEntryIcon struct {
	BottomEntryIcon        string `json:"bottom_entry_icon"`         // 底部入口图标
	DynamicWidgetEntryIcon string `json:"dynamic_widget_entry_icon"` // 动态widget入口图标
}

type WealthGodCommonAnimation struct {
	AnimationUrl string `json:"animation_url"` // 动画地址
	AnimationMd5 string `json:"animation_md5"` // 动画MD5
}

type BoxAnimation struct {
	OpenAnimation     *WealthGodCommonAnimation `json:"open_animation"`
	WaitOpenAnimation *WealthGodCommonAnimation `json:"wait_open_animation"` // 等待开启动画
}

type BusinessConf struct {
	RushInfo                       *RushInfo                 `json:"rush_info"`
	NormalEntryIcon                *WealthEntryIcon          `json:"normal_entry_icon"`             // 普通入口图标
	DividingGodEntryIcon           *WealthEntryIcon          `json:"dividing_god_entry_icon"`       // 当前房间神仙入口图标
	OtherRoomGodEntryIcon          *WealthEntryIcon          `json:"other_room_god_entry_icon"`     // 其他房间神仙入口图标
	DoingMissionEntryIcon          *WealthEntryIcon          `json:"doing_mission_entry_icon"`      // 进行中任务入口图标
	WealthBoxOpenAnimationMap      map[string]*BoxAnimation  `json:"wealth_box_animation_map"`      // 宝箱开启动画配置，key为宝箱等级
	InnerWealthBoxOpenAnimationMap map[uint32]*BoxAnimation  `json:"-"`                             // 内部宝箱开启动画配置，key为宝箱等级
	FixedEntryShowDuration         uint32                    `json:"fixed_entry_show_second"`       // 固定入口展示时间，单位秒
	FixedEntryIcon                 string                    `json:"fixed_entry_icon"`              // 固定入口图标
	BoxRewardWindowShowDuration    uint32                    `json:"box_reward_window_show_second"` // 宝箱奖励窗口展示时间，单位秒
	RuleDesc                       *RuleDesc                 `json:"rule_desc"`                     // 玩法说明
	BoxCountDownAnimation          *WealthGodCommonAnimation `json:"box_count_down_animation"`      // 宝箱倒计时动画配置
	WealthGodShowAnimation         *WealthGodCommonAnimation `json:"wealth_god_show_animation"`     // 财神降临动画配置
	GodEndPictureUrl               string                    `json:"god_end_picture_url"`           // 财神降临结束切图
	// box_changing_animation
	WealthBoxChangeAnimation *WealthGodCommonAnimation `json:"wealth_box_change_animation"` // 宝箱变化动画配置
	// box_wait_open_animation_map 	// 内部宝箱等待开启动画配置，key为宝箱等级
}

type RuleDesc struct {
	PlayingInstruction string `json:"playing_instruction"` // 玩法说明
	GodBoxTitle        string `json:"god_box_title"`       // 财神宝箱标题
	GodBoxDesc         string `json:"god_box_desc"`        // 财神宝箱描述
	GodBoxIcon         string `json:"god_box_icon"`        // 财神宝箱图标
}

func (c *BusinessConf) Parse(configFile string) (isChange bool, err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("Failed to parse config: %v \n", e)
		}
	}()

	data, err := ioutil.ReadFile(configFile)
	if err != nil {
		return false, err
	}

	md5Sum := md5.Sum(data)
	if md5Sum == LastConfMd5Sum {
		isChange = false
		return
	}

	err = json.Unmarshal(data, &c)
	if err != nil {
		return false, err
	}

	innerBoxOpenAnimationMap := make(map[uint32]*BoxAnimation)
	for k, v := range c.WealthBoxOpenAnimationMap {
		// 将字符串key转换为uint32
		level, err := strconv.ParseUint(k, 10, 32)
		if err != nil {
			log.Errorf("Reload fail to Parse BusinessConf err:%v", err)
			continue
		}
		innerBoxOpenAnimationMap[uint32(level)] = v
	}
	c.InnerWealthBoxOpenAnimationMap = innerBoxOpenAnimationMap
	log.Debugf("Reload BusinessConf InnerWealthBoxOpenAnimationMap: %+v", c.InnerWealthBoxOpenAnimationMap)
	err = c.CheckConf()
	if err != nil {
		return false, err
	}

	LastConfMd5Sum = md5Sum

	log.Infof("BusinessConf : %+v", c)
	return true, nil
}

type BusinessConfManager struct {
	Done chan interface{}
	//mutex sync.RWMutex
	conf *BusinessConf
}

func NewBusinessConfManager() (*BusinessConfManager, error) {
	businessConf := &BusinessConf{}

	businessConfFilePath := BusinessConfPath + BusinessConfFile
	if devBusinessConfPath := os.Getenv("DEV_BUSINESS_CONF_PATH"); devBusinessConfPath != "" {
		businessConfFilePath = devBusinessConfPath + BusinessConfFile
	}
	_, err := businessConf.Parse(businessConfFilePath)
	if err != nil {
		log.Errorf("NewBusinessConfManager fail to Parse BusinessConf err:%v", err)
		return nil, err
	}

	confMgr := &BusinessConfManager{
		conf: businessConf,
		Done: make(chan interface{}),
	}

	go confMgr.Watch(businessConfFilePath)

	return confMgr, nil
}

func (bm *BusinessConfManager) Reload(file string) error {
	businessConf := &BusinessConf{}

	isChange, err := businessConf.Parse(file)
	if err != nil {
		log.Errorf("NewBusinessConfManager fail to Parse BusinessConf err:%v", err)
		return err
	}

	if isChange {
		//bm.mutex.Lock()

		bm.conf = businessConf

		//bm.mutex.Unlock()

		log.Infof("Reload %+v", businessConf)
	}

	return nil
}

func (bm *BusinessConfManager) Watch(file string) {
	log.Infof("Watch start. file:%s", file)

	for {
		select {
		case _, ok := <-bm.Done:
			if !ok {
				log.Infof("Watch done")
				return
			}

		case <-time.After(30 * time.Second):
			log.Debugf("Watch check change")

			err := bm.Reload(file)
			if err != nil {
				log.Errorf("Watch Reload fail. file:%s, err:%v", file, err)
			}
		}
	}
}

func (bm *BusinessConfManager) Close() {
	close(bm.Done)
}

func (c *BusinessConf) CheckConf() error {
	return nil
}

func (bm *BusinessConfManager) GetBusinessConf() *BusinessConf {
	return bm.conf
}
