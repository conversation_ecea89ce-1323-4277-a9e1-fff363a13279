// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/tt-rev/wealth-god-logic/internal/conf (interfaces: IBusinessConfManager)

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	conf "golang.52tt.com/services/tt-rev/wealth-god-logic/internal/conf"
)

// MockIBusinessConfManager is a mock of IBusinessConfManager interface.
type MockIBusinessConfManager struct {
	ctrl     *gomock.Controller
	recorder *MockIBusinessConfManagerMockRecorder
}

// MockIBusinessConfManagerMockRecorder is the mock recorder for MockIBusinessConfManager.
type MockIBusinessConfManagerMockRecorder struct {
	mock *MockIBusinessConfManager
}

// NewMockIBusinessConfManager creates a new mock instance.
func NewMockIBusinessConfManager(ctrl *gomock.Controller) *MockIBusinessConfManager {
	mock := &MockIBusinessConfManager{ctrl: ctrl}
	mock.recorder = &MockIBusinessConfManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIBusinessConfManager) EXPECT() *MockIBusinessConfManagerMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockIBusinessConfManager) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIBusinessConfManagerMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIBusinessConfManager)(nil).Close))
}

// GetBusinessConf mocks base method.
func (m *MockIBusinessConfManager) GetBusinessConf() *conf.BusinessConf {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBusinessConf")
	ret0, _ := ret[0].(*conf.BusinessConf)
	return ret0
}

// GetBusinessConf indicates an expected call of GetBusinessConf.
func (mr *MockIBusinessConfManagerMockRecorder) GetBusinessConf() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBusinessConf", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetBusinessConf))
}

// Reload mocks base method.
func (m *MockIBusinessConfManager) Reload(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Reload", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Reload indicates an expected call of Reload.
func (mr *MockIBusinessConfManagerMockRecorder) Reload(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Reload", reflect.TypeOf((*MockIBusinessConfManager)(nil).Reload), arg0)
}

// Watch mocks base method.
func (m *MockIBusinessConfManager) Watch(arg0 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Watch", arg0)
}

// Watch indicates an expected call of Watch.
func (mr *MockIBusinessConfManagerMockRecorder) Watch(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Watch", reflect.TypeOf((*MockIBusinessConfManager)(nil).Watch), arg0)
}
