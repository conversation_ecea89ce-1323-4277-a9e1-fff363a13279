// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/tt-rev/esport/esport-skill/internal/store (interfaces: IStore)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	esport_skill "golang.52tt.com/protocol/services/esport-skill"
	store "golang.52tt.com/services/tt-rev/esport/esport-skill/internal/store"
)

// MockIStore is a mock of IStore interface.
type MockIStore struct {
	ctrl     *gomock.Controller
	recorder *MockIStoreMockRecorder
}

// MockIStoreMockRecorder is the mock recorder for MockIStore.
type MockIStoreMockRecorder struct {
	mock *MockIStore
}

// NewMockIStore creates a new mock instance.
func NewMockIStore(ctrl *gomock.Controller) *MockIStore {
	mock := &MockIStore{ctrl: ctrl}
	mock.recorder = &MockIStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIStore) EXPECT() *MockIStoreMockRecorder {
	return m.recorder
}

// AddEsportGameConfig mocks base method.
func (m *MockIStore) AddEsportGameConfig(arg0 context.Context, arg1 *esport_skill.AddEsportGameConfigRequest) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddEsportGameConfig", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddEsportGameConfig indicates an expected call of AddEsportGameConfig.
func (mr *MockIStoreMockRecorder) AddEsportGameConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddEsportGameConfig", reflect.TypeOf((*MockIStore)(nil).AddEsportGameConfig), arg0, arg1)
}

// AddUserSkill mocks base method.
func (m *MockIStore) AddUserSkill(arg0 context.Context, arg1 *store.UserSkillInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUserSkill", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddUserSkill indicates an expected call of AddUserSkill.
func (mr *MockIStoreMockRecorder) AddUserSkill(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserSkill", reflect.TypeOf((*MockIStore)(nil).AddUserSkill), arg0, arg1)
}

// AddUserSkillAuditHistory mocks base method.
func (m *MockIStore) AddUserSkillAuditHistory(arg0 context.Context, arg1 *store.UserSkillAuditHistory) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUserSkillAuditHistory", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddUserSkillAuditHistory indicates an expected call of AddUserSkillAuditHistory.
func (mr *MockIStoreMockRecorder) AddUserSkillAuditHistory(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserSkillAuditHistory", reflect.TypeOf((*MockIStore)(nil).AddUserSkillAuditHistory), arg0, arg1)
}

// AddUserSkillFreezeHistory mocks base method.
func (m *MockIStore) AddUserSkillFreezeHistory(arg0 context.Context, arg1 *store.UserSkillFreezeHistoryEntity) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUserSkillFreezeHistory", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddUserSkillFreezeHistory indicates an expected call of AddUserSkillFreezeHistory.
func (mr *MockIStoreMockRecorder) AddUserSkillFreezeHistory(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserSkillFreezeHistory", reflect.TypeOf((*MockIStore)(nil).AddUserSkillFreezeHistory), arg0, arg1)
}

// BatGetUserSkillFreezeStatus mocks base method.
func (m *MockIStore) BatGetUserSkillFreezeStatus(arg0 context.Context, arg1 []uint32) ([]*store.UserSkillFreezeStatusEntity, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetUserSkillFreezeStatus", arg0, arg1)
	ret0, _ := ret[0].([]*store.UserSkillFreezeStatusEntity)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetUserSkillFreezeStatus indicates an expected call of BatGetUserSkillFreezeStatus.
func (mr *MockIStoreMockRecorder) BatGetUserSkillFreezeStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetUserSkillFreezeStatus", reflect.TypeOf((*MockIStore)(nil).BatGetUserSkillFreezeStatus), arg0, arg1)
}

// BatUpsertUserSkillFreezeStatus mocks base method.
func (m *MockIStore) BatUpsertUserSkillFreezeStatus(arg0 context.Context, arg1 []*store.UserSkillFreezeStatusEntity) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatUpsertUserSkillFreezeStatus", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatUpsertUserSkillFreezeStatus indicates an expected call of BatUpsertUserSkillFreezeStatus.
func (mr *MockIStoreMockRecorder) BatUpsertUserSkillFreezeStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatUpsertUserSkillFreezeStatus", reflect.TypeOf((*MockIStore)(nil).BatUpsertUserSkillFreezeStatus), arg0, arg1)
}

// BatchAddRenownedPlayer mocks base method.
func (m *MockIStore) BatchAddRenownedPlayer(arg0 context.Context, arg1 []*store.RenownedPlayer) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchAddRenownedPlayer", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchAddRenownedPlayer indicates an expected call of BatchAddRenownedPlayer.
func (mr *MockIStoreMockRecorder) BatchAddRenownedPlayer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchAddRenownedPlayer", reflect.TypeOf((*MockIStore)(nil).BatchAddRenownedPlayer), arg0, arg1)
}

// BatchCheckCoachHasGame mocks base method.
func (m *MockIStore) BatchCheckCoachHasGame(arg0 context.Context, arg1 []uint32, arg2 uint32) (map[uint32]bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCheckCoachHasGame", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[uint32]bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCheckCoachHasGame indicates an expected call of BatchCheckCoachHasGame.
func (mr *MockIStoreMockRecorder) BatchCheckCoachHasGame(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCheckCoachHasGame", reflect.TypeOf((*MockIStore)(nil).BatchCheckCoachHasGame), arg0, arg1, arg2)
}

// BatchCreateIssuanceRecord mocks base method.
func (m *MockIStore) BatchCreateIssuanceRecord(arg0 context.Context, arg1 []*store.IssuanceRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCreateIssuanceRecord", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchCreateIssuanceRecord indicates an expected call of BatchCreateIssuanceRecord.
func (mr *MockIStoreMockRecorder) BatchCreateIssuanceRecord(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreateIssuanceRecord", reflect.TypeOf((*MockIStore)(nil).BatchCreateIssuanceRecord), arg0, arg1)
}

// BatchDeleteRenownedPlayers mocks base method.
func (m *MockIStore) BatchDeleteRenownedPlayers(arg0 context.Context, arg1 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDeleteRenownedPlayers", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDeleteRenownedPlayers indicates an expected call of BatchDeleteRenownedPlayers.
func (mr *MockIStoreMockRecorder) BatchDeleteRenownedPlayers(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeleteRenownedPlayers", reflect.TypeOf((*MockIStore)(nil).BatchDeleteRenownedPlayers), arg0, arg1)
}

// BatchGetAuditSkill mocks base method.
func (m *MockIStore) BatchGetAuditSkill(arg0 context.Context, arg1 *esport_skill.BatchGetAuditSkillRequest) ([]*store.UserSkillAuditHistory, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetAuditSkill", arg0, arg1)
	ret0, _ := ret[0].([]*store.UserSkillAuditHistory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetAuditSkill indicates an expected call of BatchGetAuditSkill.
func (mr *MockIStoreMockRecorder) BatchGetAuditSkill(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAuditSkill", reflect.TypeOf((*MockIStore)(nil).BatchGetAuditSkill), arg0, arg1)
}

// BatchGetBasePriceSetting mocks base method.
func (m *MockIStore) BatchGetBasePriceSetting(arg0 context.Context, arg1, arg2 []uint32) ([]*store.CoachBasePriceSetting, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetBasePriceSetting", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*store.CoachBasePriceSetting)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetBasePriceSetting indicates an expected call of BatchGetBasePriceSetting.
func (mr *MockIStoreMockRecorder) BatchGetBasePriceSetting(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetBasePriceSetting", reflect.TypeOf((*MockIStore)(nil).BatchGetBasePriceSetting), arg0, arg1, arg2)
}

// BatchGetGameInfoByNames mocks base method.
func (m *MockIStore) BatchGetGameInfoByNames(arg0 context.Context, arg1 []string) ([]*esport_skill.SimpleGameInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetGameInfoByNames", arg0, arg1)
	ret0, _ := ret[0].([]*esport_skill.SimpleGameInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetGameInfoByNames indicates an expected call of BatchGetGameInfoByNames.
func (mr *MockIStoreMockRecorder) BatchGetGameInfoByNames(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetGameInfoByNames", reflect.TypeOf((*MockIStore)(nil).BatchGetGameInfoByNames), arg0, arg1)
}

// BatchGetIssuanceRecordByCoachIdsAndLabelIds mocks base method.
func (m *MockIStore) BatchGetIssuanceRecordByCoachIdsAndLabelIds(arg0 context.Context, arg1, arg2 []uint32) ([]*store.IssuanceRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetIssuanceRecordByCoachIdsAndLabelIds", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*store.IssuanceRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetIssuanceRecordByCoachIdsAndLabelIds indicates an expected call of BatchGetIssuanceRecordByCoachIdsAndLabelIds.
func (mr *MockIStoreMockRecorder) BatchGetIssuanceRecordByCoachIdsAndLabelIds(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetIssuanceRecordByCoachIdsAndLabelIds", reflect.TypeOf((*MockIStore)(nil).BatchGetIssuanceRecordByCoachIdsAndLabelIds), arg0, arg1, arg2)
}

// BatchGetLabel mocks base method.
func (m *MockIStore) BatchGetLabel(arg0 context.Context, arg1 []uint32) ([]*store.Label, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetLabel", arg0, arg1)
	ret0, _ := ret[0].([]*store.Label)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetLabel indicates an expected call of BatchGetLabel.
func (mr *MockIStoreMockRecorder) BatchGetLabel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetLabel", reflect.TypeOf((*MockIStore)(nil).BatchGetLabel), arg0, arg1)
}

// BatchGetLabelByIds mocks base method.
func (m *MockIStore) BatchGetLabelByIds(arg0 context.Context, arg1 []uint32) (map[uint32]*store.Label, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetLabelByIds", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]*store.Label)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetLabelByIds indicates an expected call of BatchGetLabelByIds.
func (mr *MockIStoreMockRecorder) BatchGetLabelByIds(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetLabelByIds", reflect.TypeOf((*MockIStore)(nil).BatchGetLabelByIds), arg0, arg1)
}

// BatchGetSkillsById mocks base method.
func (m *MockIStore) BatchGetSkillsById(arg0 context.Context, arg1 []string) (map[string]*store.UserSkillInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetSkillsById", arg0, arg1)
	ret0, _ := ret[0].(map[string]*store.UserSkillInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetSkillsById indicates an expected call of BatchGetSkillsById.
func (mr *MockIStoreMockRecorder) BatchGetSkillsById(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetSkillsById", reflect.TypeOf((*MockIStore)(nil).BatchGetSkillsById), arg0, arg1)
}

// BatchGetSpecialLabelIssuanceRecords mocks base method.
func (m *MockIStore) BatchGetSpecialLabelIssuanceRecords(arg0 context.Context, arg1, arg2 []uint32) ([]*store.IssuanceRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetSpecialLabelIssuanceRecords", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*store.IssuanceRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetSpecialLabelIssuanceRecords indicates an expected call of BatchGetSpecialLabelIssuanceRecords.
func (mr *MockIStoreMockRecorder) BatchGetSpecialLabelIssuanceRecords(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetSpecialLabelIssuanceRecords", reflect.TypeOf((*MockIStore)(nil).BatchGetSpecialLabelIssuanceRecords), arg0, arg1, arg2)
}

// BatchGetUserGameSpecialLabel mocks base method.
func (m *MockIStore) BatchGetUserGameSpecialLabel(arg0 context.Context, arg1 uint32, arg2 []uint32) (map[uint32][]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserGameSpecialLabel", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[uint32][]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserGameSpecialLabel indicates an expected call of BatchGetUserGameSpecialLabel.
func (mr *MockIStoreMockRecorder) BatchGetUserGameSpecialLabel(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserGameSpecialLabel", reflect.TypeOf((*MockIStore)(nil).BatchGetUserGameSpecialLabel), arg0, arg1, arg2)
}

// BatchGetUserRenownedInfoByCoachIdAndGameIds mocks base method.
func (m *MockIStore) BatchGetUserRenownedInfoByCoachIdAndGameIds(arg0 context.Context, arg1 uint32, arg2 []uint32) ([]*store.RenownedPlayer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserRenownedInfoByCoachIdAndGameIds", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*store.RenownedPlayer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserRenownedInfoByCoachIdAndGameIds indicates an expected call of BatchGetUserRenownedInfoByCoachIdAndGameIds.
func (mr *MockIStoreMockRecorder) BatchGetUserRenownedInfoByCoachIdAndGameIds(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserRenownedInfoByCoachIdAndGameIds", reflect.TypeOf((*MockIStore)(nil).BatchGetUserRenownedInfoByCoachIdAndGameIds), arg0, arg1, arg2)
}

// BatchGetUserRenownedInfoByCoachIdsAndGameId mocks base method.
func (m *MockIStore) BatchGetUserRenownedInfoByCoachIdsAndGameId(arg0 context.Context, arg1 []uint32, arg2 uint32) ([]*store.RenownedPlayer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserRenownedInfoByCoachIdsAndGameId", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*store.RenownedPlayer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserRenownedInfoByCoachIdsAndGameId indicates an expected call of BatchGetUserRenownedInfoByCoachIdsAndGameId.
func (mr *MockIStoreMockRecorder) BatchGetUserRenownedInfoByCoachIdsAndGameId(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserRenownedInfoByCoachIdsAndGameId", reflect.TypeOf((*MockIStore)(nil).BatchGetUserRenownedInfoByCoachIdsAndGameId), arg0, arg1, arg2)
}

// BatchGetUserSkills mocks base method.
func (m *MockIStore) BatchGetUserSkills(arg0 context.Context, arg1 []uint32) ([]*store.UserSkillInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserSkills", arg0, arg1)
	ret0, _ := ret[0].([]*store.UserSkillInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserSkills indicates an expected call of BatchGetUserSkills.
func (mr *MockIStoreMockRecorder) BatchGetUserSkills(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserSkills", reflect.TypeOf((*MockIStore)(nil).BatchGetUserSkills), arg0, arg1)
}

// BatchGetUserSpecialLabel mocks base method.
func (m *MockIStore) BatchGetUserSpecialLabel(arg0 context.Context, arg1 []uint32) (map[uint32]map[uint32][]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserSpecialLabel", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]map[uint32][]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserSpecialLabel indicates an expected call of BatchGetUserSpecialLabel.
func (mr *MockIStoreMockRecorder) BatchGetUserSpecialLabel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserSpecialLabel", reflect.TypeOf((*MockIStore)(nil).BatchGetUserSpecialLabel), arg0, arg1)
}

// BatchSetBasePriceSetting mocks base method.
func (m *MockIStore) BatchSetBasePriceSetting(arg0 context.Context, arg1 []*store.CoachBasePriceSetting) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchSetBasePriceSetting", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchSetBasePriceSetting indicates an expected call of BatchSetBasePriceSetting.
func (mr *MockIStoreMockRecorder) BatchSetBasePriceSetting(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSetBasePriceSetting", reflect.TypeOf((*MockIStore)(nil).BatchSetBasePriceSetting), arg0, arg1)
}

// CheckAddLabelOrder mocks base method.
func (m *MockIStore) CheckAddLabelOrder(arg0 context.Context, arg1 esport_skill.LabelType, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckAddLabelOrder", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckAddLabelOrder indicates an expected call of CheckAddLabelOrder.
func (mr *MockIStoreMockRecorder) CheckAddLabelOrder(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckAddLabelOrder", reflect.TypeOf((*MockIStore)(nil).CheckAddLabelOrder), arg0, arg1, arg2)
}

// CheckUpdateLabelOrder mocks base method.
func (m *MockIStore) CheckUpdateLabelOrder(arg0 context.Context, arg1 esport_skill.LabelType, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckUpdateLabelOrder", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckUpdateLabelOrder indicates an expected call of CheckUpdateLabelOrder.
func (mr *MockIStoreMockRecorder) CheckUpdateLabelOrder(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUpdateLabelOrder", reflect.TypeOf((*MockIStore)(nil).CheckUpdateLabelOrder), arg0, arg1, arg2, arg3)
}

// Close mocks base method.
func (m *MockIStore) Close(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockIStoreMockRecorder) Close(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIStore)(nil).Close), arg0)
}

// CountIssuanceRecords mocks base method.
func (m *MockIStore) CountIssuanceRecords(arg0 context.Context, arg1 []uint32, arg2 uint32, arg3, arg4 int64, arg5 store.IssuanceRecordStatus, arg6 store.LabelType) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountIssuanceRecords", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountIssuanceRecords indicates an expected call of CountIssuanceRecords.
func (mr *MockIStoreMockRecorder) CountIssuanceRecords(arg0, arg1, arg2, arg3, arg4, arg5, arg6 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountIssuanceRecords", reflect.TypeOf((*MockIStore)(nil).CountIssuanceRecords), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// CountLabels mocks base method.
func (m *MockIStore) CountLabels(arg0 context.Context, arg1 uint32, arg2 store.LabelType, arg3 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountLabels", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountLabels indicates an expected call of CountLabels.
func (mr *MockIStoreMockRecorder) CountLabels(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountLabels", reflect.TypeOf((*MockIStore)(nil).CountLabels), arg0, arg1, arg2, arg3)
}

// CountRenownedPlayers mocks base method.
func (m *MockIStore) CountRenownedPlayers(arg0 context.Context, arg1 uint32, arg2 []uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountRenownedPlayers", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountRenownedPlayers indicates an expected call of CountRenownedPlayers.
func (mr *MockIStoreMockRecorder) CountRenownedPlayers(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountRenownedPlayers", reflect.TypeOf((*MockIStore)(nil).CountRenownedPlayers), arg0, arg1, arg2)
}

// CreateIssuanceRecord mocks base method.
func (m *MockIStore) CreateIssuanceRecord(arg0 context.Context, arg1 *store.IssuanceRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateIssuanceRecord", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateIssuanceRecord indicates an expected call of CreateIssuanceRecord.
func (mr *MockIStoreMockRecorder) CreateIssuanceRecord(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateIssuanceRecord", reflect.TypeOf((*MockIStore)(nil).CreateIssuanceRecord), arg0, arg1)
}

// CreateLabel mocks base method.
func (m *MockIStore) CreateLabel(arg0 context.Context, arg1 *store.Label) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateLabel", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateLabel indicates an expected call of CreateLabel.
func (mr *MockIStoreMockRecorder) CreateLabel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateLabel", reflect.TypeOf((*MockIStore)(nil).CreateLabel), arg0, arg1)
}

// DelSkillByGameId mocks base method.
func (m *MockIStore) DelSkillByGameId(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelSkillByGameId", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelSkillByGameId indicates an expected call of DelSkillByGameId.
func (mr *MockIStoreMockRecorder) DelSkillByGameId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelSkillByGameId", reflect.TypeOf((*MockIStore)(nil).DelSkillByGameId), arg0, arg1)
}

// DelUserSkill mocks base method.
func (m *MockIStore) DelUserSkill(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelUserSkill", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelUserSkill indicates an expected call of DelUserSkill.
func (mr *MockIStoreMockRecorder) DelUserSkill(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserSkill", reflect.TypeOf((*MockIStore)(nil).DelUserSkill), arg0, arg1, arg2)
}

// DeleteAuditRecordByAuditToken mocks base method.
func (m *MockIStore) DeleteAuditRecordByAuditToken(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteAuditRecordByAuditToken", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteAuditRecordByAuditToken indicates an expected call of DeleteAuditRecordByAuditToken.
func (mr *MockIStoreMockRecorder) DeleteAuditRecordByAuditToken(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAuditRecordByAuditToken", reflect.TypeOf((*MockIStore)(nil).DeleteAuditRecordByAuditToken), arg0, arg1)
}

// DeleteCoachBasePriceSetting mocks base method.
func (m *MockIStore) DeleteCoachBasePriceSetting(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteCoachBasePriceSetting", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteCoachBasePriceSetting indicates an expected call of DeleteCoachBasePriceSetting.
func (mr *MockIStoreMockRecorder) DeleteCoachBasePriceSetting(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteCoachBasePriceSetting", reflect.TypeOf((*MockIStore)(nil).DeleteCoachBasePriceSetting), arg0, arg1, arg2)
}

// DeleteEsportGameConfig mocks base method.
func (m *MockIStore) DeleteEsportGameConfig(arg0 context.Context, arg1 *esport_skill.DeleteEsportGameConfigRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteEsportGameConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteEsportGameConfig indicates an expected call of DeleteEsportGameConfig.
func (mr *MockIStoreMockRecorder) DeleteEsportGameConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteEsportGameConfig", reflect.TypeOf((*MockIStore)(nil).DeleteEsportGameConfig), arg0, arg1)
}

// DeleteIssuanceRecordByID mocks base method.
func (m *MockIStore) DeleteIssuanceRecordByID(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteIssuanceRecordByID", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteIssuanceRecordByID indicates an expected call of DeleteIssuanceRecordByID.
func (mr *MockIStoreMockRecorder) DeleteIssuanceRecordByID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteIssuanceRecordByID", reflect.TypeOf((*MockIStore)(nil).DeleteIssuanceRecordByID), arg0, arg1)
}

// DeleteIssuanceRecordByLabelID mocks base method.
func (m *MockIStore) DeleteIssuanceRecordByLabelID(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteIssuanceRecordByLabelID", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteIssuanceRecordByLabelID indicates an expected call of DeleteIssuanceRecordByLabelID.
func (mr *MockIStoreMockRecorder) DeleteIssuanceRecordByLabelID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteIssuanceRecordByLabelID", reflect.TypeOf((*MockIStore)(nil).DeleteIssuanceRecordByLabelID), arg0, arg1)
}

// DeleteLabelByID mocks base method.
func (m *MockIStore) DeleteLabelByID(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteLabelByID", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteLabelByID indicates an expected call of DeleteLabelByID.
func (mr *MockIStoreMockRecorder) DeleteLabelByID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteLabelByID", reflect.TypeOf((*MockIStore)(nil).DeleteLabelByID), arg0, arg1)
}

// DeleteRenownedPlayerByID mocks base method.
func (m *MockIStore) DeleteRenownedPlayerByID(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteRenownedPlayerByID", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteRenownedPlayerByID indicates an expected call of DeleteRenownedPlayerByID.
func (mr *MockIStoreMockRecorder) DeleteRenownedPlayerByID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteRenownedPlayerByID", reflect.TypeOf((*MockIStore)(nil).DeleteRenownedPlayerByID), arg0, arg1)
}

// DeleteUserSkillFreezeStatus mocks base method.
func (m *MockIStore) DeleteUserSkillFreezeStatus(arg0 context.Context, arg1 uint32, arg2 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteUserSkillFreezeStatus", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteUserSkillFreezeStatus indicates an expected call of DeleteUserSkillFreezeStatus.
func (mr *MockIStoreMockRecorder) DeleteUserSkillFreezeStatus(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteUserSkillFreezeStatus", reflect.TypeOf((*MockIStore)(nil).DeleteUserSkillFreezeStatus), arg0, arg1, arg2)
}

// GetAllGameConfig mocks base method.
func (m *MockIStore) GetAllGameConfig(arg0 context.Context) ([]*store.GameConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllGameConfig", arg0)
	ret0, _ := ret[0].([]*store.GameConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllGameConfig indicates an expected call of GetAllGameConfig.
func (mr *MockIStoreMockRecorder) GetAllGameConfig(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllGameConfig", reflect.TypeOf((*MockIStore)(nil).GetAllGameConfig), arg0)
}

// GetAllGameSimpleInfo mocks base method.
func (m *MockIStore) GetAllGameSimpleInfo(arg0 context.Context) (*esport_skill.GetAllGameSimpleInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllGameSimpleInfo", arg0)
	ret0, _ := ret[0].(*esport_skill.GetAllGameSimpleInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllGameSimpleInfo indicates an expected call of GetAllGameSimpleInfo.
func (mr *MockIStoreMockRecorder) GetAllGameSimpleInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllGameSimpleInfo", reflect.TypeOf((*MockIStore)(nil).GetAllGameSimpleInfo), arg0)
}

// GetAuditSkillCount mocks base method.
func (m *MockIStore) GetAuditSkillCount(arg0 context.Context, arg1 *esport_skill.BatchGetAuditSkillRequest) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAuditSkillCount", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAuditSkillCount indicates an expected call of GetAuditSkillCount.
func (mr *MockIStoreMockRecorder) GetAuditSkillCount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAuditSkillCount", reflect.TypeOf((*MockIStore)(nil).GetAuditSkillCount), arg0, arg1)
}

// GetCoachAvailableIssuanceRecord mocks base method.
func (m *MockIStore) GetCoachAvailableIssuanceRecord(arg0 context.Context, arg1 uint32) ([]*store.IssuanceRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCoachAvailableIssuanceRecord", arg0, arg1)
	ret0, _ := ret[0].([]*store.IssuanceRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCoachAvailableIssuanceRecord indicates an expected call of GetCoachAvailableIssuanceRecord.
func (mr *MockIStoreMockRecorder) GetCoachAvailableIssuanceRecord(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCoachAvailableIssuanceRecord", reflect.TypeOf((*MockIStore)(nil).GetCoachAvailableIssuanceRecord), arg0, arg1)
}

// GetCoachBasePriceSetting mocks base method.
func (m *MockIStore) GetCoachBasePriceSetting(arg0 context.Context, arg1, arg2 uint32) (*store.CoachBasePriceSetting, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCoachBasePriceSetting", arg0, arg1, arg2)
	ret0, _ := ret[0].(*store.CoachBasePriceSetting)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCoachBasePriceSetting indicates an expected call of GetCoachBasePriceSetting.
func (mr *MockIStoreMockRecorder) GetCoachBasePriceSetting(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCoachBasePriceSetting", reflect.TypeOf((*MockIStore)(nil).GetCoachBasePriceSetting), arg0, arg1, arg2)
}

// GetEffectiveIssuanceRecord mocks base method.
func (m *MockIStore) GetEffectiveIssuanceRecord(arg0 context.Context, arg1, arg2 int64) ([]*store.IssuanceRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEffectiveIssuanceRecord", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*store.IssuanceRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEffectiveIssuanceRecord indicates an expected call of GetEffectiveIssuanceRecord.
func (mr *MockIStoreMockRecorder) GetEffectiveIssuanceRecord(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEffectiveIssuanceRecord", reflect.TypeOf((*MockIStore)(nil).GetEffectiveIssuanceRecord), arg0, arg1, arg2)
}

// GetEsportGameConfigCnt mocks base method.
func (m *MockIStore) GetEsportGameConfigCnt(arg0 context.Context, arg1 string, arg2 esport_skill.GAME_TYPE) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEsportGameConfigCnt", arg0, arg1, arg2)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEsportGameConfigCnt indicates an expected call of GetEsportGameConfigCnt.
func (mr *MockIStoreMockRecorder) GetEsportGameConfigCnt(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEsportGameConfigCnt", reflect.TypeOf((*MockIStore)(nil).GetEsportGameConfigCnt), arg0, arg1, arg2)
}

// GetEsportGameConfigListByPage mocks base method.
func (m *MockIStore) GetEsportGameConfigListByPage(arg0 context.Context, arg1 *esport_skill.GetEsportGameConfigListByPageRequest) ([]*esport_skill.EsportGameConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEsportGameConfigListByPage", arg0, arg1)
	ret0, _ := ret[0].([]*esport_skill.EsportGameConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEsportGameConfigListByPage indicates an expected call of GetEsportGameConfigListByPage.
func (mr *MockIStoreMockRecorder) GetEsportGameConfigListByPage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEsportGameConfigListByPage", reflect.TypeOf((*MockIStore)(nil).GetEsportGameConfigListByPage), arg0, arg1)
}

// GetExpiredUserSkillFreezeStatus mocks base method.
func (m *MockIStore) GetExpiredUserSkillFreezeStatus(arg0 context.Context, arg1 int64) ([]*store.UserSkillFreezeStatusEntity, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExpiredUserSkillFreezeStatus", arg0, arg1)
	ret0, _ := ret[0].([]*store.UserSkillFreezeStatusEntity)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExpiredUserSkillFreezeStatus indicates an expected call of GetExpiredUserSkillFreezeStatus.
func (mr *MockIStoreMockRecorder) GetExpiredUserSkillFreezeStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExpiredUserSkillFreezeStatus", reflect.TypeOf((*MockIStore)(nil).GetExpiredUserSkillFreezeStatus), arg0, arg1)
}

// GetGameDetailById mocks base method.
func (m *MockIStore) GetGameDetailById(arg0 context.Context, arg1 uint32) (*esport_skill.EsportGameConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameDetailById", arg0, arg1)
	ret0, _ := ret[0].(*esport_skill.EsportGameConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameDetailById indicates an expected call of GetGameDetailById.
func (mr *MockIStoreMockRecorder) GetGameDetailById(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameDetailById", reflect.TypeOf((*MockIStore)(nil).GetGameDetailById), arg0, arg1)
}

// GetGameDetailByIds mocks base method.
func (m *MockIStore) GetGameDetailByIds(arg0 context.Context, arg1 []uint32) ([]*esport_skill.EsportGameConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameDetailByIds", arg0, arg1)
	ret0, _ := ret[0].([]*esport_skill.EsportGameConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameDetailByIds indicates an expected call of GetGameDetailByIds.
func (mr *MockIStoreMockRecorder) GetGameDetailByIds(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameDetailByIds", reflect.TypeOf((*MockIStore)(nil).GetGameDetailByIds), arg0, arg1)
}

// GetGameList mocks base method.
func (m *MockIStore) GetGameList(arg0 context.Context, arg1 esport_skill.GAME_TYPE, arg2, arg3 uint32) ([]*esport_skill.GameItem, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameList", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*esport_skill.GameItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameList indicates an expected call of GetGameList.
func (mr *MockIStoreMockRecorder) GetGameList(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameList", reflect.TypeOf((*MockIStore)(nil).GetGameList), arg0, arg1, arg2, arg3)
}

// GetGameListByGameNameFuzzy mocks base method.
func (m *MockIStore) GetGameListByGameNameFuzzy(arg0 context.Context, arg1 string) ([]*esport_skill.SimpleGameInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameListByGameNameFuzzy", arg0, arg1)
	ret0, _ := ret[0].([]*esport_skill.SimpleGameInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameListByGameNameFuzzy indicates an expected call of GetGameListByGameNameFuzzy.
func (mr *MockIStoreMockRecorder) GetGameListByGameNameFuzzy(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameListByGameNameFuzzy", reflect.TypeOf((*MockIStore)(nil).GetGameListByGameNameFuzzy), arg0, arg1)
}

// GetGameUserSkillsByPage mocks base method.
func (m *MockIStore) GetGameUserSkillsByPage(arg0 context.Context, arg1 uint32, arg2, arg3 int64) ([]*store.UserSkillInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameUserSkillsByPage", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*store.UserSkillInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameUserSkillsByPage indicates an expected call of GetGameUserSkillsByPage.
func (mr *MockIStoreMockRecorder) GetGameUserSkillsByPage(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameUserSkillsByPage", reflect.TypeOf((*MockIStore)(nil).GetGameUserSkillsByPage), arg0, arg1, arg2, arg3)
}

// GetIssuanceRecordByID mocks base method.
func (m *MockIStore) GetIssuanceRecordByID(arg0 context.Context, arg1 uint32) (*store.IssuanceRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIssuanceRecordByID", arg0, arg1)
	ret0, _ := ret[0].(*store.IssuanceRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIssuanceRecordByID indicates an expected call of GetIssuanceRecordByID.
func (mr *MockIStoreMockRecorder) GetIssuanceRecordByID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIssuanceRecordByID", reflect.TypeOf((*MockIStore)(nil).GetIssuanceRecordByID), arg0, arg1)
}

// GetIssuanceRecords mocks base method.
func (m *MockIStore) GetIssuanceRecords(arg0 context.Context, arg1, arg2 int32, arg3 []uint32, arg4 uint32, arg5, arg6 int64, arg7 store.IssuanceRecordStatus, arg8 store.LabelType) ([]*store.IssuanceRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIssuanceRecords", arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8)
	ret0, _ := ret[0].([]*store.IssuanceRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIssuanceRecords indicates an expected call of GetIssuanceRecords.
func (mr *MockIStoreMockRecorder) GetIssuanceRecords(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIssuanceRecords", reflect.TypeOf((*MockIStore)(nil).GetIssuanceRecords), arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8)
}

// GetLabelByID mocks base method.
func (m *MockIStore) GetLabelByID(arg0 context.Context, arg1 uint32) (*store.Label, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLabelByID", arg0, arg1)
	ret0, _ := ret[0].(*store.Label)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLabelByID indicates an expected call of GetLabelByID.
func (mr *MockIStoreMockRecorder) GetLabelByID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLabelByID", reflect.TypeOf((*MockIStore)(nil).GetLabelByID), arg0, arg1)
}

// GetLabels mocks base method.
func (m *MockIStore) GetLabels(arg0 context.Context, arg1, arg2 int32, arg3 uint32, arg4 store.LabelType, arg5 uint32) ([]*store.Label, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLabels", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].([]*store.Label)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLabels indicates an expected call of GetLabels.
func (mr *MockIStoreMockRecorder) GetLabels(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLabels", reflect.TypeOf((*MockIStore)(nil).GetLabels), arg0, arg1, arg2, arg3, arg4, arg5)
}

// GetLabelsByCoachTypeAndGames mocks base method.
func (m *MockIStore) GetLabelsByCoachTypeAndGames(arg0 context.Context, arg1 ...uint32) ([]*store.Label, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLabelsByCoachTypeAndGames", varargs...)
	ret0, _ := ret[0].([]*store.Label)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLabelsByCoachTypeAndGames indicates an expected call of GetLabelsByCoachTypeAndGames.
func (mr *MockIStoreMockRecorder) GetLabelsByCoachTypeAndGames(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLabelsByCoachTypeAndGames", reflect.TypeOf((*MockIStore)(nil).GetLabelsByCoachTypeAndGames), varargs...)
}

// GetRenownedPlayerByID mocks base method.
func (m *MockIStore) GetRenownedPlayerByID(arg0 context.Context, arg1 uint32) (*store.RenownedPlayer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRenownedPlayerByID", arg0, arg1)
	ret0, _ := ret[0].(*store.RenownedPlayer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRenownedPlayerByID indicates an expected call of GetRenownedPlayerByID.
func (mr *MockIStoreMockRecorder) GetRenownedPlayerByID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRenownedPlayerByID", reflect.TypeOf((*MockIStore)(nil).GetRenownedPlayerByID), arg0, arg1)
}

// GetRenownedPlayers mocks base method.
func (m *MockIStore) GetRenownedPlayers(arg0 context.Context, arg1, arg2 int32, arg3 uint32, arg4 []uint32) ([]*store.RenownedPlayer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRenownedPlayers", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*store.RenownedPlayer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRenownedPlayers indicates an expected call of GetRenownedPlayers.
func (mr *MockIStoreMockRecorder) GetRenownedPlayers(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRenownedPlayers", reflect.TypeOf((*MockIStore)(nil).GetRenownedPlayers), arg0, arg1, arg2, arg3, arg4)
}

// GetSpecialLabelIssuanceRecord mocks base method.
func (m *MockIStore) GetSpecialLabelIssuanceRecord(arg0 context.Context, arg1, arg2 int64) ([]*store.IssuanceRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSpecialLabelIssuanceRecord", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*store.IssuanceRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSpecialLabelIssuanceRecord indicates an expected call of GetSpecialLabelIssuanceRecord.
func (mr *MockIStoreMockRecorder) GetSpecialLabelIssuanceRecord(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSpecialLabelIssuanceRecord", reflect.TypeOf((*MockIStore)(nil).GetSpecialLabelIssuanceRecord), arg0, arg1, arg2)
}

// GetTopGameList mocks base method.
func (m *MockIStore) GetTopGameList(arg0 context.Context, arg1 esport_skill.GAME_TYPE) ([]*esport_skill.GameItem, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTopGameList", arg0, arg1)
	ret0, _ := ret[0].([]*esport_skill.GameItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTopGameList indicates an expected call of GetTopGameList.
func (mr *MockIStoreMockRecorder) GetTopGameList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTopGameList", reflect.TypeOf((*MockIStore)(nil).GetTopGameList), arg0, arg1)
}

// GetUserSkillAuditRecordByToken mocks base method.
func (m *MockIStore) GetUserSkillAuditRecordByToken(arg0 context.Context, arg1 string) (*store.UserSkillAuditHistory, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserSkillAuditRecordByToken", arg0, arg1)
	ret0, _ := ret[0].(*store.UserSkillAuditHistory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserSkillAuditRecordByToken indicates an expected call of GetUserSkillAuditRecordByToken.
func (mr *MockIStoreMockRecorder) GetUserSkillAuditRecordByToken(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSkillAuditRecordByToken", reflect.TypeOf((*MockIStore)(nil).GetUserSkillAuditRecordByToken), arg0, arg1)
}

// GetUserSkillAuditRecordsByUidOrTokens mocks base method.
func (m *MockIStore) GetUserSkillAuditRecordsByUidOrTokens(arg0 context.Context, arg1 uint32, arg2 []string) ([]*store.UserSkillAuditHistory, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserSkillAuditRecordsByUidOrTokens", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*store.UserSkillAuditHistory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserSkillAuditRecordsByUidOrTokens indicates an expected call of GetUserSkillAuditRecordsByUidOrTokens.
func (mr *MockIStoreMockRecorder) GetUserSkillAuditRecordsByUidOrTokens(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSkillAuditRecordsByUidOrTokens", reflect.TypeOf((*MockIStore)(nil).GetUserSkillAuditRecordsByUidOrTokens), arg0, arg1, arg2)
}

// GetUserSkillByGameId mocks base method.
func (m *MockIStore) GetUserSkillByGameId(arg0 context.Context, arg1, arg2 uint32) (*store.UserSkillInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserSkillByGameId", arg0, arg1, arg2)
	ret0, _ := ret[0].(*store.UserSkillInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserSkillByGameId indicates an expected call of GetUserSkillByGameId.
func (mr *MockIStoreMockRecorder) GetUserSkillByGameId(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSkillByGameId", reflect.TypeOf((*MockIStore)(nil).GetUserSkillByGameId), arg0, arg1, arg2)
}

// GetUserSkillFreezeStatus mocks base method.
func (m *MockIStore) GetUserSkillFreezeStatus(arg0 context.Context, arg1 uint32) (map[uint32]*store.UserSkillFreezeStatusEntity, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserSkillFreezeStatus", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]*store.UserSkillFreezeStatusEntity)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserSkillFreezeStatus indicates an expected call of GetUserSkillFreezeStatus.
func (mr *MockIStoreMockRecorder) GetUserSkillFreezeStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSkillFreezeStatus", reflect.TypeOf((*MockIStore)(nil).GetUserSkillFreezeStatus), arg0, arg1)
}

// GetUserSkills mocks base method.
func (m *MockIStore) GetUserSkills(arg0 context.Context, arg1 uint32) ([]*store.UserSkillInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserSkills", arg0, arg1)
	ret0, _ := ret[0].([]*store.UserSkillInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserSkills indicates an expected call of GetUserSkills.
func (mr *MockIStoreMockRecorder) GetUserSkills(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSkills", reflect.TypeOf((*MockIStore)(nil).GetUserSkills), arg0, arg1)
}

// GetUserSkillsWithGuaranteeWin mocks base method.
func (m *MockIStore) GetUserSkillsWithGuaranteeWin(arg0 context.Context) ([]*store.UserSkillInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserSkillsWithGuaranteeWin", arg0)
	ret0, _ := ret[0].([]*store.UserSkillInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserSkillsWithGuaranteeWin indicates an expected call of GetUserSkillsWithGuaranteeWin.
func (mr *MockIStoreMockRecorder) GetUserSkillsWithGuaranteeWin(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSkillsWithGuaranteeWin", reflect.TypeOf((*MockIStore)(nil).GetUserSkillsWithGuaranteeWin), arg0)
}

// IsCoachHasGame mocks base method.
func (m *MockIStore) IsCoachHasGame(arg0 context.Context, arg1 uint32, arg2 string) (*store.UserSkillInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsCoachHasGame", arg0, arg1, arg2)
	ret0, _ := ret[0].(*store.UserSkillInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsCoachHasGame indicates an expected call of IsCoachHasGame.
func (mr *MockIStoreMockRecorder) IsCoachHasGame(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsCoachHasGame", reflect.TypeOf((*MockIStore)(nil).IsCoachHasGame), arg0, arg1, arg2)
}

// IsRenownedPlayerExist mocks base method.
func (m *MockIStore) IsRenownedPlayerExist(arg0 context.Context, arg1, arg2 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsRenownedPlayerExist", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsRenownedPlayerExist indicates an expected call of IsRenownedPlayerExist.
func (mr *MockIStoreMockRecorder) IsRenownedPlayerExist(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsRenownedPlayerExist", reflect.TypeOf((*MockIStore)(nil).IsRenownedPlayerExist), arg0, arg1, arg2)
}

// SearchUserSkillFreezeHistory mocks base method.
func (m *MockIStore) SearchUserSkillFreezeHistory(arg0 context.Context, arg1 []uint32, arg2, arg3 uint32, arg4, arg5 int32) ([]*store.UserSkillFreezeHistoryEntity, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchUserSkillFreezeHistory", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].([]*store.UserSkillFreezeHistoryEntity)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchUserSkillFreezeHistory indicates an expected call of SearchUserSkillFreezeHistory.
func (mr *MockIStoreMockRecorder) SearchUserSkillFreezeHistory(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchUserSkillFreezeHistory", reflect.TypeOf((*MockIStore)(nil).SearchUserSkillFreezeHistory), arg0, arg1, arg2, arg3, arg4, arg5)
}

// SearchUserSkillFreezeHistoryCnt mocks base method.
func (m *MockIStore) SearchUserSkillFreezeHistoryCnt(arg0 context.Context, arg1 []uint32, arg2, arg3 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchUserSkillFreezeHistoryCnt", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchUserSkillFreezeHistoryCnt indicates an expected call of SearchUserSkillFreezeHistoryCnt.
func (mr *MockIStoreMockRecorder) SearchUserSkillFreezeHistoryCnt(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchUserSkillFreezeHistoryCnt", reflect.TypeOf((*MockIStore)(nil).SearchUserSkillFreezeHistoryCnt), arg0, arg1, arg2, arg3)
}

// SetGameGuaranteeWin mocks base method.
func (m *MockIStore) SetGameGuaranteeWin(arg0 context.Context, arg1, arg2 uint32, arg3 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetGameGuaranteeWin", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetGameGuaranteeWin indicates an expected call of SetGameGuaranteeWin.
func (mr *MockIStoreMockRecorder) SetGameGuaranteeWin(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetGameGuaranteeWin", reflect.TypeOf((*MockIStore)(nil).SetGameGuaranteeWin), arg0, arg1, arg2, arg3)
}

// SetUserAudioAuditType mocks base method.
func (m *MockIStore) SetUserAudioAuditType(arg0 context.Context, arg1 uint32, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserAudioAuditType", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserAudioAuditType indicates an expected call of SetUserAudioAuditType.
func (mr *MockIStoreMockRecorder) SetUserAudioAuditType(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserAudioAuditType", reflect.TypeOf((*MockIStore)(nil).SetUserAudioAuditType), arg0, arg1, arg2)
}

// SetUserSkillAudio mocks base method.
func (m *MockIStore) SetUserSkillAudio(arg0 context.Context, arg1, arg2, arg3 uint32, arg4 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserSkillAudio", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserSkillAudio indicates an expected call of SetUserSkillAudio.
func (mr *MockIStoreMockRecorder) SetUserSkillAudio(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserSkillAudio", reflect.TypeOf((*MockIStore)(nil).SetUserSkillAudio), arg0, arg1, arg2, arg3, arg4)
}

// SetUserSkillAuditType mocks base method.
func (m *MockIStore) SetUserSkillAuditType(arg0 context.Context, arg1 uint32, arg2, arg3, arg4 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserSkillAuditType", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserSkillAuditType indicates an expected call of SetUserSkillAuditType.
func (mr *MockIStoreMockRecorder) SetUserSkillAuditType(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserSkillAuditType", reflect.TypeOf((*MockIStore)(nil).SetUserSkillAuditType), arg0, arg1, arg2, arg3, arg4)
}

// SetUserSkillImgAndSection mocks base method.
func (m *MockIStore) SetUserSkillImgAndSection(arg0 context.Context, arg1, arg2 uint32, arg3 string, arg4 []*store.SectionInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserSkillImgAndSection", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserSkillImgAndSection indicates an expected call of SetUserSkillImgAndSection.
func (mr *MockIStoreMockRecorder) SetUserSkillImgAndSection(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserSkillImgAndSection", reflect.TypeOf((*MockIStore)(nil).SetUserSkillImgAndSection), arg0, arg1, arg2, arg3, arg4)
}

// SetUserSkillImgAuditType mocks base method.
func (m *MockIStore) SetUserSkillImgAuditType(arg0 context.Context, arg1 uint32, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserSkillImgAuditType", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserSkillImgAuditType indicates an expected call of SetUserSkillImgAuditType.
func (mr *MockIStoreMockRecorder) SetUserSkillImgAuditType(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserSkillImgAuditType", reflect.TypeOf((*MockIStore)(nil).SetUserSkillImgAuditType), arg0, arg1, arg2)
}

// SetUserSkillSection mocks base method.
func (m *MockIStore) SetUserSkillSection(arg0 context.Context, arg1, arg2 uint32, arg3 []*store.SectionInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserSkillSection", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserSkillSection indicates an expected call of SetUserSkillSection.
func (mr *MockIStoreMockRecorder) SetUserSkillSection(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserSkillSection", reflect.TypeOf((*MockIStore)(nil).SetUserSkillSection), arg0, arg1, arg2, arg3)
}

// SetUserSkillTextAuditType mocks base method.
func (m *MockIStore) SetUserSkillTextAuditType(arg0 context.Context, arg1 uint32, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserSkillTextAuditType", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserSkillTextAuditType indicates an expected call of SetUserSkillTextAuditType.
func (mr *MockIStoreMockRecorder) SetUserSkillTextAuditType(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserSkillTextAuditType", reflect.TypeOf((*MockIStore)(nil).SetUserSkillTextAuditType), arg0, arg1, arg2)
}

// SetUserSkillTextDesc mocks base method.
func (m *MockIStore) SetUserSkillTextDesc(arg0 context.Context, arg1, arg2 uint32, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserSkillTextDesc", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserSkillTextDesc indicates an expected call of SetUserSkillTextDesc.
func (mr *MockIStoreMockRecorder) SetUserSkillTextDesc(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserSkillTextDesc", reflect.TypeOf((*MockIStore)(nil).SetUserSkillTextDesc), arg0, arg1, arg2, arg3)
}

// UpdateEsportGameConfig mocks base method.
func (m *MockIStore) UpdateEsportGameConfig(arg0 context.Context, arg1 *esport_skill.UpdateEsportGameConfigRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateEsportGameConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateEsportGameConfig indicates an expected call of UpdateEsportGameConfig.
func (mr *MockIStoreMockRecorder) UpdateEsportGameConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateEsportGameConfig", reflect.TypeOf((*MockIStore)(nil).UpdateEsportGameConfig), arg0, arg1)
}

// UpdateIssuanceRecord mocks base method.
func (m *MockIStore) UpdateIssuanceRecord(arg0 context.Context, arg1 *store.IssuanceRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateIssuanceRecord", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateIssuanceRecord indicates an expected call of UpdateIssuanceRecord.
func (mr *MockIStoreMockRecorder) UpdateIssuanceRecord(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateIssuanceRecord", reflect.TypeOf((*MockIStore)(nil).UpdateIssuanceRecord), arg0, arg1)
}

// UpdateLabel mocks base method.
func (m *MockIStore) UpdateLabel(arg0 context.Context, arg1 *store.Label) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateLabel", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateLabel indicates an expected call of UpdateLabel.
func (mr *MockIStoreMockRecorder) UpdateLabel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLabel", reflect.TypeOf((*MockIStore)(nil).UpdateLabel), arg0, arg1)
}

// UpdateLabelPriceAdditionalSwitch mocks base method.
func (m *MockIStore) UpdateLabelPriceAdditionalSwitch(arg0 context.Context, arg1, arg2 uint32, arg3 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateLabelPriceAdditionalSwitch", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateLabelPriceAdditionalSwitch indicates an expected call of UpdateLabelPriceAdditionalSwitch.
func (mr *MockIStoreMockRecorder) UpdateLabelPriceAdditionalSwitch(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLabelPriceAdditionalSwitch", reflect.TypeOf((*MockIStore)(nil).UpdateLabelPriceAdditionalSwitch), arg0, arg1, arg2, arg3)
}

// UpdateRenownedPlayer mocks base method.
func (m *MockIStore) UpdateRenownedPlayer(arg0 context.Context, arg1 *store.RenownedPlayer) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRenownedPlayer", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateRenownedPlayer indicates an expected call of UpdateRenownedPlayer.
func (mr *MockIStoreMockRecorder) UpdateRenownedPlayer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRenownedPlayer", reflect.TypeOf((*MockIStore)(nil).UpdateRenownedPlayer), arg0, arg1)
}

// UpdateUserSpecialLabel mocks base method.
func (m *MockIStore) UpdateUserSpecialLabel(arg0 context.Context, arg1 *esport_skill.UpdateUserSpecialLabelRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUserSpecialLabel", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateUserSpecialLabel indicates an expected call of UpdateUserSpecialLabel.
func (mr *MockIStoreMockRecorder) UpdateUserSpecialLabel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserSpecialLabel", reflect.TypeOf((*MockIStore)(nil).UpdateUserSpecialLabel), arg0, arg1)
}

// UpsertCoachBasePriceSetting mocks base method.
func (m *MockIStore) UpsertCoachBasePriceSetting(arg0 context.Context, arg1 *store.CoachBasePriceSetting) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertCoachBasePriceSetting", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertCoachBasePriceSetting indicates an expected call of UpsertCoachBasePriceSetting.
func (mr *MockIStoreMockRecorder) UpsertCoachBasePriceSetting(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertCoachBasePriceSetting", reflect.TypeOf((*MockIStore)(nil).UpsertCoachBasePriceSetting), arg0, arg1)
}
