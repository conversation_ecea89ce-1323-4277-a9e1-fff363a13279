// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/tt-rev/esport/esport-skill/internal/conf (interfaces: IBusinessConfManager)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	esport_skill "golang.52tt.com/protocol/services/esport-skill"
	conf "golang.52tt.com/services/tt-rev/esport/esport-skill/internal/conf"
)

// MockIBusinessConfManager is a mock of IBusinessConfManager interface.
type MockIBusinessConfManager struct {
	ctrl     *gomock.Controller
	recorder *MockIBusinessConfManagerMockRecorder
}

// MockIBusinessConfManagerMockRecorder is the mock recorder for MockIBusinessConfManager.
type MockIBusinessConfManagerMockRecorder struct {
	mock *MockIBusinessConfManager
}

// NewMockIBusinessConfManager creates a new mock instance.
func NewMockIBusinessConfManager(ctrl *gomock.Controller) *MockIBusinessConfManager {
	mock := &MockIBusinessConfManager{ctrl: ctrl}
	mock.recorder = &MockIBusinessConfManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIBusinessConfManager) EXPECT() *MockIBusinessConfManagerMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockIBusinessConfManager) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIBusinessConfManagerMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIBusinessConfManager)(nil).Close))
}

// GetConfig mocks base method.
func (m *MockIBusinessConfManager) GetConfig() *conf.BusinessConf {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConfig")
	ret0, _ := ret[0].(*conf.BusinessConf)
	return ret0
}

// GetConfig indicates an expected call of GetConfig.
func (mr *MockIBusinessConfManagerMockRecorder) GetConfig() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConfig", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetConfig))
}

// GetFileUrlPrefix mocks base method.
func (m *MockIBusinessConfManager) GetFileUrlPrefix() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFileUrlPrefix")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetFileUrlPrefix indicates an expected call of GetFileUrlPrefix.
func (mr *MockIBusinessConfManagerMockRecorder) GetFileUrlPrefix() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFileUrlPrefix", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetFileUrlPrefix))
}

// GetGameListPageSize mocks base method.
func (m *MockIBusinessConfManager) GetGameListPageSize() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameListPageSize")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetGameListPageSize indicates an expected call of GetGameListPageSize.
func (mr *MockIBusinessConfManagerMockRecorder) GetGameListPageSize() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameListPageSize", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetGameListPageSize))
}

// GetMinimumPrice mocks base method.
func (m *MockIBusinessConfManager) GetMinimumPrice() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMinimumPrice")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetMinimumPrice indicates an expected call of GetMinimumPrice.
func (mr *MockIBusinessConfManagerMockRecorder) GetMinimumPrice() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMinimumPrice", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetMinimumPrice))
}

// GetOpenSkillUrl mocks base method.
func (m *MockIBusinessConfManager) GetOpenSkillUrl() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOpenSkillUrl")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetOpenSkillUrl indicates an expected call of GetOpenSkillUrl.
func (mr *MockIBusinessConfManagerMockRecorder) GetOpenSkillUrl() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOpenSkillUrl", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetOpenSkillUrl))
}

// GetSwitch mocks base method.
func (m *MockIBusinessConfManager) GetSwitch(arg0 context.Context, arg1 uint32) *esport_skill.SwitchStatus {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSwitch", arg0, arg1)
	ret0, _ := ret[0].(*esport_skill.SwitchStatus)
	return ret0
}

// GetSwitch indicates an expected call of GetSwitch.
func (mr *MockIBusinessConfManagerMockRecorder) GetSwitch(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSwitch", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetSwitch), arg0, arg1)
}

// IsInSwitchWhiteList mocks base method.
func (m *MockIBusinessConfManager) IsInSwitchWhiteList(arg0 uint32) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsInSwitchWhiteList", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsInSwitchWhiteList indicates an expected call of IsInSwitchWhiteList.
func (mr *MockIBusinessConfManagerMockRecorder) IsInSwitchWhiteList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsInSwitchWhiteList", reflect.TypeOf((*MockIBusinessConfManager)(nil).IsInSwitchWhiteList), arg0)
}

// Reload mocks base method.
func (m *MockIBusinessConfManager) Reload(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Reload", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Reload indicates an expected call of Reload.
func (mr *MockIBusinessConfManagerMockRecorder) Reload(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Reload", reflect.TypeOf((*MockIBusinessConfManager)(nil).Reload), arg0)
}

// Watch mocks base method.
func (m *MockIBusinessConfManager) Watch(arg0 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Watch", arg0)
}

// Watch indicates an expected call of Watch.
func (mr *MockIBusinessConfManagerMockRecorder) Watch(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Watch", reflect.TypeOf((*MockIBusinessConfManager)(nil).Watch), arg0)
}
