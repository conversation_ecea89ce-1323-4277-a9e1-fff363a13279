package mgr

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/timer"
	"time"
)

func (m *Manager) setupTimer() error {
	// 创建定时器
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	// 大小进程的定时器，同一个任务只会在一个节点上执行
	timerD, err := timer.NewTimerD(ctx, "esport-skill", timer.WithV8RedisCmdable(m.cache.GetRedisClient()), timer.WithTTL(10*time.Second))
	if err != nil {
		log.Errorf("setupTimer fail to NewTimerD. err:%v", err)
		return err
	}

	err = timerD.AddTask("@every 30s", "runTaskHandleExpiredFreeze", timer.BuildFromLambda(func(ctx context.Context) {
		// 处理到期的技能冻结
		m.runTaskHandleExpiredFreeze()
	}))
	if err != nil {
		return err
	}

	// 每天运行1次
	err = timerD.AddTask("0 30 0 * * *", "runTaskCheckGuaranteeWinPermission", timer.BuildFromLambda(func(ctx context.Context) {
		// 检查是否还有包赢权限
		m.RunTaskCheckGuaranteeWinPermission()
	}))
	if err != nil {
		return err
	}

	timerD.Start()
	m.timerD = timerD
	return nil
}
