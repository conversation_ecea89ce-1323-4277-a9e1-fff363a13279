package mgr

import (
    "context"
    "errors"
    "fmt"
    "github.com/golang/mock/gomock"
    "github.com/stretchr/testify/assert"
    "testing"
)

func TestManager_SetGameGuaranteeWin(t *testing.T) {

    type args struct {
        ctx            context.Context
        uid            uint32
        gameId         uint32
        isGuaranteeWin bool
        isSystemSource bool
    }
    tests := []struct {
        name     string
        args     args
        wantErr  assert.ErrorAssertionFunc
        initFunc func(*mgrHelperForTest)
    }{
        {
            name: "case1",
            args: args{
                ctx:            context.Background(),
                uid:            1,
                gameId:         1,
                isGuaranteeWin: true,
                isSystemSource: true,
            },
            initFunc: func(m *mgrHelperForTest) {
                m.getStore().EXPECT().SetGameGuaranteeWin(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
                m.getEsportHallCli().EXPECT().SetGuaranteeWinSwitch(gomock.Any(), gomock.Any()).Return(nil, errors.New("错误"))
            },
            wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
                return assert.Error(t, err)
            },
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := newMgrHelperForTest(t)
            if tt.initFunc != nil {
                tt.initFunc(m)
            }
            tt.wantErr(t, m.SetGameGuaranteeWin(tt.args.ctx, tt.args.uid, tt.args.gameId, tt.args.isGuaranteeWin, tt.args.isSystemSource), fmt.Sprintf("SetGameGuaranteeWin(%v, %v, %v, %v, %v)", tt.args.ctx, tt.args.uid, tt.args.gameId, tt.args.isGuaranteeWin, tt.args.isSystemSource))
        })
    }
}
