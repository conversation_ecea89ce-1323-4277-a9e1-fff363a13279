package mgr

import (
    "context"
    "golang.52tt.com/pkg/log"
    pb "golang.52tt.com/protocol/services/esport_hall"
    "golang.52tt.com/services/tt-rev/esport/common/user_group"
    "strconv"
    "time"
)

func (m *Manager) handleBlackCoach() {
    ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
    defer cancel()
    groupId := m.bc.GetCoachSwitchGroupId()
    if groupId == "" {
        log.DebugWithCtx(ctx, "handleBlackCoach groupId is empty")
        return
    }
    now := time.Now()

    var page = uint32(1)
    for {
        resp, err := m.rpc.UserGroupCli.GetEntityList(ctx, &user_group.EntityListReq{
            GroupId:  groupId,
            PageNo:   int(page),
            PageSize: int(m.bc.GetBlackCoachPageSize()),
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "handleBlackCoach GetEntityList err: %v", err)
            return
        }
        if resp == nil || resp.Data == nil {
            log.ErrorWithCtx(ctx, "handleBlackCoach GetEntityList resp is nil or empty")
            return
        }
        var uidList []uint32
        for _, entityId := range resp.Data.EntityIdList {

            uid, _ := strconv.ParseUint(entityId, 10, 32)
            if uid == 0 {
                continue
            }
            uidList = append(uidList, uint32(uid))
        }

        if len(uidList) > 0 {
            log.DebugWithCtx(ctx, "handleBlackCoach uidList len: %d, page: %d", len(uidList), page)
            _, err = m.rpc.EsportHallCli.BatchCloseCoachSwitch(ctx, &pb.BatchCloseCoachSwitchRequest{
                CoachUidList: uidList,
            })
            if err != nil {
                log.ErrorWithCtx(ctx, "handleBlackCoach BatchCloseCoachSwitch err: %v", err)
                return
            }

            err = m.store.BatchCloseCoachGuaranteeWin(ctx, uidList)
            if err != nil {
                log.ErrorWithCtx(ctx, "handleBlackCoach BatchCloseCoachGuaranteeWin err: %v", err)
                return
            }
            page++
        } else {
            log.InfoWithCtx(ctx, "handleBlackCoach No black coaches remain in group %s, 耗时: %s, page: %d", m.bc.GetCoachSwitchGroupId(), time.Since(now), page)
            return
        }
    }

}
