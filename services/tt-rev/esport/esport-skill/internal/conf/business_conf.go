package conf

import (
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/pkg/ttversion"
	pb "golang.52tt.com/protocol/services/esport-skill"
	"io/ioutil"
	"os"
	"strings"
	"time"

	"gitlab.ttyuyin.com/tt-infra/tyr/log"
)

//go:generate mockgen -destination ../mocks/mock_conf.go -package mocks golang.52tt.com/services/tt-rev/esport/esport-skill/internal/conf IBusinessConfManager

const (
	BusinessConfPath = "/data/oss/conf-center/tt/"
	BusinessConfFile = "esport-skill.json"
)

var LastConfMd5Sum [md5.Size]byte

type Switch struct {
	HomepageSwitch uint32 `json:"homepage_switch"`
	AppealSwitch   uint32 `json:"appeal_switch"`
	SidebarSwitch  uint32 `json:"sidebar_switch"`
}

type newMainSwitch struct {
	mainSwitch uint32
	feature    *ttversion.Feature
}

// VersionLimitConfig 版本可见控制
type VersionLimitConfig struct {
	ClientLimitVersionStr string `json:"client_limit_version"`
	MarketId              uint32 `json:"market_id"`
	MainSwitch            uint32 `json:"main_switch"` // 版本小于时，，默认为关
}

type PricingService struct {
	IsOpenWhiteList bool     `json:"is_open_white_list"`
	WhiteList       []uint32 `json:"white_list"`
}

func (p *PricingService) IsInWhiteList(uid uint32) bool {
	for _, v := range p.WhiteList {
		if v == uid {
			return true
		}
	}
	return false
}

type BusinessConf struct {
	FileUrlPrefix    string                `json:"file_url_prefix"`
	GameListPageSize uint32                `json:"game_list_page_size"`
	EsportSwitch     *Switch               `json:"esport_switch"`
	WhiteUidList     []uint32              `json:"white_uid_list"`
	MinimumPrice     uint32                `json:"minimum_price"`      // 统一最低价格
	VersionLimitList []*VersionLimitConfig `json:"version_limit_list"` // 总开关 带版本控制
	PricingService   *PricingService       `json:"pricing_service"`
	OpenSkillUrl     string                `json:"open_skill_url"`
}

func (c *BusinessConf) Parse(configFile string) (isChange bool, err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("Failed to parse config: %v \n", e)
		}
	}()

	data, err := ioutil.ReadFile(configFile)
	if err != nil {
		return false, err
	}

	md5Sum := md5.Sum(data)
	if md5Sum == LastConfMd5Sum {
		isChange = false
		return
	}

	err = json.Unmarshal(data, &c)
	if err != nil {
		return false, err
	}

	if c.PricingService == nil {
		c.PricingService = &PricingService{}
	}

	err = c.CheckConf()
	if err != nil {
		return false, err
	}

	LastConfMd5Sum = md5Sum

	log.Infof("BusinessConf : %+v", c)
	return true, nil
}

type BusinessConfManager struct {
	Done chan interface{}
	//mutex sync.RWMutex
	conf               *BusinessConf
	EsportSwitch       *pb.SwitchStatus
	WhiteUidMap        map[uint32]bool
	ClientLimitVersion map[uint32]*newMainSwitch
	BeginTs            int64
	EndTs              int64
}

func NewBusinessConfManager() (*BusinessConfManager, error) {
	businessConf := &BusinessConf{}

	businessConfFilePath := BusinessConfPath + BusinessConfFile
	if devBusinessConfPath := os.Getenv("DEV_BUSINESS_CONF_PATH"); devBusinessConfPath != "" {
		businessConfFilePath = devBusinessConfPath + BusinessConfFile
	}
	_, err := businessConf.Parse(businessConfFilePath)
	if err != nil {
		log.Errorf("NewBusinessConfManager fail to Parse BusinessConf err:%v", err)
		return nil, err
	}

	tmpWhiteMap := make(map[uint32]bool)
	for _, uid := range businessConf.WhiteUidList {
		tmpWhiteMap[uid] = true
	}

	confMgr := &BusinessConfManager{
		conf:         businessConf,
		Done:         make(chan interface{}),
		EsportSwitch: transToPbSwitch(businessConf.EsportSwitch),
		WhiteUidMap:  tmpWhiteMap,
	}
	confMgr.InitClientLimitVersion()
	log.Infof("NewBusinessConfManager conf:%+v, switch:%+v, whiteUidMap:%v, tmpLimitVersion:%+v", businessConf,
		confMgr.EsportSwitch, tmpWhiteMap, confMgr.ClientLimitVersion)
	go confMgr.Watch(businessConfFilePath)

	return confMgr, nil
}

func (bm *BusinessConfManager) InitClientLimitVersion() {
	tmpLimitVersion := make(map[uint32]*newMainSwitch)
	for _, v := range bm.conf.VersionLimitList {
		mainSwitch := &newMainSwitch{
			mainSwitch: v.MainSwitch,
			feature:    ttversion.Parse("", strings.Split(v.ClientLimitVersionStr, ",")...),
		}
		tmpLimitVersion[v.MarketId] = mainSwitch
	}
	bm.ClientLimitVersion = tmpLimitVersion
}

func (bm *BusinessConfManager) Reload(file string) error {
	businessConf := &BusinessConf{}

	isChange, err := businessConf.Parse(file)
	if err != nil {
		log.Errorf("NewBusinessConfManager fail to Parse BusinessConf err:%v", err)
		return err
	}

	if isChange {
		//bm.mutex.Lock()
		bm.conf = businessConf

		// 原子更新开关
		bm.EsportSwitch = transToPbSwitch(businessConf.EsportSwitch)
		tmpWhiteMap := make(map[uint32]bool)
		for _, uid := range businessConf.WhiteUidList {
			tmpWhiteMap[uid] = true
		}
		// 原子更新map
		bm.WhiteUidMap = tmpWhiteMap
		//bm.mutex.Unlock()
		bm.InitClientLimitVersion()

		log.Infof("Reload conf:%+v, switch:%+v, whiteUidMap:%+v, clientVersionLimit:%+v", businessConf,
			bm.EsportSwitch, tmpWhiteMap, bm.ClientLimitVersion)
	}

	return nil
}

// 默认关闭
func transToPbSwitch(gameSwitch *Switch) *pb.SwitchStatus {
	tmpSwitch := &pb.SwitchStatus{}
	if gameSwitch != nil {
		tmpSwitch.HomepageSwitchStatus = pb.EsportSwitchStatus(gameSwitch.HomepageSwitch)
		tmpSwitch.AppealSwitchStatus = pb.EsportSwitchStatus(gameSwitch.AppealSwitch)
		tmpSwitch.SidebarSwitchStatus = pb.EsportSwitchStatus(gameSwitch.SidebarSwitch)

		// 默认on
		if gameSwitch.HomepageSwitch != uint32(pb.EsportSwitchStatus_SWITCH_STATUS_ON) {
			tmpSwitch.HomepageSwitchStatus = pb.EsportSwitchStatus_SWITCH_STATUS_OFF
		}
		if gameSwitch.AppealSwitch != uint32(pb.EsportSwitchStatus_SWITCH_STATUS_ON) {
			tmpSwitch.AppealSwitchStatus = pb.EsportSwitchStatus_SWITCH_STATUS_OFF
		}
		if gameSwitch.SidebarSwitch != uint32(pb.EsportSwitchStatus_SWITCH_STATUS_ON) {
			tmpSwitch.SidebarSwitchStatus = pb.EsportSwitchStatus_SWITCH_STATUS_OFF
		}
	} else { // 默认on
		tmpSwitch.HomepageSwitchStatus = pb.EsportSwitchStatus_SWITCH_STATUS_OFF
		tmpSwitch.AppealSwitchStatus = pb.EsportSwitchStatus_SWITCH_STATUS_OFF
		tmpSwitch.SidebarSwitchStatus = pb.EsportSwitchStatus_SWITCH_STATUS_OFF
	}
	return tmpSwitch
}

func (bm *BusinessConfManager) Watch(file string) {
	log.Infof("Watch start. file:%s", file)

	for {
		select {
		case _, ok := <-bm.Done:
			if !ok {
				log.Infof("Watch done")
				return
			}

		case <-time.After(30 * time.Second):
			log.Debugf("Watch check change")

			err := bm.Reload(file)
			if err != nil {
				log.Errorf("Watch Reload fail. file:%s, err:%v", file, err)
			}
		}
	}
}

func (bm *BusinessConfManager) Close() {
	close(bm.Done)
}

func (c *BusinessConf) CheckConf() error {
	return nil
}

func (bm *BusinessConfManager) GetFileUrlPrefix() string {
	return bm.conf.FileUrlPrefix
}

func (bm *BusinessConfManager) GetGameListPageSize() uint32 {
	if bm.conf.GameListPageSize == 0 {
		// 默认6条
		return 6
	}
	return bm.conf.GameListPageSize
}

func (bm *BusinessConfManager) GetOpenSkillUrl() string {
	return bm.conf.OpenSkillUrl
}

// 默认开关打开
func (bm *BusinessConfManager) GetSwitch(ctx context.Context, uid uint32) *pb.SwitchStatus {
	out := &pb.SwitchStatus{}

	out.HomepageSwitchStatus = bm.EsportSwitch.HomepageSwitchStatus
	out.AppealSwitchStatus = bm.EsportSwitch.AppealSwitchStatus
	out.SidebarSwitchStatus = bm.EsportSwitch.SidebarSwitchStatus

	log.DebugWithCtx(ctx, "GetSwitchStatus:%+v", out)
	// 申诉开关不受白名单控制
	if bm.IsInSwitchWhiteList(uid) {
		out.MainSwitchStatus = pb.EsportSwitchStatus_SWITCH_STATUS_ON
		out.HomepageSwitchStatus = pb.EsportSwitchStatus_SWITCH_STATUS_ON
		out.SidebarSwitchStatus = pb.EsportSwitchStatus_SWITCH_STATUS_ON
		log.InfoWithCtx(ctx, "GetSwitchStatus finish, uid:%d in whiteList", uid)
		return out
	}

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return &pb.SwitchStatus{
			MainSwitchStatus:     pb.EsportSwitchStatus_SWITCH_STATUS_OFF,
			HomepageSwitchStatus: pb.EsportSwitchStatus_SWITCH_STATUS_OFF,
			AppealSwitchStatus:   pb.EsportSwitchStatus_SWITCH_STATUS_OFF,
			SidebarSwitchStatus:  pb.EsportSwitchStatus_SWITCH_STATUS_OFF,
		}
	}
	log.DebugWithCtx(ctx, "GetSwitch serviceInfo:%+v", serviceInfo)

	mainStatus := bm.getMarketClientMainSwitchStatus(ctx, serviceInfo.ClientType, serviceInfo.MarketID, serviceInfo.ClientVersion)
	if mainStatus != pb.EsportSwitchStatus_SWITCH_STATUS_ON {
		mainStatus = pb.EsportSwitchStatus_SWITCH_STATUS_OFF
	}
	// 主开关不影响退款开关
	if mainStatus == pb.EsportSwitchStatus_SWITCH_STATUS_OFF {
		out.MainSwitchStatus = pb.EsportSwitchStatus_SWITCH_STATUS_OFF
		out.HomepageSwitchStatus = pb.EsportSwitchStatus_SWITCH_STATUS_OFF
		out.SidebarSwitchStatus = pb.EsportSwitchStatus_SWITCH_STATUS_OFF
	} else {
		out.MainSwitchStatus = pb.EsportSwitchStatus_SWITCH_STATUS_ON
	}
	return out
}

func (bm *BusinessConfManager) getMarketClientMainSwitchStatus(ctx context.Context, clientType uint16, marketId, clientVersion uint32) pb.EsportSwitchStatus {
	if bm.ClientLimitVersion == nil {
		return pb.EsportSwitchStatus_SWITCH_STATUS_OFF
	}
	versionConf, ok := bm.ClientLimitVersion[marketId]
	if !ok {
		log.InfoWithCtx(ctx, "getMarketClientMainSwitchStatus unknown marketId:%d", marketId)
		return pb.EsportSwitchStatus_SWITCH_STATUS_OFF
	}
	log.DebugWithCtx(ctx, "getMarketClientMainSwitchStatus %+v, %d-%d-%d", versionConf, marketId, clientType, clientVersion)
	feature := versionConf.feature
	switch clientType {
	case protocol.ClientTypeANDROID:
		if uint32(feature.Android()) <= clientVersion {
			return pb.EsportSwitchStatus(versionConf.mainSwitch)
		} else {
			return pb.EsportSwitchStatus_SWITCH_STATUS_OFF
		}
	case protocol.ClientTypeIOS:
		if uint32(feature.Iphone()) <= clientVersion {
			return pb.EsportSwitchStatus(versionConf.mainSwitch)
		} else {
			return pb.EsportSwitchStatus_SWITCH_STATUS_OFF
		}
	case protocol.ClientTypeCAR:
		if uint32(feature.Car()) <= clientVersion {
			return pb.EsportSwitchStatus(versionConf.mainSwitch)
		} else {
			return pb.EsportSwitchStatus_SWITCH_STATUS_OFF
		}
	case protocol.ClientTypePcTT:
		if uint32(feature.Pc()) <= clientVersion {
			return pb.EsportSwitchStatus(versionConf.mainSwitch)
		} else {
			return pb.EsportSwitchStatus_SWITCH_STATUS_OFF
		}
	case protocol.ClientTypeTX_MINI:
		if uint32(feature.TxMini()) <= clientVersion {
			return pb.EsportSwitchStatus(versionConf.mainSwitch)
		} else {
			return pb.EsportSwitchStatus_SWITCH_STATUS_OFF
		}
	default:
		log.ErrorWithCtx(ctx, "getMarketClientMainSwitchStatus unknown clientType:%v", feature)
		return pb.EsportSwitchStatus_SWITCH_STATUS_OFF
	}
}

func (bm *BusinessConfManager) IsInSwitchWhiteList(uid uint32) bool {
	return bm.WhiteUidMap[uid]
}

func (bm *BusinessConfManager) GetMinimumPrice() uint32 {
	// 兜底1 T豆
	if bm.conf.MinimumPrice == 0 {
		return 1
	}
	return bm.conf.MinimumPrice
}

func (bm *BusinessConfManager) GetConfig() *BusinessConf {
	return bm.conf
}
