package conf

import (
	"context"
	redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
	pkgConfig "golang.52tt.com/pkg/config"
	pb "golang.52tt.com/protocol/services/esport-skill"
)

type IBusinessConfManager interface {
	Close()
	Reload(file string) error
	Watch(file string)

	GetFileUrlPrefix() string
	GetGameListPageSize() uint32
	GetOpenSkillUrl() string
	GetSwitch(ctx context.Context, uid uint32) *pb.SwitchStatus
	IsInSwitchWhiteList(uid uint32) bool
	GetMinimumPrice() uint32
	GetConfig() *BusinessConf
}

type IBusinessConf interface {
	CheckConf() error
	Parse(configFile string) (isChange bool, err error)
}

type StartConfig struct {
	// [optional] from startup arguments

	// from config file
	RedisConfig *redisConnect.RedisConfig `json:"redis"`

	MongoConfig *pkgConfig.MongoConfig            `json:"mongo"`
	KafkaMap    map[string]*pkgConfig.KafkaConfig `json:"kafkaMap"`
}
