package store

import (
	context "context"
	pb "golang.52tt.com/protocol/services/esport-skill"
	context0 "golang.org/x/net/context"
)

type IStore interface {
	AddEsportGameConfig(ctx context.Context, req *pb.AddEsportGameConfigRequest) (uint32, error)
	AddUserSkill(ctx context.Context, skill *UserSkillInfo) error
	AddUserSkillAuditHistory(ctx context.Context, skill *UserSkillAuditHistory) error
	AddUserSkillFreezeHistory(ctx context.Context, history *UserSkillFreezeHistoryEntity) error
	BatGetUserSkillFreezeStatus(ctx context.Context, uidList []uint32) ([]*UserSkillFreezeStatusEntity, error)
	BatUpsertUserSkillFreezeStatus(ctx context.Context, freezeList []*UserSkillFreezeStatusEntity) error
	BatchAddRenownedPlayer(ctx context.Context, itemList []*RenownedPlayer) error
	BatchCheckCoachHasGame(ctx context.Context, uidList []uint32, gameId uint32) (map[uint32]bool, error)
	BatchCreateIssuanceRecord(ctx context.Context, records []*IssuanceRecord) error
	BatchDeleteRenownedPlayers(ctx context.Context, ids []uint32) error
	BatchGetAuditSkill(ctx context.Context, req *pb.BatchGetAuditSkillRequest) (skills []*UserSkillAuditHistory, err error)
	BatchGetBasePriceSetting(ctx context.Context, coachIDList []uint32, gameIDList []uint32) ([]*CoachBasePriceSetting, error)
	BatchGetGameInfoByNames(ctx context.Context, nameList []string) ([]*pb.SimpleGameInfo, error)
	BatchGetIssuanceRecordByCoachIdsAndLabelIds(ctx context.Context, coachIds []uint32, labelIds []uint32) ([]*IssuanceRecord, error)
	BatchGetLabel(ctx context.Context, ids []uint32) ([]*Label, error)
	BatchGetLabelByIds(ctx context.Context, ids []uint32) (map[uint32]*Label, error)
	BatchGetSkillsById(ctx context.Context, idList []string) (skills map[string]*UserSkillInfo, err error)
	BatchGetSpecialLabelIssuanceRecords(c context.Context, coachIds []uint32, labelIds []uint32) ([]*IssuanceRecord, error)
	BatchGetUserGameSpecialLabel(c context0.Context, gameId uint32, uidList []uint32) (map[uint32][]uint32, error)
	BatchGetUserRenownedInfoByCoachIdAndGameIds(ctx context.Context, coachId uint32, gameIds []uint32) ([]*RenownedPlayer, error)
	BatchGetUserRenownedInfoByCoachIdsAndGameId(ctx context.Context, coachIds []uint32, gameId uint32) ([]*RenownedPlayer, error)
	BatchGetUserSkills(ctx context.Context, uidList []uint32) (skills []*UserSkillInfo, err error)
	BatchGetUserSpecialLabel(c context0.Context, uidList []uint32) (map[uint32]map[uint32][]uint32, error)
	BatchSetBasePriceSetting(ctx context.Context, setting []*CoachBasePriceSetting) error
	CheckAddLabelOrder(c context0.Context, labelType pb.LabelType, displayOrder uint32) error
	CheckUpdateLabelOrder(c context0.Context, labelType pb.LabelType, displayOrder uint32, id uint32) error
	Close(ctx context.Context) error
	CountIssuanceRecords(ctx context.Context, coachIds []uint32, labelId uint32, effectiveStartTime, effectiveEndTime int64, status IssuanceRecordStatus, labelType LabelType) (uint32, error)
	CountLabels(ctx context.Context, labelId uint32, labelType LabelType, gameId uint32) (uint32, error)
	CountRenownedPlayers(ctx context.Context, coachUid uint32, gameIdList []uint32) (uint32, error)
	CreateIssuanceRecord(ctx context.Context, record *IssuanceRecord) error
	CreateLabel(ctx context.Context, label *Label) error
	DelSkillByGameId(ctx context.Context, gameId uint32) error
	DelUserSkill(ctx context.Context, uid, gameId uint32) error
	DeleteAuditRecordByAuditToken(ctx context.Context, auditToken string) (err error)
	DeleteCoachBasePriceSetting(ctx context.Context, coachID, gameID uint32) error
	DeleteEsportGameConfig(ctx context.Context, req *pb.DeleteEsportGameConfigRequest) error
	DeleteIssuanceRecordByID(ctx context.Context, id uint32) error
	DeleteIssuanceRecordByLabelID(ctx context.Context, labelId uint32) error
	DeleteLabelByID(ctx context.Context, id uint32) error
	DeleteRenownedPlayerByID(ctx context.Context, id uint32) error
	DeleteUserSkillFreezeStatus(ctx context.Context, uid uint32, gameIdList []uint32) error
	GetAllGameConfig(ctx context.Context) ([]*GameConfig, error)
	GetAllGameSimpleInfo(ctx context.Context) (*pb.GetAllGameSimpleInfoResponse, error)
	GetAuditSkillCount(ctx context.Context, req *pb.BatchGetAuditSkillRequest) (count uint32, err error)
	GetCoachAvailableIssuanceRecord(ctx context.Context, coachId uint32) ([]*IssuanceRecord, error)
	GetCoachBasePriceSetting(ctx context.Context, coachID, gameID uint32) (*CoachBasePriceSetting, error)
	GetEffectiveIssuanceRecord(c context.Context, pageNum, pageSize int64) ([]*IssuanceRecord, error)
	GetEsportGameConfigCnt(ctx context.Context, name string, ty pb.GAME_TYPE) (int64, error)
	GetEsportGameConfigListByPage(ctx context.Context, req *pb.GetEsportGameConfigListByPageRequest) ([]*pb.EsportGameConfig, error)
	GetExpiredUserSkillFreezeStatus(ctx context.Context, ts int64) ([]*UserSkillFreezeStatusEntity, error)
	GetGameDetailById(ctx context.Context, id uint32) (*pb.EsportGameConfig, error)
	GetGameDetailByIds(ctx context.Context, ids []uint32) ([]*pb.EsportGameConfig, error)
	GetGameList(ctx context.Context, ty pb.GAME_TYPE, pageNum, pageSize uint32) (itemList []*pb.GameItem, err error)
	GetGameListByGameNameFuzzy(c context.Context, name string) ([]*pb.SimpleGameInfo, error)
	GetGameUserSkillsByPage(ctx context.Context, gameId uint32, page, pageSize int64) ([]*UserSkillInfo, error)
	GetIssuanceRecordByID(ctx context.Context, id uint32) (*IssuanceRecord, error)
	GetIssuanceRecords(ctx context.Context, pageNumber int32, pageSize int32, coachIds []uint32, labelId uint32, effectiveStartTime, effectiveEndTime int64, status IssuanceRecordStatus, labelType LabelType) ([]*IssuanceRecord, error)
	GetLabelByID(ctx context.Context, id uint32) (*Label, error)
	GetLabels(ctx context.Context, pageNumber int32, pageSize int32, labelId uint32, labelType LabelType, gameId uint32) ([]*Label, error)
	GetLabelsByCoachTypeAndGames(ctx context.Context, gameIds ...uint32) ([]*Label, error)
	GetRenownedPlayerByID(ctx context.Context, id uint32) (*RenownedPlayer, error)
	GetRenownedPlayers(ctx context.Context, pageNumber int32, pageSize int32, coachUid uint32, gameIdList []uint32) ([]*RenownedPlayer, error)
	GetSpecialLabelIssuanceRecord(c context.Context, pageNum, pageSize int64) ([]*IssuanceRecord, error)
	GetTopGameList(ctx context.Context, ty pb.GAME_TYPE) ([]*pb.GameItem, error)
	GetUserSkillAuditRecordByToken(ctx context.Context, auditToken string) (record *UserSkillAuditHistory, err error)
	GetUserSkillAuditRecordsByUidOrTokens(ctx context.Context, uid uint32, tokenList []string) (recordList []*UserSkillAuditHistory, err error)
	GetUserSkillByGameId(ctx context.Context, uid, gameId uint32) (skill *UserSkillInfo, err error)
	GetUserSkillFreezeStatus(ctx context.Context, uid uint32) (map[uint32]*UserSkillFreezeStatusEntity, error)
	GetUserSkills(ctx context.Context, uid uint32) (skills []*UserSkillInfo, err error)
	GetUserSkillsWithGuaranteeWin(ctx context.Context) ([]*UserSkillInfo, error)
	IsCoachHasGame(ctx context.Context, uid uint32, gameName string) (*UserSkillInfo, error)
	IsRenownedPlayerExist(ctx context.Context, uid, gameId uint32) (bool, error)
	SearchUserSkillFreezeHistory(ctx context.Context, uidList []uint32, guildId, gameId uint32, page, pageSize int32) ([]*UserSkillFreezeHistoryEntity, error)
	SearchUserSkillFreezeHistoryCnt(ctx context.Context, uidList []uint32, guildId, gameId uint32) (int64, error)
	SetGameGuaranteeWin(ctx context.Context, uid, gameId uint32, isGuaranteeWin bool) error
	SetUserAudioAuditType(ctx context.Context, auditType uint32, auditToken string) (err error)
	SetUserSkillAudio(ctx context.Context, uid, gameId, audioDuration uint32, audio string) (err error)
	SetUserSkillAuditType(ctx context.Context, auditType uint32, auditToken, reason, operator string) (err error)
	SetUserSkillImgAndSection(ctx context.Context, uid, gameId uint32, skillEvidence string, info []*SectionInfo) (err error)
	SetUserSkillImgAuditType(ctx context.Context, auditType uint32, auditToken string) (err error)
	SetUserSkillSection(ctx context.Context, uid, gameId uint32, info []*SectionInfo) (err error)
	SetUserSkillTextAuditType(ctx context.Context, auditType uint32, auditToken string) (err error)
	SetUserSkillTextDesc(ctx context.Context, uid, gameId uint32, text string) (err error)
	UpdateEsportGameConfig(ctx context.Context, req *pb.UpdateEsportGameConfigRequest) error
	UpdateIssuanceRecord(ctx context.Context, record *IssuanceRecord) error
	UpdateLabel(ctx context.Context, label *Label) error
	UpdateLabelPriceAdditionalSwitch(ctx context.Context, coachId, labelId uint32, priceAdditionalSwitch bool) error
	UpdateRenownedPlayer(ctx context.Context, player *RenownedPlayer) error
	UpdateUserSpecialLabel(c context0.Context, request *pb.UpdateUserSpecialLabelRequest) error
	UpsertCoachBasePriceSetting(ctx context.Context, setting *CoachBasePriceSetting) error
}

type IUserSkillAuditHistory interface {
	CheckDeleteAudio() bool
	CheckImgAndAudioPassRisk() bool
	CheckModifyTypeSkillImgAndSection() bool
	CheckModifyTypeSkillSectionNormal() bool
	CheckModifyTypeSkillTextAndAudio() bool
	NeedGuild() bool
	PassRisk() bool
}

type IUserSkillFreezeStatusEntity interface {
	GetFreezeStopTs() int64
	GetFreezeType() uint32
}
