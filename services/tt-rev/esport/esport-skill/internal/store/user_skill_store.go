package store

import (
	"context"
	"fmt"
	tyrMongo "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	"google.golang.org/grpc/codes"
	"time"

	"gitlab.ttyuyin.com/tt-infra/tyr/log"

	"errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type SectionInfo struct {
	SectionName string   `bson:"section_name"` // 属性名称 eg:段位信息/擅长位置
	ItemList    []string `bson:"item_list"`    //  属性列表
	SectionId   uint32   `bson:"section_id"`
}

// UserSkillInfo 用户技能表(审核通过的)
type UserSkillInfo struct {
	ID             string         `bson:"_id"`              // _id
	Uid            uint32         `bson:"uid"`              // uid
	GameId         uint32         `bson:"game_id"`          // game_id
	GameRank       uint32         `bson:"game_rank"`        // game_rank
	GameName       string         `bson:"game_name"`        // game_name
	SkillEvidence  string         `bson:"skill_evidence"`   // 技能图
	TextDesc       string         `bson:"text_desc"`        // 文字描述
	Audio          string         `bson:"audio"`            // 语音介绍
	AudioDuration  uint32         `bson:"audio_duration"`   // 语音介绍时长
	SectionList    []*SectionInfo `bson:"section_list"`     // []*SectionInfo  游戏资料
	IsGuaranteeWin bool           `bson:"is_guarantee_win"` // 是否保胜
	CreateTime     time.Time      `bson:"create_time"`      // 创建时间
	UpdateTime     time.Time      `bson:"update_time"`      // 更新时间
}

func (s *Store) AddUserSkill(ctx context.Context, skill *UserSkillInfo) error {
	skill.ID = fmt.Sprintf("%d_%d", skill.Uid, skill.GameId)
	log.Infof("AddUserSkill skill %+v", skill)
	_, err := s.skillColl.InsertOne(ctx, skill)
	if err != nil {
		log.Infof("AddUserSkill skill:%+v, err:%v", skill, err)
		return err
	}
	return err
}

func (s *Store) BatchGetSkillsById(ctx context.Context, idList []string) (skills map[string]*UserSkillInfo, err error) {
	iter, err := s.skillColl.Collection.Find(ctx, bson.M{"_id": bson.M{"$in": idList}})
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetSkillsById idList:%v, err:%v", idList, err)
		return nil, err
	}

	skills = make(map[string]*UserSkillInfo)
	for iter.Next(ctx) {
		var skill UserSkillInfo
		if err := iter.Decode(&skill); err != nil {
			log.ErrorWithCtx(ctx, "BatchGetSkillsById idList:%v, err:%v", idList, err)
			return nil, err
		}
		skills[skill.ID] = &skill
	}
	return skills, nil
}

func (s *Store) GetUserSkillByGameId(ctx context.Context, uid, gameId uint32) (skill *UserSkillInfo, err error) {
	res := s.skillColl.Collection.FindOne(ctx, bson.M{"uid": uid, "game_id": gameId})
	if res.Err() != nil {
		if errors.Is(res.Err(), mongo.ErrNoDocuments) {
			log.WarnWithCtx(ctx, "GetUserSkillByGameId uid:%d, gameId:%d", uid, gameId)
			return nil, nil
		}

		log.ErrorWithCtx(ctx, "GetUserSkillByGameId uid:%d, err:%v", uid, err)
		return nil, err
	}

	err = res.Decode(&skill)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserSkillByGameId uid:%d, err:%v", uid, err)
		return nil, err
	}
	return skill, err
}

func (s *Store) GetUserSkills(ctx context.Context, uid uint32) (skills []*UserSkillInfo, err error) {
	iter, err := s.skillColl.Collection.Find(ctx, bson.M{"uid": uid})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserSkills uid:%d, err:%v", uid, err)
		return skills, err
	}

	err = iter.All(ctx, &skills)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserSkills uid:%d, err:%v", uid, err)
		return skills, err
	}
	return skills, err
}

func (s *Store) BatchGetUserSkills(ctx context.Context, uidList []uint32) (skills []*UserSkillInfo, err error) {
	iter, err := s.skillColl.Collection.Find(ctx, bson.M{"uid": bson.M{"$in": uidList}}, options.Find().SetSort(bson.M{"create_time": 1}))
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetUserSkills uid:%d, err:%v", uidList, err)
		return nil, err
	}

	err = iter.All(ctx, &skills)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetUserSkills uidList:%d, err:%v", uidList, err)
		return skills, err
	}
	return skills, err
}

func (s *Store) DelUserSkill(ctx context.Context, uid, gameId uint32) error {
	log.InfoWithCtx(ctx, "DelUserSkill uid:%d, gameId:%d", uid, gameId)
	if gameId == 0 {
		_, err := s.skillColl.DeleteMany(ctx, bson.M{"uid": uid})
		if err != nil {
			if errors.Is(err, tyrMongo.NoDocuments) {
				log.WarnWithCtx(ctx, "DelUserSkill uid:%d, gameId:%d", uid, gameId)
				return nil
			}
			log.ErrorWithCtx(ctx, "DelUserSkill uid:%d, err:%v", uid, err)
			return err
		}

	} else {
		_, err := s.skillColl.DeleteOne(ctx, bson.M{"_id": GetUserSkillId(uid, gameId)})
		if err != nil {
			if errors.Is(err, tyrMongo.NoDocuments) {
				log.WarnWithCtx(ctx, "DelUserSkill uid:%d, gameId:%d", uid, gameId)
				return nil
			}
			log.ErrorWithCtx(ctx, "DelUserSkill uid:%d, err:%v", uid, err)
			return err
		}
	}
	log.InfoWithCtx(ctx, "DelUserSkill finish, uid:%d, gameId:%d", uid, gameId)
	return nil
}

func (s *Store) DelSkillByGameId(ctx context.Context, gameId uint32) error {
	log.DebugWithCtx(ctx, "DelSkillByGameId gameId:%d", gameId)
	deletedCount, err := s.skillColl.DeleteMany(ctx, bson.M{"game_id": gameId})
	if err != nil {
		log.ErrorWithCtx(ctx, "DelSkillByGameId gameId:%d, err:%v", gameId, err)
		return err
	}
	log.InfoWithCtx(ctx, "DelSkillByGameId finish, gameId:%d, deletedCount:%d", gameId, deletedCount)
	return nil
}

func (s *Store) SetUserSkillTextDesc(ctx context.Context, uid, gameId uint32, text string) (err error) {
	update := bson.M{"$set": bson.M{"text_desc": text}}
	_, err = s.skillColl.UpdateOne(ctx, bson.M{"_id": GetUserSkillId(uid, gameId)}, update)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetUserSkillTextDesc auditToken:%s, err:%v", uid, gameId, err)
		return err
	}
	return nil
}

func (s *Store) SetUserSkillAudio(ctx context.Context, uid, gameId, audioDuration uint32, audio string) (err error) {
	update := bson.M{"$set": bson.M{"audio_duration": audioDuration, "audio": audio}}
	_, err = s.skillColl.UpdateOne(ctx, bson.M{"_id": GetUserSkillId(uid, gameId)}, update)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetUserSkillAudio auditToken:%s, err:%v", uid, gameId, err)
		return err
	}
	return nil
}

func (s *Store) SetUserSkillImgAndSection(ctx context.Context, uid, gameId uint32, skillEvidence string, info []*SectionInfo) (err error) {
	update := bson.M{"$set": bson.M{"section_list": info, "skill_evidence": skillEvidence, "update_time": time.Now()}}
	_, err = s.skillColl.UpdateOne(ctx, bson.M{"_id": GetUserSkillId(uid, gameId)}, update)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetUserImgAndSection auditToken:%s, err:%v", uid, gameId, err)
		return err
	}
	return nil
}

func (s *Store) SetUserSkillSection(ctx context.Context, uid, gameId uint32, info []*SectionInfo) (err error) {
	update := bson.M{"$set": bson.M{"section_list": info, "update_time": time.Now()}}
	_, err = s.skillColl.UpdateOne(ctx, bson.M{"_id": GetUserSkillId(uid, gameId)}, update)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetUserImgAndSection auditToken:%s, err:%v", uid, gameId, err)
		return err
	}
	return nil
}

func (s *Store) IsCoachHasGame(ctx context.Context, uid uint32, gameName string) (*UserSkillInfo, error) {
	res, err := s.skillColl.SelectList(ctx, bson.M{"uid": uid, "game_name": gameName}, bson.M{})
	if err != nil {
		log.ErrorWithCtx(ctx, "IsCoachHasGame uid:%d, gameName:%s, err:%v", uid, gameName, err)
		return nil, err
	}
	if res.Next(ctx) {
		var skill UserSkillInfo
		if err := res.Decode(&skill); err != nil {
			log.ErrorWithCtx(ctx, "IsCoachHasGame uid:%d, gameName:%s, err:%v", uid, gameName, err)
			return nil, err
		}
		return &skill, nil
	}
	return nil, nil
}

func (s *Store) BatchCheckCoachHasGame(ctx context.Context, uidList []uint32, gameId uint32) (map[uint32]bool, error) {
	res, err := s.skillColl.SelectList(ctx, bson.M{"uid": bson.M{"$in": uidList}, "game_id": gameId}, bson.M{"uid": 1})
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchCheckCoachHasGame uidList:%v, gameId:%d, err:%v", uidList, gameId, err)
		return nil, err
	}
	defer res.Close(ctx)
	ret := make(map[uint32]bool)
	for res.Next(ctx) {
		var skill UserSkillInfo
		if err := res.Decode(&skill); err != nil {
			log.ErrorWithCtx(ctx, "BatchCheckCoachHasGame uidList:%v, gameId:%d, err:%v", uidList, gameId, err)
			return nil, err
		}
		ret[skill.Uid] = true
	}
	return ret, nil
}

func GetUserSkillId(uid, gameId uint32) string {
	return fmt.Sprintf("%d_%d", uid, gameId)
}

// SetGameGuaranteeWin 设置包赢
func (s *Store) SetGameGuaranteeWin(ctx context.Context, uid, gameId uint32, isGuaranteeWin bool) error {
	update := bson.M{"$set": bson.M{"is_guarantee_win": isGuaranteeWin}}
	_, err := s.skillColl.UpdateOne(ctx, bson.M{"_id": GetUserSkillId(uid, gameId)}, update)
	if err != nil {
		if errors.Is(err, tyrMongo.NoDocuments) {
			log.ErrorWithCtx(ctx, "SetGameGuaranteeWin uid:%d, gameId:%d not found", uid, gameId)
			return protocol.NewExactServerError(codes.OK, status.ErrEsportsSkillNotFound)
		}
		log.ErrorWithCtx(ctx, "SetGameGuaranteeWin uid:%d, gameId:%d, err:%v", uid, gameId, err)
		return err
	}
	log.InfoWithCtx(ctx, "SetGameGuaranteeWin uid:%d, gameId:%d success, isGuaranteeWin:%v", uid, gameId, isGuaranteeWin)
	return nil
}

func (s *Store) GetGameUserSkillsByPage(ctx context.Context, gameId uint32, page, pageSize int64) ([]*UserSkillInfo, error) {
	skillInfoList := make([]*UserSkillInfo, 0)
	res, err := s.skillColl.Collection.Find(ctx, bson.M{"game_id": gameId}, options.Find().SetSkip(page*pageSize).SetLimit(pageSize))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameUsersByPage gameId:%d, page:%d, pageSize:%d, err:%v", gameId, page, pageSize, err)
		return nil, err
	}
	defer res.Close(ctx)
	for res.Next(ctx) {
		var skill *UserSkillInfo
		if err := res.Decode(&skill); err != nil {
			log.ErrorWithCtx(ctx, "GetGameUsersByPage gameId:%d, page:%d, pageSize:%d, err:%v", gameId, page, pageSize, err)
			return nil, err
		}
		fmt.Println(skill)
		skillInfoList = append(skillInfoList, skill)
	}
	return skillInfoList, nil
}

func (s *Store) GetUserSkillsWithGuaranteeWin(ctx context.Context) ([]*UserSkillInfo, error) {
	cursor, err := s.skillColl.Collection.Find(ctx, bson.M{"is_guarantee_win": true})
	if err != nil {
		return nil, err
	}
	var result []*UserSkillInfo
	err = cursor.All(ctx, &result)
	return result, err
}
