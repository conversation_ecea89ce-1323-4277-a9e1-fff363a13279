// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/tt-rev/esport/esport-hall/internal/store (interfaces: IStore)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	primitive "go.mongodb.org/mongo-driver/bson/primitive"
	store "golang.52tt.com/services/tt-rev/esport/esport-hall/internal/store"
)

// MockIStore is a mock of IStore interface.
type MockIStore struct {
	ctrl     *gomock.Controller
	recorder *MockIStoreMockRecorder
}

// MockIStoreMockRecorder is the mock recorder for MockIStore.
type MockIStoreMockRecorder struct {
	mock *MockIStore
}

// NewMockIStore creates a new mock instance.
func NewMockIStore(ctrl *gomock.Controller) *MockIStore {
	mock := &MockIStore{ctrl: ctrl}
	mock.recorder = &MockIStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIStore) EXPECT() *MockIStoreMockRecorder {
	return m.recorder
}

// AddCoachIncentiveAddition mocks base method.
func (m *MockIStore) AddCoachIncentiveAddition(arg0 context.Context, arg1 *store.CoachIncentiveAddition) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddCoachIncentiveAddition", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddCoachIncentiveAddition indicates an expected call of AddCoachIncentiveAddition.
func (mr *MockIStoreMockRecorder) AddCoachIncentiveAddition(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddCoachIncentiveAddition", reflect.TypeOf((*MockIStore)(nil).AddCoachIncentiveAddition), arg0, arg1)
}

// AddCoachRecommend mocks base method.
func (m *MockIStore) AddCoachRecommend(arg0 context.Context, arg1 *store.CoachRecommend) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddCoachRecommend", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddCoachRecommend indicates an expected call of AddCoachRecommend.
func (mr *MockIStoreMockRecorder) AddCoachRecommend(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddCoachRecommend", reflect.TypeOf((*MockIStore)(nil).AddCoachRecommend), arg0, arg1)
}

// AddFirstRoundOrderRecord mocks base method.
func (m *MockIStore) AddFirstRoundOrderRecord(arg0 context.Context, arg1 *store.FirstRoundOrderRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddFirstRoundOrderRecord", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddFirstRoundOrderRecord indicates an expected call of AddFirstRoundOrderRecord.
func (mr *MockIStoreMockRecorder) AddFirstRoundOrderRecord(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddFirstRoundOrderRecord", reflect.TypeOf((*MockIStore)(nil).AddFirstRoundOrderRecord), arg0, arg1)
}

// AddNewCustomerOrderRecord mocks base method.
func (m *MockIStore) AddNewCustomerOrderRecord(arg0 context.Context, arg1 *store.NewCustomerOrderRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddNewCustomerOrderRecord", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddNewCustomerOrderRecord indicates an expected call of AddNewCustomerOrderRecord.
func (mr *MockIStoreMockRecorder) AddNewCustomerOrderRecord(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddNewCustomerOrderRecord", reflect.TypeOf((*MockIStore)(nil).AddNewCustomerOrderRecord), arg0, arg1)
}

// BatchFindSkillProductByProductIds mocks base method.
func (m *MockIStore) BatchFindSkillProductByProductIds(arg0 context.Context, arg1 []uint32) (map[uint32]*store.SkillProduct, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchFindSkillProductByProductIds", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]*store.SkillProduct)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchFindSkillProductByProductIds indicates an expected call of BatchFindSkillProductByProductIds.
func (mr *MockIStoreMockRecorder) BatchFindSkillProductByProductIds(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchFindSkillProductByProductIds", reflect.TypeOf((*MockIStore)(nil).BatchFindSkillProductByProductIds), arg0, arg1)
}

// BatchFindSkillProductByUid mocks base method.
func (m *MockIStore) BatchFindSkillProductByUid(arg0 context.Context, arg1 []uint32, arg2 uint32) (map[uint32]*store.SkillProduct, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchFindSkillProductByUid", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[uint32]*store.SkillProduct)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchFindSkillProductByUid indicates an expected call of BatchFindSkillProductByUid.
func (mr *MockIStoreMockRecorder) BatchFindSkillProductByUid(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchFindSkillProductByUid", reflect.TypeOf((*MockIStore)(nil).BatchFindSkillProductByUid), arg0, arg1, arg2)
}

// BatchGetUSerSetting mocks base method.
func (m *MockIStore) BatchGetUSerSetting(arg0 context.Context, arg1 []uint32) (map[uint32]*store.UserSetting, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUSerSetting", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]*store.UserSetting)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUSerSetting indicates an expected call of BatchGetUSerSetting.
func (mr *MockIStoreMockRecorder) BatchGetUSerSetting(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUSerSetting", reflect.TypeOf((*MockIStore)(nil).BatchGetUSerSetting), arg0, arg1)
}

// BatchInsertSkillProduct mocks base method.
func (m *MockIStore) BatchInsertSkillProduct(arg0 context.Context, arg1 []*store.SkillProduct) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchInsertSkillProduct", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchInsertSkillProduct indicates an expected call of BatchInsertSkillProduct.
func (mr *MockIStoreMockRecorder) BatchInsertSkillProduct(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchInsertSkillProduct", reflect.TypeOf((*MockIStore)(nil).BatchInsertSkillProduct), arg0, arg1)
}

// BatchUpdateSkillProduct mocks base method.
func (m *MockIStore) BatchUpdateSkillProduct(arg0 context.Context, arg1 []*store.SkillProduct) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdateSkillProduct", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchUpdateSkillProduct indicates an expected call of BatchUpdateSkillProduct.
func (mr *MockIStoreMockRecorder) BatchUpdateSkillProduct(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateSkillProduct", reflect.TypeOf((*MockIStore)(nil).BatchUpdateSkillProduct), arg0, arg1)
}

// ClearFirstRoundOrderRecord mocks base method.
func (m *MockIStore) ClearFirstRoundOrderRecord(arg0 context.Context, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearFirstRoundOrderRecord", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// ClearFirstRoundOrderRecord indicates an expected call of ClearFirstRoundOrderRecord.
func (mr *MockIStoreMockRecorder) ClearFirstRoundOrderRecord(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearFirstRoundOrderRecord", reflect.TypeOf((*MockIStore)(nil).ClearFirstRoundOrderRecord), arg0, arg1, arg2, arg3)
}

// ClearNewCustomerOrderRecord mocks base method.
func (m *MockIStore) ClearNewCustomerOrderRecord(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearNewCustomerOrderRecord", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ClearNewCustomerOrderRecord indicates an expected call of ClearNewCustomerOrderRecord.
func (mr *MockIStoreMockRecorder) ClearNewCustomerOrderRecord(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearNewCustomerOrderRecord", reflect.TypeOf((*MockIStore)(nil).ClearNewCustomerOrderRecord), arg0, arg1)
}

// Close mocks base method.
func (m *MockIStore) Close(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockIStoreMockRecorder) Close(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIStore)(nil).Close), arg0)
}

// CloseCoachAllNewCustomerSwitch mocks base method.
func (m *MockIStore) CloseCoachAllNewCustomerSwitch(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CloseCoachAllNewCustomerSwitch", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CloseCoachAllNewCustomerSwitch indicates an expected call of CloseCoachAllNewCustomerSwitch.
func (mr *MockIStoreMockRecorder) CloseCoachAllNewCustomerSwitch(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CloseCoachAllNewCustomerSwitch", reflect.TypeOf((*MockIStore)(nil).CloseCoachAllNewCustomerSwitch), arg0, arg1)
}

// CntCoachRecommend mocks base method.
func (m *MockIStore) CntCoachRecommend(arg0 context.Context, arg1 string, arg2, arg3, arg4, arg5 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CntCoachRecommend", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CntCoachRecommend indicates an expected call of CntCoachRecommend.
func (mr *MockIStoreMockRecorder) CntCoachRecommend(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CntCoachRecommend", reflect.TypeOf((*MockIStore)(nil).CntCoachRecommend), arg0, arg1, arg2, arg3, arg4, arg5)
}

// DelCoachRecommend mocks base method.
func (m *MockIStore) DelCoachRecommend(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelCoachRecommend", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelCoachRecommend indicates an expected call of DelCoachRecommend.
func (mr *MockIStoreMockRecorder) DelCoachRecommend(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelCoachRecommend", reflect.TypeOf((*MockIStore)(nil).DelCoachRecommend), arg0, arg1)
}

// DelSkillProduct mocks base method.
func (m *MockIStore) DelSkillProduct(arg0 context.Context, arg1, arg2 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelSkillProduct", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelSkillProduct indicates an expected call of DelSkillProduct.
func (mr *MockIStoreMockRecorder) DelSkillProduct(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelSkillProduct", reflect.TypeOf((*MockIStore)(nil).DelSkillProduct), arg0, arg1, arg2)
}

// DistinctSkillId mocks base method.
func (m *MockIStore) DistinctSkillId(arg0 context.Context) []uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DistinctSkillId", arg0)
	ret0, _ := ret[0].([]uint32)
	return ret0
}

// DistinctSkillId indicates an expected call of DistinctSkillId.
func (mr *MockIStoreMockRecorder) DistinctSkillId(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DistinctSkillId", reflect.TypeOf((*MockIStore)(nil).DistinctSkillId), arg0)
}

// DistinctUidBySkillId mocks base method.
func (m *MockIStore) DistinctUidBySkillId(arg0 context.Context, arg1 uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DistinctUidBySkillId", arg0, arg1)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DistinctUidBySkillId indicates an expected call of DistinctUidBySkillId.
func (mr *MockIStoreMockRecorder) DistinctUidBySkillId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DistinctUidBySkillId", reflect.TypeOf((*MockIStore)(nil).DistinctUidBySkillId), arg0, arg1)
}

// DistnctUid mocks base method.
func (m *MockIStore) DistnctUid(arg0 context.Context) []uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DistnctUid", arg0)
	ret0, _ := ret[0].([]uint32)
	return ret0
}

// DistnctUid indicates an expected call of DistnctUid.
func (mr *MockIStoreMockRecorder) DistnctUid(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DistnctUid", reflect.TypeOf((*MockIStore)(nil).DistnctUid), arg0)
}

// EnsureIndex mocks base method.
func (m *MockIStore) EnsureIndex(arg0 context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "EnsureIndex", arg0)
}

// EnsureIndex indicates an expected call of EnsureIndex.
func (mr *MockIStoreMockRecorder) EnsureIndex(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnsureIndex", reflect.TypeOf((*MockIStore)(nil).EnsureIndex), arg0)
}

// EnsureIndexCoachIncentiveAddition mocks base method.
func (m *MockIStore) EnsureIndexCoachIncentiveAddition(arg0 context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "EnsureIndexCoachIncentiveAddition", arg0)
}

// EnsureIndexCoachIncentiveAddition indicates an expected call of EnsureIndexCoachIncentiveAddition.
func (mr *MockIStoreMockRecorder) EnsureIndexCoachIncentiveAddition(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnsureIndexCoachIncentiveAddition", reflect.TypeOf((*MockIStore)(nil).EnsureIndexCoachIncentiveAddition), arg0)
}

// EnsureIndexCoachRecommend mocks base method.
func (m *MockIStore) EnsureIndexCoachRecommend(arg0 context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "EnsureIndexCoachRecommend", arg0)
}

// EnsureIndexCoachRecommend indicates an expected call of EnsureIndexCoachRecommend.
func (mr *MockIStoreMockRecorder) EnsureIndexCoachRecommend(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnsureIndexCoachRecommend", reflect.TypeOf((*MockIStore)(nil).EnsureIndexCoachRecommend), arg0)
}

// EnsureIndexFirstRoundOrderRecord mocks base method.
func (m *MockIStore) EnsureIndexFirstRoundOrderRecord(arg0 context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "EnsureIndexFirstRoundOrderRecord", arg0)
}

// EnsureIndexFirstRoundOrderRecord indicates an expected call of EnsureIndexFirstRoundOrderRecord.
func (mr *MockIStoreMockRecorder) EnsureIndexFirstRoundOrderRecord(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnsureIndexFirstRoundOrderRecord", reflect.TypeOf((*MockIStore)(nil).EnsureIndexFirstRoundOrderRecord), arg0)
}

// EnsureIndexInviteOrderLog mocks base method.
func (m *MockIStore) EnsureIndexInviteOrderLog(arg0 context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "EnsureIndexInviteOrderLog", arg0)
}

// EnsureIndexInviteOrderLog indicates an expected call of EnsureIndexInviteOrderLog.
func (mr *MockIStoreMockRecorder) EnsureIndexInviteOrderLog(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnsureIndexInviteOrderLog", reflect.TypeOf((*MockIStore)(nil).EnsureIndexInviteOrderLog), arg0)
}

// EnsureIndexNewCustomerOrderRecord mocks base method.
func (m *MockIStore) EnsureIndexNewCustomerOrderRecord(arg0 context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "EnsureIndexNewCustomerOrderRecord", arg0)
}

// EnsureIndexNewCustomerOrderRecord indicates an expected call of EnsureIndexNewCustomerOrderRecord.
func (mr *MockIStoreMockRecorder) EnsureIndexNewCustomerOrderRecord(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnsureIndexNewCustomerOrderRecord", reflect.TypeOf((*MockIStore)(nil).EnsureIndexNewCustomerOrderRecord), arg0)
}

// EnsureIndexQuickReceiveRecord mocks base method.
func (m *MockIStore) EnsureIndexQuickReceiveRecord(arg0 context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "EnsureIndexQuickReceiveRecord", arg0)
}

// EnsureIndexQuickReceiveRecord indicates an expected call of EnsureIndexQuickReceiveRecord.
func (mr *MockIStoreMockRecorder) EnsureIndexQuickReceiveRecord(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnsureIndexQuickReceiveRecord", reflect.TypeOf((*MockIStore)(nil).EnsureIndexQuickReceiveRecord), arg0)
}

// EnsureIndexSkillProduct mocks base method.
func (m *MockIStore) EnsureIndexSkillProduct(arg0 context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "EnsureIndexSkillProduct", arg0)
}

// EnsureIndexSkillProduct indicates an expected call of EnsureIndexSkillProduct.
func (mr *MockIStoreMockRecorder) EnsureIndexSkillProduct(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnsureIndexSkillProduct", reflect.TypeOf((*MockIStore)(nil).EnsureIndexSkillProduct), arg0)
}

// EnsureIndexUserSettingColl mocks base method.
func (m *MockIStore) EnsureIndexUserSettingColl(arg0 context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "EnsureIndexUserSettingColl", arg0)
}

// EnsureIndexUserSettingColl indicates an expected call of EnsureIndexUserSettingColl.
func (mr *MockIStoreMockRecorder) EnsureIndexUserSettingColl(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnsureIndexUserSettingColl", reflect.TypeOf((*MockIStore)(nil).EnsureIndexUserSettingColl), arg0)
}

// Find15MinAgoInviteOrderLog mocks base method.
func (m *MockIStore) Find15MinAgoInviteOrderLog(arg0 context.Context) ([]*store.InviteOrderLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Find15MinAgoInviteOrderLog", arg0)
	ret0, _ := ret[0].([]*store.InviteOrderLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Find15MinAgoInviteOrderLog indicates an expected call of Find15MinAgoInviteOrderLog.
func (mr *MockIStoreMockRecorder) Find15MinAgoInviteOrderLog(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Find15MinAgoInviteOrderLog", reflect.TypeOf((*MockIStore)(nil).Find15MinAgoInviteOrderLog), arg0)
}

// FindBySkillId mocks base method.
func (m *MockIStore) FindBySkillId(arg0 context.Context, arg1 uint32) ([]*store.SkillProduct, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindBySkillId", arg0, arg1)
	ret0, _ := ret[0].([]*store.SkillProduct)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindBySkillId indicates an expected call of FindBySkillId.
func (mr *MockIStoreMockRecorder) FindBySkillId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindBySkillId", reflect.TypeOf((*MockIStore)(nil).FindBySkillId), arg0, arg1)
}

// FindEnableSkillProductByUid mocks base method.
func (m *MockIStore) FindEnableSkillProductByUid(arg0 context.Context, arg1 uint32) ([]*store.SkillProduct, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindEnableSkillProductByUid", arg0, arg1)
	ret0, _ := ret[0].([]*store.SkillProduct)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindEnableSkillProductByUid indicates an expected call of FindEnableSkillProductByUid.
func (mr *MockIStoreMockRecorder) FindEnableSkillProductByUid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindEnableSkillProductByUid", reflect.TypeOf((*MockIStore)(nil).FindEnableSkillProductByUid), arg0, arg1)
}

// FindFirstRoundOrderRecord mocks base method.
func (m *MockIStore) FindFirstRoundOrderRecord(arg0 context.Context, arg1 uint32, arg2 []uint32, arg3 uint32) ([]*store.FirstRoundOrderRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindFirstRoundOrderRecord", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*store.FirstRoundOrderRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindFirstRoundOrderRecord indicates an expected call of FindFirstRoundOrderRecord.
func (mr *MockIStoreMockRecorder) FindFirstRoundOrderRecord(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindFirstRoundOrderRecord", reflect.TypeOf((*MockIStore)(nil).FindFirstRoundOrderRecord), arg0, arg1, arg2, arg3)
}

// FindInviteOrderLogByInviteId mocks base method.
func (m *MockIStore) FindInviteOrderLogByInviteId(arg0 context.Context, arg1 uint32) (*store.InviteOrderLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindInviteOrderLogByInviteId", arg0, arg1)
	ret0, _ := ret[0].(*store.InviteOrderLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindInviteOrderLogByInviteId indicates an expected call of FindInviteOrderLogByInviteId.
func (mr *MockIStoreMockRecorder) FindInviteOrderLogByInviteId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindInviteOrderLogByInviteId", reflect.TypeOf((*MockIStore)(nil).FindInviteOrderLogByInviteId), arg0, arg1)
}

// FindQuickReceiveRecord mocks base method.
func (m *MockIStore) FindQuickReceiveRecord(arg0 context.Context, arg1 uint32, arg2, arg3 time.Time) ([]*store.QuickReceiveRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindQuickReceiveRecord", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*store.QuickReceiveRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindQuickReceiveRecord indicates an expected call of FindQuickReceiveRecord.
func (mr *MockIStoreMockRecorder) FindQuickReceiveRecord(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindQuickReceiveRecord", reflect.TypeOf((*MockIStore)(nil).FindQuickReceiveRecord), arg0, arg1, arg2, arg3)
}

// FindSkillProduct mocks base method.
func (m *MockIStore) FindSkillProduct(arg0 context.Context, arg1 []uint32, arg2, arg3 uint32) ([]*store.SkillProduct, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindSkillProduct", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*store.SkillProduct)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindSkillProduct indicates an expected call of FindSkillProduct.
func (mr *MockIStoreMockRecorder) FindSkillProduct(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindSkillProduct", reflect.TypeOf((*MockIStore)(nil).FindSkillProduct), arg0, arg1, arg2, arg3)
}

// FindSkillProductByCoachUidsAndGameId mocks base method.
func (m *MockIStore) FindSkillProductByCoachUidsAndGameId(arg0 context.Context, arg1 uint32, arg2 []uint32) ([]*store.SkillProduct, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindSkillProductByCoachUidsAndGameId", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*store.SkillProduct)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindSkillProductByCoachUidsAndGameId indicates an expected call of FindSkillProductByCoachUidsAndGameId.
func (mr *MockIStoreMockRecorder) FindSkillProductByCoachUidsAndGameId(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindSkillProductByCoachUidsAndGameId", reflect.TypeOf((*MockIStore)(nil).FindSkillProductByCoachUidsAndGameId), arg0, arg1, arg2)
}

// FindSkillProductByProductId mocks base method.
func (m *MockIStore) FindSkillProductByProductId(arg0 context.Context, arg1 uint32) (*store.SkillProduct, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindSkillProductByProductId", arg0, arg1)
	ret0, _ := ret[0].(*store.SkillProduct)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindSkillProductByProductId indicates an expected call of FindSkillProductByProductId.
func (mr *MockIStoreMockRecorder) FindSkillProductByProductId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindSkillProductByProductId", reflect.TypeOf((*MockIStore)(nil).FindSkillProductByProductId), arg0, arg1)
}

// FindSkillProductBySkillId mocks base method.
func (m *MockIStore) FindSkillProductBySkillId(arg0 context.Context, arg1, arg2 uint32) (*store.SkillProduct, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindSkillProductBySkillId", arg0, arg1, arg2)
	ret0, _ := ret[0].(*store.SkillProduct)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindSkillProductBySkillId indicates an expected call of FindSkillProductBySkillId.
func (mr *MockIStoreMockRecorder) FindSkillProductBySkillId(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindSkillProductBySkillId", reflect.TypeOf((*MockIStore)(nil).FindSkillProductBySkillId), arg0, arg1, arg2)
}

// FindSkillProductBySkillIdIgnoreStatus mocks base method.
func (m *MockIStore) FindSkillProductBySkillIdIgnoreStatus(arg0 context.Context, arg1, arg2 uint32) (*store.SkillProduct, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindSkillProductBySkillIdIgnoreStatus", arg0, arg1, arg2)
	ret0, _ := ret[0].(*store.SkillProduct)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindSkillProductBySkillIdIgnoreStatus indicates an expected call of FindSkillProductBySkillIdIgnoreStatus.
func (mr *MockIStoreMockRecorder) FindSkillProductBySkillIdIgnoreStatus(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindSkillProductBySkillIdIgnoreStatus", reflect.TypeOf((*MockIStore)(nil).FindSkillProductBySkillIdIgnoreStatus), arg0, arg1, arg2)
}

// FindSkillProductBySkillIdPaged mocks base method.
func (m *MockIStore) FindSkillProductBySkillIdPaged(arg0 context.Context, arg1 uint32, arg2, arg3 int) ([]*store.SkillProduct, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindSkillProductBySkillIdPaged", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*store.SkillProduct)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindSkillProductBySkillIdPaged indicates an expected call of FindSkillProductBySkillIdPaged.
func (mr *MockIStoreMockRecorder) FindSkillProductBySkillIdPaged(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindSkillProductBySkillIdPaged", reflect.TypeOf((*MockIStore)(nil).FindSkillProductBySkillIdPaged), arg0, arg1, arg2, arg3)
}

// FindSkillProductByUid mocks base method.
func (m *MockIStore) FindSkillProductByUid(arg0 context.Context, arg1 uint32) ([]*store.SkillProduct, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindSkillProductByUid", arg0, arg1)
	ret0, _ := ret[0].([]*store.SkillProduct)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindSkillProductByUid indicates an expected call of FindSkillProductByUid.
func (mr *MockIStoreMockRecorder) FindSkillProductByUid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindSkillProductByUid", reflect.TypeOf((*MockIStore)(nil).FindSkillProductByUid), arg0, arg1)
}

// FindSkillProductByUids mocks base method.
func (m *MockIStore) FindSkillProductByUids(arg0 context.Context, arg1 []uint32) ([]*store.SkillProduct, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindSkillProductByUids", arg0, arg1)
	ret0, _ := ret[0].([]*store.SkillProduct)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindSkillProductByUids indicates an expected call of FindSkillProductByUids.
func (mr *MockIStoreMockRecorder) FindSkillProductByUids(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindSkillProductByUids", reflect.TypeOf((*MockIStore)(nil).FindSkillProductByUids), arg0, arg1)
}

// GenInviteId mocks base method.
func (m *MockIStore) GenInviteId(arg0 context.Context) uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenInviteId", arg0)
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GenInviteId indicates an expected call of GenInviteId.
func (mr *MockIStoreMockRecorder) GenInviteId(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenInviteId", reflect.TypeOf((*MockIStore)(nil).GenInviteId), arg0)
}

// GenProductId mocks base method.
func (m *MockIStore) GenProductId(arg0 context.Context) uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenProductId", arg0)
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GenProductId indicates an expected call of GenProductId.
func (mr *MockIStoreMockRecorder) GenProductId(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenProductId", reflect.TypeOf((*MockIStore)(nil).GenProductId), arg0)
}

// GetAllFirstRoundCoachUids mocks base method.
func (m *MockIStore) GetAllFirstRoundCoachUids(arg0 context.Context) ([]*store.SkillProduct, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllFirstRoundCoachUids", arg0)
	ret0, _ := ret[0].([]*store.SkillProduct)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllFirstRoundCoachUids indicates an expected call of GetAllFirstRoundCoachUids.
func (mr *MockIStoreMockRecorder) GetAllFirstRoundCoachUids(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllFirstRoundCoachUids", reflect.TypeOf((*MockIStore)(nil).GetAllFirstRoundCoachUids), arg0)
}

// GetAllGuaranteeWinSwitch mocks base method.
func (m *MockIStore) GetAllGuaranteeWinSwitch(arg0 context.Context) ([]*store.GuaranteeWinSwitch, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllGuaranteeWinSwitch", arg0)
	ret0, _ := ret[0].([]*store.GuaranteeWinSwitch)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllGuaranteeWinSwitch indicates an expected call of GetAllGuaranteeWinSwitch.
func (mr *MockIStoreMockRecorder) GetAllGuaranteeWinSwitch(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllGuaranteeWinSwitch", reflect.TypeOf((*MockIStore)(nil).GetAllGuaranteeWinSwitch), arg0)
}

// GetAllSkillProductPaged mocks base method.
func (m *MockIStore) GetAllSkillProductPaged(arg0 context.Context, arg1, arg2 uint32) ([]*store.SkillProduct, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllSkillProductPaged", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*store.SkillProduct)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllSkillProductPaged indicates an expected call of GetAllSkillProductPaged.
func (mr *MockIStoreMockRecorder) GetAllSkillProductPaged(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllSkillProductPaged", reflect.TypeOf((*MockIStore)(nil).GetAllSkillProductPaged), arg0, arg1, arg2)
}

// GetCoachNewCustomerOrderCntToday mocks base method.
func (m *MockIStore) GetCoachNewCustomerOrderCntToday(arg0 context.Context, arg1 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCoachNewCustomerOrderCntToday", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCoachNewCustomerOrderCntToday indicates an expected call of GetCoachNewCustomerOrderCntToday.
func (mr *MockIStoreMockRecorder) GetCoachNewCustomerOrderCntToday(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCoachNewCustomerOrderCntToday", reflect.TypeOf((*MockIStore)(nil).GetCoachNewCustomerOrderCntToday), arg0, arg1)
}

// GetCoachRecommend mocks base method.
func (m *MockIStore) GetCoachRecommend(arg0 context.Context, arg1 string, arg2, arg3, arg4, arg5 uint32) ([]*store.CoachRecommend, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCoachRecommend", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].([]*store.CoachRecommend)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCoachRecommend indicates an expected call of GetCoachRecommend.
func (mr *MockIStoreMockRecorder) GetCoachRecommend(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCoachRecommend", reflect.TypeOf((*MockIStore)(nil).GetCoachRecommend), arg0, arg1, arg2, arg3, arg4, arg5)
}

// GetFirstRoundOrderCountToday mocks base method.
func (m *MockIStore) GetFirstRoundOrderCountToday(arg0 context.Context, arg1 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFirstRoundOrderCountToday", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFirstRoundOrderCountToday indicates an expected call of GetFirstRoundOrderCountToday.
func (mr *MockIStoreMockRecorder) GetFirstRoundOrderCountToday(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFirstRoundOrderCountToday", reflect.TypeOf((*MockIStore)(nil).GetFirstRoundOrderCountToday), arg0, arg1)
}

// GetUserSetting mocks base method.
func (m *MockIStore) GetUserSetting(arg0 context.Context, arg1 uint32) (*store.UserSetting, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserSetting", arg0, arg1)
	ret0, _ := ret[0].(*store.UserSetting)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserSetting indicates an expected call of GetUserSetting.
func (mr *MockIStoreMockRecorder) GetUserSetting(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSetting", reflect.TypeOf((*MockIStore)(nil).GetUserSetting), arg0, arg1)
}

// HasFirstRoundOrderRecord mocks base method.
func (m *MockIStore) HasFirstRoundOrderRecord(arg0 context.Context, arg1, arg2, arg3 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HasFirstRoundOrderRecord", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HasFirstRoundOrderRecord indicates an expected call of HasFirstRoundOrderRecord.
func (mr *MockIStoreMockRecorder) HasFirstRoundOrderRecord(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HasFirstRoundOrderRecord", reflect.TypeOf((*MockIStore)(nil).HasFirstRoundOrderRecord), arg0, arg1, arg2, arg3)
}

// HasNewCustomerOrderRecord mocks base method.
func (m *MockIStore) HasNewCustomerOrderRecord(arg0 context.Context, arg1 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HasNewCustomerOrderRecord", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HasNewCustomerOrderRecord indicates an expected call of HasNewCustomerOrderRecord.
func (mr *MockIStoreMockRecorder) HasNewCustomerOrderRecord(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HasNewCustomerOrderRecord", reflect.TypeOf((*MockIStore)(nil).HasNewCustomerOrderRecord), arg0, arg1)
}

// HasSkillProductOpenFirstRound mocks base method.
func (m *MockIStore) HasSkillProductOpenFirstRound(arg0 context.Context, arg1 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HasSkillProductOpenFirstRound", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HasSkillProductOpenFirstRound indicates an expected call of HasSkillProductOpenFirstRound.
func (mr *MockIStoreMockRecorder) HasSkillProductOpenFirstRound(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HasSkillProductOpenFirstRound", reflect.TypeOf((*MockIStore)(nil).HasSkillProductOpenFirstRound), arg0, arg1)
}

// HasSkillProductOpenNewCustomer mocks base method.
func (m *MockIStore) HasSkillProductOpenNewCustomer(arg0 context.Context, arg1 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HasSkillProductOpenNewCustomer", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HasSkillProductOpenNewCustomer indicates an expected call of HasSkillProductOpenNewCustomer.
func (mr *MockIStoreMockRecorder) HasSkillProductOpenNewCustomer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HasSkillProductOpenNewCustomer", reflect.TypeOf((*MockIStore)(nil).HasSkillProductOpenNewCustomer), arg0, arg1)
}

// IncCounter mocks base method.
func (m *MockIStore) IncCounter(arg0 context.Context, arg1 string) uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncCounter", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	return ret0
}

// IncCounter indicates an expected call of IncCounter.
func (mr *MockIStoreMockRecorder) IncCounter(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncCounter", reflect.TypeOf((*MockIStore)(nil).IncCounter), arg0, arg1)
}

// InsertInviteOrderLog mocks base method.
func (m *MockIStore) InsertInviteOrderLog(arg0 context.Context, arg1 *store.InviteOrderLog) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertInviteOrderLog", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertInviteOrderLog indicates an expected call of InsertInviteOrderLog.
func (mr *MockIStoreMockRecorder) InsertInviteOrderLog(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertInviteOrderLog", reflect.TypeOf((*MockIStore)(nil).InsertInviteOrderLog), arg0, arg1)
}

// InsertQuickReceiveRecord mocks base method.
func (m *MockIStore) InsertQuickReceiveRecord(arg0 context.Context, arg1 *store.QuickReceiveRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertQuickReceiveRecord", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertQuickReceiveRecord indicates an expected call of InsertQuickReceiveRecord.
func (mr *MockIStoreMockRecorder) InsertQuickReceiveRecord(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertQuickReceiveRecord", reflect.TypeOf((*MockIStore)(nil).InsertQuickReceiveRecord), arg0, arg1)
}

// SearchFirstRoundCoachList mocks base method.
func (m *MockIStore) SearchFirstRoundCoachList(arg0 context.Context, arg1, arg2 uint32, arg3 []uint32) ([]*store.SkillProduct, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchFirstRoundCoachList", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*store.SkillProduct)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchFirstRoundCoachList indicates an expected call of SearchFirstRoundCoachList.
func (mr *MockIStoreMockRecorder) SearchFirstRoundCoachList(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchFirstRoundCoachList", reflect.TypeOf((*MockIStore)(nil).SearchFirstRoundCoachList), arg0, arg1, arg2, arg3)
}

// SearchUserFirstRecordByPage mocks base method.
func (m *MockIStore) SearchUserFirstRecordByPage(arg0 context.Context, arg1 primitive.ObjectID, arg2 uint32, arg3 []uint32) ([]*store.FirstRoundOrderRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchUserFirstRecordByPage", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*store.FirstRoundOrderRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchUserFirstRecordByPage indicates an expected call of SearchUserFirstRecordByPage.
func (mr *MockIStoreMockRecorder) SearchUserFirstRecordByPage(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchUserFirstRecordByPage", reflect.TypeOf((*MockIStore)(nil).SearchUserFirstRecordByPage), arg0, arg1, arg2, arg3)
}

// SetCoachAllFirstRoundSwitch mocks base method.
func (m *MockIStore) SetCoachAllFirstRoundSwitch(arg0 context.Context, arg1 uint32, arg2 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetCoachAllFirstRoundSwitch", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetCoachAllFirstRoundSwitch indicates an expected call of SetCoachAllFirstRoundSwitch.
func (mr *MockIStoreMockRecorder) SetCoachAllFirstRoundSwitch(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetCoachAllFirstRoundSwitch", reflect.TypeOf((*MockIStore)(nil).SetCoachAllFirstRoundSwitch), arg0, arg1, arg2)
}

// SetCoachAllNewCustomerSwitch mocks base method.
func (m *MockIStore) SetCoachAllNewCustomerSwitch(arg0 context.Context, arg1 uint32, arg2 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetCoachAllNewCustomerSwitch", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetCoachAllNewCustomerSwitch indicates an expected call of SetCoachAllNewCustomerSwitch.
func (mr *MockIStoreMockRecorder) SetCoachAllNewCustomerSwitch(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetCoachAllNewCustomerSwitch", reflect.TypeOf((*MockIStore)(nil).SetCoachAllNewCustomerSwitch), arg0, arg1, arg2)
}

// SetFirstRoundSwitch mocks base method.
func (m *MockIStore) SetFirstRoundSwitch(arg0 context.Context, arg1, arg2 uint32, arg3 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetFirstRoundSwitch", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetFirstRoundSwitch indicates an expected call of SetFirstRoundSwitch.
func (mr *MockIStoreMockRecorder) SetFirstRoundSwitch(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetFirstRoundSwitch", reflect.TypeOf((*MockIStore)(nil).SetFirstRoundSwitch), arg0, arg1, arg2, arg3)
}

// SetGuaranteeWinSwitch mocks base method.
func (m *MockIStore) SetGuaranteeWinSwitch(arg0 context.Context, arg1, arg2 uint32, arg3 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetGuaranteeWinSwitch", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetGuaranteeWinSwitch indicates an expected call of SetGuaranteeWinSwitch.
func (mr *MockIStoreMockRecorder) SetGuaranteeWinSwitch(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetGuaranteeWinSwitch", reflect.TypeOf((*MockIStore)(nil).SetGuaranteeWinSwitch), arg0, arg1, arg2, arg3)
}

// SetNewCustomerSwitch mocks base method.
func (m *MockIStore) SetNewCustomerSwitch(arg0 context.Context, arg1, arg2 uint32, arg3 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetNewCustomerSwitch", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetNewCustomerSwitch indicates an expected call of SetNewCustomerSwitch.
func (mr *MockIStoreMockRecorder) SetNewCustomerSwitch(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetNewCustomerSwitch", reflect.TypeOf((*MockIStore)(nil).SetNewCustomerSwitch), arg0, arg1, arg2, arg3)
}

// UpdateCoachRecommend mocks base method.
func (m *MockIStore) UpdateCoachRecommend(arg0 context.Context, arg1 string, arg2 *store.CoachRecommend) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCoachRecommend", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateCoachRecommend indicates an expected call of UpdateCoachRecommend.
func (mr *MockIStoreMockRecorder) UpdateCoachRecommend(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCoachRecommend", reflect.TypeOf((*MockIStore)(nil).UpdateCoachRecommend), arg0, arg1, arg2)
}

// UpdateInviteOrderLogStatus mocks base method.
func (m *MockIStore) UpdateInviteOrderLogStatus(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInviteOrderLogStatus", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateInviteOrderLogStatus indicates an expected call of UpdateInviteOrderLogStatus.
func (mr *MockIStoreMockRecorder) UpdateInviteOrderLogStatus(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInviteOrderLogStatus", reflect.TypeOf((*MockIStore)(nil).UpdateInviteOrderLogStatus), arg0, arg1, arg2)
}

// UpdateReceiveTimeFrame mocks base method.
func (m *MockIStore) UpdateReceiveTimeFrame(arg0 context.Context, arg1 uint32, arg2 *store.ReceiveTimeFrame) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateReceiveTimeFrame", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateReceiveTimeFrame indicates an expected call of UpdateReceiveTimeFrame.
func (mr *MockIStoreMockRecorder) UpdateReceiveTimeFrame(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateReceiveTimeFrame", reflect.TypeOf((*MockIStore)(nil).UpdateReceiveTimeFrame), arg0, arg1, arg2)
}

// UpdateRecommendVal mocks base method.
func (m *MockIStore) UpdateRecommendVal(arg0 context.Context, arg1 uint32, arg2 float64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRecommendVal", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateRecommendVal indicates an expected call of UpdateRecommendVal.
func (mr *MockIStoreMockRecorder) UpdateRecommendVal(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRecommendVal", reflect.TypeOf((*MockIStore)(nil).UpdateRecommendVal), arg0, arg1, arg2)
}

// UpdateSkillProductPrice mocks base method.
func (m *MockIStore) UpdateSkillProductPrice(arg0 context.Context, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSkillProductPrice", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSkillProductPrice indicates an expected call of UpdateSkillProductPrice.
func (mr *MockIStoreMockRecorder) UpdateSkillProductPrice(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSkillProductPrice", reflect.TypeOf((*MockIStore)(nil).UpdateSkillProductPrice), arg0, arg1, arg2, arg3)
}

// UpdateSkillProductReceivingTime mocks base method.
func (m *MockIStore) UpdateSkillProductReceivingTime(arg0 context.Context, arg1, arg2 uint32, arg3, arg4 time.Time, arg5 []bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSkillProductReceivingTime", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSkillProductReceivingTime indicates an expected call of UpdateSkillProductReceivingTime.
func (mr *MockIStoreMockRecorder) UpdateSkillProductReceivingTime(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSkillProductReceivingTime", reflect.TypeOf((*MockIStore)(nil).UpdateSkillProductReceivingTime), arg0, arg1, arg2, arg3, arg4, arg5)
}

// UpdateSkillProductSwitch mocks base method.
func (m *MockIStore) UpdateSkillProductSwitch(arg0 context.Context, arg1, arg2 uint32, arg3 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSkillProductSwitch", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSkillProductSwitch indicates an expected call of UpdateSkillProductSwitch.
func (mr *MockIStoreMockRecorder) UpdateSkillProductSwitch(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSkillProductSwitch", reflect.TypeOf((*MockIStore)(nil).UpdateSkillProductSwitch), arg0, arg1, arg2, arg3)
}
