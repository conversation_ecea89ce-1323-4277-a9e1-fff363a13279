package internal

import (
    "context"
    "encoding/json"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/pkg/protocol"
    errCode "golang.52tt.com/protocol/common/status"
    "golang.52tt.com/services/tt-rev/esport/esport-hall/internal/conf"
    "golang.52tt.com/services/tt-rev/esport/esport-hall/internal/event"
    "golang.52tt.com/services/tt-rev/esport/esport-hall/internal/mgr"
    "golang.52tt.com/services/tt-rev/esport/esport-hall/internal/rpc"
    "golang.52tt.com/services/tt-rev/esport/esport-hall/internal/store"
    userGameCard "golang.52tt.com/services/tt-rev/esport/esport-hall/internal/user-game-card/mgr"
    context0 "golang.org/x/net/context"
    "google.golang.org/grpc/codes"
    "google.golang.org/grpc/status"

    pb "golang.52tt.com/protocol/services/esport_hall"

    "golang.52tt.com/services/tt-rev/esport/esport-hall/internal/cache"
)

var (
    errUnimplemented = status.Error(codes.Unimplemented, "")
)

func NewServer(ctx context.Context, cfg *conf.StartConfig) (*Server, error) {
    log.Infof("server startup with cfg: %+v", *cfg)

    s := &Server{}

    bc, err := conf.NewBusinessConfManager()
    if nil != err {
        log.ErrorWithCtx(ctx, "init business conf fail, err: %v", err)
        return nil, err
    }
    s.bc = bc

    cache_, err := cache.NewCache(ctx, cfg.RedisConfig)
    if nil != err {
        log.ErrorWithCtx(ctx, "init redis fail, err: %v", err)
        return nil, err
    }
    s.cache = cache_

    store_, err := store.NewStore(ctx, cfg.MongoConfig)
    if nil != err {
        log.ErrorWithCtx(ctx, "init mongo fail, err: %v", err)
        return nil, err
    }
    s.store = store_

    rpcCli := rpc.NewClient(bc)

    mgr := mgr.NewMgr(bc, store_, cache_, rpcCli)
    s.mgr = mgr

    userGameCardMgr, err := userGameCard.NewMgr(ctx, cfg.MongoConfig, rpcCli, bc)
    if err != nil {
        log.ErrorWithCtx(ctx, "init user game card mgr fail, err: %v", err)
        return nil, err
    }
    s.userGameCardMgr = userGameCardMgr

    kfkEv, err := event.NewKafkaEvent(cfg, mgr, userGameCardMgr, bc, store_, cache_, rpcCli)
    if err != nil {
        log.ErrorWithCtx(ctx, "init kafka event fail, err: %v", err)
        return nil, err
    }
    s.kfkEv = kfkEv

    return s, nil
}

type Server struct {
    cache           cache.ICache
    store           *store.Store
    mgr             *mgr.Mgr
    userGameCardMgr *userGameCard.Mgr
    kfkEv           *event.KafkaEvent
    bc              conf.IBusinessConfManager
}

func (s *Server) GetCoachIncentiveAddition(ctx context.Context, request *pb.GetCoachIncentiveAdditionRequest) (*pb.GetCoachIncentiveAdditionResponse, error) {
    resp := &pb.GetCoachIncentiveAdditionResponse{}
    progressInfo, err := s.mgr.GetCoachIncentiveTaskProgressV2(ctx, request.GetUid())
    if err != nil {
        log.ErrorWithCtx(ctx, "get coach incentive task progress fail, err: %v", err)
        return resp, err
    }

    rsAddition := uint32(0)
    for _, item := range progressInfo {
        if item.GetIsReach() && item.GetAddition() > rsAddition {
            rsAddition = item.GetAddition()
        }
    }

    resp.Addition = rsAddition
    return resp, nil
}

func (s *Server) GetOperationCoachRecommendWithoutExposed(c context0.Context, request *pb.GetOperationCoachRecommendWithoutExposedRequest) (*pb.GetOperationCoachRecommendWithoutExposedResponse, error) {
    out := &pb.GetOperationCoachRecommendWithoutExposedResponse{}
    defer func() {
        log.DebugWithCtx(c, "GetOperationCoachRecommendWithoutExposed request: %v, resp: %v", request, out)
    }()
    itemList, err := s.mgr.GetOperationCoachRecommendWithoutExposed(c, request.GetUid(), request.GetGameId())
    if err != nil {
        log.ErrorWithCtx(c, "GetOperationCoachRecommendWithoutExposed fail, err: %v", err)
        return out, err
    }
    for _, item := range itemList {
        out.ProductList = append(out.ProductList, &pb.GetOperationCoachRecommendWithoutExposedResponse_SimpleProductItem{
            ProductId: item.ProductId,
            Uid:       item.Uid,
        })
    }
    return out, nil
}

func (s *Server) BatchGetSkillProductInfo(c context0.Context, request *pb.BatchGetSkillProductInfoRequest) (*pb.BatchGetSkillProductInfoResponse, error) {
    out := &pb.BatchGetSkillProductInfoResponse{}
    defer func() {
        log.DebugWithCtx(c, "BatchGetSkillProductInfo request: %v, resp: %v", request, out)
    }()
    if len(request.GetProductIdList()) == 0 {
        return out, nil
    }
    out, err := s.mgr.BatchGetSkillProductInfo(c, request)
    if err != nil {
        log.ErrorWithCtx(c, "BatchGetSkillProductInfo fail, err: %v", err)
        return out, err
    }
    return out, nil

}

func (s *Server) SetUserToRealCoach(ctx context.Context, request *pb.SetUserToRealCoachRequest) (*pb.SetUserToRealCoachResponse, error) {
    err := s.mgr.SetUserToRealCoach(ctx, request.GetGameId(), request.GetUid(), request.GetCustomerUid(), request.GetCoachUid())
    if err != nil {
        log.ErrorWithCtx(ctx, "set user to real coach fail, err: %v", err)
    }
    return &pb.SetUserToRealCoachResponse{}, err
}

func (s *Server) GetRecommendSkillProduct(ctx context.Context, req *pb.GetRecommendSkillProductReq) (*pb.GetRecommendSkillProductResp, error) {
    return s.mgr.GetRecommendSkillProduct(ctx, req)
}

func (s *Server) GetGlobalRcmdCoach(ctx context.Context, req *pb.GetGlobalRcmdCoachRequest) (*pb.GetGlobalRcmdCoachResponse, error) {
    return s.mgr.GetGlobalRcmdCoach(ctx, req)
}

func (s *Server) GetGameCoachListByUid(ctx context.Context, request *pb.GetGameCoachListByUidRequest) (*pb.GetGameCoachListByUidResponse, error) {
    defer func() {
        log.DebugWithCtx(ctx, "GetGameCoachListByUid request: %v", request)
    }()
    // 判断size
    if request.GetLimit() == 0 {
        // 设置默认值，兼容老版本需求：电竞助手推送活动页的大神列表，最多不过100个
        request.Limit = 100
    }
    return s.mgr.GetGameCoachListByUid(ctx, request)
}

func (s *Server) CreateEsportGameCard(ctx context.Context, request *pb.CreateEsportGameCardRequest) (*pb.CreateEsportGameCardResponse, error) {
    return s.userGameCardMgr.CreateEsportGameCard(ctx, request)
}

func (s *Server) UpdateEsportGameCard(ctx context.Context, request *pb.UpdateEsportGameCardRequest) (*pb.UpdateEsportGameCardResponse, error) {
    return s.userGameCardMgr.UpdateEsportGameCard(ctx, request)
}

func (s *Server) ReportExposeCoach(ctx context.Context, request *pb.ReportExposeCoachRequest) (*pb.ReportExposeCoachResponse, error) {
    return s.mgr.ReportExposeCoach(ctx, request)
}

func (s *Server) BatchGetQuickReceiveSwitch(c context0.Context, request *pb.BatchGetQuickReceiveSwitchRequest) (*pb.BatchGetQuickReceiveSwitchResponse, error) {
    out := &pb.BatchGetQuickReceiveSwitchResponse{
        SwitchMap: make(map[uint32]bool),
    }

    //quickMap, err := s.cache.BatchGetQuickReceiveSwitchStartTime(c, request.GetUidList())
    //if err != nil {
    //    log.ErrorWithCtx(c, "batch get quick receive switch start time fail, err: %v", err)
    //    return out, err
    //}

    for _, uid := range request.GetUidList() {
        out.SwitchMap[uid] = s.mgr.IsQuickReceiveUser(uid)
    }
    return out, nil
}

func (s *Server) GetSkillProductInfoByGameId(ctx context.Context, request *pb.GetSkillProductInfoByGameIdRequest) (*pb.GetSkillProductInfoByGameIdResponse, error) {
    return s.mgr.GetSkillProductInfoByGameId(ctx, request)
}

func (s *Server) SetGuaranteeWinSwitch(c context0.Context, request *pb.SetGuaranteeWinSwitchRequest) (*pb.SetGuaranteeWinSwitchResponse, error) {
    return s.mgr.SetGuaranteeWinSwitch(c, request)
}

func (s *Server) DeleteEsportGameCard(ctx context.Context, request *pb.DeleteEsportGameCardRequest) (*pb.DeleteEsportGameCardResponse, error) {
    return s.userGameCardMgr.DeleteEsportGameCard(ctx, request)
}

func (s *Server) GetEsportGameCardInfo(ctx context.Context, request *pb.GetEsportGameCardInfoRequest) (*pb.GetEsportGameCardInfoResponse, error) {
    return s.userGameCardMgr.GetEsportGameCardInfo(ctx, request)
}

func (s *Server) GetEsportGameCardList(ctx context.Context, request *pb.GetEsportGameCardListRequest) (*pb.GetEsportGameCardListResponse, error) {
    return s.userGameCardMgr.GetEsportGameCardList(ctx, request)
}

func (s *Server) SendEsportGameCard(ctx context.Context, request *pb.SendEsportGameCardRequest) (*pb.SendEsportGameCardResponse, error) {
    return s.userGameCardMgr.SendEsportGameCard(ctx, request)
}

func (s *Server) HasFamousPlayer(ctx context.Context, request *pb.HasFamousPlayerRequest) (*pb.HasFamousPlayerResponse, error) {
    return s.mgr.HasFamousPlayer(ctx, request)
}

func (s *Server) SetQuickReceiveSwitch(ctx context.Context, request *pb.SetQuickReceiveSwitchRequest) (*pb.SetQuickReceiveSwitchResponse, error) {
    return s.mgr.SetQuickReceiveSwitch(ctx, request)
}

func (s *Server) GetQuickReceiveSwitch(ctx context.Context, request *pb.GetQuickReceiveSwitchRequest) (*pb.GetQuickReceiveSwitchResponse, error) {
    return s.mgr.GetQuickReceiveSwitch(ctx, request)
}

func (s *Server) Test(ctx context.Context, request *pb.TestRequest) (*pb.TestResponse, error) {
    params := &struct {
        Uid       uint32 `json:"uid"`
        TargetUid uint32 `json:"target_uid"`
        GameId    uint32 `json:"game_id"`
    }{}

    json.Unmarshal([]byte(request.GetParamJson()), params)

    s.mgr.HandleCoachIncentiveAdditionUp(ctx, params.Uid)
    return &pb.TestResponse{}, nil
}

func (s *Server) ShutDown() {
    _ = s.cache.Close()

    _ = s.store.Close(context.Background())

    s.mgr.Shutdown()

    s.kfkEv.Shutdown()
}

// GetVisibleSkillProductList 获取当前可见技能商品
func (s *Server) GetVisibleSkillProductList(ctx context.Context, req *pb.GetVisibleSkillProductListRequest) (*pb.GetVisibleSkillProductListResponse, error) {
    return s.mgr.GetVisibleSkillProductList(ctx, req)
}

// GetAllSkillList 获取所有技能
func (s *Server) GetAllSkillList(ctx context.Context, req *pb.GetAllSkillListRequest) (*pb.GetAllSkillListResponse, error) {
    return s.mgr.GetAllSkillList(ctx, req)
}

// SetSkillReceiveSwitch 设置技能接单开关
func (s *Server) SetSkillReceiveSwitch(ctx context.Context, request *pb.SetSkillReceiveSwitchRequest) (*pb.SetSkillReceiveSwitchResponse, error) {
    return s.mgr.SetSkillReceiveSwitch(ctx, request)
}

// SetSkillPrice 设置技能价格
func (s *Server) SetSkillPrice(ctx context.Context, req *pb.SetSkillPriceRequest) (*pb.SetSkillPriceResponse, error) {
    return s.mgr.SetSkillPrice(ctx, req)
}

// GetSkillProductInfo 获取技能商品信息(下单前校验)
func (s *Server) GetSkillProductInfo(ctx context.Context, req *pb.GetSkillProductInfoRequest) (*pb.GetSkillProductInfoResponse, error) {
    return s.mgr.GetSkillProductInfo(ctx, req)
}

func (s *Server) GetReceiveTimeFrame(ctx context.Context, req *pb.GetReceiveTimeFrameRequest) (*pb.GetReceiveTimeFrameResponse, error) {
    return s.mgr.GetReceiveTimeFrame(ctx, req)
}

func (s *Server) SetReceiveTimeFrame(ctx context.Context, req *pb.SetReceiveTimeFrameRequest) (*pb.SetReceiveTimeFrameResponse, error) {
    return s.mgr.SetReceiveTimeFrame(ctx, req)
}

func (s *Server) InitUserSkillInfo(ctx context.Context, req *pb.InitUserSkillInfoRequest) (*pb.InitUserSkillInfoResponse, error) {
    return s.mgr.InitSkillInfo(ctx, req)
}

func (s *Server) GetGameCoachList(ctx context.Context, request *pb.GetGameCoachListRequest) (*pb.GetCoachListResponse, error) {
    return s.mgr.GetGameCoachList(ctx, request)
}

func (s *Server) InviteOrder(ctx context.Context, request *pb.InviteOrderRequest) (*pb.InviteOrderResponse, error) {
    return s.mgr.InviteOrder(ctx, request)
}

func (s *Server) HandleInviteOrder(ctx context.Context, request *pb.HandleInviteOrderRequest) (*pb.HandleInviteOrderResponse, error) {
    return s.mgr.HandleInviteOrder(ctx, request)
}

func (s *Server) DelSkillProduct(ctx context.Context, request *pb.DelSkillProductRequest) (*pb.DelSkillProductResponse, error) {
    return s.mgr.DelSkillProduct(ctx, request)
}

func (s *Server) GetGamePriceProperty(ctx context.Context, request *pb.GetGamePricePropertyRequest) (*pb.GetGamePricePropertyResponse, error) {
    return s.mgr.GetGamePriceProperty(ctx, request.GetGameId())
}

func (s *Server) GetSkillProductByUidGameId(ctx context.Context, request *pb.GetSkillProductByUidGameIdRequest) (*pb.GetSkillProductByUidGameIdResponse, error) {
    return s.mgr.FindSkillProductByUidGameId(ctx, request)
}

// ===================== 运营后台相关 ===============================
func (s *Server) AddCoachRecommend(ctx context.Context, request *pb.AddCoachRecommendRequest) (*pb.AddCoachRecommendResponse, error) {
    return s.mgr.AddCoachRecommend(ctx, request)
}

func (s *Server) GetCoachRecommend(ctx context.Context, request *pb.GetCoachRecommendRequest) (*pb.GetCoachRecommendResponse, error) {
    return s.mgr.GetCoachRecommend(ctx, request)
}

func (s *Server) UpdateCoachRecommend(ctx context.Context, request *pb.UpdateCoachRecommendRequest) (*pb.UpdateCoachRecommendResponse, error) {
    return s.mgr.UpdateCoachRecommend(ctx, request)
}

func (s *Server) DelCoachRecommend(ctx context.Context, request *pb.DelCoachRecommendRequest) (*pb.DelCoachRecommendResponse, error) {
    return s.mgr.DeleteCoachRecommend(ctx, request)
}

func (s *Server) SetFirstRoundSwitch(ctx context.Context, request *pb.SetFirstRoundSwitchRequest) (*pb.SetFirstRoundSwitchResponse, error) {
    return &pb.SetFirstRoundSwitchResponse{}, s.mgr.SetFirstRoundSwitch(ctx, request)
}

func (s *Server) GetFirstRoundDiscountInfo(ctx context.Context, request *pb.GetFirstRoundDiscountInfoRequest) (*pb.GetFirstRoundDiscountInfoResponse, error) {
    return s.mgr.GetFirstRoundDiscountInfo(ctx, request)
}

func (s *Server) GetFirstRoundDiscountGameList(ctx context.Context, request *pb.GetFirstRoundDiscountGameListRequest) (*pb.GetFirstRoundDiscountGameListResponse, error) {
    return s.mgr.GetFirstRoundDiscountGameList(ctx, request)
}

func (s *Server) CheckFirstRoundOrderRight(ctx context.Context, request *pb.CheckFirstRoundOrderRightRequest) (*pb.CheckFirstRoundOrderRightResponse, error) {
    return s.mgr.CheckFirstRoundOrderRight(ctx, request)
}

func (s *Server) GetFirstRoundOrderRight(ctx context.Context, request *pb.GetFirstRoundOrderRightRequest) (*pb.GetFirstRoundOrderRightResponse, error) {
    return s.mgr.GetFirstRoundOrderRight(ctx, request)
}

func (s *Server) ClearFirstRoundOrder(ctx context.Context, request *pb.ClearFirstRoundOrderRequest) (*pb.ClearFirstRoundOrderResponse, error) {
    return s.mgr.ClearFirstRoundOrder(ctx, request)
}

func (s *Server) GetFirstRoundLabelByUid(ctx context.Context, request *pb.GetFirstRoundLabelByUidRequest) (*pb.GetFirstRoundLabelByUidResponse, error) {
    return s.mgr.GetFirstRoundLabelByUid(ctx, request)
}

func (s *Server) GetFirstRoundLabelBySkill(ctx context.Context, request *pb.GetFirstRoundLabelBySkillRequest) (*pb.GetFirstRoundLabelBySkillResponse, error) {
    return s.mgr.GetFirstRoundLabelBySkill(ctx, request)
}

func (s *Server) SetNewCustomerSwitch(ctx context.Context, request *pb.SetNewCustomerSwitchRequest) (*pb.SetNewCustomerSwitchResponse, error) {
    return &pb.SetNewCustomerSwitchResponse{}, s.mgr.SetNewCustomerSwitch(ctx, request)
}

func (s *Server) GetNewCustomerDiscountInfo(ctx context.Context, request *pb.GetNewCustomerDiscountInfoRequest) (*pb.GetNewCustomerDiscountInfoResponse, error) {
    return s.mgr.GetNewCustomerDiscountInfo(ctx, request)
}

func (s *Server) ClearNewCustomerOrder(ctx context.Context, request *pb.ClearNewCustomerOrderRequest) (*pb.ClearNewCustomerOrderResponse, error) {
    return &pb.ClearNewCustomerOrderResponse{}, s.mgr.ClearNewCustomerOrder(ctx, request.GetPlayerUid())
}

func (s *Server) GetNewCustomerPriceByUid(ctx context.Context, request *pb.GetNewCustomerPriceByUidRequest) (*pb.GetNewCustomerPriceByUidResponse, error) {
    return s.mgr.GetNewCustomerPriceByUid(ctx, request)
}

func (s *Server) GetNewCustomerPriceBySkill(ctx context.Context, request *pb.GetNewCustomerPriceBySkillRequest) (*pb.GetNewCustomerPriceBySkillResponse, error) {
    return s.mgr.GetNewCustomerPriceBySkill(ctx, request)
}

// SearchCoach 搜索大神，使用推荐列表排序，如果不在推荐列表，则按照默认排序
func (s *Server) SearchCoach(ctx context.Context, request *pb.SearchCoachRequest) (*pb.SearchCoachResponse, error) {
    out := &pb.SearchCoachResponse{}
    defer func() {
        log.DebugWithCtx(ctx, "SearchCoach request: %v, resp: %v", request, out)
    }()

    // 首先搜索推荐列表的
    gameCoach, err := s.mgr.GetGameCoachList(ctx, &pb.GetGameCoachListRequest{
        GameId: request.GetGameId(),
        Limit:  s.bc.GetRecommendValConfig().EachGameTopN,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SearchCoach GetGameCoachList error: %v", err)
        return out, protocol.NewExactServerError(nil, errCode.ErrEsportsCommonErr, "搜索大神失败")
    }
    // 如果推荐列表为空，则直接返回
    if len(gameCoach.GetCoachList()) == 0 {
        log.InfoWithCtx(ctx, "SearchCoach GetGameCoachList empty, req:%v", request)
        return out, nil
    }
    out.CoachList = gameCoach.GetCoachList()
    // 如果参数有传大神的uid，则需要使用uid过滤
    if len(request.GetCoachUidList()) > 0 {
        result := make([]*pb.GameCoachInfo, 0, len(request.GetCoachUidList()))
        validCoachUidMap := make(map[uint32]bool)
        for _, coachUid := range request.GetCoachUidList() {
            validCoachUidMap[coachUid] = false
        }
        for _, item := range gameCoach.GetCoachList() {
            if _, ok := validCoachUidMap[item.Uid]; ok {
                result = append(result, item)
                // 记录
                validCoachUidMap[item.Uid] = true
            }
        }
        // 找出不在推荐列表的大神，即为false的大神
        notInRecommendList := make([]uint32, 0)
        for uid, isIn := range validCoachUidMap {
            if !isIn {
                notInRecommendList = append(notInRecommendList, uid)
            }
        }

        // 如果有大神是不在推荐列表的，则需要额外的查询
        if len(notInRecommendList) > 0 {
            coach, err := s.mgr.SearchCoach(ctx, request.GetGameId(), notInRecommendList)
            if err != nil {
                log.ErrorWithCtx(ctx, "SearchCoach 获取不在推荐列表的大神 error: %v, req: %v, not in uids: %v", err, request, notInRecommendList)
            } else {
                // 合并到推荐列表中的末尾位置
                result = append(result, coach...)
            }
        }
        out.CoachList = result
    }
    // 分页切割
    startIndex := request.GetOffset()
    if startIndex >= uint32(len(out.CoachList)) {
        out.CoachList = []*pb.GameCoachInfo{}
        out.NextOffset = 0
    } else {
        endIndex := startIndex + request.GetLimit()
        out.NextOffset = endIndex
        if endIndex > uint32(len(out.CoachList)) {
            endIndex = uint32(len(out.CoachList))
            out.NextOffset = 0
        }
        out.CoachList = out.CoachList[startIndex:endIndex]
    }
    return out, nil
}

// AddIgnoreRecommendCoach UGC不再推荐大神
func (s *Server) AddIgnoreRecommendCoach(c context0.Context, request *pb.AddIgnoreRecommendCoachRequest) (*pb.AddIgnoreRecommendCoachResponse, error) {
    out := &pb.AddIgnoreRecommendCoachResponse{}
    defer func() {
        log.DebugWithCtx(c, "AddIgnoreRecommendCoach request: %v, resp: %v", request, out)
    }()

    if request.GetUid() == 0 || request.GetCoachId() == 0 || request.GetCid() == 0 {
        log.ErrorWithCtx(c, "AddIgnoreRecommendCoach invalid param: %v", request)
        return nil, protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid, "参数错误")
    }

    // 调用领域服务接口执行具体业务逻辑
    err := s.mgr.AddIgnoreRecommendCoach(c, request.GetUid(), request.GetCoachId())
    if err != nil {
        log.ErrorWithCtx(c, "AddIgnoreRecommendCoach AddIgnoreRecommendCoach error: %v", err)
        return out, protocol.NewExactServerError(nil, errCode.ErrEsportsCommonErr, err.Error())
    }
    return out, nil
}

func (s *Server) GetReCoachForUGC(c context0.Context, request *pb.GetReCoachForUGCRequest) (*pb.GetReCoachForUGCResponse, error) {
    out := &pb.GetReCoachForUGCResponse{}
    defer func() {
        // json 序列化 request和out
        reqJson, _ := json.Marshal(request)
        outJson, _ := json.Marshal(out)
        log.DebugWithCtx(c, "GetReCoachForUGC request: %s, resp: %s", string(reqJson), string(outJson))
    }()
    // 获取大神卡配置
    recommendTabConfig := s.bc.GetRecommendTabConfig()
    //if recommendTabConfig.GameId != request.GetGameId() {
    //    log.InfoWithCtx(c, "GetReCoachForUGC gameid not match, req:%+v, config: %+v", request, recommendTabConfig)
    //    return out, nil
    //}

    // ===================================== 根据关键词查询大神 =====================================
    list, err := s.mgr.GetGameCoachListForUGC(c, &pb.GetGameCoachListRequest{
        GameId:       request.GetGameId(),
        Offset:       0,
        Limit:        s.bc.GetRecommendValConfig().EachGameTopN,
        PropertyList: request.GetPropertyList(),
    })
    if err != nil {
        log.ErrorWithCtx(c, "GetReCoachForUGC GetGameCoachList error: %v, req: %+v", err, request)
        return out, protocol.NewExactServerError(nil, errCode.ErrEsportsCommonErr, err.Error())
    }
    // 如果关键词没有match到大神，则直接查询所有的大神
    if len(list.GetCoachList()) == 0 {
        log.InfoWithCtx(c, "GetReCoachForUGC 关键词没有match到大神, 走兜底逻辑, req:%+v", request)
        list, err = s.mgr.GetGameCoachList(c, &pb.GetGameCoachListRequest{
            GameId: request.GetGameId(),
            Offset: 0,
            Limit:  s.bc.GetRecommendValConfig().EachGameTopN,
        })
        if err != nil {
            log.ErrorWithCtx(c, "GetReCoachForUGC GetGameCoachList for all error: %v, req: %+v", err, request)
            return out, protocol.NewExactServerError(nil, errCode.ErrEsportsCommonErr, err.Error())
        }
    }

    // ===================================== 根据关键词查询大神end =====================================

    // ===================================== 只要大神卡片池的大神 =====================================
    if recommendTabConfig.GameId == request.GetGameId() {
        wantCoachUidsIndex := make(map[uint32]bool)
        for _, coach := range recommendTabConfig.RecommendTabCoachCardUid {
            wantCoachUidsIndex[coach] = true
        }
        // 过滤掉不在大神卡片池的大神
        result := make([]*pb.GameCoachInfo, 0, len(list.GetCoachList()))
        for _, coach := range list.GetCoachList() {
            if wantCoachUidsIndex[coach.Uid] {
                result = append(result, coach)
            }
        }
        list.CoachList = result
    }
    // ===================================== 只要大神卡片池的大神end =====================================

    // ===================================== 强出首单大神 =====================================
    frcList, err := s.mgr.SearchFirstRoundCoachListRandomly(c, request.GetUid(), 999, 0, request.GetGameId(), []uint32{})
    if err != nil {
        log.ErrorWithCtx(c, "GetReCoachForUGC SearchFirstRoundCoachList error: %v, req: %+v", err, request)
        return out, protocol.NewExactServerError(nil, errCode.ErrEsportsCommonErr, err.Error())
    }
    // 合并去重
    exist := make(map[uint32]bool)
    for _, item := range frcList {
        exist[item.Uid] = true
    }
    frPart := make([]*pb.GameCoachInfo, 0, len(list.GetCoachList())) // 首单大神
    otPart := make([]*pb.GameCoachInfo, 0, len(list.GetCoachList())) // 其他大神
    for _, item := range list.GetCoachList() {
        if exist[item.Uid] {
            frPart = append(frPart, item)
        } else {
            otPart = append(otPart, item)
        }
    }
    list.CoachList = append(frPart, otPart...)
    // ===================================== 强出首单大神end =====================================

    // ===================================== 置底已曝光过大神 =====================================
    exposedUidList, err := s.mgr.GetUgcExposeCoachUidList(c, request.GetGameId(), request.GetUid())
    if err != nil {
        log.ErrorWithCtx(c, "GetReCoachForUGC GetUgcExposeCoachUidList error: %v, req: %+v", err, request)
        return out, protocol.NewExactServerError(nil, errCode.ErrEsportsCommonErr, err.Error())
    }
    exposedUidMap := make(map[uint32]bool)
    for _, uid := range exposedUidList {
        exposedUidMap[uid] = true
    }
    noExposePart := make([]*pb.GameCoachInfo, 0, len(list.GetCoachList())) // 未曝光过的大神
    exposePart := make([]*pb.GameCoachInfo, 0, len(list.GetCoachList()))   // 曝光过的大神
    for _, item := range list.CoachList {
        if exposedUidMap[item.Uid] {
            exposePart = append(exposePart, item)
        } else {
            noExposePart = append(noExposePart, item)
        }
    }
    list.CoachList = append(noExposePart, exposePart...)
    // ===================================== 置底已曝光过大神end =====================================

    // 输出日志，json序列化查询到的结果
    listJson, _ := json.Marshal(list.GetCoachList())
    log.DebugWithCtx(c, "GetReCoachForUGC req: %+v, list: %s", request, string(listJson))
    // ===================================== 过滤无效的数据 =====================================
    {
        ignoreCoachUids := make([]uint32, 0)
        // 过滤不在线，不是秒接单
        onlineUserIndex := s.mgr.GetOnlineUser()
        for _, coach := range list.GetCoachList() {
            if !s.mgr.IsQuickReceiveUser(coach.GetUid()) || !onlineUserIndex[coach.GetUid()] {
                ignoreCoachUids = append(ignoreCoachUids, coach.GetUid())
            }
        }

        log.DebugWithCtx(c, "GetReCoachForUGC after is_on_line and is_in_time ignoreCoachUids: %v", ignoreCoachUids)
        // 获取用户不喜欢的大神进行过滤
        ignoreCoaches, err := s.mgr.GetIgnoreRecommendCoaches(c, request.GetUid())
        if err != nil {
            // 如果有错误，则记录下来，但是不影响正常返回
            log.ErrorWithCtx(c, "GetReCoachForUGC GetIgnoreRecommendCoaches error: %v", err)
        } else {
            ignoreCoachUids = append(ignoreCoachUids, ignoreCoaches...)
        }
        if len(ignoreCoachUids) != 0 {
            ignoreIndex := make(map[uint32]bool)
            for _, coach := range ignoreCoachUids {
                ignoreIndex[coach] = true
            }
            // 过滤掉不喜欢的大神
            result := make([]*pb.GameCoachInfo, 0, len(list.GetCoachList()))
            for _, coach := range list.GetCoachList() {
                if !ignoreIndex[coach.Uid] {
                    coach.IsOnline = onlineUserIndex[coach.Uid]
                    result = append(result, coach)
                }
            }
            list.CoachList = result
        }
    }
    // ===================================== 过滤无效的数据end =====================================

    // ===================================== 截取 =====================================
    {
        // 截取
        limit := uint32(10)
        if request.GetLimit() != 0 {
            limit = request.GetLimit()
        }
        // 随机抽取limit个
        if len(list.GetCoachList()) > int(limit) {
            // 保证顺序的情况下，随机抽取
            list.CoachList = list.CoachList[:limit]
        }
    }
    // ===================================== 截取end =====================================

    out.CoachList = list.GetCoachList()
    return out, nil
}

func (s *Server) GetCoachMinPrice(c context0.Context, request *pb.GetCoachMinPriceRequest) (*pb.GetCoachMinPriceResponse, error) {
    resp := &pb.GetCoachMinPriceResponse{}
    minPrice, err := s.mgr.GetCoachMinPrice(c, request.GetUid(), request.GetIncludeFirstRound())
    if err != nil {
        log.ErrorWithCtx(c, "get coach min price fail, err: %v", err)
        return resp, err
    }
    resp.MinPrice = minPrice
    return resp, nil
}

func (s *Server) GetCoachIncentiveTaskInfo(ctx context.Context, request *pb.GetCoachIncentiveTaskInfoRequest) (*pb.GetCoachIncentiveTaskInfoResponse, error) {
    return s.mgr.GetCoachIncentiveTaskInfo(ctx, request)
}

// ReBuildUserFirstRoundCache 重建用户使用了首单优惠的缓存
func (s *Server) ReBuildUserFirstRoundCache(ctx context.Context, request *pb.ReBuildUserFirstRoundCacheRequest) (*pb.ReBuildUserFirstRoundCacheResponse, error) {
    _ = s.mgr.ReBuildUserFirstRoundCache(ctx, request.GetUidList()...)
    return &pb.ReBuildUserFirstRoundCacheResponse{}, nil
}

// SaveUserTopGamePreSelectConfig 保存用户的top game列表的前置选择配置
func (s *Server) SaveUserTopGamePreSelectConfig(ctx context.Context, request *pb.SaveUserTopGamePreSelectConfigRequest) (*pb.SaveUserTopGamePreSelectConfigResponse, error) {
    out := &pb.SaveUserTopGamePreSelectConfigResponse{}
    defer func() {
        log.DebugWithCtx(ctx, "SaveUserTopGamePreSelectConfig request: %v, resp: %v", request, out)
    }()

    err := s.cache.SaveUserTopGamePreSelectConfig(ctx, request.GetUid(), request.GetGameIdList())
    if err != nil {
        log.ErrorWithCtx(ctx, "SaveUserTopGamePreSelectConfig fail, err: %v", err)
        return out, protocol.NewExactServerError(nil, errCode.ErrEsportsCommonErr, err.Error())
    }

    return out, nil
}

// GetUserTopGamePreSelectConfig 获取用户的top game列表的前置选择配置
func (s *Server) GetUserTopGamePreSelectConfig(ctx context.Context, request *pb.GetUserTopGamePreSelectConfigRequest) (*pb.GetUserTopGamePreSelectConfigResponse, error) {
    out := &pb.GetUserTopGamePreSelectConfigResponse{}
    defer func() {
        log.DebugWithCtx(ctx, "GetUserTopGamePreSelectConfig request: %v, resp: %v", request, out)
    }()

    gameIdList, err := s.cache.GetUserTopGamePreSelectConfig(ctx, request.GetUid())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserTopGamePreSelectConfig fail, err: %v", err)
        return out, protocol.NewExactServerError(nil, errCode.ErrEsportsCommonErr, err.Error())
    }
    out.GameIdList = gameIdList
    return out, nil
}
