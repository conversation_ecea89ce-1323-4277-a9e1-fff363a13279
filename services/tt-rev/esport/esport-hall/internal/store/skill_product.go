package store

import (
	"context"
	"encoding/json"
	"errors"
	"math"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/esport_hall"
	"google.golang.org/grpc/codes"
)

const (
	productCounterName = "skill_product_id"
)

type SimpleProduct struct {
	ProductId uint32 `bson:"product_id"`
	Uid       uint32 `bson:"uid"`
}

type SkillProduct struct {
	ID            primitive.ObjectID `bson:"_id,omitempty"`
	ProductId     uint32             `bson:"product_id"`
	Uid           uint32             `bson:"uid"`
	SkillId       uint32             `bson:"skill_id"`
	SkillProperty []*pb.SectionInfo  `bson:"skill_property"`
	Switch        bool               `bson:"switch"`
	Price         uint32             `bson:"price"`
	RecommendVal  float64            `bson:"recommend_val"`
	CreateTime    time.Time          `bson:"create_time"`
	GuaranteeWin  bool               `bson:"guarantee_win_switch"`
	UnitType      uint32             `bson:"-"`
	// 以下字段用于前期日志打印便于调试
	CoachLevel       uint32  `bson:"-"`
	BaseRecommendVal float64 `bson:"-"`
	IsQuickReceive   uint32  `bson:"-"`
	IsLimit          bool    `bson:"-"`
	IsOnline         bool    `bson:"-"`
	Sex              uint32  `bson:"-"`
	IsUnderlay       uint32  `bson:"-"`
	IsExposeGrading  uint32  `bson:"-"` // 触发曝光分级策略

	FirstRoundSwitch  bool `bson:"first_round_switch"`
	NewCustomerSwitch bool `bson:"new_customer_switch"`
	IsNewCoach        bool `bson:"-"`
}

func (s *SkillProduct) Marshal() ([]byte, error) {
	// 检查并处理 NaN 值
	if math.IsNaN(s.RecommendVal) {
		s.RecommendVal = 0
	}
	// 检查并处理 NaN 值
	if math.IsNaN(s.BaseRecommendVal) {
		s.BaseRecommendVal = 0
	}
	return json.Marshal(s)
}

func (s *SkillProduct) GetGuaranteeWinTexts() []string {
	guaranteeWinText := make([]string, 0)
	for _, item := range s.SkillProperty {
		if item.SectionName == "包赢承诺" {
			for _, subItem := range item.ItemList {
				guaranteeWinText = append(guaranteeWinText, subItem)
			}
			break
		}
	}
	return guaranteeWinText
}

type SkillProductList []*SkillProduct

func (s SkillProductList) ExtractUid() []uint32 {
	uids := make([]uint32, 0, len(s))
	for _, v := range s {
		uids = append(uids, v.Uid)
	}
	return uids
}

func (s *Store) GenProductId(ctx context.Context) uint32 {
	return s.IncCounter(ctx, productCounterName)
}

func (s *Store) EnsureIndexSkillProduct(ctx context.Context) {
	_, err := s.skillProductColl.Indexes().CreateMany(context.Background(), []mongo.IndexModel{
		{
			Keys: bson.M{
				"product_id": -1,
			},
			Options: options.Index().SetUnique(true),
		},

		{
			Keys: bson.D{
				{
					Key:   "uid",
					Value: 1,
				},
				{
					Key:   "skill_id",
					Value: 1,
				},
			},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys: bson.D{
				{
					Key:   "skill_id",
					Value: 1,
				},
				{
					Key:   "recommend_val",
					Value: -1,
				},
			},
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "EnsureIndex err: %v", err)
	}
}

func (s *Store) BatchInsertSkillProduct(ctx context.Context, data []*SkillProduct) error {
	insertData := make([]interface{}, 0, len(data))
	for _, v := range data {
		v.ProductId = s.GenProductId(ctx)
		insertData = append(insertData, v)
	}
	_, err := s.skillProductColl.InsertMany(ctx, insertData)
	return err
}

func (s *Store) BatchUpdateSkillProduct(ctx context.Context, data []*SkillProduct) error {
	for _, v := range data {
		updateRs, err := s.skillProductColl.UpdateOne(ctx, bson.M{"uid": v.Uid, "skill_id": v.SkillId},
			bson.M{"$set": bson.M{"skill_property": v.SkillProperty, "price": v.Price}})
		log.InfoWithCtx(ctx, "rs : %+v", updateRs)
		if err != nil {
			return err
		}
	}
	return nil
}

func (s *Store) SetGuaranteeWinSwitch(ctx context.Context, uid, skillId uint32, switchStatus bool) error {
	err := s.skillProductColl.FindOneAndUpdate(ctx, bson.M{"uid": uid, "skill_id": skillId}, bson.M{"$set": bson.M{"guarantee_win_switch": switchStatus}}).Err()
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			log.ErrorWithCtx(ctx, "SetGuaranteeWinSwitch uid:%d, skillId:%d, err: %v", uid, skillId, protocol.NewExactServerError(codes.OK, status.ErrEsportHallProductNotFound))
			return protocol.NewExactServerError(codes.OK, status.ErrEsportHallProductNotFound)
		}
		log.ErrorWithCtx(ctx, "SetGuaranteeWinSwitch uid:%d, skillId:%d, err: %v", uid, skillId, err)
		return err
	}
	log.InfoWithCtx(ctx, "SetGuaranteeWinSwitch success, uid: %d, skillId: %d, switchStatus: %v", uid, skillId, switchStatus)
	return nil
}

// FindSkillProductByUid 查找用户的商品
func (s *Store) FindSkillProductByUid(ctx context.Context, uid uint32) ([]*SkillProduct, error) {
	rsData := make([]*SkillProduct, 0)
	query := bson.M{
		"uid": uid,
	}

	cur, err := s.skillProductColl.Find(ctx, query)
	if err != nil {
		return rsData, err
	}
	defer cur.Close(ctx)
	for cur.Next(ctx) {
		var data SkillProduct
		err := cur.Decode(&data)
		if err != nil {
			return rsData, err
		}
		rsData = append(rsData, &data)
	}
	return rsData, nil
}

// FindEnableSkillProductByUid 查找用户的商品
func (s *Store) FindEnableSkillProductByUid(ctx context.Context, uid uint32) ([]*SkillProduct, error) {
	rsData := make([]*SkillProduct, 0)
	query := bson.M{
		"uid":    uid,
		"switch": true,
	}

	cur, err := s.skillProductColl.Find(ctx, query)
	if err != nil {
		return rsData, err
	}
	defer cur.Close(ctx)
	for cur.Next(ctx) {
		var data SkillProduct
		err := cur.Decode(&data)
		if err != nil {
			return rsData, err
		}
		rsData = append(rsData, &data)
	}
	return rsData, nil
}

func (s *Store) FindSkillProductByProductId(ctx context.Context, productId uint32) (*SkillProduct, error) {
	data := &SkillProduct{}
	err := s.skillProductColl.FindOne(ctx, bson.M{"product_id": productId, "switch": true}).Decode(data)
	if errors.Is(err, mongo.ErrNoDocuments) {
		return data, protocol.NewExactServerError(codes.OK, status.ErrEsportHallProductNotFound)
	}
	return data, err
}

func (s *Store) BatchFindSkillProductByProductIds(ctx context.Context, productIds []uint32) (map[uint32]*SkillProduct, error) {
	rsData := make(map[uint32]*SkillProduct)
	query := bson.M{
		"product_id": bson.M{"$in": productIds},
		"switch":     true,
	}

	cur, err := s.skillProductColl.Find(ctx, query)
	if err != nil {
		return rsData, err
	}
	defer cur.Close(ctx)
	for cur.Next(ctx) {
		var data SkillProduct
		err := cur.Decode(&data)
		if err != nil {
			return rsData, err
		}
		rsData[data.ProductId] = &data
	}
	return rsData, nil
}

func (s *Store) FindSkillProductBySkillId(ctx context.Context, uid, skillId uint32) (*SkillProduct, error) {
	data := &SkillProduct{}
	err := s.skillProductColl.FindOne(ctx, bson.M{"uid": uid, "skill_id": skillId, "switch": true}).Decode(data)
	if errors.Is(err, mongo.ErrNoDocuments) {
		return data, protocol.NewExactServerError(codes.OK, status.ErrEsportHallProductNotFound)
	}
	return data, err
}

func (s *Store) HasSkillProductOpenFirstRound(ctx context.Context, uid uint32) (bool, error) {
	err := s.skillProductColl.FindOne(ctx, bson.M{"uid": uid, "first_round_switch": true, "switch": true}).Err()
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, nil
		} else {
			log.ErrorWithCtx(ctx, "HasSkillProductOpenFirstRound uid:%d, err: %v", uid, err)
			return false, err
		}
	}
	return true, err
}

func (s *Store) HasSkillProductOpenNewCustomer(ctx context.Context, uid uint32) (bool, error) {
	err := s.skillProductColl.FindOne(ctx, bson.M{"uid": uid, "new_customer_switch": true, "switch": true}).Err()
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, nil
		} else {
			log.ErrorWithCtx(ctx, "HasSkillProductOpenNewCustomer uid:%d, err: %v", uid, err)
			return false, err
		}
	}
	return true, err
}

func (s *Store) SetCoachAllNewCustomerSwitch(ctx context.Context, coachUid uint32, switchStatus bool) error {
	_, err := s.skillProductColl.UpdateMany(ctx, bson.M{"uid": coachUid}, bson.M{"$set": bson.M{"new_customer_switch": switchStatus}})
	if err != nil {
		log.ErrorWithCtx(ctx, "SetAllCoachNewCustomerSwitch err: %v", err)
		return err
	}
	log.InfoWithCtx(ctx, "SetAllCoachNewCustomerSwitch success, coachUid: %d, switchStatus: %v", coachUid, switchStatus)
	return nil
}

func (s *Store) SetCoachAllFirstRoundSwitch(ctx context.Context, coachUid uint32, switchStatus bool) error {
	_, err := s.skillProductColl.UpdateMany(ctx, bson.M{"uid": coachUid}, bson.M{"$set": bson.M{"first_round_switch": switchStatus}})
	if err != nil {
		log.ErrorWithCtx(ctx, "SetCoachAllFirstRoundSwitch err: %v", err)
		return err
	}
	log.InfoWithCtx(ctx, "SetCoachAllFirstRoundSwitch success, coachUid: %d, switchStatus: %v", coachUid, switchStatus)
	return nil
}

func (s *Store) FindSkillProductBySkillIdIgnoreStatus(ctx context.Context, uid, skillId uint32) (*SkillProduct, error) {
	data := &SkillProduct{}
	err := s.skillProductColl.FindOne(ctx, bson.M{"uid": uid, "skill_id": skillId}).Decode(data)
	if errors.Is(err, mongo.ErrNoDocuments) {
		return data, protocol.NewExactServerError(codes.OK, status.ErrEsportHallProductNotFound)
	}
	return data, err
}

func (s *Store) UpdateSkillProductPrice(ctx context.Context, uid, skillId, price uint32) error {
	rs := s.skillProductColl.FindOneAndUpdate(ctx, bson.M{"uid": uid, "skill_id": skillId}, bson.M{"$set": bson.M{"price": price}})
	return rs.Err()
}

func (s *Store) UpdateSkillProductSwitch(ctx context.Context, uid, skillId uint32, switchStatus bool) error {
	rs := s.skillProductColl.FindOneAndUpdate(ctx, bson.M{"uid": uid, "skill_id": skillId}, bson.M{"$set": bson.M{"switch": switchStatus}})
	return rs.Err()
}

func (s *Store) UpdateSkillProductReceivingTime(ctx context.Context, uid, skillId uint32, startTime, endTime time.Time, dayOfWeek []bool) error {
	rs := s.skillProductColl.FindOneAndUpdate(ctx, bson.M{"uid": uid, "skill_id": skillId}, bson.M{"$set": bson.M{
		"receiving_start_time":  startTime,
		"receiving_end_time":    endTime,
		"receiving_day_of_week": dayOfWeek,
	}})
	return rs.Err()
}

func (s *Store) FindBySkillId(ctx context.Context, skillId uint32) ([]*SkillProduct, error) {
	rsData := make([]*SkillProduct, 0)
	query := bson.M{
		"skill_id": skillId,
		"switch":   true,
	}

	cur, err := s.skillProductColl.Find(ctx, query)
	if err != nil {
		return rsData, err
	}
	defer cur.Close(ctx)
	for cur.Next(ctx) {
		var data SkillProduct
		err := cur.Decode(&data)
		if err != nil {
			return rsData, err
		}
		rsData = append(rsData, &data)
	}
	return rsData, nil
}

func (s *Store) DelSkillProduct(ctx context.Context, uid, skillId []uint32) error {
	query := bson.M{}
	if len(uid) > 0 {
		query["uid"] = bson.M{"$in": uid}
	}
	if len(skillId) > 0 {
		query["skill_id"] = bson.M{"$in": skillId}
	}
	if len(query) == 0 {
		return nil // 忽略不带条件的
	}
	_, err := s.skillProductColl.DeleteMany(ctx, query)
	return err
}

func (s *Store) GetAllSkillProductPaged(ctx context.Context, offset, limit uint32) ([]*SkillProduct, error) {
	rsData := make([]*SkillProduct, 0)
	query := bson.M{"switch": true}
	cur, err := s.skillProductColl.Find(ctx, query, options.Find().SetSkip(int64(offset)).SetLimit(int64(limit)))
	if err != nil {
		return rsData, err
	}
	defer cur.Close(ctx)
	for cur.Next(ctx) {
		var data SkillProduct
		err := cur.Decode(&data)
		if err != nil {
			return rsData, err
		}
		rsData = append(rsData, &data)
	}
	return rsData, nil
}

func (s *Store) UpdateRecommendVal(ctx context.Context, productId uint32, recommendVal float64) error {
	_, err := s.skillProductColl.UpdateOne(ctx, bson.M{"product_id": productId}, bson.M{"$set": bson.M{"recommend_val": recommendVal}})
	return err
}

func (s *Store) DistinctSkillId(ctx context.Context) []uint32 {
	var rs []uint32
	cur, err := s.skillProductColl.Distinct(ctx, "skill_id", bson.M{"switch": true})
	if err != nil {
		return rs
	}
	for _, v := range cur {
		rs = append(rs, uint32(v.(int64)))
	}
	return rs
}

func (s *Store) DistnctUid(ctx context.Context) []uint32 {
	var rs []uint32
	cur, err := s.skillProductColl.Distinct(ctx, "uid", bson.M{"switch": true})
	if err != nil {
		return rs
	}
	for _, v := range cur {
		rs = append(rs, uint32(v.(int64)))
	}
	return rs
}

func (s *Store) DistinctUidBySkillId(ctx context.Context, skillId uint32) ([]uint32, error) {
	var rs []uint32
	cur, err := s.skillProductColl.Distinct(ctx, "uid", bson.M{"skill_id": skillId, "switch": true})
	if err != nil {
		return rs, err
	}
	for _, v := range cur {
		rs = append(rs, uint32(v.(int64)))
	}
	return rs, nil
}

func (s *Store) FindSkillProductBySkillIdPaged(ctx context.Context, skillId uint32, offset, limit int) ([]*SkillProduct, error) {
	rsData := make([]*SkillProduct, 0)
	query := bson.M{"skill_id": skillId, "switch": true}
	opts := options.Find()
	opts.SetSkip(int64(offset)).SetLimit(int64(limit))
	cur, err := s.skillProductColl.Find(ctx, query, opts)
	if err != nil {
		return rsData, err
	}
	defer cur.Close(ctx)
	for cur.Next(ctx) {
		var data SkillProduct
		err := cur.Decode(&data)
		if err != nil {
			return rsData, err
		}
		rsData = append(rsData, &data)
	}

	return rsData, nil
}

type GuaranteeWinSwitch struct {
	ProductId    uint32 `bson:"product_id"`
	GuaranteeWin bool   `bson:"guarantee_win_switch"`
}

func (s *Store) GetAllGuaranteeWinSwitch(ctx context.Context) ([]*GuaranteeWinSwitch, error) {
	rsData := make([]*GuaranteeWinSwitch, 0)
	cur, err := s.skillProductColl.Find(ctx, bson.M{}, options.Find().SetProjection(bson.M{"product_id": 1, "guarantee_win_switch": 1}))
	if err != nil {
		return rsData, err
	}
	defer cur.Close(ctx)
	for cur.Next(ctx) {
		var data GuaranteeWinSwitch
		err := cur.Decode(&data)
		if err != nil {
			return rsData, err
		}
		rsData = append(rsData, &data)
	}
	return rsData, nil
}

func (s *Store) FindSkillProductByUids(ctx context.Context, uids []uint32) ([]*SkillProduct, error) {
	rsData := make([]*SkillProduct, 0)
	query := bson.M{
		"uid":    bson.M{"$in": uids},
		"switch": true,
	}

	cur, err := s.skillProductColl.Find(ctx, query)
	if err != nil {
		return rsData, err
	}
	defer cur.Close(ctx)
	for cur.Next(ctx) {
		var data SkillProduct
		err := cur.Decode(&data)
		if err != nil {
			return rsData, err
		}
		rsData = append(rsData, &data)
	}
	return rsData, nil
}
func (s *Store) FindSkillProductByCoachUidsAndGameId(ctx context.Context, gameId uint32, uids []uint32) ([]*SkillProduct, error) {
	rsData := make([]*SkillProduct, 0)
	query := bson.M{
		"uid":      bson.M{"$in": uids},
		"switch":   true,
		"skill_id": gameId,
	}

	cur, err := s.skillProductColl.Find(ctx, query)
	if err != nil {
		return rsData, err
	}
	defer cur.Close(ctx)
	for cur.Next(ctx) {
		var data SkillProduct
		err := cur.Decode(&data)
		if err != nil {
			return rsData, err
		}
		rsData = append(rsData, &data)
	}
	return rsData, nil
}

func (s *Store) SetFirstRoundSwitch(ctx context.Context, uid, skillId uint32, switchStatus bool) error {
	_, err := s.skillProductColl.UpdateOne(ctx, bson.M{"uid": uid, "skill_id": skillId}, bson.M{"$set": bson.M{"first_round_switch": switchStatus}})
	return err
}

func (s *Store) SetNewCustomerSwitch(ctx context.Context, uid, skillId uint32, switchStatus bool) error {
	_, err := s.skillProductColl.UpdateOne(ctx, bson.M{"uid": uid, "skill_id": skillId}, bson.M{"$set": bson.M{"new_customer_switch": switchStatus}})
	return err
}

// CloseFirstRoundAllNewCustomerSwitch 关闭大神的所有新用户首单优惠开关
func (s *Store) CloseCoachAllNewCustomerSwitch(ctx context.Context, coachUid uint32) error {
	_, err := s.skillProductColl.UpdateMany(ctx, bson.M{"uid": coachUid}, bson.M{"$set": bson.M{"new_customer_switch": false}})
	return err
}

// FindSkillProduct 通用搜索，支持多个条件组合过滤
func (s *Store) FindSkillProduct(ctx context.Context, uidList []uint32, skillId, productId uint32) ([]*SkillProduct, error) {
	rsData := make([]*SkillProduct, 0)

	query := bson.M{
		"switch": true,
	}
	if len(uidList) > 0 {
		query["uid"] = bson.M{"$in": uidList}
	}
	if skillId > 0 {
		query["skill_id"] = skillId
	}
	if productId > 0 {
		query["product_id"] = productId
	}

	cur, err := s.skillProductColl.Find(ctx, query)
	if err != nil {
		return rsData, err
	}
	defer cur.Close(ctx)
	for cur.Next(ctx) {
		var data SkillProduct
		err := cur.Decode(&data)
		if err != nil {
			return rsData, err
		}
		rsData = append(rsData, &data)
	}
	return rsData, nil
}

func (s *Store) BatchFindSkillProductByUid(ctx context.Context, uids []uint32, gameId uint32) (map[uint32]*SkillProduct, error) {
	rsData := make(map[uint32]*SkillProduct)
	query := bson.M{
		"uid":      bson.M{"$in": uids},
		"skill_id": gameId,
		"switch":   true,
	}

	cur, err := s.skillProductColl.Find(ctx, query)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchFindSkillProductByUid uids:%+v, gameId:%d, err: %v", uids, gameId, err)
		return rsData, err
	}
	defer cur.Close(ctx)
	for cur.Next(ctx) {
		var data SkillProduct
		err := cur.Decode(&data)
		if err != nil {
			log.ErrorWithCtx(ctx, "BatchFindSkillProductByUid uids:%+v, gameId:%d, err: %v", uids, gameId, err)
			return rsData, err
		}
		rsData[data.Uid] = &data
	}
	return rsData, nil
}
