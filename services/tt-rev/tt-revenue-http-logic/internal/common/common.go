package common

import (
    "context"
    "crypto/md5"
    "encoding/base64"
    "encoding/binary"
    "encoding/hex"
    "encoding/json"
    "errors"
    "fmt"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/pkg/web"
    "golang.52tt.com/protocol/common/status"
    "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/conf"
    "io/ioutil"
    "net"
    "net/http"
    "net/http/httputil"
    "strconv"
    "strings"
    "time"
)

const (
    clientTypeHeaderKey    = "x-tt-client-type"
    clientVersionHeaderKey = "x-tt-client-version"
    markerIdHeaderKey      = "x-tt-market"
    clientTypeParamKey    = "tt-client-type"
    clientVersionParamKey = "tt-client-version"
    markerIdParamKey      = "tt-market"
    deviceIdParamKey      = "tt-device-id"
)

func AuthHandleInterceptor(cfg *conf.ServiceConfig, handleFunc func(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request)) func(ctx context.Context, w http.ResponseWriter, r *http.Request) {
    auth := web.NewAuth(&web.UidAuthVerify{}, cfg.ValidateToken)
    authHandler := web.NewHandler(auth, cfg.Cors, true)
    handler := authHandler.SetHandler(handleFunc)

    return func(ctx context.Context, w http.ResponseWriter, r *http.Request) {
        handler.ServeHTTP(w, r)
    }
}

func HandleWrapper(cfg *conf.ServiceConfig,
    newReq func() interface{},
    handler func(ctx context.Context, req interface{}) (interface{}, error),
) func(ctx context.Context, w http.ResponseWriter, r *http.Request) { //nolint:golint,typecheck
    return AuthHandleInterceptor(cfg, func(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
        if log.AtomLv.Level() <= log.DebugLevel {
            raw, err := httputil.DumpRequest(r, true)
            if err != nil {
                log.ErrorWithCtx(r.Context(), "HttpReqDump err: %v", err)
            } else {
                log.DebugWithCtx(r.Context(), "HttpReqDump: %s", string(raw))
            }
        }

        req := newReq()
        if err := json.Unmarshal(authInfo.Body, &req); err != nil {
            log.ErrorWithCtx(r.Context(), "HandleWrapper json.Unmarshal err: %v", err)
            _ = web.ServeAPICodeJson(w, status.ErrSys, "internal error", nil)
            return
        }

        ip, err := ipToUint32(clientIp(r))
        if err != nil {
            log.ErrorWithCtx(r.Context(), "HandleWrapper ipToUint32 err: %v", err)
            _ = web.ServeAPICodeJson(w, status.ErrSys, "internal error", nil)
            return
        }

        serviceInfo := &grpc.ServiceInfo{
            UserID:        authInfo.UserID,
            ClientIP:      ip,
            ClientVersion: authInfo.CliVersion,
            MarketID:      authInfo.MarketID,
        }
        // 从header中获取clientType
        clientTypeHeader := r.Header.Get(clientTypeHeaderKey)
        switch clientTypeHeader {
        case "pc":
            serviceInfo.ClientType = protocol.ClientTypePcTT
        case "android":
            serviceInfo.ClientType = protocol.ClientTypeANDROID
        case "ios":
            serviceInfo.ClientType = protocol.ClientTypeIOS
        }
        // 从header中获取clientVersion
        clientVersionHeader := r.Header.Get(clientVersionHeaderKey)
        if x, err := strconv.Atoi(clientVersionHeader); err == nil {
            serviceInfo.ClientVersion = uint32(x)
        }
        // 从头中获取marketId
        marketIdHeader := r.Header.Get(markerIdHeaderKey)
        if x, err := strconv.Atoi(marketIdHeader); err == nil {
            serviceInfo.MarketID = uint32(x)
        }

        // 从request param中获取clientType
        clientTypeParam := r.URL.Query().Get(clientTypeParamKey)
        switch clientTypeParam {
        case "pc":
            serviceInfo.ClientType = protocol.ClientTypePcTT
        case "android":
            serviceInfo.ClientType = protocol.ClientTypeANDROID
        case "ios":
            serviceInfo.ClientType = protocol.ClientTypeIOS
        }
        // 从request param中获取clientVersion
        clientVersionParam := r.URL.Query().Get(clientVersionParamKey)
        if x, err := strconv.Atoi(clientVersionParam); err == nil {
            serviceInfo.ClientVersion = uint32(x)
        }
        // 从request param中获取marketId
        marketIdParam := r.URL.Query().Get(markerIdParamKey)
        if x, err := strconv.Atoi(marketIdParam); err == nil {
            serviceInfo.MarketID = uint32(x)
        }
        // 从request param中获取deviceId
        deviceIdParam := r.URL.Query().Get(deviceIdParamKey)
        if len(deviceIdParam) > 0 {
            // 去掉iOS携带的中划线
            deviceIdParam = strings.ReplaceAll(deviceIdParam, "-", "")
            // 用16进制解码
            deviceId, err := hex.DecodeString(deviceIdParam)
            if err == nil && len(deviceId) > 0 {
                serviceInfo.DeviceID = deviceId
            }
        }

        ctx, cancel := context.WithTimeout(r.Context(), 2*time.Second)
        defer cancel()
        serviceInfoCtx := grpc.WithServiceInfo(ctx, serviceInfo)
        log.DebugWithCtx(serviceInfoCtx, "serviceInfo: %+v", serviceInfo)
        resp, err := handler(serviceInfoCtx, req)
        if err != nil {
            var serverErr protocol.ServerError
            if errors.As(err, &serverErr) {
                log.ErrorWithCtx(ctx, "HandleWrapper ProtocolServerErrorInterceptor serverErr: %+v", serverErr)
                _ = web.ServeAPICodeJson(w, int32(serverErr.Code()), serverErr.Message(), nil)
            } else {
                log.ErrorWithCtx(ctx, "HandleWrapper ProtocolServerErrorInterceptor err: %+v", serverErr)
                _ = web.ServeAPICodeJson(w, status.ErrSys, "内部错误", nil)
            }
            return
        }
        _ = web.ServeAPIJsonV2(w, resp)
    },
    )
}

func HandleWrapperG[R any, P any](cfg *conf.ServiceConfig,
    handler func(ctx context.Context, req *R) (*P, error),
) func(ctx context.Context, w http.ResponseWriter, r *http.Request) { //nolint:golint,typecheck
    return AuthHandleInterceptor(cfg, func(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
        if log.AtomLv.Level() <= log.DebugLevel {
            raw, err := httputil.DumpRequest(r, true)
            if err != nil {
                log.ErrorWithCtx(r.Context(), "HttpReqDump err: %v", err)
            } else {
                log.DebugWithCtx(r.Context(), "HttpReqDump: %s", string(raw))
            }
        }

        req := new(R)
        if err := json.Unmarshal(authInfo.Body, &req); err != nil {
            log.ErrorWithCtx(r.Context(), "HandleWrapper json.Unmarshal err: %v", err)
            _ = web.ServeAPICodeJson(w, status.ErrSys, "internal error", nil)
            return
        }

        ip, err := ipToUint32(clientIp(r))
        if err != nil {
            log.ErrorWithCtx(r.Context(), "HandleWrapper ipToUint32 err: %v", err)
            _ = web.ServeAPICodeJson(w, status.ErrSys, "internal error", nil)
            return
        }

        serviceInfo := &grpc.ServiceInfo{
            UserID:        authInfo.UserID,
            ClientIP:      ip,
            ClientVersion: authInfo.CliVersion,
            MarketID:      authInfo.MarketID,
        }
        // 从header中获取clientType
        clientTypeHeader := r.Header.Get(clientTypeHeaderKey)
        switch clientTypeHeader {
        case "pc":
            serviceInfo.ClientType = protocol.ClientTypePcTT
        case "android":
            serviceInfo.ClientType = protocol.ClientTypeANDROID
        case "ios":
            serviceInfo.ClientType = protocol.ClientTypeIOS
        }
        // 从header中获取clientVersion
        clientVersionHeader := r.Header.Get(clientVersionHeaderKey)
        if x, err := strconv.Atoi(clientVersionHeader); err == nil {
            serviceInfo.ClientVersion = uint32(x)
        }
        // 从头中获取marketId
        marketIdHeader := r.Header.Get(markerIdHeaderKey)
        if x, err := strconv.Atoi(marketIdHeader); err == nil {
            serviceInfo.MarketID = uint32(x)
        }

        // 从request param中获取clientType
        clientTypeParam := r.URL.Query().Get(clientTypeParamKey)
        switch clientTypeParam {
        case "pc":
            serviceInfo.ClientType = protocol.ClientTypePcTT
        case "android":
            serviceInfo.ClientType = protocol.ClientTypeANDROID
        case "ios":
            serviceInfo.ClientType = protocol.ClientTypeIOS
        }
        // 从request param中获取clientVersion
        clientVersionParam := r.URL.Query().Get(clientVersionParamKey)
        if x, err := strconv.Atoi(clientVersionParam); err == nil {
            serviceInfo.ClientVersion = uint32(x)
        }
        // 从request param中获取marketId
        marketIdParam := r.URL.Query().Get(markerIdParamKey)
        if x, err := strconv.Atoi(marketIdParam); err == nil {
            serviceInfo.MarketID = uint32(x)
        }
        // 从request param中获取deviceId
        deviceIdParam := r.URL.Query().Get(deviceIdParamKey)
        if len(deviceIdParam) > 0 {
            // 去掉iOS携带的中划线
            deviceIdParam = strings.ReplaceAll(deviceIdParam, "-", "")
            // 用16进制解码
            deviceId, err := hex.DecodeString(deviceIdParam)
            if err == nil && len(deviceId) > 0 {
                serviceInfo.DeviceID = deviceId
            }
        }

        ctx := r.Context()
        serviceInfoCtx := grpc.WithServiceInfo(ctx, serviceInfo)
        log.DebugWithCtx(serviceInfoCtx, "serviceInfo: %+v", serviceInfo)
        resp, err := handler(serviceInfoCtx, req)
        if err != nil {
            var serverErr protocol.ServerError
            if errors.As(err, &serverErr) {
                log.ErrorWithCtx(ctx, "HandleWrapper ProtocolServerErrorInterceptor serverErr: %+v", serverErr)
                _ = web.ServeAPICodeJsonV2(w, int32(serverErr.Code()), serverErr.Message(), resp)
            } else {
                log.ErrorWithCtx(ctx, "HandleWrapper ProtocolServerErrorInterceptor err: %+v", serverErr)
                _ = web.ServeAPICodeJson(w, status.ErrSys, "内部错误", nil)
            }
            return
        }
        _ = web.ServeAPIJsonV2(w, resp)
    },
    )
}

func clientIp(r *http.Request) string {
    forwards := strings.Split(r.Header.Get("X-Forwarded-For"), ",")
    if 0 != len(forwards) {
        ip := strings.TrimSpace(forwards[0])
        if ip != "" {
            return ip
        }
    }

    ip := strings.TrimSpace(r.Header.Get("X-Real-Ip"))
    if ip != "" {
        return ip
    }

    if ip, _, err := net.SplitHostPort(strings.TrimSpace(r.RemoteAddr)); err == nil {
        return ip
    }
    return ""
}

func ipToUint32(ipStr string) (uint32, error) {
    if ipStr == "::1" {
        ipStr = "127.0.0.1"
    }
    ip := net.ParseIP(ipStr)
    if ip == nil {
        return 0, fmt.Errorf("invalid IP address: %s", ipStr)
    }
    ip = ip.To4()
    return binary.BigEndian.Uint32(ip), nil
}


func HandleCoinWrapper[R any](cfg *conf.ServiceConfig,
    handler func(ctx context.Context, req *R) error,
) func(ctx context.Context, w http.ResponseWriter, r *http.Request) { //nolint:golint,typecheck
    return func(ctx context.Context, w http.ResponseWriter, r *http.Request) {
        sign := r.Header.Get("sign")
        body, err := ioutil.ReadAll(r.Body)
        if err != nil {
            log.Errorf("HandleCoinWrapper ReadAll err: %v", err)
            _ = web.ServeAPICodeJson(w, status.ErrRequestParamInvalid, "invalid body", nil)
            return
        }

        if !checkSign(sign, string(body), cfg.CoinSecretKey) {
            log.Errorf("HandleCoinWrapper checkSign fail")
            _ = web.ServeAPICodeJson(w, status.ErrRequestParamInvalid, "invalid sign", nil)
            return
        }

        req := new(R)
        err = json.Unmarshal(body, &req)
        if err != nil {
            log.Errorf("HandleCoinWrapper Unmarshal err: %v", err)
            _ = web.ServeAPICodeJson(w, status.ErrRequestParamInvalid, "Unmarshal fail", nil)
            return
        }

        err = handler(r.Context(), req)
        if err == nil {
            w.Write([]byte("1"))
        }
    }
}

func checkSign(sign, body, secretKey string) bool {
    if len(secretKey) == 0 {
        return true
    }

    md5Text := fmt.Sprintf("%v%v", body, secretKey)
    md5Byte := md5.Sum([]byte(md5Text)) // #nosec
    encoded := base64.StdEncoding.EncodeToString(md5Byte[:])
    encoded = strings.Trim(encoded, "\r\n")
    return encoded == sign
}
