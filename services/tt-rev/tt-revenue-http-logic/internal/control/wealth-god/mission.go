package wealth_god

import (
	"context"
	tyr_http "gitlab.ttyuyin.com/avengers/tyr/core/service/http"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protoGrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/common/status"
	wealth_god "golang.52tt.com/protocol/services/wealth-god"
	"golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/common"
	"golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/conf"
	wealth_god_http "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/models/wealth-god"
	"google.golang.org/grpc/codes"
)

type WealthGodMgr struct {
	WealthGodCli wealth_god.WealthGodServiceClient
	bc           conf.BusinessConfApi
}

func NewWealthGodMgr(ctx context.Context, bc conf.BusinessConfApi) *WealthGodMgr {
	wealthGodCli, _ := wealth_god.NewClient(ctx)
	return &WealthGodMgr{
		WealthGodCli: wealthGodCli,
		bc:           bc,
	}
}

func (mgr *WealthGodMgr) Register(cfg *conf.ServiceConfig, router *tyr_http.Router) {
	router.POST("/get_wealth_mission_info", common.HandleWrapperG(cfg, mgr.GetWealthMissionInfo))
	router.POST("/get_wealth_god_box_reward_list", common.HandleWrapperG(cfg, mgr.GetWealthGodBoxRewardList))
}

func (mgr *WealthGodMgr) GetWealthMissionInfo(ctx context.Context, req *wealth_god_http.GetWealthMissionInfoRequest) (*wealth_god_http.GetWealthMissionInfoResponse, error) {
	out := &wealth_god_http.GetWealthMissionInfoResponse{}
	defer func() {
		log.DebugWithCtx(ctx, "GetWealthMissionInfo request: %+v, response: %+v", req, out)
	}()

	serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetWealthMissionInfo: service info not found in context")
		return nil, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
	}
	resp, err := mgr.WealthGodCli.GetWealthMissionInfo(ctx, &wealth_god.GetWealthMissionInfoRequest{
		Uid:   serviceInfo.UserID,
		GodId: req.GodId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetWealthMissionInfo request: %+v, err: %v", req, err)
		return out, err
	}

	for _, item := range resp.GetWealthMissionInfoList() {
		out.WealthMissionInfoList = append(out.WealthMissionInfoList, &wealth_god_http.WealthMissionInfo{
			MissionName:    item.GetMissionName(),
			MissionDesc:    item.GetMissionDesc(),
			MissionType:    item.GetMissionType(),
			MissionIcon:    item.GetMissionIcon(),
			IsReachLimit:   item.GetIsReachLimit(),
			CurrentCnt:     item.GetCurrentCnt(),
			MissionCntUint: item.GetMissionCntUint(),
			NeedMissionCnt: item.GetNeedMissionCnt(),
		})
	}
	return out, nil
}

func (mgr *WealthGodMgr) GetWealthGodBoxRewardList(ctx context.Context, in *wealth_god_http.GetWealthGodBoxRewardListRequest) (*wealth_god_http.GetWealthGodBoxRewardListResponse, error) {
	out := &wealth_god_http.GetWealthGodBoxRewardListResponse{}
	defer func() {
		log.DebugWithCtx(ctx, "GetWealthGodBoxRewardList in:%+v, out:%+v", in, out)
	}()

	serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return nil, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
	}

	rsp, err := mgr.WealthGodCli.GetWealthGodBoxRewardList(ctx, &wealth_god.GetWealthGodBoxRewardListRequest{
		Uid:      serviceInfo.UserID,
		Page:     in.Page,
		PageSize: in.PageSize,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetWealthGodBoxRewardList err: %v", err)
		return nil, err
	}

	out.HasMore = rsp.GetHasMore()
	for _, item := range rsp.GetRewardList() {
		out.RewardList = append(out.RewardList, &wealth_god_http.WealthGodBoxRewardItem{
			Id:       item.Id,
			IconUrl:  item.IconUrl,
			Title:    item.Title,
			SubTitle: item.SubTitle,
			IsRare:   item.IsRare,
			TimeStr:  item.TimeStr,
		})
	}
	return out, nil
}
