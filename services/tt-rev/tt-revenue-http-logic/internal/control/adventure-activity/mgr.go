package adventure_activity

import (
    "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/common"
    tyr_http "gitlab.ttyuyin.com/avengers/tyr/core/service/http"
    adventure_activity "golang.52tt.com/protocol/services/adventure-activity"
    "context"
    "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/conf"
    pb "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/models/adventure-activity"
    "golang.52tt.com/pkg/tbean"
    risk_mng_api "golang.52tt.com/clients/risk-mng-api"
    chance_game_entry "golang.52tt.com/protocol/services/chance-game-entry"
    "golang.52tt.com/pkg/protocol"
    grpcProtocol "golang.52tt.com/pkg/protocol/grpc"
    "fmt"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/protocol/common/status"
    "google.golang.org/grpc/codes"
    "time"
    useronline "golang.52tt.com/clients/user-online"
    "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channelol_go"
    "golang.52tt.com/protocol/app"
    userProfileApi "golang.52tt.com/clients/user-profile-api"
)

const (
    PropUnitPriceTBean = 2000
)

var DrawAmountOptions = []uint32{
    1, 10,
}

func checkDrawAmount(amount uint32) bool {
    for _, v := range DrawAmountOptions {
        if amount == v {
            return true
        }
    }
    return false
}

type Mgr struct {
    adventureActivityCli adventure_activity.AdventureActivityClient
    tbeanCli             tbean.Client
    riskMngApiCli        risk_mng_api.IClient
    chanceGameEntryCli   chance_game_entry.ChanceGameEntryClient
    userOnlineCli        useronline.IClient
    channelOlCli         channelol_go.ChannelolGoClient

    userProfileApi userProfileApi.IClient
}

func NewMgr(sc *conf.ServiceConfig) *Mgr {
    adventureActivityCli := adventure_activity.MustNewClient(context.Background())
    tbeanCli := tbean.NewClient(sc.TbeanContextPath)
    riskMngApiCli := risk_mng_api.NewIClient()
    chanceGameEntryCli := chance_game_entry.MustNewClient(context.Background())

    channelOlGoCli := channelol_go.MustNewClient(context.Background())
    userOnlineCli, _ := useronline.NewClient()
    userProfileApi, _ := userProfileApi.NewClient()

    m := &Mgr{
        adventureActivityCli: adventureActivityCli,
        tbeanCli:             tbeanCli,
        riskMngApiCli:        riskMngApiCli,
        chanceGameEntryCli:   chanceGameEntryCli,
        channelOlCli:         channelOlGoCli,
        userOnlineCli:        userOnlineCli,
        userProfileApi:       userProfileApi,
    }
    return m
}

func (m *Mgr) Register(cfg *conf.ServiceConfig, router *tyr_http.Router) {
    chile := router.Child("/adventure-activity")
    chile.POST("/get_curr_game_info", common.HandleWrapperG(cfg, m.GetCurrGameInfo))               //获取活动信息
    chile.POST("/buy_prop", common.HandleWrapperG(cfg, m.BuyProp))                                 //购买礼包
    chile.POST("/draw_card", common.HandleWrapperG(cfg, m.DrawCard))                               //抽奖
    chile.POST("/get_platform_winning_info", common.HandleWrapperG(cfg, m.GetPlatformWinningInfo)) //获取平台中奖记录
    chile.POST("/get_user_record", common.HandleWrapperG(cfg, m.GetUserRecord))                    //获取用户冒险记录
    chile.POST("/get_user_balance", common.HandleWrapperG(cfg, m.GetUserBalance))                  //获取用户T豆余额
}

func (m *Mgr) GetCurrGameInfo(ctx context.Context, req *pb.GetCurrGameInfoRequest) (*pb.GetCurrGameInfoResponse, error) {
    out := &pb.GetCurrGameInfoResponse{}
    serviceInfo, ok := grpcProtocol.ServiceInfoFromContext(ctx)
    if !ok {
        return out, fmt.Errorf("GetCurrGameInfo fail to get service info")
    }
    log.DebugWithCtx(ctx, "GetCurrGameInfo, uid: %d, start", serviceInfo.UserID)
    uid := serviceInfo.UserID

    // 检查用户权限
    entryResp, err := m.chanceGameEntryCli.CheckAdventureActAccess(ctx, &chance_game_entry.CheckAdventureActAccessReq{
        Uid:       uid,
        ChannelId: req.GetChannelId(),
        MarketId:  serviceInfo.MarketID,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetCurrGameInfo CheckAdventureActAccess fail,err:%v", err)
        return out, protocol.NewExactServerError(nil, status.ErrAdventureActivitySystemErr)
    }

    if !entryResp.GetAccess().GetAccess() {
        out.HasAccess = false
        out.NoPermissionText = "本活动玩法仅限贵族等级子爵及以上用户参与~"
        return out, nil
    }

    // 获取游戏信息
    gameResp, err := m.adventureActivityCli.GetCurrGameInfo(ctx, &adventure_activity.GetCurrGameInfoRequest{
        Uid:       uid,
        ChannelId: req.GetChannelId(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetCurrGameInfo GetCurrGameInfo fail,err:%v", err)
        return out, protocol.NewExactServerError(codes.OK, status.ErrAdventureActivitySystemErr, "系统错误")
    }

    levelList := make([]*pb.LevelCfg, 0, len(gameResp.GetLevelList()))
    for _, v := range gameResp.GetLevelList() {
        level := v.GetLevelCfg()

        levelList = append(levelList, &pb.LevelCfg{
            LevelId:    level.GetLevelId(),
            LevelName:  level.GetLevelName(),
            MaxNFixed:  level.GetMaxNFixed(),
            CardList:   transAwardListPb(v.GetAwardList()),
            LevelAward: transAwardInfoPb(level.GetLevelAward()),
        })
    }

    out = &pb.GetCurrGameInfoResponse{
        ActivityId:   gameResp.GetActivityId(),
        ActivityName: gameResp.GetActivityName(),
        BeginTime:    gameResp.GetBeginTime(),
        EndTime:      gameResp.GetEndTime(),
        BuyPropCfg: &pb.BuyPropCfg{
            GiftInfo: &pb.AwardInfo{
                AwardName: gameResp.GetBuyPropCfg().GetGiftInfo().GetAwardName(),
                AwardPic:  gameResp.GetBuyPropCfg().GetGiftInfo().GetAwardIcon(),
                AwardType: 1,
                Amount:    1,
            },
            BuyAmountOptions: gameResp.GetBuyPropCfg().GetBuyAmountOptions(),
            UnitPrice:        gameResp.GetBuyPropCfg().GetUnitPrice(),
            DailyBuyLimit:    0,
            SingleBuyLimit:   0,
        },
        LevelList: levelList,
        TopNAwardInfo: &pb.TopNAwardCfg{
            TopNLimit: gameResp.GetTopNAwardInfo().GetTopNLimit(),
            AwardList: transAwardListPb(gameResp.GetTopNAwardInfo().GetAwardList()),
        },
        UserPlayFile: transUserPlayFilePb(gameResp.GetUserPlayFile()),
        ServerTs:     time.Now().Unix(), // todo
        HasAccess:    true,
    }

    log.DebugWithCtx(ctx, "GetCurrGameInfo, uid: %d, userPlayFile:%+v", serviceInfo.UserID, gameResp.GetUserPlayFile())
    return out, nil
}

func transAwardListPb(infoList []*adventure_activity.AwardInfo) []*pb.AwardInfo {
    awardList := make([]*pb.AwardInfo, 0, len(infoList))
    for _, v := range infoList {
        info := transAwardInfoPb(v)
        if info == nil {
            continue
        }
        awardList = append(awardList, info)
    }
    return awardList
}

func transAwardInfoPb(info *adventure_activity.AwardInfo) *pb.AwardInfo {
    if info == nil {
        return nil
    }
    return &pb.AwardInfo{
        AwardId:   info.AwardId,
        AwardName: info.AwardName,
        AwardPic:  info.AwardIcon,
        AwardDesc: info.AwardDesc,
        AwardType: info.AwardType,
        Amount:    info.Amount,
        ExpTime:   info.ExpTime,
    }
}

func transUserPlayFilePb(info *adventure_activity.UserPlayFile) *pb.UserPlayFile {
    if info == nil {
        return nil
    }
    return &pb.UserPlayFile{
        LevelId:        info.LevelId,
        CurrentChance:  info.CurrentChance,
        UserN:          info.UserN,
        CardCollection: info.CardCollection,
    }
}

func (m *Mgr) BuyProp(ctx context.Context, req *pb.BuyPropRequest) (*pb.BuyPropResponse, error) {
    out := &pb.BuyPropResponse{}

    // 参数检查
    if req.GetBuyAmount() == 0 || req.GetBuyAmount() > 100000 {
        log.ErrorWithCtx(ctx, "BuyProp 参数错误 req:%+v", req)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }

    serviceInfo, ok := grpcProtocol.ServiceInfoFromContext(ctx)
    if !ok {
        return out, fmt.Errorf("BuyProp fail to get service info")
    }
    log.DebugWithCtx(ctx, "BuyProp, uid: %d, start", serviceInfo.UserID)
    uid := serviceInfo.UserID

    // 用户权限检查
    entryResp, err := m.chanceGameEntryCli.CheckAdventureActAccess(ctx, &chance_game_entry.CheckAdventureActAccessReq{
        Uid:       serviceInfo.UserID,
        ChannelId: req.GetChannelId(),
        MarketId:  serviceInfo.MarketID,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "BuyProp CheckAdventureActAccess fail,err:%v", err)
        return out, protocol.NewExactServerError(codes.OK, status.ErrAdventureActivitySystemErr, "系统错误")
    }

    if !entryResp.GetAccess().GetAccess() {
        return out, protocol.NewExactServerError(codes.OK, status.ErrAdventureActivityNotAvailable, "暂无参与权限")
    }

    balance, e := m.tbeanCli.GetBalance(ctx, tbean.AppID_TT_HZ, tbean.AccountTypeCustomer, uid)
    if e != nil {
        serverError := protocol.ToServerError(e)
        if serverError != tbean.ErrAccountNotExists {
            log.ErrorWithCtx(ctx, "GetUserTBeanBalance GetBalance err ,uid :%v , err : %v", uid, e)
            return out, nil
        }
    }

    if uint32(balance) < req.GetBuyAmount()*PropUnitPriceTBean {
        log.ErrorWithCtx(ctx, "CoinConsume failed to GetBalance. uid:%d req %v, err %v", uid, req, err)
        return out, protocol.NewExactServerError(codes.OK, status.ErrTbeanNoEnoughBalance, "豆豆余额不足")
    }

    // 风控检查
    riskResp := m.consumeRiskCheck(ctx, &riskCheckReq{
        uid:             uid,
        cid:             req.GetChannelId(),
        tbeanFee:        req.GetBuyAmount() * PropUnitPriceTBean,
        faceResultToken: req.GetFaceAuthResultToken(),
        svrInfo:         serviceInfo,
    })
    if riskResp.errCode != 0 {
        out.FaceAuthContextJson = riskResp.faceAuthContextJson
        log.ErrorWithCtx(ctx, "riskCheck failed. riskResp:%+v", uid, riskResp)
        return out, protocol.NewExactServerError(codes.OK, int(riskResp.errCode), riskResp.errMsg)
    }

    // 购买
    buyResp, err := m.adventureActivityCli.BuyChance(ctx, &adventure_activity.BuyChanceRequest{
        Uid:          uid,
        ChannelId:    req.GetChannelId(),
        ChanceAmount: req.GetBuyAmount(),
        Fee:          req.GetBuyAmount() * PropUnitPriceTBean,
    })
    if err != nil {
        e := protocol.ToServerError(err)
        message := e.Message()
        if e.Code() == status.ErrTbeanNoEnoughBalance {
            message = "豆豆余额不足"
        }
        log.ErrorWithCtx(ctx, "BuyChance failed BuyChance req:%+v err:%v", req, err)
        return out, protocol.NewExactServerError(codes.OK, e.Code(), message)
    }

    out.Balance = buyResp.GetBalance()
    out.FinalChanceAmount = buyResp.GetFinalChanceAmount()
    log.InfoWithCtx(ctx, "BuyChance success. req:%+v  out:%+v", req, out)
    return out, nil
}

func (m *Mgr) DrawCard(ctx context.Context, req *pb.DrawCardRequest) (*pb.DrawCardResponse, error) {
    out := &pb.DrawCardResponse{}

    // 请求参数检查
    if !checkDrawAmount(req.GetAmount()) || req.GetLevelId() == 0 {
        log.ErrorWithCtx(ctx, "DrawCard invalid param, req:%+v", req)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }

    serviceInfo, ok := grpcProtocol.ServiceInfoFromContext(ctx)
    if !ok {
        return out, fmt.Errorf("DrawCard fail to get service info")
    }
    log.DebugWithCtx(ctx, "DrawCard, uid: %d, start", serviceInfo.UserID)
    uid := serviceInfo.UserID

    // 不检查用户权限了

    // 去抽奖
    drawResp, err := m.adventureActivityCli.LotteryDraw(ctx, &adventure_activity.LotteryDrawRequest{
        Uid:       uid,
        ChannelId: req.GetChannelId(),
        LevelId:   req.GetLevelId(),
        Amount:    req.GetAmount(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "DrawCard, uid: %d, err: %v", serviceInfo.UserID, err)
        return nil, err
    }

    resultList := make([]*pb.DrawResult, 0, len(drawResp.GetResultList()))

    for _, v := range drawResp.GetResultList() {
        resultList = append(resultList, &pb.DrawResult{
            LevelId:    v.LevelId,
            AwardList:  transAwardListPb(v.GetAwardList()),
            ResultType: v.ResultType,
        })
    }

    out = &pb.DrawCardResponse{
        ResultList:   resultList,
        UserPlayFile: transUserPlayFilePb(drawResp.GetUserPlayFile()),
    }

    log.InfoWithCtx(ctx, "DrawCard success req:%+v out:%+v", req, out)
    return out, nil
}

func (m *Mgr) GetPlatformWinningInfo(ctx context.Context, req *pb.GetPlatformWinningRecordRequest) (*pb.GetPlatformWinningRecordResponse, error) {
    out := &pb.GetPlatformWinningRecordResponse{}

    svrResp, err := m.adventureActivityCli.GetPlatformWinningRecord(ctx, &adventure_activity.GetPlatformWinningRecordRequest{
        Version: req.GetVersion(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetPlatformWinningRecord failed req:%+v err:%v", req, err)
        return out, err
    }

    uidMap := make(map[uint32]struct{})
    uidList := make([]uint32, 0, len(svrResp.GetRecordList()))
    for _, v := range svrResp.GetRecordList() {
        if _, ok := uidMap[v.GetUid()]; ok {
            continue
        }
        uidMap[v.GetUid()] = struct{}{}
        uidList = append(uidList, v.GetUid())
    }

    userMap := make(map[uint32]*app.UserProfile)

    // 如果uidList大小大于200，则分批处理, 每次最多处理200个
    if len(uidList) > 200 {

        for i := 0; i < len(uidList); i += 200 {
            end := i + 200
            if end > len(uidList) {
                end = len(uidList)
            }

            batch := make([]uint32, end-i)
            copy(batch, uidList[i:end])

            userResp, err := m.userProfileApi.BatchGetUserProfileV2(ctx, batch, false)
            if err != nil {
                log.ErrorWithCtx(ctx, "BatchGetUserProfileV2 failed. uids: %+v, err: %v", batch, err)
                return out, nil
            }

            for k, v := range userResp {
                userMap[k] = v
            }
        }
    } else {
        userMap, err = m.userProfileApi.BatchGetUserProfileV2(ctx, uidList, false)
        if err != nil {
            log.ErrorWithCtx(ctx, "BatchGetUserProfileV2 failed. uids: %+v, err: %v", uidList, err)
            return out, nil
        }
    }

    recordList := make([]*pb.PlatformWinningRecord, 0, len(svrResp.GetRecordList()))
    for _, v := range svrResp.GetRecordList() {
        if _, ok := userMap[v.GetUid()]; !ok {
            continue
        }
        recordList = append(recordList, &pb.PlatformWinningRecord{
            Uid:        v.GetUid(),
            Nickname:   userMap[v.GetUid()].GetNickname(),
            Username:   userMap[v.GetUid()].GetAccount(),
            LevelId:    v.GetLevelId(),
            AwardInfo:  transAwardInfoPb(v.GetAwardInfo()),
            ResultType: v.GetResultType(),
        })
    }

    out = &pb.GetPlatformWinningRecordResponse{
        NewVersion:     svrResp.GetNewVersion(),
        RecordList:     recordList,
        TopNUsed:       svrResp.GetTopNUsed(),
        ReqDurationSec: svrResp.GetReqDurationSec(),
    }

    return out, nil
}

func (m *Mgr) GetUserRecord(ctx context.Context, req *pb.GetUserAdventureRecordRequest) (*pb.GetUserAdventureRecordResponse, error) {
    out := &pb.GetUserAdventureRecordResponse{}
    serviceInfo, ok := grpcProtocol.ServiceInfoFromContext(ctx)
    if !ok {
        return out, fmt.Errorf("GetUserRecord fail to get service info")
    }
    log.DebugWithCtx(ctx, "GetUserRecord, uid: %d, start", serviceInfo.UserID)
    uid := serviceInfo.UserID

    svrResp, err := m.adventureActivityCli.GetUserAdventureRecord(ctx, &adventure_activity.GetUserAdventureRecordRequest{
        RecordType: req.GetRecordType(),
        Offset:     req.GetOffset(),
        Limit:      req.GetLimit(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserAdventureRecord failed, uid:%d, err:%v", uid, err)
        return out, err
    }

    recordList := make([]*pb.DrawResult, 0)
    for _, v := range svrResp.GetRecordList() {
        out.RecordList = append(out.RecordList, &pb.DrawResult{
            LevelId:    v.LevelId,
            AwardList:  transAwardListPb(v.GetAwardList()),
            ResultType: v.ResultType,
        })
    }

    out.RecordList = recordList
    out.NextOffset = svrResp.GetNextOffset()
    return out, nil
}

func (m *Mgr) GetUserBalance(ctx context.Context, req *pb.GetUserBalanceRequest) (*pb.GetUserBalanceRespone, error) {
    out := &pb.GetUserBalanceRespone{}

    serviceInfo, ok := grpcProtocol.ServiceInfoFromContext(ctx)
    if !ok {
        return out, fmt.Errorf("GetUserBalance fail to get service info")
    }
    log.DebugWithCtx(ctx, "GetUserBalance, uid: %d, start", serviceInfo.UserID)
    uid := serviceInfo.UserID

    balance, e := m.tbeanCli.GetBalance(ctx, tbean.AppID_TT_HZ, tbean.AccountTypeCustomer, uid)
    if e != nil {
        serverError := protocol.ToServerError(e)
        if serverError != tbean.ErrAccountNotExists {
            log.ErrorWithCtx(ctx, "GetUserTBeanBalance GetBalance err ,uid :%v , err : %v", uid, e)
            return out, nil
        }
    }
    out.Balance = uint64(balance)
    return out, nil
}
