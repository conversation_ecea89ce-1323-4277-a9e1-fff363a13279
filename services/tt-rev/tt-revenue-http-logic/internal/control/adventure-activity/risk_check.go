package adventure_activity

import (
    "fmt"
    "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channelol_go"
    riskMngApiPb "golang.52tt.com/protocol/services/risk-mng-api"
    apppb "golang.52tt.com/protocol/app"
    "golang.52tt.com/protocol/services/tt_rev_common"
    "context"
    user_online "golang.52tt.com/protocol/services/user-online"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    serviceinfo "gitlab.ttyuyin.com/avengers/tyr/core/service/basepb/info"
)

type riskCheckReq struct {
    uid             uint32
    cid             uint32
    tbeanFee        uint32
    faceResultToken string
    svrInfo         *serviceinfo.ServiceInfo
}

type riskCheckResp struct {
    faceAuthContextJson string
    errMsg              string
    errCode             int32
}

func (m *Mgr) consumeRiskCheck(ctx context.Context, req *riskCheckReq) *riskCheckResp {
    resp := &riskCheckResp{}

    if req == nil {
        log.ErrorWithCtx(ctx, "riskCheck req is nil")
        // 非关键路径错误
        return resp
    }

    onlineInfo := m.getUserOnlineInfo(ctx, req.uid)

    cid := uint32(0)
    // 查询用户当前所在的房间
    channelResp, err := m.channelOlCli.GetUserChannelId(ctx, &channelol_go.GetUserChannelIdReq{
        Uid: req.uid,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "riskCheck failed to GetUserChannelId. checkReq.uid %+v err %v", req, err)
        // 非关键路径，可忽略系统错误
    }
    cid = channelResp.GetChannelId()

    feeRMB := float64(req.tbeanFee) / 100.0
    // feeRMB to string
    feeStr := fmt.Sprintf("%.2f", feeRMB)

    checkReq := &riskMngApiPb.CheckReq{
        Scene: "H5_CONSUME",
        SourceEntity: &riskMngApiPb.Entity{
            // 必选
            Uid:       req.uid,
            ClientIp:  onlineInfo.GetClientIp(),
            ChannelId: cid,
        },
        // 通用参数传递
        CustomParams: map[string]string{
            "consume_type": "1",                                                                                        // 1-"普通消费"
            "scene_id":     fmt.Sprintf("%d", tt_rev_common.ConsumeSceneType_CONSUME_SCENE_TYPE_CHANNEL_EXT_GAME_ZDXX), // 活动玩法
            "amount":       feeStr,
        },
    }

    if req.svrInfo.ClientType == uint16(apppb.TT_CLIENT_TYPE_TT_CLIENT_TYPE_PC_TT) {
        // PC端补充RiskCommInfo.Token
        checkReq.RiskCommInfo = &riskMngApiPb.RiskCommInfo{
            Token: req.faceResultToken,
        }

    } else {
        // 移动端：补充FaceAuthInfo.ResultToken
        checkReq.SourceEntity.FaceAuthInfo = &riskMngApiPb.FaceAuthInfo{
            ResultToken: req.faceResultToken,
        }
    }

    checkResp, err := m.riskMngApiCli.CheckHelper(ctx, checkReq, nil)
    if err != nil {
        // 系统错误，风控非关键路径，可忽略系统错误
        log.ErrorWithCtx(ctx, "riskCheck risk-mng-api.Check failed, err:%v, req:%+v", err, checkReq)
        return resp
    }

    // 命中风控拦截
    if checkResp.ErrCode < 0 {
        // 建议打个 info 拦截日志，方便排查，风控拦截日志不会很多
        log.InfoWithCtx(ctx, "riskCheck risk-mng-api.Check hit, req:%+v, resp:%+v", checkReq, checkResp)

        // 返回错误码给客户端，并设置 gRPC 错误码为 OK
        return &riskCheckResp{
            faceAuthContextJson: string(checkResp.ErrInfo), // 需要返回 ErrInfo 给客户端
            errMsg:              checkResp.ErrMsg,
            errCode:             checkResp.ErrCode,
        }
    }

    log.DebugWithCtx(ctx, "riskCheck risk-mng-api.Check not hit, req:%+v, resp:%+v", checkReq, checkResp)
    // 无拦截
    return resp
}

func (m *Mgr) getUserOnlineInfo(ctx context.Context, uid uint32) *user_online.OnlineInfo {
    onlineInfo, err := m.userOnlineCli.GetLatestOnlineInfo(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "getUserOnlineInfo failed to GetLatestOnlineInfo. uid:%d, err %+v", uid, err)
        return nil
    }

    return onlineInfo
}
