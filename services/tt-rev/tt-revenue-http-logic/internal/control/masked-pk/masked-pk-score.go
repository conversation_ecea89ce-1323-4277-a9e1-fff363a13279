package masked_pk

import (
	"golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/models/gen-go"
	"net/http"
	"time"

	json "github.com/json-iterator/go"
	maskedPKCli "golang.52tt.com/clients/masked-pk-score"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/web"
	anchorcontract_go "golang.52tt.com/protocol/services/anchorcontract-go"
	masked_pk_score "golang.52tt.com/protocol/services/masked-pk-score"
	"golang.org/x/net/context"
)

type maskPKMgr struct {
	maskedPkScoreCli       maskedPKCli.IClient
	anchorContractGoClient anchorcontract_go.AnchorContractGoClient
}

func NewMaskPKMgr(
	maskedPkScoreCli maskedPKCli.IClient,
	anchorContractGoClient anchorcontract_go.AnchorContractGoClient,
) *maskPKMgr {
	return &maskPKMgr{
		maskedPkScoreCli:       maskedPkScoreCli,
		anchorContractGoClient: anchorContractGoClient,
	}
}

func (m *maskPKMgr) GetMaskedPkScoreHandler(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*3)
	defer cancel()

	uid := authInfo.UserID

	log.Debugf("GetMaskedPkScoreHandler %v", uid)

	score, err := m.maskedPkScoreCli.GetUserMaskPkScore(ctx, uid)
	if err != nil {
		log.Errorf("GetMaskedPkScoreHandler Failed to GetUserMaskPkScore, uid:%v, err %+v", uid, err)
		_ = web.ServeAPICodeJson(w, int32(err.Code()), err.Error(), nil)
		return
	}

	hasWithdraw := false
	if score > 0 {
		// 积分大于0
		hasWithdraw = true

	} else {
		contract, err := m.anchorContractGoClient.GetUserContractCacheInfo(ctx, &anchorcontract_go.GetUserContractCacheInfoReq{Uid: uid})
		if err != nil {
			log.Errorf("GetMaskedPkScoreHandler Failed to GetUserContractCacheInfo, uid:%v, err %+v", uid, err)
		} else {
			if contract.GetContract().GetActorUid() == uid && contract.GetContract().GetGuildId() > 0 {
				// 有签约
				hasWithdraw = true
			}
		}
	}

	anchorScore := api.MaskedPkScore{
		Uid:         uid,
		Score:       score,
		HasWithdraw: hasWithdraw,
	}
	_ = web.ServeAPIJson(w, &anchorScore)

	log.Debugf("GetMaskedPkScoreHandler resp:%v %v", anchorScore, uid)
}

// 积分流水记录
func (m *maskPKMgr) GetMaskedPkScoreLogHandler(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*3)
	defer cancel()

	uid := authInfo.UserID
	var in api.GetMaskedPkScoreLogReq
	err := json.Unmarshal(authInfo.Body, &in)
	if err != nil {
		log.Errorf("GetMaskedPkScoreLogHandler Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.Debugf("GetMaskedPkScoreLogHandler %v", in)

	if in.GetPage() == 0 || in.GetPageSize() == 0 {
		log.Errorf("GetMaskedPkScoreLogHandler bad page. in:%+v", in)
		web.ServeBadReq(w)
		return
	}

	now := time.Now()
	begin := (in.GetPage() - 1) * in.GetPageSize()

	req := &masked_pk_score.GetUserMaskPkChangeLogReq{
		Uid:   uid,
		Begin: begin,
		Limit: in.GetPageSize(),
		SourceTypeList: []uint32{
			uint32(masked_pk_score.SourceType_EntertainmentMaskedPkAward),
			uint32(masked_pk_score.SourceType_LiveMaskedPkAward),
			uint32(masked_pk_score.SourceType_ScoreExchangeReturn),
			uint32(masked_pk_score.SourceType_ScoreExchange),
			uint32(masked_pk_score.SourceType_GuildChangePrivate),
			uint32(masked_pk_score.SourceType_GuildQuit),
			uint32(masked_pk_score.SourceType_GuildOfficalRecycle),
			uint32(masked_pk_score.SourceType_GuildExchange),
		},
	}

	list, serr := m.maskedPkScoreCli.GetUserMaskPkChangeLog(ctx, req)
	if serr != nil {
		log.Errorf("GetMaskedPkScoreLogHandler GetChannelLiveAnchorScoreLog in:%+v err:%v", in, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Error(), nil)
		return
	}

	out := &api.GetMaskedPkScoreLogResp{}
	var desc string

	for _, r := range list {
		switch masked_pk_score.SourceType(r.SourceType) {
		case masked_pk_score.SourceType_LiveMaskedPkAward:
			desc = "语音直播蒙面PK积分"
		case masked_pk_score.SourceType_EntertainmentMaskedPkAward:
			desc = "娱乐厅蒙面PK积分"
		case masked_pk_score.SourceType_ScoreExchangeReturn:
			desc = "提现失败积分退还"
		case masked_pk_score.SourceType_ScoreExchange:
			desc = "积分提现"
		case masked_pk_score.SourceType_GuildChangePrivate:
			desc = "更改结算方式，扣除对公积分"
		case masked_pk_score.SourceType_GuildQuit:
			desc = "个人对公合同失效，扣除对公积分"
		case masked_pk_score.SourceType_GuildOfficalRecycle:
			desc = "个人对公合同失效，扣除对公积分"
		case masked_pk_score.SourceType_GuildExchange:
			desc = "每日个人对公积分归入会长账户"

		default:
			continue
		}

		out.List = append(out.List, &api.MaskedPkScoreLog{
			OrderId:  r.GetOrderId(),
			Amount:   r.GetChangeScore(),
			CreateAt: r.GetCreateTime(),
			Desc:     desc,
		})
	}

	_ = web.ServeAPIJson(w, out)
	log.Debugf("GetMaskedPkScoreLogHandler in:%+v, resp:%v %v cost:%v", in, out, time.Since(now))
}
