//go:generate mockgen -destination=../../mocks/mock_skill_service.go -package=mocks golang.52tt.com/protocol/services/esport-skill EsportSkillClient
package esport

import (
	"context"
	"crypto/rand"
	"encoding/json"
	"fmt"
	"math/big"
	"net/http"
	"time"

	"github.com/thinkeridea/go-extend/exnet"
	grpc "gitlab.ttyuyin.com/avengers/tyr/core/service/basepb/info"
	tyr_http "gitlab.ttyuyin.com/avengers/tyr/core/service/http"
	"go.opentelemetry.io/otel/codes"
	esport_role "golang.52tt.com/clients/esport-role"
	risk_mng_api "golang.52tt.com/clients/risk-mng-api"
	ttcProxy "golang.52tt.com/clients/ttc-proxy"
	"golang.52tt.com/pkg/device_id"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/pkg/web"
	"golang.52tt.com/protocol/common/status"
	anchorContractPb "golang.52tt.com/protocol/services/anchorcontract-go"
	anchorcontract_go "golang.52tt.com/protocol/services/anchorcontract-go"
	esport_skill "golang.52tt.com/protocol/services/esport-skill"
	esportRolePb "golang.52tt.com/protocol/services/esport_role"
	riskMngApiPb "golang.52tt.com/protocol/services/risk-mng-api"
	"golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/common"
	"golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/conf"
	esport_http "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/models/esport"
)

type ApplyRoleMgrImpl struct {
	sc                *conf.ServiceConfig
	bc                conf.BusinessConfApi
	ESportRoleCli     esport_role.IClient
	RiskMngApiCli     risk_mng_api.IClient
	AnchorContractCli anchorcontract_go.AnchorContractGoClient
	ESportSkillCli    esport_skill.EsportSkillClient
	TTCProxyClient    ttcProxy.IClient
}

func NewApplyRoleMgr(
	esportRoleCli esport_role.IClient,
	riskMngRoleCli risk_mng_api.IClient,
	anchorContractCli anchorcontract_go.AnchorContractGoClient,
	esportSkillCli *esport_skill.Client,
	TTCProxyClient ttcProxy.IClient,
	sc *conf.ServiceConfig,
	bc conf.BusinessConfApi,
) *ApplyRoleMgrImpl {
	return &ApplyRoleMgrImpl{
		bc:                bc,
		sc:                sc,
		ESportRoleCli:     esportRoleCli,
		RiskMngApiCli:     riskMngRoleCli,
		AnchorContractCli: anchorContractCli,
		ESportSkillCli:    esportSkillCli,
		TTCProxyClient:    TTCProxyClient,
	}
}
func (apply *ApplyRoleMgrImpl) Register(cfg *conf.ServiceConfig, router *tyr_http.Router) {
	child := router.Child("/esport/apply")
	child.POST("/get_game_list", common.AuthHandleInterceptor(cfg, apply.GetESportGameList))
	child.POST("/get_skill_conf", common.AuthHandleInterceptor(cfg, apply.GetSkillConfByGameId))
	child.POST("/apply_role", common.AuthHandleInterceptor(cfg, apply.ApplyESportRole))
	child.POST("/apply_risk_check", common.AuthHandleInterceptor(cfg, apply.ApplyRiskCheck))
}

const (
	ApplySourcePersonal = uint32(1)
	ApplySourceGuild    = uint32(2)

	ApplyStatusWaitForGuild    = uint32(1)
	ApplyStatusWaitForPlatform = uint32(2)
)

func toRiskCheckScene(source uint32) string {
	if source == ApplySourcePersonal {
		return "APPLY_ESPORT_COACH"
	} else {
		return "APPLY_GUILD_ESPORT_COACH"
	}
}

func applyStatusToPb(applyType, applyStatus uint32) uint32 {
	if applyType == 0 {
		return 0
	}
	if applyType == uint32(esportRolePb.ApplyESportRequset_ESPORT_APPLY_TYPE_PERSONAL) {
		return ApplyStatusWaitForPlatform

	} else {

		if applyStatus < uint32(esportRolePb.ApplyESportAuditType_ESPORT_AUDIT_TYPE_WAIT_FOR_PLATFORM) {
			return ApplyStatusWaitForGuild
		} else {
			return ApplyStatusWaitForPlatform
		}
	}
}

func genServiceInfoCtx(ctx context.Context, authInfo *web.AuthInfo, deviceId string, appVersion, clientType uint32) *grpc.ServiceInfo {
	serviceInfo := &grpc.ServiceInfo{}
	serviceInfo.UserID = authInfo.UserID
	serviceInfo.DeviceID = device_id.ParseStringDeviceId(deviceId)
	serviceInfo.TerminalType = protocol.PackTerminalType(authInfo.Platform, authInfo.OS, authInfo.AppID)
	serviceInfo.ClientVersion = appVersion
	serviceInfo.ClientType = uint16(clientType)
	serviceInfo.MarketID = authInfo.MarketID
	log.Debugf("genServiceInfoCtx serviceInfo:%+v", serviceInfo)
	return serviceInfo
}

func (apply *ApplyRoleMgrImpl) riskCheckBeforeApplyRole(ctx context.Context, ip string, authInfo *web.AuthInfo, in *esport_http.ApplyRiskCheckRequest) (faceAuthContextJson, errMsg string, errCode int32) {

	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok || svrInfo == nil {
		log.WarnWithCtx(ctx, "riskCheckBeforeApplyRole ServiceInfoFromContext fail. in:%+v", in)
	} else {
		log.DebugWithCtx(ctx, "riskCheckBeforeApplyRole serviceInfo:%+v", svrInfo)
	}

	checkReq := &riskMngApiPb.CheckReq{
		Scene: toRiskCheckScene(in.GetSource()),
		SourceEntity: &riskMngApiPb.Entity{
			// 必选
			Uid: authInfo.UserID,
			// 必选，人脸信息
			FaceAuthInfo: &riskMngApiPb.FaceAuthInfo{
				ResultToken: in.GetFaceAuthResultToken(),
			},

			// 设备信息，如果 ctx 里面有 servceInfo，可以不填，风控端自行查 ctx
			DeviceIdRaw:  device_id.ParseStringDeviceId(in.GetDeviceId()),
			ClientIp:     ip,
			TerminalType: protocol.PackTerminalType(authInfo.Platform, authInfo.OS, authInfo.AppID),
		},
	}
	checkResp, err := apply.RiskMngApiCli.Check(ctx, checkReq)
	if err != nil {
		// 系统错误，风控非关键路径，可忽略系统错误
		log.ErrorWithCtx(ctx, "riskCheckBeforeApplyRole risk-mng-api.Check failed, err:%v, req:%+v", err, checkReq)
		return "", "", 0
	}

	// 命中风控拦截
	if checkResp.ErrCode < 0 {
		// 建议打个 info 拦截日志，方便排查，风控拦截日志不会很多
		log.InfoWithCtx(ctx, "riskCheckBeforeApplyRole risk-mng-api.Check hit, req:%+v, resp:%+v", checkReq, checkResp)
		// 需要返回 ErrInfo 给客户端
		faceAuthContextJson = string(checkResp.ErrInfo)
		// 返回错误码给客户端，并设置 gRPC 错误码为 OK
		return faceAuthContextJson, checkResp.ErrMsg, checkResp.ErrCode
	}

	log.DebugWithCtx(ctx, "riskCheckBeforeApplyRole risk-mng-api.Check not hit, req:%+v, resp:%+v", checkReq, checkResp)
	// 无拦截
	return "", "", 0
}

// ApplyRiskCheck 风控前置检查
func (apply *ApplyRoleMgrImpl) ApplyRiskCheck(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	var in *esport_http.ApplyRiskCheckRequest
	err := json.Unmarshal(authInfo.Body, &in)
	if err != nil {
		e := protocol.ToServerError(err)
		log.Errorf("ApplyRiskCheck Failed to parse request  body(%s)  err(%v)", string(authInfo.Body), err)
		_ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
		return
	}

	log.Debugf("ApplyRiskCheck req %v", in)

	uid := authInfo.UserID
	c, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	ip := exnet.ClientIP(r)
	log.DebugWithCtx(c, "ApplyRiskCheck ip:%s", ip)

	svrInfo := genServiceInfoCtx(c, authInfo, in.GetDeviceId(), in.GetAppVersion(), in.GetClientType())
	ctx := grpc.WithServiceInfo(c, svrInfo)

	out := &esport_http.ApplyRiskCheckResponse{}

	// 电竞总开关检查
	//err = apply.mainSwitchCheck(ctx, uid)
	//if err != nil {
	//	e := protocol.ToServerError(err)
	//	log.Errorf("ApplyRiskCheck mainSwitchCheck Failed, uid: %d, err: %v", uid, err)
	//	_ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
	//	return
	//}

	// 获取用户身份证信息
	identityNum, err := apply.getUserIdentityNum(ctx, uid)
	if err != nil {
		e := protocol.ToServerError(err)
		log.Errorf("ApplyRiskCheck GetUserIdentityNum Failed, uid: %d, err: %v", uid, err)
		_ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
		return
	}

	// 检查用户当前是否有进行中的审核
	applyResp, err := apply.ESportRoleCli.CheckUserApplyESport(ctx, &esportRolePb.CheckUserApplyESportReq{Uid: uid, IdentifyNum: identityNum})
	if err != nil {
		e := protocol.ToServerError(err)
		log.Errorf("ApplyRiskCheck CheckUserApplyESport Failed, uid: %d, err: %v", uid, err)
		_ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
		return
	}

	if applyResp.GetApplyId() != 0 {

		if in.GetSource() == ApplySourcePersonal {
			if applyResp.GetApplyType() > uint32(esport_http.ESportApplyType_ESPORT_APPLY_TYPE_PERSONAL) {
				_ = web.ServeAPICodeJson(w, status.ErrEsportsRoleApplyAlready, "您的公会大神的申请正在审核中，当前不可申请", nil)
				return
			}
		} else {
			if applyResp.GetApplyType() == uint32(esport_http.ESportApplyType_ESPORT_APPLY_TYPE_PERSONAL) {
				_ = web.ServeAPICodeJson(w, status.ErrEsportsRoleApplyAlready, "您有个人大神的申请正在审核中，当前不可申请", nil)
				return
			}
		}

		out = &esport_http.ApplyRiskCheckResponse{
			ApplyStatus: applyStatusToPb(applyResp.GetApplyType(), applyResp.GetApplyStatus()),
			ApplyType:   applyResp.GetApplyType(),
			GuildId:     applyResp.GetGuildId(),
		}

		_ = web.ServeAPIJson(w, out)
		return
	}

	// 查询用户当前身份
	roleResp, err := apply.ESportRoleCli.GetUserESportRole(ctx, &esportRolePb.GetUserESportRoleReq{Uid: uid})
	if err != nil {
		e := protocol.ToServerError(err)
		log.Errorf("ApplyRiskCheck GetUserESportRole Failed, uid: %d, err: %v", uid, err)
		_ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
		return
	}

	out.UserCurRole = roleResp.GetEsportRole()
	if (out.UserCurRole == uint32(esportRolePb.ESportErType_ESPORT_TYPE_PERSONAL) && in.GetSource() == ApplySourcePersonal) ||
		(out.UserCurRole == uint32(esportRolePb.ESportErType_ESPORT_TYPE_GUILD) && in.GetSource() == ApplySourceGuild) {
		_ = web.ServeAPIJson(w, out)
		return
	}

	// 签约工会前置检查
	if in.GetSource() == ApplySourceGuild {
		err = apply.CheckCanApplySign(ctx, uid, in.GetGuildId(), identityNum)
		if err != nil {
			e := protocol.ToServerError(err)
			log.Errorf("ApplyRiskCheck CheckCanApplySign Failed, uid: %d, err: %v", uid, err)
			_ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
			return
		}
	}

	// 风控检查
	faceAuthContextJson, errMsg, errCode := apply.riskCheckBeforeApplyRole(ctx, ip, authInfo, in)
	if errCode != 0 {
		_ = web.ServeAPICodeJson(w, errCode, errMsg, &esport_http.ApplyRiskCheckResponse{
			FaceAuthContextJson: faceAuthContextJson,
			ApplyStatus:         applyStatusToPb(applyResp.GetApplyType(), applyResp.GetApplyStatus()),
			ApplyType:           in.GetSource(),
			GuildId:             applyResp.GetGuildId(),
			UserCurRole:         roleResp.GetEsportRole(),
		})
		return
	}

	log.Debugf("ApplyRiskCheck end uid:%d in %v resp %v", uid, in, out)
	_ = web.ServeAPIJson(w, out)
}

// CheckCanApplySign 签约工会前置检查
func (apply *ApplyRoleMgrImpl) CheckCanApplySign(ctx context.Context, uid, guildId uint32, idNum string) error {
	_, err := apply.AnchorContractCli.CheckCanApplySign(ctx, &anchorContractPb.CheckCanApplySignReq{
		ActorUid:     uid,
		GuildId:      guildId,
		IdentityType: uint32(esport_http.ESportApplyType_ESPORT_APPLY_TYPE_GUILD),
		IdentityNum:  idNum,
	})
	if err != nil {
		log.Errorf("ApplyRiskCheck CheckCanApplySign Failed, uid: %d, err: %v", uid, err)
		return err
	}

	return nil
}

// 获取用户身份证信息
func (apply *ApplyRoleMgrImpl) getUserIdentityNum(ctx context.Context, uid uint32) (string, error) {
	authResp, err := apply.TTCProxyClient.GetUserRealNameAuthInfoV2(ctx, uint64(uid), false, true)
	if err != nil {
		log.Errorf("ApplyESportRole GetUserRealNameAuthInfoV2 Failed ,uid:%d err: %v", uid, err)
		return "", err
	}

	identifyNum := authResp.GetIdcardInfo().GetIdentityNum()
	if apply.sc.TestMode {
		if identifyNum == "" {
			identifyNum = fmt.Sprintf("%06d19991001%04d", generateRandomNumber(6), generateRandomNumber(4))
		}
	} else {
		if identifyNum == "" || !authResp.GetIsAdult() {
			log.Errorf("ApplyESportRole GetUserRealNameAuthInfoV2 Is not Adult,uid:%d authResp:%v err: %v", uid, authResp, err)
			return "", protocol.NewExactServerError(codes.Ok, status.ErrEsportsRoleUserInApplyBlacklist, "完成实名认证且满18周岁才可申请成为大神哦")
		}
	}

	return identifyNum, nil
}

func (apply *ApplyRoleMgrImpl) GetESportGameList(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	var in *esport_http.GetESportGameListRequest
	err := json.Unmarshal(authInfo.Body, &in)
	if err != nil {
		e := protocol.ToServerError(err)
		log.Errorf("GetESportGameList Failed to parse request  body(%s)  err(%v)", string(authInfo.Body), err)
		_ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
		return
	}

	log.Debugf("GetESportGameList req %v", in)

	uid := authInfo.UserID
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	ip := exnet.ClientIP(r)
	log.DebugWithCtx(ctx, "GetESportGameList ip:%s", ip)
	out := &esport_http.GetESportGameListResponse{}

	// 获取用户身份证信息
	identityNum, err := apply.getUserIdentityNum(ctx, uid)
	if err != nil {
		e := protocol.ToServerError(err)
		log.Errorf("ApplyRiskCheck GetUserIdentityNum Failed, uid: %d, err: %v", uid, err)
		_ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
		return
	}

	// 检查用户当前是否有进行中的审核
	applyResp, err := apply.ESportRoleCli.CheckUserApplyESport(ctx, &esportRolePb.CheckUserApplyESportReq{Uid: uid, IdentifyNum: identityNum})
	if err != nil {
		e := protocol.ToServerError(err)
		log.Errorf("ApplyRiskCheck CheckUserApplyESport Failed, uid: %d, err: %v", uid, err)
		_ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
		return
	}

	if applyResp.GetApplyId() != 0 {

		if in.GetSource() == ApplySourcePersonal {
			if applyResp.GetApplyType() > uint32(esport_http.ESportApplyType_ESPORT_APPLY_TYPE_PERSONAL) {
				_ = web.ServeAPICodeJson(w, status.ErrEsportsRoleApplyAlready, "您的公会大神的申请正在审核中，当前不可申请", nil)
				return
			}
		} else {
			if applyResp.GetApplyType() == uint32(esport_http.ESportApplyType_ESPORT_APPLY_TYPE_PERSONAL) {
				_ = web.ServeAPICodeJson(w, status.ErrEsportsRoleApplyAlready, "您有个人大神的申请正在审核中，当前不可申请", nil)
				return
			}
		}

		out = &esport_http.GetESportGameListResponse{
			ApplyStatus: applyStatusToPb(applyResp.GetApplyType(), applyResp.GetApplyStatus()),
			ApplyType:   applyResp.GetApplyType(),
		}

		_ = web.ServeAPIJson(w, out)
		return
	}

	// 查询用户当前身份
	roleResp, err := apply.ESportRoleCli.GetUserESportRole(ctx, &esportRolePb.GetUserESportRoleReq{Uid: uid})
	if err != nil {
		e := protocol.ToServerError(err)
		log.Errorf("ApplyRiskCheck GetUserESportRole Failed, uid: %d, err: %v", uid, err)
		_ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
		return
	}

	out.UserCurRole = roleResp.EsportRole
	if out.UserCurRole == uint32(esportRolePb.ESportErType_ESPORT_TYPE_PERSONAL) && in.GetSource() == ApplySourcePersonal {
		// 申请个人指导且当前身份为个人指导
		_ = web.ServeAPIJson(w, out)
		return
	}

	if out.UserCurRole == uint32(esportRolePb.ESportErType_ESPORT_TYPE_GUILD) && in.GetSource() == ApplySourceGuild {
		// 申请公会指导且当前身份为公会指导
		_ = web.ServeAPIJson(w, out)
		return
	}

	// 获取当前游戏列表
	gameListResp, err := apply.ESportSkillCli.GetAllGameSimpleInfo(ctx, &esport_skill.GetAllGameSimpleInfoRequest{})
	if err != nil {
		log.Errorf("GetESportGameList GetAllGameSimpleInfo Failed, uid: %d, err: %v", uid, err)
		_ = web.ServeAPICodeJson(w, status.ErrSys, err.Error(), nil)
		return
	}

	if out.UserCurRole == uint32(esportRolePb.ESportErType_ESPORT_TYPE_UNSPECIFIED) {
		out.GameList = toGameListHttpPb(gameListResp.GetGameList())
	} else {
		// 获取用户当前的技能列表
		skillResp, err := apply.ESportSkillCli.GetUserCurrentSkill(ctx, &esport_skill.GetUserCurrentSkillRequest{
			Uid: uid,
		})
		if err != nil {
			log.Errorf("GetESportGameList GetUserCurrentSkill Failed ,uid:%d err: %v", uid, err)
			_ = web.ServeAPICodeJson(w, status.ErrSys, err.Error(), nil)
			return
		}
		out.GameList = skillRespToGameListPb(skillResp.GetSkill(), gameListResp.GetGameList())
	}
	out.GameTypeList = getGameTypeConf(apply.bc)

	log.Debugf("GetESportGameList end uid:%d in %v resp %v", uid, in, out)
	_ = web.ServeAPIJson(w, out)
}

func skillRespToGameListPb(curSkill []*esport_skill.UserSkillInfo, allSimpleInfo []*esport_skill.SimpleGameInfo) []*esport_http.ESportGameSimpleInfo {
	pbGameList := make([]*esport_http.ESportGameSimpleInfo, 0, len(curSkill))
	gameMap := make(map[uint32]*esport_skill.SimpleGameInfo, len(allSimpleInfo))
	for _, v := range allSimpleInfo {
		gameMap[v.GetGameId()] = v
	}

	for _, v := range curSkill {
		pbGameList = append(pbGameList, &esport_http.ESportGameSimpleInfo{
			GameId:   v.GetGameId(),
			Name:     v.GetGameName(),
			Icon:     gameMap[v.GetGameId()].GetGameIcon(),
			GameType: uint32(gameMap[v.GetGameId()].GetGameType()),
			Rank:     uint32(gameMap[v.GetGameId()].GetRank() * 100),
		})
	}
	return pbGameList
}

func toGameListHttpPb(gameList []*esport_skill.SimpleGameInfo) []*esport_http.ESportGameSimpleInfo {
	pbGameList := make([]*esport_http.ESportGameSimpleInfo, 0, len(gameList))
	for _, v := range gameList {

		pbGameList = append(pbGameList, &esport_http.ESportGameSimpleInfo{
			GameId:   v.GetGameId(),
			Name:     v.GetGameName(),
			Icon:     v.GetGameIcon(),
			GameType: uint32(v.GetGameType()),
			Rank:     uint32(int64(v.GetRank() * 100)),
		})
	}
	return pbGameList
}

func skillConfToPb(skillConfList []*esport_skill.GameInformation) []*esport_http.GameInformation {
	skillConfPbList := make([]*esport_http.GameInformation, 0, len(skillConfList))
	for _, v := range skillConfList {
		skillConfPbList = append(skillConfPbList, &esport_http.GameInformation{
			InformationType: uint32(v.GetInformationType()),
			SectionName:     v.GetSectionName(),
			ItemList:        v.GetItemList(),
			SelectType:      uint32(v.GetSelectType()),
			SectionId:       v.GetSectionId(),
		})
	}
	return skillConfPbList
}

// GetSkillConfByGameId 获取指定游戏的技能配置
func (apply *ApplyRoleMgrImpl) GetSkillConfByGameId(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	var in *esport_http.GetESportGameSkillConfRequest
	err := json.Unmarshal(authInfo.Body, &in)
	if err != nil {
		e := protocol.ToServerError(err)
		log.Errorf("GetESportGameList Failed to parse request  body(%s)  err(%v)", string(authInfo.Body), err)
		_ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
		return
	}

	log.Debugf("GetESportGameList req %v", in)

	uid := authInfo.UserID
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	out := &esport_http.GetESportGameSkillConfResponse{}

	log.Infof("GetSkillConfByGameId req %v", in)
	confResp, err := apply.ESportSkillCli.GetGameDetailById(ctx, &esport_skill.GetGameDetailByIdRequest{
		GameId: in.GetGameId(),
	})
	if err != nil {
		log.Errorf("GetSkillConfByGameId GetGameDetailById Failed ,uid:%d gameId:%d err: %v", uid, in.GetGameId(), err)
		_ = web.ServeAPICodeJson(w, status.ErrSys, err.Error(), nil)
		return
	}
	skillConf := confResp.GetConfig()

	out.GameId = in.GetGameId()

	out.SkillConf = &esport_http.GameSkillConf{
		GameId:         skillConf.GetGameId(),
		Name:           skillConf.GetName(),
		GameType:       uint32(skillConf.GetGameType()),
		GameIcon:       skillConf.GetGameIcon(),
		GameBackground: skillConf.GetGameBackground(),
		GameColor:      skillConf.GetGameColor(),
		SkillEvidence:  skillConf.GetSkillEvidence(),
		SkillDesc:      skillConf.GetSkillDesc(),
		GameInfoList:   skillConfToPb(skillConf.GetGameInformationList()),
	}

	out.IntroConf = &esport_http.SelfIntroConf{
		TextMaxCnt:  apply.bc.Get().ESportGameSkillConfig.SelfIntroConf.TextMaxCnt,
		AudioMaxSec: apply.bc.Get().ESportGameSkillConfig.SelfIntroConf.AudioMaxSec,
		AudioReq:    apply.bc.Get().ESportGameSkillConfig.AudioIntroNeeded,
	}
	out.GuaranteeDesc = apply.bc.GetGuaranteeWinDesc()

	log.Debugf("GetSkillConfByGameId end in %v resp %v", in, out)
	_ = web.ServeAPIJson(w, out)
}

// ApplyESportRole 提交身份申请
func (apply *ApplyRoleMgrImpl) ApplyESportRole(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	var in *esport_http.ApplyESportRoleReq
	err := json.Unmarshal(authInfo.Body, &in)
	if err != nil {
		e := protocol.ToServerError(err)
		log.Errorf("ApplyESportRole Failed to parse request  body(%s)  err(%v)", string(authInfo.Body), err)
		_ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
		return
	}

	log.Infof("ApplyESportRole req %v", in)

	uid := authInfo.UserID
	now := time.Now()
	c, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	if !applyTypeCheck(in.GetApplyType()) {
		log.Errorf("ApplyESportRole applyTypeCheck Failed ,uid:%d applyType:%d err: %v", uid, in.GetApplyType(), err)
		_ = web.ServeAPICodeJson(w, status.ErrRequestParamInvalid, status.MessageFromCode(status.ErrRequestParamInvalid), nil)
		return
	}

	svrInfo := genServiceInfoCtx(c, authInfo, in.GetDeviceId(), in.GetAppVersion(), in.GetClientType())
	ctx := grpc.WithServiceInfo(c, svrInfo)

	// 电竞总开关检查
	//err = apply.mainSwitchCheck(ctx, uid)
	//if err != nil {
	//	e := protocol.ToServerError(err)
	//	log.Errorf("ApplyESportRole mainSwitchCheck Failed, uid: %d, err: %v", uid, err)
	//	_ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
	//	return
	//}

	// 获取用户身份证信息
	identityNum, err := apply.getUserIdentityNum(ctx, uid)
	if err != nil {
		e := protocol.ToServerError(err)
		log.Errorf("ApplyESportRole GetUserIdentityNum Failed, uid: %d, err: %v", uid, err)
		_ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
		return
	}

	out := &esport_http.ApplyESportRoleResp{}
	auditToken := fmt.Sprintf("%d_%d", uid, time.Now().Unix())
	log.Infof("ApplyESportRole auditToken:%s", auditToken)

	// 签约工会前置检查
	if in.GetApplyType() > ApplySourcePersonal {
		err = apply.CheckCanApplySign(ctx, uid, in.GetGuildId(), identityNum)
		if err != nil {
			e := protocol.ToServerError(err)
			log.Errorf("ApplyRiskCheck CheckCanApplySign Failed, uid: %d, err: %v", uid, err)
			_ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
			return
		}
	}

	// 提交技能信息
	err = apply.commitApplySkillInfoList(ctx, uid, auditToken, in)
	if err != nil {
		e := protocol.ToServerError(err)
		log.Errorf("ApplyESportRole commitApplySkillInfoList Failed, uid: %d, err: %v", uid, err)
		_ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
		return
	}

	guildSignTime, guildExpireTime := uint32(0), uint32(0)
	if in.GetApplyType() != uint32(esportRolePb.ApplyESportRequset_ESPORT_APPLY_TYPE_PERSONAL) {
		// 如果是签约工会的申请，查询对应的工会签约时间信息
		contractInfoResp, err := apply.AnchorContractCli.GetUserContractCacheInfo(ctx, &anchorcontract_go.GetUserContractCacheInfoReq{Uid: uid})
		if err != nil {
			log.Errorf("ApplyESportRole GetUserContractCacheInfo Failed ,uid:%d err: %v", uid, err)
			_ = web.ServeAPICodeJson(w, status.ErrSys, err.Error(), nil)
			return
		}
		if contractInfoResp.GetContract().GetSignTime() > 0 {
			guildSignTime = contractInfoResp.GetContract().GetSignTime()
		}
		if contractInfoResp.GetContract().GetExpireTime() > 0 {
			guildExpireTime = contractInfoResp.GetContract().GetExpireTime()
		}
	}

	// 提交身份申请
	_, err = apply.ESportRoleCli.ApplyESportRequest(ctx, &esportRolePb.ApplyESportRequset{
		AuditToken:     auditToken,
		Uid:            uid,
		ApplyType:      in.GetApplyType(),
		IdentifyId:     identityNum,
		ApplyTime:      uint32(now.Unix()),
		GuildId:        in.GetGuildId(),
		SignDuration:   in.GetSignDuration(),
		SignTime:       guildSignTime,
		SignExpireTime: guildExpireTime,
	})
	if err != nil {
		e := protocol.ToServerError(err)
		log.Errorf("ApplyESportRole ApplyESportRequest Failed ,uid:%d err: %v", uid, err)
		_ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
		return
	}

	log.InfoWithCtx(ctx, "ApplyESportRole ApplyESportRequest Success ,uid:%d in:%+v", uid, in)
	_ = web.ServeAPIJson(w, out)
}

// 提交身份申请技能信息
func (apply *ApplyRoleMgrImpl) commitApplySkillInfoList(ctx context.Context, uid uint32, auditToken string, in *esport_http.ApplyESportRoleReq) error {

	riskCheck := false
	skillList := make([]*esport_skill.UserSkillInfo, 0)
	if in.GetApplyType() != uint32(esportRolePb.ApplyESportRequset_ESPORT_APPLY_TYPE_PERSONAL_TO_GUILD) {
		if in.GetUserSkill() == nil || in.GetUserSkill().GameId == 0 {
			log.ErrorWithCtx(ctx, "ApplyESportRole applyTypeCheck Failed ,uid:%d in:%+v 未填写游戏技能资料", uid, in)
			return protocol.NewExactServerError(codes.Ok, status.ErrRequestParamInvalid, "请填写游戏技能资料")
		}
		riskCheck = true
		skillList = append(skillList, skillInfoToSkillPb([]*esport_http.UserSkillInfo{in.GetUserSkill()})...)

	} else {
		// 获取用户当前的技能信息提交重新提交
		skillResp, err := apply.ESportSkillCli.GetUserCurrentSkill(ctx, &esport_skill.GetUserCurrentSkillRequest{
			Uid:           uid,
			WithUrlPrefix: false,
		})
		if err != nil {
			log.Errorf("ApplyESportRole GetUserCurrentSkill Failed ,uid:%d err: %v", uid, err)
			return err
		}
		if len(skillResp.GetSkill()) == 0 {
			log.ErrorWithCtx(ctx, "ApplyESportRole applyTypeCheck Failed ,uid:%d in:%+v 用户当前没有技能", uid, in)
			return protocol.NewExactServerError(codes.Ok, status.ErrRequestParamInvalid, "当前没有技能资料，请先去大神页申请技能成功后，再签约工会")
		}
		skillList = skillResp.GetSkill()
	}

	req := &esport_skill.AddUserAuditSkillRequest{
		Uid:         uid,
		AuditToken:  auditToken,
		AuditSource: uint32(esport_skill.AuditSource_AUDIT_SOURCE_NEW_ROLE),
		Skill:       skillList,
		//GuildId:     0,
		AuditType: uint32(esportRolePb.ApplyESportAuditType_ESPORT_AUDIT_TYPE_INIT),
	}

	if !riskCheck {
		// 无需审核
		req.AuditType = uint32(esportRolePb.ApplyESportAuditType_ESPORT_AUDIT_TYPE_RISK_PASS)
	}

	_, err := apply.ESportSkillCli.AddUserAuditSkill(ctx, req)
	if err != nil {
		log.Errorf("commitApplySkillInfoList AddUserAuditSkill Failed ,uid:%d req:%+v err: %v", uid, req, err)
		return err
	}

	return nil
}

// 电竞总开关检查
func (apply *ApplyRoleMgrImpl) mainSwitchCheck(ctx context.Context, uid uint32) error {
	resp, err := apply.ESportSkillCli.GetSwitch(ctx, &esport_skill.GetSwitchRequest{
		Uid: uid,
	})
	if err != nil {
		log.Errorf("ApplyESportRole GetSwitch Failed ,uid:%d err: %v", uid, err)
		return err
	}

	if resp.GetSwitchStatus().GetMainSwitchStatus() == esport_skill.EsportSwitchStatus_SWITCH_STATUS_OFF {
		return protocol.NewExactServerError(codes.Ok, status.ErrEsportsOrderCommonError, "当前不可申请～")
	}
	return nil
}

func applyTypeCheck(applyType uint32) bool {
	if applyType == uint32(esportRolePb.ApplyESportRequset_ESPORT_APPLY_TYPE_PERSONAL) ||
		applyType == uint32(esportRolePb.ApplyESportRequset_ESPORT_APPLY_TYPE_GUILD) ||
		applyType == uint32(esportRolePb.ApplyESportRequset_ESPORT_APPLY_TYPE_PERSONAL_TO_GUILD) {
		return true
	} else {
		return false
	}
}
func generateRandomNumber(digits int) *big.Int {
	maxInt := big.NewInt(1)
	maxInt.Exp(big.NewInt(10), big.NewInt(int64(digits)), nil)
	n, err := rand.Int(rand.Reader, maxInt)
	if err != nil {
		return big.NewInt(int64(digits))
	}
	return n
}

func sectionListToSkillPb(sectionList []*esport_http.SectionInfo) []*esport_skill.SectionInfo {
	sectionListPb := make([]*esport_skill.SectionInfo, 0, len(sectionList))
	for _, v := range sectionList {
		sectionListPb = append(sectionListPb, &esport_skill.SectionInfo{
			SectionName: v.GetSectionName(),
			ItemList:    v.GetItemList(),
		})
	}
	return sectionListPb
}

func skillInfoToSkillPb(skillList []*esport_http.UserSkillInfo) []*esport_skill.UserSkillInfo {
	skillPbList := make([]*esport_skill.UserSkillInfo, 0, len(skillList))
	for _, v := range skillList {
		skillPbList = append(skillPbList, &esport_skill.UserSkillInfo{
			GameId:        v.GetGameId(),
			GameName:      v.GetGameName(),
			SkillEvidence: v.GetSkillEvidence(),
			Audio:         v.GetAudio(),
			AudioDuration: v.GetAudioDuration(),
			SectionList:   sectionListToSkillPb(v.GetSectionList()),
			TextDesc:      v.GetTextDesc(),
		})
	}
	return skillPbList
}
