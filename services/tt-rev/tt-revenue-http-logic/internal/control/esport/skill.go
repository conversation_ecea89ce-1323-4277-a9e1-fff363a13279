package esport

import (
    "context"
    "encoding/json"
    "fmt"
    "github.com/jinzhu/copier"
    protoGrpc "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/services/esport_grab_order"
    "google.golang.org/grpc/codes"
    "io"
    "sort"
    "strconv"
    "sync"

    "golang.52tt.com/clients/account"
    anchorcontractGo "golang.52tt.com/clients/anchorcontract-go"
    "golang.52tt.com/protocol/services/esport_role"

    tyrHttp "gitlab.ttyuyin.com/avengers/tyr/core/service/http"
    "golang.52tt.com/clients/guild"
    headdynamicimage "golang.52tt.com/clients/head-dynamic-image-logic"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/pkg/web"
    "golang.52tt.com/protocol/common/status"
    esportSkill "golang.52tt.com/protocol/services/esport-skill"
    "golang.52tt.com/protocol/services/esport_hall"

    "net/http"
    "time"

    pb "golang.52tt.com/protocol/services/esport-skill"
    pbRole "golang.52tt.com/protocol/services/esport_role"
    "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/common"
    "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/conf"
    esportHttp "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/models/esport"
)

const (
    beanMinuteUnit       = "豆/30分钟"
    beanRoundUnit        = "豆/局"
    guaranteeSectionName = "包赢承诺"
)

type SkillMgr struct {
    ESportSkillCli      *esportSkill.Client
    ESportRoleCli       esport_role.ESportRoleClient
    ESportHallCli       *esport_hall.Client
    ESportGrabOrderCli  *esport_grab_order.Client
    GuildCli            guild.IClient
    AccountCli          account.IClient
    ContractCli         anchorcontractGo.IClient
    bc                  conf.BusinessConfApi
    headDynamicImageCli headdynamicimage.IClient
    httpCli             *http.Client
}

func NewEsportSkillMgr(ctx context.Context, bc conf.BusinessConfApi) *SkillMgr {
    eSportSkillCli, _ := esportSkill.NewClient(ctx)
    eSportRoleCli, _ := esport_role.NewClient(ctx)
    eSportHallCli, _ := esport_hall.NewClient(ctx)
    eSportGrabOrderCli, _ := esport_grab_order.NewClient(ctx)
    guildCli := guild.NewIClient()
    accountCli := account.NewIClient()
    contractCli := anchorcontractGo.NewIClient()
    headDynamicImageCli := headdynamicimage.NewIClient()

    transport := http.DefaultTransport.(*http.Transport)
    transport.MaxIdleConnsPerHost = 100

    httpCli := &http.Client{
        Transport: transport,
        Timeout:   time.Second * 5,
    }
    return &SkillMgr{
        ESportSkillCli:      eSportSkillCli,
        ESportRoleCli:       eSportRoleCli,
        ESportHallCli:       eSportHallCli,
        ESportGrabOrderCli:  eSportGrabOrderCli,
        GuildCli:            guildCli,
        AccountCli:          accountCli,
        ContractCli:         contractCli,
        bc:                  bc,
        headDynamicImageCli: headDynamicImageCli,
        httpCli:             httpCli,
    }
}

func (mgr *SkillMgr) Register(cfg *conf.ServiceConfig, router *tyrHttp.Router) {
    child := router.Child("/esport/skill")
    child.POST("/batch_get_audit_skill", common.AuthHandleInterceptor(cfg, mgr.BatchGetAuditSkill))
    child.POST("/set_user_skill_audit_type", common.AuthHandleInterceptor(cfg, mgr.SetUserSkillAuditType))
    child.POST("/get_esport_master_info", common.HandleWrapperG(cfg, mgr.GetESportMasterInfo))
    child.POST("/del_user_skill", common.AuthHandleInterceptor(cfg, mgr.DelUserSkill))
    child.POST("/modify_user_skill", common.AuthHandleInterceptor(cfg, mgr.ModifyUserSkill))
    child.POST("/add_user_skill", common.AuthHandleInterceptor(cfg, mgr.AddUserSkill))
    child.POST("/get_user_skill_by_game_id", common.AuthHandleInterceptor(cfg, mgr.GetUserSkillByGameId))
    child.POST("/get_esport_game_list_with_audit", common.AuthHandleInterceptor(cfg, mgr.GetEsportGameListWithAudit))
    child.POST("set_guarantee_win_status", common.AuthHandleInterceptor(cfg, mgr.SetGuaranteeWinStatus))

    child.POST("/get_top_game_list", common.HandleWrapperG(cfg, mgr.GetTopGameList))
}

func (mgr *SkillMgr) GetUserSkillByGameId(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
    ctx, cancel := context.WithTimeout(r.Context(), 5*time.Second)
    defer cancel()

    var req esportHttp.GetUserSkillByGameIdRequest
    err := json.Unmarshal(authInfo.Body, &req)
    if err != nil {
        e := protocol.ToServerError(err)
        log.ErrorWithCtx(ctx, "GetUserSkillByGameId Failed to parse request  body(%s)  err(%v)", string(authInfo.Body), err)
        _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
        return
    }

    uid := authInfo.UserID

    resp, err := mgr.ESportSkillCli.GetUserSkillByGameId(ctx, &esportSkill.GetUserSkillByGameIdRequest{
        Uid:    uid,
        GameId: req.GetGameId(),
    })
    if err != nil {
        e := protocol.ToServerError(err)
        log.ErrorWithCtx(ctx, "GetUserSkillByGameId err body(%s)  err(%v)", string(authInfo.Body), err)
        _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
        return
    }

    // 判断是否能显示包赢
    hasGuaranteeWinPermission, err := mgr.checkSkillLabel(ctx, uid, req.GetGameId())
    if err != nil {
        e := protocol.ToServerError(err)
        log.ErrorWithCtx(ctx, "GetUserSkillByGameId checkSkillLabel err body(%s)  err(%v)", string(authInfo.Body), err)
        _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
        return
    }

    out := &esportHttp.GetUserSkillByGameIdResponse{
        TargetUid:                 uid,
        GameId:                    req.GetGameId(),
        AuditSkill:                make([]*esportHttp.UserSkillInfo, len(resp.GetAuditSkill())),
        CurrentSkill:              &esportHttp.UserSkillInfo{},
        AuditType:                 resp.AuditType,
        HasGuaranteeWinPermission: hasGuaranteeWinPermission,
    }
    copier.Copy(&out.AuditSkill, &resp.AuditSkill)
    copier.Copy(out.CurrentSkill, resp.CurrentSkill)
    _ = web.ServeAPICodeJson(w, status.Success, "", out)
}

func (mgr *SkillMgr) checkSkillLabel(ctx context.Context, coachUid uint32, gameId uint32) (bool, error) {
    coachLabelResp, err := mgr.ESportSkillCli.BatchGetCoachLabelsForGame(ctx, &esportSkill.BatchGetCoachLabelsForGameRequest{
        GameId:   gameId,
        CoachIds: []uint32{coachUid},
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetCoachLabelsForGame err: %v", err)
        return false, err
    }

    if len(coachLabelResp.GetLabelList()) > 0 {
        coachLabelInfo := coachLabelResp.GetLabelList()[0]
        skillLabelOriginList, ok := coachLabelInfo.GetLabelMap()[uint32(esportSkill.LabelType_LABEL_TYPE_SKILL)]
        if ok && len(skillLabelOriginList.GetLabelList()) > 0 {
            return true, nil
        }
    }

    return false, nil
}

func (mgr *SkillMgr) GetEsportGameListWithAudit(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
    ctx, cancel := context.WithTimeout(r.Context(), 5*time.Second)
    defer cancel()

    var req esportHttp.GetEsportGameListWithAuditRequest
    err := json.Unmarshal(authInfo.Body, &req)
    if err != nil {
        e := protocol.ToServerError(err)
        log.ErrorWithCtx(ctx, "GetEsportGameListWithAudit Failed to parse request  body(%s)  err(%v)", string(authInfo.Body), err)
        _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
        return
    }
    uid := authInfo.UserID
    skillRes, err := mgr.ESportSkillCli.GetAllGameSimpleInfo(ctx, &esportSkill.GetAllGameSimpleInfoRequest{})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetEsportGameListWithAudit failed, uid:%d, err:%v", uid, err)
        e := protocol.ToServerError(err)
        _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
        return
    }
    auditRes, err := mgr.ESportSkillCli.GetUserSkillStatus(ctx, &esportSkill.GetUserSkillStatusRequest{
        Uid: uid,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetEsportGameListWithAudit failed to BatchGetAuditSkill, uid:%d, err:%v", uid, err)
        e := protocol.ToServerError(err)
        _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
        return
    }
    auditMap := auditRes.GetStatusMap()

    freezeRes, err := mgr.ESportSkillCli.GetUserSkillFreezeStatus(ctx, &esportSkill.GetUserSkillFreezeStatusRequest{Uid: uid})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetEsportGameListWithAudit failed to GetUserSkillFreezeStatus, uid:%d, err:%v", uid, err)
        e := protocol.ToServerError(err)
        _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
        return
    }
    freezeMap := freezeRes.GetGameStatusMap()

    out := &esportHttp.GetEsportGameListWithAuditResponse{}
    for _, item := range skillRes.GetGameList() {
        out.GameList = append(out.GameList, &esportHttp.ESportGameSimpleInfo{
            GameId:       item.GetGameId(),
            Name:         item.GetGameName(),
            Icon:         item.GetGameIcon(),
            GameType:     uint32(item.GetGameType()),
            Rank:         uint32(item.GetRank() * 100),
            AuditType:    auditMap[item.GameId],
            FreezeType:   esportHttp.FreezeType(freezeMap[item.GameId].GetFreezeType()),
            FreezeStopTs: freezeMap[item.GameId].GetFreezeStopTs(),
        })
    }
    out.GameTypeList = getGameTypeConf(mgr.bc)
    _ = web.ServeAPICodeJson(w, status.Success, "", out)
}

func (mgr *SkillMgr) getUserGuildId(ctx context.Context, uid uint32) (uint32, error) {
    contractInfo, err := mgr.ContractCli.GetUserContractCacheInfo(ctx, uid, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "anchor_contract_go.GetUserContractCacheInfo failed, uid:%d, err:%v", uid, err)
        return 0, err
    }
    var guildId uint32
    for _, v := range contractInfo.GetAnchorIdentityList() {
        if v == 2 {
            guildId = contractInfo.GetContract().GetGuildId()
        }
    }

    return guildId, nil
}
func (mgr *SkillMgr) AddUserSkill(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
    ctx, cancel := context.WithTimeout(r.Context(), 5*time.Second)
    defer cancel()

    var req esportHttp.AddUserSkillRequest
    err := json.Unmarshal(authInfo.Body, &req)
    if err != nil {
        e := protocol.ToServerError(err)
        log.ErrorWithCtx(ctx, "AddUserSkill Failed to parse request  body(%s)  err(%v)", string(authInfo.Body), err)
        _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
        return
    }
    uid := authInfo.UserID

    roleInfo, err := mgr.ESportRoleCli.GetUserESportRole(ctx, &pbRole.GetUserESportRoleReq{Uid: uid})
    if err != nil {
        return
    }

    if roleInfo.GetEsportRole() == uint32(pbRole.ESportErType_ESPORT_TYPE_UNSPECIFIED) {
        _ = web.ServeAPICodeJson(w, int32(status.ErrEsportsSkillCommonErr), "非电竞指导不能添加技能", nil)
        return
    }

    skill := req.GetSkill()
    sectionList := make([]*esportSkill.SectionInfo, 0)
    for _, v := range skill.GetSectionList() {
        section := &esportSkill.SectionInfo{
            SectionName: v.SectionName,
            ItemList:    v.GetItemList(),
            SectionId:   v.GetSectionId(),
        }
        sectionList = append(sectionList, section)
    }

    guildId, _ := mgr.getUserGuildId(ctx, uid)

    modReq := &esportSkill.AddUserAuditSkillRequest{
        Uid:         uid,
        GuildId:     guildId,
        AuditSource: uint32(esportSkill.AuditSource_AUDIT_SOURCE_NEW_SKILL),
        AuditType:   uint32(pbRole.ApplyESportAuditType_ESPORT_AUDIT_TYPE_INIT),
        AuditToken:  fmt.Sprintf("ADD_%d_%d", uid, time.Now().Unix()),
        Skill: []*esportSkill.UserSkillInfo{{
            GameId:        skill.GetGameId(),
            GameName:      skill.GetGameName(),
            GameRank:      skill.GetGameRank(),
            SkillEvidence: skill.GetSkillEvidence(),
            Audio:         skill.GetAudio(),
            AudioDuration: skill.GetAudioDuration(),
            SectionList:   sectionList,
            TextDesc:      skill.GetTextDesc(),
        },
        },
    }
    _, err = mgr.ESportSkillCli.AddUserAuditSkill(ctx, modReq)
    if err != nil {
        e := protocol.ToServerError(err)
        log.ErrorWithCtx(ctx, "AddUserSkill err body(%s)  err(%v)", string(authInfo.Body), err)
        _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
        return
    }

    out := &esportSkill.AddUserAuditSkillResponse{}
    log.InfoWithCtx(ctx, "AddUserSkill req:%v, out: %+v", req, out)
    _ = web.ServeAPICodeJson(w, status.Success, "", out)
}

func (mgr *SkillMgr) ModifyUserSkill(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
    ctx, cancel := context.WithTimeout(r.Context(), 5*time.Second)
    defer cancel()

    var req esportHttp.ModifyUserSkillRequest
    err := json.Unmarshal(authInfo.Body, &req)
    if err != nil {
        e := protocol.ToServerError(err)
        log.ErrorWithCtx(ctx, "ModifyUserSkill Failed to parse request body(%s) err(%v)", string(authInfo.Body), err)
        _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
        return
    }

    uid := authInfo.UserID
    roleInfo, err := mgr.ESportRoleCli.GetUserESportRole(ctx, &pbRole.GetUserESportRoleReq{Uid: uid})
    if err != nil {
        return
    }

    if roleInfo.GetEsportRole() == uint32(pbRole.ESportErType_ESPORT_TYPE_UNSPECIFIED) {
        _ = web.ServeAPICodeJson(w, int32(status.ErrEsportsSkillCommonErr), "非电竞指导不能修改技能", nil)
        return
    }

    guildId, _ := mgr.getUserGuildId(ctx, uid)

    sectionList := make([]*esportSkill.SectionInfo, 0)
    for _, v := range req.GetSkill().GetSectionList() {
        section := &esportSkill.SectionInfo{
            SectionName: v.SectionName,
            ItemList:    v.GetItemList(),
            SectionId:   v.GetSectionId(),
        }
        sectionList = append(sectionList, section)
    }

    modReq := &esportSkill.ModifyUserSkillRequest{
        Uid:     uid,
        GuildId: guildId,
        Skill: &esportSkill.UserSkillInfo{
            GameId:        req.GetSkill().GetGameId(),
            GameName:      req.GetSkill().GetGameName(),
            SkillEvidence: req.GetSkill().GetSkillEvidence(),
            Audio:         req.GetSkill().GetAudio(),
            AudioDuration: req.GetSkill().GetAudioDuration(),
            SectionList:   sectionList,
            TextDesc:      req.GetSkill().GetTextDesc(),
        },
    }
    log.InfoWithCtx(ctx, "ModifyUserSkill modReq:%+v  err(%v)", modReq, err)
    _, err = mgr.ESportSkillCli.ModifyUserSkill(ctx, modReq)
    if err != nil {
        e := protocol.ToServerError(err)
        log.ErrorWithCtx(ctx, "ModifyUserSkill err, uid:%d, modReq:%+v  err(%v)", uid, modReq, err)
        _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
        return
    }

    out := &esportSkill.ModifyUserSkillResponse{}
    log.InfoWithCtx(ctx, "ModifyUserSkill req:%v, uid：%d, out: %+v", req, uid, out)
    _ = web.ServeAPICodeJson(w, status.Success, "", out)
}

func (mgr *SkillMgr) DelUserSkill(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
    ctx, cancel := context.WithTimeout(r.Context(), 5*time.Second)
    defer cancel()

    var req esportHttp.DelUserSkillRequest
    err := json.Unmarshal(authInfo.Body, &req)
    if err != nil {
        e := protocol.ToServerError(err)
        log.ErrorWithCtx(ctx, "DelUserSkill Failed to parse request  body(%s)  err(%v)", string(authInfo.Body), err)
        _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
        return
    }

    uid := authInfo.UserID

    _, err = mgr.ESportSkillCli.DelUserSkill(ctx, &esportSkill.DelUserSkillRequest{
        Uid:    uid,
        GameId: req.GetGameId(),
    })
    if err != nil {
        e := protocol.ToServerError(err)
        log.ErrorWithCtx(ctx, "DelUserSkill err, uid:%d, body(%s),err(%v)", uid, string(authInfo.Body), err)
        _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
        return
    }

    skillId := make([]uint32, 0)
    if req.GetGameId() != 0 {
        skillId = append(skillId, req.GetGameId())
    }
    // 删除接单那边的技能
    _, err = mgr.ESportHallCli.DelSkillProduct(ctx, &esport_hall.DelSkillProductRequest{
        Uid:     []uint32{uid},
        SkillId: skillId,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "DelSkillProduct err: %v", err)
    }

    out := &esportSkill.DelUserSkillResponse{}
    log.InfoWithCtx(ctx, "DelUserSkill req:%v, uid:%d, out: %+v", req, uid, out)
    _ = web.ServeAPICodeJson(w, status.Success, "", out)
}

func (mgr *SkillMgr) GetESportMasterInfo(c context.Context, req *esportHttp.GetESportMasterInfoRequest) (*esportHttp.GetESportMasterInfoResponse, error) {
    ts := time.Now()
    ctx, cancel := protoGrpc.NewContextWithInfoTimeout(c, 10*time.Second)
    defer cancel()

    out := &esportHttp.GetESportMasterInfoResponse{}
    authInfo, ok := protoGrpc.ServiceInfoFromContext(c)
    if !ok {
        log.ErrorWithCtx(ctx, "GetESportMasterInfo Failed to get authInfo from context")
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }

    uid := authInfo.UserID
    var guildName string
    roleInfo, err := mgr.ESportRoleCli.GetUserESportRole(ctx, &pbRole.GetUserESportRoleReq{Uid: uid})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetESportMasterInfo Failed to get roleInfo from svr, uid:%d, err:%v", uid, err)
        return out, err
    }

    if roleInfo.GetGuildId() > 0 {
        guildInfo, err := mgr.GuildCli.GetGuild(ctx, roleInfo.GetGuildId())
        if err == nil {
            guildName = guildInfo.GetName()
        }
    }

    tradeStat, err := mgr.GetTradeDataStat(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "getTradeDataSta uid: %d, err: %v", uid, err)
    }

    userInfo, err := mgr.AccountCli.GetUser(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUser err: %v", err)
    }

    coachLabel := make([]string, 0, 8)
    oldCoachLabelResp, err := mgr.ESportRoleCli.BatchGetCoachLabel(ctx, &esport_role.BatchGetCoachLabelRequest{
        UidList: []uint32{uid},
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetESportMasterInfo.BatchGetCoachLabel, uid: %d, err: %v", uid, err)
    }
    if oldLabel, ok := oldCoachLabelResp.GetCoachLabelMap()[uid]; ok {
        coachLabel = append(coachLabel, oldLabel.GetSourceUrl())
    }

    // 大神标识
    coachLabelResp, err := mgr.ESportSkillCli.BatchGetCoachLabelsForGame(ctx, &esportSkill.BatchGetCoachLabelsForGameRequest{
        CoachIds: []uint32{authInfo.UserID},
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetESportMasterInfo failed to BatchGetCoachLabelsForGame, uid:%d, err:%v", uid, err)
    }

    if len(coachLabelResp.GetLabelList()) > 0 {
        for _, item := range coachLabelResp.GetLabelList()[0].GetLabelMap()[uint32(esportSkill.LabelType_LABEL_TYPE_COACH)].GetLabelList() {
            coachLabel = append(coachLabel, item.GetLabelImage())
        }
    }

    switchRsp, err := mgr.ESportGrabOrderCli.GetSwitch(ctx, &esport_grab_order.GetSwitchRequest{})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetESportMasterInfo failed to GetSwitch, uid:%d, err:%v", uid, err)
    }

    out = &esportHttp.GetESportMasterInfoResponse{
        TargetUid:           uid,
        Account:             userInfo.GetUsername(),
        NickName:            userInfo.GetNickname(),
        Ttid:                userInfo.GetAlias(),
        EsportRole:          roleInfo.GetEsportRole(),
        GuildId:             roleInfo.GetGuildId(),
        GuildName:           guildName,
        Divide:              mgr.bc.Get().ESportGameSkillConfig.GuildDivide,
        StatList:            tradeStat,
        HeadDyMd5:           mgr.getDynamicHeadMd5(ctx, uid),
        CoachLabelList:      coachLabel,
        GuaranteeDesc:       mgr.bc.GetGuaranteeWinDesc(),
        ShowGrabOrderCenter: switchRsp.GetShowGrabOrderCenter(),
    }
    labelResp, err := mgr.ESportRoleCli.BatchGetCoachLabel(ctx, &esport_role.BatchGetCoachLabelRequest{
        UidList: []uint32{uid},
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetESportMasterInfo failed to BatchGetCoachLabel, uid:%d, err:%v", uid, err)
    }
    if labelResp.GetCoachLabelMap() != nil {
        if label, ok := labelResp.GetCoachLabelMap()[uid]; ok {
            out.Label = &esportHttp.CoachLabel{
                Type:      esportHttp.LabelSourceType(label.GetType()),
                SourceUrl: label.GetSourceUrl(),
            }
        }
    }

    if !mgr.bc.Get().ESportGameSkillConfig.GuildIntroNeeded {
        out.GuildId = 0
        out.GuildName = ""
        out.Divide = 0
    }

    // 查询技能商品
    hallResp, err := mgr.ESportHallCli.GetAllSkillList(ctx, &esport_hall.GetAllSkillListRequest{
        Uid: authInfo.UserID,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetAllSkillList err:%v, uid:%d", err, uid)
        return out, err
    }

    skillResp, err := mgr.ESportSkillCli.GetUserCurrentSkill(ctx, &pb.GetUserCurrentSkillRequest{
        Uid: uid,
    })
    if err != nil {
        e := protocol.ToServerError(err)
        log.ErrorWithCtx(ctx, "GetUserCurrentSkill err: %v, uid:%d", err, uid)
        return out, e
    }

    out.SkillList = mgr.toOuterSkillList(ctx, hallResp.GetProductList(), skillResp.GetSkill())

    log.DebugWithCtx(ctx, "GetESportMasterInfo req:%+v, out:%+v, 耗时:%s", req, out, time.Since(ts))
    return out, nil
}

func (mgr *SkillMgr) BatchGetAuditSkill(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
    ctx, cancel := context.WithTimeout(r.Context(), 5*time.Second)
    defer cancel()

    var req esportHttp.BatchGetAuditSkillRequest
    err := json.Unmarshal(authInfo.Body, &req)
    if err != nil {
        e := protocol.ToServerError(err)
        log.ErrorWithCtx(ctx, "BatchGetAuditSkill Failed to parse request  body(%s)  err(%v)", string(authInfo.Body), err)
        _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
        return
    }

    resp, err := mgr.ESportSkillCli.BatchGetAuditSkill(ctx, &pb.BatchGetAuditSkillRequest{
        GuildId:     req.GetGuildId(),
        Uids:        req.GetUids(),
        AuditType:   req.GetAuditType(),
        OffSet:      req.GetOffSet(),
        Limit:       req.GetLimit(),
        NeedTotal:   req.GetNeedTotal(),
        AuditSource: req.GetAuditSource(),
    })

    if err != nil {
        e := protocol.ToServerError(err)
        log.ErrorWithCtx(ctx, "BatchGetAuditSkill Failed to parse request  body(%s)  err(%v)", string(authInfo.Body), err)
        _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
        return
    }

    out := &esportHttp.BatchGetAuditSkillResponse{
        GuildId:     req.GetGuildId(),
        Uids:        req.GetUids(),
        AuditType:   req.GetAuditType(),
        OffSet:      req.GetOffSet(),
        Limit:       req.GetLimit(),
        TotalCount:  resp.GetTotalCount(),
        AuditSource: req.GetAuditSource(),
    }

    for _, v := range resp.GetList() {
        skillList := make([]*esportHttp.UserSkillInfo, 0)
        for _, sk := range v.Skill {
            sectionList := make([]*esportHttp.SectionInfo, 0)
            for _, section := range sk.SectionList {
                sectionList = append(sectionList, &esportHttp.SectionInfo{
                    SectionName: section.SectionName,
                    ItemList:    section.ItemList,
                    SectionId:   section.SectionId,
                })
            }

            skill := &esportHttp.UserSkillInfo{
                GameId:        sk.GameId,
                GameName:      sk.GameName,
                SkillEvidence: sk.SkillEvidence,
                Audio:         sk.Audio,
                AudioDuration: sk.AudioDuration,
                SectionList:   sectionList,
                TextDesc:      sk.TextDesc,
            }

            skillList = append(skillList, skill)
        }
        record := &esportHttp.AuditSkillRecord{
            Uid:         v.GetUid(),
            Account:     v.GetAccount(),
            NickName:    v.GetNickName(),
            Ttid:        v.GetTtid(),
            AuditToken:  v.GetAuditToken(),
            AuditType:   v.GetAuditType(),
            ApplyTime:   v.GetApplyTime(),
            Skill:       skillList,
            AuditSource: v.GetAuditSource(),
            Reason:      v.GetReason(),
        }

        out.List = append(out.List, record)
    }
    log.DebugWithCtx(ctx, "BatchGetAuditSkill req:%+v, out: %+v", req, out)
    _ = web.ServeAPICodeJson(w, status.Success, "", out)
}

func (mgr *SkillMgr) SetUserSkillAuditType(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
    ctx, cancel := context.WithTimeout(r.Context(), 5*time.Second)
    defer cancel()

    var req esportHttp.SetUserSkillAuditTypeRequest
    err := json.Unmarshal(authInfo.Body, &req)
    if err != nil {
        e := protocol.ToServerError(err)
        log.ErrorWithCtx(ctx, "SetUserSkillAuditTypeRequest Failed to parse request  body(%s)  err(%v)", string(authInfo.Body), err)
        _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
        return
    }

    _, err = mgr.ESportSkillCli.SetUserSkillAuditType(ctx, &pb.SetUserSkillAuditTypeRequest{
        Uid:        req.GetUid(),
        AuditToken: req.GetAuditToken(),
        AuditType:  req.GetAuditType(),
        Reason:     req.GetReason(),
    })

    if err != nil {
        e := protocol.ToServerError(err)
        log.ErrorWithCtx(ctx, "SetUserSkillAuditTypeRequest  req(%v)  err(%v)", req, err)
        _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
        return
    }

    out := &esportHttp.SetUserSkillAuditTypeResponse{}
    log.DebugWithCtx(ctx, "SetUserSkillAuditTypeRequest req:%+v, out: %+v", req, out)
    _ = web.ServeAPICodeJson(w, status.Success, "", out)
}

// =========================== internal ==============================
func (mgr *SkillMgr) toOuterSkillList(ctx context.Context, origin []*esport_hall.SkillProduct, skillInfoList []*esportSkill.UserSkillInfo) []*esportHttp.GodPageSkill {
    if origin == nil {
        return nil
    }

    // 根据游戏配置排序
    gameIds := make([]uint32, 0, len(origin))
    for _, item := range origin {
        gameIds = append(gameIds, item.GetGameId())
    }

    gameConfList, err := mgr.ESportSkillCli.GetGameDetailByIds(ctx, &esportSkill.GetGameDetailByIdsRequest{
        GameIds: gameIds,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetGameDetailByIds err: %v", err)
        return nil
    }

    gameCfgMap := make(map[uint32]*esportSkill.EsportGameConfig)
    for _, item := range gameConfList.GetConfigList() {
        gameCfgMap[item.GetGameId()] = item
    }

    skillInfoMap := make(map[uint32]*esportSkill.UserSkillInfo)
    for _, item := range skillInfoList {
        skillInfoMap[item.GetGameId()] = item
    }

    result := make([]*esportHttp.GodPageSkill, 0, len(origin))
    for _, item := range origin {
        gameDetail, ok := gameCfgMap[item.GetGameId()]
        if !ok {
            log.ErrorWithCtx(ctx, "toOuterSkillList gameDetail not found, game_id: %d, gameCfgMap:%+v", item.GetGameId(), gameCfgMap)
            continue
        }
        godPageSkill := &esportHttp.GodPageSkill{
            Id:            item.GetId(),
            Name:          gameDetail.GetName(),
            Icon:          gameDetail.GetGameIcon(),
            Price:         buildPriceInfo(item, gameDetail, gameConfList.GetMinimumPrice()),
            ReceiveSwitch: item.GetSwitch(),
            GameId:        gameDetail.GetGameId(),
            GuaranteeInfo: mgr.getGuaranteeInfo(ctx, gameDetail, skillInfoMap, item),
            FreezeType:    esportHttp.FreezeType(skillInfoMap[item.GetGameId()].GetFreezeType()),
            FreezeStopTs:  skillInfoMap[item.GetGameId()].GetFreezeStopTs(),
        }
        result = append(result, godPageSkill)
    }

    // 按游戏配置排序
    sort.Slice(result, func(i, j int) bool {
        if gameCfgMap[result[i].GetGameId()].GetGameType() != gameCfgMap[result[j].GetGameId()].GetGameType() {
            return gameCfgMap[result[i].GetGameId()].GetGameType() < gameCfgMap[result[j].GetGameId()].GetGameType()
        }

        return gameCfgMap[result[i].GetGameId()].GetGameRank() < gameCfgMap[result[j].GetGameId()].GetGameRank()
    })

    return result
}

func (mgr *SkillMgr) getGuaranteeInfo(ctx context.Context, gameDetail *pb.EsportGameConfig, skillInfoMap map[uint32]*pb.UserSkillInfo, item *esport_hall.SkillProduct) *esportHttp.GodPageSkill_GuaranteeInfo {
    var guaranteeInfo *esportHttp.GodPageSkill_GuaranteeInfo
    for _, sectionInfo := range gameDetail.GameInformationList {
        if sectionInfo.SectionName == guaranteeSectionName {
            guaranteeInfo = &esportHttp.GodPageSkill_GuaranteeInfo{
                GuaranteeRank: sectionInfo.GetItemList(),
                SectionId:     sectionInfo.SectionId,
                SelectType:    esportHttp.SelectType(sectionInfo.SelectType),
            }

            // 找出当前段位名称
            curranGuaranteeRankNameMap := make(map[string]bool)

            skillInfo, ok := skillInfoMap[item.GetGameId()]
            if !ok {
                log.ErrorWithCtx(ctx, "getGuaranteeInfo skillInfoMap not found, game_id: %d, skillInfoMap:%+v", item.GetGameId(), skillInfoMap)
                break
            }
            for _, section := range skillInfo.SectionList {
                if section.SectionName == guaranteeSectionName {
                    for _, rankName := range section.ItemList {
                        curranGuaranteeRankNameMap[rankName] = true
                    }
                }
            }
            // 匹配段位index
            for index, rankName := range sectionInfo.GetItemList() {
                if curranGuaranteeRankNameMap[rankName] {
                    guaranteeInfo.CurrentGuaranteeIndexList = append(guaranteeInfo.CurrentGuaranteeIndexList, uint32(index))
                }
            }
            // 原选项被删除or被重命名 默认选择第一个
            // if len(sectionInfo.GetItemList()) > 0 && len(guaranteeInfo.CurrentGuaranteeIndexList) == 0 {
            //     guaranteeInfo.CurrentGuaranteeIndexList = append(guaranteeInfo.CurrentGuaranteeIndexList, 0)
            // }
            break
        }
    }
    return guaranteeInfo
}

func buildPriceInfo(skillProduct *esport_hall.SkillProduct, gameCfg *esportSkill.EsportGameConfig, minPrice uint32) *esportHttp.PriceInfo {
    var minRankPrice, maxRankPrice uint32

    if len(gameCfg.GetGamePricing().GetPrice()) == 0 {
        return &esportHttp.PriceInfo{}
    }
    minRankPrice = gameCfg.GetGamePricing().GetPrice()[0] // 统一配置的最高价 或 段位配置的最低价
    if gameCfg.GetGamePricing().GetGamePricingType() == esportSkill.GAME_PRICING_TYPE_GAME_PRICING_TYPE_UNIFY {
        maxRankPrice = gameCfg.GetGamePricing().GetPrice()[0]
    } else {
        // 获取段位配置
        rankTagCfg := &esportSkill.GameInformation{}
        for _, item := range gameCfg.GetGameInformationList() {
            if item.GetInformationType() == esportSkill.GameInformation_GAME_INFORMATION_TYPE_RANK {
                rankTagCfg = item
                break
            }
        }

        // 确定段位最大价格下标
        tagIdx := 0
        for i, item := range rankTagCfg.GetItemList() {
            if item == skillProduct.GetTag() {
                tagIdx = i
                break
            }
        }
        maxRankPrice = gameCfg.GetGamePricing().GetPrice()[0]
        if tagIdx < len(gameCfg.GetGamePricing().GetPrice()) {
            maxRankPrice = gameCfg.GetGamePricing().GetPrice()[tagIdx]
        }
    }

    priceInfo := &esportHttp.PriceInfo{
        Price:     skillProduct.GetPrice(),
        PriceUnit: beanMinuteUnit,
        MinPrice:  minPrice,
        BasePrice: minRankPrice,
        MaxPrice:  maxRankPrice,
    }
    realPriceType := uint32(gameCfg.GetGamePricing().GetGamePricingUnitType())
    if skillProduct.GetUintType() > 0 {
        realPriceType = skillProduct.GetUintType()
    }
    if realPriceType == uint32(esportSkill.GAME_PRICING_UNIT_TYPE_GAME_PRICING_UNIT_TYPE_PER_GAME) {
        priceInfo.PriceUnit = beanRoundUnit
    }
    return priceInfo
}

func (mgr *SkillMgr) getDynamicHeadMd5(ctx context.Context, uid uint32) string {
    dyHeadMap, tErr := mgr.headDynamicImageCli.GetHeadDynamicImageMd5(ctx, []uint32{uid})
    if tErr != nil || dyHeadMap == nil {
        log.ErrorWithCtx(ctx, "getDynamicHeadMd5 uid: %+v, err: %v", uid, tErr)
        return ""
    }
    return dyHeadMap[uid]
}

func (mgr *SkillMgr) GetTradeDataStat(ctx context.Context, uid uint32) ([]*esportHttp.TradeStat, error) {
    defaultTradeMap := map[uint32]*esportHttp.TradeStat{
        uint32(esportHttp.ESportStatType_ESPORT_STAT_TYPE_WEEKLY): {
            StatType: uint32(esportHttp.ESportStatType_ESPORT_STAT_TYPE_WEEKLY),
            Trade: &esportHttp.TradeData{
                Income:      0,
                ServiceUser: 0,
                RetradeUser: 0,
                FreshUser:   0,
            },
        },
        uint32(esportHttp.ESportStatType_ESPORT_STAT_TYPE_MONTHLY): {
            StatType: uint32(esportHttp.ESportStatType_ESPORT_STAT_TYPE_MONTHLY),
            Trade: &esportHttp.TradeData{
                Income:      0,
                ServiceUser: 0,
                RetradeUser: 0,
                FreshUser:   0,
            },
        },
        uint32(esportHttp.ESportStatType_ESPORT_STAT_TYPE_ALL): {
            StatType: uint32(esportHttp.ESportStatType_ESPORT_STAT_TYPE_ALL),
            Trade: &esportHttp.TradeData{
                Income:      0,
                ServiceUser: 0,
                RetradeUser: 0,
                FreshUser:   0,
            },
        },
    }

    originData := &TradeDataStat{}
    outSideData := make([]*esportHttp.TradeStat, 0, 3)
    var err error
    if len(mgr.bc.Get().ESportGameSkillConfig.TradeDataStatApi) > 0 {
        originData, err = mgr.getTradeDataStat(ctx, uid)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetTradeDataStat uid: %d err:%+v", uid, err)
        }
    }
    if originData != nil && len(originData.Data) > 0 {
        for _, item := range originData.Data {
            st, _ := strconv.Atoi(item.DateDim)
            if st == 0 {
                continue
            }
            defaultTradeMap[uint32(st)] = &esportHttp.TradeStat{
                StatType: uint32(st),
                Trade: &esportHttp.TradeData{
                    Income:      item.OrderAmt,
                    ServiceUser: item.ServiceUserCnt,
                    RetradeUser: item.RechargeUserCnt,
                    FreshUser:   item.NewUserCnt,
                },
            }
        }
    }
    outSideData = append(outSideData, defaultTradeMap[uint32(esportHttp.ESportStatType_ESPORT_STAT_TYPE_WEEKLY)])
    outSideData = append(outSideData, defaultTradeMap[uint32(esportHttp.ESportStatType_ESPORT_STAT_TYPE_MONTHLY)])
    outSideData = append(outSideData, defaultTradeMap[uint32(esportHttp.ESportStatType_ESPORT_STAT_TYPE_ALL)])
    return outSideData, nil
}

type TradeDataStatItem struct {
    DateDim         string `json:"date_dim"`
    OrderAmt        uint32 `json:"order_amt"`
    ServiceUserCnt  uint32 `json:"service_user_cnt"`
    NewUserCnt      uint32 `json:"new_user_cnt"`
    RechargeUserCnt uint32 `json:"recharge_user_cnt"`
}

type TradeDataStat struct {
    Data []*TradeDataStatItem `json:"data"`
}

type TradeDataStatResp struct {
    Data *TradeDataStat `json:"data"`
}

func (mgr *SkillMgr) getTradeDataStat(ctx context.Context, uid uint32) (*TradeDataStat, error) {
    dataResp := &TradeDataStatResp{}

    fullApi := fmt.Sprintf("%s%d", mgr.bc.Get().ESportGameSkillConfig.TradeDataStatApi, uid)

    req, err := http.NewRequestWithContext(ctx, "GET", fullApi, nil)
    if err != nil {
        log.ErrorWithCtx(ctx, "getTradeDataStat url:%s err:%+v", fullApi, err)
        return nil, err
    }

    resp, err := mgr.httpCli.Do(req)
    if err != nil {
        log.ErrorWithCtx(ctx, "getTradeDataStat url:%s err:%+v", fullApi, err)
        return nil, err
    }

    if err != nil && resp == nil {
        log.ErrorWithCtx(ctx, "getTradeDataStat error url%s, err:%v", fullApi, err)
        return nil, err
    } else {
        // Close the connection to reuse it
        defer func() {
            _ = resp.Body.Close()
        }()

        // Let's check if the work actually is done
        // We have seen inconsistencies even when we get 200 OK response

        body, err := io.ReadAll(resp.Body)
        if err != nil {
            log.ErrorWithCtx(ctx, "getTradeDataStat cant parse response body. %+v", err)
            return nil, err
        }

        log.DebugWithCtx(ctx, "getTradeDataStat url:%s, Body:%s", fullApi, string(body))
        err = json.Unmarshal(body, dataResp)
        if err != nil {
            log.ErrorWithCtx(ctx, "getTradeDataStat marshal response body:%s, err: %+v", string(body), err)
            return nil, err
        }
    }

    return dataResp.Data, nil
}

func (mgr *SkillMgr) SetGuaranteeWinStatus(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
    ctx, cancel := context.WithTimeout(r.Context(), 5*time.Second)
    defer cancel()

    var req esportHttp.SetGameGuaranteeStatusRequest
    err := json.Unmarshal(authInfo.Body, &req)
    if err != nil {
        e := protocol.ToServerError(err)
        log.ErrorWithCtx(ctx, "SetGuaranteeWinStatus Failed to parse request  body(%s)  err(%v)", string(authInfo.Body), err)
        _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
        return
    }

    uid := authInfo.UserID

    // 检测是否有权限操作包赢
    hasGuaranteeWinPermission, err := mgr.checkSkillLabel(ctx, uid, req.GetGameId())
    if err != nil {
        e := protocol.ToServerError(err)
        log.ErrorWithCtx(ctx, "SetGuaranteeWinStatus checkSkillLabel err: %v", err)
        _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
        return
    }
    if !hasGuaranteeWinPermission {
        log.WarnWithCtx(ctx, "SetGuaranteeWinStatus could not operate, uid:%d, req:%+v, err: %v", uid, req, err)
        _ = web.ServeAPICodeJson(w, int32(status.ErrEsportsSkillCommonErr), "没有权限操作包赢", nil)
        return
    }

    // 开启时做检查
    if req.GetIsGuaranteeWin() {
        gameInfo, err := mgr.ESportSkillCli.GetUserSkillByGameId(ctx, &esportSkill.GetUserSkillByGameIdRequest{
            Uid:    uid,
            GameId: req.GetGameId(),
        })
        if err != nil {
            e := protocol.ToServerError(err)
            log.ErrorWithCtx(ctx, "SetGuaranteeWinStatus GetUserSkillByGameId err: %v", err)
            _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
            return
        }

        var couldSet bool
        for _, section := range gameInfo.GetCurrentSkill().GetSectionList() {
            if section.SectionName == GuaranteeWinSectionName {
                if len(section.ItemList) >= 0 {
                    couldSet = true
                    break
                }
            }
        }
        if !couldSet {
            log.WarnWithCtx(ctx, "SetGuaranteeWinStatus could not operate, uid:%d, req:%+v, err: %v", uid, req, err)
            _ = web.ServeAPICodeJson(w, int32(status.ErrEsportsSkillCommonErr), "需要选择包赢段位才可生效", nil)
            return
        }
    }

    _, err = mgr.ESportSkillCli.SetGameGuaranteeStatus(ctx, &esportSkill.SetGameGuaranteeStatusRequest{
        Uid:            uid,
        GameId:         req.GetGameId(),
        IsGuaranteeWin: req.GetIsGuaranteeWin(),
    })
    if err != nil {
        e := protocol.ToServerError(err)
        log.ErrorWithCtx(ctx, "SetGuaranteeWinStatus err, uid:%d, body(%s),err(%v)", uid, string(authInfo.Body), err)
        _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
        return
    }

    // 强制包赢时，关闭包赢开关顺带关闭接单
    if mgr.bc.GetMustGuaranteeWin() && !req.GetIsGuaranteeWin() {
        skillList, err := mgr.ESportHallCli.GetAllSkillList(ctx, &esport_hall.GetAllSkillListRequest{
            Uid: uid,
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "GetAllSkillList uid:%d, err: %v", uid, err)
            e := protocol.ToServerError(err)
            log.ErrorWithCtx(ctx, "GetAllSkillList err, uid:%d, err(%v)", uid, err)
            _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
            return
        }
        for _, product := range skillList.ProductList {
            // 正在接单中
            if product.GetGameId() == req.GetGameId() && product.GetSwitch() {
                _, err = mgr.ESportHallCli.SetSkillReceiveSwitch(ctx, &esport_hall.SetSkillReceiveSwitchRequest{
                    Uid:     uid,
                    SkillId: product.GetGameId(),
                    Switch:  false,
                })
                if err != nil {
                    e := protocol.ToServerError(err)
                    log.ErrorWithCtx(ctx, "SetSkillSwitch err, uid:%d, err(%v)", uid, err)
                    _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
                    return
                }
                log.InfoWithCtx(ctx, "SetSkillSwitch 强制包赢，大神关闭包赢开关， uid:%d, skillId:%d, switch:%v", uid, product.GetGameId(), false)
                _ = web.ServeAPICodeJson(w, int32(status.ErrEsportsSkillCommonErr), "关闭包赢并停止该技能接单", nil)
                return
            }

        }
    }

    out := &esportHttp.SetGameGuaranteeStatusResponse{}
    log.InfoWithCtx(ctx, "SetGuaranteeWinStatus req:%v, uid:%d, out: %+v", req, uid, out)
    _ = web.ServeAPICodeJson(w, status.Success, "", out)
}

// GetTopGameList 获取游戏列表
func (mgr *SkillMgr) GetTopGameList(ctx context.Context, req *esportHttp.GetTopGameListRequest) (*esportHttp.GetTopGameListResponse, error) {
    out := &esportHttp.GetTopGameListResponse{
        ItemList: make([]*esportHttp.GameItem, 0),
    }
    defer func() {
        log.DebugWithCtx(ctx, "[GetTopGameList] req: %v, out: %+v", req, out)
    }()
    // 获取当前请求的客服用户
    svrInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "[SendCoachSkillCard] ServiceInfoFromContext fail. in:%+v", req)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "缺少请求上下文信息")
    }

    var mobileRes *esportSkill.GetTopGameListResponse
    var pcRes *esportSkill.GetTopGameListResponse

    wg := sync.WaitGroup{}
    wg.Add(1)
    go func() {
        defer wg.Done()
        var err error
        // 手游的
        mobileRes, err = mgr.ESportSkillCli.GetTopGameList(ctx, &esportSkill.GetTopGameListRequest{
            Uid:      svrInfo.UserID,
            GameType: esportSkill.GAME_TYPE_GAME_TYPE_MOBILE,
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "[SendCoachSkillCard] failed to call svr to get mobile, uid:%d, req:%+v, err:%v", svrInfo.UserID, req, err)
            return
        }
    }()
    wg.Add(1)
    go func() {
        defer wg.Done()
        var err error
        // 端游的
        pcRes, err = mgr.ESportSkillCli.GetTopGameList(ctx, &esportSkill.GetTopGameListRequest{
            Uid:      svrInfo.UserID,
            GameType: esportSkill.GAME_TYPE_GAME_TYPE_PC,
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "[SendCoachSkillCard] failed to call svr to get pc, uid:%d, req:%+v, err:%v", svrInfo.UserID, req, err)
            return
        }
    }()
    wg.Wait()

    for _, item := range mobileRes.GetItemList() {
        out.ItemList = append(out.ItemList, &esportHttp.GameItem{
            GameId:   item.GetGameId(),
            GameName: item.GetGameName(),
            GameIcon: item.GetGameIcon(),
        })
    }
    for _, item := range pcRes.GetItemList() {
        out.ItemList = append(out.ItemList, &esportHttp.GameItem{
            GameId:   item.GetGameId(),
            GameName: item.GetGameName(),
            GameIcon: item.GetGameIcon(),
        })
    }

    return out, nil
}
