package esport

import (
	"bytes"
	"context"
	"encoding/json"
	"github.com/golang/mock/gomock"
	mockEsportRole "golang.52tt.com/clients/mocks/esport-role"
	mockMngApi "golang.52tt.com/clients/mocks/risk-mng-api"
	mockTtcProxy "golang.52tt.com/clients/mocks/ttc-proxy"
	ttc_proxy "golang.52tt.com/clients/ttc-proxy"
	"golang.52tt.com/pkg/web"
	anchorcontractpb "golang.52tt.com/protocol/services/anchorcontract-go"
	esport_skill "golang.52tt.com/protocol/services/esport-skill"
	esportRolePb "golang.52tt.com/protocol/services/esport_role"
	ttc_proxy_pb "golang.52tt.com/protocol/services/ttc-proxy"
	"golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/conf"
	"golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/mocks"
	esport_http "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/models/esport"
	"io/ioutil"
	"net/http"
	"net/http/httptest"
	"testing"
)

var mockBc *mocks.MockBusinessConfApi
var mgr *ApplyRoleMgrImpl

var (
	uid = uint32(2266893)
	ctx = context.Background()

	auditToken  = "test"
	applyType   = uint32(2)
	applyStatus = uint32(8)
	idNum       = "111111200010012222"

	guildId = uint32(1)
	gameId  = uint32(1)
)

var TtcProxyCli *mockTtcProxy.MockIClient
var ESportSkillCli *esport_skill.MockEsportSkillClient
var ESportRoleCli *mockEsportRole.MockIClient
var RiskMngApiCli *mockMngApi.MockIClient
var AnchorContractCli *anchorcontractpb.MockAnchorContractGoClient

func initRpcCli(t *testing.T) {
	ctl := gomock.NewController(t)

	TtcProxyCli = mockTtcProxy.NewMockIClient(ctl)
	ESportSkillCli = esport_skill.NewMockEsportSkillClient(ctl)
	ESportRoleCli = mockEsportRole.NewMockIClient(ctl)
	RiskMngApiCli = mockMngApi.NewMockIClient(ctl)
	AnchorContractCli = anchorcontractpb.NewMockAnchorContractGoClient(ctl)

	mockBc = mocks.NewMockBusinessConfApi(ctl)

	mgr = &ApplyRoleMgrImpl{
		bc:                mockBc,
		sc:                &conf.ServiceConfig{},
		ESportRoleCli:     ESportRoleCli,
		RiskMngApiCli:     RiskMngApiCli,
		AnchorContractCli: AnchorContractCli,
		ESportSkillCli:    ESportSkillCli,
		TTCProxyClient:    TtcProxyCli,
	}
}

func TestApplyRoleMgrImpl_GetSkillConfByGameId(t *testing.T) {
	initRpcCli(t)

	tests := []struct {
		name        string
		initFunc    func()
		getBody     func() []byte
		getAuthInfo func(body []byte) *web.AuthInfo
	}{
		{
			name: "common",
			initFunc: func() {
				ESportSkillCli.EXPECT().GetGameDetailById(gomock.Any(), gomock.Any()).Return(&esport_skill.GetGameDetailByIdResponse{
					Config: &esport_skill.EsportGameConfig{
						GameId:         1,
						Name:           "1",
						GameType:       0,
						GameIcon:       "",
						GameBackground: "",
						GameColor:      "",
						GameRank:       0,
						SkillEvidence:  "",
						SkillDesc:      "",
						GameInformationList: []*esport_skill.GameInformation{
							{
								InformationType: 1,
								SectionName:     "11",
								ItemList:        []string{"111"},
								SelectType:      0,
								SectionId:       0,
							},
						},
						GamePricing: &esport_skill.GamePricing{},
						UpdateTime:  0,
					},
					MinimumPrice: 0,
				}, nil)
				mockBc.EXPECT().Get().Return(&conf.BusinessConf{
					EsportTradeAppealConfig: nil,
					ESportGameSkillConfig: &conf.ESportGameSkillConfig{
						AudioIntroNeeded: false,
						GuildIntroNeeded: false,
						GuildDivide:      0,
						GameTypeInfoList: []*conf.GameTypeInfo{
							{
								GameType:     1,
								GameTypeName: "111",
								Rank:         1,
							},
						},
						TradeDataStatApi: "",
						SelfIntroConf:    &conf.SelfIntroConf{},
					},
				}).AnyTimes()
				mockBc.EXPECT().GetGuaranteeWinDesc().Return("奇怪的包赢文案")

			},
			getBody: func() []byte {
				req := &esport_http.GetUserSkillByGameIdRequest{
					TargetUid: uid,
					GameId:    gameId,
				}
				body, _ := json.Marshal(req)
				return body
			},
			getAuthInfo: func(body []byte) *web.AuthInfo {
				return &web.AuthInfo{
					Body:   body,
					UserID: uid,
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			apply := mgr

			if tt.initFunc != nil {
				tt.initFunc()
			}

			handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				r.Body = ioutil.NopCloser(bytes.NewBuffer(tt.getBody()))
				apply.GetSkillConfByGameId(tt.getAuthInfo(tt.getBody()), w, r)
			})
			rr := httptest.NewRecorder()
			req, err := http.NewRequest("POST", "/tt-revenue-http-logic/esport/apply/get_skill_conf", nil)
			if err != nil {
				t.Fatal(err)
			}
			handler.ServeHTTP(rr, req)

		})
	}
}

func TestApplyRoleMgrImpl_ApplyESportRole(t *testing.T) {
	initRpcCli(t)
	tests := []struct {
		name        string
		initFunc    func()
		getBody     func() []byte
		getAuthInfo func(body []byte) *web.AuthInfo
	}{
		{
			name: "common",
			initFunc: func() {
				//ESportSkillCli.EXPECT().GetSwitch(gomock.Any(), gomock.Any()).Return(&esport_skill.GetSwitchResponse{
				//	SwitchStatus: &esport_skill.SwitchStatus{
				//		MainSwitchStatus: 1,
				//	},
				//}, nil)
				TtcProxyCli.EXPECT().GetUserRealNameAuthInfoV2(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ttc_proxy.GetUserRealNameAuthInfoV2Resp{
					IdcardInfo: &ttc_proxy_pb.AuthIdCardInfo{
						IdentityNum: idNum,
					},
					IsAdult: true,
				}, nil)
				AnchorContractCli.EXPECT().CheckCanApplySign(gomock.Any(), gomock.Any()).Return(nil, nil)

				ESportSkillCli.EXPECT().AddUserAuditSkill(gomock.Any(), gomock.Any()).Return(&esport_skill.AddUserAuditSkillResponse{}, nil)

				AnchorContractCli.EXPECT().GetUserContractCacheInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&anchorcontractpb.ContractCacheInfo{
					Contract: &anchorcontractpb.ContractInfo{},
				}, nil)
				ESportRoleCli.EXPECT().ApplyESportRequest(gomock.Any(), gomock.Any()).Return(&esportRolePb.ApplyESportResponse{}, nil)
			},

			getBody: func() []byte {
				req := &esport_http.ApplyESportRoleReq{
					ApplyType:    applyType,
					GuildId:      guildId,
					SignDuration: 36,
					UserSkill: &esport_http.UserSkillInfo{
						GameId:        1,
						GameName:      "1",
						SkillEvidence: "1",
						Audio:         "1",
						AudioDuration: 36,
						SectionList: []*esport_http.SectionInfo{
							{
								SectionId:   1,
								SectionName: "1",
							},
						},
						TextDesc: "111111",
						GameRank: 1,
					},
					DeviceId:   "1",
					ClientType: 1,
					AppVersion: 1,
				}
				body, _ := json.Marshal(req)
				return body
			},
			getAuthInfo: func(body []byte) *web.AuthInfo {
				return &web.AuthInfo{
					Body:   body,
					UserID: uid,
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			apply := mgr
			if tt.initFunc != nil {
				tt.initFunc()
			}

			handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				r.Body = ioutil.NopCloser(bytes.NewBuffer(tt.getBody()))
				apply.ApplyESportRole(tt.getAuthInfo(tt.getBody()), w, r)
			})
			rr := httptest.NewRecorder()
			req, err := http.NewRequest("POST", "/tt-revenue-http-logic/esport/apply/apply_role", nil)
			if err != nil {
				t.Fatal(err)
			}
			handler.ServeHTTP(rr, req)
		})
	}
}
