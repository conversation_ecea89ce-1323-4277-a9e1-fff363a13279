// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/adventure-activity-http/adventure-activity-http.proto

package adventure_activity_http

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type AwardType int32

const (
	AwardType_AWARD_TYPE_UNSPECIFIED AwardType = 0
	AwardType_AWARD_TYPE_GIFT        AwardType = 1
	AwardType_AWARD_TYPE_DRESS       AwardType = 2
)

var AwardType_name = map[int32]string{
	0: "AWARD_TYPE_UNSPECIFIED",
	1: "AWARD_TYPE_GIFT",
	2: "AWARD_TYPE_DRESS",
}
var AwardType_value = map[string]int32{
	"AWARD_TYPE_UNSPECIFIED": 0,
	"AWARD_TYPE_GIFT":        1,
	"AWARD_TYPE_DRESS":       2,
}

func (x AwardType) String() string {
	return proto.EnumName(AwardType_name, int32(x))
}
func (AwardType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_http_3aced8e6db9b5dcb, []int{0}
}

type DrawResultType int32

const (
	DrawResultType_DRAW_RESULT_UNSPECIFIED     DrawResultType = 0
	DrawResultType_DRAW_RESULT_CARD_LIGHTED    DrawResultType = 1
	DrawResultType_DRAW_RESULT_LEVEL_COMPLETED DrawResultType = 2
	DrawResultType_DRAW_RESULT_PEAK_REACHED    DrawResultType = 3
	DrawResultType_DRAW_RESULT_TOP_N_BOUNS     DrawResultType = 4
)

var DrawResultType_name = map[int32]string{
	0: "DRAW_RESULT_UNSPECIFIED",
	1: "DRAW_RESULT_CARD_LIGHTED",
	2: "DRAW_RESULT_LEVEL_COMPLETED",
	3: "DRAW_RESULT_PEAK_REACHED",
	4: "DRAW_RESULT_TOP_N_BOUNS",
}
var DrawResultType_value = map[string]int32{
	"DRAW_RESULT_UNSPECIFIED":     0,
	"DRAW_RESULT_CARD_LIGHTED":    1,
	"DRAW_RESULT_LEVEL_COMPLETED": 2,
	"DRAW_RESULT_PEAK_REACHED":    3,
	"DRAW_RESULT_TOP_N_BOUNS":     4,
}

func (x DrawResultType) String() string {
	return proto.EnumName(DrawResultType_name, int32(x))
}
func (DrawResultType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_http_3aced8e6db9b5dcb, []int{1}
}

type GetUserAdventureRecordRequest_RecordType int32

const (
	GetUserAdventureRecordRequest_RECORD_TYPE_UNSPECIFIED  GetUserAdventureRecordRequest_RecordType = 0
	GetUserAdventureRecordRequest_RECORD_TYPE_CARD_LIGHTED GetUserAdventureRecordRequest_RecordType = 2
	GetUserAdventureRecordRequest_RECORD_TYPE_COMPLETED    GetUserAdventureRecordRequest_RecordType = 3
)

var GetUserAdventureRecordRequest_RecordType_name = map[int32]string{
	0: "RECORD_TYPE_UNSPECIFIED",
	2: "RECORD_TYPE_CARD_LIGHTED",
	3: "RECORD_TYPE_COMPLETED",
}
var GetUserAdventureRecordRequest_RecordType_value = map[string]int32{
	"RECORD_TYPE_UNSPECIFIED":  0,
	"RECORD_TYPE_CARD_LIGHTED": 2,
	"RECORD_TYPE_COMPLETED":    3,
}

func (x GetUserAdventureRecordRequest_RecordType) String() string {
	return proto.EnumName(GetUserAdventureRecordRequest_RecordType_name, int32(x))
}
func (GetUserAdventureRecordRequest_RecordType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_http_3aced8e6db9b5dcb, []int{15, 0}
}

// 奖励信息
type AwardInfo struct {
	AwardId              string   `protobuf:"bytes,1,opt,name=award_id,json=awardId" json:"award_id"`
	AwardName            string   `protobuf:"bytes,2,opt,name=award_name,json=awardName" json:"award_name"`
	AwardPic             string   `protobuf:"bytes,3,opt,name=award_pic,json=awardPic" json:"award_pic"`
	AwardDesc            string   `protobuf:"bytes,4,opt,name=award_desc,json=awardDesc" json:"award_desc"`
	AwardType            uint32   `protobuf:"varint,5,opt,name=award_type,json=awardType" json:"award_type"`
	Amount               uint32   `protobuf:"varint,6,opt,name=amount" json:"amount"`
	ExpTime              int64    `protobuf:"varint,7,opt,name=exp_time,json=expTime" json:"exp_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AwardInfo) Reset()         { *m = AwardInfo{} }
func (m *AwardInfo) String() string { return proto.CompactTextString(m) }
func (*AwardInfo) ProtoMessage()    {}
func (*AwardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_http_3aced8e6db9b5dcb, []int{0}
}
func (m *AwardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardInfo.Unmarshal(m, b)
}
func (m *AwardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardInfo.Marshal(b, m, deterministic)
}
func (dst *AwardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardInfo.Merge(dst, src)
}
func (m *AwardInfo) XXX_Size() int {
	return xxx_messageInfo_AwardInfo.Size(m)
}
func (m *AwardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AwardInfo proto.InternalMessageInfo

func (m *AwardInfo) GetAwardId() string {
	if m != nil {
		return m.AwardId
	}
	return ""
}

func (m *AwardInfo) GetAwardName() string {
	if m != nil {
		return m.AwardName
	}
	return ""
}

func (m *AwardInfo) GetAwardPic() string {
	if m != nil {
		return m.AwardPic
	}
	return ""
}

func (m *AwardInfo) GetAwardDesc() string {
	if m != nil {
		return m.AwardDesc
	}
	return ""
}

func (m *AwardInfo) GetAwardType() uint32 {
	if m != nil {
		return m.AwardType
	}
	return 0
}

func (m *AwardInfo) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *AwardInfo) GetExpTime() int64 {
	if m != nil {
		return m.ExpTime
	}
	return 0
}

// 关卡信息
type LevelCfg struct {
	LevelId              uint32       `protobuf:"varint,1,opt,name=level_id,json=levelId" json:"level_id"`
	LevelName            string       `protobuf:"bytes,2,opt,name=level_name,json=levelName" json:"level_name"`
	MaxNFixed            uint32       `protobuf:"varint,3,opt,name=max_n_fixed,json=maxNFixed" json:"max_n_fixed"`
	CardList             []*AwardInfo `protobuf:"bytes,4,rep,name=card_list,json=cardList" json:"card_list"`
	LevelAward           *AwardInfo   `protobuf:"bytes,5,opt,name=level_award,json=levelAward" json:"level_award"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *LevelCfg) Reset()         { *m = LevelCfg{} }
func (m *LevelCfg) String() string { return proto.CompactTextString(m) }
func (*LevelCfg) ProtoMessage()    {}
func (*LevelCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_http_3aced8e6db9b5dcb, []int{1}
}
func (m *LevelCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelCfg.Unmarshal(m, b)
}
func (m *LevelCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelCfg.Marshal(b, m, deterministic)
}
func (dst *LevelCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelCfg.Merge(dst, src)
}
func (m *LevelCfg) XXX_Size() int {
	return xxx_messageInfo_LevelCfg.Size(m)
}
func (m *LevelCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelCfg.DiscardUnknown(m)
}

var xxx_messageInfo_LevelCfg proto.InternalMessageInfo

func (m *LevelCfg) GetLevelId() uint32 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

func (m *LevelCfg) GetLevelName() string {
	if m != nil {
		return m.LevelName
	}
	return ""
}

func (m *LevelCfg) GetMaxNFixed() uint32 {
	if m != nil {
		return m.MaxNFixed
	}
	return 0
}

func (m *LevelCfg) GetCardList() []*AwardInfo {
	if m != nil {
		return m.CardList
	}
	return nil
}

func (m *LevelCfg) GetLevelAward() *AwardInfo {
	if m != nil {
		return m.LevelAward
	}
	return nil
}

type UserPlayFile struct {
	LevelId              uint32            `protobuf:"varint,1,opt,name=level_id,json=levelId" json:"level_id"`
	CurrentChance        uint32            `protobuf:"varint,2,opt,name=current_chance,json=currentChance" json:"current_chance"`
	UserN                uint32            `protobuf:"varint,3,opt,name=user_n,json=userN" json:"user_n"`
	CardCollection       map[string]uint32 `protobuf:"bytes,4,rep,name=card_collection,json=cardCollection" json:"card_collection" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UserPlayFile) Reset()         { *m = UserPlayFile{} }
func (m *UserPlayFile) String() string { return proto.CompactTextString(m) }
func (*UserPlayFile) ProtoMessage()    {}
func (*UserPlayFile) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_http_3aced8e6db9b5dcb, []int{2}
}
func (m *UserPlayFile) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserPlayFile.Unmarshal(m, b)
}
func (m *UserPlayFile) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserPlayFile.Marshal(b, m, deterministic)
}
func (dst *UserPlayFile) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserPlayFile.Merge(dst, src)
}
func (m *UserPlayFile) XXX_Size() int {
	return xxx_messageInfo_UserPlayFile.Size(m)
}
func (m *UserPlayFile) XXX_DiscardUnknown() {
	xxx_messageInfo_UserPlayFile.DiscardUnknown(m)
}

var xxx_messageInfo_UserPlayFile proto.InternalMessageInfo

func (m *UserPlayFile) GetLevelId() uint32 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

func (m *UserPlayFile) GetCurrentChance() uint32 {
	if m != nil {
		return m.CurrentChance
	}
	return 0
}

func (m *UserPlayFile) GetUserN() uint32 {
	if m != nil {
		return m.UserN
	}
	return 0
}

func (m *UserPlayFile) GetCardCollection() map[string]uint32 {
	if m != nil {
		return m.CardCollection
	}
	return nil
}

// 购买礼包配置信息
type BuyPropCfg struct {
	GiftInfo             *AwardInfo `protobuf:"bytes,1,opt,name=gift_info,json=giftInfo" json:"gift_info"`
	BuyAmountOptions     []uint32   `protobuf:"varint,3,rep,packed,name=buy_amount_options,json=buyAmountOptions" json:"buy_amount_options"`
	UnitPrice            uint32     `protobuf:"varint,4,opt,name=unit_price,json=unitPrice" json:"unit_price"`
	DailyBuyLimit        uint32     `protobuf:"varint,5,opt,name=daily_buy_limit,json=dailyBuyLimit" json:"daily_buy_limit"`
	SingleBuyLimit       uint32     `protobuf:"varint,6,opt,name=single_buy_limit,json=singleBuyLimit" json:"single_buy_limit"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *BuyPropCfg) Reset()         { *m = BuyPropCfg{} }
func (m *BuyPropCfg) String() string { return proto.CompactTextString(m) }
func (*BuyPropCfg) ProtoMessage()    {}
func (*BuyPropCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_http_3aced8e6db9b5dcb, []int{3}
}
func (m *BuyPropCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuyPropCfg.Unmarshal(m, b)
}
func (m *BuyPropCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuyPropCfg.Marshal(b, m, deterministic)
}
func (dst *BuyPropCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuyPropCfg.Merge(dst, src)
}
func (m *BuyPropCfg) XXX_Size() int {
	return xxx_messageInfo_BuyPropCfg.Size(m)
}
func (m *BuyPropCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_BuyPropCfg.DiscardUnknown(m)
}

var xxx_messageInfo_BuyPropCfg proto.InternalMessageInfo

func (m *BuyPropCfg) GetGiftInfo() *AwardInfo {
	if m != nil {
		return m.GiftInfo
	}
	return nil
}

func (m *BuyPropCfg) GetBuyAmountOptions() []uint32 {
	if m != nil {
		return m.BuyAmountOptions
	}
	return nil
}

func (m *BuyPropCfg) GetUnitPrice() uint32 {
	if m != nil {
		return m.UnitPrice
	}
	return 0
}

func (m *BuyPropCfg) GetDailyBuyLimit() uint32 {
	if m != nil {
		return m.DailyBuyLimit
	}
	return 0
}

func (m *BuyPropCfg) GetSingleBuyLimit() uint32 {
	if m != nil {
		return m.SingleBuyLimit
	}
	return 0
}

// TOPN 加码奖励信息
type TopNAwardCfg struct {
	TopNLimit            uint32       `protobuf:"varint,1,opt,name=top_n_limit,json=topNLimit" json:"top_n_limit"`
	AwardList            []*AwardInfo `protobuf:"bytes,2,rep,name=award_list,json=awardList" json:"award_list"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *TopNAwardCfg) Reset()         { *m = TopNAwardCfg{} }
func (m *TopNAwardCfg) String() string { return proto.CompactTextString(m) }
func (*TopNAwardCfg) ProtoMessage()    {}
func (*TopNAwardCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_http_3aced8e6db9b5dcb, []int{4}
}
func (m *TopNAwardCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopNAwardCfg.Unmarshal(m, b)
}
func (m *TopNAwardCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopNAwardCfg.Marshal(b, m, deterministic)
}
func (dst *TopNAwardCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopNAwardCfg.Merge(dst, src)
}
func (m *TopNAwardCfg) XXX_Size() int {
	return xxx_messageInfo_TopNAwardCfg.Size(m)
}
func (m *TopNAwardCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_TopNAwardCfg.DiscardUnknown(m)
}

var xxx_messageInfo_TopNAwardCfg proto.InternalMessageInfo

func (m *TopNAwardCfg) GetTopNLimit() uint32 {
	if m != nil {
		return m.TopNLimit
	}
	return 0
}

func (m *TopNAwardCfg) GetAwardList() []*AwardInfo {
	if m != nil {
		return m.AwardList
	}
	return nil
}

// 获取活动信息 url: /tt-revenue-http-logic/adventure-activity/get_curr_game_info
type GetCurrGameInfoRequest struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId" json:"channel_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCurrGameInfoRequest) Reset()         { *m = GetCurrGameInfoRequest{} }
func (m *GetCurrGameInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetCurrGameInfoRequest) ProtoMessage()    {}
func (*GetCurrGameInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_http_3aced8e6db9b5dcb, []int{5}
}
func (m *GetCurrGameInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCurrGameInfoRequest.Unmarshal(m, b)
}
func (m *GetCurrGameInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCurrGameInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetCurrGameInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCurrGameInfoRequest.Merge(dst, src)
}
func (m *GetCurrGameInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetCurrGameInfoRequest.Size(m)
}
func (m *GetCurrGameInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCurrGameInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCurrGameInfoRequest proto.InternalMessageInfo

func (m *GetCurrGameInfoRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetCurrGameInfoResponse struct {
	ActivityId           uint32        `protobuf:"varint,1,opt,name=activity_id,json=activityId" json:"activity_id"`
	ActivityName         string        `protobuf:"bytes,2,opt,name=activity_name,json=activityName" json:"activity_name"`
	BeginTime            int64         `protobuf:"varint,3,opt,name=begin_time,json=beginTime" json:"begin_time"`
	EndTime              int64         `protobuf:"varint,4,opt,name=end_time,json=endTime" json:"end_time"`
	BuyPropCfg           *BuyPropCfg   `protobuf:"bytes,5,opt,name=buy_prop_cfg,json=buyPropCfg" json:"buy_prop_cfg"`
	LevelList            []*LevelCfg   `protobuf:"bytes,6,rep,name=level_list,json=levelList" json:"level_list"`
	TopNAwardInfo        *TopNAwardCfg `protobuf:"bytes,7,opt,name=top_n_award_info,json=topNAwardInfo" json:"top_n_award_info"`
	UserPlayFile         *UserPlayFile `protobuf:"bytes,8,opt,name=user_play_file,json=userPlayFile" json:"user_play_file"`
	ServerTs             int64         `protobuf:"varint,9,opt,name=server_ts,json=serverTs" json:"server_ts"`
	HasAccess            bool          `protobuf:"varint,10,opt,name=has_access,json=hasAccess" json:"has_access"`
	NoPermissionText     string        `protobuf:"bytes,11,opt,name=no_permission_text,json=noPermissionText" json:"no_permission_text"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetCurrGameInfoResponse) Reset()         { *m = GetCurrGameInfoResponse{} }
func (m *GetCurrGameInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetCurrGameInfoResponse) ProtoMessage()    {}
func (*GetCurrGameInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_http_3aced8e6db9b5dcb, []int{6}
}
func (m *GetCurrGameInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCurrGameInfoResponse.Unmarshal(m, b)
}
func (m *GetCurrGameInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCurrGameInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetCurrGameInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCurrGameInfoResponse.Merge(dst, src)
}
func (m *GetCurrGameInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetCurrGameInfoResponse.Size(m)
}
func (m *GetCurrGameInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCurrGameInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCurrGameInfoResponse proto.InternalMessageInfo

func (m *GetCurrGameInfoResponse) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *GetCurrGameInfoResponse) GetActivityName() string {
	if m != nil {
		return m.ActivityName
	}
	return ""
}

func (m *GetCurrGameInfoResponse) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetCurrGameInfoResponse) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetCurrGameInfoResponse) GetBuyPropCfg() *BuyPropCfg {
	if m != nil {
		return m.BuyPropCfg
	}
	return nil
}

func (m *GetCurrGameInfoResponse) GetLevelList() []*LevelCfg {
	if m != nil {
		return m.LevelList
	}
	return nil
}

func (m *GetCurrGameInfoResponse) GetTopNAwardInfo() *TopNAwardCfg {
	if m != nil {
		return m.TopNAwardInfo
	}
	return nil
}

func (m *GetCurrGameInfoResponse) GetUserPlayFile() *UserPlayFile {
	if m != nil {
		return m.UserPlayFile
	}
	return nil
}

func (m *GetCurrGameInfoResponse) GetServerTs() int64 {
	if m != nil {
		return m.ServerTs
	}
	return 0
}

func (m *GetCurrGameInfoResponse) GetHasAccess() bool {
	if m != nil {
		return m.HasAccess
	}
	return false
}

func (m *GetCurrGameInfoResponse) GetNoPermissionText() string {
	if m != nil {
		return m.NoPermissionText
	}
	return ""
}

// 购买礼包  url: /tt-revenue-http-logic/adventure-activity/buy_prop
type BuyPropRequest struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId" json:"channel_id"`
	BuyAmount            uint32   `protobuf:"varint,2,opt,name=buy_amount,json=buyAmount" json:"buy_amount"`
	FaceAuthResultToken  string   `protobuf:"bytes,3,opt,name=face_auth_result_token,json=faceAuthResultToken" json:"face_auth_result_token"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BuyPropRequest) Reset()         { *m = BuyPropRequest{} }
func (m *BuyPropRequest) String() string { return proto.CompactTextString(m) }
func (*BuyPropRequest) ProtoMessage()    {}
func (*BuyPropRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_http_3aced8e6db9b5dcb, []int{7}
}
func (m *BuyPropRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuyPropRequest.Unmarshal(m, b)
}
func (m *BuyPropRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuyPropRequest.Marshal(b, m, deterministic)
}
func (dst *BuyPropRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuyPropRequest.Merge(dst, src)
}
func (m *BuyPropRequest) XXX_Size() int {
	return xxx_messageInfo_BuyPropRequest.Size(m)
}
func (m *BuyPropRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BuyPropRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BuyPropRequest proto.InternalMessageInfo

func (m *BuyPropRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BuyPropRequest) GetBuyAmount() uint32 {
	if m != nil {
		return m.BuyAmount
	}
	return 0
}

func (m *BuyPropRequest) GetFaceAuthResultToken() string {
	if m != nil {
		return m.FaceAuthResultToken
	}
	return ""
}

type BuyPropResponse struct {
	FinalChanceAmount    uint32   `protobuf:"varint,1,opt,name=final_chance_amount,json=finalChanceAmount" json:"final_chance_amount"`
	Balance              uint64   `protobuf:"varint,2,opt,name=balance" json:"balance"`
	FaceAuthContextJson  string   `protobuf:"bytes,3,opt,name=face_auth_context_json,json=faceAuthContextJson" json:"face_auth_context_json"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BuyPropResponse) Reset()         { *m = BuyPropResponse{} }
func (m *BuyPropResponse) String() string { return proto.CompactTextString(m) }
func (*BuyPropResponse) ProtoMessage()    {}
func (*BuyPropResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_http_3aced8e6db9b5dcb, []int{8}
}
func (m *BuyPropResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuyPropResponse.Unmarshal(m, b)
}
func (m *BuyPropResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuyPropResponse.Marshal(b, m, deterministic)
}
func (dst *BuyPropResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuyPropResponse.Merge(dst, src)
}
func (m *BuyPropResponse) XXX_Size() int {
	return xxx_messageInfo_BuyPropResponse.Size(m)
}
func (m *BuyPropResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BuyPropResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BuyPropResponse proto.InternalMessageInfo

func (m *BuyPropResponse) GetFinalChanceAmount() uint32 {
	if m != nil {
		return m.FinalChanceAmount
	}
	return 0
}

func (m *BuyPropResponse) GetBalance() uint64 {
	if m != nil {
		return m.Balance
	}
	return 0
}

func (m *BuyPropResponse) GetFaceAuthContextJson() string {
	if m != nil {
		return m.FaceAuthContextJson
	}
	return ""
}

// 抽奖结果
type DrawResult struct {
	LevelId              uint32       `protobuf:"varint,1,opt,name=level_id,json=levelId" json:"level_id"`
	AwardList            []*AwardInfo `protobuf:"bytes,2,rep,name=award_list,json=awardList" json:"award_list"`
	ResultType           uint32       `protobuf:"varint,3,opt,name=result_type,json=resultType" json:"result_type"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *DrawResult) Reset()         { *m = DrawResult{} }
func (m *DrawResult) String() string { return proto.CompactTextString(m) }
func (*DrawResult) ProtoMessage()    {}
func (*DrawResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_http_3aced8e6db9b5dcb, []int{9}
}
func (m *DrawResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DrawResult.Unmarshal(m, b)
}
func (m *DrawResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DrawResult.Marshal(b, m, deterministic)
}
func (dst *DrawResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DrawResult.Merge(dst, src)
}
func (m *DrawResult) XXX_Size() int {
	return xxx_messageInfo_DrawResult.Size(m)
}
func (m *DrawResult) XXX_DiscardUnknown() {
	xxx_messageInfo_DrawResult.DiscardUnknown(m)
}

var xxx_messageInfo_DrawResult proto.InternalMessageInfo

func (m *DrawResult) GetLevelId() uint32 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

func (m *DrawResult) GetAwardList() []*AwardInfo {
	if m != nil {
		return m.AwardList
	}
	return nil
}

func (m *DrawResult) GetResultType() uint32 {
	if m != nil {
		return m.ResultType
	}
	return 0
}

// 抽奖 url: /tt-revenue-http-logic/adventure-activity/draw_card
type DrawCardRequest struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId" json:"channel_id"`
	LevelId              uint32   `protobuf:"varint,2,opt,name=level_id,json=levelId" json:"level_id"`
	Amount               uint32   `protobuf:"varint,3,opt,name=amount" json:"amount"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DrawCardRequest) Reset()         { *m = DrawCardRequest{} }
func (m *DrawCardRequest) String() string { return proto.CompactTextString(m) }
func (*DrawCardRequest) ProtoMessage()    {}
func (*DrawCardRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_http_3aced8e6db9b5dcb, []int{10}
}
func (m *DrawCardRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DrawCardRequest.Unmarshal(m, b)
}
func (m *DrawCardRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DrawCardRequest.Marshal(b, m, deterministic)
}
func (dst *DrawCardRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DrawCardRequest.Merge(dst, src)
}
func (m *DrawCardRequest) XXX_Size() int {
	return xxx_messageInfo_DrawCardRequest.Size(m)
}
func (m *DrawCardRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DrawCardRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DrawCardRequest proto.InternalMessageInfo

func (m *DrawCardRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *DrawCardRequest) GetLevelId() uint32 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

func (m *DrawCardRequest) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

type DrawCardResponse struct {
	ResultList           []*DrawResult `protobuf:"bytes,1,rep,name=result_list,json=resultList" json:"result_list"`
	UserPlayFile         *UserPlayFile `protobuf:"bytes,2,opt,name=user_play_file,json=userPlayFile" json:"user_play_file"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *DrawCardResponse) Reset()         { *m = DrawCardResponse{} }
func (m *DrawCardResponse) String() string { return proto.CompactTextString(m) }
func (*DrawCardResponse) ProtoMessage()    {}
func (*DrawCardResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_http_3aced8e6db9b5dcb, []int{11}
}
func (m *DrawCardResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DrawCardResponse.Unmarshal(m, b)
}
func (m *DrawCardResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DrawCardResponse.Marshal(b, m, deterministic)
}
func (dst *DrawCardResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DrawCardResponse.Merge(dst, src)
}
func (m *DrawCardResponse) XXX_Size() int {
	return xxx_messageInfo_DrawCardResponse.Size(m)
}
func (m *DrawCardResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DrawCardResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DrawCardResponse proto.InternalMessageInfo

func (m *DrawCardResponse) GetResultList() []*DrawResult {
	if m != nil {
		return m.ResultList
	}
	return nil
}

func (m *DrawCardResponse) GetUserPlayFile() *UserPlayFile {
	if m != nil {
		return m.UserPlayFile
	}
	return nil
}

// 平台中奖记录 轮播用
type PlatformWinningRecord struct {
	Uid                  uint32     `protobuf:"varint,1,opt,name=uid" json:"uid"`
	Nickname             string     `protobuf:"bytes,2,opt,name=nickname" json:"nickname"`
	Username             string     `protobuf:"bytes,3,opt,name=username" json:"username"`
	LevelId              uint32     `protobuf:"varint,4,opt,name=level_id,json=levelId" json:"level_id"`
	AwardInfo            *AwardInfo `protobuf:"bytes,5,opt,name=award_info,json=awardInfo" json:"award_info"`
	ResultType           uint32     `protobuf:"varint,6,opt,name=result_type,json=resultType" json:"result_type"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *PlatformWinningRecord) Reset()         { *m = PlatformWinningRecord{} }
func (m *PlatformWinningRecord) String() string { return proto.CompactTextString(m) }
func (*PlatformWinningRecord) ProtoMessage()    {}
func (*PlatformWinningRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_http_3aced8e6db9b5dcb, []int{12}
}
func (m *PlatformWinningRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlatformWinningRecord.Unmarshal(m, b)
}
func (m *PlatformWinningRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlatformWinningRecord.Marshal(b, m, deterministic)
}
func (dst *PlatformWinningRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlatformWinningRecord.Merge(dst, src)
}
func (m *PlatformWinningRecord) XXX_Size() int {
	return xxx_messageInfo_PlatformWinningRecord.Size(m)
}
func (m *PlatformWinningRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_PlatformWinningRecord.DiscardUnknown(m)
}

var xxx_messageInfo_PlatformWinningRecord proto.InternalMessageInfo

func (m *PlatformWinningRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PlatformWinningRecord) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *PlatformWinningRecord) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *PlatformWinningRecord) GetLevelId() uint32 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

func (m *PlatformWinningRecord) GetAwardInfo() *AwardInfo {
	if m != nil {
		return m.AwardInfo
	}
	return nil
}

func (m *PlatformWinningRecord) GetResultType() uint32 {
	if m != nil {
		return m.ResultType
	}
	return 0
}

// 获取平台中奖记录 url: /tt-revenue-http-logic/adventure-activity/get_platform_winning_info
type GetPlatformWinningRecordRequest struct {
	Version              string   `protobuf:"bytes,1,opt,name=version" json:"version"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPlatformWinningRecordRequest) Reset()         { *m = GetPlatformWinningRecordRequest{} }
func (m *GetPlatformWinningRecordRequest) String() string { return proto.CompactTextString(m) }
func (*GetPlatformWinningRecordRequest) ProtoMessage()    {}
func (*GetPlatformWinningRecordRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_http_3aced8e6db9b5dcb, []int{13}
}
func (m *GetPlatformWinningRecordRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlatformWinningRecordRequest.Unmarshal(m, b)
}
func (m *GetPlatformWinningRecordRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlatformWinningRecordRequest.Marshal(b, m, deterministic)
}
func (dst *GetPlatformWinningRecordRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlatformWinningRecordRequest.Merge(dst, src)
}
func (m *GetPlatformWinningRecordRequest) XXX_Size() int {
	return xxx_messageInfo_GetPlatformWinningRecordRequest.Size(m)
}
func (m *GetPlatformWinningRecordRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlatformWinningRecordRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlatformWinningRecordRequest proto.InternalMessageInfo

func (m *GetPlatformWinningRecordRequest) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

type GetPlatformWinningRecordResponse struct {
	NewVersion           string                   `protobuf:"bytes,1,opt,name=new_version,json=newVersion" json:"new_version"`
	RecordList           []*PlatformWinningRecord `protobuf:"bytes,2,rep,name=record_list,json=recordList" json:"record_list"`
	TopNUsed             uint32                   `protobuf:"varint,3,opt,name=top_n_used,json=topNUsed" json:"top_n_used"`
	ReqDurationSec       uint32                   `protobuf:"varint,4,opt,name=req_duration_sec,json=reqDurationSec" json:"req_duration_sec"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetPlatformWinningRecordResponse) Reset()         { *m = GetPlatformWinningRecordResponse{} }
func (m *GetPlatformWinningRecordResponse) String() string { return proto.CompactTextString(m) }
func (*GetPlatformWinningRecordResponse) ProtoMessage()    {}
func (*GetPlatformWinningRecordResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_http_3aced8e6db9b5dcb, []int{14}
}
func (m *GetPlatformWinningRecordResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlatformWinningRecordResponse.Unmarshal(m, b)
}
func (m *GetPlatformWinningRecordResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlatformWinningRecordResponse.Marshal(b, m, deterministic)
}
func (dst *GetPlatformWinningRecordResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlatformWinningRecordResponse.Merge(dst, src)
}
func (m *GetPlatformWinningRecordResponse) XXX_Size() int {
	return xxx_messageInfo_GetPlatformWinningRecordResponse.Size(m)
}
func (m *GetPlatformWinningRecordResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlatformWinningRecordResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlatformWinningRecordResponse proto.InternalMessageInfo

func (m *GetPlatformWinningRecordResponse) GetNewVersion() string {
	if m != nil {
		return m.NewVersion
	}
	return ""
}

func (m *GetPlatformWinningRecordResponse) GetRecordList() []*PlatformWinningRecord {
	if m != nil {
		return m.RecordList
	}
	return nil
}

func (m *GetPlatformWinningRecordResponse) GetTopNUsed() uint32 {
	if m != nil {
		return m.TopNUsed
	}
	return 0
}

func (m *GetPlatformWinningRecordResponse) GetReqDurationSec() uint32 {
	if m != nil {
		return m.ReqDurationSec
	}
	return 0
}

// 获取用户冒险记录 url: /tt-revenue-http-logic/adventure-activity/get_user_record
type GetUserAdventureRecordRequest struct {
	RecordType           uint32   `protobuf:"varint,1,opt,name=record_type,json=recordType" json:"record_type"`
	Offset               string   `protobuf:"bytes,2,opt,name=offset" json:"offset"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit" json:"limit"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserAdventureRecordRequest) Reset()         { *m = GetUserAdventureRecordRequest{} }
func (m *GetUserAdventureRecordRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserAdventureRecordRequest) ProtoMessage()    {}
func (*GetUserAdventureRecordRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_http_3aced8e6db9b5dcb, []int{15}
}
func (m *GetUserAdventureRecordRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAdventureRecordRequest.Unmarshal(m, b)
}
func (m *GetUserAdventureRecordRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAdventureRecordRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserAdventureRecordRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAdventureRecordRequest.Merge(dst, src)
}
func (m *GetUserAdventureRecordRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserAdventureRecordRequest.Size(m)
}
func (m *GetUserAdventureRecordRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAdventureRecordRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAdventureRecordRequest proto.InternalMessageInfo

func (m *GetUserAdventureRecordRequest) GetRecordType() uint32 {
	if m != nil {
		return m.RecordType
	}
	return 0
}

func (m *GetUserAdventureRecordRequest) GetOffset() string {
	if m != nil {
		return m.Offset
	}
	return ""
}

func (m *GetUserAdventureRecordRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetUserAdventureRecordResponse struct {
	NextOffset           string        `protobuf:"bytes,1,opt,name=next_offset,json=nextOffset" json:"next_offset"`
	RecordList           []*DrawResult `protobuf:"bytes,2,rep,name=record_list,json=recordList" json:"record_list"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetUserAdventureRecordResponse) Reset()         { *m = GetUserAdventureRecordResponse{} }
func (m *GetUserAdventureRecordResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserAdventureRecordResponse) ProtoMessage()    {}
func (*GetUserAdventureRecordResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_http_3aced8e6db9b5dcb, []int{16}
}
func (m *GetUserAdventureRecordResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAdventureRecordResponse.Unmarshal(m, b)
}
func (m *GetUserAdventureRecordResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAdventureRecordResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserAdventureRecordResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAdventureRecordResponse.Merge(dst, src)
}
func (m *GetUserAdventureRecordResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserAdventureRecordResponse.Size(m)
}
func (m *GetUserAdventureRecordResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAdventureRecordResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAdventureRecordResponse proto.InternalMessageInfo

func (m *GetUserAdventureRecordResponse) GetNextOffset() string {
	if m != nil {
		return m.NextOffset
	}
	return ""
}

func (m *GetUserAdventureRecordResponse) GetRecordList() []*DrawResult {
	if m != nil {
		return m.RecordList
	}
	return nil
}

// 获取用户T豆余额 url: /tt-revenue-http-logic/adventure-activity/get_user_balance
type GetUserBalanceRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserBalanceRequest) Reset()         { *m = GetUserBalanceRequest{} }
func (m *GetUserBalanceRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserBalanceRequest) ProtoMessage()    {}
func (*GetUserBalanceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_http_3aced8e6db9b5dcb, []int{17}
}
func (m *GetUserBalanceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserBalanceRequest.Unmarshal(m, b)
}
func (m *GetUserBalanceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserBalanceRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserBalanceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserBalanceRequest.Merge(dst, src)
}
func (m *GetUserBalanceRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserBalanceRequest.Size(m)
}
func (m *GetUserBalanceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserBalanceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserBalanceRequest proto.InternalMessageInfo

type GetUserBalanceRespone struct {
	Balance              uint64   `protobuf:"varint,1,opt,name=balance" json:"balance"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserBalanceRespone) Reset()         { *m = GetUserBalanceRespone{} }
func (m *GetUserBalanceRespone) String() string { return proto.CompactTextString(m) }
func (*GetUserBalanceRespone) ProtoMessage()    {}
func (*GetUserBalanceRespone) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_http_3aced8e6db9b5dcb, []int{18}
}
func (m *GetUserBalanceRespone) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserBalanceRespone.Unmarshal(m, b)
}
func (m *GetUserBalanceRespone) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserBalanceRespone.Marshal(b, m, deterministic)
}
func (dst *GetUserBalanceRespone) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserBalanceRespone.Merge(dst, src)
}
func (m *GetUserBalanceRespone) XXX_Size() int {
	return xxx_messageInfo_GetUserBalanceRespone.Size(m)
}
func (m *GetUserBalanceRespone) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserBalanceRespone.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserBalanceRespone proto.InternalMessageInfo

func (m *GetUserBalanceRespone) GetBalance() uint64 {
	if m != nil {
		return m.Balance
	}
	return 0
}

func init() {
	proto.RegisterType((*AwardInfo)(nil), "adventure_activity_http.AwardInfo")
	proto.RegisterType((*LevelCfg)(nil), "adventure_activity_http.LevelCfg")
	proto.RegisterType((*UserPlayFile)(nil), "adventure_activity_http.UserPlayFile")
	proto.RegisterMapType((map[string]uint32)(nil), "adventure_activity_http.UserPlayFile.CardCollectionEntry")
	proto.RegisterType((*BuyPropCfg)(nil), "adventure_activity_http.BuyPropCfg")
	proto.RegisterType((*TopNAwardCfg)(nil), "adventure_activity_http.TopNAwardCfg")
	proto.RegisterType((*GetCurrGameInfoRequest)(nil), "adventure_activity_http.GetCurrGameInfoRequest")
	proto.RegisterType((*GetCurrGameInfoResponse)(nil), "adventure_activity_http.GetCurrGameInfoResponse")
	proto.RegisterType((*BuyPropRequest)(nil), "adventure_activity_http.BuyPropRequest")
	proto.RegisterType((*BuyPropResponse)(nil), "adventure_activity_http.BuyPropResponse")
	proto.RegisterType((*DrawResult)(nil), "adventure_activity_http.DrawResult")
	proto.RegisterType((*DrawCardRequest)(nil), "adventure_activity_http.DrawCardRequest")
	proto.RegisterType((*DrawCardResponse)(nil), "adventure_activity_http.DrawCardResponse")
	proto.RegisterType((*PlatformWinningRecord)(nil), "adventure_activity_http.PlatformWinningRecord")
	proto.RegisterType((*GetPlatformWinningRecordRequest)(nil), "adventure_activity_http.GetPlatformWinningRecordRequest")
	proto.RegisterType((*GetPlatformWinningRecordResponse)(nil), "adventure_activity_http.GetPlatformWinningRecordResponse")
	proto.RegisterType((*GetUserAdventureRecordRequest)(nil), "adventure_activity_http.GetUserAdventureRecordRequest")
	proto.RegisterType((*GetUserAdventureRecordResponse)(nil), "adventure_activity_http.GetUserAdventureRecordResponse")
	proto.RegisterType((*GetUserBalanceRequest)(nil), "adventure_activity_http.GetUserBalanceRequest")
	proto.RegisterType((*GetUserBalanceRespone)(nil), "adventure_activity_http.GetUserBalanceRespone")
	proto.RegisterEnum("adventure_activity_http.AwardType", AwardType_name, AwardType_value)
	proto.RegisterEnum("adventure_activity_http.DrawResultType", DrawResultType_name, DrawResultType_value)
	proto.RegisterEnum("adventure_activity_http.GetUserAdventureRecordRequest_RecordType", GetUserAdventureRecordRequest_RecordType_name, GetUserAdventureRecordRequest_RecordType_value)
}

func init() {
	proto.RegisterFile("tt/quicksilver/adventure-activity-http/adventure-activity-http.proto", fileDescriptor_adventure_activity_http_3aced8e6db9b5dcb)
}

var fileDescriptor_adventure_activity_http_3aced8e6db9b5dcb = []byte{
	// 1480 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x57, 0x4d, 0x73, 0xdb, 0x44,
	0x18, 0x46, 0x76, 0x9a, 0x58, 0x6f, 0xe2, 0xc4, 0x28, 0x4d, 0xa2, 0x36, 0x6d, 0x13, 0xd4, 0x29,
	0x93, 0xe9, 0xd0, 0x74, 0xa0, 0x07, 0xbe, 0x0e, 0xc5, 0xb1, 0x95, 0xd4, 0xd4, 0x38, 0x1e, 0xc5,
	0x69, 0x07, 0x2e, 0x3b, 0xb2, 0xb4, 0x4e, 0x96, 0xc8, 0x2b, 0x45, 0x5a, 0x25, 0xf6, 0x99, 0x03,
	0x07, 0x66, 0x18, 0xfe, 0x04, 0xc3, 0xf0, 0x77, 0xe0, 0xca, 0x81, 0x33, 0xc3, 0x8f, 0x60, 0xf6,
	0x43, 0xb2, 0xdc, 0xc4, 0x25, 0x03, 0x37, 0xbf, 0xef, 0xa3, 0xdd, 0x7d, 0xde, 0xaf, 0x67, 0xd7,
	0xd0, 0x64, 0xec, 0xe9, 0x79, 0x4a, 0xbc, 0xb3, 0x84, 0x04, 0x17, 0x38, 0x7e, 0xea, 0xfa, 0x17,
	0x98, 0xb2, 0x34, 0xc6, 0x4f, 0x5c, 0x8f, 0x91, 0x0b, 0xc2, 0xc6, 0x4f, 0x4e, 0x19, 0x8b, 0x66,
	0xf9, 0x77, 0xa3, 0x38, 0x64, 0xa1, 0xb1, 0x91, 0xc3, 0x28, 0x83, 0x11, 0x87, 0xad, 0xdf, 0x35,
	0xd0, 0xeb, 0x97, 0x6e, 0xec, 0xb7, 0xe8, 0x20, 0x34, 0xee, 0x40, 0xc5, 0xe5, 0x06, 0x22, 0xbe,
	0xa9, 0x6d, 0x6b, 0x3b, 0xba, 0xb3, 0x20, 0xec, 0x96, 0x6f, 0xdc, 0x07, 0x90, 0x10, 0x75, 0x87,
	0xd8, 0x2c, 0x09, 0x50, 0x17, 0x9e, 0x8e, 0x3b, 0xc4, 0xc6, 0x26, 0x48, 0x03, 0x45, 0xc4, 0x33,
	0xcb, 0x02, 0x95, 0x5b, 0x75, 0x89, 0x37, 0x59, 0xeb, 0xe3, 0xc4, 0x33, 0xe7, 0x0a, 0x6b, 0x9b,
	0x38, 0x29, 0xc0, 0x6c, 0x1c, 0x61, 0xf3, 0xd6, 0xb6, 0xb6, 0x53, 0x55, 0x70, 0x6f, 0x1c, 0x61,
	0x63, 0x1d, 0xe6, 0xdd, 0x61, 0x98, 0x52, 0x66, 0xce, 0x0b, 0x48, 0x59, 0x9c, 0x2c, 0x1e, 0x45,
	0x88, 0x91, 0x21, 0x36, 0x17, 0xb6, 0xb5, 0x9d, 0xb2, 0xb3, 0x80, 0x47, 0x51, 0x8f, 0x0c, 0xb1,
	0xf5, 0x97, 0x06, 0x95, 0x36, 0xbe, 0xc0, 0x41, 0x63, 0x70, 0xc2, 0xbf, 0x0b, 0xf8, 0xef, 0x2c,
	0xa8, 0xaa, 0xb3, 0x20, 0x6c, 0x19, 0x94, 0x84, 0x8a, 0x41, 0x09, 0x8f, 0x08, 0xea, 0x01, 0x2c,
	0x0e, 0xdd, 0x11, 0xa2, 0x68, 0x40, 0x46, 0xd8, 0x17, 0x61, 0x55, 0x1d, 0x7d, 0xe8, 0x8e, 0x3a,
	0xfb, 0xdc, 0x61, 0x3c, 0x07, 0xdd, 0xe3, 0xbc, 0x03, 0x92, 0x30, 0x73, 0x6e, 0xbb, 0xbc, 0xb3,
	0xf8, 0x91, 0xb5, 0x3b, 0x23, 0xd3, 0xbb, 0x79, 0x96, 0x9d, 0x0a, 0x5f, 0xd4, 0x26, 0x09, 0x33,
	0x1a, 0xb0, 0x28, 0xcf, 0x17, 0xd1, 0x8a, 0xd0, 0x6f, 0xb6, 0x85, 0xa4, 0x2d, 0x6c, 0xeb, 0xc7,
	0x12, 0x2c, 0x1d, 0x27, 0x38, 0xee, 0x06, 0xee, 0x78, 0x9f, 0x04, 0xf8, 0x6d, 0x01, 0x3f, 0x82,
	0x65, 0x2f, 0x8d, 0x63, 0x4c, 0x19, 0xf2, 0x4e, 0x5d, 0xea, 0xc9, 0xa0, 0xab, 0x4e, 0x55, 0x79,
	0x1b, 0xc2, 0x69, 0xac, 0xc1, 0x7c, 0x9a, 0xe0, 0x18, 0x51, 0x15, 0xf3, 0x2d, 0x6e, 0x75, 0x8c,
	0x3e, 0xac, 0x88, 0x78, 0xbd, 0x30, 0x08, 0xb0, 0xc7, 0x48, 0x48, 0x55, 0xd4, 0x9f, 0xce, 0xa4,
	0x5c, 0x24, 0xb6, 0xdb, 0x70, 0x63, 0xbf, 0x91, 0xaf, 0xb5, 0x29, 0x8b, 0xc7, 0xce, 0xb2, 0x37,
	0xe5, 0xbc, 0x5b, 0x87, 0xd5, 0x6b, 0x3e, 0x33, 0x6a, 0x50, 0x3e, 0xc3, 0x63, 0xd5, 0x94, 0xfc,
	0xa7, 0x71, 0x1b, 0x6e, 0x5d, 0xb8, 0x41, 0x9a, 0x45, 0x20, 0x8d, 0xcf, 0x4a, 0x9f, 0x68, 0xd6,
	0xdf, 0x1a, 0xc0, 0x5e, 0x3a, 0xee, 0xc6, 0x61, 0xc4, 0xeb, 0xff, 0x1c, 0xf4, 0x13, 0x32, 0x60,
	0x88, 0xd0, 0x41, 0x28, 0x36, 0xb8, 0x61, 0x95, 0xf8, 0x22, 0x31, 0x15, 0x1f, 0x80, 0xd1, 0x4f,
	0xc7, 0x48, 0xb6, 0x1d, 0x0a, 0x23, 0xce, 0x2a, 0x31, 0xcb, 0xdb, 0xe5, 0x9d, 0xaa, 0x53, 0xeb,
	0xa7, 0xe3, 0xba, 0x00, 0x0e, 0xa5, 0x9f, 0xf7, 0x54, 0x4a, 0x09, 0x43, 0x51, 0x4c, 0x3c, 0x2c,
	0x9a, 0xbd, 0xea, 0xe8, 0xdc, 0xd3, 0xe5, 0x0e, 0xe3, 0x7d, 0x58, 0xf1, 0x5d, 0x12, 0x8c, 0x11,
	0xdf, 0x32, 0x20, 0x43, 0xc2, 0x54, 0xc7, 0x57, 0x85, 0x7b, 0x2f, 0x1d, 0xb7, 0xb9, 0xd3, 0xd8,
	0x81, 0x5a, 0x42, 0xe8, 0x49, 0x80, 0x0b, 0x1f, 0xca, 0xfe, 0x5f, 0x96, 0xfe, 0xec, 0x4b, 0xeb,
	0x1c, 0x96, 0x7a, 0x61, 0xd4, 0x11, 0xcc, 0x79, 0xbc, 0x0f, 0x60, 0x91, 0x85, 0x11, 0xa2, 0x6a,
	0x91, 0xec, 0x00, 0x9d, 0x85, 0x51, 0x47, 0xee, 0x5c, 0xcf, 0xc6, 0x4d, 0xb4, 0x6d, 0xe9, 0xc6,
	0x6d, 0x2b, 0x47, 0x92, 0xf7, 0xad, 0xf5, 0x31, 0xac, 0x1f, 0x60, 0xd6, 0x48, 0xe3, 0xf8, 0xc0,
	0x1d, 0x62, 0x81, 0xe2, 0xf3, 0x14, 0x27, 0x8c, 0x47, 0xcf, 0x1b, 0x8b, 0x16, 0xbb, 0x4f, 0x57,
	0x9e, 0x96, 0x6f, 0xfd, 0x3a, 0x07, 0x1b, 0x57, 0x56, 0x26, 0x51, 0x48, 0x13, 0x6c, 0x6c, 0xc1,
	0x62, 0x7e, 0x74, 0xbe, 0x16, 0x32, 0x57, 0xcb, 0x37, 0x1e, 0x42, 0x35, 0xff, 0xa0, 0x30, 0xb0,
	0x4b, 0x99, 0x53, 0xcc, 0xec, 0x7d, 0x80, 0x3e, 0x3e, 0x21, 0x54, 0xea, 0x42, 0x59, 0xe8, 0x82,
	0x2e, 0x3c, 0x5c, 0x19, 0x84, 0x68, 0x50, 0x5f, 0x82, 0x73, 0x4a, 0x34, 0xa8, 0x2f, 0x20, 0x1b,
	0x96, 0x78, 0xaa, 0xa3, 0x38, 0x8c, 0x90, 0x37, 0x38, 0x51, 0xd3, 0xf8, 0x70, 0x66, 0x66, 0x26,
	0x2d, 0xe6, 0x40, 0x7f, 0xd2, 0x6e, 0x5f, 0x64, 0x9a, 0x22, 0xd2, 0x3b, 0x2f, 0xd2, 0xfb, 0xde,
	0xcc, 0x4d, 0x32, 0x95, 0x52, 0xb2, 0x23, 0x54, 0xa1, 0x03, 0x35, 0x59, 0x40, 0xa5, 0xc5, 0xbc,
	0x6f, 0x17, 0x04, 0x99, 0x47, 0x33, 0xf7, 0x29, 0x76, 0x80, 0x53, 0x65, 0x99, 0x25, 0xfa, 0xf7,
	0x25, 0x2c, 0x8b, 0x69, 0x8e, 0x02, 0x77, 0x8c, 0x06, 0x24, 0xc0, 0x66, 0xe5, 0x5f, 0x76, 0x2b,
	0x4e, 0xad, 0xb3, 0x94, 0x16, 0xc5, 0x65, 0x13, 0xf4, 0x04, 0xc7, 0x17, 0x38, 0x46, 0x2c, 0x31,
	0x75, 0x91, 0xc1, 0x8a, 0x74, 0xf4, 0x44, 0xef, 0x9f, 0xba, 0x09, 0x72, 0x3d, 0x0f, 0x27, 0x89,
	0x09, 0xdb, 0xda, 0x4e, 0xc5, 0xd1, 0x4f, 0xdd, 0xa4, 0x2e, 0x1c, 0x7c, 0x90, 0x68, 0x88, 0x22,
	0x1c, 0x0f, 0x49, 0x92, 0x90, 0x90, 0x22, 0x86, 0x47, 0xcc, 0x5c, 0x14, 0x55, 0xac, 0xd1, 0xb0,
	0x9b, 0x03, 0x3d, 0x3c, 0x62, 0xd6, 0x77, 0x1a, 0x2c, 0xab, 0x1c, 0xdf, 0xac, 0xbb, 0x44, 0xed,
	0xf3, 0x41, 0x55, 0xba, 0xa0, 0xe7, 0x03, 0x6a, 0x3c, 0x83, 0xf5, 0x81, 0xeb, 0x61, 0xe4, 0xa6,
	0xec, 0x14, 0xc5, 0x38, 0x49, 0x03, 0x86, 0x58, 0x78, 0x86, 0xa9, 0xba, 0xb0, 0x56, 0x39, 0x5a,
	0x4f, 0xd9, 0xa9, 0x23, 0xb0, 0x1e, 0x87, 0xac, 0x9f, 0x34, 0x58, 0xc9, 0x59, 0xa8, 0x4e, 0xdd,
	0x85, 0xd5, 0x01, 0xa1, 0x6e, 0xa0, 0x34, 0x34, 0x3b, 0x50, 0xf2, 0x79, 0x57, 0x40, 0x52, 0x48,
	0xd5, 0xc1, 0x26, 0x2c, 0xf4, 0xdd, 0x20, 0x97, 0xdb, 0x39, 0x27, 0x33, 0xa7, 0x29, 0x79, 0x21,
	0xe5, 0x09, 0x41, 0xdf, 0x26, 0xe1, 0x15, 0x4a, 0x0d, 0x89, 0x7d, 0x99, 0x84, 0xd4, 0xfa, 0x41,
	0x03, 0x68, 0xc6, 0xee, 0xa5, 0xa4, 0xf9, 0x36, 0xb9, 0xff, 0xff, 0xa3, 0xce, 0xa7, 0x32, 0x4b,
	0x15, 0xbf, 0x9d, 0xe5, 0x7d, 0x00, 0xd2, 0xc5, 0xaf, 0x67, 0xcb, 0x83, 0x15, 0x4e, 0x86, 0x8b,
	0xf6, 0x0d, 0xcb, 0x54, 0x24, 0x5c, 0x9a, 0x26, 0x3c, 0xb9, 0xeb, 0xcb, 0xc5, 0xbb, 0xde, 0xfa,
	0x59, 0x83, 0xda, 0xe4, 0x14, 0x55, 0x86, 0x66, 0x4e, 0x4d, 0x84, 0xa7, 0x89, 0xf0, 0x66, 0xcf,
	0xeb, 0x24, 0x65, 0x19, 0x7f, 0x11, 0xe0, 0xd5, 0xe9, 0x28, 0xfd, 0xe7, 0xe9, 0xb0, 0xfe, 0xd4,
	0x60, 0xad, 0x1b, 0xb8, 0x6c, 0x10, 0xc6, 0xc3, 0xd7, 0x84, 0x52, 0x42, 0x4f, 0x1c, 0xec, 0x85,
	0xb1, 0xcf, 0x2f, 0xb0, 0x34, 0x4f, 0x06, 0xff, 0x69, 0xdc, 0x85, 0x0a, 0x25, 0xde, 0x59, 0x41,
	0xc9, 0x72, 0x9b, 0x63, 0x7c, 0x5f, 0x81, 0xa9, 0xd7, 0x54, 0x66, 0x4f, 0xa5, 0x6f, 0x6e, 0x46,
	0xbd, 0x85, 0x66, 0xdc, 0xfc, 0x39, 0x21, 0xeb, 0x2d, 0xc4, 0xe2, 0x8d, 0x7a, 0xcf, 0x5f, 0xa9,
	0xf7, 0xe7, 0xb0, 0x75, 0x80, 0xd9, 0xb5, 0x41, 0x66, 0xf5, 0x37, 0x61, 0xe1, 0x02, 0xc7, 0x7c,
	0x90, 0xb3, 0x57, 0xa4, 0x32, 0xad, 0x3f, 0x34, 0xd8, 0x9e, 0xbd, 0x7a, 0x72, 0x11, 0x50, 0x7c,
	0x89, 0xa6, 0xb7, 0x00, 0x8a, 0x2f, 0x5f, 0x49, 0x8f, 0x71, 0xc8, 0x39, 0xf2, 0x25, 0xc5, 0xbe,
	0xde, 0x9d, 0x19, 0xe7, 0xf5, 0xa7, 0x81, 0xdc, 0x42, 0xf4, 0xc0, 0x3d, 0x00, 0xa9, 0xb8, 0x69,
	0x92, 0xbf, 0xf3, 0x2a, 0x5c, 0x44, 0x8f, 0x13, 0xec, 0xf3, 0xab, 0x38, 0xc6, 0xe7, 0xc8, 0x4f,
	0x63, 0x97, 0x5f, 0xf1, 0x28, 0xc1, 0x9e, 0x4a, 0xfc, 0x72, 0x8c, 0xcf, 0x9b, 0xca, 0x7d, 0x84,
	0x3d, 0xeb, 0x37, 0x0d, 0xee, 0x1f, 0x60, 0xc6, 0x1b, 0xa4, 0x9e, 0x91, 0x99, 0x4e, 0xcd, 0x56,
	0x4e, 0x5d, 0xa4, 0x57, 0xcb, 0xd2, 0xcb, 0x5d, 0xd9, 0x6b, 0x37, 0x1c, 0x0c, 0x12, 0xcc, 0x54,
	0x4f, 0x28, 0x8b, 0x3f, 0x77, 0xe4, 0x7d, 0xae, 0x5e, 0x64, 0xc2, 0xb0, 0xfa, 0x00, 0xce, 0x64,
	0xed, 0x26, 0x6c, 0x38, 0x76, 0xe3, 0xd0, 0x69, 0xa2, 0xde, 0xd7, 0x5d, 0x1b, 0x1d, 0x77, 0x8e,
	0xba, 0x76, 0xa3, 0xb5, 0xdf, 0xb2, 0x9b, 0xb5, 0x77, 0x8c, 0x7b, 0x60, 0x16, 0xc1, 0x46, 0xdd,
	0x69, 0xa2, 0x76, 0xeb, 0xe0, 0x45, 0xcf, 0x6e, 0xd6, 0x4a, 0xc6, 0x1d, 0x58, 0x9b, 0x42, 0x0f,
	0xbf, 0xea, 0xb6, 0x6d, 0x0e, 0x95, 0xad, 0xef, 0x35, 0x78, 0x30, 0x2b, 0xa8, 0x62, 0xc5, 0x46,
	0x0c, 0x29, 0xe6, 0x79, 0xc5, 0x46, 0xec, 0x50, 0xb2, 0x6f, 0x5e, 0x57, 0xb1, 0x9b, 0x8e, 0x6a,
	0x56, 0x26, 0x6b, 0x03, 0xd6, 0x14, 0x91, 0x3d, 0xa9, 0x9f, 0x2a, 0xab, 0xd6, 0x87, 0x57, 0x01,
	0xce, 0x0c, 0x17, 0x95, 0x57, 0x9b, 0x52, 0xde, 0xc7, 0x8e, 0xfa, 0xdf, 0x23, 0x12, 0x77, 0x17,
	0xd6, 0xeb, 0xaf, 0xeb, 0xd7, 0xe7, 0x6d, 0x15, 0x56, 0x0a, 0xd8, 0x41, 0x6b, 0xbf, 0x57, 0xd3,
	0x8c, 0xdb, 0x50, 0x2b, 0x38, 0x9b, 0x8e, 0x7d, 0x74, 0x54, 0x2b, 0x3d, 0xfe, 0x45, 0x83, 0xe5,
	0x09, 0xf5, 0xac, 0x24, 0x4d, 0xa7, 0xfe, 0x1a, 0x39, 0xf6, 0xd1, 0x71, 0xbb, 0x77, 0xb5, 0x24,
	0x45, 0x70, 0xaa, 0x24, 0x9a, 0xb1, 0x05, 0x9b, 0x45, 0xb4, 0x6d, 0xbf, 0xb2, 0xdb, 0x85, 0xc2,
	0x94, 0xde, 0x5c, 0xde, 0xb5, 0xeb, 0x2f, 0x91, 0x63, 0xd7, 0x1b, 0x2f, 0x78, 0xd9, 0xde, 0x3c,
	0xb9, 0x77, 0xd8, 0x45, 0x1d, 0xb4, 0x77, 0x78, 0xdc, 0x39, 0xaa, 0xcd, 0xed, 0xdd, 0xf9, 0x66,
	0x63, 0xc6, 0x1f, 0xc6, 0xfe, 0xbc, 0xf8, 0xc7, 0xf8, 0xec, 0x9f, 0x00, 0x00, 0x00, 0xff, 0xff,
	0x43, 0x57, 0xf8, 0x3f, 0x79, 0x0e, 0x00, 0x00,
}
