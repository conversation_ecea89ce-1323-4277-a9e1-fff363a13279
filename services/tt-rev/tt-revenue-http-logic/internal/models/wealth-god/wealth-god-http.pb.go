// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/wealth-god-http/wealth-god-http.proto

package wealth_god_http

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type WealthMissionType int32

const (
	WealthMissionType_WEALTH_MISSION_TYPE_UNSPECIFIED     WealthMissionType = 0
	WealthMissionType_WEALTH_MISSION_TYPE_STAY_IN_GAME    WealthMissionType = 1
	WealthMissionType_WEALTH_MISSION_TYPE_SEND_TBEAN_GIFT WealthMissionType = 2
)

var WealthMissionType_name = map[int32]string{
	0: "WEALTH_MISSION_TYPE_UNSPECIFIED",
	1: "WEALTH_MISSION_TYPE_STAY_IN_GAME",
	2: "WEALTH_MISSION_TYPE_SEND_TBEAN_GIFT",
}
var WealthMissionType_value = map[string]int32{
	"WEALTH_MISSION_TYPE_UNSPECIFIED":     0,
	"WEALTH_MISSION_TYPE_STAY_IN_GAME":    1,
	"WEALTH_MISSION_TYPE_SEND_TBEAN_GIFT": 2,
}

func (x WealthMissionType) String() string {
	return proto.EnumName(WealthMissionType_name, int32(x))
}
func (WealthMissionType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_http_e7423e7185fda8d0, []int{0}
}

type GetWealthMissionInfoRequest struct {
	GodId                string   `protobuf:"bytes,1,opt,name=god_id,json=godId,proto3" json:"god_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWealthMissionInfoRequest) Reset()         { *m = GetWealthMissionInfoRequest{} }
func (m *GetWealthMissionInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetWealthMissionInfoRequest) ProtoMessage()    {}
func (*GetWealthMissionInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_http_e7423e7185fda8d0, []int{0}
}
func (m *GetWealthMissionInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWealthMissionInfoRequest.Unmarshal(m, b)
}
func (m *GetWealthMissionInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWealthMissionInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetWealthMissionInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWealthMissionInfoRequest.Merge(dst, src)
}
func (m *GetWealthMissionInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetWealthMissionInfoRequest.Size(m)
}
func (m *GetWealthMissionInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWealthMissionInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWealthMissionInfoRequest proto.InternalMessageInfo

func (m *GetWealthMissionInfoRequest) GetGodId() string {
	if m != nil {
		return m.GodId
	}
	return ""
}

type WealthMissionInfo struct {
	MissionType          uint32   `protobuf:"varint,1,opt,name=mission_type,json=missionType,proto3" json:"mission_type"`
	MissionIcon          string   `protobuf:"bytes,2,opt,name=mission_icon,json=missionIcon,proto3" json:"mission_icon"`
	MissionName          string   `protobuf:"bytes,3,opt,name=mission_name,json=missionName,proto3" json:"mission_name"`
	MissionDesc          string   `protobuf:"bytes,4,opt,name=mission_desc,json=missionDesc,proto3" json:"mission_desc"`
	MissionCntUint       string   `protobuf:"bytes,5,opt,name=mission_cnt_uint,json=missionCntUint,proto3" json:"mission_cnt_uint"`
	NeedMissionCnt       uint32   `protobuf:"varint,6,opt,name=need_mission_cnt,json=needMissionCnt,proto3" json:"need_mission_cnt"`
	CurrentCnt           uint32   `protobuf:"varint,7,opt,name=current_cnt,json=currentCnt,proto3" json:"current_cnt"`
	IsReachLimit         bool     `protobuf:"varint,8,opt,name=is_reach_limit,json=isReachLimit,proto3" json:"is_reach_limit"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WealthMissionInfo) Reset()         { *m = WealthMissionInfo{} }
func (m *WealthMissionInfo) String() string { return proto.CompactTextString(m) }
func (*WealthMissionInfo) ProtoMessage()    {}
func (*WealthMissionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_http_e7423e7185fda8d0, []int{1}
}
func (m *WealthMissionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WealthMissionInfo.Unmarshal(m, b)
}
func (m *WealthMissionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WealthMissionInfo.Marshal(b, m, deterministic)
}
func (dst *WealthMissionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WealthMissionInfo.Merge(dst, src)
}
func (m *WealthMissionInfo) XXX_Size() int {
	return xxx_messageInfo_WealthMissionInfo.Size(m)
}
func (m *WealthMissionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WealthMissionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WealthMissionInfo proto.InternalMessageInfo

func (m *WealthMissionInfo) GetMissionType() uint32 {
	if m != nil {
		return m.MissionType
	}
	return 0
}

func (m *WealthMissionInfo) GetMissionIcon() string {
	if m != nil {
		return m.MissionIcon
	}
	return ""
}

func (m *WealthMissionInfo) GetMissionName() string {
	if m != nil {
		return m.MissionName
	}
	return ""
}

func (m *WealthMissionInfo) GetMissionDesc() string {
	if m != nil {
		return m.MissionDesc
	}
	return ""
}

func (m *WealthMissionInfo) GetMissionCntUint() string {
	if m != nil {
		return m.MissionCntUint
	}
	return ""
}

func (m *WealthMissionInfo) GetNeedMissionCnt() uint32 {
	if m != nil {
		return m.NeedMissionCnt
	}
	return 0
}

func (m *WealthMissionInfo) GetCurrentCnt() uint32 {
	if m != nil {
		return m.CurrentCnt
	}
	return 0
}

func (m *WealthMissionInfo) GetIsReachLimit() bool {
	if m != nil {
		return m.IsReachLimit
	}
	return false
}

// 获取财神任务信息
type GetWealthMissionInfoResponse struct {
	WealthMissionInfoList []*WealthMissionInfo `protobuf:"bytes,1,rep,name=wealth_mission_info_list,json=wealthMissionInfoList,proto3" json:"wealth_mission_info_list"`
	XXX_NoUnkeyedLiteral  struct{}             `json:"-"`
	XXX_unrecognized      []byte               `json:"-"`
	XXX_sizecache         int32                `json:"-"`
}

func (m *GetWealthMissionInfoResponse) Reset()         { *m = GetWealthMissionInfoResponse{} }
func (m *GetWealthMissionInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetWealthMissionInfoResponse) ProtoMessage()    {}
func (*GetWealthMissionInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_http_e7423e7185fda8d0, []int{2}
}
func (m *GetWealthMissionInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWealthMissionInfoResponse.Unmarshal(m, b)
}
func (m *GetWealthMissionInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWealthMissionInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetWealthMissionInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWealthMissionInfoResponse.Merge(dst, src)
}
func (m *GetWealthMissionInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetWealthMissionInfoResponse.Size(m)
}
func (m *GetWealthMissionInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWealthMissionInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWealthMissionInfoResponse proto.InternalMessageInfo

func (m *GetWealthMissionInfoResponse) GetWealthMissionInfoList() []*WealthMissionInfo {
	if m != nil {
		return m.WealthMissionInfoList
	}
	return nil
}

// uri: /tt-revenue-http-logic/wealth_god/get_wealth_god_box_reward_list
// 获取财神宝箱奖励列表
type GetWealthGodBoxRewardListRequest struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page"`
	PageSize             uint32   `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWealthGodBoxRewardListRequest) Reset()         { *m = GetWealthGodBoxRewardListRequest{} }
func (m *GetWealthGodBoxRewardListRequest) String() string { return proto.CompactTextString(m) }
func (*GetWealthGodBoxRewardListRequest) ProtoMessage()    {}
func (*GetWealthGodBoxRewardListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_http_e7423e7185fda8d0, []int{3}
}
func (m *GetWealthGodBoxRewardListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWealthGodBoxRewardListRequest.Unmarshal(m, b)
}
func (m *GetWealthGodBoxRewardListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWealthGodBoxRewardListRequest.Marshal(b, m, deterministic)
}
func (dst *GetWealthGodBoxRewardListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWealthGodBoxRewardListRequest.Merge(dst, src)
}
func (m *GetWealthGodBoxRewardListRequest) XXX_Size() int {
	return xxx_messageInfo_GetWealthGodBoxRewardListRequest.Size(m)
}
func (m *GetWealthGodBoxRewardListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWealthGodBoxRewardListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWealthGodBoxRewardListRequest proto.InternalMessageInfo

func (m *GetWealthGodBoxRewardListRequest) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetWealthGodBoxRewardListRequest) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetWealthGodBoxRewardListResponse struct {
	RewardList           []*WealthGodBoxRewardItem `protobuf:"bytes,1,rep,name=reward_list,json=rewardList,proto3" json:"reward_list"`
	HasMore              bool                      `protobuf:"varint,2,opt,name=has_more,json=hasMore,proto3" json:"has_more"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetWealthGodBoxRewardListResponse) Reset()         { *m = GetWealthGodBoxRewardListResponse{} }
func (m *GetWealthGodBoxRewardListResponse) String() string { return proto.CompactTextString(m) }
func (*GetWealthGodBoxRewardListResponse) ProtoMessage()    {}
func (*GetWealthGodBoxRewardListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_http_e7423e7185fda8d0, []int{4}
}
func (m *GetWealthGodBoxRewardListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWealthGodBoxRewardListResponse.Unmarshal(m, b)
}
func (m *GetWealthGodBoxRewardListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWealthGodBoxRewardListResponse.Marshal(b, m, deterministic)
}
func (dst *GetWealthGodBoxRewardListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWealthGodBoxRewardListResponse.Merge(dst, src)
}
func (m *GetWealthGodBoxRewardListResponse) XXX_Size() int {
	return xxx_messageInfo_GetWealthGodBoxRewardListResponse.Size(m)
}
func (m *GetWealthGodBoxRewardListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWealthGodBoxRewardListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWealthGodBoxRewardListResponse proto.InternalMessageInfo

func (m *GetWealthGodBoxRewardListResponse) GetRewardList() []*WealthGodBoxRewardItem {
	if m != nil {
		return m.RewardList
	}
	return nil
}

func (m *GetWealthGodBoxRewardListResponse) GetHasMore() bool {
	if m != nil {
		return m.HasMore
	}
	return false
}

type WealthGodBoxRewardItem struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	IconUrl              string   `protobuf:"bytes,2,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url"`
	Title                string   `protobuf:"bytes,3,opt,name=title,proto3" json:"title"`
	SubTitle             string   `protobuf:"bytes,4,opt,name=sub_title,json=subTitle,proto3" json:"sub_title"`
	IsRare               bool     `protobuf:"varint,5,opt,name=is_rare,json=isRare,proto3" json:"is_rare"`
	TimeStr              string   `protobuf:"bytes,6,opt,name=time_str,json=timeStr,proto3" json:"time_str"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WealthGodBoxRewardItem) Reset()         { *m = WealthGodBoxRewardItem{} }
func (m *WealthGodBoxRewardItem) String() string { return proto.CompactTextString(m) }
func (*WealthGodBoxRewardItem) ProtoMessage()    {}
func (*WealthGodBoxRewardItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_http_e7423e7185fda8d0, []int{5}
}
func (m *WealthGodBoxRewardItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WealthGodBoxRewardItem.Unmarshal(m, b)
}
func (m *WealthGodBoxRewardItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WealthGodBoxRewardItem.Marshal(b, m, deterministic)
}
func (dst *WealthGodBoxRewardItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WealthGodBoxRewardItem.Merge(dst, src)
}
func (m *WealthGodBoxRewardItem) XXX_Size() int {
	return xxx_messageInfo_WealthGodBoxRewardItem.Size(m)
}
func (m *WealthGodBoxRewardItem) XXX_DiscardUnknown() {
	xxx_messageInfo_WealthGodBoxRewardItem.DiscardUnknown(m)
}

var xxx_messageInfo_WealthGodBoxRewardItem proto.InternalMessageInfo

func (m *WealthGodBoxRewardItem) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *WealthGodBoxRewardItem) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *WealthGodBoxRewardItem) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *WealthGodBoxRewardItem) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *WealthGodBoxRewardItem) GetIsRare() bool {
	if m != nil {
		return m.IsRare
	}
	return false
}

func (m *WealthGodBoxRewardItem) GetTimeStr() string {
	if m != nil {
		return m.TimeStr
	}
	return ""
}

func init() {
	proto.RegisterType((*GetWealthMissionInfoRequest)(nil), "wealth_god_http.GetWealthMissionInfoRequest")
	proto.RegisterType((*WealthMissionInfo)(nil), "wealth_god_http.WealthMissionInfo")
	proto.RegisterType((*GetWealthMissionInfoResponse)(nil), "wealth_god_http.GetWealthMissionInfoResponse")
	proto.RegisterType((*GetWealthGodBoxRewardListRequest)(nil), "wealth_god_http.GetWealthGodBoxRewardListRequest")
	proto.RegisterType((*GetWealthGodBoxRewardListResponse)(nil), "wealth_god_http.GetWealthGodBoxRewardListResponse")
	proto.RegisterType((*WealthGodBoxRewardItem)(nil), "wealth_god_http.WealthGodBoxRewardItem")
	proto.RegisterEnum("wealth_god_http.WealthMissionType", WealthMissionType_name, WealthMissionType_value)
}

func init() {
	proto.RegisterFile("tt/quicksilver/wealth-god-http/wealth-god-http.proto", fileDescriptor_wealth_god_http_e7423e7185fda8d0)
}

var fileDescriptor_wealth_god_http_e7423e7185fda8d0 = []byte{
	// 605 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x7c, 0x94, 0xef, 0x4e, 0xdb, 0x3c,
	0x18, 0xc5, 0xdf, 0x14, 0x68, 0x83, 0x0b, 0xa5, 0x58, 0x2f, 0x5b, 0x26, 0x26, 0x51, 0x02, 0x12,
	0xd5, 0x24, 0x40, 0xda, 0xb8, 0x81, 0x02, 0xa1, 0x44, 0xa2, 0x1d, 0x72, 0x82, 0x10, 0xdb, 0x07,
	0x2b, 0x24, 0xa6, 0xb5, 0x96, 0xd8, 0xc5, 0x76, 0xc6, 0x60, 0x17, 0xb0, 0x5d, 0xcb, 0x2e, 0x61,
	0x57, 0x37, 0xd9, 0x49, 0xab, 0x52, 0xba, 0x7d, 0x6a, 0x9f, 0xe3, 0x5f, 0x8e, 0xff, 0x3c, 0xc7,
	0x06, 0x47, 0x4a, 0x1d, 0xde, 0xe7, 0x34, 0xfe, 0x22, 0x69, 0xfa, 0x95, 0x88, 0xc3, 0x07, 0x12,
	0xa5, 0x6a, 0xb8, 0x3f, 0xe0, 0xc9, 0xfe, 0x50, 0xa9, 0xd1, 0x6c, 0x7d, 0x30, 0x12, 0x5c, 0x71,
	0xb8, 0x56, 0xc8, 0x78, 0xc0, 0x13, 0xac, 0x65, 0xf7, 0x08, 0x6c, 0x76, 0x89, 0xba, 0x36, 0x6a,
	0x8f, 0x4a, 0x49, 0x39, 0xf3, 0xd9, 0x1d, 0x47, 0xe4, 0x3e, 0x27, 0x52, 0xc1, 0x0d, 0x50, 0xd5,
	0x28, 0x4d, 0x1c, 0xab, 0x65, 0xb5, 0x97, 0xd1, 0xd2, 0x80, 0x27, 0x7e, 0xe2, 0xfe, 0xae, 0x80,
	0xf5, 0x17, 0xdf, 0xc0, 0x6d, 0xb0, 0x92, 0x15, 0x25, 0x56, 0x8f, 0x23, 0x62, 0x3e, 0x59, 0x45,
	0xf5, 0x52, 0x0b, 0x1f, 0x47, 0x64, 0x1a, 0xa1, 0x31, 0x67, 0x4e, 0xc5, 0xb8, 0x8e, 0x11, 0x3f,
	0xe6, 0x6c, 0x1a, 0x61, 0x51, 0x46, 0x9c, 0x85, 0x67, 0x48, 0x3f, 0xca, 0x9e, 0xb9, 0x24, 0x44,
	0xc6, 0xce, 0xe2, 0x33, 0xe4, 0x94, 0xc8, 0x18, 0xb6, 0x41, 0x73, 0x8c, 0xc4, 0x4c, 0xe1, 0x9c,
	0x32, 0xe5, 0x2c, 0x19, 0xac, 0x51, 0xea, 0x27, 0x4c, 0x5d, 0x51, 0xa6, 0x34, 0xc9, 0x08, 0x49,
	0xf0, 0x14, 0xee, 0x54, 0xcd, 0xca, 0x1b, 0x5a, 0xef, 0x4d, 0x68, 0xb8, 0x05, 0xea, 0x71, 0x2e,
	0x04, 0x61, 0xca, 0x40, 0x35, 0x03, 0x81, 0x52, 0xd2, 0xc0, 0x2e, 0x68, 0x50, 0x89, 0x05, 0x89,
	0xe2, 0x21, 0x4e, 0x69, 0x46, 0x95, 0x63, 0xb7, 0xac, 0xb6, 0x8d, 0x56, 0xa8, 0x44, 0x5a, 0xbc,
	0xd0, 0x9a, 0xfb, 0x1d, 0xbc, 0x9d, 0x7f, 0xe4, 0x72, 0xc4, 0x99, 0x24, 0xf0, 0x33, 0x70, 0xca,
	0x2e, 0x4d, 0x8e, 0x8a, 0xdd, 0x71, 0x9c, 0x52, 0xa9, 0x1c, 0xab, 0xb5, 0xd0, 0xae, 0xbf, 0x77,
	0x0f, 0x66, 0xda, 0x78, 0xf0, 0xd2, 0x6d, 0xe3, 0x61, 0x56, 0xba, 0xa0, 0x52, 0xb9, 0x01, 0x68,
	0x4d, 0x26, 0xef, 0xf2, 0xe4, 0x98, 0x7f, 0x43, 0xe4, 0x21, 0x12, 0x89, 0x1e, 0x1c, 0x37, 0x1d,
	0x82, 0xc5, 0x51, 0x34, 0x18, 0xf7, 0xcf, 0xfc, 0x87, 0x9b, 0x60, 0x59, 0xff, 0x62, 0x49, 0x9f,
	0x88, 0xe9, 0xda, 0x2a, 0xb2, 0xb5, 0x10, 0xd0, 0x27, 0xe2, 0xfe, 0xb4, 0xc0, 0xf6, 0x3f, 0x5c,
	0xcb, 0x7d, 0x9d, 0x83, 0xba, 0x30, 0xea, 0xf4, 0x56, 0xf6, 0xfe, 0xb2, 0x95, 0x69, 0x17, 0x5f,
	0x91, 0x0c, 0x01, 0x31, 0x71, 0x84, 0x6f, 0x80, 0x3d, 0x8c, 0x24, 0xce, 0xb8, 0x28, 0xd6, 0x62,
	0xa3, 0xda, 0x30, 0x92, 0x3d, 0x2e, 0x88, 0xfb, 0xcb, 0x02, 0xaf, 0xe6, 0x3b, 0xc0, 0x06, 0xa8,
	0x94, 0x39, 0x5e, 0x40, 0x15, 0x9a, 0x68, 0x17, 0x9d, 0x41, 0x9c, 0x8b, 0xb4, 0xcc, 0x61, 0x4d,
	0xd7, 0x57, 0x22, 0x85, 0xff, 0x83, 0x25, 0x45, 0x55, 0x3a, 0x0e, 0x5f, 0x51, 0xe8, 0x33, 0x90,
	0xf9, 0x2d, 0x2e, 0x46, 0x8a, 0xcc, 0xd9, 0x32, 0xbf, 0x0d, 0xcd, 0xe0, 0x6b, 0x50, 0xd3, 0xbd,
	0x8f, 0x04, 0x31, 0x39, 0xb3, 0x51, 0x95, 0x4a, 0x14, 0x09, 0xa2, 0xa7, 0x51, 0x34, 0x23, 0x58,
	0x2a, 0x61, 0x72, 0xb5, 0x8c, 0x6a, 0xba, 0x0e, 0x94, 0x78, 0xf7, 0xc3, 0x9a, 0xb9, 0x46, 0xe6,
	0x8e, 0xec, 0x80, 0xad, 0x6b, 0xaf, 0x73, 0x11, 0x9e, 0xe3, 0x9e, 0x1f, 0x04, 0xfe, 0xc7, 0x3e,
	0x0e, 0x6f, 0x2e, 0x3d, 0x7c, 0xd5, 0x0f, 0x2e, 0xbd, 0x13, 0xff, 0xcc, 0xf7, 0x4e, 0x9b, 0xff,
	0xc1, 0x5d, 0xd0, 0x9a, 0x07, 0x05, 0x61, 0xe7, 0x06, 0xfb, 0x7d, 0xdc, 0xed, 0xf4, 0xbc, 0xa6,
	0x05, 0xf7, 0xc0, 0xce, 0x5c, 0xca, 0xeb, 0x9f, 0xe2, 0xf0, 0xd8, 0xeb, 0xf4, 0x71, 0xd7, 0x3f,
	0x0b, 0x9b, 0x95, 0xe3, 0xf5, 0x4f, 0x6b, 0x33, 0x0f, 0xc6, 0x6d, 0xd5, 0xbc, 0x18, 0x1f, 0xfe,
	0x04, 0x00, 0x00, 0xff, 0xff, 0x28, 0x21, 0xca, 0xc0, 0x69, 0x04, 0x00, 0x00,
}
