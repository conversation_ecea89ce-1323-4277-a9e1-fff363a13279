package mgr

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkapresent"
	userPresentpb "golang.52tt.com/protocol/services/userpresent"
	youknowwhopb "golang.52tt.com/protocol/services/youknowwho"
	"golang.52tt.com/services/tt-rev/revenue-ext-game/internal/cache"
	"golang.52tt.com/services/tt-rev/revenue-ext-game/internal/entity"
    "regexp"
    "time"
)

func (m *Mgr) GenUserAvatarUrl(userName string) string {
	if userName == "" {
		return ""
	}
	return fmt.Sprintf(m.bc.GetAvatarUrlFormat(), userName)
}

func (m *Mgr) checkIfMatchGift(ctx context.Context, gameType, giftId uint32) bool {
	cfg, err := m.bc.GetGameAppConf(gameType)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkIfMatchGift fail to GetGameAppConf. gameType:%d, err:%v", gameType, err)
		return false
	}

	for _, gift := range cfg.GameGiftList {
		if giftId == gift.GiftId {
			return true
		}
	}

	return false
}

func (m *Mgr) PresentEventHandle(ctx context.Context, eventMsg *kafkapresent.PresentEvent) error {
	// 通过channelId检查 是否存在推送任务
	haveTask, gameType, err := m.cache.GetChannelTaskDataReportInfo(ctx, eventMsg.GetChannelId(), cache.TaskTypeGift)
	if err != nil {
		return err
	}

	if !haveTask || gameType == 0 {
		return nil
	}

	if !m.checkIfMatchGift(ctx, gameType, eventMsg.GetItemId()) {
		// 不需要处理
		return nil
	}

	account, nickname := eventMsg.GetFromUkwAccount(), eventMsg.GetFromUkwNickname()
	if account == "" {
		user, err := m.rpcClient.AccountCli.GetUser(ctx, eventMsg.GetUid())
		if err != nil {
			log.ErrorWithCtx(ctx, "TaskDataReport fail to GetUser, evenMsg:%+v, err:%v", eventMsg, err)
		}

		account, nickname = user.GetUsername(), user.GetNickname()
	}

	price := uint32(0)
	if eventMsg.GetPriceType() == uint32(userPresentpb.PresentPriceType_PRESENT_PRICE_TBEAN) {
		price = eventMsg.GetPrice()
	}
	
	// 组装payload
	dataPayload := []*entity.GiftData{
		{
			MsgId:     eventMsg.GetOrderId(),
			SecOpenId: account,
			SecGiftId: fmt.Sprint(eventMsg.GetItemId()),
			GiftNum:   eventMsg.GetItemCount(),
			GiftValue: price,
			AvatarUrl: m.GenUserAvatarUrl(account),
			Nickname:  nickname,
			Timestamp: time.Now().UnixNano() / 1e6,
		},
	}

	err = m.DataReportGift(ctx, eventMsg.GetChannelId(), gameType, dataPayload)
	if err != nil {
		log.ErrorWithCtx(ctx, "TaskDataReport fail, evenMsg:%+v, err:%v", eventMsg, err)
		return err
	}

	return nil
}

func (m *Mgr) checkIfMatchComment(ctx context.Context, gameType uint32, comment string) bool {
	cfg, err := m.bc.GetGameAppConf(gameType)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkIfMatchComment fail to GetGameAppConf. gameType:%d, err:%v", gameType, err)
		return false
	}

	for _, pattern := range cfg.CommentList {
		/*if comment == pattern {
			return true
		}*/
        // 支持正则匹配
        if ok, _ := regexp.MatchString(pattern, comment); ok {
            return true
        }
	}

	return false
}

func (m *Mgr) ChannelMsgEventHandle(ctx context.Context, channelId, uid uint32, text string) error {
	// 通过channelId检查 是否存在推送任务
	haveTask, gameType, err := m.cache.GetChannelTaskDataReportInfo(ctx, channelId, cache.TaskTypeComment)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelMsgEventHandle fail to GetChannelTaskDataReportInfo. channelId:%d, uid:%d, err:%v", channelId, uid, err)
		return err
	}

	if !haveTask || gameType == 0 {
		return nil
	}

	if !m.checkIfMatchComment(ctx, gameType, text) {
		// 不需要处理
		return nil
	}

	resp, err := m.rpcClient.UserProfileCli.GetUserProfileV2(ctx, uid, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelMsgEventHandle fail to GetUserProfileV2. channelId:%d, uid:%d, err:%v", channelId, uid, err)
		//return err
	}

	// test
	/*if info, ok := mapCampInfo[text]; ok {
		err = m.SetUserCamp(ctx, &pb.SetUserCampReq{
			GameType:  pb.ExtGameType(gameType),
			ChannelId: channelId,
			RoundId:   fmt.Sprintf("%d_%d", channelId, gameType),
			Uid:       uid,
			RecodeUid: resp.GetUid(),
			Camp:      info.GetButtonText(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "ChannelMsgEventHandle fail to SetUserCamp. channelId:%d, uid:%d, err:%v", channelId, uid, err)
		}
	}*/

	now := time.Now()
	return m.DataReportComment(ctx, channelId, gameType, []*entity.Comment{
		{
			MsgId:     fmt.Sprint(now.UnixNano()),
			SecOpenId: resp.GetAccount(),
			Content:   text,
			AvatarUrl: m.GenUserAvatarUrl(resp.GetAccount()),
			Nickname:  resp.GetNickname(),
			Timestamp: now.UnixNano() / 1e6,
		},
	})
}

// ChannelUserWantPlayDataClean 清理房间“我想玩”数据
func (m *Mgr) ChannelUserWantPlayDataClean(channelId uint32) error {
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	gameTypeList := m.bc.GetGameTypeList()
	if len(gameTypeList) == 0 {
		return nil
	}
	err := m.cache.DelChannelGameWantPlaySetLen(ctx, channelId, gameTypeList)
	if err != nil {
		log.Errorf("ChannelUserWantPlayDataClean fail,channelId:%d,err:%+v", channelId, err)
		return err
	}

	log.Infof("ChannelUserWantPlayDataClean success,cid:%d,gameTypeList:%v", channelId, gameTypeList)
	return nil
}

func (m *Mgr) UkwEventHandle(ctx context.Context, eventMsg *youknowwhopb.UKWChangeStatusMsg) error {
	channelId := eventMsg.GetChannelId()
	gameType, err := m.GetChannelMountGame(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "UkwEventHandle fail to GetChannelMountGame, evenMsg:%+v,err:%v", eventMsg, err)
		return err
	}

	if gameType == 0 {
		return nil
	}

	_, opCfg, err := m.cache.GetChannelGameOpCfg(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "UkwEventHandle fail to GetChannelMountGame, evenMsg:%+v,err:%v", eventMsg, err)
		return err
	}

	camp, err := m.cache.GetChannelUserCamp(ctx, channelId, gameType, eventMsg.GetUid(), opCfg.GetRoundId())
	if err != nil {
		log.ErrorWithCtx(ctx, "UkwEventHandle fail to GetChannelUserCamp, evenMsg:%+v,err:%v", eventMsg, err)
		return err
	}

	err = m.userCampNotify(ctx, eventMsg.GetUid(), channelId, gameType, opCfg.GetRoundId(), camp)
	if err != nil {
		log.ErrorWithCtx(ctx, "UkwEventHandle fail to userCampNotify, evenMsg:%+v,err:%v", eventMsg, err)
		return err
	}

	return nil
}

func (m *Mgr) ChannelLiveEventHandle(ctx context.Context, channelId, uid uint32) error {
	exist, gameType, err := m.cache.GetChannelExtGameMountStatus(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelLiveEventHandle fail to GetChannelExtGameMountStatus, channelId:%v, gameType:%v, err:%v", channelId, gameType, err)
		return err
	}

	if !exist {
		// 没有挂载的房间的异步事件，不处理
		return nil
	}

	err = m.DoChannelUnmountGameHandle(ctx, channelId, gameType, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelLiveEventHandle fail to DoChannelUnmountGameHandle, channelId:%v, gameType:%v, err:%v", channelId, gameType, err)
		return err
	}

	log.InfoWithCtx(ctx, "ChannelLiveEventHandle channelId:%v, gameType:%v, uid:%v", channelId, gameType, uid)
	return nil
}
