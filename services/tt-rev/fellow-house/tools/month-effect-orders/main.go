package main

import (
    "context"
    "crypto/tls"
    "encoding/json"
    "flag"
    "fmt"
    "github.com/go-gomail/gomail"
    "github.com/tealeg/xlsx"
    mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/services/tt-rev/fellow-house/tools/month-effect-orders/store"
    "os"
    "path"
    "sort"
    "time"
)

const daySec = 86400

var st *store.Store
var sc *ServiceConfigT

type ServiceConfigT struct {
    MysqlConfig *mysqlConnect.MysqlConfig `json:"mysql"`
    MailsTo     []string                  `json:"mails_to"`
}

func (sc *ServiceConfigT) Parse(configFile string) (err error) {
    defer func() {
        if e := recover(); e != nil {
            err = fmt.Errorf("Failed to parse config: %v \n", e)
        }
    }()

    data, err := os.ReadFile(configFile)
    if err != nil {
        return err
    }
    err = json.Unmarshal(data, &sc)
    if err != nil {
        return err
    }

    log.Infof("ServiceConfigT::MysqlConfig:%+v\n", sc.MysqlConfig)
    return
}

func main() {
    sc = &ServiceConfigT{}
    var filePath string
    flag.StringVar(&filePath, "config", "", "config file path")
    flag.Parse()

    if filePath == "" {
        log.Errorf("config file path is empty")
        return
    }

    err := sc.Parse(filePath)
    if err != nil {
        log.Errorf("Parse fail. err:%v", err)
        return
    }

    ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
    defer cancel()

    st, err = store.NewStore(ctx, sc.MysqlConfig)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to NewStore, %+v, err:%v", sc.MysqlConfig, err)
        return
    }
    defer st.Close()

    now := time.Now()
    endTime := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
    beginTime := endTime.AddDate(0, -1, 0)

    // 生成本周期的有效订单
    err = GenMonthEffectOrders(beginTime, endTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "GenMonthEffectOrders fail. err:%v", err)
        return
    }

    // 输出对账报表
    err = SendReconcileReport(beginTime, endTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendReconcileReport fail. err:%v", err)
        return
    }
}

// GenMonthEffectOrders 生成本周期的有效订单
func GenMonthEffectOrders(beginTime, endTime time.Time) error {
    ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
    defer cancel()

    // 1. 查上周期的有效订单表
    lastMonthOrders, err := st.GetEffectOrders(ctx, beginTime.AddDate(0, -1, 0), beginTime, true)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetEffectOrders fail. err:%v", err)
        return err
    }

    // 2. 联表获取本周期的有效订单
    thisMonthOrders, err := st.GetOrdersFromAwardLog(ctx, beginTime, endTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetOrdersFromAwardLog fail. err:%v", err)
        return err
    }

    // 3. 合并1、2的订单并写入本周期的有效订单表
    orders := append(lastMonthOrders, thisMonthOrders...)
    limit := 100
    // 每次插入100条
    for i := 0; i < len(orders); i += limit {
        end := i + limit
        if end > len(orders) {
            end = len(orders)
        }

        insertOrders := orders[i:end]
        err = st.InsertMonthEffectOrders(ctx, beginTime, insertOrders)
        if err != nil {
            log.ErrorWithCtx(ctx, "InsertMonthEffectOrders fail. err:%v", err)
            return err
        }
    }

    log.InfoWithCtx(ctx, "GenMonthEffectOrders success. len:%d", len(orders))
    return nil
}

// SendReconcileReport 发送对账报表
func SendReconcileReport(begin, end time.Time) error {
    ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
    defer cancel()

    // 查本周期的有效订单表
    orders, err := st.GetEffectOrders(ctx, begin, begin, false)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetEffectOrders fail. err:%v", err)
        return err
    }

    details := genDetailList(orders, begin, end)

    // 创建报表
    file := xlsx.NewFile()

    // 生成汇总报表
    err = genSummarySheet(file, details, begin, end)
    if err != nil {
        log.ErrorWithCtx(ctx, "genSummarySheet fail. err:%v", err)
        return err
    }

    // 生成明细报表
    err = genDetailSheet(file, details, begin, end)
    if err != nil {
        log.ErrorWithCtx(ctx, "genDetailSheet fail. err:%v", err)
        return err
    }

    filePath := fmt.Sprintf("%s/month_effect_orders_%s_%s.xlsx", path.Dir(os.Args[0]), begin.Format("2006-01-02"), end.Format("2006-01-02"))
    err = file.Save(filePath)
    if err != nil {
        log.ErrorWithCtx(ctx, "File save fail, err: %v\n", err)
        return err
    }

    // send email
    subject := fmt.Sprintf("挚友小屋财务数据 %s-%s", begin.Format("2006-01-02 15点"), end.Format("2006-01-02 15点"))
    err = SendMail(filePath, subject, sc.MailsTo)
    if err != nil {
        log.ErrorWithCtx(ctx, "Failed to SendMail %v\n", err)
        return err
    }

    log.InfoWithCtx(ctx, "SendReconcileReport success. begin:%v, end:%v", begin, end)
    return nil
}

type Detail struct {
    Id            uint32
    OrderId       string
    Uid           uint32
    CpUid         uint32
    BuyTime       time.Time
    BeginTime     time.Time // 发放前过期时间
    EndTime       time.Time // 发放后过期时间
    CTime         time.Time
    ProductId     uint32
    EffectiveDays uint32
    Amount        uint32
    Price         float64
    TotalPrice    uint32
    BeginDays     float64
    BuyDays       float64
    UseDays       float64
    EndDays       float64
}

type Recovery struct {
    Id        uint32
    OrderId   string
    Uid       uint32
    CpUid     uint32
    ProductId uint32
    BeginTime time.Time // 回收前过期时间
    EndTime   time.Time // 回收后过期时间
    CTime     time.Time
}

func sortUid(a, b uint32) (uint32, uint32) {
    if a > b {
        return b, a
    }
    return a, b
}

func genRecoveryKey(uid, cpUid, houseId uint32) string {
    a, b := sortUid(uid, cpUid)
    return fmt.Sprintf("%d_%d_%d", a, b, houseId)
}

func genDetailList(orders []*store.Order, begin, end time.Time) []*Detail {
    // 根据订单记录创建时间倒序
    sort.SliceStable(orders, func(i, j int) bool {
        return orders[j].Id < orders[i].Id
    })

    details := make([]*Detail, 0)
    house2RecoveryList := make(map[string][]*Recovery)
    for _, order := range orders {
        if order.AwardDurationSec <= 0 {
            recovery := &Recovery{
                Id:        order.Id,
                OrderId:   order.OrderId,
                Uid:       order.UidA,
                CpUid:     order.UidB,
                ProductId: order.HouseId,
                BeginTime: order.BeginTime,
                EndTime:   order.ExpireTime,
                CTime:     order.OrderCtime,
            }

            key := genRecoveryKey(recovery.Uid, recovery.CpUid, recovery.ProductId)
            if _, ok := house2RecoveryList[key]; !ok {
                house2RecoveryList[key] = make([]*Recovery, 0)
            }
            house2RecoveryList[key] = append(house2RecoveryList[key], recovery)
            continue
        }

        detail := &Detail{
            Id:        order.Id,
            OrderId:   order.OrderId,
            Uid:       order.UidA,
            CpUid:     order.UidB,
            BuyTime:   order.AwardTime,
            BeginTime: order.BeginTime,
            EndTime:   order.ExpireTime,
            ProductId: order.HouseId,
            Amount:    order.Amount,
            CTime:     order.OrderCtime,
        }

        if order.OrderCtime.After(begin) && order.OrderCtime.Before(end) {
            detail.BuyDays = float64(order.AwardDurationSec / daySec)
            detail.TotalPrice = order.TotalPrice
        }

        if order.Amount > 0 {
            detail.EffectiveDays = uint32(order.AwardDurationSec) / order.Amount / daySec
            if detail.EffectiveDays > 0 {
                detail.Price = float64(order.TotalPrice) / float64(order.Amount) / float64(detail.EffectiveDays)
            }
        }

        details = append(details, detail)
    }

    // 根据回收记录矫正明细
    correctDetailByRecovery(details, house2RecoveryList, begin, end)

    for _, detail := range details {
        detail.UseDays += calcUnionDay(detail.BeginTime, detail.EndTime, begin, end)
        // 跨过账期开始时间
        if detail.CTime.Before(begin) && detail.EndTime.After(begin) {
            detail.BeginDays += calcRemainDays(detail.BeginTime, detail.EndTime, begin)
        }
        // 跨过账期结束时间
        if detail.CTime.Before(end) && detail.EndTime.After(end) {
            detail.EndDays += calcRemainDays(detail.BeginTime, detail.EndTime, end)
        }
    }

    return details
}

func genDetailSheet(file *xlsx.File, details []*Detail, begin, end time.Time) error {
    sheet, err := file.AddSheet("明细")
    if err != nil {
        log.Errorf("genDetailSheet fail to AddSheet. err:%v", err)
        return err
    }

    // 账期	订单id	uid	购买日期	商品id	商品有效天数	购买数量	单价	期初剩余价值	本期购买价值	本月核销价值	本月剩余价值	期初剩余天数	本期购买天数	本月核销天数	本月剩余天数
    sheet.AddRow().WriteSlice(&[]string{"账期", "订单id", "uid", "购买日期", "商品id", "商品有效天数", "购买数量", "单价",
        "期初剩余价值", "本期购买价值", "本月核销价值", "本月剩余价值", "期初剩余天数", "本期购买天数", "本月核销天数", "本月剩余天数"}, -1)
    for _, detail := range details {
        row := sheet.AddRow()
        row.AddCell().SetValue(begin.Format("2006-01"))
        row.AddCell().SetValue(detail.OrderId)
        row.AddCell().SetValue(detail.Uid)
        row.AddCell().SetValue(detail.BuyTime.Format("2006-01-02"))
        row.AddCell().SetValue(detail.ProductId)
        row.AddCell().SetValue(detail.EffectiveDays)
        row.AddCell().SetValue(detail.Amount)
        row.AddCell().SetValue(detail.Price)
        row.AddCell().SetValue(detail.BeginDays * float64(detail.Price))
        row.AddCell().SetValue(detail.TotalPrice)
        row.AddCell().SetValue(detail.UseDays * float64(detail.Price))
        row.AddCell().SetValue(detail.EndDays * float64(detail.Price))
        row.AddCell().SetValue(detail.BeginDays)
        row.AddCell().SetValue(detail.BuyDays)
        row.AddCell().SetValue(detail.UseDays)
        row.AddCell().SetValue(detail.EndDays)
    }

    return nil
}

// 根据回收记录矫正明细
// 通过对已经按照订单创建时间倒序的订单列表，遍历回收记录，对订单进行回溯矫正，使得订单的使用天数正确
func correctDetailByRecovery(details []*Detail, house2RecoveryList map[string][]*Recovery, begin, end time.Time) {
    if len(house2RecoveryList) == 0 || len(details) == 0 {
        return
    }

    for _, detail := range details {
        key := genRecoveryKey(detail.Uid, detail.CpUid, detail.ProductId)
        recoveryList, ok := house2RecoveryList[key]
        if !ok {
            continue
        }

        for _, recovery := range recoveryList {
            // 回收后的时间 应在 回收前的时间之前
            // 否则不处理
            if !recovery.EndTime.Before(recovery.BeginTime) {
                continue
            }

            // 回收时间在订单创建时间之前或在到期时间之后，不处理
            if recovery.CTime.Before(detail.CTime) || recovery.CTime.After(detail.EndTime) {
                continue
            }

            // 回收前后的时间和订单时间没有交集， 不处理
            if recovery.BeginTime.Before(detail.BeginTime) || recovery.EndTime.After(detail.EndTime) {
                continue
            }

            if recovery.EndTime.Before(detail.BeginTime) {
                // 全部回收, 即回收后的结束时间为订单开始时间
                handleDetailRecovery(detail, recovery, detail.BeginTime, begin, end)
            } else {
                // 部分回收， 即回收后的结束时间为回收记录的结束时间
                handleDetailRecovery(detail, recovery, recovery.EndTime, begin, end)
            }
        }
    }
}

// 处理回收记录
// endTimeAfterRecovery 回收后的结束时间
func handleDetailRecovery(detail *Detail, recovery *Recovery, endTimeAfterRecovery, begin, end time.Time) {
    if recovery.CTime.After(begin) && recovery.CTime.Before(end) {
        tmpBegin := endTimeAfterRecovery
        if begin.After(tmpBegin) {
            tmpBegin = begin
        }

        recoveryDays := detail.EndTime.Sub(tmpBegin).Seconds() / daySec
        detail.UseDays += recoveryDays

        if detail.CTime.Before(begin) {
            detail.BeginDays += recoveryDays
        }
    }

    detail.EndTime = endTimeAfterRecovery
    recovery.BeginTime = endTimeAfterRecovery
}

type Summary struct {
    ProductId     uint32
    EffectiveDays uint32
    Price         float64
    TotalPrice    uint32
    BeginDays     float64
    BuyDays       float64
    UseDays       float64
    EndDays       float64
}

// genSummarySheet 生成汇总报表
func genSummarySheet(file *xlsx.File, details []*Detail, begin, end time.Time) error {
    summaryMap := make(map[uint32]*Summary)
    for _, detail := range details {
        summary, ok := summaryMap[detail.ProductId]
        if !ok {
            summary = &Summary{
                ProductId:     detail.ProductId,
                EffectiveDays: detail.EffectiveDays,
                Price:         detail.Price,
            }
            summaryMap[detail.ProductId] = summary
        }

        summary.TotalPrice += detail.TotalPrice
        summary.BuyDays += detail.BuyDays
        summary.UseDays += detail.UseDays
        summary.BeginDays += detail.BeginDays
        summary.EndDays += detail.EndDays
    }

    sheet, err := file.AddSheet("汇总")
    if err != nil {
        log.Errorf("genSummarySheet fail to AddSheet. err:%v", err)
        return err
    }

    sheet.AddRow().WriteSlice(&[]string{"月份", "商品id", "有效天数", "单价", "期初剩余金额", "本期购买金额", "本期使用金额", "本期剩余金额"}, -1)
    for _, summary := range summaryMap {
        row := sheet.AddRow()
        row.AddCell().SetValue(begin.Format("2006-01"))
        row.AddCell().SetValue(summary.ProductId)
        row.AddCell().SetValue(summary.EffectiveDays)
        row.AddCell().SetValue(summary.Price)
        row.AddCell().SetValue(summary.BeginDays * float64(summary.Price))
        row.AddCell().SetValue(summary.TotalPrice)
        row.AddCell().SetValue(summary.UseDays * float64(summary.Price))
        row.AddCell().SetValue(summary.EndDays * float64(summary.Price))
    }

    return nil
}

/*
// genSummarySheet 生成汇总报表
func genSummarySheet(file *xlsx.File, orders []*store.Order, begin, end time.Time) error {
	decrOrders := make([]*store.Order, 0)
	summaryMap := make(map[uint32]*Summary)
	for _, order := range orders {
		if order.AwardDurationSec <= 0 {
			// 先不处理回收记录
			decrOrders = append(decrOrders, order)
			continue
		}

		summary, ok := summaryMap[order.HouseId]
		if !ok {
			summary = &Summary{
				ProductId: order.HouseId,
			}
			summaryMap[order.HouseId] = summary
		}

		// 商品信息
		if order.Amount > 0 && summary.EffectiveDays == 0 {
			summary.EffectiveDays = uint32(order.AwardDurationSec) / order.Amount / daySec
			if summary.EffectiveDays > 0 {
				summary.Price = order.TotalPrice / order.Amount / summary.EffectiveDays
			}
		}

		summary.BuyDays += float64(order.AwardDurationSec / daySec)
		summary.UseDays += calcUnionDay(order.BeginTime, order.ExpireTime, begin, end)

		// 跨过账期开始时间
		if order.OrderCtime.Before(begin) && order.ExpireTime.After(begin) {
			summary.BeginDays += calcRemainDays(order.BeginTime, order.ExpireTime, begin)
		}

		// 跨过账期结束时间
		if order.OrderCtime.Before(end) && order.ExpireTime.After(end) {
			summary.EndDays += calcRemainDays(order.BeginTime, order.ExpireTime, end)
		}
	}

	// 处理回收记录
	for _, order := range decrOrders {
		// 回收后时间 应在 回收前时间在之前
		// 否则不处理
		if !order.ExpireTime.Before(order.BeginTime) {
			continue
		}
		summary, ok := summaryMap[order.HouseId]
		if !ok {
			continue
		}

		// 跨过账期开始时间
		if order.BeginTime.After(begin) && order.ExpireTime.Before(begin) {
			summary.BeginDays -= float64(order.BeginTime.Sub(begin).Seconds()) / daySec
		}

		// 跨过账期结束时间
		if order.BeginTime.After(end) && order.ExpireTime.Before(end) {
			days := float64(order.BeginTime.Sub(end).Seconds()) / daySec
			summary.UseDays += days // 回收的算作已使用了
			summary.EndDays -= days // 回收的不算在期末剩余里
		}
	}

	sheet, err := file.AddSheet("汇总")
	if err != nil {
		log.Errorf("genSummarySheet fail to AddSheet. err:%v", err)
		return err
	}

	sheet.AddRow().WriteSlice(&[]string{"月份", "商品id", "有效天数", "单价", "期初剩余金额", "本期购买金额", "本期使用金额", "本期剩余金额"}, -1)
	for _, summary := range summaryMap {
		row := sheet.AddRow()
		row.AddCell().SetValue(begin.Format("2006-01"))
		row.AddCell().SetValue(summary.ProductId)
		row.AddCell().SetValue(summary.EffectiveDays)
		row.AddCell().SetValue(summary.Price)
		row.AddCell().SetValue(summary.BeginDays * float64(summary.Price))
		row.AddCell().SetValue(summary.BuyDays * float64(summary.Price))
		row.AddCell().SetValue(summary.UseDays * float64(summary.Price))
		row.AddCell().SetValue(summary.EndDays * float64(summary.Price))
	}

	return nil
}

*/

// 计算剩余天数
func calcRemainDays(orderBegin, orderEnd, remainTime time.Time) float64 {
    if orderEnd.Before(remainTime) || orderEnd.Before(orderBegin) {
        return 0
    }

    tmpBegin := remainTime
    if orderBegin.After(tmpBegin) {
        tmpBegin = orderBegin
    }
    return float64(orderEnd.Sub(tmpBegin).Seconds()) / daySec
}

// 计算 orderBegin-orderEnd 和 begin-end 两个时间段的交集天数
func calcUnionDay(orderBegin, orderEnd, begin, end time.Time) float64 {
    if orderBegin.After(end) || orderEnd.Before(begin) {
        return 0
    }

    if orderBegin.Before(begin) {
        orderBegin = begin
    }
    if orderEnd.After(end) {
        orderEnd = end
    }

    if orderBegin.After(orderEnd) {
        return 0
    }

    return orderEnd.Sub(orderBegin).Seconds() / 86400
}

func SendMail(filePath, subject string, to []string) error {
    m := gomail.NewMessage()
    m.SetHeader("From", "<EMAIL>")

    m.SetHeader("To", to...)
    m.SetHeader("Subject", subject)

    m.SetBody("text/html", "见附件")
    m.Attach(filePath) //附件

    d := gomail.NewDialer("mail.52tt.com", 465, "<EMAIL>", "V7.D.sTy@%bDB50t#n.r$A4h+-FnXA")

    d.TLSConfig = &tls.Config{InsecureSkipVerify: true}
    if err := d.DialAndSend(m); err != nil {
        fmt.Println(err)
        return err
    }
    return nil
}
