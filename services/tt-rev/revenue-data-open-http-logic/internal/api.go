package internal

import (
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/http"
	"golang.52tt.com/protocol/common/status"
	"time"
)

const (
	ErrInvalidToken = 10000 + iota
	ErrBadSecret
	ErrInternalSysErr
	ErrGameNotMount
	ErrRoomNotFound
	ErrBadRequest
	ErrBadAppid
	ErrBadChatMethod
)

type AuthReq struct {
	AppId string `json:"appid"`
}

type GetAccessTokenReq struct {
	Appid     string `json:"appid"`
	Secret    string `json:"secret"`
	GrantType string `json:"grant_type"`
}

type GetAccessTokenResp struct {
	AccessToken string `json:"access_token"`
	ExpiresIn   int32  `json:"expires_in"`
}

type GetChannelInfoReq struct {
	Appid    string `json:"appid"`
	UserCode string `json:"user_code"`
}

type GetChannelInfoResp struct {
	RoomId    string `json:"roomId"`
	OpenId    string `json:"openId"`
	AvatarUrl string `json:"avatarUrl"`
	UserName  string `json:"userName"`
}

type GetDataReportTaskReq struct {
	Appid   string `json:"appid"`
	RoomId  string `json:"roomid"`
	MsgType string `json:"msg_type"`
}

type GetDataReportTaskResp struct {
	Status int32 `json:"status"`
}

type CampButtonCfg struct {
	ButtonColor string `json:"buttonColor"`
	ButtonText  string `json:"buttonText"`
	CommentText string `json:"commentText"`
}

type CampCfg struct {
	CloseQuickInfoBySendGift bool             `json:"closeQuickInfoBySendGift"`
	QuickInfos               []*CampButtonCfg `json:"quickInfos"`
}

type ReportCampCfgReq struct {
	Appid     string `json:"appid"`
	RoomId    string `json:"roomid"`
	RoundId   string `json:"roundid"`
	Type      string `json:"type"`
	Timestamp int64  `json:"timestamp"`
	CampCfg   string `json:"data"`
}

type ReportGameEndReq struct {
	Appid     string `json:"appid"`
	RoomId    string `json:"roomid"`
	RoundId   string `json:"roundid"`
	Type      string `json:"type"`
	Timestamp int64  `json:"timestamp"`
	Data      string `json:"data"`
}

type ReportUserCampReq struct {
	Appid   string `json:"appid"`
	RoomId  string `json:"roomid"`
	RoundId string `json:"roundid"`
	Openid  string `json:"openid"`
	Camp    string `json:"camp"`
}

type StartDataReportTaskReq struct {
	Appid   string `json:"appid"`
	RoomId  string `json:"roomid"`
	MsgType string `json:"msg_type"`
}

type StartDataReportTaskResp struct {
	TaskId string `json:"task_id"`
}

type StopDataReportTaskReq struct {
	Appid   string `json:"appid"`
	RoomId  string `json:"roomid"`
	MsgType string `json:"msg_type"`
}

const (
	ChatMethodConfig    = "config"
	ChatMethodStart     = "start"
	ChatMethodHeartbeat = "heartbeat"
	ChatMethodStop      = "stop"
)

type ChatPkMembers struct {
	PkId     string   `json:"cp_pk_id"`
	RoomList []string `json:"room_list"`
}

type ChatPkLayout struct {
	PkId          string                       `json:"cp_pk_id"`
	Author2Layout map[string]map[string]uint32 `json:"host_to_layout"` // 主播视角下，每个房间的布局id映射
}

type ChatPkReq struct {
	Appid     string `json:"appid"`
	RoomId    string `json:"roomid"`
	Timestamp int64  `json:"timestamp"`
	Method    string `json:"method"`
	Data      string `json:"data"`
}

type RankInfo struct {
	OpenId string `json:"openid"`
	Rank   int32  `json:"rank"`
	Score  int64  `json:"score"`
}

type ReportScoreRankReq struct {
	Appid    string      `json:"appid"`
	RankType int64       `json:"rank_type"`
	RankName string      `json:"rank_name"`
	RankDate string      `json:"rank_date"`
	RankList []*RankInfo `json:"rank_list"`
}

type Response struct {
	Code       int32       `json:"err_no"`
	Message    string      `json:"err_msg"`
	ServerTime string      `json:"server_time"`
	Data       interface{} `json:"data"`
}

func ServePB2JSON(w http.ResponseWriter, data interface{}) {
	ServeAPIJsonWithResponse(w, &Response{
		Code: 0, Message: "success", ServerTime: fmt.Sprint(time.Now().UnixNano() / 1e6), Data: data,
	})
}

func ServeAPIJsonWithError(w http.ResponseWriter, code int, message string) {
	r := &Response{
		Code: int32(code), ServerTime: fmt.Sprint(time.Now().UnixNano() / 1e6),
	}
	if len(message) > 0 {
		r.Message = message
	} else {
		r.Message = status.MessageFromCode(code)
	}

	ServeAPIJsonWithResponse(w, r)
}

func ServeAPIJsonWithResponse(w http.ResponseWriter, r *Response) {
	_ = http.WriteJSON(w, http.StatusOK, r)
}
