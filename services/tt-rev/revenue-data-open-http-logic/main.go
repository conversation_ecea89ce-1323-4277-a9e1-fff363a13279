package main

import (
	"context"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/http"
	"golang.52tt.com/services/tt-rev/revenue-data-open-http-logic/internal"

	startup "gitlab.ttyuyin.com/avengers/tyr/core/service/startup/suit/http"

	_ "golang.52tt.com/pkg/hub/tyr/compatible/http" // 兼容tyr公共库
)

func main() {
	var (
		server *internal.Server
		cfg    = &internal.StartConfig{}
		err    error
	)

	initializeFunc := func(ctx context.Context, r *http.Router) error {
		// do something with cfg
		if server, err = internal.NewServer(ctx, cfg); err != nil {
			log.Errorf("NewServer fail, err: %v", err)
			return err
		}

		// 鉴权
		r.AddRequestInterceptor(server.Auth())

		revenueRouter := r.Child("/revenue_data")
		revenueRouter.POST("/echo", server.Echo)
		revenueRouter.POST(internal.GetTokenPath, server.GetAccessToken)
		revenueRouter.POST("/room/query", server.GetChannelInfo)
		revenueRouter.POST("/task/start", server.StartDataReportTask)
		revenueRouter.POST("/task/stop", server.StopDataReportTask)
		revenueRouter.POST("/task/query", server.GetDataReportTaskStatus)
		revenueRouter.POST("/interactive/start", server.GameStartReportCampCfg)
		revenueRouter.POST("/interactive/end", server.GameEndReport)
		revenueRouter.POST("/interactive/chat", server.ChatPk)
		revenueRouter.POST("/user_camp/report", server.ReportUserCamp)
		revenueRouter.POST("/score_rank/report", server.ReportScoreRank)

		return nil
	}

	closeFunc := func(ctx context.Context) {
		// do something when server terminating
	}

	if err := startup.New("revenue-data-open-http", cfg).
		AddHttpServer(
			http.NewBuildOption().WithInitializeFunc(initializeFunc),
		).
		WithCloseFunc(closeFunc).
		Start(); err != nil {
		log.Errorf("server start fail, err: %v", err)
	}
}
