package server

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/chance-game-entry"
)

func (s *Server) SetChanceGameSwitch(ctx context.Context, req *pb.SetChanceGameSwitchReq) (*pb.SetChanceGameSwitchResp, error) {

	err := s.mgr.SetChanceGameSwitch(ctx, req.GetGameType(), req.GetOnOrOff())

	return &pb.SetChanceGameSwitchResp{}, err
}

func (s *Server) GetAllChanceGameSwitchState(ctx context.Context, req *pb.GetAllChanceGameSwitchStateReq) (*pb.GetAllChanceGameSwitchStateResp, error) {
	out := &pb.GetAllChanceGameSwitchStateResp{}

	switchList, err := s.mgr.GetAllChanceGameSwitch(ctx)
	if err != nil {
		return out, err
	}

	out.SwitchList = switchList
	return out, nil
}

func (s *Server) AddChannelBWListV2(ctx context.Context, req *pb.AddChannelBWListV2Req) (*pb.AddChannelBWListV2Resp, error) {
	out := &pb.AddChannelBWListV2Resp{}

	if len(req.GetChannelList()) == 0 {
		return out, nil
	}

	err := s.mgr.AddBlackChannelListV2(ctx, req.GetChannelList())

	return out, err
}

func (s *Server) AddUserBWListV2(ctx context.Context, req *pb.AddUserBWListV2Req) (*pb.AddUserBWListV2Resp, error) {
	out := &pb.AddUserBWListV2Resp{}

	if !checkListType(req.GetListType()) {
		return out, protocol.NewExactServerError(nil, status.ErrParam, "参数错误")
	}

	if len(req.GetUserList()) == 0 {
		return out, nil
	}

	err := s.mgr.AddUserBWListV2(ctx, req.GetUserList(), req.GetListType())

	return out, err
}

func (s *Server) BatDelUserBWListV2(ctx context.Context, req *pb.BatDelUserBWListV2Req) (*pb.BatDelUserBWListV2Resp, error) {
	out := &pb.BatDelUserBWListV2Resp{}

	if !checkListType(req.GetListType()) {
		return out, protocol.NewExactServerError(nil, status.ErrParam, "参数错误")
	}

	if len(req.GetUidList()) == 0 {
		return out, nil
	}

	err := s.mgr.BatDelUserBWListV2(ctx, req.GetUidList(), req.GetListType())

	return out, err
}

func (s *Server) BatDelChannelBWListV2(ctx context.Context, req *pb.BatDelChannelBWListV2Req) (*pb.BatDelChannelBWListV2Resp, error) {
	out := &pb.BatDelChannelBWListV2Resp{}

	if len(req.GetChannelIdList()) == 0 {
		return out, nil
	}

	err := s.mgr.BatDelChannelBWListV2(ctx, req.GetChannelIdList())

	return out, err
}

func (s *Server) GetChannelBWListInfoV2(ctx context.Context, req *pb.GetChannelBWListInfoV2Req) (*pb.GetChannelBWListInfoV2Resp, error) {
	out := &pb.GetChannelBWListInfoV2Resp{}
	var err error

	if req.GetChannelId() != 0 {
		out, err = s.mgr.SearchByChannelId(ctx, req.GetChannelId())
	} else {
		// 分页获取名单
		if req.GetPage() == 0 {
			return out, protocol.NewExactServerError(nil, status.ErrParam, "参数错误")
		}
		out, err = s.mgr.GetChannelBWListInfoV2(ctx, req.GetListType(), req.GetPage(), req.GetCount())
	}

	return out, err
}

func (s *Server) GetUserBWListInfoV2(ctx context.Context, req *pb.GetUserBWListInfoV2Req) (*pb.GetUserBWListInfoV2Resp, error) {
	out := &pb.GetUserBWListInfoV2Resp{}
	var err error

	if !checkListType(req.GetListType()) {
		return out, protocol.NewExactServerError(nil, status.ErrParam, "参数错误")
	}

	if req.GetUid() != 0 {
		out, err = s.mgr.SearchByUid(ctx, req.GetUid(), req.GetListType())
	} else {
		if req.GetPage() == 0 {
			return out, protocol.NewExactServerError(nil, status.ErrParam, "参数错误")
		}
		out, err = s.mgr.GetUserBWListInfoV2(ctx, req.GetListType(), req.GetPage(), req.GetCount())
	}

	return out, err
}

func checkListType(listType uint32) bool {
	return listType == uint32(pb.ChanceGameListType_ChanceGameListType_White) || listType == uint32(pb.ChanceGameListType_ChanceGameListType_Black)
}

func checkRelateType(relateType uint32) bool {
	return relateType == uint32(pb.RelateType_RelateType_And) || relateType == uint32(pb.RelateType_RelateType_Or)
}

func (s *Server) SetChanceGameAccessCond(ctx context.Context, req *pb.SetChanceGameAccessCondReq) (*pb.SetChanceGameAccessCondResp, error) {
	out := &pb.SetChanceGameAccessCondResp{}
	log.Infof("SetChanceGameAccessCond req:%+v", req)

	if len(req.GetConditionList()) >= 2 {
		if !checkRelateType(req.GetRelateType()) {
			return out, protocol.NewExactServerError(nil, status.ErrParam, "参数错误")
		}
	}

	out, err := s.mgr.SetChanceGameAccessCond(ctx, req)

	return out, err
}

func (s *Server) GetChanceGameAccessCond(ctx context.Context, req *pb.GetChanceGameAccessCondReq) (*pb.GetChanceGameAccessCondResp, error) {
	out, err := s.mgr.GetChanceGameAccessCond(ctx, req.GetGameType(), req.GetCondType())

	return out, err
}

func (s *Server) CheckGameEntryAccess(ctx context.Context, req *pb.CheckGameEntryAccessReq) (*pb.CheckGameEntryAccessResp, error) {
	// 参数校验
	if req.GetUid() == 0 || len(req.GetGameType()) == 0 {
		log.ErrorWithCtx(ctx, "CheckGameEntryAccess req:%+v,uid invalid", req)
		return &pb.CheckGameEntryAccessResp{}, nil
	}

    quickReturn := true
    for _, v := range req.GetGameType() {
        if !s.bc.CheckIsQuickReturnGameType(v) {
            quickReturn = false // 不能走快速返回
        }
    }
    if quickReturn {
        // 快速返回
        log.DebugWithCtx(ctx, "CheckGameEntryAccess quick return, req:%v", req)
        return &pb.CheckGameEntryAccessResp{}, nil
    }

	channelId := req.GetChannelId()

	if channelId == 0 {
		// 查一下用户所在的房间
		colResp, err := s.channelOlCli.GetUsersChannelId(ctx, req.GetUid(), req.GetUid())
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckGameEntryAccess GetUsersChannelId fail,err:%v", err)
			return &pb.CheckGameEntryAccessResp{}, protocol.NewExactServerError(nil, status.ErrOnePieceBuyChanceLimit)
		}
		channelId = colResp.GetChannelId()

		if channelId == 0 {
			log.ErrorWithCtx(ctx, "CheckGameEntryAccess fail channelId invalid,req:%v", req)
			return &pb.CheckGameEntryAccessResp{}, nil
		}
	}

	out := &pb.CheckGameEntryAccessResp{}

	channelInfo, err := s.mgr.GetChannelSimpleInfo(ctx, req.GetUid(), channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckGameEntryAccess GetChannelSimpleInfo fail. req:%+v err:%v", req, err)
		return out, err
	}

	for _, v := range req.GetGameType() {
		res, err := s.mgr.CheckGameEntryAccess(ctx, req, channelId, v, channelInfo)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckGameEntryAccess req:%+v err:%v	", req, err)
			return out, err
		}
		out.ConfList = append(out.ConfList, res)
	}

	log.Debugf("out:%+v", out)
	return out, nil
}

func (s *Server) GetLocalCacheConf(ctx context.Context, req *pb.GetLocalCacheConfReq) (*pb.GetLocalCacheConfResp, error) {

	out, err := s.mgr.GetLocalCacheConf(ctx, req)
	return out, err
}

func (s *Server) GetUserValue(ctx context.Context, req *pb.GetUserValueReq) (*pb.GetUserValueResp, error) {
	if req.GetUid() == 0 {
		return &pb.GetUserValueResp{}, nil
	}

	out, err := s.mgr.GetUserValue(ctx, req.GetUid())

	return out, err
}

func (s *Server) SetGameNotifyInfo(ctx context.Context, req *pb.SetGameNotifyInfoReq) (*pb.SetGameNotifyInfoResp, error) {
	resp := &pb.SetGameNotifyInfoResp{}

	if req.GetGameType() == pb.NotifyGameType_NotifyGameType_UNSPECIFIED || req.GetInfo() == nil {
		return &pb.SetGameNotifyInfoResp{}, protocol.NewExactServerError(nil, status.ErrParam, "请求参数错误")
	}

	err := s.mgr.SetChanceGameNotify(ctx, req)
	if err != nil {
		return resp, err
	}

	return resp, nil
}

func (s *Server) GetGameNotifyInfo(ctx context.Context, req *pb.GetGameNotifyInfoReq) (*pb.GetGameNotifyInfoResp, error) {
	resp := &pb.GetGameNotifyInfoResp{}
	notifyList, err := s.mgr.GetNotifyInfoByGameType(ctx, uint32(req.GetGameType()))
	if err != nil {
		return &pb.GetGameNotifyInfoResp{}, err
	}

	for _, v := range notifyList {
		if v.GetNotifyType() == req.GetNotifyType() {
			resp.Info = v
		}
	}

	return resp, nil
}

func (s *Server) BatGetNotifyInfo(ctx context.Context, req *pb.BatGetNotifyInfoReq) (*pb.BatGetNotifyInfoResp, error) {
	return s.mgr.BatGetNotifyInfoFromCache(ctx, req)
}

func (s *Server) GetMagicSpiritAccessCond(ctx context.Context, req *pb.GetMagicSpiritAccessCondReq) (*pb.GetMagicSpiritAccessCondResp, error) {
	return s.mgr.GetMagicSpiritAccessConfList(ctx, req.GetCondType())
}

func (s *Server) DelMagicSpiritAccessCond(ctx context.Context, req *pb.DelMagicSpiritAccessCondReq) (*pb.DelMagicSpiritAccessCondResp, error) {
	out := &pb.DelMagicSpiritAccessCondResp{}

	if req.GetMagicId() == 0 {
		return out, nil
	}

	return out, s.mgr.DelMagicAccessConfByMagicId(ctx, req.GetMagicId(), req.GetCondType())
}

func (s *Server) CheckMagicSpiritAccess(ctx context.Context, req *pb.CheckMagicSpiritAccessReq) (*pb.CheckMagicSpiritAccessResp, error) {
	out := &pb.CheckMagicSpiritAccessResp{}

	channelId := req.GetChannelId()

	if channelId == 0 {
		// 查一下用户所在的房间
		colResp, err := s.channelOlCli.GetUsersChannelId(ctx, req.GetUid(), req.GetUid())
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckGameEntryAccess GetUsersChannelId fail,err:%v", err)
			return &pb.CheckMagicSpiritAccessResp{}, protocol.NewExactServerError(nil, status.ErrOnePieceBuyChanceLimit)
		}
		channelId = colResp.GetChannelId()

		if channelId == 0 {
			log.ErrorWithCtx(ctx, "CheckGameEntryAccess fail channelId invalid,req:%v", req)
			return &pb.CheckMagicSpiritAccessResp{}, nil
		}
	}

	channelInfo, err := s.mgr.GetChannelSimpleInfo(ctx, req.GetUid(), channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckGameEntryAccess GetChannelSimpleInfo fail. req:%+v err:%v", req, err)
		return out, err
	}

	return s.mgr.CheckMagicSpiritAccess(ctx, req, channelId, channelInfo)
}

func (s *Server) SetChanceGameOpenTime(c context.Context, req *pb.SetGameSwitchOpenTimeReq) (*pb.SetGameSwitchOpenTimeResp, error) {
	log.InfoWithCtx(c, "SetChanceGameOpenTime req:%+v", req)
	return &pb.SetGameSwitchOpenTimeResp{}, s.mgr.SetGameOpenTime(c, req)
}

func (s *Server) GetChanceGameOpenTime(c context.Context, req *pb.GetGameSwitchOpenTimeReq) (*pb.GetGameSwitchOpenTimeResp, error) {
	return s.mgr.GetGameOpenTime(c, req)
}

func (s *Server) CheckChanceGameIsOpen(ctx context.Context, req *pb.CheckChanceGameIsOpenReq) (*pb.CheckChanceGameIsOpenResp, error) {
	out := &pb.CheckChanceGameIsOpenResp{}
	out.SwitchStatus = s.mgr.CheckOpenTime(req.GetGameType())
	out.GameType = req.GetGameType()
	return out, nil
}
