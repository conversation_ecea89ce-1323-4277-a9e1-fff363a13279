// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/tt-rev/chance-game-entry/internal/manager (interfaces: IGameEntryMgr)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	chance_game_entry "golang.52tt.com/protocol/services/chance-game-entry"
	Channel "golang.52tt.com/protocol/services/channelsvr"
)

// MockIGameEntryMgr is a mock of IGameEntryMgr interface.
type MockIGameEntryMgr struct {
	ctrl     *gomock.Controller
	recorder *MockIGameEntryMgrMockRecorder
}

// MockIGameEntryMgrMockRecorder is the mock recorder for MockIGameEntryMgr.
type MockIGameEntryMgrMockRecorder struct {
	mock *MockIGameEntryMgr
}

// NewMockIGameEntryMgr creates a new mock instance.
func NewMockIGameEntryMgr(ctrl *gomock.Controller) *MockIGameEntryMgr {
	mock := &MockIGameEntryMgr{ctrl: ctrl}
	mock.recorder = &MockIGameEntryMgrMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIGameEntryMgr) EXPECT() *MockIGameEntryMgrMockRecorder {
	return m.recorder
}

// AddBlackChannelListV2 mocks base method.
func (m *MockIGameEntryMgr) AddBlackChannelListV2(arg0 context.Context, arg1 []*chance_game_entry.ListItemConf) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddBlackChannelListV2", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddBlackChannelListV2 indicates an expected call of AddBlackChannelListV2.
func (mr *MockIGameEntryMgrMockRecorder) AddBlackChannelListV2(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddBlackChannelListV2", reflect.TypeOf((*MockIGameEntryMgr)(nil).AddBlackChannelListV2), arg0, arg1)
}

// AddUserBWListV2 mocks base method.
func (m *MockIGameEntryMgr) AddUserBWListV2(arg0 context.Context, arg1 []*chance_game_entry.ListItemConf, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUserBWListV2", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddUserBWListV2 indicates an expected call of AddUserBWListV2.
func (mr *MockIGameEntryMgrMockRecorder) AddUserBWListV2(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserBWListV2", reflect.TypeOf((*MockIGameEntryMgr)(nil).AddUserBWListV2), arg0, arg1, arg2)
}

// BatDelChannelBWListV2 mocks base method.
func (m *MockIGameEntryMgr) BatDelChannelBWListV2(arg0 context.Context, arg1 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatDelChannelBWListV2", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatDelChannelBWListV2 indicates an expected call of BatDelChannelBWListV2.
func (mr *MockIGameEntryMgrMockRecorder) BatDelChannelBWListV2(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatDelChannelBWListV2", reflect.TypeOf((*MockIGameEntryMgr)(nil).BatDelChannelBWListV2), arg0, arg1)
}

// BatDelUserBWListV2 mocks base method.
func (m *MockIGameEntryMgr) BatDelUserBWListV2(arg0 context.Context, arg1 []uint32, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatDelUserBWListV2", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatDelUserBWListV2 indicates an expected call of BatDelUserBWListV2.
func (mr *MockIGameEntryMgrMockRecorder) BatDelUserBWListV2(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatDelUserBWListV2", reflect.TypeOf((*MockIGameEntryMgr)(nil).BatDelUserBWListV2), arg0, arg1, arg2)
}

// BatGetNotifyInfoFromCache mocks base method.
func (m *MockIGameEntryMgr) BatGetNotifyInfoFromCache(arg0 context.Context, arg1 *chance_game_entry.BatGetNotifyInfoReq) (*chance_game_entry.BatGetNotifyInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetNotifyInfoFromCache", arg0, arg1)
	ret0, _ := ret[0].(*chance_game_entry.BatGetNotifyInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetNotifyInfoFromCache indicates an expected call of BatGetNotifyInfoFromCache.
func (mr *MockIGameEntryMgrMockRecorder) BatGetNotifyInfoFromCache(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetNotifyInfoFromCache", reflect.TypeOf((*MockIGameEntryMgr)(nil).BatGetNotifyInfoFromCache), arg0, arg1)
}

// CheckGameEntryAccess mocks base method.
func (m *MockIGameEntryMgr) CheckGameEntryAccess(arg0 context.Context, arg1 *chance_game_entry.CheckGameEntryAccessReq, arg2, arg3 uint32, arg4 *Channel.ChannelSimpleInfo) (*chance_game_entry.GameEntryAccess, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckGameEntryAccess", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*chance_game_entry.GameEntryAccess)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckGameEntryAccess indicates an expected call of CheckGameEntryAccess.
func (mr *MockIGameEntryMgrMockRecorder) CheckGameEntryAccess(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckGameEntryAccess", reflect.TypeOf((*MockIGameEntryMgr)(nil).CheckGameEntryAccess), arg0, arg1, arg2, arg3, arg4)
}

// CheckMagicSpiritAccess mocks base method.
func (m *MockIGameEntryMgr) CheckMagicSpiritAccess(arg0 context.Context, arg1 *chance_game_entry.CheckMagicSpiritAccessReq, arg2 uint32, arg3 *Channel.ChannelSimpleInfo) (*chance_game_entry.CheckMagicSpiritAccessResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckMagicSpiritAccess", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*chance_game_entry.CheckMagicSpiritAccessResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckMagicSpiritAccess indicates an expected call of CheckMagicSpiritAccess.
func (mr *MockIGameEntryMgrMockRecorder) CheckMagicSpiritAccess(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckMagicSpiritAccess", reflect.TypeOf((*MockIGameEntryMgr)(nil).CheckMagicSpiritAccess), arg0, arg1, arg2, arg3)
}

// CheckOpenTime mocks base method.
func (m *MockIGameEntryMgr) CheckOpenTime(arg0 uint32) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckOpenTime", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// CheckOpenTime indicates an expected call of CheckOpenTime.
func (mr *MockIGameEntryMgrMockRecorder) CheckOpenTime(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckOpenTime", reflect.TypeOf((*MockIGameEntryMgr)(nil).CheckOpenTime), arg0)
}

// DelMagicAccessConfByMagicId mocks base method.
func (m *MockIGameEntryMgr) DelMagicAccessConfByMagicId(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelMagicAccessConfByMagicId", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelMagicAccessConfByMagicId indicates an expected call of DelMagicAccessConfByMagicId.
func (mr *MockIGameEntryMgrMockRecorder) DelMagicAccessConfByMagicId(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelMagicAccessConfByMagicId", reflect.TypeOf((*MockIGameEntryMgr)(nil).DelMagicAccessConfByMagicId), arg0, arg1, arg2)
}

// GetAllChanceGameSwitch mocks base method.
func (m *MockIGameEntryMgr) GetAllChanceGameSwitch(arg0 context.Context) ([]*chance_game_entry.ChanceGameSwitchState, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllChanceGameSwitch", arg0)
	ret0, _ := ret[0].([]*chance_game_entry.ChanceGameSwitchState)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllChanceGameSwitch indicates an expected call of GetAllChanceGameSwitch.
func (mr *MockIGameEntryMgrMockRecorder) GetAllChanceGameSwitch(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllChanceGameSwitch", reflect.TypeOf((*MockIGameEntryMgr)(nil).GetAllChanceGameSwitch), arg0)
}

// GetChanceGameAccessCond mocks base method.
func (m *MockIGameEntryMgr) GetChanceGameAccessCond(arg0 context.Context, arg1, arg2 uint32) (*chance_game_entry.GetChanceGameAccessCondResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChanceGameAccessCond", arg0, arg1, arg2)
	ret0, _ := ret[0].(*chance_game_entry.GetChanceGameAccessCondResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChanceGameAccessCond indicates an expected call of GetChanceGameAccessCond.
func (mr *MockIGameEntryMgrMockRecorder) GetChanceGameAccessCond(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChanceGameAccessCond", reflect.TypeOf((*MockIGameEntryMgr)(nil).GetChanceGameAccessCond), arg0, arg1, arg2)
}

// GetChannelBWListInfoV2 mocks base method.
func (m *MockIGameEntryMgr) GetChannelBWListInfoV2(arg0 context.Context, arg1, arg2, arg3 uint32) (*chance_game_entry.GetChannelBWListInfoV2Resp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelBWListInfoV2", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*chance_game_entry.GetChannelBWListInfoV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelBWListInfoV2 indicates an expected call of GetChannelBWListInfoV2.
func (mr *MockIGameEntryMgrMockRecorder) GetChannelBWListInfoV2(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelBWListInfoV2", reflect.TypeOf((*MockIGameEntryMgr)(nil).GetChannelBWListInfoV2), arg0, arg1, arg2, arg3)
}

// GetChannelSimpleInfo mocks base method.
func (m *MockIGameEntryMgr) GetChannelSimpleInfo(arg0 context.Context, arg1, arg2 uint32) (*Channel.ChannelSimpleInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelSimpleInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(*Channel.ChannelSimpleInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelSimpleInfo indicates an expected call of GetChannelSimpleInfo.
func (mr *MockIGameEntryMgrMockRecorder) GetChannelSimpleInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelSimpleInfo", reflect.TypeOf((*MockIGameEntryMgr)(nil).GetChannelSimpleInfo), arg0, arg1, arg2)
}

// GetGameOpenTime mocks base method.
func (m *MockIGameEntryMgr) GetGameOpenTime(arg0 context.Context, arg1 *chance_game_entry.GetGameSwitchOpenTimeReq) (*chance_game_entry.GetGameSwitchOpenTimeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameOpenTime", arg0, arg1)
	ret0, _ := ret[0].(*chance_game_entry.GetGameSwitchOpenTimeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameOpenTime indicates an expected call of GetGameOpenTime.
func (mr *MockIGameEntryMgrMockRecorder) GetGameOpenTime(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameOpenTime", reflect.TypeOf((*MockIGameEntryMgr)(nil).GetGameOpenTime), arg0, arg1)
}

// GetLocalCacheConf mocks base method.
func (m *MockIGameEntryMgr) GetLocalCacheConf(arg0 context.Context, arg1 *chance_game_entry.GetLocalCacheConfReq) (*chance_game_entry.GetLocalCacheConfResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLocalCacheConf", arg0, arg1)
	ret0, _ := ret[0].(*chance_game_entry.GetLocalCacheConfResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLocalCacheConf indicates an expected call of GetLocalCacheConf.
func (mr *MockIGameEntryMgrMockRecorder) GetLocalCacheConf(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLocalCacheConf", reflect.TypeOf((*MockIGameEntryMgr)(nil).GetLocalCacheConf), arg0, arg1)
}

// GetMagicSpiritAccessConfList mocks base method.
func (m *MockIGameEntryMgr) GetMagicSpiritAccessConfList(arg0 context.Context, arg1 uint32) (*chance_game_entry.GetMagicSpiritAccessCondResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicSpiritAccessConfList", arg0, arg1)
	ret0, _ := ret[0].(*chance_game_entry.GetMagicSpiritAccessCondResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMagicSpiritAccessConfList indicates an expected call of GetMagicSpiritAccessConfList.
func (mr *MockIGameEntryMgrMockRecorder) GetMagicSpiritAccessConfList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicSpiritAccessConfList", reflect.TypeOf((*MockIGameEntryMgr)(nil).GetMagicSpiritAccessConfList), arg0, arg1)
}

// GetNotifyInfoByGameType mocks base method.
func (m *MockIGameEntryMgr) GetNotifyInfoByGameType(arg0 context.Context, arg1 uint32) ([]*chance_game_entry.NotifyInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNotifyInfoByGameType", arg0, arg1)
	ret0, _ := ret[0].([]*chance_game_entry.NotifyInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNotifyInfoByGameType indicates an expected call of GetNotifyInfoByGameType.
func (mr *MockIGameEntryMgrMockRecorder) GetNotifyInfoByGameType(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNotifyInfoByGameType", reflect.TypeOf((*MockIGameEntryMgr)(nil).GetNotifyInfoByGameType), arg0, arg1)
}

// GetUserBWListInfoV2 mocks base method.
func (m *MockIGameEntryMgr) GetUserBWListInfoV2(arg0 context.Context, arg1, arg2, arg3 uint32) (*chance_game_entry.GetUserBWListInfoV2Resp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserBWListInfoV2", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*chance_game_entry.GetUserBWListInfoV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserBWListInfoV2 indicates an expected call of GetUserBWListInfoV2.
func (mr *MockIGameEntryMgrMockRecorder) GetUserBWListInfoV2(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserBWListInfoV2", reflect.TypeOf((*MockIGameEntryMgr)(nil).GetUserBWListInfoV2), arg0, arg1, arg2, arg3)
}

// GetUserValue mocks base method.
func (m *MockIGameEntryMgr) GetUserValue(arg0 context.Context, arg1 uint32) (*chance_game_entry.GetUserValueResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserValue", arg0, arg1)
	ret0, _ := ret[0].(*chance_game_entry.GetUserValueResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserValue indicates an expected call of GetUserValue.
func (mr *MockIGameEntryMgrMockRecorder) GetUserValue(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserValue", reflect.TypeOf((*MockIGameEntryMgr)(nil).GetUserValue), arg0, arg1)
}

// GetVerifyInfo mocks base method.
func (m *MockIGameEntryMgr) GetVerifyInfo(arg0 context.Context, arg1, arg2, arg3 uint32) (bool, bool, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVerifyInfo", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(bool)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// GetVerifyInfo indicates an expected call of GetVerifyInfo.
func (mr *MockIGameEntryMgrMockRecorder) GetVerifyInfo(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVerifyInfo", reflect.TypeOf((*MockIGameEntryMgr)(nil).GetVerifyInfo), arg0, arg1, arg2, arg3)
}

// SearchByChannelId mocks base method.
func (m *MockIGameEntryMgr) SearchByChannelId(arg0 context.Context, arg1 uint32) (*chance_game_entry.GetChannelBWListInfoV2Resp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchByChannelId", arg0, arg1)
	ret0, _ := ret[0].(*chance_game_entry.GetChannelBWListInfoV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchByChannelId indicates an expected call of SearchByChannelId.
func (mr *MockIGameEntryMgrMockRecorder) SearchByChannelId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchByChannelId", reflect.TypeOf((*MockIGameEntryMgr)(nil).SearchByChannelId), arg0, arg1)
}

// SearchByUid mocks base method.
func (m *MockIGameEntryMgr) SearchByUid(arg0 context.Context, arg1, arg2 uint32) (*chance_game_entry.GetUserBWListInfoV2Resp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchByUid", arg0, arg1, arg2)
	ret0, _ := ret[0].(*chance_game_entry.GetUserBWListInfoV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchByUid indicates an expected call of SearchByUid.
func (mr *MockIGameEntryMgrMockRecorder) SearchByUid(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchByUid", reflect.TypeOf((*MockIGameEntryMgr)(nil).SearchByUid), arg0, arg1, arg2)
}

// SetChanceGameAccessCond mocks base method.
func (m *MockIGameEntryMgr) SetChanceGameAccessCond(arg0 context.Context, arg1 *chance_game_entry.SetChanceGameAccessCondReq) (*chance_game_entry.SetChanceGameAccessCondResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChanceGameAccessCond", arg0, arg1)
	ret0, _ := ret[0].(*chance_game_entry.SetChanceGameAccessCondResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChanceGameAccessCond indicates an expected call of SetChanceGameAccessCond.
func (mr *MockIGameEntryMgrMockRecorder) SetChanceGameAccessCond(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChanceGameAccessCond", reflect.TypeOf((*MockIGameEntryMgr)(nil).SetChanceGameAccessCond), arg0, arg1)
}

// SetChanceGameNotify mocks base method.
func (m *MockIGameEntryMgr) SetChanceGameNotify(arg0 context.Context, arg1 *chance_game_entry.SetGameNotifyInfoReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChanceGameNotify", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetChanceGameNotify indicates an expected call of SetChanceGameNotify.
func (mr *MockIGameEntryMgrMockRecorder) SetChanceGameNotify(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChanceGameNotify", reflect.TypeOf((*MockIGameEntryMgr)(nil).SetChanceGameNotify), arg0, arg1)
}

// SetChanceGameSwitch mocks base method.
func (m *MockIGameEntryMgr) SetChanceGameSwitch(arg0 context.Context, arg1 uint32, arg2 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChanceGameSwitch", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetChanceGameSwitch indicates an expected call of SetChanceGameSwitch.
func (mr *MockIGameEntryMgrMockRecorder) SetChanceGameSwitch(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChanceGameSwitch", reflect.TypeOf((*MockIGameEntryMgr)(nil).SetChanceGameSwitch), arg0, arg1, arg2)
}

// SetGameOpenTime mocks base method.
func (m *MockIGameEntryMgr) SetGameOpenTime(arg0 context.Context, arg1 *chance_game_entry.SetGameSwitchOpenTimeReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetGameOpenTime", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetGameOpenTime indicates an expected call of SetGameOpenTime.
func (mr *MockIGameEntryMgrMockRecorder) SetGameOpenTime(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetGameOpenTime", reflect.TypeOf((*MockIGameEntryMgr)(nil).SetGameOpenTime), arg0, arg1)
}

// ShutDown mocks base method.
func (m *MockIGameEntryMgr) ShutDown() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ShutDown")
}

// ShutDown indicates an expected call of ShutDown.
func (mr *MockIGameEntryMgrMockRecorder) ShutDown() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ShutDown", reflect.TypeOf((*MockIGameEntryMgr)(nil).ShutDown))
}

// StartTimer mocks base method.
func (m *MockIGameEntryMgr) StartTimer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "StartTimer")
}

// StartTimer indicates an expected call of StartTimer.
func (mr *MockIGameEntryMgrMockRecorder) StartTimer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartTimer", reflect.TypeOf((*MockIGameEntryMgr)(nil).StartTimer))
}

// TimerHandle mocks base method.
func (m *MockIGameEntryMgr) TimerHandle(arg0 time.Duration, arg1 func() error) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "TimerHandle", arg0, arg1)
}

// TimerHandle indicates an expected call of TimerHandle.
func (mr *MockIGameEntryMgrMockRecorder) TimerHandle(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TimerHandle", reflect.TypeOf((*MockIGameEntryMgr)(nil).TimerHandle), arg0, arg1)
}

// UpdateAccessConf mocks base method.
func (m *MockIGameEntryMgr) UpdateAccessConf() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAccessConf")
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAccessConf indicates an expected call of UpdateAccessConf.
func (mr *MockIGameEntryMgrMockRecorder) UpdateAccessConf() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAccessConf", reflect.TypeOf((*MockIGameEntryMgr)(nil).UpdateAccessConf))
}

// UpdateChannelBlackList mocks base method.
func (m *MockIGameEntryMgr) UpdateChannelBlackList() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateChannelBlackList")
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateChannelBlackList indicates an expected call of UpdateChannelBlackList.
func (mr *MockIGameEntryMgrMockRecorder) UpdateChannelBlackList() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateChannelBlackList", reflect.TypeOf((*MockIGameEntryMgr)(nil).UpdateChannelBlackList))
}

// UpdateGameNotifyInfo mocks base method.
func (m *MockIGameEntryMgr) UpdateGameNotifyInfo() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateGameNotifyInfo")
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateGameNotifyInfo indicates an expected call of UpdateGameNotifyInfo.
func (mr *MockIGameEntryMgrMockRecorder) UpdateGameNotifyInfo() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateGameNotifyInfo", reflect.TypeOf((*MockIGameEntryMgr)(nil).UpdateGameNotifyInfo))
}

// UpdateGameOpenTime mocks base method.
func (m *MockIGameEntryMgr) UpdateGameOpenTime() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateGameOpenTime")
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateGameOpenTime indicates an expected call of UpdateGameOpenTime.
func (mr *MockIGameEntryMgrMockRecorder) UpdateGameOpenTime() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateGameOpenTime", reflect.TypeOf((*MockIGameEntryMgr)(nil).UpdateGameOpenTime))
}

// UpdateGameSwitchConf mocks base method.
func (m *MockIGameEntryMgr) UpdateGameSwitchConf() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateGameSwitchConf")
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateGameSwitchConf indicates an expected call of UpdateGameSwitchConf.
func (mr *MockIGameEntryMgrMockRecorder) UpdateGameSwitchConf() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateGameSwitchConf", reflect.TypeOf((*MockIGameEntryMgr)(nil).UpdateGameSwitchConf))
}

// UpdateUserBlackList mocks base method.
func (m *MockIGameEntryMgr) UpdateUserBlackList() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUserBlackList")
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateUserBlackList indicates an expected call of UpdateUserBlackList.
func (mr *MockIGameEntryMgrMockRecorder) UpdateUserBlackList() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserBlackList", reflect.TypeOf((*MockIGameEntryMgr)(nil).UpdateUserBlackList))
}

// UpdateUserWhiteList mocks base method.
func (m *MockIGameEntryMgr) UpdateUserWhiteList() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUserWhiteList")
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateUserWhiteList indicates an expected call of UpdateUserWhiteList.
func (mr *MockIGameEntryMgrMockRecorder) UpdateUserWhiteList() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserWhiteList", reflect.TypeOf((*MockIGameEntryMgr)(nil).UpdateUserWhiteList))
}
