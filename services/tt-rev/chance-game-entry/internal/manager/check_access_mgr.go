package manager

import (
	"context"
	"crypto/md5" //#nosec
	"encoding/binary"
	"encoding/json"
	"errors"
	"fmt"
	"golang.52tt.com/pkg/foundation/utils"
	"golang.52tt.com/pkg/log"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	channelAppPb "golang.52tt.com/protocol/app/channel"
	pb "golang.52tt.com/protocol/services/chance-game-entry"
	channelPb "golang.52tt.com/protocol/services/channelsvr"
	"golang.52tt.com/services/tt-rev/chance-game-entry/internal/conf"
	"golang.52tt.com/services/tt-rev/chance-game-entry/internal/entity"
	"golang.52tt.com/services/tt-rev/chance-game-entry/internal/mysql"
	"io/ioutil"
	"net"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"
)

func (m *GameEntryMgr) CheckGameEntryAccess(ctx context.Context, req *pb.CheckGameEntryAccessReq, cid, gameType uint32, channelInfo *channelPb.ChannelSimpleInfo) (*pb.GameEntryAccess, error) {
	out := &pb.GameEntryAccess{}
	out.Switch = true
	out.GameType = gameType

	uid := req.GetUid()
	listType := gameType
	channelStatusGameType := gameType

	if gameType == uint32(pb.NewChanceGameType_NewChanceGameType_GoldSmash) {
		listType = uint32(pb.NewChanceGameType_NewChanceGameType_SmashEgg)
		channelStatusGameType = uint32(pb.NewChanceGameType_NewChanceGameType_SmashEgg)
	}

	// 大房检测
	if gameType != uint32(pb.NewChanceGameType_NewChanceGameType_StarTrain) { // 摘星列车不屏蔽cpl房
		if _, ok := m.superChannelMap.Load(cid); ok {
			log.InfoWithCtx(ctx, "CheckGameEntryAccess [cannot] uid:%d, gType:%d,channel:%d superChannel", uid, gameType, cid)
			out.Switch = false
			out.Access = false
			return out, nil
		}
	}

	// 白名单用户是最高等级，略过所有检查
	if _, ok := m.cgc.userWhiteMap.GetSyncMap(listType).Load(uid); ok {
		log.InfoWithCtx(ctx, "CheckGameEntryAccess [can] uid:%d, gType:%d,user white", uid, gameType)
		out.Access = true
		return out, nil
	}

	//if m.bc.GetFeatureSwitch("use_open_time_feature") || m.bc.CheckIfIsTestUid(uid) {
	//	// 检查开放时间
	//	if !m.CheckOpenTime(gameType) {
	//		log.InfoWithCtx(ctx, "CheckGameEntryAccess [cannot] uid:%d, gType:%d,open time", uid, gameType)
	//		out.Switch = false
	//		out.Access = false
	//		return out, nil
	//	}
	//
	//} else {
	//	// 判断开关
	//	if state, ok := m.cgc.accessSwitchMap.m.Load(gameType); ok {
	//		// 不在map中，默认开关开启
	//		if state.(uint32) == mysql.SwitchClose {
	//			log.InfoWithCtx(ctx, "CheckGameEntryAccess [cannot] uid:%d,cid:%d, gType:%d, switch close", uid, cid, gameType)
	//			out.Access = false
	//			out.Switch = false
	//			return out, nil
	//		}
	//	}
	//}
	// 检查开放时间
	if !m.CheckOpenTime(gameType) {
		log.InfoWithCtx(ctx, "CheckGameEntryAccess [cannot] uid:%d, gType:%d,open time", uid, gameType)
		out.Switch = false
		out.Access = false
		return out, nil
	}

	// 房间黑名单
	if _, ok := m.cgc.channelBlackMap.GetSyncMap(listType).Load(cid); ok {
		log.InfoWithCtx(ctx, "CheckGameEntryAccess [cannot] uid:%d, gType:%d,channel black", uid, gameType)
		out.Switch = false
		out.Access = false
		return out, nil
	}

	// 房间状态检查
	if !m.checkUserChannelStatus(ctx, channelStatusGameType, uid, cid, channelInfo) {
		out.Switch = false
		out.Access = false
		return out, nil
	}

	// 用户黑名单
	if _, ok := m.cgc.userBlackMap.GetSyncMap(listType).Load(uid); ok {
		log.InfoWithCtx(ctx, "CheckGameEntryAccess [cannot] uid:%d, gType:%d,user black", uid, gameType)
		out.Access = false
		return out, nil
	}

	// 获取用户CondValue
	userValue, err := m.getUserCondValueFromCache(ctx, uid, req.GetClientIp(), req.GetMarketId(), gameType)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckGameEntryAccess [cannot] GetUserCondValueFromCache fail")
		return out, err
	}

	// 检测未成年人和黑产
	if !userValue.VerifyFlag {
		log.InfoWithCtx(ctx, "CheckGameEntryAccess [cannot] uid:%d, gType:%d,user black or youth", uid, gameType)
		out.Access = false
		return out, nil
	}

	// 补充豁免条件配置
	if valCond, ok := m.cgc.accessCondMap.GetSyncMap(gameType).Load(uint32(pb.CondType_CondType_ExemptCond)); ok {
		exemptCond := valCond.(ConditionConf)
		out.ConditionList, out.RelateType = m.genConditionConfPb(exemptCond)
	}

	result := true
	if valCond, ok := m.cgc.accessCondMap.GetSyncMap(gameType).Load(uint32(pb.CondType_CondType_AccessCond)); ok {
		cond := valCond.(ConditionConf)
		for i, v := range cond.CondList {
			if i > 0 {
				if result && cond.Relate == uint32(pb.RelateType_RelateType_Or) {
					// 前面的条件满足 且 条件的关联关系为or
					break
				}

				if !result && cond.Relate == uint32(pb.RelateType_RelateType_And) {
					// 前面的条件不满足 且 一条件的关联关系为And
					break
				}
			}
			// 进行一级条件的检查
			result = checkFirstCond(ctx, uid, v.Relate, v.SubList, userValue)
		}
		out.Access = result
	}

	if !out.Access && out.Switch {
		log.InfoWithCtx(ctx, "CheckGameEntryAccess [cannot] uid:%v,gameType:%d, check condition fail", uid, gameType)
	}
	return out, nil
}

func checkFirstCond(ctx context.Context, uid, relate uint32, condList []SubCond, userValue *entity.UserCondValue) bool {
	result := true
	for i, v := range condList {
		if i > 0 {
			if result && relate == uint32(pb.RelateType_RelateType_Or) {
				// 前面的条件满足 且 条件的关联关系为or
				break

			} else {
				if !result && relate == uint32(pb.RelateType_RelateType_And) {
					// 前面的条件不满足 且 条件的关联关系为And
					break
				}
			}
		}

		// 具体条件的检查
		var err error
		result, err = checkSubCond(v, userValue)
		if err != nil {
			log.ErrorWithCtx(ctx, "checkFirstCond fail,[cannot] uid:%d,condType invalid,condType:%d", uid, v.CType)
			return false
		}

	}

	return result
}

func checkSubCond(condVal SubCond, userVal *entity.UserCondValue) (bool, error) {
	switch condVal.CType {
	case uint32(pb.ConditionType_ConditionType_Nobility):
		return userVal.Nobility >= uint64(condVal.Value), nil
	case uint32(pb.ConditionType_ConditionType_Wealth):
		return userVal.Wealth >= uint64(condVal.Value), nil
	case uint32(pb.ConditionType_ConditionType_Charm):
		return userVal.Charm >= uint64(condVal.Value), nil
	case uint32(pb.ConditionType_ConditionType_PlatformLv):
		return userVal.PlatformLv >= condVal.Value, nil
	case uint32(pb.ConditionType_ConditionType_RechargeThirty):
		return userVal.RechargeThirty >= condVal.Value, nil
	case uint32(pb.ConditionType_ConditionType_RechargeEighty):
		return userVal.RechargeNinety >= condVal.Value, nil
	case uint32(pb.ConditionType_ConditionType_RechargeHundrednEighty):
		return userVal.RechargeOez >= condVal.Value, nil
	default:
		return false, errors.New("condType invalid")
	}
}

func (m *GameEntryMgr) GetChannelSimpleInfo(ctx context.Context, uid, channelId uint32) (*channelPb.ChannelSimpleInfo, error) {
	ret, err, _ := m.sg.Do(fmt.Sprintf("get_channel_info_%d", channelId), func() (interface{}, error) {
		ctxN, cancel := context.WithTimeout(ctx, 200*time.Millisecond)
		defer cancel()

		// 查询房间信息
		channelInfo, err := m.channelCli.GetChannelSimpleInfo(ctxN, uid, channelId)
		return channelInfo, err
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelSimpleInfo fail,uid:%d,channelId:%d,err:%v", uid, channelId, err)
		return &channelPb.ChannelSimpleInfo{}, err
	}

	channelInfo, ok := ret.(*channelPb.ChannelSimpleInfo)
	if !ok {
		log.ErrorWithCtx(ctx, "ret.(*channelPb.ChannelSimpleInfo) fail")
		return &channelPb.ChannelSimpleInfo{}, err
	}
	return channelInfo, nil
}

// 检查房间状态handle
func (m *GameEntryMgr) checkUserChannelStatus(ctx context.Context, gameType, uid, channelId uint32, channelInfo *channelPb.ChannelSimpleInfo) bool {
	banStatusList := m.bc.GetChannelBanStatusListByGameType(gameType)
	if len(banStatusList) == 0 {
		return true
	}

	isLocking := channelInfo.GetEnterControlType() == uint32(channelAppPb.EnterControlType_EnterControlType_PASSWD)
	isPGC := m.bc.CheckIfIsPGC(channelInfo.GetChannelType())

	for _, status := range banStatusList {
		if CheckIfIsBan(status, isPGC, isLocking) {
			log.InfoWithCtx(ctx, "checkUserChannelStatus [cannot] see, channelStatus=%d,uid:%d,cid:%d,channelType:%v,channelEnterControlType():%v",
				status, uid, channelId, channelInfo.GetChannelType(), channelInfo.GetEnterControlType())
			return false
		}
	}

	return true
}

func CheckIfIsBan(banStatus uint32, isPGC, isLocking bool) bool {
	log.Debugf("banStatus:%d,isPGC:%v,isLocking:%v", banStatus, isPGC, isLocking)
	switch banStatus {
	case conf.PGCUnlock:
		return isPGC && !isLocking
	case conf.UGCUnlock:
		return !isPGC && !isLocking
	case conf.PGCLocking:
		return isPGC && isLocking
	case conf.UGCLocking:
		return !isPGC && isLocking
	default:
		return true
	}
}

// 从缓存获取UserCondValue
func (m *GameEntryMgr) getUserCondValueFromCache(ctx context.Context, uid, clientIp, marketId, gameType uint32) (*entity.UserCondValue, error) {
	ret, err, shared := m.sg.Do(fmt.Sprintf("get_value_cache_%d", uid), func() (interface{}, error) {
		// 从redis获取
		exist, conf, err := m.redisCache.GetUserCondValue(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserCondValueFromCache fail at cahce.GetUserCondValue,gType:%d", gameType)
			return &entity.UserCondValue{}, err
		}

		if exist {
			return conf, nil
		}

		// 批量获取
		conf, err = m.batGetUserCondValue(ctx, uid, clientIp, marketId, gameType)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserCondValueFromCache fail at batGetUserCondValue")
			return &entity.UserCondValue{}, err
		}

		log.InfoWithCtx(ctx, "GetUserCondValueFromCache batGetUserCondValue uid:%d,conf:%v", uid, conf)

		// 记录缓存
		err = m.redisCache.SetUserCondValue(ctx, uid, conf, time.Duration(m.bc.GetUserAccessStateCacheExp())*time.Second)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserCondValueFromCache fail at SetUserCondValue")
			return &entity.UserCondValue{}, err
		}

		return conf, nil
	})

	if shared {
		log.InfoWithCtx(ctx, "GetUserCondValueFromCache sg shared, uid:%v,ret:%v", uid, ret)
	}

	if _, ok := ret.(*entity.UserCondValue); !ok {
		return &entity.UserCondValue{}, err
	}

	return ret.(*entity.UserCondValue), err
}

// batGetUserCondValue 批量获取用户条件值
func (m *GameEntryMgr) batGetUserCondValue(ctx context.Context, uid, clientIp, marketId, gameType uint32) (*entity.UserCondValue, error) {

	ret, err, shared := m.sg.Do(fmt.Sprintf("bat_get_value_%d", uid), func() (interface{}, error) {
		var err error
		var wg sync.WaitGroup
		out := &entity.UserCondValue{}

		wg.Add(2)

		// 获取实名认证信息
		go func() {
			var e error
			ctxN, cancel := protogrpc.NewContextWithInfoTimeout(ctx, time.Duration(m.bc.GetBatGetTagTimeout())*time.Millisecond)
			defer func() {
				wg.Done()
				cancel()
			}()

			out.VerifyFlag, e = m.getUserVerify(ctxN, uid, gameType, clientIp, marketId)
			if e != nil {
				err = e
				log.ErrorWithCtx(ctx, "batGetUserCondValue,fail getUserVerify")
				return
			}
		}()

		// 获取人群累充标签值
		go func() {
			ctxN, cancel := protogrpc.NewContextWithInfoTimeout(ctx, time.Duration(m.bc.GetBatGetTagTimeout())*time.Millisecond)
			defer func() {
				wg.Done()
				cancel()
			}()

			if len(m.bc.GetIopTagList()) == 0 {
				return
			}

			tagVal, e := m.getIopUserTagValue(ctxN, uid)
			if e != nil {
				//err = e  // 人群包接口的错误 忽略，当作不满足条件
				log.ErrorWithCtx(ctx, "batGetUserCondValue,fail batGetIopTagValue")
			}
			if tagVal != nil {
				out.RechargeThirty, out.RechargeNinety, out.RechargeOez = tagVal.RechargeThirty, tagVal.RechargeNinety, tagVal.RechargeOez
			}
		}()

		// 并发拉取用户财富魅力值、贵族制、平台等级
		out.Nobility, out.Wealth, out.Charm, out.PlatformLv, err = m.getFromReliableSvr(ctx, uid, gameType)
		if err != nil {
			log.ErrorWithCtx(ctx, "batGetUserCondValue,fail getFromReliableSvr")
		}

		wg.Wait()
		return out, err
	})

	if shared {
		log.InfoWithCtx(ctx, "batGetUserCondValue sg shared, uid:%v,ret:%v", uid, ret)
	}

	if _, ok := ret.(*entity.UserCondValue); !ok {
		return &entity.UserCondValue{}, err
	}

	return ret.(*entity.UserCondValue), err
}

// getUserVerify 获取用户认证信息
func (m *GameEntryMgr) getUserVerify(ctx context.Context, uid, gameType, clientIp, marketId uint32) (bool, error) {
	ok := true
	canSee := true

	ctxN, cancel := context.WithTimeout(ctx, 500*time.Millisecond)
	defer cancel()

	// 从认证获取用户信息
	black, youth, _, err := m.GetVerifyInfo(ctxN, uid, clientIp, marketId)
	if err != nil {
		log.WarnWithCtx(ctx, "getUserVerify GetVerifyInfo timeout")
		ctxN, cancel := context.WithTimeout(ctx, 500*time.Millisecond)
		defer cancel()
		black, youth, _, err = m.GetVerifyInfo(ctxN, uid, clientIp, marketId)

		if err != nil {
			log.ErrorWithCtx(ctx, "getUserVerify [cannot] at getVerifyInfo uid:%d,[gType:%d] err:%v", uid, gameType, err)
			return false, err
		}
	}

	// 实名黑名单和青少年，返回不可见
	if black || youth {
		log.InfoWithCtx(ctx, "getUserVerify [cannot] uid:%d,[gType:%d] black=%v,youth=%v", uid, gameType, black, youth)
		ok = false
	}

	if m.bc.GetCheckGuardianStateSwitch() {
		// 未成年人模式不可见入口  -- 理论上app客户端是看不见房间的，但是pc端没有未成年人模式的概念，所以服务端还是需要兜底检查
		inTeenMode, sErr := m.parentGuardianCli.GetParentGuardianState(ctx, uint64(uid))
		if nil != sErr {
			log.ErrorWithCtx(ctx, "CheckIfAccessible [cannot] see,uid:%d, [gType:%d], "+
				"GetParentGuardianState failed:%+v", uid, gameType, sErr)
			return false, sErr
		}
		if inTeenMode {
			log.InfoWithCtx(ctx, "CheckIfAccessible [cannot] see,uid:%d, [gType:%d], "+
				"GetParentGuardianState is on:%+v", uid, gameType, sErr)
			canSee = false
		}
	}

	return ok && canSee, nil
}

func (m *GameEntryMgr) GetVerifyInfo(ctx context.Context, uid, clientIp, mid uint32) (black, youth, adult bool, err error) {
	dataStr := fmt.Sprintf("uid=%d", uid)

	cfg, err := m.bc.GetAuthAccessCfg()
	if err != nil {
		log.ErrorWithCtx(ctx, "getVerifyInfo GetVerifyInfo fail GetAuthAccessCfg,err:%v", err)
		return false, false, true, nil //获取实名信息配置失败不影响到最终的入口判断，如果失败了，就当做符合条件
	}

	hash := md5.New() // #nosec
	hash.Write([]byte(cfg.ClientCaller + dataStr + cfg.ClientSecret))
	sign := hash.Sum(nil)

	data := fmt.Sprintf(`{"data":{"uid":%d},"sign":"%x","client":{"caller":"%s"}}`, uid, string(sign), cfg.ClientCaller)

	log.DebugWithCtx(ctx, "POST %s with %s", cfg.ServerHost, data)

	request, err := http.NewRequest("POST", cfg.ServerHost, strings.NewReader(data))
	if nil != err {
		log.ErrorWithCtx(ctx, "getVerifyInfo Failed to NewRequest err(%+v)", err)
		return
	}

	request = request.WithContext(ctx)
	request.Header.Set("Content-Type", "application/json")
	strUid := strconv.Itoa(int(uid))
	request.Header.Set("uid", strUid)
	if clientIp != 0 {
		ip := make(net.IP, 4)
		binary.LittleEndian.PutUint32(ip, clientIp)
		ipStr := fmt.Sprintf("%v", ip)
		request.Header.Set("userIp", ipStr)
	}
	strMarketId := strconv.Itoa(int(mid))
	request.Header.Set("marketId", strMarketId)

	resp, err := client.Do(request)
	if err != nil {
		log.ErrorWithCtx(ctx, "getVerifyInfo Failed to User Review err(%+v)", err)
		return
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		_ = resp.Body.Close()

		log.ErrorWithCtx(ctx, "getVerifyInfo Failed to ReadAll err(%+v)", err)
		return
	}
	_ = resp.Body.Close()

	result := struct {
		Data *struct {
			IsBlack bool   `json:"isBlackUser"`
			Verify  string `json:"checkStatus"`
		}
		State struct {
			Code    int    `json:"code"`
			Message string `json:"message"`
		}
	}{}

	err = json.Unmarshal(body, &result)
	if err != nil {
		log.ErrorWithCtx(ctx, "getVerifyInfo Failed to Unmarshal url(%s) data(%s) body(%s) err(%+v)",
			cfg.ServerHost, data, string(body), err)
		return
	}

	if nil == result.Data {
		err = errors.New(result.State.Message)

		log.ErrorWithCtx(ctx, "getVerifyInfo Failed to check err(%+v)", err)
		return
	}
	//0:未成年人，1：成年人，2：未实名，3：无法判断
	black = result.Data.IsBlack
	switch result.Data.Verify {
	case "0":
		youth = true
	case "1":
		adult = true
	default:
	}

	if black || youth {
		log.InfoWithCtx(ctx, "getVerifyInfo uid(%d) result(%v) black(%v) youth(%v) adult(%v)",
			uid, utils.ToJson(result), black, youth, adult)
	} else {
		log.Debugf("getVerifyInfo uid(%d) result(%v) black(%v) youth(%v) adult(%v)",
			uid, utils.ToJson(result), black, youth, adult)
	}

	return black, youth, adult, err
}

func (m *GameEntryMgr) getIopUserTagValue(ctx context.Context, uid uint32) (*entity.UserTagValue, error) {
	// 先从缓存获取
	exist, tagVal, err := m.redisCache.GetUserIopTagValue(ctx, uid)

	if exist {
		return tagVal, nil
	}

	if err != nil {
		log.ErrorWithCtx(ctx, "getIopUserTagValue fail GetUserIopTagValue")
	}

	ctxN, cancel := context.WithTimeout(ctx, 500*time.Millisecond)
	defer cancel()

	thirty, ninety, Oez, err := m.batGetIopTagValue(ctxN, uid, m.bc.GetIopTagList())
	if err != nil {
		ctxN, cancel := context.WithTimeout(ctx, 500*time.Millisecond)
		defer cancel()
		// 重试一次
		thirty, ninety, Oez, err = m.batGetIopTagValue(ctxN, uid, m.bc.GetIopTagList())
		if err != nil {
			log.ErrorWithCtx(ctx, "getIopUserTagValue fail batGetIopTagValue")
			return &entity.UserTagValue{}, err
		}
	}

	tagVal = &entity.UserTagValue{
		RechargeThirty: thirty,
		RechargeNinety: ninety,
		RechargeOez:    Oez,
	}

	// set cache
	err = m.redisCache.SetUserIopTagValue(ctx, uid, tagVal, time.Duration(m.bc.GetUserIopTagValueCacheExp())*time.Second)
	if err != nil {
		log.ErrorWithCtx(ctx, "getIopUserTagValue fail SetUserIopTagValue")
		//return &entity.UserTagValue{}, err
	}

	return tagVal, nil
}

// getFromReliableSvr 获取贵族值、财富、魅力值、平台等级
func (m *GameEntryMgr) getFromReliableSvr(ctx context.Context, uid, gameType uint32) (nobility uint64, wealth, charm uint64, platLv uint32, err error) {

	var wg sync.WaitGroup
	wg.Add(3)

	go func() {
		var e error
		ctxN, cancel := protogrpc.NewContextWithInfoTimeout(ctx, time.Duration(m.bc.GetBatGetTagTimeout())*time.Millisecond)
		defer func() {
			wg.Done()
			cancel()
		}()

		nobilityResp, e := m.nobilityCli.GetNobilityInfo(ctxN, uid, false)
		if e != nil {
			err = e
			log.ErrorWithCtx(ctx, "getFromReliableSvr [cannot] at GetNobilityInfo uid:%d,err:%v", uid, err)
			return
		}
		nobility = nobilityResp.GetValue()
	}()

	go func() {
		var e error
		ctxN, cancel := protogrpc.NewContextWithInfoTimeout(ctx, time.Duration(m.bc.GetBatGetTagTimeout())*time.Millisecond)
		defer func() {
			wg.Done()
			cancel()
		}()

		numericResp, e := m.numericCli.GetPersonalNumericV2(ctxN, uid)
		if e != nil {
			err = e
			log.ErrorWithCtx(ctx, "getFromReliableSvr [cannot] at GetPersonalNumeric, [uid:%d],[gType:%d] err:%v", uid, gameType, err)
			return
		}
		wealth = numericResp.GetRich()
		charm = numericResp.GetCharm()
	}()

	go func() {
		var e error
		ctxN, cancel := protogrpc.NewContextWithInfoTimeout(ctx, time.Duration(m.bc.GetBatGetTagTimeout())*time.Millisecond)
		defer func() {
			wg.Done()
			cancel()
		}()

		_, platLv, e = m.expCli.GetUserExp(ctxN, uid)
		if e != nil {
			err = e
			log.ErrorWithCtx(ctx, "getFromReliableSvr [cannot] at GetUserExp uid:%d,[gType:%d] err:%v", uid, gameType, err)
			return
		}
	}()

	wg.Wait()
	return
}

func rangeMapReturnList(targetMap *sync.Map) []uint32 {
	retList := make([]uint32, 0)
	targetMap.Range(func(key, value interface{}) bool {
		cid := key.(uint32)
		retList = append(retList, cid)
		return true
	})
	return retList
}

func (m *GameEntryMgr) transConfMapToPb(gameType, magicSpiritId uint32, condType uint32) *pb.LocalCacheAccessConf {
	out := &pb.LocalCacheAccessConf{}

	if magicSpiritId > 0 {
		if valCond, ok := m.cgc.magicGiftMap.GetSyncMap(magicSpiritId).Load(condType); ok {
			accessCond := valCond.(ConditionConf)
			condList, relate := m.genConditionConfPb(accessCond)

			return &pb.LocalCacheAccessConf{
				CondType:      condType,
				ConditionList: condList,
				RelateType:    relate,
			}
		}

	} else {
		if valCond, ok := m.cgc.accessCondMap.GetSyncMap(gameType).Load(condType); ok {
			accessCond := valCond.(ConditionConf)
			condList, relate := m.genConditionConfPb(accessCond)

			return &pb.LocalCacheAccessConf{
				CondType:      condType,
				ConditionList: condList,
				RelateType:    relate,
			}
		}
	}

	return out
}

// GetLocalCacheConf 获取本地缓存配置
func (m *GameEntryMgr) GetLocalCacheConf(ctx context.Context, req *pb.GetLocalCacheConfReq) (*pb.GetLocalCacheConfResp, error) {
	out := &pb.GetLocalCacheConfResp{}
	gameType := req.GetGameType()

	if state, ok := m.cgc.accessSwitchMap.m.Load(gameType); ok {
		// 不在map中，默认开关开启
		if state.(uint32) == mysql.SwitchOpen {
			out.State = true
		}
	}

	listType := gameType
	if gameType == uint32(pb.NewChanceGameType_NewChanceGameType_GoldSmash) {
		listType = uint32(pb.NewChanceGameType_NewChanceGameType_SmashEgg)
	}

	out.ChannelList = rangeMapReturnList(m.cgc.channelBlackMap.GetSyncMap(listType))
	out.UserBlackList = rangeMapReturnList(m.cgc.userBlackMap.GetSyncMap(listType))
	out.UserWhiteList = rangeMapReturnList(m.cgc.userWhiteMap.GetSyncMap(listType))

	out.ConfList = append(out.ConfList, m.transConfMapToPb(gameType, req.GetMagicSpiritId(), uint32(pb.CondType_CondType_AccessCond)))
	out.ConfList = append(out.ConfList, m.transConfMapToPb(gameType, req.GetMagicSpiritId(), uint32(pb.CondType_CondType_ExemptCond)))

	if val, ok := m.cgc.openTimeMap.m.Load(gameType); ok {
		openTime := val.(*GameOpenTime)
		out.BeginTime = uint32(openTime.BeginTime.Unix())
		out.EndTime = uint32(openTime.EndTime.Unix())
	}

	return out, nil
}

// GetUserValue 获取用户数值
func (m *GameEntryMgr) GetUserValue(ctx context.Context, uid uint32) (*pb.GetUserValueResp, error) {
	userValue, err := m.getUserCondValueFromCache(ctx, uid, 0, 0, 1)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserValue fail batGetUserCondValue, uid:%d err:%v", uid, err)
		return &pb.GetUserValueResp{}, err
	}

	out := &pb.GetUserValueResp{
		Uid:      uid,
		Nobility: userValue.Nobility,
		//Wealth:         userValue.Wealth,
		//Charm:          userValue.Charm,
		PlatformLv:     userValue.PlatformLv,
		RechargeThirty: userValue.RechargeThirty,
		RechargeNinety: userValue.RechargeNinety,
		RechargeOez:    userValue.RechargeOez,
		AuthResult:     userValue.VerifyFlag,
		WealthV2:       userValue.Wealth,
		CharmV2:        userValue.Charm,
	}

	return out, nil
}

func (m *GameEntryMgr) CheckMagicSpiritAccess(ctx context.Context, req *pb.CheckMagicSpiritAccessReq, cid uint32, channelInfo *channelPb.ChannelSimpleInfo) (*pb.CheckMagicSpiritAccessResp, error) {
	out := &pb.CheckMagicSpiritAccessResp{}

	uid := req.GetUid()
	gameType := uint32(pb.NewChanceGameType_NewChanceGameType_MagicSpirit)

	// 大房检测
	if _, ok := m.superChannelMap.Load(cid); ok {
		log.InfoWithCtx(ctx, "CheckGameEntryAccess [cannot] uid:%d, gType:%d,channel:%d superChannel", uid, gameType, cid)
		out.Switch = false
		return out, nil
	}

	// 白名单用户是最高等级，略过所有检查
	if _, ok := m.cgc.userWhiteMap.GetSyncMap(gameType).Load(uid); ok {
		log.InfoWithCtx(ctx, "CheckGameEntryAccess [can] uid:%d, gType:%d,user white", uid, gameType)

		accessList := make([]*pb.MagicSpiritAccess, 0)
		if req.GetCheckAll() {
			for magicId := range m.cgc.magicGiftMap.m {
				if magicId == 0 {
					continue
				}
				accessList = append(accessList, &pb.MagicSpiritAccess{
					MagicSpiritId: magicId,
					Access:        true,
				})
			}
		} else {
			accessList = append(accessList, &pb.MagicSpiritAccess{
				MagicSpiritId: req.GetMagicSpiritId(),
				Access:        true,
			})
		}

		out.Switch = true
		out.ConfList = accessList
		return out, nil
	}

	// 判断开关
	if state, ok := m.cgc.accessSwitchMap.m.Load(gameType); ok {
		out.Switch = state.(uint32) == mysql.SwitchOpen

		// 不在map中，默认开关开启
		if !out.Switch {
			log.InfoWithCtx(ctx, "CheckGameEntryAccess [cannot] uid:%d,cid:%d, gType:%d, switch close", uid, cid, gameType)
			return out, nil
		}
	}

	// 房间黑名单
	if _, ok := m.cgc.channelBlackMap.GetSyncMap(gameType).Load(cid); ok {
		log.InfoWithCtx(ctx, "CheckGameEntryAccess [cannot] uid:%d, gType:%d,channel black", uid, gameType)
		out.Switch = false
		return out, nil
	}

	// 房间状态检查
	if !m.checkUserChannelStatus(ctx, gameType, uid, cid, channelInfo) {
		out.Switch = false
		return out, nil
	}

	// 用户黑名单
	if _, ok := m.cgc.userBlackMap.GetSyncMap(gameType).Load(uid); ok {
		log.InfoWithCtx(ctx, "CheckGameEntryAccess [cannot] uid:%d, gType:%d,user black", uid, gameType)
		out.Switch = false
		return out, nil
	}

	// 获取用户CondValue
	userValue, err := m.getUserCondValueFromCache(ctx, uid, 0, 0, gameType)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckGameEntryAccess [cannot] GetUserCondValueFromCache fail")
		return out, err
	}

	// 检测未成年人和黑产
	if !userValue.VerifyFlag {
		log.InfoWithCtx(ctx, "CheckGameEntryAccess [cannot] uid:%d, gType:%d,user black or youth", uid, gameType)
		return out, nil
	}

	accessList := make([]*pb.MagicSpiritAccess, 0, len(m.cgc.magicGiftMap.m))

	if req.GetCheckAll() {
		for magicId := range m.cgc.magicGiftMap.m {
			if magicId == 0 {
				continue
			}

			if _, ok := m.cgc.magicGiftMap.GetSyncMap(magicId).Load(uint32(pb.CondType_CondType_AccessCond)); !ok {
				if _, ok := m.cgc.magicGiftMap.GetSyncMap(magicId).Load(uint32(pb.CondType_CondType_ExemptCond)); !ok {
					continue
				}
			}

			pass, condList, relateType := m.checkCertainCondition(ctx, uid, magicId, userValue)
			accessList = append(accessList, &pb.MagicSpiritAccess{
				MagicSpiritId: magicId,
				Access:        pass,
				ConditionList: condList,
				RelateType:    relateType,
			})
		}
	} else {
		if req.GetMagicSpiritId() > 0 {
			pass, condList, relateType := m.checkCertainCondition(ctx, uid, req.GetMagicSpiritId(), userValue)
			accessList = append(accessList, &pb.MagicSpiritAccess{
				MagicSpiritId: req.GetMagicSpiritId(),
				Access:        pass,
				ConditionList: condList,
				RelateType:    relateType,
			})
		}
	}

	out.ConfList = accessList
	return out, nil
}

func (m *GameEntryMgr) checkCertainCondition(ctx context.Context, uid, magicId uint32, userValue *entity.UserCondValue) (
	pass bool, condList []*pb.AccessCondition, relateType uint32) {
	// 补充豁免条件配置
	if valCond, ok := m.cgc.magicGiftMap.GetSyncMap(magicId).Load(uint32(pb.CondType_CondType_ExemptCond)); ok {
		exemptCond := valCond.(ConditionConf)
		condList, relateType = m.genConditionConfPb(exemptCond)
	}

	result := true
	if valCond, ok := m.cgc.magicGiftMap.GetSyncMap(magicId).Load(uint32(pb.CondType_CondType_AccessCond)); ok {
		cond := valCond.(ConditionConf)
		for i, v := range cond.CondList {
			if i > 0 {
				if result && cond.Relate == uint32(pb.RelateType_RelateType_Or) {
					// 前面的条件满足 且 条件的关联关系为or
					break
				}

				if !result && cond.Relate == uint32(pb.RelateType_RelateType_And) {
					// 前面的条件不满足 且 一条件的关联关系为And
					break
				}
			}
			// 进行一级条件的检查
			result = checkFirstCond(ctx, uid, v.Relate, v.SubList, userValue)
		}
		pass = result
	}

	return
}
