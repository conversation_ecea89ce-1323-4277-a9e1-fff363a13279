package manager

import (
	"context"
	crand "crypto/rand"
	"golang.52tt.com/clients/account"
	"golang.52tt.com/clients/channel"
	"golang.52tt.com/clients/exp"
	"golang.52tt.com/clients/nobility"
	numeric "golang.52tt.com/clients/numeric-go"
	parentGuardian "golang.52tt.com/clients/parent-guardian"
	"golang.52tt.com/pkg/coroutine"
	"golang.52tt.com/pkg/log"
	channelPb "golang.52tt.com/protocol/app/channel"
	superchannelcreator "golang.52tt.com/services/super-channel/channel-creator/client"
	"golang.52tt.com/services/tt-rev/chance-game-entry/internal/cache"
	"golang.52tt.com/services/tt-rev/chance-game-entry/internal/conf"
	"golang.52tt.com/services/tt-rev/chance-game-entry/internal/mysql"
	"golang.org/x/sync/singleflight"
	"math/big"
	"reflect"
	"sync"
	"time"
)

type GameEntryMgr struct {
	shutDown chan interface{}
	wg       sync.WaitGroup
	//sc         conf.IServiceConfigT
	bc         conf.IBusinessConfManager
	store      mysql.IEntryMysql
	redisCache cache.ICache

	cgc             ChanceGameConf
	superChannelMap sync.Map
	sg              *singleflight.Group

	channelCli                channel.IClient
	accountCli                account.IClient
	numericCli                numeric.IClient
	expCli                    exp.IClient
	parentGuardianCli         parentGuardian.IClient
	nobilityCli               nobility.IClient
	superChannelCreatorClient *superchannelcreator.SuperChannelCreatorClient
}

// MapWithVersion 主要是大量读的情况，写的情况很少，所以这里还是考虑用sync.Map
type MapWithVersion struct {
	m           map[uint32]*sync.Map
	versionTime uint32
}

// GetSyncMap 入参中的key对应的一般是gameType，在MagicGiftMap中，key对应的是magic_spirit_id
func (mg *MapWithVersion) GetSyncMap(key uint32) *sync.Map {
	if v, ok := mg.m[key]; ok {
		return v
	} else {
		mg.m[key] = &sync.Map{}
		return mg.m[key]
	}
}

type SingleMapWithVer struct {
	m           sync.Map
	versionTime uint32
}

type ChanceGameConf struct {
	userWhiteMap    MapWithVersion   // 用户白名单
	userBlackMap    MapWithVersion   // 用户黑名单
	channelBlackMap MapWithVersion   // 房间黑名单
	accessCondMap   MapWithVersion   // 可见人群配置 / 豁免条件配置
	accessSwitchMap SingleMapWithVer // 玩法开关

	gameNotifyMap SingleMapWithVer // 玩法提醒信息

	// 增加幸运礼物配置Map
	magicGiftMap MapWithVersion

	// 玩法开放时间配置
	openTimeMap SingleMapWithVer // 玩法开放时间Map
}

type GameOpenTime struct {
	BeginTime time.Time
	EndTime   time.Time
}

type WrapperDependCli struct {
	ChannelCli        channel.IClient
	AccountCli        account.IClient
	NumericCli        numeric.IClient
	ExpCli            exp.IClient
	ParentGuardianCli parentGuardian.IClient
	NobilityCli       nobility.IClient
}

// SubCond 二级条件
type SubCond struct {
	CType uint32 // condType
	Value uint32 // Threshold
}

// FirstLayerCond 一级条件
type FirstLayerCond struct {
	SubList []SubCond
	Relate  uint32
}

type ConditionConf struct {
	CondList []FirstLayerCond
	Relate   uint32
}

func (c ConditionConf) IsEmpty() bool {
	return reflect.DeepEqual(c, ConditionConf{})
}

func NewGameEntryMgr(store mysql.IEntryMysql, redisCache cache.ICache, bc conf.IBusinessConfManager,
	dependCli WrapperDependCli,
) IGameEntryMgr {

	superChannelCreatorClient, _ := superchannelcreator.NewClient()

	mgr := &GameEntryMgr{
		shutDown:   make(chan interface{}),
		bc:         bc,
		store:      store,
		redisCache: redisCache,
		sg:         &singleflight.Group{},

		channelCli:                dependCli.ChannelCli,
		accountCli:                dependCli.AccountCli,
		numericCli:                dependCli.NumericCli,
		expCli:                    dependCli.ExpCli,
		parentGuardianCli:         dependCli.ParentGuardianCli,
		nobilityCli:               dependCli.NobilityCli,
		superChannelCreatorClient: superChannelCreatorClient,
	}

	mgr.cgc = ChanceGameConf{
		userWhiteMap: MapWithVersion{
			m:           make(map[uint32]*sync.Map),
			versionTime: 0,
		},
		userBlackMap: MapWithVersion{
			m:           make(map[uint32]*sync.Map),
			versionTime: 0,
		},
		channelBlackMap: MapWithVersion{
			m:           make(map[uint32]*sync.Map),
			versionTime: 0,
		},
		accessCondMap: MapWithVersion{
			m:           make(map[uint32]*sync.Map),
			versionTime: 0,
		},
		accessSwitchMap: SingleMapWithVer{
			m:           sync.Map{},
			versionTime: 0,
		},
		gameNotifyMap: SingleMapWithVer{
			m:           sync.Map{},
			versionTime: 0,
		},
		magicGiftMap: MapWithVersion{
			m:           make(map[uint32]*sync.Map),
			versionTime: 0,
		},
		openTimeMap: SingleMapWithVer{
			m:           sync.Map{},
			versionTime: 0,
		},
	}

	unifyValue := true
	// 大房缓存
	coroutine.FixIntervalExecWithDelay(func() {
		// 获取活跃房间
		ctx, cancel := context.WithTimeout(context.Background(), 20*time.Second)
		defer cancel()
		channelMap, _ := superChannelCreatorClient.GetAllActiveChannelMap(ctx, 0)
		if 0 == len(channelMap) {
			return
		}

		channelList := channelMap[uint32(channelPb.ChannelType_CPL_SUPER_CHANNEL_TYPE)] // 获取大房列表
		var tmpChannelMap sync.Map
		for _, cid := range channelList {
			tmpChannelMap.Store(cid, unifyValue)
		}

		// 清理缓存数据
		mgr.superChannelMap.Range(func(key, value interface{}) bool {
			_, ok := tmpChannelMap.Load(key)
			if !ok {
				mgr.superChannelMap.Delete(key)
				return false
			}
			return true
		})
		// 更新缓存
		for _, chId := range channelList {
			_, _ = mgr.superChannelMap.LoadOrStore(chId, unifyValue)
		}

	}, time.Second*15, time.Millisecond*100*time.Duration(randInt64(10)+1))

	return mgr
}

func randInt64(max int64) int64 {
	n, err := crand.Int(crand.Reader, big.NewInt(max))
	if err != nil {
		log.Errorf("randInt64 error: %v", err)
		return 0
	}
	return n.Int64()
}

func (m *GameEntryMgr) ShutDown() {
	close(m.shutDown)
	m.wg.Wait()
	m.bc.Close()
}

// 这里需要做的转换好多，看看有没有问题优化方案
func (m *GameEntryMgr) CheckOpenTime(gameType uint32) bool {
	if val, ok := m.cgc.openTimeMap.m.Load(gameType); ok {
		openTime := val.(*GameOpenTime)
		return openTime.BeginTime.Before(time.Now()) && time.Now().Before(openTime.EndTime)
	}
	return false
}
