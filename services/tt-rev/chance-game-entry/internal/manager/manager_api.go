package manager

import (
	context "context"
	pb "golang.52tt.com/protocol/services/chance-game-entry"
	channelPb "golang.52tt.com/protocol/services/channelsvr"
	sync "sync"
	time "time"
)

type IGameEntryMgr interface {
	AddBlackChannelListV2(ctx context.Context, channelList []*pb.ListItemConf) error
	AddUserBWListV2(ctx context.Context, userList []*pb.ListItemConf, listType uint32) error
	BatDelChannelBWListV2(ctx context.Context, cidList []uint32) error
	BatDelUserBWListV2(ctx context.Context, uidList []uint32, listType uint32) error
	BatGetNotifyInfoFromCache(ctx context.Context, req *pb.BatGetNotifyInfoReq) (*pb.BatGetNotifyInfoResp, error)
	CheckGameEntryAccess(ctx context.Context, req *pb.CheckGameEntryAccessReq, cid, gameType uint32, channelInfo *channelPb.ChannelSimpleInfo) (*pb.GameEntryAccess, error)
	CheckMagicSpiritAccess(ctx context.Context, req *pb.CheckMagicSpiritAccessReq, cid uint32, channelInfo *channelPb.ChannelSimpleInfo) (*pb.CheckMagicSpiritAccessResp, error)
	DelMagicAccessConfByMagicId(ctx context.Context, magicId, condType uint32) error
	GetAllChanceGameSwitch(ctx context.Context) ([]*pb.ChanceGameSwitchState, error)
	GetChanceGameAccessCond(ctx context.Context, gameType, condType uint32) (*pb.GetChanceGameAccessCondResp, error)
	GetChannelBWListInfoV2(ctx context.Context, listType, page, count uint32) (*pb.GetChannelBWListInfoV2Resp, error)
	GetChannelSimpleInfo(ctx context.Context, uid, channelId uint32) (*channelPb.ChannelSimpleInfo, error)
	GetGameOpenTime(ctx context.Context, req *pb.GetGameSwitchOpenTimeReq) (*pb.GetGameSwitchOpenTimeResp, error)
	GetLocalCacheConf(ctx context.Context, req *pb.GetLocalCacheConfReq) (*pb.GetLocalCacheConfResp, error)
	GetMagicSpiritAccessConfList(ctx context.Context, condType uint32) (*pb.GetMagicSpiritAccessCondResp, error)
	GetNotifyInfoByGameType(ctx context.Context, gameType uint32) ([]*pb.NotifyInfo, error)
	GetUserBWListInfoV2(ctx context.Context, listType, page, count uint32) (*pb.GetUserBWListInfoV2Resp, error)
	GetUserValue(ctx context.Context, uid uint32) (*pb.GetUserValueResp, error)
	GetVerifyInfo(ctx context.Context, uid, clientIp, mid uint32) (black, youth, adult bool, err error)
	SearchByChannelId(ctx context.Context, channelId uint32) (*pb.GetChannelBWListInfoV2Resp, error)
	SearchByUid(ctx context.Context, uid, listType uint32) (*pb.GetUserBWListInfoV2Resp, error)
	SetChanceGameAccessCond(ctx context.Context, req *pb.SetChanceGameAccessCondReq) (*pb.SetChanceGameAccessCondResp, error)
	SetChanceGameNotify(ctx context.Context, req *pb.SetGameNotifyInfoReq) error
	SetChanceGameSwitch(ctx context.Context, gameType uint32, op bool) error
	SetGameOpenTime(ctx context.Context, req *pb.SetGameSwitchOpenTimeReq) error
	ShutDown()
	StartTimer()
	TimerHandle(d time.Duration, handle func() error)
	UpdateAccessConf() error
	UpdateChannelBlackList() error
	UpdateGameNotifyInfo() error
	UpdateGameOpenTime() error
	UpdateGameSwitchConf() error
	UpdateUserBlackList() error
	UpdateUserWhiteList() error
	CheckOpenTime(gameType uint32) bool
}

type ICheckTagIdReq interface {
	GenSign(secretKey string)
}

type IMapWithVersion interface {
	GetSyncMap(key uint32) *sync.Map
}

type IConditionConf interface {
	IsEmpty() bool
}
