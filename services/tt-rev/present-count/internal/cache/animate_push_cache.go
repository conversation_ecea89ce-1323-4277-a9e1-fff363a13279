package cache

import (
	"fmt"
	"golang.52tt.com/pkg/log"
)

func genLevelAnimateFlagKey(channelId uint32) string {
	return fmt.Sprintf("channel_lv_animate_%d", channelId)
}

func (r *PresentCountCache) SAddChannelLevelAnimateFlag(channelId, uid, level uint32) (setSuc bool, err error) {
	key := genLevelAnimateFlagKey(channelId)
	mem := fmt.Sprintf("%d_%d", uid, level)

	n, err := r.redisCli.SAdd(key, mem).Result()
	if n == 1 {
		setSuc = true
	}

	return
}

//func (r *PresentCountCache) CheckChannelLevelAnimate(channelId, uid, level uint32) (bool, error) {
//	key := genLevelAnimateFlagKey(channelId)
//	mem := fmt.Sprintf("%d_%d", uid, level)
//
//	return r.redisCli.SIsMember(key, mem).Result()
//}

func (r *PresentCountCache) SRemUserChannelLevelAnimateFlag(channelId, uid uint32, levelList []uint32) {
	key := genLevelAnimateFlagKey(channelId)

	for _, v := range levelList {
		mem := fmt.Sprintf("%d_%d", uid, v)
		err := r.redisCli.SRem(key, mem).Err()
		if err != nil {
			log.Errorf("SRemUserChannelLevelAnimateFlag fail,channelId:%d, mem:%d", channelId, mem)
		}
	}
}

func (r *PresentCountCache) DelChannelLevelAnimateKey(channelId uint32) error {
	key := genLevelAnimateFlagKey(channelId)
	err := r.redisCli.Del(key).Err()
	if err != nil {
		log.Errorf("DelChannelLevelAnimateKey fail,channelId:%d,err:%v", channelId, err)
	}
	return err
}
