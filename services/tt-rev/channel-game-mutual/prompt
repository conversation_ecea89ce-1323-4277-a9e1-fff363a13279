------------------------------对话1---------------------------------------------------
假设你是一名golang高级工程师。

1 项目需求:
    实现一个简单的房间游戏登记系统，记录房间当前在玩的小游戏。
    - 增加在玩游戏记录
    - 删除在玩游戏记录
    - 获取指定房间在玩游戏列表
2 工程目录结构:
  - mgr: 功能逻辑层。定义功能接口Mgr，并实现功能逻辑。功能逻辑依赖存储接口Cache。
  - cache: 存储逻辑层。定义存储接口Cache，并实现具体的存储逻辑。
3 存储:
    使用redis存储。
4 约束:
  - 所有的接口方法都要包含context上下文参数
  - 接口方法都要给出具体实现代码
  - 按照项目工程结构分层存放源代码
  - 房间id是uint32类型
  - 游戏id是uint32类型
  - 各层之间的依赖关系，使用接口定义

------------------------------对话2---------------------------------------------------
我已经使用mockgen工具生成了Storage的mock实现，然后基于这个实现，你写出mgr层的单元测试。每个方法使用table test覆盖多条路径

------------------------------对话3---------------------------------------------------
基于miniredis库，编写cache的单元测试代码
