package mgr

import (
    "context"
    "encoding/json"
    "errors"
    "fmt"
    "github.com/fogleman/gg"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/clients/account"
    "golang.52tt.com/protocol/app"
    gaPB "golang.52tt.com/protocol/app"
    pushPb "golang.52tt.com/protocol/app/push"
    pb "golang.52tt.com/protocol/services/channel-dating-game"
    publicNoticePb "golang.52tt.com/protocol/services/public-notice"
    "golang.52tt.com/services/tt-rev/channel-dating-game/internal/conf"
    "image"
    "net/http"
    "net/url"
    "os"
    "strings"
    "time"
)

func (m *Mgr) dateSuccessHandleV2(ctx context.Context, cid, channelLv, uidA, uidB, valA, valB uint32, hatA, hatB *pb.DatingGameHatCfg,
    pushChannel bool, t time.Time, sceneId uint32) error { //NOSONAR
    sceneCfg := &conf.SceneInfo{}
    var exist bool
    var err error
    if sceneId != 0 {
        // 测试新场景，直接从数据库中获取对应的场景配置
        sceneCfg, exist, err = m.getSceneConfBySceneId(ctx, sceneId)
        if err != nil {
            log.Errorf("dateSuccessHandleV2 failed to getSceneConfBySceneId. cid:%d, uidA:%d, uidB:%d, valA:%d, valB:%d, err:%v",
                cid, uidA, uidB, valA, valB, err)
            return err
        }

    } else {
        // 匹配对应场景
        sceneCfg, exist, err = m.GetLikeBeatSceneV2(cid, channelLv, valA+valB, t)
        if err != nil {
            log.Errorf("DateSuccessHandle failed to GetLikeBeatSceneV2. cid:%d, uidA:%d, uidB:%d, valA:%d, valB:%d, err:%v",
                cid, uidA, uidB, valA, valB, err)
            return err
        }
    }

    if !exist {
        log.Errorf("dateSuccessHandleV2  failed to getSceneConf. not match scene, cid:%d, uidA:%d, uidB:%d, valA:%d, valB:%d",
            cid, uidA, uidB, valA, valB)
        return nil
    }

    userMap, err := m.userProfileCli.BatchGetUserProfileV2(ctx, []uint32{uidA, uidB}, true)
    if err != nil {
        log.Errorf("dateSuccessHandleV2 failed to GetUsersMap. cid:%d, uidA:%d, uidB:%d, valA:%d, valB:%d, err:%v",
            cid, uidA, uidB, valA, valB, err)
        return err
    }

    // 生成房间场景牵手图
    channelImageUrl, err := m.CreateChannelImageV2(ctx, cid, userMap[uidA], userMap[uidB], hatA, hatB, sceneCfg)
    if err != nil {
        log.Errorf("dateSuccessHandleV2 failed to CreateChannelImage. cid:%d, uidA:%d, uidB:%d, valA:%d, valB:%d, err:%v",
            cid, uidA, uidB, valA, valB, err)
        return err
    }

    if pushChannel {
        // 房间推送
        for i:=0;i<3;i++{
            err = m.pushChannelSceneMsg(ctx, cid, userMap[uidA], userMap[uidB], channelImageUrl, sceneCfg)
            if err != nil {
                log.Errorf("dateSuccessHandleV2 failed to pushChannelSceneMsg. cid:%d, uidA:%d, uidB:%d, valA:%d, valB:%d, err:%v",
                    cid, uidA, uidB, valA, valB, err)
                continue
            }
            break
        }
    }

    //全服推送
    go m.pushBreakingNewsV3V2(userMap[uidA], userMap[uidB], cid, sceneCfg.NewsId, sceneCfg.TBean)

    // 生成im场景牵手图
    imImageUrl, err := m.CreateImImageV2(ctx, cid, userMap[uidA], userMap[uidB], hatA, hatB, sceneCfg)
    if err != nil {
        log.Errorf("dateSuccessHandleV2 failed to CreateImImage. cid:%d, uidA:%d, uidB:%d, valA:%d, valB:%d, err:%v",
            cid, uidA, uidB, valA, valB, err)
        return err
    }

    // Im推送
    err = m.pushImSceneMsg(ctx, cid, userMap[uidA], userMap[uidB], channelImageUrl, imImageUrl)
    if err != nil {
        log.Errorf("dateSuccessHandleV2 failed to pushImSceneMsg. cid:%d, uidA:%d, uidB:%d, valA:%d, valB:%d, err:%v",
            cid, uidA, uidB, valA, valB, err)
    }

    //发送头像框奖励
    if sceneCfg.HwIdMale != 0 && sceneCfg.HwIdFemale != 0 {
        m.sendHeadWearReward(ctx, cid, uidA, uidB, sceneCfg.HwIdMale, sceneCfg.HwIdFemale)
    }

    log.Infof("dateSuccessHandleV2 cid:%d, uidA:%d, uidB:%d, valA:%d, valB:%d, level:%d, levelScore:%d",
        cid, uidA, uidB, valA, valB, sceneCfg.Level, sceneCfg.TBean)
    m.SendOssReport(ctx, uidA, uidB, cid, valA, valB, sceneCfg.Level, sceneCfg.TBean) // todo看看这里的level怎么处理。映射/直接不用
    m.sendKFKDatingSuccess(uidA, uidB, cid, valA, valB, sceneCfg.Level, sceneCfg.TBean, uint32(sceneCfg.SceneId), sceneCfg.SceneName)
    return nil
}

// GetLikeBeatSceneV2 获取对应心动场景配置
func (m *Mgr) GetLikeBeatSceneV2(channelId, channelLv, totalScore uint32, t time.Time) (sceneConf *conf.SceneInfo, exist bool, err error) {
    sceneTBean := totalScore

    effectiveConfList := m.getEffectiveDrawConf(t)

    if sceneTBean > m.bc.GetChannelLv2MaxTBean(channelLv) {
        if m.bc.GetChannelLv2MaxTBean(channelLv) != 0 {
            sceneTBean = m.bc.GetChannelLv2MaxTBean(channelLv)
        }
        // else 不受房间等级限制，均可展示
    }

    // 房间等级下最大的可见场景
    for _, v := range effectiveConfList {
        if sceneTBean >= v.TBean {
            sceneConf = &conf.SceneInfo{
                SceneId:             v.SceneId,
                Level:               0, // todoTBean2level 的映射
                TBean:               v.TBean,
                EffectUrl:           v.EffectUrl,
                Md5:                 v.Md5,
                BackgroundPicName:   v.BackgroundPicName,
                BackgroundParam:     v.BackgroundParam,
                ImBackgroundPicName: v.ImBackgroundPicName,
                ImBackgroundParam:   m.bc.GetDefaultImBackgroundParam(),
                OpenAllChannel:      v.OpenAllChannel,
                IsTimeLimit:         v.IsTimeLimitType,

                NewsId:     v.BreakNewsId,
                HwIdMale:   v.HWMaleId,
                HwIdFemale: v.HWFemaleId,
                SceneName:  v.SceneName,
                UpdateTs:   v.UpdateTs,
            }
            break
        }
    }

    // 无符合的场景
    if sceneConf == nil {
        log.Infof("GetLikeBeatSceneV2 fail.房间等级下最大的可见场景为空 channelId:%d, val:%d, not scene match", channelId, totalScore)
        //return &conf.SceneInfo{}, false, nil
    }

    // 如果用户可见的最大牵手值正好已经是限时场景，可直接返回
    if sceneTBean == totalScore && sceneConf.IsTimeLimit {
        log.Infof("GetLikeBeatSceneV2 channelId:%d, val:%d, sceneConf:%+v", channelId, totalScore, sceneConf)
        return sceneConf, true, nil
    }

    // 判断是否符合限时活动下，对所有房间开放的场景
    for _, v := range effectiveConfList {
        if v.IsTimeLimitType && v.OpenAllChannel {
            if sceneConf != nil {
                // 若限时场景的牵手值比原来场景的牵手值低，选用原来的牵手值场景
                if sceneConf.TBean > v.TBean {
                    continue
                }
            }
            if totalScore >= v.TBean {
                sceneConf = &conf.SceneInfo{
                    SceneId:             v.SceneId,
                    Level:               0, // todoTBean2level 的映射
                    TBean:               v.TBean,
                    EffectUrl:           v.EffectUrl,
                    Md5:                 v.Md5,
                    BackgroundPicName:   v.BackgroundPicName,
                    BackgroundParam:     v.BackgroundParam,
                    ImBackgroundPicName: v.ImBackgroundPicName,
                    ImBackgroundParam:   m.bc.GetDefaultImBackgroundParam(),
                    OpenAllChannel:      v.OpenAllChannel,
                    IsTimeLimit:         v.IsTimeLimitType,

                    NewsId:     v.BreakNewsId,
                    HwIdMale:   v.HWMaleId,
                    HwIdFemale: v.HWFemaleId,
                    SceneName:  v.SceneName,
                    UpdateTs:   v.UpdateTs,
                }
                break
            }
        }
    }
    // 无符合的场景
    if sceneConf == nil {
        log.Infof("GetLikeBeatSceneV2 fail. channelId:%d, val:%d, not scene match", channelId, totalScore)
        return &conf.SceneInfo{}, false, nil
    }

    log.Infof("GetLikeBeatSceneV2 cid:%d,val:%d sceneConf:%+v", channelId, totalScore, sceneConf)
    return sceneConf, true, nil
}

func (m *Mgr) pushBreakingNewsV3V2(fromUser, toUser *gaPB.UserProfile, channelId, newsId, sceneVal uint32) error {
    ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
    defer cancel()

    triggerType := uint32(pushPb.CommBreakingNewsBaseOpt_COMMON_RICH_TEXT_NEWS)
    prefix := "哇~全服庆贺~ "
    content := "喜结良缘，长相厮守！"
    sceneName := "天作之合"

    var oldContent string
    if newsId == 0 {
        if sceneVal == 1000000 { // 旧全服资源，固定写死牵手值
            triggerType = uint32(pushPb.CommBreakingNewsBaseOpt_TOPEST_DATING_SCENE)
            
        } else if sceneVal == 2000000 { // 旧全服资源，固定写死牵手值
            triggerType = uint32(pushPb.CommBreakingNewsBaseOpt_TOPEST_DATING_SCENE_LV6)
            prefix = "哇~绝美爱情~ "
            content = "相爱穿梭，绝恋千年！"
            sceneName = "永恒爱恋"
        } else if sceneVal == 5200000 { // 固定写死牵手值
            triggerType = uint32(pushPb.CommBreakingNewsBaseOpt_TOPEST_DATING_SCENE_LV7)
            prefix = ""
            content = "甜蜜牵手，梦中の婚礼邀您前来！"
            sceneName = "梦中の婚礼"
            oldContent = "与ta的cp甜蜜牵手，梦中の婚礼邀您前来！"
        } else {
            return nil
        }
    }

    //user & channel
    breakingNewsMessage := &publicNoticePb.CommonBreakingNewsV3{
        FromUid: fromUser.GetUid(),
        FromUserInfo: &publicNoticePb.UserInfo{
            Account: fromUser.GetAccount(),
            Nick:    fromUser.GetNickname(),
        },
        TargetUid: toUser.GetUid(),
        TargetUserInfo: &publicNoticePb.UserInfo{
            Account: toUser.GetAccount(),
            Nick:    toUser.GetNickname(),
        },
        ChannelId:       channelId,
        NewsPrefix:      prefix,
        NewsContent:     content,
        IsOldDeal:       1,
        OldNewsContent:  oldContent,
        DatingSceneName: sceneName,
    }

    //breaking
    breakingNewsMessage.BreakingNewsBaseOpt = &publicNoticePb.CommBreakingNewsBaseOpt{
        TriggerType: triggerType, RollingCount: 2, RollingTime: 10,
        AnnounceScope:    uint32(pushPb.CommBreakingNewsBaseOpt_INSIDE_CHANNEL + pushPb.CommBreakingNewsBaseOpt_OUTSIDE_CHANNEL),
        JumpType:         uint32(pushPb.CommBreakingNewsBaseOpt_JUMP_ClICK_OUTSIDE),
        JumpPosition:     uint32(pushPb.CommBreakingNewsBaseOpt_JUMP_TO_CHANNEL_CLICK),
        AnnouncePosition: uint32(pushPb.CommBreakingNewsBaseOpt_UPPER),
    }
    req := &publicNoticePb.PushBreakingNewsReq{
        CommonBreakingNews: breakingNewsMessage,
    }
    if newsId != 0 {
        req.RichTextNews = &publicNoticePb.RichTextNews{
            NewsId: newsId,
        }
    }

    _, err := m.publicNoticeCli.PushBreakingNews(ctx, req)
    if err != nil {
        log.ErrorWithCtx(ctx, "PushBreakingNewsV3 failed to PushBreakingNews, req:%v, err:%v", req, err)
        return err
    }

    log.Infof("pushBreakingNewsV3 success channelId:%d, newsId：%d, triggerType:%d", channelId, newsId, triggerType)
    return nil
}

func (m *Mgr) sendHeadWearReward(ctx context.Context, channelId uint32, uidA uint32, uidB uint32, maleSuit uint32, femaleSuit uint32) {
    userMap, err := m.accountCli.GetUsersMap(ctx, []uint32{uidA, uidB})
    if err != nil {
        log.Infof("sendLevelReward GetUsersMap failed, uidA:%d, uidB:%d, channelId:%d, err:%v", uidA, uidB, channelId, err)
        return
    }

    userA := userMap[uidA]
    aSuitID := maleSuit
    bSuitID := femaleSuit
    if userA.GetSex() != account.Male {
        aSuitID = femaleSuit
        bSuitID = maleSuit
    }

    m.sendReward(ctx, uidA, uidB, aSuitID)
    m.sendReward(ctx, uidB, uidA, bSuitID)

    log.Infof("sendHeadwearReward: cid:%d, a:%d, b:%d, maleSuit:%d, femaleSuit:%d", channelId, uidA, uidB, aSuitID, bSuitID)
}

func (m *Mgr) getSceneConfBySceneId(ctx context.Context, sceneId uint32) (*conf.SceneInfo, bool, error) {
    list, err := m.st.BatGetDrawConfBySceneId(ctx, []uint32{sceneId})
    if err != nil {
        log.ErrorWithCtx(ctx, "getSceneConfBySceneId failed, sceneId:%d, err:%v", sceneId, err)
        return &conf.SceneInfo{}, false, err
    }
    if len(list) == 0 {
        return &conf.SceneInfo{}, false, fmt.Errorf("getSceneConfBySceneId failed, sceneId:%d", sceneId)
    }

    storeConf := list[0]
    // param 反序列化
    backgroundParam := &conf.BackgroundParam{}
    if err := json.Unmarshal([]byte(storeConf.BackgroudParam), backgroundParam); err != nil {
        log.ErrorWithCtx(ctx, "BatApplyLongTermScene Unmarshal err:%v", err)
        return &conf.SceneInfo{}, false, err
    }
    sceneConf := &conf.SceneInfo{
        SceneId:             uint64(storeConf.SceneId),
        Level:               0,
        TBean:               storeConf.TBeanLimit,
        EffectUrl:           storeConf.EffectUrl,
        Md5:                 storeConf.EffectUrlMd5,
        BackgroundPicName:   storeConf.ChBackgroundPic,
        BackgroundParam:     backgroundParam,
        ImBackgroundPicName: storeConf.ImBackgroundPic,
        ImBackgroundParam:   m.bc.GetDefaultImBackgroundParam(),
        OpenAllChannel:      true, // 测试场景，全局可见
        IsTimeLimit:         false,
        NewsId:              storeConf.BreaknewsId,
        HwIdMale:            storeConf.HwIdMale,
        HwIdFemale:          storeConf.HwIdFemale,
        SceneName:           storeConf.Name,
    }

    log.InfoWithCtx(ctx, "getSceneConfBySceneId success, sceneId:%d, sceneConf:%v", sceneId, sceneConf)
    return sceneConf, true, nil
}

func (m *Mgr) CreateChannelImageV2(ctx context.Context, cid uint32, userA, userB *app.UserProfile, hatA,
    hatB *pb.DatingGameHatCfg, sceneCfg *conf.SceneInfo) (imageUrl string, err error) {
    uidA, uidB := userA.GetUid(), userB.GetUid()

    defer func() {
        if err := recover(); err != nil {
            log.Errorf("CreateChannelImageV2 panic. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
        }
    }()

    if sceneCfg == nil {
        log.Errorf("CreateChannelImageV2 fail. sceneCfg == nil. uid:%v, cid:%d", []uint32{uidA, uidB}, cid)
        return
    }

    drawConf := m.bc.GetDrawConfList()
    if drawConf == nil {
        log.Errorf("CreateChannelImageV2 fail. drawConf == nil. uid:%v, cid:%d", []uint32{uidA, uidB}, cid)
        return
    }

    param := sceneCfg.BackgroundParam
    // 加载头像
    uidAHead, err := m.loadUserHeadImage(ctx, uidA, param.Radius, userA.GetAccount(), drawConf.DefaultHeadPic)
    if err != nil {
        log.Errorf("CreateChannelImageV2 fail to loadUserHeadImage. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
        return "", err
    }

    uidBHead, err := m.loadUserHeadImage(ctx, uidB, param.Radius, userB.GetAccount(), drawConf.DefaultHeadPic)
    if err != nil {
        log.Errorf("CreateChannelImageV2 fail to loadUserHeadImage. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
        return "", err
    }

    bgPic := sceneCfg.BackgroundPicName
    // 加载背景
    bgImage, err := m.loadImgV2(bgPic)
    if err != nil {
        log.Errorf("CreateChannelImageV2 fail to loadImg. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
        return "", err
    }

    c := gg.NewContextForImage(bgImage)
    // 将头像画在背景上
    c.DrawImage(uidAHead, int(param.UserA.X), int(param.UserA.Y))
    c.DrawImage(uidBHead, int(param.UserB.X), int(param.UserB.Y))

    // 画帽子
    if hatA != nil {
        err = m.drawHat(uidA, c, sceneCfg, hatA, false, true)
        if err != nil {
            log.Errorf("CreateChannelImageV2 fail to drawHat. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
            return "", err
        }
    }

    if hatB != nil {
        err = m.drawHat(uidB, c, sceneCfg, hatB, false, false)
        if err != nil {
            log.Errorf("CreateChannelImageV2 fail to drawHat. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
            return "", err
        }
    }

    // 字体文件下载
    fontPath, err := m.loadFont(drawConf.FontFileName)
    if err != nil {
        log.Errorf("CreateChannelImageV2 fail to loadFont. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
    }

    // 加载字体,设置字体大小
    err = c.LoadFontFace(fontPath, 45)
    if err != nil {
        log.Errorf("CreateChannelImageV2 fail to LoadFontFace. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
    }

    diplayId := m.getDisplayId(ctx, cid)
    desc := fmt.Sprintf("我们在语音房 ID:%d 甜蜜牵手", diplayId)
    centerDescX := param.Desc.X + param.Desc.Width/2
    centerDescY := param.Desc.Y + param.Desc.Height/2
    c.SetColor(image.Black)
    c.DrawStringAnchored(desc, float64(centerDescX+1), float64(centerDescY+1), 0.5, 0.5) // 居中展示

    c.SetColor(image.White)
    c.DrawStringAnchored(desc, float64(centerDescX), float64(centerDescY), 0.5, 0.5) // 居中展示

    // 加载字体,重新设置字体大小
    c.LoadFontFace(fontPath, 30)

    nameA := truncateText(string(userA.GetNickname()), 8)
    nameB := truncateText(string(userB.GetNickname()), 8)

    centerAX := param.NameA.X + param.NameA.Width/2
    centerAY := param.NameA.Y + param.NameA.Height/2
    centerBX := param.NameB.X + param.NameB.Width/2
    centerBY := param.NameB.Y + param.NameB.Height/2
    c.SetColor(image.Black)
    c.DrawStringAnchored(nameA, float64(centerAX+1), float64(centerAY+1), 0.5, 0.5) // 居中展示
    c.DrawStringAnchored(nameB, float64(centerBX+1), float64(centerBY+1), 0.5, 0.5) // 居中展示

    c.SetColor(image.White)
    c.DrawStringAnchored(nameA, float64(centerAX), float64(centerAY), 0.5, 0.5) // 居中展示
    c.DrawStringAnchored(nameB, float64(centerBX), float64(centerBY), 0.5, 0.5) // 居中展示

    // 加载字体,重新设置字体大小
    c.LoadFontFace(fontPath, 54)

    dateStr := time.Now().Format("2006.01.02")
    c.ClearPath()
    centerDX := param.Date.X + param.Date.Width/2
    centerDY := param.Date.Y + param.Date.Height/2
    c.SetColor(image.Black)
    c.DrawStringAnchored(dateStr, float64(centerDX+1), float64(centerDY+1), 0.5, 0.5) // 居中展示

    c.SetColor(image.White)
    c.DrawStringAnchored(dateStr, float64(centerDX), float64(centerDY), 0.5, 0.5) // 居中展示

    // 加粗文本,增加描边
    c.SetLineWidth(1)
    c.Stroke()

    imageUrl, err = m.uploadImage(ctx, cid, uidA, uidB, 75, c.Image())
    if err != nil {
        log.Errorf("CreateChannelImageV2 fail to uploadImage. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
        return imageUrl, err
    }

    log.Infof("CreateChannelImageV2 uid:%v, cid:%d, sceneLevel:%d, levelScore:%d, imageUrl:%s", []uint32{uidA, uidB}, cid, sceneCfg.Level, sceneCfg.TBean, imageUrl)
    return imageUrl, nil
}

func (m *Mgr) CreateImImageV2(ctx context.Context, cid uint32, userA, userB *app.UserProfile, hatA, hatB *pb.DatingGameHatCfg,
    sceneCfg *conf.SceneInfo) (imageUrl string, err error) {
    uidA, uidB := userA.GetUid(), userB.GetUid()

    defer func() {
        if err := recover(); err != nil {
            log.Errorf("CreateImImageV2 panic. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
        }
    }()

    if sceneCfg == nil {
        log.Errorf("CreateImImageV2 fail. sceneCfg == nil. uid:%v, cid:%d", []uint32{uidA, uidB}, cid)
        return
    }

    drawConf := m.bc.GetDrawConfList()
    if drawConf == nil {
        log.Errorf("CreateImImageV2 fail. drawConf == nil. uid:%v, cid:%d", []uint32{uidA, uidB}, cid)
        return
    }

    param := sceneCfg.ImBackgroundParam
    // 加载头像
    uidAHead, err := m.loadUserHeadImage(ctx, uidA, param.Radius, userA.GetAccount(), drawConf.DefaultHeadPic)
    if err != nil {
        log.Errorf("CreateImImageV2 fail to loadUserHeadImage. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
        return "", err
    }

    uidBHead, err := m.loadUserHeadImage(ctx, uidB, param.Radius, userB.GetAccount(), drawConf.DefaultHeadPic)
    if err != nil {
        log.Errorf("CreateImImageV2 fail to loadUserHeadImage. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
        return "", err
    }

    bgPic := sceneCfg.ImBackgroundPicName
    // 加载背景
    bgImage, err := m.loadImgV2(bgPic)
    if err != nil {
        log.Errorf("CreateImImageV2 fail to loadImg. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
        return "", err
    }

    c := gg.NewContextForImage(bgImage)
    // 将头像画在背景上
    c.DrawImage(uidAHead, int(param.UserA.X), int(param.UserA.Y))
    c.DrawImage(uidBHead, int(param.UserB.X), int(param.UserB.Y))

    // 画帽子
    if hatA != nil {
        err = m.drawHat(uidA, c, sceneCfg, hatA, true, true)
        if err != nil {
            log.Errorf("CreateImImageV2 fail to drawHat. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
            return "", err
        }
    }

    if hatB != nil {
        err = m.drawHat(uidB, c, sceneCfg, hatB, true, false)
        if err != nil {
            log.Errorf("CreateImImageV2 fail to drawHat. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
            return "", err
        }
    }

    // 字体文件下载
    fontPath, err := m.loadFont(drawConf.FontFileName)
    if err != nil {
        log.Errorf("CreateImImageV2 fail to loadFont. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
    }

    // 加载字体,重新设置字体大小
    err = c.LoadFontFace(fontPath, 27)
    if err != nil {
        log.Errorf("CreateImImageV2 fail to LoadFontFace. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
    }

    nameA := truncateText(string(userA.GetNickname()), 5)
    nameB := truncateText(string(userB.GetNickname()), 5)

    centerAX := param.NameA.X + param.NameA.Width/2
    centerAY := param.NameA.Y + param.NameA.Height/2
    centerBX := param.NameB.X + param.NameB.Width/2
    centerBY := param.NameB.Y + param.NameB.Height/2
    c.SetColor(image.Black)
    c.DrawStringAnchored(nameA, float64(centerAX+1), float64(centerAY+1), 0.5, 0.5) // 居中展示
    c.DrawStringAnchored(nameB, float64(centerBX+1), float64(centerBY+1), 0.5, 0.5) // 居中展示

    c.SetColor(image.White)
    c.DrawStringAnchored(nameA, float64(centerAX), float64(centerAY), 0.5, 0.5) // 居中展示
    c.DrawStringAnchored(nameB, float64(centerBX), float64(centerBY), 0.5, 0.5) // 居中展示

    imageUrl, err = m.uploadImage(ctx, cid, uidA, uidB, 75, c.Image())
    if err != nil {
        log.Errorf("CreateImImageV2 fail to uploadImage. uid:%v, cid:%d, err:%v", []uint32{uidA, uidB}, cid, err)
        return imageUrl, err
    }

    log.Infof("CreateImImageV2 uid:%v, cid:%d, sceneLevel:%d, levelScore:%d, imageUrl:%s", []uint32{uidA, uidB}, cid, sceneCfg.Level, sceneCfg.TBean, imageUrl)
    return imageUrl, nil
}

func (m *Mgr) loadImgV2(fileUrl string) (image.Image, error) {
    if fileUrl == "" {
        return nil, errors.New("loadImgV2 fileUrl is empty")
    }
    filePrefix, _ := m.bc.GetFileAndUrlPrefix()
    fileName := urlParse(fileUrl)

    filePath := filePrefix + fileName
    file, err := os.Open(filePath)
    if err != nil {
        file, err = downLoadImageV2(filePath, fileUrl)
    }
    if err != nil {
        log.Errorf("loadImg Open file fail. %v", err)
        return nil, err
    }

    defer file.Close() //nolint

    img, name, err := image.Decode(file)
    if err != nil {
        log.Errorf("image.Decode fail. %v", err)
        return nil, err
    }

    log.Debugf("loadImg type name:%s", name)
    return img, nil
}

func downLoadImageV2(filePath, url string) (*os.File, error) {
    resp, err := http.Get(url) //nolint
    if err != nil {
        log.Errorf("downLoadImage http.Get fail. %s %v", url, err)
        return nil, err
    }

    defer resp.Body.Close()

    if resp.StatusCode != http.StatusOK {
        log.Errorf("downLoadImage http.Get fail. %s %v", url, resp.Status)
        return nil, err
    }

    img, name, err := image.Decode(resp.Body)
    if err != nil {
        log.Errorf("downLoadImage image.Decode fail. %s %v", url, err)
        return nil, err
    }

    log.Debugf("downLoadImage name:%s", name)

    err = savePngImage(img, filePath)
    if err != nil {
        log.Errorf("downLoadImage saveImage fail. %s %v", url, err)
        return nil, err
    }

    return os.Open(filePath)
}

//func (m *Mgr) loadFontV2(fileUrl string) (string, error) {
//    if fileUrl == "" {
//        return "", errors.New("loadFontV2 fileName is empty")
//    }
//    filePrefix, _ := m.bc.GetFileAndUrlPrefix()
//    fileName := urlParse(fileUrl)
//
//    filePath := filePrefix + fileName
//    file, err := os.Open(filePath)
//    if err != nil {
//        err = downLoadFontV2(filePath, fileUrl)
//    }
//
//    if err != nil {
//        log.Errorf("loadFont Open file fail. %v", err)
//        return filePath, err
//    }
//
//    defer file.Close() //nolint
//
//    return filePath, nil
//}

//
//func downLoadFontV2(filePath, url string) error {
//    // 先创建目录
//    dir := path.Dir(filePath)
//    _ = os.MkdirAll(dir, 0777)
//
//    resp, err := http.Get(url) //nolint
//    if err != nil {
//        log.Errorf("downLoadFont http.Get fail. %s %v", url, err)
//        return err
//    }
//
//    defer resp.Body.Close()
//
//    if resp.StatusCode != http.StatusOK {
//        log.Errorf("downLoadFont http.Get fail. %s %v", url, resp.Status)
//        return err
//    }
//
//    date, err := ioutil.ReadAll(resp.Body)
//    if err != nil {
//        log.Errorf("downLoadFont ReadAll fail. %s %v", url, err)
//        return err
//    }
//
//    outFile, err := os.Create(filePath)
//    defer outFile.Close() //nolint
//    if err != nil {
//        log.Errorf("downLoadFont os.Create fail. %v", err)
//        return err
//    }
//
//    b := bufio.NewWriter(outFile)
//    _, err = b.Write(date)
//    if err != nil {
//        log.Errorf("downLoadFont Write fail. %v", err)
//        return err
//    }
//
//    err = b.Flush()
//    if err != nil {
//        log.Errorf("downLoadFont Flush fail. %v", err)
//        return err
//    }
//
//    return nil
//}

func urlParse(picUrl string) string {
    u, err := url.Parse(picUrl)
    if err != nil {
        log.Errorf("urlParse err:%v", err)
    }

    fileName := u.Path[strings.LastIndex(u.Path, "/")+1:]
    log.Infof("fileName:%s", fileName)
    return fileName
}
