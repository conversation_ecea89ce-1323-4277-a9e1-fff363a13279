# 灰度环境的cmd配置命令

logic-cmd-configer --etcd-endpoints=*************:2379 list | grep offer
logic-cmd-configer --etcd-endpoints=*************:2379 update --service offer-room-logic --id 96030 --method /ga.api.offer_room.OfferRoom/OfferRoomGetApplyList
logic-cmd-configer --etcd-endpoints=*************:2379 update --service offer-room-logic --id 96031 --method /ga.api.offer_room.OfferRoom/OfferRoomUserApply
logic-cmd-configer --etcd-endpoints=*************:2379 update --service offer-room-logic --id 96032 --method /ga.api.offer_room.OfferRoom/OfferRoomUserCancelApply
logic-cmd-configer --etcd-endpoints=*************:2379 update --service offer-room-logic --id 96033 --method /ga.api.offer_room.OfferRoom/OfferRoomGetCurOfferingGameInfo
logic-cmd-configer --etcd-endpoints=*************:2379 update --service offer-room-logic --id 96034 --method /ga.api.offer_room.OfferRoom/OfferRoomGetOfferingConfig
logic-cmd-configer --etcd-endpoints=*************:2379 update --service offer-room-logic --id 96035 --method /ga.api.offer_room.OfferRoom/OfferRoomSubmitOfferingSetting
logic-cmd-configer --etcd-endpoints=*************:2379 update --service offer-room-logic --id 96036 --method /ga.api.offer_room.OfferRoom/OfferRoomNamePriceOnce
logic-cmd-configer --etcd-endpoints=*************:2379 update --service offer-room-logic --id 96037 --method /ga.api.offer_room.OfferRoom/OfferRoomNamePriceMax
logic-cmd-configer --etcd-endpoints=*************:2379 update --service offer-room-logic --id 96038 --method /ga.api.offer_room.OfferRoom/OfferRoomOfferingSet
logic-cmd-configer --etcd-endpoints=*************:2379 update --service offer-room-logic --id 96039 --method /ga.api.offer_room.OfferRoom/OfferRoomOfferingPass
logic-cmd-configer --etcd-endpoints=*************:2379 update --service offer-room-logic --id 96040 --method /ga.api.offer_room.OfferRoom/OfferRoomOfferingEnd
logic-cmd-configer --etcd-endpoints=*************:2379 update --service offer-room-logic --id 96041 --method /ga.api.offer_room.OfferRoom/OfferRoomOfferingRelationships
logic-cmd-configer --etcd-endpoints=*************:2379 update --service offer-room-logic --id 96042 --method /ga.api.offer_room.OfferRoom/OfferRoomDeleteRelationship
logic-cmd-configer --etcd-endpoints=*************:2379 update --service offer-room-logic --id 96043 --method /ga.api.offer_room.OfferRoom/OfferRoomOfferingInit
logic-cmd-configer --etcd-endpoints=*************:2379 update --service offer-room-logic --id 96044 --method /ga.api.offer_room.OfferRoom/OfferRoomCardInfo