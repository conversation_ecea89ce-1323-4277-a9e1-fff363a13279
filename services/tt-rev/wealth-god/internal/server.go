package internal

import (
	"context"
	"encoding/json"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"golang.52tt.com/protocol/app/wealth_god_logic"
	checker "golang.52tt.com/protocol/services/channel-operate-permission-checker"
	"golang.52tt.com/protocol/services/demo/echo"
	pb "golang.52tt.com/protocol/services/wealth-god"
	"golang.52tt.com/services/tt-rev/wealth-god/internal/cache"
	"golang.52tt.com/services/tt-rev/wealth-god/internal/config"
	"golang.52tt.com/services/tt-rev/wealth-god/internal/event"
	"golang.52tt.com/services/tt-rev/wealth-god/internal/local_cache"
	"golang.52tt.com/services/tt-rev/wealth-god/internal/mgr"
	"golang.52tt.com/services/tt-rev/wealth-god/internal/rpc"
	"golang.52tt.com/services/tt-rev/wealth-god/internal/store"
	context0 "golang.org/x/net/context"
)

type Server struct {
	mgr *mgr.Mgr
	sub *event.KafkaEvent
}

func (s *Server) GetWealthGodCommonCfg(c context0.Context, request *pb.GetWealthGodCommonCfgRequest) (*pb.GetWealthGodCommonCfgResponse, error) {
	out := &pb.GetWealthGodCommonCfgResponse{}
	defer func() {
		log.DebugWithCtx(c, "GetWealthGodCommonCfg request: %+v, response: %+v", request, out)
	}()

	bc := config.GetDynamicConfig()
	out.ActivityStartTs = bc.ActivityStartTs
	out.ActivityEndTs = bc.ActivityEndTs
	out.NeedStayRoomSecond = config.GetNeedStayRoomMissionSecond()
	for _, item := range bc.GodTriggerConf.PresentTriggerList {
		out.TriggerList = append(out.TriggerList, &pb.GetWealthGodCommonCfgResponse_WealthGodTrigger{
			TriggerType: item.Type,
			TriggerId:   item.Id,
		})
	}
	return out, nil
}

func (s *Server) ReportStayRoomMissionFinish(c context0.Context, request *pb.ReportStayRoomMissionFinishRequest) (*pb.ReportStayRoomMissionFinishResponse, error) {
	out := &pb.ReportStayRoomMissionFinishResponse{}
	defer func() {
		log.InfoWithCtx(c, "ReportStayRoomMissionFinish request: %+v, response: %+v", request, out)
	}()

	err := s.mgr.FinishWealthGodMission(c, request.Uid, request.GodId, wealth_god_logic.WealthMissionType_WEALTH_MISSION_TYPE_STAY_IN_GAME)
	if nil != err {
		log.ErrorWithCtx(c, "ReportStayRoomMissionFinish request: %+v, err: %v", request, err)
		return out, err
	}
	err = s.mgr.PushMissionFinish(c, request.Uid, request.GodId)
	if nil != err {
		log.ErrorWithCtx(c, "ReportStayRoomMissionFinish request: %+v, err: %v", request, err)
	}

	return out, nil
}

func (s *Server) GetWealthMissionInfo(c context0.Context, request *pb.GetWealthMissionInfoRequest) (*pb.GetWealthMissionInfoResponse, error) {
	out := &pb.GetWealthMissionInfoResponse{}
	defer func() {
		log.DebugWithCtx(c, "GetWealthMissionInfo request: %+v, response: %+v", request, out)
	}()

	missionInfoList, err := s.mgr.GetWealthMissionInfo(c, request.Uid, request.GodId)
	if nil != err {
		log.ErrorWithCtx(c, "GetWealthMissionInfo request: %+v, err: %v", request, err)
		return out, err
	}
	out.WealthMissionInfoList = missionInfoList
	return out, nil
}

func (s *Server) GetOneWealthGodChannel(c context0.Context, request *pb.GetOneWealthGodChannelRequest) (*pb.GetOneWealthGodChannelResponse, error) {
	out := &pb.GetOneWealthGodChannelResponse{}
	defer func() {
		log.DebugWithCtx(c, "GetOneWealthGodChannel request: %+v, response: %+v", request, out)
	}()
	channel, err := s.mgr.GetOneWealthGodChannel()
	if nil != err {
		log.ErrorWithCtx(c, "GetOneWealthGodChannel request: %+v, err: %v", request, err)
		return out, err
	}
	out.ChannelId = channel
	return out, nil
}

func (s *Server) GetWealthGodEntry(c context0.Context, request *pb.GetWealthGodEntryRequest) (*pb.GetWealthGodEntryResponse, error) {
	return s.mgr.GetWealthGodEntry(c, request.GetUid(), request.GetChannelId())
}

func NewServer(ctx context.Context, cfg *config.StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)

	cache_, err := cache.NewCache(ctx, cfg.RedisConfig)
	if nil != err {
		log.ErrorWithCtx(ctx, "init redis fail, err: %v", err)
		return nil, err
	}

	store_, err := store.NewStore(ctx, cfg.MongoConfig)
	if nil != err {
		log.ErrorWithCtx(ctx, "init store fail, err: %v", err)
		return nil, err
	}

	rpcCli_, err := rpc.NewClient()
	if nil != err {
		log.ErrorWithCtx(ctx, "init rpc fail, err: %v", err)
		return nil, err
	}

	localCache_, err := local_cache.NewLocalCache(store_)
	if nil != err {
		log.ErrorWithCtx(ctx, "init local cache fail, err: %v", err)
		return nil, err
	}

	mgr_, err := mgr.NewMgr(ctx, store_, cache_, rpcCli_, localCache_)
	if nil != err {
		log.ErrorWithCtx(ctx, "init mgr fail, err: %v", err)
		return nil, err
	}

	sub_, err := event.NewKafkaEvent(cfg, store_, cache_, mgr_, rpcCli_, localCache_)
	if nil != err {
		log.ErrorWithCtx(ctx, "init kafka event fail, err: %v", err)
		return nil, err
	}

	return &Server{mgr: mgr_, sub: sub_}, nil
}

func (s *Server) ShutDown() {
	s.sub.Shutdown()
	s.mgr.Close()
}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

func (s *Server) GetWealthGodDetail(ctx context.Context, req *pb.GetWealthGodDetailRequest) (*pb.GetWealthGodDetailResponse, error) {
	return s.mgr.GetWealthGodDetail(ctx, req)
}

func (s *Server) OpenWealthGodBoxReward(ctx context.Context, req *pb.OpenWealthGodBoxRewardRequest) (*pb.OpenWealthGodBoxRewardResponse, error) {
	return s.mgr.OpenWealthGodBoxReward(ctx, req)
}

func (s *Server) SysOpenWealthGodBoxReward(ctx context.Context, req *pb.SysOpenWealthGodBoxRewardRequest) (*pb.SysOpenWealthGodBoxRewardResponse, error) {
	var godInfo *store.WealthGodRecord
	if err := json.Unmarshal([]byte(req.GodInfoJson), &godInfo); nil != err {
		log.ErrorWithCtx(ctx, "SysOpenWealthGodBoxReward json.Unmarshal err: %v, req: %+v", err, req)
		return nil, err
	}
	return &pb.SysOpenWealthGodBoxRewardResponse{}, s.mgr.SysOpenWealthGodBoxReward(ctx, godInfo, req.Uid)
}

func (s *Server) GetWealthGodBoxRewardList(ctx context.Context, req *pb.GetWealthGodBoxRewardListRequest) (*pb.GetWealthGodBoxRewardListResponse, error) {
	return s.mgr.GetWealthGodBoxRewardList(ctx, req)
}

func (s *Server) CheckOperatePermission(ctx context.Context, req *checker.CheckOperatePermissionReq) (*checker.CheckOperatePermissionResp, error) {
	return s.mgr.CheckOperatePermission(ctx, req)
}
