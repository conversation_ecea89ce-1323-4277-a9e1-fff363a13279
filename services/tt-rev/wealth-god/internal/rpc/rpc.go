package rpc

import (
	"context"
	channel_msg_api "gitlab.ttyuyin.com/bizFund/bizFund/pkg/channel-msg-api"
	"golang.52tt.com/clients/account"
	awardCenterClient "golang.52tt.com/clients/award-center"
	backpacksender "golang.52tt.com/clients/backpack-sender"
	chCli "golang.52tt.com/clients/channel"
	"golang.52tt.com/clients/channelbackground"
	"golang.52tt.com/clients/darkserver"
	imApi "golang.52tt.com/clients/im-api"
	push_notification "golang.52tt.com/clients/push-notification/v2"
	userProfileApi "golang.52tt.com/clients/user-profile-api"
	present_set "golang.52tt.com/protocol/services/present-set"
	public_notice "golang.52tt.com/protocol/services/public-notice"
	wealth_god "golang.52tt.com/protocol/services/wealth-god"
	unifiedSearchService "golang.52tt.com/services/unified-search/client"
)

type Client struct {
	AccountCli      account.IClient
	ProfileCli      userProfileApi.IClient
	ImApiCli        imApi.IClient
	PushCli         push_notification.IClient
	ChannelCli      chCli.IClient
	PresentSetCli   present_set.PresentSetClient
	ChannelMsgCli   channel_msg_api.IClient
	DarkCli         darkserver.IClient
	PublicNoticeCli *public_notice.Client
	SelfCli         wealth_god.WealthGodServiceClient

	BackpackSenderCli backpacksender.IClient
	AwardCenterCli    awardCenterClient.IClient
	ChannelBgCli      channelbackground.IClient
	SearchClient      unifiedSearchService.IUnifiedSearchClient
}

func NewClient() (*Client, error) {
	client := &Client{}
	ctx := context.Background()

	client.PushCli = push_notification.NewIClient()
	client.AccountCli = account.NewIClient()
	client.ProfileCli, _ = userProfileApi.NewClient()
	client.ImApiCli, _ = imApi.NewClient()
	client.ChannelCli = chCli.NewIClient()
	client.PresentSetCli, _ = present_set.NewClient(ctx)
	client.ChannelMsgCli, _ = channel_msg_api.NewIClient()
	client.PublicNoticeCli, _ = public_notice.NewClient(ctx)
	client.DarkCli = darkserver.NewIClient()
	client.SelfCli, _ = wealth_god.NewClient(ctx)

	client.BackpackSenderCli = backpacksender.NewIClient()
	client.AwardCenterCli, _ = awardCenterClient.NewClient()
	client.ChannelBgCli = channelbackground.NewIClient()
	client.SearchClient, _ = unifiedSearchService.NewClient()

	return client, nil
}
