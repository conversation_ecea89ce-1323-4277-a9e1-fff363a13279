package config

import (
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/core/config/ttconfig"
	"golang.52tt.com/protocol/app/wealth_god_logic"
	"sync/atomic"
)

type DynamicConfig struct {
	ActivityStartTs         int64                  `json:"activity_start_ts"`           // 活动开始时间戳，单位秒
	ActivityEndTs           int64                  `json:"activity_end_ts"`             // 活动结束时间戳，单位秒
	GodTriggerConf          GodTriggerConfig       `json:"god_trigger_conf"`            // 财神触发配置
	BoxList                 []*BoxConfig           `json:"box_list"`                    // 宝箱配置列表
	UserOpenMaxTimePerSec   int64                  `json:"user_open_max_time_per_sec"`  // 用户开箱每秒最大次数
	SysOpenMaxTimePerSec    int64                  `json:"sys_open_max_time_per_sec"`   // 系统开箱每秒最大次数
	MissionCfgMap           map[uint32]*MissionCfg `json:"mission_cfg_map"`             // 任务配置列表，key为任务类型 see wealth_god_logic.WealthMissionType
	StayRoomMissionLimitCnt uint32                 `json:"stay_room_mission_limit_cnt"` // 停留房间任务限制次数，0表示不限制
	RewardHistoryUrl        string                 `json:"reward_history_url"`          // 奖励记录url
	RewardXmlTpl            string                 `json:"reward_xml_tpl"`              // 奖励公屏xml模板
	GodEndXmlTpl            string                 `json:"god_end_xml_tpl"`             // 财神结束公屏xml模板
}

type MissionCfg struct {
	MissionIcon    string `json:"mission_icon"`     // 任务图标
	MissionName    string `json:"mission_name"`     // 任务名称
	MissionDesc    string `json:"mission_desc"`     // 任务描述
	MissionCntUnit string `json:"mission_cnt_unit"` // 任务计数单位
	NeedMissionCnt uint32 `json:"need_mission_cnt"` // 需要完成计数
}

// GodTriggerConfig 财神触发配置
type GodTriggerConfig struct {
	OpenTbeanCountTrigger      bool             `json:"open_tbean_count_trigger"`      // 是否开启T豆数量触发
	TbeanCountTriggerThreshold uint32           `json:"tbean_count_trigger_threshold"` // T豆数量触发阈值
	OpenPresentTrigger         bool             `json:"open_present_trigger"`          // 是否开启礼物触发
	PresentTriggerList         []PresentTrigger `json:"present_trigger_list"`          // 礼物触发列表
	BanChannelList             []uint32         `json:"ban_channel_list"`              // 禁止触发的房间列表
	GodMissionDuration         int64            `json:"god_mission_duration"`          // 财神任务持续时间，单位秒
	OpenBoxDuration            int64            `json:"open_box_duration"`             // 开宝箱持续时间，单位秒
	NextGodWaitDuration        int64            `json:"next_god_wait_duration"`        // 下一场财神等待时间，单位秒
}

// PresentTrigger 礼物触发配置
type PresentTrigger struct {
	Type uint32 `json:"type"` // 触发类型, 1-礼物, 2-帝王套
	Id   uint32 `json:"id"`   // ID
}

// BoxConfig 宝箱配置
type BoxConfig struct {
	BoxId                  uint32          `json:"box_id"`                    // 宝箱ID
	BoxType                uint32          `json:"box_type"`                  // 宝箱类型/等级，see WealthGodBoxType
	OpenRatio              uint32          `json:"open_ratio"`                // 开启概率，0-100之间
	RewardList             []*RewardConfig `json:"reward_list"`               // 奖励列表
	NoStockUseBoxId        uint32          `json:"no_stock_use_box_id"`       // 无库存时使用的宝箱ID
	IsBottomBox            bool            `json:"is_bottom_box"`             // 是否兜底宝箱，是的话不用检查库存
	NeedCheckStock         bool            `json:"need_check_stock"`          // 是否需要检查库存
	InitialStock           uint32          `json:"initial_stock"`             // 初始库存
	IncomeStockReleaseStep uint32          `json:"income_stock_release_step"` // 收入库存释放步长，单位元
	IncomeStockReleaseCnt  uint32          `json:"income_stock_release_cnt"`  // 收入库存每次释放个数
	TimeStockReleaseStep   uint32          `json:"time_stock_release_step"`   // 时间库存释放步长，单位分钟
	TimeStockReleaseCnt    uint32          `json:"time_stock_release_cnt"`    // 时间库存每次释放个数
	TimeStockReleaseScopes []HourScope     `json:"time_stock_release_scopes"` // 时间库存释放时间段
}

func GetBottomBoxId() uint32 {
	// 返回配置中第一个IsBottomBox为true的宝箱ID
	boxList := GetDynamicConfig().BoxList
	for _, box := range boxList {
		if box.IsBottomBox {
			return box.BoxId
		}
	}
	return 0 // 如果没有找到兜底宝箱，返回0
}

type HourScope struct {
	StartHour uint32 `json:"start_hour"` // 开始小时
	EndHour   uint32 `json:"end_hour"`   // 结束小时
}

// RewardConfig 奖励配置
type RewardConfig struct {
	RewardId           uint32 `json:"reward_id"`              // 奖励ID
	IsRare             bool   `json:"is_rare"`                // 是否稀有奖励
	IconUrl            string `json:"icon_url"`               // 图标URL
	Title              string `json:"title"`                  // 标题，填奖励名称，如超级跑车
	SubTitle           string `json:"sub_title"`              // 副标题，礼物填如“500豆”，其它填如“个人铭牌”
	RewardType         uint32 `json:"reward_type"`            // 奖励类型, see RewardType
	RewardSubType      uint32 `json:"reward_sub_type"`        // 奖励子类型，仅包裹要填。1-T豆 2-红钻
	ItemId             string `json:"item_id"`                // 奖励的物品id
	ItemCnt            uint32 `json:"item_cnt"`               // 奖励的物品数量，礼物填的是个数，其它填的是天数
	TbeanValue         uint64 `json:"tbean_value"`            // T豆价值，礼物需要填。用于统计、风控判断
	RiskControlId      uint32 `json:"risk_control_id"`        // 仅T豆和红钻礼物需要填
	RiskControlKey     string `json:"risk_control_key"`       // 仅T豆和红钻礼物需要填
	OpenRatio          uint32 `json:"open_ratio"`             // 开启概率，0-100之间
	NoStockUseRewardId uint32 `json:"no_stock_use_reward_id"` // 无库存时使用的奖励ID
	IsBottomReward     bool   `json:"is_bottom_reward"`       // 是否兜底奖励，是的话不用检查库存
	NeedCheckStock     bool   `json:"need_check_stock"`       // 是否需要检查库存
	InitialStock       uint32 `json:"initial_stock"`          // 初始库存
}

// GetCornerText 最常返回的角标文案
func (r *RewardConfig) GetCornerText() string {
	if r.RewardType == 1 {
		return fmt.Sprintf("x%d", r.ItemCnt)
	} else {
		return fmt.Sprintf("%d天", r.ItemCnt)
	}
}

// GetRewardName 获取奖励名称
func (r *RewardConfig) GetRewardName() string {
	if r.RewardType == 1 {
		if r.RewardSubType == 1 {
			return fmt.Sprintf("%s豆豆礼物", r.Title)
		} else if r.RewardSubType == 2 {
			return fmt.Sprintf("%s红钻礼物", r.Title)
		} else {
			return fmt.Sprintf("%s礼物", r.Title)
		}
	} else {
		return fmt.Sprintf("%s%s", r.Title, r.SubTitle)
	}
}

// GetRewardNameWithCnt 获取带数量的奖励名称
func (r *RewardConfig) GetRewardNameWithCnt() string {
	rewardName := r.GetRewardName()
	if r.RewardType == 1 {
		return fmt.Sprintf("%s*%d个", rewardName, r.ItemCnt)
	} else {
		return fmt.Sprintf("%s*%d天", rewardName, r.ItemCnt)
	}
}

// GetTitle4History 历史记录用的Title
func (r *RewardConfig) GetTitle4History() string {
	if r.RewardType == 1 {
		return fmt.Sprintf("%sx%d", r.Title, r.ItemCnt)
	} else {
		return fmt.Sprintf("%s%s", r.Title, r.SubTitle)
	}
}

// GetSubTitle4History 历史记录用的SubTitle
func (r *RewardConfig) GetSubTitle4History() string {
	if r.RewardType == 1 {
		return fmt.Sprintf("%s | 消耗1次开箱机会", r.SubTitle)
	} else {
		return fmt.Sprintf("%d天 | 消耗1次开箱机会", r.ItemCnt)
	}
}

// Format
// 配置加载时调用Format报错则返回错误
// 配置更新时调用Format报错则放弃此次更新
func (s *DynamicConfig) Format() error {
	return nil
}

var (
	atomicDynamicConfig *atomic.Value
)

// InitDynamicConfig
// 可以选择外部初始化或者直接init函数初始化
func InitDynamicConfig() error {
	cfg := &DynamicConfig{}
	atomCfg, err := ttconfig.AtomLoad("wealth-god.json", cfg)
	if nil != err {
		return err
	}
	atomicDynamicConfig = atomCfg
	return nil
}

func GetDynamicConfig() *DynamicConfig {
	return atomicDynamicConfig.Load().(*DynamicConfig)
}

func GetBoxConfig(boxId uint32) *BoxConfig {
	boxList := GetDynamicConfig().BoxList
	for _, box := range boxList {
		if box.BoxId == boxId {
			return box
		}
	}
	return nil
}

func GetBottomBoxConfig() *BoxConfig {
	boxList := GetDynamicConfig().BoxList
	for _, box := range boxList {
		if box.IsBottomBox {
			return box
		}
	}
	return nil
}

func GetBoxRewardConfig(boxId, rewardId uint32) *RewardConfig {
	box := GetBoxConfig(boxId)
	if box == nil {
		return nil
	}
	for _, reward := range box.RewardList {
		if reward.RewardId == rewardId {
			return reward
		}
	}
	return nil
}

func GetBoxBottomRewardConfig(boxId uint32) *RewardConfig {
	box := GetBoxConfig(boxId)
	if box == nil {
		return nil
	}
	for _, reward := range box.RewardList {
		if reward.IsBottomReward {
			return reward
		}
	}
	return nil
}

func GetNeedStayRoomMissionSecond() uint32 {
	return GetDynamicConfig().MissionCfgMap[uint32(wealth_god_logic.WealthMissionType_WEALTH_MISSION_TYPE_STAY_IN_GAME)].NeedMissionCnt
}
