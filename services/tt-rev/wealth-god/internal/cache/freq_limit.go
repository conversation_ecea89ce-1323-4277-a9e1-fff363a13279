package cache

import (
	"context"
	"fmt"
	"time"
)

func (c *Cache) checkFreqLimit(ctx context.Context, id string, maxTimePerSec int64) bool {
	key := fmt.Sprintf("wealth_god:%s:%d", id, time.Now().Unix())
	cnt, err := c.cmder.Incr(ctx, key).Result()
	if err != nil {
		return true
	}
	if cnt == 1 {
		_ = c.cmder.Expire(ctx, key, time.Second*3).Err()
	}
	return cnt > maxTimePerSec
}

func (c *Cache) CheckUserFreqLimit(ctx context.Context, maxTimePerSec int64) bool {
	return c.checkFreqLimit(ctx, "user_freq_limit", maxTimePerSec)
}

func (c *Cache) CheckSysFreqLimit(ctx context.Context, maxTimePerSec int64) bool {
	return c.checkFreqLimit(ctx, "sys_freq_limit", maxTimePerSec)
}
