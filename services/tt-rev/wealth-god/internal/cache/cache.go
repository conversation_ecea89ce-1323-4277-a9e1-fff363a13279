package cache

import (
	"context"
	redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
)

type Cache struct {
	cmder redis.Cmdable
}

func NewCache(ctx context.Context, cfg *redisConnect.RedisConfig) (*Cache, error) {
	client, err := redisConnect.NewClient(ctx, cfg)
	if err != nil {
		return nil, err
	}

	c := &Cache{
		cmder: client,
	}
	return c, nil
}

func (c *Cache) Close() error {
	return c.cmder.(redis.Client).Close()
}

func (c *Cache) GetRedisClient() redis.Cmdable {
	return c.cmder
}
