package cache

import (
	"context"
	"testing"
)

func TestCache_IncrAllGiftValue(t *testing.T) {
	ctx := context.Background()
	res, err := cacheCli.IncrAllGiftValue(ctx, 1000)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("IncrAllGiftValue failed: %v", err)
		return
	}
	t.Logf("IncrAllGiftValue success, new value: %d", res)
	res, err = cacheCli.GetAllGiftValue(ctx)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("GetAllGiftValue failed: %v", err)
		return
	}
	t.Logf("GetAllGiftValue success, current value: %d", res)
}
