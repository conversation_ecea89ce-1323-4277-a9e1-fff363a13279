package cache

import (
	"context"
	"golang.52tt.com/pkg/log"
)

func allGiftValueKey() string {
	return "wealth_god:all_gift_value"
}
func (c *Cache) IncrAllGiftValue(ctx context.Context, incrValue int64) (int64, error) {
	if incrValue <= 0 {
		return 0, nil
	}

	// Increment the all gift value in the cache
	newValue, err := c.cmder.IncrBy(ctx, allGiftValueKey(), incrValue).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "IncrAllGiftValue failed, err: %v, incrValue: %d", err, incrValue)
		return 0, err
	}
	log.InfoWithCtx(ctx, "IncrAllGiftValue success, newValue: %d, incrValue: %d", newValue, incrValue)
	return newValue, nil
}

func (c *Cache) GetAllGiftValue(ctx context.Context) (int64, error) {
	value, err := c.cmder.Get(ctx, allGiftValueKey()).Int64()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAllGiftValue failed, err: %v", err)
		return 0, err
	}
	log.InfoWithCtx(ctx, "GetAllGiftValue success, value: %d", value)
	return value, nil
}
