package cache

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	"google.golang.org/grpc/codes"
	"time"
)

const (
	lockPrefix = "wealth_god:lock"
)

func (c *Cache) tryLock(ctx context.Context, key, val string, timeOutList ...time.Duration) error {
	timeOut := 5 * time.Minute
	if len(timeOutList) > 0 {
		timeOut = timeOutList[0]
	}

	for i := 0; i < 3; i++ {
		res, err := c.cmder.SetNX(ctx, key, val, timeOut).Result()
		if err != nil {
			return err
		}
		if res {
			return nil
		}
		time.Sleep(200 * time.Millisecond)
	}
	return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "try lock fail")
}

func (c *Cache) unLock(ctx context.Context, key, val string) error {
	nowVal := c.cmder.Get(ctx, key).Val()
	if val == nowVal {
		return c.cmder.Del(ctx, key).Err()
	}
	return nil
}

func getLockCreateWealthGodKey() string {
	return fmt.Sprintf("%s:%s", lockPrefix, "create_wealth_god")
}

func getLockStayRoomMissionKey(uid uint32) string {
	return fmt.Sprintf("%s_%s:%d", lockPrefix, "mission", uid)
}

func (c *Cache) TryLockStayRoomMission(ctx context.Context, uid uint32, duration time.Duration) error {
	return c.tryLock(ctx, getLockStayRoomMissionKey(uid), "1", duration)
}

func (c *Cache) TryLockCreateWealthGod(ctx context.Context, godId string) error {
	return c.tryLock(ctx, getLockCreateWealthGodKey(), godId, time.Second*10)
}

func (c *Cache) TryLockBoxDivide(ctx context.Context, godType uint32) error {
	key := fmt.Sprintf("%s:%s_%d", lockPrefix, "box_divide", godType)
	return c.tryLock(ctx, key, "1", time.Second*10)
}

func (c *Cache) UnLockBoxDivide(ctx context.Context, godType uint32) error {
	key := fmt.Sprintf("%s:%s_%d", lockPrefix, "box_divide", godType)
	return c.unLock(ctx, key, "1")
}

func (c *Cache) UnLockCreateWealthGod(ctx context.Context, godId string) error {
	return c.unLock(ctx, getLockCreateWealthGodKey(), godId)
}

func getOpenRewardKey(uid uint32, godId string) string {
	return fmt.Sprintf("%s:%d:%s", lockPrefix, uid, godId)
}

func (c *Cache) TryLockOpenReward(ctx context.Context, uid uint32, godId, uuid string) error {
	return c.tryLock(ctx, getOpenRewardKey(uid, godId), uuid, time.Second*10)
}

func (c *Cache) UnLockOpenReward(ctx context.Context, uid uint32, godId, uuid string) error {
	return c.unLock(ctx, getOpenRewardKey(uid, godId), uuid)
}
