package mgr

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/app/wealth_god_logic"
	wealth_god "golang.52tt.com/protocol/services/wealth-god"
	"golang.52tt.com/services/tt-rev/wealth-god/internal/config"
	"time"
)

func (m *Mgr) FinishWealthGodMission(ctx context.Context, uid uint32, godId string, missionType wealth_god_logic.WealthMissionType) error {
	// 检查用户是否在黑名单中(取反)
	needReward := !m.CheckUserIsInBlackList(uid)

	if missionType == wealth_god_logic.WealthMissionType_WEALTH_MISSION_TYPE_STAY_IN_GAME {
		// lock，短时间内上报两次，说明客户端异常
		err := m.cache.TryLockStayRoomMission(ctx, uid, time.Duration(config.GetNeedStayRoomMissionSecond()-5)*time.Second)
		if err != nil {
			log.WarnWithCtx(ctx, "TryLockStayRoomMission err: %v, uid: %d", err, uid)
			return err
		}
		// 增加用户在财神房间停留的次数
		cnt, err := m.cache.IncrStayRoomMissionCnt(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "IncrStayRoomMissionCnt err: %v, uid: %d", err, uid)
			return err
		}
		cfgCnt := config.GetDynamicConfig().StayRoomMissionLimitCnt
		if cfgCnt != 0 && uint32(cnt) > cfgCnt {
			log.WarnWithCtx(ctx, "IncrStayRoomMissionCnt cnt %d > limit %d, uid: %d", cnt, cfgCnt, uid)
			return nil
		}
	}

	err := m.store.FinishMission(ctx, uid, godId, missionType, needReward)
	if err != nil {
		log.ErrorWithCtx(ctx, "FinishWealthGodMission err: %v, uid: %d, godId: %s, missionType: %d", err, uid, godId, missionType)
		return err
	}

	log.InfoWithCtx(ctx, "FinishWealthGodMission success, uid: %d, godId: %s, missionType: %d", uid, godId, missionType)
	return nil
}

func (m *Mgr) CheckUserIsInBlackList(uid uint32) bool {
	// 黑产检查
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()
	hitUid, err := m.rpcCli.DarkCli.UserBehaviorCheck(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckUserIsInBlackList UserBehaviorCheck Failed uid %d err %v", uid, err)
	}

	if hitUid != 0 {
		log.InfoWithCtx(ctx, "CheckUserIsInBlackList uid %d hitUid %d is in black list", uid, hitUid)
		return true
	}
	return false
}

func (m *Mgr) GetWealthMissionInfo(ctx context.Context, uid uint32, godId string) ([]*wealth_god.WealthMissionInfo, error) {

	missionMap, err := m.store.GetWealthMissionInfoMap(ctx, uid, godId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetWealthMissionInfo err:%v, uid:%d, godId:%s", err, uid, godId)
		return []*wealth_god.WealthMissionInfo{}, err
	}

	missionCfgMap := config.GetDynamicConfig().MissionCfgMap
	out := make([]*wealth_god.WealthMissionInfo, 0, len(missionCfgMap))

	for missionType, missionCfg := range missionCfgMap {
		currentCnt := uint32(0)
		info := &wealth_god.WealthMissionInfo{
			MissionType:    missionType,
			MissionIcon:    missionCfg.MissionIcon,
			MissionName:    missionCfg.MissionName,
			MissionDesc:    missionCfg.MissionDesc,
			MissionCntUint: missionCfg.MissionCntUnit,
			NeedMissionCnt: missionCfg.NeedMissionCnt,
			CurrentCnt:     currentCnt,
		}
		if missionMap[wealth_god_logic.WealthMissionType(missionType)] != nil {
			if missionType == uint32(wealth_god_logic.WealthMissionType_WEALTH_MISSION_TYPE_STAY_IN_GAME) {
				currentCnt = config.GetNeedStayRoomMissionSecond()
				cnt, err := m.cache.GetStayRoomMissionCnt(ctx, uid)
				if err != nil {
					log.ErrorWithCtx(ctx, "GetStayRoomMissionCnt err: %v, uid: %d", err, uid)
					return nil, err
				}
				if cntCfg := config.GetDynamicConfig().StayRoomMissionLimitCnt; cntCfg != 0 {
					info.IsReachLimit = cnt >= int64(config.GetDynamicConfig().StayRoomMissionLimitCnt)
				}
			} else {
				currentCnt = 1
			}
		}
		out = append(out, info)
	}

	return out, nil
}
