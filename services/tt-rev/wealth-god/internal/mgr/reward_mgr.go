package mgr

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/google/uuid"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/app/wealth_god_logic"
	"golang.52tt.com/protocol/common/status"
	unifiedSearchPB "golang.52tt.com/protocol/services/unified-search"
	pb "golang.52tt.com/protocol/services/wealth-god"
	"golang.52tt.com/services/tt-rev/common/goroutineex"
	"golang.52tt.com/services/tt-rev/wealth-god/internal/config"
	"golang.52tt.com/services/tt-rev/wealth-god/internal/store"
	"google.golang.org/grpc/codes"
	"math/rand"
	"strings"
	"sync"
	"time"
)

func (m *Mgr) genWealthGodBoxReward(ctx context.Context, godInfo *store.WealthGodRecord, missionInfo *store.MissionRecord) (*store.RewardRecord, error) {
	uid := missionInfo.Uid

	// 确定奖励
	rewardConf, err := m.confirmReward(ctx, godInfo.GodId, godInfo.BoxId)
	if err != nil {
		log.ErrorWithCtx(ctx, "%d genWealthGodBoxReward confirmReward err: %v", uid, err)
		return nil, err
	}

	// 更新任务表状态
	err = m.store.UpdateMissionOpenRewardStatus(ctx, missionInfo.Id)
	if err != nil {
		log.ErrorWithCtx(ctx, "%d genWealthGodBoxReward UpdateMissionOpenRewardStatus err: %v", uid, err)
		return nil, err
	}

	// 写入发奖表
	record := &store.RewardRecord{
		Id:         missionInfo.Id,
		Uid:        missionInfo.Uid,
		GodId:      missionInfo.GodId,
		BoxId:      godInfo.BoxId,
		RewardId:   rewardConf.RewardId,
		TbeanValue: rewardConf.TbeanValue,
		Status:     store.RewardRecordStatusInit,
		CreateAt:   time.Now(),
		UpdateAt:   time.Now(),
	}
	record.OrderId = record.GenOrderId()
	err = m.store.InsertRewardRecord(ctx, record)
	if err != nil {
		log.ErrorWithCtx(ctx, "%d genWealthGodBoxReward InsertRewardRecord err: %v", uid, err)
		return nil, err
	}

	goroutineex.GoroutineWithTimeoutCtx(ctx, 5*time.Second, func(ctx context.Context) {
		m.Deliver(ctx, record.Id)                                      // GODO: 加入发奖队列
		m.sendPublicScreen4NewReward(ctx, godInfo, record, rewardConf) // 发公屏
		m.riskControl4NewReward(ctx, godInfo, record, rewardConf)      // 风控处理
	})

	log.InfoWithCtx(ctx, "%d genWealthGodBoxReward success, godInfo: %+v, missionInfo: %+v, record: %+v", uid, *godInfo, *missionInfo, *record)
	return record, nil
}

// confirmReward 确定奖励
func (m *Mgr) confirmReward(ctx context.Context, godId string, boxId uint32) (*config.RewardConfig, error) {
	boxConf := config.GetBoxConfig(boxId)
	if boxConf == nil {
		return nil, errors.New("find no boxConf")
	}

	rewardConf := selectRewardByRatio(boxConf.RewardList) // 根据概率选择一个奖励
	for i := 0; i < 10; i++ {                             // 查找有限次数，避免出现死循环
		if rewardConf == nil || rewardConf.IsBottomReward || !rewardConf.NeedCheckStock { // 如果是兜底奖励，或者不需要检查库存，就直接使用
			break
		}
		stockOk, err := m.cache.CheckAndIncreaseUsedRewardCnt(ctx, godId, boxId, rewardConf.RewardId, rewardConf.InitialStock) // 检查库存
		if err != nil {
			return nil, err
		}
		if !stockOk {
			rewardConf = config.GetBoxRewardConfig(boxId, rewardConf.NoStockUseRewardId) // 如果库存不足，就使用备用奖励
		} else {
			break
		}
	}

	if rewardConf == nil { // 找不到符合要求的奖励，就用兜底奖励
		rewardConf = config.GetBoxBottomRewardConfig(boxId)
		if rewardConf == nil { // 兜底奖励都找不到，那只能报错了
			return nil, errors.New("no bottom reward")
		}
	}
	return rewardConf, nil
}

// selectRewardByRatio 根据概率选择一个奖励
func selectRewardByRatio(rewards []*config.RewardConfig) *config.RewardConfig {
	// 先计算总权重
	var totalWeight uint32
	for _, reward := range rewards {
		if reward.OpenRatio > 0 {
			totalWeight += reward.OpenRatio
		}
	}

	if totalWeight == 0 {
		return nil // 没有可选的奖励项
	}

	// 生成随机数
	randNum := rand.Uint32()%totalWeight + 1

	var accWeight uint32
	for _, reward := range rewards {
		if reward.OpenRatio == 0 {
			continue
		}
		accWeight += reward.OpenRatio
		if accWeight >= randNum {
			return reward
		}
	}

	return nil // 不应该走到这里
}

func (m *Mgr) riskControl4NewReward(ctx context.Context, godInfo *store.WealthGodRecord, rewardInfo *store.RewardRecord, rewardConf *config.RewardConfig) {
	if rewardConf.TbeanValue < 10000 { // 只有高于100元的才需要风控
		return
	}

	// 发提醒消息
	content := fmt.Sprintf("恭喜您获得%s奖励，平台严禁交易礼物行为，同时请提高防范意识，谨防受骗。", rewardConf.GetRewardName())
	m.sendTTAssistantText(ctx, rewardInfo.Uid, content, "", "")

	// 一段时间不可搜索
	err := m.rpcCli.SearchClient.AddRisky(ctx, unifiedSearchPB.AddRiskyReq_WealthGod, &unifiedSearchPB.RiskyObject{
		Id:         rewardInfo.Uid,
		ObjectType: unifiedSearchPB.ObjectType_USER,
	})
	if err != nil {
		log.WarnWithCtx(ctx, "%d riskControl4NewReward AddRisky err: %v", rewardInfo.Uid, err)
	}
}

func (m *Mgr) sendPublicScreen4NewReward(ctx context.Context, godInfo *store.WealthGodRecord, rewardInfo *store.RewardRecord, rewardConf *config.RewardConfig) {
	// 查询用户昵称、是否神秘人
	uidList := []uint32{rewardInfo.Uid, godInfo.Uid}
	userMap, err := m.rpcCli.ProfileCli.BatchGetUserProfileV2(ctx, uidList, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushNewWealthGodBoxReward BatchGetUserProfileV2 err: %v", err)
		return
	}
	srcUser := userMap[godInfo.Uid]
	dstUser := userMap[rewardInfo.Uid]
	if srcUser == nil || dstUser == nil {
		log.ErrorWithCtx(ctx, "pushNewWealthGodBoxReward userMap invalid: %+v", userMap)
		return
	}

	xmlStr := config.GetDynamicConfig().RewardXmlTpl
	xmlStr = strings.ReplaceAll(xmlStr, "{{dst_nickname}}", dstUser.Nickname)
	xmlStr = strings.ReplaceAll(xmlStr, "{{src_nickname}}", srcUser.Nickname)
	xmlStr = strings.ReplaceAll(xmlStr, "{{reward_name}}", rewardConf.GetRewardName())

	m.pushChannelXmlPublicScreen(ctx, rewardInfo.Uid, godInfo.Cid, xmlStr)
}

func (m *Mgr) sendAssistant4NewRewards(ctx context.Context, rewardList []*store.RewardRecord) {
	rewardNames := make([]string, 0, len(rewardList))
	for _, reward := range rewardList {
		rewardConf := config.GetBoxRewardConfig(reward.BoxId, reward.RewardId)
		if rewardConf == nil {
			continue
		}
		rewardNames = append(rewardNames, rewardConf.GetRewardNameWithCnt())
	}
	if len(rewardNames) == 0 {
		return
	}
	content := fmt.Sprintf("【财神降临】| 恭喜您成功开启财神宝箱，获得%s！查看详情>>>", strings.Join(rewardNames, "、"))
	m.sendTTAssistantText(ctx, rewardList[0].Uid, content, "查看详情>>>", config.GetDynamicConfig().RewardHistoryUrl)
}

func (m *Mgr) OpenWealthGodBoxReward(ctx context.Context, in *pb.OpenWealthGodBoxRewardRequest) (out *pb.OpenWealthGodBoxRewardResponse, err error) {
	out = &pb.OpenWealthGodBoxRewardResponse{}
	nowTs := time.Now().Unix()
	log.InfoWithCtx(ctx, "OpenWealthGodBoxReward in: %+v", in)

	// 参数检查
	godInfo := m.localCache.GetGodInfo(in.GetGodId())
	if godInfo == nil || nowTs > godInfo.EndTs {
		err = protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "当前宝箱已结束，未开箱的奖励将稍后到帐")
		return
	}
	if nowTs < godInfo.MissionEndTs {
		err = protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "未到开启宝箱时间")
		return
	}

	// 频控检查
	if m.cache.CheckUserFreqLimit(ctx, config.GetDynamicConfig().UserOpenMaxTimePerSec) {
		err = protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "当前活动太火爆了，请稍后重试")
		return
	}

	// 对uid+god_id加锁，避免重复点击开箱，或兜底开箱同时触发
	lockVal := uuid.NewString()
	err = m.cache.TryLockOpenReward(ctx, in.GetUid(), in.GetGodId(), lockVal)
	if err != nil {
		log.ErrorWithCtx(ctx, "OpenWealthGodBoxReward TryLockOpenReward err: %v", err)
		return
	}
	defer func() {
		_ = m.cache.UnLockOpenReward(ctx, in.GetUid(), in.GetGodId(), lockVal)
	}()

	// 获取用户未开奖的任务
	missionList, err := m.store.GetUnOpenRewardMissions(ctx, in.GetUid(), in.GetGodId())
	if err != nil {
		log.ErrorWithCtx(ctx, "OpenWealthGodBoxReward GetUnOpenRewardMissions err: %v", err)
		return
	}
	if len(missionList) == 0 {
		err = protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "已没有开箱机会哦")
		return
	}

	// 现在只有2个任务，所以先不并行处理了
	rewardRecords := make([]*store.RewardRecord, 0, len(missionList))
	for _, mission := range missionList {
		rewardRecord, err1 := m.genWealthGodBoxReward(ctx, godInfo, mission)
		if err1 != nil {
			log.ErrorWithCtx(ctx, "OpenWealthGodBoxReward genWealthGodBoxReward err: %v", err1)
			err = protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "开箱遇到点问题，将稍后到帐")
			return
		}
		rewardRecords = append(rewardRecords, rewardRecord)
		rewardConf := config.GetBoxRewardConfig(rewardRecord.BoxId, rewardRecord.RewardId)
		if rewardConf == nil {
			continue
		}
		out.RewardList = append(out.RewardList, &pb.WealthGodBoxRewardPreview{
			IsRare:     rewardConf.IsRare,
			Title:      rewardConf.Title,
			SubTitle:   rewardConf.SubTitle,
			IconUrl:    rewardConf.IconUrl,
			CornerText: rewardConf.GetCornerText(),
		})
	}

	// 非要合并在一起发助手推送，那只能放外层了
	m.sendAssistant4NewRewards(ctx, rewardRecords)

	log.InfoWithCtx(ctx, "OpenWealthGodBoxReward success, in: %+v, out: %+v", in, out)
	return
}

func (m *Mgr) SysOpenWealthGodBoxReward(ctx context.Context, godInfo *store.WealthGodRecord, uid uint32) error {
	ctx, cancel := context.WithTimeout(ctx, time.Second*5)
	defer cancel()
	log.InfoWithCtx(ctx, "%d SysOpenWealthGodBoxReward godInfo: %+v", uid, *godInfo)

	// 频控检查
	if m.cache.CheckSysFreqLimit(ctx, config.GetDynamicConfig().SysOpenMaxTimePerSec) {
		return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "当前活动太火爆了，请稍后重试")
	}

	// 对uid+god_id加锁，避免重复点击开箱，或兜底开箱同时触发
	lockVal := uuid.NewString()
	err := m.cache.TryLockOpenReward(ctx, uid, godInfo.GodId, lockVal)
	if err != nil {
		log.ErrorWithCtx(ctx, "%d SysOpenWealthGodBoxReward TryLockOpenReward err: %v", uid, err)
		return err
	}
	defer func() {
		_ = m.cache.UnLockOpenReward(ctx, uid, godInfo.GodId, lockVal)
	}()

	// 获取用户未开奖的任务
	missionList, err := m.store.GetUnOpenRewardMissions(ctx, uid, godInfo.GodId)
	if err != nil {
		log.ErrorWithCtx(ctx, "%d SysOpenWealthGodBoxReward GetUnOpenRewardMissions err: %v", uid, err)
		return err
	}
	if len(missionList) == 0 {
		return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "已没有开箱机会哦")
	}

	// 现在只有2个任务，所以先不并行处理了
	rewardList := make([]*wealth_god_logic.WealthGodBoxRewardPreview, 0)
	rewardRecords := make([]*store.RewardRecord, 0, len(missionList))
	for _, mission := range missionList {
		rewardRecord, err1 := m.genWealthGodBoxReward(ctx, godInfo, mission)
		if err1 != nil {
			log.ErrorWithCtx(ctx, "%d SysOpenWealthGodBoxReward genWealthGodBoxReward err: %v", uid, err1)
			return err1
		}
		rewardRecords = append(rewardRecords, rewardRecord)
		rewardConf := config.GetBoxRewardConfig(rewardRecord.BoxId, rewardRecord.RewardId)
		if rewardConf == nil {
			continue
		}
		rewardList = append(rewardList, &wealth_god_logic.WealthGodBoxRewardPreview{
			IsRare:     rewardConf.IsRare,
			Title:      rewardConf.Title,
			SubTitle:   rewardConf.SubTitle,
			IconUrl:    rewardConf.IconUrl,
			CornerText: rewardConf.GetCornerText(),
		})
	}

	// 发到点开奖推送
	notify := &wealth_god_logic.AutoOpenWealthGodBoxRewardResultNotify{
		ChannelId:  godInfo.Cid,
		RewardList: rewardList,
	}
	m.pushAutoOpenWealthGodBoxRewardResultNotify(ctx, uid, notify)

	// 非要合并在一起发助手推送，那只能放外层了
	m.sendAssistant4NewRewards(ctx, rewardRecords)

	log.InfoWithCtx(ctx, "%d SysOpenWealthGodBoxReward success, godInfo: %+v, rewardList: %+v", uid, *godInfo, rewardList)
	return nil
}

func (m *Mgr) GetWealthGodBoxRewardList(ctx context.Context, in *pb.GetWealthGodBoxRewardListRequest) (out *pb.GetWealthGodBoxRewardListResponse, err error) {
	out = &pb.GetWealthGodBoxRewardListResponse{}
	defer func() {
		log.DebugWithCtx(ctx, "GetWealthGodBoxRewardList in:%+v, out:%+v, err:%v", in, out, err)
	}()

	limit, offset := getLimitOffset(in.GetPage(), in.GetPageSize())
	records, err := m.store.GetUserRewardRecords(ctx, in.GetUid(), limit+1, offset)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetWealthGodBoxRewardList GetUserRewardRecords err: %v", err)
		return
	}
	if len(records) > int(limit) {
		out.HasMore = true
		records = records[:limit]
	}
	if len(records) == 0 {
		return
	}

	for _, record := range records {
		rewardConf := config.GetBoxRewardConfig(record.BoxId, record.RewardId)
		if rewardConf == nil {
			continue
		}

		out.RewardList = append(out.RewardList, &pb.WealthGodBoxRewardItem{
			Id:       record.Id,
			IconUrl:  rewardConf.IconUrl,
			Title:    rewardConf.GetTitle4History(),
			SubTitle: rewardConf.GetSubTitle4History(),
			IsRare:   rewardConf.IsRare,
			TimeStr:  record.CreateAt.In(time.Local).Format("2006/01/02 15:04"),
		})
	}
	return
}

func getLimitOffset(page, pageSize uint32) (int64, int64) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 {
		pageSize = 10
	}
	if pageSize > 50 {
		pageSize = 50
	}
	limit := pageSize
	offset := (page - 1) * limit
	return int64(limit), int64(offset)
}

func (m *Mgr) handleGodEnd(ctx context.Context) {
	ctx, cancel := context.WithTimeout(ctx, time.Minute*30)
	defer cancel()

	limit := uint32(10)
	for i := 0; i < 10; i++ { // 避免死循环
		shiftTs := time.Now().Unix()
		godList, err := m.cache.GetEndGodList(ctx, shiftTs, limit)
		if err != nil {
			log.ErrorWithCtx(ctx, "handleGodMissionEnd GetEndGodList error: %v", err)
			break
		}
		for _, godId := range godList {
			m.godCtrl.Add(godId)
		}
		if len(godList) < int(limit) {
			break
		}
	}
}

func (m *Mgr) handleGodEndHelper(ctx context.Context, godId string) {
	ctx, cancel := context.WithTimeout(ctx, time.Minute*30)
	defer cancel()
	log.InfoWithCtx(ctx, "%s handleGodEndHelper begin", godId)

	// 获取财神详情
	godInfo, err := m.store.GetWealthGodRecord(ctx, godId)
	if err != nil {
		log.ErrorWithCtx(ctx, "%s handleGodEndHelper GetWealthGodRecord err: %v", godId, err)
		return
	}
	godInfoJson, err := json.Marshal(godInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "%s handleGodEndHelper Marshal godInfo err: %v", godId, err)
		return
	}

	limit := int64(50)
	for i := 0; i < 10000; i++ {
		if m.godCtrl.IsExit() { // 及时退出
			return
		}

		// 分批捞取未开奖的任务
		missionList, err1 := m.store.GetAllUnOpenRewardMissions(ctx, godId, limit)
		if err1 != nil {
			log.ErrorWithCtx(ctx, "%s handleGodEndHelper GetAllUnOpenRewardMissions err: %v", godId, err1)
			return
		}
		if len(missionList) == 0 {
			break
		}

		// 针对uid做个简单去重
		var uidList []uint32
		uidSet := make(map[uint32]bool)
		for _, mission := range missionList {
			if uidSet[mission.Uid] {
				continue
			}
			uidSet[mission.Uid] = true
			uidList = append(uidList, mission.Uid)
		}

		// 并行处理
		var wg sync.WaitGroup
		for _, uid := range uidList {
			wg.Add(1)
			go func(uid uint32) {
				defer func() {
					wg.Done()
				}()
				// 用rpc调用，避免全部集中在单个节点，负载不均
				_, err2 := m.rpcCli.SelfCli.SysOpenWealthGodBoxReward(ctx, &pb.SysOpenWealthGodBoxRewardRequest{
					Uid:         uid,
					GodInfoJson: string(godInfoJson),
				})
				if err2 != nil {
					log.WarnWithCtx(ctx, "%d handleGodEndHelper SysOpenWealthGodBoxReward err: %v", uid, err2)
				}
			}(uid)
		}
		wg.Wait()
	}

	// 已经可以删缓存了，避免后面重复推送
	err = m.cache.DelGodEndTime(ctx, godId)
	if err != nil {
		log.WarnWithCtx(ctx, "%s handleGodEndHelper DelGodEndTime err: %v", godId, err)
	}

	// 当前房间没财神了，发结束公屏
	channelGodList := m.localCache.GetChannelGodList(godInfo.Cid)
	if len(channelGodList) == 0 || channelGodList[len(channelGodList)-1].GodId == godId {
		xmlStr := config.GetDynamicConfig().GodEndXmlTpl
		m.pushChannelXmlPublicScreen(ctx, godInfo.Uid, godInfo.Cid, xmlStr)
	}

	log.InfoWithCtx(ctx, "%s handleGodEndHelper end", godId)
}
