package mgr

import (
	"context"
	"errors"
	"fmt"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	channelPB "golang.52tt.com/protocol/app/channel"
	"golang.52tt.com/protocol/common/status"
	backpacksenderpb "golang.52tt.com/protocol/services/backpacksender"
	channelbackgroundPb "golang.52tt.com/protocol/services/channelbackground"
	channelSvrPB "golang.52tt.com/protocol/services/channelsvr"
	awardCenter "golang.52tt.com/protocol/services/risk-control/award-center"
	pb "golang.52tt.com/protocol/services/wealth-god"
	butils "golang.52tt.com/services/risk-control/backpack-sender/utils"
	"golang.52tt.com/services/tt-rev/wealth-god/internal/config"
	"golang.52tt.com/services/tt-rev/wealth-god/internal/store"
	"strconv"
	"time"
)

func (m *Mgr) Deliver(ctx context.Context, id int64) {
	ctx, cancel := context.WithTimeout(ctx, time.Second*10)
	defer cancel()

	// 查询详情
	rewardInfo, err := m.store.GetRewardRecord(ctx, id)
	if err != nil {
		log.ErrorWithCtx(ctx, "%d Deliver GetRewardRecord err: %v", id, err)
		return
	}
	if rewardInfo.Status == uint32(store.RewardRecordStatusRewarded) {
		return
	}
	// 查询配置
	rewardConf := config.GetBoxRewardConfig(rewardInfo.BoxId, rewardInfo.RewardId)
	if rewardConf == nil {
		return
	}
	log.DebugWithCtx(ctx, "Deliver start, rewardInfo: %+v, rewardConf: %+v", *rewardInfo, *rewardConf)

	switch rewardConf.RewardType {
	case uint32(pb.RewardType_REWARD_TYPE_PACKAGE):
		err = m.deliverPackage(ctx, rewardInfo, rewardConf)
	case uint32(pb.RewardType_REWARD_TYPE_HORSE):
		err = m.deliverUsualDress(ctx, rewardInfo, rewardConf, awardCenter.EGiftType_Horse)
	case uint32(pb.RewardType_REWARD_TYPE_MIC_STYLE):
		err = m.deliverUsualDress(ctx, rewardInfo, rewardConf, awardCenter.EGiftType_Headwear)
	case uint32(pb.RewardType_REWARD_TYPE_DECORATION):
		err = m.deliverUsualDress(ctx, rewardInfo, rewardConf, awardCenter.EGiftType_UserDecoration)
	case uint32(pb.RewardType_REWARD_TYPE_NAMEPLATE):
		err = m.deliverUsualDress(ctx, rewardInfo, rewardConf, awardCenter.EGiftType_Nameplate)
	case uint32(pb.RewardType_REWARD_TYPE_CHANNEL_BACKGROUND):
		err = m.deliverChannelBackground(ctx, rewardInfo, rewardConf)
	default:
		err = errors.New("invalid reward type")
	}
	if err != nil {
		log.ErrorWithCtx(ctx, "%d Deliver err: %v", id, err)
		return
	}

	// 更新状态
	err = m.store.UpdateRewardRecordStatus(ctx, rewardInfo.Id, uint32(store.RewardRecordStatusRewarded))
	if err != nil {
		log.ErrorWithCtx(ctx, "%d Deliver UpdateRewardRecordStatus err: %v", id, err)
		return
	}

	log.InfoWithCtx(ctx, "Deliver success, rewardInfo: %+v, rewardConf: %+v", *rewardInfo, *rewardConf)
}

// deliverPackage 投递包裹礼物
func (m *Mgr) deliverPackage(ctx context.Context, rewardInfo *store.RewardRecord, rewardConf *config.RewardConfig) error {
	backpackId, err := strconv.Atoi(rewardConf.ItemId)
	if err != nil || backpackId == 0 {
		return errors.New("invalid ItemId")
	}

	orderId := fmt.Sprintf("%d_%s", rewardConf.RiskControlId, rewardInfo.OrderId)
	ciphertext := butils.AESEncrypt([]byte(orderId), []byte(rewardConf.RiskControlKey))
	_, err = m.rpcCli.BackpackSenderCli.SendBackpackWithRiskControl(ctx, &backpacksenderpb.SendBackpackWithRiskControlReq{
		BusinessId:  rewardConf.RiskControlId,
		BackpackId:  uint32(backpackId),
		ReceiveUid:  rewardInfo.Uid,
		BackpackCnt: rewardConf.ItemCnt,
		ServerTime:  rewardInfo.CreateAt.Unix(),
		OrderId:     orderId,
		Ciphertext:  ciphertext,
	})
	return err
}

// deliverUsualDress 投递普通装扮
func (m *Mgr) deliverUsualDress(ctx context.Context, rewardInfo *store.RewardRecord, rewardConf *config.RewardConfig, dressType awardCenter.EGiftType) error {
	err := m.rpcCli.AwardCenterCli.Award(ctx, &awardCenter.AwardReq{
		TargetUid:  rewardInfo.Uid,
		OrderId:    rewardInfo.OrderId,
		GiftId:     rewardConf.ItemId,
		GiftType:   uint32(dressType),
		BusinessId: rewardConf.RiskControlId,
		HoldingDay: rewardConf.ItemCnt,
		OutsideTs:  uint32(rewardInfo.CreateAt.Unix()),
		ExpireType: uint32(awardCenter.AwardExpireType_DayEnd), // 0点过期
		AutoWear:   true,                                       // 自动穿戴
		UserDecorationExtra: &awardCenter.UserDecorationExtra{
			Version: "v1", // 主页飘的，给个默认版本号
		},
	})
	if err != nil && protocol.ToServerError(err).Code() != status.ErrRiskControlAwardCenterOrderExist {
		return err
	}
	return nil
}

// deliverChannelBackground 投递房间背景
func (m *Mgr) deliverChannelBackground(ctx context.Context, rewardInfo *store.RewardRecord, rewardConf *config.RewardConfig) error {
	uid := rewardInfo.Uid

	backgroundId, err := strconv.Atoi(rewardConf.ItemId)
	if err != nil || backgroundId == 0 {
		return errors.New("invalid ItemId")
	}

	// 反查用户房间信息
	cid, _, err := m.getChannelInfoByUid(ctx, rewardInfo.Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "%d deliverChannelBackground getChannelInfoByUid err: %v", uid, err)
		return err
	}
	if cid == 0 {
		log.WarnWithCtx(ctx, "%d deliverChannelBackground find no channel", uid)
		return nil
	}

	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	expireSec := today.AddDate(0, 0, int(rewardConf.ItemCnt)).Unix() - now.Unix()

	// 发放背景
	_, err = m.rpcCli.ChannelBgCli.BatchGiveChannelBg(ctx, &channelbackgroundPb.BatchGiveChannelBgReq{
		Infos: &channelbackgroundPb.ChannelBgInfo{
			BackgroundId: uint64(backgroundId),
			ChannelId:    []uint32{cid},
			ChannelType:  channelbackgroundPb.ChannelType_MULTIPLAYER,
			ExpireSec:    expireSec, // 该接口没有幂等操作，直接覆盖更新
		},
		OpSourceDesc: "财神降临奖励发放",
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "%d deliverChannelBackground BatchGiveChannelBg err: %v", uid, err)
		return err
	}

	// 设置当前背景
	_, err = m.rpcCli.ChannelBgCli.SetCurrentChannelBackground(ctx, cid, uint64(backgroundId))
	if err != nil {
		log.ErrorWithCtx(ctx, "%d deliverChannelBackground SetCurrentChannelBackground err: %v", uid, err)
		return err
	}

	return nil
}

func (m *Mgr) getChannelInfoByUid(ctx context.Context, uid uint32) (channelId uint32, displayId uint32, err error) {
	// 获取用户个人房
	roleListResp, err := m.rpcCli.ChannelCli.GetUserChannelRoleList(ctx, uid, uid, uint32(channelSvrPB.ChannelAdminRole_CHANNEL_OWNER))
	if err != nil {
		log.ErrorWithCtx(ctx, "getChannelInfoByUid fail to GetUserChannelRoleList. uid:%d, err: %v", uid, err)
		return
	}

	for _, role := range roleListResp.GetRoleList() {
		if role.GetRole() == uint32(channelSvrPB.ChannelAdminRole_CHANNEL_OWNER) &&
			role.GetChannelInfo().GetChannelBindType() == uint32(channelPB.ChannelType_USER_CHANNEL_TYPE) {
			channelId = role.GetChannelInfo().GetChannelBaseinfo().GetChannelId()
			displayId = role.GetChannelInfo().GetChannelBaseinfo().GetDisplayId()
			break
		}
	}

	log.DebugWithCtx(ctx, "getChannelInfoByUid uid: %d, channelId: %d, displayId: %d", uid, channelId, displayId)
	return
}

func (m *Mgr) handleRewardDeliverException(ctx context.Context) {
	ctx, cancel := context.WithTimeout(ctx, time.Minute*30)
	defer cancel()

	records, err := m.store.GetExceptionRewardRecords(ctx, 1000) // 理论上，不应该很多
	if err != nil {
		log.ErrorWithCtx(ctx, "handleRewardDeliverException GetExceptionRewardRecords error: %v", err)
		return
	}

	for _, record := range records {
		m.Deliver(ctx, record.Id)
	}
}
