package mgr

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	channelApp "golang.52tt.com/protocol/app/channel"
	"golang.52tt.com/protocol/app/wealth_god_logic"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/wealth-god"
	"golang.52tt.com/services/tt-rev/common/goroutineex"
	"golang.52tt.com/services/tt-rev/wealth-god/internal/config"
	"golang.52tt.com/services/tt-rev/wealth-god/internal/store"
	"google.golang.org/grpc/codes"
	"sort"
	"time"
)

func (m *Mgr) GetOneWealthGodChannel() (uint32, error) {
	return m.localCache.GetOneWealthGodChannel(), nil
}

func (m *Mgr) GetWealthGodDetail(ctx context.Context, in *pb.GetWealthGodDetailRequest) (out *pb.GetWealthGodDetailResponse, err error) {
	out = &pb.GetWealthGodDetailResponse{}
	nowTs := time.Now().Unix()
	defer func() {
		log.DebugWithCtx(ctx, "GetWealthGodDetail in: %+v, out: %+v, err: %v", in, out, err)
	}()

	// 参数检查
	godInfo := m.localCache.GetGodInfo(in.GetGodId())
	if godInfo == nil || nowTs > godInfo.EndTs {
		err = protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "当前宝箱已结束")
		return
	}

	// 填充财神信息
	out.GodInfo = &pb.WealthGod{
		GodId:        godInfo.GodId,
		ChannelId:    godInfo.Cid,
		StartTs:      godInfo.StartTs,
		EndTs:        godInfo.EndTs,
		MissionEndTs: godInfo.MissionEndTs,
	}

	// 填充可开奖次数
	missions, err := m.store.GetMissionRecordsByUid(ctx, in.Uid, in.GodId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetWealthGodDetail GetMissionRecordsByUid err: %v", err)
		return
	}
	for _, mission := range missions {
		if mission.NeedReward {
			out.GetOpenCnt += 1
			out.AvailableOpenCnt += 1
		}
		if mission.OpenReward {
			out.AvailableOpenCnt -= 1
		}
	}

	if nowTs+10 < godInfo.MissionEndTs { // 宝箱信息，仅在开奖前会返回
		return
	}
	// 填充宝箱信息
	boxConf := config.GetBoxConfig(godInfo.BoxId)
	if boxConf == nil {
		err = protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "宝箱配置不存在")
		return
	}
	var rewardList []*pb.WealthGodBoxRewardPreview
	for _, reward := range boxConf.RewardList {
		rewardList = append(rewardList, &pb.WealthGodBoxRewardPreview{
			IsRare:     reward.IsRare,
			Title:      reward.Title,
			SubTitle:   reward.SubTitle,
			IconUrl:    reward.IconUrl,
			CornerText: reward.GetCornerText(),
		})
	}
	out.BoxInfo = &pb.WealthGodBox{
		BoxType:    boxConf.BoxType,
		RewardList: rewardList,
	}

	return
}

func (m *Mgr) checkLockStatus(ctx context.Context, cid, uid uint32) (locked bool, err error) {
	channelInfo, err := m.rpcCli.ChannelCli.GetChannelDetailInfo(ctx, uid, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "%d checkLockStatus GetChannelDetailInfo err: %v", cid, err)
		return
	}
	locked = channelInfo.GetChannelBaseinfo().GetHasPwd()

	if locked { // 锁房，发各种通知
		content := "当前房间为锁房状态，无法触发财神降临，房间解锁后才能触发宝箱哦~"
		m.sendTTAssistantText(ctx, uid, content, "", "")
		m.pushToast(ctx, uid, content)
		m.rpcCli.ChannelMsgCli.SimplePushToUsers(ctx, []uint32{uid}, 0, cid, uint32(channelApp.ChannelMsgType_CHANNEL_TEXT_SYS_MSG), content, nil, false)
	}

	return locked, nil
}

func (m *Mgr) CreateWealthGod(ctx context.Context, cid, uid uint32) error {
	log.InfoWithCtx(ctx, "CreateWealthGod begin, cid: %d, uid: %d", cid, uid)

	// 检查是否锁房，是的话就发各种通知并返回
	locked, err := m.checkLockStatus(ctx, cid, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateWealthGod checkLockStatus err: %v", err)
		return err
	}
	if locked {
		log.InfoWithCtx(ctx, "%d CreateWealthGod locked, return", cid)
		return nil
	}

	// 加锁，避免同时有多个触发，导致时段计算重叠。每天量不大，因此整个加锁
	godId := store.GenGodId()
	err = m.cache.TryLockCreateWealthGod(ctx, godId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateWealthGod TryLockCreateWealthGod err: %v", err)
		return err
	}
	defer func() {
		_ = m.cache.UnLockCreateWealthGod(ctx, godId)
	}()

	// 计算时段
	var startTs, endTs, missionEndTs int64
	triggerConf := config.GetDynamicConfig().GodTriggerConf
	records, err := m.store.GetChannelUnFinishWealthGodRecords(ctx, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateWealthGod GetChannelUnFinishWealthGodRecords err: %v", err)
		return err
	}
	if len(records) == 0 {
		startTs = time.Now().Unix()
	} else {
		sort.SliceStable(records, func(i, j int) bool { // 重排，最晚结束的排最前面
			return records[i].EndTs > records[j].EndTs
		})
		startTs = records[0].EndTs + triggerConf.NextGodWaitDuration
	}
	missionEndTs = startTs + triggerConf.GodMissionDuration
	endTs = missionEndTs + triggerConf.OpenBoxDuration

	// 提前选择宝箱id
	boxId, err := m.chooseWealthGodBoxId(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateWealthGod chooseWealthGodBoxId err: %v", err)
		return err
	}

	// 写入db
	record := &store.WealthGodRecord{
		GodId:        godId,
		Cid:          cid,
		Uid:          uid,
		StartTs:      startTs,
		EndTs:        endTs,
		MissionEndTs: missionEndTs,
		BoxId:        boxId,
		CreateAt:     time.Now(),
	}
	err = m.store.InsertWealthGodRecord(ctx, record)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateWealthGod InsertWealthGodRecord err: %v", err)
		return err
	}

	goroutineex.GoroutineWithTimeoutCtx(ctx, 5*time.Second, func(ctx context.Context) {
		_ = m.cache.SetGodMissionEndTime(ctx, godId, missionEndTs)
		_ = m.cache.SetGodEndTime(ctx, godId, endTs)
		m.pushNewWealthGod(ctx, record) // 对所有在房用户发推送
	})

	return nil
}

func (m *Mgr) handleGodMissionEnd(ctx context.Context) {
	ctx, cancel := context.WithTimeout(ctx, time.Minute*30)
	defer cancel()

	limit := uint32(5)
	for i := 0; i < 10; i++ { // 避免死循环
		shiftTs := time.Now().Unix() + 10 // 要提前10s就推下去
		godList, err := m.cache.GetMissionEndGodList(ctx, shiftTs, limit)
		if err != nil {
			log.ErrorWithCtx(ctx, "handleGodMissionEnd GetMissionEndGodList error: %v", err)
			break
		}
		for _, godId := range godList {
			m.handleGodMissionEndHelper(ctx, godId)
		}
		if len(godList) < int(limit) {
			break
		}
	}
}

func (m *Mgr) handleGodMissionEndHelper(ctx context.Context, godId string) {
	log.InfoWithCtx(ctx, "%s handleGodMissionEndHelper begin", godId)
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// 获取财神详情，构造推送结构
	godInfo, err := m.store.GetWealthGodRecord(ctx, godId)
	if err != nil {
		log.ErrorWithCtx(ctx, "%s handleGodMissionEndHelper GetWealthGodRecord err: %v", godId, err)
		return
	}

	boxConf := config.GetBoxConfig(godInfo.BoxId)
	if boxConf == nil {
		log.ErrorWithCtx(ctx, "%s handleGodMissionEndHelper boxConf is nil", godId)
		return
	}
	var rewardList []*wealth_god_logic.WealthGodBoxRewardPreview
	for _, reward := range boxConf.RewardList {
		rewardList = append(rewardList, &wealth_god_logic.WealthGodBoxRewardPreview{
			IsRare:     reward.IsRare,
			Title:      reward.Title,
			SubTitle:   reward.SubTitle,
			IconUrl:    reward.IconUrl,
			CornerText: reward.GetCornerText(),
		})
	}
	notify := &wealth_god_logic.ChannelNewWealthGodBoxNotify{
		GodInfo: &wealth_god_logic.WealthGod{
			GodId:        godInfo.GodId,
			ChannelId:    godInfo.Cid,
			StartTs:      godInfo.StartTs,
			EndTs:        godInfo.EndTs,
			MissionEndTs: godInfo.MissionEndTs,
		},
		BoxInfo: &wealth_god_logic.WealthGodBox{
			BoxType:    boxConf.BoxType,
			RewardList: rewardList,
		},
	}

	// 已经可以删缓存了，避免后面重复推送
	err = m.cache.DelGodMissionEndTime(ctx, godId)
	if err != nil {
		log.WarnWithCtx(ctx, "%s handleGodMissionEndHelper DelGodMissionEndTime err: %v", godId, err)
	}

	// 发房间推送
	m.pushChannelNewWealthGodBox(ctx, godInfo.Uid, godInfo.Cid, notify)
}

func (m *Mgr) GetWealthGodEntry(ctx context.Context, uid, channel uint32) (*pb.GetWealthGodEntryResponse, error) {
	out := &pb.GetWealthGodEntryResponse{}
	defer func() {
		log.DebugWithCtx(ctx, "GetWealthGodEntry channel: %d, response: %+v", channel, out)
	}()
	for index, item := range m.localCache.GetChannelGodList(channel) {
		god := &pb.WealthGod{
			GodId:        item.GodId,
			ChannelId:    item.Cid,
			StartTs:      item.StartTs,
			EndTs:        item.EndTs,
			MissionEndTs: item.MissionEndTs,
		}
		if index == 0 {
			godId := item.GodId
			missionInfoMap, err := m.store.GetWealthMissionInfoMap(ctx, uid, godId)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetWealthGodEntry GetWealthMissionInfoMap err: %v", err)
			} else {
				if missionInfoMap[wealth_god_logic.WealthMissionType_WEALTH_MISSION_TYPE_STAY_IN_GAME] != nil {
					god.IsStayRoomMissionFinished = true
				}
			}

		}
		out.CurrentChannelGodList = append(out.CurrentChannelGodList, god)
	}

	out.LastGodEndTs = m.localCache.GetLastGodEndTs()
	return out, nil
}
