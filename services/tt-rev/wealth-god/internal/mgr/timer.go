package mgr

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
	"gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer/task/tasks"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"sync"
	"time"
)

func (m *Mgr) setupTimer() error {
	// 创建定时器
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()

	// 大小进程的定时器，同一个任务只会在一个节点上执行
	timerD, err := timer.NewTimerD(ctx, "wealth-god", timer.WithV8RedisCmdable(m.cache.GetRedisClient()), timer.WithTTL(10*time.Second))
	if err != nil {
		log.Errorf("setupTimer fail to NewTimerD. err: %v", err)
		return err
	}

	// 每天尝试创建月表
	// _ = timerD.AddCronTask("0 0 6 * * *", "handleCreatePeriodTable", tasks.FuncTask(m.handleCreatePeriodTable))
	// 处理财神结束
	timerD.AddIntervalTask("handleGodEnd", time.Second, tasks.FuncTask(m.handleGodEnd))
	// 处理财神任务结束
	timerD.AddIntervalTask("handleGodMissionEnd", time.Second, tasks.FuncTask(m.handleGodMissionEnd))
	// 处理奖品投递异常
	timerD.AddIntervalTask("handleRewardDeliverException", time.Minute, tasks.FuncTask(m.handleRewardDeliverException))

	timerD.AddIntervalTask("handleActivityStartNotify", 2*time.Second, tasks.FuncTask(m.handleActivityStartNotify))

	timerD.AddIntervalTask("handleTimeReleaseBox", time.Second*10, tasks.FuncTask(m.handleTimeReleaseBox))
	m.timerD = timerD
	return nil
}

type GodEndController struct {
	tasks    chan string
	done     chan bool
	finish   bool
	goingMap sync.Map
}

func (c *GodEndController) Setup(consumerCnt int, handler func(ctx context.Context, godId string)) {
	c.done = make(chan bool)
	c.tasks = make(chan string, 5)

	// 起consumerCnt来进行消费。handler处理完，则从goingMap中删除
	for i := 0; i < consumerCnt; i++ {
		go func() {
			for {
				select {
				case <-c.done:
					return
				case godId := <-c.tasks:
					handler(context.Background(), godId)
					c.goingMap.Delete(godId)
				}
			}
		}()
	}
}

func (c *GodEndController) Add(godId string) {
	// 判断godId是否在goingMap，没有的话则运行
	if _, ok := c.goingMap.Load(godId); !ok {
		c.goingMap.Store(godId, true)
		c.tasks <- godId
	}
}

func (c *GodEndController) IsExit() bool {
	return c.finish
}

func (c *GodEndController) Close() {
	c.finish = true
	close(c.done)
}
