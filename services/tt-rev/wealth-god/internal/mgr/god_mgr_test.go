package mgr

import (
	"context"
	"fmt"
	"golang.52tt.com/services/tt-rev/wealth-god/internal/config"
	"testing"
)

func Test_getBoxId(t *testing.T) {
	for i := 0; i < 100; i++ {
		box, err := getBoxId(context.Background(), []*config.BoxConfig{
			{
				BoxId:     1,
				OpenRatio: 10,
			},
			{
				BoxId:     2,
				OpenRatio: 20,
			},
			{
				BoxId:     3,
				OpenRatio: 70,
			},
		})
		if err != nil {
			t.Fatal(err)
		}
		fmt.Println(box)
	}
}
