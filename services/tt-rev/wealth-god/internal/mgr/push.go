package mgr

import (
	"context"
	"github.com/golang/protobuf/proto"
	push "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	channelPB "golang.52tt.com/protocol/app/channel"
	muse_interest_hub_logic "golang.52tt.com/protocol/app/muse-interest-hub-logic"
	pushPb "golang.52tt.com/protocol/app/push"
	"golang.52tt.com/protocol/app/wealth_god_logic"
	public_notice "golang.52tt.com/protocol/services/public-notice"
	push_notification "golang.52tt.com/protocol/services/push-notification/v2"
	"golang.52tt.com/services/tt-rev/wealth-god/internal/config"
	"golang.52tt.com/services/tt-rev/wealth-god/internal/store"
	"time"
)

func (m *Mgr) sendTTAssistantText(ctx context.Context, uid uint32, content, highlight, url string) {
	_, err := m.rpcCli.ImApiCli.SimpleSendTTAssistantText(ctx, uid, content, highlight, url)
	if err != nil {
		log.ErrorWithCtx(ctx, "%d sendTTAssistantText err: %v", uid, err)
	} else {
		log.InfoWithCtx(ctx, "%d sendTTAssistantText success, content: %s", uid, content)
	}
}

func buildNotification(inputMsg proto.Message, cmd uint32, labelString string) *push_notification.CompositiveNotification {
	msg, _ := proto.Marshal(inputMsg)
	pushMessage := &pushPb.PushMessage{
		Cmd:     cmd,
		Content: msg,
	}
	pushMessageBytes, _ := proto.Marshal(pushMessage)
	return &push_notification.CompositiveNotification{
		Sequence:           uint32(time.Now().Unix()),
		TerminalTypeList:   []uint32{protocol.MobileAndroidTT, protocol.MobileIPhoneTT},
		TerminalTypePolicy: push.DefaultPolicy,
		AppId:              0,
		ProxyNotification: &push_notification.ProxyNotification{
			Type:      uint32(push_notification.ProxyNotification_PUSH),
			Payload:   pushMessageBytes,
			PushLabel: labelString,
		},
	}
}

func (m *Mgr) pushToast(ctx context.Context, uid uint32, text string) {
	opt := &pushPb.CommonPlainTextToastPush{
		Uid:     uid,
		Content: text,
	}
	notification := buildNotification(opt, uint32(pushPb.PushMessage_COMMON_TEXT_TOAST_PUSH), "财神降临toast推送")
	err := m.rpcCli.PushCli.PushToUsers(ctx, []uint32{uid}, notification)
	if err != nil {
		log.ErrorWithCtx(ctx, "%d pushToast err: %v", uid, err)
	} else {
		log.InfoWithCtx(ctx, "%d pushToast success, opt: %+v", uid, opt)
	}
}

func (m *Mgr) pushAutoOpenWealthGodBoxRewardResultNotify(ctx context.Context, uid uint32, notify *wealth_god_logic.AutoOpenWealthGodBoxRewardResultNotify) {
	notification := buildNotification(notify, uint32(pushPb.PushMessage_AUTO_OPEN_WEALTH_GOD_BOX_REWARD_RESULT_NOTIFY), "自动开启财神宝箱奖励结果通知")
	err := m.rpcCli.PushCli.PushToUsers(ctx, []uint32{uid}, notification)
	if err != nil {
		log.ErrorWithCtx(ctx, "%d pushAutoOpenWealthGodBoxRewardResultNotify err: %v", uid, err)
	} else {
		log.InfoWithCtx(ctx, "%d pushAutoOpenWealthGodBoxRewardResultNotify success, opt: %+v", uid, notify)
	}
}

func (m *Mgr) pushNewWealthGod(ctx context.Context, godInfo *store.WealthGodRecord) {
	notify := wealth_god_logic.NewWealthGodNotify{
		GodInfo: &wealth_god_logic.WealthGod{
			GodId:        godInfo.GodId,
			ChannelId:    godInfo.Cid,
			StartTs:      godInfo.StartTs,
			EndTs:        godInfo.EndTs,
			MissionEndTs: godInfo.MissionEndTs,
		},
	}
	data, _ := proto.Marshal(&notify)
	_, err := m.rpcCli.PublicNoticeCli.CommonPublicMsgPush(ctx, &public_notice.CommonPublicMsgPushReq{
		OptData:       data,
		AnnounceScope: 1, // 房间内
		CmdType:       uint32(pushPb.PushMessage_NEW_WEALTH_GOD_NOTIFY),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "pushNewWealthGod err: %v", err)
	} else {
		log.InfoWithCtx(ctx, "pushNewWealthGod success, notify: %+v", notify)
	}
}

func (m *Mgr) pushChannelXmlPublicScreen(ctx context.Context, uid, cid uint32, xmlStr string) {
	notify := &muse_interest_hub_logic.MuseCommonXMLMsgNotify{
		MsgType:   uint32(muse_interest_hub_logic.MuseCommonXMLMsgType_MUSE_COMMON_XML_MSG_TYPE_COMMON_MSG),
		XmlMsg:    xmlStr,
		ChannelId: cid,
	}
	data, _ := proto.Marshal(notify)
	_, err := m.rpcCli.ChannelMsgCli.SimplePushToChannel(ctx, uid, cid, uint32(channelPB.ChannelMsgType_MUSE_COMMON_CHANNEL_XML_MSG), "财神降临公屏", data)
	if err != nil {
		log.ErrorWithCtx(ctx, "%d pushChannelXmlPublicScreen err: %v", cid, err)
	} else {
		log.InfoWithCtx(ctx, "%d pushChannelXmlPublicScreen success, notify: %+v", cid, notify)
	}
}

func (m *Mgr) pushChannelNewWealthGodBox(ctx context.Context, uid, cid uint32, notify *wealth_god_logic.ChannelNewWealthGodBoxNotify) {
	data, _ := proto.Marshal(notify)
	_, err := m.rpcCli.ChannelMsgCli.SimplePushToChannel(ctx, uid, cid, uint32(channelPB.ChannelMsgType_CHANNEL_NEW_WEALTH_GOD_BOX_NOTIFY), "财神降临新宝箱", data)
	if err != nil {
		log.ErrorWithCtx(ctx, "%d pushChannelNewWealthGodBox err: %v", cid, err)
	} else {
		log.InfoWithCtx(ctx, "%d pushChannelNewWealthGodBox success, notify: %+v", cid, notify)
	}
}

func (m *Mgr) PushMissionFinish(ctx context.Context, uid uint32, godId string) error {
	// 获取用户任务信息
	missionInfo, err := m.store.GetWealthMissionInfoMap(ctx, uid, godId)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushMissionFinish GetWealthMissionInfo err: %v, uid: %d, godId: %s", err, uid, godId)
		return err
	}
	// 获取任务配置
	missionCfgMap := config.GetDynamicConfig().MissionCfgMap
	// key 为任务类型，value为当前任务完成数量
	missionCntMap := make(map[uint32]uint32, len(missionCfgMap))
	for missionType := range missionCfgMap {
		if _, ok := missionInfo[wealth_god_logic.WealthMissionType(missionType)]; !ok {
			missionCntMap[missionType] = 0
		} else {
			if missionType == uint32(wealth_god_logic.WealthMissionType_WEALTH_MISSION_TYPE_STAY_IN_GAME) {
				// 特殊处理，发送豆豆礼物任务只要完成一次就算完成
				missionCntMap[missionType] = config.GetNeedStayRoomMissionSecond()
			} else {
				missionCntMap[missionType] = 1
			}
		}
	}

	var availableOpenCnt, openCnt uint32
	for _, mission := range missionInfo {
		if mission.NeedReward {
			openCnt++
			availableOpenCnt++
		}
		if mission.OpenReward {
			availableOpenCnt--
		}
	}

	opt := &wealth_god_logic.WealthMissionFinishNotify{
		AvailableOpenCnt:     availableOpenCnt,
		GodId:                godId,
		MissionCurrentCntMap: missionCntMap,
		GetOpenCnt:           openCnt,
	}

	notification := buildNotification(opt, uint32(pushPb.PushMessage_WEALTH_MISSION_FINISH_NOTIFY), "财神任务完成推送")
	err = m.rpcCli.PushCli.PushToUsers(ctx, []uint32{uid}, notification)
	if err != nil {
		log.ErrorWithCtx(ctx, "%d pushMissionFinish err: %v, godId: %s, opt: %+v", uid, err, godId, opt)
		return err
	}
	log.InfoWithCtx(ctx, "%d pushMissionFinish success, opt: %+v, godId: %s", uid, opt, godId)
	return nil

}

func (m *Mgr) handleActivityStartNotify(ctx context.Context) {
	// 获取配置
	startTs := config.GetDynamicConfig().ActivityStartTs
	now := time.Now().Unix()
	if startTs > now {
		return
	}

	// 时间相差7秒以上，则不再推送(预期总共推送3次)
	if startTs-now > 7 {
		return
	}

	opt := &wealth_god_logic.NotifyWealthActivityStart{}
	data, _ := proto.Marshal(opt)
	_, err := m.rpcCli.PublicNoticeCli.CommonPublicMsgPush(ctx, &public_notice.CommonPublicMsgPushReq{
		OptData:       data,
		AnnounceScope: uint32(pushPb.CommBreakingNewsBaseOpt_INSIDE_CHANNEL), // 房间内
		CmdType:       uint32(pushPb.PushMessage_NOTIFY_WEALTH_ACTIVITY_START),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "handleActivityStartNotify err: %v", err)
	} else {
		log.InfoWithCtx(ctx, "handleActivityStartNotify success, notify: %+v", opt)
	}
}
