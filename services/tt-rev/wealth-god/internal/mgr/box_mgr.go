package mgr

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/services/tt-rev/wealth-god/internal/config"
	"golang.52tt.com/services/tt-rev/wealth-god/internal/store"
	"math/rand"
	"time"
)

func getBoxId(ctx context.Context, boxList []*config.BoxConfig) (*config.BoxConfig, error) {
	if len(boxList) == 0 {
		return &config.BoxConfig{}, protocol.NewExactServerError(ctx, status.ErrRequestParamInvalid, "box list is empty")
	}
	// 先算概率
	var ratioSum uint32
	for _, box := range boxList {
		ratioSum += box.OpenRatio
	}

	randInt := rand.Intn(int(ratioSum))
	log.DebugWithCtx(ctx, "chooseWealthGodBoxId randInt: %d, ratioSum: %d", randInt, ratioSum)
	for _, box := range boxList {
		if randInt < int(box.OpenRatio) {
			log.DebugWithCtx(ctx, "chooseWealthGodBoxId selected boxId: %d, randInt: %d, ratioSum: %d", box.BoxId, randInt, ratioSum)
			return box, nil
		}
		randInt -= int(box.OpenRatio)
	}
	log.ErrorWithCtx(ctx, "chooseWealthGodBoxId failed to select boxId, randInt: %d, ratioSum: %d", randInt, ratioSum)
	return &config.BoxConfig{}, protocol.NewExactServerError(ctx, status.ErrRequestParamInvalid, "failed to select boxId")
}

func (m *Mgr) chooseWealthGodBoxId(ctx context.Context) (uint32, error) {
	boxList := config.GetDynamicConfig().BoxList
	var box *config.BoxConfig
	var err error

	chosenBoxMap := make(map[uint32]bool) // 用于记录已选择的宝箱类型，避免重复选择
	for i := 0; i < 10; i++ {
		box, err = getBoxId(ctx, boxList)
		if err != nil {
			log.ErrorWithCtx(ctx, "chooseWealthGodBoxId err: %v", err)
			return 0, err
		}

		// 检查是否需要扣库存,不需要直接返回
		if box == nil || box.IsBottomBox || !box.NeedCheckStock {
			break
		}

		if _, exists := chosenBoxMap[box.BoxId]; exists {
			log.WarnWithCtx(ctx, "chooseWealthGodBoxId already chose box type: %d, retrying", box.BoxType)
			continue // 已经选择过这个类型的宝箱，跳过
		}
		chosenBoxMap[box.BoxType] = true // 记录已选择的宝箱类型

		// 准备加锁扣库存
		err = m.cache.TryLockBoxDivide(ctx, box.BoxType)
		if err != nil {
			log.ErrorWithCtx(ctx, "chooseWealthGodBoxId TryLockBoxDivide err: %v", err)
			return 0, err
		}
		// 检查这个box的库存
		currentCnt, err := m.store.GetGodBoxCnt(ctx, box.BoxType)
		if err != nil {
			log.ErrorWithCtx(ctx, "chooseWealthGodBoxId GetGodBoxCnt err: %v", err)
			_ = m.cache.UnLockBoxDivide(ctx, box.BoxType)
			return 0, err
		}

		if currentCnt+int64(box.InitialStock) > 0 {
			// 有库存，扣除库存并返回
			err = m.store.InsertGodBoxCntLogWithTxn(ctx, &store.GodBoxCntLog{
				GodBoxType: box.BoxType,
				ChangeCnt:  -1,
				CurrentCnt: currentCnt - 1, // 扣除一个
				Reason:     "财神降临宝箱发放",
			})
			if err != nil {
				log.ErrorWithCtx(ctx, "chooseWealthGodBoxId InsertGodBoxCntLog err: %v", err)
				_ = m.cache.UnLockBoxDivide(ctx, box.BoxType)
				return 0, err
			}
			_ = m.cache.UnLockBoxDivide(ctx, box.BoxType)
			log.DebugWithCtx(ctx, "chooseWealthGodBoxId selected boxId: %d, currentCnt: %d", box.BoxId, currentCnt)
			return box.BoxId, nil
		}

	}

	if box == nil {
		bottomBoxId := config.GetBottomBoxId()
		log.WarnWithCtx(ctx, "chooseWealthGodBoxId no box selected, using bottom box, bottomBoxId: %d", bottomBoxId)
		return bottomBoxId, nil

	} else {
		return box.BoxId, nil
	}

}

func (m *Mgr) handleTimeReleaseBox(ctx context.Context) {
	now := time.Now()
	if now.Unix() < config.GetDynamicConfig().ActivityStartTs || now.Unix() > config.GetDynamicConfig().ActivityEndTs {
		log.DebugWithCtx(ctx, "handleTimeReleaseBox not in activity time, now: %v, start: %v, end: %v", now, config.GetDynamicConfig().ActivityStartTs, config.GetDynamicConfig().ActivityEndTs)
		return // 不在活动时间内
	}
	boxList := config.GetDynamicConfig().BoxList
	for _, box := range boxList {
		for _, timeRange := range box.TimeStockReleaseScopes {
			// 开始时间不能大于结束时间
			if timeRange.StartHour >= timeRange.EndHour {
				log.ErrorWithCtx(ctx, "handleTimeReleaseBox invalid time range for box type %d: start %d >= end %d", box.BoxType, timeRange.StartHour, timeRange.EndHour)
				continue // 跳过无效的时间范围
			}

			// 判断当前时间是否处于这个时间范围内
			currentHour := time.Now().Hour()
			if currentHour >= int(timeRange.StartHour) && currentHour < int(timeRange.EndHour) {

				// 发放财富神宝箱
				err := m.store.InsertGodBoxCntLogWithTxn(ctx, &store.GodBoxCntLog{
					GodBoxType: box.BoxType,
					ChangeCnt:  int64(box.TimeStockReleaseCnt),
					Reason:     "定时释放宝箱",
				})
				if err != nil {
					log.ErrorWithCtx(ctx, "handleTimeReleaseBox InsertGodBoxCntLog err: %v", err)
					return
				}
				log.InfoWithCtx(ctx, "handleTimeReleaseBox trigger box type %d, current hour: %d, start: %d, end: %d, now: %v",
					box.BoxType, currentHour, timeRange.StartHour, timeRange.EndHour, now)
			}
		}
	}
	log.DebugWithCtx(ctx, "handleTimeReleaseBox completed at %v", now)
	return
}
