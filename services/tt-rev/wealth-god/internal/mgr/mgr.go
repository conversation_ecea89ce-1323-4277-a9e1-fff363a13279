package mgr

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/app/channel"
	"golang.52tt.com/protocol/common/status"
	checker "golang.52tt.com/protocol/services/channel-operate-permission-checker"
	channel_operate_permission_mgr "golang.52tt.com/protocol/services/channel-operate-permission-mgr"
	"golang.52tt.com/services/tt-rev/wealth-god/internal/cache"
	"golang.52tt.com/services/tt-rev/wealth-god/internal/local_cache"
	"golang.52tt.com/services/tt-rev/wealth-god/internal/rpc"
	"golang.52tt.com/services/tt-rev/wealth-god/internal/store"
)

type Mgr struct {
	cache      *cache.Cache
	store      *store.Store
	rpcCli     *rpc.Client
	localCache *local_cache.LocalCache
	godCtrl    GodEndController
	timerD     *timer.Timer
}

func NewMgr(ctx context.Context, store_ *store.Store, cache_ *cache.Cache, rpcCli_ *rpc.Client, localCache_ *local_cache.LocalCache) (*Mgr, error) {
	mgr := &Mgr{
		store:      store_,
		cache:      cache_,
		rpcCli:     rpcCli_,
		localCache: localCache_,
		godCtrl:    GodEndController{},
	}

	mgr.godCtrl.Setup(5, mgr.handleGodEndHelper)

	err := mgr.setupTimer()
	if err != nil {
		log.ErrorWithCtx(ctx, "setupTimer err: %v", err)
		return nil, err
	}

	return mgr, nil
}

func (m *Mgr) Close() {
	ctx := context.Background()
	m.godCtrl.Close()
	_ = m.cache.Close()
	_ = m.store.Close(ctx)
	m.localCache.Shutdown()
}

func (m *Mgr) CheckOperatePermission(ctx context.Context, in *checker.CheckOperatePermissionReq) (*checker.CheckOperatePermissionResp, error) {
	out := &checker.CheckOperatePermissionResp{}
	cid := in.GetCid()

	if in.GetOpt() != uint32(channel_operate_permission_mgr.ChannelOperate_SET_CHANNEL_ENTER_CONTROL_TYPE) { // 只关注密码操作
		return out, nil
	}

	opt := &channel.ChannelSetEnterControlTypeReq{}
	if err := opt.Unmarshal(in.GetOriginalRequest()); err != nil {
		log.ErrorWithCtx(ctx, "%d CheckOperatePermission Unmarshal err: %v", cid, err)
		return out, nil
	}
	if opt.ControlType != uint32(channel.EnterControlType_EnterControlType_PASSWD) || len(opt.Passwd) == 0 { // 只关注锁房
		return out, nil
	}

	if len(m.localCache.GetChannelGodList(cid)) > 0 {
		log.InfoWithCtx(ctx, "%d CheckOperatePermission limit hit", cid)
		out.PermissionErrCode = int32(status.ErrRequestParamInvalid)
		out.PermissionErrMsg = "财神正降临房间不可锁房喔"
		return out, nil
	}

	return out, nil
}
