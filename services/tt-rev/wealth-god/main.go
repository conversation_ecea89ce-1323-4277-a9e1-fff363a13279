package main

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/grpc"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/startup/suit/grpc/server" // server startup
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"
	_ "golang.52tt.com/pkg/hub/tyr/compatible/server"
	checker "golang.52tt.com/protocol/services/channel-operate-permission-checker"
	pb "golang.52tt.com/protocol/services/wealth-god"
	"golang.52tt.com/services/tt-rev/wealth-god/internal"
	"golang.52tt.com/services/tt-rev/wealth-god/internal/config"
)

func main() {
	var (
		svr *internal.Server
		cfg = &config.StartConfig{}
		err error
	)

	// config file support yaml & json, default wealth-god.json/yaml
	if err := server.NewServer("wealth-god", cfg).
		AddGrpcServer(server.NewBuildOption().
			WithInitializeFunc(func(ctx context.Context, s *grpc.Server) error {
				if err = config.InitDynamicConfig(); err != nil {
					return err
				}

				kafka.InitEventLinkSubWithGrpcSvr(s) // 初始化eventlink

				if svr, err = internal.NewServer(ctx, cfg); err != nil {
					return err
				}

				// register custom grpc server
				pb.RegisterWealthGodServiceServer(s, svr)
				// 房间操作service注册
				checker.RegisterChannelOperatePermissionCheckerServer(s, svr)
				return nil
			}),
		).
		WithCloseFunc(func(ctx context.Context) {
			// do something when server terminating
			svr.ShutDown()
		}).
		Start(); err != nil {
		log.Errorf("server start fail, err: %v", err)
	}
}
