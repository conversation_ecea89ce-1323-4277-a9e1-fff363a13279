package mgr

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
	"go.mongodb.org/mongo-driver/mongo"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	imPb "golang.52tt.com/protocol/app/im"
	errCode "golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/channel-wedding-plan"
	"golang.52tt.com/services/channel-wedding-plan/internal/cache"
	"golang.52tt.com/services/channel-wedding-plan/internal/conf"
	"golang.52tt.com/services/channel-wedding-plan/internal/rpc"
	"golang.52tt.com/services/channel-wedding-plan/internal/store"
	"golang.52tt.com/services/tt-rev/esport/common/collection/transform"
	"time"
)

type WeddingPlanMgr struct {
	st     store.IStore
	cache  cache.ICache
	rpc    *rpc.Rpc
	bc     conf.IBusinessDyConf
	timerD *timer.Timer
}

func NewWeddingPlanMgr(st *store.Store, c *cache.Cache, rpc *rpc.Rpc, bc *conf.BusinessDyConf) *WeddingPlanMgr {
	mgr := &WeddingPlanMgr{st: st, rpc: rpc, bc: bc, cache: c}
	mgr.setupTimer()
	return mgr
}

func (m *WeddingPlanMgr) BatGetWeddingInfoById(ctx context.Context, idList []uint32) (map[uint32]*pb.WeddingPlanInfo, error) {
	weddingMap := make(map[uint32]*pb.WeddingPlanInfo)
	planList, err := m.st.BatGetWeddingPlan(ctx, idList)
	if err != nil {
		return weddingMap, err
	}

	for _, plan := range planList {
		weddingMap[plan.ID] = fillPb4WeddingPlan(plan)
	}
	return weddingMap, nil
}

func (m *WeddingPlanMgr) PageGetComingWeddingList(ctx context.Context, in *pb.PageGetComingWeddingListRequest) (out *pb.PageGetComingWeddingListResponse, err error) {
	out = &pb.PageGetComingWeddingListResponse{}
	defer func() {
		log.DebugWithCtx(ctx, "PageGetComingWeddingList in: %+v, out: %+v, err: %v", in, out, err)
	}()

	limit := int64(in.PageSize)
	offset := int64((in.PageNum - 1) * in.PageSize)
	planList, err := m.st.PageGetWeddingList(ctx, limit+1, offset, in.NowTs)
	if err != nil {
		log.ErrorWithCtx(ctx, "PageGetComingWeddingList PageGetWeddingList err: %v", err)
		return
	}
	if len(planList) > int(limit) {
		out.HasMore = true
		planList = planList[:limit]
	}
	if len(planList) == 0 {
		return
	}

	out.WeddingList = make([]*pb.WeddingPlanInfo, 0, len(planList))
	for _, plan := range planList {
		out.WeddingList = append(out.WeddingList, fillPb4WeddingPlan(plan))
	}
	return
}

func fillPb4WeddingPlan(plan *store.WeddingPlan) *pb.WeddingPlanInfo {
	var reserveInfo *pb.WeddingPlanReserveInfo
	if plan.ReserveInfo != nil {
		reserveInfo = &pb.WeddingPlanReserveInfo{
			ChannelId: plan.ReserveInfo.ChannelId,
			StartTs:   plan.ReserveInfo.StartTime,
			EndTs:     plan.ReserveInfo.EndTime,
		}
	}
	return &pb.WeddingPlanInfo{
		WeddingPlanId:     plan.ID,
		GroomUid:          plan.GroomUid,
		BrideUid:          plan.BrideUid,
		ThemeId:           plan.ThemeId,
		IsThemeFree:       plan.ThemeType == 1,
		ReserveInfo:       reserveInfo,
		GroomsmanUidList:  transform.Map(plan.Groomsman, func(item *store.WeddingGuest) uint32 { return item.Uid }),
		BridesmaidUidList: transform.Map(plan.Bridesmaid, func(item *store.WeddingGuest) uint32 { return item.Uid }),
		HostUid:           plan.HostUid,
		BigScreenList:     fillPb4WeddingPlanBigScreenList(plan.BigScreenList),
		Status:            plan.Status,
		IsHot:             plan.IsHot,
	}
}

func fillPb4WeddingPlanBigScreenList(bigScreenList []*store.BigScreenItem) []*pb.BigScreenItem {
	now := time.Now()
	results := make([]*pb.BigScreenItem, 0, len(bigScreenList))
	for _, item := range bigScreenList {
		if item.ReviewStatus == uint32(pb.ReviewStatus_REVIEW_STATUS_REJECT) {
			continue // 拒绝的图片不展示
		}
		if item.ReviewStatus == uint32(pb.ReviewStatus_REVIEW_STATUS_REVIEWING) && now.After(item.UpdateAt.Add(store.WeddingBigScreenReviewMaxDuration)) {
			continue // 审核中的图片一直没人审核，就不返回，这样客户端可以重新上传
		}
		results = append(results, &pb.BigScreenItem{
			ImgUrl:       item.ImgUrl,
			ReviewStatus: pb.ReviewStatus(item.ReviewStatus),
			UploadByUid:  item.UploadByUid,
		})
	}
	if len(results) > 10 {
		results = results[:10]
	}
	return results
}

func (m *WeddingPlanMgr) SubscribeWedding(ctx context.Context, uid, weddingPlanId uint32) error {
	err := m.st.InsertWeddingSubscribeRecord(ctx, &store.WeddingSubscribeRecord{
		Uid:           uid,
		WeddingPlanId: weddingPlanId,
		CreateAt:      time.Now(),
	})
	if err != nil && mongo.IsDuplicateKeyError(err) {
		return protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid, "请勿重复关注")
	}
	return err
}

func (m *WeddingPlanMgr) BatGetWeddingSubscribeStatus(ctx context.Context, uid uint32, weddingPlanIdList []uint32) (map[uint32]bool, error) {
	return m.st.BatGetWeddingSubscribeStatus(ctx, uid, weddingPlanIdList)
}

func (m *WeddingPlanMgr) SendFellowProposeMsg(ctx context.Context, uid, toUid, level uint32, fellowName string) error {
	checkLevel := false
	for _, lv := range m.bc.GetFellowProposeLevel() {
		if level == lv {
			checkLevel = true
		}
	}

	log.DebugWithCtx(ctx, "SendFellowProposeMsg uid:%d, toUid:%d, level:%d, fellowName:%s", uid, toUid, level, fellowName)
	if !checkLevel {
		log.DebugWithCtx(ctx, "SendFellowProposeMsg level:%d not in %v", level, m.bc.GetFellowProposeLevel())
		return nil
	}

	marriage, err := m.GetUserMarriageInfo(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendFellowProposeMsg GetUserMarriageInfo uid:%d, err: %v", uid, err)
		return err
	}

	if marriage.GetPartnerUid() != 0 {
		log.DebugWithCtx(ctx, "SendFellowProposeMsg GetUserMarriageInfo uid:%d, marriage:%+v ", uid, marriage)
		return nil
	}

	cpMarriage, err := m.GetUserMarriageInfo(ctx, toUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendFellowProposeMsg GetUserMarriageInfo toUid:%d, err: %v", toUid, err)
		return err
	}

	if cpMarriage.GetPartnerUid() != 0 {
		log.ErrorWithCtx(ctx, "SendFellowProposeMsg GetUserMarriageInfo toUid:%d, cpMarriage:%+v ", toUid, cpMarriage)
		return nil
	}

	usersMap, err := m.rpc.BatchGetUserProfile(ctx, []uint32{uid, toUid})
	if err != nil {
		log.ErrorWithCtx(ctx, "SendFellowProposeMsg BatchGetUserProfile uid:%d, toUid:%d, err: %v", uid, toUid, err)
	}
	type FellowProposeXml struct {
		Level         uint32
		FellowName    string
		FromAccount   string
		TargetAccount string
	}

	xmlMsg, err := renderTmpl(ctx, "FellowPropose", m.bc.GetFellowProposeImXml(), &FellowProposeXml{
		Level:         level,
		FellowName:    fellowName,
		FromAccount:   usersMap[uid].GetAccount(),
		TargetAccount: usersMap[toUid].GetAccount(),
	})

	return m.rpc.SendIMCommonXmlMsgWithMsgType(ctx, uid, []uint32{toUid}, "", xmlMsg, uint32(imPb.IM_MSG_TYPE_XML_MSG_CENTER))
}
