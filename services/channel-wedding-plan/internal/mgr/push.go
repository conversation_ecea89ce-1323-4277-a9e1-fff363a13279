package mgr

import (
    "bytes"
    "context"
    "fmt"
    "gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
    "golang.52tt.com/clients/seqgen/v2"
    "golang.52tt.com/pkg/log"
    protocolgrpc "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/app"
    imPB "golang.52tt.com/protocol/app/im"
    pushPb "golang.52tt.com/protocol/app/push"
    syncPB "golang.52tt.com/protocol/app/sync"
    pbIMApi "golang.52tt.com/protocol/services/im-api"
    timelinesvrPB "golang.52tt.com/protocol/services/timelinesvr"
    "golang.52tt.com/services/channel-wedding-plan/internal/store"
    "golang.52tt.com/services/helper-from-cpp/immsghelper"
    "golang.52tt.com/services/notify"
    "text/template"
    "time"
)

const (
    notifyBatSize = 200
)

func (m *WeddingPlanMgr) handleWeddingStartFriendsNotify(ctx context.Context) {
    // 当前时间+5s，然后只保留分钟。避免本地时间不准
    startTime := time.Now().Add(5 * time.Second).Truncate(time.Minute).Unix()
    plans, err := m.st.GetAllStartupWeddingPlan(ctx, startTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetAllStartupWeddingPlan err: %v", err)
        return
    }

    for _, plan := range plans {
        go func(plan *store.WeddingPlan) {
            m.notifyFriendsWeddingStart(ctx, plan)
        }(plan)
    }
}

func (m *WeddingPlanMgr) notifyFriendsWeddingStart(ctx context.Context, plan *store.WeddingPlan) {
    ctx, cancel := context.WithTimeout(context.Background(), time.Minute)
    defer cancel()
    log.InfoWithCtx(ctx, "notifyFriendsWeddingStart, plan: %+v", *plan)
    planId := plan.ID

    subscribeUidList, err := m.st.GetWeddingSubscribeList(ctx, planId)
    if err != nil {
        log.ErrorWithCtx(ctx, "%d notifyFriendsWeddingStart GetWeddingSubscribeList err: %v", planId, err)
        return
    }
    log.DebugWithCtx(ctx, "%d notifyFriendsWeddingStart subscribeUidList: %v", planId, subscribeUidList)

    inviteMap := make(map[uint32][]uint32) // 不同人显示的邀请对象不一样
    uidSet := make(map[uint32]bool)        // 还得去重
    defaultUid := plan.BuyerUid            // 默认的邀请人
    if defaultUid == 0 {
        defaultUid = plan.GroomUid
    }

    // 发伴郎伴娘的, 仅发im
    gabList := append(plan.Groomsman, plan.Bridesmaid...)
    for _, guest := range gabList {
        if guest == nil {
            continue
        }
        if uidSet[guest.Uid] {
            continue
        }
        uidSet[guest.Uid] = true
        if guest.InviteUid != 0 {
            inviteMap[guest.InviteUid] = append(inviteMap[guest.InviteUid], guest.Uid)
        } else {
            inviteMap[defaultUid] = append(inviteMap[defaultUid], guest.Uid)
        }
    }
    for uid, inviteList := range inviteMap {
        m.notifyWeddingStartByInviter(ctx, plan, uid, inviteList, false, subscribeUidList)
    }

    // 亲友团和订阅的, im和弹窗
    // 显示的是邀请人
    inviteMap = make(map[uint32][]uint32) // 清空发送人
    for _, guest := range plan.Friends {
        if guest == nil {
            continue
        }
        if uidSet[guest.Uid] {
            continue
        }
        uidSet[guest.Uid] = true

        if guest.InviteUid != 0 {
            inviteMap[guest.InviteUid] = append(inviteMap[guest.InviteUid], guest.Uid)
        } else {
            inviteMap[defaultUid] = append(inviteMap[defaultUid], guest.Uid)
        }
    }
    // 显示的是购买人
    for _, uid := range subscribeUidList {
        if uidSet[uid] {
            continue
        }
        uidSet[uid] = true
        inviteMap[defaultUid] = append(inviteMap[defaultUid], uid)
    }
    if plan.HostUid != 0 && !uidSet[plan.HostUid] {
        uidSet[plan.HostUid] = true
        inviteMap[defaultUid] = append(inviteMap[defaultUid], plan.HostUid)
    }
    for uid, inviteList := range inviteMap {
        m.notifyWeddingStartByInviter(ctx, plan, uid, inviteList, true, subscribeUidList)
    }
}

type WeddingStartTplData struct {
    UserInfo     *app.UserProfile
    ChannelId    uint32
    TimeRangeStr string
}

func (m *WeddingPlanMgr) notifyWeddingStartByInviter(ctx context.Context, plan *store.WeddingPlan, uid uint32, inviteList []uint32, sendPopup bool, subUidList []uint32) {
    if uid == 0 || len(inviteList) == 0 {
        return
    }
    planId := plan.ID
    log.DebugWithCtx(ctx, "%d notifyFriendsWeddingStart, uid: %d, inviteList: %v", planId, uid, inviteList)
    log.InfoWithCtx(ctx, "%d notifyFriendsWeddingStart, uid: %d, len(inviteList): %d", planId, uid, len(inviteList))

    userInfoResp, serr := m.rpc.BatchGetUserProfile(ctx, []uint32{uid})
    if serr != nil {
        log.ErrorWithCtx(ctx, "%d notifyFriendsWeddingStart GetUserProfileV2 err: %v", planId, serr)
        return
    }
    userInfo := userInfoResp[uid]

    startTimeStr := time.Unix(int64(plan.ReserveInfo.StartTime), 0).Format("1月2日 15:04")
    endTimeStr := time.Unix(int64(plan.ReserveInfo.EndTime), 0).Format("15:04")

    tplData := &WeddingStartTplData{
        UserInfo:     userInfo,
        ChannelId:    plan.ReserveInfo.ChannelId,
        TimeRangeStr: fmt.Sprintf("%s-%s", startTimeStr, endTimeStr),
    }

    var popupBuf bytes.Buffer
    popupTpl := template.Must(template.New("item").Parse(m.bc.GetConfig().WeddingStartNotifyPopupTpl))
    err := popupTpl.Execute(&popupBuf, tplData)
    if err != nil {
        log.ErrorWithCtx(ctx, "%d notifyFriendsWeddingStart genPopupXml err: %v", planId, err)
        return
    }
    popupXmlStr := popupBuf.String()

    var imBuf bytes.Buffer
    imTpl := template.Must(template.New("item").Parse(m.bc.GetConfig().WeddingStartNotifyImTpl))
    err = imTpl.Execute(&imBuf, tplData)
    if err != nil {
        log.ErrorWithCtx(ctx, "%d notifyFriendsWeddingStart genImXml err: %v", planId, err)
        return
    }
    imXmlStr := imBuf.String()

    // 分批发送，因为接口有限制
    for start := 0; start < len(inviteList); start += notifyBatSize {
        end := start + notifyBatSize
        if end > len(inviteList) {
            end = len(inviteList)
        }
        curInviteList := inviteList[start:end]

        if sendPopup {
            err = m.notifyWeddingStartPopup(ctx, curInviteList, popupXmlStr)
            if err != nil {
                log.ErrorWithCtx(ctx, "%d notifyFriendsWeddingStart notifyWeddingStartPopup err: %v", planId, err)
            }
        }

        subUidMap := make(map[uint32]bool)
        for _, subUid := range subUidList {
            subUidMap[subUid] = true
        }
        // curInviteList去掉subUidList
        if len(subUidList) > 0 {
            newInviteList := make([]uint32, 0, len(curInviteList))
            for _, inviteUid := range curInviteList {
                if !subUidMap[inviteUid] {
                    newInviteList = append(newInviteList, inviteUid)
                }
            }
            log.DebugWithCtx(ctx, "%d notifyFriendsWeddingStart, uid: %d, curInviteList: %v, newInviteList: %v", planId, uid, curInviteList, newInviteList)
            curInviteList = newInviteList
        }

        err = m.notifyWeddingStartIm(ctx, uid, curInviteList, imXmlStr)
        if err != nil {
            log.ErrorWithCtx(ctx, "%d notifyFriendsWeddingStart notifyWeddingStartIm err: %v", planId, err)
        }
    }
}

func (m *WeddingPlanMgr) notifyWeddingStartPopup(ctx context.Context, uidList []uint32, xmlStr string) error {
    opt := &pushPb.CommonTopRichTextDialogNotify{
        Content:       xmlStr,
        AnnounceScope: uint32(pushPb.CommBreakingNewsBaseOpt_OUTSIDE_CHANNEL),
        Duration:      m.bc.GetConfig().NotifyPopupDisplaySecond,
        Scene:         "wedding-plan",
    }
    log.DebugWithCtx(ctx, "notifyWeddingStartPopup, uidList: %v, xmlStr: %s", uidList, xmlStr)
    notification := buildNotification(opt, uint32(pushPb.PushMessage_COMMON_TOP_RICH_TEXT_DIALOG_NOTIFY), "婚礼开始弹窗")
    err := m.rpc.PushCli.PushToUsers(ctx, uidList, notification)
    return err
}

func (m *WeddingPlanMgr) notifyWeddingStartIm(ctx context.Context, fromUid uint32, toUidList []uint32, xmlStr string) error {
    extData := &imPB.IMCommonXmlMsg{XmlContent: xmlStr}
    ext, _ := proto.Marshal(extData)

    msgList := make([]*pbIMApi.BatchMsg, 0, len(toUidList))
    for _, toUid := range toUidList {
        msgList = append(msgList, &pbIMApi.BatchMsg{
            From: &pbIMApi.Entity{
                Type: pbIMApi.Entity_USER,
                Id:   fromUid,
            },
            To: &pbIMApi.Entity{
                Type: pbIMApi.Entity_USER,
                Id:   toUid,
            },
            Msg: &pbIMApi.CommonMsg{
                MsgType: uint32(imPB.IM_MSG_TYPE_XML_MSG_NORMAL),
                Ext:     ext,
                Content: "[婚礼开始啦]点击进房",
            },
        })
    }

    _, err := m.rpc.ImApiCli.BatchSendCommonMsg(ctx, &pbIMApi.BatchSendCommonMsgReq{
        BatchMsg:  msgList,
        Namespace: "婚礼开始通知IM",
    })
    return err
}

// SendTTAssistantText 发送TT助手文本消息
func (m *WeddingPlanMgr) SendTTAssistantText(ctx context.Context, uid uint32, str, highlight, url string) {
    _, err := m.rpc.ImApiCli.SimpleSendTTAssistantText(ctx, uid, str, highlight, url)
    if err != nil {
        log.ErrorWithCtx(ctx, "sendTTAssistantText failed, uid:%d, err:%v", uid, err)
        return
    }
    log.InfoWithCtx(ctx, "sendTTAssistantText success, uid:%d, str:%s", uid, str)
}

func (m *WeddingPlanMgr) SendImMgsToUid(ctx context.Context, fromUid, toUid uint32, str, highlight, url string) error {
    _, err := m.rpc.ImApiCli.SimpleSend1V1Text(ctx, fromUid, toUid, str, highlight, url)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendImMgsToUid failed, uid:%d, err:%v", toUid, err)
        return err
    }
    log.InfoWithCtx(ctx, "SendImMgsToUid success, uid:%d, str:%s", toUid, str)
    return nil
}

func (m *WeddingPlanMgr) SendOfficialExtImMsg(c context.Context, uid, toUid, msgType, sourceType uint32, content string, ext []byte) error {
    now := uint32(time.Now().Unix())
    ctx, cancel := protocolgrpc.NewContextWithInfoTimeout(c, time.Second*5)
    defer cancel()

    users := []uint32{uid, toUid}
    usersMap, err := m.rpc.AccountCli.GetUsersMap(ctx, users)
    if err != nil {
        log.ErrorWithCtx(ctx, "get user failed, err:%v", err)
        return err
    }

    if _, ok := usersMap[uid]; !ok {
        log.ErrorWithCtx(ctx, "get user failed, uid:%d", uid)
        return err
    }

    if _, ok := usersMap[toUid]; !ok {
        log.ErrorWithCtx(ctx, "get user failed, toUid:%d", toUid)
        return err
    }

    msg := &timelinesvrPB.ImMsg{
        FromId:        uid,
        ToId:          toUid,
        FromName:      usersMap[uid].GetUsername(),
        FromNick:      usersMap[uid].GetNickname(),
        ToName:        usersMap[toUid].GetUsername(),
        ToNick:        usersMap[toUid].GetNickname(),
        Type:          msgType,
        Content:       content,
        MsgSourceType: sourceType,
        ClientMsgTime: now,
        ServerMsgTime: now,
        Ext:           ext,
        Status:        uint32(syncPB.NewMessageSync_UN_READ),
        Platform:      uint32(timelinesvrPB.Platform_UNSPECIFIED), // 没有平台限制必须显式指定
    }

    svrMsgID, err := m.rpc.SeqgenCli.GenerateSequence(ctx, toUid, seqgen.NamespaceUser, seqgen.KeySvrMsgId, 1)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendExtImMsg failed to generate svr msg id: %v", err)
        return err
    }

    msg.ServerMsgId = uint32(svrMsgID)
    imErr := immsghelper.WriteMsgToUidWithId(ctx, toUid, msg, m.rpc.SeqgenCli, m.rpc.TimelineCli)
    if imErr != nil {
        log.ErrorWithCtx(ctx, "SendExtImMsg - WriteMsgToUidWithId fail , msg: %v , err: %v", msg, imErr)
        return imErr
    }

    imErr = notify.NotifySyncX(ctx, users, notify.ImMsg)
    if imErr != nil {
        log.ErrorWithCtx(ctx, "SendExtImMsg - NotifySyncX fail , msg: %v , err: %v", msg, imErr)
    }

    log.InfoWithCtx(ctx, "SendExtImMsg - WriteMsgToUidWithId , msg: %v, uid:%d, toUid:%d", msg, uid, toUid)
    return nil
}

func (m *WeddingPlanMgr) sendImCommonXmlMsgNoMsgId(ctx context.Context, fromUid, targetUid uint32, xmlContent, outsideText string) error {
    fromServerMsgId, err := m.rpc.GenerateSequence(ctx, fromUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "sendImCommonXmlMsgNoMsgId.GenerateSequence, fromUid: %d, err: %d", fromUid, err)
        return err
    }
    targetServerMsgId, err := m.rpc.GenerateSequence(ctx, targetUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "sendImCommonXmlMsgNoMsgId.GenerateSequence, targetUid: %d, err: %d", targetUid, err)
        return err
    }
    log.InfoWithCtx(ctx, "sendImCommonXmlMsgNoMsgId, fromUid: %d, targetUid: %d, xmdContext: %+v", fromUid, targetUid, xmlContent)
    return m.rpc.SendIMCommonXmlMsg(ctx, fromUid, targetUid, fromServerMsgId, targetServerMsgId, xmlContent, outsideText)
}
