package main

import (
    "context"
    "encoding/json"
    "fmt"
    "github.com/tealeg/xlsx"
    mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
    "golang.52tt.com/clients/channel"
    userPresentCli "golang.52tt.com/clients/userpresent"
    pkgconfig "golang.52tt.com/pkg/config"
    "golang.52tt.com/pkg/log"
    userpresentPb "golang.52tt.com/protocol/services/userpresent"

    "golang.52tt.com/services/channel-wedding-plan/internal/store"
    "os"
    "time"
)

type storeCfg struct {
    MongoConfig *pkgconfig.MongoConfig    `json:"mongo_config"`
    MysqlCfg    *mysqlConnect.MysqlConfig `json:"mysql_config"`
}

func main() {

    var cfg *storeCfg
    // get from json file
    data, err := os.ReadFile("./channel-wedding-plan.json")
    if err != nil {
        panic(err)
    }
    err = json.Unmarshal(data, &cfg)
    if err != nil {
        panic(err)
    }

    st := newFinancialStore(cfg.MongoConfig, cfg.MysqlCfg)
    ctx := context.Background()
    now := time.Now()
    monthTime := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
    lastMonthTime := monthTime.AddDate(0, -1, 0)
    stu := st.GetSummary(ctx, lastMonthTime, monthTime)
    fmt.Printf("summary:%+v\n", stu[1])
    //lastMonthTime := monthTime.AddDate(0, -1, 0)

    ////日期
    ////婚礼销售流水(T豆)
    ////婚礼完结流水(T豆)
    //

    detailDataList, err := st.getOrderInfoWithPlan(ctx, lastMonthTime, monthTime)
    if err != nil {
        fmt.Printf("getOrderInfoWithPlan fail, err: %v\n", err)
        return
    }
    channelIdSet := make(map[uint32]struct{})
    for _, detailData := range detailDataList {
        if detailData.PlanInfo == nil || detailData.PlanInfo.ReserveInfo == nil {
            continue
        }
        channelIdSet[detailData.PlanInfo.ReserveInfo.ChannelId] = struct{}{}
    }
    channelIdList := make([]uint32, 0, len(channelIdSet))
    for channelId := range channelIdSet {
        channelIdList = append(channelIdList, channelId)
    }

    channelCli := channel.NewClient()
    channelInfoMap, err := channelCli.BatchGetChannelSimpleInfo(ctx, 0, channelIdList)
    if err != nil {
        fmt.Printf("BatchGetChannelSimpleInfo fail, err: %v\n", err)
        return
    }
    var planIdList = make([]uint32, 0, len(detailDataList))
    // 可供查询themeId映射
    var planIdThemeIdMap = make(map[uint32]uint32)
    for _, detailData := range detailDataList {
        planIdList = append(planIdList, detailData.OrderInfo.Id)
        planIdThemeIdMap[detailData.OrderInfo.Id] = detailData.OrderInfo.ThemeId
        if detailData.PlanInfo == nil || detailData.PlanInfo.ReserveInfo == nil {
            continue
        }
        channelInfo, ok := channelInfoMap[detailData.PlanInfo.ReserveInfo.ChannelId]
        if !ok {
            fmt.Printf("get channel info fail, channelId: %d\n", detailData.PlanInfo.ReserveInfo.ChannelId)
            continue
        }
        detailData.GuildId = channelInfo.GetBindId()
    }

    giftRawInfoList, err := st.getGiftSentSuccessCntByTime(ctx, lastMonthTime)
    if err != nil {
        fmt.Printf("getGiftSentSuccessCntByTime fail, err: %v\n", err)
        return
    }
    var giftInfoMap = make(map[uint32]*RawGiftInfo)
    for _, giftInfo := range giftRawInfoList {
        giftInfoMap[giftInfo.GiftId] = giftInfo
    }

    giftIdSet := make(map[uint32]struct{})
    for _, giftInfo := range giftRawInfoList {
        giftIdSet[giftInfo.GiftId] = struct{}{}
    }

    sendSuccessGiftList := make([]uint32, 0, len(giftRawInfoList))
    for giftId := range giftIdSet {
        if giftId != 0 {
            sendSuccessGiftList = append(sendSuccessGiftList, giftId)
        }
    }
    // ============================================== 已送出礼物，不和下方礼物状态混淆  =======================================================

    presentCli := userPresentCli.NewClient()
    presentMapResp, err := presentCli.GetPresentConfigByIdList(ctx, 0, sendSuccessGiftList, 0)
    if err != nil {
        fmt.Printf("GetPresentConfigByIdList fail, err: %v\n", err)
        return
    }

    giftInfoCfgMap := make(map[uint32]*userpresentPb.StPresentItemConfig)
    for _, presentInfo := range presentMapResp.GetItemList() {
        giftInfoCfgMap[presentInfo.ItemId] = presentInfo
    }

    // fill price
    for _, rawInfo := range giftInfoMap {
        if giftInfoMap[rawInfo.GiftId] != nil {
            giftInfoMap[rawInfo.GiftId].Price = giftInfoCfgMap[rawInfo.GiftId].GetPrice()
        }
    }

    for giftId, giftInfo := range giftInfoMap {
        log.Infoln("giftId:", giftId, "giftInfo:", giftInfo)
    }

    // 用于查询wedding_record表， 拿到planId,便于关联themeId
    rawIdList := make([]uint32, 0, len(giftRawInfoList))
    for _, giftInfo := range giftRawInfoList {
        rawIdList = append(rawIdList, giftInfo.WeddingId)
    }

    weddingId2PlanIdMap, err := st.getWeddingId2PlanIdMap(ctx, planIdList)
    if err != nil {
        fmt.Printf("getWeddingRecordByWeddingIds fail, err: %v\n", err)
        return
    }

    // 再建立一个反向映射
    planId2WeddingIdMap := make(map[uint32]uint32)
    log.Infoln("len(weddingId2PlanIdMap):", len(weddingId2PlanIdMap))
    for weddingId, planId := range weddingId2PlanIdMap {
        planId2WeddingIdMap[planId] = weddingId
    }
    log.Infoln("len(planId2WeddingIdMap):", len(planId2WeddingIdMap))
    var themeIdGiftValueMap = make(map[uint32]uint64)
    for _, info := range giftRawInfoList {
        if giftInfo := giftInfoMap[info.GiftId]; giftInfo != nil {
            themeIdGiftValueMap[planIdThemeIdMap[weddingId2PlanIdMap[info.WeddingId]]] += uint64(giftInfoMap[info.GiftId].Price)
        }
    }

    log.Infoln("themeIdGiftValueMap:", themeIdGiftValueMap)
    // 创建报表
    file := xlsx.NewFile()

    sheet, err := file.AddSheet("汇总数据")
    if err != nil {
        log.Errorf("genDetailSheet fail to AddSheet. err:%v", err)
        return
    }
    sheet.AddRow().WriteSlice(&[]string{
        "婚礼购买月份", "婚礼主题ID", "购买婚礼次数", "婚礼完成次数", "取消婚礼次数", "购买婚礼总价值", "婚礼完成总价值", "取消婚礼总价值", "送礼总价值",
    }, -1)
    _ = sheet.SetColWidth(0, 0, 12)
    _ = sheet.SetColWidth(1, 1, 18)
    _ = sheet.SetColWidth(2, 2, 18)
    _ = sheet.SetColWidth(3, 3, 18)
    _ = sheet.SetColWidth(4, 4, 18)
    _ = sheet.SetColWidth(5, 5, 18)
    _ = sheet.SetColWidth(6, 6, 18)
    _ = sheet.SetColWidth(7, 7, 18)
    _ = sheet.SetColWidth(8, 8, 18)
    _ = sheet.SetColWidth(9, 9, 18)
    _ = sheet.SetColWidth(10, 10, 18)
    _ = sheet.SetColWidth(11, 11, 18)

    for _, stuItem := range stu {
        sheet.AddRow().WriteSlice(&[]string{
            lastMonthTime.Format("2006年1月"),
            fmt.Sprintf("%d", stuItem.ThemeId),
            fmt.Sprintf("%d", stuItem.TotalOrderCnt),
            fmt.Sprintf("%d", stuItem.FinishOrderCnt),
            fmt.Sprintf("%d", stuItem.RefundOrderCnt),
            fmt.Sprintf("%d", stuItem.TotalAmount),
            fmt.Sprintf("%d", stuItem.FinishAmount),
            fmt.Sprintf("%d", stuItem.RefundAmount),
            fmt.Sprintf("%d", themeIdGiftValueMap[uint32(stuItem.ThemeId)]),
        }, -1)
    }

    // 统计公会汇总数据

    // 统计具体婚礼订单数据

    var guildIdOrderMap = make(map[uint32][]*orderInfoWithPlan)
    for _, detailData := range detailDataList {
        guildIdOrderMap[detailData.GuildId] = append(guildIdOrderMap[detailData.GuildId], detailData)
    }

    guildDataMap := make(map[uint32]*guildData)
    for guildId, orderList := range guildIdOrderMap {
        for _, orderData := range orderList {
            // 初始化
            if _, ok := guildDataMap[guildId]; !ok {
                guildDataMap[guildId] = &guildData{}
            }
            if orderData.OrderInfo.WeddingStatus == store.WeddingStatusConfirm {
                guildDataMap[guildId].FinishCnt++
                guildDataMap[guildId].FinishAmount += uint64(orderData.OrderInfo.WeddingPrice)
            } else if orderData.OrderInfo.WeddingStatus == store.WeddingStatusCancel || orderData.OrderInfo.WeddingStatus == store.WeddingStatusRollback {
                guildDataMap[guildId].CancelCnt++
                guildDataMap[guildId].CancelAmount += uint64(orderData.OrderInfo.WeddingPrice)
            }
            // 购买过就算
            if orderData.OrderInfo.WeddingStatus != store.WeddingStatusInit {
                guildDataMap[guildId].OrderCnt++
                guildDataMap[guildId].Amount += uint64(orderData.OrderInfo.WeddingPrice)
            }

        }
    }

    sheet, err = file.AddSheet("公会数据")
    if err != nil {
        log.Errorf("genDetailSheet fail to AddSheet. err:%v", err)
        return
    }
    sheet.AddRow().WriteSlice(&[]string{
        "购买婚礼月份", "婚礼房所属公会Id", "购买婚礼场次数", "婚礼完成场次数", "取消婚礼场次数", "购买婚礼总价值", "婚礼完成价值", "取消婚礼价值",
    }, -1)
    _ = sheet.SetColWidth(0, 0, 12)
    _ = sheet.SetColWidth(1, 1, 18)
    _ = sheet.SetColWidth(2, 2, 18)
    _ = sheet.SetColWidth(3, 3, 18)
    _ = sheet.SetColWidth(4, 4, 18)
    _ = sheet.SetColWidth(5, 5, 18)
    _ = sheet.SetColWidth(6, 6, 18)
    _ = sheet.SetColWidth(7, 7, 18)
    _ = sheet.SetColWidth(8, 8, 18)

    for guildId, guildData := range guildDataMap {
        sheet.AddRow().WriteSlice(&[]string{
            lastMonthTime.Format("2006年1月"),
            fmt.Sprintf("%d", guildId),
            fmt.Sprintf("%d", guildData.OrderCnt),
            fmt.Sprintf("%d", guildData.FinishCnt),
            fmt.Sprintf("%d", guildData.CancelCnt),
            fmt.Sprintf("%d", guildData.Amount),
            fmt.Sprintf("%d", guildData.FinishAmount),
            fmt.Sprintf("%d", guildData.CancelAmount),
        }, -1)
    }

    sheet, err = file.AddSheet("订单明细")
    if err != nil {
        log.Errorf("genDetailSheet fail to AddSheet. err:%v", err)
        return
    }
    sheet.AddRow().WriteSlice(&[]string{
        "购买婚礼日期", "婚礼订单Id", "购买uid", "婚礼主题id", "购买婚礼价值", "预约婚礼时间段", "预约房间id", "婚礼房所属公会id", "婚礼订单确认时间", "婚礼是否完成", "婚礼结束时间", "礼物是否送出", "送礼价值", "送礼时间",
    }, -1)

    _ = sheet.SetColWidth(0, 0, 12)
    _ = sheet.SetColWidth(1, 1, 48)
    _ = sheet.SetColWidth(2, 2, 12)
    _ = sheet.SetColWidth(3, 3, 12)
    _ = sheet.SetColWidth(4, 4, 12)
    _ = sheet.SetColWidth(5, 5, 40)
    _ = sheet.SetColWidth(6, 6, 12)
    _ = sheet.SetColWidth(7, 7, 12)
    _ = sheet.SetColWidth(8, 8, 20)
    _ = sheet.SetColWidth(9, 9, 12)
    _ = sheet.SetColWidth(10, 10, 20)
    _ = sheet.SetColWidth(11, 11, 12)
    _ = sheet.SetColWidth(12, 12, 20)

    // 查询价格，和送出状态

    weddingIdList := make([]uint32, 0, len(planIdList))
    for _, planId := range planIdList {
        id := planId2WeddingIdMap[planId]
        if id == 0 {
            continue
        }
        weddingIdList = append(weddingIdList, id)
    }

    // 可能跨月，比如上个月买，下个月才完成婚礼
    recordTimeMap, err := st.getRecordTimeByWeddingIds(ctx, weddingIdList)
    if err != nil {
        fmt.Printf("getRecordTimeByWeddingIds fail, err: %v\n", err)
        return
    }

    presentMap, err := st.getPresentRecordInfo(ctx, lastMonthTime)
    if err != nil {
        log.Errorf("getPresentRecordInfo fail, err: %v\n", err)
        return
    }

    for _, detailData := range detailDataList {
        var isFinish string
        var isSendGift string
        var finishTime, sendTime int64
        var giftValue uint32

        weddingId := planId2WeddingIdMap[detailData.OrderInfo.Id]
        presentInfo := presentMap[planId2WeddingIdMap[detailData.OrderInfo.Id]]
        if presentInfo != nil {
            giftInfo, ok := giftInfoMap[presentInfo.GiftId]
            if ok {
                giftValue = giftInfo.Price
            }
            if presentInfo.Status == 0 {
                isSendGift = "否"
            } else {
                isSendGift = "是"
                if presentInfo.SendTime.Unix() > 0 {
                    sendTime = presentInfo.SendTime.Unix()
                }
                log.Infoln("presentInfo:", presentInfo, "sendTime:", sendTime)

            }
        } else {
            isSendGift = "没有礼物"
        }

        if recordTime, ok := recordTimeMap[weddingId]; ok {
            isFinish = "是"
            finishTime = recordTime
        } else {
            isFinish = "否"
        }
        orderTime := time.Unix(detailData.OrderInfo.CreateTs, 0)
        var timeStr string
        var roomId uint32
        if detailData.PlanInfo != nil && detailData.PlanInfo.ReserveInfo != nil {
            beginTime := time.Unix(int64(detailData.PlanInfo.ReserveInfo.StartTime), 0)
            endTime := time.Unix(int64(detailData.PlanInfo.ReserveInfo.EndTime), 0)
            timeStr = fmt.Sprintf("%s-%s", beginTime.Format("2006-01-02 15:04:05"), endTime.Format("2006-01-02 15:04:05"))
            roomId = detailData.PlanInfo.ReserveInfo.ChannelId
        }

        var commitTimeStr string
        // 婚礼订单确认时间
        if detailData.OrderInfo.WeddingStatus == store.WeddingStatusConfirm {
            commitTimeStr = time.Unix(detailData.OrderInfo.UpdateTs, 0).Format("2006-01-02 15:04:05")
        }

        var finishTimeStr string
        if finishTime != 0 {
            finishTimeStr = time.Unix(finishTime, 0).Format("2006-01-02 15:04:05")

        }

        var sendTimeStr string
        if sendTime != 0 {
            sendTimeStr = time.Unix(sendTime, 0).Format("2006-01-02 15:04:05")
        }

        sheet.AddRow().WriteSlice(&[]string{
            orderTime.Format("2006-01-02"),
            detailData.OrderInfo.OrderId,
            fmt.Sprintf("%d", detailData.OrderInfo.BuyerUid),
            fmt.Sprintf("%d", detailData.OrderInfo.ThemeId),
            fmt.Sprintf("%d", detailData.OrderInfo.WeddingPrice),
            timeStr,
            fmt.Sprintf("%d", roomId),
            fmt.Sprintf("%d", detailData.GuildId),
            commitTimeStr,
            fmt.Sprintf("%s", isFinish),
            finishTimeStr,
            fmt.Sprintf("%s", isSendGift),
            fmt.Sprintf("%d", giftValue),
            sendTimeStr,
        }, -1)
    }

    filePath := fmt.Sprintf("./购买婚礼数据_%04d%02d.xlsx", lastMonthTime.Year(), lastMonthTime.Month())
    err = file.Save(filePath)
    if err != nil {
        fmt.Printf("file save fail, err: %v\n", err)
        return
    }

}

type guildData struct {
    OrderCnt     uint32
    Amount       uint64
    CancelCnt    uint32
    CancelAmount uint64
    FinishCnt    uint32
    FinishAmount uint64
}

type orderInfoWithPlan struct {
    OrderInfo   *store.WeddingOrder
    PlanInfo    *store.WeddingPlan
    GuildId     uint32
    PresentInfo *PresentInfo
    FinishTime  int64
}
