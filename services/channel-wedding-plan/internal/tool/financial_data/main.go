package main

import (
    "fmt"
    "github.com/tealeg/xlsx"
    mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
    pkgconfig "golang.52tt.com/pkg/config"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/services/channel-wedding-plan/internal/store"
    "time"
)

type storeCfg struct {
    MongoConfig *pkgconfig.MongoConfig    `json:"mongo_config"`
    MysqlCfg    *mysqlConnect.MysqlConfig `json:"mysql_config"`
}

func main() {

    now := time.Now()

    var isCurrentMonth bool
    var input string
    fmt.Print("isCurrentMonth(y/n): ")
    _, err := fmt.Scanf("%s", &input)
    if err != nil {
        fmt.Printf("Error reading input: %v\n", err)
        return
    }

    if input == "y" || input == "Y" {
        isCurrentMonth = true
    } else if input == "n" || input == "N" {
        isCurrentMonth = false
    } else {
        fmt.Println("Invalid input, please enter 'y' or 'n'.")
        return
    }

    var monthTime, last2MonthTime, lastMonthStart time.Time
    if !isCurrentMonth {
        monthTime = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
        last2MonthTime = monthTime.AddDate(0, -2, 0)
        lastMonthStart = monthTime.AddDate(0, -1, 0)
    } else {
        last2MonthTime = time.Date(now.Year(), now.Month()-1, 1, 0, 0, 0, 0, time.Local)
        monthTime = now
        lastMonthStart = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
    }

    detailDataList := summary(last2MonthTime, monthTime)

    // 可供查询themeId映射
    var planIdThemeIdMap = make(map[uint32]uint32)
    for _, detailData := range detailDataList {
        planIdThemeIdMap[detailData.OrderInfo.Id] = detailData.OrderInfo.ThemeId
        if detailData.PlanInfo == nil || detailData.PlanInfo.ReserveInfo == nil {
            continue
        }
    }

    // 创建报表
    file := xlsx.NewFile()

    sheet, err := file.AddSheet("汇总数据")
    if err != nil {
        log.Errorf("genDetailSheet fail to AddSheet. err:%v", err)
        return
    }
    sheet.AddRow().WriteSlice(&[]string{
        "婚礼购买月份", "婚礼主题ID", "本月购买次数", "本月购买价值", "本月购买-当月已结束且已送礼价值",
        "本月购买-当月已结束（含流局）无送礼价值",
        "本月购买-当月未结束未送礼价值",
        "上月购买-本月结束且本月送礼价值",
        "上月购买-本月结束无送礼价值（含流局）",
    }, -1)
    _ = sheet.SetColWidth(0, 0, 12)
    _ = sheet.SetColWidth(1, 1, 18)
    _ = sheet.SetColWidth(2, 2, 18)
    _ = sheet.SetColWidth(3, 3, 18)
    _ = sheet.SetColWidth(4, 4, 18)
    _ = sheet.SetColWidth(5, 5, 18)
    _ = sheet.SetColWidth(6, 6, 18)
    _ = sheet.SetColWidth(7, 7, 18)
    _ = sheet.SetColWidth(8, 8, 18)
    _ = sheet.SetColWidth(9, 9, 18)
    _ = sheet.SetColWidth(10, 10, 18)
    _ = sheet.SetColWidth(11, 11, 18)

    themeStuMap := make(map[uint32]*summaryStu)
    guildStuMap := make(map[uint32]*summaryStu)
    for _, detail := range detailDataList {
        var finish, isCurrentMonthBuy, isSendGift, invalid bool
        if detail.OrderInfo.TBeanSysTime > lastMonthStart.Unix() {
            isCurrentMonthBuy = true
        }
        if detail.FinishTime != 0 {
            finish = true
            if detail.PresentInfo != nil {
                if detail.PresentInfo.Status == 1 {
                    isSendGift = true
                }
            } else {
                log.Infoln("invalid order, detail.PresentInfo is nil, orderId:", detail.OrderInfo.OrderId)
                invalid = true

            }
        } else {
            // 还没开始
            if detail.PlanInfo != nil && detail.PlanInfo.ReserveInfo != nil && int64(detail.PlanInfo.ReserveInfo.StartTime) > now.Unix() {
                invalid = false
            } else {
                log.Infoln("invalid order, detail.PlanInfo.ReserveInfo is nil, orderId:", detail.OrderInfo.OrderId)
                invalid = true
            }

        }

        // 只统计本月购买
        if isCurrentMonthBuy {
            if themeStuMap[detail.OrderInfo.ThemeId] == nil {
                themeStuMap[detail.OrderInfo.ThemeId] = &summaryStu{
                    FinishOrderCnt: 1,
                    FinishAmount:   uint64(detail.OrderInfo.WeddingPrice),
                }
            } else {
                themeStuMap[detail.OrderInfo.ThemeId].FinishOrderCnt++
                themeStuMap[detail.OrderInfo.ThemeId].FinishAmount += uint64(detail.OrderInfo.WeddingPrice)
            }

            if guildStuMap[detail.GuildId] == nil {
                guildStuMap[detail.GuildId] = &summaryStu{
                    FinishOrderCnt: 1,
                    FinishAmount:   uint64(detail.OrderInfo.WeddingPrice),
                }
            } else {
                guildStuMap[detail.GuildId].FinishOrderCnt++
                guildStuMap[detail.GuildId].FinishAmount += uint64(detail.OrderInfo.WeddingPrice)
            }

            if finish {
                if isSendGift {
                    themeStuMap[detail.OrderInfo.ThemeId].CurrentMonthBuyAndFinishedWithGiftValue += uint64(detail.OrderInfo.WeddingPrice)
                    guildStuMap[detail.GuildId].CurrentMonthBuyAndFinishedWithGiftValue += uint64(detail.OrderInfo.WeddingPrice)
                } else {
                    themeStuMap[detail.OrderInfo.ThemeId].CurrentMonthBuyAndFinishedWithoutGiftValue += uint64(detail.OrderInfo.WeddingPrice)
                    guildStuMap[detail.GuildId].CurrentMonthBuyAndFinishedWithoutGiftValue += uint64(detail.OrderInfo.WeddingPrice)
                }
            } else {
                if invalid {
                    log.Infoln("invalid order, orderId:", detail.OrderInfo.OrderId)
                    themeStuMap[detail.OrderInfo.ThemeId].CurrentMonthBuyAndFinishedWithoutGiftValue += uint64(detail.OrderInfo.WeddingPrice)
                    guildStuMap[detail.GuildId].CurrentMonthBuyAndFinishedWithoutGiftValue += uint64(detail.OrderInfo.WeddingPrice)
                } else {
                    log.Infof("CurrentMonthBuyUnfinishedWithoutGiftValue, orderId: %s, weddingPrice: %d", detail.OrderInfo.OrderId, detail.OrderInfo.WeddingPrice)
                    themeStuMap[detail.OrderInfo.ThemeId].CurrentMonthBuyUnfinishedWithoutGiftValue += uint64(detail.OrderInfo.WeddingPrice)
                    guildStuMap[detail.GuildId].CurrentMonthBuyUnfinishedWithoutGiftValue += uint64(detail.OrderInfo.WeddingPrice)
                }
            }
        } else {
            // 上月购买，本月结束
            if detail.FinishTime != 0 && detail.FinishTime > lastMonthStart.Unix() {
                if themeStuMap[detail.OrderInfo.ThemeId] == nil {
                    themeStuMap[detail.OrderInfo.ThemeId] = &summaryStu{
                        LastMonthBuyFinishedWithGiftValue:    0,
                        LastMonthBuyFinishedWithoutGiftValue: 0,
                    }
                }
                if guildStuMap[detail.GuildId] == nil {
                    guildStuMap[detail.GuildId] = &summaryStu{
                        LastMonthBuyFinishedWithGiftValue:    0,
                        LastMonthBuyFinishedWithoutGiftValue: 0,
                    }
                }
                // 本月结束，则本月一定要送出礼物，否则当成流局或者没送出
                if isSendGift {
                    log.Infoln("上月购买，本月送出，订单Id:", detail.OrderInfo.OrderId)
                    themeStuMap[detail.OrderInfo.ThemeId].LastMonthBuyFinishedWithGiftValue += uint64(detail.OrderInfo.WeddingPrice)
                    guildStuMap[detail.GuildId].LastMonthBuyFinishedWithGiftValue += uint64(detail.OrderInfo.WeddingPrice)
                } else {
                    log.Infoln("上月购买，本月流局结束，订单Id:", detail.OrderInfo.OrderId)
                    themeStuMap[detail.OrderInfo.ThemeId].LastMonthBuyFinishedWithoutGiftValue += uint64(detail.OrderInfo.WeddingPrice)
                    guildStuMap[detail.GuildId].LastMonthBuyFinishedWithoutGiftValue += uint64(detail.OrderInfo.WeddingPrice)
                }
            }

        }
    }

    for themeId, stuItem := range themeStuMap {
        sheet.AddRow().WriteSlice(&[]string{
            lastMonthStart.Format("2006年1月"),
            fmt.Sprintf("%d", themeId),
            fmt.Sprintf("%d", stuItem.FinishOrderCnt),
            fmt.Sprintf("%d", stuItem.FinishAmount),
            fmt.Sprintf("%d", stuItem.CurrentMonthBuyAndFinishedWithGiftValue),
            fmt.Sprintf("%d", stuItem.CurrentMonthBuyAndFinishedWithoutGiftValue),
            fmt.Sprintf("%d", stuItem.CurrentMonthBuyUnfinishedWithoutGiftValue),
            fmt.Sprintf("%d", stuItem.LastMonthBuyFinishedWithGiftValue),
            fmt.Sprintf("%d", stuItem.LastMonthBuyFinishedWithoutGiftValue),
        }, -1)
    }

    // 统计公会汇总数据
    sheet, err = file.AddSheet("公会数据")
    if err != nil {
        log.Errorf("genDetailSheet fail to AddSheet. err:%v", err)
        return
    }
    sheet.AddRow().WriteSlice(&[]string{
        "购买婚礼月份", "婚礼房所属公会Id", "本月购买次数", "本月购买价值", "本月购买-当月已结束且已送礼价值",
        "本月购买-当月已结束（含流局）无送礼价值",
        "本月购买-当月未结束未送礼价值",
        "上月购买-本月结束且本月送礼价值",
        "上月购买-本月结束无送礼价值（含流局）"}, -1)
    _ = sheet.SetColWidth(0, 0, 12)
    _ = sheet.SetColWidth(1, 1, 18)
    _ = sheet.SetColWidth(2, 2, 18)
    _ = sheet.SetColWidth(3, 3, 18)
    _ = sheet.SetColWidth(4, 4, 18)
    _ = sheet.SetColWidth(5, 5, 18)
    _ = sheet.SetColWidth(6, 6, 18)
    _ = sheet.SetColWidth(7, 7, 18)
    _ = sheet.SetColWidth(8, 8, 18)

    for guildId, guildData := range guildStuMap {
        sheet.AddRow().WriteSlice(&[]string{
            lastMonthStart.Format("2006年1月"),
            fmt.Sprintf("%d", guildId),
            fmt.Sprintf("%d", guildData.FinishOrderCnt),
            fmt.Sprintf("%d", guildData.FinishAmount),
            fmt.Sprintf("%d", guildData.CurrentMonthBuyAndFinishedWithGiftValue),
            fmt.Sprintf("%d", guildData.CurrentMonthBuyAndFinishedWithoutGiftValue),
            fmt.Sprintf("%d", guildData.CurrentMonthBuyUnfinishedWithoutGiftValue),
            fmt.Sprintf("%d", guildData.LastMonthBuyFinishedWithGiftValue),
            fmt.Sprintf("%d", guildData.LastMonthBuyFinishedWithoutGiftValue),
        }, -1)
    }

    sheet, err = file.AddSheet("订单明细")
    if err != nil {
        log.Errorf("genDetailSheet fail to AddSheet. err:%v", err)
        return
    }
    sheet.AddRow().WriteSlice(&[]string{
        "购买婚礼日期", "婚礼订单Id", "购买uid", "婚礼主题id", "购买婚礼价值", "预约婚礼时间段", "预约房间id", "婚礼房所属公会id", "婚礼订单确认时间", "婚礼是否完成", "婚礼结束时间", "礼物是否送出", "送礼价值", "送礼时间", "plan_id(可忽略)", "wedding_id(可忽略)",
    }, -1)

    _ = sheet.SetColWidth(0, 0, 12)
    _ = sheet.SetColWidth(1, 1, 48)
    _ = sheet.SetColWidth(2, 2, 12)
    _ = sheet.SetColWidth(3, 3, 10)
    _ = sheet.SetColWidth(4, 4, 12)
    _ = sheet.SetColWidth(5, 5, 40)
    _ = sheet.SetColWidth(6, 6, 12)
    _ = sheet.SetColWidth(7, 7, 12)
    _ = sheet.SetColWidth(8, 8, 20)
    _ = sheet.SetColWidth(9, 9, 12)
    _ = sheet.SetColWidth(10, 10, 20)
    _ = sheet.SetColWidth(11, 11, 20)
    _ = sheet.SetColWidth(12, 12, 12)
    _ = sheet.SetColWidth(13, 13, 22)
    _ = sheet.SetColWidth(14, 14, 14)
    _ = sheet.SetColWidth(15, 15, 16)

    // 查询价格，和送出状态

    for _, detailData := range detailDataList {
        if detailData.OrderInfo.WeddingStatus != store.WeddingStatusConfirm {
            continue
        }
        var isFinish string
        var isSendGift string
        var finishTime, sendTime int64
        var giftValue uint32

        weddingId := detailData.WeddingId
        presentInfo := detailData.PresentInfo
        giftValue = detailData.Price

        if presentInfo != nil {
            if presentInfo.Status == 0 {
                isSendGift = "否"
            } else {
                isSendGift = "是"
                if presentInfo.SendTime.Unix() > 0 {
                    sendTime = presentInfo.SendTime.Unix()
                }
            }
        } else {

            if detailData.PlanInfo != nil && detailData.PlanInfo.ReserveInfo != nil && int64(detailData.PlanInfo.ReserveInfo.StartTime) > now.Unix() {
                isSendGift = "还没开始"
            } else {
                isSendGift = "没有礼物"
            }
        }

        if detailData.FinishTime != 0 {
            isFinish = "是"
            finishTime = detailData.FinishTime
        } else {
            isFinish = "否"
        }
        orderTime := time.Unix(detailData.OrderInfo.CreateTs, 0)
        var timeStr string
        var roomId uint32
        if detailData.PlanInfo != nil && detailData.PlanInfo.ReserveInfo != nil {
            beginTime := time.Unix(int64(detailData.PlanInfo.ReserveInfo.StartTime), 0)
            endTime := time.Unix(int64(detailData.PlanInfo.ReserveInfo.EndTime), 0)
            timeStr = fmt.Sprintf("%s-%s", beginTime.Format("2006-01-02 15:04:05"), endTime.Format("2006-01-02 15:04:05"))
            roomId = detailData.PlanInfo.ReserveInfo.ChannelId
        }

        var commitTimeStr string
        // 婚礼订单确认时间
        if detailData.OrderInfo.WeddingStatus == store.WeddingStatusConfirm {
            commitTimeStr = time.Unix(detailData.OrderInfo.UpdateTs, 0).Format("2006-01-02 15:04:05")
        }

        var finishTimeStr string
        if finishTime != 0 {
            finishTimeStr = time.Unix(finishTime, 0).Format("2006-01-02 15:04:05")

        }

        var sendTimeStr string
        if sendTime != 0 {
            sendTimeStr = time.Unix(sendTime, 0).Format("2006-01-02 15:04:05")
        }

        sheet.AddRow().WriteSlice(&[]string{
            orderTime.Format("2006-01-02"),
            detailData.OrderInfo.OrderId,
            fmt.Sprintf("%d", detailData.OrderInfo.BuyerUid),
            fmt.Sprintf("%d", detailData.OrderInfo.ThemeId),
            fmt.Sprintf("%d", detailData.OrderInfo.WeddingPrice),
            timeStr,
            fmt.Sprintf("%d", roomId),
            fmt.Sprintf("%d", detailData.GuildId),
            commitTimeStr,
            fmt.Sprintf("%s", isFinish),
            finishTimeStr,
            fmt.Sprintf("%s", isSendGift),
            fmt.Sprintf("%d", giftValue),
            sendTimeStr,
            fmt.Sprintf("%d", detailData.OrderInfo.Id),
            fmt.Sprintf("%d", weddingId),
        }, -1)
    }

    filePath := fmt.Sprintf("./购买婚礼数据表_%04d%02d.xlsx", lastMonthStart.Year(), lastMonthStart.Month())
    err = file.Save(filePath)
    if err != nil {
        fmt.Printf("file save fail, err: %v\n", err)
        return
    }

}

type guildData struct {
    OrderCnt     uint32
    Amount       uint64
    CancelCnt    uint32
    CancelAmount uint64
    FinishCnt    uint32
    FinishAmount uint64
}

type orderInfoWithPlan struct {
    OrderInfo   *store.WeddingOrder
    PlanInfo    *store.WeddingPlan
    GuildId     uint32
    PresentInfo *PresentInfo
    FinishTime  int64
    WeddingId   uint32
    Price       uint32
}
