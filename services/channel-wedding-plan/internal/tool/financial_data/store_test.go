package main

//
//import (
//    "context"
//    "fmt"
//    mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
//    "golang.52tt.com/pkg/config"
//    "testing"
//    "time"
//)
//
//func Test_GenSummary(t *testing.T) {
//    st := getTestStore()
//    ctx := context.Background()
//    now := time.Now()
//    monthTime := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
//    stu := st.GetSummary(ctx, monthTime, now)
//
//    fmt.Printf("summary:%+v\n", stu)
//    lastMonthTime := monthTime.AddDate(0, -1, 0)
//
//    // 统计上个月份 每一天
//    for i := 0; i < 31; i++ {
//        dayTime := lastMonthTime.AddDate(0, 0, i)
//        if dayTime.Month() != lastMonthTime.Month() {
//            break
//        }
//        stu := st.GetSummary(ctx, dayTime, dayTime.AddDate(0, 0, 1))
//
//        fmt.Printf("dayTime:%v,summary:%+v\n", dayTime, stu)
//
//    }
//
//    // 本月每一天
//    for i := 0; i < 31; i++ {
//        dayTime := monthTime.AddDate(0, 0, i)
//        if dayTime.Month() != monthTime.Month() {
//            break
//        }
//        stu := st.GetSummary(ctx, dayTime, dayTime.AddDate(0, 0, 1))
//        fmt.Printf("dayTime:%v,summary:%+v\n", dayTime, stu)
//    }
//}
//
//func getTestStore() *financialStore {
//    return newFinancialStore(&config.MongoConfig{
//        Addrs:    "*************:27017",
//        Database: "channel_wedding_plan",
//        UserName: "godman",
//        Password: "TT8thegodofman",
//    }, &mysqlConnect.MysqlConfig{
//        Host:         "**********",
//        Port:         3306,
//        Charset:      "utf8",
//        Database:     "appsvr",
//        UserName:     "godman",
//        Password:     "TT8thegodofman",
//        MaxIdleConns: 10,
//        MaxOpenConns: 10,
//    })
//}
//
//func Test_financialStore_getCommitOrderDetail(t *testing.T) {
//    st := getTestStore()
//    ctx := context.Background()
//    now := time.Now()
//    monthTime := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
//    rs, err := st.getAllOrderDetail(ctx, monthTime, now)
//    if err != nil {
//        t.Fatal(err)
//    }
//    for _, r := range rs {
//        fmt.Printf("order:%+v\n", r)
//    }
//}
//
//func Test_financialStore_getOrderPlanByIdList(t *testing.T) {
//    st := getTestStore()
//    ctx := context.Background()
//
//    rs, err := st.getOrderPlanByIdList(ctx, []uint64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10})
//    if err != nil {
//        t.Fatal(err)
//    }
//    for _, r := range rs {
//        fmt.Printf("order:%+v\n", r)
//    }
//}
//
//func Test_financialStore_getGiftCntByTime(t *testing.T) {
//    st := getTestStore()
//    ctx := context.Background()
//    now := time.Now()
//    monthTime := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
//    rs, err := st.getGiftSentSuccessCntByTime(ctx, monthTime)
//    if err != nil {
//        t.Fatal(err)
//    }
//    for _, r := range rs {
//        fmt.Printf("order:%+v\n", r)
//    }
//}
