package main

import (
    "context"
    "encoding/json"
    "fmt"
    "golang.52tt.com/clients/channel"
    userPresentCli "golang.52tt.com/clients/userpresent"
    "golang.52tt.com/pkg/log"
    "os"
    "time"
)

func summary(startTime, endTime time.Time) []*orderInfoWithPlan {

    var cfg *storeCfg
    // get from json file
    data, err := os.ReadFile("./channel-wedding-plan.json")
    if err != nil {
        panic(err)
    }
    err = json.Unmarshal(data, &cfg)
    if err != nil {
        panic(err)
    }

    st := newFinancialStore(cfg.MongoConfig, cfg.MysqlCfg)
    ctx := context.Background()
    now := time.Now()

    // 1、获取这两个月全部订单
    orderIds, err := st.getCommitOrderInfoWithPlan(ctx, time.Unix(startTime.Unix(), 0), time.Unix(endTime.Unix(), 0))
    if err != nil {
        log.Errorf("getCommitOrderInfoWithPlan err: %v", err)
        panic(err)
    }
    // 2、拿到全部的planId
    planIdList := make([]uint32, 0, len(orderIds))
    for _, order := range orderIds {
        planIdList = append(planIdList, order.OrderInfo.Id)
    }

    // 3、获取这些planId与weddingId的映射关系
    wedding2PlanMap, err := st.getWeddingId2PlanIdMap(ctx, planIdList)
    if err != nil {
        log.Errorf("getWeddingId2PlanIdMap err: %v", err)
        panic(err)
    }

    // 4、建立反向映射
    planId2WeddingId := make(map[uint32]uint32, len(wedding2PlanMap))
    for weddingId, planId := range wedding2PlanMap {
        planId2WeddingId[planId] = weddingId
    }

    // fill weddingId
    for _, order := range orderIds {
        if weddingId, ok := planId2WeddingId[order.OrderInfo.Id]; ok {
            order.WeddingId = weddingId
        } else {
            log.Errorf("weddingId not found for planId %d", order.OrderInfo.Id)
            continue
        }
    }

    // 5、拿到全部的weddingId
    weddingIdList := make([]uint32, 0, len(wedding2PlanMap))
    for weddingId, _ := range wedding2PlanMap {
        weddingIdList = append(weddingIdList, weddingId)
    }

    // 6、根据weddingId 获取 游戏结束记录
    gameEndRecords, err := st.getRecordTimeByWeddingIds(ctx, weddingIdList)
    if err != nil {
        log.Errorf("getRecordTimeByWeddingIds err: %v", err)
        panic(err)
    }

    // 7、fill finishTime
    for _, order := range orderIds {
        order.FinishTime = 0 // default 0
        if record, ok := gameEndRecords[order.WeddingId]; ok {
            // 这里的record是一个时间戳
            order.FinishTime = record
        }
    }

    // 8. 获取礼物信息
    giftInfoMap, err := st.getLast2MonthPresentRecordInfo(ctx, now)
    if err != nil {
        log.Errorf("getLast2MonthPresentRecordInfo err: %v", err)
        panic(err)
    }

    // fill 填充礼物信息
    for _, order := range orderIds {
        if presentInfo, ok := giftInfoMap[order.WeddingId]; ok {
            order.PresentInfo = presentInfo
        } else {
            log.Warnf("no gift info for weddingId %d", order.WeddingId)
        }
    }

    // 9. 去重获取礼物id
    giftIdSet := make(map[uint32]struct{}, len(giftInfoMap))
    for _, gift := range giftInfoMap {
        giftIdSet[gift.GiftId] = struct{}{}
    }

    giftIdList := make([]uint32, 0, len(giftIdSet))
    for giftId := range giftIdSet {
        giftIdList = append(giftIdList, giftId)
    }

    // 10. 获取礼物信息
    presentCli := userPresentCli.NewClient()
    presentMapResp, err := presentCli.GetPresentConfigByIdList(ctx, 0, giftIdList, 0)
    if err != nil {
        fmt.Printf("GetPresentConfigByIdList fail, err: %v\n", err)
        panic(err)
    }

    // 转换礼物map
    giftMap := make(map[uint32]*SimplePresentInfo, len(presentMapResp.GetItemList()))
    for _, item := range presentMapResp.GetItemList() {
        giftMap[item.GetItemId()] = &SimplePresentInfo{
            Id:    item.GetItemId(),
            Price: item.GetPrice(),
        }
    }
    // 填充价格
    for _, order := range orderIds {
        if order.PresentInfo != nil {
            if tmpGiftInfo, ok := giftMap[order.PresentInfo.GiftId]; ok {
                order.Price = tmpGiftInfo.Price
            } else {
                log.Warnf("no gift info for giftId %d", order.PresentInfo.GiftId)
            }
        }
    }

    channelIdSet := make(map[uint32]struct{})
    for _, detailData := range orderIds {
        if detailData.PlanInfo == nil || detailData.PlanInfo.ReserveInfo == nil {
            continue
        }
        channelIdSet[detailData.PlanInfo.ReserveInfo.ChannelId] = struct{}{}
    }
    channelIdList := make([]uint32, 0, len(channelIdSet))
    for channelId := range channelIdSet {
        channelIdList = append(channelIdList, channelId)
    }

    channelCli := channel.NewClient()
    channelInfoMap, err := channelCli.BatchGetChannelSimpleInfo(ctx, 0, channelIdList)
    if err != nil {
        log.Errorf("BatchGetChannelSimpleInfo fail, err: %v\n", err)
        panic(err)
    }
    // fill guildId
    for _, detailData := range orderIds {
        if detailData.PlanInfo == nil || detailData.PlanInfo.ReserveInfo == nil {
            continue
        }
        if channelInfo, ok := channelInfoMap[detailData.PlanInfo.ReserveInfo.ChannelId]; ok {
            detailData.GuildId = channelInfo.GetBindId()
        } else {
            log.Warnf("no channel info for channelId %d", detailData.PlanInfo.ReserveInfo.ChannelId)
        }
    }

    return orderIds
}
