package store

import (
    "context"
    "fmt"
    "testing"
    "time"
)

func TestStore_GetWeddingOrderCnt(t *testing.T) {
    cnt, err := testStore.GetValidWeddingOrderCnt(context.Background(), 2, time.Now().Unix(), 2465920)
    fmt.Println(cnt, err)
}

func TestTime(t *testing.T) {
    now := time.Now()
    endOfDay := time.Date(now.Year(), now.Month(), now.Day()+1, 0, 0, 0, 0, now.Location())
    fmt.Println(now, endOfDay)
}
