package server

import (
	"context"
	"crypto/rand"
	"encoding/binary"
	"encoding/json"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	account "golang.52tt.com/clients/account-go"
	pushclient "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/pkg/deal_token"
	"golang.52tt.com/pkg/decoration"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	ga "golang.52tt.com/protocol/app"
	"golang.52tt.com/protocol/app/channel"
	imPb "golang.52tt.com/protocol/app/im"
	magic_spirit_logic "golang.52tt.com/protocol/app/magic-spirit-logic"
	pushPb "golang.52tt.com/protocol/app/push"
	presentPB_ "golang.52tt.com/protocol/app/userpresent"
	"golang.52tt.com/protocol/common/status"
	accountPB "golang.52tt.com/protocol/services/account-go"
	backpack_base "golang.52tt.com/protocol/services/backpack-base"
	backpackPB "golang.52tt.com/protocol/services/backpacksvr"
	"golang.52tt.com/protocol/services/channellivemgr"
	channelPB "golang.52tt.com/protocol/services/channelsvr"
	guildmemberlvPB "golang.52tt.com/protocol/services/guildMemberLvSvr"
	masked_pk_live "golang.52tt.com/protocol/services/masked-pk-live"
	masked_pk_svr "golang.52tt.com/protocol/services/masked-pk-svr"
	kafkaLevel "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafka_levelup_present"
	numericGoPB "golang.52tt.com/protocol/services/numeric-go"
	pb "golang.52tt.com/protocol/services/present-middleware"
	present_set "golang.52tt.com/protocol/services/present-set"
	"golang.52tt.com/protocol/services/presentextraconf"
	pushPB "golang.52tt.com/protocol/services/push-notification/v2"
	richer_birthday "golang.52tt.com/protocol/services/richer-birthday"
	unifyPayPB "golang.52tt.com/protocol/services/unified_pay"
	presentPB "golang.52tt.com/protocol/services/userpresent"
	"golang.52tt.com/services/present-middleware/models/pay"
	"golang.52tt.com/services/present-middleware/models/presentPush"
	"golang.52tt.com/services/present-middleware/rpc/client"
	"math/big"
	"sync"
	"sync/atomic"
	"time"
)

// 获取orderid，与c++保持一致
// userType可以参考c++的currency::CURRENCY_USER，bizMaxType - currency::BIZ_SEND_PRESENT
func (s *PresentMiddlewareMgr) createOrderId(userType uint32, uid uint32, bizMaxType uint32, bizMinType uint32, data string) string {
	orderId := fmt.Sprintf("%v_%v_%v_%v_%s", userType, uid, bizMaxType, bizMinType, data)
	return orderId
}

func (s *PresentMiddlewareMgr) getStrMicroTime(nowTime time.Time) string {
	if (nowTime.UnixNano()/1000)%1000000 == 0 {
		strMicroTime := fmt.Sprintf("%s_%d", time.Now().Format("20060102150405"), (time.Now().UnixNano()/1000)%1000000)
		return strMicroTime
	}
	strMicroTime := fmt.Sprintf("%s_%d", nowTime.Format("20060102150405"), (nowTime.UnixNano()/1000)%1000000)
	return strMicroTime
}

func (s *PresentMiddlewareMgr) tryFreeze(ctx context.Context, sendExtend *baseSendExtend, itemCount, itemSource, useItemId, sendMethod uint32) (remainCurrency int64, SourceRemain uint32, realItemSource uint32, err error) {

	realItemSource = itemSource
	sendUser := sendExtend.sendUser
	presentConfig := sendExtend.presentConfig
	uniqQrderId := sendExtend.uniqOrderId
	timeVal := sendExtend.nowTs
	payOrderList := sendExtend.orderMap

	// 考虑一下背包优先的逻辑：如果背包物品够扣就走背包，否则走购买;背包优先传入的是礼物的source_id,匹配的时候需要注意
	// 防止混乱这部分就先判断，记住userpresent没有背包优先类型，所以调sendPresent前，一定要先把实际送礼方式收敛到背包/购买
	if uint32(pb.PresentSourceType_PRESENT_SOURCE_PACKAGE_FIRST) == realItemSource {
		backpackList, rErr := s.backpackCli.GetUserBackpack(ctx, sendUser.GetUid())
		if rErr != nil {
			log.ErrorWithCtx(ctx, "PresentMiddlewareMgr -- GetUserBackpack fail. uid:%v err:%v", sendUser.GetUid(), err)
			return remainCurrency, SourceRemain, realItemSource, rErr
		}
		realItemSource = uint32(pb.PresentSourceType_PRESENT_SOURCE_BUY)
		for _, item := range backpackList.GetUserItemList() {
			if item.GetSourceId() == presentConfig.GetItemConfig().GetItemId() && item.ItemType == uint32(backpackPB.PackageItemType_BACKPACK_PRESENT) &&
				item.ItemCount >= itemCount {
				// 够扣，走背包去扣
				realItemSource = uint32(pb.PresentSourceType_PRESENT_SOURCE_PACKAGE)
				break
			}
		}
	}

	// 购买，走unify服务的freezeCurrency并返回remainCurrency
	if uint32(pb.PresentSourceType_PRESENT_SOURCE_BUY) == realItemSource {
		remain, err := s.freezeCurrency(ctx, uniqQrderId, sendUser, presentConfig, itemCount, timeVal, payOrderList, sendMethod)
		if err != nil {
			log.ErrorWithCtx(ctx, "PresentMiddlewareMgr -- freezeCurrency fail. uid:%v item_id:%v price:%v total_item_count:%v "+
				"uniq_order_id %s err:%v", sendUser.GetUid(), presentConfig.ItemConfig.GetItemId(), presentConfig.GetItemConfig().GetPrice(), itemCount, uniqQrderId, err)
			return remainCurrency, SourceRemain, realItemSource, err
		}
		// T豆余额，用于客户端更新t豆显示
		if presentConfig.GetItemConfig().GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) {
			remainCurrency = remain
		}

	} else if uint32(pb.PresentSourceType_PRESENT_SOURCE_PACKAGE) == realItemSource {
		balance, _, err := s.freezeBackpackItem(ctx, sendExtend, itemSource, itemCount, useItemId, presentConfig.GetItemConfig().GetItemId())
		if err != nil {
			log.ErrorWithCtx(ctx, "PresentMiddlewareMgr -- freezeBackpackItem fail. uid:%v item_id:%v price:%v total_item_count:%v "+
				"uniq_order_id %s err:%v", sendUser.GetUid(), presentConfig.ItemConfig.GetItemId(), presentConfig.GetItemConfig().GetPrice(), itemCount, uniqQrderId, err)
			return remainCurrency, SourceRemain, realItemSource, err
		}
		// 物品余额，用于客户端更新背包显示
		SourceRemain = balance
	} else {
		return remainCurrency, SourceRemain, realItemSource, protocol.NewExactServerError(nil, status.ErrPresentSourceInvalid)
	}
	return remainCurrency, SourceRemain, realItemSource, nil
}

func (s *PresentMiddlewareMgr) freezeCurrency(ctx context.Context, uniqQrderId string, sendUser *accountPB.UserResp, itemConfig *presentPB.GetPresentConfigByIdResp, itemCount uint32,
	timeVal time.Time, payOrderList map[string]*PayOrderInfo, sendMethod uint32) (remainCurrency int64, err error) {
	if len(payOrderList) == 0 {
		log.ErrorWithCtx(ctx, "freezeCurrency -- ErrUserPresentInvalidTargetUserSize , uid:%v, item_count:%v, item_config:%v",
			sendUser.GetUid(), itemCount, itemConfig)
		return 0, protocol.NewExactServerError(nil, status.ErrUserPresentInvalidTargetUserSize)
	}

	totalPrice := itemConfig.GetItemConfig().GetPrice() * itemCount
	if 0 == totalPrice {
		log.ErrorWithCtx(ctx, "freezeCurrency -- ErrUserPresentInvalidItemCount. Invalid total_price, uid:%v, item_count:%v, item_config:%v",
			sendUser.GetUid(), itemCount, itemConfig)
		return 0, protocol.NewExactServerError(nil, status.ErrUserPresentInvalidItemCount)
	}

	var fac pay.Payer

	// 把礼物的pricetype转成支付的
	if itemConfig.GetItemConfig().GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_RED_DIAMOND) {
		fac = s.PayFactory.GetPayer(uint32(pay.PayType_Rediamond))
	} else if itemConfig.GetItemConfig().GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) {
		fac = s.PayFactory.GetPayer(uint32(pay.PayType_Tbean_Old))
	} else {
		log.ErrorWithCtx(ctx, "freezeCurrency -- ERR_USER_PRESENT_BUY_FAILED. Unknown fee type, uid:%v, item_config:%s",
			sendUser.GetUid(), itemConfig)
		return 0, protocol.NewExactServerError(nil, status.ErrUserPresentBuyFailed)
	}

	AppId := "TT_HZ"
	if sendMethod == uint32(pb.PresentSendMethodType_PRESENT_TYPE_FELLOW) {
		AppId = "TT_ZY"
	}

	freezeReq := &pay.FreezeReq{
		FreezeUser:    sendUser,
		FreezeTime:    timeVal,
		AppId:         AppId,
		UniqueOrderId: uniqQrderId,
	}
	freezeReq.OrderInfoList = make(map[string]*pay.OrderInfo)

	for _, order := range payOrderList {
		freezeReq.OrderInfoList[order.orderId] = &pay.OrderInfo{
			OrderId:       order.orderId,
			TargetUid:     order.targetUid,
			PresentConfig: itemConfig.GetItemConfig(),
			Count:         itemCount,
			Reason:        "赠送礼物",
		}
	}

	resp, err := fac.Freeze(ctx, freezeReq)

	if err != nil {
		log.ErrorWithCtx(ctx, "freezeCurrency -- Freeze err, uid:%v, err:%s",
			sendUser.GetUid(), err)
		return 0, err
	}

	for _, item := range resp.OrderResp {
		if item.Err != nil {
			log.ErrorWithCtx(ctx, "freezeCurrency -- Freeze err, uid:%v, err:%s",
				sendUser.GetUid(), item.Err)
			return 0, item.Err
		}
	}

	for _, order := range payOrderList {
		if _, ok := resp.OrderResp[order.orderId]; ok {
			order.dealToken = resp.OrderResp[order.orderId].DealToken
			order.tbTime = resp.OrderResp[order.orderId].TbeanSystemTime
			order.payOrderId = resp.OrderResp[order.orderId].PayOrderId
		}
	}

	remainCurrency = resp.Balance

	return remainCurrency, nil
}

func (s *PresentMiddlewareMgr) freezeBackpackItem(ctx context.Context, sendExtend *baseSendExtend, presentSource, count, backpackItemId, itemId uint32) (remain uint32, expireTime uint32, err error) {
	freezeReq := &pay.FreezeReq{
		FreezeUser:    sendExtend.sendUser,
		FreezeTime:    sendExtend.nowTs,
		UniqueOrderId: sendExtend.uniqOrderId,
	}

	presentConfig := sendExtend.presentConfigMap[itemId]
	if presentConfig == nil {
		presentConfig = sendExtend.presentConfig.GetItemConfig()
	}

	freezeReq.OrderInfoList = make(map[string]*pay.OrderInfo)
	freezeReq.OrderInfoList[sendExtend.uniqOrderId] = &pay.OrderInfo{
		OrderId:       sendExtend.uniqOrderId,
		PresentConfig: presentConfig,
		Count:         count,
		Reason:        "赠送礼物",
		BackpackInfo: &pay.BackpackInfo{
			BackpackItemId: backpackItemId,
			ChannelId:      sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelId(),
		},
	}
	freezeReq.OrderInfoList[sendExtend.uniqOrderId].BackpackInfo.PayOrderList = make([]string, 0)
	for _, order := range sendExtend.orderMap {
		freezeReq.OrderInfoList[sendExtend.uniqOrderId].BackpackInfo.PayOrderList = append(freezeReq.OrderInfoList[sendExtend.uniqOrderId].BackpackInfo.PayOrderList, order.orderId)
		freezeReq.OrderInfoList[sendExtend.uniqOrderId].BackpackInfo.UidList = append(freezeReq.OrderInfoList[sendExtend.uniqOrderId].BackpackInfo.UidList, order.targetUid)
	}

	freezeResp, err := s.PayFactory.GetPayer(uint32(pay.PayType_Backpack)).Freeze(ctx, freezeReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "freezeCurrency -- freezeBackpackItem , uid:%v, item_count:%v, item_config:%v")
		return 0, 0, err
	}

	// 由于分了订单，所以还要检查每个订单的成功失败，这里就只有一笔，有报错就直接返回错误
	for _, item := range freezeResp.OrderResp {
		if item.Err != nil {
			return 0, 0, item.Err
		}
	}

	// 填一下orderMap的dealToken
	for _, order := range sendExtend.orderMap {
		dealToken := freezeResp.OrderResp[sendExtend.uniqOrderId].DealToken
		dealTokenContent, err := deal_token.Decode(dealToken)
		if err != nil {
			log.ErrorWithCtx(ctx, "deal_token.Decode fail , err %v , order_id %s", err, order.orderId)
			continue
		}
		dealTokenContent.OrderID = order.orderId
		newDealToken, _ := deal_token.Encode(dealTokenContent)
		order.dealToken = newDealToken
	}

	return uint32(freezeResp.Balance), uint32(freezeResp.ExpireTime), err
}

func (s *PresentMiddlewareMgr) procPushEvent(ctx context.Context, sendExtend *baseSendExtend, in *pb.SendPresentReq, out *pb.SendPresentResp) {
	bIsNoPush := false
	sendUser := sendExtend.sendUser
	channelInfo := sendExtend.channelSimpleInfo
	targetIndex := 0

	userPushType, channelPushType, imPushType := s.genPushType(in.GetPushInfo().GetPersonalPushType(), in.GetPushInfo().GetChannelPushType(),
		in.GetPushInfo().GetImMsgType())

	// 增加短超时
	tmpCtx, cancel := context.WithTimeout(ctx, time.Millisecond*100)
	defer cancel()
	size, err := s.channelolCli.GetChannelMemberSize(tmpCtx, sendUser.GetUid(), channelInfo.GetChannelSimple().GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "PresentBatchSendService::procPushEvent GetChannelMemberSize failed. err:%v, uid:%v", err, sendUser.GetUid())
	}

	if in.GetPushInfo().GetChannelPushType() == uint32(pb.PushInfo_Channel_ALLMIC) {
		// 全麦单独摘出来，每种礼物只用发一次推送
		for _, msgInfo := range out.GetBatchInfo() {
			//房间人数较多，则丢一部分推送
			itemCfg := sendExtend.presentConfigMap[msgInfo.GetItemInfo().GetItemId()]
			if uint32(presentPB.PresentPriceType_PRESENT_PRICE_RED_DIAMOND) == itemCfg.GetPriceType() && channelInfo.GetChannelSimple().GetChannelId() != 0 {
				dropRadio := (size - 1000) / 30
				if dropRadio == 0 {
					dropRadio = 1
				}
				if dropRadio > 90 {
					dropRadio = 90
				}
				roll, _ := rand.Int(rand.Reader, big.NewInt(100))
				if size > 1000 && uint32(roll.Int64()) > dropRadio {
					bIsNoPush = true
				}
			}

			if !bIsNoPush {
				// 房间推送
				messageItemInfo := &presentPB_.PresentSendItemInfo{
					ItemId:            msgInfo.GetItemInfo().ItemId,
					Count:             msgInfo.GetItemInfo().Count,
					IsBatch:           msgInfo.GetItemInfo().IsBatch,
					ShowBatchEffect:   msgInfo.GetItemInfo().ShowBatchEffect,
					ShowEffect:        msgInfo.GetItemInfo().ShowEffect,
					ShowEffectV2:      msgInfo.GetItemInfo().ShowEffectV2,
					FlowId:            msgInfo.GetItemInfo().FlowId,
					SendType:          msgInfo.GetItemInfo().SendType,
					DynamicTemplateId: msgInfo.GetItemInfo().DynamicTemplateId,
					IsVisibleToSender: false,
					IsShowSurprise:    msgInfo.GetItemInfo().IsShowSurprise,
					SurpriseCount:     msgInfo.GetItemInfo().SurpriseCount,
				}

				// 房间推送给所有人，则可见为true
				if in.GetPushInfo().GetChannelPushType() == uint32(pb.PushInfo_Channel_ALLMIC_ALL) {
					messageItemInfo.IsVisibleToSender = true
				}

				msgTargetList := make([]*presentPB_.PresentBatchTargetInfo, 0)
				for _, user := range msgInfo.GetTargetList() {
					msg := &presentPB_.PresentBatchTargetInfo{
						Uid:        user.GetUid(),
						Account:    user.Account,
						Nickname:   user.Nickname,
						ExtendJson: user.ExtendJson,
					}
					fillBatchTargetUserProfile(msg, sendExtend)

					msgTargetList = append(msgTargetList, msg)
				}

				msgContent := presentPB_.PresentBatchInfoMsg{
					ItemId:         msgInfo.ItemId,
					TotalItemCount: msgInfo.TotalItemCount,
					BatchType:      msgInfo.BatchType,
					SendTime:       msgInfo.SendTime,
					ChannelId:      msgInfo.ChannelId,
					SendUid:        msgInfo.SendUid,
					SendAccount:    msgInfo.SendAccount,
					SendNickname:   msgInfo.SendNickname,
					ExtendJson:     msgInfo.ExtendJson,
					TargetList:     msgTargetList,
					ItemInfo:       messageItemInfo,
					IsMulti:        msgInfo.GetIsMulti(),
				}

				_ = s.PushFactory.GetPushMgr(presentPush.PushType_Channel).Push(ctx, &presentPush.PushReq{
					FromUser:       sendUser,
					Channel:        channelInfo.GetChannelSimple(),
					ItemConfig:     sendExtend.presentConfigMap[msgInfo.GetItemId()],
					UserExtendJson: msgInfo.ExtendJson,
					MsgType:        channelPushType,
					MsgContent:     &msgContent,
					ToUsers:        map[uint32]*accountPB.UserResp{sendUser.GetUid(): sendUser},
					SendTime:       int64(msgInfo.SendTime),
					UkwInfoMap:     sendExtend.ukwInfoMap,
				})

				// 全麦合成一条了，如果要发给送礼人，在这里发
				if in.GetPushInfo().GetPersonalPushType() == uint32(pb.PushInfo_Person_NORMAL_SENDER) {
					pushReq := &presentPush.PushReq{
						FromUser:       sendUser,
						Channel:        channelInfo.GetChannelSimple(),
						ItemConfig:     sendExtend.presentConfigMap[msgInfo.GetItemId()],
						ItemSendInfo:   messageItemInfo,
						UserExtendJson: sendExtend.extendInfo.userExtendJson,
						MsgType:        userPushType,
						ToUsers:        map[uint32]*accountPB.UserResp{sendUser.GetUid(): sendUser},
						ToSender:       true,
						SendTime:       int64(msgInfo.SendTime),
						UkwInfoMap:     sendExtend.ukwInfoMap,
					}
					_ = s.PushFactory.GetPushMgr(presentPush.PushType_User).Push(ctx, pushReq)
				}
			}
		}
	}

	for _, item := range out.GetMsgInfo() {
		realUid := sendExtend.fake2RealUidMap[item.GetTargetUid()]
		targetUser := sendExtend.targetUserMap[realUid]
		itemCfg := sendExtend.presentConfigMap[item.GetItemInfo().GetItemId()]

		pushDelay := uint32(0)
		if itemCfg.GetExtend().GetNotifyAll() && itemCfg.GetExtend().GetIsBoxBreaking() {
			// 默认22s
			if s.GeneralConfig.GetPresentConfig().BoxPushDelay != 0 {
				pushDelay = s.GeneralConfig.GetPresentConfig().BoxPushDelay
			} else {
				pushDelay = PushDelayTime
			}
		}

		messageItemInfo := &presentPB_.PresentSendItemInfo{
			ItemId:            item.GetItemInfo().ItemId,
			Count:             item.GetItemInfo().Count,
			IsBatch:           item.GetItemInfo().IsBatch,
			ShowBatchEffect:   item.GetItemInfo().ShowBatchEffect,
			ShowEffect:        item.GetItemInfo().ShowEffect,
			ShowEffectV2:      item.GetItemInfo().ShowEffectV2,
			FlowId:            item.GetItemInfo().FlowId,
			SendType:          item.GetItemInfo().SendType,
			DynamicTemplateId: item.GetItemInfo().DynamicTemplateId,
			IsVisibleToSender: false,
			IsShowSurprise:    item.GetItemInfo().IsShowSurprise,
			SurpriseCount:     item.GetItemInfo().SurpriseCount,
			CustomTextJson:    item.GetItemInfo().GetCustomTextJson(),
		}

		// 房间推送给所有人，则可见为true
		if in.GetPushInfo().GetChannelPushType() == uint32(pb.PushInfo_Channel_NORMAL_ALL) || in.GetPushInfo().GetChannelPushType() == uint32(pb.PushInfo_Channel_ALLMIC_ALL) {
			messageItemInfo.IsVisibleToSender = true
		}

		isBoxVisible := false
		if in.GetPushInfo().GetPersonalPushType() == uint32(pb.PushInfo_Person_NORMAL_SENDER) {
			isBoxVisible = true
		}

		//房间人数较多，则丢一部分推送
		if uint32(presentPB.PresentPriceType_PRESENT_PRICE_RED_DIAMOND) == itemCfg.GetPriceType() && channelInfo.GetChannelSimple().GetChannelId() != 0 {
			dropRadio := (size - 1000) / 30
			if dropRadio == 0 {
				dropRadio = 1
			}
			if dropRadio > 90 {
				dropRadio = 90
			}
			roll, _ := rand.Int(rand.Reader, big.NewInt(100))
			if size > 1000 && uint32(roll.Int64()) > dropRadio {
				bIsNoPush = true
			}
		}

		// 组织一下涂鸦
		drawPic := &presentPB_.DrawPresentPicture{LineList: []*presentPB_.PresentLine{}}
		if item.GetItemInfo().DrawPresentPic != nil {
			for _, line := range item.GetItemInfo().DrawPresentPic.LineList {
				drawLine := &presentPB_.PresentLine{}
				for _, point := range line.PointList {
					drawLine.PointList = append(drawLine.PointList, &presentPB_.PresentPoint{X: point.X, Y: point.Y})
				}
				drawLine.ItemId = line.ItemId
				drawPic.LineList = append(drawPic.LineList, drawLine)
			}
		}

		messageItemInfo.DrawPresentPic = drawPic

		userExtendJson := ""

		extend, ok := sendExtend.extendInfo.targetInfoMap[targetUser.GetUid()]
		if ok {
			userExtendJson = extend.userExtendJson
		}

		if !bIsNoPush && userPushType != 0 {
			_ = s.PushFactory.GetPushMgr(presentPush.PushType_User).Push(ctx, &presentPush.PushReq{
				FromUser:       sendUser,
				Channel:        channelInfo.GetChannelSimple(),
				ItemConfig:     itemCfg,
				ItemSendInfo:   messageItemInfo,
				UserExtendJson: userExtendJson,
				MsgType:        userPushType,
				ToUsers:        map[uint32]*accountPB.UserResp{targetUser.GetUid(): targetUser},
				SendTime:       int64(item.GetSendTime()),
				UkwInfoMap:     sendExtend.ukwInfoMap,
				TargetIndex:    int64(targetIndex),
				PushDelay:      pushDelay,
				IsBoxVisible:   isBoxVisible,
			})
		}

		// 如果要发给送礼人，在这里发
		if in.GetPushInfo().GetPersonalPushType() == uint32(pb.PushInfo_Person_NORMAL_SENDER) {
			extendJson, _ := json.Marshal(sendExtend.extendInfo.sendInfoMap[itemCfg.GetItemId()])
			pushReq := &presentPush.PushReq{
				FromUser:       sendUser,
				Channel:        channelInfo.GetChannelSimple(),
				ItemConfig:     itemCfg,
				ItemSendInfo:   messageItemInfo,
				UserExtendJson: string(extendJson),
				MsgType:        userPushType,
				ToUsers:        map[uint32]*accountPB.UserResp{targetUser.GetUid(): targetUser},
				ToSender:       true,
				SendTime:       int64(item.GetSendTime()),
				UkwInfoMap:     sendExtend.ukwInfoMap,
				TargetIndex:    int64(targetIndex),
				PushDelay:      pushDelay,
				IsBoxVisible:   isBoxVisible,
				OnlyMessage:    item.GetOnlyShowMessage(),
			}
			_ = s.PushFactory.GetPushMgr(presentPush.PushType_User).Push(ctx, pushReq)
		}

		if !bIsNoPush && channelPushType != 0 {
			req := &presentPush.PushReq{
				FromUser:       sendUser,
				Channel:        channelInfo.GetChannelSimple(),
				ItemConfig:     itemCfg,
				ItemSendInfo:   messageItemInfo,
				UserExtendJson: item.ExtendJson,
				MsgType:        uint32(channel.ChannelMsgType_CHANNEL_PRESENT_MSG),
				ToUsers:        map[uint32]*accountPB.UserResp{targetUser.GetUid(): targetUser},
				SendTime:       int64(item.GetSendTime()),
				UkwInfoMap:     sendExtend.ukwInfoMap,
				TargetIndex:    int64(targetIndex),
				PushDelay:      pushDelay,
				IsBoxVisible:   isBoxVisible,
			}
			_ = s.PushFactory.GetPushMgr(presentPush.PushType_Channel).Push(ctx, req)

			out.BoxDetail = presentPush.TranPushReqToBoxInfo(req, targetUser).GetBoxDetail()
		}

		if !bIsNoPush && imPushType != 0 {
			_ = s.PushFactory.GetPushMgr(presentPush.PushType_IM).Push(ctx, &presentPush.PushReq{
				FromUser:       sendUser,
				Channel:        channelInfo.GetChannelSimple(),
				ItemConfig:     itemCfg,
				ItemSendInfo:   messageItemInfo,
				UserExtendJson: item.ExtendJson,
				MsgType:        userPushType,
				ToUsers:        map[uint32]*accountPB.UserResp{targetUser.GetUid(): targetUser},
				SendTime:       int64(item.GetSendTime()),
				UkwInfoMap:     sendExtend.ukwInfoMap,
				PushDelay:      pushDelay,
				IsBoxVisible:   isBoxVisible,
			})
		}

		s.PushPresentBreakingNewsToAll(ctx, sendUser, &presentPB.GetPresentConfigByIdResp{ItemConfig: itemCfg},
			item.GetItemInfo().GetCount(), time.Now(), channelInfo.GetChannelSimple().GetChannelId(), targetUser,
			sendExtend)

		// 单次1w全服
		if item.GetItemInfo().GetCount()*itemCfg.GetPrice() >= 1000000 &&
			in.GetBatchType() == uint32(pb.PresentBatchSendType_PRESENT_SOURCE_NONE) &&
			itemCfg.GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) &&
			itemCfg.GetExtend().GetTag() != uint32(ga.PresentTagType_PRESENT_TAG_LEVELUP) {
			_ = s.PushHighSpendPresentBreakingNews(ctx, sendUser, &presentPB.GetPresentConfigByIdResp{ItemConfig: itemCfg},
				item.GetItemInfo().GetCount(), time.Now(), channelInfo.GetChannelSimple(), targetUser, sendExtend)
		}

		targetIndex++
	}

}

func (s *PresentMiddlewareMgr) ProcPresentWatch(ctx context.Context, sendExtend *baseSendExtend, itemCount uint32, isOptValid bool) (err error) {

	sendUser := sendExtend.sendUser
	extendInfo := sendExtend.extendInfo
	channelInfo := sendExtend.channelSimpleInfo
	targetUserMap := sendExtend.targetUserMap
	itemCfg := sendExtend.presentConfig
	orderId := sendExtend.uniqOrderId

	bIsOpUserNeedSyncGrowInfo := false
	if extendInfo.bIsSendUserRichLevelChanged {
		bIsOpUserNeedSyncGrowInfo = true
	}

	channelSimpleInfo := channelInfo.GetChannelSimple()
	totalItemCount := itemCount * uint32(len(targetUserMap))
	totalPrice := int32(totalItemCount * itemCfg.GetItemConfig().Price)
	channelGuildid := uint32(0)

	if channelSimpleInfo.GetChannelType() == uint32(1) || channelSimpleInfo.GetChannelType() == uint32(4) {
		channelGuildid = channelSimpleInfo.GetBindId()
	}
	memberContributionAdded := int32(0)

	feeType := unifyPayPB.FeeType_UNKNOWN
	if itemCfg.GetItemConfig().GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_RED_DIAMOND) {
		feeType = unifyPayPB.FeeType_RED_DIAMOND
	} else if itemCfg.GetItemConfig().GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) {
		feeType = unifyPayPB.FeeType_TBEAN
	} else {
		feeType = unifyPayPB.FeeType_UNKNOWN
	}

	wg := sync.WaitGroup{}
	wg.Add(1)
	go func() {
		// 加工会贡献
		if channelGuildid > 0 && sendUser.GetCurrentGuildId() == channelGuildid {
			orderDesc := fmt.Sprintf("送出礼物\"%s\"", itemCfg.GetItemConfig().Name)
			if itemCount > 1 {
				orderDesc += fmt.Sprintf(" x %d", totalItemCount)
			}
			contributionResp, err := s.guildmemberlvCli.AddMemberContribution(ctx, &guildmemberlvPB.AddMemberContributionReq{
				GuildId: channelGuildid,
				OperInfo: &guildmemberlvPB.StMemberContributionOper{
					OperType:   uint32(guildmemberlvPB.OPER_TYPE_OPER_SEND_PRESENT),
					OperValue:  totalPrice,
					OrderId:    orderId,
					OrderDesc:  orderDesc,
					OptInvalid: !isOptValid,
				},
			}, sendUser.GetUid())
			if err != nil {
				log.ErrorWithCtx(ctx, "ProcPresentWatch -- addMemberContribution fail:%v, op:%v, guild:%v, gift:%v",
					err, sendUser.GetUid(), sendUser.CurrentGuildId, itemCfg.GetItemConfig().GetItemId())
			} else {
				memberContributionAdded = contributionResp.ContributionAdded
				bIsOpUserNeedSyncGrowInfo = true
			}
		}

		if itemCfg.GetItemConfig().GetExtend().GetEffectEndDelay() {
			_, _ = client.PresentExtraCli.NotifyPresentSend(ctx, &presentextraconf.NotifyPresentSendReq{
				Uid:    sendUser.GetUid(),
				GiftId: itemCfg.GetItemConfig().GetItemId(),
				Count:  totalItemCount,
			})
			log.InfoWithCtx(ctx, "NotifyPresentSend %v", itemCfg.GetItemConfig())
		}

		wg.Done()
	}()

	wg.Add(1)
	go func() {
		//推送变动
		if feeType == unifyPayPB.FeeType_RED_DIAMOND {
			_ = s.MissionHelperCli.UpdateUserGrowTimeline(ctx, sendUser.GetUid())
			//6 对应 grow，sync.proto中定义
			_ = s.notifyClient(ctx, []uint32{sendUser.GetUid()}, 6)
		}
		wg.Done()
	}()

	wg.Add(1)
	go func() {
		if bIsOpUserNeedSyncGrowInfo {
			_ = s.MissionHelperCli.UpdateUserScoreAndCharmRichSync(ctx, sendUser.GetUid())
			//6 对应 grow，sync.proto中定义
			_ = s.notifyClient(ctx, []uint32{sendUser.GetUid()}, 6)
		}
		wg.Done()
	}()

	isRecordRich := s.isRecordSenderRich(ctx)
	var richValue int32
	if isRecordRich {
		richValue = int32(extendInfo.baseRichValue)
	}

	// 填extendjson
	jsonSendUser := jsonSendUser{
		RichValue:                richValue,
		RichValuePrefix:          "",
		MemberContribution:       memberContributionAdded,
		MemberContributionPrefix: "",
		BonusMsg:                 extendInfo.richValueBonusMsg,
	}
	userExtendJson, _ := json.Marshal(jsonSendUser)
	extendInfo.userExtendJson = string(userExtendJson)
	log.DebugWithCtx(ctx, "【exptndJson】:%s , %v", extendInfo.userExtendJson, jsonSendUser)

	for _, target := range extendInfo.targetInfoMap {
		target := target
		wg.Add(1)
		go func() {
			_ = s.MissionHelperCli.UpdateUserScoreAndCharmRichSync(ctx, target.uid)
			//6 对应 grow，sync.proto中定义
			_ = s.notifyClient(ctx, []uint32{target.uid}, 6)
			jsonTargetUser := jsonTargetUser{int32(itemCfg.GetItemConfig().GetScore() * itemCount), "", int32(target.realAddCharm), ""}
			userExtendJson, _ = json.Marshal(jsonTargetUser)
			target.userExtendJson = string(userExtendJson)
			wg.Done()
		}()
	}

	wg.Add(1)
	go func() {
		//财富魅力升级推送
		if isRecordRich && feeType == unifyPayPB.FeeType_RED_DIAMOND {
			err := s.broadcastUserRichOrCharmLevelUpdate(ctx, sendUser, channelSimpleInfo.GetChannelId(), targetUserMap, extendInfo, isRecordRich, sendExtend)
			if err != nil {
				log.ErrorWithCtx(ctx, "ProcPresentWatch -- broadcastUserRichOrCharmLevelUpdate failed. uid:%d  err %v",
					sendUser.GetUid(), err)
			}
		}
		wg.Done()
	}()

	sucUserMap := make(map[uint32]bool)
	for _, order := range sendExtend.sucOrders {
		sucUserMap[order.targetUid] = true
	}

	if itemCfg.GetItemConfig().GetExtend().GetTag() == uint32(ga.PresentTagType_PRESENT_TAG_SET) {
		wg.Add(1)
		go func() {
			defer wg.Done()
			presentCount := uint32(0)
			orderIds := make([]string, 0)

			for _, item := range sendExtend.sucOrders {
				presentCount += item.count
				orderIds = append(orderIds, item.orderId)
			}

			_, err := client.PresentSetCli.UserSentSetPresent(ctx, &present_set.UserSentSetPresentReq{
				Uid:          sendUser.GetUid(),
				PresentId:    itemCfg.GetItemConfig().GetItemId(),
				PresentCount: presentCount,
				OrderId:      orderIds,
				SendTime:     uint32(sendExtend.nowTs.Unix()),
			})

			if err != nil {
				log.ErrorWithCtx(ctx, "HandlePresentSetRecord UserSentSetPresent err , uid %d err %v", sendUser.GetUid(), err)
				return
			}

			return
		}()
	}

	wg.Wait()

	// 晚于开始时间才检测
	if s.GeneralConfig.GetPresentConfig().ScoreCheckBegin < time.Now().Unix() {
		for uid, _ := range sucUserMap {
			// 测试用，如果在这个名单里直接发
			for _, item := range s.GeneralConfig.GetPresentConfig().FirstScoreMsgTrigger {
				if item == uid {
					_ = s.SendFirstScoreMsg(ctx, uid, 2, 1)
					if sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelId() != 0 {
						_ = s.PushFirstScore(ctx, uid)
					}
					continue
				}
			}

			// 如果是无签约（不可提现积分）
			if sendExtend.ScoreTypeMap[uid] == 1 && itemCfg.GetItemConfig().GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) {
				log.DebugWithCtx(ctx, "HandleFirstPresent %d", uid)
				s.HandleFirstPresent(ctx, sendExtend, uid)
			}
		}
	}

	return err

}

func (s *PresentMiddlewareMgr) ProcPresentWatchWithList(ctx context.Context, sendExtend *baseSendExtend, itemWithCount map[uint32]uint32) (err error) {

	extendInfo := sendExtend.extendInfo
	bIsOpUserNeedSyncGrowInfo := false
	if extendInfo.bIsSendUserRichLevelChanged {
		bIsOpUserNeedSyncGrowInfo = true
	}
	channelInfo := sendExtend.channelSimpleInfo

	channelSimpleInfo := channelInfo.GetChannelSimple()
	channelGuildid := uint32(0)
	sendUser := sendExtend.sendUser

	if channelSimpleInfo.GetChannelType() == uint32(1) || channelSimpleInfo.GetChannelType() == uint32(4) {
		channelGuildid = channelSimpleInfo.GetBindId()
	}

	isRecordRich := s.isRecordRich(ctx, sendUser.GetUid())

	memberContributionAdded := make(map[uint32]int32)
	needSyncReddiamond := false
	hasTbeanPresent := false

	//log.ErrorWithCtx(ctx,"totalPrice is : %v , order : %v ,  isOptValid : %v", totalPrice, orderId)

	// 公会贡献，因为需要返回，所以没法异步处理，这里并发处理下。
	wg := sync.WaitGroup{}

	for id, count := range itemWithCount {
		wg.Add(1)
		go func(id uint32, count uint32) {
			// 加工会贡献
			defer func() {
				wg.Done()
			}()
			if channelGuildid > 0 && sendUser.GetCurrentGuildId() == channelGuildid {
				orderDesc := fmt.Sprintf("送出礼物\"%s\"", sendExtend.presentConfigMap[id].GetName())
				if count > 1 {
					orderDesc += fmt.Sprintf(" x %d", count)
				}
				sv, _ := protogrpc.ServiceInfoFromContext(ctx)
				if sv.UserID == 0 {
					sv.UserID = sendUser.GetUid()
				}
				newCtx := protogrpc.WithServiceInfo(ctx, sv)
				contributionResp, err := s.guildmemberlvCli.AddMemberContribution(newCtx, &guildmemberlvPB.AddMemberContributionReq{
					GuildId: channelGuildid,
					OperInfo: &guildmemberlvPB.StMemberContributionOper{
						OperType:   uint32(guildmemberlvPB.OPER_TYPE_OPER_SEND_PRESENT),
						OperValue:  int32(sendExtend.presentConfigMap[id].GetPrice() * count),
						OrderId:    fmt.Sprintf("%v_%d", sendExtend.uniqOrderId, id),
						OrderDesc:  orderDesc,
						OptInvalid: true,
					},
				}, sendUser.GetUid())
				log.InfoWithCtx(ctx, "%s %d", fmt.Sprintf("%v_%d", sendExtend.uniqOrderId, id), sendUser.GetUid())
				if err != nil {
					log.ErrorWithCtx(ctx, "ProcPresentWatch -- addMemberContribution fail:%v, op:%v, guild:%v, gift:%v",
						err, sendUser.GetUid(), sendUser.CurrentGuildId, sendExtend.presentConfigMap[id].GetItemId())
				} else {
					memberContributionAdded[id] = contributionResp.ContributionAdded
					bIsOpUserNeedSyncGrowInfo = true
				}
			}
		}(id, count)

		if sendExtend.presentConfigMap[id].GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_RED_DIAMOND) {
			needSyncReddiamond = true
		}

		if sendExtend.presentConfigMap[id].GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) {
			hasTbeanPresent = true
			s.HandlePresentSetRecord(ctx, sendExtend, sendUser.GetUid(), id)
		}

		log.DebugWithCtx(ctx, "NotifyPresentSend %v", sendExtend.presentConfigMap[id].GetExtend().GetEffectEndDelay())
		if sendExtend.presentConfigMap[id].GetExtend().GetEffectEndDelay() {
			s.HandleNotifyPresentSend(ctx, sendExtend, id, count)
		}

	}

	wg.Wait()

	s.HandleConsumePrivilege(ctx, sendExtend)

	sucUserMap := make(map[uint32]bool)
	for _, order := range sendExtend.sucOrders {
		sucUserMap[order.targetUid] = true
	}

	s.HandleTimePresentSend(ctx, sendExtend)
	s.HandleLiveIntimateSend(ctx, sendExtend)

	if len(sucUserMap) == 0 {
		for _, item := range sendExtend.sucUsers {
			sucUserMap[item.GetUid()] = true
		}
	}

	for uid, _ := range sucUserMap {
		// 测试用，如果在这个名单里直接发
		for _, item := range s.GeneralConfig.GetPresentConfig().FirstScoreMsgTrigger {
			if item == uid {
				_ = s.SendFirstScoreMsg(ctx, uid, 0, 0)
				if sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelId() != 0 {
					_ = s.PushFirstScore(ctx, uid)
				}
				break
			}
		}

		// 无签约（不可提现积分）
		if sendExtend.ScoreTypeMap[uid] == 1 && hasTbeanPresent && s.GeneralConfig.GetPresentConfig().ScoreCheckBegin < time.Now().Unix() {
			log.DebugWithCtx(ctx, "HandleFirstPresent %d", uid)
			s.HandleFirstPresent(ctx, sendExtend, uid)
		}
	}

	if needSyncReddiamond {
		s.HandleUpdateUserGrowTimeline(ctx, sendExtend)
		// 红钻才需要这个值
		s.HandleVipPrivilege(ctx, sendExtend)
	}

	if isRecordRich && needSyncReddiamond {
		s.HandleUserRichOrCharmLevelUpdate(ctx, sendExtend, isRecordRich)
	}

	if bIsOpUserNeedSyncGrowInfo {
		s.HandleSendUserScoreAndCharmRichSync(ctx, sendExtend)
	}

	for _, target := range extendInfo.targetInfoMap {
		s.HandleTargetUserScoreAndCharmRichSync(ctx, sendExtend, target)

		jsonTargetUser := jsonTargetUser{int32(target.score), "", int32(target.realAddCharm), ""}
		userExtendJson, _ := json.Marshal(jsonTargetUser)
		target.userExtendJson = string(userExtendJson)
	}

	for _, target := range extendInfo.multiTargetInfoMap {
		for _, item := range target {
			jsonTargetUser := jsonTargetUser{int32(item.score), "", int32(item.realAddCharm), ""}
			userExtendJson, _ := json.Marshal(jsonTargetUser)
			item.userExtendJson = string(userExtendJson)
		}
	}

	// 填extendjson
	for id := range itemWithCount {
		if _, ok := sendExtend.extendInfo.sendInfoMap[id]; !ok {
			continue
		}

		var richValue int32
		isRecordRich := ctx.Value(IsRecordSenderRichKey)

		if isRecordRich.(bool) {
			richValue = sendExtend.extendInfo.sendInfoMap[id].RichValue
		}
		jsonSendUser := jsonSendUser{
			RichValue:                richValue,
			RichValuePrefix:          "",
			MemberContribution:       memberContributionAdded[id],
			MemberContributionPrefix: "",
			BonusMsg:                 sendExtend.extendInfo.sendInfoMap[id].BonusMsg,
		}
		sendExtend.extendInfo.sendInfoMap[id] = &jsonSendUser
		log.InfoWithCtx(ctx, "【exptndJson】:%s , %v", extendInfo.userExtendJson, jsonSendUser)
	}

	return err

}

//func (s *PresentMiddlewareMgr) checkValidUserOpt(ctx context.Context, sendUser *accountPB.UserResp, itemCfg *presentPB.GetPresentConfigByIdResp, serviceInfo *pb.ServiceCtrlInfo) (isValid bool, err error) {
//
//	if itemCfg.GetItemConfig().GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) {
//		isValid = true
//		return isValid, err
//	}
//
//	if 0 == sendUser.GetCurrentGuildId() {
//		isValid = true
//		return isValid, err
//	}
//	//hex加密
//	devId := make([]byte, 0)
//	hex.Encode([]byte(serviceInfo.DeviceId), devId)
//
//	//检查公会
//	_, err = s.guildCli.GetGuildMember(ctx, sendUser.GetUid(), sendUser.GetCurrentGuildId())
//	if err != nil {
//		log.ErrorWithCtx(ctx, "checkValidUserOpt -- GetGuildMember failed. uid %d err %v", sendUser.GetUid(), err)
//		return false, err
//	}
//
//	isValidCd, err := s.checkGuildMemberOptId(ctx, serviceInfo, sendUser.GetUid())
//	if err != nil {
//		log.ErrorWithCtx(ctx, "checkValidUserOpt -- checkGuildMemberOptId failed. uid %d err %v", sendUser.GetUid(), err)
//		return false, err
//	}
//
//	isFakeDevice, err := s.checkFakeDevice(ctx, sendUser.GetUid())
//	if err != nil {
//		log.ErrorWithCtx(ctx, "checkValidUserOpt -- checkFakeDevice failed. uid %d err %v", sendUser.GetUid(), err)
//		return false, err
//	}
//
//	if isValidCd && (!isFakeDevice) {
//		return true, nil
//	}
//
//	return false, nil
//
//}

//func (s *PresentMiddlewareMgr) checkGuildMemberOptId(ctx context.Context, serviceInfo *pb.ServiceCtrlInfo, uid uint32) (isValid bool, err error) {
//	//公司出口ip白名单
//	if serviceInfo.ClientIp == "*************" || serviceInfo.ClientIp == "**************" || serviceInfo.ClientIp == "*************" {
//		isValid = true
//		return isValid, nil
//	}
//
//	profile, err := s.antiCli.GetUserProfile(ctx, uid, false, false)
//	if err != nil {
//		log.ErrorWithCtx(ctx, "checkValidUserOpt -- GetUserProfile failed. uid %d err %v", uid, err)
//		return false, err
//	}
//
//	if !(profile.Profile == int32(antiPB.USER_PROFILE_USER_PROFILE_NORMAL)) {
//		//无效用户
//		return false, err
//	}
//
//	cdKey := "CD_GUILD_MEMBER" + "_" + serviceInfo.ClientIp
//	//  这里填的3的enum：
//	//	enum ENUM_CD_GUILD_MEMBER
//	//	{
//	//		GUILD_MEMBER_CHECK_IN = 1,
//	//		GUILD_MEMBER_DONATE = 2,
//	//		GUILD_SEND_PRESENT = 3,
//	//	};
//	oldVal, err := s.cooldownCli.FetchAndAddGeneralVisit(ctx, uid, cdKey, 3, 2*3600, 1, 2*3600)
//	if err != nil {
//		log.ErrorWithCtx(ctx, "checkValidUserOpt -- FetchAndAddGeneralVisit failed. uid %d err %v key %s", uid, err, cdKey)
//		return false, err
//	}
//	if oldVal >= 3 {
//		log.ErrorWithCtx(ctx, "checkValidUserOpt -- fail. uid_ip limit, uid:%v, strIp:%s, oldValue:%v", uid, serviceInfo.ClientIp, oldVal)
//		return false, protocol.NewExactServerError(nil,status.ErrGuildMemberOptCdIp)
//	}
//
//	cdKey = "CD_GUILD_MEMBER" + "_" + string(serviceInfo.DeviceId) + "_" + time.Now().Format("20060102")
//	oldVal, err = s.cooldownCli.FetchAndAddPeriodVisit(ctx, uid, cdKey, 3, 86400, 1)
//	if err != nil {
//		log.ErrorWithCtx(ctx, "checkValidUserOpt -- FetchAndAddPeriodVisit failed. uid %d err %v key %s", uid, err, cdKey)
//		return false, err
//	}
//	if oldVal >= 1 {
//		log.ErrorWithCtx(ctx, "checkValidUserOpt -- fail. cd_deviceId limit, uid:%v, deviceId:%s, oldValue:%v", uid, serviceInfo.DeviceId, oldVal)
//		return false, protocol.NewExactServerError(nil,status.ErrGuildMemberOptCdDevice)
//	}
//	return true, nil
//}

//func (s *PresentMiddlewareMgr) checkFakeDevice(ctx context.Context, uid uint32) (isValid bool, err error) {
//	info, err := s.antiCli.GetUserLastLoginInfo(ctx, uid, uid)
//	if err != nil {
//		log.ErrorWithCtx(ctx, "checkDeviceFake -- GetUserLastLoginInfo failed. uid %d err %v", uid, err)
//		return false, err
//	}
//	if info.DeviceInfo != "" {
//		// 这部分比较麻烦 先把功能实现再回头写检查吧
//		return false, nil
//	}
//	return false, nil
//}

func (s *PresentMiddlewareMgr) sendPresent(ctx context.Context, sendExtend *baseSendExtend, serviceInfo *pb.ServiceCtrlInfo,
	itemCount, appId, marketId, itemSource, sendSource, batchType, sendMethod uint32, isOptValid bool) (sucOrder map[string]*PayOrderInfo, err error) {
	sucOrder = make(map[string]*PayOrderInfo)

	targetUserMap := sendExtend.targetUserMap
	channelInfo := sendExtend.channelSimpleInfo
	itemCfg := sendExtend.presentConfig
	sendUser := sendExtend.sendUser
	uniqOrderId := sendExtend.uniqOrderId
	timeVal := uint32(sendExtend.nowTs.Unix())
	extendInfo := sendExtend.extendInfo
	payOrderMap := sendExtend.orderMap

	if len(targetUserMap) == 0 {
		return nil, protocol.NewExactServerError(nil, status.ErrUserPresentInvalidTargetUserSize)
	}
	var channelGuildId uint32
	//1 : 公会房  4：公会公开厅（娱乐房）
	if channelInfo.GetChannelSimple().GetChannelType() == 1 || channelInfo.GetChannelSimple().GetChannelType() == 4 {
		channelGuildId = channelInfo.GetChannelSimple().GetBindId()
	}

	baseAddRich := itemCfg.GetItemConfig().GetRichValue() * itemCount * uint32(len(targetUserMap))
	// 如果是t豆，额外计算一下倍率
	if itemCfg.GetItemConfig().GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) {
		extendInfo.realAddRichValue = s.calRichRatio(timeVal, itemCfg.GetItemConfig().ItemId, extendInfo.realAddRichValue)
	}
	baseAddCharm := itemCfg.GetItemConfig().GetCharm() * itemCount
	extendInfo.baseRichValue = baseAddRich
	extendInfo.realAddRichValue = baseAddRich
	extendInfo.sendUserCurrRichValue = uint64(baseAddRich)
	extendInfo.bIsSendUserRichLevelChanged = false
	log.DebugWithCtx(ctx, "sendPresent -- extendInfo uid %d info %v", sendUser.GetUid(), extendInfo)
	//红钻加魅力值财富值。T豆走kafka事件加

	if itemCfg.GetItemConfig().GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_RED_DIAMOND) {
		s.batchAddRedDiamondCharmAndRich(ctx, sendUser.GetUid(), uniqOrderId, channelInfo.GetChannelSimple().GetChannelId(),
			channelGuildId, sendUser.GetCurrentGuildId(), itemCfg.GetItemConfig().PriceType, baseAddRich, baseAddCharm,
			itemCfg.GetItemConfig().ItemId, targetUserMap, extendInfo)
	} else {
		userBonusCardMap := make(map[uint32]*UserBonusCardEffect, 0)
		if itemCfg.GetItemConfig().GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) {
			s.calReceiverBonusCardEffects(ctx, targetUserMap, baseAddCharm, userBonusCardMap)
			realAddRichValue, richValueBonusMsg, _ := s.calUserBonusCardEffect(ctx, *sendUser, uint32(len(targetUserMap)), baseAddRich, true)
			extendInfo.realAddRichValue = realAddRichValue
			extendInfo.richValueBonusMsg = richValueBonusMsg
		}
		for _, user := range targetUserMap {
			finalValue := baseAddCharm
			_, ok := userBonusCardMap[user.GetUid()]
			if ok {
				finalValue = userBonusCardMap[user.GetUid()].finalValue
			}

			extend := &TargetExtendInfo{}
			extend.uid = user.GetUid()
			extend.realAddCharm = finalValue
			extend.baseCharm = baseAddCharm
			extend.recvUserCurrCharmValue = 0
			extend.bIsSendUserRichLevelChanged = false
			extendInfo.targetInfoMap[user.GetUid()] = extend
		}
	}

	log.DebugWithCtx(ctx, "extend info : %v", extendInfo)
	addRichOnce := extendInfo.realAddRichValue / uint32(len(targetUserMap))
	relayChannelId := uint32(0)

	//在官频
	if channelInfo.GetChannelSimple().GetChannelType() == uint32(channel.ChannelType_OFFICIAL_LIVE_CHANNEL_TYPE) {
		resp, err := s.offcialLiveCli.GetRelay(ctx, channelInfo.GetChannelSimple().GetChannelId())
		if err != nil {
			log.ErrorWithCtx(ctx, "sendPresent -- GetRelay fail ,  channelId %d err %v", channelInfo.GetChannelSimple().GetChannelId(), err)
		}
		relayChannelId = resp.GetChannelId()
	}

	wg := sync.WaitGroup{}
	lock := sync.Mutex{}
	sendReq := presentPB.SendPresentReq{
		Uid:              sendUser.GetUid(),
		ItemId:           itemCfg.GetItemConfig().ItemId,
		OrderId:          uniqOrderId,
		ItemConfig:       itemCfg.GetItemConfig(),
		ChannelId:        channelInfo.GetChannelSimple().GetChannelId(),
		ChannelType:      channelInfo.GetChannelSimple().GetChannelType(),
		ChannelName:      channelInfo.GetChannelSimple().GetName(),
		GuildId:          channelGuildId,
		ItemCount:        itemCount,
		SendTime:         timeVal,
		OptInvalid:       !isOptValid,
		UserFromIp:       serviceInfo.GetClientIp(),
		ItemSource:       itemSource,
		AsyncFlag:        true,
		AppId:            appId,
		MarketId:         marketId,
		ChannelDisplayId: channelInfo.GetChannelSimple().GetDisplayId(),
		SendSource:       sendSource,
		SendPlatform:     serviceInfo.GetClientType(),
		BatchType:        batchType,
		AddRich:          addRichOnce,
		BindChannelId:    relayChannelId,
		SendMethod:       sendMethod,
		FromUkwAccount:   sendExtend.ukwInfoMap[sendUser.GetUid()].GetPrivilege().GetAccount(),
		FromUkwNickname:  sendExtend.ukwInfoMap[sendUser.GetUid()].GetPrivilege().GetNickname(),
		GiverGuildId:     sendUser.GetCurrentGuildId(),
	}

	//开协程去同步获取送礼结果
	for _, j := range payOrderMap {
		item := j
		req := sendReq
		req.TargetUid = item.targetUid
		req.OrderId = item.orderId
		req.DealToken = item.dealToken
		req.ToUkwAccount = sendExtend.ukwInfoMap[item.targetUid].GetPrivilege().GetAccount()
		req.ToUkwNickname = sendExtend.ukwInfoMap[item.targetUid].GetPrivilege().GetNickname()
		req.ScoreType = sendExtend.ScoreTypeMap[item.targetUid]
		req.ReceiverGuildId = sendExtend.targetUserMap[item.targetUid].GetCurrentGuildId()

		isVirtualLive := s.checkLiveStatus(ctx, sendExtend.channelSimpleInfo.GetChannelSimple(), relayChannelId)

		req.IsVirtualLive = isVirtualLive

		_, targetOk := extendInfo.targetInfoMap[item.targetUid]
		if targetOk {
			req.AddCharm = extendInfo.targetInfoMap[item.targetUid].realAddCharm
		} else {
			req.AddCharm = 0
		}
		if j.tbTime != "" {
			sendTime, _ := time.ParseInLocation("2006-01-02 15:04:05", j.tbTime, time.Local)
			req.SendTime = uint32(sendTime.Unix())
		}
		wg.Add(1)
		go func() {
			defer func() {
				wg.Done()
				lock.Unlock()
			}()
			err := client.PresentCli.SendPresent(ctx, &req)
			lock.Lock()
			if err == nil {
				sucOrder[req.OrderId] = item
			} else {
				log.ErrorWithCtx(ctx, "PresentMiddlewareMgr -- SendPresent fail , req: %+v", req)
			}
		}()
	}

	wg.Wait()

	if channelInfo.ChannelSimple.GetChannelId() > 0 && len(sucOrder) != 0 {
		req, _ := proto.Marshal(&sendReq)
		_ = client.MissionCli.HandleMission(ctx, sendUser.GetUid(), 1165, req)
	}

	return sucOrder, err
}

func (s *PresentMiddlewareMgr) batchAddRedDiamondCharmAndRich(ctx context.Context, uid uint32, uniqOrderId string, channelID uint32, channelGuildid uint32, sendGuildId uint32,
	priceType uint32, baseAddRichValue uint32, baseAddCharm uint32, giftId uint32, targetUserMap map[uint32]*account.User, extendInfo *ExtendInfo) {
	req := numericGoPB.BatchRecordSendGiftEventReq{}
	req.ChannelGuild = channelGuildid
	req.ChannelId = channelID
	req.PriceType = priceType
	req.OrderId = uniqOrderId
	req.GiftId = giftId
	req.GiverUserInfo = &numericGoPB.UserGiftEventInfo{Uid: uid, GuildId: sendGuildId, AddValue: uint64(baseAddRichValue)}
	if !s.isRecordSenderRich(ctx) {
		req.GiverUserInfo.AddValue = 0
	}
	req.ReceiverUserInfoList = []*numericGoPB.UserGiftEventInfo{}
	for i, j := range targetUserMap {
		userInfo := &numericGoPB.UserGiftEventInfo{}
		userInfo.Uid = i
		userInfo.GuildId = j.GetCurrentGuildId()
		userInfo.AddValue = uint64(baseAddCharm)
		req.ReceiverUserInfoList = append(req.ReceiverUserInfoList, userInfo)
	}
	//resp, err := client.NumericCli.BatchRecordSendGiftEvent(ctx, &req)
	resp, err := client.NumericGoCli.BatchRecordSendGiftEvent(ctx, &req)
	if err != nil {
		log.ErrorWithCtx(ctx, "batchAddRedDiamondCharmAndRich BatchRecordSendGiftEvent. err:%v.  req:%v", err, req)
	} else {
		log.ErrorWithCtx(ctx, "batchAddRedDiamondCharmAndRich resp. resp:%+v", resp)
	}
	// 批量送礼后，送礼者的财富信息
	extendInfo.realAddRichValue = uint32(resp.GetGiverUserInfo().GetAddValue())
	extendInfo.baseRichValue = uint32(resp.GetGiverUserInfo().GetAddValue())

	if _, ok := extendInfo.sendInfoMap[giftId]; ok {
		extendInfo.sendInfoMap[giftId].RichValue = int32(extendInfo.baseRichValue)
	}

	extendInfo.sendUserCurrRichValue = resp.GetGiverUserInfo().GetFinalValue()
	extendInfo.bIsSendUserRichLevelChanged = resp.GetGiverUserInfo().GetLevelChange()

	// 批量送礼后，收礼者的魅力信息
	for _, receiver := range resp.GetReceiverUserInfoList() {
		addValue := uint32(receiver.GetAddValue())
		extend := TargetExtendInfo{}
		extend.uid = receiver.GetUid()
		if addValue > baseAddCharm {
			extend.baseCharm = baseAddCharm
		} else {
			extend.baseCharm = addValue
		}
		extend.realAddCharm = addValue
		extend.recvUserCurrCharmValue = receiver.GetFinalValue()
		extend.bIsSendUserRichLevelChanged = receiver.GetLevelChange()
		extendInfo.targetInfoMutex.Lock()
		extendInfo.targetInfoMap[extend.uid] = &extend
		extendInfo.targetInfoMutex.Unlock()
	}

}

// 不存在复数人送礼的情况，所以请求复数用户的加速卡情况，一定是收礼人
func (s *PresentMiddlewareMgr) calReceiverBonusCardEffects(ctx context.Context, targetUserMap map[uint32]*account.User, addValue uint32,
	userCardEffectMap map[uint32]*UserBonusCardEffect) {
	for uid := range targetUserMap {
		resp, err := s.backpackFuncCli.GetAccelerateCardUsage(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "%s -- GetUserFuncCardUse fail. err:%v targetUserMap size:%d", err, len(targetUserMap))
		}

		userCardEffectMap[uid] = &UserBonusCardEffect{uid: uid, addValue: addValue, finalValue: addValue, bonusMsg: ""}

		if len(resp.GetUserItemList()) == 0 {
			continue
		}

		for _, cardEffect := range resp.GetUserItemList() {
			if cardEffect.CardType != backpack_base.PackageItemType_BACKPACK_CARD_CHARM_ACCELERATOR {
				continue
			}
			if 100 > cardEffect.CardTimes {
				break
			}

			userCardEffectMap[uid].finalValue = userCardEffectMap[uid].finalValue * cardEffect.CardTimes / 100
			if cardEffect.CardType == backpack_base.PackageItemType_BACKPACK_CARD_RICH_ACCELERATOR {
				if !s.isRecordSenderRich(ctx) {
					// 卡片类型是财富卡，且签约用户开启不增加财富值
					break
				}
				userCardEffectMap[uid].bonusMsg = fmt.Sprintf("财富卡%d.%d倍额外加成 +%d", cardEffect.CardTimes/100,
					cardEffect.CardTimes%100/10, userCardEffectMap[uid].finalValue-userCardEffectMap[uid].addValue)
			}

			break
		}
	}
}

func (s *PresentMiddlewareMgr) calUserBonusCardEffect(ctx context.Context, sendUser accountPB.UserResp, userCount uint32, addValue uint32,
	isSend bool) (finalValue uint32, bonusMsg string, cardTimes uint32) {
	var cardType backpack_base.PackageItemType
	cardTimes = 100
	finalValue = addValue
	if isSend {
		cardType = backpack_base.PackageItemType_BACKPACK_CARD_RICH_ACCELERATOR
	} else {
		cardType = backpack_base.PackageItemType_BACKPACK_CARD_CHARM_ACCELERATOR
	}
	resp, err := s.backpackFuncCli.GetAccelerateCardUsage(ctx, sendUser.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "%s -- GetUserFuncCardUse fail. err:%v uid:%d", err, sendUser.GetUid())
	}

	for _, cardEffect := range resp.GetUserItemList() {
		if cardEffect.CardType != cardType {
			continue
		}
		if 100 > cardEffect.CardTimes {
			continue
		}

		finalValue = addValue * cardEffect.CardTimes / 100
		cardTimes = cardEffect.CardTimes
		if cardEffect.CardType == backpack_base.PackageItemType_BACKPACK_CARD_RICH_ACCELERATOR {
			if !s.isRecordSenderRich(ctx) {
				// 卡片类型是财富卡，且签约用户开启不增加财富值
				break
			}
			if userCount >= 1 {
				perUserBonusValue := (finalValue - addValue) / userCount
				bonusMsg = fmt.Sprintf("财富卡%d.%d倍额外加成 +%d", cardEffect.CardTimes/100,
					cardEffect.CardTimes%100/10, perUserBonusValue)
				break
			} else {
				continue
			}
		}
	}
	return finalValue, bonusMsg, cardTimes
}

func (s *PresentMiddlewareMgr) notifyClient(ctx context.Context, uidList []uint32, syncType uint32) (err error) {

	var b [4]byte
	binary.BigEndian.PutUint32(b[:], uint32(syncType)) // Network Order
	sequence := uint32(time.Now().UnixNano())
	seq := atomic.AddUint32(&sequence, 1)
	err = s.pushCli.PushToUsers(ctx, uidList, &pushPB.CompositiveNotification{
		Sequence:           seq,
		TerminalTypePolicy: pushclient.DefaultPolicy,
		AppId:              0,
		ProxyNotification: &pushPB.ProxyNotification{
			Type:    uint32(pushPB.ProxyNotification_NOTIFY),
			Payload: b[:],
		},
	})

	if err != nil {
		log.DebugWithCtx(ctx, "NotifySyncX - users=%v(%d) type=%v seq=%d err=%s", uidList, len(uidList), syncType, seq, err.Error())
	} else {
		log.DebugWithCtx(ctx, "NotifySyncX - users=%v(%d) type=%v seq=%d", uidList, len(uidList), syncType, seq)
	}

	return err

}

func (s *PresentMiddlewareMgr) commitPayOrder(ctx context.Context, payOrderListmap map[string]*PayOrderInfo,
	sendMethod uint32, presentSource uint32, priceType uint32) (err error) {
	appId := "TT_HZ"
	if sendMethod == uint32(pb.PresentSendMethodType_PRESENT_TYPE_FELLOW) {
		appId = "TT_ZY"
	}

	payType := uint32(0)
	if presentSource == uint32(pb.PresentSourceType_PRESENT_SOURCE_PACKAGE) {
		payType = uint32(pay.PayType_Backpack)
	} else {
		if priceType == uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) {
			payType = uint32(pay.PayType_Tbean_Old)
		} else {
			payType = uint32(pay.PayType_Rediamond)
		}
	}

	// 旧的支付不需要填freezeUser
	commitReq := &pay.CommitReq{
		AppId: appId,
	}

	commitReq.OrderDetailList = make(map[string]*pay.OrderDetail)
	for _, order := range payOrderListmap {

		commitReq.OrderDetailList[order.orderId] = &pay.OrderDetail{
			OrderId:         order.orderId,
			PayOrderId:      order.payOrderId,
			TbeanSystemTime: order.tbTime,
			DealToken:       order.dealToken,
			Err:             nil,
		}
	}

	resp, err := s.PayFactory.GetPayer(payType).Commit(ctx, commitReq)

	// 由于分了订单，所以还要检查每个订单的成功失败，这里就只有一笔，有报错就直接返回错误
	for _, item := range resp.OrderResp {
		if item.Err != nil {
			return item.Err
		}
	}

	return err
}

//
//func (s *PresentMiddlewareMgr) ossReport(ctx context.Context, sendUser *accountPB.UserResp, targetUserMap map[uint32]*account.User,
//	channelInfo *channelPB.GetChannelSimpleInfoResp, timeVal time.Time, itemCfg *presentPB.GetPresentConfigByIdResp, itemCount uint32,
//	isOptValid bool, itemSource uint32, batchType uint32, uniqOrderId string, sendSource uint32, clientType uint32) {
//	channelOwnerNickname := ""
//	channelOwnerAlias := ""
//	//获取创建者的昵称
//	if sendUser.GetUid() == channelInfo.GetChannelSimple().GetCreaterUid() {
//		channelOwnerNickname = sendUser.Nickname
//		channelOwnerAlias = sendUser.Alias
//	} else {
//		user, err := s.accountCli.GetUser(ctx, channelInfo.GetChannelSimple().GetCreaterUid())
//		if err != nil {
//			log.DebugWithCtx(ctx, "ossReport - GetUser fail uid:%v , err : %v", channelInfo.GetChannelSimple().GetCreaterUid(), err)
//		} else {
//			channelOwnerNickname = user.Nickname
//			channelOwnerAlias = user.Alias
//		}
//
//	}
//
//	//获取短号
//	channelGuildId := uint32(0)
//	channelGuildName := ""
//	channelGuildShortId := uint32(0)
//	if channelInfo.GetChannelSimple().GetChannelType() == 1 || channelInfo.GetChannelSimple().GetChannelType() == 4 {
//		channelGuild, err := s.guildCli.GetGuild(ctx, channelInfo.GetChannelSimple().GetBindId())
//		if err != nil {
//			log.DebugWithCtx(ctx, "ossReport - GetUser fail uid:%v , err : %v", channelInfo.GetChannelSimple().GetCreaterUid(), err)
//		} else {
//			channelGuildId = channelGuild.GetGuildId()
//			channelGuildShortId = channelGuild.GetShortId()
//			channelGuildName = channelGuild.GetName()
//		}
//	}
//
//	sendUserGuildName := ""
//	sendUserGuildId := uint32(0)
//	sendUserGuildShortId := uint32(0)
//	if sendUser.CurrentGuildId == channelGuildId {
//		sendUserGuildName = channelGuildName
//		sendUserGuildShortId = channelGuildShortId
//		sendUserGuildId = channelGuildId
//	} else {
//		senderGuild, err := s.guildCli.GetGuild(ctx, sendUser.CurrentGuildId)
//		if err != nil {
//			log.DebugWithCtx(ctx, "ossReport - GetUser fail uid:%v , err : %v", channelInfo.GetChannelSimple().GetCreaterUid(), err)
//		} else {
//			sendUserGuildName = senderGuild.GetName()
//			sendUserGuildShortId = senderGuild.GetShortId()
//			sendUserGuildId = senderGuild.GetGuildId()
//		}
//	}
//
//	eventTime := timeVal.Format("2006-01-02 15:04:05")
//	isOptValidNum := 0
//	if isOptValid {
//		isOptValidNum = 1
//	}
//	for _, target := range targetUserMap {
//		datacenter.StdReportKV(ctx, "************", map[string]interface{}{
//			"totalDate":              time.Now().Format("2006-01-02 15:04:05"),                           // 日期
//			"send_uid":               strconv.Itoa(int(sendUser.GetUid())),                                    // 送礼人
//			"target_uid":             strconv.Itoa(int(target.Uid)),                                      // 收礼人
//			"item_id":                strconv.Itoa(int(itemCfg.GetItemConfig().GetItemId())),             // 礼物id
//			"item_count":             strconv.Itoa(int(itemCount)),                                       // 数量
//			"price":                  strconv.Itoa(int(itemCfg.GetItemConfig().GetPrice())),              // 价格
//			"price_type":             strconv.Itoa(int(itemCfg.GetItemConfig().GetPriceType())),          // 红钻/t豆
//			"channel_id":             strconv.Itoa(int(channelInfo.GetChannelSimple().GetChannelId())),   // 送礼房间
//			"channel_bind_type":      strconv.Itoa(int(channelInfo.GetChannelSimple().GetChannelType())), // 房间类型
//			"channel_bind_id":        strconv.Itoa(int(channelInfo.GetChannelSimple().GetBindId())),      // 公会id
//			"channel_display_id":     strconv.Itoa(int(channelInfo.GetChannelSimple().GetDisplayId())),   // 房间显示id
//			"mic_mode":               strconv.Itoa(int(0)),                                               // 麦位模式
//			"is_opt_valid":           strconv.Itoa(int(isOptValidNum)),                                   // 操作是否有效
//			"send_user_reg_ts":       sendUser.GetRegisteredAt(),                                         // 送礼人注册时间
//			"channel_name":           channelInfo.GetChannelSimple().GetName(),                           // 房间名称
//			"channel_guild_short_id": strconv.Itoa(int(channelGuildShortId)),                             // 公会短id
//			"channel_creator_uid":    strconv.Itoa(int(channelInfo.GetChannelSimple().GetCreaterUid())),  // 创建者id
//			"channel_creator_name":   channelOwnerNickname,                                               // 创建者昵称
//			"send_user_guild_uid":    strconv.Itoa(int(sendUserGuildId)),                                 // 送礼人公会
//			"send_user_guild_name":   sendUserGuildName,                                                  // 送礼人公会名称
//			"send_user_nickNmae":     sendUser.GetNickname(),                                             // 送礼人昵称
//			"c_g_name":               channelGuildName,                                                   // 房间公会名
//			"suName":                 sendUser.GetAlias(),                                                // ttid
//			"sugs_id":                strconv.Itoa(int(sendUserGuildShortId)),                            // 房间公会短号
//			"cc_Name":                channelOwnerAlias,                                                  // 创建者ttid
//			"targetName":             target.Alias,                                                       // 收礼人ttid
//			"send_source":            strconv.Itoa(int(sendSource)),                                      // 公会短id
//			"platform":               strconv.Itoa(int(clientType)),                                      // 平台类型
//			"item_source":            strconv.Itoa(int(itemSource)),                                      // 礼物来源
//			"batch_type":             strconv.Itoa(int(batchType)),                                       //批量类型
//			"order_id":               uniqOrderId,                                                        //订单id
//			"eventTime":              eventTime,                                                          //事件发生时间（外部）
//		})
//	}
//	return
//}

//func (s *PresentMiddlewareMgr) kakfaPro(ctx context.Context, uid uint32, payOrderLlistmap map[uint32]*PayOrderInfo,
//	sucUser map[uint32]*account.User) (err error) {
//	for _, user := range sucUser {
//		order, ok := payOrderLlistmap[user.GetUid()]
//		if !ok {
//			continue
//		}
//		err := s.unifyPayCli.Commit(ctx, "TT_HZ", order.orderId, order.payOrderId, "PresentBatchSendService::__CommitPayOrder", false)
//		if err != nil {
//			log.ErrorWithCtx(ctx, "commitPayOrder -- unified_pay::Client::Default()->Commit() failed. uid:%v target_uid:%v order_id:%s, pay_order_id:%s, ret %v",
//				uid, order.targetUid, order.orderId, order.payOrderId, err)
//		}
//	}
//	return err
//}

func (s *PresentMiddlewareMgr) getPresentEffect(ctx context.Context, itemCfg *presentPB.StPresentItemConfig, itemCount uint32) (
	showEffect uint32, showEffectV2 uint32, flowId uint32) {
	showEffect = itemCfg.GetExtend().GetShowEffect()
	showEffectV2 = showEffect
	flowId = itemCfg.GetExtend().GetFlowId()
	if showEffect > 0 {
		return showEffect, showEffectV2, flowId
	}
	totalPrice := itemCfg.GetPrice() * itemCount
	//这里的enum见ga_base.proto , PRESENT_SHOW_EFFECT_TYPE
	if totalPrice >= 100000 {
		showEffect = 3
	} else if totalPrice >= 10000 {
		showEffect = 2
	} else {
		showEffect = 0
	}
	//有流光，用配置的
	//ga_base.proto PRESENT_SHOW_EFFECT_TYPE_V2
	if flowId > 0 {
		showEffectV2 = uint32(ga.PRESENT_SHOW_EFFECT_TYPE_V2_PRESENT_SHOW_EFFECT_V2_FLOW)
		return showEffect, showEffectV2, flowId
	}

	if totalPrice >= 100000 {
		flowId = 2
	} else if totalPrice >= 10000 {
		flowId = 1
	}
	if flowId > 0 || (showEffect == 2 || showEffect == 3) {
		showEffectV2 = uint32(ga.PRESENT_SHOW_EFFECT_TYPE_V2_PRESENT_SHOW_EFFECT_V2_FLOW)
	}

	return showEffect, showEffectV2, flowId
}

func (s *PresentMiddlewareMgr) getPresentDynamicEffectTemplate(ctx context.Context, uid uint32, itemCfg *presentPB.StPresentItemConfig, itemCount uint32) (
	templateId uint32, err error) {
	if itemCfg.GetExtend().GetUnshowBatchOption() {
		return
	}

	tmpCtx, cancel := NewContextWithInfoTimeout(ctx, 200*time.Millisecond)
	defer cancel()

	resp, err := client.PresentCli.GetPresentDETConfigById(tmpCtx, itemCfg.GetItemId())
	if err != nil {
		log.ErrorWithCtx(tmpCtx, "PresentBatchSendService::GetPresentDETConfigById failed. ret:%v, uid:%v", err, uid)
		return templateId, err
	}
	for _, effect := range resp.GetPresentEffectList() {
		if effect.PresentCnt == itemCount {
			templateId = effect.TemplateId
			return templateId, err
		}
	}
	return templateId, err
}

func (s *PresentMiddlewareMgr) preSendPresent(ctx context.Context, nowTs time.Time, sendUid uint32, targetUidList []uint32, itemId uint32, channelId uint32, sendMethod uint32, sendExtend *baseSendExtend) (err error) {
	strMicroTime := s.getStrMicroTime(nowTs)

	wg := sync.WaitGroup{}

	_, err = checkOldFakeUid(ctx, targetUidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "preSendPresent checkOldFakeUid err : %v, uid:%d", err, sendUid)
		return err
	}

	// 首先要对targetUidList做一次转换
	fake2RealUidMap, real2FakeUidMap, realUidList, err := getRealUidByFakeUid(ctx, append(targetUidList, sendUid))
	if err != nil {
		log.ErrorWithCtx(ctx, "preSendPresent getRealUidByFakeUid err : %v, uid:%d", err, sendUid)
		return err
	}
	sendExtend.fake2RealUidMap = fake2RealUidMap
	sendExtend.real2FakeUidMap = real2FakeUidMap

	sendUid = fake2RealUidMap[sendUid]

	//送礼人用户信息
	wg.Add(3)
	go func() {
		defer wg.Done()
		var sErr error
		sendExtend.targetUserMap, sErr = s.accountCli.GetUsersMap(ctx, realUidList)
		if sErr != nil {
			err = sErr
			return
		}
	}()

	ukwInfoMap := make(map[uint32]*ga.UserProfile)
	//用户神秘人信息
	go func() {
		defer wg.Done()

		var sErr error
		ukwInfoMap, sErr = client.UserProfileCli.BatchGetUserProfileV2(ctx, realUidList, true)
		if sErr != nil {
			log.ErrorWithCtx(ctx, "preSendPresent GetUsersMap err : %v, uid:%d", sErr, sendUid)
			err = sErr
		}

	}()

	//礼物信息
	go func() {
		defer wg.Done()
		var sErr error
		sendExtend.presentConfig, sErr = client.PresentCli.GetPresentConfigById(ctx, itemId)
		if sErr != nil {
			err = sErr
			return
		}
	}()

	if channelId != 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			channelInfo, sErr := s.channelCli.GetChannelSimpleInfo(ctx, sendUid, channelId)
			if sErr != nil {
				err = sErr
				return
			}
			sendExtend.channelSimpleInfo = &channelPB.GetChannelSimpleInfoResp{ChannelSimple: channelInfo}
		}()
	} else {
		sendExtend.channelSimpleInfo = &channelPB.GetChannelSimpleInfoResp{ChannelSimple: &channelPB.ChannelSimpleInfo{}}
	}

	wg.Wait()

	if err != nil {
		return err
	}

	if isPgc(sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelType()) {
		sendExtend.ukwInfoMap = ukwInfoMap
	} else {
		sendExtend.ukwInfoMap = FillUgcUserProfile(sendExtend.targetUserMap)
	}

	if sendExtend.presentConfig.GetItemConfig().GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) &&
		s.GeneralConfig.GetPresentConfig().ScoreCheckBegin < time.Now().Unix() {
		sendExtend.ScoreTypeMap, err = s.ScoreTypeMgr.GetScoreTypeMap(ctx, itemId, sendUid, realUidList)
		if err != nil {
			log.ErrorWithCtx(ctx, "preSendPresent - FillScoreType fail , uid: %+v", sendUid)
			return err
		}
		log.DebugWithCtx(ctx, "scoreTypeMap %v", sendExtend.ScoreTypeMap)
	} else {
		sendExtend.ScoreTypeMap = make(map[uint32]uint32)
		for _, item := range realUidList {
			sendExtend.ScoreTypeMap[item] = 1
		}
	}

	sendExtend.sendUser = sendExtend.targetUserMap[sendUid]
	delete(sendExtend.targetUserMap, sendUid)

	//order_id
	sendExtend.uniqOrderId = s.createOrderId(0, sendUid, 11, 0, strMicroTime)
	if sendMethod == uint32(pb.PresentSendMethodType_PRESENT_TYPE_FELLOW) {
		sendExtend.uniqOrderId = "fellow_" + sendExtend.uniqOrderId
	}

	sendExtend.orderMap = make(map[string]*PayOrderInfo, 0)
	for _, j := range realUidList {
		if j == sendUid {
			continue
		}
		orderId := fmt.Sprintf("%s_%v", sendExtend.uniqOrderId, j)
		sendExtend.orderMap[orderId] = &PayOrderInfo{targetUid: j, orderId: orderId}
	}

	return
}

func (s *PresentMiddlewareMgr) FillRespAndPush(ctx context.Context, sendExtend *baseSendExtend, in *pb.SendPresentReq, out *pb.SendPresentResp) {
	remainSource := sendExtend.remainSource
	remainTbeans := sendExtend.remainTbeans
	extendInfo := sendExtend.extendInfo

	out.SourceRemain = remainSource
	out.CurTbeans = remainTbeans
	out.SourceId = in.GetBackpackItemId()
	out.ExpireTime = sendExtend.expireTime

	out.MsgInfo = make([]*pb.PresentSendMsg, 0)

	// 把推送格式弄出来

	isShowSurprise := false
	sendTime := uint64(time.Now().UnixNano() / 1000000)

	tmpCustomText := make(map[uint32]string)
	if in.GetPushInfo().GetChannelPushType() == uint32(pb.PushInfo_Channel_ALLMIC) {
		itemMap := make(map[uint32]uint32)
		targetMap := make(map[uint32][]uint32)
		for _, item := range sendExtend.sucOrders {
			itemMap[item.giftId] += item.count
			if list, ok := targetMap[item.giftId]; ok {
				targetMap[item.giftId] = append(list, item.targetUid)
			} else {
				targetMap[item.giftId] = []uint32{item.targetUid}
			}
		}

		for itemId, count := range itemMap {
			extendJson, _ := json.Marshal(sendExtend.extendInfo.sendInfoMap[itemId])
			msgInfo := s.genMsgInfo(ctx, sendExtend, itemId, count/uint32(len(targetMap[itemId])), in.SendType, in.GetPushInfo().GetChannelPushType(), sendTime, string(extendJson), in.DrawPresentPic)
			targetList := make([]*pb.PresentBatchTargetInfo, 0)
			targetUidList := make([]uint32, 0)

			for _, uid := range targetMap[itemId] {
				//  组织 extendJson
				target := &pb.PresentBatchTargetInfo{
					Uid:        sendExtend.targetUserMap[uid].GetUid(),
					Account:    sendExtend.targetUserMap[uid].GetUsername(),
					Nickname:   sendExtend.targetUserMap[uid].GetNickname(),
					ExtendJson: sendExtend.extendInfo.targetInfoMap[uid].userExtendJson,
				}

				fillBatchTargetUserProfileResp(target, sendExtend)
				target.CustomText = decoration.GetCustomPresentText(sendExtend.presentConfigMap[itemId],
					sendExtend.ukwInfoMap[sendExtend.sendUser.GetUid()], sendExtend.ukwInfoMap[uid])
				tmpCustomText[uid] = target.CustomText

				targetList = append(targetList, target)

				targetUidList = append(targetUidList, sendExtend.targetUserMap[uid].GetUid())
			}

			if in.GetSurpriseEffectCount() > 0 {
				if count/in.GetSurpriseEffectCount() > 0 {
					msgInfo.ItemInfo.IsShowSurprise = true
					isShowSurprise = true
					msgInfo.ItemInfo.SurpriseCount = 1

					// 如果是全麦触发彩蛋 在这里发kafka
					_ = s.SendLevelupPresentSurpriseEvent(ctx, sendExtend.sendUser.GetUid(), itemId, 1,
						targetUidList)
				}
			}

			extendJson, _ = json.Marshal(sendExtend.extendInfo.sendInfoMap[itemId])

			msg := &pb.PresentBatchInfoMsg{
				ItemId:         itemId,
				TotalItemCount: count,
				BatchType:      in.BatchType,
				SendTime:       sendTime,
				ChannelId:      in.ChannelId,
				SendUid:        sendExtend.sendUser.GetUid(),
				SendAccount:    sendExtend.sendUser.GetUsername(),
				SendNickname:   sendExtend.sendUser.GetNickname(),
				ExtendJson:     string(extendJson),
				TargetList:     targetList,
				ItemInfo:       msgInfo.ItemInfo,
				IsMulti:        in.GetIsMulti(),
			}

			fillAllmicUserProfile(msg, sendExtend)

			out.BatchInfo = append(out.BatchInfo, msg)
		}

	}

	for _, item := range sendExtend.sucOrders {

		extendJson, _ := json.Marshal(sendExtend.extendInfo.sendInfoMap[item.giftId])
		msgInfo := s.genMsgInfo(ctx, sendExtend, item.giftId, item.count, in.SendType, in.GetPushInfo().GetChannelPushType(), sendTime, string(extendJson), in.DrawPresentPic)

		msgInfo.TargetUid = sendExtend.targetUserMap[item.targetUid].GetUid()
		msgInfo.TargetAccount = sendExtend.targetUserMap[item.targetUid].GetUsername()
		msgInfo.TargetNickname = sendExtend.targetUserMap[item.targetUid].GetNickname()

		msgInfo.SendUid = sendExtend.sendUser.GetUid()
		msgInfo.SendAccount = sendExtend.sendUser.GetUsername()
		msgInfo.SendNickname = sendExtend.sendUser.GetNickname()

		text, ok := tmpCustomText[item.targetUid]
		if ok {
			msgInfo.ItemInfo.CustomTextJson = text
		} else {
			msgInfo.ItemInfo.CustomTextJson = decoration.GetCustomPresentText(sendExtend.presentConfigMap[item.giftId],
				sendExtend.ukwInfoMap[sendExtend.sendUser.GetUid()], sendExtend.ukwInfoMap[item.targetUid])
		}

		msgInfo.FromUserProfile = genUserProfile(sendExtend.ukwInfoMap[msgInfo.SendUid])
		if sendExtend.ukwInfoMap[msgInfo.SendUid].GetPrivilege() != nil {
			msgInfo.SendNickname = sendExtend.ukwInfoMap[msgInfo.GetSendUid()].GetPrivilege().GetNickname()
			msgInfo.SendAccount = sendExtend.ukwInfoMap[msgInfo.GetSendUid()].GetPrivilege().GetAccount()
			msgInfo.SendUid = sendExtend.ukwInfoMap[msgInfo.GetSendUid()].GetUid()
		}

		msgInfo.TargetUserProfile = genUserProfile(sendExtend.ukwInfoMap[msgInfo.TargetUid])
		if sendExtend.ukwInfoMap[msgInfo.TargetUid].GetPrivilege() != nil {
			msgInfo.TargetNickname = sendExtend.ukwInfoMap[msgInfo.GetTargetUid()].GetPrivilege().GetNickname()
			msgInfo.TargetAccount = sendExtend.ukwInfoMap[msgInfo.GetTargetUid()].GetPrivilege().GetAccount()
			msgInfo.TargetUid = sendExtend.ukwInfoMap[msgInfo.GetTargetUid()].GetUid()
		}

		if in.GetSendChannelId() != 0 {
			msgInfo.OnlyShowMessage = in.GetSendChannelId() != in.GetChannelId()
		}

		if in.GetSurpriseEffectCount() > 0 {
			if item.count/in.GetSurpriseEffectCount() > 0 {
				msgInfo.ItemInfo.IsShowSurprise = true
				if in.GetBatchType() == uint32(pb.PresentBatchSendType_PRESENT_SOURCE_NONE) {
					msgInfo.ItemInfo.SurpriseCount = 1
					// 如果是单送触发彩蛋，在这里发kafka
					if item.count/in.GetSurpriseEffectCount() > 0 {
						_ = s.SendLevelupPresentSurpriseEvent(ctx, sendExtend.sendUser.GetUid(), item.giftId, 1,
							[]uint32{sendExtend.targetUserMap[item.targetUid].GetUid()})
					}
				}
			}

			if isShowSurprise {
				msgInfo.ItemInfo.IsShowSurprise = true
			}
		}

		out.MsgInfo = append(out.MsgInfo, msgInfo)
		out.OrderInfo = append(out.OrderInfo, &pb.OrderInfo{OrderId: item.orderId, Uid: item.targetUid})
	}

	if in.GetWithPush() {
		s.procPushEvent(ctx, sendExtend, in, out)
	}

	log.DebugWithCtx(ctx, "extend info : %v", extendInfo)
}

func (s *PresentMiddlewareMgr) SetHighConsume(ctx context.Context, sucUsers map[uint32]uint32, sendUid uint32) (err error) {
	//【防小白骚扰】24小时内单笔送出/收到1000元礼物的用户 存redis
	totalSpend := uint32(0)
	for user, value := range sucUsers {
		totalSpend += value

		if value >= 100000 {
			_, err := s.imstrangerCli.SetHighConsumeIn24Hour(ctx, user)
			if err != nil {
				log.ErrorWithCtx(ctx, "AfterSendPresent -- SetHighConsumeIn24Hour fail:%v. uid:%d", err, user)
			}
		}
	}

	if totalSpend >= 100000 {
		_, err := s.imstrangerCli.SetHighConsumeIn24Hour(ctx, sendUid)
		if err != nil {
			log.ErrorWithCtx(ctx, "AfterSendPresent -- SetHighConsumeIn24Hour fail:%v. uid:%d", err, sendUid)
		}
	}

	return
}

func (s *PresentMiddlewareMgr) genMsgInfo(ctx context.Context, sendExtend *baseSendExtend, giftId, count, sendType,
	channelPushType uint32, sendTime uint64, extendJson string, drawPresentPic *pb.DrawPresentPicture) *pb.PresentSendMsg {
	//涂鸦送礼
	tmpMsgInfo := &pb.PresentSendMsg{ItemInfo: &pb.PresentSendItemInfo{}}
	presentConfig := sendExtend.presentConfigMap[giftId]

	//送礼特效与动效

	showEffect, showEffectV2, FlowId := s.getPresentEffect(ctx, presentConfig, count)
	templateId, _ := s.getPresentDynamicEffectTemplate(ctx, sendExtend.sendUser.GetUid(), presentConfig, count)

	tmpMsgInfo.ItemInfo.ItemId = presentConfig.GetItemId()
	tmpMsgInfo.ItemInfo.Count = count
	tmpMsgInfo.ItemInfo.ShowEffect = showEffect
	tmpMsgInfo.ItemInfo.ShowEffectV2 = showEffectV2
	tmpMsgInfo.ItemInfo.FlowId = FlowId
	tmpMsgInfo.ItemInfo.IsBatch = false
	tmpMsgInfo.ItemInfo.SendType = sendType
	tmpMsgInfo.ItemInfo.ShowBatchEffect = false
	tmpMsgInfo.ItemInfo.DynamicTemplateId = templateId

	tmpMsgInfo.ChannelId = sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelId()
	tmpMsgInfo.SendTime = sendTime
	tmpMsgInfo.SendUid = sendExtend.sendUser.GetUid()
	tmpMsgInfo.SendNickname = sendExtend.sendUser.GetNickname()
	tmpMsgInfo.SendAccount = sendExtend.sendUser.GetUsername()
	tmpMsgInfo.ExtendJson = extendJson
	if presentConfig.GetExtend().FlowId > 0 {
		tmpMsgInfo.ItemInfo.DynamicTemplateId = 0
	}

	// 目前只有全麦能正常显示多目标送礼的动画
	if channelPushType == uint32(pb.PushInfo_Channel_ALLMIC) {
		tmpMsgInfo.ItemInfo.IsBatch = true
		tmpMsgInfo.ItemInfo.ShowBatchEffect = true
	}

	if presentConfig.GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) &&
		(presentConfig.GetExtend().GetShowEffect() == 1 || presentConfig.GetPrice() >= 10000) {
		tmpMsgInfo.ItemInfo.ShowBatchEffect = false
	}

	if sendType == uint32(pb.PresentSendType_PRESENT_SEND_DRAW) && presentConfig.GetExtend().GetTag() == uint32(presentPB_.PresentSendSourceType_E_SEND_SOURCE_DRAW_GIFT) {
		tmpMsgInfo.ItemInfo.SendType = sendType
		tmpMsgInfo.ItemInfo.DrawPresentPic = drawPresentPic
		tmpMsgInfo.ItemInfo.ShowBatchEffect = false
	}

	return tmpMsgInfo
}

func (s *PresentMiddlewareMgr) genPushType(userIn, channelIn, imIn uint32) (userPush, channelPush, imPush uint32) {
	if userIn == uint32(pb.PushInfo_Person_NORMAL) {
		userPush = uint32(pushPb.PushMessage_PRESENT_MSG)
	} else if userIn == uint32(pb.PushInfo_Person_NORMAL_SENDER) {
		userPush = uint32(pushPb.PushMessage_PRESENT_MSG)
	} else {
		userPush = uint32(0)
	}

	if channelIn == uint32(pb.PushInfo_Channel_NORMAL) || channelIn == uint32(pb.PushInfo_Channel_NORMAL_ALL) {
		channelPush = uint32(channel.ChannelMsgType_CHANNEL_PRESENT_MSG)
	} else if channelIn == uint32(pb.PushInfo_Channel_ALLMIC) {
		channelPush = uint32(channel.ChannelMsgType_CHANNEL_BATCH_SEND_PRESENT_NOTIFY)
	} else {
		channelPush = uint32(0)
	}

	if imIn == uint32(pb.PushInfo_IM_NORMAL) {
		imPush = uint32(imPb.IM_MSG_TYPE_IM_PRESENT_NEW)
	}

	return
}

func (s *PresentMiddlewareMgr) SendLevelupPresentSurpriseEvent(ctx context.Context, sendUid, itemId, count uint32, targetUids []uint32) error {
	changeInfo := &kafkaLevel.LevelupPresentSurpriseEvent{
		SendUid:      sendUid,
		PresentId:    itemId,
		TargetUid:    targetUids,
		SupriseCount: count,
	}

	msg, _ := proto.Marshal(changeInfo)
	s.levelupKfkProducer.Produce("levelup_surprise", fmt.Sprintf("%d", sendUid), msg)
	log.InfoWithCtx(ctx, "SendLevelupPresentSurpriseEvent %v ", changeInfo)
	return nil
}

func (s *PresentMiddlewareMgr) NeedAvoidMaskedPk(ctx context.Context, uid, channelId, channelType uint32) bool {
	if !s.GeneralConfig.AvoidPkNews {
		return false
	}

	if channelType == uint32(channel.ChannelType_RADIO_LIVE_CHANNEL_TYPE) {
		resp, err := client.LiveMaskedCli.GetLiveChannelMaskedPKStatus(ctx, uid, channelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckMaskedPk fail , err %v , uid %d , channelId %d", err, uid, channelId)
			return false
		}

		if resp == uint32(masked_pk_live.ChannelMaskedPKStatus_InPKing) {
			log.ErrorWithCtx(ctx, "CheckMaskedPk avoid pk ,uid %d , channelId %d ", uid, channelId)
			return true
		}
	} else {
		resp, err := client.MaskedCli.GetChannelMaskedPKStatus(ctx, uid, channelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckMaskedPk fail , err %v , uid %d , channelId %d", err, uid, channelId)
			return false
		}

		if resp == uint32(masked_pk_svr.ChannelMaskedPKStatus_InPKing) {
			log.ErrorWithCtx(ctx, "CheckMaskedPk avoid pk ,uid %d , channelId %d ", uid, channelId)
			return true
		}
	}

	return false
}

func UpdateDealTokenInfo(ctx context.Context, dealToken string, orderId string, isBatch bool) (string, error) {
	dealTokenContent, err := deal_token.Decode(dealToken)
	if err != nil {
		log.ErrorWithCtx(ctx, "deal_token.Decode fail , err %v , order_id %s", err, orderId)
		return dealToken, err
	}

	if isBatch {
		tbeanDealTokenContent, err := deal_token.Decode(dealTokenContent.PrevToken)
		if err != nil {
			log.ErrorWithCtx(ctx, "deal_token.Decode fail , err %v , order_id %s", err, orderId)
			return dealToken, err
		}
		tbeanDealTokenContent.OrderID = orderId
		newTbeanDealToken, _ := deal_token.Encode(tbeanDealTokenContent)
		dealTokenContent.PrevToken = newTbeanDealToken
	}

	newDt := deal_token.NewDealTokenData(dealTokenContent.TradeNo, orderId, "present-middleware", dealTokenContent.BuyerID, dealTokenContent.TotalPrice)
	newDealToken, err := deal_token.AddDealToken(dealToken, newDt)
	if err != nil {
		log.ErrorWithCtx(ctx, "deal_token.AddDealToken fail , err %v , order_id %s", err, orderId)
		return dealToken, err
	}

	return newDealToken, nil
}

// CheckDealToken todo 加日志
func CheckDealToken(dealToken string, orderId string, uid uint32, withTbean bool, withRiskControl bool) (bool, error) {
	dealTokenContent, err := deal_token.Decode(dealToken)
	if err != nil {
		log.Errorf("deal_token.Decode fail , err %v , order_id %s", err, orderId)
		return false, err
	}

	hasRiskControl := true
	if withRiskControl {
		hasRiskControl = false
	}

	hasTbean := true
	if withTbean {
		hasTbean = false
	}

	for dealTokenContent != nil {
		if dealTokenContent.ServerName == "tbean" {
			hasTbean = true
		}
		if dealTokenContent.ServerName == "prob-game-center" {
			hasRiskControl = true
		}

		if dealTokenContent.OrderID != orderId {
			log.Errorf("deal_token check fail , deal_token %s , order_id %s", dealTokenContent, orderId)
			//return false, nil
		}

		if dealTokenContent.BuyerID != int64(uid) {
			log.Errorf("deal_token check fail , deal_token %s , uid %s", dealTokenContent, uid)
			return false, nil
		}

		dealTokenContent, err = deal_token.Decode(dealTokenContent.PrevToken)
		if err != nil {
			dealTokenContent = nil
		}
	}

	if !hasTbean {
		log.Errorf("deal_token check fail , don‘t have tbean info, deal_token %s ", dealTokenContent)
		return false, nil
	}

	if !hasRiskControl {
		log.Errorf("deal_token check fail , don‘t have risk control info , deal_token %s", dealTokenContent)
		return false, nil
	}

	return true, nil
}

func isPgc(channelType uint32) bool {
	if channelType == uint32(channel.ChannelType_RADIO_LIVE_CHANNEL_TYPE) ||
		channelType == uint32(channel.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) ||
		channelType == uint32(channel.ChannelType_OFFICIAL_LIVE_CHANNEL_TYPE) {
		return true
	}
	return false
}

func genUserProfile(profile *ga.UserProfile) *pb.UserProfile {
	if profile == nil {
		return nil
	}
	resp := &pb.UserProfile{
		Uid:          profile.GetUid(),
		Account:      profile.GetAccount(),
		Nickname:     profile.GetNickname(),
		AccountAlias: profile.GetAccountAlias(),
		Sex:          profile.GetSex(),
	}

	if profile.GetPrivilege() != nil {
		resp.Privilege = &pb.UserPrivilege{
			Account:  profile.GetPrivilege().GetAccount(),
			Nickname: profile.GetPrivilege().GetNickname(),
			Type:     profile.GetPrivilege().GetType(),
			Options:  profile.GetPrivilege().GetOptions(),
		}
	}

	return resp
}

//func fillUserProfileByType(profile *ga.UserProfile, user *accountPB.UserResp, channelType uint32) *ga.UserProfile {
//	up := &ga.UserProfile{
//		Uid:          user.GetUid(),
//		Account:      user.GetUsername(),
//		Nickname:     user.GetNickname(),
//		AccountAlias: user.GetAlias(),
//		Sex:          uint32(user.GetSex()),
//	}
//
//	if isPgc(channelType) && profile != nil {
//		up = profile
//	}
//
//	return up
//}

func getRealUidByFakeUid(ctx context.Context, uid []uint32) (fake2RealUidMap, real2FakeUidMap map[uint32]uint32,
	realUidList []uint32, err error) {
	fake2RealUidMap = make(map[uint32]uint32)
	real2FakeUidMap = make(map[uint32]uint32)

	realUidList = make([]uint32, 0)
	resp, err := client.UkwCli.BatchGetTrueUidByFake(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "getRealUidByFakeUid fail , uid %v err %v", uid, err)
		return
	}

	for _, item := range resp {
		fake2RealUidMap[item.GetReqUid()] = item.GetUid()
		// 保证真的uid也能拿到真的uid
		fake2RealUidMap[item.GetUid()] = item.GetUid()
		real2FakeUidMap[item.GetUid()] = item.GetReqUid()
		realUidList = append(realUidList, item.GetUid())
	}

	return
}

func fillSendMagicUserProfile(msg *magic_spirit_logic.SendMagicSpiritOpt, extend *baseSendExtend) {
	msg.UserProfile = extend.ukwInfoMap[msg.GetSendUid()]
	if msg.GetUserProfile().GetPrivilege() != nil {
		msg.SendAccount = msg.GetUserProfile().GetPrivilege().GetAccount()
		msg.SendNickname = msg.GetUserProfile().GetPrivilege().GetNickname()
		msg.SendUid = msg.GetUserProfile().GetUid()
	}
}

func fillMagicUserProfile(msg *magic_spirit_logic.MagicSpiritAwardInfo, extend *baseSendExtend) {
	realUid := extend.fake2RealUidMap[msg.GetTargetUid()]
	msg.UserProfile = extend.ukwInfoMap[realUid]
	if msg.GetUserProfile().GetPrivilege() != nil {
		msg.TargetAccount = msg.GetUserProfile().GetPrivilege().GetAccount()
		msg.TargetNickname = msg.GetUserProfile().GetPrivilege().GetNickname()
		msg.TargetUid = msg.GetUserProfile().GetUid()
	}
}

func fillUserProfile(channelMsg *channel.ChannelBroadcastMsg, extend *baseSendExtend) {
	realFromUid := extend.fake2RealUidMap[channelMsg.GetFromUid()]
	realToUid := extend.fake2RealUidMap[channelMsg.GetTargetUid()]
	channelMsg.FromUserProfile = extend.ukwInfoMap[realFromUid]
	channelMsg.TargetUserProfile = extend.ukwInfoMap[realToUid]
	if channelMsg.GetFromUserProfile().GetPrivilege() != nil {
		channelMsg.FromAccount = channelMsg.GetFromUserProfile().GetPrivilege().GetAccount()
		channelMsg.FromNick = channelMsg.GetFromUserProfile().GetPrivilege().GetNickname()
		channelMsg.FromUid = channelMsg.GetFromUserProfile().GetUid()
	}

	if channelMsg.GetTargetUserProfile().GetPrivilege() != nil {
		channelMsg.TargetAccount = channelMsg.GetTargetUserProfile().GetPrivilege().GetAccount()
		channelMsg.TargetNick = channelMsg.GetTargetUserProfile().GetPrivilege().GetNickname()
		channelMsg.TargetUid = channelMsg.GetTargetUserProfile().GetUid()
	}
}

func fillBatchUserProfile(msg *presentPB_.PresentBatchInfoMsg, extend *baseSendExtend) {
	realUid := extend.fake2RealUidMap[msg.GetSendUid()]
	msg.FromUserProfile = extend.ukwInfoMap[realUid]
	if msg.GetFromUserProfile().GetPrivilege() != nil {
		msg.SendAccount = msg.GetFromUserProfile().GetPrivilege().GetAccount()
		msg.SendNickname = msg.GetFromUserProfile().GetPrivilege().GetNickname()
		msg.SendUid = msg.GetFromUserProfile().GetUid()
	}
}

func fillAllmicUserProfile(msg *pb.PresentBatchInfoMsg, extend *baseSendExtend) {
	realUid := extend.fake2RealUidMap[msg.GetSendUid()]
	msg.FromUserProfile = genUserProfile(extend.ukwInfoMap[realUid])
	if msg.GetFromUserProfile().GetPrivilege() != nil {
		msg.SendAccount = msg.GetFromUserProfile().GetPrivilege().GetAccount()
		msg.SendNickname = msg.GetFromUserProfile().GetPrivilege().GetNickname()
		msg.SendUid = msg.GetFromUserProfile().GetUid()
	}
}

func fillBatchTargetUserProfile(msg *presentPB_.PresentBatchTargetInfo, extend *baseSendExtend) {
	realUid := extend.fake2RealUidMap[msg.GetUid()]
	msg.UserProfile = extend.ukwInfoMap[realUid]
	if msg.GetUserProfile().GetPrivilege() != nil {
		msg.Account = msg.GetUserProfile().GetPrivilege().GetAccount()
		msg.Nickname = msg.GetUserProfile().GetPrivilege().GetNickname()
		msg.Uid = msg.GetUserProfile().GetUid()
	}
}

func fillBatchTargetUserProfileResp(msg *pb.PresentBatchTargetInfo, extend *baseSendExtend) {
	realUid := extend.fake2RealUidMap[msg.GetUid()]
	msg.UserProfile = genUserProfile(extend.ukwInfoMap[realUid])
	if msg.GetUserProfile() != nil {
		msg.Account = msg.GetUserProfile().GetAccount()
		msg.Nickname = msg.GetUserProfile().GetNickname()
		msg.Uid = msg.GetUserProfile().GetUid()
	}
}

func fillUserPushProfile(presentMsg *presentPB_.PresentSendMsg, sendExtend *baseSendExtend) {
	realFromUid := sendExtend.fake2RealUidMap[presentMsg.GetSendUid()]
	realToUid := sendExtend.fake2RealUidMap[presentMsg.GetTargetUid()]

	presentMsg.FromUserProfile = sendExtend.ukwInfoMap[realFromUid]
	presentMsg.ToUserProfile = sendExtend.ukwInfoMap[realToUid]

	if presentMsg.GetFromUserProfile().GetPrivilege() != nil {
		presentMsg.SendAccount = presentMsg.GetFromUserProfile().GetPrivilege().GetAccount()
		presentMsg.SendNickname = presentMsg.GetFromUserProfile().GetPrivilege().GetNickname()
		presentMsg.SendUid = presentMsg.GetFromUserProfile().GetUid()
	}

	if presentMsg.GetToUserProfile().GetPrivilege() != nil {
		presentMsg.TargetAccount = presentMsg.GetToUserProfile().GetPrivilege().GetAccount()
		presentMsg.TargetNickname = presentMsg.GetToUserProfile().GetPrivilege().GetNickname()
		presentMsg.TargetUid = presentMsg.GetToUserProfile().GetUid()
	}
}

func FillUgcUserProfile(usersMap map[uint32]*account.User) map[uint32]*ga.UserProfile {
	upMap := map[uint32]*ga.UserProfile{}
	for uid, user := range usersMap {
		up := &ga.UserProfile{
			Uid:          uid,
			Account:      user.GetUsername(),
			Nickname:     user.GetNickname(),
			AccountAlias: user.GetAlias(),
			Sex:          uint32(user.GetSex()),
		}
		upMap[uid] = up
	}
	return upMap
}

func checkOldFakeUid(ctx context.Context, targetUidList []uint32) (isReal bool, err error) {
	// 如果是单送，要区分下是不是旧的神秘人，防止从资料卡送礼暴露神秘人身份
	if len(targetUidList) == 1 {
		resp, ok, err := client.UkwCli.GetTrueUidAndCheckYkwCanDo(ctx, targetUidList[0])
		if err != nil {
			return false, err
		}
		// 既不是最新的神秘人uid，也不是真实的uid
		if !ok && resp != targetUidList[0] {
			return false, protocol.NewExactServerError(nil, status.ErrUserPresentUnableSendUserPresent, "神秘人已不在房间，不可送礼")
		}

		if resp == targetUidList[0] {
			return true, nil
		}
	}
	return false, nil
}

func (s *PresentMiddlewareMgr) calRichRatio(nowTs, giftId, rich uint32) uint32 {
	for _, item := range s.GeneralConfig.GiftLimits {
		if item.GiftId == giftId && item.BeginTime <= nowTs && item.EndTime >= nowTs {
			return rich * item.Ratio / 100
		}
	}
	return rich
}

func (s *PresentMiddlewareMgr) checkLiveStatus(ctx context.Context, channelInfo *channelPB.ChannelSimpleInfo, relayChannelId uint32) (isVirtualLive bool) {
	tmpCtx, cancel := context.WithTimeout(ctx, time.Millisecond*500)
	defer cancel()

	if channelInfo.GetChannelType() == uint32(channel.ChannelType_RADIO_LIVE_CHANNEL_TYPE) {
		liveStatus, err := client.ChannelLiveCli.GetChannelLiveStatus(tmpCtx, channellivemgr.GetChannelLiveStatusReq{
			Uid:       channelInfo.GetBindId(),
			ChannelId: channelInfo.GetChannelId(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "sendPresent -- GetChannelLiveStatus fail ,  channelId %d err %v", channelInfo.GetChannelId(), err)
			return isVirtualLive
		} else {
			isVirtualLive = liveStatus.GetChannelLiveInfo().GetChannelLiveStatus().GetAnchorType() == uint32(channellivemgr.AnchorType_Anchor_Type_Virtual)
		}
	} else if channelInfo.GetChannelType() == uint32(channel.ChannelType_OFFICIAL_LIVE_CHANNEL_TYPE) {
		// 如果是官频，要额外查转播房间的主播
		relayChannelInfo, err := client.ChannelCli.GetChannelSimpleInfo(ctx, 0, relayChannelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "sendPresent -- GetChannelLiveStatus fail ,  channelId %d err %v", relayChannelInfo.GetChannelId(), err)
			return isVirtualLive
		}

		if relayChannelInfo.GetChannelType() == uint32(channel.ChannelType_RADIO_LIVE_CHANNEL_TYPE) {
			liveStatus, err := client.ChannelLiveCli.GetChannelLiveStatus(tmpCtx, channellivemgr.GetChannelLiveStatusReq{
				Uid:       channelInfo.GetBindId(),
				ChannelId: relayChannelInfo.GetChannelId(),
			})
			if err != nil {
				log.ErrorWithCtx(ctx, "sendPresent -- GetChannelLiveStatus fail ,  channelId %d err %v", relayChannelInfo.GetChannelId(), err)
				return isVirtualLive
			} else {
				isVirtualLive = liveStatus.GetChannelLiveInfo().GetChannelLiveStatus().GetAnchorType() == uint32(channellivemgr.AnchorType_Anchor_Type_Virtual)
			}
		}
	} else {
		return isVirtualLive
	}

	return isVirtualLive
}

func (s *PresentMiddlewareMgr) checkPresentValid(presentConfig *presentPB.StPresentItemConfig, itemSource, uid, birthdayType uint32) protocol.ServerError {
	if (itemSource == uint32(pb.PresentSourceType_PRESENT_SOURCE_PACKAGE) || itemSource == uint32(pb.PresentSourceType_PRESENT_SOURCE_LOTTERY_PACKAGE) ||
		s.inForceList(presentConfig.GetItemId()) || presentConfig.GetExtend().GetTag() == uint32(ga.PresentTagType_PRESENT_TAG_PRIVILEGE)) && presentConfig.GetExtend().GetForceSendable() {
		return nil
	}

	// 生日相关的礼物不处理
	if birthdayType != 0 && birthdayType != uint32(richer_birthday.RicherBirthdayGiftType_RICHER_BIRTHDAY_GIFT_TYPE_NORMAL) {
		return nil
	}

	// 权限礼物调用接口检查
	if presentConfig.GetExtend().GetTag() == uint32(ga.PresentTagType_PRESENT_TAG_SET) {
		ctx, cancel := context.WithTimeout(context.Background(), time.Millisecond*200)
		defer cancel()
		_, sErr := client.PresentSetCli.CheckPresentValid(ctx, &present_set.CheckPresentValidReq{PresentId: presentConfig.GetItemId(), Uid: uid})
		if sErr != nil {
			log.ErrorWithCtx(ctx, "checkPresentValid -- CheckPresentValidReq fail , err %v", sErr)
			return protocol.ToServerError(sErr)
		}
		return nil
	}

	if presentConfig.GetIsDel() {
		return protocol.NewExactServerError(nil, status.ErrUserPresentConfigNotExist)
	}

	if presentConfig.GetExtend().GetUnshowPresentShelf() && presentConfig.GetExtend().GetTag() != uint32(ga.PresentTagType_PRESENT_TAG_LEVELUP) {
		return nil
	}

	// 礼物是否在上架中
	if presentConfig.GetEffectBegin() > uint32(time.Now().Unix()) {
		return protocol.NewExactServerError(nil, status.ErrUserPresentLmtEffectedBegin)
	}
	if presentConfig.GetEffectEnd() < uint32(time.Now().Unix()) {
		return protocol.NewExactServerError(nil, status.ErrUserPresentLmtEffectedEnd)
	}

	return nil
}

func (s *PresentMiddlewareMgr) inForceList(itemId uint32) bool {
	if s.GeneralConfig.GetPresentConfig().GiftForce == nil {
		return false
	}
	for _, item := range s.GeneralConfig.GetPresentConfig().GiftForce {
		if item == itemId {
			return true
		}
	}
	return false
}

func (s *PresentMiddlewareMgr) checkPresentSetValid(presentConfig *presentPB.StPresentItemConfig, itemSource uint32, uid uint32) protocol.ServerError {
	// 权限礼物调用接口检查
	if presentConfig.GetExtend().GetTag() == uint32(ga.PresentTagType_PRESENT_TAG_SET) {
		ctx, cancel := context.WithTimeout(context.Background(), time.Millisecond*200)
		defer cancel()
		_, sErr := client.PresentSetCli.CheckPresentValid(ctx, &present_set.CheckPresentValidReq{PresentId: presentConfig.GetItemId(), Uid: uid})
		if sErr != nil {
			log.ErrorWithCtx(ctx, "checkPresentValid -- CheckPresentValidReq fail , err %v", sErr)
			return protocol.ToServerError(sErr)
		}
		return nil
	}

	return nil
}
