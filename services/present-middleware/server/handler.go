package server

// 可以异步处理的各种函数

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/pkg/log"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app"
	present_set "golang.52tt.com/protocol/services/present-set"
	"golang.52tt.com/protocol/services/presentextraconf"
	pb "golang.52tt.com/protocol/services/time-present"
	"golang.52tt.com/protocol/services/userpresent"
	"golang.52tt.com/services/present-middleware/cache"
	"golang.52tt.com/services/present-middleware/rpc/client"
	"time"
)

func (s *PresentMiddlewareMgr) HandleVipPrivilege(ctx context.Context, extend *baseSendExtend) {
	newCtx, cancel := NewContextWithInfoTimeout(ctx, time.Second*1)
	s.AsyncQueue.Enqueue(
		func() error {
			defer cancel()
			//通知vip客服
			minScore := uint32(0)
			levelConfigs, err := s.vipprivilegeCli.GetVipLevelConfigs(newCtx)
			if err != nil {
				log.ErrorWithCtx(newCtx, "HandleVipPrivilege GetVipLevelConfigs err , uid %d err %v", extend.sendUser.GetUid(), err)
			}
			if len(levelConfigs.GetVipLevelConfigs()) <= 0 {
				minScore = 1000000
			} else {
				minScore = levelConfigs.GetVipLevelConfigs()[0].MinScore
			}
			if uint32(extend.extendInfo.sendUserCurrRichValue) > minScore {
				err = client.VipprivilegeCli.OnRichChange(newCtx, extend.sendUser.GetUid(), extend.extendInfo.sendUserCurrRichValue-uint64(extend.extendInfo.realAddRichValue),
					uint64(extend.extendInfo.sendUserCurrRichValue))
				if err != nil {
					log.ErrorWithCtx(newCtx, "HandleVipPrivilege OnRichChange err , uid %d err %v", extend.sendUser.GetUid(), err)
				}
			}
			return err
		},
	)
}

func (s *PresentMiddlewareMgr) HandleConsumePrivilege(ctx context.Context, extend *baseSendExtend) {
	newCtx, cancel := NewContextWithInfoTimeout(ctx, time.Second*1)
	s.AsyncQueue.Enqueue(
		func() error {
			defer cancel()
			//【防小白骚扰】24小时内单笔送出/收到1000元礼物的用户 存redis
			consumeMap := make(map[uint32]uint32, 0)
			for _, item := range extend.sucOrders {
				consumeMap[item.targetUid] = extend.presentConfigMap[item.giftId].GetPrice()
			}
			err := s.SetHighConsume(newCtx, consumeMap, extend.sendUser.GetUid())
			if err != nil {
				log.ErrorWithCtx(newCtx, "HandleConsumePrivilege SetHighConsume err , uid %d err %v", extend.sendUser.GetUid(), err)
			}
			return err
		},
	)
}

func (s *PresentMiddlewareMgr) HandleUpdateUserGrowTimeline(ctx context.Context, extend *baseSendExtend) {
	newCtx, cancel := NewContextWithInfoTimeout(ctx, time.Millisecond*500)
	s.AsyncQueue.Enqueue(
		func() error {
			//推送变动
			defer cancel()
			err := s.MissionHelperCli.UpdateUserGrowTimeline(newCtx, extend.sendUser.GetUid())
			if err != nil {
				log.ErrorWithCtx(newCtx, "HandleUpdateUserGrowTimeline UpdateUserGrowTimeline err , uid %d err %v", extend.sendUser.GetUid(), err)
			}
			//6 对应 grow，sync.proto中定义
			err = s.notifyClient(newCtx, []uint32{extend.sendUser.GetUid()}, 6)
			if err != nil {
				log.ErrorWithCtx(newCtx, "HandleUpdateUserGrowTimeline notifyClient err , uid %d err %v", extend.sendUser.GetUid(), err)
			}
			return err
		},
	)
}

func (s *PresentMiddlewareMgr) HandleUserRichOrCharmLevelUpdate(ctx context.Context, extend *baseSendExtend, isRecordRich bool) {
	newCtx, cancel := NewContextWithInfoTimeout(ctx, time.Second*1)
	channelSimpleInfo := extend.channelSimpleInfo.GetChannelSimple()
	s.AsyncQueue.Enqueue(
		func() error {
			defer cancel()
			err := s.broadcastUserRichOrCharmLevelUpdate(newCtx, extend.sendUser, channelSimpleInfo.GetChannelId(), extend.targetUserMap,
				extend.extendInfo, isRecordRich, extend)
			if err != nil {
				log.ErrorWithCtx(newCtx, "HandleUserRichOrCharmLevelUpdate broadcastUserRichOrCharmLevelUpdate err , uid %d err %v", extend.sendUser.GetUid(), err)
			}
			return err
		},
	)
}

func (s *PresentMiddlewareMgr) HandleSendUserScoreAndCharmRichSync(ctx context.Context, extend *baseSendExtend) {
	newCtx, cancel := NewContextWithInfoTimeout(ctx, time.Second*1)
	s.AsyncQueue.Enqueue(
		func() error {
			defer cancel()
			err := s.MissionHelperCli.UpdateUserScoreAndCharmRichSync(newCtx, extend.sendUser.GetUid())
			if err != nil {
				log.ErrorWithCtx(newCtx, "HandleSendUserScoreAndCharmRichSync UpdateUserScoreAndCharmRichSync err , uid %d err %v", extend.sendUser.GetUid(), err)
			}
			//6 对应 grow，sync.proto中定义
			err = s.notifyClient(newCtx, []uint32{extend.sendUser.GetUid()}, 6)
			if err != nil {
				log.ErrorWithCtx(newCtx, "HandleSendUserScoreAndCharmRichSync notifyClient err , uid %d err %v", extend.sendUser.GetUid(), err)
			}
			return err
		},
	)
}

func (s *PresentMiddlewareMgr) HandleTargetUserScoreAndCharmRichSync(ctx context.Context, extend *baseSendExtend, target *TargetExtendInfo) {
	newCtx, cancel := NewContextWithInfoTimeout(ctx, time.Second*1)
	s.AsyncQueue.Enqueue(
		func() error {
			defer cancel()
			err := s.MissionHelperCli.UpdateUserScoreAndCharmRichSync(newCtx, target.uid)
			if err != nil {
				log.ErrorWithCtx(newCtx, "HandleTargetUserScoreAndCharmRichSync UpdateUserScoreAndCharmRichSync err , uid %d err %v", extend.sendUser.GetUid(), err)
			}
			//6 对应 grow，sync.proto中定义
			err = s.notifyClient(newCtx, []uint32{target.uid}, 6)
			if err != nil {
				log.ErrorWithCtx(newCtx, "HandleTargetUserScoreAndCharmRichSync notifyClient err , uid %d err %v", extend.sendUser.GetUid(), err)
			}
			return err
		},
	)
}

func (s *PresentMiddlewareMgr) HandleHandleMission(ctx context.Context, extend *baseSendExtend, req *userpresent.SendPresentReq) {
	newCtx, cancel := NewContextWithInfoTimeout(ctx, time.Second*1)
	s.AsyncQueue.Enqueue(
		func() error {
			// 送礼成功，完成任务
			defer cancel()
			if extend.channelSimpleInfo.ChannelSimple.GetChannelId() > 0 {
				reqSerialize, _ := proto.Marshal(req)
				err := client.MissionCli.HandleMission(newCtx, extend.sendUser.GetUid(), 1165, reqSerialize)
				if err != nil {
					log.ErrorWithCtx(newCtx, "HandleHandleMission HandleHandleMission err , uid %d err %v", extend.sendUser.GetUid(), err)
				}
				return err
			}
			return nil
		},
	)
}

func (s *PresentMiddlewareMgr) HandleReportCustomPresentSend(ctx context.Context, extend *baseSendExtend, req *userpresent.SendPresentReq, id uint32) {
	newCtx, cancel := NewContextWithInfoTimeout(ctx, time.Second*1)
	s.AsyncQueue.Enqueue(
		func() error {
			defer cancel()
			if extend.primaryCustomGift != nil {
				//推送变动
				_, err := client.PresentExtraCli.ReportCustomPresentSend(newCtx, &presentextraconf.ReportCustomPresentSendReq{
					Uid:       extend.sendUser.GetUid(),
					TargetUid: req.GetTargetUid(),
					ChannelId: req.GetChannelId(),
					GiftId:    id,
					Count:     req.GetItemCount(),
					OrderId:   req.GetOrderId(),
				})
				if err != nil {
					log.ErrorWithCtx(newCtx, "HandleReportCustomPresentSend ReportCustomPresentSend err , uid %d err %v", extend.sendUser.GetUid(), err)
				}
				return err
			}
			return nil
		},
	)
}

func (s *PresentMiddlewareMgr) HandleNotifyPresentSend(ctx context.Context, extend *baseSendExtend, id, count uint32) {
	newCtx, cancel := NewContextWithInfoTimeout(ctx, time.Second*1)
	s.AsyncQueue.Enqueue(
		func() error {
			defer cancel()
			_, err := client.PresentExtraCli.NotifyPresentSend(newCtx, &presentextraconf.NotifyPresentSendReq{
				Uid:    extend.sendUser.GetUid(),
				GiftId: id,
				Count:  count,
			})
			if err != nil {
				log.ErrorWithCtx(newCtx, "HandleNotifyPresentSend NotifyPresentSend err , uid %d err %v", extend.sendUser.GetUid(), err)
			}
			return err
		},
	)
}

func (s *PresentMiddlewareMgr) HandleFirstPresent(ctx context.Context, extend *baseSendExtend, uid uint32) {
	newCtx, cancel := NewContextWithInfoTimeout(ctx, time.Second*1)
	s.AsyncQueue.Enqueue(
		func() error {
			defer cancel()

			olInfo, err := client.UserOlCli.GetMobileOnlineInfo(newCtx, uid)
			if err != nil {
				log.ErrorWithCtx(ctx, "HandleFirstPresent GetMobileOnlineInfo err , uid %d err %v", uid, err)
				return err
			}

			// 迷境不考虑后面的流程了
			if olInfo.GetMarketId() == 6 {
				return nil
			}

			// 首先查看积分，不够1w就直接返回
			score, sErr := client.UserScoreCli.GetUserScoreByScoreType(newCtx, uid, 1)
			if sErr != nil {
				log.ErrorWithCtx(ctx, "HandleFirstPresent GetUserScore err , uid %d err %v", uid, sErr)
				return sErr
			}

			scoreValuable, sErr := client.UserScoreCli.GetUserScoreByScoreType(newCtx, uid, 0)
			if sErr != nil {
				log.ErrorWithCtx(ctx, "HandleFirstPresent GetUserScore err , uid %d err %v", uid, sErr)
				return sErr
			}

			if score+scoreValuable < 10000 {
				return nil
			}

			// 检查是否首次，是就推im+push
			ok, err := cache.PresentMiddlewareCacheClient.IsFirstScoreNewVersion(newCtx, uid)
			if err != nil {
				log.ErrorWithCtx(newCtx, "HandleNotifyPresentSend IsFirstScore err , uid %d err %v", uid, err)
				return err
			}

			log.DebugWithCtx(newCtx, "IsFirstScoreNewVersion %d %v", uid, ok)

			if !ok {
				_ = cache.PresentMiddlewareCacheClient.AddFirstScoreNewVersion(newCtx, uid)
				_ = s.SendFirstScoreMsg(newCtx, uid, olInfo.GetMarketId(), olInfo.GetTerminalType())
				if extend.channelSimpleInfo.GetChannelSimple().GetChannelId() != 0 {
					_ = s.PushFirstScore(newCtx, uid)
				}
			}

			return nil
		},
	)
}

func (s *PresentMiddlewareMgr) HandlePresentSetRecord(ctx context.Context, extend *baseSendExtend, uid, presentId uint32) {
	newCtx, cancel := NewContextWithInfoTimeout(ctx, time.Second*1)
	s.AsyncQueue.Enqueue(
		func() error {
			defer cancel()

			presentCount := uint32(0)
			orderIds := make([]string, 0)

			for _, item := range extend.sucOrders {
				presentCount += item.count
				orderIds = append(orderIds, item.orderId)
			}

			if extend.presentConfigMap[presentId].GetExtend().GetTag() != uint32(app.PresentTagType_PRESENT_TAG_SET) {
				return nil
			}

			_, err := client.PresentSetCli.UserSentSetPresent(newCtx, &present_set.UserSentSetPresentReq{
				Uid:          uid,
				PresentId:    presentId,
				PresentCount: presentCount,
				OrderId:      orderIds,
				SendTime:     uint32(extend.nowTs.Unix()),
			})

			if err != nil {
				log.ErrorWithCtx(ctx, "HandlePresentSetRecord UserSentSetPresent err , uid %d err %v", uid, err)
				return err
			}

			return nil
		},
	)
}

func (s *PresentMiddlewareMgr) HandleEmperorSetRecord(ctx context.Context, extend *baseSendExtend, uid, setId, guildId uint32) {
	newCtx, cancel := NewContextWithInfoTimeout(ctx, time.Second*1)
	s.AsyncQueue.Enqueue(
		func() error {
			defer cancel()

			presentCount := uint32(0)
			orderIds := make([]string, 0)

			for _, item := range extend.sucOrders {
				presentCount += item.count
				orderIds = append(orderIds, item.orderId)
			}

			tmp := &present_set.UserSentEmperorSetPresentReq{
				Uid:       uid,
				TargetUid: extend.setTargetUser.GetUid(),
				OrderId:   extend.uniqOrderId,
				SendTime:  uint32(extend.nowTs.Unix()),
				ChannelId: extend.channelSimpleInfo.GetChannelSimple().GetChannelId(),
				SetId:     setId,
				Score:     uint32(extend.extendInfo.emperorSetTargetUser.Score),
				AddRich:   uint32(extend.extendInfo.emperorSetSendUser.RichValue),
				AddCharm:  uint32(extend.extendInfo.emperorSetTargetUser.Charm),
				GuildId:   guildId,
			}

			if extend.ukwInfoMap[extend.sendUser.GetUid()].GetPrivilege() != nil {
				tmp.FromUkwAccount = extend.ukwInfoMap[extend.sendUser.GetUid()].GetAccount()
				tmp.FromUkwNickname = extend.ukwInfoMap[extend.sendUser.GetUid()].GetNickname()
			}

			if extend.ukwInfoMap[extend.setTargetUser.GetUid()].GetPrivilege() != nil {
				tmp.ToUkwAccount = extend.ukwInfoMap[extend.setTargetUser.GetUid()].GetAccount()
				tmp.ToUkwNickname = extend.ukwInfoMap[extend.setTargetUser.GetUid()].GetNickname()
			}

			_, err := client.PresentSetCli.UserSentEmperorSetPresent(newCtx, tmp)

			if err != nil {
				log.ErrorWithCtx(ctx, "HandlePresentSetRecord UserSentSetPresent err , uid %d err %v", uid, err)
				return err
			}

			return nil
		},
	)
}

func (s *PresentMiddlewareMgr) HandleTimePresentSend(ctx context.Context, extend *baseSendExtend) {
	newCtx, cancel := NewContextWithInfoTimeout(ctx, time.Second*1)
	s.AsyncQueue.Enqueue(
		func() error {
			defer cancel()

			log.InfoWithCtx(ctx, "HandleTimePresentSend %d", extend)

			timePresentList := make([]*pb.AddTimePresentItem, 0)
			itemId := uint32(0)
			for _, item := range extend.sucOrders {
				if extend.presentConfigMap[item.giftId].GetExtend().GetTag() != uint32(app.PresentTagType_PRESENT_TAG_TIME) {
					continue
				}

				fromProfile, _ := proto.Marshal(extend.ukwInfoMap[extend.sendUser.GetUid()])
				toProfile, _ := proto.Marshal(extend.ukwInfoMap[item.targetUid])

				micList, err := client.ChannelMicCli.GetMicrList(newCtx, extend.channelSimpleInfo.GetChannelSimple().GetChannelId(), extend.sendUser.GetUid())
				if err != nil {
					log.ErrorWithCtx(ctx, "HandleTimePresentSend GetMicrList err , uid %d err %v", extend.sendUser.GetUid(), err)
					return err
				}

				micIdList := make([]*pb.TimePresentMicReflect, 0)
				for _, mic := range micList.GetAllMicList() {
					micIdList = append(micIdList, &pb.TimePresentMicReflect{
						MicId: mic.GetMicId(),
						Uid:   mic.GetMicUid(),
					})
				}

				timePresentList = append(timePresentList, &pb.AddTimePresentItem{
					FromUser:  fromProfile,
					ToUser:    toProfile,
					MicIdList: micIdList,
					OrderId:   item.orderId,
					FromUid:   extend.sendUser.GetUid(),
					ToUid:     item.targetUid,
				})

				itemId = item.giftId
			}

			// 一个时间礼物都没有就直接返回
			if len(timePresentList) == 0 {
				return nil
			}

			_, sErr := client.TimePresentCli.AddChannelTimePresent(newCtx, &pb.AddChannelTimePresentReq{
				ChannelId:   extend.channelSimpleInfo.GetChannelSimple().GetChannelId(),
				ItemId:      itemId, // 只支持一个item_id
				OrderId:     extend.uniqOrderId,
				TimePresent: timePresentList,
			})

			if sErr != nil {
				log.ErrorWithCtx(ctx, "HandleTimePresentSend AddChannelTimePresent err , uid %d err %v", extend.sendUser.GetUid(), sErr)
				return sErr
			}

			return nil
		},
	)
}

func (s *PresentMiddlewareMgr) HandleLiveIntimateSend(ctx context.Context, extend *baseSendExtend) {
	newCtx, cancel := NewContextWithInfoTimeout(ctx, time.Second*1)
	s.AsyncQueue.Enqueue(
		func() error {
			defer cancel()

			log.InfoWithCtx(ctx, "HandleLiveIntimateSend %d", extend)

			timePresentList := make([]*pb.AddTimePresentItem, 0)
			itemId := uint32(0)
			for _, item := range extend.sucOrders {
				if extend.presentConfigMap[item.giftId].GetExtend().GetTag() != uint32(app.PresentTagType_PRESENT_TAG_LIVE_INTIMATE) {
					continue
				}

				fromProfile, _ := proto.Marshal(extend.ukwInfoMap[extend.sendUser.GetUid()])
				toProfile, _ := proto.Marshal(extend.ukwInfoMap[item.targetUid])

				timePresentList = append(timePresentList, &pb.AddTimePresentItem{
					FromUser: fromProfile,
					ToUser:   toProfile,
					OrderId:  item.orderId,
					FromUid:  extend.sendUser.GetUid(),
					ToUid:    item.targetUid,
				})

				itemId = item.giftId
			}

			// 一个时间礼物都没有就直接返回
			if len(timePresentList) == 0 {
				return nil
			}

			_, sErr := client.TimePresentCli.AddChannelLiveIntimatePresent(newCtx, &pb.AddChannelLiveIntimatePresentReq{
				ChannelId: extend.channelSimpleInfo.GetChannelSimple().GetChannelId(),
				ItemId:    itemId, // 只支持一个item_id
				OrderId:   extend.uniqOrderId,
				Items:     timePresentList,
			})

			if sErr != nil {
				log.ErrorWithCtx(ctx, "HandleTimePresentSend AddChannelTimePresent err , uid %d err %v", extend.sendUser.GetUid(), sErr)
				return sErr
			}

			return nil
		},
	)
}

func NewContextWithInfoTimeout(ctx context.Context, timeOut time.Duration) (newCtx context.Context, cancel context.CancelFunc) {
	newCtx, cancel = context.WithTimeout(context.Background(), timeOut)
	sv, _ := protogrpc.ServiceInfoFromContext(ctx)
	newCtx = protogrpc.WithServiceInfo(newCtx, sv)
	return newCtx, cancel
}
