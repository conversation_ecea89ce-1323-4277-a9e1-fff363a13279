var cp="../../";
$(function(){

	var ww=$(window).width();
	var wh=$(window).height();

	$("#p_top").pannel({
		width:ww,
	    height:84,
	    region:"top"
	});

	$("#p_menu").pannel({
		title:"系统菜单",
		width:200,
	    height:wh-84-32,
	    region:"left",
	    content:{
	    	id:"p_menu_1"
	    }

	});

	$("#p_menu_1").append("<ul class='ztree' id='menuTree'/>");

	$("#p_main").pannel({
		width:ww-200,
		height:wh-84-32,
		region:"center"
	});

	$("#p_bottom").pannel({
		width:ww,
	    height:32,
	    region:"bottom"
	});

	// $("#p_top").append("<div class='left_title'><span class='sys_name'>dbgate配置管理系统</span></div>");
	$("#p_bottom").append("<span id='copyright' class='cr'></span>");
	var setting = {
			data: {
				simpleData: {
					enable: true
				}
			},
			callback: {
				onClick: onClick
			}
	};
	var zNodes =[
		{ id:1, pId:0, name:"CP配置管理",open:true},
		{ id:11, pId:1, name:"系统介绍",icon:"css/zTree/img/diy/leaf.gif",data:{url:'pages/welcom.html'}},
		{ id:12, pId:1, name:"游戏管理",icon:"css/zTree/img/diy/leaf.gif",data:{url:'pages/cp-base.html'}},
		{ id:13, pId:1, name:"游戏版本管理",icon:"css/zTree/img/diy/leaf.gif",data:{url:'pages/version-base.html'}},
	];
	function onClick(event, treeId, treeNode, clickFlag){

		if(!treeNode.isParent && first){
			console.log("test1:", treeNode,treeNode.isParent,first)
			$("#p_main").tab("claseTab",{name:treeNode.name,url:treeNode.data.url,closed:true});
			$("#p_main").tab("open",{name:treeNode.name,url:treeNode.data.url,closed:true});
		}else{
			if (!treeNode.isParent) {
				console.log("test2:", treeNode,treeNode.isParent,first)
				//$("#p_main").tab("claseTab",{name:treeNode.name,url:treeNode.data.url,closed:true});
				$("#p_main").tab("open",{name:treeNode.name,url:treeNode.data.url,closed:true});
			}
		}
		first = false;
	}

	$.fn.zTree.init($("#menuTree"), setting, zNodes);
	$("#p_main").tab({home:{name:"系统介绍",url:"pages/welcom.html",closed:false}});
	//$("#p_main").tab({home:{name:"游戏管理",url:'pages/cp-base.html',closed:true}});

	$(window).resize(function() {
		var ww=$(window).width();
		$("#p_main").width((ww-$("#p_menu").width()-7)+"px");
	});
});
