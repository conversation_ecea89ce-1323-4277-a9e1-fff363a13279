package control

import (
	"context"
	"encoding/json"
	"net/http"
	"time"

	"gitlab.ttyuyin.com/golang/gudetama/log"
	"golang.52tt.com/pkg/web"
	pb "golang.52tt.com/protocol/services/gameserver"
	"golang.52tt.com/services/games-config/models"
)

// useless
func ModifyGameInfo(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	var req pb.ModifyGameInfoReq
	err := json.Unmarshal(authInfo.Body, &req)
	if err != nil {
		log.Errorf("Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}
	log.Debugf("ModifyGameInfo %s %+v", string(authInfo.Body), req)
	if req.GetGameid() == 0 {
		log.Errorf("ModifyGameInfo param err: GetGameid == 0!")
		web.ServeBadReq(w)
		return
	}

	rsp := &pb.ModifyGameInfoResp{}

	_, err = models.GetModelServer().GameServerClient.ModifyGameInfo(ctx, &req)
	if err != nil {
		web.ServeAPIError(w)
		return
	}
	e := web.ServeAPIJson(w, rsp)
	if e != nil {
		log.Errorf("ServeAPIJson failed: %s", e.Error())
	}
}
