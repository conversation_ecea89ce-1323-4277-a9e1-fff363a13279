package server

import (
	"context"
	"errors"
	"github.com/golang/mock/gomock"
	pb "golang.52tt.com/protocol/services/revenue_ad_svr"
	"golang.52tt.com/services/revenue-ad-svr/internal/conf"
	"golang.52tt.com/services/revenue-ad-svr/internal/mocks"
	"golang.52tt.com/services/revenue-ad-svr/internal/mocks/rpc"
	"golang.52tt.com/services/revenue-ad-svr/internal/rpc"
	"golang.52tt.com/services/revenue-ad-svr/internal/store"
	"testing"
)

func Test_GetUserAdInfoList(t *testing.T) {

	s := &Server{}
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()
	mockDyconf := mocks.NewMockIConfDynamic(mockCtrl)
	s.dyconf = mockDyconf
	mockDyconf.EXPECT().Get().Return(&conf.RevenueAdConf{
		AdPlatformConfs: []*conf.AdPlatformConf{},
	}).AnyTimes()

	mockStore := mocks.NewMockIStore(mockCtrl)
	mockCache := mocks.NewMockICache(mockCtrl)
	s.istore = mockStore
	s.icache = mockCache
	mockAbtestCli := mocks_rpc.NewMockIAbtestApiCli(mockCtrl)
	s.abtestApiCli = mockAbtestCli
	s.adList = []*pb.AdConf{
		{
			PlatformType:  1,
			PosId:         1,
			Enabled:       true,
			AdOs:          1,
			AdPosId:       "10000",
			UserGroupType: 2,
			UserGroupId:   "1111",
			AbtestTag:     "test",
		},
	}

	testCases := []struct {
		name     string
		req      *pb.GetUserAdInfoListReq
		mockFunc func()
		wantErr  bool
	}{
		{
			name: "Test_GetUserAdInfoList case ok",
			req: &pb.GetUserAdInfoListReq{
				Uid:              1,
				SupportPlatforms: []uint32{1},
				Os:               1,
				BundleId:         "1111",
			},
			mockFunc: func() {
				mockCache.EXPECT().GetGroupsByUid(gomock.Any(), gomock.Any()).Return([]string{"1111"}, nil).AnyTimes()
				mockAbtestCli.EXPECT().GetABTestUserHit(gomock.Any(), gomock.Any(), gomock.Any()).Return(map[string]struct{}{
					"test": struct{}{},
				}, nil).AnyTimes()
			},
			wantErr: false,
		},
	}
	ctx := context.Background()
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			tc.mockFunc()
			_, err := s.GetUserAdInfoList(ctx, tc.req)
			if tc.wantErr {
				if err == nil {
					t.Errorf("Test_GetUserAdInfoList failed, got err nil, want err")
				}
			} else {
				if err != nil {
					t.Errorf("Test_GetUserAdInfoList failed, got err %v, want nil", err)
				}
			}
		})
	}
	t.Logf("Test_GetUserAdInfoList OK")
}

func Test_AddAdConf(t *testing.T) {
	s := &Server{
		gromoreAdUnits: make(map[string]*rpc.GromoreAdUnit),
	}
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()
	mockDyconf := mocks.NewMockIConfDynamic(mockCtrl)
	mockDyconf.EXPECT().Get().Return(&conf.RevenueAdConf{
		AdPlatformConfs: []*conf.AdPlatformConf{},
		AdPosConfMap: map[uint32]*conf.AdPosConf{
			1: &conf.AdPosConf{
				AdTypes: []uint32{2},
			},
		},
	}).AnyTimes()
	mockCache := mocks.NewMockICache(mockCtrl)
	mockStore := mocks.NewMockIStore(mockCtrl)
	s.istore = mockStore
	s.icache = mockCache
	s.dyconf = mockDyconf
	s.gromoreAdUnits["10000"] = &rpc.GromoreAdUnit{
		AdUnitType: 1,
	}
	testCases := []struct {
		name     string
		req      *pb.AddAdConfReq
		mockFunc func()
		wantErr  bool
	}{
		{
			name: "Test_AddAdConf case ok",
			req: &pb.AddAdConfReq{
				AdConf: &pb.AdConf{
					PosId:         1,
					AdPosId:       "10000",
					AdType:        1,
					AdOs:          1,
					UserGroupType: 1,
					PlatformType:  1,
					Enabled:       true,
				},
			},
			mockFunc: func() {
				mockStore.EXPECT().AddAdConf(gomock.Any(), gomock.Any()).Return(nil)
				mockStore.EXPECT().GetAdConfList(gomock.Any(), 0, 0, "").Return([]*store.AdConf{}, nil).AnyTimes()
				mockCache.EXPECT().IncRevenueAdConfVersion(gomock.Any()).Return(nil)
			},
			wantErr: false,
		},
	}

	ctx := context.Background()
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			tc.mockFunc()
			_, err := s.AddAdConf(ctx, tc.req)
			if tc.wantErr != (err != nil) {
				t.Errorf("Test_AddAdConf %s failed, got err %v, wantErr %v", tc.name, err, tc.wantErr)
			}
		})
	}
	t.Logf("Test_AddAdConf OK")
}

func Test_GetAdConfList(t *testing.T) {
	s := &Server{}
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()
	mockStore := mocks.NewMockIStore(mockCtrl)
	s.istore = mockStore
	testCases := []struct {
		name     string
		req      *pb.GetAdConfListReq
		mockFunc func()
		wantErr  bool
	}{
		{
			name: "Test_GetAdConfList case ok",
			req: &pb.GetAdConfListReq{
				Page:     0,
				PageSize: 10,
				AdPosId:  "10000",
			},
			mockFunc: func() {
				mockStore.EXPECT().AdTotalNum(gomock.Any(), "10000").Return(uint32(1), nil)
				mockStore.EXPECT().GetAdConfList(gomock.Any(), 0, 10, "10000").Return([]*store.AdConf{}, nil)
			},
			wantErr: false,
		},
		{
			name: "Test_GetAdConfList case err",
			req: &pb.GetAdConfListReq{
				Page:     0,
				PageSize: 10,
				AdPosId:  "10000",
			},
			mockFunc: func() {
				mockStore.EXPECT().AdTotalNum(gomock.Any(), "10000").Return(uint32(1), nil)
				mockStore.EXPECT().GetAdConfList(gomock.Any(), 0, 10, "10000").Return(nil, errors.New("mock err"))
			},
			wantErr: true,
		},
	}

	ctx := context.Background()
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			tc.mockFunc()
			_, err := s.GetAdConfList(ctx, tc.req)
			if tc.wantErr != (err != nil) {
				t.Errorf("Test_GetAdConfList %s failed, got err %v, wantErr %v", tc.name, err, tc.wantErr)
			}
		})
	}
	t.Logf("Test_GetAdConfList OK")
}
