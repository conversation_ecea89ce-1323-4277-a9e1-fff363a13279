syntax = "proto3";

package conf;

import "tt-protocol/service/quicksilver/extend/apollo/annotations.proto";

option go_package = "./;conf";

option (apollo.app_id) = "tt-pia";
option (apollo.secret) = "64422f5bf9a94bcda5acb8b63ffa5740";

message DramaConfig {
    option (apollo.namespace) = "pia_drama_config.json";
    option (apollo.config_type) = DYNAMIC;
    // 运行环境，prod：生产环境，dev：开发环境，testing：测试环境，staging：预发布环境
    string env = 1;
    // 单次拉取剧本数
    uint32 step = 2;
    // v2剧本库 数据源url
    string url = 3;
    // 剧本收藏数据上传至网站端 url
    string collect_url = 4;
    // 剧本同步token
    string sync_token = 5;
    // 剧本库列表每页的缓存开关，开关打开并且缓存时间不为0时才会缓存
    bool enable_list_cache = 6;
    // 剧本库列表每页的缓存时间，单位秒，配置为0时不缓存
    int64 list_cache_time = 7;
    // 剧本库排行榜列表拉取step
    uint32 rank_list_step = 8;
    // 剧本库排行榜刷新时间间隔， 单位分钟
    uint32 rank_list_cache_time = 9;
    // 剧本库排行榜url
    string rank_list_web_url = 10;
    // 参演记录页大小
    uint32 playing_record_page_size = 11;
    // 剧本举报到网站端的url
    string drama_report_url = 12;
    // 剧本反馈到网站端的url
    string drama_feedback_url = 13;
    // 作者同步step
    uint32 sync_author_step = 14;
    // 作者同步url
    string sync_author_web_url = 15;
}

message Timer {
    option (apollo.namespace) = "pia_timer.json";
    option (apollo.config_type) = DYNAMIC;
    // UGC房间同步定时器时间间隔(s)
    int64 ugc_channel_sync_interval = 1;
    // 剧本同步定时器时间间隔(s)
    int64 drama_sync_interval = 2;
    // 已点列表清理定时器时间间隔(s)
    int64 order_drama_clear_interval = 3;
    // 剧本进度更新定时器时间间隔(s)
    int64 drama_running_progress_update_interval = 4;
    // 本地时间更新定时器时间间隔(s)
    int64 local_time_update_interval = 5;
    // 走本期间演绎麦位人数上报定时器
    int64 playing_on_mic_report_interval = 6;
    // 剧本在玩房间热度更新间隔(s)
    int64 drama_channel_sync_interval = 7;
    // 剧本收藏数据上报间隔(s)
    int64 drama_collect_report_interval = 8;
    // 在玩房间过期时间(s)
    int64 playing_channel_expire_interval = 9;
    // 上报剧本举报记录的时间间隔(s)
    int64 upload_report_drama_record_interval = 10;
    // 上报剧本反馈记录的时间间隔(s)
    int64 upload_drama_feedback_record_interval = 11;
    // 作者作品数量更新间隔(s)
    int64 author_works_count_update_interval = 12;
    // 临时副本清除 crontab计划
    string temp_copied_drama_cleaner_interval = 13;
    // 作者信息同步间隔
    int64 author_info_sync_interval = 14;
}

message TabId {
    option (apollo.config_type) = STATIC;
    // ugc房间pia戏的tabid
    uint32 ugc_pia_tab_id = 1;
    // ugc房间pia戏的tabid
    uint32 ugc_new_pia_tab_id = 3;
    // 广播房的tabid
    uint32 broadcast_tab_id = 4;
}

message UgcChannel {
    option (apollo.config_type) = STATIC;
    // 分页大小
    uint32 page_size = 1;
}

message OrderDramaList {
    option (apollo.namespace) = "pia_order_drama_list.json";
    option (apollo.config_type) = DYNAMIC;
    // 列表大小
    uint32 list_len = 1;
    // 单个已点剧本的过期时间(s)
    int64 expire_duration = 2;
    // 用户可点的最大剧本数
    uint32 user_order_limit = 3;
}

message DramaRunningParam {
    option (apollo.namespace) = "pia_drama_running_param.json";
    option (apollo.config_type) = DYNAMIC;
    // 剧本开始倒计时(s)
    uint32 start_delay = 1;
    // 是否打开走本期间切换走本方式
    bool open_running_change_play_type = 2;
    // 房间走本过期时间(s)
    int64 expire_duration = 3;
}

// 特性开关
message FeatureTrigger {
    option (apollo.namespace) = "feature_trigger.json";
    option (apollo.config_type) = DYNAMIC;
    // 开关列表
    map<string, bool> feature_trigger = 1;
}
