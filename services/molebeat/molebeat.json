{"mongo": {"user_name": "godman", "password": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addrs": "10.110.201.250:27017", "database": "molebeat", "max_pool_size": 200}, "mongofirsttype": {"addrs": "10.110.201.250:27017", "database": "molebeat", "user_name": "godman", "password": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "max_pool_size": 200}, "lotterymongo": {"user_name": "godman", "password": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addrs": "10.110.201.250:27017", "database": "molebeat", "max_pool_size": 200}, "lotterymongofirsttype": {"addrs": "10.110.201.250:27017", "database": "molebeat", "user_name": "godman", "password": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "max_pool_size": 200}, "gameinfo": {"gamename": "2022NewYearAct", "gamedesc": "2022新年打年兽", "begintime": "2022-01-14 10:30:00", "endtime": "2022-01-19 23:00:00", "supply_begintime": "2022-01-14 13:30:00", "supply_endtime": "2022-01-19 22:50:00", "updatetime": "2032-01-08 00:03:10", "start_delay": 10, "report_delay": 8, "prepare_second": 3, "duration_second": 12, "mole_appear_cnt": 10, "attach_atleast_cnt": 8, "newyear_second": 10, "newyear_time": "2022-01-15 13:59:30", "is_gray": false, "tt_env": 3, "game_type": 3, "access_white_list": [2400027, 2197113, 2195879, 2188887, 2193840, 2189578, 2189795], "white_list_open": false, "all_broadcast_content": "等100位幸运儿在打年兽游戏中获得神仙眷侣、团圆、福气满满等限时大礼~", "record_gifts": ["131400T豆神仙眷侣", "52000T豆团圆", "10000T豆福气满满"], "lottery_result_item": [{"tbeansprice": 10000, "name": "福气满满"}, {"tbeansprice": 52000, "name": "团圆"}, {"tbeansprice": 131400, "name": "神仙眷侣"}], "period_list": [{"begintime": "2022-01-14 14:00:00", "endtime": "2022-01-15 02:00:00", "interval": 600}, {"begintime": "2022-01-15 09:00:00", "endtime": "2022-01-16 02:00:00", "interval": 600}, {"begintime": "2022-01-16 09:00:00", "endtime": "2022-01-17 02:00:00", "interval": 600}, {"begintime": "2022-01-17 09:00:00", "endtime": "2022-01-18 02:00:00", "interval": 600}, {"begintime": "2022-01-18 09:00:00", "endtime": "2022-01-19 23:00:00", "interval": 600}]}, "total_tbean_price": 800000, "feishu_chat_id": "oc_4c70fa2f3f8657f8107ed5018e93d410", "pkg_business_id": 164, "pkg_key": "bdpulqcdlupfrjgj", "RedisHash": [{"Name": "mole_redis_0", "CopyNum": 4, "Host": "**************", "Port": "6379", "Expire": 365}, {"Name": "mole_redis_1", "CopyNum": 4, "Host": "**************", "Port": "6379", "Expire": 365}], "RedisStat": [{"Name": "stat_redis1", "CopyNum": 4, "Host": "**************", "Port": "6379", "Expire": 365}, {"Name": "stat_redis2", "CopyNum": 4, "Host": "**************", "Port": "6379", "Expire": 365}, {"Name": "stat_redis3", "CopyNum": 4, "Host": "**************", "Port": "6379", "Expire": 365}, {"Name": "stat_redis4", "CopyNum": 4, "Host": "**************", "Port": "6379", "Expire": 365}, {"Name": "stat_redis5", "CopyNum": 4, "Host": "**************", "Port": "6379", "Expire": 365}], "KafkaConfig": {"brokers": "**************:9092,**************:9092,*************:9092", "topics": "molebeat_ch_info2", "group_id": "molebeat", "client_id": "molebeat"}, "SubKafkaConfig": {"brokers": "**************:9092,**************:9092,*************:9092", "topics": "molebeat_ch_info2", "group_id": "molebeat", "client_id": "molebeat"}, "server": {"listen": ":", "log_level": "debug", "name": "molebeat"}}