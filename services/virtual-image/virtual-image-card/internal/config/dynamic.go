package config

import (
    "gitlab.ttyuyin.com/avengers/tyr/core/config/ttconfig"
    "sync/atomic"
    "time"
)

type AboutToExpireCfg struct {
    Icon            string `json:"icon"`
    ExpireAlertTime uint32 `json:"expire_alert_time"` // 即将过期动画时间，单位
}

type DynamicConfig struct {
    PayApiConf                *PayApiConfig             `json:"pay_api_conf"`                  // 支付相关配置
    MaxOpenDays               uint32                    `json:"max_open_days"`                 // 最大开通天数
    MaxOpenDaysLimitHint      string                    `json:"max_open_days_limit_hint"`      // 最大开通天数限制提示
    NextPayPreNotifySecond    uint32                    `json:"next_pay_pre_notify_second"`    // 下次扣款提醒提前秒数
    PlaceDiscountOrderSecond  uint32                    `json:"place_discount_order_second"`   // 优惠订单间隔秒数
    ExpireAlertStatusKeepHour uint32                    `json:"expire_alert_status_keep_hour"` // 即将到期状态持续小时数
    IgnoreSandboxInStat       bool                      `json:"ignore_sandbox_in_stat"`        // 是否在统计中忽略沙箱数据
    CardWhiteUserList         []*CardWhiteUser          `json:"card_white_user_list"`          // 白名单用户列表
    CardWhiteUserMap          map[uint32]*CardWhiteUser `json:"-"`                             // 白名单用户map

    WaitToBuyIcon                   string              `json:"wait_to_buy_icon"`           // 从未购买/已过期购买图标
    AlreadyBuyIcon                  string              `json:"already_buy_icon"`           // 已购买图标
    AboutToExpireCfgList            []*AboutToExpireCfg `json:"about_to_expire_cfg_list"`   // 即将过期动画
    FirstEnterCardStoreUrl          string              `json:"first_enter_card_store_url"` // 首次进入无限卡商城弹窗url
    FirstEnterCardStoreMd5          string              `json:"first_enter_card_store_md5"` // 首次进入无限卡商城弹窗md5
    NDayShowOnce                    uint32              `json:"n_day_show_once"`            // N天内展示一次
    AdText                          string              `json:"ad_text"`                    // 广告文案
    CfgVersion                      uint32              `json:"cfg_version"`                // 配置版本号
    WaitToBuyBg                     string              `json:"wait_to_buy_bg"`             // 从未购买/已过期购买底图
    AlreadyBuyBg                    string              `json:"already_buy_bg"`             // 已购买底图
    AboutToExpireBg                 string              `json:"about_to_expire_bg"`         // 即将过期底图
    PcWaitToBuyBg                   string              `json:"pc_wait_to_buy_bg"`          // pc端从未购买/已过期购买底图
    PcAlreadyBuyBg                  string              `json:"pc_already_buy_bg"`          // pc端已购买底图
    PcAboutToExpireBg               string              `json:"pc_about_to_expire_bg"`      // pc端即将过期底图
    StoreResidentEntryIcon          string              `json:"store_resident_entry_icon"`  // 商城常驻入口图标
    StoreTabIconSelected            string              `json:"store_tab_icon_selected"`    // 商城【无限换装tab】选中图标
    StoreTabIconUnselected          string              `json:"store_tab_icon_unselected"`  // 商城【无限换装tab】未选中图标
    Switch                          bool                `json:"switch"`                     // 是否开启
    AdIndex                         uint32              `json:"ad_index"`                   // 广告位位置
    LowPriceText                    string              `json:"low_price_text"`
    WhiteList                       []uint32            `json:"white_list"` // 白名单
    WhiteListMap                    map[uint32]struct{} `json:"-"`
    FeiShuUrl                       string              `json:"fei_shu_url"`                           // 飞书告警地址
    CoinClientConfig                *CoinClientConfig   `json:"coin_client_config"`                    // coin链接配置
    IsAutoRepairIOSContractNextTime bool                `json:"is_auto_repair_ios_contract_next_time"` // 是否自动修复IOS下次扣款时间
    IsAutoRepairIOSContractStatus   bool                `json:"is_auto_repair_ios_contract_status"`    // 是否自动修复IOS签约状态
    NotStartIOSContractCheck        bool                `json:"not_start_ios_contract_check"`          // 是否不开启IOS签约状态检查
    UserGroupServerCfg              *UserGroupServerCfg `json:"user_group_server_cfg"`                 // 人群包服务配置
}

type UserGroupServerCfg struct {
    DspLpmApiserverHost    string `json:"dsp_lpm_apiserver_host"` // 人群包接口host
    DspLpmAdminHost        string `json:"dsp_lpm_admin_host"`
    DspLpmOfflineGroupHost string `json:"dsp_lpm_offline_group_host"`
}

type CardWhiteUser struct {
    Uid         uint32 `json:"uid"`          // 用户uid
    EffectTs    int64  `json:"effect_ts"`    // 生效时间
    ExpireTs    int64  `json:"expire_ts"`    // 过期时间
    HasContract bool   `json:"has_contract"` // 是否有签约
}

type PayApiConfig struct {
    PayApiHost                     string          `json:"pay_api_host"`                       // 支付后台域名
    PayApiClientID                 string          `json:"pay_api_client_id"`                  // 支付后台提供
    PayApiKey                      string          `json:"pay_api_key"`                        // 支付后台提供
    PayNotifyUrl                   string          `json:"pay_notify_url"`                     // 支付回调
    ContractNotifyUrl              string          `json:"contract_notify_url"`                // 签约回调
    PlaceOrderTimeoutMinute        uint32          `json:"place_order_timeout_minute"`         // 下单超时时间
    MarketConfList                 []*MarketConfig `json:"market_conf_list"`                   // 马甲包配置列表
    AccelerateNextPay              bool            `json:"accelerate_next_pay"`                // 是否加快下次扣款
    OpenSystemCancelContract       bool            `json:"open_system_cancel_contract"`        // 是否开启系统取消签约
    SystemCancelContractWaitSecond int64           `json:"system_cancel_contract_wait_second"` // 系统取消签约等待时间
    StopAutoOrderWaitSecond        int64           `json:"stop_auto_order_wait_second"`        // 停止自动续费等待时间
    IosAutoPayFailPushWaitSecond   int64           `json:"ios_auto_pay_fail_push_wait_second"` // IOS自动续费失败推送等待时间
}

type MarketConfig struct {
    MarketId         uint32 `json:"market_id"`
    FM               string `json:"fm"`
    BusinessID       string `json:"business_id"`
    PeriodBusinessID string `json:"period_business_id"`
}

type CoinClientConfig struct {
    ContextPath   string `json:"context_path"`
    BusinessCode  string `json:"business_code"`
    SecretKey     string `json:"secret_key"`
    CheckInterval uint32 `json:"check_interval"`
}

// Format
// 配置加载时调用Format报错则返回错误
// 配置更新时调用Format报错则放弃此次更新
func (s *DynamicConfig) Format() error {
    cardWhiteUserMap := make(map[uint32]*CardWhiteUser)
    for _, user := range s.CardWhiteUserList {
        cardWhiteUserMap[user.Uid] = user
    }
    s.CardWhiteUserMap = cardWhiteUserMap

    whiteListMap := make(map[uint32]struct{})
    for _, uid := range s.WhiteList {
        whiteListMap[uid] = struct{}{}
    }
    s.WhiteListMap = whiteListMap
    return nil
}

var (
    atomicDynamicConfig *atomic.Value
)

func init() {
    if err := InitDynamicConfig(); err != nil {
        panic(err)
    }
}

// InitDynamicConfig
// 可以选择外部初始化或者直接init函数初始化
func InitDynamicConfig() error {
    cfg := &DynamicConfig{}
    atomCfg, err := ttconfig.AtomLoad("virtual-image-card.json", cfg)
    if nil != err {
        return err
    }
    atomicDynamicConfig = atomCfg
    return nil
}

func GetDynamicConfig() *DynamicConfig {
    return atomicDynamicConfig.Load().(*DynamicConfig)
}

func GetMarketBusinessId(marketId uint32, isAuto bool) string {
    cfgList := GetDynamicConfig().PayApiConf.MarketConfList
    for _, cfg := range cfgList {
        if cfg.MarketId == marketId {
            bid := cfg.BusinessID
            if isAuto {
                bid = cfg.PeriodBusinessID
            }
            return bid
        }
    }
    return ""
}

func GetMarketFm(marketId uint32) string {
    cfgList := GetDynamicConfig().PayApiConf.MarketConfList
    for _, cfg := range cfgList {
        if cfg.MarketId == marketId {
            return cfg.FM
        }
    }
    return ""
}

func GetCardWhiteUser(uid uint32) *CardWhiteUser {
    userMap := GetDynamicConfig().CardWhiteUserMap
    return userMap[uid]
}

func GetAlipayNextPayTime(expireTime time.Time) time.Time {
    if GetDynamicConfig().PayApiConf.AccelerateNextPay {
        return time.Now().Add(time.Hour * 24 * 2)
    }
    return expireTime.Add(time.Hour * 24 * -3)
}

func CanSystemCancelContract(nextPayTime time.Time) bool {
    if !GetDynamicConfig().PayApiConf.OpenSystemCancelContract {
        return false
    }
    if time.Now().Sub(nextPayTime) < time.Second*time.Duration(GetDynamicConfig().PayApiConf.SystemCancelContractWaitSecond) {
        return false
    }
    return true
}
