package mgr

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
	"gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer/task/tasks"
	pb "golang.52tt.com/protocol/services/virtual-image-card"
	"golang.52tt.com/services/virtual-image/virtual-image-card/internal/config"
	"golang.52tt.com/services/virtual-image/virtual-image-card/internal/store"
	"time"

	"gitlab.ttyuyin.com/tt-infra/tyr/log"
)

func (m *Mgr) setupTimer() error {
	// 创建定时器
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()

	// 大小进程的定时器，同一个任务只会在一个节点上执行
	timerD, err := timer.NewTimerD(ctx, "virtual-image-card", timer.WithV8RedisCmdable(m.cache.GetRedisClient()), timer.WithTTL(10*time.Second))
	if err != nil {
		log.Errorf("setupTimer fail to NewTimerD. err: %v", err)
		return err
	}

	// 每天尝试创建月表
	_ = timerD.AddCronTask("0 0 6 * * *", "handleCreatePeriodTable", tasks.FuncTask(m.handleCreatePeriodTable))
	// 处理下次扣款
	timerD.AddIntervalTask("handleNextPay", time.Minute, tasks.FuncTask(m.handleNextPay))
	// 处理下次扣款提前通知
	timerD.AddIntervalTask("handleNextPayPreNotify", time.Minute, tasks.FuncTask(m.handleNextPayPreNotify))
	// 处理即将过期提醒
	timerD.AddIntervalTask("handleExpiringAlert", time.Minute, tasks.FuncTask(m.handleExpiringAlert))
	// 处理已过期通知
	timerD.AddIntervalTask("handleExpiredNotify", time.Minute, tasks.FuncTask(m.handleExpiredNotify))
	// 每月1号生成统计报表
	_ = timerD.AddCronTask("0 0 8 1 * *", "handleGenerateStat", tasks.FuncTask(m.handleGenerateStat))
	// 每天针对失效数据做清理
	_ = timerD.AddCronTask("0 0 7 * * *", "handleCleanExpiredData", tasks.FuncTask(m.handleCleanExpiredData))

	dyConfig := config.GetDynamicConfig()
	// 定时巡检IOS签约状态定时任务
	timerD.AddIntervalTask("CheckAllIOSContractStatus", time.Duration(dyConfig.CoinClientConfig.CheckInterval)*time.Second, tasks.FuncTask(func(ctx context.Context) {
		if dyConfig.NotStartIOSContractCheck {
			return
		}
		m.checkAllIOSContractStatus(ctx)
	}))

	timerD.Start()
	m.timerD = timerD
	return nil
}

func (m *Mgr) handleCreatePeriodTable(ctx context.Context) {
	ctx, cancel := context.WithTimeout(ctx, time.Minute)
	defer cancel()
	log.InfoWithCtx(ctx, "handleCreatePeriodTable")
	m.store.CreatePeriodTable(ctx)
}

func (m *Mgr) handleNextPay(ctx context.Context) {
	ctx, cancel := context.WithTimeout(ctx, time.Minute*30)
	defer cancel()
	now := time.Now()
	defer func() {
		log.InfoWithCtx(ctx, "handleNextPay cost: %v", time.Since(now))
	}()

	limit := uint32(100)
	nextId := uint32(0)
	hasMore := true
	var err error
	for hasMore {
		var contracts []*store.Contract
		contracts, nextId, hasMore, err = m.store.GetNextPayContractList(ctx, nextId, limit, now)
		if err != nil {
			log.ErrorWithCtx(ctx, "handleNextPay GetNextPayContractList error: %v", err)
			break
		}
		for _, contract := range contracts {
			if contract.PayChannel == uint8(pb.PayChannel_PAY_CHANNEL_ALIPAY) {
				m.PlaceAlipayAutoOrder(ctx, contract)
			}
			if contract.PayChannel == uint8(pb.PayChannel_PAY_CHANNEL_APPSTORE) {
				m.processPush4FailAppstoreAutoPay(ctx, contract)
			}
		}
	}
}

func (m *Mgr) handleNextPayPreNotify(ctx context.Context) {
	ctx, cancel := context.WithTimeout(ctx, time.Minute*30)
	defer cancel()
	now := time.Now()
	defer func() {
		log.InfoWithCtx(ctx, "handleNextPayPreNotify cost: %v", time.Since(now))
	}()

	preTime := now.Add(time.Second * time.Duration(config.GetDynamicConfig().NextPayPreNotifySecond))
	limit := uint32(100)
	nextId := uint32(0)
	hasMore := true
	var err error
	for hasMore {
		var contracts []*store.Contract
		contracts, nextId, hasMore, err = m.store.GetNextPayContractList(ctx, nextId, limit, preTime)
		if err != nil {
			log.ErrorWithCtx(ctx, "handleNextPayPreNotify GetNextPayContractList error: %v", err)
			break
		}
		for _, contract := range contracts {
			m.processPush4NextPayPreNotify(ctx, contract)
		}
	}
}

func (m *Mgr) handleExpiringAlert(ctx context.Context) {
	ctx, cancel := context.WithTimeout(ctx, time.Minute*30)
	defer cancel()
	now := time.Now()
	defer func() {
		log.InfoWithCtx(ctx, "handleExpiringAlert cost: %v", time.Since(now))
	}()

	limit := uint32(100)
	endTime := now.Add(time.Hour * time.Duration(config.GetDynamicConfig().ExpireAlertStatusKeepHour))
	for tableIdx := 0; tableIdx < store.CardRemainTblNum; tableIdx++ {
		nextId := uint32(0)
		hasMore := true
		var err error
		for hasMore {
			var remains []*store.Remain
			remains, nextId, hasMore, err = m.store.GetExpiringRemainList(ctx, uint32(tableIdx), nextId, limit, now, endTime)
			if err != nil {
				log.ErrorWithCtx(ctx, "handleExpiringAlert GetExpiringRemainList error: %v", err)
				break
			}

			for _, remain := range remains {
				m.processPush4ExpiringAlert(ctx, remain)
			}
		}
	}
}

func (m *Mgr) handleExpiredNotify(ctx context.Context) {
	ctx, cancel := context.WithTimeout(ctx, time.Minute*30)
	defer cancel()
	now := time.Now()
	defer func() {
		log.InfoWithCtx(ctx, "handleExpiredNotify cost: %v", time.Since(now))
	}()

	limit := uint32(5)
	shiftTime := now.Add(-time.Second * 5) // 偏移几秒，确保后面处理时真的过期了
	for i := 0; i < 1000; i++ {            // 避免死循环
		uidList, err := m.cache.GetExpiredUserList(ctx, shiftTime, limit)
		if err != nil {
			log.ErrorWithCtx(ctx, "handleExpiredNotify GetExpiredUserList error: %v", err)
			break
		}
		for _, uid := range uidList {
			m.processPush4ExpiredNotify(ctx, uid)
		}
		if len(uidList) < int(limit) {
			break
		}
	}
}

func (m *Mgr) handleGenerateStat(ctx context.Context) {
	now := time.Now()
	endTime := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
	startTime := time.Date(now.Year(), now.Month()-1, 1, 0, 0, 0, 0, time.Local)
	m.GenerateStat(ctx, startTime, endTime)
}

func (m *Mgr) handleCleanExpiredData(ctx context.Context) {
	ctx, cancel := context.WithTimeout(ctx, time.Minute*30)
	defer cancel()
	now := time.Now()
	defer func() {
		log.InfoWithCtx(ctx, "handleCleanExpiredData cost: %v", time.Since(now))
	}()

	m.store.CleanInitContract(ctx, time.Hour*24*7)
	m.store.CleanCanceledContract(ctx, time.Hour*24*30)
	m.store.CleanExpiredOrderRedemption(ctx, time.Hour*24*90)
}

func transCoinContractStatus(status string) uint8 {
	switch status {
	case "SIGN_CONTRACT", "SPSYS_SIGN_CONTRACT":
		return store.ContractStatusSigned
	case "RESCIND_CONTRACT", "SPSYS_RESCIND_CONTRACT":
		return store.ContractStatusCanceled
	default:
		return store.ContractStatusInit
	}
}

// checkAllIOSContractStatus 检查所有IOS签约状态
func (m *Mgr) checkAllIOSContractStatus(ctx context.Context) {
	ctx, cancel := context.WithTimeout(ctx, time.Hour)
	defer cancel()
	now := time.Now()
	defer func() {
		log.InfoWithCtx(ctx, "checkAllIOSContractStatus cost: %v", time.Since(now))
	}()

	dyConf := config.GetDynamicConfig()
	isAutoNextTime, isAutoStatus := dyConf.IsAutoRepairIOSContractNextTime, dyConf.IsAutoRepairIOSContractStatus
	limit, offset := 1000, 0 // 分批检查

	for {
		// 获取所有IOS签约用户
		contractList, err := m.store.GetContractsByPayChannel(ctx, uint32(pb.PayChannel_PAY_CHANNEL_APPSTORE), uint32(limit), uint32(offset))
		if err != nil {
			log.ErrorWithCtx(ctx, "checkAllIOSContractStatus GetIOSContractList err:%v", err)
			return
		}

		// 循环检查每一个签约信息
		for _, contract := range contractList {
			m.checkIOSContract(ctx, contract, isAutoNextTime, isAutoStatus)
		}

		offset = offset + limit
		if len(contractList) < limit {
			break
		}
	}
}

func (m *Mgr) checkIOSContract(ctx context.Context, contract *store.Contract, isAutoNextTime, isAutoStatus bool) {
	ctx, cancel := context.WithTimeout(ctx, time.Second*30)
	defer cancel()
	if contract.MarketId == 6 || contract.Uid == 0 {
		return
	}

	// 查询货币签约状态
	data, err := m.rpcCli.CoinCli.SignQuery(ctx, contract.ContractId, contract.Uid, uint32(contract.MarketId))
	if err != nil {
		log.ErrorWithCtx(ctx, "%s checkIOSContract SignQuery err:%v", contract.ContractId, err)
		return
	}

	hasContractInfo := false
	for _, coinContractInfo := range data {
		if coinContractInfo.CliContractId != contract.ContractId { // 合约ID不一致，跳过
			continue
		}
		hasContractInfo = true

		coinStatus := transCoinContractStatus(coinContractInfo.Status)
		if coinStatus != contract.Status { // 状态不一致，告警
			if coinStatus == store.ContractStatusInit && contract.Status != store.ContractStatusSigned { // 初始状态跳过
				continue
			}
			m.SendFeiShuMsg("IOS签约状态不一致", []string{
				fmt.Sprintf("contract_id: %s", contract.ContractId),
				fmt.Sprintf("uid: %d", contract.Uid),
				fmt.Sprintf("系统状态: %d", contract.Status),
				fmt.Sprintf("货币状态: %d, %s", coinStatus, coinContractInfo.Status),
			}, "")

			if isAutoStatus { // 自动修复
				_, err = m.store.UpdateContractStatus(ctx, contract.ContractId, coinStatus, contract.Status, nil)
				if err != nil {
					log.ErrorWithCtx(ctx, "%s checkIOSContract UpdateContractStatus err:%v", contract.ContractId, err)
				} else {
					_, _ = m.store.AddContractHistory(ctx, &store.ContractHistory{
						ContractId:    contract.ContractId,
						Uid:           contract.Uid,
						PayChannel:    contract.PayChannel,
						PackageId:     contract.PackageId,
						PackageInfo:   contract.PackageInfo,
						Status:        coinStatus,
						OperationRole: store.ContractHistoryOperationRoleSystem,
						Reason:        "巡检修正",
						CreateTime:    time.Now(),
					}, nil)
				}
				_ = m.cache.DelUserCard(ctx, contract.Uid) // 删除缓存
			}
		}

		if len(coinContractInfo.NextExecuteTime) == 0 {
			continue
		}
		nextTime, err := time.ParseInLocation("2006-01-02 15:04:05", coinContractInfo.NextExecuteTime, time.Local)
		if err != nil {
			log.ErrorWithCtx(ctx, "%s checkIOSContract ParseTimeFromString err:%v", contract.ContractId, err)
			continue
		}
		if !nextTime.Equal(contract.NextPayTime) { // 下次扣款时间不一致，告警
			m.SendFeiShuMsg("IOS签约下次扣款时间不一致", []string{
				fmt.Sprintf("contract_id: %s", contract.ContractId),
				fmt.Sprintf("uid: %d", contract.Uid),
				fmt.Sprintf("系统扣款时间: %s", contract.NextPayTime),
				fmt.Sprintf("货币扣款时间: %s", coinContractInfo.NextExecuteTime),
			}, "")

			if isAutoNextTime { // 自动修复
				_, err := m.store.UpdateContractNextPayTime(ctx, contract.ContractId, nextTime, nil)
				if err != nil {
					log.ErrorWithCtx(ctx, "%s checkIOSContract UpdateContractNextPayTime err:%v", contract.ContractId, err)
				}
				_ = m.cache.DelUserCard(ctx, contract.Uid) // 删除缓存
			}
		}
	}

	if !hasContractInfo && contract.Status == store.ContractStatusSigned { // 不存在签约信息，但当前还签约状态，飞书告警
		// 检查用户是否已经注销签约
		signCancellation, err := m.rpcCli.CoinCli.CheckSignCancellation(ctx, contract.Uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "%s checkIOSContract CheckSignCancellation err:%v", contract.ContractId, err)
			return
		}
		if signCancellation { // 如果已经注销了就不需要告警了
			if isAutoStatus { // 自动修复
				_, err := m.store.UpdateContractStatus(ctx, contract.ContractId, store.ContractStatusCanceled, contract.Status, nil)
				if err != nil {
					log.ErrorWithCtx(ctx, "%s checkIOSContract UpdateContractStatus err:%v", contract.ContractId, err)
				} else {
					_, _ = m.store.AddContractHistory(ctx, &store.ContractHistory{
						ContractId:    contract.ContractId,
						Uid:           contract.Uid,
						PayChannel:    contract.PayChannel,
						PackageId:     contract.PackageId,
						PackageInfo:   contract.PackageInfo,
						Status:        store.ContractStatusCanceled,
						OperationRole: store.ContractHistoryOperationRoleSystem,
						Reason:        "巡检检测到已注销",
						CreateTime:    time.Now(),
					}, nil)
				}
				_ = m.cache.DelUserCard(ctx, contract.Uid) // 删除缓存
			}
			return
		}

		m.SendFeiShuMsg("货币接口IOS签约信息不存在", []string{
			fmt.Sprintf("contract_id: %s", contract.ContractId),
			fmt.Sprintf("uid: %d", contract.Uid),
		}, "")
	}
}
