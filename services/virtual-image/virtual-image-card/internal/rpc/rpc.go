package rpc

import (
    "context"
    "errors"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/clients/account"
    "golang.52tt.com/clients/expsvr"
    imApi "golang.52tt.com/clients/im-api"
    "golang.52tt.com/clients/nobility"
    numeric "golang.52tt.com/clients/numeric-go"
    push_notification "golang.52tt.com/clients/push-notification/v2"
    "golang.52tt.com/services/tt-rev/esport/common/user_group"
    coinapi "golang.52tt.com/services/virtual-image/virtual-image-card/internal/coin-api"
    "golang.52tt.com/services/virtual-image/virtual-image-card/internal/config"
    "golang.52tt.com/services/virtual-image/virtual-image-card/internal/pay_api"
)

type Client struct {
    AccountCli   account.IClient
    ImApiCli     imApi.IClient
    PushCli      push_notification.IClient
    PayCli       *pay_api.Client
    CoinCli      coinapi.CoinClient
    UserGroupCli *user_group.UserGroupCli
    ExpCli       *expsvr.Client
    NobilityCli  *nobility.Client
    NumericCli   numeric.IClient
}

func NewClient() (*Client, error) {
    var err error
    client := &Client{}

    client.PushCli = push_notification.NewIClient()
    client.AccountCli = account.NewIClient()
    client.ImApiCli, _ = imApi.NewClient()

    payConf := config.GetDynamicConfig().PayApiConf
    client.PayCli = pay_api.NewClient(payConf.PayApiHost, payConf.PayApiClientID, payConf.PayApiKey)

    coinConf := config.GetDynamicConfig().CoinClientConfig
    if coinConf == nil {
        log.Errorf("coinConf is nil")
        return nil, errors.New("coinConf is nil")
    }

    client.CoinCli, err = coinapi.NewCoinClient(coinConf.ContextPath, coinConf.BusinessCode, coinConf.SecretKey)
    if err != nil {
        log.Errorf("NewCoinClient failed, coinConf:%+v, err: %v", coinConf, err)
        return nil, err
    }

    ExpCli := expsvr.NewClient()
    NumericCli := numeric.NewIClient()

    NobilityCli, err := nobility.NewClient()
    if err != nil {
        log.ErrorWithCtx(context.Background(), "nobility.NewClient() failed err:%v", err)
        return nil, err
    }

    userGroupServerCfg := config.GetDynamicConfig().UserGroupServerCfg
    if userGroupServerCfg != nil {
        userGroupCli := user_group.NewUserGroupCli(
            userGroupServerCfg.DspLpmAdminHost,
            userGroupServerCfg.DspLpmOfflineGroupHost,
            userGroupServerCfg.DspLpmApiserverHost)
        client.UserGroupCli = userGroupCli
    }

    client.ExpCli = ExpCli
    client.NobilityCli = NobilityCli
    client.NumericCli = NumericCli
    return client, nil
}
