package lark_mgr

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"github.com/coocood/freecache"
	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkcore "github.com/larksuite/oapi-sdk-go/v3/core"
	larkbitable "github.com/larksuite/oapi-sdk-go/v3/service/bitable/v1"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/virtual-image/virtual-image-resource/internal/conf"
	"io/ioutil"
	"net/http"
)

// Response 是最外层的响应结构体
type Response struct {
	Fields   Fields `json:"Fields"`
	RecordId string `json:"_"`
}

// Fields 包含记录的字段
type Fields struct {
	SuiteIDItemCode SuiteIDItemCode `json:"套装ID/物品码"`
	Progress        string          `json:"进度"`
	ItemIdList      []SuiteIDItem   `json:"物品ID"`
}

// SuiteIDItemCode 是套装ID/物品码的具体内容
type SuiteIDItemCode struct {
	Type  int           `json:"type"`
	Value []SuiteIDItem `json:"value"`
}

// SuiteIDItem 是套装ID/物品码的具体项
type SuiteIDItem struct {
	Text string `json:"text"`
	Type string `json:"type"`
}

type LarkMgr struct {
	larkClient *lark.Client
	AppID      string
	AppSecret  string
	AppToken   string
	TableId    string
	ViewId     string
	localCache freecache.Cache
}

type IMgr interface {
	SearchRecordByFieldNameAndValue(ctx context.Context, fieldName, fieldValue string) ([]Response, error)
	UpdateSheetByRecordID(ctx context.Context, recordId string, fieldName, fieldValues string) error
}

func NewLarkMgr(cfg *conf.StartConfig) *LarkMgr {
	return &LarkMgr{
		larkClient: lark.NewClient(cfg.AppID, cfg.AppSecret),
		AppID:      cfg.AppID,
		AppSecret:  cfg.AppSecret,
		localCache: *freecache.NewCache(1024 * 1024), // 1MB cache
		AppToken:   cfg.AppToken,
		TableId:    cfg.TableId,
		ViewId:     cfg.ViewId,
	}
}

func (mgr *LarkMgr) SearchRecordByFieldNameAndValue(ctx context.Context, fieldName, fieldValue string) ([]Response, error) {
	records := make([]Response, 0)
	token, _ := mgr.genAccessToken()
	// 创建请求对象
	req := larkbitable.NewSearchAppTableRecordReqBuilder().
		AppToken(mgr.AppToken).
		TableId(mgr.TableId).
		UserIdType(`open_id`).
		PageSize(10).
		Body(larkbitable.NewSearchAppTableRecordReqBodyBuilder().
			ViewId(mgr.ViewId).
			Filter(larkbitable.NewFilterInfoBuilder().
				Conjunction(`and`).
				Conditions([]*larkbitable.Condition{
					larkbitable.NewConditionBuilder().
						FieldName(fieldName).
						Operator(`is`).
						Value([]string{fieldValue}).
						Build(),
				}).
				Build()).
			AutomaticFields(false).
			Sort([]*larkbitable.Sort{
				larkbitable.NewSortBuilder().
					FieldName(fieldName).
					Desc(true).
					Build(),
			}).
			Build()).
		Build()

	// 发起请求
	resp, err := mgr.larkClient.Bitable.V1.AppTableRecord.Search(ctx, req, larkcore.WithTenantAccessToken(token))
	if err != nil {
		log.ErrorWithCtx(ctx, "SearchRecordByFieldNameAndValue failed", err)
		return records, err
	}

	// 服务端错误处理
	if !resp.Success() {
		log.ErrorWithCtx(ctx, "SearchRecordByFieldNameAndValue failed : %s, error response: %s", resp.RequestId(), larkcore.Prettify(resp.CodeError))
		return records, err
	}

	for _, item := range resp.Data.Items {
		jsonData, err := json.Marshal(item)
		var itemField Response
		err = json.Unmarshal(jsonData, &itemField)
		if err != nil {
			fmt.Printf("Error Unmarshal processing item:%s - err:%v\n", larkcore.Prettify(item), err)
			return records, err
		}

		itemField.RecordId = *item.RecordId
		records = append(records, itemField)
	}
	return records, nil
}

func (mgr *LarkMgr) UpdateSheetByRecordID(ctx context.Context, recordId string, fieldName, fieldValues string) error {
	token, _ := mgr.genAccessToken()
	req := larkbitable.NewUpdateAppTableRecordReqBuilder().
		AppToken(mgr.AppToken).
		TableId(mgr.TableId).
		RecordId(recordId).
		UserIdType(`open_id`).
		AppTableRecord(larkbitable.NewAppTableRecordBuilder().
			Fields(map[string]interface{}{fieldName: fieldValues}).
			Build()).
		Build()

	//Fields example:
	//Fields(map[string]interface{}{`复选框`: true, `电话号码`: `13026162666`, `索引`: `索引列文本类型`, `货币`: 3, `进度`: 0.25, `人员`: []interface{}{}, `双向关联`: []interface{}{}, `文本`: `文本内容`, `单选`: `选项3`, `超链接`: map[string]interface{}{`text`: `飞书多维表格官网`, `link`: `https://www.feishu.cn/product/base`}, `附件`: []interface{}{}, `单向关联`: []interface{}{}, `地理位置`: `116.397755,39.903179`, `条码`: `qawqe`, `多选`: []interface{}{}, `日期`: 1674206443000, `群组`: []interface{}{}, `数字`: 100, `评分`: 3}).

	// 发起请求
	resp, err := mgr.larkClient.Bitable.V1.AppTableRecord.Update(ctx, req, larkcore.WithTenantAccessToken(token))
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateSheetByRecordID failed", err)
		return err
	}

	// 服务端错误处理
	if !resp.Success() {
		log.ErrorWithCtx(ctx, "UpdateSheetByRecordID failed : %s, error response: %s \n", resp.RequestId(), larkcore.Prettify(resp))
		return err
	}
	log.DebugWithCtx(ctx, "UpdateSheetByRecordID success record:%s, fieldName:%s, value:%s", recordId, fieldName, fieldValues)
	return nil
}

func (mgr *LarkMgr) GetSheetDataByPageToken(ctx context.Context, pageToken string) (dataList []Response, nextPageToken string, err error) {
	out := make([]Response, 0)
	token, _ := mgr.genAccessToken()

	// 创建请求对象
	req := larkbitable.NewSearchAppTableRecordReqBuilder().
		AppToken(mgr.AppToken).
		TableId(mgr.TableId).
		UserIdType(`open_id`).
		PageSize(500).
		PageToken(pageToken).
		Body(larkbitable.NewSearchAppTableRecordReqBodyBuilder().
			ViewId(mgr.ViewId).
			FieldNames([]string{`套装ID/物品码`, `进度`, `物品ID`}).
			AutomaticFields(false).
			Sort([]*larkbitable.Sort{
				larkbitable.NewSortBuilder().
					FieldName(`套装ID/物品码`).
					Desc(true).
					Build(),
			}).
			Build()).
		Build()

	// 发起请求
	resp, err := mgr.larkClient.Bitable.V1.AppTableRecord.Search(context.Background(), req, larkcore.WithTenantAccessToken(token))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetSheetDataByPageToken failed", err)
		return out, "", err
	}

	// 服务端错误处理
	if !resp.Success() {
		log.ErrorWithCtx(ctx, "GetSheetDataByPageToken failed : %s, error response: %s", resp.RequestId(), larkcore.Prettify(resp.CodeError))
		return out, "", err
	}

	for _, item := range resp.Data.Items {
		jsonData, err := json.Marshal(item)
		var itemField Response
		err = json.Unmarshal(jsonData, &itemField)
		if err != nil {
			fmt.Printf("Error Unmarshal processing item:%s - err:%v\n", larkcore.Prettify(item), err)
			return out, "", err
		}

		if len(itemField.Fields.SuiteIDItemCode.Value) == 0 {
			continue
		}
		itemField.RecordId = *item.RecordId
		out = append(out, itemField)
	}

	if !*resp.Data.HasMore {
		return out, "", nil
	}

	return out, *resp.Data.PageToken, nil
}

const (
	apiURL = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
)

type TokenRequest struct {
	AppID     string `json:"app_id"`
	AppSecret string `json:"app_secret"`
}
type TokenResponse struct {
	Code              int    `json:"code"`
	Msg               string `json:"msg"`
	TenantAccessToken string `json:"tenant_access_token"`
	Expire            int    `json:"expire"`
}

func (mgr *LarkMgr) genAccessToken() (string, error) {
	cacheToken, err := mgr.localCache.Get([]byte("tenant_access_token"))
	if err == nil {
		return string(cacheToken), nil
	}

	requestBody := TokenRequest{
		AppID:     mgr.AppID,
		AppSecret: mgr.AppSecret,
	}
	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		log.ErrorWithCtx(context.Background(), "genAccessToken failed", err)
		return "", err
	}
	// 发送 HTTP POST 请求
	resp, err := http.Post(apiURL, "application/json; charset=utf-8", bytes.NewBuffer(jsonBody))
	if err != nil {
		log.ErrorWithCtx(context.Background(), "Error making HTTP request: %v\n", err)
		return "", err
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.ErrorWithCtx(context.Background(), "Error reading response body: %v\n", err)
		return "", err
	}
	// 解析响应
	var tokenResponse TokenResponse
	err = json.Unmarshal(body, &tokenResponse)
	if err != nil {
		log.ErrorWithCtx(context.Background(), "Error unmarshalling response: %v\n", err)
		return "", err
	}
	// 检查响应状态
	if tokenResponse.Code != 0 {
		log.ErrorWithCtx(context.Background(), "Failed to get tenant_access_token: %s\n", tokenResponse.Msg)
		return "", err
	}
	log.InfoWithCtx(context.Background(), "Successfully got tenant_access_token: %s", tokenResponse.TenantAccessToken)
	mgr.localCache.Set([]byte("tenant_access_token"), []byte(tokenResponse.TenantAccessToken), tokenResponse.Expire)
	return tokenResponse.TenantAccessToken, nil
}
