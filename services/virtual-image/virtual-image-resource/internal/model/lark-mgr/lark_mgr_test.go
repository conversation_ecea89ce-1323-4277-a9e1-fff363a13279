package lark_mgr

import (
	"context"
	"fmt"
	"golang.52tt.com/services/virtual-image/virtual-image-resource/internal/conf"
	"testing"
)

func TestNewLarkMgr(t *testing.T) {
	larkMgr := NewLarkMgr(&conf.StartConfig{
		AppID:     "cli_a4fefd83137ad00e",
		AppSecret: "4Bm9YuHeqQAAb9z1lPbDVcLnQKQd1Jgt",
		AppToken:  "K3PObLjETapgK4stQrLcgsoqnCb",
		TableId:   "tblj74HoTF67OXG9",
		ViewId:    "vewgtDnaZ4",
	})
	records, err := larkMgr.SearchRecordByFieldNameAndValue(context.Background(), "物品ID", "7")
	if err != nil {
		fmt.Println(err)
		return
	}

	for _, record := range records {
		fmt.Println(record.RecordId, record.Fields.Progress, record.Fields.SuiteIDItemCode.Value[0].Text, record.Fields.ItemIdList[0].Text)
	}

}
