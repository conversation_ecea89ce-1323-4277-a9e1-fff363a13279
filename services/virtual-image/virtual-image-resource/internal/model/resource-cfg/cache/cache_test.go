package cache

import (
	"context"
	"github.com/alicebob/miniredis/v2"
	redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"strconv"
)

var cacheCli ICache

func init() {
	log.SetLevel(log.DebugLevel)
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}

	// 连接mock的redis server
	port, _ := strconv.ParseInt(s.Port(), 10, 32)
	redisClient, _ := redisConnect.NewClient(context.Background(), &redisConnect.RedisConfig{
		Host: s.Host(),
		Port: uint32(port),
	})

	cacheCli = &Cache{cmder: redisClient}
}
