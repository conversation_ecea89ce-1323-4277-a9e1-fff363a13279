package cache

import (
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
)

//go:generate quicksilver-cli test interface ../cache
//go:generate mockgen -destination=../mocks/cache.go -package=mocks golang.52tt.com/services/virtual-image-resource/internal/model/resource-cfg/cache ICache

const (
	VACfgKey = "virtual_avatar_cfg"
)

type Cache struct {
	cmder redis.Cmdable
}

func NewCache(client redis.Cmdable) *Cache {
	c := &Cache{
		cmder: client,
	}
	return c
}

func (c *Cache) Close() error {
	return c.cmder.(redis.Client).Close()
}

func (c *Cache) GetRedisClient() redis.Cmdable {
	return c.cmder
}
