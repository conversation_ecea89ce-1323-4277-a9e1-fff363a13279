package store

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/log"
	virtualImageResourcePb "golang.52tt.com/protocol/services/virtual-image-resource"
	"time"
)

type VirtualImageLevelConfig struct {
	ID         uint32    `db:"id"`                             // 资源ID[自增ID]
	Level      uint32    `db:"level" json:"level"`             //等级
	LevelIcon  string    `db:"level_icon" json:"level_icon"`   //等级标识
	LevelWebp  string    `db:"level_webp" json:"level_webp"`   //商品图标特效
	UpdateTime time.Time `db:"update_time" json:"update_time"` //更新时间
}

func (s *Store) createTableLevelConfig(ctx context.Context) error {
	createTableSQL := `
	CREATE TABLE IF NOT EXISTS virtual_image_level_config (
		id INT AUTO_INCREMENT PRIMARY KEY,             -- 资源ID[自增ID]
		level INT DEFAULT 0,                           -- 等级
		level_icon VARCHAR(255),                       -- 等级图标
		level_webp VARCHAR(255),                       -- 商品图标特效
		update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,                               -- 更新时间[资源版本号]
		UNIQUE KEY level (level),
 		INDEX idx_update_time (update_time)
	);`

	_, err := s.db.Exec(createTableSQL)
	if err != nil {
		fmt.Println("Error creating table:", err)
		return err
	}
	return nil
}

func (s *Store) GetLevelConfig(ctx context.Context) ([]*VirtualImageLevelConfig, error) {
	levelConfig := make([]*VirtualImageLevelConfig, 0)
	err := s.db.SelectContext(ctx, &levelConfig, "SELECT * FROM virtual_image_level_config")
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLevelConfig: %w", err)
		return levelConfig, err
	}
	return levelConfig, nil
}

func (s *Store) UpdateLevelConfig(ctx context.Context, cfg *virtualImageResourcePb.LevelConfig) error {
	_, err := s.db.ExecContext(ctx, "UPDATE virtual_image_level_config SET level_icon = ?, level_webp = ? WHERE level = ?", cfg.GetLevelIcon(), cfg.GetLevelWebp(), cfg.GetLevel())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateLevelConfig: %w", err)
		return err
	}
	return err
}

func (s *Store) AddLevelConfig(ctx context.Context, cfg *virtualImageResourcePb.LevelConfig) error {
	_, err := s.db.ExecContext(ctx, "INSERT INTO virtual_image_level_config (level, level_icon, level_webp) VALUES (?, ?, ?)", cfg.Level, cfg.LevelIcon, cfg.LevelWebp)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddLevelConfig: %w", err)
		return err
	}
	return err
}
