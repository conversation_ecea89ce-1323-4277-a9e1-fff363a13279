package store

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
)

func setupTestStoreCDN(t *testing.T) (*Store, func()) {
	mysqlConfig := &mysqlConnect.MysqlConfig{
		Host:     "*************", // 根据需要修改为你的测试数据库地址
		Port:     3306,
		Database: "appsvr",
		Charset:  "utf8",
		UserName: "godman",
		Password: "thegodofman",
	}

	dbCli, err := mysqlConnect.NewClient(context.Background(), mysqlConfig)
	if err != nil {
		panic(err)
	}

	store := &Store{db: dbCli}

	// 清空表数据
	_, err = dbCli.Exec("DELETE FROM virtual_image_resource_cdn")
	require.NoError(t, err)

	// 创建表
	err = store.createTableCDN(context.Background())
	require.NoError(t, err)

	return store, func() {
		dbCli.Close()
	}
}

func TestAddCDNFileRecord(t *testing.T) {
	store, teardown := setupTestStoreCDN(t)
	defer teardown()

	url := "https://example.com/resource.zip"
	md5 := "abc123xyz"
	version := int64(1)

	err := store.AddCDNFileRecord(context.Background(), url, md5, version)
	assert.NoError(t, err)

	var count int
	err = store.db.Get(&count, "SELECT COUNT(*) FROM virtual_image_resource_cdn WHERE url = ?", url)
	require.NoError(t, err)
	assert.Equal(t, 1, count)
}

func TestGetCDNFileRecord(t *testing.T) {
	store, teardown := setupTestStoreCDN(t)
	defer teardown()

	// 插入测试数据
	url := "https://example.com/resource.zip"
	md5 := "abc123xyz"
	version := int64(1)

	_, err := store.db.ExecContext(context.Background(),
		"INSERT INTO virtual_image_resource_cdn (url, md5, version) VALUES (?, ?, ?)",
		url, md5, version)
	require.NoError(t, err)

	// 调用方法
	record, err := store.GetCDNFileRecord(context.Background())

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, url, record.Url)
	assert.Equal(t, md5, record.Md5)
	assert.Equal(t, version, record.Version)
}

func TestDelCDNFileRecord(t *testing.T) {
	store, teardown := setupTestStoreCDN(t)
	defer teardown()

	// 插入测试数据
	url := "https://example.com/resource.zip"
	md5 := "abc123xyz"
	version := int64(1)

	result, err := store.db.ExecContext(context.Background(),
		"INSERT INTO virtual_image_resource_cdn (url, md5, version) VALUES (?, ?, ?)",
		url, md5, version)
	require.NoError(t, err)

	id, err := result.LastInsertId()
	require.NoError(t, err)

	// 删除记录
	err = store.DelCDNFileRecord(context.Background(), uint32(id))
	assert.NoError(t, err)

	// 检查是否还存在
	var count int
	err = store.db.Get(&count, "SELECT COUNT(*) FROM virtual_image_resource_cdn WHERE id = ?", id)
	require.NoError(t, err)
	assert.Equal(t, 0, count)
}
