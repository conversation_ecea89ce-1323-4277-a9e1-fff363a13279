package store

import(
	context "context"
	virtualImageResourcePb "golang.52tt.com/protocol/services/virtual-image-resource"
)

type IStore interface {
	AddAndroidVirtualImageResourceInfo(ctx context.Context, list []*VirtualImageResourceInfo) error
	AddCDNFileRecord(ctx context.Context, url, md5 string, version int64) error
	AddLevelConfig(ctx context.Context, cfg *virtualImageResourcePb.LevelConfig) error
	CloneVirtualImageResource(ctx context.Context, list []*VirtualImageResourceInfo) error
	Close() error
	DelCDNFileRecord(ctx context.Context, id uint32) error
	DeleteVirtualImageResource(ctx context.Context, id uint32) error
	GetCDNFileRecord(ctx context.Context) (VirtualImageResourceInfoCDN,error)
	GetLevelConfig(ctx context.Context) ([]*VirtualImageLevelConfig,error)
	GetMaxUpdateTime(ctx context.Context) (int64,error)
	GetMaxVersion(ctx context.Context) (uint32,error)
	GetVirtualImageParentCategory(ctx context.Context) ([]*ParentCategoryInfo,error)
	GetVirtualImageResourceIdByResourceName(ctx context.Context, name string) (uint32,error)
	GetVirtualImageResourceInfoCount(ctx context.Context) (int64,error)
	GetVirtualImageSubCategory(ctx context.Context, parentCategoryId []uint32) ([]*SubCategoryInfo,error)
	InsertResourceIdRecord(ctx context.Context, id uint32, name string) (uint32,error)
	SearchVirtualImageResource(ctx context.Context, request *virtualImageResourcePb.SearchVirtualImageResourceRequest) ([]VirtualImageResourceInfo,error)
	SearchVirtualImageResourceCount(ctx context.Context, request *virtualImageResourcePb.SearchVirtualImageResourceRequest) (uint32,error)
	SetVirtualImageResourceForSale(ctx context.Context, idList []uint32) error
	UpdateIconByID(ctx context.Context, request *virtualImageResourcePb.VirtualImageResourceInfo) error
	UpdateLevelConfig(ctx context.Context, cfg *virtualImageResourcePb.LevelConfig) error
	UpdateVirtualImageResource(ctx context.Context, request *virtualImageResourcePb.VirtualImageResourceInfo) error
	UpdateVirtualImageResourceCustomMap(ctx context.Context, info *VirtualImageResourceInfo) error
	UpdateVirtualImageResourceStatus(ctx context.Context, idList []uint32, status uint32) error
	UpdateVirtualImageResourceUrl(ctx context.Context, request *virtualImageResourcePb.UpdateVirtualImageResourceUrlRequest) error
}


type IParentCategoryInfo interface {
	SqlString() string
	String() string
	TableName() string
}


type ISubCategoryInfo interface {
	SqlString() string
	String() string
	TableName() string
}

