package store

import (
	"context"
	"encoding/json"
	"fmt"
	mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
	pb "golang.52tt.com/protocol/services/virtual-image-resource"
	"golang.52tt.com/services/virtual-image/virtual-image-resource/internal/utils"
	"testing"
	"time"
)

var testStore *Store
var testUid = uint32(1)

func init() {
	mysqlConfig := &mysqlConnect.MysqlConfig{
		Host:     "*************", //"*************"  "**********"
		Port:     3306,
		Database: "appsvr",
		Charset:  "utf8",
		UserName: "godman",
		Password: "thegodofman",
	}

	dbCli, err := mysqlConnect.NewClient(context.Background(), mysqlConfig)
	if err != nil {
		return
	}

	testStore = NewStore(dbCli)
}

func TestStore_SearchVirtualImageResource(t *testing.T) {
	var offset uint32
	limit := uint32(5)

	count, err := testStore.SearchVirtualImageResourceCount(context.Background(), &pb.SearchVirtualImageResourceRequest{
		Offset: 0,
		Limit:  10,
	})
	fmt.Println("SearchVirtualImageResourceCount --------------size ", count, err)
	totalCount, err := testStore.GetVirtualImageResourceInfoCount(context.Background())
	fmt.Println("GetVirtualImageResourceInfoCount size ", totalCount, err)
	getCount := 0
	for offset < uint32(totalCount) {

		res, err := testStore.SearchVirtualImageResource(context.Background(), &pb.SearchVirtualImageResourceRequest{
			Offset: offset,
			Limit:  limit,
		})
		offset += limit
		getCount += len(res)
		fmt.Println("SearchVirtualImageResource LLLLLLLLLLLL ", getCount, len(res), err)
	}

}

func stringToCustomMap(customMapStr string) map[string]string {
	res := make(map[string]string)
	if customMapStr == "" {
		return nil
	}
	err := json.Unmarshal([]byte(customMapStr), &res)
	if err != nil {
		return nil
	}
	return res
}

func TestNewStore(t *testing.T) {
	testStore.createTable(context.Background())

	var list []*VirtualImageResourceInfo

	for i := 1; i < 2; i++ {

		list = append(list, &VirtualImageResourceInfo{
			ID:             uint32(i),
			SkinName:       fmt.Sprintf("Body/hand/jiaquanzhang%d", time.Now().Unix()),
			ResourceURL:    "www.baidu.com55555555",
			ResourceName:   fmt.Sprintf("Body/hand/jiaquanzhang%d", i),
			Essential:      true,
			Category:       1,
			ResourceType:   1000,
			Level:          1,
			SubCategory:    1,
			DefaultSuit:    fmt.Sprintf("default>>>%d", time.Now().Unix()),
			LevelIcon:      "levelIcon111",
			MD5:            "md5555555555555",
			Status:         1,
			DisplayName:    "displayName11",
			Sex:            1,
			EncryptKey:     "encryptKey",
			SplineVersion:  "splineVersion",
			IconURL:        "www.baidu.com IconURL",
			IosResourceURL: "iosurl",
			IosMD5:         "iosmd5",
			IsDeleted:      true,
			CustomMap:      utils.CustomMapToString(map[string]string{"ios_015": "http://ios_015.png"}),
			SkinMap: utils.SkinMapToString(map[string]*pb.SkinInfo{"ios_015": {
				Url:             "ios_015",
				Md5:             "ios_015",
				MinBonesVersion: 1,
			}}),
			IsNew: true,
		})
	}

	err := testStore.AddVirtualImageResourceInfo(context.Background(), list)
	fmt.Println("AddVirtualImageResourceInfo err", err)

	err = testStore.CloneVirtualImageResource(context.Background(), list)
	fmt.Println("CloneVirtualImageResource err", err)
	updatePB := &VirtualImageResourceInfo{}
	updatePB.ResourceName = "Body/hand/jiaquanzhang30"
	updatePB.CustomMap = utils.CustomMapToString(map[string]string{"ios_015": "http://ios_01111111115.png"})

	err = testStore.UpdateVirtualImageResourceCustomMap(context.Background(), updatePB)
	fmt.Println("UpdateVirtualImageResourceCustomMap err", err)

	res, err := testStore.SearchVirtualImageResource(context.Background(), &pb.SearchVirtualImageResourceRequest{
		Offset:           0,
		Limit:            10,
		ResourceNameList: []string{"Body/hand/jiaquanzhang30", "Body/hand/jiaquanzhang32"},
	})
	for _, v := range res {
		fmt.Println(v.ID, v.CustomMap, err)
	}
	fmt.Println("SearchVirtualImageResource LLLLLLLLLLLL ", len(res), err)

	maxTime, err := testStore.GetMaxUpdateTime(context.Background())
	fmt.Println(maxTime, err)

	testStore.SetVirtualImageResourceForSale(context.Background(), []uint32{24, 25})

	clientList, err := testStore.SearchVirtualImageResource(context.Background(), &pb.SearchVirtualImageResourceRequest{
		Offset:   0,
		Limit:    2,
		SkinName: "Body/hand/jiaquanzhang10",
	})
	for _, v := range clientList {
		fmt.Println(v, err)
	}

}

func TestStore_AddLevelConfig(t *testing.T) {
	testStore.AddLevelConfig(context.Background(), &pb.LevelConfig{
		Level:     2,
		LevelIcon: "levelIcon2",
		LevelWebp: "levelWebp2",
	})

	testStore.UpdateLevelConfig(context.Background(), &pb.LevelConfig{
		Level:     2,
		LevelIcon: "levelIcon22TT",
		LevelWebp: "levelWebp22TT",
	})
	list, err := testStore.GetLevelConfig(context.Background())
	for _, v := range list {
		fmt.Println(v, err)
	}

	testStore.UpdateVirtualImageResourceUrl(context.Background(), &pb.UpdateVirtualImageResourceUrlRequest{
		SkinName:    "Body/hand/jiaquanzhang1",
		ResourceUrl: "www.tt.com111111111111",
		EncryptKey:  "encryptKey111111111111",
		Md5:         "asdfadfasd",
	})
}

func TestStore_AddVirtualImageResourceInfo(t *testing.T) {
	totalCount, err := testStore.SearchVirtualImageResourceCount(context.Background(), &pb.SearchVirtualImageResourceRequest{})
	if err != nil {
		fmt.Println("SearchVirtualImageResourceCount err", err)
	}

	fmt.Println("SearchVirtualImageResourceCount --------------size ", totalCount, err)
	for offset := 0; offset < int(100); offset += 100 {
		res, _ := testStore.SearchVirtualImageResource(context.Background(), &pb.SearchVirtualImageResourceRequest{
			Offset: uint32(offset),
			Limit:  100,
		})

		for _, v := range res {
			lastId, err := testStore.GetVirtualImageResourceIdByResourceName(context.Background(), v.ResourceName)
			//testStore.InsertResourceIdRecord(context.Background(), v.ID, v.ResourceName)
			fmt.Println("InsertResourceIdRecord LLLLLLLLLLLL ", lastId, v.ResourceName, err)
		}
	}

	lastId, err := testStore.GetVirtualImageResourceIdByResourceName(context.Background(), "aaaaaa")
	fmt.Println("GetVirtualImageResourceIdByResourceName 122222222=== ", lastId, err)
}

func TestStore_GetVirtualImageResourceInfoList(t *testing.T) {
	source := &pb.VirtualImageResourceInfo{
		ResourceName: "Body/hand/jiaquanzhang151",
		CustomMap:    map[string]string{"ios_015": "http://ios_015.png"},
	}

	err := testStore.UpdateVirtualImageResourceUrl(context.Background(), &pb.UpdateVirtualImageResourceUrlRequest{
		Resources: []*pb.VirtualImageResourceInfo{source},
	})

	fmt.Println("UpdateVirtualImageResourceUrl err>>>>>>>>>>>>>>", err)
}

func TestStore_GetVirtualImageResourceInfo(t *testing.T) {
	err := testStore.AddCDNFileRecord(context.Background(), fmt.Sprintf("http://ios_011115_%d.png", time.Now().Unix()), "mdddd", time.Now().Unix())
	if err != nil {
		fmt.Println("AddCDNFileRecord err>>>>>>>>>>>>>>", err)
	}

	record, err := testStore.GetCDNFileRecord(context.Background())

	fmt.Println("GetCDNFileRecord err>>>>>>>>>>>>>>", record, err)
}
