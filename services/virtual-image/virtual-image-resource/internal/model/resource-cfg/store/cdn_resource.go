package store

import (
	"database/sql"
	"errors"
	"golang.52tt.com/pkg/log"
	"time"

	"context"
)

// VirtualImageResourceInfoCDN 虚拟形像资源下载信息
type VirtualImageResourceInfoCDN struct {
	ID         uint32    `db:"id"`          // 资源ID[自增ID]
	Url        string    `db:"url"`         // 资源名称
	Md5        string    `db:"md5"`         // md5
	Version    int64     `db:"version"`     // 版本号
	CreateTime time.Time `db:"create_time"` // 创建时间
}

func (s *Store) AddCDNFileRecord(ctx context.Context, url, md5 string, version int64) error {
	_, err := s.db.ExecContext(ctx, "INSERT INTO virtual_image_resource_cdn (url, md5, version) VALUES (?, ?, ?)",
		url, md5, version)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddCDNFileRecord: %w", err)
		return err
	}
	log.InfoWithCtx(ctx, "AddCDNFileRecord: url=%s, md5=%s, version=%d", url, md5, version)
	return nil
}

func (s *Store) GetCDNFileRecord(ctx context.Context) (VirtualImageResourceInfoCDN, error) {
	var resource VirtualImageResourceInfoCDN
	err := s.db.GetContext(ctx, &resource, "SELECT * FROM virtual_image_resource_cdn order by id desc limit 1")
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			log.ErrorWithCtx(ctx, "GetCDNFileRecord no file: %w", err)
			return resource, nil
		}
		log.ErrorWithCtx(ctx, "GetCDNFileRecord: %w", err)
		return resource, err
	}
	return resource, nil
}

func (s *Store) DelCDNFileRecord(ctx context.Context, id uint32) error {
	_, err := s.db.ExecContext(ctx, "delete from virtual_image_resource_cdn where id = ?", id)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelCDNFileRecord: %w", err)
		return err
	}
	log.InfoWithCtx(ctx, "DelCDNFileRecord id =%d", id)
	return nil
}
