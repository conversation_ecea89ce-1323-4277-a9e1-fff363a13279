package store

import (
	"context"
	mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
	virtualImageResourcePb "golang.52tt.com/protocol/services/virtual-image-resource"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func setupTestStore(t *testing.T) (*Store, func()) {
	mysqlConfig := &mysqlConnect.MysqlConfig{
		Host:     "*************", //"*************"  "**********"
		Port:     3306,
		Database: "appsvr",
		Charset:  "utf8",
		UserName: "godman",
		Password: "thegodofman",
	}

	dbCli, err := mysqlConnect.NewClient(context.Background(), mysqlConfig)
	if err != nil {
		panic(err)
	}

	store := &Store{db: dbCli}

	// 清空表数据
	_, err = dbCli.Exec("DELETE FROM virtual_image_level_config")
	require.NoError(t, err)

	// 创建表
	err = store.createTableLevelConfig(context.Background())
	require.NoError(t, err)

	return store, func() {
		dbCli.Close()
	}
}

func TestGetLevelConfig(t *testing.T) {
	store, teardown := setupTestStore(t)
	defer teardown()

	// 插入测试数据
	_, err := store.db.Exec("INSERT INTO virtual_image_level_config (level, level_icon, level_webp) VALUES (1, 'icon1.png', 'webp1.webp'), (2, 'icon2.png', 'webp2.webp')")
	require.NoError(t, err)

	// 调用方法
	configs, err := store.GetLevelConfig(context.Background())

	// 验证结果
	assert.NoError(t, err)
	assert.Len(t, configs, 2)
	assert.Equal(t, uint32(1), configs[0].Level)
	assert.Equal(t, "icon1.png", configs[0].LevelIcon)
	assert.Equal(t, "webp1.webp", configs[0].LevelWebp)
	assert.Equal(t, uint32(2), configs[1].Level)
	assert.Equal(t, "icon2.png", configs[1].LevelIcon)
	assert.Equal(t, "webp2.webp", configs[1].LevelWebp)
}

func TestUpdateLevelConfig(t *testing.T) {
	store, teardown := setupTestStore(t)
	defer teardown()

	// 插入初始数据
	_, err := store.db.Exec("INSERT INTO virtual_image_level_config (level, level_icon, level_webp) VALUES (1, 'old_icon.png', 'old_webp.webp')")
	require.NoError(t, err)

	// 准备更新的数据
	cfg := &virtualImageResourcePb.LevelConfig{
		Level:     1,
		LevelIcon: "new_icon.png",
		LevelWebp: "new_webp.webp",
	}

	// 执行更新
	err = store.UpdateLevelConfig(context.Background(), cfg)
	assert.NoError(t, err)

	// 查询验证
	var updated VirtualImageLevelConfig
	err = store.db.Get(&updated, "SELECT * FROM virtual_image_level_config WHERE level = 1")
	require.NoError(t, err)

	assert.Equal(t, "new_icon.png", updated.LevelIcon)
	assert.Equal(t, "new_webp.webp", updated.LevelWebp)
}

func TestAddLevelConfig(t *testing.T) {
	store, teardown := setupTestStore(t)
	defer teardown()

	// 准备新增的数据
	cfg := &virtualImageResourcePb.LevelConfig{
		Level:     3,
		LevelIcon: "test_icon.png",
		LevelWebp: "test_webp.webp",
	}

	// 执行添加
	err := store.AddLevelConfig(context.Background(), cfg)
	assert.NoError(t, err)

	// 查询验证
	var added VirtualImageLevelConfig
	err = store.db.Get(&added, "SELECT * FROM virtual_image_level_config WHERE level = 3")
	require.NoError(t, err)

	assert.Equal(t, uint32(3), added.Level)
	assert.Equal(t, "test_icon.png", added.LevelIcon)
	assert.Equal(t, "test_webp.webp", added.LevelWebp)
}
