package store

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/jmoiron/sqlx"
	"golang.52tt.com/pkg/foundation/utils"
	"golang.52tt.com/pkg/log"

	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
)

type ParentCategoryInfo struct {
	CategoryId   uint32 `db:"category_id" json:"category_id"`     //分类id
	CategoryName string `db:"category_name" json:"category_name"` //分类名称
	CategoryType uint32 `db:"category_type" json:"category_type"` //分类类型
	Rank         uint32 `db:"c_rank" json:"c_rank"`               //排序
}

func (t *ParentCategoryInfo) String() string {
	return utils.ToJson(t)
}
func (t *ParentCategoryInfo) SqlString() string {
	data, _ := json.Marshal(t)
	log.Debugf("GetVirtualImageParentCategory error: %v sql:%v", data)
	return utils.ToSqlStr(t)
}
func (t *ParentCategoryInfo) TableName() string {
	return tblParentCategoryInfo
}

type SubCategoryInfo struct {
	CategoryId               uint32 `db:"category_id" json:"category_id"`                                   //分类id
	CategoryName             string `db:"category_name" json:"category_name"`                               //分类名称
	CategoryImgUrl           string `db:"category_img_url" json:"category_img_url"`                         //分类图片
	CategoryImgPreviewUrl    string `db:"category_img_preview_url" json:"category_img_preview_url"`         //分类预览图片
	WebCategoryImgUrl        string `db:"web_category_img_url" json:"web_category_img_url"`                 //分类图片
	WebCategoryImgPreviewUrl string `db:"web_category_img_preview_url" json:"web_category_img_preview_url"` //分类预览图片
	CategoryType             uint32 `db:"category_type" json:"category_type"`                               //分类类型
	ParentId                 uint32 `db:"parent_id" json:"parent_id"`                                       //父级分类id
	Rank                     uint32 `db:"c_rank" json:"c_rank"`                                             //排序
}

func (t *SubCategoryInfo) String() string {
	return utils.ToJson(t)
}

func (t *SubCategoryInfo) SqlString() string {
	return utils.ToSqlStr(t)
}

func (t *SubCategoryInfo) TableName() string {
	return tblSubCategoryInfo
}

func (s *Store) createTableCategory(ctx context.Context) error {
	createTableSQL := `CREATE TABLE IF NOT EXISTS tbl_parent_category_info (
		id INT AUTO_INCREMENT PRIMARY KEY COMMENT "自增 ", 
		category_id INT NOT NULL DEFAULT 0  COMMENT "品类id",
		category_name VARCHAR(255) DEFAULT "" COMMENT "品类名称",
    	category_type INT NOT NULL DEFAULT 0  COMMENT "品类类型",
    	c_rank INT NOT NULL DEFAULT 0 COMMENT "排序",
		create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间", 
	    update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT "更新时间"
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT '品类表';`

	_, err := s.db.Exec(createTableSQL)
	if err != nil {
		log.ErrorWithCtx(ctx, "Error creating table:%v", err)
		return err
	}

	createTableSQL = `CREATE TABLE IF NOT EXISTS tbl_sub_category_info (
    	id INT AUTO_INCREMENT PRIMARY KEY COMMENT "自增 ", 
		category_id INT NOT NULL DEFAULT 0  COMMENT "子品类id",
		parent_id INT NOT NULL DEFAULT 0 COMMENT "父级品类id",
		category_name VARCHAR(255) DEFAULT "" COMMENT "品类名称",
		category_type INT NOT NULL DEFAULT 0  COMMENT "品类类型",
    	category_img_url VARCHAR(255) DEFAULT "" COMMENT "品类图片",
    	c_rank INT NOT NULL DEFAULT 0 COMMENT "排序",
    	category_img_preview_url VARCHAR(255) DEFAULT "" COMMENT "品类预览图片",
    	web_category_img_url VARCHAR(255) DEFAULT "" COMMENT "web端品类图片",
    	web_category_img_preview_url VARCHAR(255) DEFAULT "" COMMENT "web端品类预览图片",
		create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间", 
	    update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT "更新时间",
	     KEY idx_parent_id (parent_id)
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT '子品类表';`

	_, err = s.db.Exec(createTableSQL)
	if err != nil {
		log.ErrorWithCtx(ctx, "Error creating table:%v", err)
		return err
	}
	return nil
}

func (s *Store) GetVirtualImageParentCategory(ctx context.Context) ([]*ParentCategoryInfo, error) {
	log.DebugWithCtx(ctx, "GetVirtualImageParentCategory ")
	categoryList := make([]*ParentCategoryInfo, 0)
	info := ParentCategoryInfo{}
	tableName := info.TableName()
	sql := fmt.Sprintf("select %s from %+v", info.SqlString(), tableName)
	inQuery, args, err := sqlx.In(sql)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetVirtualImageParentCategory In fail, err :%v", err)
		return categoryList, nil
	}
	log.ErrorWithCtx(ctx, "GetVirtualImageParentCategory error: %v sql:%v", err, sql)
	err = s.db.SelectContext(ctx, &categoryList, inQuery, args...)
	if err != nil {
		if mysql.IsNoRowsError(err) {
			log.DebugWithCtx(ctx, "GetVirtualImageParentCategory no data pidList: %v", categoryList)
			return categoryList, nil
		}
		log.ErrorWithCtx(ctx, "GetVirtualImageParentCategory error: %v sql:%v", err, sql)
		return nil, err
	}

	return categoryList, nil

}

func (s *Store) GetVirtualImageSubCategory(ctx context.Context, parentCategoryId []uint32) ([]*SubCategoryInfo, error) {
	log.DebugWithCtx(ctx, "GetVirtualImageSubCategory parentCategoryId:%v", parentCategoryId)
	categoryList := make([]*SubCategoryInfo, 0)
	info := SubCategoryInfo{}
	tableName := info.TableName()
	sql := fmt.Sprintf("select %s from %+v where `parent_id` in (?) ", info.SqlString(), tableName)
	inQuery, args, err := sqlx.In(sql, parentCategoryId)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetCommodityData In fail, err :%v", err)
		return categoryList, nil
	}
	err = s.db.SelectContext(ctx, &categoryList, inQuery, args...)
	if err != nil {
		if mysql.IsNoRowsError(err) {
			log.DebugWithCtx(ctx, "BatchGetCommodityData no data pidList: %v", categoryList)
			return categoryList, nil
		}
		log.ErrorWithCtx(ctx, "BatchGetCommodityData error: %v", err)
		return nil, err
	}
	log.DebugWithCtx(ctx, "GetVirtualImageSubCategory categoryList:%v", categoryList)

	return categoryList, nil
}
