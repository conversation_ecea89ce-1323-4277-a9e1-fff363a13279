package resource_cfg

import (
	context "context"
	virtual_image_resource "golang.52tt.com/protocol/services/virtual-image-resource"
	time "time"
)

type IMgr interface {
	AddLevelConfig(ctx context.Context, request *virtual_image_resource.LevelConfig) error
	AddVirtualImageResource(ctx context.Context, request *virtual_image_resource.AddVirtualImageResourceRequest) (*virtual_image_resource.AddVirtualImageResourceResponse, error)
	BatchUpdateIcon(ctx context.Context, request *virtual_image_resource.BatchUpdateIconRequest) error
	CloneVirtualImageResource(ctx context.Context, request *virtual_image_resource.CloneVirtualImageResourceRequest) (*virtual_image_resource.CloneVirtualImageResourceResponse, error)
	DeleteVirtualImageResource(ctx context.Context, request *virtual_image_resource.DeleteVirtualImageResourceRequest) error
	EditVirtualImageResource(ctx context.Context, request *virtual_image_resource.EditVirtualImageResourceRequest) (*virtual_image_resource.EditVirtualImageResourceResponse, error)
	GetClientListByPage(ctx context.Context, request *virtual_image_resource.GetClientListByPageRequest) (*virtual_image_resource.GetClientListByPageResponse, error)
	GetDefaultResourceList(ctx context.Context) (*virtual_image_resource.GetDefaultResourceListResponse, error)
	GetLevelConfig(ctx context.Context) (*virtual_image_resource.GetLevelConfigResponse, error)
	GetVirtualImageResourceBySuit(ctx context.Context) (*virtual_image_resource.GetVirtualImageResourceBySuitResponse, error)
	GetVirtualImageResourceCategory(ctx context.Context, req *virtual_image_resource.GetVirtualImageResourceCategoryRequest) (*virtual_image_resource.GetVirtualImageResourceCategoryResponse, error)
	GetVirtualImageResourcesByIds(ctx context.Context, request *virtual_image_resource.GetVirtualImageResourcesByIdsRequest) (*virtual_image_resource.GetVirtualImageResourcesByIdsResponse, error)
	SearchVirtualImageResource(ctx context.Context, request *virtual_image_resource.SearchVirtualImageResourceRequest) (*virtual_image_resource.SearchVirtualImageResourceResponse, error)
	SetVirtualImageResourceForSale(ctx context.Context, idList []uint32) error
	StartTimer()
	Stop()
	TimerHandle(d time.Duration, handle func() error)
	UpdateLevelConfig(ctx context.Context, request *virtual_image_resource.LevelConfig) error
	GetActionResourceMap(ctx context.Context) (*virtual_image_resource.GetActionResourceMapResponse, error)
	UpdateVirtualImageResourceUrl(ctx context.Context, request *virtual_image_resource.UpdateVirtualImageResourceUrlRequest) (*virtual_image_resource.UpdateVirtualImageResourceUrlResponse, error)
}
