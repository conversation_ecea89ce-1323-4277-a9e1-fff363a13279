package main

import (
	"context"
	"encoding/json"
	"fmt"
	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkcore "github.com/larksuite/oapi-sdk-go/v3/core"
	larkbitable "github.com/larksuite/oapi-sdk-go/v3/service/bitable/v1"
)

type LarkMgr struct {
	larkClient  *lark.Client
	AccessToken string
	AppToken    string
	TableId     string
	ViewId      string
}

func NewLarkMgr() *LarkMgr {
	token, err := genAccessToken()
	if err != nil {
		fmt.Println("NewLarkMgr gen token err:", err)
		return nil
	}

	return &LarkMgr{
		larkClient:  lark.NewClient(AppID, AppSecret),
		AccessToken: token,
		AppToken:    AppToken,
		TableId:     TableId,
		ViewId:      ViewId,
	}
}

func (mgr *LarkMgr) genAccessToken() string {
	token, _ := genAccessToken()
	return token
}
func (mgr *LarkMgr) SearchRecordByFieldNameAndValue(ctx context.Context, fieldName, fieldValue string) string {
	// 创建请求对象
	req := larkbitable.NewSearchAppTableRecordReqBuilder().
		AppToken(mgr.AppToken).
		TableId(mgr.TableId).
		UserIdType(`open_id`).
		PageSize(10).
		Body(larkbitable.NewSearchAppTableRecordReqBodyBuilder().
			ViewId(mgr.ViewId).
			Filter(larkbitable.NewFilterInfoBuilder().
				Conjunction(`and`).
				Conditions([]*larkbitable.Condition{
					larkbitable.NewConditionBuilder().
						FieldName(fieldName).
						Operator(`is`).
						Value([]string{fieldValue}).
						Build(),
				}).
				Build()).
			AutomaticFields(false).
			Sort([]*larkbitable.Sort{
				larkbitable.NewSortBuilder().
					FieldName(fieldName).
					Desc(true).
					Build(),
			}).
			Build()).
		Build()

	// 发起请求
	resp, err := mgr.larkClient.Bitable.V1.AppTableRecord.Search(ctx, req, larkcore.WithTenantAccessToken(mgr.genAccessToken()))
	if err != nil {
		fmt.Println(err)
		return ""
	}

	// 服务端错误处理
	if !resp.Success() {
		fmt.Printf("logId: %s, error response: \n%s", resp.RequestId(), larkcore.Prettify(resp.CodeError))
		return ""
	}

	for _, item := range resp.Data.Items {
		return *item.RecordId
	}
	return ""
}

func (mgr *LarkMgr) UpdateSheetByRecordID(ctx context.Context, recordId string, fieldName, fieldValues string) error {
	req := larkbitable.NewUpdateAppTableRecordReqBuilder().
		AppToken(mgr.AppToken).
		TableId(mgr.TableId).
		RecordId(recordId).
		UserIdType(`open_id`).
		AppTableRecord(larkbitable.NewAppTableRecordBuilder().
			Fields(map[string]interface{}{fieldName: fieldValues}).
			Build()).
		Build()

	//Fields example:
	//Fields(map[string]interface{}{`复选框`: true, `电话号码`: `13026162666`, `索引`: `索引列文本类型`, `货币`: 3, `进度`: 0.25, `人员`: []interface{}{}, `双向关联`: []interface{}{}, `文本`: `文本内容`, `单选`: `选项3`, `超链接`: map[string]interface{}{`text`: `飞书多维表格官网`, `link`: `https://www.feishu.cn/product/base`}, `附件`: []interface{}{}, `单向关联`: []interface{}{}, `地理位置`: `116.397755,39.903179`, `条码`: `qawqe`, `多选`: []interface{}{}, `日期`: 1674206443000, `群组`: []interface{}{}, `数字`: 100, `评分`: 3}).

	// 发起请求
	resp, err := mgr.larkClient.Bitable.V1.AppTableRecord.Update(ctx, req, larkcore.WithTenantAccessToken(mgr.AccessToken))
	if err != nil {
		fmt.Println("UpdateSheetByRecordID failed", err)
		return err
	}

	// 服务端错误处理
	if !resp.Success() {
		fmt.Printf("UpdateSheetByRecordID failed : %s, error response: %s \n", resp.RequestId(), larkcore.Prettify(resp))
		return err
	}
	fmt.Println("UpdateSheetByRecordID success", resp.Data)
	return nil
}

func (mgr *LarkMgr) GetSheetDataByPageToken(pageToken string) (dataList []Response, token string, err error) {
	out := make([]Response, 0)

	// 创建请求对象
	req := larkbitable.NewSearchAppTableRecordReqBuilder().
		AppToken(mgr.AppToken).
		TableId(mgr.TableId).
		UserIdType(`open_id`).
		PageSize(500).
		PageToken(pageToken).
		Body(larkbitable.NewSearchAppTableRecordReqBodyBuilder().
			ViewId(mgr.ViewId).
			FieldNames([]string{`套装ID/物品码`, `进度`, `物品ID`}).
			AutomaticFields(false).
			Sort([]*larkbitable.Sort{
				larkbitable.NewSortBuilder().
					FieldName(`套装ID/物品码`).
					Desc(true).
					Build(),
			}).
			Build()).
		Build()

	// 发起请求
	resp, err := mgr.larkClient.Bitable.V1.AppTableRecord.Search(context.Background(), req, larkcore.WithTenantAccessToken(mgr.genAccessToken()))
	if err != nil {
		fmt.Println(err)
		return out, "", err
	}

	// 服务端错误处理
	if !resp.Success() {
		fmt.Printf("logId: %s, error response: \n%s", resp.RequestId(), larkcore.Prettify(resp.CodeError))
		return out, "", err
	}

	for _, item := range resp.Data.Items {
		jsonData, err := json.Marshal(item)
		var itemField Response
		err = json.Unmarshal(jsonData, &itemField)
		if err != nil {
			fmt.Printf("Error Unmarshal processing item:%s - err:%v\n", larkcore.Prettify(item), err)
			return out, "", err
		}

		if len(itemField.Fields.SuiteIDItemCode.Value) == 0 {
			continue
		}
		itemField.RecordId = *item.RecordId
		out = append(out, itemField)
	}

	if !*resp.Data.HasMore {
		return out, "", nil
	}

	return out, *resp.Data.PageToken, nil
}
