package conf

//go:generate quicksilver-cli test interface ../conf
//go:generate mockgen -destination=./mocks/business_config.go -package=mocks golang.52tt.com/services/virtual-image/virtual-image-resource/internal/conf IBusinessConfManager

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"strings"
	"sync"
	"time"

	"gitlab.ttyuyin.com/tt-infra/tyr/log"
)

const (
	BusinessConfPath = "/data/oss/conf-center/tt/"
	BusinessConfFile = "virtual-image-resource.json"
)

var LastConfMd5Sum [md5.Size]byte

type BusinessConf struct {
	MapPathToCategory map[string]uint32 `json:"map_path_to_category"`
	AndroidMinVersion uint32            `json:"android_min_version"`
}

func (c *BusinessConf) Parse(configFile string) (isChange bool, err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("Failed to parse config: %v \n", e)
		}
	}()

	data, err := ioutil.ReadFile(configFile)
	if err != nil {
		return false, err
	}

	md5Sum := md5.Sum(data)
	if md5Sum == LastConfMd5Sum {
		isChange = false
		return
	}

	err = json.Unmarshal(data, &c)
	if err != nil {
		return false, err
	}

	err = c.CheckConf()
	if err != nil {
		return false, err
	}

	LastConfMd5Sum = md5Sum

	log.Infof("BusinessConf : %+v", c)
	return true, nil
}

type BusinessConfManager struct {
	Done chan interface{}

	mutex sync.RWMutex
	conf  *BusinessConf
}

func (bm *BusinessConfManager) GetMapPathToCategory(path, subPath string) (uint32, uint32) {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()
	category, ok := bm.conf.MapPathToCategory[path]
	if !ok {
		log.Errorf("GetMapPathToCategory fail. path:%s, subPath:%s", path, subPath)
		return 0, 0
	}

	var subCategory uint32
	for k, _ := range bm.conf.MapPathToCategory {
		if strings.HasPrefix(subPath, k) {
			subCategory = bm.conf.MapPathToCategory[k]
			break
		}
	}
	return category, subCategory
}

func (bm *BusinessConfManager) GetAndroidMinVersion() uint32 {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()
	return bm.conf.AndroidMinVersion
}
func NewBusinessConfManager() (*BusinessConfManager, error) {
	businessConf := &BusinessConf{}

	businessConfFilePath := BusinessConfPath + BusinessConfFile
	if devBusinessConfPath := os.Getenv("DEV_BUSINESS_CONF_PATH"); devBusinessConfPath != "" {
		businessConfFilePath = devBusinessConfPath + BusinessConfFile
	}
	_, err := businessConf.Parse(businessConfFilePath)
	if err != nil {
		log.Errorf("NewBusinessConfManager fail to Parse BusinessConf err:%v", err)
		return nil, err
	}

	confMgr := &BusinessConfManager{
		conf: businessConf,
		Done: make(chan interface{}),
	}

	go confMgr.Watch(businessConfFilePath)

	return confMgr, nil
}

func (bm *BusinessConfManager) Reload(file string) error {
	businessConf := &BusinessConf{}

	isChange, err := businessConf.Parse(file)
	if err != nil {
		log.Errorf("NewBusinessConfManager fail to Parse BusinessConf err:%v", err)
		return err
	}

	if isChange {
		bm.mutex.Lock()
		bm.conf = businessConf
		bm.mutex.Unlock()

		log.Infof("Reload %+v", businessConf)
	}

	return nil
}

func (bm *BusinessConfManager) Watch(file string) {
	log.Infof("Watch start. file:%s", file)

	for {
		select {
		case _, ok := <-bm.Done:
			if !ok {
				log.Infof("Watch done")
				return
			}

		case <-time.After(30 * time.Second):
			log.Debugf("Watch check change")

			err := bm.Reload(file)
			if err != nil {
				log.Errorf("Watch Reload fail. file:%s, err:%v", file, err)
			}
		}
	}
}

func (bm *BusinessConfManager) Close() {
	close(bm.Done)
}

func (c *BusinessConf) CheckConf() error {
	return nil
}
