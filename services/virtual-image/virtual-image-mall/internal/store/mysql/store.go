package mysqlStore

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/jmoiron/sqlx"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	"golang.52tt.com/pkg/foundation/utils"
	pb "golang.52tt.com/protocol/services/virtual-image-mall"
	"golang.52tt.com/services/virtual-image/virtual-image-mall/internal/conf"
)

const (
	tblCommodityData        = "tbl_commodity_data"         //商品数据表
	tblCommodityRecommend   = "tbl_commodity_recommend"    //商品推荐表
	tblCommodityDataPackage = "tbl_commodity_data_package" //商品套餐表
	tblCommodityDataOrders  = "tbl_commodity_data_orders"  //商品订单表

	CommodityCategory_Suit = 1 //套装
)

const (
	CommodityShelfStatus_On_OR_Furture = 1 // 正在生效或未来生效
)

type ResourceIdListDb struct {
	ResourceIdList []uint32 `json:"resource_id_list"`
}

type CommodityData struct {
	CommodityId         uint32          `db:"commodity_id" json:"commodity_id"`                   //商品id
	CommodityName       string          `db:"commodity_name" json:"commodity_name"`               //商品名称
	Category            uint32          `db:"category" json:"category"`                           //商品品类大类 see CommodityCategory_*
	SubCategory         uint32          `db:"sub_category" json:"sub_category"`                   //商品品类子类
	ResourceIdList      json.RawMessage `db:"resource_id_list" json:"resource_id_list"`           //资源id列表
	CommodityType       uint32          `db:"commodity_type" json:"commodity_type"`               //商品类型 1:单品 2:套装
	ShelfTime           time.Time       `db:"shelf_time" json:"shelf_time"`                       //上架时间
	ExpireTime          time.Time       `db:"expire_time" json:"expire_time"`                     //下架时间
	ExpireTimeShow      bool            `db:"expire_time_show" json:"expire_time_show"`           //下架时间展示
	GainPath            uint32          `db:"gain_path" json:"gain_path"`                         //获取途径 1:商店 2:活动
	Rank                uint32          `db:"c_rank" json:"c_rank"`                               //排序
	CreateTime          time.Time       `db:"create_time" json:"create_time"`                     //创建时间
	UpdateTime          time.Time       `db:"update_time" json:"update_time"`                     //更新时间
	Level               uint32          `db:"c_level" json:"c_level"`                             //等级
	LevelIcon           string          `db:"level_icon" json:"level_icon"`                       //等级图标
	ResourceSex         uint32          `db:"sex" json:"sex"`                                     //资源性别 1:男 2:女 3:通用
	CustomizeLogotype   string          `db:"customize_logotype" json:"customize_logotype"`       //自定义标识
	CustomizeShelfTime  time.Time       `db:"customize_shelf_time" json:"customize_shelf_time"`   //自定义标识上架时间
	CustomizeExpireTime time.Time       `db:"customize_expire_time" json:"customize_expire_time"` //自定义标识下架时间
	CommodityIcon       string          `db:"commodity_icon" json:"commodity_icon"`               //商品图标
	CommodityAnimation  string          `db:"commodity_animation" json:"commodity_animation"`     //商品动画
	ActivityInfo        string          `db:"activity_info" json:"activity_info"`                 //活动信息
	SpineAnimation      string          `db:"spine_animation" json:"spine_animation"`             //spine动画
	RedDotVersion       uint32          `db:"red_dot_version" json:"red_dot_version"`             //红点版本
	ProVideoId          uint32          `db:"pro_video_id" json:"pro_video_id"`                   //宣传片id
}

func (t *CommodityData) String() string {
	return utils.ToJson(t)
}
func (t *CommodityData) SqlString() string {
	return utils.ToSqlStr(t)
}
func (t *CommodityData) TableName() string {
	return tblCommodityData
}

func (t *CommodityData) JoinSqlString() string {
	return utils.ToTblCommodityDataSqlStr(t)
}

func (t *CommodityData) HaseSameResourceIdList(otherResourceIdList []uint32) bool {
	var resourceIdList []uint32
	if len(otherResourceIdList) == 0 || len(t.ResourceIdList) == 0 {
		return false
	}

	err := json.Unmarshal(t.ResourceIdList, &resourceIdList)
	if err != nil {
		log.ErrorWithCtx(context.Background(), "CommodityData.HaseSameResourceId json.Unmarshal resourceIdList:%v err:%v", t.ResourceIdList, err)
		return false
	}
	return slicesEqual(resourceIdList, otherResourceIdList)
}

func slicesEqual(a, b []uint32) bool {
	if len(a) != len(b) {
		return false
	}
	for i := range a {
		if a[i] != b[i] {
			return false
		}
	}
	return true
}

type CommodityRecommend struct {
	Id          uint32    `db:"id" json:"id"`                     //自增id
	CommodityId uint32    `db:"commodity_id" json:"commodity_id"` //商品id
	Rank        uint32    `db:"c_rank" json:"c_rank"`             //排序
	CreateTime  time.Time `db:"create_time" json:"create_time"`   //创建时间
	UpdateTime  time.Time `db:"update_time" json:"update_time"`   //更新时间
}

func (t *CommodityRecommend) String() string {
	return utils.ToJson(t)
}
func (t *CommodityRecommend) SqlString() string {
	return utils.ToSqlStr(t)
}
func (t *CommodityRecommend) TableName() string {
	return tblCommodityRecommend
}

type CommodityDataPackage struct {
	PackageId         uint32    `db:"package_id" json:"package_id"`                     //自增id 套餐id
	Price             uint32    `db:"price" json:"price"`                               //价格
	DiscountPrice     uint32    `db:"discount_price" json:"discount_price"`             //折扣价格
	EffectiveDay      uint32    `db:"effective_day" json:"effective_day"`               //有效天数
	ShelfTime         time.Time `db:"shelf_time" json:"shelf_time"`                     //上架时间
	ExpireTime        time.Time `db:"expire_time" json:"expire_time"`                   //下架时间
	CommodityId       uint32    `db:"commodity_id" json:"commodity_id"`                 //商品id
	IsRefreshedRedDot bool      `db:"is_refreshed_red_dot" json:"is_refreshed_red_dot"` //是否刷新红点
	CreateTime        time.Time `db:"create_time" json:"create_time"`                   //创建时间
	UpdateTime        time.Time `db:"update_time" json:"update_time"`                   //更新时间
	ExpireTimeShow    bool      `db:"expire_time_show" json:"expire_time_show"`         //下架时间展示
}

func (t *CommodityDataPackage) String() string {
	return utils.ToJson(t)
}
func (t *CommodityDataPackage) SqlString() string {
	return utils.ToSqlStr(t)
}
func (t *CommodityDataPackage) TableName() string {
	return tblCommodityDataPackage
}

func (s *Mysql) createOrderTable() {
	tableName := CommodityDataOrders{CreateTime: time.Now()}
	createTableSQL := `
	CREATE TABLE IF NOT EXISTS %s (
		id INT AUTO_INCREMENT PRIMARY KEY COMMENT "自增id",
		uid INT NOT NULL DEFAULT 0 COMMENT "用户uid",
		order_no VARCHAR(255) NOT NULL DEFAULT "" COMMENT "订单号",
	    big_trade_no VARCHAR(255) NOT NULL DEFAULT "" COMMENT "大订单号",
		pay_status INT NOT NULL DEFAULT 0 COMMENT "支付状态 1:待支付 2:已支付",
		total_price INT NOT NULL DEFAULT 0 COMMENT "总价",
		avg_price INT NOT NULL DEFAULT 0 COMMENT "平均价", 
		commodity_id INT NOT NULL DEFAULT 0 COMMENT "商品id", 
		package_id INT NOT NULL DEFAULT 0 COMMENT "套餐id", 
	    commodity_type INT NOT NULL DEFAULT 0 COMMENT "商品类型 1:单品 2:套装",
	    category INT NOT NULL DEFAULT 0 COMMENT "商品品类大类", 
		sub_category INT NOT NULL DEFAULT 0 COMMENT "商品品类子类",
	    effective_day INT NOT NULL DEFAULT 0 COMMENT "有效天数", 
		c_count INT NOT NULL DEFAULT 0 COMMENT "数量",
	    resource_id_list JSON NOT NULL  COMMENT "资源id列表", 
	    commodity_name VARCHAR(255) NOT NULL DEFAULT "" COMMENT "商品名称",
		pro_video_id INT NOT NULL DEFAULT 0 COMMENT "宣传片id",
		create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间", 
	    update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT "更新时间",
		UNIQUE KEY uniq_order_no (order_no),
		INDEX idx_uid (uid),
	    INDEX idx_big_trade_no (big_trade_no),
		INDEX idx_pay_status (create_time, pay_status)
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT '商城订单表';`

	createTableSQL = fmt.Sprintf(createTableSQL, tableName.TableName())
	_, err := s.db.Exec(createTableSQL)
	if err != nil {
		log.Errorf("createOrderTable creating table err:", err)
		return
	}
}
func (s *Mysql) createTable(ctx context.Context) {
	s.createOrderTable()

	createTableSQL := `CREATE TABLE IF NOT EXISTS tbl_commodity_data_package (
		package_id INT AUTO_INCREMENT PRIMARY KEY COMMENT "套餐ID",
		price INT NOT NULL DEFAULT 0 COMMENT "套餐价格", 
		discount_price INT NOT NULL DEFAULT 0 COMMENT "套餐折扣后价格",
		effective_day INT NOT NULL DEFAULT 0 COMMENT "有效天数", 
		expire_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT "下架时间", 
		shelf_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT "上架时间", 
		expire_time_show bool NOT NULL DEFAULT 0 COMMENT "下架时间展示",
		commodity_id INT DEFAULT 0 COMMENT "商品id",
		is_refreshed_red_dot bool NOT NULL DEFAULT 0 COMMENT "是否刷新红点",
		create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间", 
	    update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT "更新时间",
	    KEY idx_commodity_id (commodity_id),
	    KEY idx_shelf_time (shelf_time, expire_time)
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT '商城套餐表';`

	_, err := s.db.Exec(createTableSQL)
	if err != nil {
		fmt.Println("Error creating table:", err)
		return
	}

	createTableSQL = `CREATE TABLE IF NOT EXISTS tbl_commodity_recommend (
		id INT AUTO_INCREMENT PRIMARY KEY COMMENT '自增',  
		commodity_id INT DEFAULT 0 COMMENT "商品id",
		c_rank INT NOT NULL DEFAULT 0  COMMENT "排名",
		create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间", 
	    update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT "更新时间",
	    UNIQUE KEY uniq_commodity_id (commodity_id)
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT '商城推荐';`

	_, err = s.db.Exec(createTableSQL)
	if err != nil {
		fmt.Println("Error creating table:", err)
		return
	}

	createTableSQL = ` CREATE TABLE IF NOT EXISTS tbl_commodity_data (
		commodity_id INT AUTO_INCREMENT PRIMARY KEY COMMENT "商品id[自增ID]",
		commodity_name VARCHAR(255) NOT NULL DEFAULT "" COMMENT "商品名称",
		category INT NOT NULL DEFAULT 0 COMMENT "商品品类大类", 
		sub_category INT NOT NULL DEFAULT 0 COMMENT "商品品类子类",
		resource_id_list JSON NOT NULL  COMMENT "资源id列表", 
		commodity_type INT NOT NULL DEFAULT 0 COMMENT "商品类型 1:单品 2:套装", 
		shelf_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT "上架时间", 
		expire_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT "下架时间", 
		expire_time_show bool NOT NULL DEFAULT 0 COMMENT "下架时间展示",
		gain_path INT NOT NULL DEFAULT 0 COMMENT "获取途径 1:商店 2:活动", 
		c_rank INT NOT NULL DEFAULT 0 COMMENT "排名", 
		c_level INT NOT NULL DEFAULT 0 COMMENT "等级", 
		level_icon VARCHAR(255) NOT NULL DEFAULT "" COMMENT "等级图标", 
		sex  INT NOT NULL DEFAULT 0 COMMENT "资源性别 1:男 2:女 3:通用",
		customize_logotype VARCHAR(255) NOT NULL DEFAULT "" COMMENT "自定义标识", 
		customize_shelf_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT "自定义标识上架时间",
		customize_expire_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT "自定义标识下架时间",  
		commodity_icon VARCHAR(255) NOT NULL DEFAULT "" COMMENT "商品图标", 
		commodity_animation VARCHAR(255) NOT NULL DEFAULT "" COMMENT "商品动画", 
		activity_info VARCHAR(2048) NOT NULL DEFAULT "" COMMENT "活动信息描述", 
		spine_animation VARCHAR(255) NOT NULL DEFAULT "" COMMENT "spine动画", 
        pro_video_id  INT NOT NULL DEFAULT 0 COMMENT  "宣传片id",
		create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间", 
	    update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT "更新时间",
	    KEY idx_category (category, sub_category),
	    KEY idx_commodity_type (commodity_type),
	    KEY idx_shelf_time (shelf_time, expire_time),
	    KEY idx_gain_path (gain_path)
	) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT '商城商品表';;`

	_, err = s.db.Exec(createTableSQL)
	if err != nil {
		fmt.Println("Error creating table:", err)
		return
	}
}

// Store 存储实体
// 该结构体是存储的抽象，用于存储数据
type Mysql struct {
	db     mysql.DBx
	dyConf *conf.SDyConfigHandler
}

// NewStore 创建存储
func NewStore(ctx context.Context, cfg *mysqlConnect.MysqlConfig, dyConf_ *conf.SDyConfigHandler) (*Mysql, error) {
	db, err := mysqlConnect.NewClient(ctx, cfg)
	if err != nil {
		return nil, err
	}

	s := Mysql{
		db:     db,
		dyConf: dyConf_,
	}

	return &s, nil
}

func (s *Mysql) Close() error {
	return s.db.Close()
}

func (s *Mysql) Transaction(ctx context.Context, f func(tx mysql.Txx) error) error {
	tx, err := s.db.Beginx()
	if err != nil {
		log.ErrorWithCtx(ctx, "Transaction fail err %v", err)
		return err
	}

	err = f(tx)
	if err != nil {
		log.ErrorWithCtx(ctx, "Transaction fail err %v", err)
		_ = tx.Rollback()
		return err
	}

	_ = tx.Commit()
	return nil
}

func getCommodityEffectTime(ctx context.Context, data *pb.CommodityData) (time.Time, time.Time) {
	var shelfTs uint32 = 0
	var expireTs uint32 = 0
	for _, v := range data.GetPricePackageList() {
		if v.GetShelfTime() <= shelfTs || shelfTs == 0 {
			shelfTs = v.GetShelfTime()
		}

		if v.GetExpireTime() >= expireTs || expireTs == 0 {
			expireTs = v.GetExpireTime()

		}
	}
	return time.Unix(int64(shelfTs), 0), time.Unix(int64(expireTs), 0)
}

func (s *Mysql) AddCommodity(ctx context.Context, tx mysql.Txx, data *pb.CommodityData) (int64, error) {
	tableName := tblCommodityData
	var sql string
	if data.GetCommodityId() == 0 {
		sql = fmt.Sprintf("insert into %+v (`commodity_name`,`category`,`sub_category`,`resource_id_list`,`commodity_type`,`shelf_time`,`expire_time`,"+
			"`gain_path`,`c_rank`,`c_level`,`level_icon`,`sex`,`customize_logotype`,`customize_shelf_time`,`customize_expire_time`,`commodity_icon`,`commodity_animation`,"+
			"`activity_info`,`spine_animation`, `pro_video_id`) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)", tableName)
	} else {
		sql = fmt.Sprintf("insert into %+v (`commodity_id`,`commodity_name`,`category`,`sub_category`,`resource_id_list`,`commodity_type`,`shelf_time`,`expire_time`,"+
			"`gain_path`,`c_rank`,`c_level`,`level_icon`,`sex`,`customize_logotype`,`customize_shelf_time`,`customize_expire_time`,`commodity_icon`,`commodity_animation`,"+
			"`activity_info`,`spine_animation`, `pro_video_id`) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)", tableName)
	}

	byteActInfo, err := json.Marshal(&data.ActInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddCommodity json.Marshal fail err:%v data:%v", err, data)
		return 0, err
	}

	byteIdList, err := json.Marshal(&data.ResourceIdList)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddCommodity json.Marshal fail err:%v data:%v", err, data)
		return 0, err
	}

	shelfTime, expireTime := getCommodityEffectTime(ctx, data)
	customizeShelfTime := time.Unix(int64(data.CustomizeIcon.ShelfTime), 0)
	customizeExpireTime := time.Unix(int64(data.CustomizeIcon.ExpireTime), 0)

	if data.GetCommodityId() == 0 {
		res, err := tx.ExecContext(ctx, sql, data.CommodityName, data.Category, data.SubCategory, byteIdList, data.CommodityType, shelfTime, expireTime,
			data.GainPath, data.Rank, data.Level, data.LevelIcon, data.ResourceSex, data.CustomizeIcon.GetLogotype(), customizeShelfTime, customizeExpireTime, data.CommodityIcon,
			data.CommodityAnimation, string(byteActInfo), data.SpineAnimation, data.PromotionalVideoId)
		if err != nil {
			log.ErrorWithCtx(ctx, "AddCommodity fail err:%v data:%v", err, data)
			return 0, err
		}
		id, err := res.LastInsertId()
		if err != nil {
			log.ErrorWithCtx(ctx, "AddCommodity fail err:%v data:%v", err, data)
			return 0, err
		}
		log.InfoWithCtx(ctx, "AddCommodity success data:%+v id:%v", data, id)
		return id, nil
	} else {
		_, err := tx.ExecContext(ctx, sql, data.CommodityId, data.CommodityName, data.Category, data.SubCategory, byteIdList, data.CommodityType, shelfTime, expireTime,
			data.GainPath, data.Rank, data.Level, data.LevelIcon, data.ResourceSex, data.CustomizeIcon.GetLogotype(), customizeShelfTime, customizeExpireTime, data.CommodityIcon,
			data.CommodityAnimation, string(byteActInfo), data.SpineAnimation, data.PromotionalVideoId)
		if err != nil {
			log.ErrorWithCtx(ctx, "AddCommodity fail err:%v data:%v", err, data)
			return 0, err
		}
		return int64(data.CommodityId), nil
	}

	return 0, nil
}

// 更新商品信息
func (s *Mysql) UpdateCommodity(ctx context.Context, tx mysql.Txx, data *pb.CommodityData) error {
	tableName := tblCommodityData
	sql := fmt.Sprintf("update %+v set `commodity_name` = ?, `category` = ?, `sub_category` = ?, `resource_id_list` = ?, `commodity_type` = ?, `shelf_time` = ?, `expire_time` = ?, "+
		"`gain_path` = ?, `c_rank` = ?, `c_level` = ?, `level_icon` = ?, `sex` = ?, `customize_logotype` = ?, `customize_shelf_time` = ?, `customize_expire_time` = ?, `commodity_icon` = ?, "+
		"`commodity_animation` = ?, `activity_info` = ?, `spine_animation` = ?, `pro_video_id` = ? where `commodity_id` = ?", tableName)

	byteActInfo, err := json.Marshal(&data.ActInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateCommodity json.Marshal fail err:%v data:%v", err, data)
		return err
	}

	byteIdList, err := json.Marshal(&data.ResourceIdList)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateCommodity json.Marshal fail err:%v data:%v", err, data)
		return err
	}

	shelfTime, expireTime := getCommodityEffectTime(ctx, data)
	customizeShelfTime := time.Unix(int64(data.CustomizeIcon.ShelfTime), 0)
	customizeExpireTime := time.Unix(int64(data.CustomizeIcon.ExpireTime), 0)
	_, err = tx.ExecContext(ctx, sql, data.CommodityName, data.Category, data.SubCategory, byteIdList, data.CommodityType, shelfTime, expireTime,
		data.GainPath, data.Rank, data.Level, data.LevelIcon, data.ResourceSex, data.CustomizeIcon.GetLogotype(), customizeShelfTime, customizeExpireTime, data.CommodityIcon,
		data.CommodityAnimation, string(byteActInfo), data.SpineAnimation, data.PromotionalVideoId, data.CommodityId)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateCommodity fail err:%v data:%v", err, data)
		return err
	}

	return nil
}

func (s *Mysql) UpdateCommoditRedDotVersion(ctx context.Context, commodityId uint32, shelfTime uint32) error {
	tableName := tblCommodityData
	sql := fmt.Sprintf("update %+v set `red_dot_version` = ? where `commodity_id` = ?", tableName)

	_, err := s.db.ExecContext(ctx, sql, shelfTime, commodityId)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateCommoditRedDotVersion fail err:%v commodityId:%v shelfTime:%v ", err, commodityId, shelfTime)
		return err
	}

	return nil
}

// 更新商品的物品信息
func (s *Mysql) UpdateCommodityResource(ctx context.Context, resourceId, sex uint32, resourceName, resourceIcon string) error {
	tableName := tblCommodityData
	sql := fmt.Sprintf("update %+v set `commodity_name` = ?,  `commodity_icon` = ?, `sex` = ? where `commodity_type` = ? and JSON_CONTAINS(`resource_id_list`,'%d')", tableName, resourceId)

	_, err := s.db.ExecContext(ctx, sql, resourceName, resourceIcon, sex, uint32(pb.CommodityType_COMMODITY_TYPE_SINGLE))
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateCommodityResource fail err:%v resourceId:%v resourceName:%v resourceIcon:%v", err, resourceId, resourceName, resourceIcon)
		return err
	}

	return nil
}

func (s *Mysql) GetCommodityDataListByPrice(ctx context.Context, in *pb.GetCommodityDataListRequest) ([]*CommodityData, uint32, error) {
	log.DebugWithCtx(ctx, "GetCommodityDataListByPrice in:%v", in)
	list := make([]*CommodityData, 0)
	info := CommodityData{}
	var totalCnt uint32 = 0
	tableName := info.TableName()
	query := " where 1=1 and "
	if in.GetCategory() != 0 {
		if in.GetCategory() == CommodityCategory_Suit {
			query += fmt.Sprintf("tbl_commodity_data.commodity_type = %d and ", pb.CommodityType_COMMODITY_TYPE_SUIT)
		} else {
			query += fmt.Sprintf("tbl_commodity_data.category = %d and ", in.GetCategory())
		}
	}
	if in.GetSubCategory() != 0 {
		query += fmt.Sprintf("tbl_commodity_data.sub_category = %d and ", in.GetSubCategory())
	}
	if len(in.GetCommodityIdList()) != 0 {
		idStr := ""
		for _, v := range in.GetCommodityIdList() {
			idStr += fmt.Sprintf("%d,", v)
		}
		query += fmt.Sprintf("tbl_commodity_data.commodity_id in (%s) and ", idStr[:len(idStr)-1])
	}
	if in.GetCommodityType() != 0 {
		query += fmt.Sprintf("tbl_commodity_data.commodity_type = %d and ", in.GetCommodityType())
	}
	if in.GetIsSexSelect() {
		query += fmt.Sprintf("tbl_commodity_data.sex = %d and ", in.GetResourceSex())
	}
	// 性别多选
	if len(in.GetResourceSexList()) != 0 {
		sexStr := ""
		for _, v := range in.GetResourceSexList() {
			sexStr += fmt.Sprintf("%d,", v)
		}
		query += fmt.Sprintf("tbl_commodity_data.sex in (%s) and ", sexStr[:len(sexStr)-1])
	}

	if len(in.GetResourceIdList()) != 0 {
		query += "("
		for i, v := range in.GetResourceIdList() {
			if i == len(in.GetResourceIdList())-1 {
				query += fmt.Sprintf("JSON_CONTAINS(tbl_commodity_data.resource_id_list,'%d')) and ", v)
				break
			} else {
				query += fmt.Sprintf("JSON_CONTAINS(tbl_commodity_data.resource_id_list,'%d') or ", v)
			}
		}
	}

	if in.GetGainPath() != 0 {
		query += fmt.Sprintf("tbl_commodity_data.gain_path = %d and ", in.GetGainPath())
	}
	if in.GetLevel() != 0 {
		query += fmt.Sprintf("tbl_commodity_data.c_level = %d and ", in.GetLevel())
	}
	if len(in.GetCommodityName()) > 0 {
		query += fmt.Sprintf("tbl_commodity_data.commodity_name like '%%%s%%' and ", in.GetCommodityName())
	}

	switch in.GetShelfStatus() {
	case CommodityShelfStatus_On_OR_Furture:
		query += "tbl_commodity_data.expire_time >= now() and "
	default:
		break
	}

	// 去掉最后一个and
	query = query[:len(query)-4]

	sql := ""
	totalSql := ""
	sql = fmt.Sprintf("select %s from %s INNER JOIN tbl_commodity_data_package package ON tbl_commodity_data.commodity_id = package.commodity_id", info.JoinSqlString(), tableName)
	totalSql = fmt.Sprintf("select count(*) from %s %s", tableName, query)

	if in.GetMaxPrice() != 0 {
		query += fmt.Sprintf(" and price >= %d and price <= %d", in.GetMinPrice(), in.GetMaxPrice())
	}

	if in.GetMinDiscountPrice() != 0 {
		query += fmt.Sprintf(" and discount_price >= %d and discount_price <= %d", in.GetMinDiscountPrice(), in.GetMaxDiscountPrice())
	}

	sql += query

	log.DebugWithCtx(ctx, "GetCommodityDataListByPrice sql:%s", sql)

	if len(in.GetSortData().GetField()) > 0 && strings.Contains(in.GetSortData().GetField(), "price") {
		switch pb.SortType(in.GetSortData().GetSortType()) {
		case pb.SortType_SORT_TYPE_ASC:
			sql = fmt.Sprintf("%s order by %s asc, commodity_id desc", sql, in.GetSortData().GetField())
		case pb.SortType_SORT_TYPE_DESC:
			sql = fmt.Sprintf("%s order by %s desc, commodity_id desc", sql, in.GetSortData().GetField())
		}
	}

	if in.GetPageSize() != 0 {
		sql += fmt.Sprintf(" limit %d, %d ", (in.GetPage()-1)*in.GetPageSize(), in.GetPageSize())
	}

	err := s.db.GetContext(ctx, &totalCnt, totalSql)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCommodityDataListByPrice fail err:%v sql:%v", err, sql)
		return list, totalCnt, err
	}

	log.DebugWithCtx(ctx, "GetCommodityDataListByPrice sql:%v", sql)
	rows, err := s.db.QueryxContext(ctx, sql)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCommodityDataListByPrice fail err:%v sql:%v", err, sql)
		return list, totalCnt, err
	}

	defer rows.Close()
	for rows.Next() {
		info := CommodityData{}
		err = rows.StructScan(&info)
		if err == nil {
			list = append(list, &info)
		} else {
			log.Errorf("GetCommodityDataListByPrice StructScan fail sql:%v %v", sql, err)
		}
	}
	log.Debugf("GetCommodityDataListByPrice list %d totalCnt:%d", len(list), totalCnt)
	return list, totalCnt, nil
}

func (s *Mysql) GetCommodityDataList(ctx context.Context, in *pb.GetCommodityDataListRequest, isNeedTotalCnt, isOnlyName bool, shelfStatus int) ([]*CommodityData, uint32, error) {
	log.DebugWithCtx(ctx, "GetCommodityDataList in:%v", in)
	list := make([]*CommodityData, 0)
	info := CommodityData{}
	var totalCnt uint32 = 0
	tableName := info.TableName()
	query := " where "
	if in.GetCategory() != 0 {
		if in.GetCategory() == CommodityCategory_Suit {
			query += fmt.Sprintf("`commodity_type` = %d and ", pb.CommodityType_COMMODITY_TYPE_SUIT)
		} else {
			query += fmt.Sprintf("`category` = %d and ", in.GetCategory())
		}
	}
	if in.GetSubCategory() != 0 {
		query += fmt.Sprintf("`sub_category` = %d and ", in.GetSubCategory())
	}
	if len(in.GetCommodityIdList()) != 0 {

		idStr := ""
		for _, v := range in.GetCommodityIdList() {
			idStr += fmt.Sprintf("%d,", v)
		}
		query += fmt.Sprintf("`commodity_id` in (%s) and ", idStr[:len(idStr)-1])
	}
	if in.GetCommodityType() != 0 {

		query += fmt.Sprintf("`commodity_type` = %d and ", in.GetCommodityType())
	}
	if in.GetIsSexSelect() {
		query += fmt.Sprintf("`sex` = %d and ", in.GetResourceSex())
	}
	// 性别多选
	if len(in.GetResourceSexList()) != 0 {
		sexStr := ""
		for _, v := range in.GetResourceSexList() {
			sexStr += fmt.Sprintf("%d,", v)
		}
		query += fmt.Sprintf("`sex` in (%s) and ", sexStr[:len(sexStr)-1])
	}
	if len(in.GetResourceIdList()) != 0 {
		query += "("
		for i, v := range in.GetResourceIdList() {
			if i == len(in.GetResourceIdList())-1 {
				query += fmt.Sprintf("JSON_CONTAINS(`resource_id_list`,'%d')) and ", v)
				break
			} else {
				query += fmt.Sprintf("JSON_CONTAINS(`resource_id_list`,'%d') or ", v)
			}
		}
	}
	if in.GetGainPath() != 0 {
		query += fmt.Sprintf("`gain_path` = %d and ", in.GetGainPath())
	}
	if in.GetLevel() != 0 {
		query += fmt.Sprintf("`c_level` = %d and ", in.GetLevel())
	}
	if len(in.GetCommodityName()) > 0 {
		if isOnlyName {
			query += fmt.Sprintf("`commodity_name` = '%s' and ", in.GetCommodityName())
		} else {
			query += fmt.Sprintf("`commodity_name` like '%%%s%%' and ", in.GetCommodityName())
		}
	}

	switch shelfStatus {
	case CommodityShelfStatus_On_OR_Furture:
		query += "`expire_time` >= now() and "
	default:
		break
	}

	if query != " where " {
		query = query[:len(query)-4]
	}

	sql := ""
	totalSql := ""
	if query == " where " {
		sql = fmt.Sprintf("select %s from %+v order by `update_time` desc", info.SqlString(), tableName)
		totalSql = fmt.Sprintf("select count(*) from %+v ", tableName)

		if len(in.GetSortData().GetField()) > 0 && !strings.Contains(in.GetSortData().GetField(), "price") {
			switch pb.SortType(in.GetSortData().GetSortType()) {
			case pb.SortType_SORT_TYPE_ASC:
				sql = fmt.Sprintf("select %s from %+v order by `%s` asc, `commodity_id` desc", info.SqlString(), tableName, in.GetSortData().GetField())
			case pb.SortType_SORT_TYPE_DESC:
				sql = fmt.Sprintf("select %s from %+v order by `%s` desc, `commodity_id` desc", info.SqlString(), tableName, in.GetSortData().GetField())
			}
		}

	} else {
		if in.GetCommodityIdList() == nil {
			sql = fmt.Sprintf("select %s from %+v %s order by `update_time` desc", info.SqlString(), tableName, query)
		} else {
			sql = fmt.Sprintf("select %s from %+v %s", info.SqlString(), tableName, query)
		}

		totalSql = fmt.Sprintf("select count(*) from %+v %s", tableName, query)

		if len(in.GetSortData().GetField()) > 0 && !strings.Contains(in.GetSortData().GetField(), "price") {
			switch pb.SortType(in.GetRankSort()) {
			case pb.SortType_SORT_TYPE_ASC:
				sql = fmt.Sprintf("select %s from %+v %s order by `%s` asc, `update_time` desc", info.SqlString(), tableName, query, in.GetSortData().GetField())
			case pb.SortType_SORT_TYPE_DESC:
				sql = fmt.Sprintf("select %s from %+v %s order by `%s` desc, `commodity_id` desc", info.SqlString(), tableName, query, in.GetSortData().GetField())
			}
		}
	}

	if in.GetPageSize() != 0 {
		sql += fmt.Sprintf(" limit %d, %d ", (in.GetPage()-1)*in.GetPageSize(), in.GetPageSize())
	}
	var err error
	if isNeedTotalCnt {
		err = s.db.GetContext(ctx, &totalCnt, totalSql)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetCommodityDataList fail err:%v sql:%v", err, sql)
			return list, totalCnt, err
		}
	}

	log.DebugWithCtx(ctx, "GetCommodityDataList sql:%v", sql)
	rows, err := s.db.QueryxContext(ctx, sql)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCommodityDataList fail err:%v sql:%v", err, sql)
		return list, totalCnt, err
	}

	defer rows.Close()
	for rows.Next() {
		info := CommodityData{}
		err = rows.StructScan(&info)
		if err == nil {
			list = append(list, &info)
		} else {
			log.Errorf("GetCommodityDataList StructScan fail sql:%v %v", sql, err)
		}
	}
	log.Debugf("GetCommodityDataList list %d totalCnt:%d", len(list), totalCnt)
	return list, totalCnt, nil
}

func (s *Mysql) BatchGetCommodityData(ctx context.Context, commodityIdList []uint32) ([]*CommodityData, error) {
	log.DebugWithCtx(ctx, "BatchGetCommodityData commodityId:%v", commodityIdList)
	list := make([]*CommodityData, 0)
	info := CommodityData{}
	tableName := info.TableName()
	sql := fmt.Sprintf("select %s from %+v where `commodityId` in (?)", info.SqlString(), tableName)
	var err error
	var inQuery string
	args := make([]interface{}, 0)
	inQuery, args, err = sqlx.In(sql, commodityIdList)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetCommodityData In fail, err :%v", err)
		return list, nil
	}
	err = s.db.SelectContext(ctx, &list, inQuery, args...)
	if err != nil {
		if mysql.IsNoRowsError(err) {
			log.DebugWithCtx(ctx, "BatchGetCommodityData no data pidList: %v", commodityIdList)
			return list, nil
		}
		log.ErrorWithCtx(ctx, "BatchGetCommodityData error: %v", err)
		return nil, err
	}

	return list, nil
}

func (s *Mysql) AddCommodityRecommend(ctx context.Context, data *pb.CommodityRecommendInfo) error {
	tableName := tblCommodityRecommend
	sql := fmt.Sprintf("insert into %+v (`commodity_id`,`c_rank`) values (?,?)", tableName)

	_, err := s.db.ExecContext(ctx, sql, data.GetData().GetCommodityId(), data.GetRecRank())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddCommodityRecommend fail err:%v data:%v", err, data)
		return err
	}

	return nil
}

func (s *Mysql) UpdateCommodityRecommend(ctx context.Context, data *pb.CommodityRecommendInfo) error {
	tableName := tblCommodityRecommend
	sql := fmt.Sprintf("update %+v set `c_rank` = ? where `commodity_id` = ?", tableName)

	_, err := s.db.ExecContext(ctx, sql, data.GetRecRank(), data.GetData().GetCommodityId())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateCommodityRecommend fail err:%v data:%v", err, data)
		return err
	}

	return nil
}

func (s *Mysql) DeleteCommodityRecommend(ctx context.Context, commodityId uint32) error {
	tableName := tblCommodityRecommend
	sql := fmt.Sprintf("delete from %+v where `commodity_id` = ?", tableName)

	_, err := s.db.ExecContext(ctx, sql, commodityId)
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteCommodityRecommend fail err:%v commodityId:%v", err, commodityId)
		return err
	}

	return nil
}

func (s *Mysql) GetCommodityRecommendList(ctx context.Context, req *pb.GetCommodityRecommendListRequest) ([]*CommodityRecommend, error) {
	commodityIdList := make([]*CommodityRecommend, 0)
	info := CommodityRecommend{}
	tableName := info.TableName()

	var sql string
	if req.GetSortData().GetSortType() == 0 {
		sql = fmt.Sprintf("select %s from %+v order by `update_time` desc", info.SqlString(), tableName)
	} else if req.GetSortData().GetSortType() == uint32(pb.SortType_SORT_TYPE_ASC) {
		sql = fmt.Sprintf("select %s from %+v order by %s asc", info.SqlString(), tableName, req.GetSortData().GetField())
	} else {
		sql = fmt.Sprintf("select %s from %+v order by %s desc", info.SqlString(), tableName, req.GetSortData().GetField())
	}
	var err error
	var inQuery string
	args := make([]interface{}, 0)
	inQuery, args, err = sqlx.In(sql)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCommodityRecommendList In fail, err :%v", err)
		return commodityIdList, nil
	}
	err = s.db.SelectContext(ctx, &commodityIdList, inQuery, args...)
	if err != nil {
		if mysql.IsNoRowsError(err) {
			log.DebugWithCtx(ctx, "GetCommodityRecommendList no data pidList: %v", commodityIdList)
			return commodityIdList, nil
		}
		log.ErrorWithCtx(ctx, "GetCommodityRecommendList error: %v", err)
		return nil, err
	}

	return commodityIdList, nil
}

func (s *Mysql) AddCommodityDataPackage(ctx context.Context, tx mysql.Txx, data *pb.CommodityDataPackage) error {
	tableName := tblCommodityDataPackage
	sql := fmt.Sprintf("insert into %+v (`price`,`discount_price`,`effective_day`,`shelf_time`,`expire_time`,`commodity_id`, `expire_time_show`, `is_refreshed_red_dot`) "+
		"values (?,?,?,?,?,?,?, ?)", tableName)

	shelfTime := time.Unix(int64(data.ShelfTime), 0)
	expireTime := time.Unix(int64(data.ExpireTime), 0)

	_, err := tx.ExecContext(ctx, sql, data.Price, data.DiscountPrice, data.EffectiveDay, shelfTime, expireTime, data.CommodityId, data.ExpireTimeShow, data.GetIsRefreshedRedDot())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddCommodityDataPackage fail err:%v data:%v", err, data)
		return err
	}
	return nil
}

func (s *Mysql) UpdateCommodityDataPackage(ctx context.Context, tx mysql.Txx, data *pb.CommodityDataPackage) error {
	tableName := tblCommodityDataPackage
	sql := fmt.Sprintf("update %+v set `price` = ?, `discount_price` = ?, `effective_day` = ?, `shelf_time` = ?, `expire_time` = ?, `expire_time_show` = ? where `package_id` = ?", tableName)

	shelfTime := time.Unix(int64(data.ShelfTime), 0)
	expireTime := time.Unix(int64(data.ExpireTime), 0)

	_, err := tx.ExecContext(ctx, sql, data.Price, data.DiscountPrice, data.EffectiveDay, shelfTime, expireTime, data.ExpireTimeShow, data.PackageId)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateCommodityDataPackage fail err:%v data:%v", err, data)
		return err
	}
	return nil
}

func (s *Mysql) UpdateCommodityDataPackageRedDot(ctx context.Context, isReefreshed bool, packageId uint32) error {
	tableName := tblCommodityDataPackage
	sql := fmt.Sprintf("update %+v set `is_refreshed_red_dot` = ? where `package_id` = ?", tableName)

	_, err := s.db.ExecContext(ctx, sql, isReefreshed, packageId)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateCommodityDataPackageRedDot fail err:%v isReefreshed:%v packageId:%v", err, isReefreshed, packageId)
		return err
	}
	return nil
}

func (s *Mysql) GetUnRefreshedRedDotPackage(ctx context.Context) ([]*CommodityDataPackage, error) {
	list := []*CommodityDataPackage{}
	info := CommodityDataPackage{}
	tableName := info.TableName()
	now := time.Now().Format("2006-01-02 15:04:05")
	sql := fmt.Sprintf("select %s from %+v where `shelf_time` < '%s' and `is_refreshed_red_dot` = 0", info.SqlString(), tableName, now)
	var err error
	log.DebugWithCtx(ctx, "GetUnRefreshedRedDotPackage sql:%v", sql)
	err = s.db.SelectContext(ctx, &list, sql)
	if err != nil {
		if mysql.IsNoRowsError(err) {
			log.DebugWithCtx(ctx, "GetUnRefreshedRedDotPackage no data")
			return list, nil
		}
		log.ErrorWithCtx(ctx, "GetUnRefreshedRedDotPackage sql:%v error: %v", sql, err)
		return nil, err
	}

	return list, nil
}

func (s *Mysql) DeleteCommodityDataPackage(ctx context.Context, tx mysql.Txx, packageId, commodityId uint32) error {
	tableName := tblCommodityDataPackage
	sql := fmt.Sprintf("delete from %+v where `commodity_id` = ? and `package_id` = ? ", tableName)

	_, err := tx.ExecContext(ctx, sql, commodityId, packageId)
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteCommodityDataPackage fail err:%v commodityId:%v packageId:%d", err, commodityId, packageId)
		return err
	}

	return nil
}

func (s *Mysql) BathGetCommodityDataPackage(ctx context.Context, commodityIdList []uint32) ([]*CommodityDataPackage, error) {
	log.DebugWithCtx(ctx, "BathGetCommodityDataPackage commodityIdList：%v", commodityIdList)
	packageList := make([]*CommodityDataPackage, 0)
	if len(commodityIdList) == 0 {
		return packageList, nil
	}
	info := CommodityDataPackage{}
	tableName := info.TableName()
	sql := fmt.Sprintf("select %s from %+v where commodity_id in (?)", info.SqlString(), tableName)
	var err error
	var inQuery string
	args := make([]interface{}, 0)
	inQuery, args, err = sqlx.In(sql, commodityIdList)
	if err != nil {
		log.ErrorWithCtx(ctx, "BathGetCommodityDataPackage In fail, err :%v", err)
		return packageList, nil
	}
	err = s.db.SelectContext(ctx, &packageList, inQuery, args...)
	if err != nil {
		if mysql.IsNoRowsError(err) {
			log.DebugWithCtx(ctx, "BathGetCommodityDataPackage no data pidList: %v", commodityIdList)
			return packageList, nil
		}
		log.ErrorWithCtx(ctx, "BathGetCommodityDataPackage error: %v %v %v", err, inQuery, args)
		return nil, err
	}

	return packageList, nil
}

func (s *Mysql) GetCommodityDataPackageList(ctx context.Context, commodityIdList []uint32, shelfStatus pb.ShelfStatus) ([]*CommodityDataPackage, error) {
	list := []*CommodityDataPackage{}
	info := CommodityDataPackage{}
	tableName := info.TableName()
	query := " where "
	if len(commodityIdList) != 0 {
		idStr := ""
		for _, v := range commodityIdList {
			idStr += fmt.Sprintf("%d,", v)
		}
		query += fmt.Sprintf("`commodity_id` in (%s) and ", idStr[:len(idStr)-1])
	}
	switch shelfStatus {
	case pb.ShelfStatus_SHELF_STATUS_FUTURE:
		query += "`shelf_time` > now() and "
	case pb.ShelfStatus_SHELF_STATUS_NOW:
		query += "`shelf_time` <= now() and `expire_time` >= now() and "
	case pb.ShelfStatus_SHELF_STATUS_EXPIRE:
		query += "`expire_time` < now() and "
	default:
		break
	}
	if query != " where " {
		query = query[:len(query)-4]
	}
	sql := ""
	if query == " where " {
		sql = fmt.Sprintf("select %s from %+v ", info.SqlString(), tableName)
	} else {
		sql = fmt.Sprintf("select %s from %+v %s", info.SqlString(), tableName, query)
	}

	log.Debugf("GetCommodityDataList list %+v", sql)
	var err error
	rows, err := s.db.QueryxContext(ctx, sql)
	if err != nil {
		return list, err
	}

	defer rows.Close()
	for rows.Next() {
		tmpInfo := CommodityDataPackage{}
		err = rows.StructScan(&tmpInfo)
		if err == nil {
			list = append(list, &tmpInfo)
		} else {
			log.Errorf("GetCommodityDataPackageList StructScan fail sql:%v %v", sql, err)
		}
		log.Debugf("GetCommodityDataPackageList StructScan %+v", info)
	}
	log.Debugf("GetCommodityDataPackageList list %+v", list)
	return list, nil
}

// GetCommodityDataPackageByPrice 根据价格获取套餐信息
func (s *Mysql) GetCommodityDataPackageByPrice(ctx context.Context, price uint32, shelfStatus pb.ShelfStatus) ([]*CommodityDataPackage, error) {
	list := make([]*CommodityDataPackage, 0)
	info := CommodityDataPackage{}
	tableName := info.TableName()
	sql := fmt.Sprintf("select %s from %+v where `discount_price` = ?", info.SqlString(), tableName)

	switch shelfStatus {
	case pb.ShelfStatus_SHELF_STATUS_FUTURE:
		sql += " and `shelf_time` > now() "
	case pb.ShelfStatus_SHELF_STATUS_NOW:
		sql += " and `shelf_time` <= now() and `expire_time` >= now() "
	case pb.ShelfStatus_SHELF_STATUS_EXPIRE:
		sql += " and `expire_time` < now() "
	default:
		break
	}

	var err error
	err = s.db.SelectContext(ctx, &list, sql, price)
	if err != nil {
		if mysql.IsNoRowsError(err) {
			log.DebugWithCtx(ctx, "GetCommodityDataPackageByPrice no data")
			return list, nil
		}
		log.ErrorWithCtx(ctx, "GetCommodityDataPackageByPrice error: %v", err)
		return nil, err
	}

	return list, nil
}

// GetCommodityIDListByDiscountPrice 根据价格获取套餐信息
func (s *Mysql) GetCommodityIDListByDiscountPrice(ctx context.Context, req *pb.GetCommodityDataListRequest) ([]uint32, uint32, error) {
	commodityIDList := make([]uint32, 0)
	list := make([]*CommodityDataPackage, 0)
	info := CommodityDataPackage{}
	tableName := info.TableName()
	sql := fmt.Sprintf("select %s from %+v where `discount_price` >= ? AND `discount_price` <= ?", info.SqlString(), tableName)

	switch pb.SortType(req.GetSortData().GetSortType()) {
	case pb.SortType_SORT_TYPE_ASC:
		sql += " order by `discount_price` asc,`package_id` desc"
	default:
		sql += " order by `discount_price` desc,`package_id` desc"
	}

	totalSql := fmt.Sprintf("select count(1) from %+v where `discount_price` >= ? AND `discount_price` <= ?", tableName)
	var totalCnt uint32
	err := s.db.GetContext(ctx, &totalCnt, totalSql, req.GetMinDiscountPrice(), req.GetMaxDiscountPrice())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCommodityIDListByPrice fail err:%v sql:%v", err, sql)
		return commodityIDList, totalCnt, err
	}

	if req.GetPage() > 0 {
		//sql += fmt.Sprintf(" limit %d,%d", (req.GetPage()-1)*req.GetPageSize(), req.GetPageSize())
	}

	err = s.db.SelectContext(ctx, &list, sql, req.GetMinDiscountPrice(), req.GetMaxDiscountPrice())
	if err != nil {
		if mysql.IsNoRowsError(err) {
			log.DebugWithCtx(ctx, "GetCommodityDataPackageByDiscountPrice no data")
			return commodityIDList, totalCnt, nil
		}
		log.ErrorWithCtx(ctx, "GetCommodityDataPackageByDiscountPrice error: %v", sql, err)
		return commodityIDList, totalCnt, err
	}
	for _, v := range list {
		commodityIDList = append(commodityIDList, v.CommodityId)
	}
	return commodityIDList, totalCnt, nil
}

// GetCommodityIDListByPrice 根据价格获取套餐信息
func (s *Mysql) GetCommodityIDListByPrice(ctx context.Context, req *pb.GetCommodityDataListRequest) ([]uint32, uint32, error) {
	commodityIDList := make([]uint32, 0)
	list := make([]*CommodityDataPackage, 0)
	info := CommodityDataPackage{}
	tableName := info.TableName()
	sql := fmt.Sprintf("select %s from %+v where `price` >= ? AND `price` <= ?", info.SqlString(), tableName)

	switch pb.SortType(req.GetSortData().GetSortType()) {
	case pb.SortType_SORT_TYPE_ASC:
		sql += " order by `price` asc, package_id desc"
	default:
		sql += " order by `price` desc, package_id desc"
		break
	}

	totalSql := fmt.Sprintf("select count(1) from %+v where `price` >= ? AND `price` <= ?", tableName)
	var totalCnt uint32
	err := s.db.GetContext(ctx, &totalCnt, totalSql, req.GetMinPrice(), req.GetMaxPrice())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCommodityIDListByPrice fail err:%v sql:%v", err, sql)
		return commodityIDList, totalCnt, err
	}

	if req.GetPage() > 0 {
		//sql += fmt.Sprintf(" limit %d,%d", (req.GetPage()-1)*req.GetPageSize(), req.GetPageSize())
	}

	err = s.db.SelectContext(ctx, &list, sql, req.GetMinPrice(), req.GetMaxPrice())
	if err != nil {
		if mysql.IsNoRowsError(err) {
			log.DebugWithCtx(ctx, "GetCommodityIDListByPrice no data")
			return commodityIDList, totalCnt, nil
		}
		log.ErrorWithCtx(ctx, "GetCommodityIDListByPrice error: %v", err)
		return commodityIDList, totalCnt, err
	}
	for _, v := range list {
		commodityIDList = append(commodityIDList, v.CommodityId)
	}
	return commodityIDList, totalCnt, nil
}
