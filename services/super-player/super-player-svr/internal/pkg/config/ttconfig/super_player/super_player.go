package config

import (
	"errors"
	"fmt"
	"sync/atomic"
	"time"

	pb "golang.52tt.com/protocol/services/superplayersvr"

	"golang.52tt.com/pkg/protocol"

	"golang.52tt.com/services/super-player/super-player-svr/internal/pkg/pay-api"

	"gitlab.ttyuyin.com/avengers/tyr/core/config/ttconfig"
)

type SuperPlayerSvrConfig struct {
	PayLogDays        int   `json:"pay_log_days"`        //可以查多少个天前的付费记录
	ExpireNotifyHours int64 `json:"expire_notify_hours"` //差多少个小时过期算即将过期
	//WebUrl             string `json:"web_url"`               //会员页URL
	IosDelayExpireHour int64 `json:"ios_delay_expire_hour"` //IOS会员签约状态下，延迟会员结束时间，避免会员已经过期，但是IOS支付回调还没到

	IsTest              bool                    `json:"is_test"`              //是否测试用，如果测试有些周期会不一样
	TestPayDay          int64                   `json:"test_pay_day"`         //如果是测试环境，第一次扣款的时间
	CacheSecond         int64                   `json:"cache_second"`         //缓存多少秒
	MaxMemberDays       int64                   `json:"max_member_day"`       //连续可以开通多少个月
	Level2Value         []int64                 `json:"level_2_value"`        //每个等级对应最小会员值
	Level2ReduceVal     []int64                 `json:"level_2_reduce_value"` //每个等级减少的值,下标是等级对应需要扣减的分值
	PackageConfList     []*PackageConf          `json:"package_conf_list"`    //套餐列表
	PackageConfMap      map[string]*PackageConf //
	PayApi              *pay.ApiPara            `json:"pay_api"` //支付相关参数
	CloseErrMap         map[string]bool
	NotBalanceErrMap    map[string]bool
	TestNextPay         bool                    `json:"test_next_pay"`
	PackageWhiteList    []*PackageWhiteListConf `json:"package_white_list_conf"`
	CancelContractHours int64                   `json:"cancel_contract_hours"` //会员过期多少个小时之后，发起签约解约
	ExperienceHours     int64                   `json:"experience_hours"`      //体验时长
	MinMonthLimit       int64                   `json:"min_month_limit"`
	BusinessCfgObj      *BusinessCfg            `json:"business_cfg"`

	ImPushConfs                 []*ImPushConf       `json:"im_push_conf"` //im推送区分app配置
	BusinessCfgList             []*BusinessCfg      `json:"business_cfg_list"`
	CancelContractInfo          *CancelContractConf `json:"cancel_contract_info"`           //支付成功后有签约的情况下，助手消息
	CancelContractNotFound      bool                `json:"cancel_contract_not_found"`      //解约签约时，没找到签约返回成功
	SPSystemBeginTs             int64               `json:"sp_system_begin_ts"`             //会员系统什么时候上的
	SelectContractStep          int64               `json:"select_contract_step"`           //取会员签约时间跨度
	IosNoticeCancelContractTs   float64             `json:"ios_notice_cancel_contract_ts"`  //iOS的签约超过多少秒还没扣款成功会发出助手消息
	AutoPayInterval             int64               `json:"auto_pay_interval"`              //自动扣款定时器间隔
	InitiativeCancelContract    bool                `json:"initiative_cancel_contract"`     //超过扣款时间X天后是否主动发起解约
	InitiativeCancelContractDay int                 `json:"initiative_cancel_contract_day"` //距离扣款多少天后发起主动解约

	RenewNotifyDay int `json:"renew_notify_day"` //扣款多少天前通知续费

	ActivityConfig *ActivityPackageConf `json:"activity_config"` // 活动配置
	FeishuNotice   string               `json:"feishuNotice"`    //飞书告警地址

	SwitchMemberShipRemarkInterface   bool   `json:"switch_member_ship_remark_interface"`   // 是否切换新会员关系模块接口
	RemakeStartTime                   int64  `json:"remake_start_time"`                     // 重构版本接口切换开始时间
	SwitchOrderAndRedemptionInterface bool   `json:"switch_order_and_redemption_interface"` // 是否切换新订单和签约模块接口
	SpecialNotice                     bool   `json:"special_notice"`                        // 特殊通知开关
	YearPackageLimit                  uint32 `json:"year_package_limit"`                    // 年套餐限制（满足多少就为年套餐）

	IsTestReduceValue          bool  `json:"is_test_reduce_value"`            // 是否测试减成长值
	IsTestAutoPay              bool  `json:"is_test_auto_pay"`                // 是否测试自动扣款
	AllSignUserDelayExpireHour int64 `json:"all_sign_user_delay_expire_hour"` // 所有签约用户延迟过期时间

	// 支付宝专用扩展字段
	AliPayDistinctionList []*AliPayPackageTypeDistinction `json:"ali_pay_distinction_list"` // 支付扣款方式的产品码区分
	AliPayProductCodeMap  map[uint32]string               // 支付扣款方式的产品码区分

	// 套餐类型区分支付订单标记
	PayPackageTypeRemarkList []*PayPackageTypeRemark `json:"pay_package_type_remark_list"` // 支付套餐类型备注
	PayPackageTypeRemarkMap  map[uint32]string       // 支付套餐类型备注映射

	IsStartNewTimer                 bool `json:"is_start_new_timer"`                    // 是否开启新的定时器
	IsStartValueTimer               bool `json:"is_start_value_timer"`                  // 是否开启成长值新的定时器
	IsStartExpired                  bool `json:"is_start_expired"`                      // 是否开启过期提醒新的定时器
	IsStartReportSum                bool `json:"is_start_report_sum"`                   // 是否开启报表统计定时器
	IsTestReportSum                 bool `json:"is_test_report_sum"`                    // 是否开启报表统计测试定时器
	IsAutoRepairIOSContractNextTime bool `json:"is_auto_repair_ios_contract_next_time"` // 是否自动修复IOS下次扣款时间
	IsAutoRepairIOSContractStatus   bool `json:"is_auto_repair_ios_contract_status"`    // 是否自动修复IOS签约状态
	NotStartIOSContractCheck        bool `json:"not_start_ios_contract_check"`          // 是否不开启IOS签约状态检查
}

type PackageConf struct {
	ID             string   `json:"id"`
	ProductID      string   `json:"product_id"` //苹果商品ID
	Name           string   `json:"name"`
	Desc           string   `json:"desc"`
	Label          string   `json:"label"`
	OriginalPrice  float32  `json:"original_price"` //原价
	Price          float32  `json:"price"`          //现价
	Months         int64    `json:"months"`
	Days           int64    `json:"days"` //months弃用，使用天周期
	Value          int64    `json:"value"`
	AutoValue      int64    `json:"auto_value"`       //自动续费加的积分
	Auto           bool     `json:"auto"`             //是否自动续约类型
	PayChannelList []string `json:"pay_channel_list"` //支持的支付渠道
	PayChannelMap  map[string]bool
	DiscountPrice  float32 `json:"discount_price"` //套餐优惠价格
	MarketId       uint32  `json:"market_id"`
	PackageStatus  uint32  `json:"package_status"` //包裹下架状态
}

type BusinessCfg struct {
	MarketId         uint32 `json:"market_id"`
	FM               string `json:"fm"`
	BusinessID       string `json:"business_id"`
	PeriodBusinessID string `json:"period_business_id"`
}

// 套餐白名单配置
type PackageWhiteListConf struct {
	SaleId  uint32   `json:"sale_id"`
	UidList []uint32 `json:"uid_list"`
}

// 推送配置
type ImPushConf struct {
	MarketId    uint32 `json:"market_id"`
	Os          uint32 `json:"os"`
	WebUrl      string `json:"web_url"`
	AppName     string `json:"app_name"`
	AppPlatform string `json:"app_platform"`
}

type CancelContractConf struct {
	NoticeCancelContractMsg        map[string]string                `json:"notice_cancel_contract_msg"`      //支付成功后有签约的情况下，助手消息
	NotBalanceCancelContractMsg    map[string]string                `json:"not_balance_cancel_contract_msg"` //自动扣款失败时提示充值，不然扣款失败会自动解约
	RenewalNotifyMsg               map[string]string                `json:"renew_notifyt_msg"`               //提前5天提示扣款消息，如果不需要续签请主动取消订阅(解析参数原来有问题，暂时不动它做兼容)
	IntervalSec                    int64                            `json:"interval_sec"`
	NewRenewalNotifyMsg            *RenewalNotifyMsgInfo            `json:"new_renewal_notify_msg"`              //提前5天提示扣款消息，如果不需要续签请主动取消订阅（重构版本结构）
	NewNotBalanceCancelContractMsg *NotBalanceCancelContractMsgInfo `json:"new_not_balance_cancel_contract_msg"` //自动扣款失败时提示充值，不然扣款失败会自动解约（重构版本结构）
}

type RenewalNotifyMsgInfo struct {
	VipRenewalNotifyMsg  map[string]string `json:"vip_renewal_notify_msg"`  //提前5天提示扣款消息，如果不需要续签请主动取消订阅
	SvipRenewalNotifyMsg map[string]string `json:"svip_renewal_notify_msg"` //提前5天提示扣款消息，如果不需要续签请主动取消订阅
}

type NotBalanceCancelContractMsgInfo struct {
	VipNotBalanceCancelContractMsg  map[string]string `json:"vip_not_balance_cancel_contract_msg"`  //自动扣款失败时提示充值，不然扣款失败会自动解约
	SvipNotBalanceCancelContractMsg map[string]string `json:"svip_not_balance_cancel_contract_msg"` //自动扣款失败时提示充值，不然扣款失败会自动解约
}

// ActivityPackageConf 活动套餐配置
type ActivityPackageConf struct {
	IsOpen         bool              `json:"is_open"`         // 是否开启单次购买限制配置
	LimitTime      int64             `json:"limit_time"`      // 限制时间
	Activities     []ActivityConf    `json:"activities"`      // 限制配置
	TestFailedUid  uint32            `json:"test_failed_uid"` // 测试失败场景使用的uid
	Id2ActivityMap map[string]string // 套餐ID对应关键字
}

// ActivityConf 活动配置
type ActivityConf struct {
	PackageIdList []string `json:"package_id_list"` // 限制的套餐ID列表
	Name          string   `json:"name"`            // 活动名称
}

// PayPackageTypeRemark 支付套餐类型备注
type PayPackageTypeRemark struct {
	PackageType uint32 `json:"package_type"` // 套餐类型（这里同PB里类型一致）
	Remark      string `json:"remark"`       // 套餐类型对应的标记
}

// AliPayPackageTypeDistinction 支付扣款方式的产品码区分
type AliPayPackageTypeDistinction struct {
	MarketId    uint32 `json:"market_id"`    // 新套餐市场支持列表，0:TT，2:欢游，4：T次元，5：麦可，6:迷境
	ProductCode string `json:"product_code"` // 扣款方式的产品码
}

// Format
// 配置加载时调用Format报错则返回错误
// 配置更新时调用Format报错则放弃此次更新
func (s *SuperPlayerSvrConfig) Format() error {
	s.PackageConfMap = make(map[string]*PackageConf)
	for _, p := range s.PackageConfList {
		p.PayChannelMap = make(map[string]bool)
		for _, ch := range p.PayChannelList {
			p.PayChannelMap[ch] = true
		}
		s.PackageConfMap[p.ID] = p
	}

	s.CloseErrMap = make(map[string]bool)
	for _, e := range s.PayApi.CloseOrderErrList {
		s.CloseErrMap[e] = true
	}

	s.NotBalanceErrMap = make(map[string]bool)
	for _, e := range s.PayApi.NotBalanceErrList {
		s.NotBalanceErrMap[e] = true
	}

	if s.ActivityConfig != nil {
		s.ActivityConfig.Id2ActivityMap = make(map[string]string)
		for _, activity := range s.ActivityConfig.Activities {
			for _, packageId := range activity.PackageIdList {
				s.ActivityConfig.Id2ActivityMap[packageId] = activity.Name
			}
		}
	}

	s.AliPayProductCodeMap = make(map[uint32]string)
	if len(s.AliPayDistinctionList) != 0 {
		for _, distinction := range s.AliPayDistinctionList {
			s.AliPayProductCodeMap[distinction.MarketId] = distinction.ProductCode
		}
	}

	s.PayPackageTypeRemarkMap = make(map[uint32]string)
	if len(s.PayPackageTypeRemarkList) != 0 {
		for _, remark := range s.PayPackageTypeRemarkList {
			s.PayPackageTypeRemarkMap[remark.PackageType] = remark.Remark
		}
	}

	return nil
}

type DynamicConfig struct {
	atomicSuperPlayerSvrConfig *atomic.Value
}

// InitSuperPlayerConfig
// 可以选择外部初始化或者直接init函数初始化
func InitSuperPlayerConfig() (*DynamicConfig, error) {
	cfg := &SuperPlayerSvrConfig{}
	atomCfg, err := ttconfig.AtomLoad("super-player", cfg)
	if nil != err {
		return nil, err
	}
	dyConf := &DynamicConfig{}
	dyConf.atomicSuperPlayerSvrConfig = atomCfg
	return dyConf, nil
}

func (d *DynamicConfig) GetSuperPlayerSvrConfig() *SuperPlayerSvrConfig {
	return d.atomicSuperPlayerSvrConfig.Load().(*SuperPlayerSvrConfig)
}

func (d *DynamicConfig) GetLevelByValue(val int64) int64 {

	conf := d.GetSuperPlayerSvrConfig()
	if conf == nil {
		return 0
	}

	var level int64 = 0

	for l, v := range conf.Level2Value {
		if val >= v {
			level = int64(l + 1)
		} else {
			break
		}
	}
	return level
}

func (d *DynamicConfig) GetIosDelayExpireHour() int64 {

	conf := d.GetSuperPlayerSvrConfig()
	if conf != nil && conf.IosDelayExpireHour > 0 {
		return conf.IosDelayExpireHour
	}

	return 0
}

func (d *DynamicConfig) GetCacheSecond() int64 {
	conf := d.GetSuperPlayerSvrConfig()
	if conf == nil {
		return 3
	}
	return conf.CacheSecond
}

func (d *DynamicConfig) IsSinglePurchaseLimitOpen() bool {
	conf := d.GetSuperPlayerSvrConfig()
	if conf == nil {
		return false
	}
	return conf.ActivityConfig.IsOpen
}

func (d *DynamicConfig) GetSinglePurchaseLimitKeyword(packageId string) (key string, limitTime int64) {
	conf := d.GetSuperPlayerSvrConfig()
	if conf == nil || conf.ActivityConfig.Id2ActivityMap == nil {
		return "", 0
	}
	return conf.ActivityConfig.Id2ActivityMap[packageId], conf.ActivityConfig.LimitTime
}

// IsActivityPackage 是否活动套餐
func (d *DynamicConfig) IsActivityPackage(packageId string) bool {
	conf := d.GetSuperPlayerSvrConfig()
	if nil != conf && conf.ActivityConfig != nil {
		_, ok := conf.ActivityConfig.Id2ActivityMap[packageId]
		return ok
	}
	return false
}

func (d *DynamicConfig) GetPackageWhiteListById(saleId uint32) []uint32 {
	var whiteList []uint32
	conf := d.GetSuperPlayerSvrConfig()
	if conf == nil {
		return whiteList
	}

	for _, config := range conf.PackageWhiteList {
		if config.SaleId == saleId {
			return config.UidList
		}
	}

	return whiteList
}

func (d *DynamicConfig) GetMemberShipChangeTableTime() time.Time {
	conf := d.GetSuperPlayerSvrConfig()
	if conf == nil || conf.RemakeStartTime == 0 {
		return time.Date(2024, 4, 1, 12, 0, 0, 0, time.Local) // 默认时间2024年4月1日12：00
	}
	return time.Unix(conf.RemakeStartTime, 0)
}

func (d *DynamicConfig) IsStartNewMembership() bool {
	conf := d.GetSuperPlayerSvrConfig()
	if conf == nil {
		return false
	}
	return conf.SwitchMemberShipRemarkInterface
}

func (d *DynamicConfig) GetPayLogDays() int {
	conf := d.GetSuperPlayerSvrConfig()
	days := conf.PayLogDays
	if days == 0 {
		return 364
	}
	return days
}

func (d *DynamicConfig) GetPayApiPara() *pay.ApiPara {
	conf := d.GetSuperPlayerSvrConfig()
	if conf == nil {
		return nil
	}
	return conf.PayApi
}

func (d *DynamicConfig) GetExpireNotifyHours() int64 {
	conf := d.GetSuperPlayerSvrConfig()
	if conf == nil {
		return 0
	}

	return conf.ExpireNotifyHours
}

func (d *DynamicConfig) GetContractNotifyUrl() string {
	conf := d.GetSuperPlayerSvrConfig()
	if conf == nil {
		return ""
	}
	return conf.PayApi.ContractNotifyUrl
}

func (d *DynamicConfig) GetBusinessCfg(marketId uint32, auto bool) (string, string, error) {
	conf := d.GetSuperPlayerSvrConfig()
	if conf == nil {
		return "", "", errors.New("GetBusinessCfg fail")
	}

	for _, o := range conf.BusinessCfgList {
		if o.MarketId == marketId {
			bid := o.BusinessID
			if auto {
				bid = o.PeriodBusinessID
			}
			fm := o.FM
			return bid, fm, nil
		}
	}

	return "", "", errors.New("GetBusinessCfg fail")
}

const bothCancelContract = "both"

func (d *DynamicConfig) GetCancelContractMsg(payChannel string, both bool) (string, int64) {
	conf := d.GetSuperPlayerSvrConfig()
	if conf == nil || conf.CancelContractInfo == nil {
		return "", 0
	}
	if both {
		return conf.CancelContractInfo.NoticeCancelContractMsg[bothCancelContract], conf.CancelContractInfo.IntervalSec
	}
	return conf.CancelContractInfo.NoticeCancelContractMsg[payChannel], conf.CancelContractInfo.IntervalSec
}

func (d *DynamicConfig) IsStartNewOrderSvr() bool {
	conf := d.GetSuperPlayerSvrConfig()
	if conf == nil {
		return false
	}
	return conf.SwitchOrderAndRedemptionInterface
}

func (d *DynamicConfig) GetMaxDays() int64 {
	conf := d.GetSuperPlayerSvrConfig()
	if nil == conf || conf.MaxMemberDays == 0 {
		return 1825
	}
	return conf.MaxMemberDays
}

// 体验套餐天数
func (d *DynamicConfig) GetExperienceHours() int64 {
	conf := d.GetSuperPlayerSvrConfig()
	if conf == nil || conf.ExperienceHours == 0 {
		return 12
	}
	return conf.ExperienceHours
}

func (d *DynamicConfig) IsTestNextPay() bool {
	conf := d.GetSuperPlayerSvrConfig()
	if nil == conf {
		return false
	}
	return conf.TestNextPay
}

func (d *DynamicConfig) TestNextPayDay() int64 {
	conf := d.GetSuperPlayerSvrConfig()
	payDay := 0
	if nil != conf && conf.TestPayDay != 0 {
		payDay = int(conf.TestPayDay)
	}
	return int64(payDay)
}

func (d *DynamicConfig) GetSpecialNotice() bool {
	conf := d.GetSuperPlayerSvrConfig()
	if nil == conf {
		return false
	}
	return conf.SpecialNotice
}

func (d *DynamicConfig) IsYearPack(days uint32) bool {
	conf := d.GetSuperPlayerSvrConfig()
	if nil == conf || conf.YearPackageLimit == 0 {
		return days >= 360
	}
	return days >= conf.YearPackageLimit
}

func (d *DynamicConfig) IsTestReduceValue() bool {
	conf := d.GetSuperPlayerSvrConfig()
	if nil == conf {
		return false
	}
	return conf.IsTestReduceValue
}

func (d *DynamicConfig) GetAllSignUserDelayExpireHour() int64 {
	conf := d.GetSuperPlayerSvrConfig()
	if conf != nil && conf.AllSignUserDelayExpireHour > 0 {
		return conf.AllSignUserDelayExpireHour
	}
	return 0
}

func (d *DynamicConfig) GetSPSystemBeginTs() (int64, int64) {
	var defaultTs, step int64 = 1625500800, 86400
	conf := d.GetSuperPlayerSvrConfig()
	if conf != nil {
		if conf.SPSystemBeginTs > 0 {
			defaultTs = conf.SPSystemBeginTs
		}
		if conf.SelectContractStep > 0 {
			step = conf.SelectContractStep
		}
	}
	return defaultTs, step
}

func (d *DynamicConfig) RenewalNotifyDay() int {
	days := 5
	conf := d.GetSuperPlayerSvrConfig()
	if conf != nil {
		if conf.RenewNotifyDay > 0 {
			days = conf.RenewNotifyDay
		}
	}
	return days
}

func (d *DynamicConfig) GetRenewalNotifyMsg(os protocol.OS, ts time.Time, packageType uint32) string {
	msg := ""
	conf := d.GetSuperPlayerSvrConfig()
	if conf != nil && conf.CancelContractInfo != nil && conf.CancelContractInfo.NewRenewalNotifyMsg != nil {
		msgInfo := conf.CancelContractInfo.NewRenewalNotifyMsg
		switch pb.PackageType(packageType) {
		case pb.PackageType_ENUM_PACKAGE_TYPE_NORMAL:
			if msgInfo.VipRenewalNotifyMsg != nil {
				msg = msgInfo.VipRenewalNotifyMsg[os.String()]
			}
		case pb.PackageType_ENUM_PACKAGE_TYPE_SVIP:
			if msgInfo.SvipRenewalNotifyMsg != nil {
				msg = msgInfo.SvipRenewalNotifyMsg[os.String()]
			}
		}
	}
	if len(msg) > 0 {
		return fmt.Sprintf(msg, ts.Year(), int(ts.Month()), ts.Day(), ts.Hour(), ts.Minute())
	}
	if packageType == uint32(pb.PackageType_ENUM_PACKAGE_TYPE_NORMAL) {
		msg = "您购买的超级玩家【连续包月】套餐即将自动续费，如您需取消订阅，请在%04v年%02v月%02v日%02v:%02v前，前往手机【支付宝】APP-【我的】-【（右上角）设置】-【支付设置】-【免密支付/自动扣款】中找到对应协议-点击关闭服务-确认关闭。"
		if os == protocol.IOS {
			msg = "您购买的超级玩家【连续包月】套餐即将自动续费，如您需取消订阅，请请在%04v年%02v月%02v日%02v:%02v前，前往手机【设置】-【Apple ID、iChloud+、媒体与购买项目】-【订阅】中找到所购项目并取消订阅。"
		}
		return fmt.Sprintf(msg, ts.Year(), int(ts.Month()), ts.Day(), ts.Hour(), ts.Minute())
	}
	msg = "您购买的超级玩家SVIP【连续包月】套餐即将自动续费，如您需取消订阅，请在%04v年%02v月%02v日%02v:%02v前，前往手机【支付宝】APP-【我的】-【（右上角）设置】-【支付设置】-【免密支付/自动扣款】中找到对应协议-点击关闭服务-确认关闭。"
	if os == protocol.IOS {
		msg = "您购买的超级玩家SVIP【连续包月】套餐即将自动续费，如您需取消订阅，请请在%04v年%02v月%02v日%02v:%02v前，前往手机【设置】-【Apple ID、iChloud+、媒体与购买项目】-【订阅】中找到所购项目并取消订阅。"
	}
	return fmt.Sprintf(msg, ts.Year(), int(ts.Month()), ts.Day(), ts.Hour(), ts.Minute())
}

func (d *DynamicConfig) GetIosNoticeCancelContractTs() float64 {
	var defaultTs float64 = 86400
	conf := d.GetSuperPlayerSvrConfig()
	if conf != nil {
		if conf.IosNoticeCancelContractTs > 0 {
			defaultTs = conf.IosNoticeCancelContractTs
		}
	}
	return defaultTs
}

// GetCancelContractNotifyMsg 扣款失败提示解约助手消息
func (d *DynamicConfig) GetCancelContractNotifyMsg(os protocol.OS, ts time.Time, packageType uint32) string {
	msg := ""
	conf := d.GetSuperPlayerSvrConfig()
	if conf != nil && conf.CancelContractInfo != nil && conf.CancelContractInfo.NewNotBalanceCancelContractMsg != nil {
		msgInfo := conf.CancelContractInfo.NewNotBalanceCancelContractMsg
		switch pb.PackageType(packageType) {
		case pb.PackageType_ENUM_PACKAGE_TYPE_NORMAL:
			if msgInfo.VipNotBalanceCancelContractMsg != nil {
				msg = msgInfo.VipNotBalanceCancelContractMsg[os.String()]
			}
		case pb.PackageType_ENUM_PACKAGE_TYPE_SVIP:
			if msgInfo.SvipNotBalanceCancelContractMsg != nil {
				msg = msgInfo.SvipNotBalanceCancelContractMsg[os.String()]
			}
		}
	}

	if len(msg) > 0 {
		return fmt.Sprintf(msg, ts.Year(), int(ts.Month()), ts.Day(), ts.Hour(), ts.Minute())
	}

	if packageType == uint32(pb.PackageType_ENUM_PACKAGE_TYPE_NORMAL) {
		if os == protocol.IOS {
			return fmt.Sprintf("超级玩家连续订阅套餐续费失败，如未及时扣款系统将自动解约，请在%04v年%02v月%02v日%02v时%02v分前充值账户确保扣款成功",
				ts.Year(), int(ts.Month()), ts.Day(), ts.Hour(), ts.Minute())
		}
		return fmt.Sprintf("当前账户余额不足，超级玩家连续订阅套餐续费失败，如未及时扣款系统将自动解约，请在%04v年%02v月%02v日%02v时%02v分前充值账户确保扣款成功",
			ts.Year(), int(ts.Month()), ts.Day(), ts.Hour(), ts.Minute())
	}
	if os == protocol.IOS {
		return fmt.Sprintf("超级玩家SVIP连续订阅套餐续费失败，如未及时扣款系统将自动解约，请在%04v年%02v月%02v日%02v时%02v分前充值账户确保扣款成功",
			ts.Year(), int(ts.Month()), ts.Day(), ts.Hour(), ts.Minute())
	}
	return fmt.Sprintf("当前账户余额不足，超级玩家SVIP连续订阅套餐续费失败，如未及时扣款系统将自动解约，请在%04v年%02v月%02v日%02v时%02v分前充值账户确保扣款成功",
		ts.Year(), int(ts.Month()), ts.Day(), ts.Hour(), ts.Minute())
}

func (d *DynamicConfig) IsInitiativeCancelContract(nextPayTs time.Time) bool {
	conf := d.GetSuperPlayerSvrConfig()
	if conf == nil || !conf.InitiativeCancelContract {
		return false
	}
	var days = 2
	if conf.InitiativeCancelContractDay > 0 {
		days = conf.InitiativeCancelContractDay
	}
	afterDays := time.Date(nextPayTs.Year(), nextPayTs.Month(), nextPayTs.Day()+days, 0, 0, 0, 0, time.Local)
	if afterDays.Before(time.Now()) {
		return true
	}

	return false
}

func (d *DynamicConfig) IsTestAutoPay() bool {
	conf := d.GetSuperPlayerSvrConfig()
	if nil == conf {
		return false
	}
	return conf.IsTestAutoPay
}

func (d *DynamicConfig) IsNotBalance(err error) bool {
	conf := d.GetSuperPlayerSvrConfig()
	return conf.NotBalanceErrMap[err.Error()]
}

func (d *DynamicConfig) IsCloseOrder(err error) bool {
	conf := d.GetSuperPlayerSvrConfig()
	return conf.CloseErrMap[err.Error()]
}

func (d *DynamicConfig) GetAutoPayInterval() int64 {
	var defaultTs int64 = 30
	conf := d.GetSuperPlayerSvrConfig()
	if conf != nil {
		if conf.AutoPayInterval > 0 {
			defaultTs = conf.AutoPayInterval
		}
	}
	return defaultTs
}

func (d *DynamicConfig) GetCancelContractHours() int64 {
	conf := d.GetSuperPlayerSvrConfig()
	var hours int64 = 48
	if nil != conf && conf.CancelContractHours > 0 {
		hours = conf.CancelContractHours
	}
	return hours
}

func (d *DynamicConfig) GetRemarkAndProductCode(packageType uint32, marketId uint32, isIos bool, isWeiXinPay bool) (string, string) {
	conf := d.GetSuperPlayerSvrConfig()
	if nil == conf {
		return "", ""
	}
	remark, ok := conf.PayPackageTypeRemarkMap[packageType]
	if !ok {
		remark = ""
	}
	if isIos || isWeiXinPay || packageType == uint32(pb.PackageType_ENUM_PACKAGE_TYPE_NORMAL) { // ios或微信支付不需要productCode
		return remark, ""
	}
	productCode, ok := conf.AliPayProductCodeMap[marketId]
	if !ok {
		productCode = ""
	}
	return remark, productCode
}

func (d *DynamicConfig) IsTest() bool {
	conf := d.GetSuperPlayerSvrConfig()
	if nil == conf {
		return false
	}
	return conf.IsTest
}

func (d *DynamicConfig) IsStartNewTimer() bool {
	conf := d.GetSuperPlayerSvrConfig()

	if conf != nil {
		return conf.IsStartNewTimer
	}
	return false
}

func (d *DynamicConfig) IsSpecialNotice() bool {
	conf := d.GetSuperPlayerSvrConfig()
	if nil == conf {
		return false
	}
	return conf.SpecialNotice
}

func (d *DynamicConfig) IsStartValueTimer() bool {
	conf := d.GetSuperPlayerSvrConfig()

	if conf != nil {
		return conf.IsStartValueTimer
	}
	return false
}

func (d *DynamicConfig) IsStartExpired() bool {
	conf := d.GetSuperPlayerSvrConfig()

	if conf != nil {
		return conf.IsStartExpired
	}
	return false
}

func (d *DynamicConfig) IsStartReportSum() bool {
	conf := d.GetSuperPlayerSvrConfig()

	if conf != nil {
		return conf.IsStartReportSum
	}
	return false
}

func (d *DynamicConfig) IsTestReportSum() bool {
	conf := d.GetSuperPlayerSvrConfig()

	if conf != nil {
		return conf.IsTestReportSum
	}
	return false
}

func (d *DynamicConfig) GetCheckIOSContractConfig() (bool, bool) {
	conf := d.GetSuperPlayerSvrConfig()
	if conf != nil {
		return conf.IsAutoRepairIOSContractNextTime, conf.IsAutoRepairIOSContractStatus
	}
	return false, false
}

func (d *DynamicConfig) NotStartIOSContractCheck() bool {
	conf := d.GetSuperPlayerSvrConfig()
	if conf != nil {
		return conf.NotStartIOSContractCheck
	}
	return false
}
