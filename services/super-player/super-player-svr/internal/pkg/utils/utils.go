package utils

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/protocol"
	"strconv"
	"strings"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	pb "golang.52tt.com/protocol/services/superplayersvr"
	"golang.52tt.com/services/super-player/super-player-svr/internal/pkg/pay-api"
)

const (
	APPSTORE             = "APPSTORE"
	ALIPAY               = "ALIPAY"
	WECHAT               = "WECHAT"
	PERIOD_SANDBOX       = "PERIOD_SANDBOX"
	SANDBOX              = "SANDBOX"
	OrderVipPrefix       = "sp"
	OrderSvipPrefix      = "svip"
	OrderAutoVipPrefix   = "auto"
	OrderAutoXVipPrefix  = "autox"
	OrderAutoSvipPrefix  = "autosvip"
	OrderAutoXSvipPrefix = "autoxsvip"

	INIT_CONTRACT          = "INIT_CONTRACT"
	SIGN_CONTRACT          = "SIGN_CONTRACT"
	RESCIND_CONTRACT       = "RESCIND_CONTRACT"
	SPSYS_RESCIND_CONTRACT = "SPSYS_RESCIND_CONTRACT" //会员服务的签约状态，购买第二个套餐的时候标志这个状态，然后等待发起解约进入RESCIND_CONTRACT状态
	SPSYS_SIGN_CONTRACT    = "SPSYS_SIGN_CONTRACT"    //会员服务的签约状态，购买第二个套餐的时候标志这个状态，然后等待发起解约进入RESCIND_CONTRACT状态

	WebSource = "WebSource"

	DeadlineExpireHours = 72 * 60 * 60 // 会员定义的即将过期时间范围(72小时)

	AllMarketId = -1

	// 支付类型（下单时使用，兼容旧版本）
	OrderTypeBuy     = "BUY"     // 购买
	OrderTypePeriod  = "PERIOD"  // 试用
	OrderTypeUpgrade = "UPGRADE" // 升级

	// 支付相关接口返回码
	ContractCancelCode = "30000" //接口失败
	CancelResult       = "11000" //已经解约
	SuccessResult      = "10000" //操作成功

	// web传上来的os_type，历史原因，
	OsTypeIOS     = "i"
	OsTypeAndroid = "a"

	// 支付宝模版固定参数
	AliPayVIPPlanId  = "INDUSTRY|SOCIALIZATION"
	AliPaySVIPPlanId = "INDUSTRY|PERIOD_SVIP"
)

func IsAppstore(payChannel string) bool {
	// 如果是沙箱也更新
	if strings.Contains(payChannel, PERIOD_SANDBOX) {
		return true

	}
	return strings.Contains(payChannel, APPSTORE)
}

func TryCall(f func() error, count int, interval time.Duration) error {
	var err error
	for i := 0; i < count; i++ {
		if err = f(); nil == err {
			return nil
		}
		time.Sleep(interval)
	}
	return err
}

// GetStatusWithPrivilege 用于判断客户端外显字段类型
func GetStatusWithPrivilege(ctx context.Context, info *pb.SuperPlayerInfo, contracts []*pb.SuperPlayerContractInfo, now time.Time) (
	vipStatus, svipStatus pb.SuperPlayerStatus, vipSignType pb.SuperPlayerVipType, entryAdvStatus pb.EntryAdvStatus) {
	// 判断svip类型
	{
		// 先赋值没开通状态
		if !info.IsSignedSvip {
			svipStatus = pb.SuperPlayerStatus_ENUM_STATUS_NO_OPEN
		}
		if info.SvipExpireTimestamp <= now.Unix() && info.IsSignedSvip { // 如果过期了，且开通了，状态为过期
			svipStatus = pb.SuperPlayerStatus_ENUM_STATUS_EXPIRED
		}
		if info.SvipExpireTimestamp > now.Unix() { // 如果没过期，且开通了，状态为开通
			svipStatus = pb.SuperPlayerStatus_ENUM_STATUS_OPENING
			// 72小内过期就设置为即将过期
			if info.SvipExpireTimestamp-now.Unix() <= int64(DeadlineExpireHours) && info.IsSignedSvip { // 仅当是付费用户并且即将过期才显示为即将过期
				svipStatus = pb.SuperPlayerStatus_ENUM_STATUS_SOON_EXPIRE
			}
			// 修正vip和svip状态，如果有签约就显示签约中
			for _, contract := range contracts {
				if contract.Status == SIGN_CONTRACT && contract.GetPackageType() == pb.PackageType_ENUM_PACKAGE_TYPE_SVIP {
					svipStatus = pb.SuperPlayerStatus_ENUM_STATUS_SIGN
					break
				}
			}
		} else { // 其他情况说明没考虑到，就需要看下日志
			log.DebugWithCtx(ctx, "getStatusWithPrivilege invalid status info:%v", info)
		}
	}
	// 判断VIP类型
	{
		// 先赋值没开通状态
		if !info.IsSignedSuperPlayer {
			vipStatus = pb.SuperPlayerStatus_ENUM_STATUS_NO_OPEN
		}
		if info.ExpireTimestamp <= now.Unix() && info.IsSignedSuperPlayer { // 如果过期了，且开通了，状态为过期
			vipStatus = pb.SuperPlayerStatus_ENUM_STATUS_EXPIRED
		}
		if info.ExpireTimestamp > now.Unix() { // 如果没过期，且开通了，状态为开通
			log.DebugWithCtx(ctx, "info:%v", info)
			vipStatus = pb.SuperPlayerStatus_ENUM_STATUS_OPENING
			// 72小内过期就覆盖为即将过期
			if info.ExpireTimestamp-now.Unix() <= int64(DeadlineExpireHours) && info.IsSignedSuperPlayer { // 仅当是付费用户并且即将过期才显示为即将过期
				vipStatus = pb.SuperPlayerStatus_ENUM_STATUS_SOON_EXPIRE
			}
			// 如果有年费会员，就是待生效状态
			if svipStatus == pb.SuperPlayerStatus_ENUM_STATUS_OPENING || svipStatus == pb.SuperPlayerStatus_ENUM_STATUS_SIGN ||
				svipStatus == pb.SuperPlayerStatus_ENUM_STATUS_SOON_EXPIRE {
				vipStatus = pb.SuperPlayerStatus_ENUM_STATUS_PENDING
			}
			for _, contract := range contracts {
				if contract.Status == SIGN_CONTRACT && contract.GetPackageType() == pb.PackageType_ENUM_PACKAGE_TYPE_NORMAL {
					vipStatus = pb.SuperPlayerStatus_ENUM_STATUS_SIGN
					break
				}
			}
			if info.ExpireTimestamp <= info.SvipExpireTimestamp || info.ExpireTimestamp < time.Now().Unix() { // 这种情况说明是SVIP和VIP同时到期，说明VIP已经到期了，VIP就显示已过期
				if info.IsSignedSuperPlayer {
					vipStatus = pb.SuperPlayerStatus_ENUM_STATUS_EXPIRED
				} else { // 体验卡就显示未开通
					vipStatus = pb.SuperPlayerStatus_ENUM_STATUS_NO_OPEN
				}
			}
		} else { // 其他情况说明没考虑到，就需要看下日志
			log.DebugWithCtx(ctx, "getStatusWithPrivilege invalid status info:%v", info)
		}
	}
	// 判断外显标志类型
	{
		// 如果有普通会员，就是普通会员标志
		if info.ExpireTimestamp > now.Unix() {
			vipSignType = pb.SuperPlayerVipType_SUPER_PLAYER_VIP_TYPE_SUPER_PLAYER
			// 如果有年费会员，就是年费会员标志
			if info.YearMemberExpireTimestamp > now.Unix() {
				vipSignType = pb.SuperPlayerVipType_SUPER_PLAYER_VIP_TYPE_SUPER_PLAYER_ANNUAL
			}
		}
		// 如果有SVIP，就是SVIP标志
		if info.SvipExpireTimestamp > now.Unix() {
			vipSignType = pb.SuperPlayerVipType_SUPER_PLAYER_VIP_TYPE_SVIP
			// 如果有年费会员或SVIP年费，就是年费SVIP标志（产品逻辑，虽然有点怪）
			if info.YearMemberExpireTimestamp > now.Unix() || info.SvipYearMemberExpireTimestamp > now.Unix() {
				vipSignType = pb.SuperPlayerVipType_SUPER_PLAYER_VIP_TYPE_SVIP_ANNUAL
			}
		}
	}
	// 判断入口文案类型
	{
		entryAdvStatus = pb.EntryAdvStatus_ENUM_ENTRY_ADV_STATUS_NO_OPEN // 后续入口文案不区分开通状态
	}

	return vipStatus, svipStatus, vipSignType, entryAdvStatus
}

// GenPayCtx 生成支付上下文
func GenPayCtx(subCtx context.Context, uid, marketId uint32, fm string) context.Context {
	subCtx = context.WithValue(subCtx, pay.CtxUidKey, strconv.FormatInt(int64(uid), 10))
	subCtx = context.WithValue(subCtx, pay.CtxFmKey, fm)
	subCtx = context.WithValue(subCtx, pay.CtxMarketIdKey, strconv.FormatInt(int64(marketId), 10))
	return subCtx
}

func GetMonthStr(ts time.Time) string {
	return fmt.Sprintf("%v月%v日%02v:%02v:%02v", int(ts.Month()), ts.Day(), ts.Hour(), ts.Minute(), ts.Second())
}

func GetTimeStr(ts time.Time) string {
	return ts.Format("2006-01-02 15:04:05")
}

func GetMonStr(ts time.Time) string {
	return ts.Format("2006-01")
}

func GetDayStr(ts time.Time) string {
	return ts.Format("2006-01-02")
}

func GetHourStr(ts time.Time) string {
	return ts.Format("2006-01-02-15")
}

func GetMinStr(ts time.Time) string {
	return ts.Format("2006-01-02-15-04")
}

func GetUTS() *time.Location {
	uts, err := time.LoadLocation("Asia/Shanghai")
	if nil != err {
		uts = time.FixedZone("CST", 8*3600)
	}
	return uts
}

func GetOrderType(isAuto bool) string {
	orderType := OrderTypeBuy
	if isAuto {
		orderType = OrderTypePeriod
	}
	return orderType
}

func ParseTimeFromString(str string) (time.Time, error) {
	return time.ParseInLocation("2006-01-02 15:04:05", str, time.Local)
}

func GetOsType(payChannelList []string) string {
	for _, payChannel := range payChannelList {
		if strings.Contains(payChannel, APPSTORE) {
			return OsTypeIOS
		}
	}
	return OsTypeAndroid
}

func GetMarketName(marketId uint32) string {
	switch marketId {
	case uint32(protocol.TT_MarketID):
		return "TT"
	case uint32(protocol.HUANYOU_MarketID):
		return "欢游"
	case uint32(protocol.MIC_MarketID):
		return "麦可"
	}
	return "未知"
}
