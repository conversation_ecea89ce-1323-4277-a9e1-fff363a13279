package client

import (
	"context"
	"time"

	"golang.52tt.com/services/super-player/super-player-svr/internal/old-version/rpcs"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	User "golang.52tt.com/clients/account"
	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	pushNotification "golang.52tt.com/clients/push-notification/v2"
	spDressCli "golang.52tt.com/clients/super-player-dress"
	userol "golang.52tt.com/clients/user-online"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/marketid_helper"
	"golang.52tt.com/pkg/protocol"
	gaPush "golang.52tt.com/protocol/app/push"
	"golang.52tt.com/protocol/app/superplayerlogic"
	syncPB "golang.52tt.com/protocol/app/sync"
	apiPB "golang.52tt.com/protocol/services/apicenter/apiserver"
	pushPb "golang.52tt.com/protocol/services/push-notification/v2"
	"golang.52tt.com/protocol/services/superplayerdress"
	"golang.52tt.com/services/notify"
	"golang.52tt.com/services/super-player/super-player-svr/internal/old-version/conf"
)

type Client struct {
	pushCli    *pushNotification.Client
	userOlCli  *userol.Client
	apiClient  *apicenter.Client
	spDressCli *spDressCli.Client
}

func NewClient() *Client {
	client_ := &Client{}
	pushCli, _ := pushNotification.NewClient()
	client_.pushCli = pushCli
	userOlCli, _ := userol.NewClient()
	client_.userOlCli = userOlCli
	apiClient := apicenter.NewClient()
	client_.apiClient = apiClient
	spDressCli_, _ := spDressCli.NewClient()
	client_.spDressCli = spDressCli_
	return client_
}

// PushUserMsg 推送用户消息
func (c *Client) PushUserMsg(ctx context.Context, subMsg []byte, msgTy superplayerlogic.SuperPlayerInfoPushMsg_ENUM_SUPER_PLAYER_PUSH_MSG, uid uint32) error {
	msgBin, err := proto.Marshal(&superplayerlogic.SuperPlayerInfoPushMsg{
		MsgType: msgTy, //superplayerlogic.SuperPlayerInfoPushMsg_ENUM_SUPER_PLAYER_INFO_CHANGE
		MsgBin:  subMsg,
	})

	if nil != err {
		return err
	}

	pushMessage := &gaPush.PushMessage{
		Cmd:     uint32(gaPush.PushMessage_SUPER_PLAYER_MSG),
		Content: msgBin,
	}
	pushMessageBytes, _ := proto.Marshal(pushMessage)
	notification := &pushPb.CompositiveNotification{
		Sequence:           uint32(time.Now().Unix()),
		TerminalTypeList:   []uint32{protocol.MobileAndroidTT, protocol.WindowsTT}, // 这里原代码也不推送IOS，不知道为啥。后面如果有问题再改
		TerminalTypePolicy: pushNotification.DefaultPolicy,
		AppId:              0,
		ProxyNotification: &pushPb.ProxyNotification{
			Type:    uint32(pushPb.ProxyNotification_PUSH),
			Payload: pushMessageBytes,
		},
	}

	err = c.pushCli.PushToUsers(ctx, []uint32{uid}, notification)
	if err != nil {
		log.ErrorWithCtx(ctx, "PushUserMsg perr:%v cmd:%v uids:%v", err, msgTy, []uint32{uid})
		return err
	}

	return nil
}

// SendImMsg 推送IM消息
func (c *Client) SendImMsg(uid uint32, content, hlight string) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()
	log.DebugWithCtx(ctx, "SendImMsg uid:%d content:%s hlight:%s", uid, content, hlight)

	// 获取用户在线平台信息
	onlineInfo, err := c.userOlCli.GetLastMobileOnlineInfo(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendImMsg GetLastMultiOnlineInfo failed uid:%d err:%v", uid, err)
		return err
	}

	if onlineInfo.GetUid() != uid {
		log.InfoWithCtx(ctx, "SendImMsg user no onLineInfo uid:%d info:%v", uid, onlineInfo)
		return nil
	}

	_, os, _ := protocol.UnPackTerminalType(onlineInfo.TerminalType)
	pushConf := conf.GetImPushConf(onlineInfo.MarketId, uint32(os))
	if pushConf.WebUrl == "" {
		log.InfoWithCtx(ctx, "SendImMsg user no push conf uid:%d marketId:%d", uid, onlineInfo.MarketId, os)
		return nil
	}

	msg := new(apiPB.ImMsg)
	msg.ImType = &apiPB.ImType{
		SenderType:   uint32(apiPB.IM_SENDER_TYPE_IM_SENDER_NORMAL),
		ReceiverType: uint32(apiPB.IM_RECEIVER_TYPE_IM_RECEIVER_USER),
		ContentType:  uint32(apiPB.IM_CONTENT_TYPE_IM_CONTENT_TEXT),
	}
	msg.FromUid = 10000 // TT语音助手
	msg.ToIdList = []uint32{uid}
	msg.ImContent = &apiPB.ImContent{}
	msg.ImContent.TextHlUrl = &apiPB.ImTextWithHighlightUrl{
		Content:    content,
		Hightlight: hlight,
		Url:        pushConf.WebUrl,
	}
	msg.ImType.ContentType = uint32(apiPB.IM_CONTENT_TYPE_IM_CONTENT_TEXT_WITH_HL_URL)
	msg.Platform = apiPB.Platform_UNSPECIFIED
	msg.AppPlatform = pushConf.AppPlatform
	msg.AppName = marketid_helper.GetAppName(onlineInfo.MarketId)

	log.InfoWithCtx(ctx, "SendImMsg uid:%d content:%s hlight:%s", uid, content, hlight)
	err = c.apiClient.SendImMsg(ctx, uid, protocol.TT, []*apiPB.ImMsg{msg}, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendImMsg fail uid:%d err:%v", uid, err)
	}

	notify.NotifySync(uid, syncPB.SyncReq_IM_MSG)
	totalPre, ok := notify.NotifySync(uid, syncPB.SyncReq_IM_MSG)

	log.DebugWithCtx(ctx, "SendImMsg NotifySync uid:%d totalPre:%v ok:%v AppPlatform %v AppName %v", uid, totalPre, ok, msg.AppPlatform, msg.AppName)

	return nil
}

// SetDefaultDressInUse 设置默认装扮
func (c *Client) SetDefaultDressInUse(ctx context.Context, uid uint32) error {
	_, err := c.spDressCli.SetDefaultDressInUse(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetDefaultDressInUse err:%v", err)
		return err
	}
	return nil
}

// GetUserInfo 获取用户信息
func (c *Client) GetUserInfo(ctx context.Context, uid uint32) (*User.User, error) {
	userInfo, err := rpcs.AccountCli.GetUser(ctx, uid)
	if nil != err {
		log.ErrorWithCtx(ctx, "GetUser failed, superPlayerUid:%v err:%v", uid, err)
		return nil, err
	}

	return userInfo, nil
}

func (c *Client) SendDressExperience(ctx context.Context, uid uint32, incrConfPackage uint64, orderId string, reason string, serverTime int64) error {
	if err := rpcs.SpDressCli.SendDressExperience(ctx, &superplayerdress.SendDressExperienceReq{
		SuperPlayerUid:  uid,
		IncrConfPackage: incrConfPackage,
		OrderId:         orderId,
		Reason:          reason,
		ServerTime:      serverTime,
	}); err != nil {
		log.ErrorWithCtx(ctx, "SendDressExperience err:%v", err)
		return err
	}
	return nil
}
