package rpcs

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/clients/account"
	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	pushNotification "golang.52tt.com/clients/push-notification/v2"
	spDressCli "golang.52tt.com/clients/super-player-dress"
	userol "golang.52tt.com/clients/user-online"
	"golang.52tt.com/pkg/datacenter"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/marketid_helper"
	"golang.52tt.com/pkg/protocol"
	gaPush "golang.52tt.com/protocol/app/push"
	"golang.52tt.com/protocol/app/superplayerlogic"
	syncPB "golang.52tt.com/protocol/app/sync"
	apiPB "golang.52tt.com/protocol/services/apicenter/apiserver"
	pushPb "golang.52tt.com/protocol/services/push-notification/v2"
	"golang.52tt.com/services/notify"
	"golang.52tt.com/services/super-player/super-player-svr/internal/old-version/conf"
	"golang.52tt.com/services/super-player/super-player-svr/internal/old-version/mysql"
	"golang.52tt.com/services/super-player/super-player-svr/internal/old-version/utils"
	"time"
)

var PushCli *pushNotification.Client
var ApiClient *apicenter.Client
var SpDressCli *spDressCli.Client
var UserOlCli *userol.Client
var AccountCli *account.Client

func init() {
	PushCli, _ = pushNotification.NewClient()
	ApiClient = apicenter.NewClient()
	SpDressCli, _ = spDressCli.NewClient()
	UserOlCli, _ = userol.NewClient()
	AccountCli, _ = account.NewClient()
}

func PushUserMsg(ctx context.Context, msg []byte, cmd uint32, uidList []uint32, ios bool) error {
	pushMessage := &gaPush.PushMessage{
		Cmd:     cmd,
		Content: msg,
	}
	pushMessageBytes, _ := proto.Marshal(pushMessage)

	terminalList := []uint32{protocol.MobileAndroidTT, protocol.MobileIPhoneTT, protocol.WindowsTT}
	if !ios {
		terminalList = []uint32{protocol.MobileAndroidTT, protocol.WindowsTT}
	}

	notification := &pushPb.CompositiveNotification{
		Sequence:           uint32(time.Now().Unix()),
		TerminalTypeList:   terminalList,
		TerminalTypePolicy: pushNotification.DefaultPolicy,
		AppId:              0,
		ProxyNotification: &pushPb.ProxyNotification{
			Type:    uint32(pushPb.ProxyNotification_PUSH),
			Payload: pushMessageBytes,
		},
	}

	perr := PushCli.PushToUsers(ctx, uidList, notification)

	if perr != nil {
		log.ErrorWithCtx(ctx, "PushUserMsg perr:%v cmd:%v uids:%v", perr, cmd, uidList)
	}

	return perr
}

func NotifyInfoChangeV2(subMsg []byte, msgTy superplayerlogic.SuperPlayerInfoPushMsg_ENUM_SUPER_PLAYER_PUSH_MSG, uid uint32) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*2)
	defer cancel()
	mbin, err := proto.Marshal(&superplayerlogic.SuperPlayerInfoPushMsg{
		MsgType: msgTy, //superplayerlogic.SuperPlayerInfoPushMsg_ENUM_SUPER_PLAYER_INFO_CHANGE
		MsgBin:  subMsg,
	})

	if nil != err {
		return err
	}

	//推送到客户端
	err = PushUserMsg(context.Background(), mbin, uint32(gaPush.PushMessage_SUPER_PLAYER_MSG), []uint32{uid}, false)
	if nil != err {
		log.ErrorWithCtx(ctx, "notifyInfoChangeV2 uid:%v err:%v", uid, err)
		return err
	}

	log.InfoWithCtx(ctx, "notifyInfoChangeV2 msgTy:%v uid:%v", msgTy, uid)

	return nil
}

func ReportContract(ctx context.Context, newContract *mysql.ContractRecord) {
	reportMap := make(map[string]interface{})
	reportMap["uid"] = newContract.Uid //
	reportMap["version"] = newContract.Version
	reportMap["osType"] = newContract.OsType
	reportMap["payChannel"] = newContract.PayChannel
	reportMap["orderId"] = newContract.OrderID
	reportMap["packageId"] = newContract.PackageID //
	reportMap["bundleId"] = newContract.BundleId
	reportMap["contractId"] = newContract.ContractID
	reportMap["sceneId"] = newContract.SceneID
	reportMap["status"] = newContract.Status //
	reportMap["periodType"] = newContract.PeriodType
	reportMap["period"] = newContract.Period

	reportMap["lastTime"] = utils.GetTimeStr(newContract.LastTime)
	reportMap["nextTime"] = utils.GetTimeStr(newContract.NextTime)

	reportMap["createTime"] = utils.GetTimeStr(newContract.CreateTime) //
	reportMap["updateTime"] = utils.GetTimeStr(newContract.UpdateTime) //
	reportMap["totalDate"] = utils.GetTimeStr(time.Now())

	datacenter.StdReportKV(context.Background(), "************", reportMap)

	log.DebugWithCtx(ctx, "reportContract %+v", reportMap)
}

func SendImMsg(uid uint32, content, hlight string) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()

	// 获取用户在线平台信息
	onlineInfo, err := UserOlCli.GetLastMobileOnlineInfo(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendImMsg GetLastMultiOnlineInfo failed uid:%d err:%v", uid, err)
		return err
	}

	if onlineInfo.GetUid() != uid {
		log.InfoWithCtx(ctx, "SendImMsg user no onLineInfo uid:%d info:%v", uid, onlineInfo)
		return nil
	}

	_, os, _ := protocol.UnPackTerminalType(onlineInfo.TerminalType)
	pushConf := conf.GetImPushConf(onlineInfo.MarketId, uint32(os))
	if pushConf.WebUrl == "" {
		log.InfoWithCtx(ctx, "SendImMsg user no push conf uid:%d marketId:%d", uid, onlineInfo.MarketId, os)
		return nil
	}

	msg := new(apiPB.ImMsg)
	msg.ImType = &apiPB.ImType{
		SenderType:   uint32(apiPB.IM_SENDER_TYPE_IM_SENDER_NORMAL),
		ReceiverType: uint32(apiPB.IM_RECEIVER_TYPE_IM_RECEIVER_USER),
		ContentType:  uint32(apiPB.IM_CONTENT_TYPE_IM_CONTENT_TEXT),
	}
	msg.FromUid = 10000 // TT语音助手
	msg.ToIdList = []uint32{uid}
	msg.ImContent = &apiPB.ImContent{}
	msg.ImContent.TextHlUrl = &apiPB.ImTextWithHighlightUrl{
		Content:    content,
		Hightlight: hlight,
		Url:        pushConf.WebUrl,
	}
	msg.ImType.ContentType = uint32(apiPB.IM_CONTENT_TYPE_IM_CONTENT_TEXT_WITH_HL_URL)
	msg.Platform = apiPB.Platform_UNSPECIFIED
	msg.AppPlatform = pushConf.AppPlatform
	msg.AppName = marketid_helper.GetAppName(onlineInfo.MarketId)

	err = ApiClient.SendImMsg(ctx, uid, protocol.TT, []*apiPB.ImMsg{msg}, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendImMsg fail uid:%d err:%v", uid, err)
	}

	notify.NotifySync(uid, syncPB.SyncReq_IM_MSG)
	totalPre, ok := notify.NotifySync(uid, syncPB.SyncReq_IM_MSG)

	log.DebugWithCtx(ctx, "SendImMsg NotifySync uid:%d totalPre:%v ok:%v AppPlatform %v AppName %v", uid, totalPre, ok, msg.AppPlatform, msg.AppName)

	return nil
}
