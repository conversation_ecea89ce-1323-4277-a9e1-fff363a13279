package mgr

import (
	"context"
	"fmt"
	"sort"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/protocol/app/superplayerlogic"
	pb "golang.52tt.com/protocol/services/superplayersvr"
	"golang.52tt.com/services/super-player/super-player-svr/internal/pkg/utils"
)

// sendPayForOtherUserMsg 通知相关人员支付成功
func (m *Manager) sendPayForOtherUserMsg(superPlayerUid, buyerUid uint32, packageType pb.PackageType) error {
	ctx := context.Background()
	userInfo, err := m.Cli.GetUserInfo(ctx, superPlayerUid)
	if nil != err {
		log.ErrorWithCtx(ctx, "sendContractSuccessNotifyMsg GetUser superPlayerUid:%v buyerUid:%v err:%v", superPlayerUid, buyerUid, err)
		return err
	}

	userName := userInfo.GetUsername()
	if len(userName) >= 2 {
		if userName[0:2] == "tt" {
			userName = userName[2:]
		}
	}

	noticeMsg := fmt.Sprintf("同个苹果账号只能为一个ID开通连续订阅服务，您当前的苹果账号已为ID：%v开通连续订阅服务，请登录ID：%v查看", userName, userName)
	payCallBackMsg := &superplayerlogic.PayResultPushMsg{
		Result:         true,
		Reason:         "SUCCESS",
		SuperPlayerUid: superPlayerUid,
		NoticeMsg:      noticeMsg,
	}

	binMsg, _ := proto.Marshal(payCallBackMsg)
	serr := m.Cli.PushUserMsg(ctx, binMsg, superplayerlogic.SuperPlayerInfoPushMsg_ENUM_SUPER_PLAYER_PAY_NOTICE, buyerUid)
	if nil != serr {
		log.ErrorWithCtx(ctx, "sendContractSuccessNotifyMsg NotifyInfoChangeV2 superPlayerUid:%v buyerUid:%v err:%v", superPlayerUid, buyerUid, serr)
		return serr
	}

	nowTs := time.Now()
	ttHelpMsg := fmt.Sprintf("您在%v购买的超级玩家连续包月套餐实际到账账号为{%v}（ID：%v）。温馨提示：一个苹果账号只能为一个app账号开通超级玩家连续订阅服务，您可选择为当前账号开通非连续包月类型的超级玩家套餐",
		utils.GetMonthStr(nowTs), userInfo.GetNickname(), userName)
	if packageType == pb.PackageType_ENUM_PACKAGE_TYPE_SVIP {
		ttHelpMsg = fmt.Sprintf("您在%v购买的超级玩家SVIP连续包月套餐实际到账账号为{%v}（ID：%v）。温馨提示：一个苹果账号只能为一个app账号开通超级玩家连续订阅服务，您可选择为当前账号开通非连续包月类型的超级玩家SVIP套餐",
			utils.GetMonthStr(nowTs), userInfo.GetNickname(), userName)
	}
	serr = utils.TryCall(func() error {
		return m.Cli.SendImMsg(buyerUid, ttHelpMsg, "")
	}, 3, time.Second)
	if nil != serr {
		log.ErrorWithCtx(ctx, "sendContractSuccessNotifyMsg SendImMsg superPlayerUid:%v buyerUid:%v err:%v", superPlayerUid, buyerUid, serr)
		return serr
	}

	return nil
}

// sendContractSuccessNotifyMsg 发送签约成功通知消息
func (m *Manager) sendContractSuccessNotifyMsg(ctx context.Context, superPlayerUid uint32, contractInfo *ContractRecord) error {
	log.DebugWithCtx(ctx, "sendContractSuccessNotifyMsg superPlayerUid:%v contractInfo:%v", superPlayerUid, contractInfo)
	contractList, err := m.getContracts(ctx, superPlayerUid, false, true)
	if nil != err {
		log.ErrorWithCtx(ctx, "sendContractSuccessNotifyMsg getContracts uid:%v err:%v", superPlayerUid, err)
		return err
	}

	payChannelMap := make(map[string]bool)
	var hasAliPayVip, hasAppStoreVip bool
	var hasSignTime int
	log.InfoWithCtx(ctx, "sendContractSuccessNotifyMsg contractList:%v", contractList)
	sort.Slice(contractList, func(i, j int) bool { // 按照签约时间排序，最新的在后面
		return contractList[i].CreateTime.Unix() < contractList[j].CreateTime.Unix()
	})
	for _, contract := range contractList {
		hasSignTime++
		if contract.Status != utils.SIGN_CONTRACT {
			continue
		}
		if contract.PackageType == uint32(pb.PackageType_ENUM_PACKAGE_TYPE_NORMAL) {
			if contract.PayChannel == utils.ALIPAY {
				hasAliPayVip = true
			}
			if contract.PayChannel == utils.APPSTORE {
				hasAppStoreVip = true
			}
		}
		if payChannelMap[contract.PayChannel] {
			continue
		}
		payChannelMap[contract.PayChannel] = true
	}

	var ttHelpMsg, hLight string
	hLight = "点击前往>"
	// 超级玩家自动续费服务
	if contractInfo.PackageType == uint32(pb.PackageType_ENUM_PACKAGE_TYPE_NORMAL) && contractInfo.Status == utils.SIGN_CONTRACT {
		ttHelpMsg = fmt.Sprintf("亲爱的用户，您已开通超级玩家自动续费服务，系统将在%v前自动扣款，请确保您的签约账户有足够的余额以避免扣款失败导致超级玩家权益失效。\n若要取消自动续费，可前往超级玩家主页查看指引。点击前往>", contractInfo.NextTime.Format("2006-01-02 15:04:05"))
		if hasAliPayVip && hasAppStoreVip {
			ttHelpMsg = "您的账户同时存在苹果App Store和支付宝签约的连续订阅套餐，如需取消连续订阅，请在苹果手机【设置】-【iTunes Store与App Store】中取消订阅；并在支付宝设置中关闭超级玩家连续订阅服务。"
			hLight = ""
		}
	}

	// 超级玩家SVIP自动续费服务
	if contractInfo.PackageType == uint32(pb.PackageType_ENUM_PACKAGE_TYPE_SVIP) && contractInfo.Status == utils.SIGN_CONTRACT {
		ttHelpMsg = fmt.Sprintf("亲爱的用户，您已开通超级玩家SVIP自动续费服务，系统将在%v前自动扣款，请确保您的签约账户有足够的余额以避免扣款失败导致超级玩家权益失效。\n若要取消自动续费，可前往超级玩家主页查看指引。点击前往>", contractInfo.NextTime.Format("2006-01-02 15:04:05"))
		if hasAliPayVip {
			ttHelpMsg = fmt.Sprintf("您已开通超级玩家SVIP自动续费服务，由于SVIP覆盖了全部普通超级玩家权益，已帮您自动取消了普通超级玩家连续订阅。系统将在%v前自动扣款，请确保您的签约账户有足够的余额以避免扣款失败导致超级玩家权益失效。\n若要取消自动续费，可前往超级玩家主页查看指引。点击前往>", contractInfo.NextTime.Format("2006-01-02 15:04:05"))
		}
		if hasAppStoreVip {
			ttHelpMsg = "您的账户同时存在普通超级玩家连续订阅与超级玩家SVIP连续订阅签约，如需取消普通超级玩家签约，请您前往苹果手机设置-【Apple ID、iCloud+、媒体与购买项目>】-【订阅】中取消签约，已获取的普通超级玩家天数将在SVIP到期后继续生效。"
			hLight = ""
		}
	}

	log.DebugWithCtx(ctx, "sendContractSuccessNotifyMsg ttHelpMsg:%v", ttHelpMsg)
	if ttHelpMsg != "" {
		err := utils.TryCall(func() error {
			return m.Cli.SendImMsg(superPlayerUid, ttHelpMsg, hLight)
		}, 3, time.Second)
		if nil != err {
			log.ErrorWithCtx(ctx, "sendContractSuccessNotifyMsg SendImMsg uid:%v err:%v", superPlayerUid, err)
		}
	} else {
		log.ErrorWithCtx(ctx, "sendContractSuccessNotifyMsg GetCancelContractMsg empty uid:%v", superPlayerUid)
	}
	return nil
}

// sendPaySuccessNotifyMsg 发送支付成功通知消息
func (m *Manager) sendPaySuccessNotifyMsg(ctx context.Context, superPlayerUid uint32, isAuto bool) error {
	log.DebugWithCtx(ctx, "sendPaySucessNotifyMsg superPlayerUid:%v isAuto:%v", superPlayerUid, isAuto)
	contractList, err := m.getContracts(ctx, superPlayerUid, false, true)
	if nil != err {
		log.ErrorWithCtx(ctx, "sendPaySucessNotifyMsg getContracts uid:%v err:%v", superPlayerUid, err)
		return err
	}

	payChannelMap := make(map[string]bool)
	var hasSvip bool
	var hasSignTime int
	log.InfoWithCtx(ctx, "sendPaySucessNotifyMsg contractList:%v", contractList)
	sort.Slice(contractList, func(i, j int) bool { // 按照签约时间排序，最新的在后面
		return contractList[i].CreateTime.Unix() < contractList[j].CreateTime.Unix()
	})
	for _, contract := range contractList {
		hasSignTime++
		if contract.PackageType == uint32(pb.PackageType_ENUM_PACKAGE_TYPE_SVIP) {
			hasSvip = true
		}
		if contract.Status != utils.SIGN_CONTRACT {
			continue
		}
		if payChannelMap[contract.PayChannel] {
			continue
		}
		payChannelMap[contract.PayChannel] = true
	}

	noticeMsg := ""
	//购买非连续套餐才需要推送解约提示助手消息
	if !isAuto {
		for payChannel := range payChannelMap {
			ttHelpMsg, _ := m.DyConf.GetCancelContractMsg(payChannel, false)
			if ttHelpMsg == "" {
				log.ErrorWithCtx(ctx, "sendPaySucessNotifyMsg GetCancelContractMsg empty uid:%v paychannel:%v", superPlayerUid, payChannel)
				continue
			}
			if hasSvip {
				continue
			}
			err := utils.TryCall(func() error {
				return m.Cli.SendImMsg(superPlayerUid, ttHelpMsg, "")
			}, 3, time.Second)
			if nil != err {
				log.ErrorWithCtx(ctx, "sendPaySucessNotifyMsg SendImMsg uid:%v err:%v", superPlayerUid, err)
			}

			log.DebugWithCtx(ctx, "sendPaySucessNotifyMsg SendImMsg uid:%v ttHelpMsg:%v", superPlayerUid, ttHelpMsg)
		}

		both := len(payChannelMap) >= 2
		for payChannel := range payChannelMap {
			noticeMsg, _ = m.DyConf.GetCancelContractMsg(payChannel, both)
			if both {
				break
			}
		}
	}

	payCallBackMsg := &superplayerlogic.PayResultPushMsg{
		Result:         true,
		Reason:         "SUCCESS",
		SuperPlayerUid: superPlayerUid,
		NoticeMsg:      noticeMsg,
	}
	binMsg, _ := proto.Marshal(payCallBackMsg)
	if err := m.Cli.PushUserMsg(ctx, binMsg, superplayerlogic.SuperPlayerInfoPushMsg_ENUM_SUPER_PLAYER_PAY_RESULT, superPlayerUid); err != nil {
		log.ErrorWithCtx(ctx, "sendPaySucessNotifyMsg NotifyInfoChangeV2 uid:%v err:%v", superPlayerUid, err)
	}

	log.DebugWithCtx(ctx, "sendPaySucessNotifyMsg NotifyInfoChangeV2 uid:%v noticeMsg:%v", superPlayerUid, noticeMsg)

	return nil
}
