package mgr

import (
	"context"
	"fmt"
	"strings"
	"time"

	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"

	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/superplayersvr"
	"golang.52tt.com/services/super-player/super-player-svr/internal/pkg/utils"
)

// Order 订单表
type Order struct {
	OrderId       string    `db:"order_id" json:"order_id"`                 // 订单ID
	ContractId    string    `db:"contract_id" json:"contract_id"`           // 签约ID（重构补充字段）
	Uid           uint32    `db:"uid" json:"uid"`                           // 用户ID
	ActiveUid     uint32    `db:"super_player_uid" json:"super_player_uid"` // 生效的uid（兼容旧表旧字段）
	OriginalPrice float32   `db:"original_price" json:"original_price"`     // 原价（套餐价格，重构补充字段）
	Price         float32   `db:"price" json:"price"`                       // 实际支付价格
	PackageId     string    `db:"package_id" json:"package_id"`             // 套餐ID
	PackageName   string    `db:"package_name" json:"package_name"`         // 套餐名称（重构补充字段）
	PackageInfo   string    `db:"package_info" json:"package_info"`         // 套餐信息（为整个下单时套餐的json结构，重构补充字段）
	PackageType   uint32    `db:"package_type" json:"package_type"`         // 套餐类型（重构补充字段）
	Days          uint32    `db:"days" json:"days"`                         // 套餐天数（重构补充字段）
	OrderValue    uint32    `db:"order_value" json:"order_value"`           // 订单成长值
	LeftValue     uint32    `db:"left_value" json:"left_value"`             // 剩余成长值(仅升级订单存在，用于记录其它订单结算过来的剩余成长值)
	PayChannel    string    `db:"pay_channel" json:"pay_channel"`           // 支付渠道
	Status        int8      `db:"status" json:"status"`                     // 订单状态：0-待支付，1-支付成功
	OrderType     int8      `db:"order_type" json:"order_type"`             // 订单类型（主要是标识是不是连续订阅）
	OsType        string    `db:"os_type" json:"os_type"`                   // 操作系统类型
	Version       string    `db:"version" json:"version"`                   // 版本号
	BundleId      string    `db:"bundle_id" json:"bundle_id"`               // APPSTORE商品ID
	ServerTime    time.Time `db:"server_time" json:"server_time"`           // 支付订单生效时间
	CreateTime    time.Time `db:"create_time" json:"create_time"`           // 创建订单时间
	UpdateTime    time.Time `db:"update_time" json:"update_time"`           // 订单更新时间
	MarketId      uint32    `db:"market_id" json:"market_id"`               // 市场ID，区分马甲包
	BeginTime     time.Time `db:"begin_time" json:"begin_time"`             // 订单生效时间（废弃, 新数据将不再记录该数据）
	ExpireTime    time.Time `db:"expire_time" json:"expire_time"`           // 订单失效时间（废弃，新数据将不再记录该数据）
}

// OrderIndex 订单索引表
type OrderIndex struct {
	OrderId            string    `db:"order_id" json:"order_id"`                          // 订单ID
	Uid                uint32    `db:"uid" json:"uid"`                                    // 用户ID
	Days               uint32    `db:"days" json:"days"`                                  // 套餐天数
	LeftDays           uint32    `db:"left_days" json:"left_days"`                        // 剩余天数
	OrderValue         uint32    `db:"order_value" json:"order_value"`                    // 订单成长值
	LeftValue          uint32    `db:"left_value" json:"left_value"`                      // 剩余成长值（当前订单剩余成长值）
	ServerTime         time.Time `db:"server_time" json:"server_time"`                    // 创建订单时间（主要用于核销排序）
	SettleEndTime      time.Time `db:"settle_end_time" json:"settle_end_time"`            // 结算结束时间（用于记录时间）
	IsFinishSettlement bool      `db:"is_finish_settlement" json:"is_finish_settlement"`  // 是否完成结算（该字段主要是为了加速筛选）
	OrderType          int8      `db:"order_type" json:"order_type"`                      // 订单类型（主要是标识是不是连续订阅）
	MarketId           uint32    `db:"market_id" json:"market_id"`                        // 市场ID，区分马甲包
	PackageType        uint32    `db:"package_type" json:"package_type"`                  // 套餐类型
	UpdateTime         time.Time `db:"update_time" json:"update_time" json:"update_time"` // 更新时间
}

// OrderRevokeFlow 撤销订单流水表
type OrderRevokeFlow struct {
	OrderId       string    `db:"order_id" json:"order_id"`               // 订单ID
	Uid           uint32    `db:"uid" json:"uid"`                         // 用户ID
	OrderType     int8      `db:"order_type" json:"order_type"`           // 订单类型
	PackageType   uint32    `db:"package_type" json:"package_type"`       // 套餐类型
	RevokeDays    uint32    `db:"revoke_days" json:"revoke_days"`         // 撤销天数
	RevokeOrderId string    `db:"revoke_order_id" json:"revoke_order_id"` // 撤销订单ID（用于回收权限以及标记订单）
	ServerTime    time.Time `db:"server_time" json:"server_time"`         // 创建订单时间
	CreateTime    time.Time `db:"create_time" json:"create_time"`         // 撤销订单生效时间（如果有回滚订单，则表示回滚订单生效时间）
	UpdateTime    time.Time `db:"update_time" json:"update_time"`         // 更新时间
}

// OrderOpt 订单下单参数
type OrderOpt struct {
	ActiveUid     uint32
	OrderId       string
	ServerTime    time.Time
	PayChannel    string
	TotalPrice    float32
	Status        pb.EnumOrderStatus
	PkgLimitId    uint32
	PkgLimitCount uint32
}

type PlaceOrderResp struct {
	OrderId    string
	Token      string
	ChannelMap string
	CliOrderNo string
	Tsk        string
}

// getOrderPrefix 获取订单前缀
func getOrderPrefix(packageType uint32) string {
	if packageType == uint32(pb.PackageType_ENUM_PACKAGE_TYPE_SVIP) {
		return utils.OrderSvipPrefix
	}
	return utils.OrderVipPrefix
}

// 订单长度不能超40字节，支付组那边有限制
func genOrderID(uid uint32, payChannel, prefix string, ts time.Time) string {
	return fmt.Sprintf("%v_%v_%v_%v", prefix, uid, payChannel, ts.Format("20060102150405"))
}

const defaultPayInterval = 30

// PlaceOrder 下单
func (m *Manager) PlaceOrder(ctx context.Context, payOrder *Order, discountPrice float32, code string, newUser bool, effectTime time.Time, originalTransactionIds []string) (*PlaceOrderResp, error) {
	payApiPara := m.DyConf.GetPayApiPara()
	nowTs := time.Now()
	payOrder.OrderId = genOrderID(payOrder.Uid, payOrder.PayChannel, getOrderPrefix(payOrder.PackageType), nowTs)

	// 检查签约
	auto := payOrder.OrderType == int8(pb.EnumOrderType_ORDER_TYPE_AUTO_PAY)
	if auto {
		// 检查是否重复开通
		err := m.checkContractLimit(ctx, payOrder.Uid, payOrder.PackageType, payOrder.MarketId)
		if err != nil {
			log.ErrorWithCtx(ctx, "PlaceOrder checkContractLimit uid:%v err:%v", payOrder.Uid, err)
			return nil, err
		}
		//连续包X订单时间间隔检查
		err = m.CheckAutoPackageInterval(ctx, payOrder.Uid, payOrder, payApiPara.AutoOrderInterval, payOrder.OrderId, discountPrice != 0)
		if nil != err {
			log.ErrorWithCtx(ctx, "PlaceOrder CheckAutoPackageInterval err:%v", err)
			return nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPacket, "连续订阅不可进行频繁下单操作")
		}
	}

	//订单支付超时分钟
	timeOut := defaultPayInterval
	if payApiPara.TimeOut > 0 {
		timeOut = int(payApiPara.TimeOut)
	}
	SingleAmount := fmt.Sprintf("%.2f", payOrder.Price)

	price := m.getPackPrice(discountPrice, payOrder.Price, newUser)
	payOrder.Price = price
	payOrder.ServerTime = nowTs
	payOrder.CreateTime = nowTs
	payOrder.UpdateTime = nowTs

	// 开启事务
	tx, err := m.Store.GetTx()
	if err != nil {
		log.ErrorWithCtx(ctx, "PlaceOrder GetTx err:%v", err)
		return nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "数据库异常")
	}

	// 创建订单
	err = m.Store.CreateOrder(ctx, payOrder, tx)
	if err != nil {
		log.ErrorWithCtx(ctx, "PlaceOrder PlaceOrder uid:%v err:%v", payOrder.Uid, err)
		if err = tx.Rollback(); err != nil {
			log.ErrorWithCtx(ctx, "PlaceOrder Rollback err:%v", err)
		}
		return nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, fmt.Sprintf("订单创建失败:%v", err))
	}

	// 下单请求
	subCtx, cancel := context.WithTimeout(ctx, time.Second*10)
	defer cancel()
	businessID, fm, err := m.DyConf.GetBusinessCfg(payOrder.MarketId, auto)
	if err != nil {
		if err = tx.Rollback(); err != nil {
			log.ErrorWithCtx(ctx, "PlaceOrder Rollback err:%v", err)
		}
		log.ErrorWithCtx(ctx, "PlaceOrder GetBusinessCfg fail uid:%v err:%v", payOrder.Uid, err)
		return nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, fmt.Sprintf("获取配置文件失败:%v", err))
	}
	remark, productCode := m.DyConf.GetRemarkAndProductCode(payOrder.PackageType, payOrder.MarketId,
		payOrder.OsType == utils.OsTypeIOS, payOrder.PayChannel == utils.WECHAT)
	placeOrderReq := &pb.ApiPlaceOrderReq{
		OrderType:              utils.GetOrderType(auto),
		OsType:                 payOrder.OsType,
		PayChannel:             payOrder.PayChannel,
		BusinessId:             businessID,
		Fm:                     fm,
		Version:                payOrder.Version,
		CliOrderNo:             payOrder.OrderId,
		CliBuyerId:             fmt.Sprintf("%v", payOrder.Uid),
		CliPrice:               fmt.Sprintf("%.2f", price),
		CliOrderTitle:          getOrderTitle(payOrder.PackageName, payOrder.PackageType, false),
		CliOrderDesc:           getOrderTitle(payOrder.PackageName, payOrder.PackageType, false), // 支付那边说是预留的字段，后续有别的用处再改
		CliNotifyUrl:           payApiPara.PayCallBackUrl,
		CreateTime:             utils.GetTimeStr(nowTs),
		Remark:                 remark,
		BundleId:               payOrder.BundleId,
		ProductId:              payOrder.BundleId, // 如果是APPSTORE，这个值就和bundleId一样
		Code:                   code,
		TimeOut:                fmt.Sprintf("%v", timeOut),
		OriginalTransactionIds: originalTransactionIds,
	}
	//如果是签约套餐，加上对应额外参数
	lastTime, nextPay := getNextPayTime(effectTime, int(payOrder.Days))
	if m.DyConf.IsTestNextPay() {
		nextPay = nowTs.AddDate(0, 0, int(m.DyConf.TestNextPayDay()))
	}
	if auto {
		placeOrderReq.PeriodParam = &pb.ApiPeriodParam{
			ContractId:        payOrder.OrderId, //签约ID，跟订单ID一致
			PlanId:            getPlanID(payOrder.PayChannel, payOrder.PackageType),
			PeriodType:        "DAY", //先固定是日类型
			Period:            int64(payOrder.Days),
			ContractNotifyUrl: payApiPara.ContractNotifyUrl,
			ExecuteTime:       utils.GetTimeStr(nextPay), //由算出来的时间为准
			ProductCode:       productCode,
			SingleAmount:      SingleAmount,
		}
	}
	subCtx = utils.GenPayCtx(subCtx, payOrder.Uid, payOrder.MarketId, fm)
	payResp, err := m.Pay.PlaceOrder(subCtx, placeOrderReq)
	if err != nil {
		if err := tx.Rollback(); err != nil {
			log.ErrorWithCtx(ctx, "PlaceOrder Rollback err:%v", err)
		}
		log.ErrorWithCtx(ctx, "PlaceOrder uid:%v placeOrderID:%v err:%v", payOrder.Uid, payOrder.OrderId, err)
		if svrErr, ok := err.(protocol.ServerError); ok {
			if svrErr.Code() == status.ErrSuperPlayerInvalidAppstoreUser {
				return nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidAppstoreUser, svrErr.Message())
			}
		}
		log.ErrorWithCtx(ctx, "PlaceOrder uid:%v placeOrderID:%v err:%v", payOrder.Uid, payOrder.OrderId, err)
		return nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, fmt.Sprintf("下单失败:%v", err))
	}
	outResp := payResp.(pb.ApiPlaceOrderResp)

	// 加一个签约信息
	if auto { //&& payOrder.PayChannel != APPSTORE 这里原本苹果的由签约回调去处理，后来又改成这个了
		newContract := &ContractRecord{
			Uid:         payOrder.Uid,
			Version:     payOrder.Version,
			OsType:      payOrder.OsType,
			PayChannel:  payOrder.PayChannel,
			OrderId:     payOrder.OrderId,
			PackageId:   payOrder.PackageId,
			PackageName: payOrder.PackageName,
			BundleId:    payOrder.BundleId,
			ContractId:  payOrder.OrderId,
			SceneId:     "",
			Status:      utils.INIT_CONTRACT, // 初始化状态，等回调更新状态，否则就是异常状态
			PeriodType:  "DAY",
			Period:      int32(payOrder.Days),
			LastTime:    lastTime,
			NextTime:    nextPay,
			CreateTime:  nowTs,
			UpdateTime:  nowTs,
			MarketId:    payOrder.MarketId,
			PackageType: payOrder.PackageType,
		}
		if cacheErr := m.Cache.DelContractList(ctx, payOrder.Uid); cacheErr != nil { // 双删
			log.ErrorWithCtx(ctx, "PlaceOrder DelContractList uid:%v err:%v", payOrder.Uid, cacheErr)
		}
		log.DebugWithCtx(ctx, "checkContractLimit currContracts:%+v", newContract)

		//把新签约先记录下来
		err = m.Store.CreateOrUpdateContract(ctx, newContract, tx) //更新签约状态
		if err != nil {
			if err = tx.Rollback(); err != nil {
				log.ErrorWithCtx(ctx, "PlaceOrder Rollback err:%v", err)
			}
			log.ErrorWithCtx(ctx, "PlaceOrder CreateOrUpdateContract uid:%v err:%v", payOrder.Uid, err)
			return nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, fmt.Sprintf("更新签约信息失败:%v", err))
		}
		//清掉签约信息缓存
		if cacheErr := m.Cache.DelContractList(ctx, payOrder.Uid); cacheErr != nil {
			log.ErrorWithCtx(ctx, "PlaceOrder DelContractList uid:%v err:%v", payOrder.Uid, cacheErr)
		}
	}

	// 提交事务
	if err = tx.Commit(); err != nil {
		log.ErrorWithCtx(ctx, "PlaceOrder Commit err:%v", err)
		return nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "数据库异常")
	}

	return &PlaceOrderResp{
		OrderId:    outResp.OrderNo,
		Token:      outResp.Token,
		ChannelMap: outResp.ChannelMap,
		CliOrderNo: outResp.CliOrderNo,
		Tsk:        outResp.Tsk,
	}, nil
}

func (m *Manager) getPackPrice(discountPrice float32, price float32, newUser bool) float32 {
	if discountPrice <= 0 {
		return price
	}

	//首次开通享受优惠套餐
	if newUser {
		return discountPrice
	}
	return price
}

func getNextPayTime(ts time.Time, days int) (time.Time, time.Time) {
	nowTs := time.Now()
	if ts.Before(nowTs) {
		ts = nowTs
	}

	nextCycTs := ts.AddDate(0, 0, days)      // 周期到期时间
	nextPayTs := nextCycTs.AddDate(0, 0, -3) // 提前3天发起扣款
	if nextPayTs.Day() == 29 {
		nextPayTs = nextPayTs.AddDate(0, 0, -1)
	}
	return ts, nextPayTs // 调整为返回当前时间和下次扣款时间
}

// getPlanID 获取支付模版ID（目前仅支付宝有）
func getPlanID(payChannel string, packageType uint32) string {
	if payChannel == utils.ALIPAY {
		if packageType == uint32(pb.PackageType_ENUM_PACKAGE_TYPE_SVIP) {
			return utils.AliPaySVIPPlanId // 支付宝SVIP固定参数
		}
		return utils.AliPayVIPPlanId // 支付宝VIP固定参数
	}
	if payChannel == utils.WECHAT {
		return ""
	}
	return ""
}

// getOrderPrefixForAutoX 获取自动续约订单前缀(外部回调下单)
func getOrderPrefixForAutoX(packageType uint32) string {
	if packageType == uint32(pb.PackageType_ENUM_PACKAGE_TYPE_SVIP) {
		return utils.OrderAutoXSvipPrefix
	}
	return utils.OrderAutoXVipPrefix
}

// GenOrderIDForAuto 自动续约订单ID，目前长度为40+，和支付组确认目前不超过60即可（修复存在补单重复订单的情况）
// 这里仅精确到月份，因为自动续约的订单是按月扣款的，且考虑到支付调用为HTTP调用，可能存在失败的场景，保证月不重复即可，月订单表有主键锁
// 这种情况极少，只有当更新签约关系失败时才会出现重复订单
func genOrderIDForAuto(uid uint32, payChannel, prefix string, ts time.Time) string {
	return fmt.Sprintf("%v_%v_%v_%v", prefix, uid, payChannel, ts.Format("20060102150405"))
}

type PlaceAutoPayOrderReq struct {
	ProduceId   string
	ContractId  string
	PkgId       string
	PkgName     string
	PkgMarketId uint32
	PkgStatus   uint32
	OrderValue  uint32
	Price       float32
	Days        uint32
	PkgInfo     string
	BundleId    string
	PackageType uint32
}

// PlaceAutoPayOrder 外部自动下单接口,补单用
// 主要是APPSTORE的签约用户，以及运营补单使用
// 沙箱帐号：云测每4分钟回调一次，一次增加一天，不会自动解除签约；生产每1分钟回调一次，5分钟后自动解除签约关系。
func (m *Manager) PlaceAutoPayOrder(ctx context.Context, req *PlaceAutoPayOrderReq) error {
	ctx, cancel := context.WithTimeout(ctx, time.Minute*3)
	defer cancel()
	log.InfoWithCtx(ctx, "placeAutoPayOrder req:%+v", req)

	// 开启事务
	tx, err := m.Store.GetTx()
	if err != nil {
		log.ErrorWithCtx(ctx, "placeAutoPayOrder GetTx req:%+v err:%v", req, err)
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "db error")
	}
	isCommit := false
	defer func() {
		if !isCommit {
			if err = tx.Rollback(); err != nil {
				log.ErrorWithCtx(ctx, "placeAutoPayOrder Rollback req:%+v err:%v", req, err)
			}
		}
	}()
	//defer func() { // 防止崩溃，有些判断是用err.Error()的
	//	if r := recover(); nil != r {
	//		log.ErrorWithCtx(ctx, "placeAutoPayOrder recover produceId:%v contractId:%v r:%v", produceId, contractId, r)
	//		_ = tx.Rollback()
	//	}
	//}()

	contract, err := m.Store.GetContract(ctx, req.ContractId, tx)
	if err != nil {
		log.ErrorWithCtx(ctx, "placeAutoPayOrder GetContract req:%+v err:%v", req, err)
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "invalid contractId")
	}

	if req.PkgMarketId != contract.MarketId {
		log.ErrorWithCtx(ctx, "placeAutoPayOrder produceId not match contract req:%v contract:%+v", req, contract)
		//return errors.New("invalid produceId")
	}

	//订单已经下架,包月的套餐之前订阅的需要继续扣
	if pb.PackageStatus(req.PkgStatus) == pb.PackageStatus_ENUM_PACKAGE_STATUS_STOPING {
		log.ErrorWithCtx(ctx, "placeAutoPayOrder invalid PackageStatus req:%v", req)
	}

	superPlayerUid := contract.Uid
	nowTs := time.Now()
	orderID := genOrderIDForAuto(superPlayerUid, contract.PayChannel, getOrderPrefixForAutoX(contract.PackageType), contract.NextTime)
	isLock, err := m.Cache.LockOrderId(ctx, orderID, time.Minute)
	if err != nil {
		log.ErrorWithCtx(ctx, "placeAutoPayOrder Lock req:%+v orderId:%+v contractId:%v err:%v", req, orderID, err)
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "frequency limit")
	}
	if !isLock {
		log.ErrorWithCtx(ctx, "placeAutoPayOrder Lock req:%v orderID:%v", req, orderID)
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "frequency limit")
	}

	preOrder, err := m.Store.GetOrder(ctx, orderID, nowTs, tx, false)
	if preOrder != nil {
		//没报错，能查到已有订单,并且订单状态是成功状态
		if err == nil && (preOrder.Status == int8(pb.EnumOrderStatus_ORDER_PAY_SUCCESS)) {
			log.ErrorWithCtx(ctx, "placeAutoPayOrder GetOrder ok req:%+v orderID:%v", req, orderID)
			return nil
		}

	}

	//先记录订单到数据库，状态是init状态
	err = m.Store.CreateOrder(ctx, &Order{
		OrderId:       orderID,
		ContractId:    contract.ContractId,
		Uid:           superPlayerUid,
		OriginalPrice: req.Price,
		Price:         req.Price,
		PackageId:     req.PkgId,
		PackageName:   req.PkgName,
		PackageInfo:   req.PkgInfo,
		PackageType:   req.PackageType,
		Days:          req.Days,
		OrderValue:    req.OrderValue,
		PayChannel:    contract.PayChannel,
		Status:        int8(pb.EnumOrderStatus_ORDER_INIT),
		OrderType:     int8(pb.EnumOrderType_ORDER_TYPE_AUTO_PAY),
		OsType:        contract.OsType,
		Version:       contract.Version,
		BundleId:      req.BundleId,
		ServerTime:    nowTs,
		CreateTime:    nowTs,
		UpdateTime:    nowTs,
		MarketId:      contract.MarketId,
	}, tx)
	if err != nil && !mysql.IsDupEntryError(err) {
		log.ErrorWithCtx(ctx, "placeAutoPayOrder PlaceOrder req:%v orderID:%v err:%v", req, orderID, err)
		if err := m.Feishu.SendError(fmt.Sprintf("【超级玩家】自动回调下单失败 req:%v orderID:%v err:%v", req, orderID, err)); err != nil {
			log.ErrorWithCtx(ctx, "placeAutoPayOrder SendError req:%v orderID:%v err:%v", req, orderID, err)
		}
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "创建自动下单订单错误")
	}

	payApiPara := m.DyConf.GetPayApiPara()
	businessID, fm, err := m.DyConf.GetBusinessCfg(contract.MarketId, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "placeAutoPayOrder GetBusinessCfg contractId:%v orderID:%v err:%v", contract.ContractId, orderID, err)
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "GetBusinessCfg fail")
	}
	remark, productCode := m.DyConf.GetRemarkAndProductCode(contract.PackageType, contract.MarketId, contract.OsType == utils.OsTypeIOS, contract.PayChannel == utils.WECHAT)
	autoPayReq := &pb.ApiAutoPayReq{
		OrderType:     "PERIOD",
		OsType:        contract.OsType,
		PayChannel:    contract.PayChannel,
		BusinessId:    businessID,
		Fm:            fm,
		Version:       contract.Version,
		CliOrderNo:    orderID,
		CliBuyerId:    fmt.Sprintf("%v", superPlayerUid),
		CliPrice:      fmt.Sprintf("%.2f", req.Price),
		CliOrderTitle: getOrderTitle(req.PkgName, req.PackageType, false),
		CliOrderDesc:  getOrderTitle(req.PkgName, req.PackageType, false), // 支付那边说是预留的字段，后续有别的用处再改
		CliNotifyUrl:  payApiPara.PayCallBackUrl,
		CreateTime:    utils.GetTimeStr(nowTs),
		Remark:        remark,
		BundleId:      req.BundleId,
		ProductId:     req.BundleId,
		UserIp:        "",
		DeductParam: &pb.DeductParam{
			ContractId:   req.ContractId,
			PlanId:       getPlanID(contract.PayChannel, req.PackageType),
			ProductCode:  productCode, // 支付宝专用
			SingleAmount: fmt.Sprintf("%.2f", req.Price),
		},
	}
	subCtx := utils.GenPayCtx(ctx, contract.Uid, contract.MarketId, fm)
	_, err = m.Pay.AutoPay(subCtx, autoPayReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "placeAutoPayOrder AutoPay req:%v orderID:%v err:%v", req, orderID, err)
		return err
	}

	log.InfoWithCtx(ctx, "placeAutoPayOrder AutoPay Sucess req:%v orderID:%+v ", req, orderID)

	// 更新签约
	contract.PackageId, contract.BundleId = req.PkgId, req.ProduceId //用户可能会在IOS更好套餐
	err = m.Store.CreateOrUpdateContract(ctx, contract, tx)
	if err != nil {
		log.ErrorWithCtx(ctx, "placeAutoPayOrder CreateOrUpdateContract req:%v orderID:%v err:%v", req, orderID, err)
		return err
	}

	isCommit = true
	if err = tx.Commit(); err != nil {
		log.ErrorWithCtx(ctx, "placeAutoPayOrder Commit req:%v orderID:%v err:%v", req, orderID, contract)
	}

	if err = m.Cache.DelContractList(ctx, superPlayerUid); err != nil {
		log.ErrorWithCtx(ctx, "placeAutoPayOrder DelContractList uid:%v err:%v", superPlayerUid, err)
	}

	log.InfoWithCtx(ctx, "placeAutoPayOrder AutoPay success %+v contract:%+v", autoPayReq, contract)
	return nil
}

const defaultAutoPackageInterval = 300

// CheckAutoPackageInterval 检查自动订阅套餐下单间隔
func (m *Manager) CheckAutoPackageInterval(ctx context.Context, uid uint32, payOrder *Order, interval int64, orderId string, hasDiscount bool) error {
	log.DebugWithCtx(ctx, "CheckAutoPackageInterval uid:%v payOrder:%+v interval:%v orderId:%v", uid, payOrder, interval, orderId)

	isLock, err := m.Cache.LockPlaceOrder(ctx, uid, payOrder.OrderId, time.Second)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckAutoPackageInterval LockPlaceOrder uid:%v err:%v", uid, err)
	}
	if !isLock {
		log.ErrorWithCtx(ctx, "CheckAutoPackageInterval orderId:%v uid:%v", orderId, uid)
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "下单太频繁，请稍后操作")
	}

	// 不是签约套餐且没有优惠就不上锁了
	if payOrder.OrderType != int8(pb.EnumOrderType_ORDER_TYPE_AUTO_PAY) && !hasDiscount {
		return nil
	}

	if interval == 0 {
		interval = defaultAutoPackageInterval
	}

	err = m.Cache.AutoOrderIntervalLock(ctx, uid, payOrder.PackageType, orderId, time.Second*time.Duration(interval))
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckAutoPackageInterval AutoOrderIntervalLock uid:%v err:%v", uid, err)
		return err
	}

	return nil
}

// NoticeOrder 更新订单状态
func (m *Manager) NoticeOrder(ctx context.Context, orderOpt *OrderOpt) (*Order, error) {
	log.InfoWithCtx(ctx, "NoticeOrder orderOpt:%+v", orderOpt)

	// 拿操作锁
	locked, err := m.Cache.LockOrderOperate(ctx, orderOpt.ActiveUid, orderOpt.OrderId, time.Second*5)
	if err != nil || !locked {
		log.ErrorWithCtx(ctx, "NoticeOrder LockOrderOperate orderID:%v err:%v", orderOpt.OrderId, err)
		return nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "获取操作锁失败")
	}
	defer func() {
		if deferErr := m.Cache.UnlockOrderOperate(ctx, orderOpt.ActiveUid, orderOpt.OrderId); deferErr != nil {
			log.ErrorWithCtx(ctx, "NoticeOrder UnlockOrderOperate orderID:%v err:%v", orderOpt.OrderId, deferErr)
		}
	}()

	// 判断限购
	if orderOpt.PkgLimitId != 0 {
		isPurchase, err := m.getLimitPurchaseLock(ctx, orderOpt.ActiveUid, orderOpt.OrderId, orderOpt.PkgLimitId, orderOpt.PkgLimitCount)
		if err != nil {
			log.ErrorWithCtx(ctx, "NoticeOrder getLimitPurchaseLock orderID:%v err:%v", orderOpt.OrderId, err)
			return nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "获取限购锁失败")
		}
		if isPurchase {
			log.ErrorWithCtx(ctx, "NoticeOrder getLimitPurchaseLock orderID:%v", orderOpt.OrderId)
			return nil, nil
		}
		defer m.cancelLimitPurchaseLock(ctx, orderOpt.ActiveUid, orderOpt.PkgLimitId, orderOpt.OrderId)
	}

	// 更新订单状态
	newOrder, err := m.Store.NoticeOrder(ctx, orderOpt.OrderId, orderOpt.ActiveUid, orderOpt.ServerTime, orderOpt.TotalPrice, orderOpt.PayChannel, orderOpt.Status, orderOpt.PkgLimitId)
	if err != nil {
		log.ErrorWithCtx(ctx, "NoticeOrder NoticeOrder orderID:%v err:%v", orderOpt.OrderId, err)
		if strings.Contains(fmt.Sprintf("%+v", err), "Not enough days") {
			_ = m.Feishu.SendError(fmt.Sprintf("NoticeOrder orderID:%v err:%v", orderOpt.OrderId, err))
		}
		return nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "更新订单状态失败")
	}
	if newOrder == nil {
		log.ErrorWithCtx(ctx, "NoticeOrder NoticeOrder orderID:%v newOrder is nil", orderOpt.OrderId)
		return nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "订单不存在")
	}

	//开通成功推送
	isAuto := newOrder.OrderType == int8(pb.EnumOrderType_ORDER_TYPE_AUTO_PAY)
	newCtx := grpc.NewContextWithInfo(ctx)
	if orderOpt.Status == pb.EnumOrderStatus_ORDER_PAY_SUCCESS {
		go func() {
			if newOrder.ActiveUid == newOrder.Uid {
				err = m.sendPaySuccessNotifyMsg(newCtx, newOrder.ActiveUid, isAuto)
				if err != nil {
					log.ErrorWithCtx(newCtx, "NoticeOrder sendPaySuccessNotifyMsg orderID:%v err:%v", orderOpt.OrderId, err)
				}
			}
			// 记录支付记录
			payRecords := make([]*pb.PayRecord, 0, 1)
			payRecords = append(payRecords, &pb.PayRecord{
				TimeStamp: newOrder.ServerTime.Unix(),
				Desc:      getOrderTitle(newOrder.PackageName, newOrder.PackageType, newOrder.OrderType == int8(pb.EnumOrderType_ORDER_TYPE_UPGRADE)),
				Price:     orderOpt.TotalPrice,
				PackageId: newOrder.PackageId,
			})
			//特定marketID的所有套餐
			if err := m.Cache.SetOrderRecordList(newCtx, newOrder.ActiveUid, int(newOrder.MarketId), payRecords); err != nil {
				log.ErrorWithCtx(ctx, "NoticeOrder SetOrderRecordList uid:%v marketId:%v payRecords:%+v err:%v", newOrder.ActiveUid, newOrder.MarketId, payRecords, err)
			}
			if !isAuto { //所有marketID的非连续套餐
				if err := m.Cache.SetOrderRecordList(newCtx, newOrder.ActiveUid, utils.AllMarketId, payRecords); err != nil {
					log.ErrorWithCtx(ctx, "NoticeOrder SetOrderRecordList uid:%v marketId:%v payRecords:%+v err:%v", newOrder.ActiveUid, utils.AllMarketId, payRecords, err)
				}
			}
			// 如果是升级套餐的订单，解锁用户升级套餐的锁
			if newOrder.OrderType == int8(pb.EnumOrderType_ORDER_TYPE_UPGRADE) {
				m.Cache.UnlockUserPlaceUpgradeOrder(ctx, newOrder.ActiveUid, newOrder.OrderId)
			}
		}()
	}

	log.InfoWithCtx(ctx, "NoticeOrder success orderID:%v", orderOpt.OrderId)
	return newOrder, nil
}

// cleanAllAlipayBasicContract 清除所有支付宝普通会员签约
func (m *Manager) cleanAllAlipayBasicContract(ctx context.Context, uid uint32) error {
	contracts, err := m.Store.GetContractListByUid(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "cleanAllAlipayBasicContract GetContractsByUid uid:%v err:%v", uid, err)
		return err
	}

	for _, c := range contracts {
		contract := c
		if contract.PayChannel == utils.ALIPAY && contract.PackageType == uint32(pb.PackageType_ENUM_PACKAGE_TYPE_NORMAL) {
			// 解约
			if err := m.CancelContract(ctx, uid, contract.ContractId); err != nil {
				log.ErrorWithCtx(ctx, "cleanAllAlipayBasicContract CancelContract uid:%v contractId:%v err:%v", uid, contract.ContractId, err)
				return err
			}
		}
	}
	return nil
}

// GetPayRecordList 获取支付记录
func (m *Manager) GetPayRecordList(ctx context.Context, uid uint32, marketid int, off, count int64) ([]*pb.PayRecord, error) {

	orderList, err := m.Cache.GetOrderRecordList(ctx, uid, marketid, off, count)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPayRecordList cache uid:%v %v", uid, err)
		return orderList, err
	}

	//no cache
	if off == 0 && len(orderList) == 0 {
		tmpOrderList, err := m.Store.GetPastYearPaidOrderListByUid(ctx, uid, marketid, 12, false)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetPayRecordList db uid:%v %v", uid, err)
			return nil, err
		}

		for _, r := range tmpOrderList {
			orderList = append(orderList, &pb.PayRecord{
				TimeStamp: r.CreateTime.Unix(),
				Desc:      getOrderTitle(r.PackageName, r.PackageType, r.OrderType == int8(pb.EnumOrderType_ORDER_TYPE_UPGRADE)),
				Price:     r.Price,
				PackageId: r.PackageId,
				//BeginTime:  r.BeginTime.Unix(),  废弃
				//ExpireTime: r.ExpireTime.Unix(),  废弃
			})
		}

		if len(orderList) == 0 {
			orderList = append(orderList, &pb.PayRecord{
				TimeStamp: 0,
				Desc:      "NULL",
				Price:     0,
			})
		}
		if err = m.Cache.SetOrderRecordList(ctx, uid, marketid, orderList); err != nil {
			log.ErrorWithCtx(ctx, "GetPayRecordList SetOrderRecordList uid:%v marketId:%v orderList:%+v, err:%v", uid, marketid, orderList, err)
		}
		log.DebugWithCtx(ctx, "GetPayRecordList SetPayRecordList uid:%v marketId:%v orderList:%+v", uid, marketid, orderList)

		//再取一次排序
		orderList, _ = m.Cache.GetOrderRecordList(ctx, uid, marketid, off, count)
	}

	results := make([]*pb.PayRecord, 0)
	for _, r := range orderList {
		if r.TimeStamp == 0 {
			continue
		}

		results = append(results, &pb.PayRecord{
			TimeStamp: r.TimeStamp,
			Desc:      r.Desc,
			Price:     r.Price,
			PackageId: r.PackageId,
			//BeginTime:  r.BeginTime, 废弃
			//ExpireTime: r.ExpireTime, 废弃
		})
	}

	return results, nil
}

// GetOrder 获取订单，该方法会往前找一年的订单，保证找到
func (m *Manager) GetOrder(ctx context.Context, orderId string, createTime time.Time, deepSelect bool) (*Order, error) {
	if createTime.IsZero() {
		createTime = time.Now()
	}
	order, err := m.Store.GetOrder(ctx, orderId, createTime, nil, deepSelect)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOrder GetOrder orderId:%v err:%v", orderId, err)
		return nil, err
	}
	return order, nil
}

// GetUserCanUpgradeDays 获取用户可升级天数
func (m *Manager) GetUserCanUpgradeDays(ctx context.Context, uid uint32) (int64, error) {
	log.DebugWithCtx(ctx, "GetUserCanUpgradeDays uid:%v", uid)

	// 白名单
	if days, has := m.DyConfWhitelist.GetUserCanUpgradeDays(uid); has {
		log.DebugWithCtx(ctx, "GetUserCanUpgradeDays uid:%v days:%v", uid, days)
		return days, nil
	}

	// 获取用户的剩余天数
	leftTimeSum, err := m.Store.GetUserLeftTimeSum(ctx, uid, uint32(pb.PackageType_ENUM_PACKAGE_TYPE_NORMAL), nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserLeftTimeSum failed, err: %v", err)
		return 0, err
	}
	// 判断是否今天已经核销结束，如果没结束能升级天数-1
	now := time.Now()
	if now.Hour() < 8 { // 8点之前才判断，大概率8小时已经核销完毕了
		finish, err := m.Cache.ValidRedemptionUserDayFinish(ctx, uid, time.Now())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserCanUpgradeDays ValidRedemptionUserDayFinish uid:%v err:%v", uid, err)
			return 0, err
		}
		if !finish {
			leftTimeSum--
		}
	}

	// 修正
	if leftTimeSum < 0 {
		leftTimeSum = 0
	}

	log.DebugWithCtx(ctx, "GetUserCanUpgradeDays uid:%v leftTimeSum:%v", uid, leftTimeSum)
	return int64(leftTimeSum), nil
}

// GetUserOrderLeftDays 获取用户订单剩余天数
func (m *Manager) GetUserOrderLeftDays(ctx context.Context, uid uint32) (int64, int64, error) {
	// 从白名单获取
	if vip, svip, has := m.DyConfWhitelist.GetUserLeftDays(uid); has {
		log.DebugWithCtx(ctx, "GetUserOrderLeftDays whiteList uid:%v vip:%v svip:%v", uid, vip, svip)
		return vip, svip, nil
	}
	// 获取用户VIP的剩余天数
	vipLeftDays, err := m.Store.GetUserLeftTimeSum(ctx, uid, uint32(pb.PackageType_ENUM_PACKAGE_TYPE_NORMAL), nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserLeftTimeSum failed, err: %v", err)
		return 0, 0, err
	}
	// 判断是否今天已经核销结束，如果没结束能升级天数-1
	now := time.Now()
	if now.Hour() < 8 { // 8点之前才判断，大概率8小时已经核销完毕了
		finish, err := m.Cache.ValidRedemptionUserDayFinish(ctx, uid, now)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserLeftTimeSum ValidRedemptionUserDayFinish uid:%v err:%v", uid, err)
			return 0, 0, err
		}
		if !finish {
			vipLeftDays--
		}
		// 修正
		if vipLeftDays < 0 {
			vipLeftDays = 0
		}
	}
	// 获取用户SVIP的剩余天数
	svipLeftDays, err := m.Store.GetUserLeftTimeSum(ctx, uid, uint32(pb.PackageType_ENUM_PACKAGE_TYPE_SVIP), nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserLeftTimeSum failed, err: %v", err)
		return 0, 0, err
	}

	// 修正
	if svipLeftDays < 0 {
		svipLeftDays = 0
	}

	return int64(vipLeftDays), int64(svipLeftDays), nil
}

// GetReplacementOrderLock 获取补单锁
func (m *Manager) GetReplacementOrderLock(ctx context.Context, orderId string) (bool, error) {
	isLock, err := m.Cache.LockReplacementOrder(ctx, orderId, time.Minute)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetReplacementOrderLock orderId:%v err:%v", orderId, err)
		return false, err
	}
	return isLock, nil
}

// PlaceUpgradeOrder 下升级单
func (m *Manager) PlaceUpgradeOrder(ctx context.Context, payOrder *Order, discountPrice float32, code string, newUser bool,
	updateAll bool) (*PlaceOrderResp, error) {

	// 下单时间黑名单
	settleTime := time.Now()
	canNotSettleHour, canNotSettleMin := m.DyConfWhitelist.GetUserCanNotUpgradeTime(payOrder.Uid)
	// 默认如果下单时间在23:57:00-次日00:00:00 不允许下单
	if settleTime.Hour() == canNotSettleHour && settleTime.Minute() >= canNotSettleMin {
		return nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPacket, "下单太频繁，请稍后操作")
	}

	payApiPara := m.DyConf.GetPayApiPara()
	nowTs := time.Now()
	payOrder.OrderId = genOrderID(payOrder.Uid, payOrder.PayChannel, getOrderPrefix(payOrder.PackageType), nowTs)
	// 所有升级订单加锁
	isLock, err := m.Cache.LockUserPlaceUpgradeOrder(ctx, payOrder.Uid, payOrder.OrderId, time.Minute*5)
	if err != nil {
		log.ErrorWithCtx(ctx, "PlaceUpgradeOrder LockPlaceOrder uid:%v err:%v", payOrder.Uid, err)
		return nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPacket, "下单太频繁，请稍后操作")
	}
	if !isLock { // 30分钟内只能下一次升级单（已知会产品，这个后续优化）
		log.ErrorWithCtx(ctx, "PlaceUpgradeOrder LockPlaceOrder uid:%v", payOrder.Uid)
		return nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPacket, "下单太频繁，请稍后操作")
	}

	//订单支付超时分钟
	timeOut := defaultPayInterval
	if payApiPara.TimeOut > 0 {
		timeOut = int(payApiPara.TimeOut)
	}

	price := m.getPackPrice(discountPrice, payOrder.Price, newUser)
	payOrder.Price = price
	payOrder.ServerTime = nowTs
	payOrder.CreateTime = nowTs
	payOrder.UpdateTime = nowTs

	// 开启事务
	tx, err := m.Store.GetTx()
	if err != nil {
		log.ErrorWithCtx(ctx, "PlaceOrder GetTx err:%v", err)
		return nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "数据库异常")
	}
	notNeedRollback := false
	defer func() {
		if !notNeedRollback {
			if err = tx.Rollback(); err != nil {
				log.ErrorWithCtx(ctx, "PlaceOrder Rollback err:%v", err)
			}
		}
	}()

	// 再查询一下可升级天数，防止并发问题
	canUpdateDays, err := m.Store.GetUserLeftTimeSum(ctx, payOrder.Uid, 0, tx)
	if err != nil {
		log.ErrorWithCtx(ctx, "PlaceOrder GetUserCanSettlementOrders uid:%v err:%v", payOrder.Uid, err)
		return nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "数据库异常")
	}
	if uint32(canUpdateDays) < payOrder.Days {
		log.ErrorWithCtx(ctx, "PlaceOrder GetUserCanSettlementOrders uid:%v canUpdateDays:%v", payOrder.Uid, canUpdateDays)
		return nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "可升级天数不足")
	}

	// 创建订单
	//payOrder.OrderType = int8(pb.EnumOrderType_ORDER_TYPE_INITIATIVE_PAY) // 主动下单，将状态修复
	err = m.Store.CreateOrder(ctx, payOrder, tx)
	if err != nil {
		log.ErrorWithCtx(ctx, "PlaceOrder PlaceOrder uid:%v err:%v", payOrder.Uid, err)
		return nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, fmt.Sprintf("创建订单失败:%v", err))
	}

	// 下单请求
	subCtx, cancel := context.WithTimeout(ctx, time.Second*10)
	defer cancel()
	businessId, fm, err := m.DyConf.GetBusinessCfg(payOrder.MarketId, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "PlaceOrder GetBusinessCfg fail uid:%v err:%v", payOrder.Uid, err)
		return nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, fmt.Sprintf("获取商户配置失败:%v", err))
	}
	var packageName string
	if updateAll {
		packageName = fmt.Sprintf("升级%+v天", payOrder.Days)
	} else {
		packageName = payOrder.PackageName
	}
	remark, _ := m.DyConf.GetRemarkAndProductCode(payOrder.PackageType, payOrder.MarketId, payOrder.OsType == utils.OsTypeIOS, payOrder.PayChannel == utils.WECHAT)
	placeOrderReq := &pb.ApiPlaceOrderReq{
		OrderType:     utils.GetOrderType(false),
		OsType:        payOrder.OsType,
		PayChannel:    payOrder.PayChannel,
		BusinessId:    businessId,
		Fm:            fm,
		Version:       payOrder.Version,
		CliOrderNo:    payOrder.OrderId,
		CliBuyerId:    fmt.Sprintf("%v", payOrder.Uid),
		CliPrice:      fmt.Sprintf("%.2f", price),
		CliOrderTitle: getOrderTitle(packageName, payOrder.PackageType, true),
		CliOrderDesc:  getOrderTitle(packageName, payOrder.PackageType, true), // 支付那边说是预留的字段，后续有别的用处再改
		CliNotifyUrl:  payApiPara.PayCallBackUrl,
		CreateTime:    utils.GetTimeStr(nowTs),
		Remark:        remark,
		BundleId:      payOrder.BundleId,
		ProductId:     payOrder.BundleId, // 如果是APPSTORE，这个值就和bundleId一样
		Code:          code,              // 这个字段没有用，保留
		TimeOut:       fmt.Sprintf("%v", timeOut),
	}
	subCtx = utils.GenPayCtx(subCtx, payOrder.Uid, payOrder.MarketId, fm)
	payResp, err := m.Pay.PlaceOrder(subCtx, placeOrderReq)
	outResp := payResp.(pb.ApiPlaceOrderResp)
	if err != nil {
		log.ErrorWithCtx(ctx, "PlaceOrder PlaceOrder uid:%v placeOrderID:%v err:%v", payOrder.Uid, payOrder.OrderId, err)
		return nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, fmt.Sprintf("下单失败:%v", err))
	}

	// 提交事务
	if err = tx.Commit(); err != nil {
		log.ErrorWithCtx(ctx, "PlaceOrder Commit err:%v", err)
		return nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "数据库异常")
	}
	notNeedRollback = true

	return &PlaceOrderResp{
		OrderId:    outResp.OrderNo,
		Token:      outResp.Token,
		ChannelMap: outResp.ChannelMap,
		CliOrderNo: outResp.CliOrderNo,
		Tsk:        outResp.Tsk,
	}, nil
}

// getOrderTitle 获取订单标题
func getOrderTitle(packageName string, packageType uint32, isUpdate bool) string {
	if packageType == uint32(pb.PackageType_ENUM_PACKAGE_TYPE_SVIP) {
		if isUpdate {
			return fmt.Sprintf("升级超级玩家SVIP%v", packageName)
		}
		return fmt.Sprintf("开通超级玩家SVIP%v", packageName)
	}
	return fmt.Sprintf("购买超级玩家%v", packageName)
}

// GetLastFiveMinutesActiveOrderInfo 获取最近5分钟生效的订单信息
func (m *Manager) GetLastFiveMinutesActiveOrderInfo(ctx context.Context) ([]*Order, error) {
	orders, err := m.Store.GetLastFiveMinutesActiveOrderInfo(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLastFiveMinutesActiveOrderInfo err:%v", err)
		return nil, err
	}
	return orders, nil
}

// RevokeOrder 撤销订单
func (m *Manager) RevokeOrder(ctx context.Context, uid uint32, orderId string, createTime time.Time) (uint32, string, time.Time, error) {
	// 拿操作锁
	locked, err := m.Cache.LockOrderOperate(ctx, uid, orderId, time.Second*10)
	if err != nil || !locked {
		log.ErrorWithCtx(ctx, "RevokeOrder LockOrderOperate orderID:%v err:%v", orderId, err)
		return 0, "", time.Time{}, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "获取操作锁失败")
	}
	defer func() {
		if deferErr := m.Cache.UnlockOrderOperate(ctx, uid, orderId); deferErr != nil {
			log.ErrorWithCtx(ctx, "RevokeOrder UnlockOrderOperate orderID:%v err:%v", orderId, deferErr)
		}
	}()

	// 操作订单
	err = m.Store.RevokeOrder(ctx, orderId, createTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "RevokeOrder orderID:%v err:%v", orderId, err)
		return 0, "", time.Time{}, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "撤销订单失败")
	}

	// 查询回滚信息
	revokeFlow, err := m.Store.GetOrderRevokeFlow(ctx, uid, orderId)
	if err != nil {
		log.ErrorWithCtx(ctx, "RevokeOrder GetOrderRevokeFlow orderID:%v err:%v", orderId, err)
		return 0, "", time.Time{}, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "查询回滚信息失败")
	}

	return revokeFlow.RevokeDays, revokeFlow.RevokeOrderId, revokeFlow.CreateTime, nil
}

// GetLastMinRevokeFlows 获取最近一分钟的撤销流水
func (m *Manager) GetLastMinRevokeFlows(ctx context.Context, now time.Time) ([]*OrderRevokeFlow, error) {
	startTime := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()-1, now.Second(), 0, now.Location())
	endTime := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute(), now.Second()-20, 0, now.Location()) // 这里减少重复操作的概率
	flows, err := m.Store.GetOrderRevokeFlowsByTime(ctx, startTime, endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLastThirtySecendsRevokeFlows err:%v", err)
		return nil, err
	}

	return flows, nil
}

// ValidUserIsAnnualSvipBeforeTime 判断用户在某个时间点之前是否是年卡会员
func (m *Manager) ValidUserIsAnnualSvipBeforeTime(ctx context.Context, uid uint32, time time.Time) (bool, error) {
	isAnnualSvip, err := m.Store.ValidUserIsAnnualSvipBeforeTime(ctx, uid, time)
	if err != nil {
		log.ErrorWithCtx(ctx, "ValidUserIsAnnualSvipBeforeTime GetContractByTime uid:%v time:%v err:%v", uid, time, err)
		return false, err
	}
	return isAnnualSvip, nil
}

// GetLatestPayRecord 获取用户最新的支付的时间戳
func (m *Manager) GetLatestPayRecord(ctx context.Context, uid uint32) (uint32, error) {
	timeStamp, err := m.Cache.GetLatestPayRecord(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLatestPayRecord uid:%v err:%v", uid, err)
		return 0, err
	}
	log.ErrorWithCtx(ctx, "GetLatestPayRecord uid:%v timeStamp:%v", uid, timeStamp)
	return uint32(timeStamp), nil
}

// SetLatestPayRecord 设置用户最新的支付的时间戳
func (m *Manager) SetLatestPayRecord(ctx context.Context, uid uint32) error {
	err := m.Cache.SetLatestPayRecord(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetLatestPayRecord uid:%v err:%v", uid)
		return err
	}
	return nil
}
