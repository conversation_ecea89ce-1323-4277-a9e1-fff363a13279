package mgr

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	"golang.52tt.com/pkg/datacenter"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/superplayersvr"
	"golang.52tt.com/services/super-player/super-player-svr/internal/pkg/pay-api"
	"golang.52tt.com/services/super-player/super-player-svr/internal/pkg/utils"
)

// ContractRecord 签约包月，包季状态
type ContractRecord struct { //`gorm:"AUTO_INCREMENT"`
	Uid         uint32    `db:"uid" json:"uid"`
	Version     string    `db:"version" json:"version"`
	OsType      string    `db:"os_type" json:"os_type"`
	PayChannel  string    `db:"pay_channel" json:"pay_channel"`   // 支付渠道，微信，支付宝、IOS
	OrderId     string    `db:"order_id" json:"order_id"`         // 关闭套餐的情况应该是空
	PackageId   string    `db:"package_id" json:"package_id"`     // 已有套餐不能修复配置
	PackageName string    `db:"package_name" json:"package_name"` // 重构增加字段，套餐名称
	BundleId    string    `db:"bundle_id" json:"bundle_id"`       // 已有套餐不能修复配置
	ContractId  string    `db:"contract_id" json:"contract_id"`   // 签约ID
	SceneId     string    `db:"scene_id" json:"scene_id"`         // 场景ID
	Status      string    `db:"status" json:"status"`             // SIGN_CONTRACT，RESCIND_CONTRACT
	PeriodType  string    `db:"period_type" json:"period_type"`   // MONTH,YEAR,DAY
	Period      int32     `db:"period" json:"period"`             // 3 MONTH
	LastTime    time.Time `db:"last_time" json:"last_time"`
	NextTime    time.Time `db:"next_time" json:"next_time"`
	CreateTime  time.Time `db:"create_time" json:"create_time"`
	UpdateTime  time.Time `db:"update_time" json:"update_time"`
	MarketId    uint32    `db:"market_id" json:"market_id"`
	PackageType uint32    `db:"package_type" json:"package_type"` // 套餐类型：0-普通会员；1-超级会员
}

// NotifyContractInfo 更新签约状态信息
type NotifyContractInfo struct {
	ContractId  string    // 签约ID
	Status      string    // 签约状态
	PayChannel  string    // 支付渠道
	PackageId   string    // 套餐ID
	PackageName string    // 套餐名称
	BundleId    string    // 苹果支付包ID
	RealBuyerId uint32    // 真实购买者ID
	ActiveUid   uint32    // 实际生效的用户ID
	ActiveDays  uint32    // 实际生效的天数
	NextPayTime time.Time // 下次支付时间
	Now         time.Time // 当前时间
}

// GetUserContract 获取用户签约关系
func (m *Manager) GetUserContract(ctx context.Context, uid uint32, isTriggerQuery bool) ([]*pb.SuperPlayerContractInfo, string, error) {
	// 本地缓存查
	var lastContractId string

	// 白名单
	isWhitelist, infos, lastContractId := m.getContractInfoFromWhitelist(uid)
	if isWhitelist {
		return infos, lastContractId, nil
	}

	contracts, err := m.Cache.GetContractList(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserContract GetContractList uid:%v err:%v", uid, err)
		return contracts, lastContractId, err
	}

	if len(contracts) == 0 {
		contracts, err = m.getContractInfoFromDB(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserContract getContractInfoFromDB uid:%v err:%v", uid, err)
			return contracts, lastContractId, err
		}
		//写到缓存
		if err = m.Cache.SetContractList(ctx, uid, contracts); err != nil {
			log.ErrorWithCtx(ctx, "GetUserContract SetContractList uid:%v err:%v", uid, err)
		}
	}

	results := make([]*pb.SuperPlayerContractInfo, 0)
	for _, c := range contracts {
		if c.Status != utils.SIGN_CONTRACT {
			continue
		}

		results = append(results, &pb.SuperPlayerContractInfo{
			PayChannel: c.PayChannel,
			Status:     c.Status,
			PackageId:  c.PackageId,
			ContractId: c.ContractId,
			Uid:        c.Uid,
			//ExtraTag:         extraTag, // 外部补充
			NextPayTimestamp: c.NextPayTimestamp,
			MarketId:         c.MarketId,
			SignTime:         c.SignTime,
			Price:            c.Price,
			PackageType:      c.PackageType,
		})

		log.DebugWithCtx(ctx, "GetUserContract %+v", c)

		if isTriggerQuery {
			newCtx := grpc.NewContextWithInfo(ctx)
			// 触发支付组活跃用户查询
			go func() {
				_, err = m.queryContractInfo(newCtx, &ContractRecord{
					Uid:        c.Uid,
					ContractId: c.ContractId,
					MarketId:   c.MarketId,
				})
				if err != nil {
					log.ErrorWithCtx(ctx, "GetSuperPlayerContract queryContractInfo uid:%v err:%v", uid, err)
				}
			}()
		}
	}

	if isTriggerQuery {
		tmpId, err := m.Cache.GetIosUserHasSign(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetSuperPlayerContract GetSignContractOrderID uid:%v err:%v", uid, err)
		} else {
			lastContractId = tmpId
		}
	}

	return results, lastContractId, nil
}

type SignInfo struct {
	ContractVip  bool
	ContractSvip bool
}

// GetHasSignUserMap 获取已签约用户
func (m *Manager) GetHasSignUserMap(ctx context.Context) (map[uint32]bool, map[uint32]bool, error) {
	log.InfoWithCtx(ctx, "GetHasSignUserMap begin")
	signVip := make(map[uint32]bool)
	signSvip := make(map[uint32]bool)
	var start, step int64 = 1625500800, 86400
	tmpTs, tmpStep := m.DyConf.GetSPSystemBeginTs()
	if tmpTs != 0 {
		start = tmpTs
	}
	if tmpStep != 0 {
		step = tmpStep
	}
	end := time.Now().Unix()
	for start < end {
		tempEnd := start + step
		infos, err := m.Store.GetContractUidInfoList(ctx, time.Unix(start, 0), time.Unix(tempEnd, 0), []string{utils.SIGN_CONTRACT, utils.SPSYS_SIGN_CONTRACT})
		if err != nil {
			log.ErrorWithCtx(ctx, "GetHasSignUserMap GetContractList err:%v", err)
			return signVip, signSvip, err
		}
		for _, contract := range infos {
			if contract.PackageType == uint32(pb.PackageType_ENUM_PACKAGE_TYPE_NORMAL) {
				signVip[contract.Uid] = true
			} else if contract.PackageType == uint32(pb.PackageType_ENUM_PACKAGE_TYPE_SVIP) {
				signSvip[contract.Uid] = true
			} else {
				log.DebugWithCtx(ctx, "GetHasSignUserMap contract:%v", contract)
			}
		}
		start = tempEnd
	}

	log.DebugWithCtx(ctx, "GetHasSignUserMap signVip:%v signSvip:%v", signVip, signSvip)
	return signVip, signSvip, nil
}

// GetActiveContractListWithCreateTime 获取指定时间段内的有效签约关系
func (m *Manager) GetActiveContractListWithCreateTime(ctx context.Context, startTime, endTime time.Time) ([]*ContractRecord, error) {
	contractList, err := m.Store.GetContractList(ctx, startTime, endTime, []string{utils.SIGN_CONTRACT})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetActiveContractListWithCreateTime GetContractList err:%v", err)
		return nil, err
	}
	return contractList, nil
}

// GetContractListCanCancelWithCreateTime 获取指定时间段内的可以解约的签约关系
func (m *Manager) GetContractListCanCancelWithCreateTime(ctx context.Context, startTime, endTime time.Time) ([]*ContractRecord, error) {
	contractList, err := m.Store.GetContractList(ctx, startTime, endTime, []string{utils.INIT_CONTRACT, utils.SIGN_CONTRACT, utils.SPSYS_RESCIND_CONTRACT})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetActiveContractListWithCreateTime GetContractList err:%v", err)
		return nil, err
	}
	return contractList, nil
}

// LockAutoCancelContractWithContract 锁定自动解约
func (m *Manager) LockAutoCancelContractWithContract(ctx context.Context, contractId string) (bool, error) {
	return m.Cache.LockAutoCancelContractWithContract(ctx, contractId, time.Minute*60)
}

// LockAutoCancelContract 锁定自动解约
func (m *Manager) LockAutoCancelContract(ctx context.Context) (bool, error) {
	return m.Cache.LockAutoCancelContract(ctx, time.Hour*2)
}

// RenewalNotify 续费通知
func (m *Manager) RenewalNotify(ctx context.Context, contract *ContractRecord, os protocol.OS) {
	nowTs := time.Now()
	days := m.DyConf.RenewalNotifyDay()
	leftTime := nowTs.AddDate(0, 0, days)
	if contract.NextTime.After(leftTime) || contract.NextTime.Before(nowTs) { // 判断是否需要推送
		return
	}
	// 判断是否已经推送过了
	if ok, _ := m.Cache.LockFiveDayRenewalNotify(ctx, contract.ContractId, contract.NextTime.Unix(), time.Hour*24*30); !ok {
		log.DebugWithCtx(ctx, "RenewalNotify contractId:%v nextTime:%v", contract.ContractId, contract.NextTime)
		return
	}
	// 获取文案并推送
	content := m.DyConf.GetRenewalNotifyMsg(os, contract.NextTime, contract.PackageType)
	if err := m.Cli.SendImMsg(contract.Uid, content, ""); err != nil {
		log.ErrorWithCtx(ctx, "RenewalNotify SendImMsg err:%v", err)
	}
	log.InfoWithCtx(ctx, "renewNotify uid:%v os:%v content:%v", contract.Uid, os, content)
}

// SendCancelContractNotifyMsg 发送解约通知
func (m *Manager) SendCancelContractNotifyMsg(ctx context.Context, uid uint32, os protocol.OS, contract *ContractRecord) {
	// 判断是否已经推送过了
	if ok, _ := m.Cache.LockCancelContractNotify(ctx, contract.ContractId, contract.NextTime.Unix(), time.Hour*24*30); !ok {
		log.DebugWithCtx(ctx, "SendCancelContractNotifyMsg contractId:%v nextTime:%v", contract.ContractId, contract.NextTime)
		return
	}
	// 获取文案并推送
	content := m.DyConf.GetCancelContractNotifyMsg(os, contract.NextTime.AddDate(0, 0, 2), contract.PackageType)
	if err := m.Cli.SendImMsg(uid, content, ""); err != nil {
		log.ErrorWithCtx(ctx, "SendCancelContractNotifyMsg SendImMsg err:%v", err)
	}
	log.InfoWithCtx(ctx, "sendCancelContractNotifyMsg uid:%v os:%v content:%v", uid, os, content)
}

// getContractInfoFromWhitelist 从白名单获取用户签约信息
func (m *Manager) getContractInfoFromWhitelist(uid uint32) (bool, []*pb.SuperPlayerContractInfo, string) {
	result := make([]*pb.SuperPlayerContractInfo, 0)

	isWhitelistUser, vipExpired, isVipPeriodPkg, svipExpired, isSvipPeriodPkg := m.DyConfWhitelist.ValidUserAndGetWhiteListInfoForPeriod(uid)
	if !isWhitelistUser {
		return false, result, ""
	}
	if !isVipPeriodPkg && !isSvipPeriodPkg {
		return false, result, ""
	}

	// 组装白名单签约信息
	if isVipPeriodPkg {
		result = append(result, &pb.SuperPlayerContractInfo{
			PayChannel: utils.ALIPAY,
			Status:     utils.SIGN_CONTRACT,
			PackageId:  "160",
			ContractId: "TEST-VIP-CONTRACT",
			Uid:        uid,
			//ExtraTag:         "",
			NextPayTimestamp: uint32(vipExpired),
			MarketId:         0,
			SignTime:         uint32(time.Now().Unix()),
			//Price:            0,
			PackageType: pb.PackageType_ENUM_PACKAGE_TYPE_NORMAL,
		})
	}
	if isSvipPeriodPkg {
		result = append(result, &pb.SuperPlayerContractInfo{
			PayChannel: utils.ALIPAY,
			Status:     utils.SIGN_CONTRACT,
			PackageId:  "37",
			ContractId: "TEST-SVIP-CONTRACT",
			Uid:        uid,
			//ExtraTag:         "",
			NextPayTimestamp: uint32(svipExpired),
			MarketId:         0,
			SignTime:         uint32(time.Now().Unix()),
			//Price:            0,
			PackageType: pb.PackageType_ENUM_PACKAGE_TYPE_SVIP,
		})
	}
	var lastContractId string
	if vipExpired > svipExpired {
		lastContractId = "TEST-VIP-CONTRACT"
	} else {
		lastContractId = "TEST-SVIP-CONTRACT"
	}

	return true, result, lastContractId
}

// getContractInfoFromDB 从数据库获取用户签约信息
func (m *Manager) getContractInfoFromDB(ctx context.Context, uid uint32) ([]*pb.SuperPlayerContractInfo, error) {
	result := make([]*pb.SuperPlayerContractInfo, 0)
	log.DebugWithCtx(ctx, "getContractInfoFromDB uid:%v", uid)

	tmpContracts, err := m.Store.GetContractListByUid(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "getContractInfoFromDB GetContractListByUid err:%v", err)
		return result, err
	}

	for _, c := range tmpContracts {
		if c.Status != utils.SIGN_CONTRACT {
			continue
		}
		result = append(result, &pb.SuperPlayerContractInfo{
			PayChannel: c.PayChannel,
			Status:     c.Status,
			PackageId:  c.PackageId,
			ContractId: c.ContractId,
			Uid:        c.Uid,
			//ExtraTag:         "",
			NextPayTimestamp: uint32(c.NextTime.Unix()),
			MarketId:         c.MarketId,
			SignTime:         uint32(c.CreateTime.Unix()),
			//Price:            0,
			PackageType: pb.PackageType(c.PackageType),
		})

		//设置是否购买过IOS签约套餐标志
		if utils.IsAppstore(c.PayChannel) {
			if err = m.Cache.SetIosUserHasSign(ctx, uid, c.ContractId); err != nil {
				log.ErrorWithCtx(ctx, "getContractInfoFromDB SetIosUserHasSign err:%v", err)
			}
		}
	}

	if len(result) == 0 { // 如果没查到，说明没签过
		result = append(result, &pb.SuperPlayerContractInfo{
			PayChannel: "NULL",
			PackageId:  "NULL",
			Status:     utils.RESCIND_CONTRACT,
			Uid:        uid,
		})
	}

	log.DebugWithCtx(ctx, "getContractInfoFromDB result:%v", result)
	return result, nil
}

// GetContractByDbWithContractId 通过contractId从数据库获取用户签约信息
func (m *Manager) GetContractByDbWithContractId(ctx context.Context, contractId string) (*ContractRecord, error) {
	log.DebugWithCtx(ctx, "GetContractByDbWithContractId contractId:%v", contractId)

	contract, err := m.Store.GetContract(ctx, contractId, nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetContractByDbWithContractId GetContract contractId:%v err:%v", contractId, err)
		return nil, err
	}

	log.DebugWithCtx(ctx, "GetContractByDbWithContractId contract:%+v", contract)
	return contract, nil
}

// getContractLocalCacheKey 获取用户签约关系本地缓存key
func getContractLocalCacheKey(uid uint32) string {
	return fmt.Sprintf("get_super_player_contract_module_%v", uid)
}

// BatchGetUsersContract 批量获取用户签约关系
func (m *Manager) BatchGetUsersContract(ctx context.Context, uidList []uint32) (map[uint32][]*pb.SuperPlayerContractInfo, error) {
	// 本地缓存查，这里主要是为了降低接口时延，可以存在一定数据准确性延迟
	result := make(map[uint32][]*pb.SuperPlayerContractInfo)
	lackUidMap := make(map[uint32]bool)
	for _, uid := range uidList {
		// 补充白名单
		isWhitelist, infos, _ := m.getContractInfoFromWhitelist(uid)
		if isWhitelist {
			result[uid] = infos
			lackUidMap[uid] = false
			continue
		}
		lackUidMap[uid] = true
	}

	contractsMap, err := m.Cache.GetContractLists(ctx, lackUidMap)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetUsersContract GetContractList uidList:%v err:%v", uidList, err)
		return result, err
	}

	for uid, contract := range contractsMap {
		if len(contract) != 0 {
			result[uid] = contract  // 有的话，就往结果里面放
			lackUidMap[uid] = false // 标记一下，不用查了
			continue
		}
		lackUidMap[uid] = true // 没有的话，就标记一下，从存储查
	}

	lackUidList := make([]uint32, 0)
	for uid, ok := range lackUidMap {
		if ok {
			lackUidList = append(lackUidList, uid)
		}
	}

	if len(lackUidList) != 0 {
		contractsMap, err = m.batchGetContractInfosFromDB(ctx, lackUidList)
		if err != nil {
			log.ErrorWithCtx(ctx, "BatchGetUsersContract batchGetContractInfosFromDB lackUidList:%+v err:%+v", lackUidList, err)
			return result, err
		}
		//写到缓存
		if err = m.Cache.BatchSetContractList(ctx, contractsMap); err != nil {
			log.ErrorWithCtx(ctx, "BatchGetUsersContract BatchSetContractList contractsMap:%+v err:%+v", contractsMap, err)
		}
		// 写入结果
		for uid, contract := range contractsMap {
			result[uid] = contract
		}
	}

	return result, nil
}

// batchGetContractInfosFromDB 从数据库批量获取用户签约信息
func (m *Manager) batchGetContractInfosFromDB(ctx context.Context, uidList []uint32) (map[uint32][]*pb.SuperPlayerContractInfo, error) {
	result := make(map[uint32][]*pb.SuperPlayerContractInfo)
	log.DebugWithCtx(ctx, "batchGetContractInfosFromDB uidList:%v", uidList)

	tmpContractsMap, err := m.Store.GetContractListByUidList(ctx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "batchGetContractInfosFromDB GetContractListByUid err:%v", err)
		return result, err
	}

	for _, uid := range uidList {
		for _, c := range tmpContractsMap[uid] {
			if c.Status != utils.SIGN_CONTRACT {
				continue
			}
			result[uid] = append(result[uid], &pb.SuperPlayerContractInfo{
				PayChannel: c.PayChannel,
				Status:     c.Status,
				PackageId:  c.PackageId,
				ContractId: c.ContractId,
				Uid:        c.Uid,
				//ExtraTag:         "", // 外层
				NextPayTimestamp: uint32(c.NextTime.Unix()),
				MarketId:         c.MarketId,
				SignTime:         uint32(c.CreateTime.Unix()),
				//Price:            0,// 这里的价格是套餐的价格，新签约存在该关系，外层做兼容判断，若为0则取套餐价格
				PackageType: pb.PackageType(c.PackageType),
			})

			//设置是否购买过IOS签约套餐标志
			if utils.IsAppstore(c.PayChannel) {
				if err = m.Cache.SetIosUserHasSign(ctx, uid, c.ContractId); err != nil {
					log.ErrorWithCtx(ctx, "batchGetContractInfosFromDB SetIosUserHasSign err:%v", err)
				}
			}
		}
		if len(tmpContractsMap[uid]) == 0 { // 如果没查到，说明没签过
			result[uid] = append(result[uid], &pb.SuperPlayerContractInfo{
				PayChannel: "NULL",
				PackageId:  "NULL",
				Status:     utils.RESCIND_CONTRACT,
				Uid:        uid,
			})
		}
	}

	log.DebugWithCtx(ctx, "batchGetContractInfosFromDB result:%v", result)
	return result, nil
}

// queryContractInfo 从支付组查询签约信息
func (m *Manager) queryContractInfo(ctx context.Context, contract *ContractRecord) (*pb.ApiGetContractResp, error) {
	newCtx, cancel := grpc.NewContextWithInfoTimeout(ctx, time.Second*16)
	defer cancel()

	var contractResp *pb.ApiGetContractResp
	businessID, fm, err := m.DyConf.GetBusinessCfg(contract.MarketId, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "doAutoPay queryContractInfo GetBusinessCfg contractID:%v err:%v", contract.ContractId, err)
		return contractResp, err
	}

	_, productCode := m.DyConf.GetRemarkAndProductCode(contract.PackageType, contract.MarketId,
		contract.OsType == utils.OsTypeIOS, contract.PayChannel == utils.WECHAT)
	getContractReq := &pb.ApiGetContractReq{
		ContractId:  contract.ContractId,
		BusinessId:  businessID,
		BuyerId:     fmt.Sprintf("%v", contract.Uid),
		ProductCode: productCode,
	}

	subCtx := utils.GenPayCtx(newCtx, contract.Uid, contract.MarketId, fm)

	getContractResp, err := m.Pay.GetContract(subCtx, getContractReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "doAutoPay queryContractInfo getContract getContractReq:%v getContractResp:%v err:%v", getContractReq, getContractResp, err)
		return contractResp, err
	}

	log.InfoWithCtx(ctx, "doAutoPay queryContractInfo getContract getContractReq:%v getContractResp:%v", getContractReq, getContractResp)

	if getContractResp != nil {
		if resp, ok := getContractResp.(pb.ApiGetContractResp); ok {
			contractResp = &resp
		}
	}
	return contractResp, nil
}

const (
	vipNoticeMsg  = "亲爱的用户，您已开通超级玩家自动续费服务，系统将在%v前自动扣款，请确保您的签约账户有足够的余额以避免扣款失败导致超级玩家权益失效。若要取消自动续费，可前往会员页查看指引。点击前往>"
	svipNoticeMsg = "亲爱的用户，您已开通超级玩家SVIP自动续费服务，系统将在%v前自动扣款，请确保您的签约账户有足够的余额以避免扣款失败导致超级玩家权益失效。若要取消自动续费，可前往会员页查看指引。点击前往>"
)

// leftDayNotice 离过期还有5天的时候提示
// NOTICE: 产品调整逻辑，改为离自动续费还有5天的时候提示，提示同预下单时间一致
func (m *Manager) leftDayNotice(ctx context.Context, expireTimestamp int64, contract *ContractRecord) {
	//离过期还有5天的时候提示
	nowTs := time.Time{}
	expireTs := time.Unix(expireTimestamp, 0)
	leftHour := contract.NextTime.Sub(nowTs).Hours()
	if leftHour >= 24*3 && leftHour <= 24*2 { // 预下单提前三天，这里提前两天，就满足原有的提前五天在预下单周期内推送
		isLock, err := m.Cache.LockAutoPayNotice(ctx, contract.Uid, contract.NextTime, time.Hour*24*20)
		if err == nil && isLock {
			var content string
			if contract.PackageType == uint32(pb.PackageType_ENUM_PACKAGE_TYPE_SVIP) {
				content = fmt.Sprintf(svipNoticeMsg, utils.GetDayStr(expireTs))
			} else {
				content = fmt.Sprintf(vipNoticeMsg, utils.GetDayStr(expireTs))
			}
			if err = m.Cli.SendImMsg(contract.Uid, content, "点击前往>"); err != nil {
				log.ErrorWithCtx(context.Background(), "leftDayNotice SendImMsg err:%v", err)
			}
		}
	}
}

// 检查当前签约状态
func (m *Manager) getContracts(ctx context.Context, uid uint32, all bool, needInitContract bool) ([]*ContractRecord, error) {
	ctx, cancel := grpc.NewContextWithInfoTimeout(ctx, time.Second*5)
	defer cancel()
	contracts := make([]*ContractRecord, 0)
	tmpContracts, err := m.Store.GetContractListByUid(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "getContract GetContractListByUid err:%v", err)
		return contracts, err
	}

	for _, c := range tmpContracts {
		if c.Status == utils.SIGN_CONTRACT || all {
			log.DebugWithCtx(ctx, "getContracts contract:%+v", c)
			contracts = append(contracts, c)
		}
		if needInitContract && c.Status == utils.INIT_CONTRACT {
			log.DebugWithCtx(ctx, "getContracts contract:%+v", c)
			contracts = append(contracts, c)
		}
	}

	return contracts, nil
}

// checkContract 检查签约状态
func (m *Manager) checkContract(ctx context.Context, contract *ContractRecord, marketId uint32) error {
	newCtx, cancel := grpc.NewContextWithInfoTimeout(ctx, time.Minute*2)
	defer cancel()

	// 从支付组查询签约信息
	getContractResp, err := m.queryContractInfo(newCtx, contract)
	if err != nil {
		log.ErrorWithCtx(newCtx, "checkContract GetContract c:%+v err:%+v", contract, err)
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, fmt.Sprintf("查询签约信息失败:%v", err))
	}
	if getContractResp.Code != utils.SuccessResult {
		log.ErrorWithCtx(newCtx, "checkContract GetContract code fail c:%+v getContractResp:%+v", contract, getContractResp)
		return nil
	}

	if utils.IsAppstore(contract.PayChannel) && contract.MarketId == marketId {
		errMsg := "你已在苹果端订阅服务，若需更换订阅，请前往系统设置 > Apple ID > 订阅，找到对应APP选项，点击后进入页面进行更换"
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPacket, errMsg)
	}
	return protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "已有自动续费服务生效中，不能重复开通哟")
}

// checkContractLimit 检查签约限制
func (m *Manager) checkContractLimit(ctx context.Context, uid uint32, packageType uint32, marketId uint32) error {

	//查本地的签约记录
	currContracts, err := m.getContracts(ctx, uid, true, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkContractLimit getContracts uid:%v err:%v", uid, err)
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, fmt.Sprintf("查询签约信息失败:%v", err))
	}
	if len(currContracts) == 0 {
		return nil
	}
	log.DebugWithCtx(ctx, "checkContractLimit currContracts sz:%v", len(currContracts))

	signContractList := make([]*ContractRecord, 0)
	for _, c := range currContracts {
		if c.Status != utils.SIGN_CONTRACT {
			continue
		}
		signContractList = append(signContractList, c)
	}
	if 0 == len(signContractList) {
		return nil
	}

	for _, c := range signContractList {
		if c.PackageType != packageType && packageType == uint32(pb.PackageType_ENUM_PACKAGE_TYPE_SVIP) { // SVIP时，可以开通过VIP
			continue
		}
		err := m.checkContract(ctx, c, marketId)
		if err != nil {
			log.ErrorWithCtx(ctx, "checkContractLimit checkContract fail contract:%+v err:%v", c, err)
			return err
		}
	}

	return nil
}

// NotifyContract 更新签约状态
func (m *Manager) NotifyContract(ctx context.Context, req *NotifyContractInfo) error {
	log.InfoWithCtx(ctx, "NotifyContractInfo req:%+v", req)

	// 操作签约信息
	newContract, delCache, newSign, err := m.Store.NotifyContract(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "NotifyContractInfo NotifyContractInfo err:%v", err)
		return err
	}
	if delCache { // 说明没啥需要更新的，直接返回了
		if err = m.Cache.DelContractList(ctx, req.ActiveUid); err != nil {
			log.ErrorWithCtx(ctx, "NotifyContractInfo DelContractList err:%v", err)
		}
	}
	if newContract == nil { // 没更新，直接返回
		return nil
	}

	log.DebugWithCtx(ctx, "NotifyContractInfo success newContract:%+v, newSign:%v", newContract, newSign)

	//帮别人开通推送
	isPayForOtherUser := req.RealBuyerId != 0 && req.ActiveUid != req.RealBuyerId
	if isPayForOtherUser {
		go func() {
			if err := m.sendPayForOtherUserMsg(req.ActiveUid, req.RealBuyerId, pb.PackageType(newContract.PackageType)); err != nil {
				log.ErrorWithCtx(ctx, "NotifyContractInfo sendPayForOtherUserMsg err:%v", err)
			}
		}()
	}

	if utils.IsAppstore(req.PayChannel) {
		if err := m.Cache.SetIosUserHasSign(ctx, req.ActiveUid, req.ContractId); err != nil {
			log.ErrorWithCtx(ctx, "NotifyContractInfo SetIosUserHasSign err %v", err)
		}
	}

	// 判断如果是新开通的，需要发送通知
	log.DebugWithCtx(ctx, "NotifyContractInfo newSign:%v ios:%+v", newSign, strings.Contains(newContract.PayChannel, utils.APPSTORE))
	if newSign || strings.Contains(newContract.PayChannel, utils.APPSTORE) {
		newCtx := grpc.NewContextWithInfo(ctx)
		go func() {
			err = m.sendContractSuccessNotifyMsg(newCtx, newContract.Uid, newContract)
			if err != nil {
				log.ErrorWithCtx(newCtx, "NoticeOrder sendPaySuccessNotifyMsg orderID:%v err:%v", newContract.OrderId, err)
			}
			// 如果是SVIP订单，自动清除VIP所有支付宝订阅
			if newContract.PackageType == uint32(pb.PackageType_ENUM_PACKAGE_TYPE_SVIP) {
				if err = m.cleanAllAlipayBasicContract(newCtx, newContract.Uid); err != nil {
					log.ErrorWithCtx(newCtx, "NoticeOrder cleanAllAlipayBasicContract uid:%v err:%v", newContract.Uid, err)
				}
			}
		}()
	}

	go reportContract(ctx, newContract)
	log.DebugWithCtx(ctx, "NotifyContractInfo success newContract:%+v", newContract)
	return nil
}

// updateOldContractList 更新旧的签约信息
func (m *Manager) updateOldContractList(ctx context.Context, superPlayerUid uint32, order *Order, nowTs time.Time, tx mysql.Txx) error {
	currContracts, err := m.getContracts(ctx, superPlayerUid, true, false)

	if err != nil {
		log.ErrorWithCtx(ctx, "updateOldContractList getContracts order:%+v err:%v", order, err)
		return nil
	}

	for _, c := range currContracts {
		if c.MarketId != order.MarketId { //不是当前APP的签约忽略
			continue
		}

		// IOS保留第一笔签约信息就OK，然后签约信息通过签约回调更新，ALIPAY需要更新最新那笔
		if c.Status == utils.SIGN_CONTRACT && utils.IsAppstore(c.PayChannel) {
			log.InfoWithCtx(ctx, "updateOldContractList sign contract:%+v order:%+v", c, order)
			return nil
		}

		isChange := false
		if c.ContractId == order.OrderId {
			c.Status = utils.SIGN_CONTRACT
			isChange = true
		} else {
			if c.Status == utils.SIGN_CONTRACT {
				//旧签约全部废掉
				c.Status = utils.SPSYS_RESCIND_CONTRACT
				isChange = true
			}
		}
		if isChange {
			c.UpdateTime = nowTs
			err = m.Store.CreateOrUpdateContract(ctx, c, tx)
			if err != nil {
				log.ErrorWithCtx(ctx, "updateOldContractList CreateOrUpdateContract order:%+v err:%v", order, err)
				return err
			}
		}
	}

	return nil
}

// GetCancelContractPopUpInfo 有签约而且有已经生效或者即将生效的非连续套餐
func (m *Manager) GetCancelContractPopUpInfo(ctx context.Context, uid uint32) (string, error) {
	// 判断近期是否已经弹过
	if m.Cache.IsExistCancelContractPopUpInfo(ctx, uid) {
		log.DebugWithCtx(ctx, "GetCancelContractPopUpInfo uid:%v get lack fail", uid)
		return "", nil
	}

	// 获取用户签约关系
	contractList, err := m.getContracts(ctx, uid, false, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCancelContractPopUpInfo getContracts uid:%v err:%v", uid, err)
		return "", err
	}
	if len(contractList) == 0 {
		log.DebugWithCtx(ctx, "GetCancelContractPopUpInfo contractList empty uid:%v", uid)
		return "", nil
	}

	// 获取过去一年的已支付订单
	now := time.Now()
	start := time.Date(now.Year()-1, now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
	orderList, err := m.Store.GetOrderIndexByUid(ctx, uid, utils.AllMarketId, start, now)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCancelContractPopUpInfo GetPastYearPaidOrderListByUid uid:%v err:%v", uid, err)
		return "", err
	}

	nowTs := time.Now()
	//是否有正在或者将要消耗的非连续套餐
	hasCurrPack := false
	hasPeriodOrder := false
	for _, r := range orderList {
		if r.IsFinishSettlement {
			continue
		}
		if r.OrderType != int8(pb.EnumOrderType_ORDER_TYPE_AUTO_PAY) {
			hasCurrPack = true
			continue
		}
		hasPeriodOrder = true
	}
	if !hasPeriodOrder || !hasCurrPack {
		log.DebugWithCtx(ctx, "GetCancelContractPopUpInfo hasPeriodOrder:%v hasCurrPack:%v uid:%v", hasPeriodOrder, hasCurrPack, uid)
		return "", nil
	}

	payChannel := ""
	cnt := 0
	for _, contract := range contractList {
		if contract.Status != utils.SIGN_CONTRACT {
			continue
		}
		if payChannel == "" || payChannel != contract.PayChannel {
			payChannel = contract.PayChannel
			cnt++
		}
	}
	if payChannel == "" {
		log.ErrorWithCtx(ctx, "GetCancelContractPopUpInfo payChannel empty uid:%v", uid)
		return "", nil
	}
	both := cnt >= 2
	msg, interval := m.DyConf.GetCancelContractMsg(payChannel, both)
	if msg == "" {
		log.ErrorWithCtx(ctx, "GetCancelContractPopUpInfo GetCancelContractMsg empty uid:%v paychannel:%v both:%v", uid, payChannel, both)
		return "", nil
	}

	log.DebugWithCtx(ctx, "GetCancelContractPopUpInfo uid:%v msg:%v", uid, msg)
	weekHour := int64(time.Date(nowTs.Year(), nowTs.Month(), nowTs.Day(), 0, 0, 0, 0, time.Local).AddDate(0, 0, 1).Sub(nowTs).Seconds())
	if err = m.Cache.SetCancelContractPopUpInfoLock(ctx, uid, time.Duration(interval+weekHour)*time.Second); err != nil {
		log.ErrorWithCtx(ctx, "GetCancelContractPopUpInfo SetCancelContractPopUpInfoLock err:%v", err)
		return "", err
	}
	return msg, nil
}

// SetContractTimeRange 设置签约套餐有效期
func (m *Manager) SetContractTimeRange(ctx context.Context, uid, marketId uint32, ts int64) error {
	return m.Cache.SetContractTimeRange(ctx, uid, marketId, ts)
}

// GetDeviceRecord 获取设备购买记录
func (m *Manager) GetDeviceRecord(ctx context.Context, deviceId string) (string, error) {
	return m.Cache.GetDeviceBuyRecord(ctx, deviceId)
}

// IsContractTimeRange 是否在签约套餐有效期
func (m *Manager) IsContractTimeRange(ctx context.Context, uid, marketId uint32) (bool, error) {
	return m.Cache.IsContractTimeRange(ctx, uid, marketId)
}

// CancelContract 解约
func (m *Manager) CancelContract(ctx context.Context, uid uint32, contractId string) error {
	contract, err := m.Store.GetContract(ctx, contractId, nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelContract GetContract contractId:%v err:%v", contractId, err)
		return err
	}
	if contract == nil {
		log.InfoWithCtx(ctx, "CancelContract GetContract contractId:%v contract:%+v", contractId, contract)
		return nil
	}

	// 判断是否是自己的签约
	if contract.Uid != uid {
		log.ErrorWithCtx(ctx, "CancelContract GetContract uid:%v contract:%+v", uid, contract)
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara)
	}

	return m.DoCancelContract(ctx, contract)
}

// DoCancelContract 解约操作
func (m *Manager) DoCancelContract(ctx context.Context, contract *ContractRecord) error {
	log.InfoWithCtx(ctx, "doCancelContract begin contractId:%+v", contract)

	if contract.Status != utils.SIGN_CONTRACT { // 不是签约状态，直接转换解约状态
		contract.Status = utils.RESCIND_CONTRACT
		if err := m.Store.CreateOrUpdateContract(ctx, contract, nil); err != nil {
			log.ErrorWithCtx(ctx, "doCancelContract CreateOrUpdateContract err:%v", err)
		}
		//清掉签约信息缓存
		if err := m.Cache.DelContractList(ctx, contract.Uid); err != nil {
			log.ErrorWithCtx(ctx, "doCancelContract DelContractList err:%v", err)
		}
	} else { // 如果是签约状态，先查状态
		info, err := m.queryContractInfo(ctx, contract)
		if err != nil {
			log.ErrorWithCtx(ctx, "CancelContract queryContractInfo err:%v", err)
			if err := m.Feishu.SendInfo(fmt.Sprintf("查询签约信息失败:%v，签约信息:%+v", err, contract)); err != nil {
				log.ErrorWithCtx(ctx, "CancelContract SendError err:%v", err)
			}
		}
		if info != nil && info.Code != utils.SuccessResult { // 如果状态有问题就提示一下
			log.InfoWithCtx(ctx, "CancelContract queryContractInfo contractId:%v info:%+v", contract.ContractId, info)
		}
	}

	businessId, fm, err := m.DyConf.GetBusinessCfg(contract.MarketId, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "doCancelContract GetBusinessCfg err:%v", err)
		return err
	}

	_, productCode := m.DyConf.GetRemarkAndProductCode(contract.PackageType, contract.MarketId, contract.OsType == utils.OsTypeIOS, false) // 目前没有微信支付的连续订阅
	cancelContractReq := &pb.ApiCancelContractReq{
		ContractId:        contract.ContractId,
		BusinessId:        businessId,
		BuyerId:           fmt.Sprintf("%v", contract.Uid),
		ContractNotifyUrl: m.DyConf.GetContractNotifyUrl(),
		ProductCode:       productCode,
	}

	//发起解约
	subCtx := utils.GenPayCtx(ctx, contract.Uid, contract.MarketId, fm)
	cancelResp, err := m.Pay.CancelContract(subCtx, cancelContractReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "doCancelContract CancelContract err:%v", err)
		return err
	}
	cancelContractResp := cancelResp.(pb.ApiCancelContractResp)

	if cancelContractResp.Code == utils.ContractCancelCode {
		log.ErrorWithCtx(ctx, "doCancelContract CancelContract contractId:%v code:%v", contract.ContractId, cancelContractResp.Code)
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara)
	}

	if cancelContractResp.Code == utils.CancelResult || cancelContractResp.Code == utils.SuccessResult {
		contract.Status = utils.RESCIND_CONTRACT
		if err = m.Store.CreateOrUpdateContract(ctx, contract, nil); err != nil {
			log.ErrorWithCtx(ctx, "doCancelContract CreateOrUpdateContract err:%v", err)
		}
		reportContract(ctx, contract)
		//清掉签约信息缓存
		if err = m.Cache.DelContractList(ctx, contract.Uid); err != nil {
			log.ErrorWithCtx(ctx, "doCancelContract DelContractList err:%v", err)
		}
	}

	log.InfoWithCtx(ctx, "doCancelContract CancelContract success contractId:%+v uid:%v code:%v", contract.ContractId, contract.Uid, cancelContractResp.Code)
	return nil
}

// LockAutoPay 锁定自动续费
func (m *Manager) LockAutoPay(ctx context.Context) (bool, error) {
	lockAutoPay, err := m.Cache.LockAutoPay(ctx, time.Minute*10)
	if err != nil {
		log.ErrorWithCtx(ctx, "LockAutoPay LockAutoPay err:%v", err)
		return false, err
	}
	return lockAutoPay, nil
}

// UnlockAutoPay 解锁自动续费
func (m *Manager) UnlockAutoPay(ctx context.Context) {
	m.Cache.UnlockAutoPay(ctx)
}

// SetAutoPayContractOffSetFinishKey 设置自动续费偏移完成标志
func (m *Manager) SetAutoPayContractOffSetFinishKey(ctx context.Context, alreadyFinTs int64) error {
	if err := m.Cache.SetAutoPayContractOffSetFinishKey(ctx, alreadyFinTs, 0); err != nil {
		log.ErrorWithCtx(ctx, "SetAutoPayContractOffSetFinishKey err:%v", err)
		return err
	}
	return nil
}

// GetAutoPayContractOffSetFinishKey 获取自动续费偏移完成标志
func (m *Manager) GetAutoPayContractOffSetFinishKey(ctx context.Context) (int64, error) {
	offSetFinishTs, err := m.Cache.GetAutoPayContractOffSetFinishKey(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAutoPayContractOffSetFinishKey err:%v", err)
		return 0, err
	}
	return offSetFinishTs, nil
}

type DoAutoPayParams struct {
	Contract        *ContractRecord
	ExpireTimestamp int64
	Price           float32
	ProductId       string
	Days            uint32
	Value           uint32
	PackageName     string
	PackageInfo     string
	PackageType     uint32
}

// DoAutoPay 主要是安卓签约主动扣费使用
func (m *Manager) DoAutoPay(ctxUp context.Context, params *DoAutoPayParams) error {
	ctx, cancel := context.WithTimeout(ctxUp, time.Minute*5)
	defer cancel()
	log.InfoWithCtx(ctx, "doAutoPay begin params:%+v", params)
	contract := params.Contract
	// 获取支付接口参数
	payApiPara := m.DyConf.GetPayApiPara()
	nowTs := time.Now()
	// 每分钟调用次数限制
	callCnt, _ := m.Cache.GetPayCallCnt(ctx, nowTs)
	callPayCntLimit := payApiPara.CallPayCntLimit
	if callPayCntLimit == 0 {
		callPayCntLimit = 600
	}
	if callCnt >= callPayCntLimit {
		log.DebugWithCtx(ctx, "doAutoPay call cnt limit contract %v", contract.ContractId)
		return errors.New("call cnt limit")
	}
	// 检查自动扣款时间
	payTimeErr := m.checkAutoPayTime(params.ExpireTimestamp, contract.NextTime, nowTs, contract.PayChannel)
	if payTimeErr != nil {
		log.ErrorWithCtx(ctx, "doAutoPay checkAutoPayTime ContractID:%v err:%v", contract.ContractId, payTimeErr)
		return payTimeErr
	}
	// 离过期还有5天的时候提示
	m.leftDayNotice(ctx, params.ExpireTimestamp, contract)
	// 生成订单ID
	orderId := genOrderIDForAuto(contract.Uid, contract.PayChannel, getOrderPrefixForAutoX(contract.PackageType), contract.NextTime)
	// 检查订单间隔
	err := m.checkOrderInterval(ctx, payApiPara, orderId, contract.ContractId)
	if err != nil {
		log.ErrorWithCtx(ctx, "doAutoPay checkOrderInterval fail orderId:%v ContractID:%v", orderId, contract.ContractId)
		return err
	}
	// 开启事务
	tx, err := m.Store.GetTx()
	if err != nil {
		log.ErrorWithCtx(ctx, "doAutoPay GetTx err:%v", err)
		return err
	}
	notNeedRollback := false
	defer func() {
		if !notNeedRollback {
			if err = tx.Rollback(); err != nil {
				log.ErrorWithCtx(ctx, "doAutoPay Rollback err:%v", err)
			}
		}
	}()
	// 查询订单，检查是否已经完成
	orderInfo, err := m.Store.GetOrder(ctx, orderId, nowTs, tx, false)
	//没报错，能查到已有订单,并且订单状态是成功状态
	if err == nil && pb.EnumOrderStatus(orderInfo.Status) == pb.EnumOrderStatus_ORDER_PAY_SUCCESS {
		log.ErrorWithCtx(ctx, "doAutoPay GetOrder orderId:%v ContractID:%v err:%v", orderId, contract.ContractId, err)
		return nil
	}
	// 查询签约信息
	cResp, err := m.queryContractInfo(ctx, contract)
	isContractOk := err == nil && cResp.Code == utils.SuccessResult
	// 是否测试模式
	if (!isContractOk) && (!m.DyConf.IsTestAutoPay()) {
		log.ErrorWithCtx(ctx, "doAutoPay queryContractInfo orderId:%v contractID:%v err:%v", orderId, contract.ContractId, err)
		return err
	}
	// 生成订单
	// 先记录订单到数据库，状态是init状态
	log.DebugWithCtx(ctx, "doAutoPay Before PlaceOrder orderId:%v contractID:%v", orderId, contract.ContractId)
	if orderInfo == nil {
		err = m.Store.CreateOrder(ctx, &Order{
			OrderId:       orderId,
			ContractId:    contract.ContractId,
			Uid:           contract.Uid,
			OriginalPrice: params.Price,
			Price:         params.Price,
			PackageId:     contract.PackageId,
			PackageName:   params.PackageName,
			PackageInfo:   params.PackageInfo,
			PackageType:   params.PackageType,
			Days:          params.Days,
			OrderValue:    params.Value,
			PayChannel:    contract.PayChannel,
			Status:        int8(pb.EnumOrderStatus_ORDER_INIT),
			OrderType:     int8(pb.EnumOrderType_ORDER_TYPE_AUTO_PAY),
			OsType:        contract.OsType,
			Version:       contract.Version,
			BundleId:      contract.BundleId,
			ServerTime:    nowTs,
			CreateTime:    nowTs,
			UpdateTime:    nowTs,
			MarketId:      contract.MarketId,
		}, nil)
		if err != nil && !mysql.IsDupEntryError(err) {
			log.ErrorWithCtx(ctx, "doAutoPay Do PlaceOrder contractID:%v orderId:%v err:%v", contract.ContractId, orderId, err)
			return err
		}
		log.DebugWithCtx(ctx, "doAutoPay Do PlaceOrder orderId:%v contractID:%v", orderId, contract.ContractId)
	}
	log.DebugWithCtx(ctx, "doAutoPay After PlaceOrder orderId:%v contractID:%v", orderId, contract.ContractId)

	// 查询交易参数
	businessID, fm, err := m.DyConf.GetBusinessCfg(contract.MarketId, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "doAutoPay GetBusinessCfg contractID:%v orderId:%v err:%v", contract.ContractId, orderId, err)
		return err
	}

	remark, productCode := m.DyConf.GetRemarkAndProductCode(contract.PackageType, contract.MarketId, contract.OsType == utils.OsTypeIOS, false) // 目前没有微信支付的连续订阅
	// 发起扣款
	autoPayReq := &pb.ApiAutoPayReq{
		OrderType:     "PERIOD",
		OsType:        contract.OsType,
		PayChannel:    contract.PayChannel,
		BusinessId:    businessID,
		Fm:            fm,
		Version:       contract.Version,
		CliOrderNo:    orderId,
		CliBuyerId:    fmt.Sprintf("%v", contract.Uid),
		CliPrice:      fmt.Sprintf("%v", params.Price),
		CliOrderTitle: getOrderTitle(contract.PackageName, contract.PackageType, false),
		CliOrderDesc:  getOrderTitle(contract.PackageName, contract.PackageType, false), // 支付那边说是预留的字段，后续有别的用处再改
		CliNotifyUrl:  payApiPara.PayCallBackUrl,
		CreateTime:    utils.GetTimeStr(nowTs),
		Remark:        remark,
		BundleId:      contract.BundleId,
		ProductId:     params.ProductId,
		UserIp:        "",
		DeductParam: &pb.DeductParam{
			ContractId:   contract.ContractId,
			PlanId:       getPlanID(contract.PayChannel, contract.PackageType),
			ProductCode:  productCode,
			SingleAmount: fmt.Sprintf("%.2f", params.Price),
		},
	}
	// 扣款
	subCtx := context.WithValue(ctx, pay.CtxUidKey, strconv.FormatInt(int64(contract.Uid), 10))
	subCtx = context.WithValue(subCtx, pay.CtxFmKey, fm)
	subCtx = context.WithValue(subCtx, pay.CtxMarketIdKey, strconv.FormatInt(int64(contract.MarketId), 10))
	autoPayResp, payErr := m.Pay.AutoPay(subCtx, autoPayReq)
	if payErr != nil {
		// 扣款余额不足提醒
		isSendNotifyMsg := m.DyConf.IsNotBalance(payErr)
		if isSendNotifyMsg {
			m.SendCancelContractNotifyMsg(ctx, contract.Uid, protocol.ANDROID, contract)
		}
		log.ErrorWithCtx(ctx, "doAutoPay AutoPay ContractID:%v  autoPayReq:%v isSendNotifyMsg:%v err:%v", contract.ContractId, autoPayReq, isSendNotifyMsg, payErr)
		//发起延迟扣款申请
		if pay.IsDelayPay(payErr) {
			m.delayPay(ctx, contract, nowTs)
		} else {
			if m.DyConf.IsCloseOrder(payErr) || m.DyConf.IsInitiativeCancelContract(contract.NextTime) {
				contract.Status = utils.SPSYS_RESCIND_CONTRACT // 更新签约信息到预解约状态
				contract.SceneId = payErr.Error()
				if err := m.Store.CreateOrUpdateContract(ctx, contract, nil); err != nil { // 这里不包括在事务里面
					log.ErrorWithCtx(ctx, "doAutoPay CreateOrUpdateContract contractID:%v orderId:%v err:%v", contract.ContractId, orderId, err)
				}
				if err := m.Cache.DelContractList(ctx, contract.Uid); err != nil {
					log.ErrorWithCtx(ctx, "doAutoPay DelContractList err:%v", err)
				}
				if err := m.Store.AddContractAutoCancelLog(ctx, contract.ContractId, contract.Uid, contract.NextTime, contract.SceneId); err != nil {
					log.ErrorWithCtx(ctx, "doAutoPay AddContractAutoCancelLog err:%v", err)
				}
			}
		}
		return payErr // 这里已经回滚了事务了，所以直接返回
	}
	log.InfoWithCtx(ctx, "doAutoPay AutoPay ContractID:%v autoPayReq:%+v autoPayResp:%+v", contract.ContractId, autoPayReq, autoPayResp)
	// 提交事务
	if err := tx.Commit(); err != nil {
		log.ErrorWithCtx(ctx, "doAutoPay Commit err:%v", err)
	}
	notNeedRollback = true
	log.InfoWithCtx(ctx, "doAutoPay AutoPay success %+v contract:%+v", autoPayReq, contract)
	return nil
}

// checkAutoPayTime 检查自动扣款时间
func (m *Manager) checkAutoPayTime(expireTimestamp int64, nextTime time.Time, nowTs time.Time, payChannel string) error {
	//在扣款时间之前
	payBeginTs := time.Date(nextTime.Year(), nextTime.Month(), nextTime.Day(), 0, 0, 0, 0, time.Local)
	if utils.IsAppstore(payChannel) { //appstore再提前一天
		tmpNextPay := nextTime.Add(-24 * time.Hour)
		payBeginTs = time.Date(tmpNextPay.Year(), tmpNextPay.Month(), tmpNextPay.Day(), 0, 0, 0, 0, time.Local)
	}

	if nowTs.Before(payBeginTs) {
		return errors.New("before pay time")
	}

	//会员已经过期好几天就不扣款了
	passExpireHours := (nowTs.Unix() - expireTimestamp) / 3600
	log.DebugWithCtx(context.Background(), "checkAutoPayTime passExpireHours:%v", passExpireHours)
	if passExpireHours > (m.DyConf.GetIosDelayExpireHour() + 24*15) {
		return errors.New("expire super player not auto pay")
	}
	return nil
}

// checkOrderInterval 检查订单间隔
func (m *Manager) checkOrderInterval(ctx context.Context, payApiPara *pay.ApiPara, orderID, ContractID string) error {
	mins := payApiPara.AutoOrderInterval
	if mins == 0 {
		mins = 60
	}

	intervalTime := time.Minute * time.Duration(mins)
	if isLock, _ := m.Cache.LockOrderId(ctx, orderID, intervalTime); !isLock {
		log.DebugWithCtx(ctx, "doAutoPay isLock orderID:%v ContractID:%v", orderID, ContractID)
		return errors.New("order lock fail")
	}
	return nil
}

// delayPay 延迟扣款
func (m *Manager) delayPay(ctx context.Context, contract *ContractRecord, nowTs time.Time) {
	log.DebugWithCtx(ctx, "delayPay contract:%+v", contract)
	//发起延迟扣款申请
	businessID, fm, err := m.DyConf.GetBusinessCfg(contract.MarketId, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "doAutoPay GetBusinessCfg err:%v", err)
		return
	}
	delayPayReq := &pb.ApiDelayPayReq{
		ContractId: contract.ContractId,
		BusinessId: businessID,
		BuyerId:    fmt.Sprintf("%v", contract.Uid),
		DeductTime: utils.GetTimeStr(nowTs),
		Memo:       "会员发起延期扣款",
		PayChannel: contract.PayChannel,
	}
	subCtx := utils.GenPayCtx(ctx, contract.Uid, contract.MarketId, fm)
	_, err = m.Pay.DelayPay(subCtx, delayPayReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "doAutoPay DelayPay delayPayReq:%+v err:%v", delayPayReq, err)
	}
	log.DebugWithCtx(ctx, "delayPay success contract:%+v", contract)
}

// UpdateContractInfoWithNoticeAutoOrder 更新签约信息
func (m *Manager) UpdateContractInfoWithNoticeAutoOrder(ctx context.Context, orderId, contractId string, lastTime time.Time, expiredTime time.Time) {
	// 根据订单记录的签约ID查询签约记录
	contract, err := m.GetContractByDbWithContractId(ctx, contractId)
	if err != nil {
		log.ErrorWithCtx(ctx, "updateContractInfoWithNoticeAutoOrder GetContractByDbWithContractId contractId:%v err:%v", contractId, err)
		// 出错就飞书告警
		if err = m.Feishu.SendError(fmt.Sprintf("回调更新自动续约订单，查询签约信息失败:%v，签约ID:%v", err, contractId)); err != nil {
			log.ErrorWithCtx(ctx, "updateContractInfoWithNoticeAutoOrder SendError err:%v", err)
		}
		return
	}
	if contract == nil {
		log.ErrorWithCtx(ctx, "updateContractInfoWithNoticeAutoOrder contract is nil contractId:%v", contractId)
		return
	}

	//更新签约扣款时间到下个周期
	contract.LastTime = lastTime
	_, contract.NextTime = getNextPayTime(expiredTime, 0)
	contract.Status = utils.SIGN_CONTRACT // 如果扣费成功，重新转回签约状态
	err = m.Store.CreateOrUpdateContract(ctx, contract, nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "doAutoPay CreateOrUpdateContract contractID:%v orderId:%v err:%v", contract.ContractId, orderId, err)
		// 出错就飞书告警
		if err = m.Feishu.SendError(fmt.Sprintf("回调更新自动续约订单，更新签约信息失败:%v，签约ID:%v", err, contractId)); err != nil {
			log.ErrorWithCtx(ctx, "updateContractInfoWithNoticeAutoOrder SendError err:%v", err)
		}
		return
	}
	// 记录扣款次数
	if err = m.Cache.IncrPayCallCnt(ctx, time.Now(), 1); err != nil {
		log.ErrorWithCtx(ctx, "doAutoPay IncrPayCallCnt err:%v", err)
	}
	// 判断上报
	if !utils.IsAppstore(contract.PayChannel) {
		reportContract(ctx, contract)
	}
	defer func() {
		time.Sleep(2 * time.Second)
		if err = m.Cache.DelContractList(ctx, contract.Uid); err != nil {
			log.ErrorWithCtx(ctx, "updateContractInfoWithNoticeAutoOrder DelContractList err:%v", err)
		}
	}()
}

// DelUserContract 删除用户签约(仅测试使用)
func (m *Manager) DelUserContract(ctx context.Context, uid uint32) error {
	err := m.Store.DelUserContract(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelUserContract DelUserContract err:%v", err)
		return err
	}
	err = m.Cache.DelContractList(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelUserContract DelContractList err:%v", err)
		return err
	}
	return nil
}

// reportContract 上报签约信息(这里调用了外部包)
func reportContract(ctx context.Context, newContract *ContractRecord) {
	reportMap := make(map[string]interface{})
	reportMap["uid"] = newContract.Uid //
	reportMap["version"] = newContract.Version
	reportMap["osType"] = newContract.OsType
	reportMap["payChannel"] = newContract.PayChannel
	reportMap["orderId"] = newContract.OrderId
	reportMap["packageId"] = newContract.PackageId //
	reportMap["bundleId"] = newContract.BundleId
	reportMap["contractId"] = newContract.ContractId
	reportMap["sceneId"] = newContract.SceneId
	reportMap["status"] = newContract.Status //
	reportMap["periodType"] = newContract.PeriodType
	reportMap["period"] = newContract.Period

	reportMap["lastTime"] = utils.GetTimeStr(newContract.LastTime)
	reportMap["nextTime"] = utils.GetTimeStr(newContract.NextTime)

	reportMap["createTime"] = utils.GetTimeStr(newContract.CreateTime) //
	reportMap["updateTime"] = utils.GetTimeStr(time.Now())             //强制更新时间
	reportMap["totalDate"] = utils.GetTimeStr(time.Now())
	datacenter.StdReportKV(ctx, "************", reportMap)

	log.DebugWithCtx(ctx, "reportContract %+v", reportMap)
}
