package mgr

import (
	"context"
	"time"

	"golang.52tt.com/pkg/protocol"

	User "golang.52tt.com/clients/account"
	"golang.52tt.com/protocol/app/superplayerlogic"

	"golang.52tt.com/services/super-player/super-player-svr/internal/pkg/pay-api"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	pb "golang.52tt.com/protocol/services/superplayersvr"
	"golang.52tt.com/services/super-player/super-player-svr/internal/pkg/localcache"
	"golang.52tt.com/services/super-player/super-player-svr/internal/pkg/reporter"
)

type Store interface {
	AddOrUpdateOrderIndex(ctx context.Context, orderIndex *OrderIndex, tx mysql.Txx) error
	AddOrUpdateOrderMonthlyDetailVip(ctx context.Context, detail *OrderMonthlyDetailVip, period time.Time) error
	AddOrUpdateOrderMonthlyDetailSvip(ctx context.Context, detail *OrderMonthlyDetailSvip, period time.Time) error
	AddOrUpdateOrderMonthlySumInfo(ctx context.Context, info *OrderMonthlySum) error
	AddOrUpdateOrderMonthlySumInfos(ctx context.Context, infos []*OrderMonthlySum) error
	BatchGetAllOrderMonthlyDetailVIP(ctx context.Context, period time.Time) ([]*OrderMonthlyDetailVip, error)
	BatchGetAllOrderMonthlyDetailVIPLeft(ctx context.Context, period time.Time) ([]*OrderMonthlyDetailVip, error)
	BatchGetAllOrderMonthlyDetailSVIP(ctx context.Context, period time.Time) ([]*OrderMonthlyDetailSvip, error)
	BatchGetAllOrderMonthlyDetailSVIPLeft(ctx context.Context, period time.Time) ([]*OrderMonthlyDetailSvip, error)
	BatchGetAllOrderDailyRedemptionInfo(ctx context.Context, startTime time.Time) ([]*OrderDailyRedemptionInfo, error)
	BatchGetAllOrderDailyRedemptionWithLastDay(ctx context.Context, period time.Time) ([]*OrderDailyRedemptionInfo, error)
	BatchGetAllOrderRevokeRedemptionInfo(ctx context.Context, period time.Time) ([]*OrderDailyRedemptionInfo, error)
	BatchGetRedemptions(ctx context.Context, uids []uint32, startTime time.Time, endTime time.Time) ([]*OrderRedemption, error)
	BatchGetUpgradeRedemptionInfoBySettlePeriod(ctx context.Context, startTime time.Time, endTime time.Time) ([]*UpgradeOrderRedemptionInfo, error)
	BatchGetUserOrderLimit(ctx context.Context, uid uint32, orderLimitIds []uint32) ([]*OrderLimitPkgCount, error)
	Close() error
	CreateMonthlyTable(ctx context.Context, now time.Time) error
	CreateOrder(ctx context.Context, order *Order, tx mysql.Txx) error
	CreateOrUpdateContract(ctx context.Context, contract *ContractRecord, tx mysql.Txx) error
	DeleteOrderMonthlySumInfo(ctx context.Context, period string, packageId string) error
	DelUserContract(ctx context.Context, uid uint32) error
	GetActivityUserLimitPurchase(ctx context.Context, uid uint32, activityId string) (*ActivityUserLimitPurchase, error)
	GetContract(ctx context.Context, contractID string, tx mysql.Txx) (*ContractRecord, error)
	GetContractList(ctx context.Context, beginTs, endTs time.Time, status []string) ([]*ContractRecord, error)
	GetContractUidInfoList(ctx context.Context, beginTs, endTs time.Time, status []string) ([]*ContractRecord, error)
	GetContractListByUid(ctx context.Context, uid uint32) ([]*ContractRecord, error)
	GetContractListByUidList(ctx context.Context, uidList []uint32) (map[uint32][]*ContractRecord, error)
	GetLastFiveMinutesActiveOrderInfo(ctx context.Context) ([]*Order, error)
	GetLastTenMinuteUserRedemptionValueInfo(ctx context.Context) ([]*OrderValueRedemption, error)
	GetOrder(ctx context.Context, orderID string, serverTime time.Time, tx mysql.Txx, isGlobalSearch bool) (*Order, error)
	GetOrderIndexByUid(ctx context.Context, uid uint32, marketId int, startTime time.Time, endTime time.Time) ([]*OrderIndex, error)
	GetOrderRevokeFlow(ctx context.Context, uid uint32, orderId string) (*OrderRevokeFlow, error)
	GetPaidOrderList(ctx context.Context, beginTs, endTs time.Time) ([]*Order, error)
	GetPastYearPaidOrderListByUid(ctx context.Context, uid uint32, marketId, months int, isAutoPay bool) ([]*Order, error)
	GetUserRedemptionWithReason(ctx context.Context, uid uint32, reason string, startTime, endTime time.Time) ([]*OrderRedemption, error)
	GetTx() (mysql.Txx, error)
	GetUserCanSettlementOrders(ctx context.Context, uid uint32, packageType uint32, tx mysql.Txx) ([]*OrderIndex, error)
	GetUserLeftTimeSum(ctx context.Context, uid uint32, packageType uint32, tx mysql.Txx) (int, error)
	GetOrderRevokeFlowsByTime(ctx context.Context, startTime, endTime time.Time) ([]*OrderRevokeFlow, error)
	GetUserOrderLimitCount(ctx context.Context, uid uint32, orderLimitId uint32) (int64, error)
	InsertOrderMonthlyDetailVIP(ctx context.Context, details []*OrderMonthlyDetailVip, needCover bool, period time.Time) error
	InsertOrderMonthlyDetailSVIP(ctx context.Context, details []*OrderMonthlyDetailSvip, needCover bool, period time.Time) error
	RedemptionUserMonthlyOrderValue(ctx context.Context, uid uint32, now time.Time) (icrValue uint32, orderId string, parentOrder string, err error)
	RedemptionUserOrderIndex(ctx context.Context, uid uint32, orderId string, redemptionDays uint32, isDaily bool, reason string, isTest bool) error
	RedemptionUserOrdersWithPkgType(ctx context.Context, uid uint32, orderId string, redemptionDays uint32, packageType uint32, settlementTime time.Time, forTest bool) error
	RedemptionUserOrderIndexValue(ctx context.Context, uid uint32, originOrderId string) (uint32, string, string, error)
	RevokeOrder(ctx context.Context, orderId string, serverTime time.Time) error
	NotifyContract(ctx context.Context, req *NotifyContractInfo) (newRecord *ContractRecord, updateCache bool, newSign bool, err error)
	NoticeOrder(ctx context.Context, orderId string, activeUid uint32, serverTime time.Time, totalFee float32, payChannel string, status pb.EnumOrderStatus, pkgLimitId uint32) (*Order, error)
	UpdateOrderMonthlyDetailsVIP(ctx context.Context, details []*OrderMonthlyDetailVip, period time.Time) error
	UpdateOrderMonthlyDetailsSVIP(ctx context.Context, details []*OrderMonthlyDetailSvip, period time.Time) error
	ValidUserIsAnnualSvipBeforeTime(ctx context.Context, uid uint32, activityTime time.Time) (bool, error)

	AddContractAutoCancelLog(ctx context.Context, contractId string, uid uint32, nextTime time.Time, errInfo string) error

	GetOrderDetailByOrderId(ctx context.Context, orderId string, period time.Time) (*OrderMonthlyDetailVip, error)
	GetSvipOrderDetailByOrderId(ctx context.Context, orderId string, period time.Time) (*OrderMonthlyDetailSvip, error)
	GetOrderIndexInfo(ctx context.Context, uid uint32, orderId string, tx mysql.Txx) (*OrderIndex, error)

	DelContractByContractId(ctx context.Context, uid uint32, contractId string) error
	GetContractsWithIOS(ctx context.Context, limit int, offset int) ([]*CheckContract, error)
	UpdateContractStatus(ctx context.Context, uid uint32, contractId string, status string) error
	UpdateContractNextTime(ctx context.Context, uid uint32, contractId string, nextTime time.Time) error
}

type Cache interface {
	AutoOrderIntervalLock(ctx context.Context, uid uint32, packageType uint32, orderId string, duration time.Duration) error
	BatchSetContractList(ctx context.Context, contractsMap map[uint32][]*pb.SuperPlayerContractInfo) error
	Close() error
	DelContractList(ctx context.Context, uid uint32) error
	GetAutoPayContractOffSetFinishKey(ctx context.Context) (int64, error)
	GetAutoRedemptionUserOrderIndex(ctx context.Context, val int64) (int64, error)
	GetContractList(ctx context.Context, uid uint32) ([]*pb.SuperPlayerContractInfo, error)
	GetContractLists(ctx context.Context, uidMap map[uint32]bool) (map[uint32][]*pb.SuperPlayerContractInfo, error)
	GetDeviceBuyRecord(ctx context.Context, deviceId string) (string, error)
	GetIosUserHasSign(ctx context.Context, uid uint32) (string, error)
	GetOrderRecordList(ctx context.Context, uid uint32, marketId int, off, count int64) ([]*pb.PayRecord, error)
	GetPayCallCnt(ctx context.Context, ts time.Time) (int64, error)
	IsContractTimeRange(ctx context.Context, uid, marketId uint32) (bool, error)
	IncrPayCallCnt(ctx context.Context, ts time.Time, val int64) error
	IsExistCancelContractPopUpInfo(ctx context.Context, uid uint32) bool
	LockAutoCancelContract(ctx context.Context, expire time.Duration) (bool, error)
	LockAutoCancelContractWithContract(ctx context.Context, contractId string, expire time.Duration) (bool, error)
	LockAutoPay(ctx context.Context, expire time.Duration) (bool, error)
	LockAutoPayNotice(ctx context.Context, uid uint32, nextTime time.Time, expire time.Duration) (bool, error)
	LockCancelContractNotify(ctx context.Context, contractId string, nextPayTs int64, expire time.Duration) (bool, error)
	LockFiveDayRenewalNotify(ctx context.Context, contractId string, nextPayTs int64, expire time.Duration) (bool, error)
	LockOrderLimitPurchase(ctx context.Context, uid uint32, limitId uint32, orderId string, expire time.Duration) (bool, error)
	LockOrderOneMinLimitPurchase(ctx context.Context, uid uint32, limitId uint32, expire time.Duration) (bool, error)
	LockOrderId(ctx context.Context, orderId string, expire time.Duration) (bool, error)
	LockOrderOperate(ctx context.Context, uid uint32, orderId string, expire time.Duration) (bool, error)
	LockPlaceOrder(ctx context.Context, uid uint32, orderId string, expire time.Duration) (bool, error)
	LockUserPlaceUpgradeOrder(ctx context.Context, uid uint32, orderId string, expire time.Duration) (bool, error)
	LockRedemptionUserDayFinish(ctx context.Context, uid uint32, dayTime time.Time) error
	LockRedemptionUserDayOpt(ctx context.Context, uid uint32, dayTime time.Time) (bool, error)
	LockRedemptionUserOrderIndex(ctx context.Context, index int64, expire time.Duration) (bool, error)
	LockRedemptionUserOrderIndexFinish(ctx context.Context, index int64, finishTime time.Time) error
	LockRedemptionUserValueDayFinish(ctx context.Context, uid uint32, dayTime time.Time) error
	LockRedemptionUserValueDayOpt(ctx context.Context, uid uint32, dayTime time.Time) (bool, error)
	LockReplacementOrder(ctx context.Context, order string, expire time.Duration) (bool, error)
	SetAutoPayContractOffSetFinishKey(ctx context.Context, val int64, expire time.Duration) error
	SetCancelContractPopUpInfoLock(ctx context.Context, uid uint32, expire time.Duration) error
	SetContractList(ctx context.Context, uid uint32, contracts []*pb.SuperPlayerContractInfo) error // 该方法强制要求写入整个签约列表
	SetContractTimeRange(ctx context.Context, uid, marketId uint32, ts int64) error
	SetDeviceBuyRecord(ctx context.Context, deviceId string, value string) error
	SetIosUserHasSign(ctx context.Context, uid uint32, contractId string) error
	SetOrderRecordList(ctx context.Context, uid uint32, marketId int, orderList []*pb.PayRecord) error
	UnLockAutoOrderInterval(ctx context.Context, uid uint32, packageType uint32, orderId string)
	UnlockAutoPay(ctx context.Context)
	UnlockOrderLimitPurchase(ctx context.Context, uid uint32, limitId uint32, orderId string) error
	UnlockOrderOneMinLimitPurchase(ctx context.Context, uid uint32, limitId uint32)
	UnlockOrderOperate(ctx context.Context, uid uint32, orderId string) error
	UnlockPlaceOrder(ctx context.Context, uid uint32, orderId string) error
	UnlockRedemptionUserDayOpt(ctx context.Context, uid uint32, dayTime time.Time)
	UnlockRedemptionUserValueDayOpt(ctx context.Context, uid uint32, dayTime time.Time)
	UnlockUserPlaceUpgradeOrder(ctx context.Context, uid uint32, orderId string)
	ValidRedemptionUserDayFinish(ctx context.Context, uid uint32, dayTime time.Time) (bool, error)
	ValidRedemptionUserValueDayFinish(ctx context.Context, uid uint32, dayTime time.Time) (bool, error)
	ValidRedemptionUserOrderIndexFinish(ctx context.Context, index int64, finishTime time.Time) (bool, error)
	LockGenReportOpt(ctx context.Context) (bool, error)
	UnlockGenReportOpt(ctx context.Context)
	LockGenReportFinish(ctx context.Context, dayTime time.Time) error
	ValidGenReportFinish(ctx context.Context, dayTime time.Time) (bool, error)
	GetLatestPayRecord(ctx context.Context, uid uint32) (int64, error)
	SetLatestPayRecord(ctx context.Context, uid uint32) error
}

type DynamicConfig interface {
	GetBusinessCfg(marketId uint32, auto bool) (string, string, error)
	GetCacheSecond() int64
	GetCancelContractMsg(payChannel string, both bool) (string, int64)
	GetContractNotifyUrl() string
	GetSinglePurchaseLimitKeyword(packageId string) (key string, limitTime int64)
	GetPayLogDays() int
	GetPayApiPara() *pay.ApiPara
	IsSinglePurchaseLimitOpen() bool
	IsTestNextPay() bool
	TestNextPayDay() int64
	GetSPSystemBeginTs() (int64, int64)
	RenewalNotifyDay() int
	GetRenewalNotifyMsg(os protocol.OS, ts time.Time, packageType uint32) string
	GetCancelContractNotifyMsg(os protocol.OS, ts time.Time, packageType uint32) string
	GetIosDelayExpireHour() int64
	IsInitiativeCancelContract(nextPayTs time.Time) bool
	IsTestAutoPay() bool
	IsNotBalance(err error) bool
	IsCloseOrder(err error) bool
	GetRemarkAndProductCode(packageType uint32, marketId uint32, isIos bool, isWeiXinPay bool) (string, string)
}

type WhitelistDynamicConfig interface {
	GetUserCanUpgradeDays(uid uint32) (int64, bool)
	ValidUserAndGetWhiteListInfoForPeriod(uid uint32) (isWhitelistUser bool, vipExpired int64, isVipPeriodPkg bool, svipExpired int64, isSvipPeriodPkg bool)
	GetUserCanNotUpgradeTime(uid uint32) (int, int)
	GetUserLeftDays(uid uint32) (vip int64, svip int64, isExist bool)
}

type PayClient interface {
	PlaceOrder(ctx context.Context, in proto.MessageV1) (interface{}, error)
	GetContract(ctx context.Context, in proto.MessageV1) (interface{}, error)
	CancelContract(ctx context.Context, in proto.MessageV1) (interface{}, error)
	AutoPay(ctx context.Context, in proto.MessageV1) (interface{}, error)
	DelayPay(ctx context.Context, in proto.MessageV1) (interface{}, error)
}

type Client interface {
	GetUserInfo(ctx context.Context, uid uint32) (*User.User, error)
	PushUserMsg(ctx context.Context, subMsg []byte, msgTy superplayerlogic.SuperPlayerInfoPushMsg_ENUM_SUPER_PLAYER_PUSH_MSG, uid uint32) error
	SendImMsg(uid uint32, content, hlight string) error
}

type Manager struct {
	Store           Store
	Cache           Cache
	Feishu          reporter.IFeishu
	DyConf          DynamicConfig
	LocalCache      localcache.LocalCache
	Pay             PayClient
	DyConfWhitelist WhitelistDynamicConfig
	Cli             Client
}

func (m *Manager) ShutDown() {
	_ = m.Store.Close()
	_ = m.Cache.Close()
}

// CreateMonthlyTable 创建月表
func (m *Manager) CreateMonthlyTable(ctx context.Context, now time.Time) error {
	return m.Store.CreateMonthlyTable(ctx, now)
}
