package models

// IGroupApiCli 查询人群包API-client
// https://q9jvw0u5f5.feishu.cn/docx/WrdOdjE2AoHDDTxXlw5cK6BnnAe
import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/services/super-player/super-player-http-logic/conf"
	"golang.org/x/net/context/ctxhttp"
	"io/ioutil"
	"net/http"
	"strings"
	"time"
)

var GroupApiClient IGroupApiCli

func init() {
	GroupApiClient = NewGroupApiCli()
}

const (
	API_CALLER      = "super-player-http-logic"
	APP_ID          = "ttvoice"
	CHECK_URI       = "/iop-api/group/matchBatch"
	CHECK_EXIST_URI = "/lpm-admin/openapi/group/batchQuery"
)

type IGroupApiCli interface {
	CheckUserGroup(ctx context.Context, uid uint32, groupIds []string) ([]string, error)
	CheckGroupIsExist(ctx context.Context, groupId string) (bool, error)
}
type GroupApiCli struct {
	httpClient      *http.Client
	httpExistClient *http.Client
}

func NewGroupApiCli() IGroupApiCli {
	return &GroupApiCli{
		httpClient:      &http.Client{Timeout: time.Second * 32},
		httpExistClient: &http.Client{Timeout: time.Second * 32},
	}
}

type CheckUserGroupApiReq struct {
	ID     int64 `json:"id"`
	Client struct {
		Caller string `json:"caller"`
		Ex     string `json:"ex"`
	}
	Data struct {
		AppId     string   `json:"appId"`
		GroupList []string `json:"groupList"`
		UidList   []string `json:"uidList"`
	} `json:"data"`
}

type CheckUserGroupApiResp struct {
	ID      int64  `json:"id"`
	Status  int    `json:"status"`
	Message string `json:"message"`
	Data    struct {
		AppId  string              `json:"appId"`
		Result []map[string]string `json:"result"`
	} `json:"data"`
}

// CheckUserGroup 检查用户人群包映射关系，返回属于的人群包ID
func (g *GroupApiCli) CheckUserGroup(ctx context.Context, uid uint32, groupIdList []string) ([]string, error) {
	if len(groupIdList) == 0 {
		return groupIdList, nil
	}
	var groupIds []string
	groupMaps := map[string]struct{}{}
	for _, gid := range groupIdList {
		if _, ok := groupMaps[gid]; ok {
			continue
		}
		groupIds = append(groupIds, gid)
		groupMaps[gid] = struct{}{}
	}
	now := time.Now()
	req := &CheckUserGroupApiReq{
		ID: now.Unix(),
	}
	req.Client.Caller = API_CALLER
	req.Data.AppId = APP_ID
	req.Data.GroupList = groupIds
	uidStr := fmt.Sprintf("%d", uid)
	req.Data.UidList = []string{uidStr}

	rsp, err := g.checkUserGroupApi(ctx, uid, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUidsByGroupId Failed to uid:%d err:%v", uid, err)
		return nil, err
	}
	if len(rsp.Data.Result) == 0 { //no groups
		return nil, nil
	}
	result := rsp.Data.Result[0]
	if result["uid"] != uidStr {
		return nil, nil
	}
	var okGroups []string
	for _, groupId := range groupIds {
		if v, ok := result[groupId]; ok {
			if v == "1" {
				okGroups = append(okGroups, groupId)
			}
		}
	}
	log.InfoWithCtx(ctx, "GetUidsByGroupId ok uid:%d, okgroups:%v", uid, okGroups)
	return okGroups, nil
}

func (g *GroupApiCli) checkUserGroupApi(ctx context.Context, uid uint32, req *CheckUserGroupApiReq) (*CheckUserGroupApiResp, error) {
	reqBytes, err := json.Marshal(req)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkUserGroupApi Failed to marshal uid:%d, request: %v %v", uid, req, err)
		return nil, err
	}
	reqJson := string(reqBytes)
	url := conf.GetGroupHost() + CHECK_URI
	httpReq, err := http.NewRequest("POST", url, strings.NewReader(reqJson))
	if nil != err {
		log.ErrorWithCtx(ctx, "checkUserGroupApi Failed to NewRequest uid:%d request: %v %v", uid, reqJson, err)
		return nil, err
	}
	httpReq.Header.Set("Content-Type", "application/json")
	shortCtx, cancel := context.WithTimeout(ctx, time.Second*5)
	defer cancel()

	log.DebugWithCtx(ctx, "checkUserGroupApi req:%v", reqJson)
	resp, err := ctxhttp.Do(shortCtx, g.httpClient, httpReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkUserGroupApi %s, uid:%d, req: %v post error %v", url, uid, reqJson, err)
		return nil, err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkUserGroupApi %s, uid:%d, req: %v read body failed: %+v", url, uid, reqJson, err)
		return nil, err
	}

	response := &CheckUserGroupApiResp{}
	err = json.Unmarshal(body, response)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkUserGroupApi req:%v unmarshal body %v failed: %v", url, reqJson, string(body), err)
		return nil, err
	}
	if response.Status != 0 {
		log.ErrorWithCtx(ctx, "checkUserGroupApi url :%s, uid:%d req:%v response body %v ", url, uid, reqJson, string(body))
		return nil, errors.New(fmt.Sprintf("api status:%d", response.Status))
	}
	log.InfoWithCtx(ctx, "checkUserGroupApi ok uid:%d, response:%+v", uid, response)
	return response, nil
}

type GroupIsExistApiRsp struct {
	Code    int    `json:"code"`
	Msg     string `json:"msg"`
	Success bool   `json:"success"`
	Data    struct {
		GroupList []struct {
			GroupId int64 `json:"groupId"`
		} `json:"groupList"`
		NoExistGroupIdList []int64 `json:"noExistGroupIdList"`
	} `json:"data"`
}

// CheckGroupIsExist 检查人群包ID是否存在
func (g *GroupApiCli) CheckGroupIsExist(ctx context.Context, groupId string) (bool, error) {
	reqJson := fmt.Sprintf(`{"idList":[%s]}`, groupId)
	url := conf.GetGroupCheckHost() + CHECK_EXIST_URI
	httpReq, err := http.NewRequest("POST", url, strings.NewReader(reqJson))
	if nil != err {
		log.ErrorWithCtx(ctx, "CheckGroupIsExist Failed to NewRequest request: %v %v", reqJson, err)
		return false, err
	}
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("System-Id", API_CALLER)
	shortCtx, cancel := context.WithTimeout(ctx, time.Second*5)
	defer cancel()
	resp, err := ctxhttp.Do(shortCtx, g.httpExistClient, httpReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckGroupIsExist %s, req: %v post error %v", url, reqJson, err)
		return false, err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckGroupIsExist %s, req: %v read body failed: %+v", url, reqJson, err)
		return false, err
	}

	response := &GroupIsExistApiRsp{}
	err = json.Unmarshal(body, response)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckGroupIsExist req:%v unmarshal body %v failed: %v", url, reqJson, string(body), err)
		return false, err
	}
	if response.Code != 200 {
		log.ErrorWithCtx(ctx, "CheckGroupIsExist url:%s, req:%v response body %v ", url, reqJson, string(body))
		return false, errors.New(fmt.Sprintf("api status:%s", response.Msg))
	}
	log.InfoWithCtx(ctx, "CheckGroupIsExist ok req:%s, response:%+v", reqJson, response)
	if len(response.Data.NoExistGroupIdList) > 0 {
		return false, nil
	}
	return true, nil
}
