package main

import (
	"context"

	"golang.52tt.com/pkg/log"
	_ "gitlab.ttyuyin.com/avengers/tyr/core/service/startup/deprecated"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/grpc"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/startup/suit/grpc/logic"
	pb "golang.52tt.com/protocol/app/api/pgc_channel"
	"golang.52tt.com/protocol/services/demo/echo"
	internal "golang.52tt.com/services/pgc-channel/pgc-channel-logic/internal/server"

	_ "golang.52tt.com/pkg/hub/tyr/compatible/logic" // 兼容tyr公共库
)

func main() {
	var (
		svr *internal.PgcChannelLogicImpl
		cfg = &internal.StartConfig{}
		err error
	)

	// config file support yaml & json, default pgc-channel-logic.json/yaml
	if err := logic.NewLogic("pgc-channel-logic", cfg).
		AddGrpcServer(grpc.NewBuildOption().
			WithInitializeFunc(func(ctx context.Context, s *grpc.Server) error {
				if svr, err = internal.NewPgcChannelLogicImpl(cfg); err != nil {
					return err
				}

				// register custom grpc server
				pb.RegisterPgcChannelLogicServiceServer(s, svr)

				// grpcurl -plaintext -d '{"value":"hello"}' 127.0.0.1:80 demo.echo.EchoService.Echo
				// grpcurl -plaintext 127.0.0.1:80 grpc.health.v1.Health.Check
				echo.RegisterEchoServiceServer(s, svr)
				return nil
			}),
		).
		WithCloseFunc(func(ctx context.Context) {
			// do something when server terminating
			svr.ShutDown()
		}).
		Start(); err != nil {
		log.Errorf("server start fail, err: %v", err)
	}
	log.Infof("server start")
}
