package manager

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/datahouse"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/urrc"
	liveMgrPb "golang.52tt.com/protocol/services/channellivemgr"
	"golang.52tt.com/services/channel-live-stats/mysql"
	"math"
	"math/rand"
	"strconv"
	"time"
)

const (
	RevenueLevel_1 = uint64(1000 * 10000)
	RevenueLevel_2 = uint64(3000 * 10000)
	RevenueLevel_3 = uint64(5000 * 10000)
	RevenueLevel_4 = uint64(10000 * 10000)
	RevenueLevel_5 = uint64(20000 * 10000)
	RevenueLevel_6 = uint64(30000 * 10000)
	RevenueLevel_7 = uint64(50000 * 10000)
	RevenueLevel_8 = uint64(100000 * 10000)
)

func (m *ChannelLiveStatsManager) CheckIsNeedUpdateThisMonth(scene string, nowTm time.Time) bool {
	// 而且数仓每天定时统计数据需要时间，7点后再更新
	if nowTm.Hour() < 7 {
		return false
	}

	isProc, err := m.statsCache.CheckDailyUpdateIsProc(scene, nowTm)
	if err != nil {
		log.Errorf("CheckIsNeedUpdateThisMonth CheckDailyUpdateIsProc failed scene:%s now:%v err:%v",
			scene, nowTm, err)
		return false
	}

	if isProc {
		log.Debugf("CheckIsNeedUpdateThisMonth is proc scene:%s now:%v", scene, nowTm)
		return false
	}

	log.Infof("CheckIsNeedUpdateThisMonth need update scene:%s nowTm:%v", scene, nowTm)
	return true
}

func (m *ChannelLiveStatsManager) CheckIsNeedUpdateLastMonth(scene string, nowTm time.Time) bool {
	// 付费关系链数据是7天一个周期，而且数仓每天定时统计数据需要时间
	if nowTm.Day() > 8 || nowTm.Hour() < 7 {
		return false
	}

	isProc, err := m.statsCache.CheckDailyUpdateIsProc(scene, nowTm)
	if err != nil {
		log.Errorf("CheckIsNeedUpdateLastMonth CheckDailyUpdateIsProc failed scene:%s now:%v err:%v",
			scene, nowTm, err)
		return false
	}

	if isProc {
		log.Debugf("CheckIsNeedUpdateLastMonth is proc scene:%s now:%v", scene, nowTm)
		return false
	}

	log.Infof("CheckIsNeedUpdateLastMonth need update scene:%s nowTm:%v", scene, nowTm)
	return true
}

func (m *ChannelLiveStatsManager) ScheduledUpdateGuildOperationalCapabilities(ctx context.Context) {

	now := time.Now()
	if now.Hour() < 8 && now.Hour() < 10 {
		log.DebugWithCtx(ctx, "ScheduledUpdateGuildOperationalCapabilities now:%v", now)
		return
	}
	log.InfoWithCtx(ctx, "ScheduledUpdateGuildOperationalCapabilities now:%v", now)
	lastDay := time.Now().AddDate(0, 0, -1)
	m.PullGuildOperationalCapabilities(ctx, lastDay)
}

func (m *ChannelLiveStatsManager) PullGuildOperationalCapabilities(ctx context.Context, lastDay time.Time) {
	log.InfoWithCtx(ctx, "PullGuildOperationalCapabilities begin lastDay:%v", lastDay)

	beginDay := time.Date(lastDay.Year(), lastDay.Month(), 1, 0, 0, 0, 0, time.Local)
	endDay := time.Date(lastDay.Year(), lastDay.Month(), lastDay.Day(), 23, 59, 59, 0, time.Local)
	var (
		maxMatureAnchorCnt, maxPotActiveAnchorCnt, maxProAnchorCnt, maxNewActiveAnchorCnt uint32
		maxRevenue                                                                        uint64
	)

	guildIdList, err := m.getAllLiveCoopGuildList(ctx, "", false)
	if err != nil {
		log.ErrorWithCtx(ctx, "PullGuildOperationalCapabilities getAllLiveCoopGuildList err:%s", err)
		return
	}

	mapId2IsCoop := make(map[uint32]bool)
	for _, guildId := range guildIdList {
		mapId2IsCoop[guildId] = true
	}

	mapId2List, mapId2BindId, _, err := m.getBindGuildList(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "PullGuildOperationalCapabilities getBindGuildList err:%s", err)
		return
	}

	guildDataMap := make(map[uint32]*mysql.GuildOperationalCapabilities)
	yesTime := lastDay

	for _, guildId := range guildIdList {
		url := fmt.Sprintf("%s?start_date=%s&end_date=%s&date_dim=month&guild_id=%s&apiToken=%s",
			m.sc.GuildOperationalCapabilitiesUrl, beginDay.Format("2006-01-02"), endDay.Format("2006-01-02"), strconv.FormatInt(int64(guildId), 10), m.sc.DWApiToken)
		log.DebugWithCtx(ctx, "PullGuildOperationalCapabilities url:%s", url)
		respData, err := HttpQueryGuildLiveStats(url)
		if err != nil {
			log.Errorf("PullGuildOperationalCapabilities HttpQueryGuildLiveStats failed url:%s err:%v", url, err)
			continue
		}

		guildList := []uint32{guildId}
		if val, ok := mapId2BindId[guildId]; ok && mapId2IsCoop[val] {
			guildList = append(guildList, val)
		}

		log.DebugWithCtx(ctx, "PullGuildOperationalCapabilities guildId:%d guildList:%d respData:%v", guildId, guildList, respData)
		for _, tmpGuildId := range guildList {
			for _, data := range respData.DataList {
				newSignAnchorCnt, _ := strconv.Atoi(data.NewSignAnchorCnt)
				enabledLiveUserCount, _ := strconv.Atoi(data.EnabledLiveUserCount)
				enabledLiveNewUserCount, _ := strconv.Atoi(data.EnabledLiveNewUserCount)
				newActiveAnchorCnt, _ := strconv.Atoi(data.NewActiveAnchorCnt)
				proAnchorCnt, _ := strconv.Atoi(data.ProAnchorCnt)
				matureAnchorCnt, _ := strconv.Atoi(data.MatureAnchorCnt)
				potActiveAnchorCnt, _ := strconv.Atoi(data.PotActiveAnchorCnt)
				newSignProAnchor, _ := strconv.Atoi(data.NewSignProAnchor)
				revenue, _ := strconv.ParseUint(data.Revenue, 10, 64)
				rand.Seed(time.Now().UnixNano())
				r := rand.Intn(900) + 10
				r = 1
				revenue = revenue * uint64(r)
				t, _ := time.ParseInLocation("2006-01-02 15:04:05", data.RevenueUpdateTime, time.Local)
				if val, ok := guildDataMap[tmpGuildId]; ok {
					val.NewSignAnchorCnt += uint32(newSignAnchorCnt)
					val.EnabledLiveUserCount += uint32(enabledLiveUserCount)
					val.EnabledLiveNewUserCount += uint32(enabledLiveNewUserCount)
					val.NewActiveAnchorCnt += uint32(newActiveAnchorCnt)
					val.ProAnchorCnt += uint32(proAnchorCnt)
					val.MatureAnchorCnt += uint32(matureAnchorCnt)
					val.PotActiveAnchorCnt += uint32(potActiveAnchorCnt)
					val.NewSignProAnchor += uint32(newSignProAnchor)
					val.Revenue += revenue

				} else {
					guildData := &mysql.GuildOperationalCapabilities{
						GuildID:                 tmpGuildId,
						Month:                   yesTime.Format("2006-01-02"),
						NewSignAnchorCnt:        uint32(newSignAnchorCnt),
						EnabledLiveUserCount:    uint32(enabledLiveUserCount),
						EnabledLiveNewUserCount: uint32(enabledLiveNewUserCount),
						NewActiveAnchorCnt:      uint32(newActiveAnchorCnt),
						ProAnchorCnt:            uint32(proAnchorCnt),
						MatureAnchorCnt:         uint32(matureAnchorCnt),
						PotActiveAnchorCnt:      uint32(potActiveAnchorCnt),
						NewSignProAnchor:        uint32(newSignProAnchor),
						Revenue:                 revenue,
						RevenueUpdateTime:       t,
					}
					guildDataMap[tmpGuildId] = guildData
				}
				tmpGuildData := guildDataMap[tmpGuildId]

				if tmpGuildData.NewActiveAnchorCnt > maxNewActiveAnchorCnt {
					maxNewActiveAnchorCnt = tmpGuildData.NewActiveAnchorCnt
				}
				log.DebugWithCtx(ctx, "PullGuildOperationalCapabilities tmpGuild:%+v   %+v   %+v", tmpGuildData.GuildID, tmpGuildData.NewActiveAnchorCnt, maxNewActiveAnchorCnt)
				if tmpGuildData.ProAnchorCnt > maxProAnchorCnt {
					maxProAnchorCnt = tmpGuildData.ProAnchorCnt
				}
				if tmpGuildData.MatureAnchorCnt > maxMatureAnchorCnt {
					maxMatureAnchorCnt = tmpGuildData.MatureAnchorCnt
				}
				if tmpGuildData.PotActiveAnchorCnt > maxPotActiveAnchorCnt {
					maxPotActiveAnchorCnt = tmpGuildData.PotActiveAnchorCnt
				}
				if tmpGuildData.Revenue > maxRevenue {
					maxRevenue = tmpGuildData.Revenue
				}

				if tmpGuildData.Revenue < RevenueLevel_1 {
					tmpGuildData.RevenueLevel = 1
				} else if tmpGuildData.Revenue >= RevenueLevel_1 && tmpGuildData.Revenue < RevenueLevel_2 {
					tmpGuildData.RevenueLevel = 2
				} else if tmpGuildData.Revenue >= RevenueLevel_2 && tmpGuildData.Revenue < RevenueLevel_3 {
					tmpGuildData.RevenueLevel = 3
				} else if tmpGuildData.Revenue >= RevenueLevel_3 && tmpGuildData.Revenue < RevenueLevel_4 {
					tmpGuildData.RevenueLevel = 4
				} else if tmpGuildData.Revenue >= RevenueLevel_4 && tmpGuildData.Revenue < RevenueLevel_5 {
					tmpGuildData.RevenueLevel = 5
				} else if tmpGuildData.Revenue >= RevenueLevel_5 && tmpGuildData.Revenue < RevenueLevel_6 {
					tmpGuildData.RevenueLevel = 6
				} else if tmpGuildData.Revenue >= RevenueLevel_6 && tmpGuildData.Revenue < RevenueLevel_7 {
					tmpGuildData.RevenueLevel = 7
				} else if tmpGuildData.Revenue >= RevenueLevel_7 && tmpGuildData.Revenue < RevenueLevel_8 {
					tmpGuildData.RevenueLevel = 8
				} else if tmpGuildData.Revenue >= RevenueLevel_8 {
					tmpGuildData.RevenueLevel = 9
				}

				log.InfoWithCtx(ctx, "PullGuildOperationalCapabilities guildId:%d tmpGuildId:%d data:%+v tmpGuildData:%v", guildId, tmpGuildId, data, tmpGuildData)
			}
		}
	}

	log.DebugWithCtx(ctx, "PullGuildOperationalCapabilities %v", mapId2List)

	for _, data := range guildDataMap {
		data.RevenueScore = uint32(math.Round(float64(data.Revenue) / float64(maxRevenue) * 100000))
		data.ProAnchorScore = uint32(math.Round(float64(data.ProAnchorCnt) / float64(maxProAnchorCnt) * 100000))
		data.NewActiveAnchorScore = uint32(math.Round(float64(data.NewActiveAnchorCnt) / float64(maxNewActiveAnchorCnt) * 100000))
		data.MatureAnchorScore = uint32(math.Round(float64(data.MatureAnchorCnt) / float64(maxMatureAnchorCnt) * 100000))
		data.PotActiveAnchorScore = uint32(math.Round(float64(data.PotActiveAnchorCnt) / float64(maxPotActiveAnchorCnt) * 100000))
		data.TotalScore = uint32(math.Round((float64(data.RevenueScore)*0.20 + float64(data.NewActiveAnchorScore)*0.15 + float64(data.ProAnchorScore)*0.25 + float64(data.MatureAnchorScore)*0.2 + float64(data.PotActiveAnchorScore)*0.2) * 100))
		log.DebugWithCtx(ctx, "PullGuildOperationalCapabilities tmpGuild:%+v   %+v   %+v", data.GuildID, data.NewActiveAnchorCnt, maxNewActiveAnchorCnt)
		m.mysqlStore.UpdateGuildOperationalCapabilities(ctx, nil, data)

	}

	log.InfoWithCtx(ctx, "PullGuildOperationalCapabilities end")
}

// 定时统计公会月维度主播信息
func (m *ChannelLiveStatsManager) TimerUpdateGuildAnchorMonthInfo(ctx context.Context) {
	nowTm := time.Now()

	log.Infof("TimerUpdateGuildAnchorMonthInfo begin now:%v", nowTm)

	lastDayTm := nowTm.AddDate(0, 0, -1)
	beginMonthTm := time.Date(lastDayTm.Year(), lastDayTm.Month(), 1, 0, 0, 0, 0, time.Local)
	endMonthTm := time.Date(lastDayTm.Year(), lastDayTm.Month(), lastDayTm.Day(), 23, 59, 59, 0, time.Local)

	lastMonthTm := beginMonthTm.AddDate(0, 0, -1)
	lastMonthBeginTm := time.Date(lastMonthTm.Year(), lastMonthTm.Month(), 1, 0, 0, 0, 0, time.Local)
	lastMonthEndTm := time.Date(lastMonthTm.Year(), lastMonthTm.Month(), lastMonthTm.Day(), 23, 59, 59, 0, time.Local)

	// 更新pgc房间本月统计数据
	if m.CheckIsNeedUpdateThisMonth("this_anchor_update", nowTm) {
		m.UpdateGuildAnchorMonthInfo("this_anchor_update", nowTm, beginMonthTm, endMonthTm)
	}

	// 检查是否需要统计pgc房间上个月的统计数据
	if m.CheckIsNeedUpdateLastMonth("last_anchor_update", nowTm) {
		m.UpdateGuildAnchorMonthInfo("last_anchor_update", nowTm, lastMonthBeginTm, lastMonthEndTm)
	}
}
func (m *ChannelLiveStatsManager) UpdateGuildAnchorMonthInfo(scene string, nowTm, beginMonthTm, endMonthTm time.Time) {
	log.Infof("UpdateGuildAnchorMonthInfo begin now:%v  begin:%v end:%v ", nowTm, beginMonthTm, endMonthTm)

	var offset uint32 = 0
	var limit uint32 = 100
	ctx := context.Background()

	for {
		monthlyList, err := m.mysqlStore.GetAllAnchorMonthlyStatsList(ctx, beginMonthTm, offset, limit)
		if err != nil {
			log.Errorf("UpdateGuildAnchorMonthInfo GetAllAnchorMonthlyStatsList failed nowTm:%v %d %d err:%v", nowTm, offset, limit, err)
			return
		}

		mapGuild2UidList := make(map[uint32][]uint32, 0)
		mapGuildUid2Stats := make(map[string]*mysql.AnchorMonthlyLiveRecord, 0)
		for _, info := range monthlyList {
			mapGuild2UidList[info.SignGuildId] = append(mapGuild2UidList[info.SignGuildId], info.Uid)
			mapGuildUid2Stats[fmt.Sprintf("%d-%d", info.SignGuildId, info.Uid)] = info
		}

		for guildId, uidList := range mapGuild2UidList {
			dailylist, err := m.mysqlStore.BatchGetAnchorDailyRecord(ctx, guildId, 0, uidList, beginMonthTm, endMonthTm)
			if err != nil {
				log.ErrorWithCtx(ctx, "UpdateGuildAnchorMonthInfo BatchGetAnchorDailyRecord fail. guildId:%d len:%d err:%v",
					guildId, len(uidList), err)
				return
			}

			mapUid2DailyInfo := make(map[uint32][]*mysql.AnchorDailyLiveRecord)
			for _, daily := range dailylist {
				mapUid2DailyInfo[daily.Uid] = append(mapUid2DailyInfo[daily.Uid], daily)
			}

			tmpUidList := make([]uint32, 0)
			for _, uid := range uidList {
				info, ok := mapGuildUid2Stats[fmt.Sprintf("%d-%d", guildId, uid)]
				if !ok {
					continue
				}

				if (info.AnchorIncome+info.KnightIncome) >= mysql.QualityMinMonthIncome || info.ChannelFee >= mysql.ActiveMinMonthChannelFee {
					tmpUidList = append(tmpUidList, info.Uid)
				}
			}

			mapUid2MemInfo := make(map[uint32]*datahouse.GuildMemberInfo)
			mapUid2PayInfo := make(map[uint32]*datahouse.GuildMemberChainMonthInfo)
			mapUid2Alias := make(map[uint32]string)
			mapTid2Violations := make(map[string][]uint32, 0)

			if len(tmpUidList) != 0 {
				guildMemInfoList, err := datahouse.QueryGuildMemberMonthInfo(ctx, tmpUidList, guildId, beginMonthTm, endMonthTm)
				if err != nil {
					log.ErrorWithCtx(ctx, "UpdateGuildAnchorMonthInfo QueryGuildMemberMonthInfo failed guildId:%d len:%d err:%v",
						guildId, len(tmpUidList), err)
					return
				}

				for _, mem := range guildMemInfoList {
					mapUid2MemInfo[mem.UserId] = mem
				}

				payInfoList, err := datahouse.QueryMemberChainMonthInfo(ctx, guildId, tmpUidList, beginMonthTm, endMonthTm)
				if err != nil {
					log.ErrorWithCtx(ctx, "UpdateGuildAnchorMonthInfo QueryMemberChainMonthInfo failed guildId:%d len:%d err:%v",
						guildId, len(tmpUidList), err)
					return
				}

				for _, payInfo := range payInfoList {
					mapUid2PayInfo[payInfo.UserId] = payInfo
				}

				userMap, err := m.accountCli.GetUsersMap(ctx, tmpUidList)
				if err != nil {
					log.Errorf("UpdateGuildAnchorMonthInfo GetUsersMap err:%v, uidList:%v", err, tmpUidList)
					return
				}

				tidList := make([]string, 0)
				for _, v := range userMap {
					tidList = append(tidList, v.GetAlias())
					mapUid2Alias[v.GetUid()] = v.GetAlias()
				}

				mapTid2Violations, _, _, _, err = urrc.GetPractitionerViolationsInfoFromAudit(tidList, beginMonthTm, endMonthTm)
				if err != nil {
					log.Errorf("UpdateGuildAnchorMonthInfo GetPractitionerViolationsInfoFromAudit failed guild:%d len:%d last:%v err:%v",
						guildId, len(tidList), beginMonthTm, err)
					return
				}
			}

			for _, uid := range uidList {
				statsInfo, ok := mapGuildUid2Stats[fmt.Sprintf("%d-%d", guildId, uid)]
				if !ok {
					continue
				}

				liveActiveCnt := 0
				for _, daily := range mapUid2DailyInfo[uid] {
					if daily.LiveValidMinutes >= mysql.LiveActiveAnchorMinutes {
						liveActiveCnt++
					}
				}

				platActiveDays := 0
				if memInfo, ok := mapUid2MemInfo[uid]; ok {
					platActiveDays = int(memInfo.LoginDays)
				}

				vaildChainCnt := 0
				if payInfo, ok := mapUid2PayInfo[uid]; ok {
					vaildChainCnt = int(payInfo.VaildChainCnt)
				}

				vioACnt, vioBCnt, vioCCnt := 0, 0, 0
				if vio, ok := mapTid2Violations[mapUid2Alias[uid]]; ok && len(vio) == 3 {
					vioACnt = int(vio[0])
					vioBCnt = int(vio[1])
					vioCCnt = int(vio[2])
				}

				isQuality, isActive := 0, 0
				if vioACnt <= mysql.MaxMonthAViolationsCnt && vioBCnt <= mysql.MaxMonthBViolationsCnt && vioCCnt <= mysql.MaxMonthCViolationsCnt && liveActiveCnt >= mysql.QualityMinMonthLiveActiveDayCnt &&
					platActiveDays >= mysql.QualityMinMonthPlatActiveDayCnt && vaildChainCnt >= mysql.QualityMinMonthPayChains &&
					(statsInfo.AnchorIncome+statsInfo.KnightIncome) >= mysql.QualityMinMonthIncome {
					isQuality = 1
				}

				if liveActiveCnt >= mysql.ActiveMinMonthLiveActiveDayCnt && platActiveDays >= mysql.ActiveMinMonthPlatActiveDayCnt &&
					statsInfo.ChannelFee >= mysql.ActiveMinMonthChannelFee {
					isActive = 1
				}

				statsInfo.IsQuality = uint32(isQuality)
				statsInfo.IsActive = uint32(isActive)

				err = m.mysqlStore.UpdateAnchorMonthlyQualityAndActiveInfo(ctx, nil, statsInfo)
				if err != nil {
					log.Errorf("UpdateGuildAnchorMonthInfo UpdateAnchorMonthlyQualityAndActiveInfo failed info:%v err:%v",
						statsInfo, err)
					return
				}
			}
		}

		if uint32(len(monthlyList)) < limit {
			break
		}
		offset = offset + limit
	}

	err := m.statsCache.SetDailyUpdateProcFlag(scene, nowTm)
	if err != nil {
		log.Errorf("UpdateGuildAnchorMonthInfo SetDailyUpdateProcFlag failed last:%v %d %d err:%v", beginMonthTm, offset, limit, err)
		return
	}

	log.Infof("UpdateGuildAnchorMonthInfo end now:%v last:%v cost:%v offset:%d", nowTm, beginMonthTm, time.Since(nowTm), offset)
}

func (m *ChannelLiveStatsManager) getAllRunningCh(index uint32) ([]uint32, map[uint32]int64, error) {
	var offset int64
	var limit int64 = 200
	uidList := make([]uint32, 0)
	mapUid2Ts := make(map[uint32]int64, 0)
	for {
		tmpMapUid2Ts, cnt, err := m.statsCache.GetRunningChannelList(offset, offset+limit-1, index)
		if err != nil {
			log.Errorf("getAllRunningCh GetRunningChannelList failed %d %d %d err:%v", offset, limit, index, err)
			return nil, nil, err
		}

		for k, v := range tmpMapUid2Ts {
			uidList = append(uidList, k)
			mapUid2Ts[k] = v
		}

		if cnt < uint32(limit) {
			break
		}

		offset += limit
	}

	log.Debugf("getAllRunningCh end cidList:%v mapCid2Ts:%v", uidList, mapUid2Ts)
	return uidList, mapUid2Ts, nil
}

func (m *ChannelLiveStatsManager) getAllGamingCh() (map[uint32]int64, error) {
	var offset int64
	var limit int64 = 200
	uidList := make([]uint32, 0)
	mapUid2Ts := make(map[uint32]int64, 0)
	for {
		tmpMapUid2Ts, cnt, err := m.statsCache.GetExtGameChannelList(offset, offset+limit-1)
		if err != nil {
			log.Errorf("getAllGamingCh GetExtGameChannelList failed %d %d err:%v", offset, limit, err)
			return nil, err
		}

		for k, v := range tmpMapUid2Ts {
			uidList = append(uidList, k)
			mapUid2Ts[k] = v
		}

		if cnt < uint32(limit) {
			break
		}

		offset += limit
	}

	log.Debugf("getAllGamingCh end mapUid2Ts", uidList, mapUid2Ts)
	return mapUid2Ts, nil
}

func (m *ChannelLiveStatsManager) TimerProcAnchorLiveTs() {
	for i := 0; i < RunningChPoorCnt; i++ {
		m.procAnchorLiveTs(uint32(i))
	}
}

func (m *ChannelLiveStatsManager) procAnchorLiveTs(index uint32) {
	ctx := context.Background()
	lockName := fmt.Sprintf("%s_%d", LiveTsProcLock, index)
	isGetLock, err := m.statsCache.GetLock(lockName, 5*time.Minute)
	if err != nil {
		log.ErrorWithCtx(ctx, "procAnchorLiveTs GetLock failed lockName:%s err:%v", lockName, err)
		return
	}

	if !isGetLock {
		log.InfoWithCtx(ctx, "procAnchorLiveTs no get lock, lockName:%s", lockName)
		return
	}

	defer func(lockKey string) {
		err := m.statsCache.ReleaseLock(lockKey)
		if err != nil {
			log.ErrorWithCtx(ctx, "procAnchorLiveTs failed to ReleaseLock, lockName:%s, err:%v", lockKey, err)
		}
		log.Infof("procAnchorLiveTs ReleaseLock success lockName:%s", lockKey)
	}(lockName)

	beginTm := time.Now()
	log.InfoWithCtx(ctx, "procAnchorLiveTs lockName:%s beginTm:%v beginTm:%d", lockName, beginTm, beginTm.Unix())

	uidList, mapUid2Ts, err := m.getAllRunningCh(index)
	if err != nil {
		log.ErrorWithCtx(ctx, "procAnchorLiveTs getAllRunningCh failed %d err:%v", index, err)
		return
	}

	mapUid2GameTs, err := m.getAllGamingCh()
	if err != nil {
		log.ErrorWithCtx(ctx, "procAnchorLiveTs getAllGamingCh failed %d err:%v", index, err)
		return
	}

	for uid, ts := range mapUid2Ts {
		if uid%RunningChPoorCnt != index {
			// 池子有调整，不处理
			err := m.statsCache.DelRunningChannel(uid, index)
			if err != nil {
				log.ErrorWithCtx(ctx, "procAnchorLiveTs failed to DelRunningChannel anchorUid:%d err(%v)", uid, err)
				continue
			}

			m.statsCache.AddRunningChannel(uid, uid%RunningChPoorCnt, ts)
			if err != nil {
				log.ErrorWithCtx(ctx, "procAnchorLiveTs failed to AddRunningChannel anchorUid:%d err(%v)", uid, err)
			}

			continue
		}

		nowTm := time.Now()
		endTs := nowTm.Unix()
		tm := time.Unix(int64(ts), 0)

		var incrMin int64
		if endTs > ts {
			if nowTm.Day() != tm.Day() {
				// 跨天了，就计算到下一天的0点先
				endTs = time.Date(tm.Year(), tm.Month(), tm.Day()+1, 0, 0, 0, 0, time.Local).Unix()
				incrMin = (endTs - ts) / 60

				// 直接更新时间，免得不够一分钟一直不更新时间
				err = m.statsCache.UpdateRunningChannelTs(index, map[uint32]int64{uid: endTs})
				if err != nil {
					log.ErrorWithCtx(ctx, "procAnchorLiveTs failed to UpdateRunningChannelTs anchorUid:%d endTs:%d err(%v)", uid, endTs, err)
					continue
				}
			} else {
				incrMin = (endTs - ts) / 60
				endTs = ts + incrMin*60
			}
		}

		// 一分钟以上才记录
		if incrMin >= 1 {
			info, _, err := m.mysqlStore.GetAnchorBaseInfo(ctx, uid)
			if err != nil {
				log.ErrorWithCtx(ctx, "procAnchorLiveTs failed to GetAnchorBaseInfo anchorUid:%d err(%v)", uid, err)
				continue
			}

			liveInfo, err := m.statsCache.GetAnchorLiveInfo(uid)
			if err != nil {
				log.ErrorWithCtx(ctx, "procAnchorLiveTs failed to GetAnchorLiveInfo anchorUid:%d err(%v)", uid, err)
				continue
			}

			if liveInfo.AnchorUid == 0 {
				log.InfoWithCtx(ctx, "procAnchorLiveTs no live info uid:%d", uid)
				err = m.statsCache.DelRunningChannel(uid, index)
				if err != nil {
					log.ErrorWithCtx(ctx, "procAnchorLiveTs failed to DelRunningChannel anchorUid:%d err(%v)", uid, err)
				}
				continue
			}

			newLiveMin, err := m.statsCache.IncrLiveTsById(liveInfo.ChannelLiveId, incrMin)
			if err != nil {
				log.ErrorWithCtx(ctx, "procAnchorLiveTs failed to IncrLiveTs anchorUid:%d endTs:%d incrTs:%d err(%v)", uid, endTs, incrMin, err)
				continue
			}

			err = m.statsCache.UpdateRunningChannelTs(index, map[uint32]int64{uid: endTs})
			if err != nil {
				log.ErrorWithCtx(ctx, "procAnchorLiveTs failed to UpdateRunningChannelTs anchorUid:%d endTs:%d err(%v)", uid, endTs, err)
				continue
			}

			// 需要判断是否跨天了
			liveBeginTm := time.Unix(liveInfo.LiveTs, 0)
			if tm.Day() != liveBeginTm.Day() && tm.Unix() > liveBeginTm.Unix() {
				beginTm := time.Date(tm.Year(), tm.Month(), tm.Day(), 0, 0, 0, 0, time.Local)

				lastMin := (beginTm.Unix() - liveBeginTm.Unix()) / 60
				if newLiveMin >= lastMin {
					newLiveMin = newLiveMin - lastMin
				}
			}

			if newLiveMin >= mysql.ValidDayMin {
				addMin := newLiveMin
				if newLiveMin-incrMin >= mysql.ValidDayMin {
					// 之前已到达有效，只计算本次增加的
					addMin = incrMin
				}

				newValidLiveMin, err := m.statsCache.IncrValidLiveTsByAnchor(uid, addMin, tm)
				if err != nil {
					log.ErrorWithCtx(ctx, "procAnchorLiveTs failed to IncrValidLiveTsByAnchor anchorUid:%d endTs:%d err(%v)", uid, endTs, err)
				}

				if newValidLiveMin >= mysql.LiveActiveAnchorMinutes && newValidLiveMin-addMin < mysql.LiveActiveAnchorMinutes {
					// 活跃天
					err = m.mysqlStore.IncrAnchorMonthlyRecordLiveActiveCnt(ctx, nil, &mysql.AnchorMonthlyLiveRecord{
						SignGuildId:   info.SignGuildId,
						Yearmonth:     mysql.GetYearMonth(tm),
						Uid:           uid,
						LiveActiveCnt: 1,
					})
					if err != nil {
						log.ErrorWithCtx(ctx, "procAnchorLiveTs failed to IncrAnchorMonthlyRecordLiveActiveCnt anchorUid:%d endTs:%d err(%v)", uid, endTs, err)
					}
				}

				var incrVirtualValidMin uint32
				if liveInfo.AnchorType == uint32(liveMgrPb.AnchorType_Anchor_Type_Virtual) {
					incrVirtualValidMin = uint32(addMin)
					// 虚拟直播
					newVirtualLiveMin, err := m.statsCache.IncrVirtualLiveTs(uid, addMin, tm)
					if err != nil {
						log.ErrorWithCtx(ctx, "procAnchorLiveTs failed to IncrVirtualLiveTs anchorUid:%d endTs:%d err(%v)", uid, endTs, err)
					}

					if newVirtualLiveMin >= mysql.VirtualActiveAnchorMinutes && newVirtualLiveMin-addMin < mysql.VirtualActiveAnchorMinutes {
						// 虚拟直播活跃天
						err = m.mysqlStore.IncrAnchorMonthlyRecordVirtualActiveCnt(ctx, nil, &mysql.AnchorMonthlyLiveRecord{
							SignGuildId:       info.SignGuildId,
							Yearmonth:         mysql.GetYearMonth(tm),
							Uid:               uid,
							VirtualActiveDays: 1,
						})
						if err != nil {
							log.ErrorWithCtx(ctx, "procAnchorLiveTs failed to IncrAnchorMonthlyRecordVirtualActiveCnt anchorUid:%d endTs:%d err(%v)", uid, endTs, err)
						}
					}
				}

				// 记录有效天，,增加有效时长
				err = m.RecordAnchorValidDay(ctx, uid, info.SignGuildId, uint32(addMin), incrVirtualValidMin, tm)
				if err != nil {
					log.ErrorWithCtx(ctx, "procAnchorLiveTs failed to RecordAnchorValidDay anchorUid:%d endTs:%d ts:%d err(%v)", uid, endTs, ts, err)
				}

			}

			if mapUid2GameTs[uid] > 0 {
				// 记录互动游戏时长
				m.IncrAnchorGameTs(ctx, uid, info.SignGuildId, incrMin, tm)
			}

			var virtualMin int64
			if liveInfo.AnchorType == uint32(liveMgrPb.AnchorType_Anchor_Type_Virtual) {
				virtualMin = incrMin
			}

			err = m.mysqlStore.IncrAnchorDailyLiveTime(ctx, nil, uid, info.SignGuildId, uint32(incrMin), uint32(virtualMin), tm)
			if err != nil {
				log.ErrorWithCtx(ctx, "procAnchorLiveTs failed to IncrAnchorDailyLiveTime anchorUid:%d guildId:%d beginTime:%v endTIme:%v",
					uid, info.SignGuildId, ts, endTs)
			}

			err = m.mysqlStore.IncrAnchorMonthlyLiveTime(ctx, nil, uid, info.SignGuildId, uint32(incrMin), uint32(virtualMin), mysql.GetYearMonth(tm))
			if err != nil {
				log.ErrorWithCtx(ctx, "procAnchorLiveTs failed to IncrAnchorMonthlyLiveTime anchorUid:%d guildId:%d beginTime:%v endTIme:%v",
					uid, info.SignGuildId, ts, endTs)
			}

			err = m.mysqlStore.UpdateAnchorMatchData(ctx, nil, uid, info.SignGuildId, uint32(liveInfo.ChannelLiveId), 0, uint32(incrMin),
				0, 0, info.AgentUid)
			if err != nil {
				log.ErrorWithCtx(ctx, "IncrAnchorIncome failed to UpdateAnchorMatchData uid:%d err(%v)", uid, err)
			}
		}

		log.DebugWithCtx(ctx, "procAnchorLiveTs %d %d", uid, ts)
	}

	log.InfoWithCtx(ctx, "procAnchorLiveTs end cost:%v beginTm:%d lockKey:%s cidLen:%d", time.Since(beginTm), beginTm.Unix(), lockName, len(uidList))
}

// 定时清理关播的主播
func (m *ChannelLiveStatsManager) TimerClearNoLiveAnchor() {
	nowTm := time.Now()
	ctx := context.Background()

	lockName := "ClearNoLiveAnchor"
	isGetLock, err := m.statsCache.GetLock(lockName, 60*time.Minute)
	if err != nil {
		log.ErrorWithCtx(ctx, "TimerClearNoLiveAnchor GetLock failed lockName:%s err:%v", lockName, err)
		return
	}

	if !isGetLock {
		log.InfoWithCtx(ctx, "TimerClearNoLiveAnchor no get lock, lockName:%s", lockName)
		return
	}

	log.Infof("TimerClearNoLiveAnchor begin now:%v", nowTm)

	var offset uint32 = 0
	var limit uint32 = 500
	anchorMatchList := make([]*mysql.AnchorMatchRecord, 0)

	for {
		infoList, err := m.mysqlStore.GetGuildAnchorList(ctx, 0, 0, 0, 1, 0,
			0, offset, limit)
		if err != nil {
			log.ErrorWithCtx(ctx, "TimerClearNoLiveAnchor GetGuildAnchorList failed offset:%d err:%v", offset, err)
			return
		}

		anchorMatchList = append(anchorMatchList, infoList...)
		if uint32(len(infoList)) < limit {
			break
		}
		offset = offset + limit
	}

	for _, anchorMatch := range anchorMatchList {
		baseInfo, _, err := m.mysqlStore.GetAnchorBaseInfo(ctx, anchorMatch.Uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "TimerClearNoLiveAnchor GetAnchorBaseInfo failed anchorMatch:%v err:%v", anchorMatch, err)
			continue
		}

		if baseInfo.LastLiveAt.Before(nowTm.AddDate(0, 0, -7)) && baseInfo.ChannelLiveId != 0 {
			// 超过7天了，应该不在开播了
			err = m.mysqlStore.UpdateAnchorLastLiveTime(ctx, nil, baseInfo.Uid, baseInfo.SignGuildId, baseInfo.LiveRoomId, 0, baseInfo.LastLiveAt)
			if err != nil {
				log.ErrorWithCtx(ctx, "TimerClearNoLiveAnchor UpdateAnchorLastLiveTime failed anchorMatch:%v baseInfo:%v err:%v", anchorMatch, baseInfo, err)
			}

			if baseInfo.ChannelLiveId == anchorMatch.ChannelLiveId {
				err = m.mysqlStore.UpdateAnchorMatchLiveFlag(ctx, nil, anchorMatch.Uid, anchorMatch.SignGuildId, anchorMatch.ChannelLiveId, 0, anchorMatch.AgentUid)
				if err != nil {
					log.ErrorWithCtx(ctx, "TimerClearNoLiveAnchor UpdateAnchorMatchLiveFlag failed anchorMatch:%v baseInfo:%v err:%v", anchorMatch, baseInfo, err)
				}
			}

			log.Infof("TimerClearNoLiveAnchor no live anchorMatch:%v baseInfo:%v", anchorMatch, baseInfo)
		}

		if baseInfo.ChannelLiveId != anchorMatch.ChannelLiveId {
			err = m.mysqlStore.UpdateAnchorMatchLiveFlag(ctx, nil, anchorMatch.Uid, anchorMatch.SignGuildId, anchorMatch.ChannelLiveId, 0, anchorMatch.AgentUid)
			if err != nil {
				log.ErrorWithCtx(ctx, "TimerClearNoLiveAnchor UpdateAnchorMatchLiveFlag failed anchorMatch:%v baseInfo:%v err:%v", anchorMatch, baseInfo, err)
			}
			log.Infof("TimerClearNoLiveAnchor no live anchorMatch:%v baseInfo:%v", anchorMatch, baseInfo)
		}
	}

	log.Infof("TimerClearNoLiveAnchor end now:%v cost:%v anchorMatchList:%d", nowTm, time.Since(nowTm), len(anchorMatchList))
}
