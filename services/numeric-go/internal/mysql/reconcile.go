package mysql

import (
	"context"
	"fmt"
	"time"

	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/numeric-go"
	"golang.52tt.com/services/numeric-go/internal/common"
	"gorm.io/gorm/clause"
)

type ReconcileStatus uint8

const (
	ReconcileStatusInit    ReconcileStatus = 0
	ReconcileStatusFailed  ReconcileStatus = 1
	ReconcileStatusSuccess ReconcileStatus = 2
)

type RichReconcile struct {
	ID         uint32          `gorm:"primaryKey;column:id" json:"id"`                       // id
	UID        uint32          `gorm:"column:uid" json:"uid"`                                // uid
	Type       pb.NumericT     `gorm:"column:type" json:"type"`                              // 变更类型
	AddValue   uint64          `gorm:"column:add_value" json:"add_value"`                    // 变更值
	RichSwitch bool            `gorm:"column:rich_switch" json:"rich_switch"`                // 财富值开关
	OrderID    string          `gorm:"column:order_id;unique" json:"order_id"`               // 订单号
	SourceType pb.SourceT      `gorm:"column:source_type" json:"source_type"`                // 来源
	Status     ReconcileStatus `gorm:"column:status" json:"status"`                          // 状态
	OrderTime  time.Time       `gorm:"column:order_time" json:"order_time"`                  // 订单时间
	CreateTime time.Time       `gorm:"column:create_time;autoCreateTime" json:"create_time"` // 创建时间
	UpdateTime time.Time       `gorm:"column:update_time;autoUpdateTime" json:"update_time"` // 更新时间
}

// getGoldReconcileTbl 获取娱乐房校验表名称
func getReconcileTbl(t time.Time) string {
	return fmt.Sprintf("rich_reconcile_%s", t.Format("200601"))
}

func (s *Store) CreateRichReconcile(ctx context.Context, gn *common.GeneralNumeric) error {
	record := &RichReconcile{
		UID:        gn.Sender,
		Type:       pb.NumericT_Rich,
		AddValue:   gn.AddRich,
		RichSwitch: !gn.IsRecordRich,
		OrderID:    gn.OrderId,
		SourceType: gn.SourceType,
		Status:     ReconcileStatusInit,
		OrderTime:  gn.SendTime,
	}
	if err := s.db.WithContext(ctx).Table(getReconcileTbl(gn.SendTime)).
		Clauses(clause.OnConflict{
			DoUpdates: clause.Assignments(map[string]interface{}{
				"status": ReconcileStatusInit,
			}),
		}).Create(&record).Error; err != nil {
		log.ErrorWithCtx(ctx, "CreateNumericReconcile Create err:%s", err)
		return err
	}
	return nil
}

func (s *Store) SetRichReconcileStatus(ctx context.Context, orderId string, t time.Time, status ReconcileStatus) error {
	if err := s.db.WithContext(ctx).Table(getReconcileTbl(t)).
		Where("order_id = ?", orderId).
		Limit(1).
		Updates(&RichReconcile{
			OrderID: orderId,
			Status:  status,
		}).Error; err != nil {
		log.ErrorWithCtx(ctx, "SetGoldReconcileStatus Updates err:%s", err)
		return err
	}
	return nil
}

func (s *Store) GetRichReconcile(ctx context.Context, orderId string, t time.Time) (*RichReconcile, error) {
	record := &RichReconcile{}
	if err := s.db.WithContext(ctx).Table(getReconcileTbl(t)).
		Where("order_id = ?", orderId).First(&record).Error; err != nil {
		if isNoTable(err) {
			createSQL := fmt.Sprintf(CreateReconcileTableSql, getReconcileTbl(t))
			if err = s.db.WithContext(ctx).Exec(createSQL).Error; err != nil {
				log.ErrorWithCtx(ctx, "GetRichReconcile Exec err:%s", err)
				return nil, err
			}
		}
		if isNotFound(err) {
			return nil, nil
		}
		log.ErrorWithCtx(ctx, "GetRichReconcile First err:%s", err)
		return nil, err
	}
	return record, nil
}

func (s *Store) GetRichReconcileCount(ctx context.Context, start, end time.Time, sourceType pb.SourceT) (int64, error) {
	var count int64
	if err := s.db.WithContext(ctx).
		Table(getReconcileTbl(start)).
		Select("sum(fee) as fee").
		Where("order_time >= ?", start.Unix()).
		Where("order_time < ?", end.Unix()).
		Where("source_type = ?", sourceType).
		Where("status = ?", ReconcileStatusSuccess).
		Count(&count).Error; err != nil {
		if isNotFound(err) {
			return 0, nil
		}
		log.ErrorWithCtx(ctx, "GetRichReconcileCount First err:%s", err)
		return 0, err
	}
	return count, nil
}

func (s *Store) GetRichReconcileList(ctx context.Context, start, end time.Time, sourceType pb.SourceT) ([]*RichReconcile, error) {
	records := make([]*RichReconcile, 0)
	if err := s.db.WithContext(ctx).
		Table(getReconcileTbl(start)).
		Where("order_time >= ?", start.Unix()).
		Where("order_time < ?", end.Unix()).
		Where("source_type = ?", sourceType).
		Where("status = ?", ReconcileStatusSuccess).
		Find(&records).Error; err != nil {
		if isNotFound(err) {
			return records, nil
		}
		log.ErrorWithCtx(ctx, "GetRichReconcileList Find err:%s", err)
		return nil, err
	}
	return records, nil
}

func (s *Store) GetChangeLogByOrderId(ctx context.Context, t time.Time, orderId string) (*PersonalRichCharmLog, error) {
	if orderId == "" {
		return nil, nil
	}
	record := &PersonalRichCharmLog{}
	if err := s.db.WithContext(ctx).Table(getPersonalRichCharmLogTbl(t)).
		Where("order_id = ?", orderId).First(&record).Error; err != nil {
		if isNotFound(err) {
			return nil, nil
		}
		log.ErrorWithCtx(ctx, "GetChangeLogByOrderId First err:%s", err)
		return nil, err
	}
	return record, nil
}

// CleanOrderData 清理对账表订单数据，每次删除 1000 条，直到删完
func (s *Store) CleanOrderData(ctx context.Context) {
	t := time.Now().AddDate(0, 0, -60)
	tbl := getReconcileTbl(t)
	for {
		ret := s.db.WithContext(ctx).Table(tbl).
			Where("order_time < ?", t).
			Limit(1000).
			Delete(&RichReconcile{})
		if err := ret.Error; err != nil {
			log.ErrorWithCtx(ctx, "CleanOrderData err: %v", err)
			break
		}
		// 检查删除的记录数
		if ret.RowsAffected == 0 {
			log.InfoWithCtx(ctx, "All outdated order data has been cleaned.")
			break
		}

		log.InfoWithCtx(ctx, "Deleted %d records from %s.", ret.RowsAffected, tbl)
	}
}
