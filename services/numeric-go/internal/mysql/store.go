package mysql

import (
	"context"
	"errors"
	"strings"
	"time"

	mysqlDriver "github.com/go-sql-driver/mysql"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/numeric-go"
	"golang.52tt.com/services/numeric-go/internal/common"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

const (
	OrderEqual = "order_id = ?"
)

func NewMysql(cfg *config.MysqlConfig) (*Store, error) {
	var err error
	var mysqlDb *gorm.DB
	if mysqlDb, err = gorm.Open(mysql.New(mysql.Config{
		DSN:                       cfg.ConnectionString(),
		DefaultStringSize:         256,   // default size for string fields
		DisableDatetimePrecision:  true,  // disable datetime precision, which not supported before MySQL 5.6
		DontSupportRenameIndex:    true,  // drop & create when rename index, rename index not supported before MySQL 5.7, MariaD<PERSON>
		DontSupportRenameColumn:   true,  // `change` when rename column, rename column not supported before MySQL 8, MariaDB
		SkipInitializeWithVersion: false, // auto configure based on currently MySQL version
	}), &gorm.Config{
		NowFunc: func() time.Time {
			return time.Now().Local().Truncate(time.Second)
		},
	}); err != nil {
		log.Errorf("NewMysql gorm.Open err: %v", err)
		return nil, err
	}
	db, err := mysqlDb.DB()
	if err != nil {
		log.Errorf("NewMysql mysqlDb.DB err: %v", err)
		return nil, err
	}

	log.InfoWithCtx(context.Background(), "NewMysql cfg maxOpenConns %d maxIdleConns %d", cfg.MaxOpenConns, cfg.MaxIdleConns)

	db.SetMaxOpenConns(cfg.MaxOpenConns) // 最大打开连接数 默认值为 0（不限制连接数）
	db.SetMaxIdleConns(cfg.MaxIdleConns) // 最大空闲连接数 默认值为 2
	if err = db.Ping(); err != nil {
		log.Errorf("NewMysql db.Ping err: %v", err)
		return nil, err
	}

	go func() {
		for {
			time.Sleep(30 * time.Second)
			if db != nil {
				log.InfoWithCtx(context.Background(), "NewMysql db.stats %+v", db.Stats())
			}
		}
	}()

	s := &Store{
		db: mysqlDb,
	}

	s.CreateLogTable(context.Background())

	return s, nil
}

type Store struct {
	db *gorm.DB
}

type PersonalRichSwitchLog struct {
	ID         uint32                  `gorm:"primaryKey;column:id" json:"id"`
	UID        uint32                  `gorm:"column:uid" json:"uid"`
	GuildID    uint32                  `gorm:"column:guild_id" json:"guild_id"`
	ChangeDesc string                  `gorm:"column:change_desc" json:"change_desc"`
	Type       uint8                   `gorm:"column:type" json:"type"` // 1 财富值开关
	Op         common.SwitchOp         `gorm:"column:op" json:"op"`     // 1 开启 2 关闭
	ChangeType pb.RichSwitchChangeType `gorm:"column:change_type" json:"change_type"`
	CreateTime time.Time               `gorm:"autoCreateTime;column:create_time" json:"create_time"`
}

// TableName get sql table name.获取数据库表名
func (m *PersonalRichSwitchLog) TableName() string {
	return "personal_rich_switch_log"
}
func (m *PersonalRichSwitchLog) SetOperation(enable bool) {
	if m == nil {
		return
	}
	var op common.SwitchOp
	if enable {
		op = common.SwitchOpEnable
	} else {
		op = common.SwitchOpDisable
	}
	m.Op = op
}

func (s *Store) Transaction(ctx context.Context, f func(tx *gorm.DB) error) error {
	tx := s.db.WithContext(ctx).Begin()
	err := f(tx)
	if err != nil {
		log.ErrorWithCtx(ctx, "Transaction fail err %v", err)
		_ = tx.Rollback()
		return err
	}
	return tx.Commit().Error
}

func isDuplicate(err error) bool {
	if mysqlErr, ok := err.(*mysqlDriver.MySQLError); ok {
		if mysqlErr.Number == 1062 ||
			mysqlErr.Number == 1586 {
			return true
		}
	}
	return false
}

func isNotFound(err error) bool {
	return errors.Is(err, gorm.ErrRecordNotFound)
}

func isNoTable(err error) bool {
	if mysqlErr, ok := err.(*mysqlDriver.MySQLError); ok {
		if mysqlErr.Number == 1146 {
			return true
		}
	}
	return false
}

// isDeadlock checks if the error is a MySQL deadlock (Error 1213)
func isDeadlock(err error) bool {
	if err == nil {
		return false
	}
	// unwrap GORM's error layers
	rawErr := errors.Unwrap(err)
	if rawErr == nil {
		rawErr = err
	}

	// MySQL 错误信息通用判断
	if strings.Contains(rawErr.Error(), "Deadlock found when trying to get lock") {
		return true
	}
	// 有的驱动也可能用 error code 判断（看使用的 MySQL 驱动）
	if strings.Contains(rawErr.Error(), "Error 1213") {
		return true
	}

	return false
}
