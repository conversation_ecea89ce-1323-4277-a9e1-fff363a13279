package mysql

import (
	"context"
	"time"

	pb "golang.52tt.com/protocol/services/numeric-go"
	"golang.52tt.com/services/numeric-go/internal/common"
	"gorm.io/gorm"
)

type IStore interface {
	AddPersonRichOrCharmChangeLog(ctx context.Context, t time.Time, numericChangeLogs []*PersonalRichCharmLog) error
	BatchGetPersonalNumeric(ctx context.Context, uidList []uint32) ([]*pb.PersonalNumeric, error)
	CheckCleanOrderRecords(ctx context.Context, uidList []uint32) ([]uint32, error)
	CleanUserRich(ctx context.Context, orderID uint32, records []*PersonalRichCleanRecord) error
	CreateCleanRichOrder(ctx context.Context, task *PersonalRichCleanOrder, records []*PersonalRichCleanRecord) (uint32, error)
	GetCleanRichOrdersByIDs(ctx context.Context, orderIDs []uint32) (map[uint32]*PersonalRichCleanOrder, error)
	GetCleanRichRecordsByOrderID(ctx context.Context, orderID uint32) ([]*PersonalRichCleanRecord, error)
	GetPersonalLockBitmap(ctx context.Context, uid uint32) (*PersonalLockBitmap, error)
	GetUserNumericLock(ctx context.Context, uid uint32) (*pb.UserNumericLock, bool, error)
	BatchGetUserNumericLock(ctx context.Context, uids []uint32) (map[uint32]*pb.UserNumericLock, error)
	SetUserNumericLock(ctx context.Context, uid uint32, lockType common.NumericLockT, lockStatus pb.LockStatus) error
	SetUserNumericLockBatch(ctx context.Context, uid uint32, lockTypes []common.NumericLockT, lockStatus pb.LockStatus) error
	CreateMysqlTable() error
	CreateSwitchOpLog(ctx context.Context, uid, guildId uint32, enable bool, changeType pb.RichSwitchChangeType) error
	CreateSwitchOpLogV2(ctx context.Context, record *PersonalRichSwitchLog) error
	GetLastSwitchOpLog(ctx context.Context, uid uint32) (*PersonalRichSwitchLog, error)
	GetLastSwitchOpLogByTime(ctx context.Context, uid uint32, t time.Time) (*PersonalRichSwitchLog, error)
	GetCleanRichRecordList(ctx context.Context, req *pb.GetCleanUserRichRecordsReq) ([]*PersonalRichCleanRecord, uint32, error)
	GetCleanRichOrder(ctx context.Context, orderID uint32) (*PersonalRichCleanOrder, error)
	GetPersonVip(ctx context.Context, uid uint32) (*PersonRichVip, error)
	GetPersonalMonthNumeric(ctx context.Context, uid uint32, t time.Time) (*pb.PersonalNumeric, error)
	GetPersonalNumeric(ctx context.Context, uid uint32) (*pb.PersonalNumeric, error)
	GetGetPersonalNumericWithError(ctx context.Context, uid uint32) (*pb.PersonalNumeric, error)
	// RecordGiftEventChange 记录T豆送礼事件财富值变更
	RecordGiftEventChange(ctx context.Context, n *common.GeneralNumeric) error
	// RecordGiftEventRichChangeV2 记录红钻送礼财富值变更
	RecordGiftEventRichChangeV2(ctx context.Context, gn *common.GeneralNumeric) error
	// RecordGiftEventCharmChangeV2 记录红钻送礼魅力值变更
	RecordGiftEventCharmChangeV2(ctx context.Context, gn *common.GeneralNumeric) error
	RecordGuildCharm(tx *gorm.DB, guildId, uid uint32, charm uint64) error
	RecordGuildGiftRich(tx *gorm.DB, guildId uint32, rich uint64) error
	RecordPersonCharm(tx *gorm.DB, uid uint32, charm uint64) error
	RecordPersonMonthCharm(ctx context.Context, t time.Time, uid uint32, charm uint64) error
	RecordPersonMonthRich(ctx context.Context, t time.Time, uid uint32, rich uint64) error
	RecordPersonRich(tx *gorm.DB, uid uint32, rich uint64) error
	RecordPersonRichCharm(ctx context.Context, uid uint32, rich, charm uint64) error
	RecordPersonVip(ctx context.Context, uid, level uint32) error
	RemoveCleanRichOrder(ctx context.Context, orderID uint32) error
	Transaction(ctx context.Context, f func(db *gorm.DB) error) error
	PreUseRichCard(ctx context.Context, card *RichCardOrder) error
	CertainUseRichCard(ctx context.Context, card *RichCardOrder, userNumeric *pb.PersonalNumeric) error
	CancelUseRichCard(ctx context.Context, orderID string) error
	GetRichCardOrder(ctx context.Context, orderID string) (*RichCardOrder, error)

	CreateRichReconcile(ctx context.Context, gn *common.GeneralNumeric) error
	SetRichReconcileStatus(ctx context.Context, orderId string, t time.Time, status ReconcileStatus) error
	GetRichReconcile(ctx context.Context, orderId string, t time.Time) (*RichReconcile, error)
	GetRichReconcileCount(ctx context.Context, start, end time.Time, sourceType pb.SourceT) (int64, error)
	GetRichReconcileList(ctx context.Context, start, end time.Time, sourceType pb.SourceT) ([]*RichReconcile, error)
	GetChangeLogByOrderId(ctx context.Context, t time.Time, orderId string) (*PersonalRichCharmLog, error)
	CleanOrderData(ctx context.Context)
}

type IPersonalRichCleanRecord interface {
	GetContractIdentities() []pb.ContractIdentity
	TableName() string
}

type IPersonRichVip interface {
	TableName() string
}

type IPersonalRichSwitchLog interface {
	TableName() string
}

type IPersonalRichCleanOrder interface {
	TableName() string
}
