package manager

import (
	"context"
	"errors"
	"reflect"
	"testing"
	"time"

	"bou.ke/monkey"
	"github.com/golang/mock/gomock"
	"github.com/panjf2000/ants"
	"golang.52tt.com/clients/account"
	anchorcontract_go "golang.52tt.com/clients/anchorcontract-go"
	"golang.52tt.com/clients/channel"
	channel_msg_express "golang.52tt.com/clients/channel-msg-express"
	channel_personalization "golang.52tt.com/clients/channel-personalization"
	"golang.52tt.com/clients/channelol"
	"golang.52tt.com/clients/guild"
	missionTL "golang.52tt.com/clients/missiontimeline"
	mockAnchorContractGo "golang.52tt.com/clients/mocks/anchorcontract-go"
	present_extra_conf "golang.52tt.com/clients/present-extra-conf"
	public_notice "golang.52tt.com/clients/public-notice"
	PushNotification "golang.52tt.com/clients/push-notification/v2"
	reconcile_present "golang.52tt.com/clients/reconcile-v2-svr/reconcile-present"
	"golang.52tt.com/clients/seqgen/v2"
	user_profile_api "golang.52tt.com/clients/user-profile-api"
	channel_msg "golang.52tt.com/pkg/channel-msg"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkaanchorcontract"
	pb "golang.52tt.com/protocol/services/numeric-go"
	"golang.52tt.com/services/numeric-go/internal/cache"
	"golang.52tt.com/services/numeric-go/internal/common"
	"golang.52tt.com/services/numeric-go/internal/config"
	mocks2 "golang.52tt.com/services/numeric-go/internal/mocks"
	"golang.52tt.com/services/numeric-go/internal/mysql"
)

func TestManager_GetUserNumericLockByUid(t *testing.T) {
	uid := uint32(123456)
	ctx := context.Background()

	// 恢复修改！
	monkey.UnpatchInstanceMethod(reflect.TypeOf(&Manager{}), "GetUserNumericLockByUid")

	// Test case 1: Cache hit
	t.Run("cache hit", func(t *testing.T) {
		expectedLock := &pb.UserNumericLock{
			BeanRichLock:     pb.LockStatus_LOCK_STATUS_DISABLE,
			DiamondRichLock:  pb.LockStatus_LOCK_STATUS_DISABLE,
			BeanCharmLock:    pb.LockStatus_LOCK_STATUS_DISABLE,
			DiamondCharmLock: pb.LockStatus_LOCK_STATUS_DISABLE,
		}

		monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericGoCache{}), "GetUserNumericLock", func(c *cache.NumericGoCache,
			ctx context.Context, uid uint32) (*pb.UserNumericLock, error) {
			return &pb.UserNumericLock{
				BeanRichLock:    pb.LockStatus_LOCK_STATUS_DISABLE,
				DiamondRichLock: pb.LockStatus_LOCK_STATUS_DISABLE,
			}, nil
		})

		m := &Manager{
			cacheClient: &cache.NumericGoCache{},
			mysqlStore:  &mysql.Store{},
		}

		got, err := m.GetUserNumericLockByUid(ctx, uid)
		if err != nil {
			t.Errorf("GetUserNumericLockByUid() error = %v", err)
			return
		}
		if !reflect.DeepEqual(got, decorateLock(expectedLock)) {
			t.Errorf("GetUserNumericLockByUid() = %v, want %v", got, decorateLock(expectedLock))
		}
	})

	// Test case 2: Cache miss, DB hit
	t.Run("cache miss db hit", func(t *testing.T) {
		expectedLock := &pb.UserNumericLock{
			BeanRichLock:     pb.LockStatus_LOCK_STATUS_ENABLE,
			DiamondRichLock:  pb.LockStatus_LOCK_STATUS_DISABLE,
			BeanCharmLock:    pb.LockStatus_LOCK_STATUS_DISABLE,
			DiamondCharmLock: pb.LockStatus_LOCK_STATUS_DISABLE,
		}

		monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericGoCache{}), "GetUserNumericLock", func(c *cache.NumericGoCache,
			ctx context.Context, uid uint32) (*pb.UserNumericLock, error) {
			return nil, nil
		})
		monkey.PatchInstanceMethod(reflect.TypeOf(&mysql.Store{}), "GetUserNumericLock", func(s *mysql.Store,
			ctx context.Context, uid uint32) (*pb.UserNumericLock, bool, error) {
			return &pb.UserNumericLock{
				BeanRichLock:    pb.LockStatus_LOCK_STATUS_ENABLE,
				DiamondRichLock: pb.LockStatus_LOCK_STATUS_DISABLE,
			}, true, nil
		})
		monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericGoCache{}), "SetUserNumericLock", func(c *cache.NumericGoCache,
			ctx context.Context, uid uint32, lock *pb.UserNumericLock) error {
			return nil
		})

		m := &Manager{
			cacheClient: &cache.NumericGoCache{},
			mysqlStore:  &mysql.Store{},
		}

		got, err := m.GetUserNumericLockByUid(ctx, uid)
		if err != nil {
			t.Errorf("GetUserNumericLockByUid() error = %v", err)
			return
		}
		if !reflect.DeepEqual(got, decorateLock(expectedLock)) {
			t.Errorf("GetUserNumericLockByUid() = %v, want %v", got, decorateLock(expectedLock))
		}
	})

	// Test case 3: Cache miss, DB miss, legacy lock
	t.Run("cache miss db miss legacy lock", func(t *testing.T) {
		emptyLock := &pb.UserNumericLock{}
		expectedLock := &pb.UserNumericLock{
			BeanRichLock:     pb.LockStatus_LOCK_STATUS_ENABLE,
			DiamondRichLock:  pb.LockStatus_LOCK_STATUS_ENABLE,
			BeanCharmLock:    pb.LockStatus_LOCK_STATUS_DISABLE,
			DiamondCharmLock: pb.LockStatus_LOCK_STATUS_DISABLE,
		}

		monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericGoCache{}), "GetUserNumericLock", func(c *cache.NumericGoCache,
			ctx context.Context, uid uint32) (*pb.UserNumericLock, error) {
			return emptyLock, nil
		})
		monkey.PatchInstanceMethod(reflect.TypeOf(&mysql.Store{}), "GetUserNumericLock", func(s *mysql.Store,
			ctx context.Context, uid uint32) (*pb.UserNumericLock, bool, error) {
			return emptyLock, false, nil
		})
		monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericGoCache{}), "GetRichSwitch", func(c *cache.NumericGoCache,
			ctx context.Context, uid uint32) (bool, error) {
			return true, nil
		})
		monkey.PatchInstanceMethod(reflect.TypeOf(&mysql.Store{}), "SetUserNumericLockBatch", func(s *mysql.Store,
			ctx context.Context, uid uint32, lockTypes []common.NumericLockT, lockStatus pb.LockStatus) error {
			return nil
		})
		monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericGoCache{}), "ConvToNumericLockEnable", func(c *cache.NumericGoCache,
			ctx context.Context, uid uint32, lockStatus pb.LockStatus) error {
			return nil
		})

		m := &Manager{
			cacheClient: &cache.NumericGoCache{},
			mysqlStore:  &mysql.Store{},
		}

		got, err := m.GetUserNumericLockByUid(ctx, uid)
		if err != nil {
			t.Errorf("GetUserNumericLockByUid() error = %v", err)
			return
		}
		if !reflect.DeepEqual(got, decorateLock(expectedLock)) {
			t.Errorf("GetUserNumericLockByUid() = %v, want %v", got, decorateLock(expectedLock))
		}
	})

	// Test case 4: Error case
	t.Run("error case", func(t *testing.T) {
		monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericGoCache{}), "GetUserNumericLock", func(c *cache.NumericGoCache,
			ctx context.Context, uid uint32) (*pb.UserNumericLock, error) {
			return nil, errors.New("cache error")
		})

		m := &Manager{
			cacheClient: &cache.NumericGoCache{},
			mysqlStore:  &mysql.Store{},
		}

		got, err := m.GetUserNumericLockByUid(ctx, uid)
		if err == nil {
			t.Error("GetUserNumericLockByUid() expected error")
			return
		}
		if got != nil {
			t.Errorf("GetUserNumericLockByUid() = %v, want nil", got)
		}
	})
}

func TestManager_GetUserRichSwitch(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	uid := uint32(123456)

	resp := &pb.GetUserRichSwitchResp{
		Enable: true,
	}

	monkey.PatchInstanceMethod(reflect.TypeOf(&Manager{}), "GetUserNumericLockByUid", func(m *Manager,
		ctx context.Context, uid uint32) (*pb.UserNumericLock, error) {
		return &pb.UserNumericLock{
			BeanRichLock: pb.LockStatus_LOCK_STATUS_ENABLE,
		}, nil
	})

	type fields struct {
		cacheClient          cache.INumericGoCache
		mysqlStore           mysql.IStore
		anchorContractClient anchorcontract_go.IClient
	}
	type args struct {
		ctx context.Context
		req *pb.GetUserRichSwitchReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetUserRichSwitchResp
		wantErr bool
	}{
		{name: "GetUserRichSwitch",
			fields: fields{
				mysqlStore:  &mysql.Store{},
				cacheClient: &cache.NumericGoCache{},
			},
			args: args{ctx: context.Background(), req: &pb.GetUserRichSwitchReq{
				Uid: uid,
			}},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cacheClient:          tt.fields.cacheClient,
				mysqlStore:           tt.fields.mysqlStore,
				anchorContractClient: tt.fields.anchorContractClient,
			}
			got, err := m.GetUserRichSwitch(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserRichSwitch() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetUserRichSwitch() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_HandleAnchorContractCancel(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	uid := uint32(123456)
	guildId := uint32(123)

	mockStore := mocks2.NewMockIStore(ctl)
	mockCache := mocks2.NewMockINumericGoCache(ctl)
	mockContract := mockAnchorContractGo.NewMockIClient(ctl)

	ctx := context.Background()
	gomock.InOrder(
		mockCache.EXPECT().GetRichSwitch(ctx, uid).Return(true, nil),
		mockCache.EXPECT().RichSwitchDisable(ctx, uid).Return(nil),
		mockStore.EXPECT().CreateSwitchOpLogV2(ctx, &mysql.PersonalRichSwitchLog{
			UID:        uid,
			GuildID:    guildId,
			ChangeDesc: "解约解锁",
			Op:         common.SwitchOpDisable,
			ChangeType: pb.RichSwitchChangeType_RICH_SWITCH_CHANGE_TYPE_CONTRACT_CANCEL,
		}).Return(nil),
	)

	type fields struct {
		cacheClient          cache.INumericGoCache
		mysqlStore           mysql.IStore
		anchorContractClient anchorcontract_go.IClient
	}
	type args struct {
		ctx context.Context
		e   *kafkaanchorcontract.AnchorContractEvent
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{name: "handleAnchorContractCancel",
			fields: fields{
				mysqlStore:           mockStore,
				cacheClient:          mockCache,
				anchorContractClient: mockContract,
			},
			args: args{ctx: ctx, e: &kafkaanchorcontract.AnchorContractEvent{
				EventType: uint32(kafkaanchorcontract.EVENT_TYPE_EVENT_CANCEL_CONTRACT),
				Uid:       uid,
				GuildId:   guildId,
			}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cacheClient:          tt.fields.cacheClient,
				mysqlStore:           tt.fields.mysqlStore,
				anchorContractClient: tt.fields.anchorContractClient,
			}
			if err := m.handleAnchorContractCancel(tt.args.ctx, tt.args.e); (err != nil) != tt.wantErr {
				t.Errorf("handleAnchorContractCancel() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
	time.Sleep(1 * time.Second)
}

func TestManager_SetUserRichSwitch(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	uid := uint32(123456)

	resp := &pb.SetUserRichSwitchResp{}

	ctx := context.Background()

	monkey.PatchInstanceMethod(reflect.TypeOf(&mysql.Store{}), "SetUserNumericLockBatch", func(m *mysql.Store,
		ctx context.Context, uid uint32, lockTypes []common.NumericLockT, lockStatus pb.LockStatus) error {
		return nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericGoCache{}), "SetUserNumericLock", func(m *cache.NumericGoCache,
		ctx context.Context, uid uint32, lock *pb.UserNumericLock) error {
		return nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericGoCache{}), "RichSwitchDisable", func(m *cache.NumericGoCache,
		ctx context.Context, uid uint32) error {
		return nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericGoCache{}), "SetUserNumericLockByType", func(c *cache.NumericGoCache,
		ctx context.Context, uid uint32, lockStatus pb.LockStatus, lockType ...common.NumericLockT) error {
		return nil
	})

	type fields struct {
		cacheClient          cache.INumericGoCache
		mysqlStore           mysql.IStore
		anchorContractClient anchorcontract_go.IClient
	}
	type args struct {
		ctx context.Context
		req *pb.SetUserRichSwitchReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.SetUserRichSwitchResp
		wantErr bool
	}{
		{name: "SetUserRichSwitch-enable",
			fields: fields{
				mysqlStore:  &mysql.Store{},
				cacheClient: &cache.NumericGoCache{},
			},
			args: args{ctx: ctx, req: &pb.SetUserRichSwitchReq{
				Uid:    uid,
				Enable: true,
			}},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cacheClient:          tt.fields.cacheClient,
				mysqlStore:           tt.fields.mysqlStore,
				anchorContractClient: tt.fields.anchorContractClient,
			}
			got, err := m.SetUserRichSwitch(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("SetUserRichSwitch() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SetUserRichSwitch() got = %v, want %v", got, tt.want)
			}
		})
	}
	time.Sleep(1 * time.Second)
}

func TestManager_HandleAnchorContractSign(t *testing.T) {

	ctx := context.Background()
	uid := uint32(123456)
	gid := uint32(123)
	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericGoCache{}), "GetRichSwitch", func(c *cache.NumericGoCache,
		ctx context.Context, uid uint32) (bool, error) {
		return false, nil
	})
	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericGoCache{}), "RichSwitchEnable", func(c *cache.NumericGoCache,
		ctx context.Context, uid uint32) error {
		return nil
	})
	monkey.PatchInstanceMethod(reflect.TypeOf(&mysql.Store{}), "GetLastSwitchOpLog", func(s *mysql.Store,
		ctx context.Context, uid uint32) (*mysql.PersonalRichSwitchLog, error) {
		return &mysql.PersonalRichSwitchLog{
			UID:        uid,
			Op:         common.SwitchOpDisable,
			ChangeType: pb.RichSwitchChangeType_RICH_SWITCH_CHANGE_TYPE_CONTRACT_CANCEL,
		}, nil
	})
	monkey.PatchInstanceMethod(reflect.TypeOf(&mysql.Store{}), "CreateSwitchOpLogV2", func(s *mysql.Store,
		ctx context.Context, record *mysql.PersonalRichSwitchLog) error {
		return nil
	})

	type fields struct {
		cacheClient          cache.INumericGoCache
		mysqlStore           mysql.IStore
		anchorContractClient anchorcontract_go.IClient
		guildClient          guild.IClient
		accountClient        account.IClient
		missionTLCli         missionTL.IClient
		pushCli              PushNotification.IClient
		seqGenCli            seqgen.IClient
		channelOlCli         channelol.IClient
		channelCli           channel.IClient
		channelMsgSender     channel_msg.ISender
		taskPool             *ants.Pool
		configCenter         config.IConfigCenter
		reconcilePresentCli  reconcile_present.IClient
		presentExtraCli      present_extra_conf.IClient
		personalizationCli   channel_personalization.IClient
		expressCli           channel_msg_express.IClient
		userProfileCli       user_profile_api.IClient
		publicNoticeCli      public_notice.IClient
		seqgenCli            seqgen.IClient
	}
	type args struct {
		ctx context.Context
		e   *kafkaanchorcontract.AnchorContractEvent
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "handleAnchorContractSign",
			fields: fields{
				cacheClient:          &cache.NumericGoCache{},
				mysqlStore:           &mysql.Store{},
				anchorContractClient: &anchorcontract_go.Client{},
				taskPool:             &ants.Pool{},
				configCenter:         &config.ConfigCenter{},
			},
			args: args{
				ctx: ctx,
				e: &kafkaanchorcontract.AnchorContractEvent{
					EventType: 0,
					Uid:       uid,
					GuildId:   gid,
					EventTime: 0,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cacheClient:          tt.fields.cacheClient,
				mysqlStore:           tt.fields.mysqlStore,
				anchorContractClient: tt.fields.anchorContractClient,
				guildClient:          tt.fields.guildClient,
				accountClient:        tt.fields.accountClient,
				missionTLCli:         tt.fields.missionTLCli,
				pushCli:              tt.fields.pushCli,
				seqGenCli:            tt.fields.seqGenCli,
				channelOlCli:         tt.fields.channelOlCli,
				channelCli:           tt.fields.channelCli,
				channelMsgSender:     tt.fields.channelMsgSender,
				taskPool:             tt.fields.taskPool,
				configCenter:         tt.fields.configCenter,
				reconcilePresentCli:  tt.fields.reconcilePresentCli,
				presentExtraCli:      tt.fields.presentExtraCli,
				personalizationCli:   tt.fields.personalizationCli,
				expressCli:           tt.fields.expressCli,
				userProfileCli:       tt.fields.userProfileCli,
				publicNoticeCli:      tt.fields.publicNoticeCli,
				seqgenCli:            tt.fields.seqgenCli,
			}
			if err := m.handleAnchorContractSign(tt.args.ctx, tt.args.e); (err != nil) != tt.wantErr {
				t.Errorf("handleAnchorContractSign() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestManager_BatchGetUserNumericLock(t *testing.T) {
	ctx := context.Background()
	uidList := []uint32{123456, 789012}

	// reset
	monkey.UnpatchInstanceMethod(reflect.TypeOf(&Manager{}), "BatUserNumericLockByUids")

	// Test case 1: Empty uid list
	t.Run("empty uid list", func(t *testing.T) {
		m := &Manager{
			cacheClient: &cache.NumericGoCache{},
			mysqlStore:  &mysql.Store{},
		}

		LockMap, err := m.BatUserNumericLockByUids(ctx, []uint32{}...)
		if err != nil {
			t.Errorf("BatUserNumericLockByUids() error = %v", err)
			return
		}
		if len(LockMap) != 0 {
			t.Errorf("BatUserNumericLockByUids() = %v, want empty map", LockMap)
		}
	})

	// Test case 2: All cache hits
	t.Run("all cache hits", func(t *testing.T) {
		expectedLockMap := map[uint32]*pb.UserNumericLock{
			123456: {
				BeanRichLock:    pb.LockStatus_LOCK_STATUS_ENABLE,
				DiamondRichLock: pb.LockStatus_LOCK_STATUS_DISABLE,
			},
			789012: {
				BeanRichLock:    pb.LockStatus_LOCK_STATUS_DISABLE,
				DiamondRichLock: pb.LockStatus_LOCK_STATUS_ENABLE,
			},
		}

		monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericGoCache{}), "BatchGetUserNumericLock", func(c *cache.NumericGoCache,
			ctx context.Context, uidList []uint32) (map[uint32]*pb.UserNumericLock, []uint32, error) {
			return expectedLockMap, []uint32{}, nil
		})

		m := &Manager{
			cacheClient: &cache.NumericGoCache{},
			mysqlStore:  &mysql.Store{},
		}

		LockMap, err := m.BatUserNumericLockByUids(ctx, uidList...)
		if err != nil {
			t.Errorf("BatUserNumericLockByUids() error = %v", err)
			return
		}
		for uid, lock := range LockMap {
			if !reflect.DeepEqual(lock, decorateLock(expectedLockMap[uid])) {
				t.Errorf("BatUserNumericLockByUids() for uid %d = %v, want %v", uid, lock, decorateLock(expectedLockMap[uid]))
			}
		}
	})

	// Test case 3: Some cache misses
	t.Run("some cache misses", func(t *testing.T) {
		cacheHitMap := map[uint32]*pb.UserNumericLock{
			123456: {
				BeanRichLock:    pb.LockStatus_LOCK_STATUS_ENABLE,
				DiamondRichLock: pb.LockStatus_LOCK_STATUS_DISABLE,
			},
		}
		dbHitMap := map[uint32]*pb.UserNumericLock{
			789012: {
				BeanRichLock:    pb.LockStatus_LOCK_STATUS_DISABLE,
				DiamondRichLock: pb.LockStatus_LOCK_STATUS_ENABLE,
			},
		}

		monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericGoCache{}), "BatchGetUserNumericLock", func(c *cache.NumericGoCache,
			ctx context.Context, uidList []uint32) (map[uint32]*pb.UserNumericLock, []uint32, error) {
			return cacheHitMap, []uint32{789012}, nil
		})
		monkey.PatchInstanceMethod(reflect.TypeOf(&mysql.Store{}), "BatchGetUserNumericLock", func(s *mysql.Store,
			ctx context.Context, uidList []uint32) (map[uint32]*pb.UserNumericLock, error) {
			return dbHitMap, nil
		})
		monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericGoCache{}), "SetUserNumericLock", func(c *cache.NumericGoCache,
			ctx context.Context, uid uint32, lock *pb.UserNumericLock) error {
			return nil
		})

		m := &Manager{
			cacheClient: &cache.NumericGoCache{},
			mysqlStore:  &mysql.Store{},
		}

		LockMap, err := m.BatUserNumericLockByUids(ctx, uidList...)
		if err != nil {
			t.Errorf("BatchGetUserNumericLock() error = %v", err)
			return
		}
		expectedLockMap := map[uint32]*pb.UserNumericLock{
			123456: cacheHitMap[123456],
			789012: dbHitMap[789012],
		}
		for uid, lock := range LockMap {
			if !reflect.DeepEqual(lock, decorateLock(expectedLockMap[uid])) {
				t.Errorf("BatUserNumericLockByUids() for uid %d = %v, want %v", uid, lock, decorateLock(expectedLockMap[uid]))
			}
		}
	})
}

func TestManager_GetUserNumericLock(t *testing.T) {
	uid := uint32(123456)
	ctx := context.Background()

	// Test case 1: Success case
	t.Run("success", func(t *testing.T) {
		expectedLock := &pb.UserNumericLock{
			BeanRichLock:    pb.LockStatus_LOCK_STATUS_ENABLE,
			DiamondRichLock: pb.LockStatus_LOCK_STATUS_DISABLE,
		}

		monkey.PatchInstanceMethod(reflect.TypeOf(&Manager{}), "GetUserNumericLockByUid", func(m *Manager,
			ctx context.Context, uid uint32) (*pb.UserNumericLock, error) {
			return expectedLock, nil
		})

		m := &Manager{
			cacheClient: &cache.NumericGoCache{},
			mysqlStore:  &mysql.Store{},
		}

		got, err := m.GetUserNumericLock(ctx, &pb.GetUserNumericLockReq{Uid: uid})
		if err != nil {
			t.Errorf("GetUserNumericLock() error = %v", err)
			return
		}
		if !reflect.DeepEqual(got.Locks, expectedLock) {
			t.Errorf("GetUserNumericLock() = %v, want %v", got.Locks, expectedLock)
		}
	})

	// Test case 2: Error case
	t.Run("error", func(t *testing.T) {
		monkey.PatchInstanceMethod(reflect.TypeOf(&Manager{}), "GetUserNumericLockByUid", func(m *Manager,
			ctx context.Context, uid uint32) (*pb.UserNumericLock, error) {
			return nil, errors.New("get lock error")
		})

		m := &Manager{
			cacheClient: &cache.NumericGoCache{},
			mysqlStore:  &mysql.Store{},
		}

		got, err := m.GetUserNumericLock(ctx, &pb.GetUserNumericLockReq{Uid: uid})
		if err == nil {
			t.Error("GetUserNumericLock() expected error")
			return
		}
		if got.Locks != nil {
			t.Errorf("GetUserNumericLock() = %v, want nil", got.Locks)
		}
	})
}

func TestManager_SetUserNumericLock(t *testing.T) {
	uid := uint32(123456)
	ctx := context.Background()

	// Test case 1: Set bean rich lock
	t.Run("set bean rich lock", func(t *testing.T) {
		curLock := &pb.UserNumericLock{
			BeanRichLock:    pb.LockStatus_LOCK_STATUS_DISABLE,
			DiamondRichLock: pb.LockStatus_LOCK_STATUS_DISABLE,
		}

		monkey.PatchInstanceMethod(reflect.TypeOf(&Manager{}), "GetUserNumericLockByUid", func(m *Manager,
			ctx context.Context, uid uint32) (*pb.UserNumericLock, error) {
			return curLock, nil
		})

		monkey.PatchInstanceMethod(reflect.TypeOf(&mysql.Store{}), "SetUserNumericLock", func(s *mysql.Store,
			ctx context.Context, uid uint32, lockType common.NumericLockT, lockStatus pb.LockStatus) error {
			return nil
		})

		monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericGoCache{}), "SetUserNumericLockByType", func(c *cache.NumericGoCache,
			ctx context.Context, uid uint32, lockStatus pb.LockStatus, lockType ...common.NumericLockT) error {
			return nil
		})

		m := &Manager{
			cacheClient: &cache.NumericGoCache{},
			mysqlStore:  &mysql.Store{},
		}

		got, err := m.SetUserNumericLock(ctx, &pb.SetUserNumericLockReq{
			Uid: uid,
			Locks: &pb.UserNumericLock{
				BeanRichLock: pb.LockStatus_LOCK_STATUS_ENABLE,
			},
		})
		if err != nil {
			t.Errorf("SetUserNumericLock() error = %v", err)
			return
		}
		if got == nil {
			t.Error("SetUserNumericLock() got nil response")
		}
	})

	// Test case 2: Multiple lock types error
	t.Run("multiple lock types error", func(t *testing.T) {
		curLock := &pb.UserNumericLock{
			BeanRichLock:    pb.LockStatus_LOCK_STATUS_DISABLE,
			DiamondRichLock: pb.LockStatus_LOCK_STATUS_DISABLE,
		}

		monkey.PatchInstanceMethod(reflect.TypeOf(&Manager{}), "GetUserNumericLockByUid", func(m *Manager,
			ctx context.Context, uid uint32) (*pb.UserNumericLock, error) {
			return curLock, nil
		})

		m := &Manager{
			cacheClient: &cache.NumericGoCache{},
			mysqlStore:  &mysql.Store{},
		}

		got, err := m.SetUserNumericLock(ctx, &pb.SetUserNumericLockReq{
			Uid: uid,
			Locks: &pb.UserNumericLock{
				BeanRichLock:    pb.LockStatus_LOCK_STATUS_ENABLE,
				DiamondRichLock: pb.LockStatus_LOCK_STATUS_ENABLE,
			},
		})
		if err == nil {
			t.Error("SetUserNumericLock() expected error")
			return
		}
		if got == nil {
			t.Error("SetUserNumericLock() got nil response")
		}
	})

	// Test case 3: Repeat operation error
	t.Run("repeat operation error", func(t *testing.T) {
		curLock := &pb.UserNumericLock{
			BeanRichLock:    pb.LockStatus_LOCK_STATUS_ENABLE,
			DiamondRichLock: pb.LockStatus_LOCK_STATUS_DISABLE,
		}

		monkey.PatchInstanceMethod(reflect.TypeOf(&Manager{}), "GetUserNumericLockByUid", func(m *Manager,
			ctx context.Context, uid uint32) (*pb.UserNumericLock, error) {
			return curLock, nil
		})

		m := &Manager{
			cacheClient: &cache.NumericGoCache{},
			mysqlStore:  &mysql.Store{},
		}

		got, err := m.SetUserNumericLock(ctx, &pb.SetUserNumericLockReq{
			Uid: uid,
			Locks: &pb.UserNumericLock{
				BeanRichLock: pb.LockStatus_LOCK_STATUS_ENABLE,
			},
		})
		if err == nil {
			t.Error("SetUserNumericLock() expected error")
			return
		}
		if got == nil {
			t.Error("SetUserNumericLock() got nil response")
		}
	})

	// Test case 4: Error case
	t.Run("error case", func(t *testing.T) {
		curLock := &pb.UserNumericLock{
			BeanRichLock:    pb.LockStatus_LOCK_STATUS_DISABLE,
			DiamondRichLock: pb.LockStatus_LOCK_STATUS_DISABLE,
		}

		monkey.PatchInstanceMethod(reflect.TypeOf(&Manager{}), "GetUserNumericLockByUid", func(m *Manager,
			ctx context.Context, uid uint32) (*pb.UserNumericLock, error) {
			return curLock, nil
		})

		monkey.PatchInstanceMethod(reflect.TypeOf(&mysql.Store{}), "SetUserNumericLock", func(s *mysql.Store,
			ctx context.Context, uid uint32, lockType common.NumericLockT, lockStatus pb.LockStatus) error {
			return errors.New("db error")
		})

		m := &Manager{
			cacheClient: &cache.NumericGoCache{},
			mysqlStore:  &mysql.Store{},
		}

		got, err := m.SetUserNumericLock(ctx, &pb.SetUserNumericLockReq{
			Uid: uid,
			Locks: &pb.UserNumericLock{
				BeanRichLock: pb.LockStatus_LOCK_STATUS_ENABLE,
			},
		})
		if err == nil {
			t.Error("SetUserNumericLock() expected error")
			return
		}
		if got == nil {
			t.Error("SetUserNumericLock() got nil response")
		}
	})
}
