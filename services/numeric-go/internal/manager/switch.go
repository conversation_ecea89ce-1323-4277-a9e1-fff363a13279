package manager

import (
	"context"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkaanchorcontract"
	pb "golang.52tt.com/protocol/services/numeric-go"
	"golang.52tt.com/services/numeric-go/internal/common"
	"golang.52tt.com/services/numeric-go/internal/mysql"
	"google.golang.org/grpc/codes"
)

// convertLegacyLock 将旧版财富值开关转换为新版
func (m *Manager) convertLegacyLock(ctx context.Context, uid uint32) (*pb.UserNumericLock, error) {
	// 检查旧版开关
	curLockStatus := pb.LockStatus_LOCK_STATUS_DISABLE
	enable, err := m.cacheClient.GetRichSwitch(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "convertLegacyLock GetRichSwitch err: %+v, uid:%d", err, uid)
		return nil, err
	}

	// 旧版未设置，直接返回
	if !enable {
		return &pb.UserNumericLock{}, nil
	}

	log.InfoWithCtx(ctx, "convertLegacyLock uid %d rich switch enable, start convert to new lock", uid)

	lock := &pb.UserNumericLock{}
	lock.BeanRichLock = pb.LockStatus_LOCK_STATUS_ENABLE
	lock.DiamondRichLock = pb.LockStatus_LOCK_STATUS_ENABLE
	curLockStatus = pb.LockStatus_LOCK_STATUS_ENABLE

	// 初始化新版财富值开关
	lockTypes := []common.NumericLockT{common.LockBeanRich, common.LockDiamondRich}
	if err = m.mysqlStore.SetUserNumericLockBatch(ctx, uid, lockTypes, curLockStatus); err != nil {
		log.ErrorWithCtx(ctx, "convertLegacyLock SetUserNumericLockBatch err: %+v, uid:%d", err, uid)
		return nil, err
	}

	// 删除旧版，初始化为新版
	if err := m.cacheClient.ConvToNumericLockEnable(ctx, uid, curLockStatus); err != nil {
		log.ErrorWithCtx(ctx, "convertLegacyLock RichSwitchDisable err: %+v, uid:%d", err, uid)
	}
	return lock, nil
}

func (m *Manager) GetUserNumericLockByUid(ctx context.Context, uid uint32) (*pb.UserNumericLock, error) {
	lock, err := m.cacheClient.GetUserNumericLock(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserNumericLockByUid GetUserNumericLock err: %+v, uid:%d", err, uid)
		return nil, err
	}

	if isLockNotSet(lock) { // 锁无缓存，查DB
		exactLock, hasRecord, err := m.mysqlStore.GetUserNumericLock(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserNumericLockByUid GetUserNumericLock err: %+v, uid:%d", err, uid)
			return nil, err
		}

		if hasRecord {
			// 设置过锁，重建缓存，结束
			exactLock = decorateLock(exactLock)
			go func() {
				ctx = grpc.NewContextWithInfo(ctx)
				if err := m.cacheClient.SetUserNumericLock(ctx, uid, exactLock); err != nil {
					log.ErrorWithCtx(ctx, "GetUserNumericLockByUid SetUserNumericLock err: %+v, uid:%d", err, uid)
				}
			}()
			return exactLock, nil
		}

		// 从未设置过新版锁，检查旧版开关
		legacyLock, err := m.convertLegacyLock(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserNumericLockByUid convertLegacyLock err: %+v, uid:%d", err, uid)
			return nil, err
		}
		lock = legacyLock
	}

	// 新版旧版均未设置，初始化新版开关缓存，避免穿透
	if isLockNotSet(lock) {
		lock.BeanRichLock = pb.LockStatus_LOCK_STATUS_DISABLE
		lock.DiamondRichLock = pb.LockStatus_LOCK_STATUS_DISABLE
		lock.BeanCharmLock = pb.LockStatus_LOCK_STATUS_DISABLE
		lock.DiamondCharmLock = pb.LockStatus_LOCK_STATUS_DISABLE
		go func() {
			ctx = grpc.NewContextWithInfo(ctx)
			if err := m.cacheClient.SetUserNumericLock(ctx, uid, lock); err != nil {
				log.ErrorWithCtx(ctx, "GetUserNumericLockByUid SetUserNumericLock err: %+v, uid:%d", err, uid)
			}
		}()
	}

	return decorateLock(lock), nil
}

func (m *Manager) BatUserNumericLockByUids(ctx context.Context, uid ...uint32) (map[uint32]*pb.UserNumericLock, error) {
	log.WarnWithCtx(ctx, "BatUserNumericLockByUids uid len :%+v", len(uid))
	if len(uid) == 0 {
		return make(map[uint32]*pb.UserNumericLock), nil
	}
	lockMap, missUidList, err := m.cacheClient.BatchGetUserNumericLock(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatUserNumericLockByUids BatchGetUserNumericLock err: %+v, uids:%v", err, uid)
		return nil, err
	}

	// 处理缓存未命中的用户
	if len(missUidList) > 0 {
		missLockMap, err := m.mysqlStore.BatchGetUserNumericLock(ctx, missUidList)
		if err != nil {
			log.ErrorWithCtx(ctx, "BatUserNumericLockByUids BatchGetUserNumericLock err: %+v, uids:%v", err, missUidList)
			return nil, err
		}

		// 处理每个未命中的用户
		for _, uid := range missUidList {
			lock, ok := missLockMap[uid]
			if ok {
				// 设置过锁，重建缓存
				lock = decorateLock(lock)
				go func(uid uint32, lock *pb.UserNumericLock) {
					ctx = grpc.NewContextWithInfo(ctx)
					if err := m.cacheClient.SetUserNumericLock(ctx, uid, lock); err != nil {
						log.ErrorWithCtx(ctx, "BatUserNumericLockByUids SetUserNumericLock err: %+v, uid:%d", err, uid)
					}
				}(uid, lock)
			} else {
				// 从未设置过新版锁，检查旧版
				legacyLock, err := m.convertLegacyLock(ctx, uid)
				if err != nil {
					log.ErrorWithCtx(ctx, "BatUserNumericLockByUids convertLegacyLock err: %+v, uid:%d", err, uid)
					continue
				}
				lock = legacyLock
			}
			lockMap[uid] = lock
		}
	}

	// 装饰所有锁
	for uid, lock := range lockMap {
		lockMap[uid] = decorateLock(lock)
		// 新版与旧版均未设置，初始化新版缓存，避免穿透
		if isLockNotSet(lock) {
			go func(uid uint32, lock *pb.UserNumericLock) {
				ctx = grpc.NewContextWithInfo(ctx)
				if err := m.cacheClient.SetUserNumericLock(ctx, uid, lock); err != nil {
					log.ErrorWithCtx(ctx, "BatUserNumericLockByUids SetUserNumericLock err: %+v, uid:%d", err, uid)
				}
			}(uid, lock)
		}
	}

	return lockMap, nil
}

// GetUserNumericLock 获取用户财富值魅力值锁状态
func (m *Manager) GetUserNumericLock(ctx context.Context, req *pb.GetUserNumericLockReq) (*pb.GetUserNumericLockResp, error) {
	resp := &pb.GetUserNumericLockResp{}
	uid := req.Uid

	lock, err := m.GetUserNumericLockByUid(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserNumericLock GetUserNumericLockByUid err: %+v, uid:%d", err, uid)
		return resp, err
	}

	return &pb.GetUserNumericLockResp{Locks: lock}, nil
}

// SetUserNumericLock 设置用户财富值魅力值锁状态
func (m *Manager) SetUserNumericLock(ctx context.Context, req *pb.SetUserNumericLockReq) (*pb.SetUserNumericLockResp, error) {
	resp := &pb.SetUserNumericLockResp{}
	uid := req.GetUid()
	reqLocks := req.GetLocks()
	var reqLockType common.NumericLockT
	var reqLockStatus pb.LockStatus
	var reqLockCount int
	var lockRepeat bool

	// 查询现在的锁状态
	// 查询旧锁，若已经开启了旧版财富值锁，先转为新的
	curLock, err := m.GetUserNumericLockByUid(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetUserNumericLock GetUserNumericLockByUid err: %+v, uid:%d", err, uid)
		return resp, err
	}

	// T豆财富值
	if reqLocks.GetBeanRichLock() != pb.LockStatus_LOCK_STATUS_UNSPECIFIED {
		reqLockType = common.LockBeanRich
		reqLockStatus = reqLocks.GetBeanRichLock()
		reqLockCount++
		// 检查是否重复操作：开启时检查是否已开启，关闭时检查是否已关闭
		lockRepeat = (reqLockStatus == pb.LockStatus_LOCK_STATUS_ENABLE && curLock.GetBeanRichLock() == pb.LockStatus_LOCK_STATUS_ENABLE) ||
			(reqLockStatus == pb.LockStatus_LOCK_STATUS_DISABLE && curLock.GetBeanRichLock() == pb.LockStatus_LOCK_STATUS_DISABLE)
	}
	// 红钻财富值
	if reqLocks.GetDiamondRichLock() != pb.LockStatus_LOCK_STATUS_UNSPECIFIED {
		reqLockType = common.LockDiamondRich
		reqLockStatus = reqLocks.GetDiamondRichLock()
		reqLockCount++
		lockRepeat = lockRepeat || (reqLockStatus == pb.LockStatus_LOCK_STATUS_ENABLE && curLock.GetDiamondRichLock() == pb.LockStatus_LOCK_STATUS_ENABLE) ||
			(reqLockStatus == pb.LockStatus_LOCK_STATUS_DISABLE && curLock.GetDiamondRichLock() == pb.LockStatus_LOCK_STATUS_DISABLE)
	}
	// T豆魅力值
	if reqLocks.GetBeanCharmLock() != pb.LockStatus_LOCK_STATUS_UNSPECIFIED {
		reqLockType = common.LockBeanCharm
		reqLockStatus = reqLocks.GetBeanCharmLock()
		reqLockCount++
		lockRepeat = lockRepeat || (reqLockStatus == pb.LockStatus_LOCK_STATUS_ENABLE && curLock.GetBeanCharmLock() == pb.LockStatus_LOCK_STATUS_ENABLE) ||
			(reqLockStatus == pb.LockStatus_LOCK_STATUS_DISABLE && curLock.GetBeanCharmLock() == pb.LockStatus_LOCK_STATUS_DISABLE)
	}
	// 红钻魅力值
	if reqLocks.GetDiamondCharmLock() != pb.LockStatus_LOCK_STATUS_UNSPECIFIED {
		reqLockType = common.LockDiamondCharm
		reqLockStatus = reqLocks.GetDiamondCharmLock()
		reqLockCount++
		lockRepeat = lockRepeat || (reqLockStatus == pb.LockStatus_LOCK_STATUS_ENABLE && curLock.GetDiamondCharmLock() == pb.LockStatus_LOCK_STATUS_ENABLE) ||
			(reqLockStatus == pb.LockStatus_LOCK_STATUS_DISABLE && curLock.GetDiamondCharmLock() == pb.LockStatus_LOCK_STATUS_DISABLE)
	}

	// 每次只允许操作一种锁
	if reqLockCount != 1 {
		log.ErrorWithCtx(ctx, "SetUserNumericLock only one lock type is allowed, but got %d", reqLockCount)
		return resp, protocol.NewExactServerError(codes.OK, status.ErrNumericsCommonErr, "每次只允许操作一种锁")
	}

	// 重复操作
	if lockRepeat {
		log.ErrorWithCtx(ctx, "SetUserNumericLock lock repeat, uid:%d, type:%d, status:%d", uid, reqLockType, reqLockStatus)
		return resp, protocol.NewExactServerError(codes.OK, status.ErrNumericsRepeatOp)
	}

	// 设置锁
	if err := m.mysqlStore.SetUserNumericLock(ctx, uid, reqLockType, reqLockStatus); err != nil {
		log.ErrorWithCtx(ctx, "SetUserNumericLock SetUserNumericLock err: %+v, uid:%d", err, uid)
		return resp, err
	}

	// 缓存
	go func() {
		ctx = grpc.NewContextWithInfo(ctx)
		if err := m.cacheClient.SetUserNumericLockByType(ctx, uid, reqLockStatus, reqLockType); err != nil {
			log.ErrorWithCtx(ctx, "SetUserNumericLock SetUserNumericLock err: %+v, uid:%d", err, uid)
			if err := m.cacheClient.DeleteUserNumericLock(ctx, uid); err != nil {
				log.ErrorWithCtx(ctx, "SetUserNumericLock DeleteUserNumericLock err: %+v, uid:%d", err, uid)
			}
		}
	}()

	return resp, nil
}

// BatchGetUserNumericLock 批量获取用户财富值魅力值锁状态
func (m *Manager) BatchGetUserNumericLock(ctx context.Context, req *pb.BatchGetUserNumericLockReq) (*pb.BatchGetUserNumericLockResp, error) {
	uidList := req.GetUidList()
	lockMap, err := m.BatUserNumericLockByUids(ctx, uidList...)
	if err != nil {
		return nil, err
	}
	return &pb.BatchGetUserNumericLockResp{LockMap: lockMap}, nil
}

// GetUserRichSwitch 获取签约用户财富值开关状态（旧版本客户端）
func (m *Manager) GetUserRichSwitch(ctx context.Context, req *pb.GetUserRichSwitchReq) (*pb.GetUserRichSwitchResp, error) {
	resp := &pb.GetUserRichSwitchResp{}

	locks, err := m.GetUserNumericLockByUid(ctx, req.Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserRichSwitch GetUserNumericLock err: %+v, uid:%d", err, req.Uid)
		return resp, err
	}
	var enable bool
	// 新版任意财富值开关开启，则视为旧版开启
	if locks.GetBeanRichLock() == pb.LockStatus_LOCK_STATUS_ENABLE ||
		locks.GetDiamondRichLock() == pb.LockStatus_LOCK_STATUS_ENABLE {
		enable = true
	}
	return &pb.GetUserRichSwitchResp{Enable: enable}, nil
}

// SetUserRichSwitch 设置签约用户财富值开关状态（旧版本客户端）
func (m *Manager) SetUserRichSwitch(ctx context.Context, req *pb.SetUserRichSwitchReq) (*pb.SetUserRichSwitchResp, error) {
	resp := &pb.SetUserRichSwitchResp{}
	uid := req.GetUid()
	enable := req.GetEnable()

	if uid == 0 {
		log.ErrorWithCtx(ctx, "SetUserRichSwitch uid is 0")
		return resp, protocol.NewExactServerError(codes.OK, status.ErrParam)
	}

	log.InfoWithCtx(ctx, "SetUserRichSwitch start uid %d, enable: %v", uid, enable)

	var lockStatus pb.LockStatus
	// 操作旧版开关，相当于同时操作新版的T豆财富值与红钻财富值开关
	if enable {
		lockStatus = pb.LockStatus_LOCK_STATUS_ENABLE
	} else {
		lockStatus = pb.LockStatus_LOCK_STATUS_DISABLE
	}

	// 设置新锁
	lockTypes := []common.NumericLockT{common.LockBeanRich, common.LockDiamondRich}
	if err := m.mysqlStore.SetUserNumericLockBatch(ctx, uid, lockTypes, lockStatus); err != nil {
		log.ErrorWithCtx(ctx, "SetUserRichSwitch SetUserNumericLockBatch err: %+v, uid:%d", err, uid)
		return resp, err
	}

	go func() {
		ctx = grpc.NewContextWithInfo(ctx)
		// 新锁缓存
		if err := m.cacheClient.SetUserNumericLockByType(ctx, uid, lockStatus, common.LockBeanRich, common.LockDiamondRich); err != nil {
			log.ErrorWithCtx(ctx, "SetUserRichSwitch SetUserNumericLock err: %+v, uid:%d", err, uid)
			if err := m.cacheClient.DeleteUserNumericLock(ctx, uid); err != nil {
				log.ErrorWithCtx(ctx, "SetUserRichSwitch DeleteUserNumericLock err: %+v, uid:%d", err, uid)
			}
		}
		// 删除旧锁
		if err := m.cacheClient.RichSwitchDisable(ctx, uid); err != nil {
			log.ErrorWithCtx(ctx, "SetUserRichSwitch RichSwitchDisable err: %+v, uid:%d", err, uid)
		}
	}()

	return resp, nil
}

// HandleAnchorContractChange 处理签约事件 废弃
func (m *Manager) HandleAnchorContractChange(ctx context.Context, e *kafkaanchorcontract.AnchorContractEvent) error {
	switch e.EventType {
	case uint32(kafkaanchorcontract.EVENT_TYPE_EVENT_CANCEL_CONTRACT):
		err := m.handleAnchorContractCancel(ctx, e)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleAnchorContractChange handleAnchorContractCancel err: %+v", err)
			return err
		}
	case uint32(kafkaanchorcontract.EVENT_TYPE_EVENT_SIGN_CONTRACT):
		err := m.handleAnchorContractSign(ctx, e)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleAnchorContractChange handleAnchorContractSign err: %+v", err)
			return err
		}
	default:
	}
	return nil
}

// handleAnchorContractCancel 签约用户解约 废弃
func (m *Manager) handleAnchorContractCancel(ctx context.Context, e *kafkaanchorcontract.AnchorContractEvent) error {
	uid := e.GetUid()
	guildId := e.GetGuildId()
	changeType := pb.RichSwitchChangeType_RICH_SWITCH_CHANGE_TYPE_CONTRACT_CANCEL

	enable, err := m.cacheClient.GetRichSwitch(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleAnchorContractCancel GetRichSwitch err: %+v, uid:%d", err, uid)
	}

	if enable {
		if err = m.cacheClient.RichSwitchDisable(ctx, uid); err != nil {
			log.ErrorWithCtx(ctx, "handleAnchorContractCancel RichSwitchEnable err: %+v, uid:%d", err, uid)
			return err
		}
		log.InfoWithCtx(ctx, "handleAnchorContractCancel uid %d rich switch disable, start rich incr.", uid)

		if err = m.mysqlStore.CreateSwitchOpLogV2(ctx, &mysql.PersonalRichSwitchLog{
			UID:        uid,
			GuildID:    guildId,
			ChangeType: changeType,
			ChangeDesc: "解约解锁",
			Op:         common.SwitchOpDisable,
		}); err != nil {
			log.ErrorWithCtx(ctx, "handleAnchorContractCancel CreateSwitchOpLogV2 err:%v, uid:%d", err, uid)
		}
	}

	return nil
}

// handleAnchorContractSign 用户签约，检查解约时是否打开了财富值开关 废弃
func (m *Manager) handleAnchorContractSign(ctx context.Context, e *kafkaanchorcontract.AnchorContractEvent) error {
	uid := e.GetUid()
	guildId := e.GetGuildId()
	changeType := pb.RichSwitchChangeType_RICH_SWITCH_CHANGE_TYPE_CONTRACT_RENEW

	enable, err := m.cacheClient.GetRichSwitch(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleAnchorContractSign GetRichSwitch err: %+v, uid:%d", err, uid)
	}
	if enable {
		// 已打开开关，不处理
		log.WarnWithCtx(ctx, "handleAnchorContractSign uid %d rich switch already enable, no need to handle.", uid)
		return nil
	}

	// 获取上一次开关操作记录
	record, err := m.mysqlStore.GetLastSwitchOpLog(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleAnchorContractSign GetLastSwitchOpLog err:%v, uid:%d", err, uid)
		return err
	}
	if record == nil || record.UID == 0 {
		// 无记录，不处理
		log.WarnWithCtx(ctx, "handleAnchorContractSign uid %d no switch op log, no need to handle.", uid)
		return nil
	}

	// 上一次是因解约导致关闭
	if record.ChangeType == pb.RichSwitchChangeType_RICH_SWITCH_CHANGE_TYPE_CONTRACT_CANCEL &&
		record.Op == common.SwitchOpDisable {
		// 恢复开关
		if err = m.cacheClient.RichSwitchEnable(ctx, uid); err != nil {
			log.ErrorWithCtx(ctx, "handleAnchorContractSign RichSwitchEnable err: %+v, uid:%d", err, uid)
			return err
		}
		log.InfoWithCtx(ctx, "handleAnchorContractSign uid %d rich switch enable, start rich incr.", uid)

		if err = m.mysqlStore.CreateSwitchOpLogV2(ctx, &mysql.PersonalRichSwitchLog{
			UID:        uid,
			GuildID:    guildId,
			ChangeType: changeType,
			ChangeDesc: "重新签约",
			Op:         common.SwitchOpEnable,
		}); err != nil {
			log.ErrorWithCtx(ctx, "handleAnchorContractSign CreateSwitchOpLogV2 err:%v, uid:%d", err, uid)
		}
		return nil
	}

	return nil
}

// GetUserLastRichSwitchStatus 获取用户最后一次财富值开关操作
func (m *Manager) GetUserLastRichSwitchStatus(ctx context.Context, uid uint32, t time.Time) (common.SwitchOp, error) {
	switchLog, err := m.mysqlStore.GetLastSwitchOpLogByTime(ctx, uid, t)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserLastRichSwitchStatus GetRichSwitch err: %+v, uid:%d", err, uid)
		return common.SwitchOpDisable, err
	}
	if switchLog != nil {
		return switchLog.Op, nil
	}
	return common.SwitchOpDisable, nil

}

// 锁未设置
func isLockNotSet(lock *pb.UserNumericLock) bool {
	return lock == nil ||
		lock.BeanRichLock == pb.LockStatus_LOCK_STATUS_UNSPECIFIED &&
			lock.DiamondRichLock == pb.LockStatus_LOCK_STATUS_UNSPECIFIED &&
			lock.BeanCharmLock == pb.LockStatus_LOCK_STATUS_UNSPECIFIED &&
			lock.DiamondCharmLock == pb.LockStatus_LOCK_STATUS_UNSPECIFIED
}

// 一个locks装饰器，把未设置的锁转为未开启
func decorateLock(lock *pb.UserNumericLock) *pb.UserNumericLock {
	if lock == nil {
		lock = &pb.UserNumericLock{}
	}
	if lock.BeanRichLock == pb.LockStatus_LOCK_STATUS_UNSPECIFIED {
		lock.BeanRichLock = pb.LockStatus_LOCK_STATUS_DISABLE
	}
	if lock.DiamondRichLock == pb.LockStatus_LOCK_STATUS_UNSPECIFIED {
		lock.DiamondRichLock = pb.LockStatus_LOCK_STATUS_DISABLE
	}
	if lock.BeanCharmLock == pb.LockStatus_LOCK_STATUS_UNSPECIFIED {
		lock.BeanCharmLock = pb.LockStatus_LOCK_STATUS_DISABLE
	}
	if lock.DiamondCharmLock == pb.LockStatus_LOCK_STATUS_UNSPECIFIED {
		lock.DiamondCharmLock = pb.LockStatus_LOCK_STATUS_DISABLE
	}
	return lock
}
