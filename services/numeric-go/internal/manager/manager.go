package manager

import (
	"context"
	"fmt"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/protocol"
	vipprivilegesvr_go "golang.52tt.com/clients/vipprivilegesvr-go"
	pushPb "golang.52tt.com/protocol/app/push"
	push_notification "golang.52tt.com/protocol/services/push-notification/v2"
	"golang.52tt.com/services/numeric-go/internal/cache"
	"golang.52tt.com/services/numeric-go/internal/config"
	numericConfig "golang.52tt.com/services/numeric-go/internal/config/ttconfig/numeric"
	"golang.52tt.com/services/numeric-go/internal/event"
	"golang.52tt.com/services/numeric-go/internal/mysql"

	redisV6 "github.com/go-redis/redis"
	"github.com/panjf2000/ants"
	"gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
	"golang.52tt.com/clients/account"
	anchorcontractgo "golang.52tt.com/clients/anchorcontract-go"
	backpack_func_card "golang.52tt.com/clients/backpack-func-card"
	"golang.52tt.com/clients/channel"
	channelmsgexpress "golang.52tt.com/clients/channel-msg-express"
	channelOpenGameController "golang.52tt.com/clients/channel-open-game-controller"
	personalization "golang.52tt.com/clients/channel-personalization"
	"golang.52tt.com/clients/channelol"
	"golang.52tt.com/clients/guild"
	missionTL "golang.52tt.com/clients/missiontimeline"
	presentextraconf "golang.52tt.com/clients/present-extra-conf"
	publicnotice "golang.52tt.com/clients/public-notice"
	pushclient "golang.52tt.com/clients/push-notification/v2"
	reconcilePresent "golang.52tt.com/clients/reconcile-v2-svr/reconcile-present"
	"golang.52tt.com/clients/seqgen/v2"
	userprofileapi "golang.52tt.com/clients/user-profile-api"
	channelMsg "golang.52tt.com/pkg/channel-msg"
	"golang.52tt.com/pkg/datacenter"
	"golang.52tt.com/pkg/log"
	backpack_base "golang.52tt.com/protocol/services/backpack-base"
	pb "golang.52tt.com/protocol/services/numeric-go"
)

type Manager struct {
	cacheClient               cache.INumericGoCache
	mysqlStore                mysql.IStore
	timer                     *timer.Timer
	anchorContractClient      anchorcontractgo.IClient
	guildClient               guild.IClient
	accountClient             account.IClient
	missionTLCli              missionTL.IClient
	pushCli                   pushclient.IClient
	seqGenCli                 seqgen.IClient
	channelOlCli              channelol.IClient
	channelCli                channel.IClient
	channelMsgSender          channelMsg.ISender
	channelOpenGameController channelOpenGameController.IClient
	taskPool                  *ants.Pool
	configCenter              config.IConfigCenter
	reconcilePresentCli       reconcilePresent.IClient
	presentExtraCli           presentextraconf.IClient
	personalizationCli        personalization.IClient
	expressCli                channelmsgexpress.IClient
	userProfileCli            userprofileapi.IClient
	publicNoticeCli           publicnotice.IClient
	seqgenCli                 seqgen.IClient
	backpackFuncCardCli       backpack_func_card.IClient
	vipPrivilegesGoCli        vipprivilegesvr_go.IClient

	anchorKafkaSub  *event.AnchorContractEventLinkSub
	presentKafkaSub *event.PresentEventLinkSub
	esportKafkaSub  *event.ESportEventLinkSub
	knightKafkaSub  *event.KnightEventLinkSub
	tBeanKafkaSub   *event.TBeanEventLinkSub
}

func NewManager(sc *config.ServiceConfigT) (*Manager, error) {
	ctx := context.Background()

	cacheClient := cache.NewNumericGoCache(sc)

	mysqlStore, err := mysql.NewMysql(sc.GetMysqlConfig())
	if err != nil {
		log.Errorf("init mysql store err: %+v", err)
		return nil, err
	}

	anchorContractClient, err := anchorcontractgo.NewClient()
	if err != nil {
		log.Errorf("init anchorcontract_go client err: %+v", err)
		return nil, err
	}
	accountClient, err := account.NewClient()
	if err != nil {
		log.Errorf("init account client err: %+v", err)
		return nil, err
	}
	seqGenCli, err := seqgen.NewClient()
	if err != nil {
		log.Errorf("init seqgen client err: %+v", err)
		return nil, err
	}
	pushCli, err := pushclient.NewClient()
	if err != nil {
		log.Errorf("init pushclient client err: %+v", err)
		return nil, err
	}
	channelMsgSender := channelMsg.NewISender()
	channelOlCli := channelol.NewClient()
	channelCli := channel.NewClient()
	channelOpenGameControllerCli, _ := channelOpenGameController.NewClient()
	guildClient := guild.NewClient()
	missionTLCli := missionTL.NewClient()
	reconcilePresentCli, _ := reconcilePresent.NewClient()
	presentExtraCli, _ := presentextraconf.NewClient()
	personalizationCli, _ := personalization.NewClient()
	expressCli, _ := channelmsgexpress.NewClient()
	userProfileCli, _ := userprofileapi.NewClient()
	publicNoticeCli, _ := publicnotice.NewClient()
	seqgenCli, _ := seqgen.NewClient()
	backpackFuncCardCli, _ := backpack_func_card.NewClient()
	vipPrivilegesGoCli, _ := vipprivilegesvr_go.NewClient()

	antPool, err := ants.NewPool(3, ants.WithPreAlloc(true))
	if err != nil {
		return nil, err
	}
	log.Debugf("Initialized antPool max: %d", 3)

	configCenter, err := config.NewConfigCenter()
	if err != nil {
		return nil, err
	}

	if err = numericConfig.InitNumericConfig(); err != nil {
		log.ErrorWithCtx(ctx, "InitNumericConfig err: %s", err)
		return nil, err
	}

	m := &Manager{
		cacheClient:               cacheClient,
		mysqlStore:                mysqlStore,
		anchorContractClient:      anchorContractClient,
		guildClient:               guildClient,
		accountClient:             accountClient,
		seqGenCli:                 seqGenCli,
		pushCli:                   pushCli,
		missionTLCli:              missionTLCli,
		channelCli:                channelCli,
		channelOlCli:              channelOlCli,
		channelMsgSender:          channelMsgSender,
		channelOpenGameController: channelOpenGameControllerCli,
		taskPool:                  antPool,
		configCenter:              configCenter,
		reconcilePresentCli:       reconcilePresentCli,
		presentExtraCli:           presentExtraCli,
		personalizationCli:        personalizationCli,
		expressCli:                expressCli,
		userProfileCli:            userProfileCli,
		publicNoticeCli:           publicNoticeCli,
		seqgenCli:                 seqgenCli,
		backpackFuncCardCli:       backpackFuncCardCli,
		vipPrivilegesGoCli:        vipPrivilegesGoCli,
	}

	if m.timer, err = m.NewTimer(ctx, cacheClient.GetRedisClient()); err != nil {
		log.Errorf("Failed to NewTimer err %s", err.Error())
		return nil, err
	}

	m.presentKafkaSub, err = event.NewPresentEventLinkSub(ctx, sc.GetPresentKafkaConfig(), m.PresentEventHandle)
	if err != nil {
		log.Errorf("Failed to NewPresentEventLinkSub err %s", err.Error())
		return nil, err
	}

	m.knightKafkaSub, err = event.NewKnightEventLinkSub(ctx, sc.GetKnightKafkaConfig(), m.KnightEventHandle)
	if err != nil {
		log.Errorf("Failed to NewKnightEventLinkSub err %s", err.Error())
		return nil, err
	}

	m.tBeanKafkaSub, err = event.NewTBeanEventLinkSub(ctx, sc.GetTBeanKafkaConfig(), m.TBeanEventHandle)
	if err != nil {
		log.Errorf("Failed to NewTBeanEventLinkSub err %s", err.Error())
		return nil, err
	}

	// 2024.11.08 需求 电竞业务不增加贵族值/财富值/魅力值
	//m.esportKafkaSub, err = event.NewESportEventLinkSub(ctx, sc.GetESportKafkaConfig(), m.ESportEventHandle)
	//if err != nil {
	//	log.Errorf("Failed to NewESportEventLinkSub err %s", err.Error())
	//	return nil, err
	//}

	// 2025.05 需求 财富值开关对所有人开放
	//m.anchorKafkaSub, err = event.NewAnchorContractEventLinkSub(ctx, sc.GetAnchorKafkaConfig(), m.HandleAnchorContractChange)
	//if err != nil {
	//	log.Errorf("Failed to NewAnchorContractEventLinkSub err %s", err.Error())
	//	return nil, err
	//}

	return m, nil
}

func (m *Manager) NewTimer(ctx context.Context, redisClient *redisV6.Client) (*timer.Timer, error) {
	timerD, err := timer.NewTimerD(ctx, "numeric-go", timer.WithV6RedisCmdable(redisClient))
	if err != nil {
		log.Errorf("NewTimer NewTimerD err:%v", err)
		return nil, err
	}

	// 找产品与客户端确认后，这个逻辑貌似废弃。因为当日的榜单不会外显，更不会提示变更
	//if err = timerD.AddTask("@every 60s", "CheckRankChangeTimer", timer.BuildFromLambda(func(ctx context.Context) {
	//	m.CheckRankChangeTimer(ctx)
	//})); err != nil {
	//	log.Errorf("NewTimer AddTask CheckRankChangeTimer err:%v", err)
	//	return nil, err
	//}

	//timerD.Start()

	return timerD, nil
}

func TransRichOrCharmLevel(tp pb.NumericT, value uint64) *pb.Level {
	tmpServerLevel := level(value)
	serverLevel := tmpServerLevel

	mainLevel, subLevel1, subLevel2 := uint32(0), uint32(0), uint32(0)
	for {
		if tmpServerLevel <= 0 {
			break
		}
		mainLevel++
		subLevel2 = subLevel1
		subLevel1 = uint32(tmpServerLevel % 10)
		tmpServerLevel = tmpServerLevel / 10
	}

	return &pb.Level{
		MainLevel:   mainLevel,
		SubLevel:    subLevel1,
		SubLevel2:   subLevel2,
		LevelText:   fmt.Sprintf("%d-%d-%d", mainLevel, subLevel1, subLevel2),
		LevelName:   config.GetNumericLevelName(tp, mainLevel),
		ServerLevel: serverLevel,
	}
}

func TransVipLevel(richVal uint64) *pb.Level {
	if richVal < config.VipThreshold {
		return &pb.Level{}
	}
	tmpVal := richVal / config.VipThreshold
	var mainLevel, subLevel uint64
	if tmpVal >= 100 {
		mainLevel = 5
	} else if tmpVal >= 30 {
		mainLevel = 4
	} else if tmpVal >= 9 {
		mainLevel = 3
	} else if tmpVal >= 4 {
		mainLevel = 2
	} else if tmpVal >= 1 {
		mainLevel = 1
	} else {
		mainLevel = 0
	}
	return &pb.Level{
		MainLevel: uint32(mainLevel),
		SubLevel:  uint32(subLevel),
		LevelName: fmt.Sprintf("VIP%d", mainLevel),
	}
}

func (m *Manager) ReportDataCenter(ctx context.Context, sender, receiver uint32, now time.Time, isRecordRich, isRecordCharm bool, afterRich uint64, afterCharm uint64) {
	// oss 上报财富魅力值变化
	if isRecordRich && sender > 0 && afterRich > 0 {
		datacenter.StdReportKV(ctx, DataCenterBizID, map[string]interface{}{
			"totalDate":    now.Format("2006-01-02 15:04:05"),
			"uid":          sender,
			"createTime":   now.Format("2006-01-02 15:04:05"),
			"level":        "",
			"glamourLevel": "",
			"wealthLevel":  afterRich,
		})
	}
	if isRecordCharm && receiver > 0 && afterCharm > 0 {
		datacenter.StdReportKV(ctx, DataCenterBizID, map[string]interface{}{
			"totalDate":    now.Format("2006-01-02 15:04:05"),
			"uid":          receiver,
			"createTime":   now.Format("2006-01-02 15:04:05"),
			"level":        "",
			"glamourLevel": afterCharm,
			"wealthLevel":  "",
		})
	}
}

// checkAccelerateCard 检查加速卡
func (m *Manager) checkAccelerateCard(ctx context.Context, addRichUid, addCharmUid uint32) (map[uint32]float64, error) {
	accelerateCardMap := make(map[uint32]float64)
	uidList := make([]uint32, 0)
	if addRichUid != 0 {
		uidList = append(uidList, addRichUid)
	}
	if addCharmUid != 0 {
		uidList = append(uidList, addCharmUid)
	}
	if len(uidList) == 0 {
		return accelerateCardMap, nil
	}

	funcCardResp, sErr := m.backpackFuncCardCli.BatchGetAccelerateCardUsage(ctx, uidList)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "checkAccelerateCard BatchGetAccelerateCardUsage err:%s", sErr)
		return accelerateCardMap, sErr
	}

	for _, card := range funcCardResp.GetUserItemList() {
		times := float64(card.GetCardTimes() / 100)
		if card.GetUid() == addRichUid && card.GetCardType() == backpack_base.PackageItemType_BACKPACK_CARD_RICH_ACCELERATOR {
			accelerateCardMap[addRichUid] = times
		}
		if card.GetUid() == addCharmUid && card.GetCardType() == backpack_base.PackageItemType_BACKPACK_CARD_CHARM_ACCELERATOR {
			accelerateCardMap[addCharmUid] = times
		}
	}
	return accelerateCardMap, nil
}

func (m *Manager) PushToast(ctx context.Context, uid uint32, content string) error {
	msgSt := &pushPb.CommonPlainTextToastPush{
		Uid:     uid,
		Content: content,
	}
	msg, _ := proto.Marshal(msgSt)

	pushMessage := &pushPb.PushMessage{
		Cmd:     uint32(pushPb.PushMessage_COMMON_TEXT_TOAST_PUSH),
		Content: msg,
	}
	pushMessageBytes, _ := proto.Marshal(pushMessage)

	notification := &push_notification.CompositiveNotification{
		Sequence:           uint32(time.Now().Unix()),
		TerminalTypeList:   []uint32{protocol.MobileAndroidTT, protocol.MobileIPhoneTT},
		TerminalTypePolicy: pushclient.DefaultPolicy,
		AppId:              0,
		ProxyNotification: &push_notification.ProxyNotification{
			Type:    uint32(push_notification.ProxyNotification_PUSH),
			Payload: pushMessageBytes,
		},
	}

	err := m.pushCli.PushToUsers(ctx, []uint32{uid}, notification)
	if err != nil {
		log.ErrorWithCtx(ctx, "PushToast PushToUsers err: %s, uid: %d, content: %s", err, uid, content)
	}
	return err
}

func (m *Manager) ShutDown() {
	if m.anchorKafkaSub != nil {
		m.anchorKafkaSub.Close()
	}
	if m.esportKafkaSub != nil {
		m.esportKafkaSub.Close()
	}
	if m.presentKafkaSub != nil {
		m.presentKafkaSub.Close()
	}
	if m.knightKafkaSub != nil {
		m.knightKafkaSub.Close()
	}
}
