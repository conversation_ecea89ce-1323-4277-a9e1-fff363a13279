package cache

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/go-redis/redis"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/numeric-go"
	"golang.52tt.com/services/numeric-go/internal/common"
)

const (
	UserNumericLockKey = "nic_lock" // 新财富魅力值开关KEY
)

// 新财富魅力值开关 key
func getUserNumericLockKey(uid uint32) string {
	return fmt.Sprintf("%s:%d", UserNumericLockKey, uid)
}

const UserRichSwitchKey = "user_rich_switch_enable"

func (c *NumericGoCache) GetRichSwitch(ctx context.Context, uid uint32) (bool, error) {
	ret := c.redisClient.WithContext(ctx).SIsMember(UserRichSwitchKey, strconv.Itoa(int(uid)))
	if ret.Err() != nil {
		if !errors.Is(ret.Err(), redis.Nil) {
			log.Errorf("GetRichSwitch SIsMember err %s, uid: %d", ret.Err(), uid)
			return false, ret.Err()
		}
		return false, nil
	}
	return ret.Val(), nil
}

func (c *NumericGoCache) RichSwitchEnable(ctx context.Context, uid uint32) error {
	ret := c.redisClient.WithContext(ctx).SAdd(UserRichSwitchKey, strconv.Itoa(int(uid)))
	if ret.Err() != nil {
		log.Errorf("RichSwitchEnable SAdd err %s, uid: %d", ret.Err(), uid)
		return ret.Err()
	}
	return nil
}

func (c *NumericGoCache) RichSwitchDisable(ctx context.Context, uid uint32) error {
	ret := c.redisClient.WithContext(ctx).SRem(UserRichSwitchKey, strconv.Itoa(int(uid)))
	if ret.Err() != nil {
		log.Errorf("RichSwitchDisable SRem err %s, uid: %d", ret.Err(), uid)
		return ret.Err()
	}
	return nil
}

// GetUserNumericLock 获取用户财富值魅力值锁状态
func (c *NumericGoCache) GetUserNumericLock(ctx context.Context, uid uint32) (*pb.UserNumericLock, error) {
	key := getUserNumericLockKey(uid)
	val, err := c.redisClient.WithContext(ctx).Get(key).Uint64()
	if err != nil {
		if !errors.Is(err, redis.Nil) {
			log.Errorf("GetUserNumericLock Get err %s, uid: %d", err, uid)
			return nil, err
		}
		return &pb.UserNumericLock{}, nil
	}

	return &pb.UserNumericLock{
		BeanRichLock:     bitMapToStatus(val, common.LockBeanRich),
		DiamondRichLock:  bitMapToStatus(val, common.LockDiamondRich),
		BeanCharmLock:    bitMapToStatus(val, common.LockBeanCharm),
		DiamondCharmLock: bitMapToStatus(val, common.LockDiamondCharm),
	}, nil
}

// SetUserNumericLockByType 按类型设置用户财富值魅力值锁状态
func (c *NumericGoCache) SetUserNumericLockByType(ctx context.Context, uid uint32, lockStatus pb.LockStatus, lockType ...common.NumericLockT) error {
	key := getUserNumericLockKey(uid)

	// 获取当前锁状态
	val, err := c.redisClient.WithContext(ctx).Get(key).Uint64()
	if err != nil && !errors.Is(err, redis.Nil) {
		log.Errorf("SetUserNumericLockByType Get err %s, uid: %d", err, uid)
		return err
	}

	lockBitmap := common.NumericLockT(val)
	switch lockStatus {
	case pb.LockStatus_LOCK_STATUS_ENABLE: // 打开
		for _, lt := range lockType {
			lockBitmap |= lt
		}
	case pb.LockStatus_LOCK_STATUS_DISABLE: // 关闭
		for _, lt := range lockType {
			lockBitmap &^= lt
		}
	default:
		return fmt.Errorf("invalid lock status: %d", lockStatus)
	}

	if err := c.redisClient.WithContext(ctx).Set(key, uint64(lockBitmap), 24*time.Hour).Err(); err != nil {
		log.Errorf("SetUserNumericLockByType Set err %s, uid: %d", err, uid)
		return err
	}
	return nil
}

// SetUserNumericLock 设置用户财富值魅力值锁状态
func (c *NumericGoCache) SetUserNumericLock(ctx context.Context, uid uint32, lock *pb.UserNumericLock) error {
	key := getUserNumericLockKey(uid)

	// 构建位图
	var lockBitmap common.NumericLockT
	if lock.BeanRichLock == pb.LockStatus_LOCK_STATUS_ENABLE {
		lockBitmap |= common.LockBeanRich
	}
	if lock.DiamondRichLock == pb.LockStatus_LOCK_STATUS_ENABLE {
		lockBitmap |= common.LockDiamondRich
	}
	if lock.BeanCharmLock == pb.LockStatus_LOCK_STATUS_ENABLE {
		lockBitmap |= common.LockBeanCharm
	}
	if lock.DiamondCharmLock == pb.LockStatus_LOCK_STATUS_ENABLE {
		lockBitmap |= common.LockDiamondCharm
	}

	if err := c.redisClient.WithContext(ctx).Set(key, uint64(lockBitmap), 24*time.Hour).Err(); err != nil {
		log.Errorf("SetUserNumericLock Set err %s, uid: %d", err, uid)
		return err
	}
	return nil
}

// DeleteUserNumericLock 删除用户财富值魅力值锁状态
func (c *NumericGoCache) DeleteUserNumericLock(ctx context.Context, uid uint32) error {
	return c.redisClient.WithContext(ctx).Del(getUserNumericLockKey(uid)).Err()
}

// ConvToNumericLockEnable 将旧版财富值开关转换为新版财富值开关，并开启
func (c *NumericGoCache) ConvToNumericLockEnable(ctx context.Context, uid uint32, lockStatus pb.LockStatus) error {
	key := getUserNumericLockKey(uid)
	pipe := c.redisClient.WithContext(ctx).TxPipeline()

	// 删除旧版财富值开关
	pipe.SRem(UserRichSwitchKey, strconv.Itoa(int(uid)))

	// 设置新版财富值开关，同时开启T豆与红钻
	var lockBitmap common.NumericLockT
	if lockStatus == pb.LockStatus_LOCK_STATUS_ENABLE {
		lockBitmap = common.LockBeanRich | common.LockDiamondRich
	}
	pipe.Set(key, uint64(lockBitmap), 24*time.Hour)

	_, err := pipe.Exec()
	if err != nil {
		log.Errorf("ConvToNumericLockEnable TxPipeline Exec err %s, uid: %d", err, uid)
		return err
	}
	return nil
}

// BatchGetUserNumericLock 批量获取用户财富值魅力值锁状态
func (c *NumericGoCache) BatchGetUserNumericLock(ctx context.Context, uids []uint32) (map[uint32]*pb.UserNumericLock, []uint32, error) {
	if len(uids) == 0 {
		return make(map[uint32]*pb.UserNumericLock), nil, nil
	}

	// key列表
	keys := make([]string, len(uids))
	uid2Key := make(map[string]uint32, len(uids))
	for i, uid := range uids {
		key := getUserNumericLockKey(uid)
		keys[i] = key
		uid2Key[key] = uid
	}

	vals, err := c.redisClient.WithContext(ctx).MGet(keys...).Result()
	if err != nil {
		log.Errorf("BatchGetUserNumericLock MGet err %s", err)
		return nil, nil, err
	}

	result := make(map[uint32]*pb.UserNumericLock)
	notFoundUids := make([]uint32, 0)

	for i, val := range vals {
		key := keys[i]
		uid := uid2Key[key]

		if val == nil {
			notFoundUids = append(notFoundUids, uid)
			continue
		}

		// 转为uint64
		var lockBitmap uint64
		switch v := val.(type) {
		case string:
			var err error
			lockBitmap, err = strconv.ParseUint(v, 10, 64)
			if err != nil {
				log.Errorf("BatchGetUserNumericLock ParseUint err %s, val: %v", err, v)
				continue
			}
		default:
			log.Errorf("BatchGetUserNumericLock unexpected type %T for value", val)
			continue
		}

		result[uid] = &pb.UserNumericLock{
			BeanRichLock:     bitMapToStatus(lockBitmap, common.LockBeanRich),
			DiamondRichLock:  bitMapToStatus(lockBitmap, common.LockDiamondRich),
			BeanCharmLock:    bitMapToStatus(lockBitmap, common.LockBeanCharm),
			DiamondCharmLock: bitMapToStatus(lockBitmap, common.LockDiamondCharm),
		}
	}

	return result, notFoundUids, nil
}

// 获取bitMap中lockType锁的状态
func bitMapToStatus(bitMap uint64, lockType common.NumericLockT) pb.LockStatus {
	if bitMap&uint64(lockType) > 0 {
		return pb.LockStatus_LOCK_STATUS_ENABLE
	}
	return pb.LockStatus_LOCK_STATUS_DISABLE
}
