// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/numeric-go/internal/mysql (interfaces: IStore)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	numeric_go "golang.52tt.com/protocol/services/numeric-go"
	common "golang.52tt.com/services/numeric-go/internal/common"
	mysql "golang.52tt.com/services/numeric-go/internal/mysql"
	gorm "gorm.io/gorm"
)

// MockIStore is a mock of IStore interface.
type MockIStore struct {
	ctrl     *gomock.Controller
	recorder *MockIStoreMockRecorder
}

// MockIStoreMockRecorder is the mock recorder for MockIStore.
type MockIStoreMockRecorder struct {
	mock *MockIStore
}

// NewMockIStore creates a new mock instance.
func NewMockIStore(ctrl *gomock.Controller) *MockIStore {
	mock := &MockIStore{ctrl: ctrl}
	mock.recorder = &MockIStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIStore) EXPECT() *MockIStoreMockRecorder {
	return m.recorder
}

// AddPersonRichOrCharmChangeLog mocks base method.
func (m *MockIStore) AddPersonRichOrCharmChangeLog(arg0 context.Context, arg1 time.Time, arg2 []*mysql.PersonalRichCharmLog) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPersonRichOrCharmChangeLog", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddPersonRichOrCharmChangeLog indicates an expected call of AddPersonRichOrCharmChangeLog.
func (mr *MockIStoreMockRecorder) AddPersonRichOrCharmChangeLog(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPersonRichOrCharmChangeLog", reflect.TypeOf((*MockIStore)(nil).AddPersonRichOrCharmChangeLog), arg0, arg1, arg2)
}

// BatchGetPersonalNumeric mocks base method.
func (m *MockIStore) BatchGetPersonalNumeric(arg0 context.Context, arg1 []uint32) ([]*numeric_go.PersonalNumeric, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetPersonalNumeric", arg0, arg1)
	ret0, _ := ret[0].([]*numeric_go.PersonalNumeric)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetPersonalNumeric indicates an expected call of BatchGetPersonalNumeric.
func (mr *MockIStoreMockRecorder) BatchGetPersonalNumeric(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetPersonalNumeric", reflect.TypeOf((*MockIStore)(nil).BatchGetPersonalNumeric), arg0, arg1)
}

// BatchGetUserNumericLock mocks base method.
func (m *MockIStore) BatchGetUserNumericLock(arg0 context.Context, arg1 []uint32) (map[uint32]*numeric_go.UserNumericLock, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserNumericLock", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]*numeric_go.UserNumericLock)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserNumericLock indicates an expected call of BatchGetUserNumericLock.
func (mr *MockIStoreMockRecorder) BatchGetUserNumericLock(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserNumericLock", reflect.TypeOf((*MockIStore)(nil).BatchGetUserNumericLock), arg0, arg1)
}

// CancelUseRichCard mocks base method.
func (m *MockIStore) CancelUseRichCard(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelUseRichCard", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CancelUseRichCard indicates an expected call of CancelUseRichCard.
func (mr *MockIStoreMockRecorder) CancelUseRichCard(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelUseRichCard", reflect.TypeOf((*MockIStore)(nil).CancelUseRichCard), arg0, arg1)
}

// CertainUseRichCard mocks base method.
func (m *MockIStore) CertainUseRichCard(arg0 context.Context, arg1 *mysql.RichCardOrder, arg2 *numeric_go.PersonalNumeric) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CertainUseRichCard", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// CertainUseRichCard indicates an expected call of CertainUseRichCard.
func (mr *MockIStoreMockRecorder) CertainUseRichCard(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CertainUseRichCard", reflect.TypeOf((*MockIStore)(nil).CertainUseRichCard), arg0, arg1, arg2)
}

// CheckCleanOrderRecords mocks base method.
func (m *MockIStore) CheckCleanOrderRecords(arg0 context.Context, arg1 []uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckCleanOrderRecords", arg0, arg1)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckCleanOrderRecords indicates an expected call of CheckCleanOrderRecords.
func (mr *MockIStoreMockRecorder) CheckCleanOrderRecords(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckCleanOrderRecords", reflect.TypeOf((*MockIStore)(nil).CheckCleanOrderRecords), arg0, arg1)
}

// CleanOrderData mocks base method.
func (m *MockIStore) CleanOrderData(arg0 context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "CleanOrderData", arg0)
}

// CleanOrderData indicates an expected call of CleanOrderData.
func (mr *MockIStoreMockRecorder) CleanOrderData(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CleanOrderData", reflect.TypeOf((*MockIStore)(nil).CleanOrderData), arg0)
}

// CleanUserRich mocks base method.
func (m *MockIStore) CleanUserRich(arg0 context.Context, arg1 uint32, arg2 []*mysql.PersonalRichCleanRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CleanUserRich", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// CleanUserRich indicates an expected call of CleanUserRich.
func (mr *MockIStoreMockRecorder) CleanUserRich(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CleanUserRich", reflect.TypeOf((*MockIStore)(nil).CleanUserRich), arg0, arg1, arg2)
}

// CreateCleanRichOrder mocks base method.
func (m *MockIStore) CreateCleanRichOrder(arg0 context.Context, arg1 *mysql.PersonalRichCleanOrder, arg2 []*mysql.PersonalRichCleanRecord) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateCleanRichOrder", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateCleanRichOrder indicates an expected call of CreateCleanRichOrder.
func (mr *MockIStoreMockRecorder) CreateCleanRichOrder(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateCleanRichOrder", reflect.TypeOf((*MockIStore)(nil).CreateCleanRichOrder), arg0, arg1, arg2)
}

// CreateMysqlTable mocks base method.
func (m *MockIStore) CreateMysqlTable() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateMysqlTable")
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateMysqlTable indicates an expected call of CreateMysqlTable.
func (mr *MockIStoreMockRecorder) CreateMysqlTable() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateMysqlTable", reflect.TypeOf((*MockIStore)(nil).CreateMysqlTable))
}

// CreateRichReconcile mocks base method.
func (m *MockIStore) CreateRichReconcile(arg0 context.Context, arg1 *common.GeneralNumeric) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRichReconcile", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateRichReconcile indicates an expected call of CreateRichReconcile.
func (mr *MockIStoreMockRecorder) CreateRichReconcile(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRichReconcile", reflect.TypeOf((*MockIStore)(nil).CreateRichReconcile), arg0, arg1)
}

// CreateSwitchOpLog mocks base method.
func (m *MockIStore) CreateSwitchOpLog(arg0 context.Context, arg1, arg2 uint32, arg3 bool, arg4 numeric_go.RichSwitchChangeType) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSwitchOpLog", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateSwitchOpLog indicates an expected call of CreateSwitchOpLog.
func (mr *MockIStoreMockRecorder) CreateSwitchOpLog(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSwitchOpLog", reflect.TypeOf((*MockIStore)(nil).CreateSwitchOpLog), arg0, arg1, arg2, arg3, arg4)
}

// CreateSwitchOpLogV2 mocks base method.
func (m *MockIStore) CreateSwitchOpLogV2(arg0 context.Context, arg1 *mysql.PersonalRichSwitchLog) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSwitchOpLogV2", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateSwitchOpLogV2 indicates an expected call of CreateSwitchOpLogV2.
func (mr *MockIStoreMockRecorder) CreateSwitchOpLogV2(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSwitchOpLogV2", reflect.TypeOf((*MockIStore)(nil).CreateSwitchOpLogV2), arg0, arg1)
}

// GetChangeLogByOrderId mocks base method.
func (m *MockIStore) GetChangeLogByOrderId(arg0 context.Context, arg1 time.Time, arg2 string) (*mysql.PersonalRichCharmLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChangeLogByOrderId", arg0, arg1, arg2)
	ret0, _ := ret[0].(*mysql.PersonalRichCharmLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChangeLogByOrderId indicates an expected call of GetChangeLogByOrderId.
func (mr *MockIStoreMockRecorder) GetChangeLogByOrderId(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChangeLogByOrderId", reflect.TypeOf((*MockIStore)(nil).GetChangeLogByOrderId), arg0, arg1, arg2)
}

// GetCleanRichOrder mocks base method.
func (m *MockIStore) GetCleanRichOrder(arg0 context.Context, arg1 uint32) (*mysql.PersonalRichCleanOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCleanRichOrder", arg0, arg1)
	ret0, _ := ret[0].(*mysql.PersonalRichCleanOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCleanRichOrder indicates an expected call of GetCleanRichOrder.
func (mr *MockIStoreMockRecorder) GetCleanRichOrder(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCleanRichOrder", reflect.TypeOf((*MockIStore)(nil).GetCleanRichOrder), arg0, arg1)
}

// GetCleanRichOrdersByIDs mocks base method.
func (m *MockIStore) GetCleanRichOrdersByIDs(arg0 context.Context, arg1 []uint32) (map[uint32]*mysql.PersonalRichCleanOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCleanRichOrdersByIDs", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]*mysql.PersonalRichCleanOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCleanRichOrdersByIDs indicates an expected call of GetCleanRichOrdersByIDs.
func (mr *MockIStoreMockRecorder) GetCleanRichOrdersByIDs(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCleanRichOrdersByIDs", reflect.TypeOf((*MockIStore)(nil).GetCleanRichOrdersByIDs), arg0, arg1)
}

// GetCleanRichRecordList mocks base method.
func (m *MockIStore) GetCleanRichRecordList(arg0 context.Context, arg1 *numeric_go.GetCleanUserRichRecordsReq) ([]*mysql.PersonalRichCleanRecord, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCleanRichRecordList", arg0, arg1)
	ret0, _ := ret[0].([]*mysql.PersonalRichCleanRecord)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetCleanRichRecordList indicates an expected call of GetCleanRichRecordList.
func (mr *MockIStoreMockRecorder) GetCleanRichRecordList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCleanRichRecordList", reflect.TypeOf((*MockIStore)(nil).GetCleanRichRecordList), arg0, arg1)
}

// GetCleanRichRecordsByOrderID mocks base method.
func (m *MockIStore) GetCleanRichRecordsByOrderID(arg0 context.Context, arg1 uint32) ([]*mysql.PersonalRichCleanRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCleanRichRecordsByOrderID", arg0, arg1)
	ret0, _ := ret[0].([]*mysql.PersonalRichCleanRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCleanRichRecordsByOrderID indicates an expected call of GetCleanRichRecordsByOrderID.
func (mr *MockIStoreMockRecorder) GetCleanRichRecordsByOrderID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCleanRichRecordsByOrderID", reflect.TypeOf((*MockIStore)(nil).GetCleanRichRecordsByOrderID), arg0, arg1)
}

// GetGetPersonalNumericWithError mocks base method.
func (m *MockIStore) GetGetPersonalNumericWithError(arg0 context.Context, arg1 uint32) (*numeric_go.PersonalNumeric, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGetPersonalNumericWithError", arg0, arg1)
	ret0, _ := ret[0].(*numeric_go.PersonalNumeric)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGetPersonalNumericWithError indicates an expected call of GetGetPersonalNumericWithError.
func (mr *MockIStoreMockRecorder) GetGetPersonalNumericWithError(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGetPersonalNumericWithError", reflect.TypeOf((*MockIStore)(nil).GetGetPersonalNumericWithError), arg0, arg1)
}

// GetLastSwitchOpLog mocks base method.
func (m *MockIStore) GetLastSwitchOpLog(arg0 context.Context, arg1 uint32) (*mysql.PersonalRichSwitchLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastSwitchOpLog", arg0, arg1)
	ret0, _ := ret[0].(*mysql.PersonalRichSwitchLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLastSwitchOpLog indicates an expected call of GetLastSwitchOpLog.
func (mr *MockIStoreMockRecorder) GetLastSwitchOpLog(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastSwitchOpLog", reflect.TypeOf((*MockIStore)(nil).GetLastSwitchOpLog), arg0, arg1)
}

// GetLastSwitchOpLogByTime mocks base method.
func (m *MockIStore) GetLastSwitchOpLogByTime(arg0 context.Context, arg1 uint32, arg2 time.Time) (*mysql.PersonalRichSwitchLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastSwitchOpLogByTime", arg0, arg1, arg2)
	ret0, _ := ret[0].(*mysql.PersonalRichSwitchLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLastSwitchOpLogByTime indicates an expected call of GetLastSwitchOpLogByTime.
func (mr *MockIStoreMockRecorder) GetLastSwitchOpLogByTime(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastSwitchOpLogByTime", reflect.TypeOf((*MockIStore)(nil).GetLastSwitchOpLogByTime), arg0, arg1, arg2)
}

// GetPersonVip mocks base method.
func (m *MockIStore) GetPersonVip(arg0 context.Context, arg1 uint32) (*mysql.PersonRichVip, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPersonVip", arg0, arg1)
	ret0, _ := ret[0].(*mysql.PersonRichVip)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPersonVip indicates an expected call of GetPersonVip.
func (mr *MockIStoreMockRecorder) GetPersonVip(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPersonVip", reflect.TypeOf((*MockIStore)(nil).GetPersonVip), arg0, arg1)
}

// GetPersonalLockBitmap mocks base method.
func (m *MockIStore) GetPersonalLockBitmap(arg0 context.Context, arg1 uint32) (*mysql.PersonalLockBitmap, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPersonalLockBitmap", arg0, arg1)
	ret0, _ := ret[0].(*mysql.PersonalLockBitmap)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPersonalLockBitmap indicates an expected call of GetPersonalLockBitmap.
func (mr *MockIStoreMockRecorder) GetPersonalLockBitmap(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPersonalLockBitmap", reflect.TypeOf((*MockIStore)(nil).GetPersonalLockBitmap), arg0, arg1)
}

// GetPersonalMonthNumeric mocks base method.
func (m *MockIStore) GetPersonalMonthNumeric(arg0 context.Context, arg1 uint32, arg2 time.Time) (*numeric_go.PersonalNumeric, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPersonalMonthNumeric", arg0, arg1, arg2)
	ret0, _ := ret[0].(*numeric_go.PersonalNumeric)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPersonalMonthNumeric indicates an expected call of GetPersonalMonthNumeric.
func (mr *MockIStoreMockRecorder) GetPersonalMonthNumeric(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPersonalMonthNumeric", reflect.TypeOf((*MockIStore)(nil).GetPersonalMonthNumeric), arg0, arg1, arg2)
}

// GetPersonalNumeric mocks base method.
func (m *MockIStore) GetPersonalNumeric(arg0 context.Context, arg1 uint32) (*numeric_go.PersonalNumeric, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPersonalNumeric", arg0, arg1)
	ret0, _ := ret[0].(*numeric_go.PersonalNumeric)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPersonalNumeric indicates an expected call of GetPersonalNumeric.
func (mr *MockIStoreMockRecorder) GetPersonalNumeric(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPersonalNumeric", reflect.TypeOf((*MockIStore)(nil).GetPersonalNumeric), arg0, arg1)
}

// GetRichCardOrder mocks base method.
func (m *MockIStore) GetRichCardOrder(arg0 context.Context, arg1 string) (*mysql.RichCardOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRichCardOrder", arg0, arg1)
	ret0, _ := ret[0].(*mysql.RichCardOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRichCardOrder indicates an expected call of GetRichCardOrder.
func (mr *MockIStoreMockRecorder) GetRichCardOrder(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRichCardOrder", reflect.TypeOf((*MockIStore)(nil).GetRichCardOrder), arg0, arg1)
}

// GetRichReconcile mocks base method.
func (m *MockIStore) GetRichReconcile(arg0 context.Context, arg1 string, arg2 time.Time) (*mysql.RichReconcile, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRichReconcile", arg0, arg1, arg2)
	ret0, _ := ret[0].(*mysql.RichReconcile)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRichReconcile indicates an expected call of GetRichReconcile.
func (mr *MockIStoreMockRecorder) GetRichReconcile(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRichReconcile", reflect.TypeOf((*MockIStore)(nil).GetRichReconcile), arg0, arg1, arg2)
}

// GetRichReconcileCount mocks base method.
func (m *MockIStore) GetRichReconcileCount(arg0 context.Context, arg1, arg2 time.Time, arg3 numeric_go.SourceT) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRichReconcileCount", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRichReconcileCount indicates an expected call of GetRichReconcileCount.
func (mr *MockIStoreMockRecorder) GetRichReconcileCount(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRichReconcileCount", reflect.TypeOf((*MockIStore)(nil).GetRichReconcileCount), arg0, arg1, arg2, arg3)
}

// GetRichReconcileList mocks base method.
func (m *MockIStore) GetRichReconcileList(arg0 context.Context, arg1, arg2 time.Time, arg3 numeric_go.SourceT) ([]*mysql.RichReconcile, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRichReconcileList", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*mysql.RichReconcile)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRichReconcileList indicates an expected call of GetRichReconcileList.
func (mr *MockIStoreMockRecorder) GetRichReconcileList(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRichReconcileList", reflect.TypeOf((*MockIStore)(nil).GetRichReconcileList), arg0, arg1, arg2, arg3)
}

// GetUserNumericLock mocks base method.
func (m *MockIStore) GetUserNumericLock(arg0 context.Context, arg1 uint32) (*numeric_go.UserNumericLock, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserNumericLock", arg0, arg1)
	ret0, _ := ret[0].(*numeric_go.UserNumericLock)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserNumericLock indicates an expected call of GetUserNumericLock.
func (mr *MockIStoreMockRecorder) GetUserNumericLock(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserNumericLock", reflect.TypeOf((*MockIStore)(nil).GetUserNumericLock), arg0, arg1)
}

// PreUseRichCard mocks base method.
func (m *MockIStore) PreUseRichCard(arg0 context.Context, arg1 *mysql.RichCardOrder) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PreUseRichCard", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PreUseRichCard indicates an expected call of PreUseRichCard.
func (mr *MockIStoreMockRecorder) PreUseRichCard(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PreUseRichCard", reflect.TypeOf((*MockIStore)(nil).PreUseRichCard), arg0, arg1)
}

// RecordGiftEventChange mocks base method.
func (m *MockIStore) RecordGiftEventChange(arg0 context.Context, arg1 *common.GeneralNumeric) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordGiftEventChange", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordGiftEventChange indicates an expected call of RecordGiftEventChange.
func (mr *MockIStoreMockRecorder) RecordGiftEventChange(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordGiftEventChange", reflect.TypeOf((*MockIStore)(nil).RecordGiftEventChange), arg0, arg1)
}

// RecordGiftEventCharmChangeV2 mocks base method.
func (m *MockIStore) RecordGiftEventCharmChangeV2(arg0 context.Context, arg1 *common.GeneralNumeric) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordGiftEventCharmChangeV2", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordGiftEventCharmChangeV2 indicates an expected call of RecordGiftEventCharmChangeV2.
func (mr *MockIStoreMockRecorder) RecordGiftEventCharmChangeV2(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordGiftEventCharmChangeV2", reflect.TypeOf((*MockIStore)(nil).RecordGiftEventCharmChangeV2), arg0, arg1)
}

// RecordGiftEventRichChangeV2 mocks base method.
func (m *MockIStore) RecordGiftEventRichChangeV2(arg0 context.Context, arg1 *common.GeneralNumeric) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordGiftEventRichChangeV2", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordGiftEventRichChangeV2 indicates an expected call of RecordGiftEventRichChangeV2.
func (mr *MockIStoreMockRecorder) RecordGiftEventRichChangeV2(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordGiftEventRichChangeV2", reflect.TypeOf((*MockIStore)(nil).RecordGiftEventRichChangeV2), arg0, arg1)
}

// RecordGuildCharm mocks base method.
func (m *MockIStore) RecordGuildCharm(arg0 *gorm.DB, arg1, arg2 uint32, arg3 uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordGuildCharm", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordGuildCharm indicates an expected call of RecordGuildCharm.
func (mr *MockIStoreMockRecorder) RecordGuildCharm(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordGuildCharm", reflect.TypeOf((*MockIStore)(nil).RecordGuildCharm), arg0, arg1, arg2, arg3)
}

// RecordGuildGiftRich mocks base method.
func (m *MockIStore) RecordGuildGiftRich(arg0 *gorm.DB, arg1 uint32, arg2 uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordGuildGiftRich", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordGuildGiftRich indicates an expected call of RecordGuildGiftRich.
func (mr *MockIStoreMockRecorder) RecordGuildGiftRich(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordGuildGiftRich", reflect.TypeOf((*MockIStore)(nil).RecordGuildGiftRich), arg0, arg1, arg2)
}

// RecordPersonCharm mocks base method.
func (m *MockIStore) RecordPersonCharm(arg0 *gorm.DB, arg1 uint32, arg2 uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordPersonCharm", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordPersonCharm indicates an expected call of RecordPersonCharm.
func (mr *MockIStoreMockRecorder) RecordPersonCharm(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordPersonCharm", reflect.TypeOf((*MockIStore)(nil).RecordPersonCharm), arg0, arg1, arg2)
}

// RecordPersonMonthCharm mocks base method.
func (m *MockIStore) RecordPersonMonthCharm(arg0 context.Context, arg1 time.Time, arg2 uint32, arg3 uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordPersonMonthCharm", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordPersonMonthCharm indicates an expected call of RecordPersonMonthCharm.
func (mr *MockIStoreMockRecorder) RecordPersonMonthCharm(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordPersonMonthCharm", reflect.TypeOf((*MockIStore)(nil).RecordPersonMonthCharm), arg0, arg1, arg2, arg3)
}

// RecordPersonMonthRich mocks base method.
func (m *MockIStore) RecordPersonMonthRich(arg0 context.Context, arg1 time.Time, arg2 uint32, arg3 uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordPersonMonthRich", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordPersonMonthRich indicates an expected call of RecordPersonMonthRich.
func (mr *MockIStoreMockRecorder) RecordPersonMonthRich(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordPersonMonthRich", reflect.TypeOf((*MockIStore)(nil).RecordPersonMonthRich), arg0, arg1, arg2, arg3)
}

// RecordPersonRich mocks base method.
func (m *MockIStore) RecordPersonRich(arg0 *gorm.DB, arg1 uint32, arg2 uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordPersonRich", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordPersonRich indicates an expected call of RecordPersonRich.
func (mr *MockIStoreMockRecorder) RecordPersonRich(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordPersonRich", reflect.TypeOf((*MockIStore)(nil).RecordPersonRich), arg0, arg1, arg2)
}

// RecordPersonRichCharm mocks base method.
func (m *MockIStore) RecordPersonRichCharm(arg0 context.Context, arg1 uint32, arg2, arg3 uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordPersonRichCharm", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordPersonRichCharm indicates an expected call of RecordPersonRichCharm.
func (mr *MockIStoreMockRecorder) RecordPersonRichCharm(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordPersonRichCharm", reflect.TypeOf((*MockIStore)(nil).RecordPersonRichCharm), arg0, arg1, arg2, arg3)
}

// RecordPersonVip mocks base method.
func (m *MockIStore) RecordPersonVip(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordPersonVip", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordPersonVip indicates an expected call of RecordPersonVip.
func (mr *MockIStoreMockRecorder) RecordPersonVip(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordPersonVip", reflect.TypeOf((*MockIStore)(nil).RecordPersonVip), arg0, arg1, arg2)
}

// RemoveCleanRichOrder mocks base method.
func (m *MockIStore) RemoveCleanRichOrder(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveCleanRichOrder", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveCleanRichOrder indicates an expected call of RemoveCleanRichOrder.
func (mr *MockIStoreMockRecorder) RemoveCleanRichOrder(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveCleanRichOrder", reflect.TypeOf((*MockIStore)(nil).RemoveCleanRichOrder), arg0, arg1)
}

// SetRichReconcileStatus mocks base method.
func (m *MockIStore) SetRichReconcileStatus(arg0 context.Context, arg1 string, arg2 time.Time, arg3 mysql.ReconcileStatus) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetRichReconcileStatus", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetRichReconcileStatus indicates an expected call of SetRichReconcileStatus.
func (mr *MockIStoreMockRecorder) SetRichReconcileStatus(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetRichReconcileStatus", reflect.TypeOf((*MockIStore)(nil).SetRichReconcileStatus), arg0, arg1, arg2, arg3)
}

// SetUserNumericLock mocks base method.
func (m *MockIStore) SetUserNumericLock(arg0 context.Context, arg1 uint32, arg2 common.NumericLockT, arg3 numeric_go.LockStatus) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserNumericLock", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserNumericLock indicates an expected call of SetUserNumericLock.
func (mr *MockIStoreMockRecorder) SetUserNumericLock(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserNumericLock", reflect.TypeOf((*MockIStore)(nil).SetUserNumericLock), arg0, arg1, arg2, arg3)
}

// SetUserNumericLockBatch mocks base method.
func (m *MockIStore) SetUserNumericLockBatch(arg0 context.Context, arg1 uint32, arg2 []common.NumericLockT, arg3 numeric_go.LockStatus) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserNumericLockBatch", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserNumericLockBatch indicates an expected call of SetUserNumericLockBatch.
func (mr *MockIStoreMockRecorder) SetUserNumericLockBatch(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserNumericLockBatch", reflect.TypeOf((*MockIStore)(nil).SetUserNumericLockBatch), arg0, arg1, arg2, arg3)
}

// Transaction mocks base method.
func (m *MockIStore) Transaction(arg0 context.Context, arg1 func(*gorm.DB) error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Transaction", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Transaction indicates an expected call of Transaction.
func (mr *MockIStoreMockRecorder) Transaction(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Transaction", reflect.TypeOf((*MockIStore)(nil).Transaction), arg0, arg1)
}
