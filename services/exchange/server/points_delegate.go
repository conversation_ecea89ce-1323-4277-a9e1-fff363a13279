package server

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/go-redis/redis"
	"golang.52tt.com/pkg/log"
	currencyPB "golang.52tt.com/protocol/services/currencysvr"
	userscore2 "golang.52tt.com/protocol/services/userscore"
	"golang.52tt.com/services/exchange/cache"
	"golang.52tt.com/services/exchange/comm"
	"golang.52tt.com/services/exchange/model"
	"time"

	"golang.org/x/net/context"

	"golang.52tt.com/clients/userscore"
	"golang.52tt.com/pkg/commission"
	apiPB "golang.52tt.com/protocol/services/apicenter/apiserver"

	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tbean"
	"golang.52tt.com/protocol/common/status"
	exchangePB "golang.52tt.com/protocol/services/exchange"
	"golang.52tt.com/services/exchange/protection"
	"golang.52tt.com/services/notify"
)

const (
	TT10100 = 10100 // for TBean Transfer
	TT60400 = 60400 //骑士团积分
	TT60600 = 60600 //电竞积分

	WithdrawMin = 10000
)

type pointsDelegate struct {
	tbeanClient tbean.Client
	//commissionClient commission.Client
	rc        *redis.Client
	rb        *redis.Client
	s         *exchangeServer
	sc        *serverConfig
	scoreType uint32
	secretKey string
}

func newPointsDelegate(sc *serverConfig, c *redis.Client, rb *redis.Client, s *exchangeServer, scoreType uint32) delegate {
	return &pointsDelegate{
		tbeanClient: tbean.NewClient(sc.tbeanContextPath),
		//commissionClient: commission.NewClient(sc.commissionURLPrefix, sc.commissionAppID, sc.commissionSignKey),
		rc:        c,
		rb:        rb,
		s:         s,
		sc:        sc,
		scoreType: scoreType,
		secretKey: sc.tbeanSecretKey,
	}
}

func (d *pointsDelegate) getBalance(ctx context.Context, uid uint32) (uint64, error) {
	b, err := userScoreClient.GetUserScore(ctx, uid, d.scoreType)
	return uint64(b), err
}

func (d *pointsDelegate) precheckTx(ctx context.Context, tx *model.ExchangeTransaction) error {

	//查看是否在黑名单
	var blackType string
	switch tx.TargetType {
	case uint32(exchangePB.CurrencyType_COMMISSION):
		blackType = tbean.BLACK_TYPE_RECHARGE
	case uint32(exchangePB.CurrencyType_TBEAN):
		blackType = tbean.BLACK_TYPE_EXCHANGE
	default:
		return errCurrencyTypeNotSupported
	}
	isBlack, err := d.tbeanClient.CheckBlackUser(ctx, fmt.Sprintf("%d", tx.UserID), blackType)
	if err != nil { //机房访问问题，就放过
		log.Errorln(err)
	}
	if isBlack {
		log.InfoWithCtx(ctx, "tbeanClient.CheckBlackUser uid=%d is black", tx.UserID)
		return protocol.NewServerError(status.ErrExchangeBlackUser)
	}

	if tx.TargetType == uint32(exchangePB.CurrencyType_TBEAN) {
		// 兑换T豆, 先查T豆账户的库存余额
		b, err := d.tbeanClient.GetBalance(ctx, tbean.AppID_TT, tbean.AccountTypeInternal, TT10100)
		if err != nil {
			return err
		}

		if b <= 0 || uint32(b) < tx.TargetAmount {
			// T豆账户库存不足
			return insufficientTBean
		}

		cerr := protection.CheckFuseProtection(exchangePB.CurrencyType_POINTS, int64(tx.SourceAmount), tx.UserID)
		if nil != cerr {
			return cerr
		}
	}

	if tx.TargetType == uint32(exchangePB.CurrencyType_COMMISSION) && tx.SourceType == uint32(exchangePB.CurrencyType_POINTS_TBEAN_ONLY) {
		score, err := comm.GetScore(tx.UserID, 0)
		if err != nil {
			log.ErrorWithCtx(ctx, "comm.GetScore err=%v", err)
			return err
		}
		tBeanScore, err := comm.GetScore(tx.UserID, 1)
		if err != nil {
			log.ErrorWithCtx(ctx, "comm.GetScore err=%v", err)
			return err
		}
		if score+tBeanScore < WithdrawMin {
			withdrawCache := cache.NewScoreCanWithdrawCache(d.rc)
			canWithdraw, err := withdrawCache.Get(tx.UserID)
			if err != nil {
				log.ErrorWithCtx(ctx, "withdrawCache.Get err=%v", err)
				return err
			}
			if !canWithdraw {
				return protocol.NewExactServerError(nil, status.ErrExchangeParamErr, "没有提现权限")
			}
		}
	}
	return nil
}

func (d *pointsDelegate) processTx(ctx context.Context, tx *model.ExchangeTransaction) (outsideOrderId string, outsideTime time.Time, balance uint32, err error) {
	log.DebugWithCtx(ctx, "pointsDelegate processing exchangeTransaction: %+v", tx)

	//scoreType := userscore2.ScoreType_Origin
	//switch tx.SourceType {
	//case uint32(exchangePB.CurrencyType_POINTS):
	//	scoreType = userscore2.ScoreType_Origin
	//case uint32(exchangePB.CurrencyType_POINTS_WITHDRAW):
	//	scoreType = userscore2.ScoreType_Withdraw
	//default:
	//	err = errCurrencyTypeNotSupported
	//	return
	//}

	total := tx.TargetAmount + tx.BonusAmount
	log.InfoWithCtx(ctx, "Exchange POINTS-TBEAN OK %d %d %d", tx.UserID, tx.SourceAmount, total)
	var desc string
	var reason userscore2.ScoreChangeReason
	switch tx.TargetType {
	case uint32(exchangePB.CurrencyType_RED_DIAMOND):
		desc = fmt.Sprintf("兑换: %d 积分 -> %d 红钻", tx.SourceAmount, total)
		reason = userscore.ExchangeRedDiamond
	case uint32(exchangePB.CurrencyType_COMMISSION):
		desc = fmt.Sprintf("结算: %d 积分 -> %d 佣金", tx.SourceAmount, total)
		reason = userscore.Settlement
	case uint32(exchangePB.CurrencyType_TBEAN):
		desc = fmt.Sprintf("兑换: %d 积分 -> %d T豆", tx.SourceAmount, total)
		reason = userscore.ExchangeTBean
	default:
		err = errCurrencyTypeNotSupported
		return
	}

	uid := tx.UserID
	if tx.OrderDesc != "" {
		desc = tx.OrderDesc
	}

	locker := cache.NewLocker(d.rc, d.rb)
	lockV := locker.GenerateValue()
	if !locker.LockScore(uid, lockV) {
		err = errors.New("score locked")
		return
	}
	defer locker.UnlockScore(uid, lockV)

	_, err = userScoreClient.AddUserScore(ctx, uid, uid, -1*int32(tx.SourceAmount),
		reason, tx.OrderID, desc, "", nil, uint32(tx.CreateAt.Unix()), d.scoreType)
	if err != nil /*&& err.Code() != status.ErrUserScoreOrderExist*/ {
		log.ErrorWithCtx(ctx, "userscore.Client.AddUserScore uid(%d) orderId(%s) failed: %v", uid, tx.OrderID, err)
		return
	}
	// 通知积分更新
	go notify.NotifySync(tx.UserID, notify.Grow)

	switch tx.TargetType {
	case uint32(exchangePB.CurrencyType_RED_DIAMOND):
		err = currencyClient.AddUserCurrency(ctx, tx.UserID, int32(total), tx.OrderID, desc, uint32(currencyPB.ADD_CURRENCY_REASON_UNKNOWN))
		if err != nil {
			serr := err.(protocol.ServerError)
			if serr.Code() == status.ErrGrowCurrencyAdded {
				log.WarnWithCtx(ctx, "currencyClient.AddUserCurrency(%d, %d, %s, %s) failed with code ErrGrowCurrencyAdded",
					tx.UserID, int32(total), tx.OrderID, desc)
			} else {
				log.ErrorWithCtx(ctx, "currencyClient.AddUserCurrency(%d, %d, %s, %s) failed: %v", tx.UserID, int32(total), tx.OrderID, desc, err)
				return
			}
		}
		log.ErrorWithCtx(ctx, "currencyClient.AddUserCurrency(%d, %d, %s, %s) OK", tx.UserID, int32(total), tx.OrderID, desc)
		log.ErrorWithCtx(ctx, "Exchange POINTS-RD OK %d %d %d", tx.UserID, tx.SourceAmount, total)
	case uint32(exchangePB.CurrencyType_TBEAN):
		// 兑换为T豆
		/*transferReq := &tbean.TransferI2CRequest{
			AppId:      tbean.AppID_TT,
			OutTradeNo: tx.OrderID,
			From:       TT10100,
			To: []*tbean.TransferI2CRequest_To{
				{Uid: tx.UserID, Amount: uint64(total)},
			},
			Notes: desc,
		}

		var resp []*tbean.TransferI2CResponseBody
		resp, err = d.tbeanClient.TransferI2C(ctx, transferReq)
		if err != nil {
			log.ErrorWithCtx(ctx, "tbeanClient.TransferI2C %v failed: %v", transferReq, err)
			if err == tbean.ErrDuplicateOrder {
				var balanceResp *tbean.TransferI2CBalanceResp
				balanceResp, err = d.tbeanClient.TransferI2CGetBalance(ctx, &tbean.TransferI2CBalanceReq{
					AppId:      tbean.AppID_TT,
					OutTradeNo: tx.OrderID,
				})
				if err != nil {
					log.ErrorWithCtx(ctx, "Failed to GetBalance uid(%d) err %+v", tx.UserID, err)
					return
				}

				balance = balanceResp.CrBalance
				return
			}
			return
		}*/

		transferReq := &tbean.TransferI2CV2Request{
			Data: &tbean.TransferI2CV2Request_Data{
				AppId:    tbean.NewAppId,
				TradeNo:  tx.OrderID,
				Scope:    "redeem-gift-points",
				SubScope: "",
				FromId:   fmt.Sprintf("%d", TT10100),
				ToId:     fmt.Sprintf("%d", tx.UserID),
				Amount:   int64(total),
				Notes:    desc,
				At:       tx.OutsideTime.Unix() * 1000, //转ms
			},
			SignedAt: tx.OutsideTime.Unix(),
		}
		transferReq.Sign = tbean.Sign(transferReq, d.secretKey)
		_, err = d.tbeanClient.TransferI2CV2(ctx, transferReq)
		if err != nil {
			log.ErrorWithCtx(ctx, "tbeanClient.TransferI2CV2 %v failed: %v", transferReq, err)
			if err == tbean.ErrDuplicateOrder {
				var balanceResp *tbean.TransferI2CBalanceResp
				balanceResp, err = d.tbeanClient.TransferI2CGetBalance(ctx, &tbean.TransferI2CBalanceReq{
					AppId:      tbean.AppID_TT,
					OutTradeNo: tx.OrderID,
				})
				if err != nil {
					log.ErrorWithCtx(ctx, "Failed to GetBalance uid(%d) err %+v", tx.UserID, err)
					return
				}

				balance = balanceResp.CrBalance
				return
			}
			return
		}

		//统计数据
		protection.RecordExchange(exchangePB.CurrencyType_POINTS, int64(tx.SourceAmount), tx.UserID)

		/*if len(resp) > 0 {
			for _, item := range resp {
				if item.Uid == tx.UserID {
					balance = item.Balance
					outsideOrderId = item.OutTradeNo
					outsideTime, err = time.ParseInLocation("2006-01-02 15:04:05", item.CheckingTime, time.Local)
					if err != nil {
						log.ErrorWithCtx(ctx, "time.ParseInLocation(%s) err: %v", item.CheckingTime, err)
						return
					}
				}
			}
		}*/

		log.InfoWithCtx(ctx, "Exchange POINTS-TBEAN OK %d %d %d", tx.UserID, tx.SourceAmount, total)

		//go d.pushWithTTAssistant(tx.UserID, fmt.Sprintf("【兑换通知】%dT豆兑换成功，已消耗积分%d。%s",
		//	total, tx.SourceAmount, time.Now().Format("01月02日 15:04:05")))

	case uint32(exchangePB.CurrencyType_COMMISSION):
		//if err := d.commissionClient.SettlementAndEncashment(ctx, tx.UserID, uint64(total), string(tx.UserData), desc); err != nil {
		//	log.ErrorWithCtx(ctx, "commissionClient.Settlement %d %d %s %s failed: %v", tx.UserID, total, string(tx.UserData), desc, err)
		//	return 0, err
		//}
		var pwResp *exchangePB.PrivateWithdrawResp
		pwResp, err = d.s.PrivateWithdraw(ctx, &exchangePB.PrivateWithdrawReq{
			OrderId:    tx.OrderID,
			Uid:        tx.UserID,
			Total:      uint64(total),
			Date:       tx.UserData,
			Remark:     desc,
			Appid:      d.sc.commissionAppID,
			SignKey:    d.sc.commissionSignKey,
			CreateTime: tx.CreateAt.Unix(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "PrivateWithdraw %d %d %s %s failed: %v", tx.UserID, total, tx.UserData, desc, err)
			return
		}
		outsideOrderId = pwResp.GetOrderId()
		outsideTime = time.Unix(pwResp.GetCreateTime(), 0)

		log.InfoWithCtx(ctx, "Settlement POINTS-COMMISSION %d %d %s %s OK", tx.UserID, total, tx.UserData, desc)
	}

	if len(tx.FinishMessage) != 0 {
		go pushWithTTAssistant(tx.UserID, tx.FinishMessage)
	}

	return
}

func (d *pointsDelegate) isRetriableError(err error) bool {
	if e, ok := err.(protocol.ServerError); ok && e.Code() == status.ErrUserScoreNotEnough {
		return false
	}
	if e, ok := err.(protocol.ServerError); ok && e.Code() == status.ErrUserScoreOrderExist {
		return false
	}
	if e, ok := err.(commission.APIError); ok && e.Code() == commission.CodeDataExisted {
		return false
	}
	return true
}

func pushWithTTAssistant(uid uint32, content string) {
	// content is serialized PushMessage
	var pm exchangePB.PushMessage
	if err := json.Unmarshal([]byte(content), &pm); err != nil {
		log.Errorf("Failed to unmarshal %s into PushMessage: %+v", content, err)
		return
	}

	msg := new(apiPB.ImMsg)
	msg.ImType = &apiPB.ImType{
		SenderType:   uint32(apiPB.IM_SENDER_TYPE_IM_SENDER_NORMAL),
		ReceiverType: uint32(apiPB.IM_RECEIVER_TYPE_IM_RECEIVER_USER),
	}
	msg.FromUid = 10000 // TT语音助手
	msg.ToIdList = []uint32{uid}
	msg.ImContent = &apiPB.ImContent{}
	msg.Platform = apiPB.Platform_UNSPECIFIED
	if len(pm.Highlight) == 0 {
		msg.ImContent.TextNormal = &apiPB.ImTextNormal{
			Content: pm.Content,
		}
		msg.ImType.ContentType = uint32(apiPB.IM_CONTENT_TYPE_IM_CONTENT_TEXT)
	} else {
		msg.ImContent.TextHlUrl = &apiPB.ImTextWithHighlightUrl{
			Content:    pm.Content,
			Hightlight: pm.Highlight,
			Url:        pm.Url,
		}
		msg.ImType.ContentType = uint32(apiPB.IM_CONTENT_TYPE_IM_CONTENT_TEXT_WITH_HL_URL)
	}

	err := apiCenterClient.SendImMsg(context.Background(), uid, protocol.TT, []*apiPB.ImMsg{msg}, true)

	if err != nil {
		log.Errorf("pushWithTTAssistant %d %v failed %v", uid, msg, err)
	} else {
		log.Debugf("pushWithTTAssistant %d %v OK", uid, msg)
	}
}
