package server

import (
	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	"golang.52tt.com/clients/currency"
	userscore_go "golang.52tt.com/clients/userscore-go"
	"golang.52tt.com/clients/verifycode"
	"google.golang.org/grpc"
)

var (
	//userScoreClient  userscore.IClient
	userscoreGoClient *userscore_go.Client
	verifyCodeClient verifycode.IClient
	currencyClient   currency.IClient
	apiCenterClient  apicenter.IClient
)

func Init() {
	//userScoreClient = userscore.NewIClient()
	userscoreGoClient = userscore_go.NewClient(grpc.WithBlock(), grpc.WithInsecure())
	verifyCodeClient = verifycode.NewIClient()
	currencyClient = currency.NewIClient()
	apiCenterClient = apicenter.NewIClient()
}
