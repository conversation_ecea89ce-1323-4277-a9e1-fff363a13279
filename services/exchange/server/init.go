package server

import (
	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	"golang.52tt.com/clients/currency"
	"golang.52tt.com/clients/userscore"
	"golang.52tt.com/clients/verifycode"
)

var (
	userScoreClient  userscore.IClient
	verifyCodeClient verifycode.IClient
	currencyClient   currency.IClient
	apiCenterClient  apicenter.IClient
)

func Init() {
	userScoreClient = userscore.NewIClient()
	verifyCodeClient = verifycode.NewIClient()
	currencyClient = currency.NewIClient()
	apiCenterClient = apicenter.NewIClient()
}
