package server

import (
    "archive/zip"
    "context"
    "errors"
    "fmt"
    _ "github.com/go-sql-driver/mysql"
    "github.com/jmoiron/sqlx"
    "github.com/tealeg/xlsx"
    "gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
    "golang.52tt.com/clients/account"
    "golang.52tt.com/clients/channel"
    exchange "golang.52tt.com/clients/exchange"
    obsObjectGateway "golang.52tt.com/clients/obsgateway"
    userPresent "golang.52tt.com/clients/userpresent"
    "golang.52tt.com/pkg/config"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/protocol/common/status"
    Exchange "golang.52tt.com/protocol/services/exchange"
    "golang.52tt.com/protocol/services/userscore"
    pb "golang.52tt.com/protocol/services/userscore-mgr"
    "golang.52tt.com/services/userscore-mgr/model"
    "google.golang.org/grpc"
    "io"
    "os"
    "time"
)

const (
    FreezeStatusNone = 0
    FreezeStatusAll  = 1
    FreezeStatusPart = 2
)

type UserScoreMgr struct {
    userScoreStore   *model.Store
    userScoreRoStore *model.Store
    presentStore     *model.Store
    accountClient    *account.Client
    channelClient    *channel.Client
    presentCli       *userPresent.Client
    obsgatewayClient *obsObjectGateway.Client
    exchangeClient   *exchange.Client
    obsHost          string
    processTimer *timer.Timer
}

const timeStepSec = int64(60)

func (u UserScoreMgr) RunHistoryScoreAmount(ctx context.Context, req *pb.RunHistoryScoreAmountReq) (*pb.EmptyMsg, error) {
    resp := &pb.EmptyMsg{}

    if req.GetReplace() {
        for i := uint32(0); i < 100; i++ {
            err := u.userScoreStore.TruncateUserScoreHistoryAmount(ctx, i)
            if err != nil {
                log.ErrorWithCtx(ctx, "userScoreStore.TruncateUserScoreHistoryAmount err=%v", err)
                return resp, err
            }
            uidList, err := u.userScoreRoStore.GetCurrent1WUid(ctx, i)
            if err != nil {
                log.ErrorWithCtx(ctx, "userScoreRoStore.GetCurrent1WUid err=%v", err)
                return resp, err
            }
            for _, uid := range uidList {
                err = u.exchangeClient.SetScoreCanWithdraw(ctx, uid)
                if err != nil {
                    log.Errorf("exchangeClient.SetScoreCanWithdraw err=%v", err)
                    continue
                }
            }
        }
    }

    runFun := func(req *pb.RunHistoryScoreAmountReq) error {
        changeUidMap := make(map[uint32]bool)
        ctx := context.Background()
        for beginTime := req.GetBeginTime(); beginTime < req.GetEndTime(); beginTime += timeStepSec {
            amountList, err := u.userScoreRoStore.GetRangeTimeUserScoreAmount(ctx, beginTime, beginTime+timeStepSec, uint32(userscore.ScoreChangeReason_REASON_RECEIVE_PRESENT))
            if err != nil {
                log.ErrorWithCtx(ctx, "userScoreRoStore.GetRangeTimeUserScoreAmount err=%v", err)
                return err
            }

            for _, elem := range amountList {
                err = u.userScoreStore.AddUserScoreHistoryAmount(ctx, elem.Uid, elem.Amount)
                    if err != nil {
                        log.Errorf("userScoreStore.AddUserScoreHistoryAmount err=%v", err)
                        return err
                    }
                changeUidMap[elem.Uid] = true
            }
        }

        for uid, _ := range changeUidMap {
            amount, err := u.userScoreStore.GetUserScoreHistoryAmount(ctx, uid)
            if err != nil {
                log.Errorf("userScoreStore.GetUserScoreHistoryAmount err=%v", err)
                return err
            }
            if amount > 10000 {
                err := u.exchangeClient.SetScoreCanWithdraw(ctx, uid)
                if err != nil {
                    log.Errorf("exchangeClient.SetScoreCanWithdraw err=%v", err)
                    continue
                }
            }
        }

        return nil
    }

    if req.GetAsync() {
        go func() {
            err := runFun(req)
            if err != nil {
                log.Errorf("runFun err=%v", err)
            }
        }()
    } else {
        err := runFun(req)
        if err != nil {
            log.Errorf("runFun err=%v", err)
        }
        return resp, err
    }

    return resp, nil
}

func (u UserScoreMgr) GetScoreAmount(ctx context.Context, req *pb.UidReq) (*pb.GetScoreAmountResp, error) {
    resp := &pb.GetScoreAmountResp{}
    amount, err := u.userScoreRoStore.GetUserScoreHistoryAmount(ctx, req.GetUid())
    if err != nil {
        log.ErrorWithCtx(ctx, "userScoreRoStore.GetUserScoreHistoryAmount err=%v", err)
        return resp, err
    }
    resp.Amount = amount
    return resp, nil
}

const (
    maxUidLen          = 100
    oneExcelFileMaxLen = 500000
    bufSize            = 1024 * 1024 * 5
    appId              = "tt"
    scope              = "user-gift-score"
)

func NewUserScoreMgr(ctx context.Context, cfg *config.ServerConfig) (*UserScoreMgr, error) {
    parseLevel, err := log.ParseLevel(cfg.LogLevel)
    if err != nil {
        return nil, err
    }
    log.SetLevel(parseLevel)

    obsHost := cfg.Configer.DefaultString("obs_host", "")
    if len(obsHost) == 0 {
        log.Errorf("config obs_host is empty")
        return nil, errors.New("config obs_host is empty")
    }

    mysqlUserScoreReadonlyConfig := new(config.MysqlConfig)
    mysqlUserScoreReadonlyConfig.Read(cfg.Configer, "mysql_user_score_readonly_config")
    log.Infof("mysql_user_score_readonly_config connect %s", mysqlUserScoreReadonlyConfig.ConnectionString())
    userScoreRoConnect, err := sqlx.Connect("mysql", mysqlUserScoreReadonlyConfig.ConnectionString())
    if err != nil {
        log.Errorf("mysql_user_score_readonly_config connect err=%v", err)
        return nil, err
    }
    userScoreRoConnect.SetMaxOpenConns(mysqlUserScoreReadonlyConfig.MaxOpenConns)
    userScoreRoConnect.SetMaxIdleConns(mysqlUserScoreReadonlyConfig.MaxIdleConns)
    userScoreRoStore := model.NewStore(userScoreRoConnect)

    mysqlPresentReadonlyConfig := new(config.MysqlConfig)
    mysqlPresentReadonlyConfig.Read(cfg.Configer, "mysql_present_readonly_config")
    log.Infof("mysql_present_readonly_config connect %s", mysqlPresentReadonlyConfig.ConnectionString())
    presentConnect, err := sqlx.Connect("mysql", mysqlPresentReadonlyConfig.ConnectionString())
    if err != nil {
        log.Errorf("mysql_present_readonly_config connect err=%v", err)
        return nil, err
    }
    presentConnect.SetMaxOpenConns(mysqlUserScoreReadonlyConfig.MaxOpenConns)
    presentConnect.SetMaxIdleConns(mysqlUserScoreReadonlyConfig.MaxIdleConns)
    presentStore := model.NewStore(presentConnect)

    mysqlUserScoreConfig := new(config.MysqlConfig)
    mysqlUserScoreConfig.Read(cfg.Configer, "mysql_user_score_config")
    log.Infof("mysql_user_score_config connect %s", mysqlUserScoreConfig.ConnectionString())
    userScoreConnect, err := sqlx.Connect("mysql", mysqlUserScoreConfig.ConnectionString())
    if err != nil {
        log.Errorf("mysql_user_score_config connect err=%v", err)
        return nil, err
    }
    userScoreConnect.SetMaxOpenConns(mysqlUserScoreConfig.MaxOpenConns)
    userScoreConnect.SetMaxIdleConns(mysqlUserScoreConfig.MaxIdleConns)
    userScoreStore := model.NewStore(userScoreConnect)

    err = userScoreStore.CreateAllUserScoreExportTaskTable()
    if err != nil {
        log.Errorf("userScoreStore.CreateAllUserScoreExportTask err=%+v", err)
        return nil, err
    }

    err = userScoreStore.CreateUserScoreInventory()
    if err != nil {
        log.Errorf("userScoreStore.CreateUserScoreInventory err=%+v", err)
        return nil, err
    }

    for i := uint32(0); i < 100; i++ {
        err = userScoreStore.CreateUserScoreHistoryAmount(i)
        if err != nil {
            log.Errorf("userScoreStore.CreateUserScoreHistoryAmount err=%v", err)
            return nil, err
        }
    }

    accountClient, err := account.NewClient(grpc.WithInsecure(), grpc.WithBlock())
    if err != nil {
        return nil, err
    }

    channelClient := channel.NewClient(grpc.WithInsecure(), grpc.WithBlock())

    presentCli := userPresent.NewClient(grpc.WithInsecure(), grpc.WithBlock())

    obsgatewayClient, err := obsObjectGateway.NewClient(grpc.WithInsecure(), grpc.WithBlock())
    if err != nil {
        return nil, err
    }

    exchangeClient, err := exchange.NewClient(grpc.WithInsecure(), grpc.WithBlock())
    if err != nil {
        return nil, err
    }

    pTimer, err := timer.NewTimerD(ctx, "userscore-mgr")
    if err != nil {
        return nil, err
    }

    svr := &UserScoreMgr{userScoreRoStore: userScoreRoStore, presentStore: presentStore, userScoreStore: userScoreStore,
        accountClient: accountClient, channelClient: channelClient, presentCli: presentCli, obsgatewayClient: obsgatewayClient,
        obsHost: obsHost, exchangeClient: exchangeClient, processTimer: pTimer}

    //c := cron.New()
    ////每个月1号1点生成上个月的仓库汇总
    //err = c.AddFunc("0 0 1 1 * *", func() {
    //    small := grpclb.GetEtcdProcess().IsSmallProcess()
    //    if !small {
    //        log.Infoln("not small")
    //        return
    //    }
    //    log.Infoln("is small")
    //
    //    req := &pb.GenerateInventoryReq{
    //        InventoryTime: time.Now().Unix() - 86400,
    //    }
    //    _, err := svr.GenerateInventory(context.Background(), req)
    //    if err != nil {
    //        log.Errorf("GenerateInventory err=%v", err)
    //    }
    //})
    //if err != nil {
    //    log.Errorf("AddFunc err=%v", err)
    //    return nil, err
    //}

    //每天0点跑昨天的积分累加
    //err = c.AddFunc("0 0 0 * * *", func() {
    //    small := grpclb.GetEtcdProcess().IsSmallProcess()
    //    if !small {
    //        log.Infoln("not small")
    //        return
    //    }
    //    log.Infoln("is small")
    //
    //    tNow := time.Now()
    //    zeroTime := time.Date(tNow.Year(), tNow.Month(), tNow.Day(), 0, 0, 0, 0, time.Local)
    //    yesterdayZeroTime := zeroTime.AddDate(0, 0, -1)
    //
    //    req := &pb.RunHistoryScoreAmountReq{
    //        BeginTime: yesterdayZeroTime.Unix(),
    //        EndTime:   zeroTime.Unix(),
    //        Replace:   false,
    //        Async:     false,
    //    }
    //    _, err = svr.RunHistoryScoreAmount(ctx, req)
    //    if err != nil {
    //        log.Errorf("RunHistoryScoreAmount err=%v", err)
    //    }
    //})
    //
    //c.Start()

    //每天0点跑昨天的积分累加
    err = svr.processTimer.AddTask("0 0 0 * * *", "GenerateInventory", timer.BuildFromLambda(func(ctx context.Context) {
        req := &pb.GenerateInventoryReq{
            InventoryTime: time.Now().Unix() - 86400,
        }
        _, err := svr.GenerateInventory(context.Background(), req)
        if err != nil {
            log.Errorf("GenerateInventory err=%v", err)
        }
    }))
    if err != nil {
        log.Errorf("AddFunc err=%v", err)
        return nil, err
    }

    //每个月1号1点生成上个月的仓库汇总
    err = svr.processTimer.AddTask("0 0 1 1 * *", "GenerateInventory", timer.BuildFromLambda(func(ctx context.Context) {
        tNow := time.Now()
        zeroTime := time.Date(tNow.Year(), tNow.Month(), tNow.Day(), 0, 0, 0, 0, time.Local)
        yesterdayZeroTime := zeroTime.AddDate(0, 0, -1)

        req := &pb.RunHistoryScoreAmountReq{
            BeginTime: yesterdayZeroTime.Unix(),
            EndTime:   zeroTime.Unix(),
            Replace:   false,
            Async:     false,
        }
        _, err = svr.RunHistoryScoreAmount(ctx, req)
        if err != nil {
            log.Errorf("RunHistoryScoreAmount err=%v", err)
        }
    }))
    if err != nil {
        log.Errorf("AddFunc err=%v", err)
        return nil, err
    }

    svr.processTimer.Start()

    go svr.CreateNextMonthHistoryTable()

    return svr, nil
}

func (u UserScoreMgr) ShutDown() {
    u.userScoreRoStore.Close()
    u.userScoreStore.Close()
    u.presentStore.Close()
}

func (u UserScoreMgr) GetUserScoreByUidList(ctx context.Context, req *pb.UidListReq) (*pb.UserScoreList, error) {
    resp := &pb.UserScoreList{}

    if len(req.GetUidList()) == 0 {
        return resp, nil
    }

    if len(req.GetUidList()) > maxUidLen {
        return resp, protocol.NewServerError(status.ErrUserscoreMgrParamMaxLen)
    }

    dataMap, err := u.userScoreRoStore.GetAllUserScoreDataMemByUidList(ctx, req.GetUidList(), req.GetBeginTime(), req.GetEndTime())
    if err != nil {
        log.Errorf("userScoreRoStore.GetAllUserScoreDataMemByUidList err=%+v", err)
        return resp, err
    }

    userInfoMap, serverError := u.accountClient.BatGetUserByUid(ctx, req.GetUidList()...)
    if serverError != nil {
        log.ErrorWithCtx(ctx, "accountClient.BatGetUserByUid err=%+v", serverError)
        return resp, serverError
    }

    tNow := time.Now()

    for _, elem := range dataMap {
        data := &pb.UserScoreData{
            SimpleData: &pb.UserScoreSimpleData{
                Uid:                      elem.Uid,
                LastRemainScore:          elem.LastRemainScore,
                LastRemainTbeanOnlyScore: elem.LastRemainScoreTbeanOnly,
                SumScore:   elem.SumScore,
                SumScore_2: elem.SumScore2,
                SettlementScore:          elem.SettlementScore + elem.SettlementScore2,
                GuildExchangeScore:       elem.GuildExchangeScore + elem.GuildExchangeScore2,
                FailedRollScore:          elem.FailedRollScore + elem.FailedRollScore2,
                ExchangeTbeanScore:       elem.ExchangeTbeanScore,
                OfficialRewardScore:      elem.OfficialRewardScore + elem.OfficialRewardScore2,
                OfficialRecycleScore:     elem.OfficialRecycleScore + elem.OfficialRecycleScore2,
                WerewolfScore:            elem.WerewolfScore,
                ExchangeTbeanScore_2:     elem.ExchangeTbeanScore2,
                RemainScore: elem.LastRemainScore + elem.SumScore + elem.WerewolfScore - elem.SettlementScore - elem.GuildExchangeScore + elem.FailedRollScore -
                    elem.ExchangeTbeanScore + elem.OfficialRewardScore - elem.OfficialRecycleScore,
                RemainTbean_2: elem.LastRemainScoreTbeanOnly + elem.SumScore2 - elem.SettlementScore2 - elem.GuildExchangeScore2 + elem.FailedRollScore2 -
                    elem.ExchangeTbeanScore2 + elem.OfficialRewardScore2 - elem.OfficialRecycleScore2,
            },
        }

        userInfo := userInfoMap[elem.Uid]
        if userInfo != nil {
            data.Ttid = userInfo.Alias
            data.Nickname = userInfo.Nickname
        }

        data.FreezeStatus = FreezeStatusNone

        list, err := u.exchangeClient.GetScoreFreezeList(ctx, &Exchange.GetScoreFreezeListReq{
            Uid:                   elem.Uid,
            UnfreezeTimeBeginTime: 0,
            UnfreezeTimeEndTime:   1, //找到没解冻的
            Offset:                0,
            Limit:                 10,
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "GetScoreFreezeList err=%v", err)
            return resp, err
        }

        log.InfoWithCtx(ctx, "exchangeClient.GetScoreFreezeList list=%+v", list.GetList())

        if len(list.GetList()) > 0 {
            data.FreezeStatus = FreezeStatusAll
        } else {
            plist, err := u.exchangeClient.GetScorePartFreezeList(ctx, &Exchange.GetScorePartFreezeListReq{
                Uid:               elem.Uid,
                UnfreezeBeginTime: 0,
                UnfreezeEndTime:   1,
                ScoreType:         0,
                Offset:            0,
                Limit:             10,
            })
            if err != nil {
                log.ErrorWithCtx(ctx, "GetScorePartFreezeList err=%v", err)
                return resp, err
            }
            if len(plist.GetList()) > 0 {
                data.FreezeStatus = FreezeStatusPart
            } else {
                plist, err = u.exchangeClient.GetScorePartFreezeList(ctx, &Exchange.GetScorePartFreezeListReq{
                    Uid:               elem.Uid,
                    UnfreezeBeginTime: tNow.Unix(),
                    ScoreType:         0,
                    Offset:            0,
                    Limit:             10,
                })
                if len(plist.GetList()) > 0 {
                    data.FreezeStatus = FreezeStatusPart
                }
            }
        }

        resp.List = append(resp.List, data)
    }

    return resp, nil
}

func (u UserScoreMgr) GetUserPresentByUidList(ctx context.Context, req *pb.UidReq) (*pb.UserPresentList, error) {
    resp := &pb.UserPresentList{}

    list, err := u.presentStore.GetPresentHistoryList(ctx, req.GetUid(), req.GetBeginTime(), req.GetEndTime(), req.GetOffset(), req.GetLimit())
    if err != nil {
        log.ErrorWithCtx(ctx, "presentStore.GetPresentHistoryList err=%+v", err)
        return resp, protocol.NewServerError(status.ErrUserscoreMgrDbErr)
    }

    log.InfoWithCtx(ctx, "presentStore.GetPresentHistoryList list=%+v", *list)

    channelIdList := make([]uint32, 0)
    uidList := make([]uint32, 0)
    for _, elem := range *list {
        channelIdList = append(channelIdList, elem.ChannelId)
        uidList = append(uidList, elem.ToUid)
        uidList = append(uidList, elem.FromUid)
    }

    channelInfoMap, serverError := u.channelClient.BatchGetChannelSimpleInfo(ctx, 0, channelIdList)
    if serverError != nil {
        log.ErrorWithCtx(ctx, "channelClient.BatchGetChannelSimpleInfo err=%+v", serverError)
        return resp, serverError
    }

    userInfoMap, serverError := u.accountClient.BatGetUserByUid(ctx, uidList...)
    if serverError != nil {
        log.ErrorWithCtx(ctx, "accountClient.BatGetUserByUid err=%+v", serverError)
        return resp, serverError
    }

    for _, elem := range *list {
        finallyScore, err := u.userScoreRoStore.GetFinallyScoreByOrderId(elem.OrderId, elem.CreateTime)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetFinallyScoreByOrderId err=%+v", err)
            return resp, protocol.NewServerError(status.ErrUserscoreMgrDbErr)
        }
        if finallyScore == 0 {
            currentTime := time.Now()
            monthFirstDayTime := time.Date(currentTime.Year(), currentTime.Month(), 1, 0, 0, 0, 0, time.Local)
            if elem.CreateTime < uint32(monthFirstDayTime.Unix()) {
                createTimeT := time.Unix(int64(elem.CreateTime), 0)
                createTimeT = createTimeT.AddDate(0, 1, 0)
                finallyScore, err = u.userScoreRoStore.GetFinallyScoreByOrderId(elem.OrderId, uint32(createTimeT.Unix()))
                if err != nil {
                    log.ErrorWithCtx(ctx, "GetFinallyScoreByOrderId err=%+v", err)
                    return resp, protocol.NewServerError(status.ErrUserscoreMgrDbErr)
                }
            }
        }

        data := pb.UserPresentData{
            ToUid:        elem.ToUid,
            CreateTime:   elem.CreateTime,
            ChangeScore:  int32(elem.TotalScore),
            FinallyScore: finallyScore,
            ItemId:       elem.ItemId,
            ItemCount:    elem.ItemCount,
            ScoreType:    elem.ScoreType,
        }

        presentInfo, err := u.presentCli.GetPresentConfigById(ctx, elem.ItemId)
        if err != nil {
            log.ErrorWithCtx(ctx, "presentCli.GetPresentConfigById err=%+v", err)
            return resp, err
        }

        data.ItemName = presentInfo.GetItemConfig().GetName()
        data.ItemPrice = presentInfo.GetItemConfig().GetPrice()

        toUserInfo := userInfoMap[elem.ToUid]
        if toUserInfo != nil {
            data.ToTtid = toUserInfo.Alias
            data.ToNickname = toUserInfo.Nickname
        }
        fromUserInfo := userInfoMap[elem.FromUid]
        if fromUserInfo != nil {
            data.FromTtid = fromUserInfo.Alias
            data.FromNickname = fromUserInfo.Nickname
        }
        channelInfo := channelInfoMap[elem.ChannelId]
        if channelInfo != nil {
            data.DisplayId = channelInfo.GetDisplayId()
            data.ChannelName = channelInfo.GetName()
            data.ChannelViewId = channelInfo.GetChannelViewId()
        }

        resp.List = append(resp.List, &data)
    }

    count, err := u.presentStore.GetPresentHistoryCount(ctx, req.GetUid(), req.GetBeginTime(), req.GetEndTime())
    if err != nil {
        log.ErrorWithCtx(ctx, "presentStore.GetPresentHistoryCount err=%+v", err)
        return resp, protocol.NewServerError(status.ErrUserscoreMgrDbErr)
    }
    resp.Total = count

    return resp, nil
}

func (u UserScoreMgr) CreateExportAllUserScoreTask(ctx context.Context, req *pb.TimeRangeReq) (*pb.EmptyMsg, error) {
    resp := &pb.EmptyMsg{}

    lastTaskDataList, err := u.userScoreStore.GetLastTaskData()
    if err != nil {
        log.ErrorWithCtx(ctx, "userScoreStore.GetLastTaskData err=%+v", err)
        return resp, protocol.NewServerError(status.ErrUserscoreMgrDbErr)
    }

    if len(*lastTaskDataList) > 0 {
        lastTaskData := (*lastTaskDataList)[0]
        if lastTaskData.Percent < 100 && len(lastTaskData.ErrData) == 0 {
            return resp, protocol.NewServerError(status.ErrUserscoreMgrTaskWait)
        }
    }

    id, err := u.userScoreStore.InsertTask(req.GetBeginTime(), req.GetEndTime(), req.GetType())
    if err != nil {
        log.ErrorWithCtx(ctx, "userScoreStore.InsertTask err=%+v", err)
        return resp, protocol.NewServerError(status.ErrUserscoreMgrDbErr)
    }

    go u.dumpData(id, req.GetBeginTime(), req.GetEndTime(), req.GetType())

    return resp, nil
}

func (u UserScoreMgr) createNewFile() (*xlsx.File, *xlsx.Sheet, error) {
    file := xlsx.NewFile()
    sheet, err := file.AddSheet("礼物积分记录")
    if err != nil {
        log.Errorf("file.AddSheet err=%+v", err)
        return nil, nil, err
    }
    title := sheet.AddRow()
	titleList := &[]string{"UID", "上月剩余一类积分", "上月剩余二类积分", "一类礼物积分", "二类礼物积分", "狼人杀积分", "总积分", "已自主提现积分", "已汇总对公积分", "提现失败返还积分", "已兑换一类积分", "已兑换二类积分",
        "官方发放积分", "官方回收积分", "剩余一类积分", "剩余二类积分", "剩余可提现金额"}
    title.WriteSlice(titleList, -1)
    return file, sheet, nil
}

func (u UserScoreMgr) saveTempDataToSheet(tempDataMap map[uint32]*model.UserScoreData, sheet *xlsx.Sheet, sumData *model.UserScoreData, sumCanSettlementScore *int64) {
    for _, elem := range tempDataMap {
        row := sheet.AddRow()
        row.AddCell().SetInt(int(elem.Uid))
		row.AddCell().SetInt64(elem.LastRemainScore) //上月剩余一类积分
        sumData.LastRemainScore += elem.LastRemainScore
		row.AddCell().SetInt64(elem.LastRemainScoreTbeanOnly) //上月剩余二类积分
        sumData.LastRemainScoreTbeanOnly += elem.LastRemainScoreTbeanOnly
		row.AddCell().SetInt64(elem.SumScore) //一类礼物积分
        sumData.SumScore += elem.SumScore
		row.AddCell().SetInt64(elem.SumScore2) //二类礼物积分
        sumData.SumScore2 += elem.SumScore2
        row.AddCell().SetInt64(elem.WerewolfScore) //狼人杀积分
        sumData.WerewolfScore += elem.WerewolfScore
        row.AddCell().SetInt64(elem.LastRemainScore + elem.LastRemainScoreTbeanOnly + elem.SumScore + elem.SumScore2 + elem.WerewolfScore)         //总积分
        row.AddCell().SetInt64(elem.SettlementScore)                                                                                               //已自主提现积分
        sumData.SettlementScore += elem.SettlementScore
        row.AddCell().SetInt64(elem.GuildExchangeScore) //已汇总对公积分
        sumData.GuildExchangeScore += elem.GuildExchangeScore
        row.AddCell().SetInt64(elem.FailedRollScore) //提现失败返还积分
        sumData.FailedRollScore += elem.FailedRollScore
        row.AddCell().SetInt64(elem.ExchangeTbeanScore) //已兑换一类积分
        sumData.ExchangeTbeanScore += elem.ExchangeTbeanScore
        row.AddCell().SetInt64(elem.ExchangeTbeanScore2) //已兑换二类积分
        sumData.ExchangeTbeanScore2 += elem.ExchangeTbeanScore2
        row.AddCell().SetInt64(elem.OfficialRewardScore) //官方发放积分
        sumData.OfficialRewardScore += elem.OfficialRewardScore
        row.AddCell().SetInt64(elem.OfficialRecycleScore) //官方回收积分
        sumData.OfficialRecycleScore += elem.OfficialRecycleScore

        //剩余积分
        remainScore := elem.LastRemainScore + elem.SumScore + elem.WerewolfScore - elem.SettlementScore - elem.GuildExchangeScore - elem.ExchangeTbeanScore - elem.OfficialRecycleScore + elem.FailedRollScore + elem.OfficialRewardScore
        row.AddCell().SetInt64(remainScore)
        remainScore2 := elem.LastRemainScoreTbeanOnly + elem.SumScore2 - elem.SettlementScore2 - elem.GuildExchangeScore2 - elem.ExchangeTbeanScore2 - elem.OfficialRecycleScore2 + elem.FailedRollScore2 + elem.OfficialRewardScore2
        row.AddCell().SetInt64(remainScore2)
        //剩余可提现金额
        if remainScore >= 10000 {
            row.AddCell().SetFloat(float64(remainScore) / 100.0)
            *sumCanSettlementScore += remainScore
        } else {
            row.AddCell().SetInt(0)
        }
    }
}

func (u UserScoreMgr) saveFile(file *xlsx.File, exportType uint32, fileIndex uint32, beginT time.Time, endT time.Time, isSum bool) string {
    var fileName string
    switch exportType {
    case model.ExportAllUserScoreType:
        fileName = fmt.Sprintf("%d%02d%02d-%02d%02d全部礼物积分查询", beginT.Year(), beginT.Month(), beginT.Day(),
            endT.Month(), endT.Day())
    case model.ExportSettlementScoreType:
        fileName = fmt.Sprintf("%d%02d%02d-%02d%02d提现汇总积分大于0礼物积分查询", beginT.Year(), beginT.Month(), beginT.Day(),
            endT.Month(), endT.Day())
    default:
        log.Errorf("exportType not support=%d", exportType)
        return ""
    }

    if isSum {
        fileName += "（总计）"
    } else {
        fileName = fmt.Sprintf("%s_%d", fileName, fileIndex)
    }
    fileName += ".xlsx"

    err := file.Save(fileName)
    log.Infof("save file=%s", fileName)
    if err != nil {
        log.Errorf("file.Save %s, err=%+v", fileName, err)
        return ""
    }
    return fileName
}

func (u UserScoreMgr) dumpData(id int64, beginTime uint32, endTime uint32, exportType uint32) {
    beginT := time.Unix(int64(beginTime), 0)
    endT := time.Unix(int64(endTime), 0)
    fileIndex := uint32(1)
    var fileList []string

    file, sheet, err := u.createNewFile()
    if err != nil {
        errLog := fmt.Sprintf("createNewFile err=%+v", err)
        log.Errorf(errLog)
        err := u.userScoreStore.UpdateTaskErrData(id, errLog)
        if err != nil {
            log.Errorf("UpdateTaskErrData err=%+v", err)
        }
        return
    }

    var sumData model.UserScoreData
    var sumCanSettlementScore int64

    dataMap, err := u.userScoreRoStore.GetAllUserScoreDataMem(context.Background(), id, beginTime, endTime, exportType)
    if err != nil {
        errLog := fmt.Sprintf("GetAllUserScoreData err=%+v", err)
        log.Errorf(errLog)
        err := u.userScoreStore.UpdateTaskErrData(id, errLog)
        if err != nil {
            log.Errorf("UpdateTaskErrData err=%+v", err)
        }
        return
    }

    count := uint32(0)

    uidList := make([]uint32, 0)
    dataMapLen := len(dataMap)
    lastPercent := uint32(0)
    tempDataMap := map[uint32]*model.UserScoreData{}

    for uid, elem := range dataMap {
        uidList = append(uidList, uid)
        tempDataMap[uid] = &model.UserScoreData{
            Uid:                      uid,
            LastRemainScore:          elem.LastRemainScore,
            LastRemainScoreTbeanOnly: elem.LastRemainScoreTbeanOnly,
        }
        if len(uidList) >= 1000 {
            err = u.userScoreRoStore.GetUidListData(uidList, beginTime, endTime, tempDataMap)
            if err != nil {
                log.Errorf("getUidListData err=%+v", err)
            }

            count += uint32(len(uidList))
            percent := count * 99 / uint32(dataMapLen) //导到内存只是99%，还有生成文件和上传文件
            log.Debugf("percent=%d, finish=%d, total=%d", percent, count, dataMapLen)
            if lastPercent != percent {
                err := u.userScoreStore.UpdateTaskPercent(id, percent)
                if err != nil {
                    log.Errorf("UpdateTaskPercent err=%+v", err)
                    return
                }
                lastPercent = percent
            }

            uidList = []uint32{}

            if len(tempDataMap) >= oneExcelFileMaxLen {
                u.saveTempDataToSheet(tempDataMap, sheet, &sumData, &sumCanSettlementScore)
                fileName := u.saveFile(file, exportType, fileIndex, beginT, endT, false)

                fileList = append(fileList, fileName)

                file, sheet, err = u.createNewFile()
                if err != nil {
                    errLog := fmt.Sprintf("createNewFile err=%+v", err)
                    log.Errorf(errLog)
                    err := u.userScoreStore.UpdateTaskErrData(id, errLog)
                    if err != nil {
                        log.Errorf("UpdateTaskErrData err=%+v", err)
                        return
                    }
                    return
                }
                fileIndex++

                tempDataMap = map[uint32]*model.UserScoreData{}
            }
        }
    }

    err = u.userScoreRoStore.GetUidListData(uidList, beginTime, endTime, tempDataMap)
    if err != nil {
        log.Errorf("getUidListData err=%+v", err)
        return
    }

    u.saveTempDataToSheet(tempDataMap, sheet, &sumData, &sumCanSettlementScore)
    fileName := u.saveFile(file, exportType, fileIndex, beginT, endT, false)
    fileList = append(fileList, fileName)

    count += uint32(len(uidList))

    percent := uint32(0)
    if dataMapLen > 0 {
        percent = count * 99 / uint32(dataMapLen) //导到内存只是99%，还有生成文件和上传文件
    }

    log.Debugf("percent=%d, finish=%d, total=%d", percent, count, dataMapLen)
    if lastPercent != percent {
        err := u.userScoreStore.UpdateTaskPercent(id, percent)
        if err != nil {
            log.Errorf("UpdateTaskPercent err=%+v", err)
            return
        }
        lastPercent = percent //nolint
    }

    //sum
    file, sheet, err = u.createNewFile()
    if err != nil {
        errLog := fmt.Sprintf("createNewFile err=%+v", err)
        log.Errorf(errLog)
        err := u.userScoreStore.UpdateTaskErrData(id, errLog)
        if err != nil {
            log.Errorf("UpdateTaskErrData err=%+v", err)
        }
        return
    }

    sumRow := sheet.AddRow()
    sumRow.AddCell().SetInt(0)
    sumRow.AddCell().SetInt64(sumData.LastRemainScore) //上月剩余积分
    sumRow.AddCell().SetInt64(sumData.LastRemainScoreTbeanOnly)
    sumRow.AddCell().SetInt64(sumData.SumScore) //礼物积分
    sumRow.AddCell().SetInt64(sumData.SumScore2)
    sumRow.AddCell().SetInt64(sumData.WerewolfScore)                                                                                                             //狼人杀积分
    sumRow.AddCell().SetInt64(sumData.LastRemainScore + sumData.LastRemainScoreTbeanOnly + sumData.SumScore + sumData.SumScore2 + sumData.WerewolfScore)         //总积分
    sumRow.AddCell().SetInt64(sumData.SettlementScore)                                                                                                           //已提现积分
    sumRow.AddCell().SetInt64(sumData.GuildExchangeScore)                                                                                                        //已汇总对公积分
    sumRow.AddCell().SetInt64(sumData.FailedRollScore)                                                                                                           //提现失败返还积分
	sumRow.AddCell().SetInt64(sumData.ExchangeTbeanScore)
	sumRow.AddCell().SetInt64(sumData.ExchangeTbeanScore2)                                                                                                       //已兑换二类积分//已兑换积分
    sumRow.AddCell().SetInt64(sumData.OfficialRewardScore)                                                                                                       //官方发放积分
    sumRow.AddCell().SetInt64(sumData.OfficialRecycleScore)                                                                                                      //官方回收积分
    //剩余积分
	remainScore := sumData.LastRemainScore + sumData.SumScore + sumData.WerewolfScore - sumData.SettlementScore - sumData.GuildExchangeScore - sumData.ExchangeTbeanScore - sumData.OfficialRecycleScore + sumData.FailedRollScore + sumData.OfficialRewardScore
	sumRow.AddCell().SetInt64(remainScore)
	remainScore2 := sumData.LastRemainScoreTbeanOnly + sumData.SumScore2 - sumData.SettlementScore2 - sumData.GuildExchangeScore2 - sumData.ExchangeTbeanScore2 - sumData.OfficialRecycleScore2 + sumData.FailedRollScore2 + sumData.OfficialRewardScore2
	sumRow.AddCell().SetInt64(remainScore2)
    //剩余可提现金额
    sumRow.AddCell().SetFloat(float64(sumCanSettlementScore) / 100.0)

    fileName = u.saveFile(file, exportType, fileIndex, beginT, endT, true)
    fileList = append(fileList, fileName)

    zipFileName := fmt.Sprintf("%d.zip", time.Now().Unix())
    archive, err := os.Create(zipFileName)
    if err != nil {
        errLog := fmt.Sprintf("os.Create err=%+v", err)
        log.Errorf(errLog)
        err := u.userScoreStore.UpdateTaskErrData(id, errLog)
        if err != nil {
            log.Errorf("UpdateTaskErrData err=%+v", err)
        }
        return
    }

    defer archive.Close()

    zipWriter := zip.NewWriter(archive)
    for _, fileElem := range fileList {
        excelFile, err := os.Open(fileElem)
        if err != nil {
            errLog := fmt.Sprintf("os.Open %s err=%+v", fileElem, err)
            log.Errorf(errLog)
            err := u.userScoreStore.UpdateTaskErrData(id, errLog)
            if err != nil {
                log.Errorf("UpdateTaskErrData err=%+v", err)
            }
            return
        }
        zipFile, err := zipWriter.Create(fileElem)
        if err != nil {
            errLog := fmt.Sprintf("zipWriter.Create %s err=%+v", fileElem, err)
            log.Errorf(errLog)
            err := u.userScoreStore.UpdateTaskErrData(id, errLog)
            if err != nil {
                log.Errorf("UpdateTaskErrData err=%+v", err)
            }
            return
        }

        _, err = io.Copy(zipFile, excelFile)
        if err != nil {
            errLog := fmt.Sprintf("io.Copy err=%+v", err)
            log.Errorf(errLog)
            err := u.userScoreStore.UpdateTaskErrData(id, errLog)
            if err != nil {
                log.Errorf("UpdateTaskErrData err=%+v", err)
            }
            return
        }

        excelFile.Close()

        err = os.Remove(fileElem)
        if err != nil {
            errLog := fmt.Sprintf("io.Remove %s err=%+v", fileElem, err)
            log.Errorf(errLog)
            err := u.userScoreStore.UpdateTaskErrData(id, errLog)
            if err != nil {
                log.Errorf("UpdateTaskErrData err=%+v", err)
            }
        }
    }
    zipWriter.Close()

    _, err = archive.Seek(0, 0)
    if err != nil {
        log.Errorf("archive.Seek err=%+v", err)
        return
    }

    key := fmt.Sprintf("userscorealldata_%d", time.Now().Unix())

    contentType := "application/x-zip-compressed"
    var opts []obsObjectGateway.Option
    opts = append(opts, obsObjectGateway.WithKey(key))
    opts = append(opts, obsObjectGateway.WithContentType(contentType))
    _, uploadId, _, srverr := u.obsgatewayClient.InitMultipartUpload(context.Background(), appId, scope, opts...)
    if srverr != nil {
        log.Errorf("obsgatewayClient.InitMultipartUpload err=%+v", srverr)
        return
    }

    buf := make([]byte, bufSize)
    partNum := int32(1)
    etags := make([]string, 0)
    partnums := make([]int32, 0)
    for {
        readLen, err := archive.Read(buf)
        if err != nil {
            log.Warnf("archive.Read err=%+v", err)
            break
        }

        if readLen < bufSize {
            buf = buf[:readLen]
        }

        etag, serverError := u.obsgatewayClient.UploadPart(context.Background(), appId, scope, key, uploadId, partNum, buf)
        if serverError != nil {
            log.Errorf("obsgatewayClient.UploadPart err=%+v", serverError)
            return
        }

        log.Debugf("partNum=%d", partNum)

        etags = append(etags, etag)
        partnums = append(partnums, partNum)

        if readLen < bufSize {
            break
        }
        partNum++
    }

    _, serverError := u.obsgatewayClient.CompleteMultipartUpload(context.Background(), appId, scope, key, uploadId, partnums, etags, contentType, "")
    if serverError != nil {
        log.Errorf("obsgatewayClient.CompleteMultipartUpload err=%+v", serverError)
        return
    }

    err = u.userScoreStore.UpdateTaskObsKey(id, key)
    if err != nil {
        log.Errorf("userScoreStore.UpdateTaskObsKey err=%+v", err)
        return
    }

    archive.Close()

    err = os.Remove(zipFileName)
    if err != nil {
        log.Errorf("os.Remove %s err=%+v", zipFileName, err)
        return
    }

    err = u.userScoreStore.UpdateTaskPercent(id, 100)
    if err != nil {
        log.Errorf("UpdateTaskErrData err=%+v", err)
    }
}

func (u UserScoreMgr) GetExportAllUserScoreTaskList(ctx context.Context, req *pb.OffsetReq) (*pb.ExportAllUserScoreTaskDataList, error) {
    resp := &pb.ExportAllUserScoreTaskDataList{}

    total, err := u.userScoreRoStore.GetTaskCount()
    if err != nil {
        log.ErrorWithCtx(ctx, "userScoreRoStore.GetTaskCount err=%+v", err)
        return resp, protocol.NewServerError(status.ErrUserscoreMgrDbErr)
    }
    resp.Total = total

    list, err := u.userScoreRoStore.GetTaskList(req.GetOffset(), req.GetLimit())
    if err != nil {
        log.ErrorWithCtx(ctx, "userScoreRoStore.GetTaskList err=%+v", err)
        return resp, protocol.NewServerError(status.ErrUserscoreMgrDbErr)
    }
    for _, elem := range *list {
        data := &pb.ExportAllUserScoreTaskData{
            Id:         elem.Id,
            BeginTime:  elem.BeginTime,
            EndTime:    elem.EndTime,
            Type:       uint32(elem.Type),
            Percent:    uint32(elem.Percent),
            CreateTime: elem.CreateTime,
            ErrData:    elem.ErrData,
        }
        resp.List = append(resp.List, data)
    }
    return resp, nil
}

func (u UserScoreMgr) GetTempZipUrl(ctx context.Context, req *pb.IdReq) (*pb.TempZipUrlResp, error) {
    resp := &pb.TempZipUrlResp{}
    //claims := make([]*obs_object_gateway.TokenClaims, 0, 1)
    //claims = append(claims, &obs_object_gateway.TokenClaims{
    //    Claims: &obs_object_gateway.TokenClaims_DownloadClaims{
    //        DownloadClaims: &obs_object_gateway.DownloadTokenClaims{
    //            App:        appId,
    //            Scope:      scope,
    //            Expiration: 600,
    //        },
    //    },
    //})

    taskData, err2 := u.userScoreRoStore.GetTaskData(req.GetId())
    if err2 != nil {
        log.Errorf("userScoreRoStore.GetTaskData err=%+v", err2)
        return resp, protocol.NewServerError(status.ErrUserscoreMgrDbErr)
    }

    var claims []interface{}
    claims = append(claims, &obsObjectGateway.DownloadTokenReq{
        App:        appId,
        Scope:      scope,
        Key:        taskData.ObsKey,
        Expiration: 600,
    })

    tokenResp, err := u.obsgatewayClient.ClaimToken(context.Background(), claims)

    if err != nil {
        log.Errorf("obsgatewayClient.ClaimToken err=%+v", err)
        return resp, err
    }

    url := fmt.Sprintf("%s%s/%s/%s?token=%s", u.obsHost, appId, scope, taskData.ObsKey, tokenResp[0])
    log.Infof("url=%s", url)
    resp.TempZipUrl = url
    return resp, nil
}

func (u UserScoreMgr) GetInventory(ctx context.Context, req *pb.GetInventoryReq) (*pb.GetInventoryResp, error) {
    resp := &pb.GetInventoryResp{}
    inventory, err := u.userScoreRoStore.GetUserScoreInventory(ctx, req.GetYearMonth())
    if err != nil {
        log.ErrorWithCtx(ctx, "userScoreRoStore.GetUserScoreInventory err=%v", err)
        return resp, err
    }
    resp.BeginShortcut = inventory.BeginShortcut
    resp.Increase = inventory.Increase
    resp.Decrease = inventory.Decrease
    resp.EndShortcut = inventory.EndShortcut

    return resp, nil
}

func (u UserScoreMgr) GenerateInventory(ctx context.Context, req *pb.GenerateInventoryReq) (*pb.EmptyMsg, error) {
    resp := &pb.EmptyMsg{}
    iTime := time.Unix(req.GetInventoryTime(), 0)
    endShortcut, err := u.userScoreRoStore.GetMonthSnapScoreSum(ctx, iTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "userScoreRoStore.GetMonthSnapScoreSum err=%v", err)
        return resp, err
    }
    lastMonthTime := time.Date(iTime.Year(), iTime.Month()-1, 1, 0, 0, 0, 0, time.Local)
    beginShortcut, err := u.userScoreRoStore.GetMonthSnapScoreSum(ctx, lastMonthTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "userScoreRoStore.GetMonthSnapScoreSum err=%v", err)
        return resp, err
    }

    incrScoreSum, err := u.userScoreRoStore.GetMonthIncrScoreSum(ctx, iTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "userScoreRoStore.GetMonthIncrScoreSum err=%v", err)
        return resp, err
    }

    decrScoreSum, err := u.userScoreRoStore.GetMonthDecrScoreSum(ctx, iTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "userScoreRoStore.GetMonthDecrScoreSum err=%v", err)
        return resp, err
    }

    monthTime := uint32(iTime.Year())*100 + uint32(iTime.Month())
    err = u.userScoreStore.ReplaceUserScoreInventory(ctx, monthTime, beginShortcut, incrScoreSum, decrScoreSum, endShortcut)
    if err != nil {
        log.ErrorWithCtx(ctx, "userScoreStore.ReplaceUserScoreInventory err=%v", err)
        return resp, err
    }
    return resp, nil
}

func (u UserScoreMgr) CreateNextMonthHistoryTable() {
    for {
        err := u.userScoreStore.CreateNextMonthHistory()
        if err != nil {
            log.Errorf("userScoreStore.CreateNextMonthHistory err=%v", err)
        }
        time.Sleep(time.Hour * 24)
    }
}