{"mysql": {"host": "medalgo-mysql.database.svc.cluster.local", "port": 3306, "database": "appsvr", "charset": "utf8", "user_name": "godman", "password": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "redis": {"host": "redis-test-tc-bj-tt-go-01.database.svc.cluster.local", "port": 6379}, "channel_kafka_sub": {"brokers": "hobby-channel-kafka-broker-01.database.svc.cluster.local:9092", "topics": "channelol_go_ev", "group_id": "activity-push", "client_id": "activity-push"}, "server.grpcListen": ":80", "server.adminListen": ":8078"}