package manager

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"github.com/DATA-DOG/go-sqlmock"
	sqlxmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/golang/mock/gomock"
	"github.com/jinzhu/gorm"
	"golang.52tt.com/pkg/config"
	pb "golang.52tt.com/protocol/services/presentextraconf"
	"golang.52tt.com/protocol/services/userpresent"
	"golang.52tt.com/services/present-extra-conf/internal/conf"
	"golang.52tt.com/services/present-extra-conf/internal/rpc"
	"golang.52tt.com/services/present-extra-conf/internal/store"
	"reflect"
	"testing"
	"time"
)

var mgr *ExtraConfManager
var ctx context.Context
var mock sqlmock.Sqlmock

func getMockDb() (mock sqlxmock.Sqlmock, gormDB *gorm.DB) {
	var err error
	var db *sql.DB

	db, mock, err = sqlmock.New(sqlmock.QueryMatcherOption(sqlmock.QueryMatcherEqual))
	if err != nil {
		panic(err)
	}

	gormDB, err = gorm.Open("mysql", db)
	if nil != err {
		panic("Init DB with sqlmock failed")
	}
	return
}

func init() {
	ctx = context.Background()

	mysqlConf := &config.MysqlConfig{
		Host:     "*************",
		Port:     3306,
		Database: "appsvr",
		Charset:  "utf8mb4",
		UserName: "godman",
		Password: "thegodofman",
	}
	fmt.Printf("string:%s\n", mysqlConf.ConnectionString())
	sc := &conf.ServiceConfigT{}
	sc.StoreConfig = mysqlConf
	err := errors.New("")

	var db *gorm.DB
	mock, db = getMockDb()

	mgr, err = NewMockManager(ctx, sc, db)

	fmt.Println(sc, err)
}

func TestExtraConfManager_GetPresentFlashInfo(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	_ = rpc.SetupForGetPresentConfigByIdList(ctl)

	type fields struct {
		store *store.Store
		stop  chan interface{}
	}
	type args struct {
		ctx       context.Context
		page      uint32
		count     uint32
		giftId    uint32
		giftName  string
		flashName string
	}

	mock.ExpectQuery("SELECT * FROM `present_flash_info`").WillReturnRows(sqlmock.NewRows([]string{"gift_id",
		"flash_id", "effect_begin", "effect_end", "operator", "created_at", "updated_at"}).AddRow(1, 1, time.Now(), time.Now(), "", time.Now(), time.Now()))
	mock.ExpectQuery("SELECT * FROM `flash_effect_config` WHERE (flash_id in (?))").WithArgs(sqlmock.AnyArg()).WillReturnRows(sqlmock.NewRows([]string{"flash_id",
		"flash_name", "flash_url", "flash_md5", "operator", "created_at", "updated_at"}).AddRow(1, "", "", "", "", time.Now(), time.Now()))
	mock.ExpectQuery("SELECT * FROM `last_update_time` LIMIT 1").WillReturnRows(&sqlmock.Rows{})
	//mock.ExpectExec("SELECT * FROM `flash_effect_config`  WHERE (flash_id in (?))").WithArgs(sqlmock.AnyArg())

	mock.ExpectQuery("SELECT * FROM `present_flash_info`").WillReturnRows(sqlmock.NewRows([]string{"gift_id",
		"flash_id", "effect_begin", "effect_end", "operator", "created_at", "updated_at"}).AddRow(1, 1, time.Now(), time.Now(), "", time.Now(), time.Now()))
	mock.ExpectQuery("SELECT * FROM `flash_effect_config` WHERE (flash_id in (?))").WithArgs(sqlmock.AnyArg()).WillReturnRows(sqlmock.NewRows([]string{"flash_id",
		"flash_name", "flash_url", "flash_md5", "operator", "created_at", "updated_at"}).AddRow(1, "", "", "", "", time.Now(), time.Now()))
	mock.ExpectQuery("SELECT * FROM `last_update_time` LIMIT 1").WillReturnRows(&sqlmock.Rows{})

	wantResp := make([]*pb.PresentFlashInfo, 0)
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*pb.PresentFlashInfo
		want1   uint32
		want2   uint32
		wantErr bool
	}{
		{name: "GetPresentFlashInfo",
			fields: fields{store: mgr.store},
			args:   args{ctx: context.Background(), page: 0, count: 0, giftId: 0, giftName: "", flashName: ""},
			want:   wantResp, want1: 0, want2: 1662367375, wantErr: false,
		},
		{name: "GetPresentFlashInfo1",
			fields: fields{store: mgr.store},
			args:   args{ctx: context.Background(), page: 0, count: 0, giftId: 0, giftName: "测试一下", flashName: ""},
			want:   wantResp, want1: 0, want2: 1662367375, wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &ExtraConfManager{
				store: tt.fields.store,
				stop:  tt.fields.stop,
			}
			_, _, _, err := m.GetPresentFlashInfo(tt.args.ctx, tt.args.page, tt.args.count, tt.args.giftId, tt.args.giftName, tt.args.flashName)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPresentFlashInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			//if !reflect.DeepEqual(got, tt.want) {
			//	t.Errorf("GetPresentFlashInfo() got = %v, want %v", got, tt.want)
			//}
		})
	}
}

func TestExtraConfManager_GetPresentFloat(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	_ = rpc.SetupForGetPresentFloat(ctl)

	mock.ExpectQuery("SELECT * FROM `present_float_layer`").WillReturnRows(sqlmock.NewRows([]string{"gift_id",
		"frame_image_url", "jump_url", "jump_url_type", "effect_begin", "effect_end", "operator", "created_at", "updated_at"}).
		AddRow(1, "", "", 1, time.Now(), time.Now(), "", time.Now(), time.Now()))
	//mock.ExpectQuery("SELECT * FROM `flash_effect_config` WHERE (flash_id in (NULL))").WillReturnRows(&sqlmock.Rows{})
	//mock.ExpectQuery("SELECT * FROM `last_update_time` LIMIT 1").WillReturnRows(&sqlmock.Rows{})

	mock.ExpectQuery("SELECT * FROM `present_float_layer`").WillReturnRows(sqlmock.NewRows([]string{"gift_id",
		"frame_image_url", "jump_url", "jump_url_type", "effect_begin", "effect_end", "operator", "created_at", "updated_at"}).
		AddRow(1, "", "", 1, time.Now(), time.Now(), "", time.Now(), time.Now()))
	//mock.ExpectQuery("SELECT * FROM `flash_effect_config` WHERE (flash_id in (NULL))").WillReturnRows(&sqlmock.Rows{})
	//mock.ExpectQuery("SELECT * FROM `last_update_time` LIMIT 1").WillReturnRows(&sqlmock.Rows{})

	type fields struct {
		store *store.Store
		stop  chan interface{}
	}
	type args struct {
		ctx       context.Context
		page      uint32
		count     uint32
		keyId     uint32
		keyStatus uint32
		keyWord   string
	}
	wantResp := make([]*pb.PresentFloatInfo, 0)
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*pb.PresentFloatInfo
		want1   uint32
		want2   uint32
		wantErr bool
	}{
		{name: "GetPresentFloat",
			fields: fields{store: mgr.store},
			args:   args{ctx: context.Background(), page: 0, count: 0, keyId: 0, keyWord: "", keyStatus: 0},
			want:   wantResp, want1: 0, want2: 1662367375, wantErr: false,
		},
		{name: "GetPresentFloat1",
			fields: fields{store: mgr.store},
			args:   args{ctx: context.Background(), page: 0, count: 0, keyId: 0, keyWord: "测试一下", keyStatus: 0},
			want:   wantResp, want1: 0, want2: 1662367375, wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &ExtraConfManager{
				store: tt.fields.store,
				stop:  tt.fields.stop,
			}
			_, _, _, err := m.GetPresentFloat(tt.args.ctx, tt.args.page, tt.args.count, tt.args.keyId, tt.args.keyStatus, tt.args.keyWord)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPresentFloat() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			//if !reflect.DeepEqual(got, tt.want) {
			//	t.Errorf("GetPresentFloat() got = %v, want %v", got, tt.want)
			//}
			//if got1 != tt.want1 {
			//	t.Errorf("GetPresentFloat() got1 = %v, want %v", got1, tt.want1)
			//}
			//if got2 != tt.want2 {
			//	t.Errorf("GetPresentFloat() got2 = %v, want %v", got2, tt.want2)
			//}
		})
	}
}

func TestExtraConfManager_AddFlashConfig(t *testing.T) {
	type fields struct {
		store *store.Store
		stop  chan interface{}
	}
	type args struct {
		ctx context.Context
		req *pb.FlashEffectConfig
	}

	mock.ExpectBegin()
	mock.ExpectExec("INSERT INTO `flash_effect_config` (`flash_name`,`flash_url`,`flash_md5`,`operator`,`created_at`,`updated_at`) VALUES (?,?,?,?,?,?)").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	mock.ExpectQuery("SELECT * FROM `last_update_time` LIMIT 1").WillReturnRows(&sqlmock.Rows{})

	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{name: "AddFlashConfig",
			fields: fields{store: mgr.store},
			args: args{ctx: context.Background(), req: &pb.FlashEffectConfig{
				FlashId: 111,
			}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &ExtraConfManager{
				store: tt.fields.store,
				stop:  tt.fields.stop,
			}
			if err := m.AddFlashConfig(tt.args.ctx, tt.args.req); (err != nil) != tt.wantErr {
				t.Errorf("AddFlashConfig() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestExtraConfManager_AddPresentFloat(t *testing.T) {

	mock.ExpectBegin()
	mock.ExpectExec("INSERT INTO `present_float_layer` (`gift_id`,`frame_image_url`,`jump_url`,`jump_url_type`,`effect_begin`,`effect_end`,`operator`,`created_at`,`updated_at`,`show_channel_type_list`,`show_app_type_list`,`app_url_list`,`activity_type`,`sub_activity_type`,`activity_name`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	mock.ExpectQuery("SELECT * FROM `last_update_time` LIMIT 1").WillReturnRows(&sqlmock.Rows{})

	type fields struct {
		store *store.Store
		stop  chan interface{}
	}
	type args struct {
		ctx context.Context
		req *pb.PresentFloatLayer
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{name: "AddPresentFloat",
			fields: fields{store: mgr.store},
			args: args{ctx: context.Background(), req: &pb.PresentFloatLayer{
				GiftId: 1,
			}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &ExtraConfManager{
				store: tt.fields.store,
				stop:  tt.fields.stop,
			}
			if err := m.AddPresentFloat(tt.args.ctx, tt.args.req); (err != nil) != tt.wantErr {
				t.Errorf("AddPresentFloat() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestExtraConfManager_DelFlashConfig(t *testing.T) {
	type fields struct {
		store *store.Store
		stop  chan interface{}
	}
	type args struct {
		ctx context.Context
		id  uint32
	}
	mock.ExpectBegin()
	mock.ExpectExec("DELETE FROM `flash_effect_config` WHERE (flash_id = ?)").
		WithArgs(1).WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()

	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{name: "DelFlashConfig",
			fields:  fields{store: mgr.store},
			args:    args{ctx: context.Background(), id: 1},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &ExtraConfManager{
				store: tt.fields.store,
				stop:  tt.fields.stop,
			}
			if err := m.DelFlashConfig(tt.args.ctx, tt.args.id); (err != nil) != tt.wantErr {
				t.Errorf("DelFlashConfig() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestExtraConfManager_DelPresentFloat(t *testing.T) {
	mock.ExpectBegin()
	mock.ExpectExec("DELETE FROM `present_float_layer` WHERE (gift_id = ?)").
		WithArgs(1).WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()

	type fields struct {
		store *store.Store
		stop  chan interface{}
	}
	type args struct {
		ctx context.Context
		id  uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{name: "DelPresentFloat",
			fields:  fields{store: mgr.store},
			args:    args{ctx: context.Background(), id: 1},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &ExtraConfManager{
				store: tt.fields.store,
				stop:  tt.fields.stop,
			}
			if err := m.DelPresentFloat(tt.args.ctx, tt.args.id); (err != nil) != tt.wantErr {
				t.Errorf("DelPresentFloat() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestExtraConfManager_GetFlashConfig(t *testing.T) {

	mock.ExpectQuery("SELECT * FROM `flash_effect_config`").WillReturnRows(&sqlmock.Rows{})
	type fields struct {
		store *store.Store
		stop  chan interface{}
	}
	type args struct {
		ctx     context.Context
		page    uint32
		count   uint32
		keyWord string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*pb.FlashEffectConfig
		want1   uint32
		wantErr bool
	}{
		{name: "GetFlashConfig",
			fields:  fields{store: mgr.store},
			args:    args{ctx: context.Background(), page: 0, count: 0, keyWord: ""},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &ExtraConfManager{
				store: tt.fields.store,
				stop:  tt.fields.stop,
			}
			_, _, err := m.GetFlashConfig(tt.args.ctx, tt.args.page, tt.args.count, tt.args.keyWord)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFlashConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestExtraConfManager_SearchPresent(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	_ = rpc.SetupForSearchPresent(ctl)

	type fields struct {
		store *store.Store
		stop  chan interface{}
	}
	wantResp := make([]*pb.PresentBaseConfig, 0)
	type args struct {
		ctx     context.Context
		page    uint32
		count   uint32
		keyId   uint32
		keyWord string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*pb.PresentBaseConfig
		want1   uint32
		wantErr bool
	}{
		{name: "SearchPresent",
			fields: fields{store: mgr.store},
			args:   args{ctx: context.Background(), page: 0, count: 0, keyId: 0, keyWord: ""},
			want:   wantResp, want1: 0, wantErr: false,
		},
		{name: "SearchPresent1",
			fields: fields{store: mgr.store},
			args:   args{ctx: context.Background(), page: 0, count: 0, keyId: 0, keyWord: "测试一下"},
			want:   wantResp, want1: 0, wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &ExtraConfManager{
				store: tt.fields.store,
				stop:  tt.fields.stop,
			}
			got, _, err := m.SearchPresent(tt.args.ctx, tt.args.page, tt.args.count, tt.args.keyId, tt.args.keyWord)
			if (err != nil) != tt.wantErr {
				t.Errorf("SearchPresent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SearchPresent() got = %v, want %v", got, tt.want)
			}
			//if got1 != tt.want1 {
			//	t.Errorf("SearchPresent() got1 = %v, want %v", got1, tt.want1)
			//}
		})
	}
}

func TestExtraConfManager_UpdateFlashConfig(t *testing.T) {

	mock.ExpectBegin()
	mock.ExpectExec("UPDATE `flash_effect_config` SET `created_at` = ?, `flash_id` = ?, `flash_md5` = ?, `flash_name` = ?, `flash_url` = ?, `operator` = ?, `updated_at` = ? WHERE (flash_id = ?)").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()

	type fields struct {
		store *store.Store
		stop  chan interface{}
	}
	type args struct {
		ctx context.Context
		req *pb.FlashEffectConfig
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{name: "UpdateFlashConfig",
			fields: fields{store: mgr.store},
			args: args{ctx: context.Background(), req: &pb.FlashEffectConfig{
				FlashId:    1,
				FlashName:  "",
				FlashUrl:   "",
				FlashMd5:   "",
				Operator:   "",
				CreateTime: 0,
				UpdateTime: 0,
			}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &ExtraConfManager{
				store: tt.fields.store,
				stop:  tt.fields.stop,
			}
			if err := m.UpdateFlashConfig(tt.args.ctx, tt.args.req); (err != nil) != tt.wantErr {
				t.Errorf("UpdateFlashConfig() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

//
//func TestExtraConfManager_UpdatePresentFlashInfo(t *testing.T) {
//	mock.ExpectQuery("SELECT * FROM `present_flash_info`  WHERE (gift_id = ?)").WithArgs(1).WillReturnRows(&sqlmock.Rows{})
//	mock.ExpectBegin()
//	mock.ExpectExec("DELETE FROM `present_flash_info` WHERE (gift_id = ?)").
//		WithArgs(sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))
//	mock.ExpectCommit()
//	mock.ExpectQuery("SELECT * FROM `last_update_time` LIMIT 1").WillReturnRows(&sqlmock.Rows{})
//	mock.ExpectBegin()
//	mock.ExpectExec("INSERT INTO `last_update_time` (`update_time`) VALUES (?)").WithArgs(gomock.Any()).WillReturnResult(sqlmock.NewResult(1, 1))
//	mock.ExpectCommit()
//
//	type fields struct {
//		store *store.Store
//		stop  chan interface{}
//	}
//	type args struct {
//		ctx      context.Context
//		giftId   uint32
//		flashId  uint32
//		begin    uint32
//		end      uint32
//		operator string
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		wantErr bool
//	}{
//		{name: "UpdateFlashConfig",
//			fields:  fields{store: mgr.store},
//			args:    args{ctx: context.Background(), giftId: 1},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			m := &ExtraConfManager{
//				store: tt.fields.store,
//				stop:  tt.fields.stop,
//			}
//			if err := m.UpdatePresentFlashInfo(tt.args.ctx, tt.args.giftId, tt.args.flashId, tt.args.begin, tt.args.end, tt.args.operator); (err != nil) != tt.wantErr {
//				t.Errorf("UpdatePresentFlashInfo() error = %v, wantErr %v", err, tt.wantErr)
//			}
//		})
//	}
//}
//
//func TestExtraConfManager_UpdatePresentFloat(t *testing.T) {
//	//mock.ExpectQuery("SELECT * FROM `present_flash_info`  WHERE (gift_id = ?)").WithArgs(1).WillReturnRows(&sqlmock.Rows{})
//	mock.ExpectBegin()
//	mock.ExpectExec("UPDATE `present_float_layer` SET `activity_name` = ?, `activity_type` = ?, `app_url_list` = ?, `created_at` = ?, `effect_begin` = ?, `effect_end` = ?, `frame_image_url` = ?, `gift_id` = ?, `jump_url` = ?, `jump_url_type` = ?, `operator` = ?, `show_app_type_list` = ?, `show_channel_type_list` = ?, `sub_activity_type` = ?, `updated_at` = ? WHERE (gift_id = ?)").
//		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))
//	mock.ExpectCommit()
//	mock.ExpectQuery("SELECT * FROM `last_update_time` LIMIT 1").WillReturnRows(&sqlmock.Rows{})
//	mock.ExpectBegin()
//	mock.ExpectExec("INSERT INTO `last_update_time` (`update_time`) VALUES (?)").WithArgs(sqlmock.AnyArg()).
//		WillReturnResult(sqlmock.NewResult(1, 1))
//	mock.ExpectCommit()
//
//	type fields struct {
//		store *store.Store
//		stop  chan interface{}
//	}
//	type args struct {
//		ctx context.Context
//		req *pb.PresentFloatLayer
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		wantErr bool
//	}{
//		{name: "UpdatePresentFloat",
//			fields: fields{store: mgr.store},
//			args: args{ctx: context.Background(), req: &pb.PresentFloatLayer{
//				GiftId:               1,
//				FloatImageUrl:        "",
//				JumpUrl:              "",
//				IsActivityUrl:        false,
//				EffectBegin:          0,
//				EffectEnd:            0,
//				Operator:             "",
//				EffectStatus:         0,
//				UpdateTime:           0,
//				XXX_NoUnkeyedLiteral: struct{}{},
//				XXX_unrecognized:     nil,
//				XXX_sizecache:        0,
//			}},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			m := &ExtraConfManager{
//				store: tt.fields.store,
//				stop:  tt.fields.stop,
//			}
//			if err := m.UpdatePresentFloat(tt.args.ctx, tt.args.req); (err != nil) != tt.wantErr {
//				t.Errorf("UpdatePresentFloat() error = %v, wantErr %v", err, tt.wantErr)
//			}
//		})
//	}
//}

func TestMatchNumber(t *testing.T) {
	type args struct {
		source  uint32
		compare uint32
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{name: "MatchNumber",
			args: args{
				source:  1,
				compare: 1,
			},
			want: true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := MatchNumber(tt.args.source, tt.args.compare); got != tt.want {
				t.Errorf("MatchNumber() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNewExtraConfManager(t *testing.T) {
	type args struct {
		ctx context.Context
		sc  *conf.ServiceConfigT
	}

	mysqlConf := &config.MysqlConfig{
		Host:     "*************",
		Port:     3306,
		Database: "appsvr",
		Charset:  "utf8mb4",
		UserName: "godman",
		Password: "thegodofman",
	}
	fmt.Printf("string:%s\n", mysqlConf.ConnectionString())
	sc := &conf.ServiceConfigT{}
	sc.StoreConfig = mysqlConf

	tests := []struct {
		name    string
		args    args
		want    *ExtraConfManager
		wantErr bool
	}{}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := NewExtraConfManager(tt.args.ctx, tt.args.sc)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewExtraConfManager() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			//if !reflect.DeepEqual(got, tt.want) {
			//	t.Errorf("NewExtraConfManager() got = %v, want %v", got, tt.want)
			//}
		})
	}
}

func Test_isSearchFlash(t *testing.T) {
	type args struct {
		giftName         string
		flashName        string
		giftId           uint32
		flashConfigMap   map[uint32]*store.FlashEffectConfig
		presentConfigMap map[uint32]*userpresent.StPresentItemConfig
		item             *store.PresentFlashInfo
	}
	flashConfigMap := make(map[uint32]*store.FlashEffectConfig)
	presentConfigMap := make(map[uint32]*userpresent.StPresentItemConfig)
	item := &store.PresentFlashInfo{}

	tests := []struct {
		name string
		args args
		want bool
	}{
		{name: "isSearchFlash",
			args: args{
				giftName:         "",
				flashName:        "",
				giftId:           0,
				flashConfigMap:   flashConfigMap,
				presentConfigMap: presentConfigMap,
				item:             item,
			},
			want: false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isSearchFlash(tt.args.giftName, tt.args.flashName, tt.args.giftId, tt.args.flashConfigMap, tt.args.presentConfigMap, tt.args.item); got != tt.want {
				t.Errorf("isSearchFlash() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_isSearchFloat(t *testing.T) {
	type args struct {
		keyWord          string
		keyId            uint32
		keyStatus        uint32
		presentConfigMap map[uint32]*userpresent.StPresentItemConfig
		item             *store.PresentFloatLayer
	}
	presentConfigMap := make(map[uint32]*userpresent.StPresentItemConfig)
	item := &store.PresentFloatLayer{}

	tests := []struct {
		name string
		args args
		want bool
	}{
		{name: "isSearchFloat",
			args: args{
				presentConfigMap: presentConfigMap,
				item:             item,
			},
			want: true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isSearchFloat(tt.args.keyWord, tt.args.keyId, tt.args.keyStatus, tt.args.presentConfigMap, tt.args.item); got != tt.want {
				t.Errorf("isSearchFloat() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_isUrlActivity(t *testing.T) {
	type args struct {
		typ uint32
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{name: "isUrlActivity",
			args: args{
				typ: 1,
			},
			want: false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isUrlActivity(tt.args.typ); got != tt.want {
				t.Errorf("isUrlActivity() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_min(t *testing.T) {
	type args struct {
		num1 uint32
		num2 uint32
	}
	tests := []struct {
		name string
		args args
		want uint32
	}{
		{name: "min",
			args: args{
				num1: 1,
				num2: 2,
			},
			want: 1},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := min(tt.args.num1, tt.args.num2); got != tt.want {
				t.Errorf("min() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_urlType(t *testing.T) {
	type args struct {
		isActivity bool
	}
	tests := []struct {
		name string
		args args
		want uint32
	}{
		{name: "min",
			args: args{
				isActivity: false,
			},
			want: 1},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := urlType(tt.args.isActivity); got != tt.want {
				t.Errorf("urlType() = %v, want %v", got, tt.want)
			}
		})
	}
}
