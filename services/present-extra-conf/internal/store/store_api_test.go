package store

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/DATA-DOG/go-sqlmock"
	sqlxmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/jinzhu/gorm"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/services/present-extra-conf/internal/conf"
	"reflect"
	"testing"
	"time"
)

func getMockDb() (mock sqlxmock.Sqlmock, gormDB *gorm.DB) {
	var err error
	var db *sql.DB

	db, mock, err = sqlmock.New(sqlmock.QueryMatcherOption(sqlmock.QueryMatcherEqual))
	if err != nil {
		panic(err)
	}

	gormDB, err = gorm.Open("mysql", db)
	if nil != err {
		panic("Init DB with sqlmock failed")
	}

	return
}

func TestFlashEffectConfig_TableName(t *testing.T) {

	type fields struct {
		FlashId   uint32
		FlashName string
		FlashUrl  string
		FlashMd5  string
		Operator  string
		CreatedAt time.Time
		UpdatedAt time.Time
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		// TODO: Add test cases.
		{name: "TableName", fields: fields{}, want: "flash_effect_config"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fl := &FlashEffectConfig{
				FlashId:   tt.fields.FlashId,
				FlashName: tt.fields.FlashName,
				FlashUrl:  tt.fields.FlashUrl,
				FlashMd5:  tt.fields.FlashMd5,
				Operator:  tt.fields.Operator,
				CreatedAt: tt.fields.CreatedAt,
				UpdatedAt: tt.fields.UpdatedAt,
			}
			if got := fl.TableName(); got != tt.want {
				t.Errorf("TableName() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestLastUpdateTime_TableName(t *testing.T) {
	type fields struct {
		UpdateTime time.Time
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		{name: "TableName", fields: fields{}, want: "last_update_time"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			la := &LastUpdateTime{
				UpdateTime: tt.fields.UpdateTime,
			}
			if got := la.TableName(); got != tt.want {
				t.Errorf("TableName() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNewStore(t *testing.T) {
	mysqlConf := &config.MysqlConfig{
		Host:     "*************",
		Port:     3306,
		Database: "appsvr",
		Charset:  "utf8mb4",
		UserName: "godman",
		Password: "thegodofman",
	}
	fmt.Printf("string:%s\n", mysqlConf.ConnectionString())
	sc := &conf.ServiceConfigT{}
	sc.StoreConfig = mysqlConf
	ctx := context.Background()
	db, _ := NewStore(ctx, sc)

	type args struct {
		ctx context.Context
		sc  *conf.ServiceConfigT
	}
	tests := []struct {
		name    string
		args    args
		want    *Store
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "NewStore", args: args{
			ctx: context.Background(),
			sc:  sc,
		}, want: db, wantErr: false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			//got, err := NewStore(tt.args.ctx, tt.args.sc)
			//if (err != nil) != tt.wantErr {
			//	t.Errorf("NewStore() error = %v, wantErr %v", err, tt.wantErr)
			//	return
			//}
			if !reflect.DeepEqual(db, tt.want) {
				t.Errorf("NewStore() got = %v, want %v", db, tt.want)
			}
		})
	}
}

func TestPresentFlashInfo_TableName(t *testing.T) {
	type fields struct {
		GiftId      uint32
		FlashId     uint32
		EffectBegin time.Time
		EffectEnd   time.Time
		Operator    string
		CreatedAt   time.Time
		UpdatedAt   time.Time
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		{name: "TableName", fields: fields{}, want: "present_flash_info"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pr := &PresentFlashInfo{
				GiftId:      tt.fields.GiftId,
				FlashId:     tt.fields.FlashId,
				EffectBegin: tt.fields.EffectBegin,
				EffectEnd:   tt.fields.EffectEnd,
				Operator:    tt.fields.Operator,
				CreatedAt:   tt.fields.CreatedAt,
				UpdatedAt:   tt.fields.UpdatedAt,
			}
			if got := pr.TableName(); got != tt.want {
				t.Errorf("TableName() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentFloatLayer_TableName(t *testing.T) {
	type fields struct {
		GiftId        uint32
		FrameImageUrl string
		JumpUrl       string
		JumpUrlType   uint32
		EffectBegin   time.Time
		EffectEnd     time.Time
		Operator      string
		CreatedAt     time.Time
		UpdatedAt     time.Time
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		{name: "TableName", fields: fields{}, want: "present_float_layer"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pr := &PresentFloatLayer{
				GiftId:        tt.fields.GiftId,
				FrameImageUrl: tt.fields.FrameImageUrl,
				JumpUrl:       tt.fields.JumpUrl,
				JumpUrlType:   tt.fields.JumpUrlType,
				EffectBegin:   tt.fields.EffectBegin,
				EffectEnd:     tt.fields.EffectEnd,
				Operator:      tt.fields.Operator,
				CreatedAt:     tt.fields.CreatedAt,
				UpdatedAt:     tt.fields.UpdatedAt,
			}
			if got := pr.TableName(); got != tt.want {
				t.Errorf("TableName() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStore_AddFlashConfig(t *testing.T) {
	mock, db := getMockDb()

	entry := &FlashEffectConfig{
		FlashName: "test",
		FlashUrl:  "www.baidu.com",
		FlashMd5:  "",
		Operator:  "",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	mock.ExpectBegin()
	mock.ExpectExec("INSERT INTO `flash_effect_config` (`flash_name`,`flash_url`,`flash_md5`,`operator`,`created_at`,`updated_at`) VALUES (?,?,?,?,?,?)").
		WithArgs(entry.FlashName, entry.FlashUrl, entry.FlashMd5, entry.Operator, entry.CreatedAt, entry.UpdatedAt).WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()

	type fields struct {
		mysql *gorm.DB
		sc    *conf.ServiceConfigT
	}
	type args struct {
		ctx   context.Context
		store *FlashEffectConfig
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{name: "AddFlashConfig", fields: fields{
			mysql: db,
			sc:    &conf.ServiceConfigT{},
		}, args: args{
			ctx:   context.Background(),
			store: entry,
		}, wantErr: false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &Store{
				mysql: tt.fields.mysql,
				sc:    tt.fields.sc,
			}
			if err := st.AddFlashConfig(tt.args.ctx, tt.args.store); (err != nil) != tt.wantErr {
				t.Errorf("AddFlashConfig() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStore_AddFloatConfig(t *testing.T) {
	mock, db := getMockDb()

	entry := &PresentFloatLayer{
		GiftId:        1,
		FrameImageUrl: "www.baidu.com",
		JumpUrl:       "www.baidu.com",
		JumpUrlType:   0,
		EffectBegin:   time.Now(),
		EffectEnd:     time.Now().AddDate(0, 1, 0),
		Operator:      "",
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	mock.ExpectBegin()
	mock.ExpectExec("INSERT INTO `present_float_layer` (`gift_id`,`frame_image_url`,`jump_url`,`jump_url_type`,`effect_begin`,`effect_end`,`operator`,`created_at`,`updated_at`,`show_channel_type_list`,`show_app_type_list`,`app_url_list`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?)").
		WithArgs(entry.GiftId, entry.FrameImageUrl, entry.JumpUrl, entry.JumpUrlType, entry.EffectBegin, entry.EffectEnd,
			entry.Operator, entry.CreatedAt, entry.UpdatedAt, entry.ShowChannelTypeList, entry.ShowAppTypeList, entry.AppUrlList).WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()

	type fields struct {
		mysql *gorm.DB
		sc    *conf.ServiceConfigT
	}
	type args struct {
		ctx   context.Context
		store *PresentFloatLayer
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{name: "AddFloatConfig", fields: fields{
			mysql: db,
			sc:    &conf.ServiceConfigT{},
		}, args: args{
			ctx:   context.Background(),
			store: entry,
		}, wantErr: false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &Store{
				mysql: tt.fields.mysql,
				sc:    tt.fields.sc,
			}
			if err := st.AddFloatConfig(tt.args.ctx, tt.args.store); (err != nil) != tt.wantErr {
				t.Errorf("AddFloatConfig() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_AddPresentEffectTimeRecord(t *testing.T) {
	mock, db := getMockDb()
	//mysqlConf := &config.MysqlConfig{
	//	Host:     "*************",
	//	Port:     3306,
	//	Database: "appsvr",
	//	Charset:  "utf8mb4",
	//	UserName: "godman",
	//	Password: "thegodofman",
	//}
	//fmt.Printf("string:%s\n", mysqlConf.ConnectionString())
	//sc := &conf.ServiceConfigT{}
	//sc.StoreConfig = mysqlConf
	//ctx := context.Background()
	//db, _ := NewStore(ctx, sc)

	rows := mock.NewRows([]string{"gift_id", "uid", "effect_begin", "send_count", "update_at"}).AddRow(1, 1, 1, 3, time.Now())

	mock.ExpectQuery("SELECT * FROM `present_effect_time_record`  WHERE (uid = ? and gift_id = ? and effect_begin = ?) ORDER BY" +
		" `present_effect_time_record`.`gift_id` ASC LIMIT 1").WillReturnRows(rows)
	mock.ExpectBegin()
	mock.ExpectExec("UPDATE `present_effect_time_record` SET `send_count` = send_count + ?, `updated_at` = ? WHERE `present_effect_time_record`.`gift_id` = ? AND `present_effect_time_record`.`uid` = ? AND `present_effect_time_record`.`effect_begin` = ?").WithArgs(
		sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()

	type fields struct {
		mysql *gorm.DB
		sc    *conf.ServiceConfigT
	}
	type args struct {
		ctx   context.Context
		store *PresentEffectTimeRecord
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{name: "PresentEffectTimeRecord", fields: fields{
			mysql: db,
			sc:    &conf.ServiceConfigT{},
		}, args: args{
			ctx: context.Background(),
			store: &PresentEffectTimeRecord{
				GiftId:      1,
				Uid:         1,
				EffectBegin: 1,
				SendCount:   2,
				UpdatedAt:   time.Now(),
			},
		}, wantErr: false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &Store{
				mysql: tt.fields.mysql,
				sc:    tt.fields.sc,
			}
			if resp, err := st.AddPresentEffectTimeRecord(tt.args.ctx, tt.args.store); (err != nil) != tt.wantErr {
				fmt.Println(resp, err)
			}
		})
	}
}

func Test_AddPresentEffectDayRecord(t *testing.T) {
	mock, db := getMockDb()
	//mysqlConf := &config.MysqlConfig{
	//	Host:     "*************",
	//	Port:     3306,
	//	Database: "appsvr",
	//	Charset:  "utf8mb4",
	//	UserName: "godman",
	//	Password: "thegodofman",
	//}
	//fmt.Printf("string:%s\n", mysqlConf.ConnectionString())
	//sc := &conf.ServiceConfigT{}
	//sc.StoreConfig = mysqlConf
	//ctx := context.Background()
	//db, _ := NewStore(ctx, sc)

	mock.ExpectQuery("SELECT * FROM `present_effect_day_record` WHERE (uid = ? and gift_id = ? and effect_begin = ?) ORDER BY `present_effect_day_record`.`gift_id` ASC LIMIT 1").WillReturnRows(sqlmock.NewRows([]string{"uid"}))
	mock.ExpectBegin()
	mock.ExpectExec("INSERT INTO `present_effect_day_record` (`gift_id`,`uid`,`effect_begin`,`expire_day_count`,`time_append`,`is_limitless`,`updated_at`) VALUES (?,?,?,?,?,?,?)").WithArgs(
		sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()

	type fields struct {
		mysql *gorm.DB
		sc    *conf.ServiceConfigT
	}
	type args struct {
		ctx   context.Context
		store *PresentEffectDayRecord
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{name: "AddPresentEffectDayRecord", fields: fields{
			mysql: db,
			sc:    &conf.ServiceConfigT{},
		}, args: args{
			ctx: context.Background(),
			store: &PresentEffectDayRecord{
				GiftId:      1,
				Uid:         1,
				TimeAppend:  86400,
				UpdatedAt:   time.Now(),
				EffectBegin: 10000,
			},
		}, wantErr: false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &Store{
				mysql: tt.fields.mysql,
				sc:    tt.fields.sc,
			}
			if err := st.AddPresentEffectDayRecord(tt.args.ctx, tt.args.store); (err != nil) != tt.wantErr {
				t.Errorf("AddFloatConfig() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStore_Close(t *testing.T) {
	_, db := getMockDb()

	type fields struct {
		mysql *gorm.DB
		sc    *conf.ServiceConfigT
	}
	tests := []struct {
		name   string
		fields fields
	}{
		{name: "Close", fields: fields{
			mysql: db,
			sc:    &conf.ServiceConfigT{},
		}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &Store{
				mysql: tt.fields.mysql,
				sc:    tt.fields.sc,
			}
			st.Close()
		})
	}
}

func TestStore_UpdateEffectDelayLevelConfig(t *testing.T) {
	mock, db := getMockDb()

	mock.ExpectBegin()
	mock.ExpectExec("UPDATE `effect_delay_level_config` SET `send_count` = ?, `day_count` = ?, `expire_day_count` = ?, `effect_end` = ?, `notice_day_count` = ?, `updated_at` = ? WHERE `effect_delay_level_config`.`gift_id` = ? AND `effect_delay_level_config`.`level` = ? AND `effect_delay_level_config`.`effect_begin` = ? AND ((gift_id = ? and level = ? and effect_begin = ?))").WithArgs(
		sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()

	type fields struct {
		mysql *gorm.DB
		sc    *conf.ServiceConfigT
	}
	type args struct {
		ctx   context.Context
		store *EffectDelayLevelConfig
		tx    *gorm.DB
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{{name: "AddPresentEffectDayRecord", fields: fields{
		mysql: db,
		sc:    &conf.ServiceConfigT{},
	}, args: args{
		ctx: context.Background(),
		store: &EffectDelayLevelConfig{
			GiftId:      1,
			Level:       1,
			UpdatedAt:   time.Now(),
			EffectBegin: 10000,
		},
	}, wantErr: false}}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &Store{
				mysql: tt.fields.mysql,
				sc:    tt.fields.sc,
			}
			if err := st.UpdateEffectDelayLevelConfig(tt.args.ctx, tt.args.store, tt.args.tx); (err != nil) != tt.wantErr {
				t.Errorf("UpdateEffectDelayLevelConfig() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStore_DelFlashConfig(t *testing.T) {
	type fields struct {
		mysql *gorm.DB
		sc    *conf.ServiceConfigT
	}
	type args struct {
		ctx    context.Context
		giftId uint32
	}

	giftId := uint32(6)
	mock, db := getMockDb()
	mock.ExpectBegin()
	mock.ExpectExec("DELETE FROM `flash_effect_config`  WHERE (flash_id = ?)").
		WithArgs(giftId).WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()

	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{name: "DelFlashConfig", fields: fields{
			mysql: db,
			sc:    &conf.ServiceConfigT{},
		}, args: args{
			ctx:    context.Background(),
			giftId: giftId,
		}, wantErr: false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &Store{
				mysql: tt.fields.mysql,
				sc:    tt.fields.sc,
			}
			if err := st.DelFlashConfig(tt.args.ctx, tt.args.giftId); (err != nil) != tt.wantErr {
				t.Errorf("DelFlashConfig() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStore_DelFloatConfig(t *testing.T) {
	type fields struct {
		mysql *gorm.DB
		sc    *conf.ServiceConfigT
	}
	type args struct {
		ctx    context.Context
		giftId uint32
	}

	giftId := uint32(6)
	mock, db := getMockDb()
	mock.ExpectBegin()
	mock.ExpectExec("DELETE FROM `present_float_layer` WHERE (gift_id = ?)").
		WithArgs(giftId).WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()

	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{name: "DelFloatConfig", fields: fields{
			mysql: db,
			sc:    &conf.ServiceConfigT{},
		}, args: args{
			ctx:    context.Background(),
			giftId: giftId,
		}, wantErr: false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &Store{
				mysql: tt.fields.mysql,
				sc:    tt.fields.sc,
			}
			if err := st.DelFloatConfig(tt.args.ctx, tt.args.giftId); (err != nil) != tt.wantErr {
				t.Errorf("DelFloatConfig() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStore_GetAllFlashConfig(t *testing.T) {
	type fields struct {
		mysql *gorm.DB
		sc    *conf.ServiceConfigT
	}
	type args struct {
		ctx context.Context
	}

	mock, db := getMockDb()
	mock.ExpectQuery("SELECT * FROM `flash_effect_config`").WillReturnRows(&sqlmock.Rows{})

	wantConfigs := make([]*FlashEffectConfig, 0)
	tests := []struct {
		name        string
		fields      fields
		args        args
		wantConfigs []*FlashEffectConfig
		wantErr     bool
	}{
		{name: "GetAllFlashConfig", fields: fields{
			mysql: db,
			sc:    &conf.ServiceConfigT{},
		}, args: args{
			ctx: context.Background(),
		}, wantConfigs: wantConfigs, wantErr: false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &Store{
				mysql: db,
				sc:    tt.fields.sc,
			}
			gotConfigs, err := st.GetAllFlashConfig(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllFlashConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotConfigs, tt.wantConfigs) {
				t.Errorf("GetAllFlashConfig() gotConfigs = %+v, want %+v", gotConfigs, tt.wantConfigs)
			}
		})
	}
}

func TestStore_GetAllFloatConfig(t *testing.T) {
	type fields struct {
		mysql *gorm.DB
		sc    *conf.ServiceConfigT
	}
	type args struct {
		ctx context.Context
	}

	wantConfigs := make([]*PresentFloatLayer, 0)
	mock, db := getMockDb()
	mock.ExpectQuery("SELECT * FROM `present_float_layer`").WillReturnRows(&sqlmock.Rows{})

	tests := []struct {
		name        string
		fields      fields
		args        args
		wantConfigs []*PresentFloatLayer
		wantErr     bool
	}{
		{name: "GetAllFloatConfig", fields: fields{
			mysql: db,
			sc:    &conf.ServiceConfigT{},
		}, args: args{
			ctx: context.Background(),
		}, wantConfigs: wantConfigs, wantErr: false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &Store{
				mysql: tt.fields.mysql,
				sc:    tt.fields.sc,
			}
			gotConfigs, err := st.GetAllFloatConfig(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllFloatConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotConfigs, tt.wantConfigs) {
				t.Errorf("GetAllFloatConfig() gotConfigs = %v, want %v", gotConfigs, tt.wantConfigs)
			}
		})
	}
}

func TestStore_GetAllPresentFlash(t *testing.T) {
	type fields struct {
		mysql *gorm.DB
		sc    *conf.ServiceConfigT
	}
	type args struct {
		ctx context.Context
	}

	wantConfigs := make([]*PresentFlashInfo, 0)
	mock, db := getMockDb()
	mock.ExpectQuery("SELECT * FROM `present_flash_info`").WillReturnRows(&sqlmock.Rows{})

	tests := []struct {
		name        string
		fields      fields
		args        args
		wantConfigs []*PresentFlashInfo
		wantErr     bool
	}{
		{name: "GetAllPresentFlash", fields: fields{
			mysql: db,
			sc:    &conf.ServiceConfigT{},
		}, args: args{
			ctx: context.Background(),
		}, wantConfigs: wantConfigs, wantErr: false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &Store{
				mysql: tt.fields.mysql,
				sc:    tt.fields.sc,
			}
			gotConfigs, err := st.GetAllPresentFlash(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllPresentFlash() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotConfigs, tt.wantConfigs) {
				t.Errorf("GetAllPresentFlash() gotConfigs = %v, want %v", gotConfigs, tt.wantConfigs)
			}
		})
	}
}

func TestStore_GetEffectDelayLevelConfig(t *testing.T) {
	type fields struct {
		mysql *gorm.DB
		sc    *conf.ServiceConfigT
	}
	type args struct {
		ctx context.Context
	}

	wantConfigs := make([]*EffectDelayLevelConfig, 0)
	mock, db := getMockDb()
	mock.ExpectQuery("SELECT * FROM `effect_delay_level_config`").WillReturnRows(&sqlmock.Rows{})

	tests := []struct {
		name        string
		fields      fields
		args        args
		wantConfigs []*EffectDelayLevelConfig
		wantErr     bool
	}{
		{name: "GetEffectDelayLevelConfig", fields: fields{
			mysql: db,
			sc:    &conf.ServiceConfigT{},
		}, args: args{
			ctx: context.Background(),
		}, wantConfigs: wantConfigs, wantErr: false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &Store{
				mysql: tt.fields.mysql,
				sc:    tt.fields.sc,
			}
			gotConfigs, err := st.GetEffectDelayLevelConfig(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllPresentFlash() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotConfigs, tt.wantConfigs) {
				t.Errorf("GetAllPresentFlash() gotConfigs = %v, want %v", gotConfigs, tt.wantConfigs)
			}
		})
	}
}

func TestStore_GetFlashConfigByIds(t *testing.T) {
	type fields struct {
		mysql *gorm.DB
		sc    *conf.ServiceConfigT
	}
	type args struct {
		ctx     context.Context
		giftIds []uint32
	}

	create, _ := time.ParseInLocation("2006-01-02 15:04:05", "2022-06-30 14:09:09", time.Local)
	update, _ := time.ParseInLocation("2006-01-02 15:04:05", "2022-06-30 14:09:09", time.Local)
	entry := []*FlashEffectConfig{&FlashEffectConfig{
		FlashId:   6,
		FlashName: "flower",
		FlashUrl:  "https://ga-album-cdnqn.52tt.com/channel/quickEntry/table_flower.zip",
		FlashMd5:  "9b985036808111e4c59d15f52f937547",
		Operator:  "test",
		CreatedAt: create,
		UpdatedAt: update,
	}}

	ids := []uint32{6}

	sqlRows := sqlmock.NewRows([]string{"flash_id", "flash_name", "flash_url", "flash_md5", "operator", "created_at", "updated_at"}).
		AddRow(entry[0].FlashId, entry[0].FlashName, entry[0].FlashUrl, entry[0].FlashMd5, entry[0].Operator, entry[0].CreatedAt, entry[0].UpdatedAt)

	mock, db := getMockDb()
	mock.ExpectQuery("SELECT * FROM `flash_effect_config` WHERE (flash_id in (?))").WithArgs(6).WillReturnRows(sqlRows)

	tests := []struct {
		name        string
		fields      fields
		args        args
		wantConfigs []*FlashEffectConfig
		wantErr     bool
	}{
		{name: "GetFlashConfigByIds", fields: fields{
			mysql: db,
			sc:    &conf.ServiceConfigT{},
		}, args: args{
			ctx:     context.Background(),
			giftIds: ids,
		}, wantConfigs: entry, wantErr: false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &Store{
				mysql: tt.fields.mysql,
				sc:    tt.fields.sc,
			}
			gotConfigs, err := st.GetFlashConfigByIds(tt.args.ctx, tt.args.giftIds)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFlashConfigByIds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotConfigs, tt.wantConfigs) {
				t.Errorf("GetFlashConfigByIds() gotConfigs = %v, want %v", gotConfigs, tt.wantConfigs)
			}
		})
	}
}

func TestStore_GetFlashTable(t *testing.T) {
	type fields struct {
		mysql *gorm.DB
		sc    *conf.ServiceConfigT
	}

	_, db := getMockDb()

	tests := []struct {
		name   string
		fields fields
		want   *gorm.DB
	}{
		{name: "GetFlashTable", fields: fields{
			mysql: db,
			sc:    &conf.ServiceConfigT{},
		}, want: db.Table("flash_effect_config"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &Store{
				mysql: tt.fields.mysql,
				sc:    tt.fields.sc,
			}
			if got := st.GetFlashTable(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetFlashTable() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStore_GetFloatConfigByIds(t *testing.T) {
	type fields struct {
		mysql *gorm.DB
		sc    *conf.ServiceConfigT
	}
	type args struct {
		ctx     context.Context
		giftIds []uint32
	}

	entry := make([]*PresentFloatLayer, 0)

	ids := []uint32{0}

	sqlRows := sqlmock.NewRows([]string{"flash_id", "flash_name", "flash_url", "flash_md5", "operator", "created_at", "updated_at"})

	mock, db := getMockDb()
	mock.ExpectQuery("SELECT * FROM `present_float_layer` WHERE (gift_id in (?))").WithArgs(0).WillReturnRows(sqlRows)

	tests := []struct {
		name        string
		fields      fields
		args        args
		wantConfigs []*PresentFloatLayer
		wantErr     bool
	}{
		{name: "GetFlashConfigByIds", fields: fields{
			mysql: db,
			sc:    &conf.ServiceConfigT{},
		}, args: args{
			ctx:     context.Background(),
			giftIds: ids,
		}, wantConfigs: entry, wantErr: false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &Store{
				mysql: tt.fields.mysql,
				sc:    tt.fields.sc,
			}
			gotConfigs, err := st.GetFloatConfigByIds(tt.args.ctx, tt.args.giftIds)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFloatConfigByIds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotConfigs, tt.wantConfigs) {
				t.Errorf("GetFloatConfigByIds() gotConfigs = %v, want %v", gotConfigs, tt.wantConfigs)
			}
		})
	}
}

func TestStore_GetFloatTable(t *testing.T) {
	type fields struct {
		mysql *gorm.DB
		sc    *conf.ServiceConfigT
	}
	tests := []struct {
		name   string
		fields fields
		want   *gorm.DB
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &Store{
				mysql: tt.fields.mysql,
				sc:    tt.fields.sc,
			}
			if got := st.GetFloatTable(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetFloatTable() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStore_GetLastUpdateTable(t *testing.T) {
	type fields struct {
		mysql *gorm.DB
		sc    *conf.ServiceConfigT
	}
	tests := []struct {
		name   string
		fields fields
		want   *gorm.DB
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &Store{
				mysql: tt.fields.mysql,
				sc:    tt.fields.sc,
			}
			if got := st.GetLastUpdateTable(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetLastUpdateTable() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStore_GetLastUpdateTime(t *testing.T) {
	type fields struct {
		mysql *gorm.DB
		sc    *conf.ServiceConfigT
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name       string
		fields     fields
		args       args
		wantUpdate time.Time
		wantErr    bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &Store{
				mysql: tt.fields.mysql,
				sc:    tt.fields.sc,
			}
			gotUpdate, err := st.GetLastUpdateTime(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLastUpdateTime() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotUpdate, tt.wantUpdate) {
				t.Errorf("GetLastUpdateTime() gotUpdate = %v, want %v", gotUpdate, tt.wantUpdate)
			}
		})
	}
}

func TestStore_GetMysql(t *testing.T) {
	type fields struct {
		mysql *gorm.DB
		sc    *conf.ServiceConfigT
	}
	tests := []struct {
		name   string
		fields fields
		want   *gorm.DB
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &Store{
				mysql: tt.fields.mysql,
				sc:    tt.fields.sc,
			}
			if got := st.GetMysql(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetMysql() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStore_GetPresentFlashByIds(t *testing.T) {
	type fields struct {
		mysql *gorm.DB
		sc    *conf.ServiceConfigT
	}
	type args struct {
		ctx     context.Context
		giftIds []uint32
	}

	entry := make([]*PresentFlashInfo, 0)

	ids := []uint32{0}

	sqlRows := sqlmock.NewRows([]string{"flash_id", "flash_name", "flash_url", "flash_md5", "operator", "created_at", "updated_at"})

	mock, db := getMockDb()
	mock.ExpectQuery("SELECT * FROM `flash_effect_config` WHERE (gift_id in (?))").WithArgs(0).WillReturnRows(sqlRows)

	tests := []struct {
		name        string
		fields      fields
		args        args
		wantConfigs []*PresentFlashInfo
		wantErr     bool
	}{
		{name: "GetPresentFlashByIds", fields: fields{
			mysql: db,
			sc:    &conf.ServiceConfigT{},
		}, args: args{
			ctx:     context.Background(),
			giftIds: ids,
		}, wantConfigs: entry, wantErr: false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &Store{
				mysql: db,
				sc:    tt.fields.sc,
			}
			gotConfigs, err := st.GetPresentFlashByIds(tt.args.ctx, tt.args.giftIds)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPresentFlashByIds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotConfigs, tt.wantConfigs) {
				t.Errorf("GetPresentFlashByIds() gotConfigs = %v, want %v", gotConfigs, tt.wantConfigs)
			}
		})
	}
}

func TestStore_GetPresentFlashTable(t *testing.T) {
	type fields struct {
		mysql *gorm.DB
		sc    *conf.ServiceConfigT
	}
	tests := []struct {
		name   string
		fields fields
		want   *gorm.DB
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &Store{
				mysql: tt.fields.mysql,
				sc:    tt.fields.sc,
			}
			if got := st.GetPresentFlashTable(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPresentFlashTable() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStore_InitTable(t *testing.T) {
	type fields struct {
		mysql *gorm.DB
		sc    *conf.ServiceConfigT
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &Store{
				mysql: tt.fields.mysql,
				sc:    tt.fields.sc,
			}
			if err := st.InitTable(); (err != nil) != tt.wantErr {
				t.Errorf("InitTable() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStore_SetLastUpdateTime(t *testing.T) {
	type fields struct {
		mysql *gorm.DB
		sc    *conf.ServiceConfigT
	}
	type args struct {
		ctx    context.Context
		update time.Time
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &Store{
				mysql: tt.fields.mysql,
				sc:    tt.fields.sc,
			}
			if err := st.SetLastUpdateTime(tt.args.ctx, tt.args.update); (err != nil) != tt.wantErr {
				t.Errorf("SetLastUpdateTime() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStore_Transaction(t *testing.T) {
	type fields struct {
		mysql *gorm.DB
		sc    *conf.ServiceConfigT
	}
	type args struct {
		ctx context.Context
		f   func(tx *gorm.DB) error
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &Store{
				mysql: tt.fields.mysql,
				sc:    tt.fields.sc,
			}
			if err := st.Transaction(tt.args.ctx, tt.args.f); (err != nil) != tt.wantErr {
				t.Errorf("Transaction() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStore_UpdateFlashConfig(t *testing.T) {
	type fields struct {
		mysql *gorm.DB
		sc    *conf.ServiceConfigT
	}
	type args struct {
		ctx   context.Context
		store *FlashEffectConfig
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &Store{
				mysql: tt.fields.mysql,
				sc:    tt.fields.sc,
			}
			if err := st.UpdateFlashConfig(tt.args.ctx, tt.args.store); (err != nil) != tt.wantErr {
				t.Errorf("UpdateFlashConfig() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStore_UpdateFloatConfig(t *testing.T) {
	type fields struct {
		mysql *gorm.DB
		sc    *conf.ServiceConfigT
	}
	type args struct {
		ctx   context.Context
		store *PresentFloatLayer
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &Store{
				mysql: tt.fields.mysql,
				sc:    tt.fields.sc,
			}
			if err := st.UpdateFloatConfig(tt.args.ctx, tt.args.store); (err != nil) != tt.wantErr {
				t.Errorf("UpdateFloatConfig() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStore_UpdatePresentFlash(t *testing.T) {
	type fields struct {
		mysql *gorm.DB
		sc    *conf.ServiceConfigT
	}
	type args struct {
		ctx    context.Context
		config *PresentFlashInfo
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &Store{
				mysql: tt.fields.mysql,
				sc:    tt.fields.sc,
			}
			if err := st.UpdatePresentFlash(tt.args.ctx, tt.args.config); (err != nil) != tt.wantErr {
				t.Errorf("UpdatePresentFlash() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStore_AddCustomizedCustomConfig(t *testing.T) {
	mock, db := getMockDb()

	entry := &CustomizedCustomConfig{
		PrimaryGiftId: 1,
		CustomText:    "www.baidu.com",
		CustomId:      1,
		CustomName:    "aa",
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	mock.ExpectBegin()
	mock.ExpectExec("INSERT INTO `customized_custom_config` (`primary_gift_id`,`custom_id`,`custom_name`,`custom_text`,`created_at`,`updated_at`) VALUES (?,?,?,?,?,?)").
		WithArgs(entry.PrimaryGiftId, entry.CustomId, entry.CustomName, entry.CustomText, entry.CreatedAt, entry.UpdatedAt).WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()

	mock.ExpectBegin()
	mock.ExpectExec("INSERT INTO `customized_custom_config` (`primary_gift_id`,`custom_id`,`custom_name`,`custom_text`,`created_at`,`updated_at`) VALUES (?,?,?,?,?,?)").
		WithArgs(entry.PrimaryGiftId, entry.CustomId, entry.CustomName, entry.CustomText, entry.CreatedAt, entry.UpdatedAt).WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()

	type fields struct {
		mysql *gorm.DB
		sc    *conf.ServiceConfigT
	}
	type args struct {
		ctx   context.Context
		store *CustomizedCustomConfig
		tx    *gorm.DB
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{name: "AddFlashConfig", fields: fields{
			mysql: db,
			sc:    &conf.ServiceConfigT{},
		}, args: args{
			ctx:   context.Background(),
			store: entry,
		}, wantErr: false},
		{name: "AddFlashConfig", fields: fields{
			mysql: db,
			sc:    &conf.ServiceConfigT{},
		}, args: args{
			ctx:   context.Background(),
			store: entry,
			tx:    db,
		}, wantErr: false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &Store{
				mysql: tt.fields.mysql,
				sc:    tt.fields.sc,
			}
			fmt.Println(tt.args.store)
			if err := st.AddCustomizedCustomConfig(tt.args.ctx, tt.args.store, tt.args.tx); (err != nil) != tt.wantErr {
				t.Errorf("AddFlashConfig() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStore_GetPresentEffectDayRecord(t *testing.T) {
	mock, db := getMockDb()
	mock.NewRows([]string{"uid", "gift_id", "effect_time", "time_append", "updated_at"}).
		AddRow(1, 1, 100, 100, 1)
	mock.NewRows([]string{"uid", "gift_id", "effect_time", "time_append", "updated_at"}).
		AddRow(1, 1, 200, 100, 1)
	rows := sqlxmock.NewRows([]string{"total_time"})
	rows.AddRow(200)
	mock.ExpectQuery("SELECT sum(time_append) as total_time FROM `present_effect_day_record`  WHERE (uid = ? and gift_id = ?) LIMIT 1").
		WithArgs(1, 1).WillReturnRows(rows)

	type fields struct {
		mysql *gorm.DB
		sc    *conf.ServiceConfigT
	}
	type args struct {
		ctx    context.Context
		uid    uint32
		giftId uint32
	}
	tests := []struct {
		name          string
		fields        fields
		args          args
		wantTotalTime uint32
		wantErr       bool
	}{
		{
			"aaa",
			fields{
				mysql: db,
				sc:    nil,
			},
			args{
				ctx:    nil,
				uid:    1,
				giftId: 1,
			},
			200, false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &Store{
				mysql: tt.fields.mysql,
				sc:    tt.fields.sc,
			}
			gotTotalTime, err := st.GetPresentEffectDayRecord(tt.args.ctx, tt.args.uid, tt.args.giftId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPresentEffectDayRecord() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			fmt.Println(gotTotalTime)
			if gotTotalTime != tt.wantTotalTime {
				t.Errorf("GetPresentEffectDayRecord() gotTotalTime = %v, want %v", gotTotalTime, tt.wantTotalTime)
			}
		})
	}
}

func TestStore_GetPresentEffectTimeRecordByGiftId(t *testing.T) {
	mock, db := getMockDb()

	mock.ExpectQuery("SELECT * FROM `present_effect_time_record`  WHERE (gift_id = ? and effect_begin = ?)").WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg()).WillReturnRows(sqlmock.NewRows([]string{"uid"}))

	type fields struct {
		mysql *gorm.DB
		sc    *conf.ServiceConfigT
	}
	type args struct {
		ctx    context.Context
		giftId uint32
		begin  uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantRec []*PresentEffectTimeRecord
		wantErr bool
	}{
		{
			name: "aaa",
			fields: fields{
				mysql: db,
				sc:    nil,
			},
			args: args{
				ctx:    nil,
				giftId: 1,
				begin:  1000,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &Store{
				mysql: tt.fields.mysql,
				sc:    tt.fields.sc,
			}
			gotRec, err := st.GetPresentEffectTimeRecordByGiftId(tt.args.ctx, tt.args.giftId, tt.args.begin)
			fmt.Println(gotRec, err)
			//if (err != nil) != tt.wantErr {
			//	t.Errorf("GetPresentEffectTimeRecordByGiftId() error = %v, wantErr %v", err, tt.wantErr)
			//	return
			//}
			//if !reflect.DeepEqual(gotRec, tt.wantRec) {
			//	t.Errorf("GetPresentEffectTimeRecordByGiftId() gotRec = %v, want %v", gotRec, tt.wantRec)
			//}
		})
	}
}

func TestStore_GetPresentEffectDayRecordNearlyExpire(t *testing.T) {
	mock, db := getMockDb()

	mock.ExpectQuery("select * from present_effect_day_record where is_limitless = 1 and (expire_day_count*86400 + unix_timestamp(updated_at) - unix_timestamp(CURRENT_TIMESTAMP())) <= 432000 AND (expire_day_count*86400 + unix_timestamp(updated_at) - unix_timestamp(CURRENT_TIMESTAMP())) >=0")

	type fields struct {
		mysql *gorm.DB
		sc    *conf.ServiceConfigT
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantResp []*PresentEffectDayRecord
		wantErr  bool
	}{
		{
			name: "aaa",
			fields: fields{
				mysql: db,
				sc:    nil,
			},
			args: args{
				ctx: nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &Store{
				mysql: tt.fields.mysql,
				sc:    tt.fields.sc,
			}
			gotResp, err := st.GetPresentEffectDayRecordNearlyExpire(tt.args.ctx)
			fmt.Println(gotResp, err)
		})
	}
}
