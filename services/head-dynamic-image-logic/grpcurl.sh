

grpcurl --plaintext --authority headimage.52tt.local -d '{"account":"tt110250759"}'    10.64.230.68:80 HeadImage.HeadImage.GetHeadImageMd5

grpcurl -v --plaintext --authority head-dynamic-image-logic.52tt.local  -d '{"uidList":[2404178]}' 10.64.230.68:80 head_dynamic_image.HeadDynamicImage.GetHeadDynamicImageMd5


grpcurl -v --plaintext --authority head-dynamic-image-logic.52tt.local  -d '{"uidList":[2404178]}' 10.64.230.68:80 head_dynamic_image.HeadDynamicImage.NotifyHeadDynamicImageUpdated




grpcurl -v --plaintext --authority head-dynamic-image-logic.52tt.local  -d '{"uid":2404178}' 10.64.230.68:80 head_dynamic_image.HeadDynamicImage.NotifyHeadDynamicImageUpdated




grpcurl -v --plaintext --authority head-dynamic-image-logic.52tt.local  -d '{"uid":2412355}' 10.64.223.135:80 head_dynamic_image.HeadDynamicImage.NotifyHeadDynamicImageUpdated



grpcurl --plaintext  --authority head-dynamic-image-logic.52tt.local -d '{"uid":2412355}' 10.64.230.68:80 head_dynamic_image.HeadDynamicImage.TestAsyncScanImage


grpcurl -v --plaintext --authority head-dynamic-image-logic.52tt.local  -d '{"uidList":[2464168,2567198,2594318]}' 10.64.230.68:80 head_dynamic_image.HeadDynamicImage.GetHeadDynamicImageMd5





grpcurl --plaintext --authority headimage.52tt.local -d '{"account":"tt110289464"}'    10.64.230.68:80 HeadImage.HeadImage.GetHeadImageMd5



grpcurl -v --plaintext --authority user-privilege.52tt.local -d '{"uid":2532765,"type":"DY_HEAD_IMAGE","privilegeId":1,"addSeconds":3000,"serverTime":**********,"orderId":"***********************************"}' \
  -rpc-header 'x-qw-traffic-mark:quicksilver-main-subenv'  -reflect-header 'x-qw-traffic-mark:quicksilver-main-subenv' 10.64.230.68:80 userprivilege.UserPrivilege.GiveUserPrivilege


grpcurl --plaintext --authority user-privilege.52tt.local -d '{"uidList":[2532765],"priType":"DY_HEAD_IMAGE"}'    10.64.230.68:80 userprivilege.UserPrivilege.BatchGetUserprivilegeList








