package hanlders

//
//func init() {
//	log.SetLevel(log.DebugLevel)
//}
//
//// go test -timeout 30s -run ^TestBatchGetAgentUid$ golang.52tt.com/services/guild-management/guild-management-http-logic/handlers -v -count=1
//func TestBatchGetAgentUid(t *testing.T) {
//
//	uids := []uint32{}
//	for i := 0; i < 102; i++ {
//		uids = append(uids, uint32(i+1))
//	}
//
//	models.GetModelServer().AnchorcontractCli, _ = anchorcontract_go.NewClient()
//	BatchGetAgentUid(context.Background(), uids)
//}
