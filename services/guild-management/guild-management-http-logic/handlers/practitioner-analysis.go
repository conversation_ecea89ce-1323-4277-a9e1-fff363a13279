package hanlders

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"golang.52tt.com/pkg/datahouse"
	"golang.52tt.com/pkg/log"
	common "golang.52tt.com/pkg/mapreduce"
	"golang.52tt.com/pkg/urrc"
	"golang.52tt.com/pkg/web"
	anchorcontract_go "golang.52tt.com/protocol/services/anchorcontract-go"
	liveStatsPb "golang.52tt.com/protocol/services/channel-live-stats"
	channelPb "golang.52tt.com/protocol/services/channelsvr"
	guildPb "golang.52tt.com/protocol/services/guildsvr"
	pb "golang.52tt.com/protocol/services/sign-anchor-stats"
	"golang.52tt.com/services/guild-management/guild-management-http-logic/models"
	api "golang.52tt.com/services/guild-management/guild-management-http-logic/models/gen-go"
	"golang.52tt.com/services/guild-management/guild-management-http-logic/utils"
)

const (
	//manthLayout = "2006/01"
	dayLayout = "2006/01/02"
)

func GetPractitionerMonthLyInfoList(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetPractitionerMonthLyInfoListReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPractitionerMonthLyInfoList Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugfWithCtx(ctx, "GetPractitionerMonthLyInfoList begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	_, hasPer, respCode, respMsg, permission := checkUserHasPer(ctx, uid, guildId, api.GuildMultiPlayDataType_MultiPlayPractitionerAnalysis.String())
	if !hasPer {
		web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}

	resp := &api.GetPractitionerMonthLyInfoListResp{}

	var queryUidList []uint32
	if len(req.GetTtidList()) > 0 {
		queryUidList, err = Tid2Uid(ctx, req.GetTtidList())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetPractitionerMonthLyInfoList Tid2Uid failed: err: %+v", err)
			_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
			return
		}
		if len(queryUidList) == 0 {
			log.DebugfWithCtx(ctx, "GetPractitionerMonthLyInfoList end req:%v resp:%v", req, resp)
			_ = web.ServeAPIJson(w, resp)
			return
		}
	}

	statResp, err := models.GetModelServer().SignAnchorStatsCli.GetMultiAnchorMonthStat(ctx, &pb.GetMultiAnchorMonthStatReq{
		GuildId:    req.GetGuildId(),
		Offset:     (req.GetPage() - 1) * req.GetPageSize(),
		Limit:      req.GetPageSize(),
		Uid:        queryUidList,
		StartTime:  req.GetMonthTs(),
		EndTime:    req.GetMonthTs(),
		AnchorType: 0,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPractitionerMonthLyInfoList GetMultiAnchorMonthStat failed: req:%v err: %+v", req, err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	resp.TotalCnt = statResp.Total
	resp.NextPage = req.GetPage() + 1
	if len(statResp.GetList()) < int(req.GetPageSize()) {
		resp.NextPage = 0
	}

	uidList := make([]uint32, 0)
	mapUid2Stats := make(map[uint32]*pb.MultiAnchorMonthStatItem, 0)
	for _, item := range statResp.GetList() {
		uidList = append(uidList, item.GetUid())
		mapUid2Stats[item.GetUid()] = item
	}

	uidList = utils.ClearRepeatUint32(uidList)
	userMap, err := models.GetModelServer().AccountCli.GetUsersMap(ctx, uidList)
	if err != nil {
		fmt.Printf("GetPractitionerMonthLyInfoList GetUsersMap err:%v, uids:%v", err, uidList)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	tidList := make([]string, 0)
	for _, v := range userMap {
		tidList = append(tidList, v.GetAlias())
	}

	mapUid2FiveHourActiveCnt := make(map[uint32]uint32, 0)
	mapUid2SevenHourActiveCnt := make(map[uint32]uint32, 0)
	mapUid2ConsumeRelationChains := make(map[uint32]uint32, 0)
	guildMemInfoList := make([]*datahouse.GuildMemberInfo, 0)
	mapTid2Violations := make(map[string][]uint32, 0)
	mapStr2ViolationsTs := make(map[string][]uint32, 0)

	monthTm := time.Unix(int64(req.GetMonthTs()), 0)
	beginTm := time.Date(monthTm.Year(), monthTm.Month(), 1, 0, 0, 0, 0, time.Local)
	endTm := beginTm.AddDate(0, 1, 0)

	getPractitionerGuildMonthlyData := func() error {
		if len(uidList) == 0 {
			return nil
		}

		mapUid2FiveHourActiveCnt, mapUid2SevenHourActiveCnt, mapUid2ConsumeRelationChains, err = datahouse.GetPractitionerGuildMonthlyDataFromDH(guildId, uidList, monthTm, monthTm)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetPractitionerMonthLyInfoList GetPractitionerGuildMonthlyDataFromDH failed size:%d req:%+v err: %+v", len(uidList), req, err)
			return err
		}

		return nil
	}

	queryGuildMemberMonthInfo := func() error {
		if len(uidList) == 0 {
			return nil
		}

		guildMemInfoList, err = datahouse.QueryGuildMemberMonthInfo(ctx, uidList, req.GetGuildId(), monthTm, monthTm)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAnchorPractitionerMonthLyInfoList QueryGuildMemberMonthInfo failed size:%d req:%+v err: %+v", len(uidList), req, err)
			return err
		}

		return nil
	}

	getPractitionerViolationsInfo := func() error {
		if len(tidList) == 0 {
			return nil
		}

		mapTid2Violations, mapStr2ViolationsTs, _, _, err = urrc.GetPractitionerViolationsInfoFromAudit(tidList, beginTm, endTm)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetPractitionerMonthLyInfoList GetPractitionerViolationsInfoFromAudit failed size:%d req:%+v err: %+v", len(uidList), req, err)
			return err
		}

		return nil
	}

	err = common.Finish(getPractitionerGuildMonthlyData, queryGuildMemberMonthInfo, getPractitionerViolationsInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPractitionerMonthLyInfoList common.Finish failed uid:%d guildId:%d err:%v", uid, guildId, err)
	}

	mapId2GuildMemInfo := make(map[uint32]*datahouse.GuildMemberInfo, 0)
	for _, info := range guildMemInfoList {
		mapId2GuildMemInfo[info.UserId] = info
	}

	contractMap := make(map[uint32]*anchorcontract_go.ContractInfo)
	contractInfoReq := &anchorcontract_go.BatchGetUserContractReq{UidList: uidList}
	contractInfoResp, err := models.GetModelServer().AnchorcontractCli.BatchGetUserContract(ctx, uid, contractInfoReq)
	if err != nil {
		fmt.Printf("GetPractitionerMonthLyInfoList BatchGetUserContract err:%v, uids:%v", err, uidList)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	for _, c := range contractInfoResp.ContractList {
		contractMap[c.GetActorUid()] = c
	}

	for _, tmpUid := range uidList {
		var nick, tid string
		var signTs uint32 = 0
		if u, ok := userMap[tmpUid]; ok {
			tid = u.GetAlias()
			nick = u.GetNickname()
		}

		if c, ok := contractMap[tmpUid]; ok {
			signTs = c.GetSignTime()
		}

		vioList := make([]*api.ViolationsInfo, 0)
		if vio, ok := mapTid2Violations[tid]; ok {
			for index, v := range vio {
				vioList = append(vioList, &api.ViolationsInfo{
					ViolationsType: uint32(MapIndex2VType[index]),
					ViolationsCnt:  v,
					TsList:         mapStr2ViolationsTs[fmt.Sprintf("%s-%d", tid, index)],
				})
			}
		}

		item := &pb.MultiAnchorMonthStatItem{}
		if stats, ok := mapUid2Stats[tmpUid]; ok {
			item = stats
		}

		memInfo := &datahouse.GuildMemberInfo{}
		if info, ok := mapId2GuildMemInfo[tmpUid]; ok {
			memInfo = info
		}

		if memInfo.IsNewSign != "是" {
			memInfo.IsNewSign = "否"
		}
		if memInfo.IsVaild != "是" {
			memInfo.IsVaild = "否"
		}
		if memInfo.IsPro != "是" {
			memInfo.IsPro = "否"
		}

		row := &api.PractitionerMonthLyInfo{
			Ts: req.GetMonthTs(),
			BaseInfo: &api.PractitionerBaseInfo{
				Uid:            tmpUid,
				Nickname:       nick,
				Ttid:           tid,
				SignTs:         signTs,
				Income:         uint32(item.GetFee()),
				ViolationsList: vioList,
				PkgFeeRatio:    float32(memInfo.PkgGiftRatio),
				IsNewSign:      memInfo.IsNewSign,
				IsVaild:        memInfo.IsVaild,
				IsPro:          memInfo.IsPro,
			},
			ActiveDayList:         []uint32{mapUid2SevenHourActiveCnt[tmpUid], mapUid2FiveHourActiveCnt[tmpUid]},
			ValidLiveDays:         item.GetMonthValidDay(),
			ConsumeRelationChains: mapUid2ConsumeRelationChains[tmpUid],
		}

		if len(vioList) == 3 {
			if (mapUid2SevenHourActiveCnt[tmpUid]+mapUid2FiveHourActiveCnt[tmpUid]) >= QualityMinMonthActiveDayCnt && mapUid2ConsumeRelationChains[tmpUid] >= QualityMinMonthPayChains && vioList[0].ViolationsCnt <= maxMonthAViolationsCnt &&
				vioList[1].ViolationsCnt <= maxMonthBViolationsCnt && vioList[2].ViolationsCnt <= maxMonthCViolationsCnt && row.GetValidLiveDays() >= QualityMinMonthValidDayCnt &&
				row.GetBaseInfo().GetIncome() >= QualityMinMonthIncome {
				// 满足优质条件，更新能力维度值
				row.BaseInfo.AbilityType = uint32(pb.AbilityType_Ability_Quality)
			}
		}

		resp.InfoList = append(resp.InfoList, GetPractitionerMonthLyInfoByPermission(row, permission))
	}

	log.DebugfWithCtx(ctx, "GetPractitionerMonthLyInfoList end req:%v resp:%v", req, resp)
	_ = web.ServeAPIJson(w, resp)
}

func GetPractitionerDailyInfoList(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetPractitionerDailyInfoListReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPractitionerDailyInfoList Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugfWithCtx(ctx, "GetPractitionerDailyInfoList begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	_, hasPer, respCode, respMsg, permission := checkUserHasPer(ctx, uid, guildId, api.GuildMultiPlayDataType_MultiPlayPractitionerAnalysis.String())
	if !hasPer {
		web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}

	resp := &api.GetPractitionerDailyInfoListResp{}

	var queryUidList []uint32
	if len(req.GetTtidList()) > 0 {
		queryUidList, err = Tid2Uid(ctx, req.GetTtidList())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetPractitionerDailyInfoList Tid2Uid failed: err: %+v", err)
			_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
			return
		}
		if len(queryUidList) == 0 {
			log.DebugfWithCtx(ctx, "GetPractitionerDailyInfoList end req:%v resp:%v", req, resp)
			_ = web.ServeAPIJson(w, resp)
			return
		}
	}

	statResp, err := models.GetModelServer().SignAnchorStatsCli.GetMultiAnchorDailyStatsList(ctx, &pb.GetMultiAnchorDailyStatsListReq{
		GuildId: guildId,
		UidList: queryUidList,
		BeginTs: req.GetBeginTs(),
		EndTs:   req.GetEndTs(),
		Offset:  (req.GetPage() - 1) * req.GetPageSize(),
		Limit:   req.GetPageSize(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPractitionerDailyInfoList GetMultiAnchorDailyStatsList failed: req:%+v err: %+v", req, err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	resp.TotalCnt = statResp.GetTotalCnt()
	resp.NextPage = req.GetPage() + 1
	if len(statResp.GetList()) < int(req.GetPageSize()) {
		resp.NextPage = 0
	}

	uidList := make([]uint32, 0)
	mapUid2Stats := make(map[uint32]*pb.MultiAnchorDailyStats, 0)
	for _, item := range statResp.GetList() {
		uidList = append(uidList, item.GetUid())
		mapUid2Stats[item.GetUid()] = item
	}

	// 去重
	uidList = utils.ClearRepeatUint32(uidList)

	beginTm := time.Unix(int64(req.GetBeginTs()), 0)
	endTm := time.Unix(int64(req.GetEndTs()), 0)
	lastMonthTm := time.Date(beginTm.Year(), beginTm.Month(), 1, 0, 0, 0, 0, time.Local).AddDate(0, -1, 0)

	mapUid2MonthStats := make(map[uint32]*pb.MultiAnchorMonthStatItem, 0)
	if len(uidList) != 0 {
		// 获取上个月的能力维度
		monthStatResp, err := models.GetModelServer().SignAnchorStatsCli.GetMultiAnchorMonthStat(ctx, &pb.GetMultiAnchorMonthStatReq{
			GuildId:    guildId,
			Offset:     0,
			Limit:      uint32(len(uidList)),
			Uid:        uidList,
			StartTime:  uint32(lastMonthTm.Unix()),
			EndTime:    uint32(lastMonthTm.Unix()),
			AnchorType: 0,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "GetPractitionerDailyInfoList GetMultiAnchorMonthStat failed: req:%+v err: %+v", req, err)
			_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
			return
		}

		for _, item := range monthStatResp.GetList() {
			mapUid2MonthStats[item.GetUid()] = item
		}
	}

	userMap, err := models.GetModelServer().AccountCli.GetUsersMap(ctx, uidList)
	if err != nil {
		fmt.Printf("GetPractitionerDailyInfoList GetUsersMap err:%v, uids:%v", err, uidList)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	tidList := make([]string, 0)
	for _, v := range userMap {
		tidList = append(tidList, v.GetAlias())
	}

	mapUid2DailyData := make(map[string][]uint32, 0)
	guildMemInfoList := make([]*datahouse.GuildMemberInfo, 0)
	mapTid2DateViolationsCnt := make(map[string][]uint32, 0)

	getPractitionerGuildDailyData := func() error {
		if len(uidList) == 0 {
			return nil
		}

		mapUid2DailyData, err = datahouse.GetPractitionerGuildDailyDataFromDH(guildId, uidList, beginTm, endTm)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetPractitionerDailyInfoList GetPractitionerGuildDailyDataFromDH failed size:%d req:%+v err: %+v", len(uidList), req, err)
			return err
		}

		return nil
	}

	queryGuildMemberDailyInfo := func() error {
		if len(uidList) == 0 {
			return nil
		}

		guildMemInfoList, err = datahouse.QueryGuildMemberDailyInfo(ctx, uidList, req.GetGuildId(), beginTm, endTm)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetPractitionerDailyInfoList QueryGuildMemberDailyInfo failed size:%d req:%+v err: %+v", len(uidList), req, err)
			return err
		}

		return nil
	}

	getPractitionerViolationsInfo := func() error {
		if len(tidList) == 0 {
			return nil
		}

		_, _, mapTid2DateViolationsCnt, _, err = urrc.GetPractitionerViolationsInfoFromAudit(tidList, beginTm, endTm)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetPractitionerDailyInfoList GetPractitionerViolationsInfoFromAudit failed size:%d req:%+v err: %+v", len(uidList), req, err)
			return err
		}

		return nil
	}

	err = common.Finish(getPractitionerGuildDailyData, queryGuildMemberDailyInfo, getPractitionerViolationsInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPractitionerDailyInfoList common.Finish failed uid:%d guildId:%d err:%v", uid, guildId, err)
	}

	mapId2PkgGiftRatio := make(map[string]float64, 0)
	for _, info := range guildMemInfoList {
		mapId2PkgGiftRatio[fmt.Sprintf("%d-%s", info.UserId, info.DataDate)] = info.PkgGiftRatio
	}

	for _, item := range statResp.List {
		var nick, tid string
		if u, ok := userMap[item.GetUid()]; ok {
			tid = u.GetAlias()
			nick = u.GetNickname()
		}

		dateTm := time.Unix(int64(item.GetDate()), 0)

		vioList := make([]*api.ViolationsInfo, 0)
		if dateVio, okA := mapTid2DateViolationsCnt[fmt.Sprintf("%s-%s", tid, dateTm.Format(dayLayout))]; okA {
			for index, v := range dateVio {
				vioList = append(vioList, &api.ViolationsInfo{
					ViolationsType: uint32(MapIndex2VType[index]),
					ViolationsCnt:  v,
				})
			}
		}

		row := &api.PractitionerDailyInfo{
			Ts: item.GetDate(),
			BaseInfo: &api.PractitionerBaseInfo{
				Uid:            item.GetUid(),
				Nickname:       nick,
				Ttid:           tid,
				AbilityType:    mapUid2MonthStats[item.GetUid()].GetAbilityType(),
				Income:         uint32(item.GetAnchorIncome()),
				ViolationsList: vioList,
				PkgFeeRatio:    float32(mapId2PkgGiftRatio[fmt.Sprintf("%d-%s", item.GetUid(), dateTm.Format("20060102"))]),
			},
			ValidLiveTs: item.GetValidSec(),
		}

		if len(mapUid2DailyData[fmt.Sprintf("%d-%04d%02d%02d", item.GetUid(), dateTm.Year(), dateTm.Month(), dateTm.Day())]) == 3 {
			row.OnlineTs = mapUid2DailyData[fmt.Sprintf("%d-%04d%02d%02d", item.GetUid(), dateTm.Year(), dateTm.Month(), dateTm.Day())][0]
			row.ValidCmsgCnt = mapUid2DailyData[fmt.Sprintf("%d-%04d%02d%02d", item.GetUid(), dateTm.Year(), dateTm.Month(), dateTm.Day())][1]
			row.ValidSenderCnt = mapUid2DailyData[fmt.Sprintf("%d-%04d%02d%02d", item.GetUid(), dateTm.Year(), dateTm.Month(), dateTm.Day())][2]
		}

		resp.InfoList = append(resp.InfoList, GetPractitionerDailyInfoByPermission(row, permission))
	}

	log.DebugfWithCtx(ctx, "GetPractitionerDailyInfoList end req:%v resp:%v", req, resp)
	_ = web.ServeAPIJson(w, resp)
}

func GetPgcMonthlyInfoList(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetPgcMonthlyInfoListReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPgcMonthlyInfoList Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugfWithCtx(ctx, "GetPgcMonthlyInfoList begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	_, hasPer, respCode, respMsg, permission := checkUserHasPer(ctx, uid, guildId, api.GuildMultiPlayDataType_MultiPlayPractitionerAnalysis.String())
	if !hasPer {
		web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}

	resp := &api.GetPgcMonthlyInfoListResp{}

	var queryCidList []uint32
	if len(req.GetDisplayIdList()) > 0 {
		queryCidList, err = DisplayId2ChannelId(ctx, req.GetDisplayIdList())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetPgcMonthlyInfoList DisplayId2ChannelId err:%+v, req:%+v", err, req)
			_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
			return
		}
		if len(queryCidList) == 0 {
			log.DebugfWithCtx(ctx, "GetPgcMonthlyInfoList end req:%v resp:%v", req, resp)
			_ = web.ServeAPIJson(w, resp)
			return
		}
	}

	statResp, err := models.GetModelServer().SignAnchorStatsCli.GetPgcMonthlyInfoList(ctx, &pb.GetPgcMonthlyInfoListReq{
		MonthTs:  req.GetMonthTs(),
		CidList:  queryCidList,
		Page:     req.GetPage(),
		PageSize: req.GetPageSize(),
		GuildId:  req.GetGuildId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPgcMonthlyInfoList GetPgcMonthlyInfoList failed: req:%+v err: %+v", req, err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	cidList := make([]uint32, 0)
	for _, item := range statResp.GetInfoList() {
		cidList = append(cidList, item.GetChannelId())
	}

	guildChannelInfoList := make([]*datahouse.GuildChannelInfo, 0)
	mapCid2SimpleInfo := make(map[uint32]*channelPb.ChannelSimpleInfo, 0)
	mapCid2Tag := make(map[uint32]uint32, 0)

	monthTm := time.Unix(int64(req.GetMonthTs()), 0)
	queryGuildChannelMonthInfo := func() error {
		if len(cidList) == 0 {
			return nil
		}

		guildChannelInfoList, err = datahouse.QueryGuildChannelMonthInfo(ctx, cidList, guildId, monthTm, monthTm)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetPractitionerDailyInfoList queryGuildChannelMonthInfo failed size:%d req:%+v err: %+v", len(cidList), req, err)
			return err
		}

		return nil
	}

	batchGetChannelSimpleInfo := func() error {
		if len(cidList) == 0 {
			return nil
		}

		mapCid2SimpleInfo, err = models.GetModelServer().ChannelCli.BatchGetChannelSimpleInfo(ctx, uid, cidList)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetPgcMonthlyInfoList BatchGetChannelSimpleInfo failed ids(%v) err(%v)", cidList, err)
			return err
		}

		return nil
	}

	getChannelTag := func() error {
		if len(cidList) == 0 {
			return nil
		}

		mapCid2Tag, err = getChannelTag(ctx, cidList)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetPgcMonthlyInfoList getChannelTag failed ids(%v) err(%v)", cidList, err)
			return err
		}

		return nil
	}

	err = common.Finish(queryGuildChannelMonthInfo, batchGetChannelSimpleInfo, getChannelTag)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPgcMonthlyInfoList common.Finish failed uid:%d guildId:%d err:%v", uid, guildId, err)
	}

	mapCid2GuildChannelInfo := make(map[uint32]*datahouse.GuildChannelInfo, 0)
	for _, info := range guildChannelInfoList {
		mapCid2GuildChannelInfo[info.RoomId] = info
	}

	for _, item := range statResp.GetInfoList() {
		var cName string
		if c, ok := mapCid2SimpleInfo[item.GetChannelId()]; ok {
			cName = c.GetName()
		}

		guildChannelInfo := &datahouse.GuildChannelInfo{}
		if info, ok := mapCid2GuildChannelInfo[item.GetChannelId()]; ok {
			guildChannelInfo = info
		}

		row := &api.PgcMonthlyInfo{
			Ts: item.GetTs(),
			BaseInfo: &api.PgcBaseInfo{
				Cid:        item.GetChannelId(),
				DisplayId:  mapCid2SimpleInfo[item.GetChannelId()].GetChannelViewId(),
				Name:       cName,
				ChannelTag: MapTagId2Name[mapCid2Tag[item.GetChannelId()]],
				ChannelFee: item.GetTotalChannelFee(),
			},
			ValidAnchorCnt:          item.GetValidAnchorCnt(),
			ActiveAnchorCnt:         item.GetActiveAnchorCnt(),
			QualityAnchorCnt:        item.GetQualityAnchorCnt(),
			ActiveAnchorFee:         item.GetActiveAnchorFee(),
			QualityAnchorFee:        item.GetQualityAnchorFee(),
			QualityAnchorCntRatio:   item.GetQualityAnchorCntRatio(),
			QualityAnchorFeeRatio:   item.GetQualityAnchorFeeRatio(),
			QualityAnchorTransRatio: item.GetQualityAnchorTransRatio(),
			ChannelPkgFeeRatio:      item.ChannelPkgFeeRatio,
			ProfessionPracCnt:       guildChannelInfo.ProAnchor,
			ValidSignMemberCnt:      item.ValidSignMemberCnt,
		}

		resp.InfoList = append(resp.InfoList, GetPgcMonthlyInfoByPermission(row, permission))
	}

	resp.TotalCnt = statResp.GetTotalCnt()
	resp.NextPage = statResp.GetNextPage()

	log.DebugfWithCtx(ctx, "GetPgcMonthlyInfoList end req:%v resp:%v", req, resp)
	_ = web.ServeAPIJson(w, resp)
}

func GetPgcDailyInfoList(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetPgcDailyInfoListReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPgcDailyInfoList Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugfWithCtx(ctx, "GetPgcDailyInfoList begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	_, hasPer, respCode, respMsg, permission := checkUserHasPer(ctx, uid, guildId, api.GuildMultiPlayDataType_MultiPlayPractitionerAnalysis.String())
	if !hasPer {
		web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}

	resp := &api.GetPgcDailyInfoListResp{}

	var queryCidList []uint32
	if len(req.GetDisplayIdList()) > 0 {
		queryCidList, err = DisplayId2ChannelId(ctx, req.GetDisplayIdList())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetPgcDailyInfoList DisplayId2ChannelId err:%+v, req:%+v", err, req)
			_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
			return
		}
		if len(queryCidList) == 0 {
			log.DebugfWithCtx(ctx, "GetPgcDailyInfoList end req:%v resp:%v", req, resp)
			_ = web.ServeAPIJson(w, resp)
			return
		}
	}

	statResp, err := models.GetModelServer().SignAnchorStatsCli.GetPgcDailyInfoList(ctx, &pb.GetPgcDailyInfoListReq{
		BeginTs:  req.GetBeginTs(),
		EndTs:    req.GetEndTs(),
		CidList:  queryCidList,
		Page:     req.GetPage(),
		PageSize: req.GetPageSize(),
		GuildId:  req.GetGuildId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPgcDailyInfoList GetPgcDailyInfoList failed: req:%+v err: %+v", req, err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	cidList := make([]uint32, 0)
	for _, item := range statResp.GetInfoList() {
		cidList = append(cidList, item.GetChannelId())
	}

	mapCid2SimpleInfo, err := models.GetModelServer().ChannelCli.BatchGetChannelSimpleInfo(ctx, uid, cidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPgcDailyInfoList BatchGetChannelSimpleInfo failed ids(%v) err(%v)", cidList, err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	mapCid2Tag, err := getChannelTag(ctx, cidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPgcDailyInfoList getChannelTag failed ids(%v) err(%v)", cidList, err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	for _, item := range statResp.GetInfoList() {
		var cName string
		if c, ok := mapCid2SimpleInfo[item.GetChannelId()]; ok {
			cName = c.GetName()
		}

		row := &api.PgcDailyInfo{
			Ts: item.GetTs(),
			BaseInfo: &api.PgcBaseInfo{
				Cid:        item.GetChannelId(),
				DisplayId:  mapCid2SimpleInfo[item.GetChannelId()].GetChannelViewId(),
				Name:       cName,
				ChannelTag: MapTagId2Name[mapCid2Tag[item.GetChannelId()]],
				ChannelFee: item.GetChannelFee(),
			},
			AnchorFee:          item.GetAnchorFee(),
			PkgGiftFee:         item.GetPkgGiftFee(),
			TbeanGiftFee:       item.GetTbeanGiftFee(),
			SendGiftCnt:        item.GetSendGiftCnt(),
			ValidAnchorCntList: []uint32{item.GetOneHourValidCnt(), item.GetTwoHourValidCnt(), item.GetFourHourValidCnt()},
			ChannelPkgFeeRatio: CulRatioByN(uint64(item.GetPkgGiftFee()), item.GetChannelFee(), 4),
		}

		resp.InfoList = append(resp.InfoList, GetPgcDailyInfoByPermission(row, permission))
	}

	resp.TotalCnt = statResp.GetTotalCnt()
	resp.NextPage = statResp.GetNextPage()

	log.DebugfWithCtx(ctx, "GetPgcDailyInfoList end req:%v resp:%v", req, resp)
	_ = web.ServeAPIJson(w, resp)
}

func GetGuildMonthlyStatsInfoList(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetGuildMonthlyStatsInfoListReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildMonthlyStatsInfoList Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugWithCtx(ctx, "GetGuildMonthlyStatsInfoList begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	_, hasPer, respCode, respMsg, permission := checkUserHasPer(ctx, uid, guildId, api.GuildMultiPlayDataType_MultiPlayPractitionerAnalysis.String())
	if !hasPer {
		web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}

	resp := &api.GetGuildMonthlyStatsInfoListResp{}

	statResp, err := models.GetModelServer().SignAnchorStatsCli.GetGuildMonthlyStatsInfoList(ctx, &pb.GetGuildMonthlyStatsInfoListReq{
		BeginMonth: req.GetBeginMonth(),
		EndMonth:   req.GetEndMonth(),
		GuildId:    req.GetGuildId(),
		Page:       req.GetPage(),
		PageSize:   req.GetPageSize(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildMonthlyStatsInfoList GetGuildMonthlyStatsInfoList failed: req:%+v err: %+v", req, err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	guildIdList := statResp.GetGuildIdList()

	beginMonth := time.Unix(int64(req.GetBeginMonth()), 0)
	endMonth := time.Unix(int64(req.GetEndMonth()), 0)

	guildResp := &guildPb.GetGuildBatResp{}
	guildMonthInfoList := make([]*datahouse.GuildMonthInfo, 0)

	getGuildBat := func() error {
		if len(guildIdList) == 0 {
			return nil
		}

		guildResp, err = models.GetModelServer().GuildCli.GetGuildBat(ctx, 0, &guildPb.GetGuildBatReq{GuildIdList: guildIdList, WithDismissed: false})
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGuildMonthlyStatsInfoList GetGuildBat failed req:%v err(%v)", req, err)
			return err
		}

		return nil
	}

	queryGuildMonthInfo := func() error {
		guildMonthInfoList, err = datahouse.QueryGuildMonthInfo(ctx, guildId, beginMonth, endMonth)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGuildMonthlyStatsInfoList QueryGuildMonthInfo failed req:%v err(%v)", req, err)
			return err
		}

		return nil
	}

	err = common.Finish(getGuildBat, queryGuildMonthInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildMonthlyStatsInfoList common.Finish failed uid:%d guildId:%d err:%v", uid, guildId, err)
	}

	mapKey2GuildMonthInfo := make(map[string]*datahouse.GuildMonthInfo, 0)
	for _, info := range guildMonthInfoList {
		mapKey2GuildMonthInfo[info.DataMonth] = info
	}

	guildMap := make(map[uint32]*guildPb.GuildResp, 0)
	for _, gui := range guildResp.GetGuildList() {
		guildMap[gui.GuildId] = gui
	}

	idList := make([]uint32, 0)
	guildName := ""
	for index, guildId := range statResp.GetGuildIdList() {
		if guildInfo, ok := guildMap[guildId]; ok {
			if guildInfo.GetShortId() != 0 {
				idList = append(idList, guildInfo.GetShortId())
			} else {
				idList = append(idList, guildInfo.GetGuildId())
			}

			if index == 0 {
				guildName = guildInfo.GetName()
			}
		}
	}

	resp.GuildIdList = idList
	resp.GuildName = guildName

	for _, item := range statResp.GetInfoList() {
		tm := time.Unix(int64(item.GetTs()), 0)

		guildMonthInfo := &datahouse.GuildMonthInfo{}
		if info, ok := mapKey2GuildMonthInfo[tm.Format("200601")]; ok {
			guildMonthInfo = info
		}
		row := &api.GuildMonthlyStatsInfo{
			Ts:                        item.GetTs(),
			GuildFee:                  item.GetGuildFee(),
			SignAnchorCnt:             item.GetSignAnchorCnt(),
			ActiveAnchorCnt:           item.GetActiveAnchorCnt(),
			QualityAnchorCnt:          item.GetQualityAnchorCnt(),
			ActiveAnchorFee:           item.GetActiveAnchorFee(),
			QualityAnchorFee:          item.GetQualityAnchorFee(),
			QualityAnchorCntRatio:     item.GetQualityAnchorCntRatio(),
			QualityAnchorFeeRatio:     item.GetQualityAnchorFeeRatio(),
			GuildPkgFeeRatio:          item.GetGuildPkgFeeRatio(),
			ProfessionPracCnt:         guildMonthInfo.ProAnchor,
			NewProfessionPracCnt:      guildMonthInfo.NewProAnchorCnt,
			ValidSignMemberCnt:        item.GetValidSignMemberCnt(),
			NewValidSignMemCnt:        item.GetNewValidSignMemCnt(),
			ProfessionPracRetainCnt:   guildMonthInfo.ProAnchorRemainCnt,
			ProfessionPracRetainRatio: float32(guildMonthInfo.ProAnchorRemainRatio),
			NewPracCnt:                guildMonthInfo.NewAnchorCnt,
			NewPracRetainCnt:          guildMonthInfo.NewProAnchorRemainCnt,
			NewPracRetainRatio:        float32(guildMonthInfo.NewProAnchorRemainRatio),
		}

		log.InfoWithCtx(ctx, "GetGuildMonthlyStatsInfoList uid:%d guildId:%d guildMonthInfo:%v info:%v tm:%s item:%v",
			uid, guildId, guildMonthInfo, mapKey2GuildMonthInfo[tm.Format("200601")], tm.Format("200601"), item)

		resp.InfoList = append(resp.InfoList, GetGuildMonthlyStatsInfoByPermission(row, permission))
	}

	resp.TotalCnt = statResp.GetTotalCnt()
	resp.NextPage = statResp.GetNextPage()

	log.InfoWithCtx(ctx, "GetGuildMonthlyStatsInfoList end uid:%d req:%v resp:%v statResp:%v", uid, req, resp, statResp)
	_ = web.ServeAPIJson(w, resp)
}

func GetAnchorPractitionerMonthLyInfoList(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetAnchorPractitionerMonthLyInfoListReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorPractitionerMonthLyInfoList Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugfWithCtx(ctx, "GetAnchorPractitionerMonthLyInfoList begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	accountRole, hasPer, respCode, respMsg, permission := checkUserHasPer(ctx, uid, guildId, api.GuildLiveDataMenuType_AnchorPractitionerAnalysis.String())
	if !hasPer {
		web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}

	resp := &api.GetAnchorPractitionerMonthLyInfoListResp{}

	var queryUidList []uint32
	if len(req.GetTtidList()) > 0 {
		queryUidList, err = Tid2Uid(ctx, req.GetTtidList())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAnchorPractitionerMonthLyInfoList Tid2Uid failed: err: %+v", err)
			_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
			return
		}
		if len(queryUidList) == 0 {
			log.DebugfWithCtx(ctx, "GetAnchorPractitionerMonthLyInfoList end req:%v resp:%v", req, resp)
			_ = web.ServeAPIJson(w, resp)
			return
		}
	}

	var agentUid uint32
	if accountRole == AccountRoleBroker {
		agentUid = uid
	}

	statResp, err := models.GetModelServer().LiveStatsCli.GetAnchorMonthlyStatsListV2(ctx, &liveStatsPb.GetAnchorMonthlyStatsListReq{
		GuildId:    req.GetGuildId(),
		BeginMonth: req.GetMonthTs(),
		EndMonth:   req.GetMonthTs(),
		Offset:     (req.GetPage() - 1) * req.GetPageSize(),
		Limit:      req.GetPageSize(),
		AgentUid:   agentUid,
		AnchorList: queryUidList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorPractitionerMonthLyInfoList GetAnchorMonthlyStatsListV2 failed: req:%v err: %+v", req, err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	resp.TotalCnt = statResp.GetTotalCnt()
	resp.NextPage = req.GetPage() + 1
	if len(statResp.GetList()) < int(req.GetPageSize()) {
		resp.NextPage = 0
	}

	uidList := make([]uint32, 0)
	agentUidList := make([]uint32, 0)
	mapUid2IsAnchor := make(map[uint32]bool, 0)
	mapUid2Stats := make(map[uint32]*liveStatsPb.AnchorMonthlyStats, 0)
	for _, item := range statResp.GetList() {
		uidList = append(uidList, item.GetUid())
		mapUid2Stats[item.GetUid()] = item
		mapUid2IsAnchor[item.GetUid()] = true

		agentUidList = append(agentUidList, item.GetAgentUid())
	}

	monthTm := time.Unix(int64(req.GetMonthTs()), 0)
	activeInfoList, err := datahouse.QueryGuildMemberMonthInfo(ctx, uidList, req.GetGuildId(), monthTm, monthTm)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorPractitionerMonthLyInfoList QueryGuildMemberMonthInfo failed size:%d req:%+v err: %+v", len(uidList), req, err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	mapId2ActiveInfo := make(map[uint32]*datahouse.GuildMemberInfo, 0)
	for _, info := range activeInfoList {
		mapId2ActiveInfo[info.UserId] = info
	}

	payInfoList, err := datahouse.QueryMemberChainMonthInfo(ctx, req.GetGuildId(), uidList, monthTm, monthTm)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorPractitionerMonthLyInfoList QueryMemberChainMonthInfo failed size:%d req:%+v err: %+v", len(uidList), req, err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	mapId2PayInfo := make(map[uint32]*datahouse.GuildMemberChainMonthInfo, 0)
	for _, info := range payInfoList {
		mapId2PayInfo[info.UserId] = info
	}

	tmpUidList := uidList
	tmpUidList = append(tmpUidList, agentUidList...)
	tmpUidList = utils.ClearRepeatUint32(tmpUidList)
	userMap, err := models.GetModelServer().AccountCli.GetUsersMap(ctx, tmpUidList)
	if err != nil {
		fmt.Printf("GetAnchorPractitionerMonthLyInfoList GetUsersMap err:%v, uids:%v", err, tmpUidList)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	tidList := make([]string, 0)
	for _, v := range userMap {
		if mapUid2IsAnchor[v.GetUid()] {
			tidList = append(tidList, v.GetAlias())
		}
	}

	beginTm := time.Date(monthTm.Year(), monthTm.Month(), 1, 0, 0, 0, 0, time.Local)
	endTm := beginTm.AddDate(0, 1, 0)
	mapTid2Violations, mapStr2ViolationsTs, _, _, err := urrc.GetPractitionerViolationsInfoFromAudit(tidList, beginTm, endTm)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorPractitionerMonthLyInfoList GetPractitionerViolationsInfoFromAudit failed size:%d req:%+v err: %+v", len(uidList), req, err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	for _, tmpUid := range uidList {
		var nick, tid, userName string
		if u, ok := userMap[tmpUid]; ok {
			tid = u.GetAlias()
			nick = u.GetNickname()
			userName = u.GetUsername()
		}

		vioList := make([]*api.ViolationsInfo, 0)
		if vio, ok := mapTid2Violations[tid]; ok {
			for index, v := range vio {
				vioList = append(vioList, &api.ViolationsInfo{
					ViolationsType: uint32(MapIndex2VType[index]),
					ViolationsCnt:  v,
					TsList:         mapStr2ViolationsTs[fmt.Sprintf("%s-%d", tid, index)],
				})
			}
		}

		item := &liveStatsPb.AnchorMonthlyStats{}
		if stats, ok := mapUid2Stats[tmpUid]; ok {
			item = stats
		}

		activeInfo := &datahouse.GuildMemberInfo{}
		if info, ok := mapId2ActiveInfo[tmpUid]; ok {
			activeInfo = info
		}

		payInfo := &datahouse.GuildMemberChainMonthInfo{}
		if info, ok := mapId2PayInfo[tmpUid]; ok {
			payInfo = info
		}

		row := &api.AnchorPractitionerMonthLyInfo{
			Ts: req.GetMonthTs(),
			BaseInfo: &api.AnchorPractitionerBaseInfo{
				Uid:            tmpUid,
				Nickname:       nick,
				Ttid:           tid,
				Username:       userName,
				AgentTtid:      userMap[item.GetAgentUid()].GetAlias(),
				LiveValidDays:  item.GetDayLiveValidCnt(),
				LiveValidHours: CulHour(int64(item.GetLiveValidMinutes()), 60, 2),
				ChannelFee:     uint64(item.GetChannelFee()),
				AnchorIncome:   uint64(item.GetAnchorIncome()),
				ViolationsList: vioList,
				LiveActiveDays: item.GetLiveActiveDays(),
				PlatActiveDays: activeInfo.GetLoginDays(),
				AgentNickname:  userMap[item.GetAgentUid()].GetNickname(),
			},
			ConsumeRelationChains: payInfo.GetVaildChainCnt(),
			IsNew:                 item.GetNewFlag() == 1,
			IsHighQuality:         item.GetIsQualityAnchor(),
			IsActive:              item.GetIsActiveAnchor(),
			IsNewActive:           item.GetIsNewActiveAnchor(),
			IsProfession:          item.GetIsProfessionPrac(),
		}

		row.BaseInfo = GetAnchorPracDataByPermission(row.BaseInfo, permission)

		resp.InfoList = append(resp.InfoList, row)
	}

	log.DebugfWithCtx(ctx, "GetAnchorPractitionerMonthLyInfoList end req:%v resp:%v", req, resp)
	_ = web.ServeAPIJson(w, resp)
}

func GetAnchorPractitionerWeeklyInfoList(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetAnchorPractitionerWeeklyInfoListReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorPractitionerWeeklyInfoList Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugfWithCtx(ctx, "GetAnchorPractitionerWeeklyInfoList begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	accountRole, hasPer, respCode, respMsg, permission := checkUserHasPer(ctx, uid, guildId, api.GuildLiveDataMenuType_AnchorPractitionerAnalysis.String())
	if !hasPer {
		web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}

	resp := &api.GetAnchorPractitionerWeeklyInfoListResp{}

	var queryUidList []uint32
	if len(req.GetTtidList()) > 0 {
		queryUidList, err = Tid2Uid(ctx, req.GetTtidList())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAnchorPractitionerWeeklyInfoList Tid2Uid failed: err: %+v", err)
			_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
			return
		}
		if len(queryUidList) == 0 {
			log.DebugfWithCtx(ctx, "GetAnchorPractitionerWeeklyInfoList end req:%v resp:%v", req, resp)
			_ = web.ServeAPIJson(w, resp)
			return
		}
	}

	var agentUid uint32
	if accountRole == AccountRoleBroker {
		agentUid = uid
	}

	statResp, err := models.GetModelServer().LiveStatsCli.GetAnchorWeeklyStatsListV2(ctx, &liveStatsPb.GetAnchorWeeklyStatsListReq{
		GuildId:    req.GetGuildId(),
		BeginTime:  req.GetBeginTs(),
		EndTime:    req.GetEndTs(),
		Offset:     (req.GetPage() - 1) * req.GetPageSize(),
		Limit:      req.GetPageSize(),
		AgentUid:   agentUid,
		AnchorList: queryUidList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorPractitionerWeeklyInfoList GetAnchorMonthlyStatsListV2 failed: req:%v err: %+v", req, err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	resp.TotalCnt = statResp.GetTotalCnt()
	resp.NextPage = req.GetPage() + 1
	if len(statResp.GetList()) < int(req.GetPageSize()) {
		resp.NextPage = 0
	}

	uidList := make([]uint32, 0)
	agentUidList := make([]uint32, 0)
	mapUid2IsAnchor := make(map[uint32]bool, 0)
	mapUid2Stats := make(map[uint32]*liveStatsPb.AnchorWeeklyStats, 0)
	for _, item := range statResp.GetList() {
		uidList = append(uidList, item.GetUid())
		mapUid2Stats[item.GetUid()] = item
		mapUid2IsAnchor[item.GetUid()] = true

		agentUidList = append(agentUidList, item.GetAgentUid())
	}

	beginTm := time.Unix(int64(req.GetBeginTs()), 0)
	endTm := time.Unix(int64(req.GetEndTs()), 0)
	activeInfoList, err := datahouse.QueryGuildMemberDailyInfo(ctx, uidList, req.GetGuildId(), beginTm, endTm)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorPractitionerWeeklyInfoList QueryGuildMemberDailyInfo failed size:%d req:%+v err: %+v", len(uidList), req, err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	mapId2PlatActive := make(map[uint32]uint32, 0)
	for _, info := range activeInfoList {
		mapId2PlatActive[info.UserId] += info.LoginDays
	}

	tmpUidList := uidList
	tmpUidList = append(tmpUidList, agentUidList...)
	tmpUidList = utils.ClearRepeatUint32(tmpUidList)
	userMap, err := models.GetModelServer().AccountCli.GetUsersMap(ctx, tmpUidList)
	if err != nil {
		fmt.Printf("GetAnchorPractitionerWeeklyInfoList GetUsersMap err:%v, uids:%v", err, tmpUidList)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	tidList := make([]string, 0)
	for _, v := range userMap {
		if mapUid2IsAnchor[v.GetUid()] {
			tidList = append(tidList, v.GetAlias())
		}
	}

	mapTid2Violations, mapStr2ViolationsTs, _, _, err := urrc.GetPractitionerViolationsInfoFromAudit(tidList, beginTm, endTm)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorPractitionerWeeklyInfoList GetPractitionerViolationsInfoFromAudit failed size:%d req:%+v err: %+v", len(tidList), req, err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	for _, tmpUid := range uidList {
		var nick, tid, userName string
		if u, ok := userMap[tmpUid]; ok {
			tid = u.GetAlias()
			nick = u.GetNickname()
			userName = u.GetUsername()
		}

		vioList := make([]*api.ViolationsInfo, 0)
		if vio, ok := mapTid2Violations[tid]; ok {
			for index, v := range vio {
				vioList = append(vioList, &api.ViolationsInfo{
					ViolationsType: uint32(MapIndex2VType[index]),
					ViolationsCnt:  v,
					TsList:         mapStr2ViolationsTs[fmt.Sprintf("%s-%d", tid, index)],
				})
			}
		}

		item := &liveStatsPb.AnchorWeeklyStats{}
		if stats, ok := mapUid2Stats[tmpUid]; ok {
			item = stats
		}

		row := &api.AnchorPractitionerWeeklyInfo{
			BeginTs: req.GetBeginTs(),
			EndTs:   req.GetEndTs(),
			BaseInfo: &api.AnchorPractitionerBaseInfo{
				Uid:            tmpUid,
				Nickname:       nick,
				Ttid:           tid,
				Username:       userName,
				AgentTtid:      userMap[item.GetAgentUid()].GetAlias(),
				LiveValidDays:  item.GetValidDaysCnt(),
				LiveValidHours: CulHour(int64(item.GetLiveValidMinutes()), 60, 2),
				ChannelFee:     uint64(item.GetChannelFee()),
				AnchorIncome:   uint64(item.GetAnchorIncome()),
				ViolationsList: vioList,
				LiveActiveDays: item.GetLiveActiveDays(),
				AgentNickname:  userMap[item.GetAgentUid()].GetNickname(),
				PlatActiveDays: mapId2PlatActive[tmpUid],
			},
		}

		row.BaseInfo = GetAnchorPracDataByPermission(row.BaseInfo, permission)

		resp.InfoList = append(resp.InfoList, row)
	}

	log.DebugfWithCtx(ctx, "GetAnchorPractitionerWeeklyInfoList end req:%v resp:%v", req, resp)
	_ = web.ServeAPIJson(w, resp)
}

func GetAnchorPractitionerDailyInfoList(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetAnchorPractitionerDailyInfoListReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorPractitionerDailyInfoList Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugfWithCtx(ctx, "GetAnchorPractitionerDailyInfoList begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	accountRole, hasPer, respCode, respMsg, permission := checkUserHasPer(ctx, uid, guildId, api.GuildLiveDataMenuType_AnchorPractitionerAnalysis.String())
	if !hasPer {
		web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}

	resp := &api.GetAnchorPractitionerDailyInfoListResp{}

	var queryUidList []uint32
	if len(req.GetTtidList()) > 0 {
		queryUidList, err = Tid2Uid(ctx, req.GetTtidList())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAnchorPractitionerDailyInfoList Tid2Uid failed: err: %+v", err)
			_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
			return
		}
		if len(queryUidList) == 0 {
			log.DebugfWithCtx(ctx, "GetAnchorPractitionerDailyInfoList end req:%v resp:%v", req, resp)
			_ = web.ServeAPIJson(w, resp)
			return
		}
	}

	var agentUid uint32
	if accountRole == AccountRoleBroker {
		agentUid = uid
	}

	statResp, err := models.GetModelServer().LiveStatsCli.GetAnchorDailyStatsListV2(ctx, &liveStatsPb.GetAnchorDailyStatsListReq{
		GuildId:   req.GetGuildId(),
		BeginTime: req.GetBeginTs(),
		EndTime:   req.GetEndTs(),
		Offset:    (req.GetPage() - 1) * req.GetPageSize(),
		Limit:     req.GetPageSize(),
		AgentUid:  agentUid,
		UidList:   queryUidList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorPractitionerDailyInfoList GetAnchorDailyStatsListV2 failed: req:%v err: %+v", req, err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	resp.TotalCnt = statResp.GetTotalCnt()
	resp.NextPage = req.GetPage() + 1
	if len(statResp.GetList()) < int(req.GetPageSize()) {
		resp.NextPage = 0
	}

	uidList := make([]uint32, 0)
	agentUidList := make([]uint32, 0)
	mapUid2IsAnchor := make(map[uint32]bool, 0)
	for _, item := range statResp.GetList() {
		uidList = append(uidList, item.GetUid())
		mapUid2IsAnchor[item.GetUid()] = true

		agentUidList = append(agentUidList, item.GetAgentUid())
	}

	beginTm := time.Unix(int64(req.GetBeginTs()), 0)
	endTm := time.Unix(int64(req.GetEndTs()), 0)
	activeInfoList, err := datahouse.QueryGuildMemberDailyInfo(ctx, uidList, req.GetGuildId(), beginTm, endTm)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorPractitionerDailyInfoList QueryGuildMemberMonthInfo failed size:%d req:%+v err: %+v", len(uidList), req, err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	mapId2PlatDur := make(map[string]uint32, 0)
	for _, info := range activeInfoList {
		mapId2PlatDur[fmt.Sprintf("%d-%s", info.UserId, info.DataDate)] += info.LoginDuration
	}

	tmpUidList := uidList
	tmpUidList = append(tmpUidList, agentUidList...)
	tmpUidList = utils.ClearRepeatUint32(tmpUidList)
	userMap, err := models.GetModelServer().AccountCli.GetUsersMap(ctx, tmpUidList)
	if err != nil {
		fmt.Printf("GetAnchorPractitionerDailyInfoList GetUsersMap err:%v, uids:%v", err, tmpUidList)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	tidList := make([]string, 0)
	for _, v := range userMap {
		if mapUid2IsAnchor[v.GetUid()] {
			tidList = append(tidList, v.GetAlias())
		}
	}

	_, _, mapTid2DateViolationsCnt, mapTid2DateViolationsTs, err := urrc.GetPractitionerViolationsInfoFromAudit(tidList, beginTm, endTm)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPractitionerDailyInfoList GetPractitionerViolationsInfoFromAudit failed size:%d req:%+v err: %+v", len(uidList), req, err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	for _, item := range statResp.GetList() {
		var nick, tid, userName string
		if u, ok := userMap[item.GetUid()]; ok {
			tid = u.GetAlias()
			nick = u.GetNickname()
			userName = u.GetUsername()
		}

		dateTm := time.Unix(int64(item.GetDate()), 0)
		vioList := make([]*api.ViolationsInfo, 0)
		if dateVio, okA := mapTid2DateViolationsCnt[fmt.Sprintf("%s-%s", tid, dateTm.Format(dayLayout))]; okA {
			for index, v := range dateVio {
				vioList = append(vioList, &api.ViolationsInfo{
					ViolationsType: uint32(MapIndex2VType[index]),
					ViolationsCnt:  v,
					TsList:         mapTid2DateViolationsTs[fmt.Sprintf("%s-%s-%d", tid, dateTm.Format(dayLayout), index)],
				})
			}
		}

		row := &api.AnchorPractitionerDailyInfo{
			Ts: item.GetDate(),
			BaseInfo: &api.AnchorPractitionerBaseInfo{
				Uid:             item.GetUid(),
				Nickname:        nick,
				Ttid:            tid,
				Username:        userName,
				AgentTtid:       userMap[item.GetAgentUid()].GetAlias(),
				LiveValidHours:  CulHour(int64(item.GetLiveValidMinutes()), 60, 2),
				ChannelFee:      uint64(item.GetChannelFee()),
				AnchorIncome:    uint64(item.GetAnchorIncome()),
				ViolationsList:  vioList,
				AgentNickname:   userMap[item.GetAgentUid()].GetNickname(),
				LiveHours:       CulHour(int64(item.GetLiveMinutes()), 60, 2),
				PlatOnlineHours: CulHour(int64(mapId2PlatDur[fmt.Sprintf("%d-%s", item.GetUid(), dateTm.Format("20060102"))]/60), 60, 2),
			},
		}

		row.BaseInfo = GetAnchorPracDataByPermission(row.BaseInfo, permission)

		resp.InfoList = append(resp.InfoList, row)
	}

	log.DebugfWithCtx(ctx, "GetAnchorPractitionerDailyInfoList end req:%v resp:%v", req, resp)
	_ = web.ServeAPIJson(w, resp)
}

func GetGuildAnchorPracMonthlyInfoList(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetGuildAnchorPracMonthlyInfoListReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildAnchorPracMonthlyInfoList Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugfWithCtx(ctx, "GetGuildAnchorPracMonthlyInfoList begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	_, hasPer, respCode, respMsg, _ := checkUserHasPer(ctx, uid, guildId, api.GuildLiveDataMenuType_AnchorPractitionerAnalysis.String())
	if !hasPer {
		web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}

	resp := &api.GetGuildAnchorPracMonthlyInfoListResp{}

	guildResp, err := models.GetModelServer().GuildCli.GetGuild(ctx, guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildAnchorPracMonthlyInfoList GetGuild failed: req:%v err: %+v", req, err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	resp.GuildId = guildResp.GetGuildId()
	if guildResp.GetShortId() != 0 {
		resp.GuildId = guildResp.GetShortId()
	}
	resp.GuildName = guildResp.GetName()

	var lock sync.Mutex
	var wg sync.WaitGroup

	mapMonth2Stats := make(map[int64]*liveStatsPb.GuildMonthlyStats, 0)
	beginMonth := time.Unix(int64(req.GetBeginMonth()), 0)
	endMonth := time.Unix(int64(req.GetEndMonth()), 0)

	for tm := beginMonth; !tm.After(endMonth); tm = tm.AddDate(0, 1, 0) {
		wg.Add(1)
		go func(tmpTm time.Time) error {
			defer wg.Done()

			ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
			defer cancel()

			statResp, err := models.GetModelServer().LiveStatsCli.GetGuildMonthlyStats(ctx, uid, req.GetGuildId(), uint32(tmpTm.Unix()))
			if err != nil {
				log.ErrorWithCtx(ctx, "GetGuildAnchorPracMonthlyInfoList GetGuildMonthlyStats failed: req:%v tm:%v err: %+v", req, tmpTm, err)
			}

			if err == nil {
				lock.Lock()
				defer lock.Unlock()
				mapMonth2Stats[tmpTm.Unix()] = statResp.GetStats()
			}

			return nil
		}(tm)
	}

	wg.Wait()

	for tm := beginMonth; !tm.After(endMonth); tm = tm.AddDate(0, 1, 0) {
		row := &api.GuildAnchorPracMonthlyStatsInfo{
			Ts: uint32(tm.Unix()),
		}

		if stats, ok := mapMonth2Stats[tm.Unix()]; ok {
			row.NewActiveAnchorCnt = stats.GetNewActiveAnchorCnt()
			row.QualityAnchorCnt = stats.GetQualityAnchorCnt()
			row.ActiveAnchorCnt = stats.GetActiveAnchorCnt()
			row.ProfessionAnchorCnt = stats.GetProfessionPracCnt()
		}

		resp.InfoList = append(resp.InfoList, row)
	}

	log.DebugfWithCtx(ctx, "GetGuildAnchorPracMonthlyInfoList end req:%v resp:%v", req, resp)
	_ = web.ServeAPIJson(w, resp)
}
