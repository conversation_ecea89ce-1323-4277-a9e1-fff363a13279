package hanlders

import (
	"context"
	"encoding/json"
	"fmt"
	"golang.52tt.com/pkg/datahouse"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/web"
	channelpb_ "golang.52tt.com/protocol/app/channel"
	"golang.52tt.com/protocol/common/status"
	anchorcontract_go "golang.52tt.com/protocol/services/anchorcontract-go"
	enterPb "golang.52tt.com/protocol/services/entertainmentRecommendBackSvr"
	gold_commission "golang.52tt.com/protocol/services/gold-commission"
	guild_management_svr "golang.52tt.com/protocol/services/guild-management-svr"
	pb "golang.52tt.com/protocol/services/sign-anchor-stats"
	"golang.52tt.com/services/guild-management/guild-management-http-logic/models"
	api "golang.52tt.com/services/guild-management/guild-management-http-logic/models/gen-go"
	"golang.52tt.com/services/guild-management/guild-management-http-logic/utils"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"
)

func GetChannelOperationData(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetChannelOperationDataReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelOperationData Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugWithCtx(ctx, "GetChannelOperationData begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	_, hasPer, respCode, respMsg, _ := checkUserHasPer(ctx, uid, guildId, api.GuildMultiPlayDataType_MultiPlayOperationData.String())
	if !hasPer {
		_ = web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}

	resp := &api.GetChannelOperationDataResp{}
	resp.List = make([]*api.ChannelOperationData, 0)
	filterChannelIds := make([]uint32, 0)

	if len(req.GetChannelIds()) > 0 {
		filterChannelIds, err = DisplayId2ChannelId(ctx, req.GetChannelIds())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetChannelOperationData DisplayId2ChannelId err %+v, display_id: %+v", err, req.GetChannelIds())
			_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
			return
		}
		if len(filterChannelIds) == 0 {
			log.DebugWithCtx(ctx, "GetContractAnchorDetail end req:%v resp:%v", req, resp)
			_ = web.ServeAPIJson(w, resp)
			return
		}
	}

	statResp, err := models.GetModelServer().GoldCommissionCli.AmuseGuildChannelIncomeList(ctx, &gold_commission.AmuseGuildChannelIncomeListReq{
		GuildId:   req.GetGuildId(),
		ChannelId: filterChannelIds,
		Offset:    req.GetOffset(),
		Limit:     req.GetLimit(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelOperationData AmuseGuildChannelIncomeList: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	resp.Total = statResp.Total

	channelIds := make([]uint32, 0)
	for _, item := range statResp.ChannelList {
		channelIds = append(channelIds, item.GetChannelId())
	}

	channelIds = utils.ClearRepeatUint32(channelIds)

	tagResp, err := models.GetModelServer().EntertainmentCli.BatchGetChannelTag(ctx, 0, &enterPb.BatchGetChannelTagReq{
		ChannelIdList: channelIds,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelOperationData BatchGetChannelTag failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	if len(channelIds) != len(tagResp.GetChannelTagList()) {
		fmt.Printf("Fail to BatchGetChannelTag %+v err:len err", channelIds)
		_ = web.ServeAPICodeJson(w, -500, status.CodeMessageMap[status.ErrSys], nil)
		return
	}
	mapTag := make(map[uint32]string)
	for i, info := range tagResp.GetChannelTagList() {
		mapTag[channelIds[i]] = info.GetName()
	}

	// 分批获取房间信息 100条
	mapChannelInfo, err := batGetChannel(ctx, channelIds)
	if err != nil {
		fmt.Printf("Fail to batGetChannel err:%v, ids:%+v", err, channelIds)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	for _, item := range statResp.ChannelList {
		var channelName, channelTag string
		var channelDisplayId string
		if c, ok := mapChannelInfo[item.ChannelId]; ok {
			channelName = c.GetName()
			channelDisplayId = c.GetChannelViewId()
		}
		if c, ok := mapTag[item.ChannelId]; ok {
			channelTag = c
		}
		row := &api.ChannelOperationData{
			ChannelDisplayID:       channelDisplayId,
			ChannelName:            channelName,
			ChannelTag:             channelTag,
			ThisMonthFee:           item.GetFee(),
			LastMonthFee:           item.GetLastMonthFee(),
			LastMonthSamePeriodFee: item.GetSamePeriodFee(),
			ChainRatio:             fmt.Sprintf("%.2f%%", item.GetSamePeriodQoq()*100),
		}
		resp.List = append(resp.List, row)
	}

	log.DebugWithCtx(ctx, "GetChannelOperationData end req:%v resp:%v", req, resp)
	_ = web.ServeAPIJson(w, resp)
}

func GetContractAnchorDetail(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetContractAnchorDetailReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetContractAnchorDetail Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugWithCtx(ctx, "GetContractAnchorDetail begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	_, hasPer, respCode, respMsg, permission := checkUserHasPer(ctx, uid, guildId, api.GuildMultiPlayDataType_MultiPlayDetail.String())
	if !hasPer {
		_ = web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}

	resp := &api.GetContractAnchorDetailResp{}

	var queryUidList []uint32
	if len(req.GetTtid()) > 0 {
		queryUidList, err = Tid2Uid(ctx, req.GetTtid())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetContractAnchorDetail Tid2Uid failed: err: %+v", err)
			_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
			return
		}
		if len(queryUidList) == 0 {
			log.DebugWithCtx(ctx, "GetContractAnchorDetail end req:%v resp:%v", req, resp)
			_ = web.ServeAPIJson(w, resp)
			return
		}
	}

	statResp, err := models.GetModelServer().SignAnchorStatsCli.GetMultiAnchorMonthStat(ctx, &pb.GetMultiAnchorMonthStatReq{
		GuildId:    req.GetGuildId(),
		Offset:     req.GetOffset(),
		Limit:      req.GetLimit(),
		Uid:        queryUidList,
		StartTime:  uint32(req.GetStartTime()),
		EndTime:    uint32(req.GetEndTime()),
		AnchorType: req.GetAnchorType(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetContractAnchorDetail GetMultiAnchorMonthStat failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	resp.Total = statResp.Total
	resp.List = make([]*api.ContractAnchorDetail, 0)

	var retStartTime, retEndTime time.Time
	uidList := make([]uint32, 0)
	for _, item := range statResp.List {
		uidList = append(uidList, item.GetUid())

		date_, err := time.ParseInLocation("2006-01", item.GetDate(), time.Local)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetContractAnchorDetail time.ParseInLocation failed: err: %+v", err)
			continue
		}

		if retStartTime.IsZero() || date_.Before(retStartTime) {
			retStartTime = date_
		}

		if retEndTime.IsZero() || date_.After(retEndTime) {
			retEndTime = date_
		}
	}

	uidList = utils.ClearRepeatUint32(uidList)

	log.DebugWithCtx(ctx, "GetContractAnchorDetail uid:%d, guildId:%d retStartTime:%v retEndTime:%v, uidList:%v", uid, guildId, retStartTime, retEndTime, uidList)

	// 从数仓取得月主播流水
	genDateUserKey := func(date string, uid uint32) string {
		return fmt.Sprintf("%s_%d", date, uid)
	}
	dhList, err := datahouse.QueryGuildMemberMonthInfo(ctx, uidList, guildId, retStartTime, retEndTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetContractAnchorDetail QueryGuildMemberMonthInfo failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, "系统错误", nil)
		return
	}
	dateUid2DataMap := make(map[string]*datahouse.GuildMemberInfo)
	for _, item := range dhList {
		date_, err := time.ParseInLocation("200601", item.DataMonth, time.Local)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetContractAnchorDetail time.ParseInLocation failed: err: %+v", err)
			continue
		}
		dateUid2DataMap[genDateUserKey(date_.Format("2006-01"), item.UserId)] = item
	}

	userMap, err := models.GetModelServer().AccountCli.GetUsersMap(ctx, uidList)
	if err != nil {
		fmt.Printf("GetContractAnchorDetail GetUsersMap err:%v, uids:%v", err, uidList)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	contractMap := make(map[uint32]*anchorcontract_go.ContractInfo)
	contractInfoReq := &anchorcontract_go.BatchGetUserContractReq{UidList: uidList}
	contractInfoResp, err := models.GetModelServer().AnchorcontractCli.BatchGetUserContract(ctx, uid, contractInfoReq)
	if err != nil {
		fmt.Printf("GetContractAnchorDetail BatchGetUserContract err:%v, uids:%v", err, uidList)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	for _, c := range contractInfoResp.ContractList {
		contractMap[c.GetActorUid()] = c
	}

	for _, item := range statResp.List {
		var tid, nick string
		var reg, start, end uint64
		if u, ok := userMap[item.Uid]; ok {
			tid = u.GetAlias()
			nick = u.GetNickname()
			reg = uint64(u.GetRegisteredAt())
		}
		if c, ok := contractMap[item.Uid]; ok {
			start = uint64(c.GetSignTime())
			end = uint64(c.GetExpireTime())
		}
		// 用数仓提供的流水与BI一致
		dateUserKey := genDateUserKey(item.GetDate(), item.GetUid()) // YYYY-MM
		dhMonthIncome := uint64(0)
		var isNewSign, isVaild, isPro string
		if data, ok := dateUid2DataMap[dateUserKey]; ok {
			dhMonthIncome = data.AnchorIncome
			isNewSign = data.IsNewSign
			isVaild = data.IsVaild
			isPro = data.IsPro
		}
		if isNewSign != "是" {
			isNewSign = "否"
		}
		if isVaild != "是" {
			isVaild = "否"
		}
		if isPro != "是" {
			isPro = "否"
		}
		row := &api.ContractAnchorDetail{
			Month:             item.Date,
			Ttid:              tid,
			Nickname:          nick,
			RegTime:           reg,
			ContractStartTime: start,
			ContractEndTime:   end,
			MonthValidDay:     item.MonthValidDay,
			MonthValidHour:    item.MonthValidHour,
			MonthFee:          dhMonthIncome,
			IsNewSign:         isNewSign,
			IsVaild:           isVaild,
			IsPro:             isPro,
		}
		resp.List = append(resp.List, GetContractAnchorDetailByPermission(row, permission))
	}

	log.DebugWithCtx(ctx, "GetContractAnchorDetail end req:%v resp:%v", req, resp)
	_ = web.ServeAPIJson(w, resp)
}

func GetContractAnchorChannelStat(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetContractAnchorChannelStatReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetContractAnchorChannelStat Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugWithCtx(ctx, "GetContractAnchorChannelStat begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	_, hasPer, respCode, respMsg, permission := checkUserHasPer(ctx, uid, guildId, api.GuildMultiPlayDataType_MultiPlayDetail.String())
	if !hasPer {
		_ = web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}
	resp := &api.GetContractAnchorChannelStatResp{}

	var queryUidList []uint32
	if len(req.GetTtid()) > 0 {
		queryUidList, err = Tid2Uid(ctx, req.GetTtid())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetContractAnchorChannelStat Tid2Uid failed: err: %+v", err)
			_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
			return
		}
		if len(queryUidList) == 0 {
			log.DebugWithCtx(ctx, "GetContractAnchorChannelStat end req:%v resp:%v", req, resp)
			_ = web.ServeAPIJson(w, resp)
			return
		}
	}

	unit := pb.TimeFilterUnit(req.GetUnit())
	statResp, err := models.GetModelServer().SignAnchorStatsCli.GetMultiAnchorChannelStat(ctx, &pb.GetMultiAnchorChannelStatReq{
		GuildId:   req.GetGuildId(),
		Offset:    req.GetOffset(),
		Limit:     req.GetLimit(),
		Uid:       queryUidList,
		StartTime: uint32(req.GetStartTime()),
		EndTime:   uint32(req.GetEndTime()),
		Unit:      unit,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetContractAnchorChannelStat GetMultiAnchorChannelStat failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	resp.Total = statResp.Total

	var retStartTime, retEndTime time.Time
	channelIds := make([]uint32, 0)
	uidList := make([]uint32, 0)
	for _, item := range statResp.List {
		channelIds = append(channelIds, item.GetChannelId())
		uidList = append(uidList, item.GetUid())
		date_ := time.Unix(int64(item.GetDateTimestamp()), 0)

		if retStartTime.IsZero() || date_.Before(retStartTime) {
			retStartTime = date_
		}

		if retEndTime.IsZero() || date_.After(retEndTime) {
			retEndTime = date_
		}
	}

	channelIds = utils.ClearRepeatUint32(channelIds)
	uidList = utils.ClearRepeatUint32(uidList)

	log.DebugWithCtx(ctx, "GetContractAnchorChannelStat uid:%d, guildId:%d retStartTime:%v retEndTime:%v, channelIds:%v, uidList:%v", uid, guildId, retStartTime, retEndTime, channelIds, uidList)

	dhReq := &datahouse.MultiGuildMemberInfoReq{
		StartDate:     retStartTime,
		EndDate:       retEndTime,
		UidList:       uidList,
		ChannelIdList: channelIds,
		Offset:        0,
		Limit:         10000,
	}
	dhList, err := getDataHouseMultiGuildMemberDayInfoByUnit(ctx, dhReq, unit)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetContractAnchorChannelStat getDataHouseMultiGuildMemberDayInfoByUnit failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, "系统错误", nil)
		return
	}

	getDateCidUidKey := func(date string, cid uint32, uid uint32) string {
		return fmt.Sprintf("%s_%d_%d", date, cid, uid)
	}
	dateCidUid2InfoMap := make(map[string]*DHMultiGuildMemberDayInfo)
	for _, item := range dhList {
		dateCidUid2InfoMap[getDateCidUidKey(item.DateKey, item.ChannelId, item.Uid)] = item
	}

	tagResp, err := models.GetModelServer().EntertainmentCli.BatchGetChannelTag(ctx, 0, &enterPb.BatchGetChannelTagReq{
		ChannelIdList: channelIds,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetContractAnchorChannelStat BatchGetChannelTag failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	if len(channelIds) != len(tagResp.GetChannelTagList()) {
		fmt.Printf("Fail to BatchGetChannelTag %+v err:len err", channelIds)
		_ = web.ServeAPICodeJson(w, -500, status.CodeMessageMap[status.ErrSys], nil)
		return
	}
	mapTag := make(map[uint32]string)
	for i, info := range tagResp.GetChannelTagList() {
		mapTag[channelIds[i]] = info.GetName()
	}

	// 分批获取房间信息 100条
	mapChannelInfo, err := batGetChannel(ctx, channelIds)
	if err != nil {
		fmt.Printf("Fail to batGetChannel err:%v, ids:%+v", err, channelIds)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	userMap, err := models.GetModelServer().AccountCli.GetUsersMap(ctx, uidList)
	if err != nil {
		fmt.Printf("GetContractAnchorChannelStat GetUsersMap err:%v, uids:%v", err, uidList)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	contractMap := make(map[uint32]*anchorcontract_go.ContractInfo)
	contractInfoReq := &anchorcontract_go.BatchGetUserContractReq{UidList: uidList}
	contractInfoResp, err := models.GetModelServer().AnchorcontractCli.BatchGetUserContract(ctx, uid, contractInfoReq)
	if err != nil {
		fmt.Printf("GetContractAnchorChannelStat BatchGetUserContract err:%v, uids:%v", err, uidList)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	for _, c := range contractInfoResp.ContractList {
		contractMap[c.GetActorUid()] = c
	}

	for _, item := range statResp.List {
		var tid, nick, channelName, channelTag, channelDisplayId string
		if u, ok := userMap[item.Uid]; ok {
			tid = u.GetAlias()
			nick = u.GetNickname()
		}
		if c, ok := mapChannelInfo[item.ChannelId]; ok {
			channelName = c.GetName()
			channelDisplayId = c.GetChannelViewId()
		}
		if c, ok := mapTag[item.ChannelId]; ok {
			channelTag = c
		}
		// 用数仓提供的流水与BI一致
		date_ := time.Unix(int64(item.GetDateTimestamp()), 0)
		dateUserKey := getDateCidUidKey(date_.Format("2006-01-02"), item.GetChannelId(), item.GetUid())
		dhMonthIncome := uint64(0)
		var isNewSign, isVaild, isPro string
		if data, ok := dateCidUid2InfoMap[dateUserKey]; ok {
			dhMonthIncome = data.AnchorIncome
			isNewSign = data.IsNewSign
			isVaild = data.IsValid
			isPro = data.IsPro
		}
		if isNewSign != "是" {
			isNewSign = "否"
		}
		if isVaild != "是" {
			isVaild = "否"
		}
		if isPro != "是" {
			isPro = "否"
		}
		row := &api.ContractAnchorChannelStat{
			Date:             item.Date,
			Ttid:             tid,
			Nickname:         nick,
			ChannelId:        item.ChannelId,
			ChannelDisplayId: channelDisplayId,
			ChannelName:      channelName,
			ChannelTag:       channelTag,
			ValidDay:         item.MonthValidDay,
			ValidHour:        item.MonthValidHour,
			Fee:              uint32(dhMonthIncome + item.WerewolfIncome),
			PresentFee:       uint32(dhMonthIncome),
			WerewolfFee:      uint32(item.WerewolfIncome),
			IsNewSign:        isNewSign,
			IsVaild:          isVaild,
			IsPro:            isPro,
		}
		resp.List = append(resp.List, GetContractAnchorChannelStatByPermission(row, permission))
	}

	log.DebugWithCtx(ctx, "GetContractAnchorChannelStat end req:%v resp:%v", req, resp)
	_ = web.ServeAPIJson(w, resp)
}

type DHMultiGuildMemberDayInfo struct {
	Date         string
	DateKey      string
	Uid          uint32
	ChannelId    uint32
	AnchorIncome uint64
	IsNewSign    string
	IsValid      string
	IsPro        string
}

func getDataHouseMultiGuildMemberDayInfoByUnit(ctx context.Context, req *datahouse.MultiGuildMemberInfoReq, unit pb.TimeFilterUnit) ([]*DHMultiGuildMemberDayInfo, error) {
	list := make([]*DHMultiGuildMemberDayInfo, 0)
	if unit == pb.TimeFilterUnit_BY_DAY {
		dhList, _, err := datahouse.QueryMultiGuildMemberDayInfo(ctx, req)
		if err != nil {
			log.ErrorWithCtx(ctx, "getDataHouseMultiGuildMemberDayInfoByUnit QueryMultiGuildMemberDayInfo failed: err: %+v", err)
			return nil, err
		}
		for _, item := range dhList {
			cid_, err := strconv.Atoi(item.RoomId)
			if err != nil {
				log.ErrorWithCtx(ctx, "getDataHouseMultiGuildMemberDayInfoByUnit strconv.Atoi failed: err: %+v", err)
				continue
			}
			dateKey, err := time.ParseInLocation("2006-01-02", item.DataDate, time.Local)
			if err != nil {
				log.ErrorWithCtx(ctx, "getDataHouseMultiGuildMemberDayInfoByUnit time.ParseInLocation failed: err: %+v", err)
				continue
			}
			list = append(list, &DHMultiGuildMemberDayInfo{
				Date:         item.DataDate,
				DateKey:      dateKey.Format("2006-01-02"),
				Uid:          item.UserId,
				ChannelId:    uint32(cid_),
				AnchorIncome: item.AnchorIncome,
				IsNewSign:    item.IsNewSign,
				IsValid:      item.IsVaild,
				IsPro:        item.IsPro,
			})
		}
	} else if unit == pb.TimeFilterUnit_BY_MONTH {
		dhList, _, err := datahouse.QueryMultiGuildMemberMonthInfo(ctx, req)
		if err != nil {
			log.ErrorWithCtx(ctx, "getDataHouseMultiGuildMemberDayInfoByUnit QueryMultiGuildMemberDayInfo failed: err: %+v", err)
			return nil, err
		}
		for _, item := range dhList {
			cid_, err := strconv.Atoi(item.RoomId)
			if err != nil {
				log.ErrorWithCtx(ctx, "getDataHouseMultiGuildMemberDayInfoByUnit strconv.Atoi failed: err: %+v", err)
				continue
			}
			dateKey, err := time.ParseInLocation("200601", item.DataMonth, time.Local)
			if err != nil {
				log.ErrorWithCtx(ctx, "getDataHouseMultiGuildMemberDayInfoByUnit time.ParseInLocation failed: %+v, [%s]", err, item.DataMonth)
				continue
			}
			list = append(list, &DHMultiGuildMemberDayInfo{
				Date:         item.DataMonth,
				DateKey:      dateKey.Format("2006-01-02"),
				Uid:          item.Uid,
				ChannelId:    uint32(cid_),
				AnchorIncome: item.AnchorIncome,
				IsNewSign:    item.IsNewSign,
				IsValid:      item.IsVaild,
				IsPro:        item.IsPro,
			})
		}
	} else {
		log.ErrorWithCtx(ctx, "getDataHouseMultiGuildMemberDayInfoByUnit invalid unit: %d", unit)
		return nil, fmt.Errorf("invalid unit: %d", unit)
	}
	return list, nil
}

// getMultiChannelListByAccess 获取母公会列表与房间列表（注意这里子公会与母公会是互通的，即子公会的可见范围与母公会相同）
func getMultiChannelListByAccess(ctx context.Context, guildId, uid uint32, isChairman bool) ([]uint32, []uint32, error) {
	m := models.GetModelServer()
	channelIdList := make([]uint32, 0)
	guildIdList := make([]uint32, 0)
	guildGroup := m.GetSignDyConfig().GetGuildGroup(guildId) // 子母公会
	guildIdList = guildGroup
	if isChairman {
		// 会长 返回所有房间
		guildChannelMap, err := m.ChannelGuildGoCli.BatGetChannelGuildMap(ctx, guildGroup, uint32(channelpb_.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE))
		if err != nil {
			log.ErrorWithCtx(ctx, "getMultiChannelListByAccess BatGetChannelGuildMap failed: err: %+v", err)
			return nil, nil, err
		}
		for _, item := range guildChannelMap {
			channelIdList = append(channelIdList, item.ChannelIds...)
		}
		log.DebugWithCtx(ctx, "getMultiChannelListByAccess is chairman guildIdList:%+v guildChannelMap:%+v", guildIdList, guildChannelMap)
	} else {
		// 非会长 返回厅管权限房间
		listResp, err := m.GuildManagementCli.GetChannelListByAdminUid(ctx, &guild_management_svr.GetChannelListByAdminUidReq{
			AdminUid: uid,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "getMultiChannelListByAccess GetChannelListByAdminUid failed: err: %+v", err)
			return nil, nil, err
		}
		channelIdList = listResp.GetChannelList()
		log.DebugWithCtx(ctx, "getMultiChannelListByAccess is not chairman guildIdList:%+v channelList:%+v", guildIdList, channelIdList)
	}
	return guildIdList, channelIdList, nil
}

func GetMultiChannelList(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetMultiChannelListReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiChannelList Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	isChairman, serr := CheckIsLibraryGuildChairman(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetMultiChannelList failed to CheckIsLibraryGuildChairman [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	log.DebugWithCtx(ctx, "GetMultiChannelList begin %s %+v", string(authInfo.Body), req)

	m := models.GetModelServer()
	_, channelIdList, err := getMultiChannelListByAccess(ctx, guildId, uid, isChairman)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiChannelList getMultiChannelListByAccess failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	channelMap, err := m.ChannelCli.BatchGetChannelSimpleInfo(ctx, 0, channelIdList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiChannelList BatchGetChannelSimpleInfo failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	// 如果是当前公会，优先展示
	sort.Slice(channelIdList, func(i, j int) bool {
		gi := channelMap[channelIdList[i]].GetBindId()
		gj := channelMap[channelIdList[j]].GetBindId()
		if gi == guildId {
			return true
		}
		if gj == guildId {
			return false
		}
		if gi == gj {
			return channelIdList[i] > channelIdList[j]
		}
		return gi > gj
	})

	resp := new(api.GetMultiChannelListResp)
	resp.ChannelList = make([]*api.GetMultiChannelListResp_ChannelInfo, 0)
	for _, item := range channelIdList {
		if c, ok := channelMap[item]; ok {
			row := &api.GetMultiChannelListResp_ChannelInfo{
				ChannelId:     c.GetChannelId(),
				ChannelName:   c.GetName(),
				ChannelViewId: c.GetChannelViewId(),
			}
			resp.ChannelList = append(resp.ChannelList, row)
		}
	}

	log.DebugWithCtx(ctx, "GetMultiChannelList end req:%v resp:%v", req, resp)
	_ = web.ServeAPIJson(w, resp)
}

func filterMultiContractMember(ctx context.Context, uidList []uint32, guildId uint32, isChairman bool) ([]uint32, error) {
	var err error
	m := models.GetModelServer()
	contractInfoMap, err := getAnchorContractInfoV2(ctx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "filterMultiContractMember getAnchorContractInfo failed: err: %+v", err)
		return nil, err
	}
	contractUidList := make([]uint32, 0)
	for _, contract := range contractInfoMap {
		if !utils.IsInSlice(contract.GetAnchorIdentityList(), uint32(api.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER)) {
			continue
		}
		contractGuild := contract.GetContract().GetGuildId()
		// 子母公会
		group := m.GetSignDyConfig().GetGuildGroup(guildId)
		// 签约公会在子母公会中，均可搜出
		if !utils.IsInSlice(group, contractGuild) {
			continue
		}
		contractUidList = append(contractUidList, contract.GetContract().GetActorUid())
	}
	return contractUidList, nil
}

// checkUserMultiContractInRange 检查用户是否在指定时间范围内有多人互动签约身份
func checkUserMultiContractInRange(ctx context.Context, uid, guildId uint32, start, end time.Time) (bool, error) {
	m := models.GetModelServer()
	contractResp, err := m.AnchorcontractCli.GetIdentityChangeHistory(ctx, &anchorcontract_go.GetIdentityChangeHistoryReq{
		Uid:            uid,
		AnchorIdentity: anchorcontract_go.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiPractitionerScheduleData BatchGetUserAnchorIdentityLog failed: err: %+v", err)
		return false, err
	}
	contractList := contractResp.GetList()
	if len(contractList) == 0 {
		return false, nil
	}

	// 防止时间顺序不对
	sort.Slice(contractList, func(i, j int) bool {
		return contractList[i].ObtainTime < contractList[j].ObtainTime
	})

	guildGroup := m.GetSignDyConfig().GetGuildGroup(guildId)

	var hasContractInRange bool
	for _, change := range contractList {
		// 签约公会不是当前子母公会组
		if !utils.IsInSlice(guildGroup, change.GuildId) {
			continue
		}

		obtainTime := time.Unix(int64(change.ObtainTime), 0)
		reclaimTime := time.Unix(int64(change.ReclaimTime), 0)

		// 处理签约未过期的情况（reclaim_time = 0）
		if change.ReclaimTime == 0 {
			reclaimTime = time.Now() // 假设当前时间为未过期的时间
		}

		// 检查是否有签约在指定时间范围内
		if (obtainTime.Before(end) || obtainTime.Equal(end)) &&
			(reclaimTime.After(start) || reclaimTime.Equal(start)) {
			hasContractInRange = true
			break
		}
	}
	return hasContractInRange, nil
}

func SearchMultiContractMember(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.SearchMultiContractMemberReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "SearchMultiContractMember Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.InfoWithCtx(ctx, "SearchMultiContractMember begin uid:%d %+v", authInfo.UserID, req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	isChairman, serr := CheckIsLibraryGuildChairman(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "SearchMultiContractMember failed to CheckIsLibraryGuildChairman [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	resp := new(api.SearchMultiContractMemberResp)
	resp.MemberList = make([]*api.MultiChannelEmploymentInfo_UserInfo, 0)
	searchUidList := req.GetTtid()
	if len(searchUidList) == 0 {
		log.DebugWithCtx(ctx, "SearchMultiContractMember end req:%v resp:%v", req, resp)
		_ = web.ServeAPIJson(w, resp)
	}

	uidList, err := batGetUidByTid(ctx, searchUidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "SearchMultiContractMember batGetUidByTid failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	contractUidList, err := filterMultiContractMember(ctx, uidList, guildId, isChairman)
	if err != nil {
		log.ErrorWithCtx(ctx, "SearchMultiContractMember filterMultiContractMember failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	userInfoMap, err := batGetUser(ctx, contractUidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "SearchMultiContractMember batGetUser failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	for _, u := range contractUidList {
		if user, ok := userInfoMap[u]; ok {
			row := &api.MultiChannelEmploymentInfo_UserInfo{
				Uid:        u,
				Ttid:       user.GetAlias(),
				Nickname:   user.GetNickname(),
				Username:   user.GetUsername(),
				IsContract: true,
			}
			resp.MemberList = append(resp.MemberList, row)
		}
	}

	log.DebugWithCtx(ctx, "SearchMultiContractMember end req:%v resp:%v", req, resp)
	_ = web.ServeAPIJson(w, resp)
}

func GetMultiPractitionerScheduleData(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetMultiPractitionerScheduleDataReq{}
	resp := &api.GetMultiPractitionerScheduleDataResp{
		ScheduleInfoList: make([]*api.MultiPractitionerScheduleInfo, 0),
	}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiPractitionerScheduleData Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	log.InfoWithCtx(ctx, "GetMultiPractitionerScheduleData begin uid:%d %+v", authInfo.UserID, req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	_, hasPer, respCode, respMsg, _ := checkUserHasPer(ctx, uid, guildId, api.GuildMultiPlayDataType_MultiPlayScheduleData.String())
	if !hasPer {
		_ = web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}
	isChairman, serr := CheckIsLibraryGuildChairman(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetMultiPractitionerScheduleData failed to CheckIsLibraryGuildChairman [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	// 检查cid有权限访问
	cidList := make([]uint32, 0)
	_, accessChannelIdList, err := getMultiChannelListByAccess(ctx, guildId, uid, isChairman)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiPractitionerScheduleData getMultiChannelListByAccess failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	accessChannelIdMap := make(map[uint32]bool)
	for _, c := range accessChannelIdList {
		accessChannelIdMap[c] = true
	}
	if len(req.GetChannelIdList()) > 0 {
		// 有查看权限的房间
		for _, c := range req.GetChannelIdList() {
			if _, ok := accessChannelIdMap[c]; ok {
				cidList = append(cidList, c)
			}
		}
	} else {
		cidList = accessChannelIdList
	}

	m := models.GetModelServer()

	var start, end time.Time
	start, err = time.ParseInLocation("2006-01-02", req.GetDateStart(), time.Local)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiPractitionerScheduleData time.Parse failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, "日期参数错误", nil)
		return
	}
	end, err = time.ParseInLocation("2006-01-02", req.GetDateEnd(), time.Local)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiPractitionerScheduleData time.Parse failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, "日期参数错误", nil)
		return
	}

	contractUidList := make([]uint32, 0)
	if len(req.GetTtidList()) > 0 {
		ttidList := req.GetTtidList()
		if len(ttidList) > 100 {
			ttidList = ttidList[:100]
		}
		uidList, err := batGetUidByTid(ctx, ttidList)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetMultiPractitionerScheduleData batGetUidByTid failed: err: %+v", err)
			_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
			return
		}

		var wg sync.WaitGroup
		var mu sync.Mutex
		results := make(chan uint32, len(uidList))

		// 回溯签约变更记录，查询时间内是否签约
		for _, u := range uidList {
			wg.Add(1)
			go func(uid uint32) {
				defer wg.Done()
				isContract, err := checkUserMultiContractInRange(ctx, uid, guildId, start, end)
				if err != nil {
					log.ErrorWithCtx(ctx, "GetMultiPractitionerScheduleData checkUserMultiContractInRange failed: err: %+v", err)
					return
				}
				if isContract {
					results <- uid
				}
			}(u)
		}

		go func() {
			wg.Wait()
			close(results)
		}()

		for u := range results {
			mu.Lock()
			contractUidList = append(contractUidList, u)
			mu.Unlock()
		}

		log.InfoWithCtx(ctx, "GetMultiPractitionerScheduleData uid:%d, req.GetTtidList:%+v, req.UidList:%+v, contractUidList:%+v", uid, req.GetTtidList(), uidList, contractUidList)
	}

	// 查询了uid，但未签约或无权访问，返回空
	if len(cidList) == 0 || (len(req.GetTtidList()) > 0 && len(contractUidList) == 0) {
		log.DebugWithCtx(ctx, "GetMultiPractitionerScheduleData end req:%v resp:%v", req, resp)
		_ = web.ServeAPIJson(w, resp)
		return
	}
	// 没有cid不查询，数仓会报错

	tmpHourList := utils.ClearRepeatUint32(req.GetHourList())
	hourList := make([]string, 0)
	for _, h := range tmpHourList {
		hourList = append(hourList, fmt.Sprintf("%02d", h)) // 1 -> 01
	}

	const MaxLimit = 1000
	if req.GetLimit() == 0 {
		req.Limit = 10
	}
	if req.GetLimit() > MaxLimit {
		req.Limit = MaxLimit
	}

	startStrTm := req.GetDateStart()
	endStrTm := req.GetDateEnd()
	switch api.GetMultiPractitionerScheduleDataReq_TimeDimType(req.GetTimeDimType()) {
	case api.GetMultiPractitionerScheduleDataReq_TimeDimType_Week:
		startStrTm = start.AddDate(0, 0, -GetWeekDayOffset(start)+1).Format("2006-01-02")
		endStrTm = end.AddDate(0, 0, -GetWeekDayOffset(end)+1).Format("2006-01-02")
	case api.GetMultiPractitionerScheduleDataReq_TimeDimType_Month:
		startStrTm = time.Date(start.Year(), start.Month(), 1, 0, 0, 0, 0, time.Local).Format("2006-01-02")
		endStrTm = time.Date(end.Year(), end.Month(), 1, 0, 0, 0, 0, time.Local).Format("2006-01-02")
	}

	dwReq := &datahouse.MultiMemberMicStatReq{
		Offset:        req.GetOffset(),
		Limit:         req.GetLimit(),
		StartTime:     startStrTm,
		EndTime:       endStrTm,
		HourList:      hourList,
		ChannelIdList: cidList,
		UidList:       contractUidList,
		TimeDim:       MapTimeDimType2QueryStr[api.GetMultiPractitionerScheduleDataReq_TimeDimType(req.GetTimeDimType())],
	}
	list, total, err := datahouse.QueryMultiMemberMicStatData(ctx, dwReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiPractitionerScheduleData QueryMultiMemberMicStatData failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, "系统错误", nil)
		return
	}

	respUidList := make([]uint32, 0)
	respCidList := make([]uint32, 0)
	var lastUpdateTime time.Time
	for i, item := range list {
		if i == 0 {
			lastUpdateTimeStr := item.UpdateTime
			lastUpdateTime, err = time.ParseInLocation("2006-01-02 15:04:05", lastUpdateTimeStr, time.Local)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetMultiPractitionerScheduleData time.Parse failed: err: %+v, lastUpdateTimeStr: %+v", err, lastUpdateTimeStr)
				return
			}
			// 小时取整
			lastUpdateTime = lastUpdateTime.Truncate(time.Hour)
		}
		respUidList = append(respUidList, item.UserID)
		respCidList = append(respCidList, item.RoomID)
	}

	log.DebugWithCtx(ctx, "GetMultiPractitionerScheduleData respUidList:%+v, respCidList:%+v", respUidList, respCidList)

	userInfoMap, err := batGetUser(ctx, respUidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiPractitionerScheduleData batGetUser failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	channelMap, err := m.ChannelCli.BatchGetChannelSimpleInfo(ctx, 0, respCidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiPractitionerScheduleData BatchGetChannelSimpleInfo failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	channelTagMap, err := getChannelTag(ctx, respCidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiPractitionerScheduleData getChannelTag failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	if len(list) > 0 && !lastUpdateTime.IsZero() {
		resp.LastUpdateTime = uint64(lastUpdateTime.Unix())
	}
	resp.Total = total
	resp.TimeDimType = req.GetTimeDimType()
	for _, item := range list {
		resp.ScheduleInfoList = append(resp.ScheduleInfoList, &api.MultiPractitionerScheduleInfo{
			Date:                    item.DataDate,
			ChannelId:               item.RoomID,
			ChannelViewId:           channelMap[item.RoomID].GetChannelViewId(),
			ChannelName:             channelMap[item.RoomID].GetName(),
			ChannelTag:              MapTagId2Name[channelTagMap[item.RoomID]],
			Hour:                    item.DataHour,
			Uid:                     item.UserID,
			Ttid:                    userInfoMap[item.UserID].GetAlias(),
			Nickname:                userInfoMap[item.UserID].GetNickname(),
			Username:                userInfoMap[item.UserID].GetUsername(),
			IsEmployed:              strings.TrimSpace(item.IsEmployment) == "是",
			ValidDuration_0:         item.MicDuration0,
			ValidDurationOther:      item.MicDurationOther,
			ReceiveGiftTotal:        item.AnchorIncome,
			ReceiveHundredGiftTotal: item.AnchorIncome100Up,
		})
	}

	log.DebugWithCtx(ctx, "GetMultiPractitionerScheduleData end req:%v resp:%v", req, resp)
	_ = web.ServeAPIJson(w, resp)
}

func GetMultiChannelEmploymentInfoList(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetMultiChannelEmploymentInfoListReq{}
	resp := &api.GetMultiChannelEmploymentInfoListResp{
		EmploymentInfoList: make([]*api.MultiChannelEmploymentInfo, 0),
	}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiChannelEmploymentInfoList Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.InfoWithCtx(ctx, "GetMultiChannelEmploymentInfoList begin uid:%d %+v", authInfo.UserID, req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	_, hasPer, respCode, respMsg, _ := checkUserHasPer(ctx, uid, guildId, api.GuildMultiPlayDataType_MultiPlayScheduleData.String())
	if !hasPer {
		_ = web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}
	isChairman, serr := CheckIsLibraryGuildChairman(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetMultiChannelEmploymentInfoList failed to CheckIsLibraryGuildChairman [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	guildIds, cids, err := getMultiChannelListByAccess(ctx, guildId, uid, isChairman)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiChannelEmploymentInfoList getMultiChannelListByAccess failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	log.DebugWithCtx(ctx, "GetMultiChannelEmploymentInfoList cids:%+v", cids)

	m := models.GetModelServer()
	listResp, err := m.GuildManagementCli.GetMultiChannelEmploymentInfoList(ctx, &guild_management_svr.GetMultiChannelEmploymentInfoListReq{
		GuildIdList:   guildIds,
		ChannelIdList: cids,
		Offset:        0,
		Limit:         9999,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiChannelEmploymentInfoList GetMultiChannelEmploymentInfoList failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	employmentInfoMap := make(map[uint32]*guild_management_svr.MultiChannelEmploymentInfo)
	for _, item := range listResp.GetEmploymentInfoList() {
		employmentInfoMap[item.ChannelId] = item
	}

	adminUidList := make([]uint32, 0)
	for _, item := range listResp.GetEmploymentInfoList() {
		adminUidList = append(adminUidList, item.GetAdminList()...)
	}
	adminUidMap, err := batGetUser(ctx, adminUidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiChannelEmploymentInfoList batGetUser failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	contractInfoMap, err := getAnchorContractInfoV2(ctx, adminUidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiChannelEmploymentInfoList getAnchorContractInfo failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	channelInfoMap, err := m.ChannelCli.BatchGetChannelSimpleInfo(ctx, 0, cids)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiChannelEmploymentInfoList BatchGetChannelSimpleInfo failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	employmentInfoList := make([]*api.MultiChannelEmploymentInfo, 0)

	for _, cid := range cids {
		item := employmentInfoMap[cid]
		adminList := make([]*api.MultiChannelEmploymentInfo_UserInfo, 0)
		for _, u := range item.GetAdminList() {
			if user, ok := adminUidMap[u]; ok {
				var isContract bool
				if c, ok1 := contractInfoMap[u]; ok1 && c.GetContract().GetGuildId() > 0 {
					if utils.IsInSlice(c.GetAnchorIdentityList(), uint32(api.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER)) {
						isContract = true
					}
				}
				adminList = append(adminList, &api.MultiChannelEmploymentInfo_UserInfo{
					Uid:        u,
					Ttid:       user.GetAlias(),
					Nickname:   user.GetNickname(),
					Username:   user.GetUsername(),
					IsContract: isContract,
				})
			}
		}
		employmentInfoList = append(employmentInfoList, &api.MultiChannelEmploymentInfo{
			ChannelId:     cid,
			ChannelViewId: channelInfoMap[cid].GetChannelViewId(),
			ChannelName:   channelInfoMap[cid].GetName(),
			ChannelAdmin:  adminList,
			EmployeeNum:   uint32(len(item.GetEmployeeList())),
		})
	}

	sort.Slice(employmentInfoList, func(i, j int) bool {
		iCidLen := len(employmentInfoList[i].ChannelAdmin)
		jCidLen := len(employmentInfoList[j].ChannelAdmin)
		if iCidLen == jCidLen {
			return employmentInfoList[i].ChannelId > employmentInfoList[j].ChannelId
		}
		return iCidLen > jCidLen
	})

	resp.EmploymentInfoList = employmentInfoList
	resp.Total = uint32(len(employmentInfoList))
	log.DebugWithCtx(ctx, "GetMultiChannelEmploymentInfoList end req:%v resp:%v", req, resp)
	_ = web.ServeAPIJson(w, resp)
}

func GetMultiChannelEmploymentInfo(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetMultiChannelEmploymentInfoReq{}
	resp := &api.GetMultiChannelEmploymentInfoResp{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiChannelEmploymentInfo Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.InfoWithCtx(ctx, "GetMultiChannelEmploymentInfo begin uid:%d %+v", authInfo.UserID, req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	_, hasPer, respCode, respMsg, _ := checkUserHasPer(ctx, uid, guildId, api.GuildMultiPlayDataType_MultiPlayScheduleData.String())
	if !hasPer {
		_ = web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}
	isChairman, serr := CheckIsLibraryGuildChairman(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetMultiChannelEmploymentInfo failed to CheckIsLibraryGuildChairman [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	if req.GetChannelId() == 0 {
		log.ErrorWithCtx(ctx, "GetMultiChannelEmploymentInfo failed to CheckIsLibraryGuildChairman [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, -500, "房间ID为空", nil)
		return
	}
	cid := req.GetChannelId()

	_, accessChannelIdList, err := getMultiChannelListByAccess(ctx, guildId, uid, isChairman)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiChannelEmploymentInfo getMultiChannelListByAccess failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	if !utils.IsInSlice(accessChannelIdList, cid) {
		log.Warnf("GetMultiChannelEmploymentInfo err: uid:%d is not admin of channel:%d", uid, cid)
		_ = web.ServeAPICodeJson(w, -500, "权限不足", nil)
		return
	}

	m := models.GetModelServer()
	infoResp, err := m.GuildManagementCli.GetMultiChannelEmploymentInfo(ctx, &guild_management_svr.GetMultiChannelEmploymentInfoReq{
		ChannelId: cid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiChannelEmploymentInfo GetMultiChannelEmploymentInfo failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	employeeInfo := infoResp.GetEmploymentInfo()
	respUidList := make([]uint32, 0)
	respUidList = append(respUidList, employeeInfo.GetAdminList()...)
	respUidList = append(respUidList, employeeInfo.GetEmployeeList()...)

	log.DebugWithCtx(ctx, "GetMultiChannelEmploymentInfoList cid:%d employeeInfo:%+v respUidList: %+v", cid, employeeInfo, respUidList)

	employeeInfoMap, err := batGetUser(ctx, respUidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiChannelEmploymentInfo batGetUser failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	contractInfoMap, err := getAnchorContractInfoV2(ctx, respUidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiChannelEmploymentInfo getAnchorContractInfo failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	cInfo, err := m.ChannelCli.GetChannelSimpleInfo(ctx, 0, req.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiChannelEmploymentInfo GetChannelSimpleInfo failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	employeeList := make([]*api.MultiChannelEmploymentInfo_UserInfo, 0)
	adminList := make([]*api.MultiChannelEmploymentInfo_UserInfo, 0)

	for _, u := range employeeInfo.GetAdminList() {
		if user, ok := employeeInfoMap[u]; ok {
			var isContract bool
			if c, ok1 := contractInfoMap[u]; ok1 && c.GetContract().GetGuildId() > 0 {
				if utils.IsInSlice(c.GetAnchorIdentityList(), uint32(api.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER)) {
					isContract = true
				}
			}
			adminList = append(adminList, &api.MultiChannelEmploymentInfo_UserInfo{
				Uid:        u,
				Ttid:       user.GetAlias(),
				Nickname:   user.GetNickname(),
				Username:   user.GetUsername(),
				IsContract: isContract,
			})
		}
	}
	for _, u := range employeeInfo.GetEmployeeList() {
		if user, ok := employeeInfoMap[u]; ok {
			var isContract bool
			if c, ok1 := contractInfoMap[u]; ok1 && c.GetContract().GetGuildId() > 0 {
				if utils.IsInSlice(c.GetAnchorIdentityList(), uint32(api.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER)) {
					isContract = true
				}
			}
			employeeList = append(employeeList, &api.MultiChannelEmploymentInfo_UserInfo{
				Uid:        u,
				Ttid:       user.GetAlias(),
				Nickname:   user.GetNickname(),
				Username:   user.GetUsername(),
				IsContract: isContract,
			})
		}
	}

	resp.EmploymentInfo = &api.MultiChannelEmploymentInfo{
		ChannelId:     cInfo.GetChannelId(),
		ChannelViewId: cInfo.GetChannelViewId(),
		ChannelName:   cInfo.GetName(),
		ChannelAdmin:  adminList,
		EmployeeNum:   uint32(len(employeeList)),
		EmployeeList:  employeeList,
	}

	log.DebugWithCtx(ctx, "GetMultiChannelEmploymentInfo end req:%v resp:%v", req, resp)
	_ = web.ServeAPIJson(w, resp)
}

// SetMultiChannelEmploymentInfo 设置房间任职成员
func SetMultiChannelEmploymentInfo(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.SetMultiChannelEmploymentInfoReq{}
	resp := &api.SetMultiChannelEmploymentInfoResp{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetMultiChannelEmploymentInfo Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	log.InfoWithCtx(ctx, "SetMultiChannelEmploymentInfo begin uid:%d %+v", authInfo.UserID, req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	_, hasPer, respCode, respMsg, _ := checkUserHasPer(ctx, uid, guildId, api.GuildMultiPlayDataType_MultiPlayScheduleData.String())
	if !hasPer {
		_ = web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}
	isChairman, serr := CheckIsLibraryGuildChairman(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "SetMultiChannelEmploymentInfo failed to CheckIsLibraryGuildChairman [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}
	m := models.GetModelServer()

	cid := req.GetChannelId()
	employeeUidList := req.GetEmployeeUidList()

	guildGroup, accessChannelIdList, err := getMultiChannelListByAccess(ctx, guildId, uid, isChairman)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetMultiChannelEmploymentInfo getMultiChannelListByAccess failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	if !utils.IsInSlice(accessChannelIdList, cid) {
		log.Warnf("SetMultiChannelEmploymentInfo failed: uid:%d is not admin of channel:%d", uid, cid)
		_ = web.ServeAPICodeJson(w, -500, "权限不足", nil)
		return
	}

	// 检查设置的任职成员属于该公会
	contractInfoMap, err := getAnchorContractInfoV2(ctx, employeeUidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetMultiChannelEmploymentInfo getAnchorContractInfo failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	noMultiUidList := make([]uint32, 0)
	for _, u := range employeeUidList {
		contract := contractInfoMap[u]
		g := contract.GetContract().GetGuildId()
		isMultiMember := utils.IsInSlice(contract.GetAnchorIdentityList(), uint32(api.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER))
		log.DebugWithCtx(ctx, "SetMultiChannelEmploymentInfo u:%d g:%d guildGroup:%+v isMultiMember:%v", u, g, guildGroup, isMultiMember)
		if !utils.IsInSlice(guildGroup, g) || !isMultiMember {
			log.ErrorWithCtx(ctx, "SetMultiChannelEmploymentInfo failed: uid:%d is not belong to guild:%d", u, guildId)
			noMultiUidList = append(noMultiUidList, u)
		}
	}

	if len(noMultiUidList) > 0 {
		mapUid2User, err := batGetUser(ctx, noMultiUidList)
		if err != nil {
			log.ErrorWithCtx(ctx, "SetMultiChannelEmploymentInfo batGetUser failed: err: %+v", err)
			_ = web.ServeAPICodeJson(w, -500, "获取用户信息错误", nil)
			return
		}

		idStr := ""
		for i, u := range noMultiUidList {
			if i == len(noMultiUidList)-1 {
				idStr += fmt.Sprintf("\"%s\"", mapUid2User[u].GetAlias())
			} else {
				idStr += fmt.Sprintf("\"%s\",", mapUid2User[u].GetAlias())
			}
		}

		_ = web.ServeAPICodeJson(w, -500, fmt.Sprintf("ttid为%s的用户已经不是本公会多人互动签约成员", idStr), nil)
		return
	}

	_, err = m.GuildManagementCli.SetMultiChannelEmploymentInfo(ctx, &guild_management_svr.SetMultiChannelEmploymentInfoReq{
		GuildId:         guildId,
		ChannelId:       cid,
		EmployeeUidList: employeeUidList,
		OperatorUid:     uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SetMultiChannelEmploymentInfo SetMultiChannelEmploymentInfo failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	log.DebugWithCtx(ctx, "SetMultiChannelEmploymentInfo end req:%v resp:%v", req, resp)
	_ = web.ServeAPIJson(w, resp)
}

// SetMultiChannelAdminInfo 会长设置厅管
func SetMultiChannelAdminInfo(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.SetMultiChannelAdminInfoReq{}
	resp := &api.SetMultiChannelAdminInfoResp{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetMultiChannelAdminInfo Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.InfoWithCtx(ctx, "SetMultiChannelAdminInfo begin uid:%d %+v", authInfo.UserID, req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	_, hasPer, respCode, respMsg, _ := checkUserHasPer(ctx, uid, guildId, api.GuildMultiPlayDataType_MultiPlayScheduleData.String())
	if !hasPer {
		_ = web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}
	isChairman, serr := CheckIsLibraryGuildChairman(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "SetMultiChannelAdminInfo failed to CheckIsLibraryGuildChairman [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	adminUid := req.GetChannelAdminUid()
	cids := req.GetChannelIdList()

	if !isChairman {
		// 仅会长能操作
		log.Warnf("SetMultiChannelAdminInfo failed: uid:%d is not chairman", uid)
		_ = web.ServeAPICodeJson(w, -500, "权限不足", nil)
		return
	}

	m := models.GetModelServer()
	guildGroup := m.GetSignDyConfig().GetGuildGroup(guildId)

	// 检查设置的任职成员属于该公会
	contractInfoMap, err := getAnchorContractInfoV2(ctx, []uint32{adminUid})
	if err != nil {
		log.ErrorWithCtx(ctx, "SetMultiChannelAdminInfo getAnchorContractInfoV2 failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	contract := contractInfoMap[adminUid]
	g := contract.GetContract().GetGuildId()
	isMultiMember := utils.IsInSlice(contract.GetAnchorIdentityList(), uint32(api.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER))
	if !utils.IsInSlice(guildGroup, g) || !isMultiMember {
		log.ErrorWithCtx(ctx, "SetMultiChannelAdminInfo failed: uid:%d is not belong to guild:%d", adminUid, guildId)
		_ = web.ServeAPICodeJson(w, -500, "设置的厅管不是该公会多人互动签约成员", nil)
		return
	}

	_, err = m.GuildManagementCli.SetMultiChannelAdminInfo(ctx, &guild_management_svr.SetMultiChannelAdminInfoReq{
		GuildId:         guildId,
		ChannelAdminUid: adminUid,
		ChannelIdList:   cids,
		OperatorUid:     uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SetMultiChannelAdminInfo SetMultiChannelAdminInfo failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	log.DebugWithCtx(ctx, "SetMultiChannelAdminInfo end req:%v resp:%v", req, resp)
	_ = web.ServeAPIJson(w, resp)
}

func BatchSetMultiChannelEmploymentInfo(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.BatchSetMultiChannelEmploymentInfoReq{}
	resp := &api.BatchSetMultiChannelEmploymentInfoResp{
		EmploymentList: make([]*api.BatchSetMultiChannelEmploymentInfoResp_Item, 0),
	}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchSetMultiChannelEmploymentInfo Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.InfoWithCtx(ctx, "BatchSetMultiChannelEmploymentInfo begin uid:%d %+v", authInfo.UserID, req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	_, hasPer, respCode, respMsg, _ := checkUserHasPer(ctx, uid, guildId, api.GuildMultiPlayDataType_MultiPlayScheduleData.String())
	if !hasPer {
		_ = web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}
	isChairman, serr := CheckIsLibraryGuildChairman(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "BatchSetMultiChannelEmploymentInfo failed to CheckIsLibraryGuildChairman [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}
	m := models.GetModelServer()

	guildGroup := m.GetSignDyConfig().GetGuildGroup(guildId)
	reqChannelViewIds := make([]string, 0)
	reqTTIdList := make([]string, 0)

	ttid2UidMap := make(map[string]uint32)
	view2CidMap := make(map[string]uint32)

	for _, item := range req.GetEmploymentList() {
		reqChannelViewIds = append(reqChannelViewIds, item.GetChannelViewId())
		reqTTIdList = append(reqTTIdList, item.GetEmployeeTtid())
	}

	channelInfoMap, err := m.ChannelCli.BatchGetChannelSimpleInfoByViewId(ctx, 0, utils.CleanRepeat(reqChannelViewIds))
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchSetMultiChannelEmploymentInfo BatchGetChannelSimpleInfoByViewId failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	for _, item := range channelInfoMap {
		view2CidMap[item.GetChannelViewId()] = item.GetChannelId()
	}

	uidList, err := batGetUidByTid(ctx, utils.CleanRepeat(reqTTIdList))
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchSetMultiChannelEmploymentInfo batGetUidByTid failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	contractUidList, err := filterMultiContractMember(ctx, uidList, guildId, isChairman)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchSetMultiChannelEmploymentInfo filterMultiContractMember failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	userInfoMap, err := batGetUser(ctx, contractUidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchSetMultiChannelEmploymentInfo batGetUser failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	for _, item := range userInfoMap {
		ttid2UidMap[item.GetAlias()] = item.GetUid()
	}

	adminCidsResp, err := m.GuildManagementCli.GetChannelListByAdminUid(ctx, &guild_management_svr.GetChannelListByAdminUidReq{
		AdminUid: uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchSetMultiChannelEmploymentInfo GetChannelListByAdminUid failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	adminCids := adminCidsResp.GetChannelList()

	log.DebugWithCtx(ctx, "BatchSetMultiChannelEmploymentInfo req:%+v reqChannelViewIds:%+v, reqTTIdList:%+v, ttid2UidMap:%+v, view2CidMap:%+v",
		req, reqChannelViewIds, reqTTIdList, ttid2UidMap, view2CidMap)

	appendFailedResp := func(channelViewId string, employeeTtid string, reason string) {
		resp.EmploymentList = append(resp.EmploymentList, &api.BatchSetMultiChannelEmploymentInfoResp_Item{
			ChannelViewId: channelViewId,
			EmployeeTtid:  employeeTtid,
			Ret:           api.BatchSetMultiChannelEmploymentInfoResp_ResultFailed,
			Reason:        reason,
		})
	}

	reqEmployeeList := make([]*guild_management_svr.BatchSetMultiChannelEmploymentInfoReq_Item, 0)
	noRepeat := make(map[string]struct{})

	for _, item := range req.GetEmploymentList() {
		ttid := item.GetEmployeeTtid()
		viewId := item.GetChannelViewId()

		itemUid, ok := ttid2UidMap[ttid]
		if !ok {
			log.Warnf("BatchSetMultiChannelEmploymentInfo err: ttid:%s not found", ttid)
			appendFailedResp(viewId, ttid, "错误成员")
			continue
		}

		itemCid, ok := view2CidMap[viewId]
		if !ok {
			log.Warnf("BatchSetMultiChannelEmploymentInfo err: viewId:%s not found", viewId)
			appendFailedResp(viewId, ttid, "错误房间")
			continue
		}

		// 忽略重复项
		noRepeatKey := fmt.Sprintf("%d_%d", itemCid, itemUid)
		if _, ok := noRepeat[noRepeatKey]; ok {
			log.Warnf("BatchSetMultiChannelEmploymentInfo err: channel:%d employee:%d repeat, ignore", itemCid, itemUid)
			continue
		}
		noRepeat[noRepeatKey] = struct{}{}

		itemCidInfo, ok := channelInfoMap[itemCid]
		if !ok {
			log.Warnf("BatchSetMultiChannelEmploymentInfo err: channel:%d not found", itemCid)
			appendFailedResp(viewId, ttid, "错误房间")
			continue
		}

		// 检擦房间所属公会是否是当前公会或在当前公会组中
		if !utils.IsInSlice(guildGroup, itemCidInfo.GetBindId()) {
			log.Warnf("BatchSetMultiChannelEmploymentInfo err: channel:%d is not in guild:%d", itemCid, guildId)
			appendFailedResp(viewId, ttid, "错误房间")
			continue
		}

		// 如果不是会长，检查是否是厅管
		if !isChairman {
			if !utils.IsInSlice(adminCids, itemCid) {
				log.Warnf("BatchSetMultiChannelEmploymentInfo err: uid:%d is not admin of channel:%d", uid, itemCid)
				appendFailedResp(viewId, ttid, "错误房间")
				continue
			}
		}

		itemGuildId := itemCidInfo.GetBindId()
		reqEmployeeList = append(reqEmployeeList, &guild_management_svr.BatchSetMultiChannelEmploymentInfoReq_Item{
			GuildId:     itemGuildId,
			ChannelId:   itemCid,
			EmployeeUid: itemUid,
		})
	}

	_, err = m.GuildManagementCli.BatchSetMultiChannelEmploymentInfo(ctx, &guild_management_svr.BatchSetMultiChannelEmploymentInfoReq{
		EmploymentList: reqEmployeeList,
		OperatorUid:    uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchSetMultiChannelEmploymentInfo BatchSetMultiChannelEmploymentInfo failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	// success
	for _, item := range reqEmployeeList {
		resp.EmploymentList = append(resp.EmploymentList, &api.BatchSetMultiChannelEmploymentInfoResp_Item{
			ChannelViewId: channelInfoMap[item.GetChannelId()].GetChannelViewId(),
			EmployeeTtid:  userInfoMap[item.GetEmployeeUid()].GetAlias(),
			Ret:           api.BatchSetMultiChannelEmploymentInfoResp_ResultSuccess,
			Reason:        "",
		})
	}

	log.DebugWithCtx(ctx, "BatchSetMultiChannelEmploymentInfoReq end req:%v resp:%v", req, resp)
	_ = web.ServeAPIJson(w, resp)
}

func GetChannelListByAdminUid(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetChannelListByAdminUidReq{}
	resp := &api.GetChannelListByAdminUidResp{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelListByAdminUid Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.InfoWithCtx(ctx, "GetChannelListByAdminUid begin uid:%d %+v", authInfo.UserID, req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	_, hasPer, respCode, respMsg, _ := checkUserHasPer(ctx, uid, guildId, api.GuildMultiPlayDataType_MultiPlayScheduleData.String())
	if !hasPer {
		_ = web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}
	isChairman, serr := CheckIsLibraryGuildChairman(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetChannelListByAdminUid failed to CheckIsLibraryGuildChairman [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}
	if !isChairman {
		log.ErrorWithCtx(ctx, "GetChannelListByAdminUid failed to CheckGuildChairman [%+v]. User is not guild chairman.", req)
		_ = web.ServeAPICodeJson(w, -505, "账号没有权限", nil)
		return
	}
	m := models.GetModelServer()

	listResp, err := m.GuildManagementCli.GetChannelListByAdminUid(ctx, &guild_management_svr.GetChannelListByAdminUidReq{
		AdminUid: req.GetAdminUid(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelListByAdminUid GetChannelListByAdminUid failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	cids := listResp.GetChannelList()
	cInfo, err := m.ChannelCli.BatchGetChannelSimpleInfo(ctx, 0, cids)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelListByAdminUid BatchGetChannelSimpleInfo failed: err: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	respChannelList := make([]*api.GetChannelListByAdminUidResp_ChannelInfo, 0)
	for _, cid := range cids {
		respChannelList = append(respChannelList, &api.GetChannelListByAdminUidResp_ChannelInfo{
			ChannelId:     cid,
			ChannelViewId: cInfo[cid].GetChannelViewId(),
			ChannelName:   cInfo[cid].GetName(),
		})
	}

	resp.ChannelList = respChannelList
	log.DebugWithCtx(ctx, "GetChannelListByAdminUid end req:%v resp:%v", req.GetAdminUid(), resp)
	_ = web.ServeAPIJson(w, resp)
}
