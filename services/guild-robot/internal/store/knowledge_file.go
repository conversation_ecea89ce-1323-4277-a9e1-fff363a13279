package store

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	pb "golang.52tt.com/protocol/services/tt/quicksilver/guild-robot"
	"time"
)

var CreateTableAmuseSQL = `CREATE TABLE IF NOT EXISTS guild_robot_knowledge_robot_type_amuse (
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    file_name varchar(255) NOT NULL COMMENT '文件名称',
    file_path varchar(255) NOT NULL COMMENT '文件url',
    knowledge_platform_id INT UNSIGNED NOT NULL COMMENT '知识库ID',
    knowledge_type INT UNSIGNED NOT NULL COMMENT '知识类型 7:语音 8:电竞 4:娱乐',
    is_enabled TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否启用 1 启用 2 禁用',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    creator <PERSON><PERSON>HA<PERSON>(50) NOT NULL COMMENT '创建人',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    updater VARCHAR(50) NOT NULL COMMENT '修改人',
    PRIMARY KEY (id),
    UNIQUE KEY uniq_file_path (file_path)
) ENGINE = INNODB DEFAULT CHARSET = utf8 COMMENT = '多人互动知识库上传信息表';`

var CreateTableYuyinSQL = `CREATE TABLE IF NOT EXISTS guild_robot_knowledge_robot_type_yuyin (
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    file_name varchar(255) NOT NULL COMMENT '文件名称',
    file_path varchar(255) NOT NULL COMMENT '文件url',
    knowledge_platform_id INT UNSIGNED NOT NULL COMMENT '知识库ID',
    knowledge_type INT UNSIGNED NOT NULL COMMENT '知识类型 7:语音 8:电竞 4:娱乐',
    is_enabled TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否启用 1 启用 2 禁用',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    creator VARCHAR(50) NOT NULL COMMENT '创建人',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    updater VARCHAR(50) NOT NULL COMMENT '修改人',
    PRIMARY KEY (id),
    UNIQUE KEY uniq_file_path (file_path)
) ENGINE = INNODB DEFAULT CHARSET = utf8 COMMENT = '语音直播知识库上传信息表';`

var CreateTableEsportSQL = `CREATE TABLE IF NOT EXISTS guild_robot_knowledge_robot_type_esport (
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    file_name varchar(255) NOT NULL COMMENT '文件名称',
    file_path varchar(255) NOT NULL COMMENT '文件url',
    knowledge_platform_id INT UNSIGNED NOT NULL COMMENT '知识库ID',
    knowledge_type INT UNSIGNED NOT NULL COMMENT '知识类型 7:语音 8:电竞 4:娱乐',
    is_enabled TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否启用 1 启用 2 禁用',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    creator VARCHAR(50) NOT NULL COMMENT '创建人',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    updater VARCHAR(50) NOT NULL COMMENT '修改人',
    PRIMARY KEY (id),
    UNIQUE KEY uniq_file_path (file_path)
) ENGINE = INNODB DEFAULT CHARSET = utf8 COMMENT = '电竞知识库上传信息表';`

// KnowledgeFile 知识库文档信息
type KnowledgeFile struct {
	ID                  uint32    `json:"id" db:"id"`
	FileName            string    `json:"file_name" db:"file_name"`
	FilePath            string    `json:"file_path" db:"file_path"`
	KnowledgePlatformId uint32    `json:"knowledge_platform_id" db:"knowledge_platform_id"`
	KnowledgeType       uint32    `json:"knowledge_type" db:"knowledge_type"`
	IsEnabled           uint32    `json:"is_enabled" db:"is_enabled"` // 是否启用 1 启用 2 禁用
	CreateTime          time.Time `json:"create_time" db:"create_time"`
	Creator             string    `json:"creator" db:"creator"`
	UpdateTime          time.Time `json:"update_time" db:"update_time"`
	Updater             string    `json:"updater" db:"updater"`
}

func (kf *KnowledgeFile) TableName(robotType pb.RobotType) string {
	return fmt.Sprintf("guild_robot_knowledge_%s", robotType.String())
}

const (
	Enabled  = 1
	Disabled = 2
)

// CreateOrUpdateKnowledgeFile 创建或更新知识库文档
func (s *Store) CreateOrUpdateKnowledgeFile(ctx context.Context, file *KnowledgeFile, robotType pb.RobotType) error {
	log.DebugWithCtx(ctx, "Creating or updating knowledge file: %+v", file)
	if file == nil {
		return fmt.Errorf("knowledge file is nil")
	}
	sql := fmt.Sprintf(`INSERT INTO %s (id, file_name, file_path, knowledge_platform_id, knowledge_type, is_enabled, create_time, creator, update_time, updater) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE file_name = VALUES(file_name), file_path = VALUES(file_path), knowledge_platform_id = VALUES(knowledge_platform_id), knowledge_type = VALUES(knowledge_type), is_enabled = VALUES(is_enabled), update_time = VALUES(update_time), updater = VALUES(updater)`, file.TableName(robotType))
	result, err := s.db.ExecContext(ctx, sql, file.ID, file.FileName, file.FilePath, file.KnowledgePlatformId, file.KnowledgeType, file.IsEnabled, file.CreateTime, file.Creator, file.UpdateTime, file.Updater)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to create or update knowledge file: %v", err)
		return err
	}
	insertId, _ := result.LastInsertId()
	file.ID = uint32(insertId)
	return nil
}

// GetKnowledgeFileList 获取知识库文档列表
func (s *Store) GetKnowledgeFileList(ctx context.Context, robotType pb.RobotType, knowledgeType uint32, page, limit int,
	searchQuery string) ([]*KnowledgeFile, int64, error) {
	log.DebugWithCtx(ctx, "Getting knowledge file list for robotType: %s, knowledgeType: %d, page: %d, limit: %d, searchQuery: %s", robotType, knowledgeType, page, limit, searchQuery)
	file := &KnowledgeFile{}
	args := make([]interface{}, 0)
	result := make([]*KnowledgeFile, 0)
	sql := fmt.Sprintf(`SELECT id, file_name, file_path, knowledge_type, create_time, creator, update_time, updater FROM %s WHERE is_enabled = ?`, file.TableName(robotType))
	countSql := fmt.Sprintf(`SELECT COUNT(*) FROM %s WHERE is_enabled = ?`, file.TableName(robotType))
	args = append(args, Enabled)
	if knowledgeType > 0 {
		sql += ` AND knowledge_type = ?`
		countSql += ` AND knowledge_type = ?`
		args = append(args, knowledgeType)
	}
	if searchQuery != "" {
		sql += ` AND file_name LIKE ?`
		countSql += ` AND file_name LIKE ?`
		searchPattern := "%" + searchQuery + "%"
		args = append(args, searchPattern)
	}
	var totalCount int64
	err := s.db.GetContext(ctx, &totalCount, countSql, args...)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to get total count of knowledge files: %v", err)
		return nil, 0, err
	}
	if totalCount < 1 {
		log.DebugWithCtx(ctx, "No knowledge files found for robotType: %s, knowledgeType: %d", robotType, knowledgeType)
		return result, totalCount, nil
	}
	offset := (page - 1) * limit
	sql += ` ORDER BY update_time DESC LIMIT ? OFFSET ?`
	args = append(args, limit, offset)
	err = s.db.SelectContext(ctx, &result, sql, args...)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to get knowledge file list: %v", err)
		return nil, 0, err
	}

	return result, totalCount, nil
}

// GetKnowledgeFileByID 根据ID获取知识库文档
func (s *Store) GetKnowledgeFileByID(ctx context.Context, fileID uint32, robotType pb.RobotType) (*KnowledgeFile, error) {
	log.DebugWithCtx(ctx, "Getting knowledge file by ID: %d for robotType: %s", fileID, robotType)
	if fileID <= 0 {
		return nil, fmt.Errorf("invalid file ID: %d", fileID)
	}
	file := &KnowledgeFile{ID: fileID}
	sql := fmt.Sprintf(`SELECT id, file_name, file_path, knowledge_type, create_time, creator, update_time, updater FROM %s WHERE id = ?`, file.TableName(robotType))
	err := s.db.GetContext(ctx, file, sql, fileID)
	if err != nil {
		if mysql.IsNoRowsError(err) {
			log.DebugWithCtx(ctx, "Knowledge file not found for ID: %d", fileID)
			return nil, nil // 返回nil表示未找到文件
		}
		log.ErrorWithCtx(ctx, "Failed to get knowledge file by ID: %v", err)
		return nil, err
	}

	return file, nil
}

// DeleteKnowledgeFile 软删除知识库文档
func (s *Store) DeleteKnowledgeFile(ctx context.Context, fileID uint32, robotType pb.RobotType) error {
	log.DebugWithCtx(ctx, "Deleting knowledge file with ID: %d for robotType: %s", fileID, robotType)
	if fileID <= 0 {
		return fmt.Errorf("invalid file ID: %d", fileID)
	}
	file := &KnowledgeFile{ID: fileID}
	sql := fmt.Sprintf(`UPDATE %s SET is_enabled = ? WHERE id = ?`, file.TableName(robotType))
	_, err := s.db.ExecContext(ctx, sql, Disabled, fileID)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to delete knowledge file: %v", err)
		return err
	}

	return nil
}
