package store

import (
	"context"
	"fmt"
	pb "golang.52tt.com/protocol/services/tt/quicksilver/guild-robot"
	"time"

	mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
)

type Store struct {
	db mysql.DBx
}

func NewStore(ctx context.Context, cfg *mysqlConnect.MysqlConfig) (*Store, error) {
	db, err := mysqlConnect.NewClient(ctx, cfg)
	if err != nil {
		return nil, err
	}

	s := &Store{
		db: db,
	}

	// 创建表
	err = s.CreateTable(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create tables: %w", err)
	}

	return s, nil
}

func (s *Store) Close() error {
	return s.db.Close()
}

func (s *Store) CreateTable(ctx context.Context) error {
	_, err := s.db.ExecContext(ctx, CreateTableAmuseSQL)
	if err != nil {
		return err
	}

	_, err = s.db.ExecContext(ctx, CreateTableYuyinSQL)
	if err != nil {
		return err
	}

	_, err = s.db.ExecContext(ctx, CreateTableEsportSQL)
	if err != nil {
		return err
	}

	now := time.Now()
	thisMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.UTC)
	nextMonth := time.Date(now.Year(), now.Month()+1, 1, 0, 0, 0, 0, time.UTC)

	// 创建本月和下月的用户反馈表
	feedBack := &UserFeedback{}
	robotTypeList := []pb.RobotType{
		pb.RobotType_ROBOT_TYPE_AMUSE,
		pb.RobotType_ROBOT_TYPE_YUYIN,
		pb.RobotType_ROBOT_TYPE_ESPORT,
	}
	for _, robotType := range robotTypeList {
		_, err = s.db.ExecContext(ctx, fmt.Sprintf(CreateTableUserFeedbackSQL, feedBack.TableName(thisMonth, robotType)))
		if err != nil {
			return err
		}
		_, err = s.db.ExecContext(ctx, fmt.Sprintf(CreateTableUserFeedbackSQL, feedBack.TableName(nextMonth, robotType)))
		if err != nil {
			return err
		}
	}

	// 创建本月和下月的聊天消息表
	message := &Message{}
	_, err = s.db.ExecContext(ctx, fmt.Sprintf(CreateTableMessageSQL, message.TableName(thisMonth)))
	if err != nil {
		return err
	}
	_, err = s.db.ExecContext(ctx, fmt.Sprintf(CreateTableMessageSQL, message.TableName(nextMonth)))
	if err != nil {
		return err
	}

	return nil
}
