package store

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	"time"
)

var CreateTableMessageSQL = `CREATE TABLE IF NOT EXISTS %+v (
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    question varchar(255) NOT NULL COMMENT '问题',
    answer text COMMENT '回答',
    message_id varchar(255) NOT NULL COMMENT '消息ID',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (id),
    UNIQUE KEY uniq_message (message_id) COMMENT '唯一索引'
) ENGINE = INNODB DEFAULT CHARSET = utf8 COMMENT = '聊天消息记录';`

// Message 聊天消息记录
type Message struct {
	ID         uint64    `json:"id" db:"id"`                   // 自增主键
	Question   string    `json:"question" db:"question"`       // 问题
	Answer     string    `json:"answer" db:"answer"`           // 回答
	MessageID  string    `json:"message_id" db:"message_id"`   // 消息ID
	CreateTime time.Time `json:"create_time" db:"create_time"` // 创建时间
}

func (m *Message) TableName(createTs time.Time) string {
	t := time.Unix(createTs.Unix(), 0)
	return fmt.Sprintf("guild_robot_message_%+v", t.Format("200601"))
}

// AddOrUpdateMessage 添加或更新聊天消息记录
func (s *Store) AddOrUpdateMessage(ctx context.Context, info *Message) error {
	sql := fmt.Sprintf(`INSERT INTO %+v (question, answer, message_id, create_time) VALUES (?, ?, ?, ?) ON DUPLICATE KEY UPDATE answer = answer + VALUES(answer)`, info.TableName(info.CreateTime))
	_, err := s.db.ExecContext(ctx, sql, info.Question, info.Answer, info.MessageID, info.CreateTime)
	if err != nil {
		if mysql.IsNoRowsError(err) {
			log.WarnWithCtx(ctx, "AddOrUpdateMessage ExecContext no rows affected, messageID: %s", info.MessageID)
			return nil
		}
		log.ErrorWithCtx(ctx, "AddOrUpdateMessage Exec fail, err: %+v", err)
		return err
	}
	return nil
}

// GetMessageByID 根据消息ID获取聊天消息记录
func (s *Store) GetMessageByID(ctx context.Context, messageID string, createTime time.Time) (*Message, error) {
	var msg Message
	sql := fmt.Sprintf(`SELECT id, question, answer, message_id FROM %+v WHERE message_id = ?`, msg.TableName(createTime))
	err := s.db.GetContext(ctx, &msg, sql, messageID)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMessageByID GetContext fail, messageID: %s, err: %+v", messageID, err)
		return nil, err
	}
	return &msg, nil
}
