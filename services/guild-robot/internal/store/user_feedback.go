package store

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	pb "golang.52tt.com/protocol/services/tt/quicksilver/guild-robot"
	"time"
)

var CreateTableUserFeedbackSQL = `CREATE TABLE IF NOT EXISTS %+v (
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    question varchar(255) NOT NULL COMMENT '问题',
    answer text COMMENT '回答',
    message_id varchar(255) NOT NULL COMMENT '消息ID',
    is_useful tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否有用 0:否 1:是',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    creator VARCHAR(50) NOT NULL COMMENT '反馈人',
    PRIMARY KEY (id),
    UNIQUE KEY uniq_message (message_id, creator) COMMENT '唯一索引，防止同一人对同一消息重复反馈'
) ENGINE = INNODB DEFAULT CHARSET = utf8 COMMENT = '用户反馈记录';`

// UserFeedback 用户反馈记录
type UserFeedback struct {
	ID         uint64    `json:"id" db:"id"`                   // 自增主键
	Question   string    `json:"question" db:"question"`       // 问题
	Answer     string    `json:"answer" db:"answer"`           // 回答
	MessageID  string    `json:"message_id" db:"message_id"`   // 消息ID
	IsUseful   bool      `json:"is_useful" db:"is_useful"`     // 是否有用
	CreateTime time.Time `json:"create_time" db:"create_time"` // 创建时间
	Creator    string    `json:"creator" db:"creator"`         // 反馈人

}

func (u *UserFeedback) TableName(createTs time.Time, robotType pb.RobotType) string {
	t := time.Unix(createTs.Unix(), 0)
	return fmt.Sprintf("guild_robot_user_feedback_%+v_%+v", t.Format("200601"), robotType.String())
}

// AddOrUpdateUserFeedback 添加或更新用户反馈记录
func (s *Store) AddOrUpdateUserFeedback(ctx context.Context, info *UserFeedback, robotType pb.RobotType) error {
	sql := fmt.Sprintf(`INSERT INTO %+v (question, answer, message_id, is_useful, create_time, creator) VALUES (?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE is_useful = VALUES(is_useful), create_time = VALUES(create_time), creator = VALUES(creator)`, info.TableName(info.CreateTime, robotType))
	_, err := s.db.ExecContext(ctx, sql, info.Question, info.Answer, info.MessageID, info.IsUseful, info.CreateTime, info.Creator)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateUserFeedbackTable Exec fail, err: %+v", err)
		return err
	}
	return nil
}
