package entity

import (
	"context"
	"encoding/json"
	"golang.52tt.com/pkg/log"
)

type Card struct {
	Schema   string     `json:"schema,omitempty"`
	Config   *Config    `json:"config,omitempty"`
	Elements []*Element `json:"elements,omitempty"`
	Header   *Header    `json:"header,omitempty"`
	Body     *Body      `json:"body,omitempty"`
}

func (s *Card) String(ctx context.Context) string {
	if s == nil {
		return ""
	}

	cardByte, err := json.Marshal(s)
	if err != nil {
		log.ErrorWithCtx(ctx, "card.String Marshal failed... err is %v", err)
		return ""
	}

	return string(cardByte)
}

type Config struct {
	EnableForward  bool     `json:"enable_forward,omitempty"`
	WideScreenMode bool     `json:"wide_screen_mode,omitempty"`
	UpdateMulti    bool     `json:"update_multi,omitempty"`
	StreamingMode  bool     `json:"streaming_mode,omitempty"`
	Summary        *Summary `json:"summary,omitempty"`
}

type Summary struct {
	Content string `json:"content,omitempty"`
}

type Element struct {
	Tag      string        `json:"tag,omitempty"`
	Elements []*SubElement `json:"elements,omitempty"`
	Actions  []*Action     `json:"actions,omitempty"`
	Columns  []*Column     `json:"columns,omitempty"`
	Name     string        `json:"name,omitempty"`
	Text     *Text         `json:"text,omitempty"`
	Icon     *Icon         `json:"icon,omitempty"`
}

type Body struct {
	Elements []*BodyElement `json:"elements,omitempty"`
}

type BodyElement struct {
	Tag               string    `json:"tag,omitempty"`
	Content           string    `json:"content,omitempty"`
	Type              string    `json:"type,omitempty"`
	ElementId         string    `json:"element_id,omitempty"`
	HorizontalSpacing string    `json:"horizontal_spacing,omitempty"`
	HorizontalAlign   string    `json:"horizontal_align,omitempty"`
	Margin            string    `json:"margin,omitempty"`
	Columns           []*Column `json:"columns,omitempty"`
}

type SubElement struct {
	Tag               string                 `json:"tag,omitempty"`
	Content           string                 `json:"content,omitempty"`
	Type              string                 `json:"type,omitempty"`
	ActionType        string                 `json:"action_type,omitempty"`
	Name              string                 `json:"name,omitempty"`
	Placeholder       *Placeholder           `json:"placeholder,omitempty"`
	Width             string                 `json:"width,omitempty"`
	Required          bool                   `json:"required,omitempty"`
	Disabled          bool                   `json:"disabled,omitempty"`
	InitialOption     string                 `json:"initial_option,omitempty"`
	SelectedValues    []string               `json:"selected_values,omitempty"`
	Options           []*Option              `json:"options,omitempty"`
	FlexMode          string                 `json:"flex_mode,omitempty"`
	BackgroundStyle   string                 `json:"background_style,omitempty"`
	HorizontalSpacing string                 `json:"horizontal_spacing,omitempty"`
	HorizontalAlign   string                 `json:"horizontal_align,omitempty"`
	Columns           []*Column              `json:"columns,omitempty"`
	Text              *SubElementText        `json:"text,omitempty"`
	Value             map[string]interface{} `json:"value,omitempty"`
	Confirm           *Confirm               `json:"confirm,omitempty"`
	TextSize          string                 `json:"text_size,omitempty"`
	TextAlign         string                 `json:"text_align,omitempty"`
	Margin            string                 `json:"margin,omitempty"`
	Icon              *Icon                  `json:"icon,omitempty"`
	Size              string                 `json:"size,omitempty"`
	HoverTips         *HoverTips             `json:"hover_tips,omitempty"`
	Behaviors         []*Behavior            `json:"behaviors,omitempty"`
	ElementId         string                 `json:"element_id,omitempty"`
}

type Confirm struct {
	Title *Title `json:"title,omitempty"`
	Text  *Text  `json:"text,omitempty"`
}

type Value struct {
	Method string `json:"method,omitempty"`
}

type SubElementText struct {
	Tag     string `json:"tag,omitempty"`
	Content string `json:"content,omitempty"`
}

type Placeholder struct {
	Tag     string `json:"tag,omitempty"`
	Content string `json:"content,omitempty"`
}

type HoverTips struct {
	Tag     string `json:"tag,omitempty"`
	Content string `json:"content,omitempty"`
}

type Behavior struct {
	Type  string            `json:"type,omitempty"`
	Value map[string]string `json:"value,omitempty"`
}

type Option struct {
	Text  *Text  `json:"text,omitempty"`
	Icon  *Icon  `json:"icon,omitempty"`
	Value string `json:"value,omitempty"`
}

type Text struct {
	Tag       string `json:"tag,omitempty"`
	Content   string `json:"content,omitempty"`
	TextSize  string `json:"text_size,omitempty"`
	TextColor string `json:"text_color,omitempty"`
	Lines     string `json:"lines,omitempty"` // 默认最大显示函数
}

type Icon struct {
	Tag   string `json:"tag,omitempty"`
	Token string `json:"token,omitempty"`
	Color string `json:"color,omitempty"`
}

type Action struct {
	Tag                string                 `json:"tag,omitempty"`
	Text               *Text                  `json:"text,omitempty"`
	Type               string                 `json:"type,omitempty"`
	ComplexInteraction bool                   `json:"complex_interaction,omitempty"`
	ActionType         string                 `json:"action_type,omitempty"`
	Name               string                 `json:"name,omitempty"`
	Disabled           bool                   `json:"disabled,omitempty"`
	Value              map[string]interface{} `json:"value,omitempty"`
	Confirm            *Confirm               `json:"confirm,omitempty"`
}

type Column struct {
	Tag               string        `json:"tag,omitempty"`
	Width             string        `json:"width,omitempty"`
	Weight            int           `json:"weight,omitempty"`
	Elements          []*SubElement `json:"elements,omitempty"`
	Padding           string        `json:"padding,omitempty"`
	Direction         string        `json:"direction,omitempty"`
	HorizontalSpacing string        `json:"horizontal_spacing,omitempty"`
	HorizontalAlign   string        `json:"horizontal_align,omitempty"`
	VerticalSpacing   string        `json:"vertical_spacing,omitempty"`
	VerticalAlign     string        `json:"vertical_align,omitempty"`
	Margin            string        `json:"margin,omitempty"`
}

type Header struct {
	Template string  `json:"template,omitempty"`
	Title    *Title  `json:"title,omitempty"`
	UdIcon   *UdIcon `json:"ud_icon,omitempty"`
}

type Title struct {
	Tag     string `json:"tag,omitempty"`
	Content string `json:"content,omitempty"`
}

type UdIcon struct {
	Token string `json:"token,omitempty"`
	Style *Style `json:"style,omitempty"`
}

type Style struct {
	Color string `json:"color,omitempty"`
}

type AppMarketInfo struct {
	AppKey            string `yaml:"appKey"`
	AppName           string `yaml:"appName"`
	MarketChannelName string `yaml:"marketChannelName"`
	MarketChannelId   string `yaml:"marketChannelId"`
}

type MultiSelectInfo struct {
	Title              string
	Name               string
	Disabled           bool
	SelectedValues     []string
	AppMarketInfo      *AppMarketInfo
	PlaceholderContent string
	Options            []*Option
}

type FormInfo struct {
	Id                              string
	Name                            string
	Disabled                        bool
	MapSelectName2Selected          map[string]string
	MapMultiSelectName2SelectedList map[string][]string
	ButtonInfo                      *ButtonInfo
}

type SingleSelectInfo struct {
	Title              string
	Name               string
	Disabled           bool
	SelectedValue      string
	ColumnName         string
	PlaceholderContent string
	Options            []*Option
}

type OpenSwitchCardInfo struct {
	Id                   string
	Title                string
	ButtonInfo           *ButtonInfo
	TitleList            []string
	MapTitle2ContentList map[string][]string
}

type CardBuildInfo struct {
	Id         string
	Title      string
	ButtonInfo *ButtonInfo
}

type ButtonInfo struct {
	Disabled       bool
	ButtonContent  string
	ButtonType     string
	CallbackMethod string
	Value          map[string]interface{}
}

type ColumnOption struct {
	ColumnName string
}

// cmd ai机器人知识库信息结构
type AiCmdDetailInfo struct {
	CmdId    string // 知识库ID
	CmdValue string `json:"cmd_value"`
	CmdOwner string `json:"cmd_owner"`
	CmdSvr   string `json:"cmd_svr"`
}

const (
	CardSchemaV1 = ""
	CardSchemaV2 = "2.0"
)

const (
	TagDiv               = "div"     // 普通文本
	LarkMd               = "lark_md" // 普通文本下的飞书markdown
	TagText              = "text"
	TagSelect            = "select"
	TagOption            = "option"
	TagButton            = "button"
	TagInput             = "input"
	TagForm              = "form"
	TagPlainText         = "plain_text"
	TagSelectStatic      = "select_static"       // 单选下拉框
	TagMultiSelectStatic = "multi_select_static" // 多选下拉框
	TagAction            = "action"
	TagMarkdown          = "markdown"
	TagColumnSet         = "column_set"
	TagColumn            = "column"
	TagStandardIcon      = "standard_icon"
)

const (
	FlexModeStretch = "stretch"
)

const (
	AppDefaultFilled = "app-default_filled"
)

const (
	BackgroundStyleDefault = "default"
)

const (
	ActionTypeFormSubmit = "form_submit"
)

const (
	WidthWeighted = "weighted"
)

const (
	ColorBlue    = "blue"
	ColorYellow  = "yellow"
	ColorOrange  = "orange"
	ColorRed     = "red"
	ColorPurple  = "purple"
	ColorCarmine = "carmine"
)

const (
	IconCommunityTabOutLined = "community-tab_outlined"
)

const (
	Weight1 = 1
	Weight5 = 5
)

const (
	VerticalAlignTop = "top"
)

const (
	PositionTop    = "top"
	PositionCenter = "center"
)

const (
	ButtonTypeLaser      = "laser"
	ButtonCallbackMethod = "method"
)

const (
	DefaultButtonConfirmTitle = "请确认操作"
	DefaultButtonConfirmText  = "你确定要进行此操作吗？"
)

const (
	PrefixMulti                     = "multi-"
	PrefixSingle                    = "single-"
	VersionUpdateFormStart          = "version_update_form_start" // 版本更新启动流程表单
	VersionUpdateSmallGameRealName  = "version_update_small_game_real_name"
	VersionUpdateNewUserMsgLimit    = "version_update_new_user_msg_limit"
	VersionUpdateNeedOpenSw         = "version_update_need_open_sw"
	VersionUpdateYes                = "是"
	VersionUpdateNo                 = "否"
	VersionUpdateButtonNotifyOpenSw = "version_update_button_notify_open_sw"
)

const (
	TextSizeHeadingThree = "heading-3" // 18px
	TextSizeHeadingFour  = "heading-4" // 16px
	TextSizeNormal       = "normal"    // 14px
)

const (
	PXOne = "1px"
)

const (
	ValueTitleListOrder = "value_title_list_order"
)

// 版更相关

const (
	VersionUpdateProcessInit           = "初始化流程"
	VersionUpdateProcessStart          = "启动流程"
	VersionUpdateProcessOpenSw         = "开启版更开关流程"
	VersionUpdateProcessWaitStoreAudit = "等待商店审核流程"
	VersionUpdateProcessCloseSw        = "关闭版更开关流程"
	VersionUpdateProcessEnd            = "结束流程"
)

const (
	VersionUpdateProcessInitNum = iota
	VersionUpdateProcessStartNum
	VersionUpdateProcessOpenSwNum
	VersionUpdateProcessWaitStoreAuditNum
	VersionUpdateProcessCloseSwNum
	VersionUpdateProcessEndNum
)

var MapVersionUpdateProcess = map[string]int{
	VersionUpdateProcessInit:           VersionUpdateProcessInitNum,
	VersionUpdateProcessStart:          VersionUpdateProcessStartNum,
	VersionUpdateProcessOpenSw:         VersionUpdateProcessOpenSwNum,
	VersionUpdateProcessWaitStoreAudit: VersionUpdateProcessWaitStoreAuditNum,
	VersionUpdateProcessCloseSw:        VersionUpdateProcessCloseSwNum,
	VersionUpdateProcessEnd:            VersionUpdateProcessEndNum,
}
