package config

import (
	"errors"
	"gitlab.ttyuyin.com/avengers/tyr/core/config/ttconfig"
	pb "golang.52tt.com/protocol/services/tt/quicksilver/guild-robot"
	"sync/atomic"
)

type GuildRobotCardConfig struct {
	YuyinMessages    []CardMessage `json:"yuyin_messages"`
	yuyinMessageMap  map[string]string
	EsportMessages   []CardMessage `json:"esport_messages"`
	esportMessageMap map[string]string
	AmuseMessages    []CardMessage `json:"amuse_messages"`
	amuseMessageMap  map[string]string
}

type CardMessage struct {
	Key    string `json:"key"`     // 消息的唯一标识
	CardId string `json:"card_id"` // 卡片ID
}

// Format
// 配置加载时调用Format报错则返回错误
// 配置更新时调用Format报错则放弃此次更新
func (s *GuildRobotCardConfig) Format() error {
	s.amuseMessageMap = make(map[string]string)
	for _, msg := range s.AmuseMessages {
		if _, ok := s.amuseMessageMap[msg.Key]; ok {
			return errors.New("amuse_message key is duplicated")
		}
		s.amuseMessageMap[msg.Key] = msg.CardId
	}
	s.esportMessageMap = make(map[string]string)
	for _, msg := range s.EsportMessages {
		if _, ok := s.esportMessageMap[msg.Key]; ok {
			return errors.New("esport_message key is duplicated")
		}
		s.esportMessageMap[msg.Key] = msg.CardId
	}
	s.yuyinMessageMap = make(map[string]string)
	for _, msg := range s.YuyinMessages {
		if _, ok := s.yuyinMessageMap[msg.Key]; ok {
			return errors.New("yuyin_message key is duplicated")
		}
		s.yuyinMessageMap[msg.Key] = msg.CardId
	}
	return nil
}

var (
	atomicGuildRobotCardConfig *atomic.Value
)

// InitGuildRobotCardConfig
// 可以选择外部初始化或者直接init函数初始化
func InitGuildRobotCardConfig() error {
	cfg := &GuildRobotCardConfig{}
	atomCfg, err := ttconfig.AtomLoad("guild-robot-card", cfg)
	if nil != err {
		return err
	}
	atomicGuildRobotCardConfig = atomCfg
	return nil
}

func GetGuildRobotCardConfig() *GuildRobotCardConfig {
	return atomicGuildRobotCardConfig.Load().(*GuildRobotCardConfig)
}

func GetCardIdByRobotTypeAndKey(robotType pb.RobotType, key string) (string, error) {
	cfg := GetGuildRobotCardConfig()
	switch robotType {
	case pb.RobotType_ROBOT_TYPE_AMUSE:
		if cardId, ok := cfg.amuseMessageMap[key]; ok {
			return cardId, nil
		}
	case pb.RobotType_ROBOT_TYPE_ESPORT:
		if cardId, ok := cfg.esportMessageMap[key]; ok {
			return cardId, nil
		}
	case pb.RobotType_ROBOT_TYPE_YUYIN:
		if cardId, ok := cfg.yuyinMessageMap[key]; ok {
			return cardId, nil
		}
	default:
		return "", errors.New("robot type not found")
	}
	return "", errors.New("card id not found")
}

func ValidWelcomeCardKey(robotType pb.RobotType, key string) bool {
	cfg := GetGuildRobotCardConfig()
	switch robotType {
	case pb.RobotType_ROBOT_TYPE_AMUSE:
		if _, ok := cfg.amuseMessageMap[key]; ok {
			return true
		}
	case pb.RobotType_ROBOT_TYPE_ESPORT:
		if _, ok := cfg.esportMessageMap[key]; ok {
			return true
		}
	case pb.RobotType_ROBOT_TYPE_YUYIN:
		if _, ok := cfg.yuyinMessageMap[key]; ok {
			return true
		}
	default:
		return false
	}
	return false
}
