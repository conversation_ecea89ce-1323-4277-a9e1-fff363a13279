package config

type LarkConfig struct {
	RobotType   uint32     `json:"robot_type"` // 机器人类型，4:多人互动；7:语音直播；8:电竞
	AppId       string     `json:"app_id"`
	AppSecret   string     `json:"app_secret"`
	VerifyToken string     `json:"verify_token"` // token与key都是为了对飞书事件进行签名验证和解密，如果控制台没有设置加密，默认可以传递为空串
	EncryptKey  string     `json:"encrypt_key"`
	LogLevel    int        `json:"log_level"`
	EventList   []string   `json:"event_list"`  // event list for lark event handler
	AIGCConf    AIGCConfig `json:"aigc_config"` // AIGC configuration
}

type AIGCConfig struct {
	Url      string `json:"url"`
	Auth     string `json:"auth"`      // Authorization token for AIGC API
	FolderId uint32 `json:"folder_id"` // 知识库文件夹Id
	Folder   string `json:"folder"`
}

type AIGCKPConfig struct {
	Url        string `json:"url"`
	AuthToken  string `json:"auth_token"`  // Authorization token for Knowledge Platform API
	BaseFolder uint32 `json:"base_folder"` // Base folder ID for Knowledge Platform
}

const (
	EventReceiveV1    = "receive_v1"
	EventReadV1       = "read_v1"
	EventChatBotAdded = "chat_member_bot_added_v1"
	EventCardTrigger  = "card_action_trigger"
)
