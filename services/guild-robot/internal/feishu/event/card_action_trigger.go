package event

import (
	"context"
	"github.com/larksuite/oapi-sdk-go/v3/event/dispatcher/callback"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	config "golang.52tt.com/services/guild-robot/internal/config/ttconfig/guild_robot_card"
	"golang.52tt.com/services/guild-robot/internal/store"
	"strconv"
	"time"
)

// 卡片响应参数
const (
	CardButtonMessageUseful  = "message_useful"  // 卡片按钮消息有用（用于回答反馈）
	CardButtonMessageUseless = "message_useless" // 卡片按钮消息无用（用于回答反馈）
)

func (e *LarkEvent) doP2CardActionTrigger(ctx context.Context, event *callback.CardActionTriggerEvent) (*callback.CardActionTriggerResponse, error) {
	log.DebugWithCtx(ctx, "do P2Card action trigger, event: %+v", event)

	resp := &callback.CardActionTriggerResponse{}
	var err error
	switch event.Event.Action.Value["action"] {
	case CardButtonMessageUseful:
		// 处理常见问题按钮点击事件
		resp, err = e.HandleMessageUseful(ctx, event)
		if err != nil {
			log.ErrorWithCtx(ctx, "Failed to send common queries: %v", err)
			return nil, err
		}
	case CardButtonMessageUseless:
		// 处理评估进度按钮点击事件
		resp, err = e.HandleMessageUseless(ctx, event)
		if err != nil {
			log.ErrorWithCtx(ctx, "Failed to send assessment progress: %v", err)
			return nil, err
		}
	default:
		if config.ValidWelcomeCardKey(e.robotType, event.Event.Action.Value["action"].(string)) {
			// 处理欢迎卡片按钮点击事件
			resp, err = e.SendWelcomeCardEventInfo(ctx, event)
			if err != nil {
				log.ErrorWithCtx(ctx, "Failed to send welcome card info: %v", err)
				return nil, err
			}
		} else {
			log.WarnWithCtx(ctx, "Unknown action type: %s", event.Event.Action.Value["action"])
			return nil, nil // 未知操作类型，直接返回
		}
	}

	return resp, nil
}

func (e LarkEvent) HandleMessageUseful(ctx context.Context, event *callback.CardActionTriggerEvent) (*callback.CardActionTriggerResponse, error) {
	// 处理消息有用的逻辑
	log.DebugWithCtx(ctx, "Message useful action triggered operator: %+v", event.Event.Operator)
	// 查询操作人信息
	if event.Event.Operator == nil || event.Event.Operator.OpenID == "" {
		log.WarnWithCtx(ctx, "Operator information is missing in the event")
		return &callback.CardActionTriggerResponse{
			Toast: &callback.Toast{
				Type:    "failed",
				Content: "反馈失败，查询不到操作人信息",
			},
		}, nil
	}
	// 查询操作人名称
	userInfo, err := e.feishuApi.GetUserInfoByOpenId(ctx, event.Event.Operator.OpenID)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to get user info by OpenID: %v", err)
		return nil, err
	}

	// 查询消息
	messageId := event.Event.Context.OpenMessageID
	if messageId == "" {
		return &callback.CardActionTriggerResponse{
			Toast: &callback.Toast{
				Type:    "failed",
				Content: "反馈失败，查询不到消息信息",
			},
		}, nil
	}
	info, err := e.feishuApi.GetMessageInfo(ctx, messageId)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to get message info by ID: %v", err)
		return nil, err
	}
	log.DebugWithCtx(ctx, "Message CreateTime: %+v", *info[0].CreateTime)
	createTs, err := strconv.ParseInt(*info[0].CreateTime, 10, 64)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to parse CreateTime: %v", err)
	}
	message, err := e.store.GetMessageByID(ctx, *info[0].RootId, time.UnixMilli(createTs))
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to get message by ID: %v", err)
		return nil, err
	}
	if message == nil {
		log.WarnWithCtx(ctx, "Message not found for ID: %s", messageId)
		return &callback.CardActionTriggerResponse{
			Toast: &callback.Toast{
				Type:    "failed",
				Content: "反馈失败，查询不到消息信息",
			},
		}, nil
	}
	log.DebugWithCtx(ctx, "Message: %+v", message)

	//入库
	var userName string
	if userInfo == nil || userInfo.Name == nil {
		log.WarnWithCtx(ctx, "User name is nil for OpenID: %s", event.Event.Operator.OpenID)
		userName = event.Event.Operator.OpenID // 使用 OpenID 作为默认名称
	} else {
		userName = *userInfo.Name
	}
	if err = e.store.AddOrUpdateUserFeedback(ctx, &store.UserFeedback{
		Question:   message.Question,
		Answer:     message.Answer,
		MessageID:  messageId,
		IsUseful:   true,
		CreateTime: time.Now(),
		Creator:    userName,
	}, e.robotType); err != nil {
		log.ErrorWithCtx(ctx, "Failed to add or update user feedback: %v", err)
	}

	return &callback.CardActionTriggerResponse{
		Toast: &callback.Toast{
			Type:    "success",
			Content: "反馈成功",
		},
	}, nil
}

func (e LarkEvent) HandleMessageUseless(ctx context.Context, event *callback.CardActionTriggerEvent) (*callback.CardActionTriggerResponse, error) {
	// 处理消息无用的逻辑
	log.DebugWithCtx(ctx, "Message useful action triggered operator: %+v", event.Event.Operator)
	// 查询操作人信息
	if event.Event.Operator == nil || event.Event.Operator.OpenID == "" {
		log.WarnWithCtx(ctx, "Operator information is missing in the event")
		return &callback.CardActionTriggerResponse{
			Toast: &callback.Toast{
				Type:    "failed",
				Content: "反馈失败，查询不到操作人信息",
			},
		}, nil
	}
	// 查询操作人名称
	userInfo, err := e.feishuApi.GetUserInfoByOpenId(ctx, event.Event.Operator.OpenID)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to get user info by OpenID: %v", err)
		return nil, err
	}

	// 查询消息
	messageId := event.Event.Context.OpenMessageID
	if messageId == "" {
		return &callback.CardActionTriggerResponse{
			Toast: &callback.Toast{
				Type:    "failed",
				Content: "反馈失败，查询不到消息信息",
			},
		}, nil
	}
	// 查询上一条消息的messageId
	info, err := e.feishuApi.GetMessageInfo(ctx, messageId)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to get message info by ID: %v", err)
		return nil, err
	}
	log.DebugWithCtx(ctx, "Message CreateTime: %+v", *info[0].CreateTime)
	createTs, err := strconv.ParseInt(*info[0].CreateTime, 10, 64)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to parse CreateTime: %v", err)
	}
	message, err := e.store.GetMessageByID(ctx, *info[0].RootId, time.UnixMilli(createTs))
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to get message by ID: %v", err)
		return nil, err
	}
	if message == nil {
		log.WarnWithCtx(ctx, "Message not found for ID: %s", messageId)
		return &callback.CardActionTriggerResponse{
			Toast: &callback.Toast{
				Type:    "failed",
				Content: "反馈失败，查询不到消息信息",
			},
		}, nil
	}
	log.DebugWithCtx(ctx, "Message: %+v", message)

	//入库
	var userName string
	if userInfo == nil || userInfo.Name == nil {
		log.WarnWithCtx(ctx, "User name is nil for OpenID: %s", event.Event.Operator.OpenID)
		userName = event.Event.Operator.OpenID // 使用 OpenID 作为默认名称
	} else {
		userName = *userInfo.Name
	}
	if err = e.store.AddOrUpdateUserFeedback(ctx, &store.UserFeedback{
		Question:   message.Question,
		Answer:     message.Answer,
		MessageID:  messageId,
		IsUseful:   false,
		CreateTime: time.Now(),
		Creator:    userName,
	}, e.robotType); err != nil {
		log.ErrorWithCtx(ctx, "Failed to add or update user feedback: %v", err)
	}

	return &callback.CardActionTriggerResponse{
		Toast: &callback.Toast{
			Type:    "success",
			Content: "反馈成功",
		},
	}, nil
}

// SendWelcomeCardEventInfo 发送欢迎卡片事件信息
func (e *LarkEvent) SendWelcomeCardEventInfo(ctx context.Context, event *callback.CardActionTriggerEvent) (*callback.CardActionTriggerResponse, error) {
	// 发送常见问题的逻辑
	log.DebugWithCtx(ctx, "Sending welcome card info for action: %s", event.Event.Action.Value["action"])

	// 查询对应卡片模版ID
	cardId, err := config.GetCardIdByRobotTypeAndKey(e.robotType, event.Event.Action.Value["action"].(string))
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to get card ID for common queries: %v", err)
		return nil, err
	}

	// 根据卡片模版ID推送内容回复
	result := &callback.CardActionTriggerResponse{}
	if err = e.feishuApi.SendCardByTemplate(ctx, larkim.ReceiveIdTypeChatId, event.Event.Context.OpenChatID, cardId, map[string]interface{}{
		"user_ask": struct {
			Id string `json:"id"`
		}{
			Id: event.Event.Operator.OpenID,
		},
	}); err != nil {
		log.ErrorWithCtx(ctx, "Failed to send card by template for common queries: %v", err)
		return result, err
	}

	return result, nil
}
