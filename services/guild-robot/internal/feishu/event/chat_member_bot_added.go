package event

import (
	"context"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	config "golang.52tt.com/services/guild-robot/internal/config/ttconfig/guild_robot_card"
)

// doP2ChatMemberBotAddedV1 处理机器人进群事件
func (e *LarkEvent) doP2ChatMemberBotAddedV1(ctx context.Context, event *larkim.P2ChatMemberBotAddedV1) error {
	return e.SendWelcomeCard(ctx, *event.Event.ChatId)
}

// SendWelcomeCard 发送进群欢迎卡片
func (e *LarkEvent) SendWelcomeCard(ctx context.Context, chatId string) error {
	// 发送欢迎卡片逻辑
	// 查询对应卡片模版ID
	cardId, err := config.GetCardIdByRobotTypeAndKey(e.robotType, "welcome_card")
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to get card ID for common queries: %v", err)
		return err
	}

	// 根据卡片模版ID推送内容回复
	if err = e.feishuApi.SendCardByTemplate(ctx, larkim.ReceiveIdTypeChatId, chatId, cardId, map[string]interface{}{}); err != nil {
		log.ErrorWithCtx(ctx, "Failed to send card by template for common queries: %v", err)
		return err
	}
	return nil
}
