package event

import (
	"context"
	"encoding/json"
	"fmt"
	larkcore "github.com/larksuite/oapi-sdk-go/v3/core"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"golang.52tt.com/services/guild-robot/internal/feishu/api"
	eventcommon "golang.52tt.com/services/guild-robot/internal/feishu/common"
	"golang.52tt.com/services/guild-robot/internal/store"
	"strconv"
	"time"
)

// doP2MessageReceiveV1 处理消息回调
func (e *LarkEvent) doP2MessageReceiveV1(ctx context.Context, event *larkim.P2MessageReceiveV1) error {
	log.DebugWithCtx(ctx, "doP2MessageReceiveV1, eventData: %+v", event)

	var errFeishu error
	errFeishu = checkP2ImMessageReceiveV1(event)
	if nil != errFeishu {
		fmt.Println(larkcore.Prettify(event))
		return errFeishu
	}

	msg := event.Event.Message

	// 暂时不处理不是群发来的消息
	if *msg.ChatType != "group" {
		log.WarnWithCtx(ctx, "Received a message from non-group chat: %s, chat type: %s", *msg.MessageId, *msg.ChatType)
		return nil
	}

	if *(msg.MessageType) == larkim.MsgTypePost { // 富文本消息
		return e.doMessageReciverPost(ctx, event)

	}
	if *(msg.MessageType) == larkim.MsgTypeText { // 文本消息
		return e.doMessageReciverText(ctx, event)
	}
	return nil
}

func checkP2ImMessageReceiveV1(data *larkim.P2MessageReceiveV1) error {

	var err error

	if data == nil {
		err = fmt.Errorf("P2ImMessageReceiveV1 data is nil")
		log.Errorf(err.Error())
		return err
	}
	if data.Event == nil {
		err = fmt.Errorf("P2ImMessageReceiveV1 data.Event is nil")
		log.Errorf(err.Error())
		return err
	}

	err = checkLarkImEventMessage(data.Event.Message)
	if err != nil {
		return err
	}

	return nil
}

func checkLarkImEventMessage(eventMessage *larkim.EventMessage) error {

	var err error

	if nil == eventMessage {
		err = fmt.Errorf("P2ImMessageReceiveV1 EventMessage is nil")
		log.Errorf(err.Error())
		return err
	}

	if nil == eventMessage.MessageType {
		err = fmt.Errorf("P2ImMessageReceiveV1 EventMessage.MessageType is nil")
		log.Errorf(err.Error())
		return err
	}

	if nil == eventMessage.MessageId {
		err = fmt.Errorf("P2ImMessageReceiveV1 EventMessage.MessageId is nil")
		log.Errorf(err.Error())
		return err
	}

	return nil
}

// doMessageReciverPost 处理富文本消息
func (e *LarkEvent) doMessageReciverPost(ctx context.Context, data *larkim.P2MessageReceiveV1) error {

	msg := data.Event.Message
	var jsonData eventcommon.JSONData
	err := json.Unmarshal([]byte(*msg.Content), &jsonData)
	if err != nil {
		fmt.Printf("json.Unmarshal failed, err:%v", err)
		return err
	}
	log.DebugWithCtx(ctx, "jsonData: %s\n", larkcore.Prettify(jsonData))

	// 提取图片URL和提问信息
	var imgKey string
	var question string
	for _, c := range jsonData.Content {
		for _, c2 := range c {
			if c2.Tag == "img" {
				imgKey = c2.ImgKey
				fmt.Printf("imgKey: %s\n", imgKey)
			}
			if c2.Tag == "text" {
				question += " " + eventcommon.GetPureContentExtMentions(c2.Text)
				fmt.Printf("question: %s\n", question)
			}
		}
	}

	var imgStr string
	if imgKey != "" {
		imgStr, err = e.feishuApi.GetImageStrFromMessage(ctx, *msg.MessageId, imgKey)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetImageStrFromMessage failed, err:%v", err)
			return err
		}
	}
	if question == "" { // 如果问题为空，返回欢迎语
		if err := e.SendWelcomeCard(ctx, *msg.ChatId); err != nil {
			log.ErrorWithCtx(ctx, "SendWelcomeCard failed, err:%v", err)
			return nil
		}
		return nil
	}

	// 请求结果
	go func() {
		err = e.GetAnswerByStream(context.Background(), msg, question, imgStr)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAnswerByStream failed, err:%v", err)
		}
	}()

	return nil
}

// doMessageReciverText 处理文本消息
func (e *LarkEvent) doMessageReciverText(ctx context.Context, eventData *larkim.P2MessageReceiveV1) error {
	msg := eventData.Event.Message

	var respContent map[string]string
	err := json.Unmarshal([]byte(*msg.Content), &respContent)

	// 处理文本消息
	log.DebugWithCtx(ctx, "Received text message: %s", respContent["text"])
	var question string
	question = eventcommon.GetPureContentExtMentions(respContent["text"])
	fmt.Printf("question: %s\n", question)

	if question == "" { // 如果问题为空，返回欢迎语
		if err := e.SendWelcomeCard(ctx, *msg.ChatId); err != nil {
			log.ErrorWithCtx(ctx, "SendWelcomeCard failed, err:%v", err)
			return nil
		}
		return nil
	}

	// 请求结果
	go func() {
		err = e.GetAnswerByStream(ctx, msg, question, "")
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAnswerByStream failed, err:%v", err)
		}
	}()

	log.DebugWithCtx(ctx, "Received text message finished, question: %s", question)
	return nil
}

func (e *LarkEvent) GetAnswerByStream(ctx context.Context, msg *larkim.EventMessage, question string, imgStr string) error {

	// 创建回复卡片
	cardId, elementId, err := e.feishuApi.CreateCardForStreamResult(ctx, question, *msg.MessageId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateCardForStreamResult failed, err:%v", err)
		return err
	}
	sequence := 2 // 2表示流式消息的第二个元素，第一个操作是删除提示语
	deleteNotice := func() error {
		// 删除提示语
		if err := e.feishuApi.DeleteCardElement(ctx, cardId, api.StreamResultNoticeElementId, *msg.MessageId, 1); err != nil {
			log.ErrorWithCtx(ctx, "DeleteMessageById failed, err:%v", err)
			return err
		}
		return nil
	}

	// 调用AI服务获取答案
	answerChan, err := e.flowCli.SendStreamRequest(ctx, question, *msg.MessageId, imgStr)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendStreamRequest failed, err:%v", err)
		return err
	}

	// 删除提示语
	if err := deleteNotice(); err != nil {
		log.ErrorWithCtx(ctx, "deleteNotice failed, err:%v", err)
		return err
	}

	// 更新卡片内容
	timeout := time.After(10 * time.Minute) // 设置一个合理的超时时间
	var words int
	var answer string
	for {
		select {
		case message, ok := <-answerChan:
			words++
			if message != "" {
				answer = message
			}
			if words%50 != 0 && !ok { // 每50个字更新一次卡片内容，减小更新频率
				continue
			}
			err = e.feishuApi.UpdateCardForStreamResult(ctx, cardId, elementId, sequence, message, strconv.FormatInt(time.Now().UnixNano(), 10))
			if err != nil {
				log.ErrorWithCtx(ctx, "UpdateFeiShuMsg failed, err:%v", err)
			}
			sequence++
			if !ok {
				// 入库
				log.DebugWithCtx(ctx, "UpdateFeiShuMsg success， message: %s", message)
				createTs, _ := strconv.ParseInt(*msg.CreateTime, 10, 64)
				if err = e.store.AddOrUpdateMessage(ctx, &store.Message{
					Question:   question,
					Answer:     answer,
					MessageID:  *msg.MessageId,
					CreateTime: time.UnixMilli(createTs),
				}); err != nil {
					log.ErrorWithCtx(ctx, "AddOrUpdateMessage failed, err:%v", err)
				}
				time.Sleep(1 * time.Second) // 等待一段时间，确保卡片更新完成
				// 如果channel已关闭，推送是否满意按钮
				err = e.feishuApi.AddCardAiFeedbackInfo(ctx, cardId, strconv.FormatInt(time.Now().UnixNano(), 10), sequence)
				if err != nil {
					fmt.Printf("AddCardAiFeedbackInfo failed, err:%v", err)
					return err
				}
				sequence++
				// 如果channel已关闭，表示所有答案都已发送完毕，关闭流式更新
				_ = e.feishuApi.CloseStreamCardUpdate(ctx, cardId, strconv.FormatInt(time.Now().UnixNano(), 10), sequence)
				return nil
			}
		case <-timeout:
			log.InfoWithCtx(ctx, "Stream processing timed out")
			return nil
		}
	}
}

func getSecondWithResult(answer string) time.Duration {
	return time.Duration(len(answer)/50 + 2)
}
