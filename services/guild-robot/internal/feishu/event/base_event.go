package event

import (
	"context"
	"fmt"
	larkcore "github.com/larksuite/oapi-sdk-go/v3/core"
	larkdispatcher "github.com/larksuite/oapi-sdk-go/v3/event/dispatcher"
	larkws "github.com/larksuite/oapi-sdk-go/v3/ws"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/tt/quicksilver/guild-robot"
	"golang.52tt.com/services/guild-robot/internal/aigc/knowledge_platform"
	flow "golang.52tt.com/services/guild-robot/internal/aigc/workflow"
	"golang.52tt.com/services/guild-robot/internal/config"
	"golang.52tt.com/services/guild-robot/internal/feishu/api"
	"golang.52tt.com/services/guild-robot/internal/store"
)

type LarkEvent struct {
	robotType        pb.RobotType
	feishuWsClient   *larkws.Client
	feishuApi        *api.FeishuApi
	aigcConfig       config.AIGCConfig
	knowledgeBaseCli *knowledge_platform.KnowledgePlatformClient
	flowCli          *flow.WorkFlowClient
	store            *store.Store
}

func NewEvent(ctx context.Context, conf config.LarkConfig, knowledgeBaseCli_ *knowledge_platform.KnowledgePlatformClient, store_ *store.Store) (*LarkEvent, error) {
	larkEv := &LarkEvent{}

	// 注册飞书消息处理器
	//NewEventDispatcher的参数token与key都是为了对事件进行签名验证和解密，如果控制台没有设置加密，默认可以传递为空串
	eventHandler := larkdispatcher.NewEventDispatcher(conf.VerifyToken, conf.EncryptKey)
	if len(conf.EventList) < 1 {
		log.ErrorWithCtx(ctx, "EventList is empty, please check your configuration")
		return nil, fmt.Errorf("EventList is empty, please check your configuration")
	}
	// 注册事件处理函数
	for _, event := range conf.EventList {
		switch event {
		case config.EventReceiveV1:
			log.DebugWithCtx(ctx, "Event receive v1")
			eventHandler.OnP2MessageReceiveV1(larkEv.doP2MessageReceiveV1)
		case config.EventReadV1:
			log.DebugWithCtx(ctx, "Event read v1")
			eventHandler.OnP2MessageReadV1(larkEv.doP2MessageReadV1)
		case config.EventChatBotAdded:
			log.DebugWithCtx(ctx, "Event chat bot added")
			eventHandler.OnP2ChatMemberBotAddedV1(larkEv.doP2ChatMemberBotAddedV1)
		case config.EventCardTrigger:
			log.DebugWithCtx(ctx, "Event card action trigger")
			eventHandler.OnP2CardActionTrigger(larkEv.doP2CardActionTrigger)
		}
	}
	// 开启飞书WebSocket客户端
	larkEv.feishuWsClient = larkws.NewClient(conf.AppId, conf.AppSecret,
		larkws.WithEventHandler(eventHandler),
		larkws.WithLogLevel(larkcore.LogLevel(conf.LogLevel)),
	)

	// 设置飞书API
	larkEv.feishuApi = api.NewFeishuApi(conf.AppId, conf.AppSecret)

	// 设置AIGC配置
	larkEv.aigcConfig = conf.AIGCConf

	// 设置知识库客户端
	larkEv.knowledgeBaseCli = knowledgeBaseCli_

	// 设置机器人类型
	larkEv.robotType = pb.RobotType(conf.RobotType)

	// 设置存储客户端
	larkEv.store = store_

	// 设置工作流客户端
	flowCli_, err := flow.NewClient(ctx, conf.AIGCConf.Url, conf.AIGCConf.Auth)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to create WorkFlowClient: %v", err)
		return nil, fmt.Errorf("failed to create WorkFlowClient: %w", err)
	}
	larkEv.flowCli = flowCli_

	// 启动飞书WebSocket客户端
	go func() {
		if err := larkEv.feishuWsClient.Start(ctx); err != nil {
			log.Errorf("Failed to start Feishu WebSocket client: %v", err)
		}
	}()
	return larkEv, nil
}

func (e *LarkEvent) GetFeishuApi() *api.FeishuApi {
	return e.feishuApi
}
