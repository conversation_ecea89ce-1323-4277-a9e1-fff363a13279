package event

import (
	"context"
	larkcore "github.com/larksuite/oapi-sdk-go/v3/core"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
)

func (e *LarkEvent) doP2MessageReadV1(ctx context.Context, event *larkim.P2MessageReadV1) error {
	if event == nil {
		log.Errorf("event is nil")
		return nil
	}

	// 处理消息 event，这里简单打印消息的内容
	log.Infof("event: %s", larkcore.Prettify(event))
	// fmt.Println(larkcore.Prettify(event))
	// fmt.Println(event.RequestId())
	return nil
}
