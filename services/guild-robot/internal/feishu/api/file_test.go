package api

//func TestFeishuApi_getFileTokenFromUrl(t *testing.T) {
//	type fields struct {
//		larkClient *lark.Client
//	}
//	type args struct {
//		ctx     context.Context
//		fileUrl string
//	}
//	tests := []struct {
//		name   string
//		fields fields
//		args   args
//		want   string
//		want1  string
//	}{
//		{
//			name: "test getFileTokenFromUrl",
//			fields: fields{
//				larkClient: lark.NewClient("********************", "5NewZFAdjvBC9Mv9pWdgNfxGfkfpX1uZ"),
//			},
//			args: args{
//				ctx:     context.Background(),
//				fileUrl: "https://test-d9x8i21emotb.feishu.cn/wiki/Ex5swEJrDiV1Iekl1ZycI3QRnBI",
//			},
//			want:  "WKfbsgnWDh7OCdtTvS8cTcCOn5e",
//			want1: "sheet",
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			f := &FeishuApi{
//				larkClient: tt.fields.larkClient,
//			}
//			got, got1, err := f.getFileTokenFromUrl(tt.args.ctx, tt.args.fileUrl)
//			if err != nil {
//				t.Errorf("getFileTokenFromUrl error: %v\n", err)
//			}
//			assert.Equalf(t, tt.want, got, "getFileTokenFromUrl(%v, %v)", tt.args.ctx, tt.args.fileUrl)
//			assert.Equalf(t, tt.want1, got1, "getFileTokenFromUrl(%v, %v)", tt.args.ctx, tt.args.fileUrl)
//		})
//	}
//}
//
//func TestFeishuApi_DownloadFileByUrl(t *testing.T) {
//	type fields struct {
//		larkClient *lark.Client
//	}
//	type args struct {
//		ctx     context.Context
//		fileUrl string
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    string
//		wantErr assert.ErrorAssertionFunc
//	}{
//		{
//			name: "test DownloadFileByUrl",
//			fields: fields{
//				larkClient: lark.NewClient("********************", "5NewZFAdjvBC9Mv9pWdgNfxGfkfpX1uZ"),
//			},
//			args: args{
//				ctx:     context.Background(),
//				fileUrl: "https://test-d9x8i21emotb.feishu.cn/wiki/UAufw1CT0iu0KvkM0dCcCe3QnPh?fromScene=spaceOverview",
//			},
//			want:    "公会考核政策.docx",
//			wantErr: assert.NoError,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			f := &FeishuApi{
//				larkClient: tt.fields.larkClient,
//			}
//			got, err := f.DownloadFileByUrl(tt.args.ctx, tt.args.fileUrl)
//			if !tt.wantErr(t, err, fmt.Sprintf("DownloadFileByUrl(%v, %v)", tt.args.ctx, tt.args.fileUrl)) {
//				return
//			}
//			assert.Equalf(t, tt.want, got, "DownloadFileByUrl(%v, %v)", tt.args.ctx, tt.args.fileUrl)
//		})
//	}
//}
