package api

import (
	"fmt"
	"reflect"
	"testing"
)

type TestStructBase struct {
	BaseName  string `xkk:"base_name"`
	baseLevel string `xkk:"base_level"`
}
type TestStructChild struct {
	ChildName  string `xkk:"child_name"`
	childLevel string `xkk:"child_level"`
	BaseStruct TestStructBase
}

func printSubStructValue(deep int, inputType reflect.Type) {

	var tStruct reflect.Type
	if reflect.Pointer == inputType.Kind() {
		tStruct = inputType.Elem()
	} else if reflect.Struct == inputType.Kind() {
		tStruct = inputType
	} else {
		return
	}

	for i := 0; i < tStruct.NumField(); i++ {

		sField := tStruct.Field(i)

		xkkTagName := tStruct.Field(i).Tag.Get("xkk")

		if sField.PkgPath != "" {
			// 这是内部字段（小写命名开头）
			continue
		}

		// 输出deep数量的空格
		for j := 0; j < deep; j++ {
			fmt.Printf(" ")
		}
		fmt.Printf("idx %d Struct.Field [ name: %s pkgPath: %s type: %v typeKind: %v tag: %s xkkTagName: %s ] \n",
			i,
			sField.Name, sField.PkgPath,
			sField.Type, sField.Type.Kind(),
			sField.Tag, xkkTagName)

		if reflect.Struct == sField.Type.Kind() {
			printSubStructValue(deep+1, sField.Type)
		}
		if reflect.Pointer == sField.Type.Kind() {
			tFieldElem := sField.Type.Elem()
			if reflect.Struct == tFieldElem.Kind() {
				printSubStructValue(deep+1, tFieldElem)
			}
		}
	}
}

// 群消息
func TestValueOf(x *testing.T) {

	var recordChild = &TestStructChild{}

	v := reflect.ValueOf(recordChild)
	t := v.Type()

	fmt.Printf("t.Kind(): %v \n", t.Kind())

	var tStruct reflect.Type
	if reflect.Pointer == t.Kind() {
		tStruct = t.Elem()
	} else if reflect.Struct == t.Kind() {
		tStruct = t
	} else {
		return
	}

	fmt.Printf("t.Elem().Kind(): %v \n", tStruct.Kind())

	printSubStructValue(0, tStruct)

}
