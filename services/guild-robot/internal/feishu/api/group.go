package api

import (
	"context"

	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
)

type GroupMember struct {
	UserId string `json:"user_id"`
	Name   string `json:"name"`
}

// 获取群成员列表
func (f *FeishuApi) GetGroupMemberList(ctx context.Context, chatId string) ([]GroupMember, int, error) {

	// 无限循环遍历请求
	var continueFlag bool = true
	var reqNextToken string = ""
	var members []GroupMember = make([]GroupMember, 0)

	var total int = 0

	for continueFlag {

		// 创建请求对象
		build := larkim.NewGetChatMembersReqBuilder().
			ChatId(chatId).
			MemberIdType("user_id").
			PageSize(20)

		if reqNextToken != "" {
			build.PageToken(reqNextToken)
		}

		req := build.Build()
		resp, err := f.larkClient.Im.ChatMembers.Get(ctx, req)

		// 处理错误
		if err != nil {
			continueFlag = false
			log.ErrorWithCtx(ctx, "GetGroupMemberList. Im.ChatMembers.Get err:%v", err)
			return nil, 0, err
		}

		// 服务端错误处理
		if !resp.Success() {
			continueFlag = false
			log.ErrorWithCtx(ctx, "GetGroupMemberList. Im.ChatMembers.Get. fail. %d, %s", resp.Code, resp.Msg)
			return nil, 0, resp.CodeError
		}

		// 业务处理
		// fmt.Println(larkcore.Prettify(resp))
		total = *(resp.Data.MemberTotal)
		for _, member := range resp.Data.Items {
			members = append(members, GroupMember{UserId: *(member.MemberId), Name: *member.Name})
		}

		log.DebugWithCtx(ctx, "GetGroupMemberList. Im.ChatMembers.Get. currCnt %d total %d hasMore %v",
			len(resp.Data.Items), *(resp.Data.MemberTotal), *(resp.Data.HasMore))

		if *(resp.Data.HasMore) == true {
			reqNextToken = *resp.Data.PageToken
		} else {
			continueFlag = false
			break
		}
	}

	return members, total, nil
}
