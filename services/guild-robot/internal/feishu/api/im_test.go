package api

import (
	"testing"

	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	"github.com/stretchr/testify/assert"
)

/*


const (
	// chenshi的自建应用grffin
	feishuAppId     = "cli_a47fc0b6da59d00c"
	feishuAppSecret = ""
)

// 群消息
func TestSendTxtMsg(t *testing.T) {
	feishuClient := NewFeishuApi(feishuAppId, feishuAppSecret)

	recID := ReceiveId{
		IdType: larkim.ReceiveIdTypeChatId,
		Id:     "ou8094ybt",
	}
	atUser := &AtUser{UserId: "ou8094ybt", Name: "test"}

	err := feishuClient.SendTxtMsg(recID,
		"test message", atUser)

	if err != nil {
		t.Error(err)
	}
}

*/

func Test_larkTextContentTrans(t *testing.T) {
	type args struct {
		oriText string
		at      *AtUser
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "test-1",
			args: args{
				oriText: "hello world",
			},
			want: larkim.NewTextMsgBuilder().
				Text("hello world").
				Build(),
		},
		{
			name: "test-2",
			args: args{
				oriText: "1 /ping() - 戳一下人家啦\n2 /addUser() - 加个人啦\n3 /addGroup() - 加个群啦\n4 /createVersion(cv) - 创建一个AoneFlow版本",
			},
			want: larkim.NewTextMsgBuilder().
				TextLine("1 /ping() - 戳一下人家啦").
				TextLine("2 /addUser() - 加个人啦").
				TextLine("3 /addGroup() - 加个群啦").
				Text("4 /createVersion(cv) - 创建一个AoneFlow版本").
				Build(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := larkTextContentTrans(tt.args.oriText, tt.args.at)
			assert.Equal(t, tt.want, got)
			t.Log(got)
		})
	}
}

func TestLarkTextContentTrans2(t *testing.T) {
	tests := []struct {
		name          string
		oriText       string
		allAtUserList []AtUser
		want          string
	}{
		{
			name:          "NoAtUsers",
			oriText:       "Hello World\nThis is a test",
			allAtUserList: []AtUser{},
			want: larkim.NewTextMsgBuilder().
				TextLine("Hello World").
				Text("This is a test").
				Build(),
		},
		{
			name:          "WithAtUsers",
			oriText:       "Hello World [AT用户] user1\nThis is a test[AT用户]user2",
			allAtUserList: []AtUser{{UserId: "user1", Name: "User One"}, {UserId: "user2", Name: "User Two"}},
			want: larkim.NewTextMsgBuilder().
				Text("Hello World ").AtUser("user1", "User One").TextLine(" user1").
				Text("This is a test").AtUser("user2", "User Two").Text("user2").
				Build(),
		},
		// 其他测试用例...
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := larkTextContentTrans2(tt.oriText, tt.allAtUserList); got != tt.want {
				t.Errorf("larkTextContentTrans2() = %v, want %v", got, tt.want)
			}
		})
	}
}
