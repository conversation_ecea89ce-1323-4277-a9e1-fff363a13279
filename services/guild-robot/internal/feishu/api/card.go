package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	larkcore "github.com/larksuite/oapi-sdk-go/v3/core"
	"github.com/larksuite/oapi-sdk-go/v3/event/dispatcher/callback"
	larkcardkit "github.com/larksuite/oapi-sdk-go/v3/service/cardkit/v1"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"golang.52tt.com/services/guild-robot/internal/entity"
)

const (
	StreamResultMdElementId     = "markdown_1" // 目前写死容器ID，后续如果需要调整可以改成入参
	StreamResultNoticeElementId = "processing_text"
)

// CreateCardForStreamResult 创建流式消息卡片回复
// 参数：header 卡片标题
// replyId 回复的消息ID
// 需要注意的是，这个函数会创建一个流式消息卡片，如果需要推送消息需要调用 UpdateCardForStreamResult 函数来更新卡片内容, 且第一次必须将提示容器删除
func (f *FeishuApi) CreateCardForStreamResult(ctx context.Context, header string, replyId string) (cardId, elementId string, err error) {

	// 创建流式消息卡片
	cardId, elementId, err = f.buildMessageCard(ctx, header)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateAiResult. buildMessageCard err:%v", err)
		return "", "", err
	}

	// 创建卡片消息
	card := &callback.Card{
		Type: "card",
		Data: struct {
			CardId string `json:"card_id"`
		}{
			CardId: cardId,
		},
	}
	content, err := json.Marshal(card)
	if err != nil {
		fmt.Println(err)
		return cardId, elementId, err
	}
	log.DebugWithCtx(ctx, "createAiResult card:", string(content))

	// 创建请求对象
	req := larkim.NewReplyMessageReqBuilder().
		MessageId(replyId).
		Body(larkim.NewReplyMessageReqBodyBuilder().
			Content(string(content)).
			MsgType(larkim.MsgTypeInteractive).
			ReplyInThread(false).
			Uuid(replyId).
			Build()).
		Build()

	resp, err := f.larkClient.Im.V1.Message.Reply(context.Background(), req)

	if err != nil {
		log.ErrorWithCtx(ctx, "createAiResult err: %+v", err)
		return cardId, elementId, err
	}
	if !resp.Success() {
		fmt.Println(resp.Code, resp.Msg, resp.RequestId())
		return cardId, elementId, errors.New(fmt.Sprintf("CreateCardForStreamResult failed, code: %d, msg: %s", resp.Code, resp.Msg))
	}
	return cardId, elementId, nil
}

// buildMessageCard 创建流式消息卡片
// 参数：header 卡片标题
func (f *FeishuApi) buildMessageCard(ctx context.Context, header string) (cardId, elementId string, err error) {
	card := &entity.Card{
		Schema: entity.CardSchemaV2,
		Config: &entity.Config{
			StreamingMode: true,
		},
		Header: &entity.Header{
			Template: entity.ColorBlue,
			Title: &entity.Title{
				Tag:     entity.TagPlainText,
				Content: header,
			},
		},
		Body: &entity.Body{
			Elements: []*entity.BodyElement{
				{
					Tag:       entity.TagMarkdown,
					Content:   "正在思考中，预计等待20～40秒，请稍后...",
					ElementId: StreamResultNoticeElementId,
				},
				{
					Tag:       entity.TagMarkdown,
					Content:   "",
					ElementId: StreamResultMdElementId,
				},
			},
		},
	}

	// 创建请求对象
	req := larkcardkit.NewCreateCardReqBuilder().
		Body(larkcardkit.NewCreateCardReqBodyBuilder().
			Type(`card_json`).
			Data(card.String(ctx)).
			Build()).
		Build()

	// 发起请求
	resp, err := f.larkClient.Cardkit.V1.Card.Create(ctx, req)

	// 处理错误
	if err != nil {
		log.ErrorWithCtx(ctx, "createCard err:%v", err)
		return "", StreamResultMdElementId, err
	}

	// 服务端错误处理
	if !resp.Success() {
		log.ErrorWithCtx(ctx, "logId: %s, error response: %s", resp.RequestId(), larkcore.Prettify(resp.CodeError))
		return "", StreamResultMdElementId, errors.New(fmt.Sprintf("CreateCardForStreamResult failed, code: %d", resp.Code))
	}

	return *resp.Data.CardId, StreamResultMdElementId, nil
}

// UpdateCardForStreamResult 更新流式消息卡片内容
// 参数：cardId 卡片ID
// elementId 元素ID（可供更新的md组建ID）
// sequence 序号
// answer 更新的内容
// uuid 元素的唯一标识符主要用于防止重复创建元素
func (f *FeishuApi) UpdateCardForStreamResult(ctx context.Context, cardId string, elementId string, sequence int, answer string, uuid string) error {
	req := larkcardkit.NewContentCardElementReqBuilder().
		CardId(cardId).
		ElementId(elementId).
		Body(larkcardkit.NewContentCardElementReqBodyBuilder().
			Content(answer).
			Sequence(sequence).
			Uuid(uuid).
			Build()).
		Build()

	// 发起请求
	resp, err := f.larkClient.Cardkit.V1.CardElement.Content(ctx, req)

	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateCardForStreamResult. CardElement.Content err:%v", err)
		return err
	}
	if !resp.Success() {
		log.ErrorWithCtx(ctx, "UpdateCardForStreamResult. CardElement.Content. fail. %d, %s", resp.Code, resp.Msg)
		return fmt.Errorf(resp.Msg)
	}
	return nil
}

// CreateNewCardElement 创建新的卡片元素（追加卡片元素）
// 参数：cardId 卡片ID
// createType 创建类型（可选类型：insert_before：在目标组件前插入；insert_after：在目标组件后插入；append：在卡片或容器组件末尾添加）
// targetElementId 目标元素ID（如果createType为insert_before或insert_after时需要指定）
// uuid 元素的唯一标识符主要用于防止重复创建元素
// sequence 卡片处于流式更新模式时，操作卡片的序号。用于保证多次更新的时序性。该序号的值应为正整数，由开发者自定义。取值范围为 int32 范围内的值。
// elements 新元素的内容，JSON格式字符串
func (f *FeishuApi) CreateNewCardElement(ctx context.Context, cardId string, createType string, targetElementId string, uuid string, sequence int, elements string) error {
	// 创建请求对象
	req := larkcardkit.NewCreateCardElementReqBuilder().
		CardId(cardId).
		Body(larkcardkit.NewCreateCardElementReqBodyBuilder().
			Type(createType).
			TargetElementId(targetElementId).
			Uuid(uuid).
			Sequence(sequence).
			Elements(elements).
			Build()).
		Build()

	// 发起请求
	resp, err := f.larkClient.Cardkit.V1.CardElement.Create(context.Background(), req)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateNewCardElement. CardElement.Create err:%v", err)
		return err
	}

	if !resp.Success() {
		log.ErrorWithCtx(ctx, "CreateNewCardElement. CardElement.Create. fail. %d, %s", resp.Code, resp.Msg)
		return fmt.Errorf(resp.Msg)
	}
	return nil
}

// SendCardByTemplate 根据模板推送卡片
// 参数：receiveId 接收者ID
// receiveIdType 接收者ID类型（如：open_id/user_id/union_id/email/chat_id等）
// templateId 模板ID
// data 模板变量数据
func (f *FeishuApi) SendCardByTemplate(ctx context.Context, receiveIdType string, receiveId string, templateId string, data map[string]interface{}) error {
	card := &callback.Card{
		Type: "template",
		Data: &callback.TemplateCard{
			TemplateID:       templateId,
			TemplateVariable: data,
		},
	}

	content, err := json.Marshal(card)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendCardByTemplate json.Marshal err:%v", err)
		return err
	}

	req := larkim.NewCreateMessageReqBuilder().
		ReceiveIdType(receiveIdType).
		Body(larkim.NewCreateMessageReqBodyBuilder().
			MsgType(larkim.MsgTypeInteractive).
			Content(string(content)).
			ReceiveId(receiveId).
			Build()).
		Build()

	resp, err := f.larkClient.Im.Message.Create(context.Background(), req)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendCardByTemplate. Im.Message.Create err:%v", err)
		return err
	}

	if !resp.Success() {
		log.ErrorWithCtx(ctx, "SendCardByTemplate. Im.Message.Create. fail. %d, %s", resp.Code, resp.Msg)
		return errors.New(resp.Msg)
	}

	return nil
}

// SendReplyCardByTemplate 根据模板推送卡片
// 参数: messageId 回复的消息ID
// templateId 卡片模板ID
// data 模板变量数据
func (f *FeishuApi) SendReplyCardByTemplate(ctx context.Context, messageId string, templateId string, data map[string]interface{}) error {
	card := &callback.Card{
		Type: "template",
		Data: &callback.TemplateCard{
			TemplateID:       templateId,
			TemplateVariable: data,
		},
	}

	content, err := json.Marshal(card)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendReplyCardByTemplate json.Marshal err:%v", err)
		return err
	}

	req := larkim.NewReplyMessageReqBuilder().
		MessageId(messageId).
		Body(larkim.NewReplyMessageReqBodyBuilder().
			MsgType(larkim.MsgTypeInteractive).
			Content(string(content)).
			Build()).
		Build()
	resp, err := f.larkClient.Im.Message.Reply(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendReplyCardByTemplate. Im.Message.Reply err:%v", err)
		return err
	}

	if !resp.Success() {
		log.ErrorWithCtx(ctx, "SendReplyCardByTemplate. Im.Message.Reply. fail. %d, %s", resp.Code, resp.Msg)
		return errors.New(resp.Msg)
	}

	return nil
}

// DeleteCardElement 删除卡片元素
// 参数：cardId 卡片ID
// elementId 元素ID
// uuid 操作的唯一标识符主要用于防止重复操作
// sequence 卡片处于流式更新模式时，操作卡片的序号。用于保证多次更新的时序性。该序号的值应为正整数，由开发者自定义。取值范围为 int32 范围内的值。
func (f *FeishuApi) DeleteCardElement(ctx context.Context, cardId string, elementId string, uuid string, sequence int) error {
	// 创建请求对象
	req := larkcardkit.NewDeleteCardElementReqBuilder().
		CardId(cardId).
		ElementId(elementId).
		Body(larkcardkit.NewDeleteCardElementReqBodyBuilder().
			Uuid(uuid).
			Sequence(sequence).
			Build()).
		Build()

	// 发起请求
	resp, err := f.larkClient.Cardkit.V1.CardElement.Delete(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteCardElement. CardElement.Delete err:%v", err)
		return err
	}

	if !resp.Success() {
		log.ErrorWithCtx(ctx, "DeleteCardElement. CardElement.Delete. fail. %d, %s", resp.Code, resp.Msg)
		return errors.New(resp.Msg)
	}
	return nil
}

// ReSettingCardConfig 重新设置卡片配置
// 参数：cardId 卡片ID
// setting 配置内容
// uuid 操作的唯一标识符主要用于防止重复操作
// sequence 序号(流式更新模式时使用)
func (f *FeishuApi) ReSettingCardConfig(ctx context.Context, cardId string, setting string, uuid string, sequence int) error {

	// 创建请求对象
	req := larkcardkit.NewSettingsCardReqBuilder().
		CardId(cardId).
		Body(larkcardkit.NewSettingsCardReqBodyBuilder().
			Settings(setting).
			Uuid(uuid).
			Sequence(sequence).
			Build()).
		Build()

	// 发起请求
	resp, err := f.larkClient.Cardkit.V1.Card.Settings(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReSettingCardConfig. Card.Settings err:%v", err)
		return err
	}

	if !resp.Success() {
		log.ErrorWithCtx(ctx, "ReSettingCardConfig. Card.Settings. fail. %d, %s", resp.Code, resp.Msg)
		return fmt.Errorf(resp.Msg)
	}

	return nil
}

// CloseStreamCardUpdate 关闭流式卡片更新
// 参数：cardId 卡片ID
// uuid 操作的唯一标识符主要用于防止重复操作
// sequence 序号(流式更新模式时使用)
func (f *FeishuApi) CloseStreamCardUpdate(ctx context.Context, cardId string, uuid string, sequence int) error {
	setting := `{"config":{"streaming_mode": false}}`
	if err := f.ReSettingCardConfig(ctx, cardId, setting, uuid, sequence); err != nil {
		log.ErrorWithCtx(ctx, "CloseStreamCardUpdate. ReSettingCardConfig err:%v", err)
		return err
	}
	return nil
}

// AddCardAiFeedbackInfo 添加AI反馈信息到卡片
// 参数：cardId 卡片ID
// uuid 元素的唯一标识符主要用于防止重复创建元素
// sequence 序号(流式更新模式时使用)
func (f *FeishuApi) AddCardAiFeedbackInfo(ctx context.Context, cardId string, uuid string, sequence int) error {
	newElements := make([]entity.BodyElement, 0)
	newElements = append(newElements, entity.BodyElement{
		Tag:               "column_set",
		HorizontalSpacing: "12px",
		HorizontalAlign:   "right",
		Margin:            "0px 0px 4px 0px",
		Columns: []*entity.Column{
			{
				Tag:    "column",
				Width:  "auto",
				Weight: 1,
				Elements: []*entity.SubElement{
					{
						Tag:       "markdown",
						Content:   `<font color="grey-600">以上内容由 AI 生成，仅供参考。如有疑问请在工作时间内联系官方运营同学解答。</font>`,
						TextAlign: "left",
						TextSize:  "notation",
						Margin:    "4px 0px 0px 0px",
						Icon: &entity.Icon{
							Tag:   "standard_icon",
							Token: "robot_outlined",
							Color: "grey",
						},
					},
				},
				Padding:           "0px 0px 0px 0px",
				Direction:         "vertical",
				HorizontalSpacing: "8px",
				HorizontalAlign:   "left",
				VerticalSpacing:   "8px",
				VerticalAlign:     "top",
				Margin:            "0px 0px 0px 0px",
			},
			{
				Tag:   "column",
				Width: "auto",
				Elements: []*entity.SubElement{
					{
						Tag: "button",
						Text: &entity.SubElementText{
							Tag:     "plain_text",
							Content: "有用",
						},
						Type:  "text",
						Width: "100px",
						Size:  "medium",
						Icon: &entity.Icon{
							Tag:   "standard_icon",
							Token: "thumbsup_outlined",
						},
						HoverTips: &entity.HoverTips{
							Tag:     "plain_text",
							Content: "如果你觉得有帮助请点击此按钮反馈！",
						},
						Behaviors: []*entity.Behavior{
							{
								Type: "callback",
								Value: map[string]string{
									"action": "message_useful",
								},
							},
						},
						Margin:    "0px 0px 0px 0px",
						ElementId: "useful_button",
					},
				},
				Padding:           "0px 0px 0px 0px",
				Direction:         "vertical",
				HorizontalSpacing: "8px",
				HorizontalAlign:   "left",
				VerticalSpacing:   "8px",
				VerticalAlign:     "top",
				Margin:            "0px 0px 0px 0px",
			},
			{
				Tag:   "column",
				Width: "auto",
				Elements: []*entity.SubElement{
					{
						Tag: "button",
						Text: &entity.SubElementText{
							Tag:     "plain_text",
							Content: "无用",
						},
						Type:  "text",
						Width: "100px",
						Size:  "medium",
						Icon: &entity.Icon{
							Tag:   "standard_icon",
							Token: "thumbdown_outlined",
						},
						HoverTips: &entity.HoverTips{
							Tag:     "plain_text",
							Content: "如果你觉得无帮助请点击此按钮反馈！",
						},
						Behaviors: []*entity.Behavior{
							{
								Type: "callback",
								Value: map[string]string{
									"action": "message_useless",
								},
							},
						},
						Margin:    "0px 0px 0px 0px",
						ElementId: "useless_button",
					},
				},
				Padding:         "0px 0px 0px 0px",
				Direction:       "vertical",
				HorizontalAlign: "left",
				VerticalSpacing: "8px",
				VerticalAlign:   "top",
				Margin:          "0px 0px 0px 0px",
			},
		},
	})
	elements, err := json.Marshal(newElements)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddCardAiFeedbackInfo json.Marshal err:%v", err)
	}

	err = f.CreateNewCardElement(ctx, cardId, "append", "", uuid, sequence, string(elements))
	if err != nil {
		log.ErrorWithCtx(ctx, "AddCardAiFeedbackInfo CreateNewCardElement err:%v", err)
		return err
	}
	return nil
}

// UpdateCardElement 更新卡片元素
// 参数：cardId 卡片ID
// elementId 元素ID
// partialElement 部分元素内容（JSON格式字符串）
// uuid 元素的唯一标识符主要用于防止重复创建元素
// sequence 序号(流式更新模式时使用)
func (f *FeishuApi) UpdateCardElement(ctx context.Context, cardId string, elementId string, partialElement string, uuid string, sequence int) error {
	// 创建请求对象
	req := larkcardkit.NewPatchCardElementReqBuilder().
		CardId(cardId).
		ElementId(elementId).
		Body(larkcardkit.NewPatchCardElementReqBodyBuilder().
			PartialElement(partialElement).
			Uuid(uuid).
			Sequence(sequence).
			Build()).
		Build()

	// 发起请求
	resp, err := f.larkClient.Cardkit.V1.CardElement.Patch(context.Background(), req)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateCardElement. CardElement.Patch err:%v", err)
		return err
	}

	if !resp.Success() {
		log.ErrorWithCtx(ctx, "UpdateCardElement. CardElement.Patch. fail. %d, %s", resp.Code, resp.Msg)
		return fmt.Errorf(resp.Msg)
	}
	return nil
}

// UpdateCardElementDisabled 更新卡片元素的禁用状态
func (f *FeishuApi) UpdateCardElementDisabled(ctx context.Context, cardId string, elementId string, uuid string) error {
	partialElement := `{"disabled": true}`
	if err := f.UpdateCardElement(ctx, cardId, elementId, partialElement, uuid, 0); err != nil {
		log.ErrorWithCtx(ctx, "UpdateCardElementDisabled. UpdateCardElement err:%v", err)
		return err
	}
	return nil
}
