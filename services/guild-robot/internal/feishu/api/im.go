package api

import (
	"context"
	"fmt"
	"strings"

	larkprotocol "github.com/larksuite/botframework-go/SDK/protocol"
	larkcard "github.com/larksuite/oapi-sdk-go/v3/card"
	larkcore "github.com/larksuite/oapi-sdk-go/v3/core"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"

	"gitlab.ttyuyin.com/avengers/tyr/core/log"
)

const AtUserFlag_TT = "[AT用户]"

type AtUser struct {
	UserId string `json:"user_id"`
	Name   string `json:"name"`
}

type ReceiveId struct {

	// IdType 用于标识接收者的类型
	// 取值 "chat_id" 群唯一标识 larkim.ReceiveIdTypeChatId 类似 "ou_e8daec8c7bd6269852c84239ac85db3e"
	// 取值 "user_id" 一个用户在某个租户内的身份 larkim.ReceiveIdTypeUserId
	IdType string `json:"receive_id_type"`

	// 具体ID值
	Id string `json:"receive_id"`
}

// SendTxtMsg 发送消息
// 参数：ReceiveId 消息接收者ID
// text 消息内容
func (f *FeishuApi) SendTxtMsg(ctx context.Context, revID ReceiveId, text string, atList []AtUser) error {

	// Build the text message content
	content := larkTextContentTrans2(text, atList)

	resp, err := f.larkClient.Im.Message.Create(ctx, larkim.NewCreateMessageReqBuilder().
		ReceiveIdType(revID.IdType).
		Body(larkim.NewCreateMessageReqBodyBuilder().
			MsgType(larkim.MsgTypeText).
			ReceiveId(revID.Id).
			Content(content).
			Build()).
		Build())

	if err != nil {
		// fmt.Println(err)
		log.ErrorWithCtx(ctx, "Im.Message.Create. err:%v", err)
		return err
	}

	if !resp.Success() {
		log.ErrorWithCtx(ctx, "Im.Message.Create. fail. %d, %s", resp.Code, resp.Msg)
		return resp.CodeError
	}

	fmt.Println(resp.Data.MessageId)
	fmt.Println(larkcore.Prettify(resp))
	fmt.Println(resp.RequestId())
	return nil
}

func (f *FeishuApi) ReplyByFeiShuText(ctx context.Context, recvMsgId, text string, atList []AtUser) error {

	content := larkTextContentTrans2(text, atList)

	req := larkim.NewReplyMessageReqBuilder().
		MessageId(recvMsgId).
		Body(larkim.NewReplyMessageReqBodyBuilder().
			MsgType(larkim.MsgTypeText).
			Content(content).
			Build()).
		Build()
	resp, err := f.larkClient.Im.Message.Reply(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReplyByFeiShuText Im.Message.Reply failed, err:%v", err)
		return err
	}
	if !resp.Success() {
		log.ErrorWithCtx(ctx, "ReplyByFeiShuText Im.Message.Reply. fail. %d, %s", resp.Code, resp.Msg)
		return resp.CodeError
	}
	// log.InfoWithCtx(ctx, "ReplyByFeiShuText ok, resp:%+v, content:%s", resp, content)
	return nil
}

func (f *FeishuApi) ReplyByFeiShuCard(ctx context.Context, msg *larkprotocol.BotRecvMsg, card *larkcard.MessageCard) error {

	cardContent, err := card.String()
	if err != nil {
		log.ErrorWithCtx(ctx, "ReplyByFeiShuCard card.String failed, err:%v, msg:%+v, card:%+v", err, msg, card)
		return err
	}

	req := larkim.NewReplyMessageReqBuilder().
		MessageId(msg.OpenMessageID).
		Body(larkim.NewReplyMessageReqBodyBuilder().
			MsgType(larkim.MsgTypeInteractive).
			Content(cardContent).
			Build()).
		Build()
	resp, err := f.larkClient.Im.Message.Reply(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReplyByFeiShuCard Im.Message.Reply failed, err:%v, content:%s", err, cardContent)
		return err
	}
	if !resp.Success() {
		log.ErrorWithCtx(ctx, "ReplyByFeiShuCard Im.Message.Reply. fail. %d, %s", resp.Code, resp.Msg)
		return resp.CodeError
	}
	// log.DebugWithCtx(ctx, "ReplyByFeiShuCard ok")
	return nil
}

// 获取会话信息
func (f *FeishuApi) GetChatInfo(chatId string) (*larkim.GetChatRespData, error) {
	req := larkim.NewGetChatReqBuilder().
		ChatId(chatId).
		Build()

	resp, err := f.larkClient.Im.Chat.Get(context.Background(), req)
	if err != nil {
		return nil, err
	}
	if !resp.Success() {
		log.Errorf("GetChatInfo Im.Chat.Get. fail. %d, %s", resp.Code, resp.Msg)

		return nil, resp.CodeError
	}

	return resp.Data, nil
}

// 更新会话名称
func (f *FeishuApi) UpdateChatName(chatId string, chatName string) error {
	req := larkim.NewUpdateChatReqBuilder().
		ChatId(chatId).
		Body(larkim.NewUpdateChatReqBodyBuilder().
			Name(chatName).
			Build()).
		Build()

	resp, err := f.larkClient.Im.Chat.Update(context.Background(), req)
	if err != nil {
		log.Errorf("UpdateChatName Im.Chat.Update. err: %v", err)
		return err
	}
	if !resp.Success() {
		log.Errorf("UpdateChatName Im.Chat.Update. fail. %d, %s", resp.Code, resp.Msg)
		return resp.CodeError
	}

	return nil
}

// 将原始的文本消息 转换为符合飞书消息格式的json格式
func larkTextContentTrans(oriText string, at *AtUser) string {
	contentBuilder := larkim.NewTextMsgBuilder()
	if at != nil {
		contentBuilder = contentBuilder.AtUser(at.UserId, at.Name)
	}

	// 遍历 oriText 原始文本，将\n标识的换行 使用contentBuilder.TextLine() 转换
	lines := strings.Split(oriText, "\n")

	for idx, line := range lines {
		if idx == len(lines)-1 {
			contentBuilder = contentBuilder.Text(line)
		} else {
			contentBuilder = contentBuilder.TextLine(line)
		}
	}

	return contentBuilder.Build()
}

func larkTextContentTrans2(oriText string, allAtUserList []AtUser) string {
	contentBuilder := larkim.NewTextMsgBuilder()

	// 遍历 oriText 原始文本，将\n标识的换行 使用contentBuilder.TextLine() 转换
	// 如果有 [AT用户] : AtUserFlag_TT 这样的标识，则需要使用contentBuilder.AtUser()

	atUserAllIdx := 0

	lineMsgList := strings.Split(oriText, "\n")
	for lineIdx, lineMsg := range lineMsgList {

		atSplitMsgList := strings.Split(lineMsg, AtUserFlag_TT)
		for atSplitIdx, atSplitMsg := range atSplitMsgList {
			if atSplitIdx < len(atSplitMsgList)-1 {
				// 本行消息 被 [AT用户] 分割 且不是最后一个
				contentBuilder = contentBuilder.Text(atSplitMsg)
				if atUserAllIdx < len(allAtUserList) {
					contentBuilder = contentBuilder.AtUser(allAtUserList[atUserAllIdx].UserId, allAtUserList[atUserAllIdx].Name)
				}
				atUserAllIdx++
			} else {
				// 本行消息 被 [AT用户] 分割已经是最后一个了
				if lineIdx < len(lineMsgList)-1 {
					// 这不是最后一行
					contentBuilder = contentBuilder.TextLine(atSplitMsg)
				} else {
					contentBuilder = contentBuilder.Text(atSplitMsg)
				}
			}

		}
	}

	for ; atUserAllIdx < len(allAtUserList); atUserAllIdx++ {
		contentBuilder = contentBuilder.AtUser(allAtUserList[atUserAllIdx].UserId, allAtUserList[atUserAllIdx].Name)
	}

	return contentBuilder.Build()
}

// ReplyMessageWithReactions 回复消息并添加表情
// replyId: 回复的消息ID
// emojiType: 表情类型，详见：https://open.feishu.cn/document/server-docs/im-v1/message-reaction/emojis-introduce
func (f *FeishuApi) ReplyMessageWithReactions(ctx context.Context, replyId string, emojiType string) error {
	// 创建请求对象
	req := larkim.NewCreateMessageReactionReqBuilder().
		MessageId(replyId).
		Body(larkim.NewCreateMessageReactionReqBodyBuilder().
			ReactionType(larkim.NewEmojiBuilder().
				EmojiType(emojiType).
				Build()).
			Build()).
		Build()

	// 发起请求
	resp, err := f.larkClient.Im.V1.MessageReaction.Create(ctx, req)

	// 处理错误
	if err != nil {
		log.ErrorWithCtx(ctx, "ReplyMessageWithReactions Im.V1.MessageReaction.Create. err", err)
		return err
	}

	// 服务端错误处理
	if !resp.Success() {
		log.ErrorWithCtx(ctx, "logId: %s, error response: %s", resp.RequestId(), larkcore.Prettify(resp.CodeError))
		return fmt.Errorf("ReplyMessageWithReactions failed, code: %d, msg: %s", resp.Code, resp.Msg)
	}

	return nil
}

// GetMessageInfo 获取消息信息
// messageId: 消息ID
func (f *FeishuApi) GetMessageInfo(ctx context.Context, messageId string) ([]*larkim.Message, error) {
	req := larkim.NewGetMessageReqBuilder().
		MessageId(messageId).
		Build()

	resp, err := f.larkClient.Im.Message.Get(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMessageInfo Im.Message.Get failed, err:%v", err)
		return nil, err
	}
	if !resp.Success() {
		log.ErrorWithCtx(ctx, "GetMessageInfo Im.Message.Get. fail. %d, %s", resp.Code, resp.Msg)
		return nil, resp.CodeError
	}
	if resp.Data == nil || resp.Data.Items == nil {
		log.ErrorWithCtx(ctx, "GetMessageInfo Im.Message.Get. no data found for messageId: %s", messageId)
		return nil, fmt.Errorf("no data found for messageId")
	}

	return resp.Data.Items, nil
}
