package api

import (
	"context"
	larkcontact "github.com/larksuite/oapi-sdk-go/v3/service/contact/v3"
	"golang.52tt.com/pkg/log"
)

// GetUserInfoByOpenId 根据 OpenId 获取用户信息
// 参数：openId - 用户的 OpenId
func (f *FeishuApi) GetUserInfoByOpenId(ctx context.Context, openId string) (*larkcontact.User, error) {
	// 创建请求对象
	req := larkcontact.NewGetUserReqBuilder().
		UserId(openId).
		UserIdType(`open_id`).
		DepartmentIdType(`department_id`).
		Build()

	// 发起请求
	resp, err := f.larkClient.Contact.V3.User.Get(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to get user info by openId: %s, error: %v", openId, err)
		return nil, err
	}

	if !resp.Success() {
		log.ErrorWithCtx(ctx, "Failed to get user info by openId: %s, response code: %d, msg: %s", openId, resp.Code, resp.Msg)
		return nil, err
	}
	if resp.Data == nil || resp.Data.User == nil {
		log.ErrorWithCtx(ctx, "User info is nil for openId: %s", openId)
		return nil, nil
	}
	log.DebugWithCtx(ctx, "Get user info by openId: %s, user: %+v", openId, resp.Data.User)
	return resp.Data.User, nil
}
