package api

import (
	"bytes"
	"context"
	"encoding/base64"
	"fmt"
	larkcore "github.com/larksuite/oapi-sdk-go/v3/core"
	larkdrive "github.com/larksuite/oapi-sdk-go/v3/service/drive/v1"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	larkwiki "github.com/larksuite/oapi-sdk-go/v3/service/wiki/v2"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"io"
	"strings"
	"time"
)

const (
	// 导出形式，word可以导出为docx、pdf格式
	ExportFileWord  = "docx"
	ExportFilePdf   = "pdf"
	ExportFileExcel = "xlsx"

	// 文件类型
	TypeOldWord = "doc"     // 旧版word文档
	TypeWord    = "docx"    // word文档
	TypeExcel   = "sheet"   // excel表格
	TypeBiTable = "bitable" // 飞书多维表格
	TypeWiki    = "wiki"    // 飞书知识文档
)

// DownloadFileByUrl 通过飞书文件URL下载文件
// 注意：此方法仅支持导出飞书文档（docx）和知识库文档（wiki）
func (f *FeishuApi) DownloadFileByUrl(ctx context.Context, fileUrl string) (*larkdrive.DownloadExportTaskResp, error) {
	// 1、创建文件导出任务
	ticket, fileToken, err := f.CreateFileExportTask(ctx, fileUrl, "")
	if err != nil {
		log.ErrorWithCtx(ctx, "DownloadPDFFileByUrl CreateFileExportTask err:%v", err)
		return nil, err
	}

	// 2、导出文件
	fileInfo, err := f.ExportTaskFile(ctx, fileToken, ticket)
	if err != nil {
		log.ErrorWithCtx(ctx, "DownloadPDFFileByUrl ExportTaskFile err:%v", err)
		return fileInfo, err
	}

	return fileInfo, nil
}

// CreateFileExportTask 创建文件导出任务
// fileExtension: 导出文件的扩展名
// fileUrl: 飞书文档的URL
// fileType: 文件类型，docx、sheet、bitable
// subId: 子任务ID，导出飞书电子表格或多维表格为 CSV 文件时，需传入电子表格工作表的 ID 或多维表格数据表的 ID
// 文档：https://open.feishu.cn/document/server-docs/docs/drive-v1/export_task/create
func (f *FeishuApi) CreateFileExportTask(ctx context.Context, fileUrl string, subId string) (string, string, error) {

	// 获取文件的Token
	token, fileType, err := f.getFileTokenFromUrl(ctx, fileUrl)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateFileExportTask getFileTokenFromUrl err:%v", err)
		return "", token, err
	}

	// 获取导出类型
	fileExtension := getFileExtension(fileType)
	if fileExtension == "" {
		log.ErrorWithCtx(ctx, "CreateFileExportTask getFileExtension fileType: %s is invalid", fileType)
		return "", token, fmt.Errorf("file_type_invalid, file type is invalid: %s", fileType)
	}
	log.DebugWithCtx(ctx, "CreateFileExportTask token: %s, fileType: %s, fileExtension: %s", token, fileType, fileExtension)

	// 创建请求对象
	req := larkdrive.NewCreateExportTaskReqBuilder().
		ExportTask(larkdrive.NewExportTaskBuilder().
			FileExtension(fileExtension).
			Token(token).
			Type(fileType).
			SubId(subId).
			Build()).
		Build()

	// 发起请求
	resp, err := f.larkClient.Drive.V1.ExportTask.Create(ctx, req)

	// 处理错误
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateFileExportTask. Drive.ExportTask.Create err:%v", err)
		return "", token, err
	}

	// 服务端错误处理
	if !resp.Success() {
		log.ErrorWithCtx(ctx, "logId: %s, error response: \n%s", resp.RequestId(), larkcore.Prettify(resp.CodeError))
		return "", token, resp.CodeError
	}
	if resp.Data == nil || resp.Data.Ticket == nil {
		log.ErrorWithCtx(ctx, "CreateFileExportTask. Drive.ExportTask.Create response data is invalid")
		return "", token, fmt.Errorf("invalid_response_data, response data is invalid")
	}
	return *resp.Data.Ticket, token, nil
}

func getFileExtension(fileType string) string {
	switch fileType {
	case TypeExcel:
		return ExportFileExcel
	case TypeWord:
		return ExportFileWord
	case TypeOldWord:
		return ExportFilePdf
	}
	return ""
}

// getFileTokenFromUrl 从文件URL中获取文件的Token
func (f *FeishuApi) getFileTokenFromUrl(ctx context.Context, fileUrl string) (string, string, error) {
	// 直接获取url尾部的token
	// 详情参考：https://open.feishu.cn/document/server-docs/docs/faq#08bb5df6
	if fileUrl == "" {
		log.ErrorWithCtx(ctx, "getFileTokenFromUrl fileUrl is empty")
		return "", "", fmt.Errorf("file_url_empty, file url is empty")
	}
	infos := strings.Split(fileUrl, "/")
	if len(infos) < 4 {
		log.ErrorWithCtx(ctx, "getFileTokenFromUrl fileUrl is invalid, %s", fileUrl)
		return "", "", fmt.Errorf("file_url_invalid, file url is invalid")
	}
	// 获取类型
	fileType := infos[3]
	if fileType != TypeWord && fileType != TypeExcel && fileType != TypeBiTable && fileType != TypeWiki {
		log.ErrorWithCtx(ctx, "getFileTokenFromUrl fileType is invalid, fileType: %s, fileUrl: %s", fileType, fileUrl)
		return "", "", fmt.Errorf("file_type_invalid, file type is invalid")
	}
	// 永远处于第四个元素
	token := infos[4]
	if token == "" {
		log.ErrorWithCtx(ctx, "getFileTokenFromUrl token is empty, fileUrl: %s", fileUrl)
		return "", "", fmt.Errorf("file_token_empty, file token is empty")
	}
	// 去除token可能存在的尾部参数
	if strings.Contains(token, "?") {
		token = strings.Split(token, "?")[0]
	}

	log.DebugWithCtx(ctx, "getFileTokenFromUrl token: %s， fileType: %s", token, fileType)
	if fileType == TypeWiki {
		// 如果是Wiki文档，获取Wiki的文件信息
		return f.GetWikiFileInfo(ctx, token)
	}

	return token, fileType, nil
}

// GetWikiFileInfo 获取Wiki文档的文件信息
// 详见：https://open.feishu.cn/document/server-docs/docs/wiki-v2/space-node/get_node
func (f *FeishuApi) GetWikiFileInfo(ctx context.Context, token string) (string, string, error) {
	// 创建请求对象
	req := larkwiki.NewGetNodeSpaceReqBuilder().
		Token(token).
		ObjType(TypeWiki).
		Build()

	// 发起请求
	resp, err := f.larkClient.Wiki.V2.Space.GetNode(context.Background(), req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetWikiFileInfo. Wiki.Space.GetNode err:%v", err)
		return "", "", err
	}

	// 服务端错误处理
	if !resp.Success() {
		log.ErrorWithCtx(ctx, "logId: %s, error response: \n%s", resp.RequestId(), larkcore.Prettify(resp.CodeError))
		return "", "", resp.CodeError
	}

	if resp.Data == nil || resp.Data.Node == nil || resp.Data.Node.ObjToken == nil || resp.Data.Node.ObjType == nil {
		log.ErrorWithCtx(ctx, "GetWikiFileInfo. Wiki.Space.GetNode response data is invalid")
		return "", "", fmt.Errorf("invalid_response_data, response data is invalid")
	}

	// 返回文件名称和文件类型
	return *resp.Data.Node.ObjToken, *resp.Data.Node.ObjType, nil
}

// ExportTaskFile 导出文件
// fileToken: 文件的Token
// ticket: 导出任务的Ticket
// 返回值：io.Reader，表示导出的文件内容
func (f *FeishuApi) ExportTaskFile(ctx context.Context, fileToken string, ticket string) (*larkdrive.DownloadExportTaskResp, error) {
	// 检查导出任务是否完成
	downLoadToken, err := f.isFinishExport(ctx, fileToken, ticket)
	if err != nil {
		log.ErrorWithCtx(ctx, "ExportTaskFile isFinishExport err:%v", err)
		return nil, err
	}

	// 如果任务完成，返回下载Token
	if downLoadToken == "" {
		log.ErrorWithCtx(ctx, "ExportTaskFile isFinishExport downLoadToken is empty")
		return nil, fmt.Errorf("down_load_token_empty, download token is empty")
	}
	// 创建请求对象
	req := larkdrive.NewDownloadExportTaskReqBuilder().
		FileToken(downLoadToken).
		Build()

	// 发起请求
	resp, err := f.larkClient.Drive.V1.ExportTask.Download(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "ExportTaskFile. Drive.ExportTask.Download err:%v", err)
		return nil, err
	}

	// 服务端错误处理
	if !resp.Success() {
		log.ErrorWithCtx(ctx, "logId: %s, error response: \n%s", resp.RequestId(), larkcore.Prettify(resp.CodeError))
		return nil, resp.CodeError
	}
	if resp.File == nil {
		log.ErrorWithCtx(ctx, "ExportTaskFile. Drive.ExportTask.Download response file is nil")
		return nil, fmt.Errorf("invalid_response_data, response file is nil")
	}

	return resp, nil
}

// isFinishExport 检查导出任务是否完成
// 详见：https://open.feishu.cn/document/server-docs/docs/drive-v1/export_task/get
func (f *FeishuApi) isFinishExport(ctx context.Context, fileToken string, ticket string) (string, error) {
	var downLoadToken string

	for { // 无限循环，直到任务完成或发生错误
		// 创建请求对象
		req := larkdrive.NewGetExportTaskReqBuilder().
			Ticket(ticket).
			Token(fileToken).
			Build()

		// 发起请求
		resp, err := f.larkClient.Drive.V1.ExportTask.Get(ctx, req)
		if err != nil {
			log.ErrorWithCtx(ctx, "isFinishExport. Drive.ExportTask.Get err:%v", err)
			return "", err
		}

		// 服务端错误处理
		if !resp.Success() {
			log.ErrorWithCtx(ctx, "logId: %s, error response: \n%s", resp.RequestId(), larkcore.Prettify(resp.CodeError))
			return "", resp.CodeError
		}
		if resp.Data == nil || resp.Data.Result == nil {
			log.ErrorWithCtx(ctx, "isFinishExport. Drive.ExportTask.Get response data is invalid")
			return "", fmt.Errorf("invalid_response_data, response data is invalid")
		}
		if *resp.Data.Result.JobStatus == 1 || *resp.Data.Result.JobStatus == 2 {
			// 任务未完成，继续等待
			log.DebugWithCtx(ctx, "isFinishExport. Drive.ExportTask.Get jobStatus: %d, waiting...", *resp.Data.Result.JobStatus)
			time.Sleep(500 * time.Millisecond) // 等待200毫秒后再次查询任务状态
			continue
		} else if *resp.Data.Result.JobStatus == 0 {
			if resp.Data.Result.FileToken == nil {
				log.ErrorWithCtx(ctx, "isFinishExport. Drive.ExportTask.Get jobStatus: 0, but FileToken is nil")
				return "", fmt.Errorf("file_token_nil, file token is nil")
			}
			// 任务完成，返回文件的下载Token
			log.DebugWithCtx(ctx, "isFinishExport. Drive.ExportTask.Get jobStatus: 0, fileToken: %s", *resp.Data.Result.FileToken)
			downLoadToken = *resp.Data.Result.FileToken
			break
		} else {
			// 任务状态异常
			log.ErrorWithCtx(ctx, "isFinishExport. Drive.ExportTask.Get jobStatus: %d, unexpected status", *resp.Data.Result.JobStatus)
			return "", fmt.Errorf("unexpected_job_status, job status is unexpected")
		}
	}

	return downLoadToken, nil
}

// DownloadMessageResource 下载消息资源
// 参数：messageId 消息ID
// key 资源的唯一标识
// resourceType 资源类型
func (f *FeishuApi) DownloadMessageResource(ctx context.Context, messageId string, key string, resourceType string) (*larkim.GetMessageResourceResp, error) {
	// 创建请求对象
	req := larkim.NewGetMessageResourceReqBuilder().
		MessageId(messageId).
		FileKey(key).
		Type(resourceType).
		Build()

	// 发起请求
	resp, err := f.larkClient.Im.V1.MessageResource.Get(ctx, req)

	// 处理错误
	if err != nil {
		log.ErrorWithCtx(ctx, "DownloadMessageResource. Im.MessageResource.Get err:%v", err)
		return nil, err
	}

	// 服务端错误处理
	if !resp.Success() {
		log.ErrorWithCtx(ctx, "logId: %s, error response: %s", resp.RequestId(), larkcore.Prettify(resp.CodeError))
		return nil, fmt.Errorf("DownloadMessageResource failed, code: %d, msg: %s", resp.Code, resp.Msg)
	}

	// 业务处理
	return resp, nil
}

// GetImageStrFromMessage 从消息中获取图片资源的URL（）
// 参数：messageId 消息ID
// key 资源的唯一标识
func (f *FeishuApi) GetImageStrFromMessage(ctx context.Context, messageId string, key string) (string, error) {
	// 下载图片
	img, err := f.DownloadMessageResource(ctx, messageId, key, "image")
	if err != nil {
		log.ErrorWithCtx(ctx, "GetImageStrFromMessage DownloadMessageResource err:%v", err)
		return "", err
	}

	// 将图片转换为Base64字符串
	var result string
	if img != nil {
		// 创建请求体缓冲区
		var body bytes.Buffer
		// 复制文件内容
		_, err := io.Copy(&body, img.File)
		if err != nil {
			return "", err
		}

		// 进行Base64编码
		base64String := base64.StdEncoding.EncodeToString(body.Bytes())

		// 添加MIME类型前缀（web显示需要）
		mimeType := "image/png" // 根据实际类型调整
		fullBase64 := fmt.Sprintf("data:%s;base64,%s", mimeType, base64String)
		result = fullBase64
	}

	return result, nil
}
