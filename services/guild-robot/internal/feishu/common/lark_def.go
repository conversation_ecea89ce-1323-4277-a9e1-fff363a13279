package common

import (
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
)

const (
	LarkMsgEventInfoContextKey = "lark-msg-event-info"
)

type LarkUser struct {
	UserId  string `json:"user_id,omitempty"` // 用户ID
	OpenId  string `json:"open_id,omitempty"` // 用户open_id
	UnionId string `json:"union_id,omitempty"`

	TenantKey string `json:"tenant_key,omitempty"` // tenant key，为租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用里面的唯一标识
}

func (l *LarkUser) Describe() string {
	return fmt.Sprintf("UserId %s OpenId %s UnionId %s", l.UserId, l.OpenId, l.UnionId)
}

type LarkMentionUser struct {
	LarkUser
	Name       string `json:"name,omitempty"`        // 可选字段  用户姓名
	MentionKey string `json:"mention_key,omitempty"` // 可选字段  仅当被@的用户或机器人的时候有值。例如，第3个被@到的成员，值为“@_user_3”
}

func (l *LarkMentionUser) Describe() string {

	return fmt.Sprintf("@名字 %s @MentionKey %s @UserId %s @OpenId %s @UnionId %s",
		l.Name, l.MentionKey,
		l.UserId, l.OpenId, l.UnionId)
}

//func NewLarkUserFromSender(sender *larkim.EventSender) *LarkUser {
//	if nil == sender || nil == sender.SenderId {
//		return nil
//	}
//
//	larkUser := &LarkUser{}
//	if nil != sender.SenderId.UserId {
//		larkUser.UserId = *(sender.SenderId.UserId)
//	}
//	if nil != sender.SenderId.OpenId {
//		larkUser.OpenId = *(sender.SenderId.OpenId)
//	}
//	if nil != sender.SenderId.UnionId {
//		larkUser.UnionId = *(sender.SenderId.UnionId)
//	}
//
//	if nil != sender.TenantKey {
//		larkUser.TenantKey = *(sender.TenantKey)
//	}
//	return larkUser
//}

//func NewLarkUserFromMentionUser(mentionUser *larkim.MentionEvent) *LarkMentionUser {
//	if nil == mentionUser || nil == mentionUser.Id {
//		return nil
//	}
//
//	larkUser := &LarkMentionUser{}
//	if nil != mentionUser.Id.UserId {
//		larkUser.UserId = *(mentionUser.Id.UserId)
//	}
//	if nil != mentionUser.Id.OpenId {
//		larkUser.OpenId = *(mentionUser.Id.OpenId)
//	}
//	if nil != mentionUser.Id.UnionId {
//		larkUser.UnionId = *(mentionUser.Id.UnionId)
//	}
//
//	if nil != mentionUser.TenantKey {
//		larkUser.TenantKey = *(mentionUser.TenantKey)
//	}
//	if nil != mentionUser.Name {
//		larkUser.Name = *(mentionUser.Name)
//	}
//
//	if nil != mentionUser.Key {
//		larkUser.MentionKey = *(mentionUser.Key)
//	}
//	return larkUser
//}

type LarkMsgEventContext struct {
	Sender   LarkUser `json:"sender"`    // 发送者信息
	ChatId   string   `json:"chat_id"`   // 会话的id
	ChatType string   `json:"chat_type"` // 会话类型(可选)  `p2p`：单聊;- `group`： 群组;- `topic_group`：话题群

	// [消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
	MessageId  string `json:"message_id,omitempty"`  // 消息的open_message_id
	RootId     string `json:"root_id,omitempty"`     // 根消息id，用于回复消息场景
	ParentId   string `json:"parent_id,omitempty"`   // 父消息的id，用于回复消息场景
	CreateTime string `json:"create_time,omitempty"` // 消息发送时间（毫秒）

	Mentions []LarkMentionUser `json:"mentions,omitempty"` // 消息提及的用户列表
}

//func NewLarkMsgEventContext(msgData *larkim.P2MessageReceiveV1Data) *LarkMsgEventContext {
//
//	eventInfo := &LarkMsgEventContext{}
//
//	// 发送者信息
//	if nil != msgData.Sender && msgData.Sender.SenderId != nil {
//		eventInfo.Sender = *NewLarkUserFromSender(msgData.Sender)
//	}
//
//	if nil != msgData.Message.ChatId {
//		eventInfo.ChatId = *(msgData.Message.ChatId)
//	}
//	if nil != msgData.Message.ChatType {
//		eventInfo.ChatType = *(msgData.Message.ChatType)
//	}
//
//	// 消息ID信息
//	if nil != msgData.Message.MessageId {
//		eventInfo.MessageId = *(msgData.Message.MessageId)
//	}
//	if nil != msgData.Message.RootId {
//		eventInfo.RootId = *(msgData.Message.RootId)
//	}
//	if nil != msgData.Message.ParentId {
//		eventInfo.ParentId = *(msgData.Message.ParentId)
//	}
//	if nil != msgData.Message.CreateTime {
//		eventInfo.CreateTime = *(msgData.Message.CreateTime)
//	}
//
//	// 消息提及的成员信息
//	if nil != msgData.Message.Mentions {
//		eventInfo.Mentions = make([]LarkMentionUser, 0)
//		for _, mention := range msgData.Message.Mentions {
//
//			mentionUser := NewLarkUserFromMentionUser(mention)
//			if mentionUser != nil {
//				eventInfo.Mentions = append(eventInfo.Mentions, *mentionUser)
//			}
//		}
//	}
//	return eventInfo
//}

// https://open.feishu.cn/document/server-docs/im-v1/message-content-description/message_content
// 飞书纯文本类型的消息
type LarkTextMessageContent struct {
	Text string `json:"text"`
}

// 将一个json字符串解析为LarkTextMessageContent结构体
func ParseLarkTextMessageContent(jsonStr string) (*LarkTextMessageContent, error) {
	var content LarkTextMessageContent
	err := json.Unmarshal([]byte(jsonStr), &content)
	if err != nil {
		return nil, err
	}
	return &content, nil
}

// GetMentionsUserKey 判断消息是否是@某人的消息
// 原消息内容格式 为： "@_user_1 @_user_2 实际内容"
// 返回@人的序号列表
func (c *LarkTextMessageContent) GetMentionsUserKey() []string {
	// 用来存储解析出来的用户序号
	var userKeyList []string

	// 按空格分割消息内容
	parts := strings.Fields(c.Text)

	// 遍历分割后的每个部分
	for _, part := range parts {

		// 检查是否以"@_"开头
		if strings.HasPrefix(part, "@_user_") {

			// 将解析出来的序号添加到列表中
			userKeyList = append(userKeyList, part)
		}
	}

	// 返回解析得到的用户序号列表
	return userKeyList
}

// GetPureContentExtMentions 获取纯粹的文本内容 剔除Mentions信息
// Mentions信息就是 @人的那些字段信息
// 原消息内容格式 为： "@_user_1 @_user_2 实际内容"
func (c *LarkTextMessageContent) GetPureContentExtMentions() string {

	// 分割消息内容为单词
	parts := strings.Fields(c.Text)

	// 用来构建结果的字符串构建器
	var builder strings.Builder

	// 遍历每个单词
	for _, part := range parts {

		if strings.HasPrefix(part, "@_user_") {

			continue // 跳过@用户部分

		} else {
			// 添加非@用户部分到结果中
			builder.WriteString(part)
			builder.WriteString(" ") // 单词之间用空格分隔
		}
	}

	// 去除末尾多余的空格（如果有）
	result := strings.TrimSpace(builder.String())
	return result
}

func GetPureContentExtMentions(msg string) string {

	// 在消息内容中添加空格，以确保@用户部分前有空格
	msg = addSpaceBeforeMention(msg)

	// 分割消息内容为单词
	parts := strings.Fields(msg)

	// 用来构建结果的字符串构建器
	var builder strings.Builder

	// 遍历每个单词
	for _, part := range parts {

		if strings.HasPrefix(part, "@_user_") {

			continue // 跳过@用户部分

		} else {
			// 添加非@用户部分到结果中
			builder.WriteString(part)
			builder.WriteString(" ") // 单词之间用空格分隔
		}
	}

	// 去除末尾多余的空格（如果有）
	result := strings.TrimSpace(builder.String())
	return result
}

func addSpaceBeforeMention(s string) string {
	// 正则匹配：非空格开头后紧跟@_user_
	re := regexp.MustCompile(`([^\s])@_user_`)
	// 在匹配位置前插入空格（保留原字符）
	return re.ReplaceAllString(s, "$1 @_user_")
}

type JSONData struct {
	Title   string      `json:"title"`
	Content [][]Content `json:"content"`
}
type Content struct {
	Tag      string   `json:"tag"`
	Text     string   `json:"text,omitempty"`
	Style    []string `json:"style"`
	Href     string   `json:"href,omitempty"`
	UserID   string   `json:"user_id,omitempty"`
	UserName string   `json:"user_name,omitempty"`
	ImgKey   string   `json:"image_key,omitempty"`
}
