package common

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestParseLarkTextMessageContent(t *testing.T) {
	content, err := ParseLarkTextMessageContent("{\"text\":\"hello\"}")
	assert.NoError(t, err)
	assert.Equal(t, "hello", content.Text)
}

func TestLarkTextMessageContent_GetAtUserIdx_msgBegin(t *testing.T) {
	content := LarkTextMessageContent{
		Text: "hello @_user_123 @_user_456 @_user_789",
	}
	atUserKeys := content.GetMentionsUserKey()
	assert.Equal(t, []string{"@_user_123", "@_user_456", "@_user_789"}, atUserKeys)
}
func TestLarkTextMessageContent_GetAtUserIdx_Empty(t *testing.T) {
	content := LarkTextMessageContent{
		Text: "hello",
	}
	atUserKeys := content.GetMentionsUserKey()
	assert.Empty(t, atUserKeys)
}
func TestLarkTextMessageContent_GetAtUserIdx_msgEnd(t *testing.T) {
	content := LarkTextMessageContent{
		Text: "@_user_123 @_user_789 hello",
	}
	atUserKeys := content.GetMentionsUserKey()
	assert.Equal(t, []string{"@_user_123", "@_user_789"}, atUserKeys)
}

func TestLarkTextMessageContent_GetAtUserContent(t *testing.T) {
	t.Run("正常情况", func(t *testing.T) {
		c := &LarkTextMessageContent{Text: "@_user_1 @_user_2 实际内容"}
		result := c.GetPureContentExtMentions()
		assert.Equal(t, "实际内容", result)
	})
	t.Run("没有@用户的情况", func(t *testing.T) {
		c := &LarkTextMessageContent{Text: "实际内容"}
		result := c.GetPureContentExtMentions()
		assert.Equal(t, "实际内容", result)
	})
	t.Run("只有@用户的情况", func(t *testing.T) {
		c := &LarkTextMessageContent{Text: "@_user_1 @_user_2"}
		result := c.GetPureContentExtMentions()
		assert.Equal(t, "", result)
	})
	t.Run("包含空格和其他特殊字符的情况", func(t *testing.T) {
		c := &LarkTextMessageContent{Text: "@_user_1 @_user_2 实际内容 @_user_3 内容2"}
		result := c.GetPureContentExtMentions()
		assert.Equal(t, "实际内容 内容2", result)
	})
}
