package version

import (
	"fmt"
)

/*
通过-ldflags方式制定版本信息
go build -ldflags "-X 'golang.52tt.com/services/rev-aone-robot/version.GitBranch=$currentBranch' -X 'golang.52tt.com/services/rev-aone-robot/version.GitLastCommitLog=$GIT_COMMIT_LASTLOG"
*/
var (
	GitLastCommitLog string
	GitBranch        string
)

func GetBotVersion() string {

	return fmt.Sprintf("GitBranch: %s\nGitLastCommitLog: %s \n",
		GitBranch, GitLastCommitLog)

}
