package internal

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
	"gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
	"gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer/task/tasks"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/protocol/services/demo/echo"
	pb "golang.52tt.com/protocol/services/tt/quicksilver/guild-robot"
	"golang.52tt.com/services/guild-robot/internal/aigc/knowledge_platform"
	"golang.52tt.com/services/guild-robot/internal/config"
	dyconfig "golang.52tt.com/services/guild-robot/internal/config/ttconfig/guild_robot_card"
	"golang.52tt.com/services/guild-robot/internal/feishu/event"
	"golang.52tt.com/services/guild-robot/internal/store"
	"time"
)

type StartConfig struct {
	LarkConfig   []config.LarkConfig  `json:"lard_config_list"`
	AIGCKPConfig *config.AIGCKPConfig `json:"aigc_knowledge_platform_config"` // AIGC Knowledge Platform configuration

	// from config file
	MysqlConfig *mysqlConnect.MysqlConfig `json:"mysql"`
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)

	// init mysql
	store_, err := store.NewStore(ctx, cfg.MysqlConfig)
	if err != nil {
		log.Errorf("mysql connect failed: %v", err)
		return nil, err
	}

	// init dynamic config
	if err = dyconfig.InitGuildRobotCardConfig(); err != nil {
		log.ErrorWithCtx(ctx, "Failed to initialize GuildRobotCardConfig: %v", err)
		return nil, err
	}

	// init knowledge platform client
	if cfg.AIGCKPConfig == nil || cfg.AIGCKPConfig.Url == "" || cfg.AIGCKPConfig.AuthToken == "" {
		log.ErrorWithCtx(ctx, "AIGCKPConfig is nil or missing required fields, please check your configuration")
		return nil, fmt.Errorf("AIGCKPConfig is nil or missing required fields")
	}
	knowledgeBaseCli_, err := knowledge_platform.NewClient(ctx, cfg.AIGCKPConfig.Url, cfg.AIGCKPConfig.AuthToken, cfg.AIGCKPConfig.BaseFolder)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to create KnowledgePlatformClient: %v", err)
		return nil, err
	}

	// init lark event
	if len(cfg.LarkConfig) < 1 {
		log.ErrorWithCtx(ctx, "LarkConfig is empty, please check your configuration")
		return nil, fmt.Errorf("LarkConfig is empty, please check your configuration")
	}
	robotConf_ := make(map[uint32]config.AIGCConfig, len(cfg.LarkConfig))
	eventsMap_ := make(map[uint32]*event.LarkEvent, len(cfg.LarkConfig))
	for _, larkConf := range cfg.LarkConfig {
		if larkConf.AppId == "" || larkConf.AppSecret == "" {
			log.ErrorWithCtx(ctx, "LarkConfig AppId or AppSecret is empty, please check your configuration")
			return nil, fmt.Errorf("LarkConfig AppId or AppSecret is empty, please check your configuration")
		}
		if _, ok := robotConf_[larkConf.RobotType]; ok {
			log.ErrorWithCtx(ctx, "LarkConfig AppId is duplicated: %s", larkConf.AppId)
			return nil, fmt.Errorf("LarkConfig AppId is duplicated")
		}
		// create lark event
		larkEvent, err := event.NewEvent(ctx, larkConf, knowledgeBaseCli_, store_)
		if err != nil {
			log.ErrorWithCtx(ctx, "Failed to create LarkEvent: %v", err)
			return nil, err
		}
		// add to events
		eventsMap_[larkConf.RobotType] = larkEvent
		// add to robotConf
		robotConf_[larkConf.RobotType] = larkConf.AIGCConf
	}

	// 新版单实例定时任务
	timerD, err := timer.NewTimerD(ctx, "guild-robot", timer.WithTTL(10*time.Second))
	if nil != err {
		log.ErrorWithCtx(ctx, "init NewTimerD fail, err: %v", err)
		return nil, err
	}
	// 定时巡检IOS签约状态定时任务
	timerD.AddIntervalTask("CreateTable", 24*time.Hour, tasks.FuncTask(func(ctx context.Context) {
		_ = store_.CreateTable(ctx)
	}))
	timerD.Start()

	s := &Server{}
	s.store = store_
	s.larkEventsMap = eventsMap_
	s.robotConfMap = robotConf_
	s.knowledgeBaseCli = knowledgeBaseCli_
	s.timer = timerD
	log.DebugWithCtx(ctx, "[guild-robot]Server startup with cfg: %+v", *cfg)
	return s, nil
}

type Server struct {
	store            *store.Store // mysql client
	larkEventsMap    map[uint32]*event.LarkEvent
	robotConfMap     map[uint32]config.AIGCConfig
	knowledgeBaseCli *knowledge_platform.KnowledgePlatformClient
	timer            *timer.Timer // 单实例定时任务
}

func (s *Server) ShutDown() {
	_ = s.store.Close()
	_ = s.timer.Stop
}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

func (s *Server) UploadKnowledgeFileUrl(ctx context.Context, req *pb.UploadKnowledgeFileUrlReq) (*pb.UploadKnowledgeFileUrlResp, error) {
	resp := &pb.UploadKnowledgeFileUrlResp{}

	if req.FileName == "" || req.FileUrl == "" || req.KnowledgeType < 5 || req.RobotType == 0 || req.Operator == "" {
		log.ErrorWithCtx(ctx, "UploadKnowledgeFileUrlReq is invalid: %+v", req)
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "无效的知识库文档上传请求")
	}

	// 检查知识库文档是否已存在
	var CreateAt string
	if req.Id > 0 {
		file, err := s.store.GetKnowledgeFileByID(ctx, req.Id, req.GetRobotType())
		if err != nil {
			log.ErrorWithCtx(ctx, "Failed to get knowledge file by ID: %v", err)
			return nil, protocol.NewExactServerError(nil, status.ErrRepositoryFailed, "获取知识库文档失败")
		}
		if file == nil {
			log.ErrorWithCtx(ctx, "Knowledge file not found for ID: %d", req.Id)
			return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "所要更新的知识库文档不存在")
		}
		CreateAt = file.CreateTime.Local().Format(time.DateTime) // 获取创建时间

		// 删除旧的知识库文档
		if err := s.knowledgeBaseCli.DeleteFile(ctx, file.KnowledgePlatformId); err != nil {
			log.ErrorWithCtx(ctx, "Failed to delete old knowledge file: %v", err)
			return nil, protocol.NewExactServerError(nil, status.ErrExternalSystemFailed, "删除旧的知识库文档失败")
		}
	}

	// 获取机器人配置
	larkEvent, ok := s.larkEventsMap[uint32(req.GetRobotType())]
	if !ok {
		log.ErrorWithCtx(ctx, "RobotType %d not found in robot configuration", req.GetRobotType())
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "无效的公会机器人类型")
	}

	// 下载飞书文档
	fileInfo, err := larkEvent.GetFeishuApi().DownloadFileByUrl(ctx, req.FileUrl)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to download file from URL: %v", err)
		return nil, protocol.NewExactServerError(nil, status.ErrExternalSystemFailed, "下载知识库文档失败")
	}

	// 上传文件到知识库
	fileKBUrl, err := s.knowledgeBaseCli.UploadFile(ctx, fileInfo.File, fileInfo.FileName, req.GetRobotType())
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to upload file to knowledge base: %v", err)
		return nil, protocol.NewExactServerError(nil, status.ErrExternalSystemFailed, "上传知识库文档失败")
	}
	log.DebugWithCtx(ctx, "Successfully uploaded file to knowledge base: %s", fileKBUrl)

	// 创建或更新知识库文档
	now := time.Now()
	kbConfig, ok := s.robotConfMap[uint32(req.GetRobotType())]
	if !ok {
		log.ErrorWithCtx(ctx, "RobotType %d not found in robot configuration", req.GetRobotType())
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "无效的公会机器人类型")
	}
	// 创建新的知识库文档
	if CreateAt == "" {
		CreateAt = now.Local().Format(time.DateTime) // 如果没有创建时间，则使用当前时间
	}
	fileId, err := s.knowledgeBaseCli.ImportFile(ctx, &knowledge_platform.ImportFileRequestPayload{
		FileUrl:   fileKBUrl,
		FileName:  req.GetFileName(),
		FolderId:  kbConfig.FolderId,
		CreateAt:  CreateAt,
		IsEnabled: true,
		UpdateAt:  now.Local().Format(time.DateTime),
		UniqueUrl: true,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to import file to knowledge base: %v", err)
		return nil, protocol.NewExactServerError(nil, status.ErrExternalSystemFailed, "导入知识库文档失败")
	}
	log.DebugWithCtx(ctx, "Successfully imported file to knowledge base: %s", fileId)
	// 保存到本地数据库
	data := &store.KnowledgeFile{
		ID:                  req.GetId(), // 新建时ID为0
		FileName:            req.GetFileName(),
		FilePath:            req.GetFileUrl(),
		KnowledgePlatformId: fileId,
		KnowledgeType:       uint32(req.GetKnowledgeType()),
		IsEnabled:           store.Enabled,
		CreateTime:          now,
		Creator:             req.GetOperator(),
		UpdateTime:          now,
		Updater:             req.GetOperator(),
	}
	if err := s.store.CreateOrUpdateKnowledgeFile(ctx, data, req.GetRobotType()); err != nil {
		log.ErrorWithCtx(ctx, "Failed to create knowledge file in local database: %v", err)
		return nil, protocol.NewExactServerError(nil, status.ErrRepositoryFailed, "保存知识库文档到本地数据库失败")
	}

	resp.KnowledgeFile = &pb.KnowledgeFile{
		RobotType:     req.GetRobotType(),
		Id:            data.ID,
		FileName:      data.FileName,
		KnowledgeType: pb.KnowledgeType(data.KnowledgeType),
		FileUrl:       data.FilePath,
		CreateTime:    uint64(data.CreateTime.Unix()),
		Creator:       data.Creator,
		UpdateTime:    uint64(data.UpdateTime.Unix()),
		Updater:       data.Updater,
	}
	return resp, nil
}

func (s *Server) GetKnowledgeFileList(ctx context.Context, req *pb.GetKnowledgeFileListReq) (*pb.GetKnowledgeFileListResp, error) {
	resp := &pb.GetKnowledgeFileListResp{}

	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 获取知识库文档列表
	files, totalCount, err := s.store.GetKnowledgeFileList(ctx, req.RobotType, uint32(req.KnowledgeType), int(req.Page), int(req.PageSize), req.GetSearchQuery())
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to get knowledge file list: %v", err)
		return nil, protocol.NewExactServerError(nil, status.ErrRepositoryFailed, "获取知识库文档列表失败")
	}
	resp.TotalCount = uint32(totalCount)
	resp.KnowledgeFiles = make([]*pb.KnowledgeFile, 0, len(files))
	for _, file := range files {
		knowledgeFile := &pb.KnowledgeFile{
			RobotType:     req.GetRobotType(),
			Id:            file.ID,
			FileName:      file.FileName,
			KnowledgeType: pb.KnowledgeType(file.KnowledgeType),
			FileUrl:       file.FilePath,
			CreateTime:    uint64(file.CreateTime.Unix()),
			Creator:       file.Creator,
			UpdateTime:    uint64(file.UpdateTime.Unix()),
			Updater:       file.Updater,
		}
		resp.KnowledgeFiles = append(resp.KnowledgeFiles, knowledgeFile)
	}

	return resp, nil
}

func (s *Server) DeleteKnowledgeFile(ctx context.Context, req *pb.DeleteKnowledgeFileReq) (*pb.DeleteKnowledgeFileResp, error) {
	resp := &pb.DeleteKnowledgeFileResp{}

	if req.Id <= 0 {
		log.ErrorWithCtx(ctx, "DeleteKnowledgeFileReq.Id is invalid: %d", req.Id)
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "无效的知识库文档ID")
	}

	// 获取文件ID
	file, err := s.store.GetKnowledgeFileByID(ctx, req.Id, req.GetRobotType())
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to get knowledge file by ID: %v", err)
		return nil, protocol.NewExactServerError(nil, status.ErrRepositoryFailed, "获取知识库文档失败")
	}
	if file == nil {
		log.ErrorWithCtx(ctx, "Knowledge file not found for ID: %d", req.Id)
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "知识库文档不存在")
	}
	fileId := file.KnowledgePlatformId

	// 删除知识库文档
	if err := s.knowledgeBaseCli.DeleteFile(ctx, fileId); err != nil {
		log.ErrorWithCtx(ctx, "Failed to update knowledge file: %v", err)
		return nil, protocol.NewExactServerError(nil, status.ErrExternalSystemFailed, "更新知识库文档失败")
	}

	// 软删除本地数据库记录
	if err := s.store.DeleteKnowledgeFile(ctx, req.Id, req.GetRobotType()); err != nil {
		log.ErrorWithCtx(ctx, "Failed to delete knowledge file: %v", err)
		return nil, protocol.NewExactServerError(nil, status.ErrRepositoryFailed, "删除知识库文档失败")
	}

	resp.Success = true
	return resp, nil
}
