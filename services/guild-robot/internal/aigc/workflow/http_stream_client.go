package flow

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"

	"golang.52tt.com/pkg/log"
)

// StreamResponse 表示流式响应的结构
type StreamResponse struct {
	Answer string `json:"answer,omitempty"`
	Chunk  string `json:"stream:chunk,omitempty"`
}

// RequestPayload 表示请求的负载
type RequestPayload struct {
	ConversationID string `json:"sys.conversation_id"`
	Query          string `json:"sys.query"`
	File           string `json:"file"`
}

// SendStreamRequest 发送流式请求并处理响应
// 返回一个channel，上层业务可以通过这个channel实时获取流式返回的内容
func (w *WorkFlowClient) SendStreamRequest(ctx context.Context, query string, conversationID string, file string) (<-chan string, error) {
	// 构建请求URL和请求体
	payload := RequestPayload{
		ConversationID: conversationID,
		Query:          query,
		File:           file,
	}

	// 将请求体转换为JSON
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to marshal request payload: %v", err)
		return nil, err
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", w.url, bytes.NewBuffer(payloadBytes))
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to create HTTP request: %v", err)
		return nil, err
	}

	// 设置请求头
	req.Header.Set("Stream", "true")
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", w.authToken)

	// 发送请求
	client := &http.Client{Timeout: 0}
	resp, err := client.Do(req)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to send HTTP request: %v", err)
		_ = resp.Body.Close()
		return nil, err
	}
	log.DebugWithCtx(ctx, "Received response: %+v", resp)

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		log.ErrorWithCtx(ctx, "Received non-200 response: %d", resp.StatusCode)
		body, _ := io.ReadAll(resp.Body)
		_ = resp.Body.Close()
		return nil, fmt.Errorf("non-200 response: %d, body: %s", resp.StatusCode, string(body))
	}

	// 创建一个channel用于传递流式响应内容
	answerChan := make(chan string, 100) // 使用缓冲channel避免阻塞

	// 在goroutine中处理流式响应
	go func() {
		defer close(answerChan) // 确保在函数结束时关闭channel
		defer resp.Body.Close() // 确保关闭响应体

		reader := bufio.NewReader(resp.Body)

		for {
			line, err := reader.ReadBytes('\n')
			log.DebugWithCtx(ctx, "line: %s", string(line))
			if err != nil {
				if err == io.EOF {
					log.InfoWithCtx(ctx, "Stream completed")
					break
				}
				log.ErrorWithCtx(ctx, "Error reading response: %v", err)
			}

			// 处理数据行，移除可能的\r\n
			lineStr := strings.TrimRight(string(line), "\r\n")

			// 跳过空行
			if lineStr == "" {
				continue
			}

			// 如果不是data事件，跳过
			strPrefix := "data:"

			if !strings.HasPrefix(lineStr, strPrefix) {
				continue
			}

			// 提取并解析JSON数据
			jsonData := strings.TrimSpace(strings.TrimPrefix(lineStr, strPrefix))
			if jsonData == "" {
				continue
			}
			if jsonData == "[DONE]" {
				log.InfoWithCtx(ctx, "Stream completed with [DONE]")
				break
			}

			var response StreamResponse
			if err := json.Unmarshal([]byte(jsonData), &response); err != nil {
				log.ErrorWithCtx(ctx, "Failed to unmarshal response: %v, data: %s", err, jsonData)
				continue
			}

			// 将非空答案片段发送到channel
			if response.Answer != "" {
				select {
				case answerChan <- response.Answer:
					// 成功发送到channel
				case <-ctx.Done():
					// 上下文已取消，退出goroutine
					log.InfoWithCtx(ctx, "Context canceled, stopping stream processing")
					return
				}
			}
		}
	}()

	return answerChan, nil
}
