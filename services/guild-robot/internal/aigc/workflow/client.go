package flow

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
)

type WorkFlowClient struct {
	url       string
	authToken string
}

func NewClient(ctx context.Context, url, authToken string) (*WorkFlowClient, error) {
	cli := &WorkFlowClient{
		url:       url,
		authToken: authToken,
	}
	if cli.url == "" || cli.authToken == "" {
		log.ErrorWithCtx(ctx, "url or authToken is empty")
		return nil, fmt.Errorf("url or authToken is empty")
	}
	return cli, nil
}
