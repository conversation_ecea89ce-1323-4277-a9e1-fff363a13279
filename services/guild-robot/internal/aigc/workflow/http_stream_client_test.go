package flow

import (
	"context"
	"testing"
	"time"

	"golang.52tt.com/pkg/log"
)

func TestSendStreamRequest(t *testing.T) {
	// 初始化测试参数
	ctx := context.Background()
	url := "http://aigc.cn-bj.rcmd-tt.skyengine.net.cn/api-workflow/release/2364/start2364xGeAGHPCD"
	authToken := "huobizhinengkefu154:b09505972e7ddc2341dbffc9791a2c81"
	query := "进房的cmd是多少"
	conversationID := "1234567"
	file := ""

	// 调用SendStreamRequest函数
	client := &WorkFlowClient{
		url:       url,
		authToken: authToken,
	}
	answerChan, err := client.SendStreamRequest(ctx, query, conversationID, file)
	if err != nil {
		t.Fatalf("SendStreamRequest failed: %v", err)
	}

	// 设置超时时间
	timeout := time.After(30 * time.Second)
	// 用于存储收到的所有回答
	fullAnswer := ""

	// 从channel中读取数据
	for {
		select {
		case answer, ok := <-answerChan:
			if !ok {
				// channel已关闭，输出收集到的所有答案
				log.InfoWithCtx(ctx, "Received all answers: %v", fullAnswer)
				return
			}
			// 将收到的答案添加到切片中
			fullAnswer = answer
			log.InfoWithCtx(ctx, "Received answer: %s", answer)

		case <-timeout:
			// 超时处理
			t.Fatal("Test timed out after 30 seconds")
			return
		}
	}
}
