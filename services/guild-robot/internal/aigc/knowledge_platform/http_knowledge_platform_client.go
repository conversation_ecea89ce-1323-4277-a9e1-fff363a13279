package knowledge_platform

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	pb "golang.52tt.com/protocol/services/tt/quicksilver/guild-robot"
	"io"
	"mime/multipart"
	"net/http"
)

// 详见文档：https://q9jvw0u5f5.feishu.cn/wiki/F2Pww1ZxliYrOkk0tBsc1IsLnMg

// BaseResponse 表示通用响应的结构
type BaseResponse struct {
	Code int    `json:"code"` // 0 成功 / 非0 失败
	Msg  string `json:"msg"`  // 失败返回信息
	Data *Data  `json:"data"` // 成功返回
}

type Data struct {
	Folders []*Folder `json:"folders,omitempty"` // 文件夹列表
	Id      uint32    `json:"id,omitempty"`      // ID
	Url     []string  `json:"url,omitempty"`
}

type Folder struct {
	Id        uint32   `json:"id"`                  // 文件夹ID
	Name      string   `json:"name"`                // 文件夹名
	Subfolder []Folder `json:"subfolder,omitempty"` // 子文件夹名
}

type GetFolderRequestPayload struct {
	FolderId uint32 `json:"folder_id"` // 文件夹ID
}

// GetFolderList 获取文件夹列表
func (c *KnowledgePlatformClient) GetFolderList(ctx context.Context, folderId uint32) (response *Data, err error) {
	// 构建请求URL和请求体
	payload := GetFolderRequestPayload{
		FolderId: folderId,
	}

	// 将请求体转换为JSON
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to marshal request payload: %v", err)
		return nil, err
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, fmt.Sprintf("%s/knowledge_platform/api/v1/folders", c.url), bytes.NewBuffer(payloadBytes))
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to create HTTP request: %v", err)
		return nil, err
	}

	// 设置请求头
	req.Header.Set("X-Auth-Token", c.authToken)
	req.Header.Set("Content-Type", "application/json")
	log.DebugWithCtx(ctx, "Sending request to %s with req: %+v", req.URL.String(), req)

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to send HTTP request: %v", err)
		return nil, err
	}
	log.DebugWithCtx(ctx, "Received response: %+v", resp)
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		log.ErrorWithCtx(ctx, "Received non-200 response: %d", resp.StatusCode)
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("non-200 response: %d, body: %s", resp.StatusCode, string(body))
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.ErrorWithCtx(ctx, "http request read body failed: %v", err)
		return nil, err
	}

	// 解析响应体
	result := &BaseResponse{}
	err = json.Unmarshal(body, result)
	if err != nil {
		log.ErrorWithCtx(ctx, "body: %s", string(body))
		return nil, err
	}

	if result.Code != 0 {
		log.ErrorWithCtx(ctx, "http request failed: %s", result.Msg)
		return nil, fmt.Errorf("http request failed: %s", result.Msg)
	}

	if result.Data == nil {
		log.ErrorWithCtx(ctx, "http request failed: data is nil")
		return nil, fmt.Errorf("http request failed: data is nil")
	}

	return result.Data, nil
}

type NewFolderRequestPayload struct {
	FolderName string `json:"name"`                // 文件夹名字
	ParentId   uint32 `json:"parent_id,omitempty"` // 父文件夹ID, 0表示根目录
}

func (c *KnowledgePlatformClient) NewFolder(ctx context.Context, folderName string, parentId uint32) (folderId uint32, err error) {
	// 构建请求URL和请求体
	payload := &NewFolderRequestPayload{
		FolderName: folderName,
		ParentId:   parentId,
	}
	// 将请求体转换为JSON
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to marshal request payload: %v", err)
		return 0, err
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, fmt.Sprintf("%s/knowledge_platform/api/v1/folder", c.url), bytes.NewBuffer(payloadBytes))
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to create HTTP request: %v", err)
		return 0, err
	}

	// 设置请求头
	req.Header.Set("X-Auth-Token", c.authToken)
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to send HTTP request: %v", err)
		return 0, err
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		log.ErrorWithCtx(ctx, "Received non-200 response: %d", resp.StatusCode)
		body, _ := io.ReadAll(resp.Body)
		return 0, fmt.Errorf("non-200 response: %d, body: %s", resp.StatusCode, string(body))
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.ErrorWithCtx(ctx, "http request read body failed: %v", err)
		return 0, err
	}

	// 解析响应体
	result := &BaseResponse{}
	err = json.Unmarshal(body, result)
	if err != nil {
		log.ErrorWithCtx(ctx, "body: %s", string(body))
		return 0, err
	}

	if result.Code != 0 {
		log.ErrorWithCtx(ctx, "http request failed: %s", result.Msg)
		return 0, fmt.Errorf("http request failed: %s", result.Msg)
	}

	if result.Data == nil || result.Data.Folders == nil || len(result.Data.Folders) == 0 {
		log.ErrorWithCtx(ctx, "http request failed: data is nil or empty")
		return 0, fmt.Errorf("http request failed: data is nil or empty")
	}
	return result.Data.Id, nil
}

// UploadFile 上传文件到知识库对象存储
func (c *KnowledgePlatformClient) UploadFile(ctx context.Context, fileReader io.Reader, fileName string, robotType pb.RobotType) (fileUrl string, err error) {

	// 创建请求体缓冲区
	var writeBody bytes.Buffer
	writer := multipart.NewWriter(&writeBody)

	// 写入文件
	part, err := writer.CreateFormFile("files", fileName)
	if err != nil {
		return "", err
	}

	_, err = io.Copy(part, fileReader)
	if err != nil {
		return "", err
	}

	// 添加其他字段
	_ = writer.WriteField("folder", getUploadFolderByRobotType(robotType))

	// 关闭multipart writer
	err = writer.Close()
	if err != nil {
		return "", err
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", fmt.Sprintf("%s/knowledge_platform/api/v1/file_storage", c.url), &writeBody)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("X-Auth-Token", c.authToken)
	req.Header.Set("Content-Type", writer.FormDataContentType())

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to send HTTP request: %v", err)
		return "", err
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		log.ErrorWithCtx(ctx, "Received non-200 response: %d", resp.StatusCode)
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("non-200 response: %d, body: %s", resp.StatusCode, string(body))
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.ErrorWithCtx(ctx, "http request read body failed: %v", err)
		return "", err
	}

	// 解析响应体
	result := &BaseResponse{}
	err = json.Unmarshal(body, result)
	if err != nil {
		log.ErrorWithCtx(ctx, "body: %s", string(body))
		return "", err
	}

	if result.Code != 0 {
		log.ErrorWithCtx(ctx, "http request failed: %s", result.Msg)
		return "", fmt.Errorf("http request failed: %s", result.Msg)
	}

	if result.Data == nil {
		log.ErrorWithCtx(ctx, "http request failed: data is nil or empty")
		return "", fmt.Errorf("http request failed: data is nil or empty")
	}
	return result.Data.Url[0], nil
}

// getUploadFolderByRobotType 根据机器人类型返回对应的上传文件夹
func getUploadFolderByRobotType(robotType pb.RobotType) string {
	// 根据机器人类型返回对应的文件夹名称
	switch robotType {
	case pb.RobotType_ROBOT_TYPE_AMUSE: // 多人互动文件夹
		return "amuse_robot_files"
	case pb.RobotType_ROBOT_TYPE_YUYIN: // 语音直播（听听房）文件夹
		return "yuyin_robot_files"
	case pb.RobotType_ROBOT_TYPE_ESPORT: // 电竞房文件夹
		return "esport_robot_files"
	default:
		return "default_folder" // 默认文件夹
	}
}

// ImportFileRequestPayload 表示导入请求的负载
type ImportFileRequestPayload struct {
	FileUrl     string      `json:"url,omitempty"`          // 文件url (url传空, 则表示创建空白文件)
	FileName    string      `json:"name"`                   // 文件名字
	TableSchema interface{} `json:"table_schema,omitempty"` // 文档型文件不需要, 表格型文件必传, 代表表格的schema
	FolderId    uint32      `json:"folder_id,omitempty"`    // 文件夹id
	Tags        []string    `json:"tags,omitempty"`         // 标签
	CreateAt    string      `json:"create_at,omitempty"`    // 成文时间
	PublicAt    string      `json:"public_at,omitempty"`    // 公开时间
	ExpireAt    string      `json:"expire_at,omitempty"`    // 过期时间
	IsEnabled   bool        `json:"is_enabled,omitempty"`   // 是否启用
	UpdateAt    string      `json:"update_at,omitempty"`    // 更新时间
	UniqueUrl   bool        `json:"unique_url,omitempty"`   // 是否在文件夹中限制同url的文件只留一个
}

type ImportFileResponse struct {
	Code int    `json:"code"` // 0 成功 / 非0 失败
	Msg  string `json:"msg"`  // 失败返回信息
	Data *File  `json:"data"` // 成功返回
}

// File 知识库文件结构
type File struct {
	Id        uint32   `json:"id"`         // 文件ID
	Name      string   `json:"name"`       // 文件名
	Url       string   `json:"url"`        // 文件URL
	FolderId  uint32   `json:"folder_id"`  // 文件夹ID
	Tags      []string `json:"tags"`       // 标签
	CreateAt  string   `json:"create_at"`  // 成文时间
	PublicAt  string   `json:"public_at"`  // 公开时间
	ExpireAt  string   `json:"expire_at"`  // 过期时间
	IsEnabled bool     `json:"is_enabled"` // 是否启用
	UpdateAt  string   `json:"update_at"`  // 更新时间
}

// ImportFile 导入文件到知识库
func (c *KnowledgePlatformClient) ImportFile(ctx context.Context, payload *ImportFileRequestPayload) (fileId uint32, err error) {
	// 将请求体转换为JSON
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to marshal request payload: %v", err)
		return 0, err
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, fmt.Sprintf("%s/knowledge_platform/api/v1/file", c.url), bytes.NewBuffer(payloadBytes))
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to create HTTP request: %v", err)
		return 0, err
	}

	// 设置请求头
	req.Header.Set("X-Auth-Token", c.authToken)
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to send HTTP request: %v", err)
		return 0, err
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		log.ErrorWithCtx(ctx, "Received non-200 response: %d", resp.StatusCode)
		body, _ := io.ReadAll(resp.Body)
		return 0, fmt.Errorf("non-200 response: %d, body: %s", resp.StatusCode, string(body))
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.ErrorWithCtx(ctx, "http request read body failed: %v", err)
		return 0, err
	}

	// 解析响应体
	result := &ImportFileResponse{}
	err = json.Unmarshal(body, result)
	if err != nil {
		log.ErrorWithCtx(ctx, "body: %s", string(body))
		return 0, err
	}

	if result.Code != 0 {
		log.ErrorWithCtx(ctx, "http request failed: %s", result.Msg)
		return 0, fmt.Errorf("http request failed: %s", result.Msg)
	}

	if result.Data == nil {
		log.ErrorWithCtx(ctx, "http request failed: data or file is nil")
		return 0, fmt.Errorf("http request failed: data or file is nil")
	}
	return result.Data.Id, nil
}

// UpdateFileRequestPayload 表示更新请求的负载
type UpdateFileRequestPayload struct {
	FileName  string   `json:"name,omitempty"`       // 文件名字
	FolderId  uint32   `json:"folder_id,omitempty"`  // 文件夹id
	Tags      []string `json:"tags,omitempty"`       // 标签
	CreateAt  string   `json:"create_at,omitempty"`  // 成文时间
	PublicAt  string   `json:"public_at,omitempty"`  // 公开时间
	ExpireAt  string   `json:"expire_at,omitempty"`  // 过期时间
	IsEnabled bool     `json:"is_enabled,omitempty"` // 是否启用
	UpdateAt  string   `json:"update_at,omitempty"`  // 更新时间
}

// UpdateFile 更新文件信息
func (c *KnowledgePlatformClient) UpdateFile(ctx context.Context, fileId uint32, payload *UpdateFileRequestPayload) (err error) {
	// 将请求体转换为JSON
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to marshal request payload: %v", err)
		return err
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, http.MethodPatch, fmt.Sprintf("%s/knowledge_platform/api/v1/file/%d", c.url, fileId), bytes.NewBuffer(payloadBytes))
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to create HTTP request: %v", err)
		return err
	}

	// 设置请求头
	req.Header.Set("X-Auth-Token", c.authToken)
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to send HTTP request: %v", err)
		return err
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		log.ErrorWithCtx(ctx, "Received non-200 response: %d", resp.StatusCode)
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("non-200 response: %d, body: %s", resp.StatusCode, string(body))
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.ErrorWithCtx(ctx, "http request read body failed: %v", err)
		return err
	}

	// 解析响应体
	result := &BaseResponse{}
	err = json.Unmarshal(body, result)
	if err != nil {
		log.ErrorWithCtx(ctx, "body: %s", string(body))
		return err
	}

	if result.Code != 0 {
		log.ErrorWithCtx(ctx, "http request failed: %s", result.Msg)
		return fmt.Errorf("http request failed: %s", result.Msg)
	}

	return nil
}

// DeleteFile 删除文件
func (c *KnowledgePlatformClient) DeleteFile(ctx context.Context, fileId uint32) (err error) {
	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, http.MethodDelete, fmt.Sprintf("%s/knowledge_platform/api/v1/file/%d", c.url, fileId), nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to create HTTP request: %v", err)
		return err
	}

	// 设置请求头
	req.Header.Set("X-Auth-Token", c.authToken)
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to send HTTP request: %v", err)
		return err
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		log.ErrorWithCtx(ctx, "Received non-200 response: %d", resp.StatusCode)
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("non-200 response: %d, body: %s", resp.StatusCode, string(body))
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.ErrorWithCtx(ctx, "http request read body failed: %v", err)
		return err
	}

	// 解析响应体
	result := &BaseResponse{}
	err = json.Unmarshal(body, result)
	if err != nil {
		log.ErrorWithCtx(ctx, "body: %s", string(body))
		return err
	}

	if result.Code != 0 {
		log.ErrorWithCtx(ctx, "http request failed: %s", result.Msg)
		return fmt.Errorf("http request failed: %s", result.Msg)
	}

	return nil
}
