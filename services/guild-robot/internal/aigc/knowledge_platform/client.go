package knowledge_platform

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/log"
)

type KnowledgePlatformClient struct {
	url        string
	authToken  string
	baseFolder uint32
}

func NewClient(ctx context.Context, url, authToken string, baseFolderId uint32) (*KnowledgePlatformClient, error) {
	cli := &KnowledgePlatformClient{
		url:        url,
		authToken:  authToken,
		baseFolder: baseFolderId,
	}
	if err := cli.CheckBaseFolderId(ctx); err != nil {
		log.ErrorWithCtx(ctx, "Failed to check base folder ID: %v", err)
		return cli, err
	}
	return cli, nil
}

func (c *KnowledgePlatformClient) CheckBaseFolderId(ctx context.Context) error {
	if c.baseFolder == 0 {
		return fmt.Errorf("base folder is zero")
	}
	if c.url == "" || c.authToken == "" {
		return fmt.Errorf("url or authToken is empty")
	}
	_, err := c.GetFolderList(ctx, c.baseFolder)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to get folder list: %v", err)
		return fmt.Errorf("base folder does not exist")
	}
	return nil
}
