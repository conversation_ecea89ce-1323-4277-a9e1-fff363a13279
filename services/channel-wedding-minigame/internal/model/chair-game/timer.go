package chair_game

import (
    "gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
    "context"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer/task/tasks"
    "time"
    "golang.52tt.com/services/channel-wedding-minigame/internal/model/chair-game/cache"
    "fmt"
    "golang.52tt.com/services/channel-wedding-minigame/internal/model/chair-game/store"
    "golang.52tt.com/services/channel-wedding-minigame/internal/conf"
    logicPb "golang.52tt.com/protocol/app/channel_wedding_logic"
    "strconv"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    "github.com/google/uuid"
    "encoding/json"
    presentPB "golang.52tt.com/protocol/services/userpresent"
)

const (
    ReissueAwardBatCount = 20
)

func (m *Mgr) startTimer() error {
    var err error
    m.timerD, err = timer.NewTimerD(context.Background(),
        "channel-wedding-minigame",
        timer.WithV8RedisCmdable(m.cache.GetRedisClient()))
    if err != nil {
        log.Errorf("startTimer NewTimerD err:%v", err)
        return err
    }

    m.timerD.AddIntervalTask("ReissueAwardHandle", 3*time.Second, tasks.FuncTask(func(ctx context.Context) {
        // 奖励补发
        m.ReissueAwardHandle()
    }))

    // 处理抢椅子自动开抢队列
    m.timerD.AddLocalIntervalTask(300*time.Millisecond, tasks.FuncTask(func(ctx context.Context) {
        m.processQueue(cache.DelayQueueTypeAutoStartGrab, m.autoStartGrabHandle)
    }))

    // 处理抢椅子结束队列
    m.timerD.AddLocalIntervalTask(300*time.Millisecond, tasks.FuncTask(func(ctx context.Context) {
        m.processQueue(cache.DelayQueueTypeEndGrab, m.endGrabQueueHandle)
    }))

    // 处理自动进入下一轮队列
    m.timerD.AddLocalIntervalTask(1*time.Second, tasks.FuncTask(func(ctx context.Context) {
        m.processQueue(cache.DelayQueueTypeAutoStartNewRound, m.autoStartNextRoundHandle)
    }))

    // 消费结算队列
    m.timerD.AddLocalIntervalTask(1*time.Second, tasks.FuncTask(func(ctx context.Context) {
        m.settlementQueueHandle()
    }))

    // 处理超时未结束的游戏 - 直接流局
    m.timerD.AddLocalIntervalTask(1*time.Minute, tasks.FuncTask(func(ctx context.Context) {
        m.timeoutGameHandle()
    }))

    m.timerD.Start()
    return nil
}

func (m *Mgr) processQueue(queueType uint32, handleFunc func(context.Context, *cache.ChairGameRound) error) {
    ctx, cancel := context.WithTimeout(protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{RequestID: uuid.New().String()}), 5*time.Second)
    defer cancel()

    now, err := m.cache.GetRedisServerTime(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "processQueue GetRedisServerTime err:%v", err)
        now = time.Now()
    }
    gameInfoList, err := m.cache.GetChairGameTimerQueueByScore(ctx, queueType, now)
    if err != nil {
        log.Errorf("processQueue GetChairGameTimerQueueByScore err:%v", err)
        return
    }

    if len(gameInfoList) == 0 {
        return
    }

    for _, mem := range gameInfoList {
        log.DebugWithCtx(ctx, "processQueue gameInfo:%s queueType:%d", mem, queueType)
        var gameInfo cache.ChairGameRound
        err = json.Unmarshal([]byte(mem), &gameInfo)
        if err != nil {
            log.ErrorWithCtx(ctx, "processQueue json.Unmarshal mem:%s err:%v", mem, err)
            continue
        }
        err = handleFunc(ctx, &gameInfo)
        if err != nil {
            continue
        }
        // 从队列中移除
        if err := m.cache.DelChairGameTimerQueue(ctx, queueType, mem); err != nil {
            log.ErrorWithCtx(ctx, "processQueue DelChairGameTimerQueue err:%v", err)
        }
    }
}

func (m *Mgr) endGrabQueueHandle(ctx context.Context, gameInfo *cache.ChairGameRound) error {
    return m.CheckRoundEndHandle(ctx, gameInfo.Cid)
}

// AutoStartGrabHandle 自动开抢handle
func (m *Mgr) autoStartGrabHandle(ctx context.Context, gameInfo *cache.ChairGameRound) error {
    // 获取锁
    ok, err := m.cache.LockUpdateChairGameInfo(ctx, gameInfo.Cid, 5*time.Second)
    if err != nil || !ok {
        log.DebugWithCtx(ctx, "autoStartGrabHandle LockUpdateChairGameInfo err:%v", err)
        return fmt.Errorf("lock fail")
    }

    defer func() {
        // 解锁
        _ = m.cache.UnlockUpdateChairGameInfo(ctx, gameInfo.Cid)
    }()

    info, _, now, err := m.getChairGameInfo(ctx, gameInfo.Cid, true)

    if info == nil || info.GameOverTime > 0 || info.CurRound != gameInfo.CurRound || info.StartTime != 0 {
        return nil
    }

    info.StartTime = now.UnixMilli()
    info.EndTime = now.Add(time.Duration(m.bc.GetChairGameGrabDuration()) * time.Second).UnixMilli()

    err = m.cache.SetChairGameInfo(ctx, info)
    if err != nil {
        log.Errorf("autoStartGrabHandle SetChairGameInfo err:%v", err)
        return err
    }

    // 加入抢椅子即将结束队列
    _ = m.cache.AddChairGameTimerQueue(ctx, cache.DelayQueueTypeEndGrab, &cache.ChairGameRound{
        Cid:      gameInfo.Cid,
        GameId:   gameInfo.GameId,
        CurRound: gameInfo.CurRound,
    }, info.EndTime)

    // 推送游戏信息
    _ = m.acLayerMgr.ChairGameInfoChannelNotify(ctx, gameInfo.Cid, m.genPushGameInfoPb(ctx, gameInfo.Cid, false, false))

    log.InfoWithCtx(ctx, "autoStartGrabHandle gameInfo:%+v", info)
    return nil
}

// autoStartNextRoundHandle 自动进入下一轮handle
func (m *Mgr) autoStartNextRoundHandle(ctx context.Context, gameInfo *cache.ChairGameRound) error {
    info, err := m.cache.GetChairGameInfo(ctx, gameInfo.Cid)
    if err != nil {
        log.Errorf("autoStartNextRoundHandle GetChairGameInfo err:%v", err)
        return err
    }

    if info == nil || info.CurRound != gameInfo.CurRound || info.GameOverTime != 0 {
        return nil
    }

    gameInfoPb, err := m.StartNextRound(ctx, gameInfo.Cid)
    if err != nil {
        if err.Error() == SetNextRoundWrongStatus {
            // 发一个当前游戏信息推送
            _ = m.acLayerMgr.ChairGameInfoChannelNotify(ctx, gameInfo.Cid, m.genPushGameInfoPb(ctx, gameInfo.Cid, false, false))
            log.InfoWithCtx(ctx, "autoStartNextRoundHandle gameInfo:%+v wrong status, no need to handle", gameInfo)
            return nil
        }
        log.Errorf("autoStartNextRoundHandle StartNextRound err:%v", err)
        return err
    }

    // 推送游戏信息
    _ = m.acLayerMgr.ChairGameInfoChannelNotify(ctx, gameInfo.Cid, gameInfoPb)

    log.InfoWithCtx(ctx, "autoStartNextRoundHandle gameInfo:%+v", info)
    return nil
}

func (m *Mgr) settlementQueueHandle() {
    ctx, cancel := context.WithTimeout(protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{RequestID: uuid.New().String()}), 10*time.Second)
    defer cancel()

    ok, err := m.cache.LockTimer(ctx, "settlementHandle", 12*time.Second)
    if err != nil {
        log.ErrorWithCtx(ctx, "settlementQueueHandle lock fail")
        return
    }
    if !ok {
        return
    }
    defer func() {
        _ = m.cache.UnlockTimer(ctx, "settlementHandle")
    }()

    settleInfoList, err := m.cache.GetChairGameSettleQueueByScore(ctx, time.Now().Unix())
    if err != nil {
        log.Errorf("settlementQueueHandle GetChairGameSettleQueueByScore err:%v", err)
        return
    }
    if len(settleInfoList) == 0 {
        return
    }

    for _, mem := range settleInfoList {
        log.InfoWithCtx(ctx, "settlementQueueHandle settleInfo:%s", mem)
        var v cache.GameSettleInfo
        err = json.Unmarshal([]byte(mem), &v)
        if err != nil {
            log.ErrorWithCtx(ctx, "settlementQueueHandle Unmarshal failed, v:%s err:%+v", v, err)
            continue
        }

        switch v.GameResult {
        case conf.GameLogStatusFail:
            // 用户设置奖励，则回滚
            if v.PayOrderId != "" {
                err = m.chairGameAwardMgr.ConsumeRollBack(ctx, v.PayOrderId)
                if err != nil {
                    log.ErrorWithCtx(ctx, "settlementQueueHandle ChairGameSettleConsumeHandle err:%v", err)
                    //continue
                }
            }

        case conf.GameLogStatusSuccess:
            // 系统奖励则直接发放；用户设置奖励，则先commit，取得dealToken, 然后再发奖
            var payUid uint32
            var dealToken string
            if v.PayOrderId != "" {
                payUid, dealToken, err = m.chairGameAwardMgr.ConsumeCommit(ctx, v.PayOrderId)
                if err != nil {
                    log.ErrorWithCtx(ctx, "settlementQueueHandle ChairGameSettleConsumeHandle err:%v", err)
                    //continue
                }
            }

            awardInfo, err := m.store.GetAwardRecordByOrderId(ctx, v.AwardOrderId)
            if err != nil || awardInfo == nil {
                log.ErrorWithCtx(ctx, "settlementQueueHandle GetAwardRecordByOrderId awardInfo:%v err:%v", awardInfo, err)
                //continue
            }

            _ = m.sendGift(ctx, awardInfo, payUid, dealToken)

        default:
            log.WarnWithCtx(ctx, "settlementQueueHandle wrong gameResult settleInfo:%+v", v)
            continue
        }
        // 从队列中移除
        _ = m.cache.DelChairGameSettleQueue(ctx, mem)
    }

}

// genAwardOrderId
func (m *Mgr) genAwardOrderId(payOrderId string, giftType, cid, gameId, uid uint32, roundStartTs int64) string {
    switch giftType {
    case conf.GiftTypePackage:
        return fmt.Sprintf("%d_OP_%d_%d_%d_%d", m.bc.GetChairGameBackSender().AppId, cid, gameId, uid, roundStartTs)
    case conf.GiftTypeNormal, conf.GiftTypeMagicSpirit:
        return payOrderId
    default:
        return ""
    }
}

// CheckRoundEndHandle 检查轮次结束handle
func (m *Mgr) CheckRoundEndHandle(ctx context.Context, cid uint32) error {
    gameInfo, winners, now, err := m.getChairGameInfo(ctx, cid, true)
    if err != nil {
        log.Errorf("CheckRoundEndHandle getChairGameInfo err:%v", err)
        return err
    }

    log.DebugWithCtx(ctx, "CheckRoundEndHandle info:%+v winnerList:%v", gameInfo, winners)
    if gameInfo == nil || gameInfo.GameOverTime > 0 {
        return nil
    }

    roundStatus := getCurRoundStatus(now, gameInfo, winners)
    if roundStatus != uint32(logicPb.ChairRoundStatus_CHAIR_ROUND_STATUS_GAME_OVER) {
        if roundStatus == uint32(logicPb.ChairRoundStatus_CHAIR_ROUND_STATUS_ROUND_END) {
            log.InfoWithCtx(ctx, "CheckRoundEndHandle round Over cid:%d", cid)
            _ = m.cache.AddChairGameTimerQueue(ctx, cache.DelayQueueTypeAutoStartNewRound, &cache.ChairGameRound{
                Cid:      gameInfo.Cid,
                GameId:   gameInfo.GameId,
                CurRound: gameInfo.CurRound,
            }, now.Unix()+m.bc.GetAutoStartNextRoundDuration()) // 当前时间加上自动开始下一轮时间
        }
        _ = m.acLayerMgr.ChairGameInfoChannelNotify(ctx, cid, m.genPushGameInfoPb(ctx, cid, false, false))
        return nil
    }

    // 游戏结束
    consumerOrderNewStatus := conf.GameLogStatusFail // 默认为流局状态
    var awardList []*store.AwardRecord
    settleInfo := &cache.GameSettleInfo{
        Cid:        cid,
        GameId:     gameInfo.GameId,
        PayOrderId: gameInfo.Reward.PayOrderId,
        WeddingId:  gameInfo.WeddingId,
    }

    if len(winners) > 0 {
        consumerOrderNewStatus = conf.GameLogStatusSuccess
        winner := winners[0]
        orderId := m.genAwardOrderId(gameInfo.Reward.PayOrderId, gameInfo.Reward.GiftType, cid, gameInfo.GameId, winner, gameInfo.StartTime)
        settleInfo.AwardOrderId = orderId
        award := &store.AwardRecord{
            OrderID:  orderId,
            GameId:   gameInfo.GameId,
            Uid:      winner,
            GiftID:   gameInfo.Reward.GiftId,
            GiftType: gameInfo.Reward.GiftType,
            //GiftWorth: gameInfo.Reward.Price,
            Amount:    gameInfo.Reward.Amount,
            AwardTime: now,
        }
        // 仅记录T豆的价值
        if gameInfo.Reward.PriceType == uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) {
            award.GiftWorth = gameInfo.Reward.Price
        }
        // 记录发奖记录
        awardList = []*store.AwardRecord{award}
    }

    err = m.store.Transaction(ctx, func(tx mysql.Txx) error {
        var ok bool
        // 更新游戏状态
        ok, err = m.store.UpdateGameLogStatus(ctx, tx, gameInfo.GameId, consumerOrderNewStatus)
        if err != nil {
            return err
        }

        if !ok {
            return nil
        }

        if consumerOrderNewStatus == conf.GameLogStatusSuccess || len(awardList) > 0 {
            err = m.store.BatchInsertAwardRecord(ctx, tx, awardList, now)
            if err != nil {
                return err
            }

            if gameInfo.Reward.PayOrderId != "" {
                // 更新订单状态
                ok, err = m.chairGameAwardMgr.UpdateOrderWithGameIdTxHandle(ctx, tx, gameInfo.Reward.PayOrderId, gameInfo.GameId, conf.ConsumeOrderStatusInUse, conf.ConsumeOrderStatusAwarded)
                if err != nil {
                    log.Errorf("CheckRoundEndHandle UpdateOrderWithGameId err:%v", err)
                    return err
                }

                if !ok {
                    log.Errorf("CheckRoundEndHandle UpdateOrderWithGameIdTxHandle not ok. gameInfo:%v", gameInfo.Reward.PayOrderId)
                    return fmt.Errorf("update consumer order fail")
                }
            }
        }

        return nil
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "CheckRoundEndHandle Transaction err:%v", err)
        return err
    }

    // 获取锁
    ok, err := m.cache.LockUpdateChairGameInfo(ctx, cid, 5*time.Second)
    if err != nil || !ok {
        log.DebugWithCtx(ctx, "CheckRoundEndHandle LockUpdateChairGameInfo ok:%v err:%v", ok, err)
        return fmt.Errorf("lock fail")
    }

    defer func() {
        // 解锁
        _ = m.cache.UnlockUpdateChairGameInfo(ctx, cid)
    }()

    info, err := m.cache.GetChairGameInfo(ctx, cid)
    if err != nil {
        log.ErrorWithCtx(ctx, "CheckRoundEndHandle GetChairGameInfo err:%v", err)
        return err
    }

    if info.GameOverTime > 0 {
        return nil
    }

    info.GameOverTime = now.UnixMilli()
    _ = m.cache.SetChairGameInfo(ctx, info)

    settleInfo.GameResult = consumerOrderNewStatus
    _ = m.cache.AddChairGameSettleQueue(ctx, settleInfo, now.Unix())

    GoroutineWithTimeoutCtx(ctx, 8*time.Second, func(ctx context.Context) {
        // 推送
        err = m.acLayerMgr.ChairGameInfoChannelNotify(ctx, cid, m.genPushGameInfoPb(ctx, cid, false, false))
        if err != nil {
            log.ErrorWithCtx(ctx, "CheckRoundEndHandle ChairGameInfoChannelNotifyy err:%v", err)
        }

        time.Sleep(3 * time.Second)
        // 延迟3秒下麦
        kickUidList := make([]uint32, 0)
        for _, v := range gameInfo.GamePlayers {
            if v.Uid != 0 {
                kickUidList = append(kickUidList, v.Uid)
            }
        }
        // 踢玩家下麦
        _ = m.acLayerMgr.KickOutMicSpace(ctx, gameInfo.Cid, kickUidList)
    })

    log.InfoWithCtx(ctx, "CheckRoundEndHandle gameInfo:%+v", gameInfo)
    return nil
}

// ReissueAwardHandle 奖励补发handle
func (m *Mgr) ReissueAwardHandle() {
    ctx, cancel := context.WithTimeout(protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{RequestID: uuid.New().String()}), 20*time.Second)
    defer cancel()

    reissueTime := time.Now().Add(-1 * time.Minute)
    // 最早的检查时间
    timeAfter := time.Now().Add(-time.Duration(m.bc.GetReissueMaxIntervalHour()) * time.Hour)

    tblTime := time.Date(reissueTime.Year(), reissueTime.Month(), 1, 0, 0, 0, 0, time.Local)
    // 最早的tblTime
    earliestTblTime := time.Date(timeAfter.Year(), timeAfter.Month(), 1, 0, 0, 0, 0, time.Local)

    awardList := make([]*store.AwardRecord, 0)
    for {
        if tblTime.Before(earliestTblTime) {
            break
        }
        // 获取待补发的奖励列表
        list, err := m.store.GetAwardRecordByStatus(ctx, tblTime, store.AwardStatusInit, ReissueAwardBatCount, timeAfter, reissueTime)
        if err != nil {
            log.ErrorWithCtx(ctx, "ReissueAwardHandle fail to GetAwardRecordByStatus. err:%v", err)
            return
        }

        awardList = append(awardList, list...)
        if len(list) >= ReissueAwardBatCount {
            break
        }

        // 本月数据不够，取上个月
        tblTime = tblTime.AddDate(0, -1, 0)
    }

    // 补发奖励
    for _, v := range awardList {
        _ = m.sendGift(ctx, v, 0, "")
    }
}

func (m *Mgr) sendGift(ctx context.Context, v *store.AwardRecord, payUid uint32, dealToken string) error {
    var err error
    gameInfo := &store.GameLog{}
    gameInfo, err = m.store.GetGameLogByGameId(ctx, v.GameId)
    if err != nil || gameInfo == nil {
        log.ErrorWithCtx(ctx, "ReissueAwardHandle gameInfo fail.gameInfo:%+v err:%v", gameInfo, err)
        return err
    }
    switch v.GiftType {
    case conf.GiftTypePackage:
        // 发包裹
        err = m.acLayerMgr.AwardPackage(ctx, v.Uid, stringId2Uint32(v.GiftID), v.Amount, v.AwardTime.Unix(), v.OrderID)

    case conf.GiftTypeNormal:

        if v.GiftWorth > 0 {

            if dealToken == "" {
                // commit
                payUid, dealToken, err = m.chairGameAwardMgr.ConsumeCommit(ctx, gameInfo.PayOrderID)
                if err != nil {
                    log.ErrorWithCtx(ctx, "ReissueAwardHandle fail to ChairGameSettleConsumeHandle. err:%v", err)
                    return err
                }
            }
        }
        // 发普通礼物
        err = m.acLayerMgr.SendCommonShelfPresent(ctx, &conf.SendPresentReq{
            FromUid:   payUid,
            FromCid:   gameInfo.Cid,
            ToUid:     v.Uid,
            GiftId:    stringId2Uint32(v.GiftID),
            Price:     v.GiftWorth,
            Amount:    v.Amount,
            OrderId:   v.OrderID,
            DealToken: dealToken,
            AwardTs:   v.AwardTime.Unix(),
        })

    case conf.GiftTypeMagicSpirit:
        if dealToken == "" {
            // commit
            payUid, dealToken, err = m.chairGameAwardMgr.ConsumeCommit(ctx, gameInfo.PayOrderID)
            if err != nil {
                log.ErrorWithCtx(ctx, "ReissueAwardHandle fail to ChairGameSettleConsumeHandle. err:%v", err)
                return err
            }
        }

        // 发放幸运礼物
        err = m.acLayerMgr.SendMagicSpiritGiftToUser(ctx, &conf.SendPresentReq{
            FromUid:   payUid,
            FromCid:   gameInfo.Cid,
            ToUid:     v.Uid,
            GiftId:    stringId2Uint32(v.GiftID),
            Price:     v.GiftWorth,
            Amount:    v.Amount,
            OrderId:   v.OrderID,
            DealToken: dealToken,
            AwardTs:   v.AwardTime.Unix(),
        }, dealToken)

    default:
        log.ErrorWithCtx(ctx, "ReissueAwardHandle 不支持的giftType:%+v", v)
        return fmt.Errorf("unspported giftType")
    }

    if err != nil {
        log.ErrorWithCtx(ctx, "ReissueAwardHandle fail to UpdateOrderWithGameId. err:%v", err)
        return err
    }
    // 更新发奖状态
    _ = m.store.UpdateAwardStatus(ctx, v.AwardTime, v.OrderID, store.AwardStatusSuccess)
    return nil
}

func stringId2Uint32(strId string) uint32 {
    id, err := strconv.ParseUint(strId, 10, 32)
    if err != nil {
        log.ErrorWithCtx(context.Background(), "stringId2Uint32 fail to ParseUint. err:%v", err)
        return 0
    }
    return uint32(id)
}

func (m *Mgr) timeoutGameHandle() {
    ctx, cancel := context.WithTimeout(protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{RequestID: uuid.New().String()}), 5*time.Second)
    defer cancel()

    ok, err := m.cache.LockTimer(ctx, "timeoutGameHandle", 6*time.Second)
    if err != nil {
        log.ErrorWithCtx(ctx, "timeoutGameHandle fail to LockTimer. err:%v", err)
        return
    }
    if !ok {
        return
    }
    defer func() {
        _ = m.cache.UnlockTimer(ctx, "timeoutGameHandle")
    }()

    beginTime := time.Now().Add(-40 * time.Minute)
    endTime := time.Now().Add(-30 * time.Minute)
    // 查半小时前还未结束的游戏
    gameList, err := m.store.GetPendingGameLogByCTime(ctx, beginTime, endTime)
    if err != nil {
        log.Errorf("timeoutGameHandle GetPendingGameLogByCTime err:%v", err)
        return
    }

    for _, game := range gameList {
        log.InfoWithCtx(ctx, "timeoutGameHandle game:%+v", game)
        err = m.pendingTimeoutGameCheck(ctx, game)
        if err != nil {
            return
        }

        err = m.store.Transaction(ctx, func(tx mysql.Txx) error {
            _, err = m.store.UpdateGameLogStatus(ctx, tx, game.ID, conf.GameLogStatusTimeOut)

            return err
        })
        if err != nil {
            log.Errorf("timeoutGameHandle UpdateGameLogStatus err:%v", err)
            return
        }
    }
}

func (m *Mgr) pendingTimeoutGameCheck(ctx context.Context, game *store.GameLog) error {
    // 获取锁
    ok, err := m.cache.LockUpdateChairGameInfo(ctx, game.Cid, 5*time.Second)
    if err != nil || !ok {
        log.DebugWithCtx(ctx, "roundGrabEndHandle LockUpdateChairGameInfo ok:%v err:%v", ok, err)
        return fmt.Errorf("lock fail")
    }

    defer func() {
        // 解锁
        _ = m.cache.UnlockUpdateChairGameInfo(ctx, game.Cid)
    }()
    // 获取游戏信息
    gameInfo, err := m.cache.GetChairGameInfo(ctx, game.Cid)
    if err != nil {
        log.Errorf("timeoutGameHandle GetChairGameInfo err:%v", err)
        return err
    }

    if gameInfo == nil || gameInfo.GameId != game.ID || gameInfo.GameOverTime > 0 {
        log.InfoWithCtx(ctx, "timeoutGameHandle cid:%d, uid:%d GetChairGameInfo not exist", game.Cid, game.ID)
        return nil
    }

    gameInfo.GameOverTime = time.Now().UnixMilli()

    // 更新游戏信息
    _ = m.cache.SetChairGameInfo(ctx, gameInfo)
    return nil
}

func GoroutineWithTimeoutCtx(ctx context.Context, timeout time.Duration, fn func(ctx context.Context)) {
    timeoutCtx, cancel := protogrpc.NewContextWithInfoTimeout(ctx, timeout)
    go func() {
        defer cancel()
        fn(timeoutCtx)
    }()
}
