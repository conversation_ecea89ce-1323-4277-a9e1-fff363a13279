package conf

import (
	"context"
	"encoding/json"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	config "golang.52tt.com/pkg/dyconfig"
)

const contractConfFile = "/data/oss/conf-center/tt/anchorcontract-go.json"

type IConfDynamic interface {
	GetContractDyConf() *ContractDyConf
}

type confDynamic struct {
	dc *config.DynamicConfig
}

type ContractDyConf struct {
	CensoringSceneCode           string                           `json:"censoring_scene_code"`             // 审核场景码
	CensoringTimeoutTs           int                              `json:"censoring_timeout_ts"`             // 审核超时时间
	CensoringIgnoreResult        bool                             `json:"censoring_ignore"`                 // 审核忽略结果
	AutoAcceptCancelContractHour int                              `json:"auto_accept_cancel_contract_hour"` // 自动接受解约申请时间
	PayCancelContractParam       *PayCancelContractParam          `json:"pay_cancel_contract_param"`        //付费解约参数
	QuietCancelContractParam     *QuietCancelContractParam        `json:"quiet_cancel_contract_param"`      //沉默解约参数
	NoReasonCancelDays           uint32                           `json:"no_reason_cancel_days"`            //无理由解约天数
	MapType2CancelContractTypes  map[uint32][]*CancelContractType `json:"map_type_2_cancel_contract_types"`
	MapType2TimeDesc             map[uint32]string                `json:"map_type_2_time_desc"`         //解约时间描述
	CancelContractTypeEditTs     uint32                           `json:"cancel_contract_type_edit_ts"` // 解约类型编辑时间戳
	MapType2Worker               map[uint32]*ContractWorker       `json:"map_type_2_worker"`            //从业者类型
	PromoteInviteTsLimit         uint32                           `json:"promote_invite_ts_limit"`      // 晋升邀请时间限制
	PromoteInviteExpireTs        uint32                           `json:"promote_invite_expire_ts"`     // 晋升邀请过期时间
	MgrCancelBaseAmountRange     []uint32                         `json:"mgr_cancel_base_amount_range"` // 管理解约初始金额范围
	ObsConfig                    *ObsConfig                       `json:"obs_config"`                   // obs配置
	NegotiateCoolDownTime        uint32                           `json:"negotiate_cool_down_time"`     // 协商解约冷却期时间
	TranscodeTimeOut             int64                            `json:"transcode_time_out"`           //转码超时时间

	DefaultSignRightMap map[string]bool `json:"default_sign_right_list"` // 默认签约权益列表
}

type ObsConfig struct {
	Host  string `json:"host"`   // host
	AppId string `json:"app_id"` // app id
	Scope string `json:"scope"`  // scope
}

// 从业者类型
type ContractWorker struct {
	Name      string `json:"name"`       //从业者类型名称
	Sort      uint32 `json:"sort"`       //从业者类型排序
	SortRight uint32 `json:"sort_right"` //从业者类型权益排序
	Desc      string `json:"desc"`       //从业者类型描述
}

type CancelContractType struct {
	Type            uint32 `json:"type"`              //解约类型
	Name            string `json:"name"`              //解约类型名称
	Desc            string `json:"desc"`              //解约类型描述
	DescGuildManage string `json:"desc_guild_manage"` //公会管理端解约类型描述
	IsDefault       bool   `json:"is_default"`        //是否默认拥有
	VersionId       uint32 `json:"version_id"`        //版本号
}

type PayCancelContractParam struct {
	SignMonthLimit          uint32   `json:"sign_month_limit"`            //签约月限制
	SingleMonthIncomeLimit  uint32   `json:"single_month_income_limit"`   //单月收入限制
	NMonthLimit             uint32   `json:"n_month"`                     //连续N月收入限制
	NMonthIncomeLimit       uint32   `json:"n_month_income_limit"`        //连续N月收入限制
	FreezeSignTimeSec       int64    `json:"freeze_sign_time_sec"`        //冻结签约时间
	GuildMaxNum             uint32   `json:"guild_max_num"`               //公会付费解约最大数量每月
	CostAmountParam         []uint32 `json:"cost_amount_param"`           //付费解约金额参数[a,b,c,d,e]【根据从业者过去a天在公会旗下房间日均接档收礼金额*e/100*剩余签约自然月数*倍数（剩余签约自然月≤b个月的*c，剩余签约自然月＞b个月的*d）
	CostAmountLimit         []int64  `json:"cost_amount_limits"`          //付费解约金额上下限
	LockAmountExpireS       int64    `json:"lock_amount_expire_s"`        //锁定金额过期时间
	FreezeCancelSignTimeSec int64    `json:"freeze_cancel_sign_time_sec"` //冻结解约时间
	PayCheckMonth           uint32   `json:"pay_check_month"`             //付费解约审核月数
}

type QuietCancelContractParam struct {
	MonthIncome uint32 `json:"month_income_limit"` //
	NMonth      int    `json:"n_month"`            //连续N月收入限制
}

func (s *confDynamic) GetContractDyConf() *ContractDyConf {
	return s.dc.Get().(*ContractDyConf)
}

func NewDyConfig(ctx context.Context) (IConfDynamic, error) {
	dc, err := config.NewDynamicConfig(ctx, contractConfFile, &ContractDyConf{
		MapType2CancelContractTypes: make(map[uint32][]*CancelContractType),
	})
	if nil != err {
		log.ErrorWithCtx(ctx, "load cancel-contract.json failed, err: %+v", err)
		return nil, err
	}
	log.InfoWithCtx(ctx, "load cancel-contract.json dynamic config ok")

	cfd := &confDynamic{
		dc: dc,
	}
	log.InfoWithCtx(ctx, "NewDyConfig ok cacnelContract file:%+v", cfd.GetContractDyConf())
	return cfd, nil
}

func (r *ContractDyConf) UnmarshalBinary(data []byte) error {
	err := json.Unmarshal(data, r)
	if err != nil {
		return err
	}
	return nil
}

func (r *ContractDyConf) GetCensoringSceneCode() string {
	return r.CensoringSceneCode
}
