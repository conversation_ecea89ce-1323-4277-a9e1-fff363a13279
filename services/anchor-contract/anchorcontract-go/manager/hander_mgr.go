package manager

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/jinzhu/gorm"
	pb "golang.52tt.com/protocol/services/anchorcontract-go"
	"golang.52tt.com/services/anchor-contract/anchorcontract-go/cache"
	"golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql"
	"runtime/debug"
	"strconv"
	"time"

	"github.com/go-redis/redis"

	"golang.52tt.com/pkg/log"
)

func (m *AnchorContractMgr) ClearTimeOutCancelContractApplyHandle() {

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	defer func() {
		if err := recover(); err != nil {
			log.Errorf("ClearTimeOutCancelContractApplyHandle panic err:%v", err)
		}
	}()

	lockKey := "clear_timeout_cancel_contract_apply_lock"
	if !m.cache.Lock(lockKey, 30*time.Second) {
		return
	}
	defer m.cache.UnLock(lockKey)

	expireTime := m.CalculateCancelContractExpireTime(time.Now())

	for i := uint32(0); i < 100; i++ {
		m.CancelContractWhenApplyTimeOut(ctx, i, expireTime)
	}

	//log.Debugf("ClearTimeOutCancelContractApplyHandle done")
}

func (m *AnchorContractMgr) ClearTimeoutLiveAnchorExamine() {
	defer func() {
		if err := recover(); err != nil {
			log.Errorf("ClearTimeoutLiveAnchorExamine panic err:%v", err)
		}
	}()

	now := time.Now()
	expiredTime := now.AddDate(0, 0, -3)

	list, err := m.store.GetTimeoutLiveAnchorExamine(expiredTime, 0)
	if err != nil {
		log.Errorf("ClearTimeoutLiveAnchorExamine fail to GetTimeoutLiveAnchorExamine err:%v", err)
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	for _, examine := range list {
		// 回收权限
		_, serr := m.liveMgrCli.DelChannelLiveInfo(ctx, examine.Uid, "语音直播考核过期")
		if serr != nil {
			log.Errorf("ClearTimeoutLiveAnchorExamine fail to DelChannelLiveInfo err:%v", serr)
			continue
		}

		// 回收身份
		err = m.ReclaimAnchorIdentity(ctx, examine.Uid, examine.GuildId, uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE), "sys")
		if err != nil {
			log.Errorf("ClearTimeoutLiveAnchorExamine fail to ReclaimAnchorIdentity err:%v", err)
		}

		err = m.store.UpdateLiveAnchorExamineStatus(nil, examine.Id, uint32(pb.LiveAnchorExamine_ENUM_FAILED), "sys", "timeout")
		if err != nil {
			log.Errorf("ClearTimeoutLiveAnchorExamine fail to UpdateLiveAnchorExamineStatus err:%v", err)
		}

		content := "抱歉，您的语音直播考核未通过，系统将回收您的开播权限。若需了解考核要求，可前往签约流程阅读语音直播相关协议"
		_ = m.SendIMMsg(ctx, examine.Uid, content)

		log.Debugf("ClearTimeoutLiveAnchorExamine %v", examine)
	}
}

func (m *AnchorContractMgr) NotifyAnchorExamineCert() {
	defer func() {
		if err := recover(); err != nil {
			log.Errorf("timer.NotifyAnchorExamineCert panic err:%v, Stack: %s", err, string(debug.Stack()))
		}
	}()

	limit := uint32(200)

	lockKey := cache.AnchorCertLockPendinKey
	lock, err := m.cache.GetRedisClient().SetNX(lockKey, "1", time.Second*30).Result()
	if err != nil {
		log.Errorf("NotifyAnchorExamineCert failed to lock, err %v", err)
		return
	}
	if !lock {
		return
	}
	defer m.cache.GetRedisClient().Del(lockKey)

	readyQueue, err := m.PopNotifyPengingQueue()
	if err != nil {
		log.Errorf("NotifyAnchorExamineCert failed to PopNotifyPengingQueue err %v", err)
		return
	}
	log.Debugf("timer.NotifyAnchorExamineCert PopNotifyPengingQueue size:%d", len(readyQueue))
	if len(readyQueue) != 0 {
		pipe := m.cache.GetRedisClient().Pipeline()
		pipe.ZRem(cache.AnchorCertPendingKey, readyQueue)
		pipe.LPush(cache.AnchorCertReadyKey, readyQueue)
		_, err = pipe.Exec()
		log.Infof("timer.NotifyAnchorExamineCert PopNotifyPengingQueue err:%v, push_cnt:%d, readyQueue:%v", err, len(readyQueue), readyQueue)
	}

	now := time.Now()
	lock, err = m.cache.LockExamineReadyQueue(cache.AnchorCertLockExpireTs)
	if err != nil {
		log.Errorf("timer.NotifyAnchorExamineCert failed to cache.LockExamineReadyQueue err:%v", err)
		return
	}
	if !lock {
		log.Debugf("timer.NotifyAnchorExamineCert ready queue lock by other.")
		return
	}
	defer m.cache.UnLockExamineReadyQueue()

	msgs, err := m.cache.PopExamineNotifyReadyQueue(limit)
	if err != nil {
		log.Errorf("timer.NotifyAnchorExamineCert failed to cache.PopExamineNotifyReadyQueue err:%v", err)
		return
	}
	log.Debugf("timer.NotifyAnchorExamineCert PopExamineNotifyReadyQueue pop ready size:%d msgs:%v", len(msgs), msgs)
	if len(msgs) == 0 {
		return
	}
	log.Infof("timer.NotifyAnchorExamineCert got lock, pop ready size:%d,%v", len(msgs), msgs)

	recordIds := make([]uint32, 0, len(msgs))
	memberInfos := make([]*cache.PushExamineCertMsg, 0, len(msgs))
	for _, msg := range msgs {
		uid, recordId, notifyType := cache.ParseNotifyMember(msg)
		if uid == 0 || recordId == 0 || notifyType == 0 {
			log.Errorf("timer.NotifyAnchorExamineCert parse fail msg:%s", msg)
			continue
		}

		recordIds = append(recordIds, recordId)
		memberInfos = append(memberInfos, &cache.PushExamineCertMsg{
			Uid:        uid,
			RecordId:   recordId,
			NotifyType: notifyType,
		})
	}
	if len(recordIds) == 0 {
		log.Errorf("timer.NotifyAnchorExamineCert parse fail, got recordIds size eq 0")
		return
	}

	recordId2Info, err := m.store.BatchGetUserExamineCertByRecordId(recordIds)
	if err != nil {
		log.Errorf("timer.NotifyAnchorExamineCert failed to store.BatchGetUserExamineCertByRecordId err:%v", err)
		return
	}

	itemIds := []uint32{}
	for _, info := range recordId2Info {
		itemIds = append(itemIds, info.ItemId)
	}
	itemId2Info, err := m.store.BatchGetExamineCertByItemId(itemIds)
	if err != nil {
		log.Errorf("timer.NotifyAnchorExamineCert failed to store.BatchGetExamineCertByItemId err:%v", err)
		return
	}

	grantRecordIds := []uint32{}
	recycleRecordIds := []uint32{}
	for _, member := range memberInfos {
		recordInfo, ok := recordId2Info[member.RecordId]
		if !ok {
			log.Errorf("timer.NotifyAnchorExamineCert no find record, info %v", member.RecordId, member)
			continue
		}
		itemInfo, ok := itemId2Info[recordInfo.ItemId]
		if !ok {
			log.Errorf("timer.NotifyAnchorExamineCert no find item, info %v", recordInfo)
			continue
		}

		if member.NotifyType == cache.AnchorCertNotifyGrant {
			grantRecordIds = append(grantRecordIds, member.RecordId)
			err = handleRetry(3, func() error {
				err = m.ImPushCertGrant(member.Uid, recordInfo.IdentityType, recordInfo.ItemId, itemInfo.ItemName, recordInfo.StartTime, recordInfo.EndTime)
				if err != nil {
					log.Errorf("timer.NotifyAnchorExamineCert failed to ImPushCertGrant err:%v, uid:%d, recordId:%d", err, member.Uid, member.RecordId)
				}
				return err
			})
			log.Infof("timer.NotifyAnchorExamineCert grant done uid:%d, recordId:%d, identityType:%d, NotifyType:%d, err:%v",
				member.Uid, member.RecordId, recordInfo.IdentityType, member.NotifyType, err)
		} else if member.NotifyType == cache.AnchorCertNotifyRecycle {
			recycleRecordIds = append(recycleRecordIds, member.RecordId)
			err = handleRetry(3, func() error {
				err = m.ImPushCertOverDue(member.Uid, recordInfo.IdentityType, recordInfo.ItemId, itemInfo.ItemName)
				if err != nil {
					log.Errorf("timer.NotifyAnchorExamineCert failed to ImPushCertRecycle err:%v, uid:%d, recordId:%d", err, member.Uid, member.RecordId)
				}
				return err
			})
			log.Infof("timer.NotifyAnchorExamineCert recycle done uid:%d, recordId:%d, identityType:%d, NotifyType:%d, err:%v",
				member.Uid, member.RecordId, recordInfo.IdentityType, member.NotifyType, err)
		}
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	err = m.store.Transaction(ctx, func(tx *gorm.DB) error {
		err := m.store.BatchUpdatetUserExamineCertStatus(grantRecordIds, uint32(pb.UserExamineCert_Status_UserExamineCert_Status_Effect))
		if err != nil {
			log.Errorf("timer.NotifyAnchorExamineCert failed to store.BatchUpdatetUserExamineCertStatus err:%v, grantRecordIds:%v", err, grantRecordIds)
			return err
		}
		err = m.store.BatchUpdatetUserExamineCertStatus(recycleRecordIds, uint32(pb.UserExamineCert_Status_UserExamineCert_Status_Overdue))
		if err != nil {
			log.Errorf("timer.NotifyAnchorExamineCert failed to store.BatchUpdatetUserExamineCertStatus err:%v, recycleRecordIds:%v", err, recycleRecordIds)
		}
		return err
	})
	cancel()
	if err != nil {
		log.Errorf("timer.NotifyAnchorExamineCert fail to begin Transaction  err:%v", err)
	}

	err = m.cache.BatchDelUserExamineCert(memberInfos)
	if err != nil {
		log.Errorf("timer.NotifyAnchorExamineCert failed to cache.BatchDelUserExamineCert err:%v, memberInfos:%v", err, memberInfos)
	}

	err = m.cache.Publish()
	if err != nil {
		log.Errorf("timer.NotifyAnchorExamineCert failed to cache.Publish err:%v, memberInfos:%v", err, memberInfos)
	}
	log.Infof("timer.NotifyAnchorExamineCert all done, ready size:%d, cost:%s", len(msgs), time.Since(now))
}

func (m *AnchorContractMgr) NotifyOverdueAnchorExamine() {
	defer func() {
		if err := recover(); err != nil {
			log.Errorf("timer.NotifyOverdueAnchorExamine panic err:%v, Stack: %s", err, string(debug.Stack()))
		}
	}()

	msg := m.anchorCheckDyconfig.GetAnchorCheckImMsg().CheckNoUpload
	log.Infof("timer.NotifyOverdueAnchorExamine work now %s msg=%s", time.Now(), msg)
	for {
		uids, err := m.cache.PopOverdueNotifyQueue(time.Now().Unix()+10, 100)
		if err != nil {
			log.Errorf("timer.NotifyOverdueAnchorExamine failed to cache.PopOverdueNotifyQueue err:%v", err)
			return
		}

		log.Infof("timer.NotifyOverdueAnchorExamine pop uids %v", uids)
		if len(uids) == 0 {
			log.Debugf("timer.NotifyOverdueAnchorExamine pop eq 0")
			return
		}

		for _, uidStr := range uids {
			uid := uint32(0)
			fmt.Sscanf(uidStr, "%d", &uid)
			if uid == 0 {
				continue
			}

			// 加入语音主播考核未提交音频记录表
			err = m.store.AddAnchorExamineRecord(&mysql.AnchorExamineRecord{
				Uid:        uid,
				UpdateTime: time.Now(),
			})
			if err != nil {
				log.Errorf("OfficialAcceptApplySign fail to AddAnchorExamineRecord uid:%d, err:%+v", uid, err)
			}

			ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
			defer cancel()
			err = m.SendIMMsgWithJumpUrl(ctx, uid, msg.Content, msg.Highlight, msg.Url)
			log.Infof("timer.NotifyOverdueAnchorExamine uid %d err %v", uid, err)
		}
	}
}

func (m *AnchorContractMgr) NotifyAnchorExamine() {
	defer func() {
		if err := recover(); err != nil {
			log.Errorf("timer.NotifyAnchorExamine panic err:%v, Stack: %s", err, string(debug.Stack()))
		}
	}()

	msg := m.anchorCheckDyconfig.GetAnchorCheckImMsg().CheckLess16h
	log.Infof("timer.NotifyAnchorExamine work now %s msg=%s", time.Now(), msg)
	for {
		uids, err := m.cache.PopNotifyQueue(time.Now().Unix()+10, 100)
		if err != nil {
			log.Errorf("timer.NotifyAnchorExamine failed to cache.PopNotifyQueue err:%v", err)
			return
		}

		log.Infof("timer.NotifyAnchorExamine pop uids %v", uids)
		if len(uids) == 0 {
			log.Debugf("timer.NotifyAnchorExamine pop eq 0")
			return
		}

		for _, uidStr := range uids {
			uid := uint32(0)
			fmt.Sscanf(uidStr, "%d", &uid)
			if uid == 0 {
				continue
			}

			ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
			defer cancel()
			err = m.SendIMMsgWithJumpUrl(ctx, uid, msg.Content, msg.Highlight, msg.Url)
			log.Infof("timer.NotifyAnchorExamine uid %d err %v", uid, err)
		}
	}
}

func (m *AnchorContractMgr) PopNotifyPengingQueue() ([]string, error) {

	offset := uint32(0)
	limit := uint32(100)
	score0 := float64(0)
	recordIds := []uint32{}
	recordId2MemberInfo := map[uint32]string{}
loop:
	for {
		res, err := m.cache.GetRedisClient().ZRangeByScoreWithScores(cache.AnchorCertPendingKey,
			redis.ZRangeBy{
				Min: "0", Max: strconv.Itoa(int(time.Now().Unix())),
				Offset: int64(offset), Count: int64(limit),
			}).Result()
		if err != nil {
			log.Errorf("PopExamineNotifyPengingQueue cache.PopMemberWithScores err %v", err)
			return nil, err
		}
		if len(res) == 0 {
			break
		}
		log.Debugf("ZRangeByScoreWithScores %d, %v", len(res), res)

		if score0 == 0 {
			score0 = res[0].Score
		}
		for _, info := range res {
			member := info.Member.(string)
			score := info.Score
			if score != score0 {
				break loop
			}

			_, recordId, _ := cache.ParseNotifyMember(member)
			recordIds = append(recordIds, recordId)
			recordId2MemberInfo[recordId] = member
		}

		offset += uint32(len(res))
	}

	if len(recordIds) == 0 {
		return nil, nil
	}

	// resort
	recordInfos, err := m.store.BatchGetUserExamineCertByItemIds(recordIds)
	if err != nil {
		log.Errorf("PopExamineNotifyPengingQueue store.BatchGetUserExamineCertByItemIds err %v", err)
		return nil, err
	}

	readyQueue := []string{}
	for _, info := range recordInfos {
		readyQueue = append(readyQueue, recordId2MemberInfo[info.Id])
	}

	log.Debugf("PopExamineNotifyPengingQueue got score:%d, readyQueue:%v", int64(score0), readyQueue)
	return readyQueue, nil
}

func (m *AnchorContractMgr) AutoPassAnchorApplySign() {
	defer func() {
		if err := recover(); err != nil {
			log.Errorf("AutoPassAnchorApplySign handler crashed: %+v, stack: %s", err, string(debug.Stack()))
		}
	}()

	// 申请会在申请提交后的2小时后自动同意，并且保留处理记录，审核人字段显示“system”

	now := time.Now()
	releaseTs, preTime := m.dyConfig.GetAutoPassAnchorApplySignConf()
	checkTs := now.Add(-time.Duration(preTime) * time.Second)
	log.Infof("AutoPassAnchorApplySign releaseTs=%d checkTs=%v", releaseTs, checkTs)

	lockKey := "lock:auto_pass_official_anchor_apply"
	if !m.cache.Lock(lockKey, time.Minute*5) {
		return
	}
	defer func() {
		m.cache.UnLock(lockKey)
	}()

	// SELECT id,uid,guild_id,identity_type,apply_status,contract_duration,identity_num,apply_time,update_time,form_extra,extra,handler,remarks,conform_status FROM `tbl_contract_apply_v2`  WHERE (apply_status=1 and identity_type=1 and apply_time>'2023-06-16 15:40:20' and update_time<'2023-06-15 15:40:20') LIMIT 10 OFFSET 0
	list, err := m.store.GetOfficialApplySignAnchorTimeRange(releaseTs, uint32(checkTs.Unix()), 0, 10)
	if err != nil {
		log.Errorf("AutoPassAnchorApplySign GetOfficialApplySignAnchorTimeRange fail %v ", err)
		return
	}
	log.Infof("AutoPassAnchorApplySign total=%d", len(list))

	type Extra struct {
		TagId uint32 `json:"tag_id"`
	}

	for _, info := range list {
		e := &Extra{}
		json.Unmarshal([]byte(info.FormExtra), e)

		ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
		err = m.OfficialHandleApplySignV2(ctx,
			info.Uid,
			e.TagId,
			info.Id,
			info.GuildId,
			pb.SIGN_ANCHOR_IDENTITY(info.IdentityType),
			pb.HANDLE_SIGN_APPLY_OPR_HANDLE_SIGN_APPLY_OPR_ACCEPT,
			"system", "")
		cancel()
		if err != nil {
			log.Errorf("AutoPassAnchorApplySign OfficialHandleApplySignV2 fail %v, info=%+v", err, info)
			continue
		}
		log.Infof("AutoPassAnchorApplySign done id=%d uid=%d guildId=%d identityType=%d",
			info.Id, info.Uid, info.GuildId, info.IdentityType)
	}
}

func handleRetry(retryCnt uint32, f func() error) error {
	var err error
	for i := uint32(0); i < retryCnt; i++ {
		if err = f(); err == nil {
			return nil
		}
	}
	return err
}
