package manager

import (
	"context"
	"encoding/json"
	"fmt"
	"golang.52tt.com/pkg/marketid_helper"
	"sort"
	"strconv"
	"strings"
	"time"

	mysql2 "github.com/go-sql-driver/mysql"
	"github.com/jinzhu/gorm"
	"golang.52tt.com/clients/account"
	"golang.52tt.com/pkg/foundation/utils"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/mapreduce"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	accountPB "golang.52tt.com/protocol/services/accountsvr"
	pb "golang.52tt.com/protocol/services/anchorcontract-go"
	channelLiveStatsPB "golang.52tt.com/protocol/services/channel-live-stats"
	"golang.52tt.com/protocol/services/channellivemgr"
	signAnchorStatsPB "golang.52tt.com/protocol/services/sign-anchor-stats"
	"golang.52tt.com/services/anchor-contract/anchorcontract-go/conf"
	"golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql"
)

func (m *AnchorContractMgr) GetGuildApplySignRecordList(ctx context.Context, in *pb.GetGuildApplySignRecordListReq) (*pb.GetGuildApplySignRecordListResp, error) {
	out := &pb.GetGuildApplySignRecordListResp{}

	queryType := in.GetQueryType()
	guildId := in.GetGuildId()
	uidList := in.GetUidList()
	page := in.GetPage()
	pageNum := in.GetPageNum()

	// 查公会审批主播限额状态
	audioLimitStatus, _, _, _, err := m.GetGuildSignAnchorLimitStatus(ctx, guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildApplySignRecordList GetGuildSignAnchorLimitStatus fail in:%+v, err:%v", in, err)
		return out, nil
	}
	out.AudioLimitStatus = audioLimitStatus
	log.DebugfWithCtx(ctx, "GetGuildSignAnchorLimitStatus guildId=%d audioLimitStatu=%d", guildId, audioLimitStatus)

	total, list, err := m.store.GetGuildApplySignRecordList(queryType, guildId, uidList, page*pageNum, pageNum)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildApplySignRecordList fail in:%+v, err:%v", in, err)
		return out, nil
	}
	if total == 0 {
		log.InfoWithCtx(ctx, "GetGuildApplySignRecordList got eq 0, in %+v, uids %v", in, uidList)
		return out, nil
	}

	applyuids := []uint32{}
	for _, info := range list {
		applyuids = append(applyuids, info.Uid)
	}

	uid2UserInfo, err := m.accountCli.GetUsersMap(ctx, applyuids)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildApplySignRecordList fail accountCli.GetUsersMap in:%+v, err:%v", in, err)
		return out, nil
	}

	// 用签约申请的相反身份 查询合约信息, 判断是不是加新身份
	uid2ContractInfo, err := m.BatchGetUserContractCacheInfo(ctx, &pb.BatchGetUserContractCacheInfoReq{Uids: applyuids})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildApplySignRecordList fail BatchGetUserContractCacheInfo in:%+v, err:%v", in, err)
		return out, nil
	}

	for _, info := range list {

		guildOpSstatus, officialOpSstatus := conventStatus(info.ApplyStatus)
		applyInfo := &pb.ApplySignExtInfo{
			ApplyId:          info.Id,
			AnchorUid:        info.Uid,
			AnchorAccount:    uid2UserInfo[info.Uid].GetUsername(),
			AnchorTtid:       uid2UserInfo[info.Uid].GetAlias(),
			AnchorNickname:   uid2UserInfo[info.Uid].GetNickname(),
			AnchorSex:        uint32(uid2UserInfo[info.Uid].GetSex()),
			GuildId:          info.GuildId,
			ApplyIdentity:    info.IdentityType,
			ApplyTime:        uint32(info.ApplyTime.Unix()),
			ContractDuration: info.ContractDuration,
			GuildOpStatus:    guildOpSstatus,
			OfficialOpStatus: officialOpSstatus,
			UpdateTime:       uint32(info.UpdateTime.Unix()),
		}

		// 可能是申请新身份
		if contractInfo, ok := uid2ContractInfo.GetUid2Contractinfo()[info.Uid]; ok && contractInfo.GetContract().GetGuildId() > 0 {
			applyInfo.ContractExpireTime = contractInfo.GetContract().GetExpireTime()
		}

		out.InfoList = append(out.InfoList, applyInfo)
	}

	out.Total = total

	log.DebugWithCtx(ctx, "GetGuildApplySignRecordList in %+v, out %s", in, utils.ToJson(out))
	return out, nil
}

func (m *AnchorContractMgr) GetGuildCancelSignRecordList(ctx context.Context, in *pb.GetGuildCancelSignRecordListReq) (*pb.GetGuildCancelSignRecordListResp, error) {
	out := &pb.GetGuildCancelSignRecordListResp{}

	queryType := in.GetQueryType()
	guildId := in.GetGuildId()
	uidList := in.GetUidList()
	page := in.GetPage()
	pageNum := in.GetPageNum()

	startTime := in.GetStartTime()
	endTime := in.GetEndTime()
	cancelType := in.GetCancelType()

	total, list, err := m.store.GetGuildCancelSignRecordList(queryType, guildId, uidList, page*pageNum, pageNum, startTime, endTime, cancelType)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildCancelSignRecordList fail in:%+v, err:%v", in, err)
		return out, nil
	}

	// 解约申请记录的第一页，要加入冻结状态的临时记录
	if in.GetQueryType() == uint32(pb.QUERY_CANCEL_SIGN_TYPE_QUERY_CANCEL_SIGN_TYPE_HISTORY) && page == 0 {
		tmpList, err := m.cache.GetTmpContractLog(ctx, guildId)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGuildCancelSignRecordList fail cache.GetTmpContractLog in:%+v, err:%v", in, err)
		}

		for _, info := range tmpList {
			// 过期的不考虑
			if info.ApplyTime.Add(time.Duration(m.newDyConf.GetContractDyConf().PayCancelContractParam.LockAmountExpireS) * time.Second).Before(time.Now()) {
				continue
			}

			// 考虑startTime / endTime / cancelType的条件
			if startTime > 0 && uint32(info.ApplyTime.Unix()) < startTime {
				continue
			}

			if endTime > 0 && uint32(info.ApplyTime.Unix()) > endTime {
				continue
			}

			if cancelType > 0 && info.CancelType != cancelType {
				continue
			}

			if len(in.GetUidList()) > 0 {
				for _, uid := range in.GetUidList() {
					if info.Uid == uid {
						list = append(list, info)
					}
				}
			} else {
				list = append(list, info)
			}
		}
	}

	// 按更新时间倒序
	sort.Slice(list, func(i, j int) bool {
		return list[i].UpdateTime.After(list[j].UpdateTime)
	})

	applyuids := []uint32{}
	for _, info := range list {
		applyuids = append(applyuids, info.Uid)
		if info.AgentUid > 0 {
			applyuids = append(applyuids, info.AgentUid)
		}
	}

	uid2UserInfo, err := m.accountCli.GetUsersMap(ctx, applyuids)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildCancelSignRecordList fail accountCli.GetUsersMap in:%+v, err:%v", in, err)
		return out, nil
	}

	allUrls := make([]string, 0)
	for _, info := range list {
		if len(info.ProofUrls) > 0 {
			allUrls = append(allUrls, strings.Split(info.ProofUrls, ",")...)
		}
		if len(info.ProofVideoUrls) > 0 {
			allUrls = append(allUrls, strings.Split(info.ProofVideoUrls, ",")...)
		}
	}
	allTargetUrls, err := m.CheckAndTransforUrls(ctx, allUrls)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildCancelSignRecordList CheckAndTransforUrls fail req: %v, err: %v", in, err)
		return out, err
	}

	for _, info := range list {
		proofList, err := m.tranProofKeyListToProofUrlList(ctx, info.ImageProofList, info.VideoProofList)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetCancelContractApplyList fail to tranProofKeyListToProofUrlList in:%+v, err:%v", in, err)
		}

		applyInfo := &pb.CancelSignExtInfo{
			ApplyId:             info.ApplyId,
			AnchorUid:           info.Uid,
			AnchorAccount:       uid2UserInfo[info.Uid].GetUsername(),
			AnchorTtid:          uid2UserInfo[info.Uid].GetAlias(),
			AnchorNickname:      uid2UserInfo[info.Uid].GetNickname(),
			AnchorSex:           uint32(uid2UserInfo[info.Uid].GetSex()),
			GuildId:             info.GuildId,
			Identity:            0,
			TagName:             m.dyConfig.GetTagName(info.TagId),
			SignTime:            uint32(info.SignTime.Unix()),
			ContractExpireTime:  uint32(info.ContractExpireTime.Unix()),
			ApplyTime:           uint32(info.ApplyTime.Unix()),
			Reason:              info.Reason,
			Status:              info.Status,
			CancelTime:          uint32(info.UpdateTime.Unix()),
			CancelType:          info.CancelType,
			OfficialNote:        info.OfficialNote,
			UpdateTime:          uint32(info.UpdateTime.Unix()),
			RejectTxt:           info.RejectTxt,
			ProofUrls:           make([]string, 0),
			ProofVideoUrls:      make([]string, 0),
			OfficialNotes:       info.OfficialNote,
			PayDesc:             info.PayDesc,
			PayAmount:           (info.PayAmount + 99) / 100,
			WorkerType:          info.WorkerType,
			ProofList:           proofList,
			CancelReason:        info.ReasonText,
			NegotiateReasonType: strings.Split(info.NegotiateReasonType, "_"),
		}
		if len(info.ProofUrls) > 0 {
			urls := strings.Split(info.ProofUrls, ",")
			for _, url := range urls {
				if targetUrl, ok := allTargetUrls[url]; ok {
					applyInfo.ProofUrls = append(applyInfo.ProofUrls, targetUrl)
				}
			}
		}
		if len(info.ProofVideoUrls) > 0 {
			urls := strings.Split(info.ProofVideoUrls, ",")
			for _, url := range urls {
				if targetUrl, ok := allTargetUrls[url]; ok {
					applyInfo.ProofVideoUrls = append(applyInfo.ProofVideoUrls, targetUrl)
				}
			}
		}

		if info.AgentUid > 0 {
			applyInfo.AgentUid = info.AgentUid
			applyInfo.AgentTtid = uid2UserInfo[info.AgentUid].GetAlias()
			applyInfo.AgentNickname = uid2UserInfo[info.AgentUid].GetNickname()
		}

		if info.LiveIdentityStatus == 1 {
			applyInfo.IdentityInfoList = append(applyInfo.IdentityInfoList, &pb.AnchorIdentityInfo{
				IdentityType: uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE),
				ObtainTime:   uint32(info.LiveObtainTime.Unix()),
			})
		}
		if info.MultiplayIdentityStatus == 1 {
			applyInfo.IdentityInfoList = append(applyInfo.IdentityInfoList, &pb.AnchorIdentityInfo{
				IdentityType: uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER),
				ObtainTime:   uint32(info.MultiplayObtainTime.Unix()),
			})
		}

		identityInfo := &mysql.IdentityInfo{}
		json.Unmarshal([]byte(info.IdentityInfo), identityInfo)
		log.Debugf("GetGuildCancelSignRecordList uid=%d identityInfo=%+v", info.Uid, identityInfo)
		for _, ident := range identityInfo.AnchorIdentityInfoList {
			if ident.IdentityType == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_E_SPORTS) {
				applyInfo.IdentityInfoList = append(applyInfo.IdentityInfoList, &pb.AnchorIdentityInfo{
					IdentityType: uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_E_SPORTS),
					ObtainTime:   ident.ObtainTime,
				})
			}
		}

		applyInfo.ReasonList = identityInfo.ReasonList
		if len(identityInfo.ReasonList) == 0 {
			applyInfo.ReasonList = append(applyInfo.ReasonList, applyInfo.Reason)
		}
		sort.Slice(applyInfo.ReasonList, func(i, j int) bool {
			return applyInfo.ReasonList[i] < applyInfo.ReasonList[j]
		})

		out.InfoList = append(out.InfoList, applyInfo)
	}

	out.Total = total

	log.DebugWithCtx(ctx, "GetGuildCancelSignRecordList in %+v, out %s", in, utils.ToJson(out))
	return out, nil
}

func (m *AnchorContractMgr) GetGuildAnchorExtInfoList(ctx context.Context,
	in *pb.GetGuildAnchorExtInfoListReq) (*pb.GetGuildAnchorExtInfoListResp, error) {
	now := time.Now()
	out := &pb.GetGuildAnchorExtInfoListResp{}
	queryType := in.GetQueryType()

	var (
		guildId       = in.GetGuildId()
		begin         = in.GetPage() * in.GetPageNum()
		limit         = in.GetPageNum()
		reqAnchorUids = in.GetAnchorUidList()

		total uint32
		list  []*mysql.ContractInfo
		err   error
	)

	if len(in.GetAgentUidList()) > 0 {
		// 反查经纪人的主播
		reqAnchorUids, err = m.store.GetAnchorUidByAgent(guildId, in.GetAgentUidList())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGuildAnchorExtInfoList fail store.GetAnchorUidByAgent in:%+v, err:%v", in, err)
			return out, err
		}
		if len(reqAnchorUids) == 0 {
			log.ErrorWithCtx(ctx, "GetGuildAnchorExtInfoList got AnchorUids eq 0, in:%+v, err:%v", in, err)
			return out, nil
		}
		log.DebugWithCtx(ctx, "GetGuildAnchorExtInfoList agentUids %v got AnchorUids %v", in.GetAgentUidList(), reqAnchorUids)
	}

	if in.GetIsAllAgent() {
		reqAnchorUids, err = m.store.GetAnchorUidWithAgent(guildId)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGuildAnchorExtInfoList fail store.GetAnchorUidWithAgent in:%+v, err:%v", in, err)
			return out, err
		}
		if len(reqAnchorUids) == 0 {
			log.ErrorWithCtx(ctx, "GetGuildAnchorExtInfoList AllAgent got AnchorUids eq 0, in:%+v, err:%v", in, err)
			return out, nil
		}
		log.DebugWithCtx(ctx, "GetGuildAnchorExtInfoList AllAgent got AnchorUids %v", reqAnchorUids)
	}

	if queryType == uint32(pb.QUERY_ANCHOR_TYPE_QUERY_ANCHOR_TYPE_ALL) ||
		queryType == uint32(pb.QUERY_ANCHOR_TYPE_QUERY_ANCHOR_TYPE_NEWSIGN) ||
		queryType == uint32(pb.QUERY_ANCHOR_TYPE_QUERY_ANCHOR_TYPE_READYEXT) {

		// 查表 tbl_contract，作区分
		total, list, err = m.store.GetGuildContractByQuery(queryType, guildId, reqAnchorUids, begin, limit)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGuildAnchorExtInfoList fail store.GetGuildContractByQuery in:%+v, err:%v", in, err)
			return out, err
		}
		if total == 0 || len(list) == 0 {
			log.InfoWithCtx(ctx, "GetGuildAnchorExtInfoList got GetGuildContractByQuery eq 0, in %+v", in)
			return out, nil
		}

	} else if queryType == uint32(pb.QUERY_ANCHOR_TYPE_QUERY_ANCHOR_TYPE_MYFOCUS) {

		// 查表 tbl_anchor_info_ext
		// 按签约时间降序排序
		focusAnchorTotal, focusAnchorUids, err := m.store.GetGuildAnchorExtInfo(guildId, reqAnchorUids, 1, begin, limit)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGuildAnchorExtInfoList fail store.GetGuildAnchorExtInfo in:%+v, err:%v", in, err)
			return out, err
		}
		if focusAnchorTotal == 0 || len(focusAnchorUids) == 0 {
			log.InfoWithCtx(ctx, "GetGuildAnchorExtInfoList got focusAnchorUids eq 0, in %+v", in)
			return out, nil
		}
		log.DebugWithCtx(ctx, "GetGuildAnchorExtInfoList focusAnchorUids total %d, list len %d, focusAnchorUids %v", focusAnchorTotal, len(focusAnchorUids), focusAnchorUids)

		total = focusAnchorTotal
		list, err = m.store.GetGuildContractByUids(guildId, focusAnchorUids)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGuildAnchorExtInfoList fail store.GetGuildContractByUids in:%+v, err:%v", in, err)
			return out, err
		}
		log.DebugWithCtx(ctx, "GetGuildAnchorExtInfoList focusAnchorUids total %d, list len %d", total, len(list))

	} else if queryType == uint32(pb.QUERY_ANCHOR_TYPE_QUERY_ANCHOR_TYPE_TODAYLIVE) {
		// 今日开播
		// 查表 tbl_live_anchor_daily_record
		todayLiveAnchorTotal, todayLiveAnchorUids, err := m.store.GetGuildLiveAnchorTodayLive(guildId, reqAnchorUids, begin, limit)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGuildAnchorExtInfoList fail store.GetGuildLiveAnchorTodayLive in:%+v, err:%v", in, err)
			return out, err
		}
		if todayLiveAnchorTotal == 0 || len(todayLiveAnchorUids) == 0 {
			log.InfoWithCtx(ctx, "GetGuildAnchorExtInfoList got focusAnchorUids eq 0, in %+v", in)
			return out, nil
		}

		total = todayLiveAnchorTotal
		list, err = m.store.GetGuildContractByUids(guildId, todayLiveAnchorUids)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGuildAnchorExtInfoList fail store.GetGuildContractByUids in:%+v, err:%v", in, err)
			return out, err
		}

	} else if queryType == uint32(pb.QUERY_ANCHOR_TYPE_QUERY_ANCHOR_TYPE_CANCEL) {
		// 解约预警
		// 签约用户获得多人互动签约成员身份后，签约期间近1个月（不包含当月）单月收益≤1000 元
		// 查表 tbl_anchorcontract_anchor_score_monthly_，查上月积分

		now := time.Now()
		readyCancelMultiTotal, readyAnchorUids, err := m.store.GetGuildAnchorLastMonthScoreLessLimit(guildId, reqAnchorUids, now.AddDate(0, -1, 0), begin, limit)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGuildAnchorExtInfoList fail store.GetGuildAnchorLastMonthScore in:%+v, err:%v", in, err)
			return out, err
		}
		if readyCancelMultiTotal == 0 || len(readyAnchorUids) == 0 {
			log.InfoWithCtx(ctx, "GetGuildAnchorExtInfoList got readyCancelMultiTotal eq 0, in %+v", in)
			return out, nil
		}

		total = readyCancelMultiTotal
		list, err = m.store.GetGuildContractByUids(guildId, readyAnchorUids)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGuildAnchorExtInfoList fail store.GetGuildContractByUids in:%+v, err:%v", in, err)
			return out, err
		}

	} else {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "invaild queryType")
	}

	log.DebugWithCtx(ctx, "GetGuildAnchorExtInfoList guild_id %d get base info >> total %d, %v", guildId, total, list)

	if total == 0 {
		log.InfoWithCtx(ctx, "GetGuildAnchorExtInfoList got eq 0, in %+v")
		return out, nil
	}

	// 返回的主播uid信息
	anchorUids := []uint32{}
	for _, info := range list {
		anchorUids = append(anchorUids, info.Uid)
	}

	// 取经纪人uid
	uid2AgentUid, agentUids, err := m.store.GetAnchorAgentUid(anchorUids)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildAnchorExtInfoList fail store.GetAnchorAgentUid in:%+v, err:%v", in, err)
		return out, err
	}
	allUids := []uint32{}
	allUids = append(allUids, anchorUids...)
	allUids = append(allUids, agentUids...)

	// 各个字段需要的信息
	var (
		uid2UserInfo            = map[uint32]*accountPB.UserResp{}
		uid2ExtInfo             = map[uint32]*mysql.AnchorExtInfo{}
		uid2IdentityInfo        = map[uint32][]*mysql.AnchorIdentity{}
		uid2AnchorBaseInfo      = map[uint32]*channelLiveStatsPB.AnchorBaseInfo{}
		uid2LiveAnchorTag       = map[uint32]uint32{}
		uid2VaildLiveInfo       = map[uint32]*vaildLiveInfo{}
		uid2VaildHoldDay        = map[uint32]uint32{}
		uid2ExtensionContract   = map[uint32]*mysql.ExtensionContract{}
		mapUid2InviteList       = map[uint32][]*mysql.PromoteInvite{}
		mapUid2WorkerTypeChange = map[uint32]bool{}
	)

	// 并发优化
	fillWiths := []func() error{}
	fillWiths = append(fillWiths,
		func() error {
			uid2UserInfo, err = getUserMap(ctx, m, allUids)
			return err
		},
		func() error {
			uid2ExtInfo, err = getAnchorExtInfo(ctx, m, anchorUids)
			return err
		},
		func() error {
			uid2IdentityInfo, err = getAnchorIdentity(ctx, m, anchorUids)
			return err
		},
		func() error {
			uid2AnchorBaseInfo, err = getAnchorLiveInfo(ctx, m, anchorUids)
			return err
		},
		func() error {
			uid2VaildLiveInfo, err = getAnchorDailyLiveInfo(ctx, m, guildId, anchorUids)
			return err
		},
		func() error {
			uid2LiveAnchorTag, err = getAnchorLiveTag(ctx, m, anchorUids)
			return err
		},
		func() error {
			uid2VaildHoldDay, err = genVaildHoldDay(ctx, m, guildId, anchorUids)
			return err
		},
		func() error {
			uid2ExtensionContract, err = genExtensionContract(ctx, m, guildId)
			return err
		},
		func() error {
			mapUid2InviteList, err = getAnchorPromoteInvite(ctx, m, guildId, anchorUids)
			return err
		},

		func() error {
			mapUid2WorkerTypeChange, err = batchCheckWorkerTypeChange(ctx, m, guildId, anchorUids)
			return err
		},
	)

	err = mapreduce.Finish(fillWiths...)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildAnchorExtInfoList fail in:%+v, err:%v", in, err)
		return out, err
	}

	out.Total = total
	for _, info := range list {
		msg := &pb.AnchorExtInfo{
			AnchorUid:          info.Uid,
			AnchorTtid:         uid2UserInfo[info.Uid].GetAlias(),
			AnchorAccount:      uid2UserInfo[info.Uid].GetUsername(),
			AnchorNickname:     uid2UserInfo[info.Uid].GetNickname(),
			AnchorSex:          uint32(uid2UserInfo[info.Uid].GetSex()),
			GuildId:            info.GuildId,
			SignTime:           uint32(info.SignTime.Unix()),
			ContractExpireTime: uint32(info.ContractExpireTime.Unix()),

			// tag
			TagName: m.dyConfig.GetTagName(uid2LiveAnchorTag[info.Uid]),

			// 最近开播
			LastLiveAt: uid2AnchorBaseInfo[info.Uid].GetLastLiveAt(),

			PracType:           info.WorkerType,
			PayAmount:          info.PayAmount,
			IsChangeWorkerType: !mapUid2WorkerTypeChange[info.Uid],
		}

		// 经纪人信息
		msg.AgentUid = uid2AgentUid[info.Uid]
		msg.AgentTtid = uid2UserInfo[msg.AgentUid].GetAlias()
		msg.AgentNickname = uid2UserInfo[msg.AgentUid].GetNickname()

		// 关注、备注
		if extInfo, ok := uid2ExtInfo[info.Uid]; ok {
			msg.IsFocus = extInfo.IsFocus == 1
			msg.Remark = extInfo.Remark
		}

		// 身份
		for _, identityInfo := range uid2IdentityInfo[info.Uid] {
			msg.IdentityInfoList = append(msg.IdentityInfoList, &pb.AnchorIdentityInfo{
				ActorUid:     identityInfo.Uid,
				IdentityType: identityInfo.IdentityType,
				GuildId:      identityInfo.GuildId,
				ObtainTime:   uint32(identityInfo.ObtainTime.Unix()),
			})

			if identityInfo.IdentityType == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER) && info.WorkerType != uint32(pb.ContractWorkerType_ContractWorkerType_Manager) {
				msg.InviteButton = uint32(pb.PromoteInviteButton_PromoteInviteButton_Display)
				for _, invite := range mapUid2InviteList[info.Uid] {
					if invite.ProcRes == uint32(pb.ProcPromoteInviteReq_ProcResType_Invalid) || (invite.ProcRes == uint32(pb.ProcPromoteInviteReq_ProcResType_NO_Agree) &&
						invite.UpdateTm.Unix()+int64(m.newDyConf.GetContractDyConf().PromoteInviteTsLimit) > time.Now().Unix()) {
						msg.InviteButton = uint32(pb.PromoteInviteButton_PromoteInviteButton_Gray)
						break
					}
				}
			}
		}

		// 有效开播
		if record, ok := uid2VaildLiveInfo[info.Uid]; ok {
			msg.Day30LiveHour = Decimal(float64(record.LiveMinutes) / float64(60))
			msg.Day30LiveValidDay = record.VaildDays
		}

		// 有效接档
		msg.Day30HoldValidDay = uid2VaildHoldDay[info.Uid]

		// 按钮
		// 续约按钮   检查续约表 是否已经发起续约
		if msg.ContractExpireTime <= uint32(time.Now().AddDate(0, 0, +30).Unix()) {
			if _, ok := uid2ExtensionContract[info.Uid]; !ok {
				msg.IsExtensionContract = true
			}
		}

		// 解约按钮 默认都可以解约
		msg.IsCancalContract = true

		out.InfoList = append(out.InfoList, msg)
	}

	log.DebugWithCtx(ctx, "GetGuildAnchorExtInfoList end cost %s in %s, out size %d %s", time.Since(now), utils.ToJson(in), len(out.InfoList), utils.ToJson(out))
	return out, nil
}

func (m *AnchorContractMgr) HandleFocusAnchor(ctx context.Context, in *pb.HandleFocusAnchorReq) (*pb.HandleFocusAnchorResp, error) {
	out := &pb.HandleFocusAnchorResp{}

	contract, exist, err := m.store.GetContractWithUid(in.GetAnchorUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleFocusAnchor fail to GetContractWithUid in:%+v, err:%v", in, err)
		return out, err
	}
	if !exist || contract.GuildId != in.GetGuildId() {
		log.ErrorWithCtx(ctx, "HandleFocusAnchor fail. in:%+v, err:contract is not found", in)
		return out, protocol.NewExactServerError(nil, status.ErrContractNonexist)
	}

	err = m.store.FocusAnchor(&mysql.AnchorExtInfo{
		Uid:                contract.Uid,
		GuildId:            contract.GuildId,
		SignTime:           contract.SignTime,
		ContractExpireTime: contract.ContractExpireTime,
		IsFocus:            in.GetHandleOpr(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleFocusAnchor fail to store.FocusAnchor in:%+v, err:%v", in, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "HandleFocusAnchor done in %+v", in)
	return out, nil
}

func (m *AnchorContractMgr) UpdateRemark(ctx context.Context, in *pb.UpdateRemarkReq) (*pb.UpdateRemarkResp, error) {
	out := &pb.UpdateRemarkResp{}

	contract, exist, err := m.store.GetContractWithUid(in.GetAnchorUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateRemark fail to GetContractWithUid in:%+v, err:%v", in, err)
		return out, err
	}
	if !exist || contract.GuildId != in.GetGuildId() {
		log.ErrorWithCtx(ctx, "UpdateRemark fail. in:%+v, err:contract is not found", in)
		return out, protocol.NewExactServerError(nil, status.ErrContractNonexist)
	}

	err = m.store.UpdateRemark(&mysql.AnchorExtInfo{
		Uid:                contract.Uid,
		GuildId:            contract.GuildId,
		SignTime:           contract.SignTime,
		ContractExpireTime: contract.ContractExpireTime,
		Remark:             in.GetRemark(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateRemark fail to store.FocusAnchor in:%+v, err:%v", in, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "UpdateRemark done in %+v", in)
	return out, nil
}

func (m *AnchorContractMgr) GuildExtensionContract(ctx context.Context, in *pb.GuildExtensionContractReq) (*pb.GuildExtensionContractResp, error) {
	out := &pb.GuildExtensionContractResp{}

	contract, exist, err := m.store.GetContractWithUid(in.GetAnchorUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GuildExtensionContract fail to GetContractWithUid in:%+v, err:%v", in, err)
		return out, err
	}
	if !exist || contract.GuildId != in.GetGuildId() {
		log.ErrorWithCtx(ctx, "GuildExtensionContract fail. in:%+v, err:contract is not found", in)
		return out, protocol.NewExactServerError(nil, status.ErrContractNonexist)
	}

	// 检查续约条件 签约到期时间小于等于30天
	if uint32(contract.ContractExpireTime.Unix()) > uint32(time.Now().AddDate(0, 0, +30).Unix()) {
		log.ErrorWithCtx(ctx, "GuildExtensionContract fail. in:%+v, err: %d %s", in, status.ErrContractExtensionCannot, status.CodeMessageMap[status.ErrContractExtensionCannot])
		return out, protocol.NewExactServerError(nil, status.ErrContractExtensionCannot)
	}

	guildResp, err := m.guildCli.GetGuild(ctx, in.GetGuildId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GuildExtensionContract fail to GetGuild in:%+v, err:%v", in, err)
		return out, err
	}

	err = m.store.AddExtensionContract(nil, &mysql.ExtensionContract{
		Uid:        in.GetAnchorUid(),
		GuildId:    in.GetGuildId(),
		ApplyTime:  time.Now(),
		UpdateTime: time.Now(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GuildExtensionContract fail store.AddExtensionContract. in:%+v, err: %v", in, err)
		if mysqlErr, ok := err.(*mysql2.MySQLError); ok && mysqlErr.Number == 1062 {
			return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "不可重复发起续约申请")
		}
		return out, err
	}

	guildId := in.GetGuildId()
	if guildResp.GetShortId() > 0 {
		guildId = guildResp.GetShortId()
	}

	msg := fmt.Sprintf("【%s】(ID:%d) 向你发来签约续签邀请，10 天内有效，请尽快处理哟~ 点击前往签约管理>", guildResp.GetName(), guildId)

	m.SendIMMsgWithJumpUrl(ctx, in.GetAnchorUid(), msg, "前往签约管理>", m.sc.GetSignMgrWebUrl())

	log.InfoWithCtx(ctx, "GuildExtensionContract done in %+v", in)
	return out, nil
}

func (m *AnchorContractMgr) BatchGuildExtensionContract(ctx context.Context, in *pb.BatchGuildExtensionContractReq) (*pb.BatchGuildExtensionContractResp, error) {
	out := &pb.BatchGuildExtensionContractResp{}

	now := time.Now()
	guildResp, err := m.guildCli.GetGuild(ctx, in.GetGuildId())
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGuildExtensionContract fail to GetGuild in:%+v, err:%v", in, err)
		return out, err
	}
	guildShortId := in.GetGuildId()
	if guildResp.GetShortId() > 0 {
		guildShortId = guildResp.GetShortId()
	}

	for _, anchorUid := range in.GetAnchorUidList() {
		contract, exist, err := m.store.GetContractWithUid(anchorUid)
		if err != nil {
			log.ErrorWithCtx(ctx, "BatchGuildExtensionContract fail to GetContractWithUid in:%+v, err:%v", in, err)
			return out, err
		}
		if !exist || contract.GuildId != in.GetGuildId() {
			log.ErrorWithCtx(ctx, "BatchGuildExtensionContract fail. anchorUid:%+v, err:contract is not found", anchorUid)
			continue
		}

		// 检查续约条件 签约到期时间小于等于30天
		if uint32(contract.ContractExpireTime.Unix()) > uint32(time.Now().AddDate(0, 0, +30).Unix()) {
			log.ErrorWithCtx(ctx, "BatchGuildExtensionContract fail. anchorUid:%+v, err: %d %s",
				anchorUid, status.ErrContractExtensionCannot, status.CodeMessageMap[status.ErrContractExtensionCannot])
			continue
		}

		err = m.store.Transaction(ctx, func(tx *gorm.DB) error {
			_, err = m.store.DelExtensionContract(tx, anchorUid, in.GetGuildId())
			if err != nil {
				return err
			}

			err = m.store.AddExtensionContract(tx, &mysql.ExtensionContract{
				Uid:        anchorUid,
				GuildId:    in.GetGuildId(),
				ApplyTime:  time.Now(),
				UpdateTime: time.Now(),
			})
			if err != nil {
				return err
			}
			return nil
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "BatchGuildExtensionContract fail to Transaction in:%+v, err:%v", in, err)
			return out, err
		}

		msg := fmt.Sprintf("【%s】(ID:%d) 向你发来签约续签邀请，10 天内有效，请尽快处理哟~ 点击前往签约管理>", guildResp.GetName(), guildShortId)
		m.SendIMMsgWithJumpUrl(ctx, anchorUid, msg, "前往签约管理>", m.sc.GetSignMgrWebUrl())
	}

	log.InfoWithCtx(ctx, "BatchGuildExtensionContract done cost %s in %+v", time.Since(now), in)
	return out, nil
}

var (
	getUserMap = func(ctx context.Context, m *AnchorContractMgr, uids []uint32) (map[uint32]*account.User, error) {
		uid2UserInfo, serr := m.accountCli.GetUsersMap(ctx, uids)
		if serr != nil {
			log.ErrorWithCtx(ctx, "fail accountCli.GetUsersMap uids:%+v, err:%v", uids, serr)
			return uid2UserInfo, serr
		}

		log.DebugWithCtx(ctx, "got getUserMap: %+v", uid2UserInfo)
		return uid2UserInfo, nil
	}

	// 查是否关注
	getAnchorExtInfo = func(ctx context.Context, m *AnchorContractMgr, anchorUids []uint32) (map[uint32]*mysql.AnchorExtInfo, error) {
		uid2ExtInfo, err := m.store.BatchGetAnchorExtInfo(anchorUids)
		if err != nil {
			log.ErrorWithCtx(ctx, "fail store.BatchGetAnchorExtInfo anchorUids:%+v, err:%v", anchorUids, err)
			return uid2ExtInfo, err
		}
		return uid2ExtInfo, nil
	}

	// 查身份
	getAnchorIdentity = func(ctx context.Context, m *AnchorContractMgr, anchorUids []uint32) (map[uint32][]*mysql.AnchorIdentity, error) {
		uid2IdentityInfo, err := m.store.BatchGetAnchorIdentity(anchorUids)
		if err != nil {
			log.ErrorWithCtx(ctx, "fail store.BatchGetAnchorIdentity anchorUids:%+v, err:%v", anchorUids, err)
			return uid2IdentityInfo, err
		}
		return uid2IdentityInfo, nil
	}

	// 查最近开播
	getAnchorLiveInfo = func(ctx context.Context, m *AnchorContractMgr, anchorUids []uint32) (map[uint32]*channelLiveStatsPB.AnchorBaseInfo, error) {

		resp, err := m.liveStatsCli.BatchGetAnchorBaseInfo(ctx, 0, anchorUids)
		if err != nil {
			log.ErrorWithCtx(ctx, "fail liveStatsCli.BatchGetAnchorBaseInfo anchorUids:%+v, err:%v", anchorUids, err)
			return nil, err
		}

		uid2AnchorBaseInfo := map[uint32]*channelLiveStatsPB.AnchorBaseInfo{}
		for _, info := range resp.GetInfoList() {
			uid2AnchorBaseInfo[info.Uid] = info
		}
		return uid2AnchorBaseInfo, nil
	}

	// 查有效开播
	getAnchorDailyLiveInfo = func(ctx context.Context, m *AnchorContractMgr, guildId uint32, anchorUids []uint32) (map[uint32]*vaildLiveInfo, error) {
		now := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), 0, 0, 0, 0, time.Local)
		begin := now.AddDate(0, 0, -30)
		dayilyRecordResp, err := m.liveStatsCli.BatchGetAnchorDailyRecord(ctx, guildId, 0, anchorUids, uint32(begin.Unix()), uint32(now.Unix()))
		if err != nil {
			log.ErrorWithCtx(ctx, "fail liveStatsCli.BatchGetAnchorDailyRecord guildId:%+v, begin %v, end %v, err:%v", guildId, begin, now, err)
			return nil, err
		}

		uid2VaildLiveInfo := map[uint32]*vaildLiveInfo{}
		for _, info := range dayilyRecordResp.GetList() {
			if record, ok := uid2VaildLiveInfo[info.Uid]; ok {
				record.LiveMinutes += info.LiveMinutes
				record.VaildDays += boolToNum(info.IsValidDay)
			} else {
				uid2VaildLiveInfo[info.Uid] = &vaildLiveInfo{
					LiveMinutes: info.LiveMinutes,
					VaildDays:   boolToNum(info.IsValidDay),
				}
			}
		}
		log.DebugWithCtx(ctx, "got liveStatsCli.BatchGetAnchorDailyRecord begin %v, end %v, map: %+v", begin, now, uid2VaildLiveInfo)
		return uid2VaildLiveInfo, nil
	}

	// 查开播tag
	getAnchorLiveTag = func(ctx context.Context, m *AnchorContractMgr, anchorUids []uint32) (map[uint32]uint32, error) {
		resp, err := m.liveMgrCli.GetAnchorByUidList(ctx, &channellivemgr.GetAnchorByUidListReq{UidList: anchorUids})
		if err != nil {
			log.ErrorWithCtx(ctx, "fail to GetAnchorByUidList anchorUids:%+v, err:%v", anchorUids, err)
			return nil, err
		}

		uid2LiveAnchorTag := map[uint32]uint32{}
		for _, info := range resp.GetAnchorList() {
			uid2LiveAnchorTag[info.Uid] = info.GetTagId()
		}
		return uid2LiveAnchorTag, nil
	}

	// 查有效接档
	genVaildHoldDay = func(ctx context.Context, m *AnchorContractMgr, guildId uint32, anchorUids []uint32) (map[uint32]uint32, error) {
		now := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), 0, 0, 0, 0, time.Local)
		begin := now.AddDate(0, 0, -30)
		resp, err := m.signanchorstatsCli.GetMultiAnchorDailyStatsListByGuildId(ctx, &signAnchorStatsPB.GetMultiAnchorDailyStatsListByGuildIdReq{
			GuildId:    guildId,
			AnchorUids: anchorUids,
			BeginTs:    uint32(begin.Unix()),
			EndTs:      uint32(time.Now().Unix()),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "fail to signanchorstatsCli.GetMultiAnchorDailyStatsListByGuildId anchorUids:%+v, err:%v", anchorUids, err)
			return nil, err
		}

		uid2VaildHoldDay := map[uint32]uint32{}
		for _, info := range resp.GetList() {
			if !info.GetIsValidDay() {
				continue
			}

			uid2VaildHoldDay[info.Uid]++
		}

		log.DebugWithCtx(ctx, "got signanchorstatsCli.GetMultiAnchorDailyStatsListByGuildId guildId %d, anchorUids %v begin %v, end %v, map: %+v",
			guildId, anchorUids, begin, now, uid2VaildHoldDay)
		return uid2VaildHoldDay, nil
	}

	// 查续约表
	genExtensionContract = func(ctx context.Context, m *AnchorContractMgr, guildId uint32) (map[uint32]*mysql.ExtensionContract, error) {
		uid2ExtensionContract, err := m.store.GetGuildAllExtensionContract(guildId)
		if err != nil {
			log.ErrorWithCtx(ctx, "fail to store.GetGuildAllExtensionContract guildId:%+v, err:%v", guildId, err)
			return uid2ExtensionContract, err
		}
		return uid2ExtensionContract, nil
	}

	// 获取主播邀请记录
	getAnchorPromoteInvite = func(ctx context.Context, m *AnchorContractMgr, guildId uint32, anchorUids []uint32) (map[uint32][]*mysql.PromoteInvite, error) {
		//只查最近一年的邀请记录
		tm := time.Now().AddDate(-1, 0, 0)
		inviteList, err := m.store.GetPromoteInviteList(ctx, guildId, anchorUids, 0, false, 0, tm)
		if err != nil {
			log.ErrorWithCtx(ctx, "fail to store.GetGuildPromoteInviteList guildId:%+v, anchorUids:%+v, err:%v", guildId, anchorUids, err)
			return nil, err
		}

		mapUid2InviteList := map[uint32][]*mysql.PromoteInvite{}
		for _, invite := range inviteList {
			if _, ok := mapUid2InviteList[invite.InvitedUid]; ok {
				mapUid2InviteList[invite.InvitedUid] = append(mapUid2InviteList[invite.InvitedUid], invite)
			} else {
				mapUid2InviteList[invite.InvitedUid] = []*mysql.PromoteInvite{invite}
			}
		}

		log.DebugWithCtx(ctx, "got store.GetGuildPromoteInviteList guildId %d, anchorUids %v, map: %+v tm:%v", guildId, anchorUids, mapUid2InviteList, tm)
		return mapUid2InviteList, nil
	}

	// 获取主播邀请记录
	batchCheckWorkerTypeChange = func(ctx context.Context, m *AnchorContractMgr, guildId uint32, anchorUids []uint32) (map[uint32]bool, error) {
		resp, err := m.BatchCheckWorkerTypeChange(ctx, guildId, anchorUids)
		if err != nil {
			log.ErrorWithCtx(ctx, "fail to BatchCheckWorkerTypeChange guildId:%+v, anchorUids:%+v, err:%v", guildId, anchorUids, err)
			return nil, err
		}

		log.DebugWithCtx(ctx, "got store.GetGuildPromoteInviteList guildId %d, anchorUids %v, map: %+v err:%v", guildId, anchorUids, resp, err)
		return resp, nil
	}
)

type vaildLiveInfo struct {
	VaildDays   uint32
	LiveMinutes uint32
}

func (c *vaildLiveInfo) String() string {
	return utils.ToJson(c)
}

var (
	boolToNum = func(b bool) uint32 {
		if b {
			return 1
		}
		return 0
	}

	// 转换成对公会后台的状态码
	conventStatus = func(opSstatus uint32) (guildOpSstatus uint32, officialOpSstatus uint32) {
		if opSstatus == uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_PRESIDENT_HANDLING) {
			return uint32(pb.APPLY_SIGN_GUILD_STATUS_APPLY_SIGN_GUILD_STATUS_PRESIDENT_HANDLING), uint32(pb.APPLY_SIGN_OFFICIAL_STATUS_APPLY_SIGN_OFFICIAL_STATUS_NONE)
		}
		if opSstatus == uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_OFFICIAL_HANDLING) {
			return uint32(pb.APPLY_SIGN_GUILD_STATUS_APPLY_SIGN_GUILD_STATUS_PRESIDENT_ACCEPT), uint32(pb.APPLY_SIGN_OFFICIAL_STATUS_APPLY_SIGN_OFFICIAL_STATUS_HANDLING)
		}
		if opSstatus == uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_PRESIDENT_REJECT) {
			return uint32(pb.APPLY_SIGN_GUILD_STATUS_APPLY_SIGN_GUILD_STATUS_PRESIDENT_REJECT), uint32(pb.APPLY_SIGN_OFFICIAL_STATUS_APPLY_SIGN_OFFICIAL_STATUS_NONE)
		}
		if opSstatus == uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_OFFICIAL_REJECT) {
			return uint32(pb.APPLY_SIGN_GUILD_STATUS_APPLY_SIGN_GUILD_STATUS_PRESIDENT_ACCEPT), uint32(pb.APPLY_SIGN_OFFICIAL_STATUS_APPLY_SIGN_OFFICIAL_STATUS_REJECT)
		}
		if opSstatus == uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_PASS) {
			return uint32(pb.APPLY_SIGN_GUILD_STATUS_APPLY_SIGN_GUILD_STATUS_PRESIDENT_ACCEPT), uint32(pb.APPLY_SIGN_OFFICIAL_STATUS_APPLY_SIGN_OFFICIAL_STATUS_ACCEPT)
		}
		return
	}

	Decimal = func(num float64) float64 {
		num, _ = strconv.ParseFloat(fmt.Sprintf("%.1f", num), 64)
		return num
	}
)

func (m *AnchorContractMgr) GetCancelContractTypeList(ctx context.Context, req *pb.GetCancelContractTypeListReq) (*pb.GetCancelContractTypeListResp, error) {
	resp := &pb.GetCancelContractTypeListResp{}

	info, err := m.store.GetGuildCancelContractType(ctx, req.GetGuildId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCancelContractTypeList fail to GetGuildCancelContractType in:%+v, err:%v", req, err)
		return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
	}

	pracCancelContractTypeList := make([]*pb.PracCancelContractTypeInfo, 0)
	if info.CancelContractType != nil {
		err = json.Unmarshal(info.CancelContractType, &pracCancelContractTypeList)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetCancelContractTypeList fail to Unmarshal in:%+v, err:%v", req, err)
		}
	}

	mapType2IsSelect := make(map[string]bool)
	mapType2UpdateTs := make(map[uint32]uint32)
	for _, pracCancelContractType := range pracCancelContractTypeList {
		mapType2UpdateTs[pracCancelContractType.PracType] = pracCancelContractType.UpdateTs
		for _, cancelContractTypeInfo := range pracCancelContractType.GetInfoList() {
			mapType2IsSelect[fmt.Sprintf("%d-%d-%d", pracCancelContractType.PracType, cancelContractTypeInfo.GetCancelType(), cancelContractTypeInfo.GetVersionId())] = true
		}
	}

	for k, v := range m.newDyConf.GetContractDyConf().MapType2CancelContractTypes {
		pracCancelContractTypeInfo := &pb.PracCancelContractTypeInfo{
			PracType:     k,
			InfoList:     make([]*pb.CancelContractTypeInfo, 0),
			UpdateTs:     mapType2UpdateTs[k],
			PracTypeDesc: m.newDyConf.GetContractDyConf().MapType2Worker[k].Name,
		}
		for _, cancelContractType := range v {
			if cancelContractType.IsDefault {
				pracCancelContractTypeInfo.InfoList = append(pracCancelContractTypeInfo.InfoList, &pb.CancelContractTypeInfo{
					CancelType:      cancelContractType.Type,
					Tilte:           cancelContractType.Name,
					Desc:            cancelContractType.Desc,
					DescGuildManage: cancelContractType.DescGuildManage,
					IsSelect:        true,
					IsDefault:       true,
				})
			} else {
				pracCancelContractTypeInfo.InfoList = append(pracCancelContractTypeInfo.InfoList, &pb.CancelContractTypeInfo{
					CancelType:      cancelContractType.Type,
					Tilte:           cancelContractType.Name,
					Desc:            cancelContractType.Desc,
					DescGuildManage: cancelContractType.DescGuildManage,
					IsSelect:        mapType2IsSelect[fmt.Sprintf("%d-%d-%d", k, cancelContractType.Type, cancelContractType.VersionId)],
				})
			}
		}

		if pracCancelContractTypeInfo.UpdateTs+m.newDyConf.GetContractDyConf().CancelContractTypeEditTs <= uint32(time.Now().Unix()) {
			pracCancelContractTypeInfo.IsCanEdit = true
			pracCancelContractTypeInfo.TimeDesc = m.newDyConf.GetContractDyConf().MapType2TimeDesc[1]
		} else {
			updateTm := time.Unix(int64(pracCancelContractTypeInfo.UpdateTs), 0)
			upDateTm := time.Date(updateTm.Year(), updateTm.Month(), updateTm.Day(), 0, 0, 0, 0, time.Local)
			tm := time.Unix(upDateTm.Unix()+int64(m.newDyConf.GetContractDyConf().CancelContractTypeEditTs), 0)

			pracCancelContractTypeInfo.TimeDesc = fmt.Sprintf(m.newDyConf.GetContractDyConf().MapType2TimeDesc[2], tm.Year(), tm.Month(), tm.Day())
		}

		resp.TypeList = append(resp.TypeList, pracCancelContractTypeInfo)
	}

	sort.Slice(resp.TypeList, func(i, j int) bool {
		return m.newDyConf.GetContractDyConf().MapType2Worker[resp.TypeList[i].PracType].Sort < m.newDyConf.GetContractDyConf().MapType2Worker[resp.TypeList[j].PracType].Sort
	})

	log.DebugWithCtx(ctx, "GetCancelContractTypeList in:%+v resp:%+v", req, resp)
	return resp, nil
}

func (m *AnchorContractMgr) CheckGuildHasCancelContractType(ctx context.Context, guildId, workerType, cancelType uint32) (bool, error) {
	resp, err := m.GetCancelContractTypeList(ctx, &pb.GetCancelContractTypeListReq{GuildId: guildId})
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckGuildHasCancelContractType fail to GetCancelContractTypeList in:%+v, err:%v", guildId, err)
		return false, err
	}

	for _, pracCancelContractType := range resp.GetTypeList() {
		if pracCancelContractType.PracType == workerType {
			for _, cancelContractTypeInfo := range pracCancelContractType.GetInfoList() {
				if cancelContractTypeInfo.GetIsSelect() && cancelContractTypeInfo.GetCancelType() == cancelType {
					return true, nil
				}
			}
		}
	}

	log.DebugWithCtx(ctx, "CheckGuildHasCancelContractType in:%+v resp:%+v", guildId, resp)
	return false, nil
}

func (m *AnchorContractMgr) SetGuildCancelContractType(ctx context.Context, req *pb.SetGuildCancelContractTypeReq) (*pb.SetGuildCancelContractTypeResp, error) {
	resp := &pb.SetGuildCancelContractTypeResp{}

	info, err := m.store.GetGuildCancelContractType(ctx, req.GetGuildId())
	if err != nil {
		log.ErrorWithCtx(ctx, "SetGuildCancelContractType fail to GetGuildCancelContractType in:%+v, err:%v", req, err)
		return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
	}

	pracCancelContractTypeList := make([]*pb.PracCancelContractTypeInfo, 0)
	if info.CancelContractType != nil {
		err = json.Unmarshal(info.CancelContractType, &pracCancelContractTypeList)
		if err != nil {
			log.ErrorWithCtx(ctx, "SetGuildCancelContractType fail to Unmarshal in:%+v, err:%v", req, err)
			return resp, err
		}
	}

	mapType2Info := make(map[uint32]*conf.CancelContractType)
	for _, cancelContractType := range m.newDyConf.GetContractDyConf().MapType2CancelContractTypes[req.GetPracType()] {
		mapType2Info[cancelContractType.Type] = cancelContractType
	}

	infoList := make([]*pb.CancelContractTypeInfo, 0)
	for _, cancelType := range req.GetCancelTypeList() {
		// 默认的不需要存储
		if !mapType2Info[cancelType].IsDefault {
			infoList = append(infoList, &pb.CancelContractTypeInfo{
				CancelType: cancelType,
				VersionId:  mapType2Info[cancelType].VersionId,
			})
		}
	}

	nowTm := time.Now()
	isExist := false
	for _, pracCancelContractType := range pracCancelContractTypeList {
		if pracCancelContractType.GetPracType() == req.GetPracType() {
			updateTm := time.Unix(int64(pracCancelContractType.UpdateTs), 0)
			upDateTm := time.Date(updateTm.Year(), updateTm.Month(), updateTm.Day(), 0, 0, 0, 0, time.Local)
			tm := time.Unix(upDateTm.Unix()+int64(m.newDyConf.GetContractDyConf().CancelContractTypeEditTs), 0)

			if pracCancelContractType.UpdateTs != 0 && (uint32(tm.Unix())+m.newDyConf.GetContractDyConf().CancelContractTypeEditTs > uint32(nowTm.Unix())) {
				log.ErrorWithCtx(ctx, "SetGuildCancelContractType fail to GetGuildCancelContractType in:%+v, pracCancelContractType:%v", req, pracCancelContractType)
				return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "不可编辑")
			}

			pracCancelContractType.InfoList = infoList
			isExist = true
			pracCancelContractType.UpdateTs = uint32(nowTm.Unix())
			break
		}
	}

	if !isExist {
		pracCancelContractTypeList = append(pracCancelContractTypeList, &pb.PracCancelContractTypeInfo{
			PracType: req.GetPracType(),
			InfoList: infoList,
			UpdateTs: uint32(nowTm.Unix()),
		})
	}

	byteList, err := json.Marshal(pracCancelContractTypeList)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetGuildCancelContractType fail to Marshal in:%+v, err:%v", req, err)
		return resp, err
	}

	err = m.store.UpdateGuildCancelContractType(ctx, &mysql.GuildCancelContractType{
		GuildID:            req.GetGuildId(),
		CancelContractType: byteList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SetGuildCancelContractType fail to UpdateGuildCancelContractType in:%+v, err:%v", req, err)
		return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
	}

	log.InfoWithCtx(ctx, "SetGuildCancelContractType in:%+v resp:%+v info:%v isZero:%v", req, resp, info, info.UpdateTm.IsZero())
	return resp, nil
}

func (m *AnchorContractMgr) InvitePromote(ctx context.Context, req *pb.InvitePromoteReq) (*pb.InvitePromoteResp, error) {
	resp := &pb.InvitePromoteResp{}

	contractList, err := m.store.GetGuildContractByUids(req.GetGuildId(), []uint32{req.GetInvitedUid()})
	if err != nil {
		log.ErrorWithCtx(ctx, "InvitePromote fail to GetGuildContractByUids in:%+v, err:%v", req, err)
		return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
	}

	if len(contractList) == 0 {
		log.ErrorWithCtx(ctx, "InvitePromote fail in:%+v, err: contract is not found", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "不能邀请，没有签约该公会")
	}

	for _, contract := range contractList {
		if contract.WorkerType == uint32(pb.ContractWorkerType_ContractWorkerType_Manager) {
			log.ErrorWithCtx(ctx, "InvitePromote fail req:%+v, err: 不能邀请主播", req)
			return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "不能邀请，已是管理员")
		}
	}

	if len(m.newDyConf.GetContractDyConf().MgrCancelBaseAmountRange) != 2 {
		log.ErrorWithCtx(ctx, "InvitePromote fail  req:%+v, err: 解约初始金额配置异常")
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "解约初始金额配置异常")
	}

	if req.GetCancelAmount() < m.newDyConf.GetContractDyConf().MgrCancelBaseAmountRange[0] || req.GetCancelAmount() > m.newDyConf.GetContractDyConf().MgrCancelBaseAmountRange[1] {
		log.ErrorWithCtx(ctx, "InvitePromote fail  req:%+v, err: 解约金额输入范围有误")
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "解约金额输入范围有误")
	}

	isCancelRunning, _, err := m.store.GetCancelContractApply(req.GetInvitedUid(), req.GetGuildId())
	if err != nil {
		log.ErrorWithCtx(ctx, "InvitePromote fail to GetCancelContractApply in:%+v, err:%v", req, err)
		return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
	}

	if isCancelRunning {
		log.ErrorWithCtx(ctx, "InvitePromote fail to GetCancelContractApply in:%+v, err: 正在解约", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "用户在解约流程中，无法晋升")
	}

	inviteList, err := m.store.GetPromoteInviteList(ctx, 0, []uint32{req.GetInvitedUid()}, 0, true, 0, time.Now().AddDate(-1, 0, 0))
	if err != nil {
		log.ErrorWithCtx(ctx, "InvitePromote fail to GetPromoteInviteList in:%+v, err:%v", req, err)
		return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
	}

	if len(inviteList) > 0 {
		log.ErrorWithCtx(ctx, "InvitePromote fail to GetPromoteInviteList in:%+v, err: 已有邀请", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "不能邀请，已有邀请")
	}

	err = m.store.AddPromoteInvite(ctx, &mysql.PromoteInvite{
		GuildID:      req.GetGuildId(),
		InvitedUid:   req.GetInvitedUid(),
		CancelAmount: req.GetCancelAmount(),
		SignMonths:   req.GetSignMonths(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "InvitePromote fail to AddPromoteInvite in:%+v, err:%v", req, err)
		return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
	}

	// 发im
	guildInfo, err := m.guildCli.GetGuild(ctx, req.GetGuildId())
	if err != nil {
		log.ErrorWithCtx(ctx, "InvitePromote fail to GetGuild in:%+v, err:%v", req, err)
		//return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
	} else {
		guildViewId := guildInfo.GetGuildId()
		if guildInfo.ShortId != 0 {
			guildViewId = guildInfo.ShortId
		}

		userOl, err := m.userOlCli.GetLastMobileOnlineInfo(ctx, req.GetInvitedUid())
		if err != nil {
			log.ErrorWithCtx(ctx, "InviteMemberChangeWorkerType GetLastMobileOnlineInfo fail uid:%d, err: %v", req.GetInvitedUid(), err)
		}

		jumpUrl := marketid_helper.Get("anchor_system", userOl.GetMarketId(), userOl.GetClientType())

		_ = m.SendFuWuHaoMsg(ctx, req.GetInvitedUid(), fmt.Sprintf("您好，你的公会【%s】id：%d，会长给您发来了晋升成为管理邀请，请您及时处理。48小时内未处理默认拒绝，邀请过期。查看详情", guildInfo.GetName(), guildViewId), "查看详情", jumpUrl)
	}
	log.DebugWithCtx(ctx, "InvitePromote in:%+v resp:%+v", req, resp)
	return resp, nil
}

func (m *AnchorContractMgr) ProcPromoteInvite(ctx context.Context, req *pb.ProcPromoteInviteReq) (*pb.ProcPromoteInviteResp, error) {
	resp := &pb.ProcPromoteInviteResp{}

	_, _, mapUid2Contract, err := m.store.GetContractByUids([]uint32{req.GetInvitedUid()}, 0, 1)
	if err != nil {
		log.ErrorWithCtx(ctx, "ProcPromoteInvite fail to GetContractByUids in:%+v, err:%v", req, err)
		return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
	}

	if mapUid2Contract[req.GetInvitedUid()].GuildId == 0 || mapUid2Contract[req.GetInvitedUid()].ContractExpireTime.Unix() < time.Now().Unix() {
		log.ErrorWithCtx(ctx, "ProcPromoteInvite fail to GetContractByUids in:%+v, err:没有签约该公会", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "没有签约该公会")
	}

	inviteList, err := m.store.GetPromoteInviteList(ctx, mapUid2Contract[req.GetInvitedUid()].GuildId, []uint32{req.GetInvitedUid()}, 0,
		true, req.GetId(), time.Now().AddDate(-1, 0, 0))
	if err != nil {
		log.ErrorWithCtx(ctx, "ProcPromoteInvite fail to GetPromoteInviteList in:%+v, err:%v", req, err)
		return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
	}

	if len(inviteList) != 1 {
		log.ErrorWithCtx(ctx, "ProcPromoteInvite fail to GetPromoteInviteList in:%+v, err: 邀请记录异常", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "公会没有邀请")
	}

	isCancelRunning, _, err := m.store.GetCancelContractApply(req.GetInvitedUid(), inviteList[0].GuildID)
	if err != nil {
		log.ErrorWithCtx(ctx, "ProcPromoteInvite fail to GetCancelContractApply in:%+v, err:%v", req, err)
		return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
	}

	if isCancelRunning {
		err = m.store.ProcPromoteInvite(ctx, req.GetId(), uint32(pb.ProcPromoteInviteReq_ProcResType_NO_Agree), req.GetInvitedUid(), "用户正在解约，无法晋升")
		if err != nil {
			log.ErrorWithCtx(ctx, "ProcPromoteInvite fail to ProcPromoteInvite in:%+v, err:%v", req, err)
		}

		log.ErrorWithCtx(ctx, "ProcPromoteInvite fail to GetCancelContractApply in:%+v, err: 正在解约", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "您已在解约流程中，无法晋升")
	}

	err = m.store.ProcPromoteInvite(ctx, req.GetId(), req.GetProcRes(), req.GetInvitedUid(), "用户")
	if err != nil {
		log.ErrorWithCtx(ctx, "ProcPromoteInvite fail to ProcPromoteInvite in:%+v, err:%v", req, err)
		return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
	}

	if req.GetProcRes() == uint32(pb.ProcPromoteInviteReq_ProcResType_Agree) {
		err = m.PromoteContract(ctx, inviteList[0].InvitedUid, inviteList[0].GuildID, inviteList[0].SignMonths, int64(inviteList[0].CancelAmount*100))
		if err != nil {
			log.ErrorWithCtx(ctx, "ProcPromoteInvite fail to UpdateContractWorkerType in:%+v, err:%v", req, err)
		}

		// 发im
		guildInfo, err := m.guildCli.GetGuild(ctx, inviteList[0].GuildID)
		if err != nil {
			log.ErrorWithCtx(ctx, "ProcPromoteInvite fail to GetGuild in:%+v, err:%v", req, err)
			//return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
		} else {
			guildViewId := guildInfo.GetGuildId()
			if guildInfo.ShortId != 0 {
				guildViewId = guildInfo.ShortId
			}

			_ = m.SendFuWuHaoMsg(ctx, req.GetInvitedUid(), fmt.Sprintf("您好，您已成功晋升为【%s】id：%d的核心管理人员", guildInfo.GetName(), guildViewId), "", "")
		}
	}

	log.DebugWithCtx(ctx, "ProcPromoteInvite in:%+v resp:%v", req, resp)
	return resp, nil
}

func (m *AnchorContractMgr) GetUserPromoteInviteInfo(ctx context.Context, req *pb.GetUserPromoteInviteInfoReq) (*pb.GetUserPromoteInviteInfoResp, error) {
	resp := &pb.GetUserPromoteInviteInfoResp{}

	inviteList, err := m.store.GetPromoteInviteList(ctx, req.GetGuildId(), []uint32{req.GetUid()}, 0, true, 0, time.Now().AddDate(-1, 0, 0))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserPromoteInviteInfo fail to GetPromoteInviteList in:%+v, err:%v", req, err)
		return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
	}

	if len(inviteList) != 1 {
		log.ErrorWithCtx(ctx, "GetUserPromoteInviteInfo fail to GetPromoteInviteList in:%+v, err: 邀请记录异常", req)
		return resp, nil
	}

	resp.Info = &pb.PromoteInfo{
		Id:           inviteList[0].Id,
		SignMonths:   inviteList[0].SignMonths,
		CancelAmount: inviteList[0].CancelAmount,
	}

	log.DebugWithCtx(ctx, "GetUserPromoteInviteInfo in:%+v", req)
	return resp, nil
}

// 定时处理晋升邀请
func (m *AnchorContractMgr) TimerProcPromoteInvite() {
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*3)
	defer cancel()

	inviteList, err := m.store.GetPromoteInviteList(ctx, 0, []uint32{}, 0, true, 0, time.Now().AddDate(-1, 0, 0))
	if err != nil {
		log.ErrorWithCtx(ctx, "TimerProcPromoteInvite fail to GetPromoteInviteList in:%+v, err:%v", nil, err)
		return
	}

	if len(inviteList) == 0 {
		log.InfoWithCtx(ctx, "TimerProcPromoteInvite no invite")
		return
	}

	nowTm := time.Now()
	for _, invite := range inviteList {
		if invite.CreateTm.Unix()+int64(m.newDyConf.GetContractDyConf().PromoteInviteExpireTs) < nowTm.Unix() {
			//默认拒绝
			err = m.store.ProcPromoteInvite(ctx, invite.Id, uint32(pb.ProcPromoteInviteReq_ProcResType_NO_Agree), invite.InvitedUid, "系统")
			if err != nil {
				log.ErrorWithCtx(ctx, "TimerProcPromoteInvite fail to ProcPromoteInvite in:%+v, err:%v", invite, err)
			}

			log.InfoWithCtx(ctx, "TimerProcPromoteInvite invite:%+v, expire", invite)
		}
	}

	log.InfoWithCtx(ctx, "TimerProcPromoteInvite done")
}
