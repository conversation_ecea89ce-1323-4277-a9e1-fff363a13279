package manager

import (
	"context"
	"fmt"
	"golang.52tt.com/clients/banuser"
	censoring_proxy "golang.52tt.com/clients/censoring-proxy"
	"golang.52tt.com/clients/obsgateway"
	"golang.52tt.com/clients/public"
	sms_go "golang.52tt.com/clients/sms-go"
	userOL "golang.52tt.com/clients/user-online"
	"golang.52tt.com/pkg/audit"
	eventlink "golang.52tt.com/pkg/event-link-wrap"
	context0 "golang.org/x/net/context"
	"strconv"
	"strings"
	"time"

	"github.com/go-redis/redis"
	"github.com/jinzhu/gorm"
	accountSvr "gitlab.ttyuyin.com/bizFund/bizFund/pkg/account"
	"golang.52tt.com/clients/account"
	anchorcheck "golang.52tt.com/clients/anchor-check"
	anchor_level "golang.52tt.com/clients/anchor-level"
	"golang.52tt.com/clients/anti"
	apicenter_go "golang.52tt.com/clients/apicenter-go"
	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	"golang.52tt.com/clients/channel"
	channellivemgr "golang.52tt.com/clients/channel-live-mgr"
	channellivestats "golang.52tt.com/clients/channel-live-stats"
	"golang.52tt.com/clients/entertainmentrecommendback"
	esport_role "golang.52tt.com/clients/esport-role"
	exchange "golang.52tt.com/clients/exchange"
	"golang.52tt.com/clients/golddiamonn"
	"golang.52tt.com/clients/greenbaba"
	"golang.52tt.com/clients/guild"
	guild_cooperation "golang.52tt.com/clients/guild-cooperation"
	guild_management_svr "golang.52tt.com/clients/guild-management-svr"
	im_api "golang.52tt.com/clients/im-api"
	"golang.52tt.com/clients/rcmd/operating_platform"
	signanchorstats "golang.52tt.com/clients/sign-anchor-stats"
	ttc_proxy "golang.52tt.com/clients/ttc-proxy"
	user_auth_history "golang.52tt.com/clients/user-auth-history"
	"golang.52tt.com/pkg/datacenter"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/metrics"
	"golang.52tt.com/pkg/protocol"
	channelLiveLogicpb "golang.52tt.com/protocol/app/channel-live-logic"
	"golang.52tt.com/protocol/app/sync"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/anchorcontract-go"
	apiPB "golang.52tt.com/protocol/services/apicenter/apiserver"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkaanchorcontract"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkapresent"
	"golang.52tt.com/services/account-appeal-http-logic/models"
	anchorCheckConf "golang.52tt.com/services/anchor-check/conf"
	"golang.52tt.com/services/anchor-contract/anchorcontract-go/cache"
	"golang.52tt.com/services/anchor-contract/anchorcontract-go/conf"
	"golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql"
	//"golang.52tt.com/services/channelrank-group/channelmemberviprank-go/internal/report"
	"golang.52tt.com/services/anchor-contract/sign-anchor-stats/report"
	"golang.52tt.com/services/notify"
)

const GOLDDIAMONN_MULTIPLAYER_LIB = 0
const GOLDDIAMONN_RADIO_LIVE_LIB = 1

type AnchorContractMgr struct {
	shutDown              chan interface{}
	sc                    *conf.ServiceConfigT
	dyConfig              *conf.SDyConfigHandler
	newDyConf             conf.IConfDynamic
	cache                 *cache.AnchorContractCache
	store                 *mysql.Store
	contractEventProducer *eventlink.KafkaProduce
	anchorCheckDyconfig   *anchorCheckConf.SDyConfigHandler

	liveMgrCli               *channellivemgr.Client
	apiCenterGoCli           *apicenter_go.Client
	apiCenterCli             *apicenter.Client
	guildCli                 *guild.Client
	accountCli               *account.Client
	golddiamonnCli           *golddiamonn.Client
	liveStatsCli             *channellivestats.Client
	antiCli                  *anti.Client
	tBeanCli                 *models.TBean
	recommendCli             *entertainmentrecommendback.Client
	signanchorstatsCli       *signanchorstats.Client
	channelCli               *channel.Client
	anchorLevelCli           *anchor_level.Client
	anchorCheckCli           *anchorcheck.Client
	rcmdOperatingPlatformCli *operating_platform.Client
	TtcProxyCli              ttc_proxy.IClient
	GreenBaBaClient          greenbaba.IClient
	GuildCooperationCli      guild_cooperation.IClient
	imApiCli                 *im_api.Client
	userAuthHistoryCli       user_auth_history.IClient
	EsportRoleCli            esport_role.IClient
	ExchangeCli              exchange.IClient
	ExchangeGuildClient      exchange.IGuildClient
	GuildManagementSvrCli    *guild_management_svr.Client
	ObsCli                   obsgateway.IClient
	publicCli                public.IClient
	userOlCli                userOL.IClient
	smsCli                   sms_go.IClient
	ttcProxyClient           ttc_proxy.IClient
	banUserCli               banuser.IClient

	apiCenterClient apicenter.IClient

	reporter report.IFeishuReporterV2

	censoringClient    censoring_proxy.IClient
	auditResultWatcher *audit.AuditResultEventWatcher
}

func NewMgr2(sc *conf.ServiceConfigT) *AnchorContractMgr {
	mgr := &AnchorContractMgr{}
	mgr.contractEventProducer, _ = eventlink.NewKafkaProduce(sc.GetContractKafkaConfig().BrokerList(),
		sc.GetContractKafkaConfig().ClientID, sc.GetContractKafkaConfig().Topics)
	return mgr
}

func (m *AnchorContractMgr) GetStore() *mysql.Store {
	return m.store
}

func (m *AnchorContractMgr) GetCache() *cache.AnchorContractCache {
	return m.cache
}

func (m *AnchorContractMgr) GetField() (*mysql.Store, *cache.AnchorContractCache, *conf.SDyConfigHandler,
	*account.Client, *channellivemgr.Client,
	*channellivestats.Client, *anchorcheck.Client,
) {
	return m.store, m.cache, m.dyConfig, m.accountCli, m.liveMgrCli, m.liveStatsCli, m.anchorCheckCli
}

func NewAnchorContractMgr(sc *conf.ServiceConfigT, cache *cache.AnchorContractCache, store *mysql.Store) (*AnchorContractMgr, error) {

	channelCli := channel.NewClient()
	liveMgrCli, _ := channellivemgr.NewClient()
	liveStatsCli, _ := channellivestats.NewClient()
	accountCli, _ := account.NewClient()
	golddiamonnCli, _ := golddiamonn.NewClient()
	apiCenterGoCli, _ := apicenter_go.NewClient()
	recommendCli := entertainmentrecommendback.NewClient()
	signanchorstatsCli := signanchorstats.NewClient()
	anchorLevelCli, _ := anchor_level.NewClient()
	anchorCheckCli, _ := anchorcheck.NewClient()
	rcmdOperatingPlatformCli, _ := operating_platform.NewClient()
	ttcProxyCli, _ := ttc_proxy.NewClient()
	GreenBaBaClient := greenbaba.NewIClient()
	GuildCooperationCli := guild_cooperation.NewIClient()
	imApiCli, _ := im_api.NewClient()
	ExchangeCli, _ := exchange.NewClient()
	ExchangeGuildClient, _ := exchange.NewGuildClient()
	GuildManagementSvrCli, _ := guild_management_svr.NewClient()
	apiCenterClient := apicenter.NewIClient()
	obsCli, _ := obsgateway.NewClient()
	publicCli := public.NewClient()
	userOlCli := userOL.NewIClient()
	smsCli := sms_go.NewIClient()
	ttcProxyClient := ttc_proxy.NewIClient()
	banUserCli := banuser.NewIClient()

	dyConfig, err := conf.NewConfigHandler(conf.DyconfigPath)
	if err != nil {
		return nil, err
	}
	err = dyConfig.Start()
	if err != nil {
		return nil, err
	}
	anchorCheckDyconfig := anchorCheckConf.NewConfigHandler(anchorCheckConf.DyconfigPath)
	if err := anchorCheckDyconfig.Start(); err != nil {
		log.Errorf("anchorCheckConf dyconfig.Start() fail %v", err)
		return nil, err
	}

	newDyConf, err := conf.NewDyConfig(context.Background())
	if err != nil {
		log.Errorf("NewDyConfig fail %v", err)
		return nil, err
	}

	push, warnFeishuUrl := dyConfig.GetWarnFeishuUrl()
	log.Infof("reporter push=%v warnFeishuUrl=%q", push, warnFeishuUrl)
	reporter := report.NewFeiShuReporterV2(warnFeishuUrl, "", push)

	mgr := &AnchorContractMgr{
		sc:                  sc,
		dyConfig:            dyConfig,
		newDyConf:           newDyConf,
		shutDown:            make(chan interface{}),
		cache:               cache,
		store:               store,
		reporter:            reporter,
		anchorCheckDyconfig: anchorCheckDyconfig,

		channelCli:               channelCli,
		liveMgrCli:               liveMgrCli,
		liveStatsCli:             liveStatsCli,
		apiCenterGoCli:           apiCenterGoCli,
		apiCenterCli:             apicenter.NewClient(),
		guildCli:                 guild.NewClient(),
		accountCli:               accountCli,
		golddiamonnCli:           golddiamonnCli,
		antiCli:                  anti.NewClient(),
		tBeanCli:                 models.InitTBean(sc.TBeanCliConf.TBeanURLPrefix, sc.TBeanCliConf.TBeanSecretKey, sc.TBeanCliConf.TBeanCaller),
		recommendCli:             recommendCli,
		signanchorstatsCli:       signanchorstatsCli,
		anchorLevelCli:           anchorLevelCli,
		anchorCheckCli:           anchorCheckCli,
		rcmdOperatingPlatformCli: rcmdOperatingPlatformCli,
		TtcProxyCli:              ttcProxyCli,
		GreenBaBaClient:          GreenBaBaClient,
		GuildCooperationCli:      GuildCooperationCli,
		imApiCli:                 imApiCli,
		userAuthHistoryCli:       user_auth_history.NewIClient(),
		EsportRoleCli:            esport_role.NewIClient(),
		ExchangeCli:              ExchangeCli,
		ExchangeGuildClient:      ExchangeGuildClient,
		GuildManagementSvrCli:    GuildManagementSvrCli,
		apiCenterClient:          apiCenterClient,
		censoringClient:          censoring_proxy.NewIClient(),
		ObsCli:                   obsCli,
		publicCli:                publicCli,
		userOlCli:                userOlCli,
		smsCli:                   smsCli,
		ttcProxyClient:           ttcProxyClient,
		banUserCli:               banUserCli,
	}

	watcher, err := mgr.StartWatchProcessReviewResult()
	if err != nil {
		log.Errorf("StartWatchProcessReviewResult fail %v", err)
		return nil, err
	}
	mgr.auditResultWatcher = watcher

	redisSub := cache.GetSubscribeChan()
	refresh := func() error {
		certs, err := mgr.store.GetAllUserExamineCerts()
		if err != nil {
			log.Errorf("failed to store.GetAllUserExamineCerts err %+v", err)
			return err
		}

		mgr.cache.LocalCache.UpdateUserExamineCert(certs)
		return nil
	}
	go mgr.handleAnchorCertChange(refresh, redisSub)
	mgr.UpdateAnchorExtraCertEffect()
	//anchorExtraCertTTL := mgr.dyConfig.GetAnchorExtraCertLocalCacheTTL()
	//go mgr.TimerHandle(anchorExtraCertTTL, mgr.updateAnchorExtraCertEffect)

	kafkaPublisher, err := eventlink.NewKafkaProduce(sc.GetContractKafkaConfig().BrokerList(), sc.GetContractKafkaConfig().ClientID, sc.GetContractKafkaConfig().Topics)

	mgr.contractEventProducer = kafkaPublisher

	//mgr.StartTimer()
	//mgr.SettleAnchorCertTimer()

	/*
		job := cron.New()
		job.AddFunc("0 8 * * *", mgr.NotifyAnchorExamine)
		job.AddFunc("0 0 * * *", mgr.NotifyOverdueAnchorExamine)
		job.AddFunc("0 0 * * *", mgr.AutoInitEsporterTimer)
		job.Start()

	*/

	return mgr, nil
}

func (m *AnchorContractMgr) handleAnchorCertChange(refresh func() error, ch <-chan *redis.Message) {
	if err := refresh(); err != nil {
		panic(err)
	}
	for {
		timer := time.NewTimer(time.Duration(m.sc.AnchorCertLocalCacheTTL) * time.Second)
		select {
		case <-timer.C:
			refresh()
		case msg := <-ch:
			log.Infof("redis_sub: %+v", msg)
			refresh()
		case <-m.shutDown:
			timer.Stop()
			return
		}
	}
}

// 发送TT助手消息
func (m *AnchorContractMgr) SendIMMsg(ctx context.Context, uid uint32, content string) error {
	log.InfoWithCtx(ctx, "SendIMMsg uid %d content %q", uid, content)

	msg := &apiPB.ImMsg{
		ImType: &apiPB.ImType{
			SenderType:   uint32(apiPB.IM_SENDER_TYPE_IM_SENDER_NORMAL),
			ReceiverType: uint32(apiPB.IM_RECEIVER_TYPE_IM_RECEIVER_USER),
			ContentType:  uint32(apiPB.IM_CONTENT_TYPE_IM_CONTENT_TEXT),
		},
		FromUid: uint32(10000),
		ToIdList: []uint32{
			uid,
		},
		ImContent: &apiPB.ImContent{
			TextNormal: &apiPB.ImTextNormal{
				Content: content,
			},
		},
		Platform:    apiPB.Platform_UNSPECIFIED,
		AppPlatform: "all",
	}

	err := m.apiCenterClient.SendImMsg(ctx, uid, protocol.TT, []*apiPB.ImMsg{msg}, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendImMsg fail. uid:%d err: %s", uid, err.Error())
		return err
	}

	notify.NotifySync(uid, sync.SyncReq_IM_MSG)

	log.DebugWithCtx(ctx, "SendIMMsg done. uid:%d, content:%s, Msg:%+v", uid, content, msg)
	return nil
}

func (m *AnchorContractMgr) SendIMMsgWithJumpUrl(ctx context.Context, uid uint32, content, highlight, jumpUrl string) error {
	log.InfoWithCtx(ctx, "SendIMMsgWithJumpUrl uid %d content %q highlight %q jumpUrl %q",
		uid, content, highlight, jumpUrl)

	msg := &apiPB.ImMsg{
		ImType: &apiPB.ImType{
			SenderType:   uint32(apiPB.IM_SENDER_TYPE_IM_SENDER_NORMAL),
			ReceiverType: uint32(apiPB.IM_RECEIVER_TYPE_IM_RECEIVER_USER),
			ContentType:  uint32(apiPB.IM_CONTENT_TYPE_IM_CONTENT_TEXT_WITH_HL_URL),
		},
		FromUid: uint32(10000),
		ToIdList: []uint32{
			uid,
		},
		ImContent: &apiPB.ImContent{
			TextHlUrl: &apiPB.ImTextWithHighlightUrl{
				Content:    content,
				Hightlight: highlight,
				Url:        jumpUrl,
			},
		},
		Platform:    apiPB.Platform_UNSPECIFIED,
		AppPlatform: "all",
	}

	err := m.apiCenterClient.SendImMsg(ctx, uid, protocol.TT, []*apiPB.ImMsg{msg}, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendIMMsgWithJumpUrl fail. uid:%d err: %s", uid, err.Error())
		return err
	}

	notify.NotifySync(uid, sync.SyncReq_IM_MSG)

	log.DebugWithCtx(ctx, "SendIMMsgWithJumpUrl done. uid:%d, content:%s, highlight:%s, jumpUrl:%s, Msg:%+v", uid, content, highlight, jumpUrl, msg)
	return nil
}

func (m *AnchorContractMgr) ContractDataCenterReport(ctx context.Context, uid, guildId, status uint32) {
	datacenter.StdReportKV(ctx, "************", map[string]interface{}{
		"totalDate": time.Now().Format("2006-01-02 15:04:05"),
		"uid":       fmt.Sprint(uid),
		"guildId":   fmt.Sprint(guildId),
		"status":    fmt.Sprint(status),
		"appId":     "ttvoice",
	})
}

func (m *AnchorContractMgr) KafkaProduceAnchorContractEvent(ctx context.Context, eventType, uid, guild uint32, t time.Time) {
	event := &kafkaanchorcontract.AnchorContractEvent{
		EventType: eventType,
		Uid:       uid,
		GuildId:   guild,
		EventTime: uint32(t.Unix()),
	}

	err := m.contractEventProducer.ProduceEvent(ctx, strconv.Itoa(int(uid)), event)
	if err != nil {
		log.Errorf("KafkaProduceAnchorContractEvent fail. event:%+v, err:%v", event, err)
		return
	}

	log.Infof("KafkaProduceAnchorContractEvent %+v", event)
}

func (m *AnchorContractMgr) HandleAnchorLiveEvent(event *channelLiveLogicpb.ChannelLiveKafkaEvent) error {
	info, exist, err := m.store.GetValidContractWithUid(event.AnchorUid)
	if err != nil {
		log.Errorf("HandleAnchorLiveEvent fail to store.GetValidContractWithUid err %v, event %+v", err, event)
		return err
	}
	if !exist {
		return nil
	}

	err = m.store.UpdateLiveAnchorDailyRecord(&mysql.LiveAnchorDailyRecord{
		Uid:     info.Uid,
		GuildId: info.GuildId,
	}, time.Now())
	if err != nil {
		log.Errorf("HandleAnchorLiveEvent fail to store.UpdateLiveAnchorDailyRecord err %v, event %+v", err, event)
		return err
	}

	return nil
}

func (m *AnchorContractMgr) HandlePresentEvent(event *kafkapresent.PresentEvent) error {
	defer metrics.DISCOVERY_FUNC_TRACK(protocol.NewExactServerError(nil, 0, "anchorcontract-go.HandlePresentEvent")).End()

	uid := event.GetTargetUid()
	info, exist, err := m.store.GetAnchorIdentityWithType(uid, uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER))
	if err != nil {
		log.Errorf("HandlePresentEvent fail to GetAnchorIdentityWithType. event:%+v, err:%v", event, err)
		return err
	}

	// 没有合约不统计
	if !exist {
		log.Debugf("HandlePresentEvent uid:%d not anchor", uid)
		return nil
	}

	// 2022.03.21 修改原有统计逻辑
	// 新增子母公会概念, 统计主播在签约关系公会及该公会关联的子母公会旗下任意经营房间产生的历史积分
	var isAddScore = m.dyConfig.IsParentChildGuild(info.GuildId, event.GetGuildId())
	if isAddScore {
		err = m.store.AddUserMonthScore(nil, uid, event.GetGuildId(), event.GetScore(), time.Unix(int64(event.GetSendTime()), 0))
		if err != nil {
			log.Errorf("HandlePresentEvent fail to AddUserMonthScore. event:%+v, err:%v", event, err)
			return err
		}
	}

	log.Infof("HandlePresentEvent uid:%d, contract_guild_id:%d, event_guild_id:%d, isAddScore:%v, channelId:%d, score:%d, orderId %q",
		uid, info.GuildId, event.GetGuildId(), isAddScore, event.GetChannelId(), event.GetScore(), event.OrderId)
	return nil
}

func (m *AnchorContractMgr) HandleTbeanEvent(uid, addScore, timestamp uint32) error {
	defer metrics.DISCOVERY_FUNC_TRACK(protocol.NewExactServerError(nil, 0, "anchorcontract-go.HandleTbeanEvent")).End()

	info, exist, err := m.store.GetAnchorIdentityWithType(uid, uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER))
	if err != nil {
		log.Errorf("HandleTbeanEvent fail to GetAnchorIdentityWithType. uid:%+v, err:%v", uid, err)
		return err
	}

	// 没有合约不统计
	if !exist {
		log.Infof("HandleTbeanEvent uid:%d not anchor", uid)
		return nil
	}

	err = m.store.AddUserMonthScore(nil, uid, info.GuildId, addScore, time.Unix(int64(timestamp), 0))
	if err != nil {
		log.Errorf("HandleTbeanEvent fail to AddUserMonthScore. event:%+v, uid:%v", uid, err)
		return err
	}

	log.Infof("HandleTbeanEvent uid:%d, contract_guild_id:%d, AddScore:%v",
		uid, info.GuildId, addScore)
	return nil
}

/*
判断是否有子母公会关系
contractGuildId 用户签约的公会id
eventGuildId	送礼事件房间所属的公会id
*/
func (m *AnchorContractMgr) IsParentChildGuild(parentGuildMap map[uint32]uint32, contractGuildId uint32, eventGuildId uint32) bool {

	if contractGuildId == 0 || eventGuildId == 0 {
		log.Warnf("IsParentChildGuild guild eq 0, contractGuildId:%d, eventGuildId:%d", contractGuildId, eventGuildId)
		return false
	}

	if contractGuildId == eventGuildId {
		return true
	}

	// case 1: 用户签约的是母公会
	{
		// 判断送礼事件房间是否为对应子公会
		if parentGuildMap[contractGuildId] == 0 && parentGuildMap[eventGuildId] == contractGuildId {
			log.Debugf("IsParentChildGuild case#1, contractGuildId:%d, eventGuildId:%d", contractGuildId, eventGuildId)
			return true
		}
	}

	// case 2: 用户签约的是子公会
	{
		// 1. 送礼事件房间为母公会 判断是否有子母关系
		if parentGuildMap[contractGuildId] > 0 && parentGuildMap[contractGuildId] == eventGuildId {
			log.Debugf("IsParentChildGuild case#2.1, contractGuildId:%d, eventGuildId:%d", contractGuildId, eventGuildId)
			return true
		}

		// 2. 送礼事件房间为子公会 判断是否有共同的母公会
		if parentGuildMap[contractGuildId] > 0 && parentGuildMap[contractGuildId] == parentGuildMap[eventGuildId] {
			log.Debugf("IsParentChildGuild case#2.2, contractGuildId:%d, eventGuildId:%d", contractGuildId, eventGuildId)
			return true
		}
	}

	return false
}

func (m *AnchorContractMgr) UpdateGuildScoreCache(guildId uint32) {
	begin := uint32(0)
	limit := uint32(500)

	transitionDay := m.sc.TransitionDay // 每月N号前过渡期
	now := time.Now()
	t := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
	if uint32(now.Day()) < transitionDay {
		//  过渡期查上个月的收入积分缓存记录
		t = now.AddDate(0, -1, 0)
	}

	err := m.cache.ClearGuildUserScore(guildId)
	if err != nil {
		log.Errorf("UpdateGuildScoreCache fail to ClearGuildUserScore. guildId:%+v, err:%v", guildId, err)
		return
	}

	for {
		uidList, err := m.store.GetGuildUidListWithIdentity(guildId, uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER), begin, limit)
		if err != nil {
			log.Errorf("UpdateGuildScoreCache fail to GetGuildUidListWithIdentity. guildId:%+v, err:%v", guildId, err)
		}

		uid2Score, err := m.store.BatchGetGuildUserMonthScore(guildId, uidList, t)
		if err != nil {
			log.Errorf("UpdateGuildScoreCache fail to BatchGetGuildUserMonthScore. guildId:%+v, err:%v", guildId, err)
		}

		err = m.cache.BatchUpdateGuildUserScore(guildId, uid2Score)
		if err != nil {
			log.Errorf("UpdateGuildScoreCache fail to BatchUpdateGuildUserScore. guildId:%+v, err:%v", guildId, err)
		}

		begin = begin + uint32(len(uidList))
		if uint32(len(uidList)) < limit {
			break
		}
	}

	if begin == 0 {
		// 哨兵
		err = m.cache.BatchUpdateGuildUserScore(guildId, map[uint32]int64{0: -1})
		if err != nil {
			log.Errorf("UpdateGuildScoreCache fail to BatchUpdateGuildUserScore  guildId:%+v, err:%v", guildId, err)
		}
	}

	log.Infof("UpdateGuildScoreCache guildId:%d, len:%d", guildId, begin)
}

func (m *AnchorContractMgr) UpdateAllGuildScoreCache() error {
	now := time.Now()
	if now.Hour() < 4 {
		// 每天4.后才更新
		return nil
	}

	lockKey := fmt.Sprintf("guild_user_score_update_flag_v2_%d", now.Day())
	if !m.cache.Lock(lockKey, 24*time.Hour) {
		return nil
	}

	guildList, err := m.store.GetAllSignGuildList()
	if err != nil {
		log.Errorf("UpdateAllGuildScoreCache fail to GetAllSignGuildList. err:%v", err)
		m.cache.UnLock(lockKey)
		return err
	}

	for _, guildId := range guildList {
		m.UpdateGuildScoreCache(guildId)
		time.Sleep(20 * time.Millisecond)
	}

	log.Infof("UpdateAllGuildScoreCache len:%d", len(guildList))
	return nil
}

func (m *AnchorContractMgr) GetUserAnchorIdentity(ctx context.Context, uid uint32) ([]uint32, error) {
	out := make([]uint32, 0)
	list, err := m.store.GetAnchorIdentity(nil, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserAnchorIdentity fail to GetAnchorIdentity. uid:%v, err:%v", uid, err)
		return out, err
	}

	for _, e := range list {
		out = append(out, e.IdentityType)
	}

	return out, nil
}

func (m *AnchorContractMgr) UpdateUserContractCacheInfo(ctx context.Context, uid uint32) (*pb.ContractCacheInfo, error) {
	out := &pb.ContractCacheInfo{Contract: &pb.ContractInfo{}}
	contract, err := m.GetUserContractInfo(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateUserContractCacheInfo fail to GetUserContractInfo. uid:%v, err:%v", uid, err)
		return out, err
	}

	if contract.GetActorUid() == uid {
		list, err := m.store.GetAnchorIdentity(nil, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateUserContractCacheInfo fail to GetAnchorIdentity. uid:%+v, err:%v", uid, err)
			return out, err
		}

		out.Contract = contract
		out.AnchorIdentityList = make([]uint32, 0, 2)
		for _, e := range list {
			if e.GuildId > 0 && e.GuildId != contract.GetGuildId() {
				log.ErrorWithCtx(ctx, "UpdateUserContractCacheInfo uid:%v AnchorIdentity data inconsistency.", uid)
				continue
			}
			out.AnchorIdentityList = append(out.AnchorIdentityList, e.IdentityType)
			out.InfoList = append(out.InfoList, &pb.AnchorIdentityInfo{
				ActorUid:     e.Uid,
				GuildId:      e.GuildId,
				IdentityType: e.IdentityType,
				ObtainTime:   uint32(e.ObtainTime.Unix()),
				AgentUid:     e.AgentUid,
			})
		}
	} else { //未公会签约，但可能签约平台达人
		identify, exist, err := m.store.GetAnchorIdentityWithType(uid, uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_DOYEN))
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateUserContractCacheInfo fail to GetAnchorIdentityWithType. uid:%+v, err:%v", uid, err)
			return out, err
		}
		if exist {
			out.AnchorIdentityList = append(out.AnchorIdentityList, identify.IdentityType)
			out.InfoList = append(out.InfoList, &pb.AnchorIdentityInfo{
				ActorUid:     identify.Uid,
				GuildId:      identify.GuildId,
				IdentityType: identify.IdentityType,
				ObtainTime:   uint32(identify.ObtainTime.Unix()),
				AgentUid:     identify.AgentUid,
			})
		}
	}

	log.Debugf("UpdateUserContractCacheInfo uid %d info %+v", uid, out)
	err = m.cache.SetUserContractInfoV2(uid, out)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateUserContractCacheInfo fail to SetUserContractInfoV2. uid:%+v, err:%v", uid, err)
	}

	log.DebugWithCtx(ctx, "UpdateUserContractCacheInfo uid:%d, out:%+v", uid, out)
	return out, nil
}

func (m *AnchorContractMgr) GetUserContractCacheInfo(ctx context.Context, uid uint32) (*pb.ContractCacheInfo, error) {
	out := &pb.ContractCacheInfo{}
	if uid == 0 {
		return out, nil
	}

	out, exist, err := m.cache.GetUserContractInfoV2(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserContractCacheInfo fail to cache.GetUserContractInfoV2. uid:%v, err:%v", uid, err)
		return out, err
	}

	if !exist {
		out, err = m.UpdateUserContractCacheInfo(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserContractCacheInfo fail to UpdateUserContractCacheInfo. uid:%v, err:%v", uid, err)
			return out, err
		}
	}

	log.DebugWithCtx(ctx, "GetUserContractCacheInfo uid %d out %+v", uid, out)

	return out, nil
}

func (m *AnchorContractMgr) BatchUpdateUserContractCacheInfo(uids []uint32) (map[uint32]*pb.ContractCacheInfo, error) {
	out := map[uint32]*pb.ContractCacheInfo{}
	uid2ContractInfo, err := m.store.BatchGetValidContractWithUid(uids)
	if err != nil {
		log.Errorf("BatchUpdateUserContractCacheInfo fail to store.BatchGetValidContractWithUid. uids:%+v, err:%v", uids, err)
		return out, err
	}

	uid2anchorIdentity, err := m.store.BatchGetAnchorIdentity(uids)
	if err != nil {
		log.Errorf("BatchUpdateUserContractCacheInfo fail to store.BatchGetAnchorIdentity. uids:%+v, err:%v", uids, err)
		return out, err
	}

	for _, uid := range uids {
		if contractInfo, ok := uid2ContractInfo[uid]; ok {
			info := &pb.ContractCacheInfo{Contract: contractInfo.FillPb()}

			for _, identityInfo := range uid2anchorIdentity[uid] {
				info.AnchorIdentityList = append(info.AnchorIdentityList, identityInfo.IdentityType)

				info.InfoList = append(info.InfoList, &pb.AnchorIdentityInfo{
					ActorUid:     identityInfo.Uid,
					GuildId:      identityInfo.GuildId,
					IdentityType: identityInfo.IdentityType,
					ObtainTime:   uint32(identityInfo.ObtainTime.Unix()),
					AgentUid:     identityInfo.AgentUid,
				})
			}

			out[uid] = info

			log.Debugf("BatchUpdateUserContractCacheInfo uid %d info %+v", uid, info)
		} else { //虽未签约公会，但可能签约平台达人
			info := &pb.ContractCacheInfo{Contract: &pb.ContractInfo{}}
			for _, identityInfo := range uid2anchorIdentity[uid] {
				if identityInfo.IdentityType != uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_DOYEN) {
					continue
				}
				info.AnchorIdentityList = append(info.AnchorIdentityList, identityInfo.IdentityType)
				info.InfoList = append(info.InfoList, &pb.AnchorIdentityInfo{
					ActorUid:     identityInfo.Uid,
					GuildId:      identityInfo.GuildId,
					IdentityType: identityInfo.IdentityType,
					ObtainTime:   uint32(identityInfo.ObtainTime.Unix()),
					AgentUid:     identityInfo.AgentUid,
				})
				out[uid] = info
				log.Debugf("BatchUpdateUserContractCacheInfo doyen uid %d info %+v", uid, info)
				break
			}
		}
	}

	err = m.cache.BatchSetUserContractInfoV2(uids, out)
	if err != nil {
		log.Errorf("BatchUpdateUserContractCacheInfo fail to cache.BatchSetUserContractInfoV2. uids:%+v, err:%v", uids, err)
	}

	return out, nil
}

func (m *AnchorContractMgr) isDoyenIdentity(info *pb.ContractCacheInfo) bool {
	for _, identity := range info.AnchorIdentityList {
		if identity == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_DOYEN) {
			return true
		}
	}
	return false
}

func (m *AnchorContractMgr) BatchGetUserContractCacheInfo(ctx context.Context, in *pb.BatchGetUserContractCacheInfoReq) (*pb.BatchGetUserContractCacheInfoResp, error) {
	out := &pb.BatchGetUserContractCacheInfoResp{}

	if len(in.GetUids()) == 0 {
		return out, nil
	}

	uid2ContractInfo := map[uint32]*pb.ContractCacheInfo{}
	list, notExistUids, err := m.cache.BatchGetUserContractInfo(in.GetUids())
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetUserContractCacheInfo fail to cache.BatchGetUserContractInfo. uids:%+v, err:%v", in, err)
		return out, err
	}
	for uid, info := range list {
		if info.GetContract().GetGuildId() > 0 {
			uid2ContractInfo[info.GetContract().GetActorUid()] = info
		} else if m.isDoyenIdentity(info) {
			uid2ContractInfo[uid] = info
		}
	}

	if len(notExistUids) > 0 {

		uid2ContractInfoDB, err := m.BatchUpdateUserContractCacheInfo(notExistUids)
		if err != nil {
			log.ErrorWithCtx(ctx, "BatchGetUserContractCacheInfo fail to m.BatchUpdateUserContractCacheInfo. uids:%+v, err:%v", in, err)
			return out, err
		}
		for uid, info := range uid2ContractInfoDB {
			uid2ContractInfo[uid] = info
		}
	}

	uid2AgentUid, _, err := m.store.GetAnchorAgentUid(in.Uids)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetUserContractCacheInfo GetAnchorAgentUid fail %v", err)
	} else {
		for uid, info := range uid2ContractInfo {
			if uid2AgentUid[uid] > 0 {
				for _, identy := range info.GetInfoList() {
					if identy.IdentityType == 1 {
						identy.AgentUid = uid2AgentUid[uid]
						break
					}
				}
			}
		}
	}

	out.Uid2Contractinfo = uid2ContractInfo
	log.DebugfWithCtx(ctx, "BatchGetUserContractCacheInfo end in %v, out %+v", in, out.Uid2Contractinfo)
	return out, nil
}

func (m *AnchorContractMgr) GetUserContractInfo(ctx context.Context, uid uint32) (*pb.ContractInfo, error) {
	out := &pb.ContractInfo{}

	info, exist, err := m.store.GetValidContractWithUid(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserContractInfo fail to GetValidContractWithUid. uid:%v, err:%v", uid, err)
		return out, err
	}

	if exist {
		out = &pb.ContractInfo{
			ActorUid:         info.Uid,
			GuildId:          info.GuildId,
			SignTime:         uint32(info.SignTime.Unix()),
			ContractDuration: info.ContractDuration,
			ExpireTime:       uint32(info.ContractExpireTime.Unix()),
			Permission:       info.Permissions,
			IdentityNum:      info.IdentityNum,
			WorkerType:       info.WorkerType,
		}
	}

	return out, nil
}

func (m *AnchorContractMgr) GetUserExtensionStatus(ctx context.Context, contract *pb.ContractInfo) (uint32, error) {
	now := time.Now()
	if contract.GetActorUid() == 0 || contract.GetExpireTime() <= uint32(now.Unix()) {
		return 0, nil
	}

	_, exist, err := m.store.GetExtensionContract(contract.GetActorUid(), contract.GetGuildId(), m.CalculateExtensionExpireTime(now))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserContractInfo fail to GetValidContractWithUid. contract:%+v, err:%v", contract, err)
		return 0, err
	}

	if exist {
		return uint32(pb.EXTENSION_STATUS_EXTENSION_STATUS_HAVE_EXTENSION), nil
	}

	// 60sec内过期
	if contract.GetExpireTime()-uint32(now.Unix()) <= 60 {
		return uint32(pb.EXTENSION_STATUS_EXTENSION_STATUS_CANNOT_EXTENSION), nil
	}

	currMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
	expiredTime := time.Unix(int64(contract.GetExpireTime()), 0)
	expireMonth := time.Date(expiredTime.Year(), expiredTime.Month(), 1, 0, 0, 0, 0, time.Local)

	// 当月或下月过期
	if currMonth.Equal(expireMonth) || currMonth.AddDate(0, 1, 0).Equal(expireMonth) {
		return uint32(pb.EXTENSION_STATUS_EXTENSION_STATUS_CAN_EXTENSION), nil
	}

	return uint32(pb.EXTENSION_STATUS_EXTENSION_STATUS_CANNOT_EXTENSION), nil
}

// 移出合作库时回收身份
func (m *AnchorContractMgr) ReclaimGuildAllAnchorIdentity(ctx context.Context, guildId, libraryType uint32, handler string) error {
	log.InfoWithCtx(ctx, "ReclaimGuildAllAnchorIdentity GuildId %d libraryType %d handler %q", guildId, libraryType, handler)

	bIn, err := m.CheckGuildIfInCoop(ctx, guildId, libraryType)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReclaimGuildAllAnchorIdentity fail to checkGuildIfInCoop guildId:%d, libraryType:%d err:%v", guildId, libraryType, err)
		return err
	}

	if bIn {
		log.ErrorWithCtx(ctx, "ReclaimGuildAllAnchorIdentity fail. Guild is in coop  library guildId:%d, libraryType:%d", guildId, libraryType)
		return nil
	}

	begin := uint32(0)
	limit := uint32(500)

	for {
		uidList, err := m.store.GetGuildUidListWithIdentity(guildId, libraryType, begin, limit)
		if err != nil {
			log.ErrorWithCtx(ctx, "ReclaimGuildAllAnchorIdentity fail to GetGuildUidListWithIdentity guildId:%d, libraryType:%d err:%v", guildId, libraryType, err)
			// 此处需要直接返回错误告知上层
			return err
		}

		for _, uid := range uidList {
			err = m.ReclaimAnchorIdentity(ctx, uid, guildId, libraryType, handler)
			if err != nil {
				log.ErrorWithCtx(ctx, "ReclaimGuildAllAnchorIdentity fail to ReclaimAnchorIdentity guildId:%d, libraryType:%d err:%v", guildId, libraryType, err)
				// 此处需要直接返回错误告知上层
				return err
			}
		}

		begin = begin + uint32(len(uidList))
		if uint32(len(uidList)) < limit {
			break
		}
	}

	log.InfoWithCtx(ctx, "ReclaimGuildAllAnchorIdentity guildId:%d, identityType:%d len:%d", guildId, libraryType, begin)
	return nil
}

// 回收身份
func (m *AnchorContractMgr) ReclaimAnchorIdentity(ctx context.Context, uid, guildId, identityType uint32, handler string) error {
	log.InfoWithCtx(ctx, "ReclaimAnchorIdentity uid %d, guildId %d, identityType %d handler %q",
		uid, guildId, identityType, handler)

	now := time.Now()
	needCancelContract := false

	contract, exist, err := m.store.GetValidContractWithUid(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelContractByUid fail to GetValidContractWithUid uid:%+v, guildId:%+v, err:%v", uid, guildId, err)
		return err
	}

	if !exist || contract.GuildId != guildId {
		log.ErrorWithCtx(ctx, "ReclaimAnchorIdentity get invalid contract uid %d, guildId %d, contract %v, info %+v", uid, guildId, exist, contract)
		return nil
	}

	err = m.store.Transaction(ctx, func(tx *gorm.DB) error {
		err := m.store.DelUserAnchorIdentity(ctx, tx, uid, guildId, identityType, now, contract.SignTime, contract.ContractExpireTime, handler)
		if err != nil {
			log.ErrorWithCtx(ctx, "ReclaimAnchorIdentity fail to DelUserAnchorIdentity uid:%d, guildId:%d, identityType:%d err:%v", uid, guildId, identityType, err)
			return err
		}

		list, err := m.store.GetAnchorIdentity(tx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "ReclaimAnchorIdentity fail to GetAnchorIdentity uid:%d, guildId:%d, identityType:%d err:%v", uid, guildId, identityType, err)
			return err
		}

		// 如果没有其他身份了，解约
		if len(list) == 0 {
			needCancelContract = true
		}
		// 只有平台达人身份，解约
		if len(list) == 1 && list[0].IdentityType == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_DOYEN) {
			needCancelContract = true
		}
		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ReclaimAnchorIdentity fail to Transaction uid:%d, guildId:%d, identityType:%d err:%v", uid, guildId, identityType, err)
		return err
	}

	if needCancelContract {
		err = m.CancelContractByUid(ctx, 0, uid, guildId, false)
		if err != nil {
			log.ErrorWithCtx(ctx, "ReclaimAnchorIdentity fail to CancelContractByUid uid:%d, guildId:%d, identityType:%d err:%v", uid, guildId, identityType, err)
			return err
		}
		log.InfoWithCtx(ctx, "ReclaimAnchorIdentity needCancelContract uid:%d, guildId:%d, identityType:%d, handler %s", uid, guildId, identityType, handler)
	}

	if identityType == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE) {
		// kafka事件
		m.KafkaProduceAnchorContractEvent(ctx, uint32(kafkaanchorcontract.EVENT_TYPE_EVENT_CONTRACT_DEL_LIVE_PERMISSION), uid, guildId, now)

	} else if identityType == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER) {
		err = m.cache.RemoveUserScore(uid, guildId)
		if err != nil {
			log.ErrorWithCtx(ctx, "ReclaimAnchorIdentity fail to RemoveUserScore uid:%d, guildId:%d, identityType:%d err:%v", uid, guildId, identityType, err)
		}

		if !needCancelContract {
			// kafka事件
			m.KafkaProduceAnchorContractEvent(ctx, uint32(kafkaanchorcontract.EVENT_TYPE_EVENT_CONTRACT_DEL_MULTIPLAYER), uid, guildId, now)
			log.InfoWithCtx(ctx, "ReclaimAnchorIdentity uid:%d, guildId:%d, identityType:%d add DEL_MULTIPLAYER event.",
				uid, guildId, identityType)
		}
	} else if identityType == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_E_SPORTS) {

		_ = m.cache.DelGuildEsportScore(guildId, uid, uint32(time.Now().Unix()))

		if !needCancelContract {
			m.KafkaProduceAnchorContractEvent(ctx, uint32(kafkaanchorcontract.EVENT_TYPE_EVENT_CONTRACT_DEL_ESPORTS), uid, guildId, now)
		}
		log.InfoWithCtx(ctx, "ReclaimAnchorIdentity uid:%d, guildId:%d, identityType:%d add DEL_ESPORTS event.",
			uid, guildId, identityType)
	}

	err = m.cache.DelUserContractInfoV2(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReclaimAnchorIdentity fail to DelUserContractInfoV2 uid:%d, guildId:%d, identityType:%d err:%v", uid, guildId, identityType, err)
		//return err
	}

	log.InfoWithCtx(ctx, "ReclaimAnchorIdentity uid:%d, guildId:%d, identityType:%d needCancelContract=%v", uid, guildId, identityType, needCancelContract)
	return nil
}

// 获取公会合约相关汇总信息
func (m *AnchorContractMgr) GetGuildContractSum(ctx context.Context, in *pb.GetGuildContractSumReq) (*pb.GetGuildContractSumResp, error) {
	out := &pb.GetGuildContractSumResp{}
	guildId := in.GetGuildId()
	now := time.Now()
	var err error

	// 合约数
	out.ActorCount, err = m.store.GetGuildContractCount(guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildContractSum fail to GetGuildContractCount. in:%+v, err:%v", in, err)
		return out, err
	}

	identityTypeList := []uint32{
		uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER),
		uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE),
		uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_E_SPORTS),
	}
	statusList := []uint32{
		uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_PRESIDENT_HANDLING),
	}

	// 签约申请数
	out.ApplySignCount, err = m.store.GetGuildContractApplyCnt(guildId, identityTypeList, statusList, 0, 0, 0, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildContractSum fail to GetGuildContractApplyCnt. in:%+v, err:%v", in, err)
		return out, err
	}

	// 可续约数
	renewAbleCnt, err := m.store.GetRenewAbleContractCnt(guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildContractSum fail to GetRenewAbleContractCnt. in:%+v, err:%v", in, err)
		return out, err
	}

	// 已发续约数
	sendRenewCnt, err := m.store.GetExtensionContractCnt(guildId, m.CalculateExtensionExpireTime(now))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildContractSum fail to GetExtensionContractCnt. in:%+v, err:%v", in, err)
		return out, err
	}

	if renewAbleCnt > sendRenewCnt {
		out.RenewableCount = renewAbleCnt - sendRenewCnt
	}
	if renewAbleCnt > 0 {
		out.Expiring = 1
	}

	// 解约申请数
	out.ApplyCancelCount, err = m.store.GetGuildCancelContractApplyCnt(guildId, m.CalculateCancelContractExpireTime(now))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildContractSum fail to GetGuildCancelContractApplyCnt. in:%+v, err:%v", in, err)
		return out, err
	}

	out.LargeScoreCount, err = m.cache.GetGuildUserScoreCnt(guildId, 150000)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildContractSum fail to GetGuildUserScoreCnt. in:%+v, err:%v", in, err)
		return out, err
	}

	log.DebugWithCtx(ctx, "GetGuildContractSum in:%+v, out:%+v", in, out)
	return out, nil
}

func (m *AnchorContractMgr) CalculateExtensionExpireTime(now time.Time) time.Time {
	extensionDuration := int(m.sc.ExtensionValidityDuration)
	return now.AddDate(0, 0, -extensionDuration)
}

func (m *AnchorContractMgr) CalculateCancelContractExpireTime(now time.Time) time.Time {
	hour := m.newDyConf.GetContractDyConf().AutoAcceptCancelContractHour
	return time.Now().Add(-time.Duration(hour) * time.Hour)
}

func (m *AnchorContractMgr) CalculateNegotiateCancelContractExpireTime(now time.Time) time.Time {
	hour := m.newDyConf.GetContractDyConf().NegotiateAutoAcceptCancelContractHour
	return time.Now().Add(-time.Duration(hour) * time.Hour)
}

func (m *AnchorContractMgr) CalculatePayAutoCancelContractExpireTime(now time.Time) time.Time {
	hour := m.newDyConf.GetContractDyConf().PayAutoAcceptCancelContractHour
	return time.Now().Add(-time.Duration(hour) * time.Hour)
}

func (m *AnchorContractMgr) CheckGuildIfInCoop(ctx context.Context, guildId, libraryType uint32) (bool, error) {
	/*
				m.golddiamonnCli.GetGuildInfo is deprecated: 改用guild-cooperation的GetGuildCooperationInfoWithCoopType接口

				// GetGuildCooperationInfoWithCoopType(ctx context.Context, guildId uint32, cooperationType pb.CooperationType) (bool, protocol.ServerError)
				const (
			// from ga:ChannelType
			CooperationType_CTypeInvalid CooperationType = 0
			CooperationType_CTypeAmuse   CooperationType = 4
			CooperationType_CTypeYuyin   CooperationType = 7
		)
	*/
	/*
		coopType := guildcooperationpb.CooperationType_CTypeInvalid
		if GOLDDIAMONN_MULTIPLAYER_LIB == libraryType {
			coopType = guildcooperationpb.CooperationType_CTypeAmuse // 娱乐
		} else if GOLDDIAMONN_RADIO_LIVE_LIB == libraryType {
			coopType = guildcooperationpb.CooperationType_CTypeYuyin // 语音
		} else if uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_E_SPORTS) == libraryType {
			coopType = guildcooperationpb.CooperationType_CTypeESports
		}
	*/
	guildCooperationInfosResp, err := m.GuildCooperationCli.GetGuildCooperationInfos(ctx, guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckGuildIfInCoop fail to GuildCooperationCli.GetGuildCooperationInfoWithCoopType guildId:%d, libraryType:%d   err:%v",
			guildId, libraryType, err)
		return false, err
	}
	log.InfoWithCtx(ctx, "CheckGuildIfInCoop guildId=%d GetGuildCooperationInfos=%+v", guildId, guildCooperationInfosResp)

	switch libraryType {
	case GOLDDIAMONN_MULTIPLAYER_LIB:
		return guildCooperationInfosResp.GetIsAmuseCoopGuild(), nil
	case GOLDDIAMONN_RADIO_LIVE_LIB:
		return guildCooperationInfosResp.GetIsYuyinCoopGuild(), nil
	case uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_E_SPORTS):
		return guildCooperationInfosResp.GetIsEsportCoopGuild(), nil
	default:
	}

	//log.InfoWithCtx(ctx, "CheckGuildIfInCoop guildId=%d, libraryType=%d, coopType=%d, exist=%v", guildId, libraryType, coopType, exist)
	return false, nil

	/*
		resp, err := m.golddiamonnCli.GetGuildInfo(ctx, &golddiamonnPb.GuildInfoReq{
			GuildId:     guildId,
			LibraryType: libraryType,
		})
		log.InfoWithCtx(ctx, "CheckGuildIfInCoop guildId=%d, libraryType=%d, resp GetGuildId=%d GetGuildType=%d",
			guildId, libraryType, resp.GetGuildId(), resp.GetGuildType())
		if err != nil || resp.GetGuildId() != guildId {
			log.ErrorWithCtx(ctx, "CheckGuildIfInCoop fail to golddiamonnCli.GetGuildInfo guildId:%d, libraryType:%d err:%v", guildId, libraryType, err)
			return false, err
		}

		return resp.GetGuildType() == 1, nil
	*/
}

func (m *AnchorContractMgr) ShutDown() {
	close(m.shutDown)
	m.liveMgrCli.Close()
	m.liveStatsCli.Close()
	m.apiCenterGoCli.Close()
	m.apiCenterCli.Close()
	m.antiCli.Close()
	m.guildCli.Close()
	m.signanchorstatsCli.Close()
	m.anchorLevelCli.Close()
	m.channelCli.Close()
	m.recommendCli.Close()
	m.accountCli.Close()
	m.golddiamonnCli.Close()
	m.contractEventProducer.Close()
}

func (m *AnchorContractMgr) GetDyConfig() *conf.SDyConfigHandler {
	return m.dyConfig
}

func (m *AnchorContractMgr) GetContract(ctx context.Context, in *pb.GetContractReq) (*pb.GetContractResp, error) {
	out := &pb.GetContractResp{}
	offset := in.Offset
	limit := in.Limit

	if limit > 1000 {
		log.ErrorWithCtx(ctx, "GetContract fail. over limit. in=%+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "单次查询上限1000")
	}

	total, list, err := m.store.GetContractReadOnly(offset, limit)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetContract GetContractReadOnly fail %v", err)
		return out, err
	}
	out.Total = total
	if len(list) == 0 {
		return out, nil
	}

	uids := make([]uint32, 0, len(list))
	for _, info := range list {
		uids = append(uids, info.Uid)
	}

	uid2Identity, err := m.store.BatchGetAnchorIdentity(uids)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetContract BatchGetAnchorIdentity fail %v", err)
		return out, err
	}

	for _, info := range list {
		uid := info.Uid
		contract := &pb.ContractCacheInfo{
			Contract: &pb.ContractInfo{
				ActorUid:         uid,
				GuildId:          info.GuildId,
				SignTime:         uint32(info.SignTime.Unix()),
				ContractDuration: info.ContractDuration,
				ExpireTime:       uint32(info.ContractExpireTime.Unix()),
			},
		}
		identityInfo := uid2Identity[uid]
		for _, identity := range identityInfo {
			contract.AnchorIdentityList = append(contract.AnchorIdentityList, identity.IdentityType)
		}
		out.List = append(out.List, contract)
	}
	log.DebugfWithCtx(ctx, "GetContract in=%+v,out=%+v", in, out)
	return out, nil
}

// 批量查询签约信息
func (m *AnchorContractMgr) BatchGetContractInfo(ctx context.Context, in *pb.BatchGetContractInfoReq) (*pb.BatchGetContractInfoResp, error) {
	out := &pb.BatchGetContractInfoResp{}
	if len(in.GetUids()) == 0 {
		return out, nil
	}
	if sz := len(in.GetUids()); sz > 200 {
		log.WarnWithCtx(ctx, "BatchGetContractInfo too many uids size:%d", sz)
		return out, protocol.NewServerError(status.ErrRequestParamInvalid, "批量查询个数超过上限")
	}

	uid2ContractInfo := map[uint32]*pb.ContractCacheInfo{}
	list, notExistUids, err := m.cache.BatchGetUserContractInfo(in.GetUids())
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetContractInfo fail to cache.BatchGetUserContractInfo. uids:%+v, err:%v", in, err)
		return out, err
	}
	for uid, info := range list {
		if info.GetContract().GetGuildId() > 0 {
			uid2ContractInfo[info.GetContract().GetActorUid()] = info
		} else if m.isDoyenIdentity(info) {
			uid2ContractInfo[uid] = info
		}
	}

	if len(notExistUids) > 0 {

		uid2ContractInfoDB, err := m.BatchUpdateUserContractCacheInfo(notExistUids)
		if err != nil {
			log.ErrorWithCtx(ctx, "BatchGetContractInfo fail to m.BatchUpdateUserContractCacheInfo. uids:%+v, err:%v", in, err)
			return out, err
		}
		for uid, info := range uid2ContractInfoDB {
			uid2ContractInfo[uid] = info
		}
	}

	out.Uid2ContractInfo = uid2ContractInfo
	return out, nil
}

func (m *AnchorContractMgr) TestSignContract(ctx context.Context, in *pb.TestSignContractReq) (*pb.TestSignContractResp, error) {
	log.InfoWithCtx(ctx, "TestSignContract begin %+v", in)
	out := &pb.TestSignContractResp{}
	if !in.Certain {
		return out, nil
	}
	uid := in.Uid
	guildId := in.GuildId
	if uid == 0 || guildId == 0 {
		return out, nil
	}
	signTime := time.Now()
	if in.ObtainTime > 0 {
		signTime = time.Unix(int64(in.ObtainTime), 0)
	}

	contract, _, err := m.store.GetContractWithUid(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "TestSignContract GetContractWithUid fail %v, in=%+v", err, in)
		return out, err
	}

	err = m.store.Transaction(ctx, func(tx *gorm.DB) error {

		err := m.store.DelContract(tx, uid, contract.GuildId)
		if err != nil {
			log.ErrorWithCtx(ctx, "TestSignContract DelContract fail %v, in=%+v", err, in)
			return err
		}

		err = m.store.DelAnchorAllIdentity(tx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "TestSignContract DelAnchorAllIdentity fail %v, in=%+v", err, in)
			return err
		}

		if len(in.AnchorIdentityList) > 0 {
			err = m.store.AddContract(tx, &mysql.ContractInfo{
				Uid:                uid,
				GuildId:            guildId,
				IdentityNum:        "111111199306171503",
				ContractDuration:   36,
				SignTime:           signTime,
				ContractExpireTime: signTime.AddDate(0, 36, 0),
			})
			if err != nil {
				log.ErrorWithCtx(ctx, "TestSignContract AddContract fail %v, in=%+v", err, in)
				return err
			}

			for _, identy := range in.AnchorIdentityList {
				err = m.store.AddAnchorIdentity(tx, &mysql.AnchorIdentity{
					Uid:          uid,
					IdentityType: identy,
					GuildId:      guildId,
					ObtainTime:   signTime,
					UpdateTime:   signTime,
				})
				if err != nil {
					log.ErrorWithCtx(ctx, "TestSignContract AddAnchorIdentity fail %v, in=%+v", err, in)
					return err
				}
			}
		}

		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "TestSignContract Transaction fail %v, in=%+v", err, in)
		return out, err
	}

	m.cache.DelUserContractInfoV2(uid)

	return out, nil
}

func (m *AnchorContractMgr) GetAnchorAgentUid(ctx context.Context, in *pb.GetAnchorAgentUidReq) (*pb.GetAnchorAgentUidResp, error) {
	out := &pb.GetAnchorAgentUidResp{}
	if sz := len(in.Uids); sz > 200 {
		log.WarnWithCtx(ctx, "GetAnchorAgentUid too many uids size:%d", sz)
		return out, protocol.NewServerError(status.ErrRequestParamInvalid, "批量查询个数超过上限")
	}

	uid2IdentInfo, err := m.store.BatchGetAnchorIdentity(in.Uids)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorAgentUid BatchGetAnchorIdentity fail %v", err)
		return out, err
	}

	out.Uid2AgentUid = map[uint32]uint32{}
	for uid, identList := range uid2IdentInfo {
		for _, ident := range identList {
			if ident.AgentUid > 0 {
				out.Uid2AgentUid[uid] = ident.AgentUid
				break
			}
		}
	}

	log.DebugWithCtx(ctx, "GetAnchorAgentUid in=%v, out=%+v", in.Uids, out.Uid2AgentUid)
	return out, nil
}

// 查询签约用户身份变更记录
func (m *AnchorContractMgr) GetIdentityChangeHistory(ctx context.Context, in *pb.GetIdentityChangeHistoryReq) (*pb.GetIdentityChangeHistoryResp, error) {
	out := &pb.GetIdentityChangeHistoryResp{}
	uid := in.Uid

	// select * from tbl_anchor_identity_change_log where uid=2186354 and identity_type=2 order by id desc;
	list, err := m.store.GetAnchorIdentityChangeLogAll(uid, uint32(in.AnchorIdentity))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetIdentityChangeHistory GetAnchorIdentityChangeLogAll fail %v, uid=%d", err, uid)
		return out, err
	}

	obTimeMap := map[uint32]bool{}

	for _, ident := range list {
		log.DebugWithCtx(ctx, "GetIdentityChangeHistory ident=%+v", ident)
		if obTimeMap[uint32(ident.ObtainTime.Unix())] {
			continue
		}
		obTimeMap[uint32(ident.ObtainTime.Unix())] = true
		changeInfo := &pb.IdentityChangeInfo{
			Uid:          ident.Uid,
			GuildId:      ident.GuildId,
			IdentityType: ident.IdentityType,
			ObtainTime:   uint32(ident.ObtainTime.Unix()),
			ReclaimTime:  0,
		}
		if ident.ChangeType == uint32(pb.ANCHOR_IDENTITY_CHANGE_TYPE_ANCHOR_IDENTITY_CHANGE_TYPE_DEL) {
			changeInfo.ReclaimTime = uint32(ident.ChangeTime.Unix())
		}

		log.DebugWithCtx(ctx, "GetIdentityChangeHistory changeInfo=%+v", changeInfo)
		out.List = append(out.List, changeInfo)
	}

	log.DebugWithCtx(ctx, "GetIdentityChangeHistory in=%+v, out=%s", in, out.String())
	return out, nil
}

func (m *AnchorContractMgr) CheckCanApplySign(ctx context.Context, in *pb.CheckCanApplySignReq) (out *pb.CheckCanApplySignResp, err error) {
	out = &pb.CheckCanApplySignResp{}
	anchorUid := in.ActorUid
	guildId := in.GuildId
	ident := in.IdentityType
	identityNum := in.IdentityNum

	defer func() {
		log.InfoWithCtx(ctx, "CheckCanApplySign in=%+v, err=%+v", in, err)
	}()

	if anchorUid == 0 || identityNum == "" {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	if ident != uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_DOYEN) && guildId == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	now := time.Now()
	if ident != uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_DOYEN) {
		guildResp, gerr := m.guildCli.GetGuild(ctx, guildId)
		if gerr != nil {
			log.ErrorWithCtx(ctx, "CheckCanApplySign fail to GetGuild in:%+v, err:%v", in, gerr)
			return out, gerr
		}
		if accountSvr.IsInternalUid(ctx, anchorUid) && !accountSvr.IsInternalUid(ctx, guildResp.GetOwner()) {
			log.ErrorWithCtx(ctx, "CheckCanApplySign ActorUid:%v IsInternalUid guild%v", anchorUid, guildId)
			return out, protocol.NewExactServerError(nil, status.ErrContractApplySignIsInternalUid)
		} else if !accountSvr.IsInternalUid(ctx, in.ActorUid) && accountSvr.IsInternalUid(ctx, guildResp.GetOwner()) {
			log.ErrorWithCtx(ctx, "CheckCanApplySign Guild:%v IsInternalGuild guild%v", anchorUid, guildId)
			return out, protocol.NewExactServerError(nil, status.ErrContractApplySignIsInternalGuild)
		}
	}

	// check apply blacklist
	blackInfo, err := m.store.GetUserApplyBlacklist(anchorUid, ident)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckCanApplySign fail to GetUserApplyBlacklist in:%+v, err:%v", in, err)
		return out, err
	}

	if blackInfo.BeginTime.Before(now) && blackInfo.EndTime.After(now) {
		log.ErrorWithCtx(ctx, "CheckCanApplySign fail. User is in apply blacklist in:%+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrContractApplyBlacklistLimit)
	}

	if ident == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_DOYEN) { //平台达人检查
		err = m.CheckCanSignDoyen(ctx, anchorUid, identityNum)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckCanApplySign fail to CheckCanSignDoyen in:%+v, err:%v", in, err)
			return out, err
		}
		return out, nil
	}
	// 检查公会合作库
	bIn, err := m.CheckGuildIfInCoop(ctx, guildId, ident)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckCanApplySign fail to checkGuildIfInCoop in:%+v, err:%v", in, err)
		return out, err
	}
	if !bIn {
		log.ErrorWithCtx(ctx, "CheckCanApplySign fail. guild not in this libraryType in:%+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrContractApplyGuildNotCoopType)
	}

	// 已签约
	contract, err := m.GetUserContractCacheInfo(ctx, anchorUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckCanApplySign GetUserContractCacheInfo fail %v, in=%+v", err, in)
		return out, err
	}
	log.InfoWithCtx(ctx, "CheckCanApplySign anchorUid=%d GetUserContractCacheInfo=%+v", anchorUid, contract)
	signGuildId := contract.GetContract().GetGuildId()
	if signGuildId > 0 {
		if signGuildId != guildId {
			log.ErrorWithCtx(ctx, "CheckCanApplySign fail. Uid already sign another guild contract in:%+v contract=%+v", in, contract)
			return out, protocol.NewExactServerError(nil, status.ErrContractHandleConflict)
		}
		for _, identityType := range contract.GetAnchorIdentityList() {
			// 已拥有该身份
			if identityType == ident {
				log.ErrorWithCtx(ctx, "CheckCanApplySign fail. Uid already have this AnchorIdentity in:%+v", in)
				return out, protocol.NewExactServerError(nil, status.ErrContractApplyHaveIdentityType)
			}
		}
	} else {

		// 检查该身份证下是否有合约
		identContract, exist, err := m.store.GetContractWithIdentityNum(in.GetIdentityNum())
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckCanApplySign fail to GetContractWithIdentityNum in:%+v, err:%v", in, err)
			return out, err
		}
		if exist {
			log.ErrorWithCtx(ctx, "CheckCanApplySign fail. Identity_num already sign contract=%+v, in:%+v", identContract, in)
			return out, protocol.NewExactServerError(nil, status.ErrContractApplyIdentityLimit)
		}
	}

	// 申请记录
	err = m.ApplySignPreCheckApplyRecord(ctx, &pb.ApplySignContractReq{
		ActorUid:       anchorUid,
		GuildId:        guildId,
		IdentityNum:    identityNum,
		AnchorIdentity: ident,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckCanApplySign ApplySignPreCheckApplyRecord fail %v, in=%+v", err, in)
		return out, err
	}

	begin := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
	end := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, time.Local)
	changeTypeList := []uint32{
		uint32(pb.CONTRACT_CHANGE_TYPE_ENUM_CONTRACT_CHANGE_DEL),
		uint32(pb.CONTRACT_CHANGE_TYPE_ENUM_CONTRACT_CHANGE_TIMEOUT),
	}

	// 检查 uid 今天是否有解约的合约
	list, err := m.store.GetContractChangeLogWithTypes(anchorUid, changeTypeList, begin, end)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckCanApplySign fail to GetContractChangeLogWithTypes in:%+v, err:%v", in, err)
		return out, err
	}
	if !m.sc.TestMod && len(list) > 0 {
		log.ErrorWithCtx(ctx, "CheckCanApplySign fail. Today have cancel (ENUM_CONTRACT_CHANGE_DEL) in:%+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrContractApplyTodayLimit)
	}

	// 查这个身份证号今年的签约记录
	yearBegin := time.Date(now.Year(), 1, 1, 0, 0, 0, 0, time.Local)
	if m.sc.TestMod {
		yearBegin = time.Now().Add(-15 * time.Minute)
	}
	list, err = m.store.GetContractChangeLogWithIdNum(in.GetIdentityNum(), uint32(pb.CONTRACT_CHANGE_TYPE_ENUM_CONTRACT_CHANGE_ACCEPT), yearBegin, end)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckCanApplySign fail to GetContractChangeLogWithIdNum in:%+v, err:%v", in, err)
		return out, err
	}

	uidList := make([]uint32, 0, len(list))
	for _, item := range list {
		uidList = append(uidList, item.Uid)
	}

	noReasonCount := 0
	// 如果是娱乐房，还要查解约
	cancelLog, err := m.store.BatchGetCancelContractApplyLogList(uidList, yearBegin)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckCanApplySign fail to GetCancelContractApplyLogList in:%+v, err:%v", in, err)
		// 这里不用退出
	}
	cancelMap := make(map[time.Time]bool)
	for _, item := range cancelLog {
		if item.CancelType == uint32(pb.CancelContractType_CancelContractType_NoReason) && item.Status == uint32(pb.CancelContractStatus_CancelContractStatus_Finish) {
			cancelMap[item.SignTime] = true
		}
	}

	for _, item := range list {
		if cancelMap[item.SignTime] == true {
			noReasonCount++
		}
	}

	if noReasonCount > len(list) {
		noReasonCount = len(list)
	}

	if !m.sc.TestMod && signGuildId == 0 && uint32(len(list)-noReasonCount) >= m.sc.YearSignLimitCnt {
		log.ErrorWithCtx(ctx, "CheckCanApplySign fail. This year sign count is over limit in:%+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrContractYearSignLimit)
	}

	log.InfoWithCtx(ctx, "CheckCanApplySign sign count=%d, noReasonCount=%d, yearSignLimitCnt=%d", len(list), noReasonCount, m.sc.YearSignLimitCnt)

	// 付费解约过，限制检查
	if err = m.CheckPayCancelSignLimit(ctx, anchorUid, guildId, identityNum); err != nil {
		log.ErrorWithCtx(ctx, "CheckCanApplySign fail to CheckPayCancelSignLimit in:%+v, err:%v", in, err)
		return out, err
	}

	// 检查付费解约冻结的情况
	expireSec := m.newDyConf.GetContractDyConf().PayCancelContractParam.LockAmountExpireS
	_, amount, err := m.cache.GetLockPayCancelAmount(ctx, guildId, anchorUid, expireSec)
	if amount != 0 {
		log.ErrorWithCtx(ctx, "CheckCanApplySign fail to GetLockPayCancelAmount in:%+v, err:%v", in, err)
		return out, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "付费解约金额冻结期间无法提起签约申请")
	}

	return out, nil
}

func (m *AnchorContractMgr) HandleIncrGuildChannelFee(presentEvent *kafkapresent.PresentEvent) {
	// 2024-02-01 00:00:00 开始统计
	if presentEvent.GetSendTime() >= 1706716800 || m.sc.TestMod {

		sendTm := time.Unix(int64(presentEvent.SendTime), 0)
		yearmonth := sendTm.Year()*100 + int(sendTm.Month())
		totalPrice := presentEvent.Price * presentEvent.ItemCount

		err := m.store.IncrGuildChannelFee(presentEvent.GuildId, presentEvent.ChannelId, uint32(yearmonth), totalPrice)
		if err != nil {
			log.Errorf("HandleIncrGuildChannelFee Failed to IncrGuildChannelFee presentEvent:%+v err(%v)", presentEvent, err)
			err = m.store.IncrGuildChannelFee(presentEvent.GuildId, presentEvent.ChannelId, uint32(yearmonth), totalPrice)
			if err != nil {
				log.Errorf("HandleIncrGuildChannelFee retry Failed to IncrGuildChannelFee presentEvent:%+v err(%v)", presentEvent, err)
			}
		}

	} else {
		log.Debugf("HandleIncrGuildChannelFee ignore IncrGuildChannelFee, order=%+v", presentEvent)
	}
}

// UpdateContractWorkerType 更新签约用户从业者类型
func (m *AnchorContractMgr) UpdateContractWorkerType(ctx context.Context, uid uint32, workerType uint32) error {
	contract, err := m.GetUserContractCacheInfo(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateContractWorkerType GetUserContractCacheInfo fail %v, uid=%d", err, uid)
		return err
	}

	if contract.GetContract().GetGuildId() == 0 {
		log.ErrorWithCtx(ctx, "UpdateContractWorkerType fail. uid not sign contract uid=%d", uid)
		return nil
	}

	//err = m.store.UpdateContractWorkerType(uid, workerType)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateContractWorkerType fail %v, uid=%d, workerType=%d", err, uid, workerType)
		return err
	}

	err = m.cache.DelUserContractInfoV2(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateContractWorkerType fail to DelUserContractInfoV2 uid=%d, err=%v", uid, err)
	} else {
		log.InfoWithCtx(ctx, "UpdateContractWorkerType uid=%d, workerType=%d", uid, workerType)
	}
	return nil
}

func (m *AnchorContractMgr) CheckAndTransforUrls(ctx context.Context, urls []string) (map[string]string, error) {
	url2TargetUrl := make(map[string]string)
	if len(urls) == 0 {
		return url2TargetUrl, nil
	}

	hasMap := make(map[string]struct{})
	transforUrls := make([]string, 0)
	for _, url := range urls {
		if _, ok := hasMap[url]; ok {
			continue
		}
		hasMap[url] = struct{}{}
		if strings.HasPrefix(url, "http://") || strings.HasPrefix(url, "https://") {
			url2TargetUrl[url] = url
			continue
		}
		transforUrls = append(transforUrls, url)
	}

	if len(transforUrls) > 0 {
		fileUrlMap, err := m.GuildManagementSvrCli.BatchGetUploadFileUrl(ctx, transforUrls)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckAndTransforUrls failed to BatchGetUploadFileUrl [%+v], err %+v", transforUrls, err)
			return url2TargetUrl, err
		}
		//if len(fileUrlMap) != len(transforUrls) {
		//	log.ErrorWithCtx(ctx, "CheckAndTransforUrls failed to BatchGetUploadFileUrl [%+v], urlRsp: %+v", transforUrls, fileUrlMap)
		//	return url2TargetUrl, errors.New("CheckAndTransforUrls BatchGetUploadFileUrl failed")
		//}
		for key, targetUrl := range fileUrlMap {
			url2TargetUrl[key] = targetUrl
		}

		// 也有可能是签约自己的桶
		tmpMap, sErr := m.GetObsTempUrl(ctx, urls, 86400)
		if sErr != nil {
			log.ErrorWithCtx(ctx, "CheckAndTransforUrls failed to GetObsTempUrl [%+v], err %+v", urls, sErr)
			return url2TargetUrl, sErr
		}

		for key, targetUrl := range tmpMap {
			if _, ok := url2TargetUrl[key]; !ok {
				url2TargetUrl[key] = targetUrl
			}
		}
	}
	log.InfoWithCtx(ctx, "CheckAndTransforUrls out=%+v", url2TargetUrl)
	return url2TargetUrl, nil
}

func NewAnchorContractMgrTest(sc *conf.ServiceConfigT) (*AnchorContractMgr, error) {
	if sc.RedisConfig.PoolSize == 0 {
		sc.RedisConfig.PoolSize = 300
	}
	redisClient := redis.NewClient(&redis.Options{
		Network:            sc.GetRedisConfig().Protocol,
		Addr:               sc.GetRedisConfig().Addr(),
		PoolSize:           sc.GetRedisConfig().PoolSize,
		IdleCheckFrequency: sc.GetRedisConfig().IdleCheckFrequency(),
		DB:                 sc.GetRedisConfig().DB,
	})
	cacheClient, err := cache.NewAnchorContractCache(redisClient, nil)
	if err != nil {
		log.Errorf("NewAnchorContractCache err:%v", err)
		return nil, err
	}

	mysqlCfg := sc.GetMysqlConfig()
	mysqlDb, err := gorm.Open("mysql", mysqlCfg.ConnectionString())
	if err != nil {
		log.Errorf("Failed to create mysql %v", err)
		return nil, err
	}
	mysqlDb.DB().SetMaxOpenConns(80)
	mysqlDb.DB().SetMaxIdleConns(80)
	mysqlDb.DB().SetConnMaxLifetime(time.Minute * 5)

	readonlyDb, err := gorm.Open("mysql", sc.GetMysqlReadonlyConfig().ConnectionString())
	if err != nil {
		log.Errorf("Failed to create mysql %v", err)
		return nil, err
	}
	readonlyDb.DB().SetMaxOpenConns(50)
	readonlyDb.DB().SetMaxIdleConns(50)
	readonlyDb.DB().SetConnMaxLifetime(time.Minute)
	mysqlStore := mysql.NewMysql(mysqlDb.Debug(), readonlyDb.Debug())

	channelCli := channel.NewClient()
	liveMgrCli, _ := channellivemgr.NewClient()
	liveStatsCli, _ := channellivestats.NewClient()
	accountCli, _ := account.NewClient()
	golddiamonnCli, _ := golddiamonn.NewClient()
	apiCenterGoCli, _ := apicenter_go.NewClient()
	recommendCli := entertainmentrecommendback.NewClient()
	signanchorstatsCli := signanchorstats.NewClient()
	anchorLevelCli, _ := anchor_level.NewClient()
	anchorCheckCli, _ := anchorcheck.NewClient()
	rcmdOperatingPlatformCli, _ := operating_platform.NewClient()
	ttcProxyCli := ttc_proxy.NewIClient()
	GreenBaBaClient := greenbaba.NewIClient()

	dyConfig, err := conf.NewConfigHandler(conf.DyconfigPath)
	if err != nil {
		log.Errorln(err)
	}

	err = dyConfig.Start()
	if err != nil {
		log.Errorln(err)
	}
	anchorCheckDyconfig := anchorCheckConf.NewConfigHandler(anchorCheckConf.DyconfigPath)
	if err := anchorCheckDyconfig.Start(); err != nil {
		log.Errorf("anchorCheckConf dyconfig.Start() fail %v", err)
		return nil, err
	}

	env := sc.Env
	push := sc.Push
	warnFeishuUrl := sc.WarnFeishuUrl
	log.Infof("reporter env=%q push=%v warnFeishuUrl=%q", env, push, warnFeishuUrl)
	reporter := report.NewFeiShuReporterV2(warnFeishuUrl, env, push)

	mgr := &AnchorContractMgr{
		sc:                       sc,
		dyConfig:                 dyConfig,
		anchorCheckDyconfig:      anchorCheckDyconfig,
		shutDown:                 make(chan interface{}),
		cache:                    cacheClient,
		store:                    mysqlStore,
		reporter:                 reporter,
		channelCli:               channelCli,
		liveMgrCli:               liveMgrCli,
		liveStatsCli:             liveStatsCli,
		apiCenterGoCli:           apiCenterGoCli,
		apiCenterCli:             apicenter.NewClient(),
		guildCli:                 guild.NewClient(),
		accountCli:               accountCli,
		golddiamonnCli:           golddiamonnCli,
		antiCli:                  anti.NewClient(),
		tBeanCli:                 models.InitTBean(sc.TBeanCliConf.TBeanURLPrefix, sc.TBeanCliConf.TBeanSecretKey, sc.TBeanCliConf.TBeanCaller),
		recommendCli:             recommendCli,
		signanchorstatsCli:       signanchorstatsCli,
		anchorLevelCli:           anchorLevelCli,
		anchorCheckCli:           anchorCheckCli,
		rcmdOperatingPlatformCli: rcmdOperatingPlatformCli,
		TtcProxyCli:              ttcProxyCli,
		GreenBaBaClient:          GreenBaBaClient,
		userAuthHistoryCli:       user_auth_history.NewIClient(),
	}
	return mgr, nil
}

func (m *AnchorContractMgr) RecordAnchorNoticeHandle(c context0.Context, req *pb.RecordAnchorNoticeHandleReq) (*pb.RecordAnchorNoticeHandleResp, error) {
	info := &mysql.HandleNoticeLog{
		GuildId:       req.GetNotice().GetGuildId(),
		Uid:           req.GetNotice().GetUid(),
		NoticeContent: req.GetNotice().GetNoticeContent(),
		ApplyTime:     time.Unix(int64(req.GetNotice().GetApplyTime()), 0),
		Operator:      req.GetNotice().GetOperator(),
		UpdateTime:    time.Unix(int64(req.GetNotice().GetUpdateTime()), 0),
		OperateType:   req.GetNotice().GetOperateType(),
		Remark:        req.GetNotice().GetRemark(),
	}

	err := m.store.AddHandleNoticeLog(nil, info)

	if err != nil {
		log.ErrorWithCtx(c, "RecordAnchorNoticeHandle AddHandleNoticeLog fail info%v err %v", info, err)
		return &pb.RecordAnchorNoticeHandleResp{}, err
	}

	return &pb.RecordAnchorNoticeHandleResp{}, nil
}

func (m *AnchorContractMgr) GetAnchorNoticeHandleHistory(ctx context.Context, in *pb.GetAnchorNoticeHandleHistoryReq) (*pb.GetAnchorNoticeHandleHistoryResp, error) {
	out := &pb.GetAnchorNoticeHandleHistoryResp{}

	count, list, err := m.store.GetHandleNoticeLog(nil, in.GuildId, in.Uid, in.GetPage(), in.GetPageSize())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorNoticeHandleHistory GetHandleNoticeLog fail %v", err)
		return out, err
	}

	out.Total = count
	out.NoticeList = make([]*pb.AnchorNoticeHandle, 0)
	for _, info := range list {
		out.NoticeList = append(out.NoticeList, &pb.AnchorNoticeHandle{
			GuildId:       info.GuildId,
			Uid:           info.Uid,
			NoticeContent: info.NoticeContent,
			ApplyTime:     uint32(info.ApplyTime.Unix()),
			UpdateTime:    uint32(info.UpdateTime.Unix()),
			Operator:      info.Operator,
			OperateType:   info.OperateType,
			Remark:        info.Remark,
		})
	}

	log.DebugWithCtx(ctx, "GetAnchorNoticeHandleHistory in:%+v, out:%+v", in, out)
	return out, nil

}

func (m *AnchorContractMgr) GetNegotiateReasonType(ctx context.Context, req *pb.GetNegotiateReasonTypeReq) (*pb.GetNegotiateReasonTypeResp, error) {
	out := &pb.GetNegotiateReasonTypeResp{}
	out.NegotiateReasonType = m.newDyConf.GetContractDyConf().RejectReasonList
	return out, nil
}
