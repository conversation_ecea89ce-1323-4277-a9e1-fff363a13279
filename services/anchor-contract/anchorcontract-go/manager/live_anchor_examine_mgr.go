package manager

import (
	"context"
	"fmt"
	"time"

	set "github.com/scylladb/go-set"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/mapreduce"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/common/status"
	accountPB "golang.52tt.com/protocol/services/accountsvr"
	pb "golang.52tt.com/protocol/services/anchorcontract-go"
	channellivemgrPB "golang.52tt.com/protocol/services/channellivemgr"
	channelsvr "golang.52tt.com/protocol/services/channelsvr"
	entertainmentRecommendBack "golang.52tt.com/protocol/services/entertainmentRecommendBackSvr"
	guildPB "golang.52tt.com/protocol/services/guildsvr"
	"golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql"
	"golang.52tt.com/services/anchor-contract/anchorcontract-go/util"
)

func (m *AnchorContractMgr) GetRadioLiveAnchorExamine(ctx context.Context, in *pb.GetRadioLiveAnchorExamineReq) (*pb.GetRadioLiveAnchorExamineResp, error) {
	si, _ := protogrpc.ServiceInfoFromContext(ctx)

	out := &pb.GetRadioLiveAnchorExamineResp{}
	uid := in.GetUid()
	if uid == 0 {
		return out, nil
	}

	ttl, err := m.cache.GetUserExamineTTL(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "mgr.GetRadioLiveAnchorExamine failed to cache.GetUserExamineTTL, err %+v uid %d", err, uid)
		return out, err
	}

	out.Ttl = ttl
	log.InfoWithCtx(ctx, "mgr.GetRadioLiveAnchorExamine uid %d, ttl %d, version:%s terminal:%s(%d) device:%02X",
		uid, ttl, protocol.ClientVersion(si.ClientVersion), protocol.NewTerminalType(si.TerminalType), si.MarketID, si.DeviceID)
	return out, nil
}

func (m *AnchorContractMgr) UpdateRadioLiveAnchorExamine(ctx context.Context, in *pb.UpdateRadioLiveAnchorExamineReq) (*pb.UpdateRadioLiveAnchorExamineResp, error) {
	out := &pb.UpdateRadioLiveAnchorExamineResp{}
	uid := in.GetUid()
	if uid == 0 {
		return out, nil
	}

	err := m.cache.RemoveExamineNotify(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "mgr.UpdateRadioLiveAnchorExamine failed to cache.RemoveExamineNotify, err %+v uid %d", err, uid)
	}

	// 可能是超时提交，需要删除过期未提交记录
	rowsAffected, err := m.store.DelAnchorExamineExpireRecord(uid, time.Now())
	if err != nil {
		log.ErrorWithCtx(ctx, "mgr.UpdateRadioLiveAnchorExamine failed to store.DelAnchorExamineExpireRecord, err %+v uid %d", err, uid)
	}

	log.InfoWithCtx(ctx, "mgr.UpdateRadioLiveAnchorExamine uid %d, rowsAffected %d", uid, rowsAffected)
	return out, nil
}

func (m *AnchorContractMgr) GetGuildLevel(ctx context.Context, in *pb.GetGuildLevelReq) (*pb.GetGuildLevelResp, error) {
	out := &pb.GetGuildLevelResp{Info: &pb.GuildLevelInfo{}}

	log.DebugWithCtx(ctx, "mgr.GetGuildLevel in %+v, out %+v", in, out)
	return out, nil
}

func (m *AnchorContractMgr) GetNotUploadExamineAnchorList(ctx context.Context, in *pb.GetNotUploadExamineAnchorListReq) (*pb.GetNotUploadExamineAnchorListResp, error) {
	out := &pb.GetNotUploadExamineAnchorListResp{}

	log.DebugWithCtx(ctx, "GetNotUploadExamineAnchorList in %+v", in)

	list, err := m.store.GetAnchorExamineNotUploadRecord(in.GetBeginTs(), in.GetEndTs())
	if err != nil {
		log.ErrorWithCtx(ctx, "mgr.GetNotUploadExamineAnchorList fail %+v", err)
		return out, err
	}

	infoList := make([]*pb.ExamineAnchorInfo, 0, len(list))
	for _, info := range list {
		ctx2, cancel := context.WithTimeout(context.Background(), time.Second*3)
		defer cancel()

		user, err := m.accountCli.GetUserByUid(ctx2, info.Uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetNotUploadExamineAnchorList failed accountCli.GetUserByUid err %+v", err)
			return out, err
		}

		//ttid := user.GetUsername()
		//if strings.HasPrefix(user.GetUsername(), "tt") {
		//	ttid = ttid[2:]
		//}

		infoList = append(infoList, &pb.ExamineAnchorInfo{
			Uid: info.Uid,
			//GuildId:  info.GuildId,
			Ttid:     user.GetAlias(),
			Nickname: user.GetNickname(),
		})
	}

	out.InfoList = infoList
	log.DebugWithCtx(ctx, "mgr.GetNotUploadExamineAnchorList in %+v out %+v", in, out)
	return out, nil
}

func (m *AnchorContractMgr) CheckUserGreatLiveAnchor(ctx context.Context, in *pb.CheckUserGreatLiveAnchorReq) (out *pb.CheckUserGreatLiveAnchorResp, err error) {
	out = &pb.CheckUserGreatLiveAnchorResp{}
	uid := in.GetUid()
	guildId := in.GetGuildId()
	defer func() {
		log.InfoWithCtx(ctx, "CheckUserGreatLiveAnchor status=%v uid=%d guildId=%d, err=%+v",
			out.AnchorApplyCheckStatus, uid, guildId, err)
	}()

	if uid == 0 || guildId == 0 {
		return out, nil
	}
	out.AnchorApplyCheckStatus = pb.ANCHOR_APPLY_CHECK_STATUS_ANCHOR_APPLY_CHECK_STATUS_ENABLE

	isRisk, code, err := m.CheckSignAnchorRiskAccount(ctx, uid, guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckUserGreatLiveAnchor CheckSignAnchorRiskAccount fail %v, uid=%d,guildId=%d", err, uid, guildId)
		return out, err
	}
	if isRisk {
		out.AnchorApplyCheckStatus = pb.ANCHOR_APPLY_CHECK_STATUS_ANCHOR_APPLY_CHECK_STATUS_DISABLE
		err = protocol.NewExactServerError(nil, code)
		return out, err
	}
	return out, nil
}

func (m *AnchorContractMgr) GetLiveAnchorExamineNotUpload(ctx context.Context, in *pb.GetLiveAnchorExamineNotUploadReq) (
	*pb.GetLiveAnchorExamineNotUploadResp, error) {
	log.Debugf("GetLiveAnchorExamineNotUpload bein %d %v %d %d", in.Type, in.Ids, in.Page, in.PageSize)

	out := &pb.GetLiveAnchorExamineNotUploadResp{}
	offset := in.Page * in.PageSize
	limit := in.PageSize
	var err error
	uids := in.Ids
	if in.Type == uint32(pb.LiveAnchorExamineQueryType_ttid) {
		ttids := []string{}
		for _, id := range in.Ids {
			ttids = append(ttids, fmt.Sprintf("%d", id))
		}
		resp, err := m.accountCli.BatchQueryUidListByAlias(ctx, ttids)
		if err != nil {
			log.Errorf("GetLiveAnchorExamineNotUpload GetUidByName fail %v, in %+v", err, in)
			return out, nil
		}
		uids = []uint32{}
		for _, info := range resp {
			uids = append(uids, info.Uid)
		}
		if len(uids) == 0 {
			return out, nil
		}
	}

	allUids, uid2TTL, err := m.cache.GetAllUserExamine()
	if err != nil {
		log.Errorf("GetLiveAnchorExamineNotUpload GetAllUserExamine fail %v, in %+v", err, in)
		return out, nil
	}
	log.Debugf("GetLiveAnchorExamineNotUpload cache total %d allUids %v", len(allUids), allUids)

	// order by sign_time
	fetchUids, contractList, uid2contract, err := m.store.GetContractByUids(allUids, offset, limit)
	if err != nil {
		log.Errorf("GetLiveAnchorExamineNotUpload GetContractByUids fail %v, in %+v", err, in)
		return out, nil
	}
	out.Count = uint32(len(allUids))

	if len(uids) > 0 {
		getUids := []uint32{}
		for _, uid := range uids {
			if uid2TTL[uid] > 0 {
				getUids = append(getUids, uid)
			}
		}
		if len(getUids) == 0 {
			out.Count = 0
			return out, nil
		}

		// order by sign_time
		fetchUids, contractList, uid2contract, err = m.store.GetContractByUids(getUids, offset, limit)
		if err != nil {
			log.Errorf("GetLiveAnchorExamineNotUpload GetContractByUids fail %v, in %+v", err, in)
			return out, nil
		}

		log.Debugf("GetLiveAnchorExamineNotUpload uid %v", uids)
		if len(fetchUids) == 0 {
			out.Count = 0
			return out, nil
		}
		out.Count = uint32(len(fetchUids))
	}

	var (
		uid2UserInfo = map[uint32]*accountPB.UserResp{}
		uid2liveInfo = map[uint32]*channellivemgrPB.AnchorInfo{}
		cids         = []uint32{}
	)

	fillWiths := []func() error{}
	fillWiths = append(fillWiths,
		func() error {
			uid2UserInfo, err = util.GetUsersMap(m.accountCli, ctx, fetchUids)
			return err
		},
		func() error {
			uid2liveInfo, cids, err = util.GetLiveAnchorInfo(m.liveMgrCli, ctx, fetchUids)
			return err
		},
	)
	err = mapreduce.Finish(fillWiths...)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLiveAnchorExamineNotUpload mapreduce fail err:%v", err)
		return out, err
	}

	guildIds := []uint32{}
	for _, info := range uid2contract {
		guildIds = append(guildIds, info.GuildId)
	}

	var (
		channelId2Info = map[uint32]*channelsvr.ChannelSimpleInfo{}
		cid2tag        = map[uint32]*entertainmentRecommendBack.ChannelTagConfigInfo{}
		guildId2Info   = map[uint32]*guildPB.GuildResp{}
	)
	fillWiths = []func() error{}
	fillWiths = append(fillWiths,
		func() error {
			channelId2Info, err = util.BatchGetChannelSimpleInfo(m.channelCli, ctx, cids)
			return err
		},
		func() error {
			cid2tag, err = util.BatchGetChannelTag(m.recommendCli, ctx, cids)
			return err
		},
		func() error {
			guildId2Info, err = util.GetGuildBat(m.guildCli, ctx, guildIds)
			return err
		},
	)
	err = mapreduce.Finish(fillWiths...)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLiveAnchorExamineNotUpload mapreduce fail err:%v", err)
		return out, err
	}

	for _, contract := range contractList {
		uid := contract.Uid
		cid := uid2liveInfo[uid].GetChannelId()
		guildId := uid2contract[uid].GuildId
		guildshortId := guildId
		if guildId2Info[guildId].GetShortId() > 0 {
			guildshortId = guildId2Info[guildId].GetShortId()
		}
		out.List = append(out.List, &pb.GetLiveAnchorExamineNotUploadRespInfo{
			Uid:           uid,
			Ttid:          uid2UserInfo[uid].GetAlias(),
			Nickname:      uid2UserInfo[uid].GetNickname(),
			GuildId:       guildId,
			GuildShortId:  guildshortId,
			GuildName:     guildId2Info[guildId].GetName(),
			DisplayId:     channelId2Info[cid].GetDisplayId(),
			ChannelViewId: channelId2Info[cid].GetChannelViewId(),
			Tag:           cid2tag[cid].GetName(),
			CreateTime:    uint32(contract.SignTime.Unix()),
		})
	}
	return out, nil
}

func (m *AnchorContractMgr) ListGuildSignAnchorInfo(ctx context.Context, in *pb.ListGuildSignAnchorInfoReq) (*pb.ListGuildSignAnchorInfoResp, error) {
	out := &pb.ListGuildSignAnchorInfoResp{}
	log.DebugfWithCtx(ctx, "ListGuildSignAnchorInfo in=%+v", in)
	guildIds := []uint32{}
	if len(in.GuildIdList) > 0 {
		guildIds = append(guildIds, in.GuildIdList...)
	}
	if len(in.GuildShortIdList) > 0 {
		guildResp, err := m.guildCli.GetGuildBat(ctx, 0, &guildPB.GetGuildBatReq{GuildIdList: in.GuildShortIdList})
		if err != nil {
			log.ErrorWithCtx(ctx, "ListGuildSignAnchorInfo GetGuildBat fail %v in=%+v", err, in)
		}
		for _, info := range guildResp.GetGuildList() {
			guildIds = append(guildIds, info.GuildId)
		}
	}
	guildIds = set.NewUint32Set(guildIds...).List()
	log.DebugfWithCtx(ctx, "ListGuildSignAnchorInfo guildIds=%v", guildIds)

	total, list, err := m.store.GetGuildSignAnchorInfoList(nil, "", guildIds, in.Page*in.PageSize, in.PageSize)
	if err != nil {
		log.ErrorWithCtx(ctx, "ListGuildSignAnchorInfo GetGuildSignAnchorInfoList fail %v in=%+v", err, in)
		return out, err
	}
	out.Total = total
	for _, info := range list {
		guildResp, err := m.guildCli.GetGuild(ctx, info.GuildId)
		if err != nil {
			log.ErrorWithCtx(ctx, "ListGuildSignAnchorInfo GetGuild fail %v GuildId=%d", err, info.GuildId)
			return out, err
		}
		shortId := info.GuildId
		if guildResp.GetShortId() > 0 {
			shortId = guildResp.GetShortId()
		}

		year, month := int(0), int(0)
		fmt.Sscanf(info.Yearmonth, "%d-%d", &year, &month)

		monthBegin := time.Date(year, time.Month(month), 1, 0, 0, 0, 0, time.Local)
		monthEnd := time.Date(year, time.Month(month)+1, 1, 0, 0, 0, 0, time.Local).Add(-time.Second)

		monthUsedCnt, _, err2 := m.store.GetGuildSignAnchorTimeRangeCnt(nil, info.GuildId, monthBegin, monthEnd, time.Now(), false)
		if err2 != nil {
			log.Errorf("ListGuildSignAnchorInfo GetGuildSignAnchorTimeRangeCnt fail %v,info=%+v", err2, info)
			return out, err2
		}
		out.List = append(out.List, &pb.GuildSignAnchorInfo{
			GuildId:             info.GuildId,
			GuildShortId:        shortId,
			Yearmonth:           info.Yearmonth,
			MonthBefore_25ThCnt: info.MonthBefore25thCnt,
			MonthCnt:            info.MonthCnt,
			MonthUseCnt:         monthUsedCnt,
			Operator:            info.Operator,
			UpdateTime:          uint32(info.UpdateTime.Unix()),
		})
	}
	return out, nil
}

func (m *AnchorContractMgr) AddGuildSignAnchorInfo(ctx context.Context, in *pb.AddGuildSignAnchorInfoReq) (*pb.AddGuildSignAnchorInfoResp, error) {
	out := &pb.AddGuildSignAnchorInfoResp{}
	log.InfoWithCtx(ctx, "AddGuildSignAnchorInfo begin total=%d,in=%+v", len(in.List), in.List)

	duplicateKeyMap := map[string]bool{}
	for _, info := range in.List {
		err := m.AddGuildSignAnchorInfoPreCheck(ctx, info)
		if err != nil {
			log.ErrorWithCtx(ctx, "AddGuildSignAnchorInfo check fail. err %v, info %+v", err, info)
			return out, err
		}

		key := fmt.Sprintf("%d_%s", info.GuildId, info.Yearmonth)
		if duplicateKeyMap[key] {
			err = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, fmt.Sprintf("公会ID:%d %s 重复配置", info.GuildId, info.Yearmonth))
			log.ErrorWithCtx(ctx, "AddGuildSignAnchorInfo check2 fail. err %v, info %+v", err, info)
			return out, err
		} else {
			duplicateKeyMap[key] = true
		}
	}

	for _, info := range in.List {
		log.InfoWithCtx(ctx, "AddGuildSignAnchorInfo guildId=%d,YearMonth=%s,MonthBefore25thCnt=%d,MonthCnt=%d,Operator=%s",
			info.GuildId, info.Yearmonth, info.MonthBefore_25ThCnt, info.MonthCnt, info.Operator)

		err := m.store.AddGuildSignAnchorInfo(nil, &mysql.GuildSignAnchorInfo{
			GuildId:            info.GuildId,
			Yearmonth:          info.Yearmonth,
			MonthBefore25thCnt: info.MonthBefore_25ThCnt,
			MonthCnt:           info.MonthCnt,
			Operator:           info.Operator,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "AddGuildSignAnchorInfo Transaction AddGuildSignAnchorInfo fail. err %v, info %+v",
				err, info)
			return out, err
		}
	}
	return out, nil
}

func (m *AnchorContractMgr) AddGuildSignAnchorInfoPreCheck(ctx context.Context, info *pb.GuildSignAnchorInfo) error {
	if info.GuildId == 0 {
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "请输入正确的公会ID")
	}
	if info.Yearmonth == "" {
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	if info.MonthCnt < info.MonthBefore_25ThCnt*25 {
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "请输入正确的本月上限数量")
	}

	_, err := m.guildCli.GetGuild(ctx, info.GuildId)
	if err != nil {
		log.Errorf("AddGuildSignAnchorInfoPreCheck GetGuild fail %v, info=%+v", err, info)
		if err.Code() == status.ErrGuildNotExist {
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "请输入正确的公会ID")
		}
		return err
	}

	now := time.Now()
	yearmonth := fmt.Sprintf("%d-%02d", now.Year(), now.Month())
	if info.Yearmonth == yearmonth {
		monthBegin := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
		monthEnd := time.Date(now.Year(), now.Month()+1, 1, 0, 0, 0, 0, time.Local).Add(-time.Second)

		monthUsedCnt, _, err2 := m.store.GetGuildSignAnchorTimeRangeCnt(nil, info.GuildId, monthBegin, monthEnd, now, false)
		if err2 != nil {
			log.Errorf("AddGuildSignAnchorInfoPreCheck GetGuildSignAnchorTimeRangeCnt fail %v, info=%+v", err2, info)
			return err2
		}
		log.Debugf("AddGuildSignAnchorInfoPreCheck GetGuildSignAnchorTimeRangeCnt guildId=%d monthUsedCnt=%d info.MonthCnt=%d",
			info.GuildId, monthUsedCnt, info.MonthCnt)
		if info.MonthCnt < monthUsedCnt {
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "请输入正确的本月上限数量")
		}
	}
	return nil
}

// 查公会审批主播限额状态
func (m *AnchorContractMgr) GetGuildSignAnchorLimitStatus(ctx context.Context, guildId uint32) (
	status pb.AUDIO_LILMIT_STATUS, limitInfo *mysql.GuildSignAnchorInfo, monthUsedCnt, nowDayUsedCnt uint32, err error) {

	status = pb.AUDIO_LILMIT_STATUS_AUDIO_LILMIT_STATUS_NONE
	now := time.Now()
	monthBegin := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
	monthEnd := time.Date(now.Year(), now.Month()+1, 1, 0, 0, 0, 0, time.Local).Add(-time.Second)

	yearmonth := fmt.Sprintf("%d-%02d", now.Year(), now.Month())
	exist, info, err := m.store.GetGuildSignAnchorInfo(nil, guildId, yearmonth)
	if err != nil {
		log.Errorf("GetGuildSignAnchorLimitStatus GetGuildSignAnchorInfo fail %v,guildId=%d", err, guildId)
		return
	}
	log.DebugfWithCtx(ctx, "GetGuildSignAnchorLimitStatus GetGuildSignAnchorInfo exist=%v info=%+v, guildId=%d", exist, info, guildId)
	limitInfo = info

	// zrl: 如果公会没有在这个后台设置名额数量，那么该公会就是没有限制啊
	if !exist {
		log.InfoWithCtx(ctx, "GetGuildSignAnchorLimitStatus no limit. guildId=%d", guildId)
		return
	}

	monthUsedCnt, nowDayUsedCnt, err = m.store.GetGuildSignAnchorTimeRangeCnt(nil, guildId, monthBegin, monthEnd, now, false)
	if err != nil {
		log.Errorf("GetGuildSignAnchorLimitStatus GetGuildSignAnchorTimeRangeCnt fail %v,guildId=%d", err, guildId)
		return
	}

	log.InfoWithCtx(ctx, "GetGuildSignAnchorLimitStatus guildId=%d yearmonth=%s monthUsedCnt=%d nowDayUsedCnt=%d MonthBefore25thCnt=%d MonthCnt=%d",
		guildId, yearmonth, monthUsedCnt, nowDayUsedCnt, info.MonthBefore25thCnt, info.MonthCnt)
	day := 25
	if m.dyConfig.GetGuildSignAnchorLimitDayTh() > 0 {
		day = int(m.dyConfig.GetGuildSignAnchorLimitDayTh())
		log.InfoWithCtx(ctx, "GetGuildSignAnchorLimitStatus GetGuildSignAnchorLimitDayTh=%d", day)
	}
	if now.Day() <= day {
		if nowDayUsedCnt >= info.MonthBefore25thCnt {
			status = pb.AUDIO_LILMIT_STATUS_AUDIO_LILMIT_STATUS_DAY
			return
		}
	}
	if monthUsedCnt >= info.MonthCnt {
		status = pb.AUDIO_LILMIT_STATUS_AUDIO_LILMIT_STATUS_MONTH
		return
	}
	status = pb.AUDIO_LILMIT_STATUS_AUDIO_LILMIT_STATUS_NONE
	return
}

/*
风险账号定义：（满足任一条件即可）
  - 账号（UID）对应实名认证信息的年龄≥50岁（精确到年份,本年年份-出生年份）
  - 账号（UID）有疑似小号①
  - 账号（UID）近30天累充值金额≥5000元
  - 账号（UID）直播间（CID）处于封禁状态   (封禁表是哪里维护？)
*/
func (m *AnchorContractMgr) CheckSignAnchorRiskAccount(ctx context.Context, uid, signGuildId uint32) (bool, int, error) {
	if m.sc.TestMod {
		log.InfoWithCtx(ctx, "CheckSignAnchorRiskAccount test pass. uid=%d", uid)
		return false, 0, nil
	}

	riskConf := m.dyConfig.GetSignAnchorRiskAccount()

	// 实名年龄
	realNameAuthInfoV2Resp, serr := m.TtcProxyCli.GetUserRealNameAuthInfoV2(ctx, uint64(uid), false, false)
	if serr != nil {
		log.ErrorWithCtx(ctx, "CheckSignAnchorRiskAccount GetUserRealNameAuthInfoV2 fail %v, uid=%d signGuildId=%d",
			serr, uid, signGuildId)
		return false, 0, serr
	}
	log.DebugfWithCtx(ctx, "CheckSignAnchorRiskAccount GetUserRealNameAuthInfoV2 uid=%d age=%d signGuildId=%d",
		uid, realNameAuthInfoV2Resp.GetAge(), signGuildId)
	if realNameAuthInfoV2Resp.GetAge() >= uint32(riskConf.RealnameAge) {
		log.InfoWithCtx(ctx, "CheckSignAnchorRiskAccount hit. age=%d RealnameAge=%d, uid=%d signGuildId=%d",
			realNameAuthInfoV2Resp.GetAge(), riskConf.RealnameAge, uid, signGuildId)
		return true, status.ErrContractApplyAnchorLimitAge, nil
	}

	// 疑似小号
	extra, err := m.getExtraInfo(ctx, uid, uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE), signGuildId, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckSignAnchorRiskAccount getExtraInfo fail %v, uid=%d signGuildId=%d", err, uid, signGuildId)
		return false, 0, err
	}
	if len(extra.LoginOtherUids) > 0 {
		log.InfoWithCtx(ctx, "CheckSignAnchorRiskAccount hit. loginOtherUids=%v uid=%d signGuildId=%d",
			extra.LoginOtherUids, uid, signGuildId)
		return true, status.ErrContractApplyAnchorLimitXiaohao, nil
	}

	// 充值
	recharge, err := m.GetTotalRecharge(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckSignAnchorRiskAccount GetTotalRecharge fail %v, uid=%d signGuildId=%d",
			err, uid, signGuildId)
		return false, 0, err
	}
	log.DebugfWithCtx(ctx, "CheckSignAnchorRiskAccount GetTotalRecharge uid=%d recharge=%d signGuildId=%d",
		uid, recharge, signGuildId)
	if recharge >= uint32(riskConf.LatelyDay30Recharge) {
		log.InfoWithCtx(ctx, "CheckSignAnchorRiskAccount hit. LatelyDay30Recharge=%d limit=%d, uid=%d signGuildId=%d",
			recharge, riskConf.LatelyDay30Recharge, uid, signGuildId)
		return true, status.ErrContractApplyAnchorLimitRecharge, nil
	}

	/*
		// 直播间封禁
		liveInfoResp, serr := m.liveMgrCli.GetChannelLiveInfo(ctx, uid, false)
		if serr != nil {
			log.ErrorWithCtx(ctx, "CheckSignAnchorRiskAccount GetChannelLiveInfo fail %v, uid=%d signGuildId=%d",
				serr, uid, signGuildId)
			if serr.Code() == status.ErrChannelLiveNotAuthority { // -5780
				return false, nil
			} else {
				return false, serr
			}
		}
		cid := liveInfoResp.GetChannelLiveInfo().GetChannelId()
		channelBanList, err := m.GreenBaBaClient.BatchGetCurrBannedStatById(ctx, 0, uint32(greenBaba.ENUM_TARGET_TYPE_E_TARGET_TYPE_CHANNEL), []uint32{cid})
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckSignAnchorRiskAccount BatchGetCurrBannedStatById fail %v, uid=%d signGuildId=%d",
				err, uid, signGuildId)
			return false, err
		}
		log.DebugfWithCtx(ctx, "CheckSignAnchorRiskAccount BatchGetCurrBannedStatById uid=%d signGuildId=%d cid=%d channelBanList=%+v",
			uid, signGuildId, cid, channelBanList)
		for _, channelBan := range channelBanList {
			for _, banInfo := range channelBan.GetBannedList() {
				if uint32(greenBaba.ENUM_TARGET_TYPE_E_TARGET_TYPE_CHANNEL) == banInfo.TargetType &&
					uint32(greenBaba.ENUM_BANNED_TYPE_E_BANNED_CHANNEL) == banInfo.BannedType &&
					channelBan.Id == banInfo.Id && banInfo.RemainSecond > 0 && channelBan.Id == cid {
					log.InfoWithCtx(ctx, "CheckSignAnchorRiskAccount hit. banInfo=%+v uid=%d cid=%d signGuildId=%d",
						banInfo, uid, cid, signGuildId)
					return true, nil
				}
			}
		}
	*/
	return false, 0, nil
}

func (m *AnchorContractMgr) GetGuildSignAnchorInfo(ctx context.Context, in *pb.GetGuildSignAnchorInfoReq) (*pb.GetGuildSignAnchorInfoResp, error) {
	out := &pb.GetGuildSignAnchorInfoResp{}
	status, limitInfo, monthUsedCnt, nowDayUsedCnt, err := m.GetGuildSignAnchorLimitStatus(ctx, in.GuildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildSignAnchorInfo fail %v GuildId=%d", err, in.GuildId)
		return out, nil
	}
	out.Info = &pb.GuildSignAnchorInfo{
		GuildId:             limitInfo.GuildId,
		Yearmonth:           limitInfo.Yearmonth,
		MonthBefore_25ThCnt: limitInfo.MonthBefore25thCnt,
		MonthCnt:            limitInfo.MonthCnt,
		MonthUseCnt:         monthUsedCnt,
		Operator:            limitInfo.Operator,
	}
	out.NowDayUseCnt = nowDayUsedCnt
	out.AudioLimitStatus = status
	return out, nil
}
