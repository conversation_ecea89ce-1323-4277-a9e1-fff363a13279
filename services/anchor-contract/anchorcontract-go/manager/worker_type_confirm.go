package manager

import (
	"context"
	"github.com/jinzhu/gorm"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql"
)

func (m *AnchorContractMgr) GetWorkerTypeConfirm(ctx context.Context, uid, guildId uint32) (needConfirm bool, err error) {
	// 检查一下是不是签约成员
	_, exist, err := m.store.GetValidContractWithUid(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckCanApplyCancelContractV2 GetValidContractWithUid fail uid:%d, guild:%d, err: %v", uid, guildId, err)
		return false, err
	}

	if !exist {
		return false, nil
	}

	confirmInfo, err := m.store.GetWorkerTypeConfirm(nil, uid, guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetWorkerTypeConfirm GetWorkerTypeConfirm fail uid:%d, guild:%d, err: %v", uid, guildId, err)
		return false, err
	}

	// 不需要签约或者已经认证过了
	if confirmInfo == nil || confirmInfo.IsConfirm {
		return false, nil
	}

	// 需要签约
	return true, nil

}

func (m *AnchorContractMgr) AddWorkerTypeConfirm(ctx context.Context, uid, guildId uint32) error {
	confirmInfo := &mysql.WorkerTypeConfirm{
		Uid:       uid,
		GuildId:   guildId,
		IsConfirm: false,
	}

	err := m.store.AddWorkerTypeConfirm(nil, confirmInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddWorkerTypeConfirm AddWorkerTypeConfirm fail uid:%d, guild:%d, err: %v", uid, guildId, err)
		return err
	}

	return nil
}

func (m *AnchorContractMgr) UpdateWorkerTypeConfirm(ctx context.Context, uid, workerType uint32) error {
	// 检查一下是不是签约成员
	info, exist, err := m.store.GetValidContractWithUid(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckCanApplyCancelContractV2 GetValidContractWithUid fail uid:%d, guild:%d, err: %v", uid, info.GuildId, err)
		return err
	}

	if !exist {
		log.InfoWithCtx(ctx, "UpdateWorkerTypeConfirm not exist contract uid:%d, guild:%d", uid)
		return nil
	}

	// 获取一下是否需要认证
	confirmInfo, err := m.store.GetWorkerTypeConfirm(nil, uid, info.GuildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetWorkerTypeConfirm GetWorkerTypeConfirm fail uid:%d, guild:%d, err: %v", uid, info.GuildId, err)
		return err
	}

	if confirmInfo == nil {
		log.InfoWithCtx(ctx, "UpdateWorkerTypeConfirm not exist confirm info uid:%d, guild:%d", uid)
		return nil
	}

	newConfirmInfo := &mysql.WorkerTypeConfirm{
		Uid:        uid,
		GuildId:    info.GuildId,
		IsConfirm:  true,
		WorkerType: workerType,
	}

	err = m.store.Transaction(ctx, func(tx *gorm.DB) error {
		err = m.store.UpdateWorkerTypeConfirm(tx, newConfirmInfo)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateWorkerTypeConfirm UpdateWorkerTypeConfirm fail uid:%d, guild:%d, err: %v", uid, info.GuildId, err)
			return err
		}

		err = m.store.UpdateContractWorkerType(tx, newConfirmInfo.Uid, newConfirmInfo.GuildId, newConfirmInfo.WorkerType)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateWorkerTypeConfirm UpdateWorkerTypeConfirm fail uid:%d, guild:%d, err: %v", uid, info.GuildId, err)
			return err
		}

		return nil
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateWorkerTypeConfirm Transaction fail uid:%d, guild:%d, err: %v", uid, info.GuildId, err)
		return err
	}

	// 删缓存
	err = m.cache.DelUserContractInfoV2(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateWorkerTypeConfirm DelUserContractInfoV2 fail uid:%d, guild:%d, err: %v", uid, info.GuildId, err)
		// 这个不用返回
	}

	return nil
}
