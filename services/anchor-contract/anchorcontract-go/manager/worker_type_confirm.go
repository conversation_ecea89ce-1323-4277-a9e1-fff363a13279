package manager

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/jinzhu/gorm"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/marketid_helper"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/anchorcontract-go"
	im_api "golang.52tt.com/protocol/services/im-api"
	publicPB "golang.52tt.com/protocol/services/publicsvr"
	"golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql"
	"time"
)

func (m *AnchorContractMgr) GetWorkerTypeConfirm(ctx context.Context, uid, guildId uint32) (needConfirm bool, err error) {
	// 检查一下是不是签约成员
	_, exist, err := m.store.GetValidContractWithUid(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckCanApplyCancelContractV2 GetValidContractWithUid fail uid:%d, guild:%d, err: %v", uid, guildId, err)
		return false, err
	}

	if !exist {
		return false, nil
	}

	confirmInfo, err := m.store.GetWorkerTypeConfirm(nil, uid, guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetWorkerTypeConfirm GetWorkerTypeConfirm fail uid:%d, guild:%d, err: %v", uid, guildId, err)
		return false, err
	}

	// 不需要签约或者已经认证过了
	if confirmInfo == nil || confirmInfo.IsConfirm {
		return false, nil
	}

	// 需要签约
	return true, nil

}

func (m *AnchorContractMgr) AddWorkerTypeConfirm(ctx context.Context, uid, guildId uint32) error {
	confirmInfo := &mysql.WorkerTypeConfirm{
		Uid:       uid,
		GuildId:   guildId,
		IsConfirm: false,
	}

	err := m.store.AddWorkerTypeConfirm(nil, confirmInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddWorkerTypeConfirm AddWorkerTypeConfirm fail uid:%d, guild:%d, err: %v", uid, guildId, err)
		return err
	}

	return nil
}

func (m *AnchorContractMgr) UpdateWorkerTypeConfirm(ctx context.Context, uid, workerType uint32) error {
	// 检查一下是不是签约成员
	info, exist, err := m.store.GetValidContractWithUid(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckCanApplyCancelContractV2 GetValidContractWithUid fail uid:%d, guild:%d, err: %v", uid, info.GuildId, err)
		return err
	}

	if !exist {
		log.InfoWithCtx(ctx, "UpdateWorkerTypeConfirm not exist contract uid:%d, guild:%d", uid)
		return nil
	}

	// 获取一下是否需要认证
	confirmInfo, err := m.store.GetWorkerTypeConfirm(nil, uid, info.GuildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetWorkerTypeConfirm GetWorkerTypeConfirm fail uid:%d, guild:%d, err: %v", uid, info.GuildId, err)
		return err
	}

	if confirmInfo == nil {
		log.InfoWithCtx(ctx, "UpdateWorkerTypeConfirm not exist confirm info uid:%d, guild:%d", uid)
		return nil
	}

	newConfirmInfo := &mysql.WorkerTypeConfirm{
		Uid:        uid,
		GuildId:    info.GuildId,
		IsConfirm:  true,
		WorkerType: workerType,
	}

	err = m.store.Transaction(ctx, func(tx *gorm.DB) error {
		err = m.store.UpdateWorkerTypeConfirm(tx, newConfirmInfo)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateWorkerTypeConfirm UpdateWorkerTypeConfirm fail uid:%d, guild:%d, err: %v", uid, info.GuildId, err)
			return err
		}

		err = m.store.UpdateContractWorkerType(tx, newConfirmInfo.Uid, newConfirmInfo.GuildId, newConfirmInfo.WorkerType)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateWorkerTypeConfirm UpdateWorkerTypeConfirm fail uid:%d, guild:%d, err: %v", uid, info.GuildId, err)
			return err
		}

		return nil
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateWorkerTypeConfirm Transaction fail uid:%d, guild:%d, err: %v", uid, info.GuildId, err)
		return err
	}

	// 删缓存
	err = m.cache.DelUserContractInfoV2(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateWorkerTypeConfirm DelUserContractInfoV2 fail uid:%d, guild:%d, err: %v", uid, info.GuildId, err)
		// 这个不用返回
	}

	return nil
}

// InviteMemberChangeWorkerType 邀请成员修改从业者类型
func (m *AnchorContractMgr) InviteMemberChangeWorkerType(ctx context.Context, uid, guildId, workerType uint32) (uint32, error) {
	// 检查一下是不是签约成员 , 是不是会长在http服务那边已经检查过了
	info, exist, err := m.store.GetValidContractWithUid(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "InviteMemberChangeWorkerType GetValidContractWithUid fail uid:%d, guild:%d, err: %v", uid, guildId, err)
		return 0, err
	}

	if !exist {
		log.InfoWithCtx(ctx, "InviteMemberChangeWorkerType not exist contract uid:%d, guild:%d", uid, guildId)
		return 0, nil
	}

	// 检查是否有处理中的申请
	applyInfo, err := m.store.GetWorkerTypeChangeApply(nil, uid, info.GuildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "InviteMemberChangeWorkerType GetWorkerTypeChangeApply fail uid:%d, guild:%d, err: %v", uid, info.GuildId, err)
		return 0, err
	}
	if applyInfo != nil {
		if applyInfo.Status == 0 {
			log.InfoWithCtx(ctx, "InviteMemberChangeWorkerType already have apply uid:%d, guild:%d, status:%d", uid, info.GuildId, applyInfo.Status)
			return 0, protocol.NewExactServerError(nil, status.ErrRevenueSvrErr, "无法重复进行邀请")
		}
	}

	// 检查冷却和次数限制
	changeInfo, err := m.store.GetWorkerTypeChangeInfo(nil, uid, info.GuildId, info.SignTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "InviteMemberChangeWorkerType GetWorkerTypeChangeInfo fail uid:%d, guild:%d, err: %v", uid, info.GuildId, err)
		return 0, err
	}

	if changeInfo != nil {
		if changeInfo.LastChangeTime.Add(time.Duration(m.newDyConf.GetContractDyConf().ChangeAnchorTypeCoolDownSec) * time.Second).After(time.Now()) {
			log.InfoWithCtx(ctx, "InviteMemberChangeWorkerType cooldown not over uid:%d, guild:%d, last change time: %s", uid, info.GuildId, changeInfo.LastChangeTime)
			return 0, protocol.NewExactServerError(nil, status.ErrRevenueSvrErr, "邀请冷却中，请稍后再试")
		}

		if changeInfo.InviteCount >= m.newDyConf.GetContractDyConf().ChangeAnchorTypeMaxCount {
			log.InfoWithCtx(ctx, "InviteMemberChangeWorkerType invite count limit uid:%d, guild:%d, invite count: %d", uid, info.GuildId, changeInfo.InviteCount)
			return 0, protocol.NewExactServerError(nil, status.ErrRevenueSvrErr, "邀请次数已达上限")
		}
	}

	newConfirmInfo := &mysql.WorkerTypeChangeApply{
		Uid:            uid,
		GuildId:        info.GuildId,
		OldWorkerType:  info.WorkerType,
		NewWorkerType:  workerType,
		WorkerChangeId: 0,
		CreateTime:     time.Now(),
		UpdateTime:     time.Now(),
		Status:         0,
	}

	id := uint32(0)
	err = m.store.Transaction(ctx, func(tx *gorm.DB) error {
		// 写变更信息
		id, err = m.store.AddWorkerTypeChangeSnapshot(tx, &mysql.WorkerTypeChangeSnapshot{
			Uid:     uid,
			GuildId: info.GuildId,
			Content: "",
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "InviteMemberChangeWorkerType AddWorkerTypeChangeSnapshot fail uid:%d, guild:%d, err: %v", uid, info.GuildId, err)
			return err
		}
		newConfirmInfo.WorkerChangeId = id
		err = m.store.AddWorkerTypeChangeApply(tx, newConfirmInfo)
		if err != nil {
			log.ErrorWithCtx(ctx, "InviteMemberChangeWorkerType UpdateWorkerTypeConfirm fail uid:%d, guild:%d, err: %v", uid, info.GuildId, err)
			return err
		}

		// 增加变更次数
		err = m.store.IncreaseInviteCount(tx, uid, guildId, info.SignTime)
		if err != nil {
			log.ErrorWithCtx(ctx, "InviteMemberChangeWorkerType IncreaseInviteCount fail uid:%d, guild:%d, err: %v", uid, guildId, err)
			return err
		}

		return nil
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "InviteMemberChangeWorkerType Transaction fail uid:%d, guild:%d, err: %v", uid, guildId, err)
		return 0, err
	}

	// 发im通知用户
	userOl, err := m.userOlCli.GetLastMobileOnlineInfo(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "InviteMemberChangeWorkerType GetLastMobileOnlineInfo fail uid:%d, err: %v", uid, err)
	}

	jumpUrl := marketid_helper.Get("anchor_right_change_url", userOl.GetMarketId(), userOl.GetClientType()) + fmt.Sprintf("&unique_id=%d", id)
	guildInfo, err := m.guildCli.GetGuild(ctx, guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "InviteMemberChangeWorkerType GetGuild fail guild:%d, err: %v", guildId, err)
	}
	guildViewId := guildInfo.GetGuildId()
	if guildInfo.GetShortId() != 0 {
		guildViewId = guildInfo.GetShortId()
	}
	expireTime := time.Now().Add(time.Duration(m.newDyConf.GetContractDyConf().ChangeAnchorExpireSec) * time.Second).Format("2006-01-02 15:04:05")
	content := fmt.Sprintf("【签约身份变更邀请】\n%s（%d）邀请您更改签约身份，由普通签约成员更改为合约成员，点击查看权益、解约方式变更内容>\n（请在%s前提交反馈，逾期邀请失效不可操作）", guildInfo.GetName(), guildViewId, expireTime)

	_ = m.SendFuWuHaoMsg(ctx, uid, content, "点击查看权益、解约方式变更内容>", jumpUrl)

	log.InfoWithCtx(ctx, "InviteMemberChangeWorkerType success uid:%d, guild:%d, workerType:%d", uid, guildId, workerType)
	return id, nil
}

// GetContractChangeInfo 获取从业者类型变更信息
func (m *AnchorContractMgr) GetContractChangeInfo(ctx context.Context, req *pb.GetContractChangeInfoReq) (*pb.GetContractChangeInfoResp, error) {
	out := &pb.GetContractChangeInfoResp{}
	changeInfo, err := m.store.GetWorkerTypeChangeSnapshot(nil, req.GetId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetContractChangeInfo GetWorkerTypeChangeApply fail id:%d, err: %v", req.GetId(), err)
		return out, err
	}

	if changeInfo == nil {
		log.ErrorWithCtx(ctx, "GetContractChangeInfo not exist change info id:%d", req.GetId())
		return out, protocol.NewExactServerError(nil, status.ErrRevenueSvrErr, "不存在该变更")
	}

	// 检查一下是不是签约成员
	_, exist, err := m.store.GetValidContractWithUid(changeInfo.Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetContractChangeInfo GetValidContractWithUid fail id:%d, err: %v", req.GetId(), err)
		return nil, err
	}

	if !exist {
		return out, nil
	}

	// 如果已经写入了变更数据
	if changeInfo.Content != "" {
		err := json.Unmarshal([]byte(changeInfo.Content), &out)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetContractChangeInfo Unmarshal fail id:%d, err: %v", req.GetId(), err)
			return out, err
		}

		out.IsAccept = true

		return out, nil
	}

	// 获取申请信息
	changeApply, err := m.store.GetWorkerTypeChangeApply(nil, changeInfo.Uid, changeInfo.GuildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetContractChangeInfo GetWorkerTypeChangeApply fail id:%d, guild:%d, err: %v", req.GetId(), changeInfo.GuildId, err)
		return out, err
	}

	if changeApply == nil {
		log.ErrorWithCtx(ctx, "GetContractChangeInfo not exist change apply id:%d, guild:%d", req.GetId(), changeInfo.GuildId)
		return out, protocol.NewExactServerError(nil, status.ErrRevenueSvrErr, "不存在该变更申请")
	}

	// 如果超过时间限制或者状态不为0
	if changeApply.Status != 0 || changeApply.UpdateTime.Add(time.Duration(m.newDyConf.GetContractDyConf().ChangeAnchorExpireSec)*time.Second).Before(time.Now()) {
		log.ErrorWithCtx(ctx, "GetContractChangeInfo change apply not valid id:%d, guild:%d, status:%d, create time: %s", req.GetId(), changeInfo.GuildId, changeApply.Status, changeApply.CreateTime)
		return out, protocol.NewExactServerError(nil, status.ErrRevenueSvrErr, "邀请已过期。")
	}

	// 没有变更数据，获取变更信息
	err = m.getChangeInfo(ctx, changeInfo, out)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetContractChangeInfo getChangeInfo fail id:%d, err: %v", req.GetId(), err)
		return out, err
	}

	return out, nil
}

func (m *AnchorContractMgr) getChangeInfo(ctx context.Context, changeInfo *mysql.WorkerTypeChangeSnapshot, out *pb.GetContractChangeInfoResp) error {
	// 没有的话，拿一下所属公会的权益
	resp, err := m.GetGuildSignRight(ctx, &pb.GetGuildSignRightReq{
		GuildId: changeInfo.GuildId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetContractChangeInfo GetGuildSignRight fail uid:%d, err: %v", changeInfo.Uid, err)
		return err
	}

	// 再拿变更申请的详情
	changeApply, err := m.store.GetWorkerTypeChangeApply(nil, changeInfo.Uid, changeInfo.GuildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetContractChangeInfo GetWorkerTypeChangeApply fail uid:%d, guild:%d, err: %v", changeInfo.Uid, changeInfo.GuildId, err)
		return err
	}

	// 检查变更
	rightNameMap := make(map[string]bool)
	rightNameList := make([]string, 0)
	oldRightList := make(map[string]map[string]*pb.SignSubRight)
	newRightList := make(map[string]map[string]*pb.SignSubRight)
	for _, prac := range resp.GetRightList() {
		for _, right := range prac.GetRightList() {
			if !rightNameMap[right.Name] {
				rightNameList = append(rightNameList, right.GetName())
				rightNameMap[right.GetName()] = true
			}

			if oldRightList[right.GetName()] == nil {
				oldRightList[right.GetName()] = make(map[string]*pb.SignSubRight)
			}
			if newRightList[right.GetName()] == nil {
				newRightList[right.GetName()] = make(map[string]*pb.SignSubRight)
			}
			for _, subRight := range right.GetSubRightList() {
				if prac.PracType == changeApply.OldWorkerType && subRight.GetIsSelect() {
					oldRightList[right.GetName()][fmt.Sprintf("%d", subRight.GetId())] = subRight
				}
				if prac.PracType == changeApply.NewWorkerType && subRight.GetIsSelect() {
					newRightList[right.GetName()][fmt.Sprintf("%d", subRight.GetId())] = subRight
				}
			}
		}
	}

	// 组装返回数据
	out.GroupList = make([]*pb.ContractPrivilegeGroupChange, 0)
	for _, rightName := range rightNameList {
		tmpRight := &pb.ContractPrivilegeGroupChange{
			Name:                 rightName,
			PrivilegeList:        make([]*pb.SignSubRight, 0),
			RemovedPrivilegeList: make([]*pb.SignSubRight, 0),
		}
		oldRight := oldRightList[rightName]
		newRight := newRightList[rightName]

		for _, subRight := range oldRight {
			if _, ok := newRightList[rightName][fmt.Sprintf("%d", subRight.GetId())]; ok {
				// 旧的有，新的也有，是不变的
				tmpRight.PrivilegeList = append(tmpRight.PrivilegeList, subRight)
			} else {
				// 旧的有，新的没有，是移除的
				tmpRight.RemovedPrivilegeList = append(tmpRight.RemovedPrivilegeList, subRight)
			}
		}

		for _, subRight := range newRight {
			// 新的有，旧的没有，是新增的
			if _, ok := oldRightList[rightName][fmt.Sprintf("%d", subRight.GetId())]; !ok {
				tmpSubRight := subRight
				tmpSubRight.IsNew = true
				tmpRight.PrivilegeList = append(tmpRight.PrivilegeList, tmpSubRight)
			}
		}

		out.GroupList = append(out.GroupList, tmpRight)
	}

	// 再查签约方式变更
	applyTypeResp, err := m.GetCancelContractTypeList(ctx, &pb.GetCancelContractTypeListReq{GuildId: changeInfo.GuildId})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetContractChangeInfo GetCancelContractTypeList fail guild:%d, err: %v", changeInfo.GuildId, err)
		return err
	}

	applyTypeMap := make(map[uint32]bool)
	oldApplyTypeMap := make(map[uint32]*pb.CancelContractTypeInfo)
	newApplyTypeMap := make(map[uint32]*pb.CancelContractTypeInfo)

	for _, applyType := range applyTypeResp.GetTypeList() {

		if applyType.GetPracType() == changeApply.OldWorkerType {
			for _, item := range applyType.GetInfoList() {
				applyTypeMap[item.GetCancelType()] = true
				if item.IsSelect {
					oldApplyTypeMap[item.GetCancelType()] = item
				}
			}
		}

		if applyType.GetPracType() == changeApply.NewWorkerType {
			for _, item := range applyType.GetInfoList() {
				applyTypeMap[item.GetCancelType()] = true
				if item.IsSelect {
					newApplyTypeMap[item.GetCancelType()] = item
				}
			}
		}
	}

	out.CancelTypes = make([]*pb.CanCancelContractChange, 0)
	for cancelType, _ := range applyTypeMap {
		tmpCancelType := &pb.CanCancelContractChange{
			CancelTypes:        make([]*pb.CancelContractTypeInfo, 0),
			RemovedCancelTypes: make([]*pb.CancelContractTypeInfo, 0),
		}

		if oldApplyTypeMap[cancelType] != nil && newApplyTypeMap[cancelType] != nil {
			// 旧的有，新的也有，是不变的
			tmpCancelType.CancelTypes = append(tmpCancelType.CancelTypes, oldApplyTypeMap[cancelType])
		} else if oldApplyTypeMap[cancelType] != nil {
			// 旧的有，新的没有，是移除的
			tmpCancelType.RemovedCancelTypes = append(tmpCancelType.RemovedCancelTypes, oldApplyTypeMap[cancelType])
		} else if newApplyTypeMap[cancelType] != nil {
			// 新的有，旧的没有，是新增的
			tmp := newApplyTypeMap[cancelType]
			tmp.IsNew = true
			tmpCancelType.CancelTypes = append(tmpCancelType.CancelTypes, tmp)
		}

		out.CancelTypes = append(out.CancelTypes, tmpCancelType)
	}

	return nil
}

// HandleContractChange 处理从业者类型变更
func (m *AnchorContractMgr) HandleContractChange(ctx context.Context, req *pb.HandleContractChangeReq) error {
	changeInfo, err := m.store.GetWorkerTypeChangeSnapshot(nil, req.GetId())
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleContractChange GetWorkerTypeChangeSnapshot fail id:%d, err: %v", req.GetId(), err)
		return err
	}

	if changeInfo == nil {
		log.InfoWithCtx(ctx, "HandleContractChange not exist change info id:%d", req.GetId())
		return nil
	}

	if changeInfo.Content != "" {
		log.InfoWithCtx(ctx, "HandleContractChange already handled id:%d", req.GetId())
		return nil
	}

	uid := changeInfo.Uid
	if req.GetUid() != changeInfo.Uid {
		log.ErrorWithCtx(ctx, "HandleContractChange uid not match id:%d, req uid:%d, change uid:%d", req.GetId(), req.GetUid(), changeInfo.Uid)
		return protocol.NewExactServerError(nil, status.ErrRevenueSvrErr, "操作失败，该记录不是您的变更记录")
	}

	guildId := changeInfo.GuildId

	// 检查一下是不是签约成员
	applyInfo, exist, err := m.store.GetValidContractWithUid(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleContractChange GetValidContractWithUid fail id:%d, err: %v", req.GetId(), err)
		return err
	}

	if !exist {
		log.InfoWithCtx(ctx, "HandleContractChange not exist contract id:%d", req.GetId())
		return nil
	}

	// 查apply信息
	info, err := m.store.GetWorkerTypeChangeApply(nil, uid, guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleContractChange GetWorkerTypeChangeApply fail id:%d, guild:%d, err: %v", req.GetId(), guildId, err)
		return err
	}

	if info.Status != 0 {
		log.InfoWithCtx(ctx, "HandleContractChange already handled apply id:%d, guild:%d, status:%d", req.GetId(), guildId, info.Status)
		return nil
	}

	if !req.GetIsAgree() {

		err = m.store.Transaction(ctx, func(tx *gorm.DB) error {
			err = m.store.UpdateWorkerTypeChangeApplyStatus(tx, info.Id, 2)
			if err != nil {
				log.ErrorWithCtx(ctx, "HandleContractChange UpdateWorkerTypeChangeApplyStatus fail id:%d, is agree:%d, err: %v", req.GetId(), req.GetIsAgree(), err)
				return err
			}

			// 再改时间
			err = m.store.UpdateWorkerTypeChangeTime(tx, uid, guildId, applyInfo.SignTime)
			if err != nil {
				log.ErrorWithCtx(ctx, "HandleContractChange UpdateWorkerTypeChangeTime fail id:%d, guild:%d, err: %v", req.GetId(), guildId, err)
				return err
			}

			return nil
		})

		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateWorkerTypeConfirm Transaction fail uid:%d, guild:%d, err: %v", uid, info.GuildId, err)
			return err
		}

		return nil
	}

	out := &pb.GetContractChangeInfoResp{}
	err = m.getChangeInfo(ctx, changeInfo, out)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleContractChange getChangeInfo fail id:%d, err: %v", req.GetId(), err)
		return err
	}

	content, _ := json.Marshal(out)

	err = m.store.Transaction(ctx, func(tx *gorm.DB) error {
		err = m.store.UpdateWorkerTypeChangeSnapshotContent(tx, changeInfo.Id, string(content))
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleContractChange UpdateWorkerTypeChangeSnapshot fail id:%d, err: %v", req.GetId(), err)
			return err
		}

		// 更新变更申请状态
		err = m.store.UpdateWorkerTypeChangeApplyStatus(tx, info.Id, 1)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleContractChange UpdateWorkerTypeChangeApplyStatus fail uid:%d, guild:%d, err: %v", changeInfo.Uid, changeInfo.GuildId, err)
			return err
		}

		err = m.store.UpdateContractWorkerType(tx, changeInfo.Uid, info.GuildId, info.NewWorkerType)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleContractChange UpdateWorkerTypeConfirm fail uid:%d, guild:%d, err: %v", uid, info.GuildId, err)
			return err
		}

		return nil
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateWorkerTypeConfirm Transaction fail uid:%d, guild:%d, err: %v", uid, info.GuildId, err)
		return err
	}

	// 删缓存
	err = m.cache.DelUserContractInfoV2(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleContractChange DelUserContractInfoV2 fail uid:%d, guild:%d, err: %v", uid, guildId, err)
		// 这个不用返回
	}

	// 发服务号消息
	guildInfo, err := m.guildCli.GetGuild(ctx, guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleContractChange GetGuild fail guild:%d, err: %v", guildId, err)
	}

	guildViewId := guildInfo.GetGuildId()
	if guildInfo.ShortId != 0 {
		guildViewId = guildInfo.ShortId
	}

	msgContent := fmt.Sprintf("您已成功转为【%s】id：%d 公会旗下的合约成员", guildInfo.GetName(), guildViewId)
	_ = m.SendFuWuHaoMsg(ctx, uid, msgContent, "", "")
	return nil
}

func (m *AnchorContractMgr) AutoExpireWorkerTypeChange() {
	// 过期时间：当前时间 - 变更申请冷却时间
	expiredTime := time.Now().Add(-time.Second * time.Duration(m.newDyConf.GetContractDyConf().ChangeAnchorExpireSec))

	// 找出所有过期的申请
	applyList, err := m.store.GetExpiredWorkerTypeChangeApply(nil, expiredTime)
	if err != nil {
		log.ErrorWithCtx(context.Background(), "AutoExpireWorkerTypeChange GetExpiredWorkerTypeChangeApply fail, err: %v", err)
		return
	}

	for _, apply := range applyList {
		log.InfoWithCtx(context.Background(), "AutoExpireWorkerTypeChange expire apply uid:%d, guild:%d, create time: %s", apply.Uid, apply.GuildId, apply.CreateTime)

		// 更新申请状态为过期
		err = m.store.Transaction(context.Background(), func(tx *gorm.DB) error {
			err := m.store.UpdateWorkerTypeChangeApplyStatus(tx, apply.Id, 2) // 2表示拒绝
			if err != nil {
				log.ErrorWithCtx(context.Background(), "AutoExpireWorkerTypeChange UpdateWorkerTypeChangeApplyStatus fail uid:%d, guild:%d, err: %v", apply.Uid, apply.GuildId, err)
				return err
			}

			// 再改时间
			err = m.store.UpdateWorkerTypeChangeTime(tx, apply.Uid, apply.GuildId, time.Now())
			if err != nil {
				log.ErrorWithCtx(context.Background(), "AutoExpireWorkerTypeChange UpdateWorkerTypeChangeTime fail uid:%d, guild:%d, err: %v", apply.Uid, apply.GuildId, err)
				return err
			}

			return nil
		})

	}
}

func (m *AnchorContractMgr) BatchCheckWorkerTypeChange(ctx context.Context, guildId uint32, uidList []uint32) (map[uint32]bool, error) {
	// 是不是已经有邀请
	result := make(map[uint32]bool)
	resp, err := m.store.GetWorkerTypeChangeApplyByUidList(nil, guildId, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchCheckWorkerTypeChange GetWorkerTypeChangeApplyByUidList fail guild:%d, err: %v", guildId, err)
		return nil, err
	}

	for _, apply := range resp {
		result[apply.Uid] = true
	}

	// 再看看变更次数和时间限制
	changeInfo, err := m.store.BatchGetWorkerTypeChangeInfo(nil, guildId, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchCheckWorkerTypeChange GetWorkerTypeChangeInfo fail guild:%d, err: %v", guildId, err)
		return nil, err
	}

	for _, info := range changeInfo {
		if info.LastChangeTime.Add(time.Duration(m.newDyConf.GetContractDyConf().ChangeAnchorTypeCoolDownSec) * time.Second).After(time.Now()) {
			log.InfoWithCtx(ctx, "BatchCheckWorkerTypeChange cooldown not over uid:%d, guild:%d, last change time: %s", info.Uid, guildId, info.LastChangeTime)
			result[info.Uid] = true
		}

		if info.InviteCount >= m.newDyConf.GetContractDyConf().ChangeAnchorTypeMaxCount {
			log.InfoWithCtx(ctx, "BatchCheckWorkerTypeChange invite count limit uid:%d, guild:%d, invite count: %d", info.Uid, guildId, info.InviteCount)
			result[info.Uid] = true
		}
	}

	return result, nil
}

func (m *AnchorContractMgr) BatchCheckWorkerTypeChangeTime(ctx context.Context, guildId uint32, uidList []uint32) (map[uint32]uint32, error) {
	// 是不是已经有邀请
	result := make(map[uint32]uint32)

	// 再看看变更次数和时间限制
	changeInfo, err := m.store.BatchGetWorkerTypeChangeInfo(nil, guildId, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchCheckWorkerTypeChange GetWorkerTypeChangeInfo fail guild:%d, err: %v", guildId, err)
		return nil, err
	}

	for _, info := range changeInfo {
		if uint32(info.LastChangeTime.Unix()) > result[info.Uid] {
			result[info.Uid] = uint32(info.LastChangeTime.Unix())
		}
	}

	return result, nil
}

func (m *AnchorContractMgr) SendFuWuHaoMsg(ctx context.Context, uid uint32, content, hlight, url string) error {
	log.InfoWithCtx(ctx, "SendFuWuHaoMsg uid=%d, content=%s, hlight=%s", uid, content, hlight)

	var fromUid uint64 = 0

	publicReq := &publicPB.GetPublicAccountsByBindedIdListReq{
		Type: uint32(publicPB.PublicAccountType_SYSTEM),
	}
	publicRsp, err := m.publicCli.GetPublicAccountsByBindedIdList(ctx, 0, publicReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendFuWuHaoMsg GetPublicAccountsByBindedIdList fail %v, uid %d", err, uid)
		return err
	}
	for _, accountElem := range publicRsp.GetPublicAccountList() {
		if accountElem.GetName() == "签约成员服务号" {
			fromUid = accountElem.GetBindedId()
			break
		}
	}
	if fromUid == 0 {
		return fmt.Errorf("no found public account")
	}

	_, sErr := m.imApiCli.SendPublicAccountText(ctx, &im_api.SendPublicAccountTextReq{
		PublicAccount: &im_api.PublicAccount{
			PublicType: im_api.PublicAccount_SYSTEM,
			BindedId:   fromUid,
		},
		ToUid: uid,
		Text: &im_api.Text{
			Content:   content,
			Highlight: hlight,
			Url:       url,
		},
		Opt:       nil,
		Namespace: "anchorcontract-go",
	})
	if sErr != nil {
		log.ErrorWithCtx(ctx, "SendFuWuHaoMsg SendPublicAccountText fail %v, uid %d", sErr, uid)
		return sErr
	}

	return nil
}
