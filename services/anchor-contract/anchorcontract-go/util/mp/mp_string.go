package mp

import (
	"fmt"
	"strings"
)

func (mp MediaProcess) String() string {
	var b strings.Builder
	fmt.Fprintf(&b, "{")
	if len(mp.Action) > 0 {
		fmt.Fprintf(&b, "action:%s,", mp.Action)
	}
	if mp.Transcode != nil {
		fmt.Fprintf(&b, "transcode:%s,", mp.Transcode)
	}

	fmt.Fprintf(&b, "transcodes:[")
	for _, tc := range mp.Transcodes {
		fmt.Fprintf(&b, "%s,", tc)
	}
	fmt.Fprintf(&b, "]")

	fmt.Fprintf(&b, "}")
	return b.String()
}
func (tc Transcode) String() string {
	var b strings.Builder
	fmt.Fprintf(&b, "{")
	if tc.Container != nil {
		fmt.Fprintf(&b, "container:%s,", tc.Container)
	}
	if tc.Video != nil {
		fmt.Fprintf(&b, "video:%s,", tc.Video)
	}
	if tc.Audio != nil {
		fmt.Fprintf(&b, "audio:%s,", tc.Audio)
	}

	fmt.Fprintf(&b, "watermarks:[")
	for _, wm := range tc.Watermarks {
		fmt.Fprintf(&b, "%s,", wm)
	}
	fmt.Fprintf(&b, "],")

	fmt.Fprintf(&b, "output:%s,", tc.Output)

	fmt.Fprintf(&b, "}")
	return b.String()
}
func (c Container) String() string {
	var b strings.Builder
	fmt.Fprintf(&b, "{")
	if len(c.Format) > 0 {
		fmt.Fprintf(&b, "format:%s,", c.Format)
	}
	fmt.Fprintf(&b, "}")
	return b.String()
}
func (v Video) String() string {
	var b strings.Builder
	fmt.Fprintf(&b, "{")
	if v.Remove {
		fmt.Fprintf(&b, "remove:true,")
	}
	if v.Codec != "" {
		fmt.Fprintf(&b, "codec:%s,", v.Codec)
	}
	if v.Fps > 0 {
		fmt.Fprintf(&b, "fps:%d,", v.Fps)
	}
	if v.Width > 0 {
		fmt.Fprintf(&b, "width:%d,", v.Width)
	}
	if v.Height > 0 {
		fmt.Fprintf(&b, "height :%d,", v.Height)
	}
	fmt.Fprintf(&b, "}")
	return b.String()
}
func (a Audio) String() string {
	var b strings.Builder
	fmt.Fprintf(&b, "{")
	if a.Remove {
		fmt.Fprintf(&b, "remove:true,")
	}
	if a.Codec != "" {
		fmt.Fprintf(&b, "codec:%s,", a.Codec)
	}
	fmt.Fprintf(&b, "}")
	return b.String()
}
func (w Watermark) String() string {
	var b strings.Builder
	fmt.Fprintf(&b, "{")
	fmt.Fprintf(&b, "location:%s,", w.Location)

	if w.WmText != nil {
		fmt.Fprintf(&b, "wmText:%s,", w.WmText)
	}
	if w.WmImage != nil {
		fmt.Fprintf(&b, "wmImage :%s,", w.WmImage)
	}
	fmt.Fprintf(&b, "}")
	return b.String()
}
func (l WatermarkLocation) String() string {
	var b strings.Builder
	fmt.Fprintf(&b, "{")
	if l.ReferPos != "" {
		fmt.Fprintf(&b, "referPos:%s,", l.ReferPos)
	}
	if l.Dm != "" {
		fmt.Fprintf(&b, "dm:%s,", l.Dm)
	}
	if l.Dx > 0 {
		fmt.Fprintf(&b, "dx:%d,", l.Dx)
	}
	if l.Dy > 0 {
		fmt.Fprintf(&b, "dy:%d,", l.Dy)
	}
	fmt.Fprintf(&b, "}")
	return b.String()
}

func (wt WatermarkText) String() string {
	var b strings.Builder
	fmt.Fprintf(&b, "{")
	if wt.Content != "" {
		fmt.Fprintf(&b, "content:%s,", wt.Content)
	}
	if wt.Font != "" {
		fmt.Fprintf(&b, "font:%s,", wt.Font)
	}
	if wt.FontSize > 0 {
		fmt.Fprintf(&b, "fontSize:%d,", wt.FontSize)
	}
	if wt.FontColor != "" {
		fmt.Fprintf(&b, "fontColor:%s,", wt.FontColor)
	}
	if wt.FontAlpha > 0 {
		fmt.Fprintf(&b, "fontAlpha :%d,", wt.FontAlpha)
	}
	fmt.Fprintf(&b, "}")
	return b.String()
}
func (wi WatermarkImage) String() string {
	var b strings.Builder
	fmt.Fprintf(&b, "{")
	if wi.Image != "" {
		fmt.Fprintf(&b, "image:%s,", wi.Image)
	}
	if wi.SizeMode != "" {
		fmt.Fprintf(&b, "font:%s,", wi.SizeMode)
	}
	if wi.Width > 0 {
		fmt.Fprintf(&b, "width:%d,", wi.Width)
	}
	if wi.Height > 0 {
		fmt.Fprintf(&b, "height :%d,", wi.Height)
	}
	fmt.Fprintf(&b, "}")
	return b.String()
}
func (o Output) String() string {
	var b strings.Builder
	fmt.Fprintf(&b, "{")
	if o.Key != "" {
		fmt.Fprintf(&b, "key :%s,", o.Key)
	}
	fmt.Fprintf(&b, "}")
	return b.String()
}
func (o Input) String() string {
	var b strings.Builder
	fmt.Fprintf(&b, "{")
	if o.App != "" {
		fmt.Fprintf(&b, "app:%s,", o.App)
	}
	if o.Scope != "" {
		fmt.Fprintf(&b, "scope:%s,", o.Scope)
	}
	if o.Key != "" {
		fmt.Fprintf(&b, "key:%s,", o.Key)
	}
	fmt.Fprintf(&b, "}")
	return b.String()
}

func (r MediaProcessResult) String() string {
	var b strings.Builder
	fmt.Fprintf(&b, "{")
	//fmt.Fprintf(&b, "state:%d,", r.State)
	fmt.Fprintf(&b, "result:%d,", r.Result)
	fmt.Fprintf(&b, "input:%s,", r.Input)

	fmt.Fprintf(&b, "items:[")
	for _, item := range r.Items {
		fmt.Fprintf(&b, "%s,", item)
	}
	fmt.Fprintf(&b, "]")

	//if r.Provider != "" {
	//    fmt.Fprintf(&b, "provider:%s,", r.Provider)
	//}
	//if r.TaskId != "" {
	//    fmt.Fprintf(&b, "taskId:%s,", r.TaskId)
	//}
	fmt.Fprintf(&b, "}")
	return b.String()
}
func (r MediaProcessResultItem) String() string {
	var b strings.Builder
	fmt.Fprintf(&b, "{")
	//fmt.Fprintf(&b, "msg:%s,", r.ErrMsg)
	fmt.Fprintf(&b, "Output:%s,", r.Output)
	fmt.Fprintf(&b, "}")
	return b.String()
}
