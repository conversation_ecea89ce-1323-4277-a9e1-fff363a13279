package mysql

import (
	"context"
	"time"

	"github.com/jinzhu/gorm"
	"golang.52tt.com/pkg/foundation/utils"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/anchorcontract-go"
)

/*
CREATE TABLE tbl_cancel_contract_apply_log (
		apply_id bigint unsigned NOT NULL AUTO_INCREMENT,
		uid int unsigned NOT NULL,
		guild_id int unsigned NOT NULL,
		sign_time datetime NOT NULL COMMENT '签约时间',
		contract_expire_time datetime NOT NULL COMMENT '到期时间',
		agent_uid int unsigned NOT NULL default 0 COMMENT '经纪人',

		tag_id tinyint unsigned NOT NULL COMMENT '标签id',
		live_identity_status int unsigned NOT NULL default 0 COMMENT '语音直播身份,为1即有身份',
		live_obtain_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '获得该身份时间',
		multiplay_identity_status int unsigned NOT NULL default 0 COMMENT '多人互动身份',
		multiplay_obtain_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '获得该身份时间',
		identity_info varchar(2048) NOT NULL DEFAULT '' COMMENT '身份信息',

		status tinyint unsigned NOT NULL COMMENT '解约状态 0-申请中 1-已解约',
		reason tinyint unsigned NOT NULL COMMENT '解约原因',
		apply_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '解约申请时间',
		op_uid int unsigned NOT NULL DEFAULT 0 COMMENT '操作人',

		update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
		identity_info varchar(2048) NOT NULL DEFAULT '' COMMENT '身份信息',
	    cancel_type tinyint NOT NULL DEFAULT 0 COMMENT '解约方式',
	    reject_txt   varchar(1024) NOT NULL DEFAULT '' COMMENT '会长拒绝原因',
	    proof_urls   varchar(1024) NOT NULL DEFAULT '' COMMENT '证据图片url',
	    official_note varchar(1024) NOT NULL DEFAULT '' COMMENT '官方处理备注',
		worker_type tinyint NOT NULL DEFAULT 0 COMMENT '从业者类型：see ContractWorkerType',
		pay_amount int NOT NULL DEFAULT 0 COMMENT '付费解约金额(分)',
		identity_num varchar(30) NOT NULL DEFAULT '' COMMENT '签约人的身份证',
		pay_desc varchar(1024) NOT NULL DEFAULT '' COMMENT '付费解约描述',
		official_remark varchar(1024) NOT NULL DEFAULT '' COMMENT '官方备注',
		PRIMARY KEY (apply_id),
		INDEX idx_uid (uid),
		INDEX idx_gid (guild_id),
		INDEX idx_identityNum(identity_num)
	)ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='解约申请记录表';
*/

type CancelContractApplyLog struct {
	ApplyId                 uint32    `db:"apply_id"`
	Uid                     uint32    `db:"uid"`
	GuildId                 uint32    `db:"guild_id"`
	SignTime                time.Time `db:"sign_time"`
	ContractExpireTime      time.Time `db:"contract_expire_time"`
	AgentUid                uint32    `db:"agent_uid"`
	TagId                   uint32    `db:"tag_id"`
	LiveIdentityStatus      uint32    `db:"live_identity_status"`
	LiveObtainTime          time.Time `db:"live_obtain_time"`
	MultiplayIdentityStatus uint32    `db:"multiplay_identity_status"`
	MultiplayObtainTime     time.Time `db:"multiplay_obtain_time"`
	IdentityInfo            string    `db:"identity_info"`
	Status                  uint32    `db:"status"`
	Reason                  uint32    `db:"reason"`
	ApplyTime               time.Time `db:"apply_time"`
	OpUid                   uint32    `db:"op_uid"`
	UpdateTime              time.Time `db:"update_time"`
	CancelType              uint32    `db:"cancel_type"`
	WorkerType              uint32    `db:"worker_type"`
	RejectTxt               string    `db:"reject_txt"`
	ProofUrls               string    `db:"proof_urls"`
	ProofVideoUrls          string    `db:"proof_video_urls"`
	OfficialNote            string    `db:"official_note"`
	ReasonText              string    `db:"reason_text" gorm:"type:varchar(2048)"`
	ImageProofList          string    `db:"image_proof_list" gorm:"type:varchar(1024)"`
	VideoProofList          string    `db:"video_proof_list" gorm:"type:varchar(1024)"`
	NegotiateReasonType     string    `db:"negotiate_reason_type" gorm:"type:varchar(1024)"`

	PayAmount          int64  `db:"pay_amount"`
	IdentityNum        string `db:"identity_num"`
	PayDesc            string `db:"pay_desc"`
	OfficialRemark     string `db:"official_remark"`
	OfficialRemarkOper string `db:"official_remark_oper"`
	OfficialRemarkTs   uint32 `db:"official_remark_ts"`
}

type IdentityInfo struct {
	AnchorIdentityInfoList []*AnchorIdentityInfo `json:"identity_list"`
	ReasonList             []uint32              `json:"reason_list"`
}

type AnchorIdentityInfo struct {
	ActorUid     uint32 `json:"actor_uid"`
	IdentityType uint32 `json:"identity_type"`
	GuildId      uint32 `json:"guild_id"`
	ObtainTime   uint32 `json:"obtain_time"`
	AgentUid     uint32 `json:"agent_uid"`
}

const (
	tblCancelContractApplyLogName   = "tbl_cancel_contract_apply_log"
	CreateCancelContractApplyLogTbl = `CREATE TABLE tbl_cancel_contract_apply_log (
		apply_id bigint unsigned NOT NULL AUTO_INCREMENT,
		uid int unsigned NOT NULL,
		guild_id int unsigned NOT NULL,
		sign_time datetime NOT NULL COMMENT '签约时间',
		contract_expire_time datetime NOT NULL COMMENT '到期时间',
		agent_uid int unsigned NOT NULL default 0 COMMENT '经纪人',
	
		tag_id tinyint unsigned NOT NULL COMMENT '标签id',
		live_identity_status int unsigned NOT NULL default 0 COMMENT '语音直播身份,为1即有身份',
		live_obtain_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '获得该身份时间',
		multiplay_identity_status int unsigned NOT NULL default 0 COMMENT '多人互动身份',
		multiplay_obtain_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '获得该身份时间',
		identity_info varchar(2048) NOT NULL DEFAULT '' COMMENT '身份信息',
	
		status tinyint unsigned NOT NULL COMMENT '解约状态 0-申请中 1-已解约',
		reason tinyint unsigned NOT NULL COMMENT '解约原因',
		apply_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '解约申请时间',
		op_uid int unsigned NOT NULL DEFAULT 0 COMMENT '操作人',
	
		update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
		cancel_type tinyint NOT NULL DEFAULT 0 COMMENT '解约方式',
		worker_type tinyint NOT NULL DEFAULT 0 COMMENT '从业者类型：see ContractWorkerType',
  		reject_txt   varchar(1024) NOT NULL DEFAULT '' COMMENT '会长拒绝原因',
  		proof_urls   varchar(1024) NOT NULL DEFAULT '' COMMENT '证据图片url',
  		proof_video_urls varchar(1024) NOT NULL DEFAULT '' COMMENT '证据视频url',
  		pay_desc varchar(1024) NOT NULL DEFAULT '' COMMENT '付费解约描述',
  		official_note varchar(1024) NOT NULL DEFAULT '' COMMENT '官方处理备注',
  		reason_text varchar(2048) NOT NULL DEFAULT '' COMMENT '解约原因文本',
  		video_proof_list varchar(1024) NOT NULL DEFAULT '' COMMENT '视频证据列表',
  		image_proof_list varchar(1024) NOT NULL DEFAULT '' COMMENT '图片证据列表',
  		negotiate_reason_type varchar(1024) NOT NULL DEFAULT '' COMMENT '协商解约原因类型',
  		
		pay_amount int NOT NULL DEFAULT 0 COMMENT '付费解约金额(分)',
		identity_num varchar(30) NOT NULL DEFAULT '' COMMENT '签约人的身份证',
		official_remark varchar(1024) NOT NULL DEFAULT '' COMMENT '官方备注',
		official_remark_oper  varchar(64) NOT NULL DEFAULT '' COMMENT '官方备注操作人', 
		official_remark_ts int NOT NULL DEFAULT 0 COMMENT '官方备注操作时间',
		PRIMARY KEY (apply_id),
		INDEX idx_uid (uid),
		INDEX idx_gid (guild_id),
        INDEX idx_identityNum(identity_num)
	)ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='解约申请记录表';`
)

func (t *CancelContractApplyLog) TableName() string {
	return tblCancelContractApplyLogName
}

func (t *CancelContractApplyLog) String() string {
	return utils.ToJson(t)
}

func (s *Store) AddCancelContractApplyLog(tx *gorm.DB, info *CancelContractApplyLog) error {
	db := s.db
	if tx != nil {
		db = tx
	}
	log.Infof("AddCancelContractApplyLog info=%+v", info)
	return db.Create(info).Error
}

func (s *Store) FinishCancelContractApplyLog(tx *gorm.DB, uid, guildId uint32) (uint32, error) {
	// 理论上 用户同时只能和一个公会解约
	db := s.db
	if tx != nil {
		db = tx
	}

	info := &CancelContractApplyLog{}
	err := s.db.Table(tblCancelContractApplyLogName).Select("uid,status").Where("uid=? and guild_id=? and status in(0,4,6) ", uid, guildId).Find(&info).Error
	if err != nil {
		log.Errorf("FinishCancelContractApplyLog fail to Select. uid:%+v, guildId:%+v, err:%v", uid, guildId, err)
		if gorm.IsRecordNotFoundError(err) {
			return 0, nil
		}
		return 0, err
	}
	st := uint32(pb.CancelContractStatus_CancelContractStatus_Finish)
	if info.Status == uint32(pb.CancelContractStatus_CancelContractStatus_NegotiateOfficeAccepted) {
		st = uint32(pb.CancelContractStatus_CancelContractStatus_NegotinateFinish)
	}
	m := map[string]interface{}{
		"status": st,
	}

	res := db.Table(tblCancelContractApplyLogName).Where("uid=? and guild_id=? and status in(0,4,6) ", uid, guildId).Updates(m)
	return uint32(res.RowsAffected), res.Error
}

func (s *Store) GetCancelContractApplyLog(uid uint32) (*CancelContractApplyLog, error) {
	info := &CancelContractApplyLog{}
	err := s.db.Table(tblCancelContractApplyLogName).Where("uid=?", uid).Find(&info).Error
	return info, err
}

func (s *Store) BatchGetCancelContractApplyLogList(uid []uint32, begin time.Time) ([]*CancelContractApplyLog, error) {
	info := make([]*CancelContractApplyLog, 0)
	err := s.db.Table(tblCancelContractApplyLogName).Where("uid in (?) AND update_time >= ?", uid, begin).Find(&info).Error
	return info, err
}

func (s *Store) GetCancelContractApplyLogById(applyId uint32) (*CancelContractApplyLog, error) {
	info := &CancelContractApplyLog{}
	err := s.db.Table(tblCancelContractApplyLogName).Where("apply_id=?", applyId).Find(&info).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return nil, nil
		}
		log.Errorf("GetCancelContractApplyLogById fail to Select. applyId:%+v, err:%v", applyId, err)
	}
	return info, err
}

func (s *Store) GetGuildCancelSignRecordList(queryType, guildId uint32, uids []uint32, begin, limit uint32, startTime, endTime, cancelType uint32) (uint32, []*CancelContractApplyLog, error) {
	list := make([]*CancelContractApplyLog, 0)

	db := s.db.Table(tblCancelContractApplyLogName).Where("guild_id=?", guildId)

	if len(uids) > 0 {
		db = db.Where("uid in(?)", uids)
	}
	if cancelType > 0 {
		db = db.Where("cancel_type=?", cancelType)
	}

	if startTime > 0 {
		db = db.Where("apply_time >= ?", time.Unix(int64(startTime), 0))
	}

	if endTime > 0 {
		db = db.Where("apply_time <= ?", time.Unix(int64(endTime), 0))
	}

	if queryType == uint32(pb.QUERY_CANCEL_SIGN_TYPE_QUERY_CANCEL_SIGN_TYPE_NOW) {
		db = db.Where("status in(0, 4, 6)").Order("apply_time DESC")
	} else if queryType == uint32(pb.QUERY_CANCEL_SIGN_TYPE_QUERY_CANCEL_SIGN_TYPE_HISTORY) {
		db = db.Where("status in(1, 2, 3, 5, 7, 8, 100)").Order("update_time DESC")
	}

	total, err := getRowsCnt(db)
	if err != nil {
		return 0, list, err
	}

	err = db.Offset(begin).Limit(limit).Find(&list).Error
	return total, list, err
}

func (s *Store) UpdateCancelContractApplyLogInfo(tx *gorm.DB, oldStatus uint32, info *CancelContractApplyLog) error {
	m := map[string]interface{}{
		"status": info.Status,
	}

	if len(info.RejectTxt) > 0 {
		m["reject_txt"] = info.RejectTxt
	}
	if len(info.ProofUrls) > 0 {
		m["proof_urls"] = info.ProofUrls
	}
	if len(info.ProofVideoUrls) > 0 {
		m["proof_video_urls"] = info.ProofVideoUrls
	}
	if len(info.OfficialNote) > 0 {
		m["official_note"] = info.OfficialNote
	}

	res := tx.Table(tblCancelContractApplyLogName).Where("uid=? and guild_id=? and cancel_type=? and status=?", info.Uid, info.GuildId, info.CancelType, oldStatus).Updates(m)
	if res.RowsAffected != 1 {
		log.Errorf("UpdateCancelContractApplyLogInfo fail to Update. info:%+v, err:%v", info, res.Error)
	}
	return res.Error
}

func (s *Store) UpdateCancelContractApplyLogInfoById(tx *gorm.DB, info *CancelContractApplyLog) error {
	db := s.db
	if tx != nil {
		db = tx
	}
	m := map[string]interface{}{
		"status": info.Status,
	}

	if len(info.RejectTxt) > 0 {
		m["reject_txt"] = info.RejectTxt
	}
	if len(info.ProofUrls) > 0 {
		m["proof_urls"] = info.ProofUrls
	}
	if len(info.ProofVideoUrls) > 0 {
		m["proof_video_urls"] = info.ProofVideoUrls
	}
	if len(info.OfficialNote) > 0 {
		m["official_note"] = info.OfficialNote
	}
	if len(info.OfficialRemark) > 0 {
		m["official_remark"] = info.OfficialRemark
		m["official_remark_oper"] = info.OfficialRemarkOper
		m["official_remark_ts"] = info.OfficialRemarkTs
	}

	if len(info.VideoProofList) > 0 {
		m["video_proof_list"] = info.VideoProofList
	}

	res := db.Table(tblCancelContractApplyLogName).Where("apply_id=?", info.ApplyId).Updates(m)
	if res.RowsAffected > 1 {
		log.Errorf("UpdateCancelContractApplyLogInfoById fail to Update. info:%+v, err:%v", info, res.Error)
	}
	return res.Error
}

func (s *Store) AbortCancelContractApplyLog(tx *gorm.DB, uid, guildId, cancelType, applyId uint32, notes string) error {
	db := s.db
	if tx != nil {
		db = tx
	}
	m := map[string]interface{}{
		"status":        uint32(pb.CancelContractStatus_CancelContractStatus_Abort),
		"official_note": notes,
	}
	if applyId == 0 {
		res := db.Table(tblCancelContractApplyLogName).Where("uid=? and guild_id=? and cancel_type=? and status in(0, 4, 6)", uid, guildId, cancelType).Updates(m)
		if res.RowsAffected > 1 {
			log.Errorf("AbortCancelContractApplyLog fail to Update. uid:%+v, guildId:%+v, cancelType:%+v, err:%v", uid, guildId, cancelType, res.Error)
		}
		return res.Error
	} else {
		res := db.Table(tblCancelContractApplyLogName).Where("apply_id=?", applyId).Updates(m)
		if res.RowsAffected > 1 {
			log.Errorf("AbortCancelContractApplyLog fail to Update. applyId:%+v, err:%v", applyId, res.Error)
		}
		return res.Error
	}

}

// GetGuildLeaderCanHandleCancelContractApplyLog 获取公会会长可以处理的解约申请
func (s *Store) GetGuildLeaderCanHandleCancelContractApplyLog(ctx context.Context, uid, guildId uint32) (*CancelContractApplyLog, error) {
	info := &CancelContractApplyLog{}
	err := s.db.Table(tblCancelContractApplyLogName).Where("uid=? and guild_id=? and status in(0, 4, 6)", uid, guildId).Find(&info).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return nil, nil
		}
		log.ErrorWithCtx(ctx, "GetGuildLeaderCanHandleCancelContractApplyLog fail to Select. uid:%+v, guildId:%+v, err:%v", uid, guildId, err)
	}
	return info, err

}

// GetOfficialCancelContractApplyLogList 获取官方运营后台处理的解约申请列表
func (s *Store) GetOfficialCancelContractApplyLogList(ctx context.Context, uid, guildId, cancelType uint32, status []uint32, begin, limit uint32, beginTs, endTs uint32) ([]*CancelContractApplyLog, uint32, error) {
	list := make([]*CancelContractApplyLog, 0, limit)

	beginTime := time.Unix(int64(beginTs), 0)
	endTime := time.Unix(int64(endTs), 0)

	query := "cancel_type=? "
	args := []interface{}{cancelType}
	if len(status) > 0 {
		query += " and status in(?) "
		args = append(args, status)
	}
	if uid > 0 {
		query += " and uid=? "
		args = append(args, uid)
	} else if guildId > 0 {
		query += " and guild_id=? "
		args = append(args, guildId)
	} else {
	}

	if beginTs > 0 {
		query += " and apply_time >= ? "
		args = append(args, beginTime)
	}

	if endTs > 0 {
		query += " and apply_time <= ? "
		args = append(args, endTime)
	}
	totalNum := uint32(0)
	err := s.db.Table(tblCancelContractApplyLogName).Where(query, args...).Order("update_time DESC").Offset(begin).Limit(limit).Scan(&list).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOfficialCancelContractApplyLogList fail to Select. uid:%+v, err:%v", uid, err)
		return list, totalNum, err
	}

	err = s.db.Table(tblCancelContractApplyLogName).Where(query, args...).Count(&totalNum).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOfficialCancelContractApplyLogList fail to Count. uid:%+v, err:%v", uid, err)
		return list, totalNum, err
	}

	log.InfoWithCtx(ctx, "GetOfficialCancelContractApplyLogList args:%v, totalNum:%d , list:%v", args, totalNum, list)
	return list, totalNum, nil
}
func (s *Store) UpdateCancelContractRemarkById(ctx context.Context, applyId uint32, remark, oper string) error {
	m := map[string]interface{}{
		"official_remark":      remark,
		"official_remark_oper": oper,
		"official_remark_ts":   uint32(time.Now().Unix()),
	}

	res := s.db.Table(tblCancelContractApplyLogName).Where("apply_id=?", applyId).Updates(m)
	if res.RowsAffected > 1 {
		log.ErrorWithCtx(ctx, "UpdateCancelContractRemarkById fail to Update. applyId:%+v,remark:%v, err:%v", applyId, remark, res.Error)
	}
	return res.Error
}

func (s *Store) GetCancelContractApplyLogIdentityNum(identityNum string) ([]*CancelContractApplyLog, error) {
	infos := make([]*CancelContractApplyLog, 0)
	err := s.db.Table(tblCancelContractApplyLogName).Where("identity_num=?", identityNum).Find(&infos).Error
	return infos, err
}

func (s *Store) GetLastPayCancelContractApplyLog(uid uint32) (*CancelContractApplyLog, error) {
	info := &CancelContractApplyLog{}
	err := s.db.Table(tblCancelContractApplyLogName).Where("uid=? and cancel_type= 4 and status=1", uid).Order("apply_id DESC").First(&info).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return nil, nil
		}
		log.Errorf("GetLastPayCancelContractApplyLog fail to Select. uid:%+v, err:%v", uid, err)
		return nil, err
	}
	return info, err
}

func (s *Store) GetLastPayCancelContractApplyRejectLog(uid uint32) (*CancelContractApplyLog, error) {
	info := &CancelContractApplyLog{}
	err := s.db.Table(tblCancelContractApplyLogName).Where("uid=? and cancel_type= 4 and status=7", uid).Order("apply_id DESC").First(&info).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return nil, nil
		}
		log.Errorf("GetLastPayCancelContractApplyLog fail to Select. uid:%+v, err:%v", uid, err)
		return nil, err
	}

	return info, err
}

// IsPayCancelContractByIdentityNum 判断身份证是否有在公会付费解约过
func (s *Store) IsPayCancelContractByIdentityNum(guildId uint32, identityNum string) (bool, error) {
	var count int
	err := s.db.Table(tblCancelContractApplyLogName).Where("guild_id=? and identity_num=? and cancel_type=4 and status=1", guildId, identityNum).Count(&count).Error
	if err != nil {
		log.Errorf("IsPayCancelContractByIdentityNum fail to Count. guildId:%+v, identityNum:%+v, err:%v", guildId, identityNum, err)
		return false, err
	}
	return count > 0, nil
}
