package mysql

import (
	"context"
	"errors"
	"github.com/jinzhu/gorm"
	"golang.52tt.com/pkg/log"
	"time"
)

const TblNegotiateCoolDown = `CREATE TABLE IF NOT EXISTS negotiate_cool_down (
  id int(10) unsigned NOT NULL AUTO_INCREMENT,
  uid int(10) unsigned NOT NULL,
  begin_time timestamp NOT NULL,
  end_time timestamp NOT NULL,
  guild_id int(10) unsigned NOT NULL,
  PRIMARY KEY (id)) ENGINE=InnoDB DEFAULT CHARSET=utf8;`

// NegotiateCoolDown 协商解约结束后的冷却记录
type NegotiateCoolDown struct {
	ID        uint32    `gorm:"primary_key;auto_increment;column:id;type:int(10) unsigned;not null" json:"id"`
	Uid       uint32    `gorm:"column:uid;type:int(10) unsigned;not null" json:"uid"`
	GuildId   uint32    `gorm:"column:guild_id;type:int(10) unsigned;not null" json:"guild_id"`
	BeginTime time.Time `gorm:"column:begin_time;type:timestamp;not null" json:"begin_time"`
	EndTime   time.Time `gorm:"column:end_time;type:timestamp;not null" json:"end_time"`
}

func (t *NegotiateCoolDown) TableName() string {
	return "negotiate_cool_down"
}

func (s *Store) AddNegotiateCoolDown(ctx context.Context, info *NegotiateCoolDown) error {
	err := s.db.Create(info).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "AddNegotiateCoolDown fail to Create. info:%+v, err:%v", info, err)
		return err
	}

	log.ErrorWithCtx(ctx, "AddNegotiateCoolDown info:%+v", info)
	return nil
}

// GetNegotiateCoolDown 取出冷却期最晚的一条记录
func (s *Store) GetNegotiateCoolDown(ctx context.Context, uid, guildId uint32) (*NegotiateCoolDown, error) {
	data := &NegotiateCoolDown{}
	err := s.readonlyDb.Where("uid = ? and guild_id = ?", uid, guildId).Order("begin_time desc").First(data).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 不存在
			return data, nil
		}
		log.ErrorWithCtx(ctx, "GetNegotiateCoolDown uid:%d err:%v", uid, err)
		return nil, err
	}

	log.DebugWithCtx(ctx, "GetNegotiateCoolDown uid:%d data:%v", uid, data)
	return data, nil
}
