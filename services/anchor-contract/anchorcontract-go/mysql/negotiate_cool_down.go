package mysql

import (
	"context"
	"errors"
	"github.com/jinzhu/gorm"
	"golang.52tt.com/pkg/log"
	"time"
)

const TblNegotiateCoolDown = `CREATE TABLE IF NOT EXISTS negotiate_cool_down (
  id int(10) unsigned NOT NULL AUTO_INCREMENT,
  uid int(10) unsigned NOT NULL,
  begin_time timestamp NOT NULL,
  end_time timestamp NOT NULL,
  guild_id int(10) unsigned NOT NULL,
  PRIMARY KEY (id)) ENGINE=InnoDB DEFAULT CHARSET=utf8;`

// NegotiateCoolDown 协商解约结束后的冷却记录
type NegotiateCoolDown struct {
	ID        uint32    `gorm:"primary_key;auto_increment;column:id;type:int(10) unsigned;not null" json:"id"`
	Uid       uint32    `gorm:"column:uid;type:int(10) unsigned;not null" json:"uid"`
	GuildId   uint32    `gorm:"column:guild_id;type:int(10) unsigned;not null" json:"guild_id"`
	BeginTime time.Time `gorm:"column:begin_time;type:timestamp;not null" json:"begin_time"`
	EndTime   time.Time `gorm:"column:end_time;type:timestamp;not null" json:"end_time"`
}

func (t *NegotiateCoolDown) TableName() string {
	return "negotiate_cool_down"
}

func (s *Store) AddNegotiateCoolDown(ctx context.Context, info *NegotiateCoolDown) error {
	err := s.db.Create(info).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "AddNegotiateCoolDown fail to Create. info:%+v, err:%v", info, err)
		return err
	}

	log.ErrorWithCtx(ctx, "AddNegotiateCoolDown info:%+v", info)
	return nil
}

// GetNegotiateCoolDown 取出冷却期最晚的一条记录
func (s *Store) GetNegotiateCoolDown(ctx context.Context, uid, guildId uint32) (*NegotiateCoolDown, error) {
	data := &NegotiateCoolDown{}
	err := s.readonlyDb.Where("uid = ? and guild_id = ?", uid, guildId).Order("begin_time desc").First(data).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 不存在
			return data, nil
		}
		log.ErrorWithCtx(ctx, "GetNegotiateCoolDown uid:%d err:%v", uid, err)
		return nil, err
	}

	log.DebugWithCtx(ctx, "GetNegotiateCoolDown uid:%d data:%v", uid, data)
	return data, nil
}

const TblNegotiateTimesRecord = `CREATE TABLE IF NOT EXISTS negotiate_times_record (` +
	`uid int(10) unsigned NOT NULL, ` +
	`guild_id int(10) unsigned NOT NULL, ` +
	`sign_time timestamp NOT NULL, ` +
	`count int(10) unsigned NOT NULL, ` +
	`PRIMARY KEY (uid, guild_id, sign_time)) ENGINE=InnoDB DEFAULT CHARSET=utf8;`

// NegotiateTimesRecord 协商解约次数记录
type NegotiateTimesRecord struct {
	Uid      uint32    `gorm:"column:uid;type:int(10) unsigned;not null" json:"uid"`
	GuildId  uint32    `gorm:"column:guild_id;type:int(10) unsigned;not null" json:"guild_id"`
	SignTime time.Time `gorm:"column:sign_time;type:timestamp;not null" json:"sign_time"`
	Count    uint32    `gorm:"column:count;type:int(10) unsigned;not null" json:"count"`
}

func (t *NegotiateTimesRecord) TableName() string {
	return "negotiate_times_record"
}

func (s *Store) IncreaseNegotiateTimesRecord(ctx context.Context, info *NegotiateTimesRecord) error {

	result := s.db.Model(&NegotiateTimesRecord{}).
		Where("uid = ? AND guild_id = ? AND sign_time = ?", info.Uid, info.GuildId, info.SignTime).
		Update("count", gorm.Expr("count + 1"))

	// 检查更新过程中是否发生错误
	if result.Error != nil {
		log.ErrorWithCtx(ctx, "IncreaseNegotiateTimesRecord failed to update. info:%+v, err:%v", info, result.Error)
		return result.Error
	}

	// 如果 RowsAffected 为 0，表示没有找到匹配的记录进行更新，此时我们需要插入新记录
	if result.RowsAffected == 0 {
		newRecord := NegotiateTimesRecord{
			Uid:      info.Uid,
			GuildId:  info.GuildId,
			SignTime: info.SignTime,
			Count:    1, // 新记录的初始计数为 1
		}
		return s.db.Create(&newRecord).Error
	}

	// 如果 RowsAffected > 0，表示更新成功
	return nil
}

// GetNegotiateTimesRecord 取出协商解约次数记录
func (s *Store) GetNegotiateTimesRecord(ctx context.Context, uid, guildId uint32, signTime time.Time) (*NegotiateTimesRecord, error) {
	data := &NegotiateTimesRecord{}
	err := s.readonlyDb.Where("uid = ? and guild_id = ? and sign_time = ?", uid, guildId, signTime).First(data).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 不存在
			return data, nil
		}
		log.ErrorWithCtx(ctx, "GetNegotiateTimesRecord uid:%d err:%v", uid, err)
		return nil, err
	}

	log.DebugWithCtx(ctx, "GetNegotiateTimesRecord uid:%d data:%v", uid, data)
	return data, nil
}
