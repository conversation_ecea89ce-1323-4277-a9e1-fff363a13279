package mysql

import (
	"context"
	"errors"
	"github.com/jinzhu/gorm"
	"golang.52tt.com/pkg/log"
	"time"
)

// 用来存放需要进行身份验证的用户

type WorkerTypeConfirm struct {
	Uid        uint32    `db:"uid"` // 用户id
	GuildId    uint32    `db:"guild_id"`
	WorkerType uint32    `db:"worker_type"` // 从业者类型
	IsConfirm  bool      `db:"is_confirm"`
	CreateTime time.Time `db:"create_time"`
	//ExpireTime        time.Time `db:"expire_time"` // 过期时间
	UpdateTime time.Time `db:"update_time"` // 更新时间
}

const CreateWorkerTypeConfirmTbl = `CREATE TABLE IF NOT EXISTS worker_type_confirm (
  	uid INT(11) NOT NULL DEFAULT 0 COMMENT '用户id',
  	guild_id INT(11) NOT NULL DEFAULT 0 COMMENT '公会id',
  	worker_type INT(11) NOT NULL DEFAULT 0 COMMENT '从业者类型',
  	is_confirm TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否确认',
  	create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (uid, guild_id)  
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='从业者类型确认表';`

func (w *WorkerTypeConfirm) TableName() string {
	return "worker_type_confirm"
}

func (s *Store) AddWorkerTypeConfirm(tx *gorm.DB, info *WorkerTypeConfirm) error {
	db := s.db
	if tx != nil {
		db = tx
	}

	// 如果时间为0
	if info.UpdateTime.IsZero() {
		info.UpdateTime = time.Now()
	}

	if info.CreateTime.IsZero() {
		info.CreateTime = time.Now()
	}

	return db.Create(info).Error
}

func (s *Store) GetWorkerTypeConfirm(tx *gorm.DB, uid, guildId uint32) (*WorkerTypeConfirm, error) {
	db := s.db
	if tx != nil {
		db = tx
	}

	var info WorkerTypeConfirm
	err := db.Where("uid = ? and guild_id = ?", uid, guildId).First(&info).Error
	// 如果没有结果
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}

	if err != nil {
		return nil, err
	}

	return &info, nil
}

func (s *Store) UpdateWorkerTypeConfirm(tx *gorm.DB, info *WorkerTypeConfirm) error {
	db := s.db
	if tx != nil {
		db = tx
	}

	if info.UpdateTime.IsZero() {
		info.UpdateTime = time.Now()
	}

	return db.Table(info.TableName()).Where("uid = ? AND guild_id = ?", info.Uid, info.GuildId).Updates(info).Error
}

type WorkerTypeChangeApply struct {
	Id             uint32    `db:"id"`  // 自增主键id
	Uid            uint32    `db:"uid"` // 用户id
	GuildId        uint32    `db:"guild_id"`
	OldWorkerType  uint32    `db:"worker_type"` // 从业者类型
	NewWorkerType  uint32    `db:"is_confirm"`
	WorkerChangeId uint32    `db:"change_id"`
	CreateTime     time.Time `db:"create_time"`
	UpdateTime     time.Time `db:"update_time"` // 更新时间
	Status         uint32    `db:"status"`      // 0:待审核 1:已通过 2:已拒绝
}

const CreateWorkerTypeChangeApplyTbl = `CREATE TABLE IF NOT EXISTS worker_type_change_apply (
     	id INT(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键id',
       	uid INT(11) NOT NULL DEFAULT 0 COMMENT '用户id',
	   	guild_id INT(11) NOT NULL DEFAULT 0 COMMENT '公会id',
	   	old_worker_type INT(11) NOT NULL DEFAULT 0 COMMENT '旧的从业者类型',
	   	new_worker_type INT(11) NOT NULL DEFAULT 0 COMMENT '新的从业者类型',
	   	worker_change_id INT(11) NOT NULL DEFAULT 0 COMMENT '从业者类型变更id',
	   	create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	   	update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
	   	status INT(11) NOT NULL DEFAULT 0 COMMENT '状态 0:待审核 1:已通过 2:已拒绝',
	   	PRIMARY KEY (id)
 	) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='从业者类型变更申请表';`

func (w *WorkerTypeChangeApply) TableName() string {
	return "worker_type_change_apply"
}

func (s *Store) AddWorkerTypeChangeApply(tx *gorm.DB, info *WorkerTypeChangeApply) error {
	db := s.db
	if tx != nil {
		db = tx
	}

	// 如果时间为0
	if info.UpdateTime.IsZero() {
		info.UpdateTime = time.Now()
	}

	if info.CreateTime.IsZero() {
		info.CreateTime = time.Now()
	}

	return db.Create(info).Error
}

func (s *Store) GetWorkerTypeChangeApply(tx *gorm.DB, uid, guildId uint32) (*WorkerTypeChangeApply, error) {
	db := s.db
	if tx != nil {
		db = tx
	}

	var info WorkerTypeChangeApply
	err := db.Where("uid = ? and guild_id = ?", uid, guildId).Order("create_time desc").First(&info).Error
	// 如果没有结果
	if errors.Is(err, gorm.ErrRecordNotFound) {
		log.DebugWithCtx(context.Background(), "GetWorkerTypeChangeApply no record found for uid: %d, guildId: %d", uid, guildId)
		return nil, nil
	}

	if err != nil {
		log.ErrorWithCtx(context.Background(), "GetWorkerTypeChangeApply failed for uid: %d, guildId: %d, err: %v", uid, guildId, err)
		return nil, err
	}

	return &info, nil
}

func (s *Store) GetExpiredWorkerTypeChangeApply(tx *gorm.DB, beginTime time.Time) ([]*WorkerTypeChangeApply, error) {
	out := make([]*WorkerTypeChangeApply, 0)
	db := s.db
	if tx != nil {
		db = tx
	}

	err := db.Where("create_time < ? and status = 0", beginTime).Find(&out).Error
	// 如果没有结果
	if errors.Is(err, gorm.ErrRecordNotFound) {
		log.DebugWithCtx(context.Background(), "GetExpiredWorkerTypeChangeApply no record found for beginTime: %v", beginTime)
		return out, nil
	}

	return out, nil
}

func (s *Store) GetWorkerTypeChangeApplyByUidList(tx *gorm.DB, guildId uint32, uidList []uint32) ([]*WorkerTypeChangeApply, error) {
	out := make([]*WorkerTypeChangeApply, 0)
	db := s.db
	if tx != nil {
		db = tx
	}

	err := db.Where(" guild_id = ? AND uid IN (?) AND status = 0", guildId, uidList).Order("create_time desc").Find(&out).Error
	// 如果没有结果
	if errors.Is(err, gorm.ErrRecordNotFound) {
		log.DebugWithCtx(context.Background(), "GetWorkerTypeChangeApplyByGuild no record found for guildId: %d", guildId)
		return out, nil
	}

	if err != nil {
		log.ErrorWithCtx(context.Background(), "GetWorkerTypeChangeApplyByGuild failed for guildId: %d, err: %v", guildId, err)
		return out, err
	}

	return out, nil
}

func (s *Store) UpdateWorkerTypeChangeApplyStatus(tx *gorm.DB, id uint32, status uint32) error {
	db := s.db
	if tx != nil {
		db = tx
	}

	return db.Table("worker_type_change_apply").Where("id = ?", id).Update("status", status).Error
}

type WorkerTypeChangeInfo struct {
	Uid            uint32    `db:"uid"` // 用户id
	GuildId        uint32    `db:"guild_id"`
	ContractTime   time.Time `db:"contract_time"` // 签约时间， 和uid、guild_id一起作为主键
	InviteCount    uint32    `db:"invite_count"`
	LastChangeTime time.Time `db:"last_change_time"`
}

const CreateWorkerTypeChangeInfoTbl = `CREATE TABLE IF NOT EXISTS worker_type_change_info (
       	uid INT(11) NOT NULL DEFAULT 0 COMMENT '用户id',
       	guild_id INT(11) NOT NULL DEFAULT 0 COMMENT '公会id',	
	   	contract_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '签约时间',
	   	invite_count INT(11) NOT NULL DEFAULT 0 COMMENT '邀请次数',	
	   	last_change_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后变更时间',
	   	PRIMARY KEY (uid, guild_id, contract_time)
 	) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='从业者类型变更信息表';`

func (w *WorkerTypeChangeInfo) TableName() string {
	return "worker_type_change_info"
}

func (s *Store) AddWorkerTypeChangeInfo(tx *gorm.DB, info *WorkerTypeChangeInfo) error {
	db := s.db
	if tx != nil {
		db = tx
	}

	// 如果时间为0
	if info.LastChangeTime.IsZero() {
		info.LastChangeTime = time.Now()
	}

	if info.ContractTime.IsZero() {
		info.ContractTime = time.Now()
	}

	return db.Create(info).Error
}

func (s *Store) GetWorkerTypeChangeInfo(tx *gorm.DB, uid, guildId uint32, anchorTime time.Time) (*WorkerTypeChangeInfo, error) {
	db := s.db
	if tx != nil {
		db = tx
	}

	var info WorkerTypeChangeInfo
	err := db.Where("uid = ? and guild_id = ? and contract_time = ?", uid, guildId, anchorTime).First(&info).Error
	// 如果没有结果
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}

	if err != nil {
		return nil, err
	}

	return &info, nil
}

func (s *Store) BatchGetWorkerTypeChangeInfo(tx *gorm.DB, guildId uint32, uidList []uint32) ([]*WorkerTypeChangeInfo, error) {
	out := make([]*WorkerTypeChangeInfo, 0)
	db := s.db
	if tx != nil {
		db = tx
	}

	err := db.Where("guild_id = ? AND uid IN (?)", guildId, uidList).Find(&out).Error
	// 如果没有结果
	if errors.Is(err, gorm.ErrRecordNotFound) {
		log.DebugWithCtx(context.Background(), "BatchGetWorkerTypeChangeInfo no record found for guildId: %d", guildId)
		return out, nil
	}

	if err != nil {
		log.ErrorWithCtx(context.Background(), "BatchGetWorkerTypeChangeInfo failed for guildId: %d, err: %v", guildId, err)
		return nil, err
	}

	return out, nil
}

func (s *Store) UpdateWorkerTypeChangeTime(tx *gorm.DB, uid, guildId uint32, anchorTime time.Time) error {
	db := s.db
	if tx != nil {
		db = tx
	}

	return db.Table("worker_type_change_info").Where("uid = ? AND guild_id = ? AND contract_time = ?", uid, guildId, anchorTime).Update("last_change_time", time.Now()).Error
}

func (s *Store) IncreaseInviteCount(tx *gorm.DB, uid, guildId uint32, anchorTime time.Time) error {
	db := s.db
	if tx != nil {
		db = tx
	}

	// 尝试更新现有记录的 invite_count
	// 使用 Model(&WorkerTypeChangeInfo{}) 可以让 GORM 知道我们操作的是哪个表/结构，
	// 并且在 Update 时不需要显式指定 Table()。
	// gorm.Expr 用于执行数据库级别的表达式，如 "invite_count + 1"
	result := db.Model(&WorkerTypeChangeInfo{}).
		Where("uid = ? AND guild_id = ? AND contract_time = ?", uid, guildId, anchorTime).
		Update("invite_count", gorm.Expr("invite_count + 1"))

	// 检查更新过程中是否发生错误
	if result.Error != nil {
		return result.Error
	}

	// 如果 RowsAffected 为 0，表示没有找到匹配的记录进行更新，此时我们需要插入新记录
	if result.RowsAffected == 0 {
		newRecord := WorkerTypeChangeInfo{
			Uid:          uid,
			GuildId:      guildId,
			ContractTime: anchorTime,
			InviteCount:  1,
		}
		return db.Create(&newRecord).Error
	}

	// 如果 RowsAffected > 0，表示更新成功
	return nil
}

func (s *Store) DeleteWorkerTypeChangeInfo(tx *gorm.DB, uid, guildId uint32, anchorTime time.Time) error {
	db := s.db
	if tx != nil {
		db = tx
	}

	return db.Where("uid = ? AND guild_id = ? AND contract_time = ?", uid, guildId, anchorTime).Delete(&WorkerTypeChangeInfo{}).Error
}

// WorkerTypeChangeSnapshot 改动权益快照
type WorkerTypeChangeSnapshot struct {
	Id      uint32 `db:"id"`  // 自增主键id
	Uid     uint32 `db:"uid"` // 用户id
	GuildId uint32 `db:"guild_id"`
	Content string `db:"content"` // 权益快照内容
}

const CreateWorkerTypeChangeSnapshotTbl = `CREATE TABLE IF NOT EXISTS worker_type_change_snapshot (
       	id INT(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键id',
	   	uid INT(11) NOT NULL DEFAULT 0 COMMENT '用户id',
	   	guild_id INT(11) NOT NULL DEFAULT 0 COMMENT '公会id',
	   	content BLOB NOT NULL DEFAULT '' COMMENT '权益快照内容',
	   	PRIMARY KEY (id)
	   	  	) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='从业者类型变更权益快照表';`

func (w *WorkerTypeChangeSnapshot) TableName() string {
	return "worker_type_change_snapshot"
}

func (s *Store) AddWorkerTypeChangeSnapshot(tx *gorm.DB, info *WorkerTypeChangeSnapshot) (uint32, error) {
	db := s.db
	if tx != nil {
		db = tx
	}

	err := db.Create(info).Error
	if err != nil {
		log.ErrorWithCtx(nil, "AddWorkerTypeChangeSnapshot Create fail uid:%d, guild:%d, err: %v", info.Uid, info.GuildId, err)
		return 0, err
	}

	return info.Id, nil
}

func (s *Store) GetWorkerTypeChangeSnapshot(tx *gorm.DB, id uint32) (*WorkerTypeChangeSnapshot, error) {
	db := s.db
	if tx != nil {
		db = tx
	}

	var info WorkerTypeChangeSnapshot
	err := db.Where("id = ?", id).First(&info).Error
	// 如果没有结果
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}

	if err != nil {
		return nil, err
	}

	return &info, nil
}

func (s *Store) UpdateWorkerTypeChangeSnapshotContent(tx *gorm.DB, id uint32, content string) error {
	db := s.db
	if tx != nil {
		db = tx
	}

	return db.Table("worker_type_change_snapshot").Where("id = ?", id).Update("content", content).Error
}
