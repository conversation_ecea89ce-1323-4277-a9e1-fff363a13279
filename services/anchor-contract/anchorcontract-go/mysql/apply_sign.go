package mysql

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	_ "github.com/go-sql-driver/mysql" // MySQL驱动。
	"github.com/jinzhu/gorm"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/anchorcontract-go"
)

/*
CREATE TABLE IF NOT EXISTS tbl_contract_apply_v2 (
    id int unsigned NOT NULL AUTO_INCREMENT,
    uid int unsigned NOT NULL COMMENT '申请人 uid',
    guild_id int unsigned NOT NULL COMMENT '申请签约的公会',
    identity_type int unsigned NOT NULL DEFAULT 0 COMMENT '申请的主播身份',
    apply_status int unsigned NOT NULL DEFAULT 0 COMMENT '申请状态',
    contract_duration int unsigned NOT NULL,
    identity_num varchar(30) NOT NULL COMMENT '签约人的身份证',
	form_extra varchar(250) NOT NULL COMMENT '申请表单',
	extra varchar(250) NOT NULL COMMENT '额外信息',
    apply_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
    update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
	handler varchar(30) NOT NULL DEFAULT '' COMMENT '处理人',
	remarks varchar(30) NOT NULL DEFAULT '' COMMENT '处理备注',
	conform_status int unsigned NOT NULL DEFAULT 1 COMMENT '是否符合签约条件 1符合 0不符合',
	age int unsigned NOT NULL DEFAULT 0 COMMENT '年龄',
	recharge_num int unsigned NOT NULL DEFAULT 0 COMMENT '近30日充值金额',
	worker_type tinyint NOT NULL DEFAULT 0 COMMENT '从业者类型：see ContractWorkerType',

    PRIMARY KEY (id),
    INDEX index_uid (uid),
	INDEX index_identity_num (identity_num),
	INDEX index_apply_status (apply_status),
    INDEX index_guild_id (guild_id, identity_type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='合约申请记录表v2';
*/

const (
	tblContractApplyName = "tbl_contract_apply_v2"
	tblRecommendTopGuild = "tbl_recommend_top_guild"
)
const queryContractApply = "id,uid,guild_id,identity_type,apply_status,contract_duration,identity_num,apply_time,update_time,form_extra,extra,handler,remarks,conform_status, worker_type"

var genQuery = func(db *gorm.DB, age, rechargeNum, beginTime, endTime uint32) *gorm.DB {
	if age > 0 {
		db = db.Where("age>=?", age)
	}
	if rechargeNum > 0 {
		db = db.Where("recharge_num>=?", rechargeNum)
	}
	if beginTime > 0 && endTime > 0 {
		beginTs := time.Unix(int64(beginTime), 0)
		endTs := time.Unix(int64(endTime), 0)
		db = db.Where("update_time>=? and update_time<=?", beginTs, endTs)
	}
	return db
}

// 主播签约申请
type ContractApply struct {
	Id               uint32    `db:"id"`
	Uid              uint32    `db:"uid"`
	IdentityType     uint32    `db:"identity_type"`
	GuildId          uint32    `db:"guild_id"`
	IdentityNum      string    `db:"identity_num"`
	ContractDuration uint32    `db:"contract_duration"`
	ApplyStatus      uint32    `db:"apply_status"`
	FormExtra        string    `db:"form_extra"`
	Extra            string    `db:"extra"`
	ApplyTime        time.Time `db:"apply_time"`
	UpdateTime       time.Time `db:"update_time"`
	Handler          string    `db:"handler"`
	Remarks          string    `db:"remarks"`
	ConformStatus    uint32    `db:"conform_status"`
	Age              uint32    `db:"age"`
	RechargeNum      uint32    `db:"recharge_num"`
	WorkerType       uint32    `db:"worker_type"`
}

func (t *ContractApply) TableName() string {
	return tblContractApplyName
}

func (t ContractApply) String() string {
	s, _ := json.Marshal(t)
	return string(s)
}

type RecommendTopGuild struct {
	Id            uint32    `db:"id"`
	GuildId       uint32    `db:"guild_id"`
	RecommendTag  string    `db:"recommend_tag"`
	RecommendRank uint32    `db:"recommend_rank"`
	AbilityTag    string    `db:"ability_tag"`
	HonorTitle    string    `db:"honor_title"`
	FromTime      time.Time `db:"from_time"`
	ToTime        time.Time `db:"to_time"`
	CreateTime    time.Time `db:"create_time"`
	UpdateTime    time.Time `db:"update_time"`
}

func (t *RecommendTopGuild) TableName() string {
	return tblRecommendTopGuild
}

type FormInfo struct {
	TagId    uint32 // 标签id
	WorksUrl string // 考核作品链接
	Contract string // 联系方式
}

/*
func GenLiveAnchorForm(tagId uint32, worksUrl, contract string) string {
	info := &FormInfo{
		TagId:    tagId,
		WorksUrl: worksUrl,
		Contract: contract,
	}

	b, err := json.Marshal(info)
	if err != nil {
		log.Errorf("GenLiveAnchorForm fail to Marshal. err:%v", err)
		return ""
	}

	return string(b)
}

func ParseLiveAnchorForm(str string) *FormInfo {
	info := &FormInfo{}
	if str == "" {
		return info
	}

	err := json.Unmarshal([]byte(str), info)
	if err != nil {
		log.Errorf("ParseLiveAnchorForm fail to Unmarshal. err:%v", err)
	}

	return info
}*/

func (s *Store) GetContractApplyWithId(tx *gorm.DB, id uint32) (*ContractApply, error) {
	db := s.db
	if tx != nil {
		db = tx
	}

	info := &ContractApply{}

	err := db.Table(tblContractApplyName).Select(queryContractApply).Where("id=?", id).Scan(info).Error

	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return info, protocol.NewExactServerError(nil, status.ErrContractApplysignNonexist)
		}

		log.Errorf("GetContractApplyWithId fail to Select. id:%+v, err:%v", id, err)
		return info, err
	}

	log.Debugf("GetContractApplyWithId id:%+v, info:%+v", id, info)
	return info, nil
}

func (s *Store) GetGuildContractApplyListWithType(guildId, identityType, begin, limit uint32, statusList []uint32,
	age, rechargeNum, beginTime, endTime uint32, export bool, isSortByUpdateTs bool) ([]*ContractApply, error) {

	list := make([]*ContractApply, 0)
	if len(statusList) == 0 {
		return list, nil
	}

	db := s.db.Table(tblContractApplyName).Select(queryContractApply)
	db = db.Where("guild_id=? and identity_type=? and apply_status in(?)", guildId, identityType, statusList)
	db = genQuery(db, age, rechargeNum, beginTime, endTime) //.Order("apply_time DESC")
	if identityType == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE) {
		if isSortByUpdateTs {
			db = db.Order("update_time DESC")
		} else {
			db = db.Order("apply_time DESC")
		}
	} else {
		db = db.Order("apply_time DESC")
	}

	if !export {
		db = db.Offset(begin).Limit(limit)
	}

	err := db.Scan(&list).Error
	if err != nil {
		log.Errorf("GetGuildContractApplyListWithType fail to Select. guildId:%+v, statusList:%+v, err:%v", guildId, statusList, err)
		return list, err
	}

	log.Debugf("GetGuildContractApplyListWithType guildId:%+v, statusList:%+v, list:%v", guildId, statusList, list)
	return list, nil
}

func (s *Store) GetContractApplyListWithType(identityType, begin, limit uint32, statusList []uint32,
	age, rechargeNum, beginTime, endTime uint32, export bool) ([]*ContractApply, error) {
	list := make([]*ContractApply, 0)
	if len(statusList) == 0 {
		return list, nil
	}

	db := s.db.Table(tblContractApplyName).Select(queryContractApply)
	db = db.Where("apply_status in(?) and identity_type=? ", statusList, identityType)

	db = genQuery(db, age, rechargeNum, beginTime, endTime).Order("apply_time DESC")
	if !export {
		db = db.Offset(begin).Limit(limit)
	}

	err := db.Scan(&list).Error
	if err != nil {
		log.Errorf("GetContractApplyListWithType fail to Select. statusList:%+v, err:%v", statusList, err)
		return list, err
	}

	log.Debugf("GetContractApplyListWithType statusList:%+v, list:%v", statusList, list)
	return list, nil
}

func (s *Store) GetAllContractApplyCntWithType(identityType uint32, statusList []uint32,
	age, rechargeNum, beginTime, endTime uint32) (uint32, error) {
	cnt := uint32(0)
	if len(statusList) == 0 {
		return 0, nil
	}

	var rows *sql.Rows
	var err error

	db := s.db.Table(tblContractApplyName).Select("count(1)")
	db = db.Where("apply_status in(?) and identity_type=?", statusList, identityType)

	db = genQuery(db, age, rechargeNum, beginTime, endTime)

	rows, err = db.Rows()
	if err != nil {
		log.Errorf("GetAllContractApplyCntWithType fail to Select. identityType:%+v, statusList:%+v, err:%v",
			identityType, statusList, err)
		return cnt, err
	}
	if rows.Err() != nil {
		log.Errorln(rows.Err())
	}

	defer rows.Close()
	if rows.Next() {
		_ = rows.Scan(&cnt)
	}

	log.Debugf("GetAllContractApplyCntWithType identityType:%+v, statusList:%+v, cnt:%v", identityType, statusList, cnt)
	return cnt, nil
}

func (s *Store) GetGuildContractApplyList(guildId, begin, limit uint32, statusList []uint32) ([]*ContractApply, error) {
	list := make([]*ContractApply, 0)
	if len(statusList) == 0 {
		return list, nil
	}

	err := s.db.Table(tblContractApplyName).Select(queryContractApply).
		Where("guild_id=? and apply_status in(?)", guildId, statusList).
		Order("apply_time DESC").Offset(begin).Limit(limit).Scan(&list).Error

	if err != nil {
		log.Errorf("GetGuildContractApplyList fail to Select. guildId:%+v, statusList:%+v, err:%v", guildId, statusList, err)
		return list, err
	}

	log.Debugf("GetGuildContractApplyList guildId:%+v, statusList:%+v, list:%v", guildId, statusList, list)
	return list, nil
}

func (s *Store) GetGuildContractApplyListWithIdentity(guildId, identityType, begin, limit uint32, statusList []uint32) ([]*ContractApply, error) {
	list := make([]*ContractApply, 0)
	if len(statusList) == 0 {
		return list, nil
	}

	err := s.db.Table(tblContractApplyName).Select(queryContractApply).
		Where("guild_id=? and identity_type=? and apply_status in(?)", guildId, identityType, statusList).
		Order("apply_time DESC").Offset(begin).Limit(limit).Scan(&list).Error

	if err != nil {
		log.Errorf("GetGuildContractApplyListWithIdentity fail to Select. guildId:%+v, statusList:%+v, err:%v", guildId, statusList, err)
		return list, err
	}

	log.Debugf("GetGuildContractApplyListWithIdentity guildId:%+v, identityType %d statusList:%+v, list:%v", guildId, identityType, statusList, list)
	return list, nil
}

func (s *Store) GetGuildContractApplyCnt(guildId uint32, identityTypeList []uint32, statusList []uint32,
	age, rechargeNum, beginTime, endTime uint32) (uint32, error) {
	cnt := uint32(0)
	if len(identityTypeList) == 0 || len(statusList) == 0 {
		return 0, nil
	}

	var rows *sql.Rows
	var err error

	db := s.db.Table(tblContractApplyName).Select("count(1)")

	db = db.Where("guild_id=? and identity_type in(?) and apply_status in(?)", guildId, identityTypeList, statusList)

	db = genQuery(db, age, rechargeNum, beginTime, endTime)

	rows, err = db.Rows()
	if err != nil {
		log.Errorf("GetGuildContractApplyCnt fail to Select. guildId:%+v, identityTypeList:%+v, statusList:%+v, err:%v",
			guildId, identityTypeList, statusList, err)
		return cnt, err
	}
	if rows.Err() != nil {
		log.Errorln(rows.Err())
	}

	defer rows.Close()
	if rows.Next() {
		_ = rows.Scan(&cnt)
	}

	log.Debugf("GetGuildContractApplyCnt guildId:%+v, identityTypeList:%+v, statusList:%+v, cnt:%v", guildId, identityTypeList, statusList, cnt)
	return cnt, nil
}

func (s *Store) BatchGetUserContractApplyList(uidList []uint32, identityType uint32, statusList []uint32,
	age, rechargeNum, beginTime, endTime uint32) ([]*ContractApply, error) {
	list := make([]*ContractApply, 0)
	if len(uidList) == 0 && len(statusList) == 0 {
		return list, nil
	}

	db := s.db.Table(tblContractApplyName).Select(queryContractApply)

	db = db.Where("uid in(?) and identity_type=? and apply_status in(?)", uidList, identityType, statusList)

	db = genQuery(db, age, rechargeNum, beginTime, endTime)

	err := db.Order("uid, apply_time DESC").Scan(&list).Error
	if err != nil {
		log.Errorf("BatchGetUserContractApplyList fail to Select. uid:%+v, statusList:%+v, err:%v", uidList, statusList, err)
		return list, err
	}

	log.Debugf("BatchGetUserContractApplyList uid:%+v, statusList:%+v, list:%v", uidList, statusList, list)
	return list, nil
}

func (s *Store) GetUserContractApplyList(uid, begin, limit uint32, statusList []uint32, getAllStatus bool) ([]*ContractApply, error) {
	list := make([]*ContractApply, 0)
	if !getAllStatus && len(statusList) == 0 {
		return list, nil
	}

	db := s.db.Table(tblContractApplyName).Select(queryContractApply)
	if getAllStatus {
		db = db.Where("uid=?", uid)
	} else {
		db = db.Where("uid=? and apply_status in(?)", uid, statusList)
	}

	err := db.Order("apply_time DESC").Offset(begin).Limit(limit).Scan(&list).Error

	if err != nil {
		log.Errorf("GetUserContractApplyList fail to Select. uid:%+v, statusList:%+v, getAllStatus:%v, err:%v", uid, statusList, getAllStatus, err)
		return list, err
	}

	log.Debugf("GetUserContractApplyList uid:%+v, statusList:%+v, getAllStatus:%v, list:%v", uid, statusList, getAllStatus, list)
	return list, nil
}

func (s *Store) GetContractApplyWithUid(uid uint32, statusList []uint32) ([]*ContractApply, error) {
	list := make([]*ContractApply, 0)
	if len(statusList) == 0 {
		return list, nil
	}

	err := s.db.Table(tblContractApplyName).Select(queryContractApply).
		Where("uid=? and apply_status in(?)", uid, statusList).
		Scan(&list).Error

	if err != nil {
		log.Errorf("GetContractApplyWithUid fail to Select. uid:%+v, statusList:%+v, err:%v", uid, statusList, err)
		return list, err
	}

	log.Debugf("GetContractApplyWithUid uid:%+v, statusList:%+v, list:%v", uid, statusList, list)
	return list, nil
}

func (s *Store) GetContractApplyWithIdNum(identityNum string, statusList []uint32) ([]*ContractApply, error) {
	list := make([]*ContractApply, 0)
	if len(statusList) == 0 {
		return list, nil
	}

	err := s.db.Table(tblContractApplyName).Select(queryContractApply).
		Where("identity_num=? and apply_status in(?)", identityNum, statusList).
		Scan(&list).Error

	if err != nil {
		log.Errorf("GetContractApplyWithIdNum fail to Select. identityNum:%+v, statusList:%+v, err:%v", identityNum, statusList, err)
		return list, err
	}

	log.Debugf("GetContractApplyWithIdNum identityNum:%+v, statusList:%+v, list:%v", identityNum, statusList, list)
	return list, nil
}

func (s *Store) AddContractApply(tx *gorm.DB, info *ContractApply) error {
	db := s.db
	if tx != nil {
		db = tx
	}

	err := db.Table(tblContractApplyName).Create(info).Error
	if err != nil {
		log.Errorf("AddContractApply fail to Create. info:%+v, err:%v", info, err)
		return err
	}

	log.Debugf("AddContractApply info:%+v", info)
	return nil
}

func (s *Store) UpdateContractApplyStatusWithId(tx *gorm.DB, id, preStatus, goalStatus uint32, handler, remarks string) (bool, error) {
	db := s.db
	if tx != nil {
		db = tx
	}

	sql := fmt.Sprintf("update %s set apply_status=?,handler=?,remarks=? where id=? and apply_status=?", tblContractApplyName)
	db = db.Exec(sql, goalStatus, handler, remarks, id, preStatus)

	err := db.Error
	if err != nil {
		log.Errorf("UpdateContractApplyStatusWithId fail to Exec. id:%v, preStatus:%v, goalStatus:%v, err:%v",
			id, preStatus, goalStatus, err)
		return false, err
	}

	log.Debugf("UpdateContractApplyStatusWithId id:%v, preStatus:%v, goalStatus:%v",
		id, preStatus, goalStatus)
	return db.RowsAffected > 0, nil
}

func (s *Store) UpdateContractApplyStatus(tx *gorm.DB, uid, identityType uint32, preStatusList []uint32, goalStatus uint32) (bool, error) {
	if len(preStatusList) == 0 {
		return false, nil
	}

	db := s.db
	if tx != nil {
		db = tx
	}

	sql := fmt.Sprintf("update %s set apply_status=? where uid=? and identity_type=? and apply_status in (?)", tblContractApplyName)

	db = db.Exec(sql, goalStatus, uid, identityType, preStatusList)
	err := db.Error
	if err != nil {
		log.Errorf("UpdateContractApplyStatus fail to Exec. uid:%v, identityType:%v, preStatusList:%v, goalStatus:%v, err:%v",
			uid, identityType, preStatusList, goalStatus, err)
		return false, err
	}

	log.Debugf("UpdateContractApplyStatus uid:%v, identityType:%v, preStatusList:%v, goalStatus:%v",
		uid, identityType, preStatusList, goalStatus)
	return db.RowsAffected > 0, nil
}

func (s *Store) UpdateContractApplyStatusAllIdentity(tx *gorm.DB, uid uint32, preStatusList []uint32, goalStatus uint32) (bool, error) {
	if len(preStatusList) == 0 {
		return false, nil
	}

	db := s.db
	if tx != nil {
		db = tx
	}

	sql := fmt.Sprintf("update %s set apply_status=? where uid=? and apply_status in (?)", tblContractApplyName)

	db = db.Exec(sql, goalStatus, uid, preStatusList)
	err := db.Error
	if err != nil {
		log.Errorf("UpdateContractApplyStatusAllIdentity fail to Exec. uid:%v, preStatusList:%v, goalStatus:%v, err:%v",
			uid, preStatusList, goalStatus, err)
		return false, err
	}

	log.Debugf("UpdateContractApplyStatusAllIdentity uid:%v, preStatusList:%v, goalStatus:%v",
		uid, preStatusList, goalStatus)
	return db.RowsAffected > 0, nil
}

func (s *Store) GetGuildApplySignRecordList(queryType, guildId uint32, uids []uint32, begin, limit uint32) (uint32, []*ContractApply, error) {
	list := make([]*ContractApply, 0)

	db := s.db.Table(tblContractApplyName).Select(queryContractApply).Where("guild_id=?", guildId)

	if len(uids) > 0 {
		db = db.Where("uid in(?)", uids)
	}

	if queryType == uint32(pb.QUERY_APPLY_SIGN_TYPE_QUERY_APPLY_SIGN_TYPE_ALL) {
		db = db.Where("apply_status in(?)", []uint32{
			uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_PRESIDENT_HANDLING),
			uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_OFFICIAL_HANDLING),
		}).Order("apply_time DESC")
	} else if queryType == uint32(pb.QUERY_APPLY_SIGN_TYPE_QUERY_APPLY_SIGN_TYPE_HISTORY) {
		db = db.Where("apply_status in(?)", []uint32{
			uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_PRESIDENT_REJECT),
			uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_OFFICIAL_REJECT),
			uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_PASS),
		}).Order("update_time DESC")
	}

	total, err := getRowsCnt(db)
	if err != nil {
		return 0, list, err
	}

	err = db.Offset(begin).Limit(limit).Scan(&list).Error
	return total, list, err
}

var (
	getRowsCnt = func(db *gorm.DB) (uint32, error) {
		rows, err := db.Select("count(1)").Rows()
		if err != nil {
			return 0, err
		}
		cnt := uint32(0)
		defer rows.Close()
		if rows.Next() {
			_ = rows.Scan(&cnt)
		}
		return cnt, nil
	}
)

func (s *Store) GetOfficialApplySignAnchorTimeRange(releaseTs, endTs, offset, limit uint32) ([]*ContractApply, error) {
	list := []*ContractApply{}
	err := s.db.Table(tblContractApplyName).Select(queryContractApply).
		Where("apply_status=1 and identity_type=1 and apply_time>? and update_time<?",
			time.Unix(int64(releaseTs), 0).Format("2006-01-02 15:04:05"),
			time.Unix(int64(endTs), 0).Format("2006-01-02 15:04:05")).
		Offset(offset).Limit(limit).
		Find(&list).Error
	return list, err
}

func (s *Store) GetRecommendTopGuildList(ctx context.Context) ([]*RecommendTopGuild, error) {
	list := make([]*RecommendTopGuild, 0)
	nowTm := time.Now()
	err := s.db.Table(tblRecommendTopGuild).Where("from_time <= ? and to_time >= ?", nowTm, nowTm).Find(&list).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRecommendTopGuildList error:%v", err)
		return list, err
	}
	log.DebugWithCtx(ctx, "GetRecommendTopGuildList list:%v", len(list))
	return list, nil
}
