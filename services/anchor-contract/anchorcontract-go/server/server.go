package server

import (
	"context"
	"fmt"
	esport_role "golang.52tt.com/clients/esport-role"
	timerManager "golang.52tt.com/services/anchor-contract/anchorcontract-go/timer"
	"net"
	"time"

	"github.com/go-redis/redis"
	"github.com/jinzhu/gorm"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/metrics"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	tracing "golang.52tt.com/pkg/tracing/jaeger"
	authPB "golang.52tt.com/protocol/app/auth"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/anchorcontract-go"
	esportRolePb "golang.52tt.com/protocol/services/esport_role"
	"golang.52tt.com/services/anchor-contract/anchorcontract-go/cache"
	"golang.52tt.com/services/anchor-contract/anchorcontract-go/conf"
	"golang.52tt.com/services/anchor-contract/anchorcontract-go/event"
	"golang.52tt.com/services/anchor-contract/anchorcontract-go/manager"
	"golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql"
	"gopkg.in/errgo.v2/errors"
)

var mapStatusTip = map[uint32]string{
	0: "",
	1: "未申请",
	2: "已申请",
	3: "已签约",
	4: "",
}

const defaultAnchorCertLocalCacheTTL = 300

type AnchorContract struct {
	sc       *conf.ServiceConfigT
	mgr      *manager.AnchorContractMgr
	timerMgr *timerManager.TimerManager
	cache    *cache.AnchorContractCache
	store    *mysql.Store

	chLiveStatusKafkaSub *event.ChLiveStatusKafkaSub
	presentKafkaSub      *event.PresentEventSub
	tbeanEventSub        *event.TbeanEventSub
	esportTradeSub       *event.EsportTradeSub
	esportRoleSub        *event.EsportRoleSub
	guildCoopKafkaSub    *event.GuildCoopSub
	mediaProcessSub      *event.MediaProcessSub

	esportRoleCli esport_role.IClient
}

func (s *AnchorContract) TriggerTimer(ctx context.Context, req *pb.TriggerTimerReq) (*pb.TriggerTimerResp, error) {
	out := &pb.TriggerTimerResp{}

	switch req.GetTimerType() {
	case pb.TriggerTimerReq_Timer_Type_SettleAnchorCertEmotionStoryTimer:
		go s.mgr.SettleAnchorCertEmotionStoryTimer()
	case pb.TriggerTimerReq_Timer_Type_SettleAnchorCertMusicTimer:
		go s.mgr.SettleAnchorCertMusicTimer()
	case pb.TriggerTimerReq_Timer_Type_SettleAnchorCertTwoDimensionsTimer:
		go s.mgr.SettleAnchorCertTwoDimensionsTimer()
	}

	log.InfoWithCtx(ctx, "TriggerTimer end req:%v", req)
	return out, nil
}

func (s *AnchorContract) TestHandleYearBanUser(ctx context.Context, req *pb.TestHandleYearBanUserReq) (*pb.TestHandleYearBanUserResp, error) {
	out := &pb.TestHandleYearBanUserResp{}
	s.mgr.HandleYearBanUser()
	return out, nil
}

func (s *AnchorContract) CheckIsTotalNewMultiAnchor(ctx context.Context, req *pb.CheckIsTotalNewMultiAnchorReq) (*pb.CheckIsTotalNewMultiAnchorResp, error) {
	out := &pb.CheckIsTotalNewMultiAnchorResp{}
	resp, err := s.mgr.CheckIsTotalNewMultiAnchor(ctx, req.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckIsTotalNewMultiAnchor error req %v err %v", req, err)
		return out, err
	}
	out.IsTotalNew = resp
	return out, nil
}

func (s *AnchorContract) TestNotifyNegotiateExpire(ctx context.Context, req *pb.TestNotifyNegotiateExpireReq) (*pb.TestNotifyNegotiateExpireResp, error) {
	out := &pb.TestNotifyNegotiateExpireResp{}
	s.mgr.NotifyNegotiateExpire()
	return out, nil
}

func (s *AnchorContract) GetRejectReason(ctx context.Context, req *pb.GetRejectReasonReq) (*pb.GetRejectReasonResp, error) {
	return s.mgr.GetRejectReason(ctx, req)
}

func (s *AnchorContract) GetNegotiateReasonType(ctx context.Context, req *pb.GetNegotiateReasonTypeReq) (*pb.GetNegotiateReasonTypeResp, error) {
	return s.mgr.GetNegotiateReasonType(ctx, req)
}

func (s *AnchorContract) InviteMemberChangeWorkerType(ctx context.Context, req *pb.InviteMemberChangeWorkerTypeReq) (*pb.InviteMemberChangeWorkerTypeResp, error) {
	_, err := s.mgr.InviteMemberChangeWorkerType(ctx, req.GetUid(), req.GetGuildId(), req.GetWorkerType())
	if err != nil {
		log.ErrorWithCtx(ctx, "InviteMemberChangeWorkerType error req %v err %v", req, err)
		return &pb.InviteMemberChangeWorkerTypeResp{}, err
	}

	return &pb.InviteMemberChangeWorkerTypeResp{}, nil
}

func (s *AnchorContract) GetContractChangeInfo(ctx context.Context, req *pb.GetContractChangeInfoReq) (*pb.GetContractChangeInfoResp, error) {
	resp, err := s.mgr.GetContractChangeInfo(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetContractChangeInfo error req %v err %v", req, err)
		return resp, err
	}
	return resp, nil
}

func (s *AnchorContract) HandleContractChange(ctx context.Context, req *pb.HandleContractChangeReq) (*pb.HandleContractChangeResp, error) {
	err := s.mgr.HandleContractChange(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleContractChange error req %v err %v", req, err)
		return &pb.HandleContractChangeResp{}, err
	}

	return &pb.HandleContractChangeResp{}, nil
}

func (s *AnchorContract) GetNeedConfirmWorkerType(ctx context.Context, req *pb.GetNeedConfirmWorkerTypeReq) (*pb.GetNeedConfirmWorkerTypeResp, error) {
	out := &pb.GetNeedConfirmWorkerTypeResp{}
	resp, err := s.mgr.GetWorkerTypeConfirm(ctx, req.GetUid(), req.GetGuildId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetNeedConfirmWorkerType error req %v err %v", req, err)
		return out, err
	}

	out.NeedConfirm = resp
	return out, nil
}

func (s *AnchorContract) ModifyWorkerType(ctx context.Context, req *pb.ModifyWorkerTypeReq) (*pb.ModifyWorkerTypeResp, error) {
	out := &pb.ModifyWorkerTypeResp{}
	err := s.mgr.UpdateWorkerTypeConfirm(ctx, req.GetUid(), req.GetWorkerType())
	if err != nil {
		log.ErrorWithCtx(ctx, "ModifyWorkerType error req %v err %v", req, err)
		return out, err
	}
	return out, nil
}

func (s *AnchorContract) AddNeedConfirmWorkerType(ctx context.Context, req *pb.AddNeedConfirmWorkerTypeReq) (*pb.AddNeedConfirmWorkerTypeResp, error) {
	out := &pb.AddNeedConfirmWorkerTypeResp{}
	err := s.mgr.AddWorkerTypeConfirm(ctx, req.GetUid(), req.GetGuildId())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddNeedConfirmWorkerType error req %v err %v", req, err)
		return out, err
	}

	return out, nil
}

func (s *AnchorContract) RecordAnchorNoticeHandle(c context.Context, req *pb.RecordAnchorNoticeHandleReq) (*pb.RecordAnchorNoticeHandleResp, error) {
	return s.mgr.RecordAnchorNoticeHandle(c, req)
}

func (s *AnchorContract) GetAnchorNoticeHandleHistory(c context.Context, req *pb.GetAnchorNoticeHandleHistoryReq) (*pb.GetAnchorNoticeHandleHistoryResp, error) {
	return s.mgr.GetAnchorNoticeHandleHistory(c, req)
}

func (s *AnchorContract) ContractClaimObsToken(c context.Context, req *pb.ContractClaimObsTokenReq) (*pb.ContractClaimObsTokenResp, error) {
	key, expireAt, err := s.mgr.ClaimUploadToken(c, req.GetExpiration())
	return &pb.ContractClaimObsTokenResp{
		Token:    key,
		ExpireAt: int64(expireAt),
	}, err
}

func (s *AnchorContract) ApplyCancelContractNew(c context.Context, req *pb.ApplyCancelContractNewReq) (*pb.ApplyCancelContractNewResp, error) {
	resp := &pb.ApplyCancelContractNewResp{}
	err := s.mgr.ApplyCancelContractNew(c, req)
	if err != nil {
		log.ErrorWithCtx(c, "ApplyCancelContractNew req:%v, err:%v", req, err)
		return resp, err
	}
	return resp, nil
}
func (s *AnchorContract) GetContractWorkerConfigs(c context.Context, _ *pb.GetContractWorkerConfigsReq) (*pb.GetContractWorkerConfigsResp, error) {
	return s.mgr.GetContractWorkerConfigs(c)
}

func (s *AnchorContract) GetCancelPayAmount(c context.Context, req *pb.GetCancelPayAmountReq) (*pb.GetCancelPayAmountResp, error) {
	return s.mgr.GetCancelPayAmount(c, req)
}

func (s *AnchorContract) GetOfficialCancelSignList(c context.Context, req *pb.GetOfficialCancelSignListReq) (*pb.GetOfficialCancelSignListResp, error) {
	return s.mgr.GetOfficialCancelSignList(c, req)
}

func (s *AnchorContract) OfficialHandleCancelSign(c context.Context, req *pb.OfficialHandleCancelSignReq) (*pb.OfficialHandleCancelSignResp, error) {
	return s.mgr.OfficialHandleCancelSign(c, req)
}

func (s *AnchorContract) OfficialRemarkPayCancelSign(c context.Context, req *pb.OfficialRemarkPayCancelSignReq) (*pb.OfficialRemarkPayCancelSignResp, error) {
	return s.mgr.OfficialRemarkPayCancelSign(c, req)
}

func (s *AnchorContract) LockCancelPayAmount(ctx context.Context, req *pb.LockCancelPayAmountReq) (*pb.LockCancelPayAmountResp, error) {
	return s.mgr.LockCancelPayAmount(ctx, req)
}

func LookupHost(host string) (ip string, err error) {
	list, err := net.LookupHost(host)
	if err != nil {
		return
	}
	if len(list) == 0 {
		err = fmt.Errorf("no such host (%s), get ip addr list eq 0", host)
		return
	}
	return list[0], nil
}

func NewAnchorContract(ctx context.Context, cfg config.Configer) (*AnchorContract, error) {

	sc := &conf.ServiceConfigT{EnableAnchorCertLocalCache: true, AnchorCertLocalCacheTTL: defaultAnchorCertLocalCacheTTL}
	cfgPath := ctx.Value("configfile").(string)
	if cfgPath == "" {
		return nil, errors.New("configfile not exist")
	}
	err := sc.Parse(cfgPath)
	if err != nil {
		log.ErrorWithCtx(ctx, "config Parse fail err:%v", err)
		return nil, err
	}

	if sc.RedisConfig.PoolSize == 0 {
		sc.RedisConfig.PoolSize = 300
	}
	redisClient := redis.NewClient(&redis.Options{
		Network:            sc.GetRedisConfig().Protocol,
		Addr:               sc.GetRedisConfig().Addr(),
		PoolSize:           sc.GetRedisConfig().PoolSize,
		IdleCheckFrequency: sc.GetRedisConfig().IdleCheckFrequency(),
		DB:                 sc.GetRedisConfig().DB,
	})
	log.DebugWithCtx(ctx, "Initialized redis %s", sc.GetRedisConfig().Print())
	redisTracer := tracing.Init("anchorcontract-go-redis")
	cacheClient, err := cache.NewAnchorContractCache(redisClient, redisTracer)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewAnchorContractCache err:%v", err)
		return nil, err
	}

	mysqlCfg, readDbCfg := sc.GetMysqlConfig(), sc.GetMysqlReadonlyConfig()
	log.InfoWithCtx(ctx, "mysql_cfg=%+v readDbCfg=%+v", mysqlCfg, readDbCfg)

	mysqlDb, err := gorm.Open("mysql", mysqlCfg.ConnectionString())
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to create mysql %v", err)
		return nil, err
	}
	mysqlDb.DB().SetMaxOpenConns(mysqlCfg.MaxOpenConns)
	mysqlDb.DB().SetMaxIdleConns(mysqlCfg.MaxIdleConns)
	mysqlDb.DB().SetConnMaxLifetime(time.Minute * 5)

	readonlyDb, err := gorm.Open("mysql", readDbCfg.ConnectionString())
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to create mysql %v", err)
		return nil, err
	}
	readonlyDb.DB().SetMaxOpenConns(readDbCfg.MaxOpenConns)
	readonlyDb.DB().SetMaxIdleConns(readDbCfg.MaxIdleConns)
	readonlyDb.DB().SetConnMaxLifetime(time.Minute)

	mysqlStore := mysql.NewMysql(mysqlDb.Debug(), readonlyDb.Debug())
	mysqlStore.CreateTable()

	mgr, err := manager.NewAnchorContractMgr(sc, cacheClient, mysqlStore)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to create manager %v", err)
		return nil, err
	}

	chLiveStatusEvent, err := event.NewChLiveStatusKafkaSub(sc.GetLiveStatusKafkaConfig().ClientID, sc.GetLiveStatusKafkaConfig().GroupID,
		sc.GetLiveStatusKafkaConfig().TopicList(), sc.GetLiveStatusKafkaConfig().BrokerList(), mgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewChLiveStatusKafkaSubscriber err %s", err.Error())
		return nil, err
	}

	presentKafkaSub, err := event.NewPresentEventSub(sc.GetPresentKafkaConfig().ClientID, sc.GetPresentKafkaConfig().GroupID,
		sc.GetPresentKafkaConfig().TopicList(), sc.GetPresentKafkaConfig().BrokerList(), mgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewPresentEventSub err %s", err.Error())
		return nil, err
	}

	tbeanKafkaSub, err := event.NewTbeanEventSub(sc.GetTbeanKafkaConfig().ClientID, sc.GetTbeanKafkaConfig().GroupID,
		sc.GetTbeanKafkaConfig().TopicList(), sc.GetTbeanKafkaConfig().BrokerList(), mgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewTbeanEventSub err %s", err.Error())
		return nil, err
	}

	esportTradeKafkaConfig := sc.EsportTradeKafkaConfig
	esportTradeSub, err := event.NewEsportTradeSub(esportTradeKafkaConfig.ClientID, esportTradeKafkaConfig.GroupID,
		esportTradeKafkaConfig.TopicList(), esportTradeKafkaConfig.BrokerList(), mgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewEsportTradeSub err %s", err.Error())
		return nil, err
	}

	esportRoleKafkaConfig := sc.EsportRoleKafkaConfig
	esportRoleSub, err := event.NewEsportRoleSub(esportRoleKafkaConfig.ClientID, esportRoleKafkaConfig.GroupID,
		esportRoleKafkaConfig.TopicList(), esportRoleKafkaConfig.BrokerList(), mgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewEsportRoleSub err %s", err.Error())
		return nil, err
	}

	guildCoopKafkaConfig := sc.GuildCoopKafkaConfig
	guildCoopKafkaSub, err := event.NewGuildCoopSub(guildCoopKafkaConfig.ClientID, guildCoopKafkaConfig.GroupID,
		guildCoopKafkaConfig.TopicList(), guildCoopKafkaConfig.BrokerList(), mgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewGuildCoopSub err %s", err.Error())
		return nil, err
	}

	mediaProcessSub, err := event.NewMediaProcessSub(sc.GetMediaProcessKafkaConfig().ClientID, sc.GetMediaProcessKafkaConfig().GroupID, sc.GetMediaProcessKafkaConfig().TopicList(),
		sc.GetMediaProcessKafkaConfig().BrokerList(), mgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewMediaProcessSub err %s", err.Error())
		return nil, err
	}

	esportRoleCli_ := esport_role.NewIClient()
	timerMgr := timerManager.NewTimerManager(ctx, mgr)

	return &AnchorContract{
		sc:    sc,
		mgr:   mgr,
		cache: cacheClient,
		store: mysqlStore,

		chLiveStatusKafkaSub: chLiveStatusEvent,
		presentKafkaSub:      presentKafkaSub,
		tbeanEventSub:        tbeanKafkaSub,
		esportTradeSub:       esportTradeSub,
		esportRoleSub:        esportRoleSub,
		guildCoopKafkaSub:    guildCoopKafkaSub,
		mediaProcessSub:      mediaProcessSub,
		esportRoleCli:        esportRoleCli_,
		timerMgr:             timerMgr,
	}, nil
}

// 获取用户合约缓存信息
func (s *AnchorContract) GetUserContractCacheInfo(ctx context.Context, in *pb.GetUserContractCacheInfoReq) (*pb.ContractCacheInfo, error) {
	defer metrics.DISCOVERY_FUNC_TRACK(protocol.NewExactServerError(nil, 0, "anchorcontract-go.GetUserContractCacheInfo")).End()
	return s.mgr.GetUserContractCacheInfo(ctx, in.GetUid())
}

func (s *AnchorContract) BatchGetUserContractCacheInfo(ctx context.Context, in *pb.BatchGetUserContractCacheInfoReq) (*pb.BatchGetUserContractCacheInfoResp, error) {
	return s.mgr.BatchGetUserContractCacheInfo(ctx, in)
}

func (s *AnchorContract) GetContract(ctx context.Context, in *pb.GetContractReq) (*pb.GetContractResp, error) {
	return s.mgr.GetContract(ctx, in)
}

// 获取用户合约
func (s *AnchorContract) GetUserContract(ctx context.Context, in *pb.GetUserContractReq) (*pb.GetUserContractResp, error) {
	out := &pb.GetUserContractResp{}
	out.ContractStatus = uint32(authPB.ENUM_ContractStatus_ENUM_ContractStatus_Invalid)
	out.StatusTip = mapStatusTip[out.ContractStatus]

	uid := in.GetUid()
	contract, err := s.mgr.GetUserContractInfo(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserContract fail to GetUserContractInfo in:%+v err:%v", in, err)
		return out, err
	}

	// 合约不存在或已过期
	if contract.GetActorUid() == 0 || contract.GetGuildId() == 0 ||
		contract.GetExpireTime() <= uint32(time.Now().Unix()) {

		out.ContractStatus = uint32(authPB.ENUM_ContractStatus_ENUM_ContractStatus_UnApply)
		out.StatusTip = mapStatusTip[out.ContractStatus]

		applyList, err := s.store.GetUserContractApplyList(uid, 0, 1, []uint32{
			uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_PRESIDENT_HANDLING),
			uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_OFFICIAL_HANDLING),
		}, false)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserContract fail to GetUserContractApplyList in:%+v err:%v", in, err)
			return out, err
		}

		if len(applyList) > 0 {
			out.ContractStatus = uint32(authPB.ENUM_ContractStatus_ENUM_ContractStatus_Apply)
			out.StatusTip = mapStatusTip[out.ContractStatus]
		}

		return out, nil
	}

	out.ContractStatus = uint32(authPB.ENUM_ContractStatus_ENUM_ContractStatus_Sign)
	out.StatusTip = mapStatusTip[out.ContractStatus]

	list, err := s.store.GetAnchorIdentity(nil, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserContract fail to GetAnchorIdentity. in:%+v, err:%v", in, err)
		return out, err
	}

	out.Contract = contract
	out.Renew, _ = s.mgr.GetUserExtensionStatus(ctx, contract)

	out.AnchorIdentityList = make([]uint32, 0)
	for _, e := range list {
		if e.GuildId != contract.GetGuildId() {
			log.ErrorWithCtx(ctx, "GetUserContract uid:%v AnchorIdentity data inconsistency.", uid)
			continue
		}
		out.AnchorIdentityList = append(out.AnchorIdentityList, e.IdentityType)
	}

	log.DebugWithCtx(ctx, "GetUserContract in:%+v out:%+v", in, out)
	return out, nil
}

// 根据身份证号获取用户合约信息
func (s *AnchorContract) GetContractWithIdentity(ctx context.Context, in *pb.GetContractWithIdentityReq) (*pb.GetContractWithIdentityResp, error) {
	out := &pb.GetContractWithIdentityResp{}

	info, exist, err := s.store.GetContractWithIdentityNum(in.GetIdentityNum())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetContractWithIdentity fail to GetContractWithIdentityNum. in:%+v, err:%v", in, err)
		return out, err
	}

	if !exist {
		return out, protocol.NewExactServerError(nil, status.ErrContractNonexist)
	}

	out.Contract = &pb.ContractInfo{
		ActorUid:         info.Uid,
		GuildId:          info.GuildId,
		SignTime:         uint32(info.SignTime.Unix()),
		ContractDuration: info.ContractDuration,
		ExpireTime:       uint32(info.ContractExpireTime.Unix()),
		Permission:       info.Permissions,
	}

	log.DebugWithCtx(ctx, "GetContractWithIdentity in:%+v out:%+v", in, out)
	return out, nil
}

// 批量获取用户合约信息
func (s *AnchorContract) BatchGetUserContract(ctx context.Context, in *pb.BatchGetUserContractReq) (*pb.BatchGetUserContractResp, error) {
	out := &pb.BatchGetUserContractResp{}
	list, err := s.store.GetContractWithUidList(in.GetUidList())
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetUserContract fail to GetContractWithUidList. in:%+v, err:%v", in, err)
		return out, err
	}

	out.ContractList = make([]*pb.ContractInfo, 0, len(list))
	for _, info := range list {
		out.ContractList = append(out.ContractList, &pb.ContractInfo{
			ActorUid:         info.Uid,
			GuildId:          info.GuildId,
			SignTime:         uint32(info.SignTime.Unix()),
			ContractDuration: info.ContractDuration,
			ExpireTime:       uint32(info.ContractExpireTime.Unix()),
			Permission:       info.Permissions,
			IdentityNum:      info.IdentityNum,
		})
	}

	log.DebugWithCtx(ctx, "GetContractWithIdentity in:%+v len:%+v", in, len(list))
	return out, nil
}

// 批量获取用户合约信息
func (s *AnchorContract) GetGuildContract(ctx context.Context, in *pb.GetGuildContractReq) (*pb.GetGuildContractResp, error) {
	out := &pb.GetGuildContractResp{}
	begin := in.GetPage() * in.GetPageSize()

	list, err := s.store.GetGuildContract(in.GetGuildId(), begin, in.GetPageSize())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildContract fail to GetGuildContract. in:%+v, err:%v", in, err)
		return out, err
	}

	out.ContractList = make([]*pb.ContractInfo, 0, len(list))
	for _, info := range list {
		out.ContractList = append(out.ContractList, &pb.ContractInfo{
			ActorUid:         info.Uid,
			GuildId:          info.GuildId,
			SignTime:         uint32(info.SignTime.Unix()),
			ContractDuration: info.ContractDuration,
			ExpireTime:       uint32(info.ContractExpireTime.Unix()),
			Permission:       info.Permissions,
			IdentityNum:      info.IdentityNum,
		})
	}

	log.DebugWithCtx(ctx, "GetGuildContract in:%+v len:%+v", in, len(list))
	return out, nil
}

// 条件查询公会签约成员列表
func (s *AnchorContract) GetGuildContractByCond(ctx context.Context, in *pb.GetGuildContractByCondReq) (*pb.GetGuildContractByCondResp, error) {
	out := &pb.GetGuildContractByCondResp{}
	begin := in.GetPage() * in.GetPageSize()

	list, err := s.store.GetGuildContractByCondition(in.GetGuildId(), begin, in.GetPageSize(), in.GetWorkerType(), in.GetIsSelectWorker())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildContractByCond fail to GetGuildContract. in:%+v, err:%v", in, err)
		return out, err
	}

	out.ContractList = make([]*pb.ContractInfo, 0, len(list))
	for _, info := range list {
		out.ContractList = append(out.ContractList, &pb.ContractInfo{
			ActorUid:         info.Uid,
			GuildId:          info.GuildId,
			SignTime:         uint32(info.SignTime.Unix()),
			ContractDuration: info.ContractDuration,
			ExpireTime:       uint32(info.ContractExpireTime.Unix()),
			Permission:       info.Permissions,
			IdentityNum:      info.IdentityNum,
			WorkerType:       info.WorkerType,
		})
	}

	log.DebugWithCtx(ctx, "GetGuildContractByCond in:%+v len:%+v", in, len(list))
	return out, nil
}

func (s *AnchorContract) GetGuildContractByIdentity(ctx context.Context, in *pb.GetGuildContractByIdentityReq) (*pb.GetGuildContractByIdentityResp, error) {
	out := &pb.GetGuildContractByIdentityResp{}

	log.DebugWithCtx(ctx, "GetGuildContractByIdentity begin in:%v", in)
	nowTm := time.Now()
	list, err := s.store.GetGuildContractByIdentity(in.GetGuildId(), in.GetAgentUid(), in.GetAnchorIdentity(), in.GetAnchorFlag(), in.GetOffset(), in.GetLimit(), nowTm)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildContractByIdentity failed in:%+v, err:%v", in, err)
		return out, err
	}

	totalCnt, err := s.store.GetGuildContractTotalCntByIdentity(in.GetGuildId(), in.GetAgentUid(), in.GetAnchorIdentity(), in.GetAnchorFlag(), nowTm)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildContractByIdentity GetGuildContractTotalCntByIdentity failed in:%+v, err:%v", in, err)
		return out, err
	}

	out.ContractList = make([]*pb.ContractInfo, 0, len(list))
	for _, info := range list {
		out.ContractList = append(out.ContractList, &pb.ContractInfo{
			ActorUid:         info.Uid,
			GuildId:          info.GuildId,
			SignTime:         uint32(info.SignTime.Unix()),
			ContractDuration: info.ContractDuration,
			ExpireTime:       uint32(info.ContractExpireTime.Unix()),
			Permission:       info.Permissions,
			IdentityNum:      info.IdentityNum,
		})
	}

	out.TotalCnt = totalCnt

	log.DebugWithCtx(ctx, "GetGuildContractByIdentity end in:%v out:%v", in, out)
	return out, nil
}

// 获取公会指定身份主播列表
func (s *AnchorContract) GetGuildAnchorIdentity(ctx context.Context, in *pb.GetGuildAnchorIdentityReq) (*pb.GetGuildAnchorIdentityResp, error) {
	out := &pb.GetGuildAnchorIdentityResp{}
	begin := in.GetPage() * in.GetPageSize()

	total, err := s.store.GetGuildIdentityListCnt(in.GetGuildId(), in.GetIdentityType())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildAnchorIdentity fail to GetGuildIdentityListCnt. in:%+v, err:%v", in, err)
		return out, err
	}

	if total == 0 {
		return out, nil
	}
	out.Total = total

	list, err := s.store.GetGuildIdentityList(in.GetGuildId(), in.GetIdentityType(), begin, in.GetPageSize())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildAnchorIdentity fail to GetGuildIdentityList. in:%+v, err:%v", in, err)
		return out, err
	}

	out.InfoList = make([]*pb.AnchorIdentityInfo, 0, len(list))
	for _, info := range list {
		out.InfoList = append(out.InfoList, &pb.AnchorIdentityInfo{
			ActorUid:     info.Uid,
			GuildId:      info.GuildId,
			IdentityType: info.IdentityType,
			ObtainTime:   uint32(info.ObtainTime.Unix()),
		})
	}

	log.DebugWithCtx(ctx, "GetGuildAnchorIdentity in:%+v len:%+v", in, len(list))
	return out, nil
}

// 获取公会指定身份主播列表
func (s *AnchorContract) BatchGetAnchorIdentity(ctx context.Context, in *pb.BatchGetAnchorIdentityReq) (*pb.BatchGetAnchorIdentityResp, error) {
	out := &pb.BatchGetAnchorIdentityResp{}

	list, err := s.store.GetIdentityWithUidList(in.GetUidList(), in.GetIdentityType())
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetAnchorIdentity fail to GetIdentityWithUidList. in:%+v, err:%v", in, err)
		return out, err
	}

	out.InfoList = make([]*pb.AnchorIdentityInfo, 0, len(list))
	for _, info := range list {
		out.InfoList = append(out.InfoList, &pb.AnchorIdentityInfo{
			ActorUid:     info.Uid,
			GuildId:      info.GuildId,
			IdentityType: info.IdentityType,
			ObtainTime:   uint32(info.ObtainTime.Unix()),
		})
	}

	log.DebugWithCtx(ctx, "GetGuildAnchorIdentity in:%+v len:%+v", in, len(list))

	return out, nil
}

// 获取公会合约相关汇总信息
func (s *AnchorContract) GetGuildContractSum(ctx context.Context, in *pb.GetGuildContractSumReq) (*pb.GetGuildContractSumResp, error) {
	return s.mgr.GetGuildContractSum(ctx, in)
}

// 申请签约
func (s *AnchorContract) ApplySignContract(ctx context.Context, in *pb.ApplySignContractReq) (*pb.ApplySignContractResp, error) {
	out := &pb.ApplySignContractResp{}

	err := s.mgr.ApplySignContract(ctx, in)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplySignContract fail to ApplySignContract. in:%+v, err:%v", in, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "ApplySignContract in:%+v AnchorIdentity:%d out:%+v", in, in.AnchorIdentity, out)
	return out, nil
}

// 检查是否签约白名单
func (s *AnchorContract) CheckIsSignWhiteUid(ctx context.Context, in *pb.CheckIsSignWhiteUidReq) (*pb.CheckIsSignWhiteUidResp, error) {
	out := &pb.CheckIsSignWhiteUidResp{}

	inWhiteList, err := s.mgr.CheckIsSignWhiteUid(ctx, in)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckIsSignWhiteUid fail to CheckIsSignWhiteUid. in:%+v, err:%v", in, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "CheckIsSignWhiteUid in:%+v CheckIsSignWhiteUid:%d out:%+v", in, in.GetUid(), out)
	out.IsWhite = inWhiteList
	return out, nil
}

// 检查是否签约白名单
func (s *AnchorContract) AddSignWhiteUid(ctx context.Context, in *pb.AddSignWhiteUidReq) (*pb.AddSignWhiteUidResp, error) {
	out := &pb.AddSignWhiteUidResp{}

	err := s.mgr.AddSignWhiteUid(ctx, in)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddSignWhiteUid fail to AddSignWhiteUid. in:%+v, err:%v", in, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "AddSignWhiteUid in:%+v AddSignWhiteUid:%d out:%+v", in, in.GetUid(), out)
	return out, nil
}

// 检查是否签约白名单
func (s *AnchorContract) DelSignWhiteUid(ctx context.Context, in *pb.DelSignWhiteUidReq) (*pb.DelSignWhiteUidResp, error) {
	out := &pb.DelSignWhiteUidResp{}

	err := s.mgr.DelSignWhiteUid(ctx, in)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelSignWhiteUid fail to DelSignWhiteUid. in:%+v, err:%v", in, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "DelSignWhiteUid in:%+v DelSignWhiteUid:%d out:%+v", in, in.GetUid(), out)
	return out, nil
}

// 撤销签约申请
func (s *AnchorContract) WithdrawApplySign(ctx context.Context, in *pb.WithdrawApplySignReq) (*pb.WithdrawApplySignResp, error) {
	out := &pb.WithdrawApplySignResp{}
	ok, err := s.store.UpdateContractApplyStatusWithId(nil, in.GetApplyId(),
		uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_PRESIDENT_HANDLING), uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_WITHDRAW), "", "")
	if err != nil {
		log.ErrorWithCtx(ctx, "WithdrawApplySign fail to UpdateContractApplyStatusWithId. in:%+v, err:%v", in, err)
		return out, err
	}

	if !ok {
		log.ErrorWithCtx(ctx, "WithdrawApplySign fail apply status has change. in:%+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrContractWithdrawApplyStatusChange)
	}

	applyInfo, err := s.store.GetContractApplyWithId(nil, in.GetApplyId())
	if err != nil {
		log.ErrorWithCtx(ctx, "WithdrawApplySign fail to GetContractApplyWithId in:%+v, err:%v", in, err)
		return out, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
	}

	if applyInfo.IdentityType == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_E_SPORTS) {
		// 电竞身份申请撤回需要回调电竞审核相关服务
		auditToken, err := s.store.GetSignEsportAuditToken(applyInfo.Id)
		if err != nil {
			log.ErrorWithCtx(ctx, "WithdrawApplySign GetSignEsportAuditToken in:%v fail:%v, applyInfo=%+v", in, err, applyInfo)
			return out, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
		}

		if auditToken == "" {
			log.ErrorWithCtx(ctx, "WithdrawApplySign GetSignEsportAuditToken no find in:%v applyInfo=%+v", in, applyInfo)
		} else {
			req := &esportRolePb.ESportGuildAuditResultReq{
				AuditToken: auditToken,
				Uid:        applyInfo.Uid,
				GuildId:    applyInfo.GuildId,
				AuditType:  uint32(esportRolePb.ApplyESportAuditType_ESPORT_AUDIT_TYPE_GUILD_APPLY_CANCEL),
			}

			_, err = s.esportRoleCli.ESportGuildAuditResult(ctx, req)
			if err == nil {
				log.ErrorWithCtx(ctx, "WithdrawApplySign ESportGuildAuditResult in:%v fail:%v applyInfo:%+v", in, err, applyInfo)
			}
		}
	}

	log.InfoWithCtx(ctx, "WithdrawApplySign in:%+v out:%+v applyInfo:%v", in, out, applyInfo)
	return out, nil
}

// 会长审批签约申请
func (s *AnchorContract) PresidentHandleApplySign(ctx context.Context, in *pb.PresidentHandleApplySignReq) (*pb.PresidentHandleApplySignResp, error) {
	out := &pb.PresidentHandleApplySignResp{}

	err := s.mgr.PresidentHandleApplySign(ctx, in)
	if err != nil {
		log.ErrorWithCtx(ctx, "PresidentHandleApplySign fail to PresidentHandleApplySign. in:%+v, err:%v", in, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "PresidentHandleApplySign in:%+v out:%+v", in, out)
	return out, nil
}

// 会长处理所有签约申请
func (s *AnchorContract) PresidentHandleAllApplySign(ctx context.Context, in *pb.PresidentHandleAllApplySignReq) (*pb.PresidentHandleAllApplySignResp, error) {
	return s.mgr.PresidentHandleAllApplySign(ctx, in)
}

// 官方审批签约申请
func (s *AnchorContract) OfficialHandleApplySign(ctx context.Context, in *pb.OfficialHandleApplySignReq) (*pb.OfficialHandleApplySignResp, error) {
	out := &pb.OfficialHandleApplySignResp{}

	err := s.mgr.OfficialHandleApplySign(ctx, in)
	if err != nil {
		log.ErrorWithCtx(ctx, "OfficialHandleApplySign fail to OfficialHandleApplySign. in:%+v, err:%v", in, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "OfficialHandleApplySign in:%+v out:%+v", in, out)
	return out, nil
}

// 获取用户签约申请记录
func (s *AnchorContract) GetUserApplySignRecord(ctx context.Context, in *pb.GetUserApplySignRecordReq) (*pb.GetUserApplySignRecordResp, error) {
	return s.mgr.GetUserApplySignRecord(ctx, in)
}

// 批量获取用户签约申请
func (s *AnchorContract) BatchGetUserApplySignRecord(ctx context.Context, in *pb.BatchGetUserApplySignRecordReq) (*pb.BatchGetUserApplySignRecordResp, error) {
	return s.mgr.BatchGetUserApplySignRecord(ctx, in)
}

// 获取公会签约申请记录
func (s *AnchorContract) GetGuildApplySignRecord(ctx context.Context, in *pb.GetGuildApplySignRecordReq) (*pb.GetGuildApplySignRecordResp, error) {
	return s.mgr.GetGuildApplySignRecord(ctx, in)
}

// 获取所有签约申请记录
func (s *AnchorContract) GetAllApplySignRecord(ctx context.Context, in *pb.GetAllApplySignRecordReq) (*pb.GetAllApplySignRecordResp, error) {
	return s.mgr.GetAllApplySignRecord(ctx, in)
}

// 获取公会签约申请数量
func (s *AnchorContract) GetGuildApplySignRecordCnt(ctx context.Context, in *pb.GetGuildApplySignRecordCntReq) (*pb.GetGuildApplySignRecordCntResp, error) {
	return s.mgr.GetGuildApplySignRecordCnt(ctx, in)
}

// 获取公会解约申请列表
func (s *AnchorContract) GetCancelContractApplyList(ctx context.Context, in *pb.GetCancelContractApplyListReq) (*pb.GetCancelContractApplyListResp, error) {
	return s.mgr.GetCancelContractApplyList(ctx, in)
}

// 申请解约
func (s *AnchorContract) ApplyCancelContract(ctx context.Context, in *pb.ApplyCancelContractReq) (*pb.ApplyCancelContractResp, error) {
	return s.mgr.ApplyCancelContract(ctx, in)
}

// 处理解约申请
func (s *AnchorContract) HandlerCancelContractApply(ctx context.Context, in *pb.HandlerCancelContractApplyReq) (*pb.HandlerCancelContractApplyResp, error) {
	return s.mgr.HandlerCancelContractApply(ctx, in)
}

// 解约用户
func (s *AnchorContract) CancelContractByUid(ctx context.Context, in *pb.CancelContractByUidReq) (*pb.CancelContractByUidResp, error) {
	out := &pb.CancelContractByUidResp{}

	err := s.mgr.CancelContractByUid(ctx, in.GetOpUid(), in.GetTargetUid(), in.GetGuildId(), false)
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelContractByUid fail to CancelContractByUid. in:%+v, err:%v", in, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "CancelContractByUid in:%+v out:%+v", in, out)
	return out, nil
}

// 回收主播身份
func (s *AnchorContract) ReclaimAnchorIdentity(ctx context.Context, in *pb.ReclaimAnchorIdentityReq) (*pb.ReclaimAnchorIdentityResp, error) {
	out := &pb.ReclaimAnchorIdentityResp{}

	log.InfoWithCtx(ctx, "ReclaimAnchorIdentity in uid %d guild %d anchor_identity %d handler %s",
		in.Uid, in.GuildId, in.AnchorIdentity, in.Handler)

	return out, s.mgr.ReclaimAnchorIdentity(ctx, in.GetUid(), in.GetGuildId(), in.GetAnchorIdentity(), in.GetHandler())
}

// 回收公会下所有成员的该身份（将公会移出合作库时使用， 慎用！！）
func (s *AnchorContract) ReclaimGuildAllAnchorIdentity(ctx context.Context, in *pb.ReclaimGuildAllAnchorIdentityReq) (*pb.ReclaimGuildAllAnchorIdentityResp, error) {
	out := &pb.ReclaimGuildAllAnchorIdentityResp{}

	log.InfoWithCtx(ctx, "ReclaimGuildAllAnchorIdentity in guild %d anchor_identity %d handler %s",
		in.GuildId, in.AnchorIdentity, in.Handler)

	return out, s.mgr.ReclaimGuildAllAnchorIdentity(ctx, in.GetGuildId(), in.GetAnchorIdentity(), in.GetHandler())
}

// 获取用户身份操作记录
func (s *AnchorContract) GetUserAnchorIdentityLog(ctx context.Context, in *pb.GetUserAnchorIdentityLogReq) (*pb.GetUserAnchorIdentityLogResp, error) {
	out := &pb.GetUserAnchorIdentityLogResp{}

	list, err := s.store.GetAnchorIdentityChangeLog(in.GetUid(), in.GetAnchorIdentity(), in.GetChangeType())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserAnchorIdentityLog fail to GetAnchorIdentityChangeLog. in:%+v, err:%v", in, err)
		return out, err
	}

	out.List = make([]*pb.AnchorIdentityChangeLog, 0, len(list))
	for _, info := range list {
		out.List = append(out.List, &pb.AnchorIdentityChangeLog{
			Id:             info.Id,
			Uid:            info.Uid,
			GuildId:        info.GuildId,
			SignTime:       uint32(info.SignTime.Unix()),
			ExpireTime:     uint32(info.ContractExpireTime.Unix()),
			ObtainTime:     uint32(info.ObtainTime.Unix()),
			ChangeTime:     uint32(info.ChangeTime.Unix()),
			AnchorIdentity: info.IdentityType,
			ChangeType:     info.ChangeType,
			Handler:        info.Handler,
		})
	}

	return out, nil
}

// 获取用户身份操作记录
func (s *AnchorContract) BatchGetUserAnchorIdentityLog(ctx context.Context, in *pb.BatchGetUserAnchorIdentityLogReq) (*pb.BatchGetUserAnchorIdentityLogResp, error) {
	out := &pb.BatchGetUserAnchorIdentityLogResp{}

	list, err := s.store.BatchGetAnchorIdentityChangeLog(in.GetUidList(), in.GetAnchorIdentity(), in.GetChangeType())
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetUserAnchorIdentityLog fail to BatchGetAnchorIdentityChangeLog. in:%+v, err:%v", in, err)
		return out, err
	}

	out.List = make([]*pb.AnchorIdentityChangeLog, 0, len(list))
	for _, info := range list {
		out.List = append(out.List, &pb.AnchorIdentityChangeLog{
			Id:             info.Id,
			Uid:            info.Uid,
			GuildId:        info.GuildId,
			SignTime:       uint32(info.SignTime.Unix()),
			ExpireTime:     uint32(info.ContractExpireTime.Unix()),
			ObtainTime:     uint32(info.ObtainTime.Unix()),
			ChangeTime:     uint32(info.ChangeTime.Unix()),
			AnchorIdentity: info.IdentityType,
			ChangeType:     info.ChangeType,
			Handler:        info.Handler,
		})
	}

	return out, nil
}

// 获取公会下用户身份操作记录
func (s *AnchorContract) GetGuildAnchorIdentityLog(ctx context.Context, in *pb.GetGuildAnchorIdentityLogReq) (*pb.GetGuildAnchorIdentityLogResp, error) {
	out := &pb.GetGuildAnchorIdentityLogResp{}

	total, err := s.store.GetGuildAnchorIdentityChangeLogCnt(in.GetGuildId(), in.GetAnchorIdentity(), in.GetChangeType())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildAnchorIdentityLog fail to GetGuildAnchorIdentityChangeLogCnt. in:%+v, err:%v", in, err)
		return out, err
	}

	if total == 0 {
		return out, nil
	}
	out.Total = total

	begin := in.GetPage() * in.GetPageSize()
	list, err := s.store.GetGuildAnchorIdentityChangeLog(in.GetGuildId(), begin, in.GetPageSize(), in.GetAnchorIdentity(), in.GetChangeType())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildAnchorIdentityLog fail to GetGuildAnchorIdentityChangeLog. in:%+v, err:%v", in, err)
		return out, err
	}

	out.List = make([]*pb.AnchorIdentityChangeLog, 0, len(list))
	for _, info := range list {
		out.List = append(out.List, &pb.AnchorIdentityChangeLog{
			Id:             info.Id,
			Uid:            info.Uid,
			GuildId:        info.GuildId,
			SignTime:       uint32(info.SignTime.Unix()),
			ExpireTime:     uint32(info.ContractExpireTime.Unix()),
			ObtainTime:     uint32(info.ObtainTime.Unix()),
			ChangeTime:     uint32(info.ChangeTime.Unix()),
			AnchorIdentity: info.IdentityType,
			ChangeType:     info.ChangeType,
			Handler:        info.Handler,
		})
	}

	return out, nil
}

// 获取公会下主播多人互动积分
func (s *AnchorContract) BatchGetGuildUserScore(ctx context.Context, in *pb.BatchGetGuildUserScoreReq) (*pb.BatchGetGuildUserScoreResp, error) {
	out := &pb.BatchGetGuildUserScoreResp{}
	var err error
	monthTime := time.Unix(int64(in.GetMonthTime()), 0)

	out.MapUserScore, err = s.store.BatchGetGuildUserMonthScore(in.GetGuildId(), in.GetUidList(), monthTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetGuildUserScore fail to BatchGetGuildUserMonthScore. in:%+v, err:%v", in, err)
		return out, err
	}

	return out, nil
}

// 批量获取申请黑名单
func (s *AnchorContract) BatchGetApplyBlacklist(ctx context.Context, in *pb.BatchGetApplyBlacklistReq) (*pb.BatchGetApplyBlacklistResp, error) {
	out := &pb.BatchGetApplyBlacklistResp{}

	list, err := s.store.BatchGetUserApplyBlacklist(in.GetUidList(), in.GetIdentityType())
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetApplyBlacklist fail to BatchGetUserApplyBlacklist. in:%+v, err:%v", in, err)
		return out, err
	}

	out.Blacklist = make([]*pb.ApplyBlackInfo, 0, len(list))
	for _, info := range list {
		out.Blacklist = append(out.Blacklist, &pb.ApplyBlackInfo{
			Uid:          info.Uid,
			IdentityType: info.IdentityType,
			BeginTime:    uint32(info.BeginTime.Unix()),
			EndTime:      uint32(info.EndTime.Unix()),
			Handler:      info.Handler,
			Remarks:      info.Remarks,
			ChangeTime:   uint32(info.UpdateTime.Unix()),
		})
	}

	return out, nil
}

// 批量获取申请黑名单
func (s *AnchorContract) GetAllApplyBlacklist(ctx context.Context, in *pb.GetAllApplyBlacklistReq) (*pb.GetAllApplyBlacklistResp, error) {
	out := &pb.GetAllApplyBlacklistResp{}

	total, err := s.store.GetAllUserApplyBlacklistCnt(in.GetIdentityType())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAllApplyBlacklist fail to GetAllUserApplyBlacklistCnt. in:%+v, err:%v", in, err)
		return out, err
	}

	if total == 0 {
		return out, nil
	}
	out.Total = total

	begin := in.GetPageSize() * in.GetPage()
	list, err := s.store.GetAllUserApplyBlacklist(in.GetIdentityType(), begin, in.GetPageSize())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAllApplyBlacklist fail to GetAllUserApplyBlacklist. in:%+v, err:%v", in, err)
		return out, err
	}

	out.Blacklist = make([]*pb.ApplyBlackInfo, 0, len(list))
	for _, info := range list {
		out.Blacklist = append(out.Blacklist, &pb.ApplyBlackInfo{
			Uid:          info.Uid,
			IdentityType: info.IdentityType,
			BeginTime:    uint32(info.BeginTime.Unix()),
			EndTime:      uint32(info.EndTime.Unix()),
			Handler:      info.Handler,
			Remarks:      info.Remarks,
			ChangeTime:   uint32(info.UpdateTime.Unix()),
		})
	}

	return out, nil
}

// 操作申请黑名单
func (s *AnchorContract) HandleApplyBlackInfo(ctx context.Context, in *pb.HandleApplyBlackInfoReq) (*pb.HandleApplyBlackInfoResp, error) {
	out := &pb.HandleApplyBlackInfoResp{}

	var endTime time.Time
	if in.GetHandleOpr() == uint32(pb.HandleApplyBlackInfoReq_ENUM_FOREVER_OPR) {
		endTime = time.Date(2031, 1, 1, 0, 0, 0, 0, time.Local)
	} else if in.GetHandleOpr() == uint32(pb.HandleApplyBlackInfoReq_ENUM_REMOVE_OPR) {
		endTime = time.Now()
	}

	err := s.store.UpdateUserApplyBlacklist(nil, in.GetUid(), in.GetIdentityType(), endTime, in.GetHandler(), in.GetRemarks())
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleApplyBlackInfo fail to UpdateUserApplyBlacklist. in:%+v, err:%v", in, err)
		return out, err
	}

	return out, nil
}

func (s *AnchorContract) UpdateSignedAnchorAgentId(ctx context.Context, in *pb.UpdateSignedAnchorAgentIdReq) (*pb.UpdateSignedAnchorAgentIdResp, error) {
	out := &pb.UpdateSignedAnchorAgentIdResp{}

	log.InfoWithCtx(ctx, "UpdateSignedAnchorAgentId begin in:%v", in)
	err := s.store.UpdateSignedAnchorAgentId(in.GetGuildId(), in.GetAgentUid(), in.GetIdentityType(), in.GetAnchorList())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateSignedAnchorAgentId failed in:%v err:%v", in, err)
		return out, err
	}

	err = s.cache.BatchDelUserContractInfoV2(in.GetAnchorList())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateSignedAnchorAgentId BatchDelUserContractInfoV2 fail %v", err)
	}
	log.InfoWithCtx(ctx, "UpdateSignedAnchorAgentId end in:%v out:%v", in, out)
	return out, nil
}

func (s *AnchorContract) GetMultiPlayerCenterEntry(ctx context.Context, in *pb.GetMultiPlayerCenterEntryReq) (out *pb.GetMultiPlayerCenterEntryResp, err error) {
	out = &pb.GetMultiPlayerCenterEntryResp{}
	uid := in.Uid
	si, _ := protogrpc.ServiceInfoFromContext(ctx)
	marketId := si.MarketID
	osType := uint32(protocol.NewTerminalType(si.TerminalType).OS()) // 终端类型

	defer func() {
		log.InfoWithCtx(ctx, "GetMultiPlayerCenterEntry uid %d, url %q, serviceInfo  %s, marketId %d, osType %d",
			uid, out.JumpUrl, si.String(), marketId, osType)
	}()

	guildId, _, multiPlayer, err := s.getUserAnchorIdentity(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiPlayerCenterEntry failed to getUserAnchorIdentity %v, uid %d", err, uid)
		return out, err
	}
	if !multiPlayer {
		log.DebugWithCtx(ctx, "GetMultiPlayerCenterEntry not multiPlayer uid %d", uid)
		return out, nil
	}

	// 合作库
	bIn, err := s.mgr.CheckGuildIfInCoop(ctx, guildId, manager.GOLDDIAMONN_MULTIPLAYER_LIB)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiPlayerCenterEntry failed to CheckGuildIfInCoop %v, uid %d", err, uid)
		return out, err
	}
	if !bIn {
		log.DebugWithCtx(ctx, "GetMultiPlayerCenterEntry guild not InCoop uid %d", uid)
		return out, nil
	}

	out.JumpUrl = s.mgr.GetDyConfig().GetMultiPlayerCenterUrl(marketId, uint8(osType))
	return out, nil
}

func (s *AnchorContract) getUserAnchorIdentity(ctx context.Context, uid uint32) (guildId uint32, radioLive bool, multiPlayer bool, err error) {
	contract, err := s.mgr.GetUserContractCacheInfo(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiPlayerCenterEntry failed to GetUserContractCacheInfo %v, uid %d", err, uid)
		return
	}
	if contract.GetContract().GetGuildId() == 0 || len(contract.GetAnchorIdentityList()) == 0 {
		return
	}

	guildId = contract.GetContract().GetGuildId()
	for _, identity := range contract.GetAnchorIdentityList() {
		if identity == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER) {
			multiPlayer = true
		}
		if identity == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE) {
			radioLive = true
		}
	}
	return
}

// 申请签约公会电竞陪玩
func (s *AnchorContract) ApplySignEsport(ctx context.Context, in *pb.ApplySignEsportReq) (*pb.ApplySignEsportResp, error) {
	return s.mgr.ApplySignEsport(ctx, in)
}

// 官方处理电竞陪玩签约申请
func (s *AnchorContract) OfficialHandleApplySignEsport(ctx context.Context, in *pb.OfficialHandleApplySignEsportReq) (*pb.OfficialHandleApplySignEsportResp, error) {
	return s.mgr.OfficialHandleApplySignEsport(ctx, in)
}

func (s *AnchorContract) GetAnchorAgentUid(ctx context.Context, in *pb.GetAnchorAgentUidReq) (*pb.GetAnchorAgentUidResp, error) {
	return s.mgr.GetAnchorAgentUid(ctx, in)
}

// 批量查询签约信息
func (s *AnchorContract) BatchGetContractInfo(ctx context.Context, in *pb.BatchGetContractInfoReq) (*pb.BatchGetContractInfoResp, error) {
	return s.mgr.BatchGetContractInfo(ctx, in)
}

func (s *AnchorContract) TestSignContract(ctx context.Context, in *pb.TestSignContractReq) (*pb.TestSignContractResp, error) {
	return s.mgr.TestSignContract(ctx, in)
}

func (s *AnchorContract) GetGuildEsportScore(ctx context.Context, in *pb.GetGuildEsportScoreReq) (*pb.GetGuildEsportScoreResp, error) {
	return s.mgr.GetGuildEsportScore(ctx, in)
}

func (s *AnchorContract) CheckCanApplyCancelContract(ctx context.Context, in *pb.CheckCanApplyCancelContractReq) (*pb.CheckCanApplyCancelContractResp, error) {
	return s.mgr.CheckCanApplyCancelContract(ctx, in)
}

func (s *AnchorContract) CheckCanApplyCancelContractV2(ctx context.Context, in *pb.CheckCanApplyCancelContractV2Req) (*pb.CheckCanApplyCancelContractV2Resp, error) {
	return s.mgr.CheckCanApplyCancelContractV2(ctx, in)
}

func (s *AnchorContract) CensorVideo(ctx context.Context, in *pb.CensorVideoReq) (*pb.CensorVideoResp, error) {
	return s.mgr.CensorVideo(ctx, in)
}

// 查询签约用户身份变更记录
func (s *AnchorContract) GetIdentityChangeHistory(ctx context.Context, in *pb.GetIdentityChangeHistoryReq) (*pb.GetIdentityChangeHistoryResp, error) {
	return s.mgr.GetIdentityChangeHistory(ctx, in)
}

func (s *AnchorContract) ActorHandleExtensionContract(ctx context.Context, in *pb.ActorHandleExtensionContractReq) (*pb.ActorHandleExtensionContractResp, error) {
	return s.mgr.ActorHandleExtensionContract(ctx, in)
}

func (s *AnchorContract) GetSignEsportAuditToken(ctx context.Context, in *pb.GetSignEsportAuditTokenReq) (*pb.GetSignEsportAuditTokenResp, error) {
	return s.mgr.GetSignEsportAuditToken(ctx, in)
}

func (s *AnchorContract) CheckCanApplySign(ctx context.Context, in *pb.CheckCanApplySignReq) (*pb.CheckCanApplySignResp, error) {
	return s.mgr.CheckCanApplySign(ctx, in)
}

func (s *AnchorContract) ApplyCancelContractV2(ctx context.Context, in *pb.ApplyCancelContractV2Req) (*pb.ApplyCancelContractResp, error) {
	return s.mgr.ApplyCancelContractV2(ctx, in)
}

func (s *AnchorContract) GetRecommendTopGuildList(ctx context.Context, in *pb.GetRecommendTopGuildListReq) (*pb.GetRecommendTopGuildListResp, error) {
	return s.mgr.GetRecommendTopGuildList(ctx, in)

}

func (s *AnchorContract) GetSignRightList(ctx context.Context, req *pb.GetSignRightListReq) (*pb.GetSignRightListResp, error) {
	return s.mgr.GetSignRightList(ctx, req)
}

func (s *AnchorContract) AddSignRight(ctx context.Context, req *pb.AddSignRightReq) (*pb.AddSignRightResp, error) {
	return s.mgr.AddSignRight(ctx, req)
}

func (s *AnchorContract) DelSignRight(ctx context.Context, req *pb.DelSignRightReq) (*pb.DelSignRightResp, error) {
	return s.mgr.DelSignRight(ctx, req)
}

func (s *AnchorContract) UpdateSignRight(ctx context.Context, req *pb.UpdateSignRightReq) (*pb.UpdateSignRightResp, error) {
	return s.mgr.UpdateSignRight(ctx, req)
}

func (s *AnchorContract) ApplySignDoyen(ctx context.Context, in *pb.ApplySignDoyenReq) (*pb.ApplySignDoyenResp, error) {
	return s.mgr.ApplySignDoyen(ctx, in, manager.DOYEN_SIGN_FROM_NORNAL)
}
func (s *AnchorContract) DelSignDoyen(ctx context.Context, in *pb.DelSignDoyenReq) (*pb.DelSignDoyenResp, error) {
	rsp := &pb.DelSignDoyenResp{}
	err := s.store.DeleteDoyenSignInfo(ctx, in.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "DelSignDoyen fail to DeleteDoyenSignInfo. in:%+v, err:%v", in, err)
		return rsp, err
	}
	err = s.cache.DelUserContractInfoV2(in.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "DelSignDoyen fail to DelUserContractInfoV2. in:%+v, err:%v", in, err)
		return rsp, err
	}
	log.InfoWithCtx(ctx, "DelSignDoyen ok in:%+v out:%+v", in, rsp)
	return rsp, nil
}

func (s *AnchorContract) ShutDown() {
	s.mgr.ShutDown()
	s.chLiveStatusKafkaSub.Close()
	s.presentKafkaSub.Close()
	s.tbeanEventSub.Close()
	s.esportTradeSub.Close()
	s.esportRoleSub.Close()
	s.guildCoopKafkaSub.Close()
	s.timerMgr.ShutDown()
	log.Infoln("ShutDown")
}
