package contract

import (
	"context"
	"encoding/json"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/pkg/web"
	"golang.52tt.com/protocol/common/status"
	anchorContractPb "golang.52tt.com/protocol/services/anchorcontract-go"
	apiPb "golang.52tt.com/protocol/services/contract-http-logic"
	guildPb "golang.52tt.com/protocol/services/guildsvr"
	"golang.52tt.com/services/anchor-contract/contract-http-logic/client"
	"net/http"
	"time"
)

// 解约相关
//https://q9jvw0u5f5.feishu.cn/wiki/LcxRwCqIyirw2AkyXIocEDARnDg

const (
	SceneUpgrade = 2 // 晋级
	SceneCancel  = 1
)

func GetCancelContractTypes(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()
	uid := authInfo.UserID
	var req apiPb.GetCancelContractTypesReq
	err := json.Unmarshal(authInfo.Body, &req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCancelContractTypes Failed to parse request uid:%d, body [%s], err %+v", uid, string(authInfo.Body), err)
		_ = web.ServeAPICodeJson(w, -2, "签约参数错误", nil)
		return
	}
	log.InfoWithCtx(ctx, "GetCancelContractTypes uid:%d, req:%+v", uid, req)
	contractInfo, sErr := client.AnchorContractCli.GetUserContractCacheInfo(ctx, uid, uid)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "GetCancelContractTypes Failed to GetUserContractCacheInfo uid:%d, err %+v", uid, sErr)
		_ = web.ServeAPICodeJson(w, int32(sErr.Code()), sErr.Message(), nil)
		return
	}
	workerType := req.WorkerType
	for _, item := range contractInfo.GetInfoList() {
		if item.GetIdentityType() == 0 {
			workerType = contractInfo.Contract.WorkerType
			if req.Scene == SceneUpgrade {
				workerType = uint32(anchorContractPb.ContractWorkerType_ContractWorkerType_Manager)
			}
		}
	}

	typeRsp, sErr := client.AnchorContractCli.GetCancelContractTypeList(ctx, &anchorContractPb.GetCancelContractTypeListReq{
		GuildId: req.GuildId,
	})
	if sErr != nil {
		log.ErrorWithCtx(ctx, "GetCancelContractTypes Failed to GetCancelContractType uid:%d, guild:%d, err %+v", uid, req.GuildId, err)
		_ = web.ServeAPICodeJson(w, -2, "获取解约类型失败", nil)
		return
	}

	var checkRsp *anchorContractPb.CheckCanApplyCancelContractV2Resp
	if req.Scene == SceneCancel {
		checkRsp, sErr = client.AnchorContractCli.CheckCanApplyCancelContractV2(ctx, &anchorContractPb.CheckCanApplyCancelContractV2Req{
			Uid:       uid,
			GuildId:   req.GuildId,
			OnlyMulti: true,
		})
		if sErr != nil {
			log.ErrorWithCtx(ctx, "CheckCanCancelContract Failed to CheckCanApplyCancelContractV2 uid:%d, err %+v", uid, sErr)
			_ = web.ServeAPICodeJson(w, int32(sErr.Code()), sErr.Message(), nil)
			return
		}
		log.InfoWithCtx(ctx, "GetCancelContractTypes checkRsp uid:%d, guildId:%d, checkRsp:%+v", uid, req.GuildId, checkRsp)
	}
	resp := &apiPb.GetCancelContractTypesResp{}
	if req.Scene == SceneCancel || req.Scene == SceneUpgrade {
		payAmountRsp, err := client.AnchorContractCli.GetCancelPayAmount(ctx, &anchorContractPb.GetCancelPayAmountReq{
			Uid:       uid,
			GuildId:   req.GuildId,
			IsUpgrade: req.Scene == SceneUpgrade,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "GetCancelContractTypes Failed to GetCancelPayAmount uid:%d, err %+v", uid, err)
			_ = web.ServeAPICodeJson(w, -2, "获取解约金额失败", nil)
			return
		}
		resp.PayCost = payAmountRsp.Amount
	}

	for _, v := range typeRsp.TypeList {
		if v.PracType != workerType {
			continue
		}
		for _, vv := range v.InfoList {
			if !vv.IsSelect {
				continue
			}
			item := &apiPb.CanCancelContract{
				CancelType: vv.CancelType,
				Name:       vv.Tilte,
				Desc:       vv.Desc,
				Enabled:    vv.IsSelect,
			}
			if checkRsp != nil {
				if _, ok := checkRsp.CantReasonMap[vv.CancelType]; ok {
					item.Enabled = false
				}

				if msg, ok := checkRsp.ShowButDisableMap[vv.CancelType]; ok {
					item.ShowButDisable = true
					item.DisableMsg = msg
				}
			}
			resp.CancelTypes = append(resp.CancelTypes, item)
		}
	}
	log.InfoWithCtx(ctx, "GetCancelContractTypes uid:%d, req:%v, resp:%+v", uid, req.String(), resp)
	_ = web.ServeAPIJson(w, resp)
}

func toCancelReason(ctx context.Context, reason uint32) string {
	switch reason {
	case uint32(anchorContractPb.CANCEL_REASON_TYPE_CANCEL_REASON_TYPE_DAY7_NOREASON):
		return "7天无理由"
	case uint32(anchorContractPb.CANCEL_REASON_TYPE_CANCEL_REASON_TYPE_GUILD_INCOME):
		return "公会收益不达标"
	case uint32(anchorContractPb.CANCEL_REASON_TYPE_CANCEL_REASON_TYPE_PERSON_INCOME):
		return "个人收益不达标"
	case uint32(anchorContractPb.CANCEL_REASON_TYPE_CANCEL_REASON_TYPE_LIVE_INCOME_ACTIVE):
		return "直播收益&活跃不达标"
	case uint32(anchorContractPb.CANCEL_REASON_TYPE_CANCEL_REASON_TYPE_NEGOTIATE):
		return "协商解约"
	case uint32(anchorContractPb.CANCEL_REASON_TYPE_CANCEL_REASON_TYPE_PAY):
		return "付费解约"
	default:
		log.ErrorWithCtx(ctx, "toCancelReason unknown reason:%d", reason)
		return ""
	}
}

func showOperateButton(cancelType, status uint32) (bool, bool) {
	showAcceptBtn := true
	showRejectBtn := false
	switch cancelType {
	case uint32(anchorContractPb.CancelContractType_CancelContractType_Negotiate):
		if status == uint32(anchorContractPb.CancelContractStatus_CancelContractStatus_Apply) {
			showRejectBtn = true
		}
	case uint32(anchorContractPb.CancelContractType_CancelContractType_Pay):
		if status == uint32(anchorContractPb.CancelContractStatus_CancelContractStatus_PayOfficeAccepted) {
			showAcceptBtn = true
		} else {
			showAcceptBtn = false
		}
	}
	return showAcceptBtn, showRejectBtn
}

// GetApplyCancelContractList 获取解约申请列表
func GetApplyCancelContractList(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()
	uid := authInfo.UserID
	var req apiPb.GetApplyCancelContractListReq
	err := json.Unmarshal(authInfo.Body, &req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetApplyCancelContractList Failed to parse request uid:%d, body [%s], err %+v", uid, string(authInfo.Body), err)
		_ = web.ServeAPICodeJson(w, -2, "签约参数错误", nil)
		return
	}
	log.InfoWithCtx(ctx, "GetApplyCancelContractList req:%+v", req)
	guildResp, err := client.GuildCli.GetGuildMember(ctx, uid, req.GuildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetApplyCancelContractList Failed to GetGuildMember uid:%d, err %+v", uid, err)
		_ = web.ServeAPICodeJson(w, -2, "获取公会成员信息失败", nil)
		return
	}
	if guildResp.Role != uint32(guildPb.E_GUILD_ROLE_TYPE_E_GUILD_ROLE_OWNER) {
		log.ErrorWithCtx(ctx, "GetApplyCancelContractList Failed to GetGuildMember uid:%d, role:%d", uid, guildResp.Role)
		_ = web.ServeAPICodeJson(w, -2, "您不是公会长", nil)
		return
	}
	page := req.Page
	pageNum := req.PageNum

	listRsp, err := client.AnchorContractCli.GetCancelContractApplyList(ctx, &anchorContractPb.GetCancelContractApplyListReq{
		GuildId:  req.GuildId,
		Page:     page,
		PageSize: pageNum,
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "GetApplyCancelContractList Failed to GetCancelContractApplyList guild:%d, uid:%d, err %+v", req.GuildId, uid, err)
		_ = web.ServeAPICodeJson(w, -2, "获取解约申请列表失败", nil)
		return
	}

	uids := make([]uint32, 0)
	for _, v := range listRsp.ApplyList {
		uids = append(uids, v.Uid)
	}
	userMap, err := client.BatchGetUserMapInfo(ctx, uids)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetApplyCancelContractList Failed to BatchGetUserMapInfo guild:%d uid:%d, err %+v", req.GuildId, uid, err)
		_ = web.ServeAPICodeJson(w, -2, "获取用户信息失败", nil)
		return
	}
	resp := &apiPb.GetApplyCancelContractListResp{}
	for _, v := range listRsp.ApplyList {
		user, ok := userMap[v.Uid]
		if !ok {
			log.ErrorWithCtx(ctx, "GetApplyCancelContractList Failed to get user info guild:%d, uid:%d", req.GuildId, v.Uid)
			continue
		}
		item := &apiPb.ApplyCancelContractItem{
			Uid:                v.Uid,
			GuildId:            v.GuildId,
			CancelType:         v.CancelType,
			Account:            user.Username,
			Nickname:           user.Nickname,
			Alias:              user.Alias,
			ApplyTime:          v.ApplyTimestamp,
			EndTime:            v.ApplyTimestamp + 3600*48,
			AnchorIdentityList: v.AnchorIdentityList,
			Reason:             toCancelReason(ctx, v.Reason),
			CancelReason:       v.CancelReasonText,
			ProofList:          make([]*apiPb.ProofShowContent, 0),
		}
		for _, vv := range v.GetProofList() {
			item.ProofList = append(item.ProofList, &apiPb.ProofShowContent{
				Url:  vv.Url,
				Type: apiPb.ProofShowContent_ProofType(vv.Type),
			})
		}

		item.ShowAcceptBtn, item.ShowRejectBtn = showOperateButton(v.CancelType, v.Status)
		resp.List = append(resp.List, item)
	}
	if uint32(len(listRsp.ApplyList)) >= pageNum {
		resp.NextPage = page + 1
	}
	log.InfoWithCtx(ctx, "GetApplyCancelContractList req:%v, resp:%+v", req.String(), resp)
	_ = web.ServeAPIJson(w, resp)
}

// ApplyCancelContract 申请解约-按解约方式
func ApplyCancelContract(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Minute)
	defer cancel()
	uid := authInfo.UserID
	var req apiPb.ApplyCancelContractReq
	err := json.Unmarshal(authInfo.Body, &req)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyCancelContract Failed to parse request uid:%d, body [%s], err %+v", uid, string(authInfo.Body), err)
		_ = web.ServeAPICodeJson(w, -2, "签约参数错误", nil)
		return
	}
	log.InfoWithCtx(ctx, "ApplyCancelContract uid:%d, session:%s, req:%+v", uid, authInfo.Session, req)

	isNeedVerifyCode, sErr := CheckIsNeedVerifyCode(ctx, uid, authInfo.Session)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "ApplyCancelContract CheckIsNeedVerifyCode failed uid:%d, err:%v", uid, sErr)
		_ = web.ServeAPICodeJson(w, -2, sErr.Message(), nil)
		return
	}
	if isNeedVerifyCode {
		log.ErrorWithCtx(ctx, "ApplyCancelContract need verify code uid:%d", uid)
		_ = web.ServeAPICodeJson(w, status.ErrVerifycodeForSensitiveOp, "申请解约需要手机短信确认,请按提示操作", nil)
		return
	}

	proofList := make([]*anchorContractPb.ProofContent, 0)
	for _, item := range req.GetProofList() {
		proofList = append(proofList, &anchorContractPb.ProofContent{
			Key:       item.Key,
			CensorKey: item.CensorKey,
			Type:      anchorContractPb.ProofContent_ProofType(item.GetType()),
		})
	}

	sErr = client.AnchorContractCli.ApplyCancelContractNew(ctx, &anchorContractPb.ApplyCancelContractNewReq{
		Uid:              uid,
		GuildId:          req.GuildId,
		CancelType:       req.CancelType,
		ProofUrls:        req.ProofUrls,
		PayDesc:          req.PayDesc,
		CancelReasonText: req.GetCancelReason(),
		ProofList:        proofList,
	})

	if sErr != nil {
		log.ErrorWithCtx(ctx, "ApplyCancelContract failed uid:%d, err:%v", uid, sErr)
		_ = web.ServeAPICodeJson(w, int32(sErr.Code()), sErr.Message(), nil)
		return
	}
	_ = web.ServeAPICodeJson(w, 0, "申请解约成功", nil)
	log.InfoWithCtx(ctx, "ApplyCancelContract success uid:%d, guildId:%d", uid, req.GuildId)
}

func AcceptApplyCancel(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()
	uid := authInfo.UserID
	var req apiPb.AcceptApplyCancelContractReq
	err := json.Unmarshal(authInfo.Body, &req)
	if err != nil {
		log.ErrorWithCtx(ctx, "AcceptApplyCancel Failed to parse request uid:%d, body [%s], err %+v", uid, string(authInfo.Body), err)
		_ = web.ServeAPICodeJson(w, -2, "参数错误", nil)
		return
	}
	log.InfoWithCtx(ctx, "AcceptApplyCancel uid:%d, session:%s, req:%+v", uid, authInfo.Session, req)
	guildResp, sErr := client.GuildCli.GetGuildMember(ctx, uid, req.GuildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "AcceptApplyCancel Failed to GetGuildMember uid:%d, err %+v", uid, sErr)
		_ = web.ServeAPICodeJson(w, -2, "获取公会成员信息失败", nil)
		return
	}
	if guildResp.Role != uint32(guildPb.E_GUILD_ROLE_TYPE_E_GUILD_ROLE_OWNER) {
		log.ErrorWithCtx(ctx, "AcceptApplyCancel Failed to GetGuildMember uid:%d, role:%d", uid, guildResp.Role)
		_ = web.ServeAPICodeJson(w, -2, "您不是公会长", nil)
		return
	}
	_, sErr = client.AnchorContractCli.HandlerCancelContractApply(ctx, &anchorContractPb.HandlerCancelContractApplyReq{
		Uid:       uid,
		GuildId:   req.GuildId,
		TargetUid: req.TargetUid,
		HandleOpr: uint32(anchorContractPb.CANCEL_APPLY_OPR_CANCEL_APPLY_OPR_ACCEPT),
	})
	if sErr != nil {
		log.ErrorWithCtx(ctx, "AcceptApplyCancel Failed to HandlerCancelContractApply uid:%d, err %+v", uid, sErr)
		_ = web.ServeAPICodeJson(w, -2, sErr.Message(), nil)
		return
	}
	_ = web.ServeAPICodeJson(w, 0, "OK", nil)
	log.InfoWithCtx(ctx, "AcceptApplyCancel success uid:%d, guildId:%d, targetUid:%d", uid, req.GuildId, req.TargetUid)
}

func RejectApplyCancel(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*60)
	defer cancel()
	uid := authInfo.UserID
	var req apiPb.RejectApplyCancelContractReq
	err := json.Unmarshal(authInfo.Body, &req)
	if err != nil {
		log.ErrorWithCtx(ctx, "RejectApplyCancel Failed to parse request uid:%d, body [%s], err %+v", uid, string(authInfo.Body), err)
		_ = web.ServeAPICodeJson(w, -2, "参数错误", nil)
		return
	}
	log.InfoWithCtx(ctx, "RejectApplyCancel uid:%d, session:%s, req:%+v", uid, authInfo.Session, req)
	guildResp, sErr := client.GuildCli.GetGuildMember(ctx, uid, req.GuildId)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "RejectApplyCancel Failed to GetGuildMember uid:%d, err %+v", uid, sErr)
		_ = web.ServeAPICodeJson(w, -2, "获取公会成员信息失败", nil)
		return
	}
	if guildResp.Role != uint32(guildPb.E_GUILD_ROLE_TYPE_E_GUILD_ROLE_OWNER) {
		log.ErrorWithCtx(ctx, "RejectApplyCancel Failed to GetGuildMember uid:%d, role:%d", uid, guildResp.Role)
		_ = web.ServeAPICodeJson(w, -2, "您不是公会长", nil)
		return
	}

	proofList := make([]*anchorContractPb.ProofContent, 0)
	for _, item := range req.GetProofList() {
		proofList = append(proofList, &anchorContractPb.ProofContent{
			Key:       item.Key,
			CensorKey: item.CensorKey,
			Type:      anchorContractPb.ProofContent_ProofType(item.GetType()),
		})
	}

	_, sErr = client.AnchorContractCli.HandlerCancelContractApply(ctx, &anchorContractPb.HandlerCancelContractApplyReq{
		Uid:       uid,
		GuildId:   req.GuildId,
		TargetUid: req.TargetUid,
		HandleOpr: uint32(anchorContractPb.CANCEL_APPLY_OPR_CANCEL_APPLY_OPR_REJECT),
		RejectTxt: req.RejectReason,
		ProofUrls: req.ProofUrls,
		ProofList: proofList,
	})
	if sErr != nil {
		log.ErrorWithCtx(ctx, "RejectApplyCancel Failed to HandlerCancelContractApply uid:%d, err %+v", uid, sErr)
		_ = web.ServeAPICodeJson(w, -2, sErr.Message(), nil)
		return
	}
	_ = web.ServeAPICodeJson(w, 0, "OK", nil)
	log.InfoWithCtx(ctx, "RejectApplyCancel success uid:%d, guildId:%d, targetUid:%d", uid, req.GuildId, req.TargetUid)
}

func CheckCanCancelContract(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()
	uid := authInfo.UserID
	var req apiPb.CheckCanCancelContractReq
	err := json.Unmarshal(authInfo.Body, &req)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckCanCancelContract Failed to parse request uid:%d, body [%s], err %+v", uid, string(authInfo.Body), err)
		_ = web.ServeAPICodeJson(w, -2, "签约参数错误", nil)
		return
	}
	log.InfoWithCtx(ctx, "CheckCanCancelContract uid:%d, session:%s, req:%+v", uid, authInfo.Session, req)
	contractInfo, sErr := client.AnchorContractCli.GetUserContractCacheInfo(ctx, uid, uid)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "CheckCanCancelContract Failed to GetUserContractCacheInfo uid:%d, err %+v", uid, sErr)
		_ = web.ServeAPICodeJson(w, int32(sErr.Code()), sErr.Message(), nil)
		return
	}
	if contractInfo.Contract == nil || contractInfo.Contract.GuildId != req.GuildId {
		log.ErrorWithCtx(ctx, "CheckCanCancelContract Failed to GetUserContractCacheInfo uid:%d, guildId:%d", uid, req.GuildId)
		_ = web.ServeAPICodeJson(w, -2, "未签约该公会", nil)
		return
	}

	checkRsp, sErr := client.AnchorContractCli.CheckCanApplyCancelContractV2(ctx, &anchorContractPb.CheckCanApplyCancelContractV2Req{
		Uid:       uid,
		GuildId:   req.GuildId,
		OnlyMulti: false,
	})
	if sErr != nil {
		log.ErrorWithCtx(ctx, "CheckCanCancelContract Failed to CheckCanApplyCancelContractV2 uid:%d, err %+v", uid, sErr)
		_ = web.ServeAPICodeJson(w, int32(sErr.Code()), sErr.Message(), nil)
		return
	}

	typeRsp, sErr := client.AnchorContractCli.GetCancelContractTypeList(ctx, &anchorContractPb.GetCancelContractTypeListReq{
		GuildId: req.GuildId,
	})
	if sErr != nil {
		log.ErrorWithCtx(ctx, "GetCancelContractTypes Failed to GetCancelContractType uid:%d, guild:%d, err %+v", uid, req.GuildId, err)
		_ = web.ServeAPICodeJson(w, int32(sErr.Code()), sErr.Message(), nil)
		return
	}
	resp := &apiPb.CheckCanCancelContractResp{}

	if checkRsp.Status == uint32(anchorContractPb.CancelContractStatus_CancelContractStatus_Apply) && checkRsp.StatusEndTime != 0 {
		if checkRsp.GetCancelType() == uint32(anchorContractPb.CancelContractType_CancelContractType_Negotiate) {
			resp.CancelStage = apiPb.CheckCanCancelContractResp_CancelContractTypeWaitingForMaster
		}

		if checkRsp.GetCancelType() == uint32(anchorContractPb.CancelContractType_CancelContractType_Older) {
			resp.CancelStage = apiPb.CheckCanCancelContractResp_CancelContractTypeWaitingForMasterLive
		}

		if checkRsp.GetCancelType() == uint32(anchorContractPb.CancelContractType_CancelContractType_NoReason) {
			resp.CancelStage = apiPb.CheckCanCancelContractResp_CancelContractTypeWaitingForMasterNoReason
		}

		if checkRsp.GetCancelType() == uint32(anchorContractPb.CancelContractType_CancelContractType_Quiet) {
			resp.CancelStage = apiPb.CheckCanCancelContractResp_CancelContractTypeWaitingForMasterQuiet
		}
		resp.StageExpireTs = int64(checkRsp.StatusEndTime)
	}

	if checkRsp.Status == uint32(anchorContractPb.CancelContractStatus_CancelContractStatus_NegotiateReject) {
		resp.CancelStage = apiPb.CheckCanCancelContractResp_CancelContractTypeWaitingForOfficial
		resp.StageExpireTs = int64(checkRsp.StatusEndTime)
	}

	if checkRsp.Status == uint32(anchorContractPb.CancelContractStatus_CancelContractStatus_Paying) {
		resp.CancelStage = apiPb.CheckCanCancelContractResp_CancelContractTypeWaitingForOfficialPay
		resp.StageExpireTs = int64(checkRsp.StatusEndTime)
	}

	if checkRsp.Status == uint32(anchorContractPb.CancelContractStatus_CancelContractStatus_PayOfficeAccepted) {
		resp.CancelStage = apiPb.CheckCanCancelContractResp_CancelContractTypeWaitingForMasterPayAccept
		resp.StageExpireTs = int64(checkRsp.StatusEndTime)
	}

	if checkRsp.Status == uint32(anchorContractPb.CancelContractStatus_CancelContractStatus_NegotiateOfficeAccepted) {
		resp.CancelStage = apiPb.CheckCanCancelContractResp_CancelContractTypeWaitingForMasterNegotiateAccept
		resp.StageExpireTs = int64(checkRsp.StatusEndTime)
	}

	payRsp, sErr := client.AnchorContractCli.GetCancelPayAmount(ctx, &anchorContractPb.GetCancelPayAmountReq{
		Uid:     uid,
		GuildId: req.GuildId,
	})
	if sErr != nil {
		log.ErrorWithCtx(ctx, "CheckCanCancelContract Failed to GetCancelPayAmount uid:%d, err %+v", uid, sErr)
		_ = web.ServeAPICodeJson(w, int32(sErr.Code()), sErr.Message(), nil)
		return
	}

	if payRsp.LockAmount > 0 { //继续付费解约
		resp.LockPayCost = payRsp.LockAmount
		resp.LockExpireTs = payRsp.LockExpireTs
		resp.LockStartTs = payRsp.LockStartTs
		_ = web.ServeAPIJson(w, resp)
		log.InfoWithCtx(ctx, "CheckCanCancelContract success uid:%d, guildId:%d, resp:%v", uid, req.GuildId, resp)
		return
	}

	// 存在娱乐厅身份，检查解约方式是否满足条件
	hasCanType := false
	for _, v := range typeRsp.TypeList {
		if v.PracType != contractInfo.Contract.WorkerType {
			continue
		}
		for _, vv := range v.InfoList {
			if !vv.IsSelect {
				continue
			}
			if e, ok := checkRsp.CantReasonMap[vv.CancelType]; ok {
				log.InfoWithCtx(ctx, "CheckCanCancelContract no cancelType:%d, uid:%d, guildId:%d, , reason:%d", vv.CancelType, uid, req.GuildId, e)
				continue
			}
			hasCanType = true
			break
		}
		if hasCanType {
			break
		}
	}

	if !hasCanType {
		log.ErrorWithCtx(ctx, "CheckCanCancelContract no cancelType uid:%d, guildId:%d", uid, req.GuildId)
		_ = web.ServeAPICodeJson(w, -2, "当前不满足解约条件", nil)
		return
	}

	_ = web.ServeAPIJson(w, resp)
	log.InfoWithCtx(ctx, "CheckCanCancelContract success uid:%d, guildId:%d, resp:%v", uid, req.GuildId, resp)
}

func LockPayCancelAmount(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()
	uid := authInfo.UserID
	var req apiPb.LockPayCancelAmountReq
	err := json.Unmarshal(authInfo.Body, &req)
	if err != nil {
		log.ErrorWithCtx(ctx, "LockPayCancelAmount Failed to parse request uid:%d, body [%s], err %+v", uid, string(authInfo.Body), err)
		_ = web.ServeAPICodeJson(w, -2, "签约参数错误", nil)
		return
	}
	log.InfoWithCtx(ctx, "LockPayCancelAmount uid:%d, session:%s, req:%+v", uid, authInfo.Session, req)
	contractInfo, sErr := client.AnchorContractCli.GetUserContractCacheInfo(ctx, uid, uid)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "LockPayCancelAmount Failed to GetUserContractCacheInfo uid:%d, err %+v", uid, sErr)
		_ = web.ServeAPICodeJson(w, int32(sErr.Code()), sErr.Message(), nil)
		return
	}
	if contractInfo.Contract == nil || contractInfo.Contract.GuildId != req.GuildId {
		log.ErrorWithCtx(ctx, "LockPayCancelAmount Failed to GetUserContractCacheInfo uid:%d, guildId:%d", uid, req.GuildId)
		_ = web.ServeAPICodeJson(w, -2, "未签约该公会", nil)
		return
	}
	lockRsp, sErr := client.AnchorContractCli.LockCancelPayAmount(ctx, &anchorContractPb.LockCancelPayAmountReq{
		Uid:     uid,
		GuildId: req.GuildId,
	})
	if sErr != nil {
		log.ErrorWithCtx(ctx, "LockPayCancelAmount Failed to LockCancelPayAmount uid:%d, err %+v", uid, sErr)
		_ = web.ServeAPICodeJson(w, int32(sErr.Code()), sErr.Message(), nil)
		return
	}
	resp := &apiPb.LockPayCancelAmountResp{
		PayCost:  lockRsp.LockAmount,
		ExpireTs: lockRsp.LockExpireTs,
		StartTs:  lockRsp.LockStartTs,
	}
	_ = web.ServeAPIJson(w, resp)
	log.InfoWithCtx(ctx, "LockPayCancelAmount success uid:%d, guildId:%d, resp:%v", uid, req.GuildId, resp)
}

func CensorVideo(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*60)
	defer cancel()
	uid := authInfo.UserID
	var req apiPb.CensorVideoReq
	err := json.Unmarshal(authInfo.Body, &req)
	if err != nil {
		log.ErrorWithCtx(ctx, "CensorVideo Failed to parse request uid:%d, body [%s], err %+v", uid, string(authInfo.Body), err)
		_ = web.ServeAPICodeJson(w, -2, "参数错误", nil)
		return
	}
	log.InfoWithCtx(ctx, "CensorVideo uid:%d, session:%s, req:%+v", uid, authInfo.Session, req)
	log.InfoWithCtx(ctx, "ctx %+v", ctx)
	resp, sErr := client.AnchorContractCli.CensorVideo(ctx, &anchorContractPb.CensorVideoReq{
		Uid:      uid,
		GuildId:  req.GetGuildId(),
		VideoKey: req.GetVideoKey(),
	})
	if sErr != nil {
		log.ErrorWithCtx(ctx, "CensorVideo Failed to CensorVideo uid:%d, err %+v ctx %+v", uid, sErr, ctx)
		_ = web.ServeAPICodeJson(w, -2, sErr.Message(), nil)
		return
	}

	censorResult := make([]*apiPb.CensorResult, 0)
	for _, v := range resp.CensorResult {
		censorResult = append(censorResult, &apiPb.CensorResult{
			VideoKey:     v.GetVideoKey(),
			CensorKey:    v.GetCensorKey(),
			TranscodeUrl: v.GetTranscodeUrl(),
		})
	}
	tmpResp := &apiPb.CensorVideoResp{
		CensorResult: censorResult,
	}

	_ = web.ServeAPIJson(w, tmpResp)
	log.InfoWithCtx(ctx, "CensorVideo success uid:%d, tmpResp:%v", uid, tmpResp)
}
