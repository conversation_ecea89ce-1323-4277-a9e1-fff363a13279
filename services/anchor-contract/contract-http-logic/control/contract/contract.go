package contract

import (
	"context"
	"encoding/json"
	"errors"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/mapreduce"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/pkg/web"
	contractPb "golang.52tt.com/protocol/services/anchorcontract-go"
	contract_http_logic "golang.52tt.com/protocol/services/contract-http-logic"
	coPb "golang.52tt.com/protocol/services/guild-cooperation"
	guildPb "golang.52tt.com/protocol/services/guild-go"
	"golang.52tt.com/services/anchor-contract/contract-http-logic/client"
	"net/http"
	"sort"
	"time"
)

const (
	DaySec       = 24 * 3600
	RegDayCnt    = 7
	LoginDayCnt  = 5
	UserAgeLimit = 40
	TrGuildId    = 2555076
)

var mapIdentity2CoType = map[uint32]coPb.CooperationType{
	uint32(contractPb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER): coPb.CooperationType_CTypeAmuse,
	uint32(contractPb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE):  coPb.CooperationType_CTypeYuyin,
	uint32(contractPb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_E_SPORTS):    coPb.CooperationType_CTypeESports,
}

var mapCoType2DayCnt = map[coPb.CooperationType]uint32{
	coPb.CooperationType_CTypeAmuse:   120,
	coPb.CooperationType_CTypeYuyin:   100,
	coPb.CooperationType_CTypeESports: 100,
}

func deduplicateUidList(uidList []uint32) []uint32 {
	mapUid2Is := make(map[uint32]bool, 0)
	tmpList := make([]uint32, 0)

	for _, uid := range uidList {
		if !mapUid2Is[uid] {
			tmpList = append(tmpList, uid)
			mapUid2Is[uid] = true
		}
	}

	return tmpList
}

func checkIsNewGuild(ctx context.Context, uid, guildId, identityType uint32) (bool, error) {
	// 过滤TR公会
	if guildId == TrGuildId && identityType == uint32(contractPb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE) {
		log.InfoWithCtx(ctx, "checkIsNewGuild end uid:%d guildId:%d identityType:%d no need check", uid, guildId, identityType)
		return false, nil
	}

	coList, err := client.GuildCoopCli.GetGuildCooperationHistory(ctx, guildId, mapIdentity2CoType[identityType])
	if err != nil {
		log.ErrorWithCtx(ctx, "checkIsNewGuild GetGuildCooperationHistory failed uid:%d guildId:%d identityType:%d", uid, guildId, identityType)
		return false, err
	}

	isNew := false
	isCo := false

	nowTm := time.Now()
	nowTm = time.Date(nowTm.Year(), nowTm.Month(), nowTm.Day(), 0, 0, 0, 0, time.Local)

	for _, co := range coList {
		if co.GetCooperationType() == mapIdentity2CoType[identityType] {
			isCo = true

			optTm := time.Unix(co.GetOptTime(), 0)
			optTm = time.Date(optTm.Year(), optTm.Month(), optTm.Day(), 0, 0, 0, 0, time.Local)

			if optTm.Unix() <= nowTm.Unix() && uint32((nowTm.Unix()-optTm.Unix())/DaySec) <= mapCoType2DayCnt[co.GetCooperationType()] {
				isNew = true
				break
			}
		}
	}

	if !isCo {
		log.ErrorWithCtx(ctx, "checkIsNewGuild is no cooperation uid:%d guildId:%d identityType:%d coList:%v", uid, guildId, identityType, coList)
		return false, errors.New("no cooperation")
	}

	log.InfoWithCtx(ctx, "checkIsNewGuild end uid:%d guildId:%d identityType:%d isNew:%v coList:%v", uid, guildId, identityType, isNew, coList)
	return isNew, nil
}

func checkIsSighWhiteUid(ctx context.Context, uid uint32) bool {

	isWhite := false
	resp, err := client.AnchorContractCli.CheckIsSignWhiteUid(ctx, &contractPb.CheckIsSignWhiteUidReq{Uid: uid})
	if err != nil {
		log.ErrorWithCtx(ctx, "checkIsSighWhiteUid failed uid:%d err:%v", uid, err)
		return false
	}
	if resp != nil {
		isWhite = resp.IsWhite
	}
	log.InfoWithCtx(ctx, "checkIsSighWhiteUid uid:%d isWhite:%v", uid, resp)
	return isWhite
}

func checkUserIsHit(ctx context.Context, uid uint32, sameUidList, bindUidList []uint32) (bool, error) {
	subCtx, cancel := protogrpc.NewContextWithInfoTimeout(ctx, time.Second*3)
	defer cancel()

	isHitSign := false
	getContractRecord := func() error {
		identityList := []uint32{uint32(contractPb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER), uint32(contractPb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE),
			uint32(contractPb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_E_SPORTS)}

		uidList := deduplicateUidList(append(sameUidList, bindUidList...))
		for _, identity := range identityList {
			contractResp, err := client.AnchorContractCli.BatchGetUserAnchorIdentityLog(subCtx, uid, &contractPb.BatchGetUserAnchorIdentityLogReq{
				UidList:        uidList,
				AnchorIdentity: identity,
				ChangeType:     uint32(contractPb.ANCHOR_IDENTITY_CHANGE_TYPE_ANCHOR_IDENTITY_CHANGE_TYPE_ADD),
			})
			if err != nil {
				log.ErrorWithCtx(ctx, "checkUserIsHit BatchGetUserAnchorIdentityLog failed uid:%d uidList:%v err:%v", uid, uidList, err)
				return err
			}

			if len(contractResp.GetList()) != 0 {
				isHitSign = true
				break
			}
		}

		return nil
	}

	isHitReg := false
	/*
		getReg := func() error {
			uidList := deduplicateUidList(sameUidList)
			mapUid2User, err := client.BatchGetUserMapInfo(subCtx, uidList)
			if err != nil {
				log.ErrorWithCtx(ctx, "checkUserIsHit BatchGetUserMapInfo failed uid:%d uidList:%v err:%v", uid,uidList, err)
				return err
			}

			nowTm := time.Now()
			nowTm = time.Date(nowTm.Year(), nowTm.Month(), nowTm.Day(), 0,0,0,0, time.Local)
			for _, user := range mapUid2User {
				regTm := time.Unix(int64(user.GetRegisteredAt()), 0)
				regTm = time.Date(regTm.Year(), regTm.Month(), regTm.Day(), 0,0,0,0, time.Local)
				if nowTm.Unix() >= regTm.Unix() && (nowTm.Unix()-regTm.Unix())/DaySec > RegDayCnt {
					isHitReg = true
					break
				}
			}

			return nil
		}
	*/

	isHitLogin := false
	/*
		getLogin := func() error {
			uidList := deduplicateUidList(bindUidList)

			for _, bindUid := range uidList {
				loginCnt, err := client.GetUserLoginCnt(subCtx, bindUid)
				if err != nil {
					log.ErrorWithCtx(ctx, "checkUserIsHit GetUserLoginCnt failed uid:%d uidList:%v err:%v", uid,uidList, err)
					return err
				}

				if loginCnt >= LoginDayCnt {
					isHitLogin = true
					break
				}
			}

			return nil
		}
	*/

	err := mapreduce.Finish(getContractRecord)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkUserIsHit common.Finish failed uid:%d err:%v", uid, err)
		return false, err
	}

	if isHitReg || isHitSign || isHitLogin {
		log.InfoWithCtx(ctx, "checkUserIsHit hit uid:%d sameUidList:%v bindUidList:%v isHitReg:%v isHitSign:%v isHitLogin:%v",
			uid, sameUidList, bindUidList, isHitReg, isHitSign, isHitLogin)
		return true, nil
	}

	log.InfoWithCtx(ctx, "checkUserIsHit no hit uid:%d sameUidList:%v bindUidList:%v", uid, sameUidList, bindUidList)
	return false, nil
}

func SignIdentityPreCheck(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	var req contract_http_logic.SignIdentityPreCheckReq
	resp := &contract_http_logic.SignIdentityPreCheckResp{}

	err := json.Unmarshal(authInfo.Body, &req)
	if err != nil {
		log.ErrorWithCtx(ctx, "SignIdentityPreCheck Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		_ = web.ServeAPICodeJson(w, -2, "签约参数错误", nil)
		return
	}

	log.InfoWithCtx(ctx, "SignIdentityPreCheck authInfo:%+v req:%+v", authInfo, req)

	uid := authInfo.UserID

	identityNum := ""
	if req.GetIdentityType() == uint32(contractPb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_E_SPORTS) ||
		req.GetIdentityType() == uint32(contractPb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_DOYEN) {
		// 判断年龄
		realNameAuthInfoV2Resp, sErr := client.TTcProxyCli.GetUserRealNameAuthInfoV2(ctx, uint64(uid), false, true)
		if sErr != nil {
			log.ErrorWithCtx(ctx, "SignIdentityPreCheck GetUserRealNameAuthInfoV2 failed uid:%d req:%+v sErr:%v", uid, req, sErr)
			_ = web.ServeAPICodeJson(w, int32(sErr.Code()), sErr.Message(), nil)
			return
		}

		if realNameAuthInfoV2Resp.GetAge() > UserAgeLimit {
			log.ErrorWithCtx(ctx, "SignIdentityPreCheck age limit failed uid:%d authInfo:%+v req:%+v realNameAuthInfoV2Resp:%v", uid, authInfo,
				req, realNameAuthInfoV2Resp)
			_ = web.ServeAPICodeJson(w, -2, "您的账号不符合申请要求，感谢您的支持", nil)
			return
		}
		if realNameAuthInfoV2Resp.IdcardInfo != nil {
			identityNum = realNameAuthInfoV2Resp.IdcardInfo.GetIdentityNum()
		}
	}

	isWhite := checkIsSighWhiteUid(ctx, uid)
	if isWhite {
		// 白名单用户，不用检查纯新用户
		_ = web.ServeAPIJson(w, resp)
		return
	}

	if req.GetIdentityType() == uint32(contractPb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_DOYEN) { //平台达人签约检查
		sErr := client.AnchorContractCli.CheckCanApplySign(ctx, &contractPb.CheckCanApplySignReq{
			ActorUid:     uid,
			IdentityType: req.GetIdentityType(),
			IdentityNum:  identityNum,
		})
		if sErr != nil {
			log.ErrorWithCtx(ctx, "SignIdentityPreCheck CheckCanApplySign failed uid:%d req:%+v sErr:%v", uid, req, sErr)
			_ = web.ServeAPICodeJson(w, int32(sErr.Code()), sErr.Message(), nil)
			return
		}
		_ = web.ServeAPIJson(w, resp)
		return
	}

	isNew, err := checkIsNewGuild(ctx, uid, req.GetGuildId(), req.GetIdentityType())
	if err != nil {
		log.ErrorWithCtx(ctx, "SignIdentityPreCheck checkIsNewGuild failed authInfo:%+v req:%+v err:%v", authInfo, req, err)
		_ = web.ServeAPICodeJson(w, -2, "签约系统错误", nil)
		return
	}

	if !isNew {
		// 不是纯新公会，不用检查
		_ = web.ServeAPIJson(w, resp)
		return
	}

	userInfo, sErr := client.AccountCli.GetUserByUid(ctx, uid)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "SignIdentityPreCheck GetUserByUid failed uid:%d req:%+v sErr:%v", uid, req, sErr)
		_ = web.ServeAPICodeJson(w, int32(sErr.Code()), sErr.Message(), nil)
		return
	}

	bindUidList := []uint32{uid}
	if userInfo.GetPhone() != "" {
		phoneResp, sErr := client.AccountCli.GetSecurePhoneBindUidList(ctx, uid, userInfo.GetPhone(), true)
		if sErr != nil {
			log.ErrorWithCtx(ctx, "SignIdentityPreCheck GetSecurePhoneBindUidList failed uid:%d req:%+v sErr:%v", uid, req, sErr)
			_ = web.ServeAPICodeJson(w, int32(sErr.Code()), sErr.Message(), nil)
			return
		}

		if phoneResp.GetLoginUid() != uid {
			bindUidList = append(bindUidList, phoneResp.GetLoginUid())
		}

		for _, secureUid := range phoneResp.GetUidList() {
			if secureUid != uid {
				bindUidList = append(bindUidList, secureUid)
			}
		}
	}

	sameUidList := []uint32{uid}
	sameResp, sErr := client.TTcProxyCli.GetTheSameRealNameUserList(ctx, uint64(uid))
	if sErr != nil {
		log.ErrorWithCtx(ctx, "SignIdentityPreCheck GetTheSameRealNameUserList failed uid:%d req:%+v sErr:%v", uid, req, sErr)
		_ = web.ServeAPICodeJson(w, int32(sErr.Code()), sErr.Message(), nil)
		return
	}

	sameUidList = append(sameUidList, sameResp.GetUids()...)

	isHit, err := checkUserIsHit(ctx, uid, sameUidList, bindUidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "SignIdentityPreCheck checkUserIsHit failed uid:%d authInfo:%+v req:%+v err:%v", uid, authInfo, req, err)
		_ = web.ServeAPICodeJson(w, -2, "签约系统错误", nil)
		return
	}

	if isHit {
		log.ErrorWithCtx(ctx, "SignIdentityPreCheck not allow sign uid:%d authInfo:%+v req:%+v err:%v", uid, authInfo, req, err)
		_ = web.ServeAPICodeJson(w, -2, "该业务目前仅对新入驻从业者开放申请，感谢您的支持", nil)
		return
	}

	log.DebugWithCtx(ctx, "SignIdentityPreCheck uid:%d authInfo:%+v req:%+v resp:%+v", uid, authInfo, req, resp)
	_ = web.ServeAPIJson(w, resp)
}

func GetRecommendTopGuildList(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()
	uid := authInfo.UserID

	var req contract_http_logic.GetRecommendTopGuildListReq
	resp := &contract_http_logic.GetRecommendTopGuildListResp{}
	err := json.Unmarshal(authInfo.Body, &req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRecommendTopGuildList Failed to parse request uid:%d body [%s], err %+v", uid, string(authInfo.Body), err)
		_ = web.ServeAPICodeJson(w, -2, "参数错误", nil)
		return
	}

	sameResp, sErr := client.TTcProxyCli.GetTheSameRealNameUserList(ctx, uint64(uid))
	if sErr != nil {
		log.ErrorWithCtx(ctx, "GetRecommendTopGuildList GetTheSameRealNameUserList failed uid:%d authInfo:%+v req:%+v err:%v", uid, authInfo, req, sErr)
		_ = web.ServeAPICodeJson(w, -2, "获取同实名列表失败", nil)
		return
	}

	mapUidIsSame := make(map[uint32]bool)
	sameUidList := make([]uint32, 0)
	for _, sameUid := range sameResp.GetUids() {
		mapUidIsSame[sameUid] = true
		sameUidList = append(sameUidList, sameUid)
	}

	mapUid2Info, err := batGetUser(ctx, append(sameUidList, uid))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRecommendTopGuildList batGetUser failed uid:%d authInfo:%+v req:%+v err:%v", uid, authInfo, req, sErr)
		_ = web.ServeAPICodeJson(w, -2, "获取用户信息失败", nil)
		return
	}

	mapGuildId2Is := make(map[uint32]bool)
	for _, info := range mapUid2Info {
		mapGuildId2Is[info.GetCurrentGuildId()] = true
	}

	isCoopGuildOwner := false
	coopTypeList := []coPb.CooperationType{
		coPb.CooperationType_CTypeYuyin,
		coPb.CooperationType_CTypeESports,
		coPb.CooperationType_CTypeAmuse,
	}
	guildYuyinMap := make(map[uint32]bool, 0)
	guildESportsMap := make(map[uint32]bool, 0)
	guildAmuseMap := make(map[uint32]bool, 0)
	for _, coopType := range coopTypeList {
		offset := uint32(0)
		limit := uint32(100)
		for {
			tmpResp, err := client.GuildCoopCli.GetCooperationGuildIdsV2(ctx, coopType, []uint32{}, offset, limit)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetRecommendTopGuildList GetCooperationGuildIdsV2 failed uid:%d err:%s", uid, err)
				return
			}
			for _, guildId := range tmpResp.GetGuildList() {
				if mapGuildId2Is[guildId] {
					log.DebugWithCtx(ctx, "GetRecommendTopGuildList uid:%d guildId:%d isCoopGuildOwner", uid, guildId)
					isCoopGuildOwner = true
				}

				if coopType == coPb.CooperationType_CTypeYuyin {
					guildYuyinMap[guildId] = true
				} else if coopType == coPb.CooperationType_CTypeESports {
					guildESportsMap[guildId] = true
				} else if coopType == coPb.CooperationType_CTypeAmuse {
					guildAmuseMap[guildId] = true
				}
			}
			if len(tmpResp.GetGuildList()) < int(limit) {
				break
			}

			offset += limit
		}

	}

	tmpResp, err := client.AnchorContractCli.GetRecommendTopGuildList(ctx, &contractPb.GetRecommendTopGuildListReq{})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRecommendTopGuildList failed uid:%d authInfo:%+v req:%+v err:%v", uid, authInfo, req, err)
		_ = web.ServeAPICodeJson(w, -2, "获取推荐公会失败", nil)
		return
	}
	guildIdList := make([]uint32, 0)
	for _, item := range tmpResp.GetGuildList() {
		guildIdList = append(guildIdList, item.GetGuildId())
	}

	guildInfoList, err := client.GuildGoCli.BatchGetGuildById(ctx, uid, &guildPb.BatchGetGuildByIdReq{
		GuildIds: guildIdList,
		GetAll:   true,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRecommendTopGuildList failed uid:%d authInfo:%+v req:%+v err:%v", uid, authInfo, req, err)
		_ = web.ServeAPICodeJson(w, -2, "获取推荐公会失败", nil)
		return
	}

	guildInfoMap := make(map[uint32]*guildPb.GuildInfo, 0)
	uidList := make([]uint32, 0)
	mapIdIsOwnerGuild := make(map[uint32]bool)
	for _, item := range guildInfoList.GetList() {
		guildInfoMap[item.GetGuildId()] = item
		uidList = append(uidList, uint32(item.GetOwner()))
		if uint32(item.GetOwner()) == uid {
			mapIdIsOwnerGuild = map[uint32]bool{item.GetGuildId(): true}
			break
		}
		if mapUidIsSame[uint32(item.GetOwner())] {
			mapIdIsOwnerGuild[item.GetGuildId()] = true
		}
	}

	userMap, err := batGetUser(ctx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRecommendTopGuildList failed uid:%d authInfo:%+v req:%+v err:%v", uid, authInfo, req, err)
		_ = web.ServeAPICodeJson(w, -2, "获取用户列表失败", nil)
		return
	}
	for _, item := range tmpResp.GetGuildList() {
		if isCoopGuildOwner && !mapIdIsOwnerGuild[item.GetGuildId()] {
			continue
		}

		userInfo := userMap[uint32(guildInfoMap[item.GetGuildId()].GetOwner())]
		if guildInfo, ok := guildInfoMap[item.GetGuildId()]; ok {
			topGuild := &contract_http_logic.TopGuildInfo{
				GuildId:      item.GetGuildId(),
				Rank:         item.GetRank(),
				RecommendTag: item.GetRecommendTag(),
				//AbilityTag:   item.GetAbilityTag(),
				HonorTitle:     item.GetHonorTitle(),
				AbilityTagList: item.GetAbilityTagList(),
				FromTime:       item.GetFromTime(),
				ToTime:         item.GetToTime(),
				Name:           guildInfo.GetName(),
				ShortId:        guildInfo.GetShortId(),
				GuildTag:       guildInfo.GetIntro(),
				OwnerUid:       uint32(guildInfo.GetOwner()),
				OwnerName:      userInfo.GetNickname(),
				OwnerAlias:     userInfo.GetAlias(),
				OwnerAccount:   userInfo.GetUsername(),
			}
			guildBusinessAmuse := &contract_http_logic.GuildBusiness{
				Name:      "多人互动",
				Type:      uint32(contractPb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER),
				Available: false,
			}
			guildBusinessYuyin := &contract_http_logic.GuildBusiness{
				Name:      "听听公会",
				Type:      uint32(contractPb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE),
				Available: false,
			}
			guildBusinessSports := &contract_http_logic.GuildBusiness{
				Name:      "电竞指导",
				Type:      uint32(contractPb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_E_SPORTS),
				Available: false,
			}
			if guildYuyinMap[item.GetGuildId()] {
				guildBusinessYuyin.Available = true
			}
			if guildESportsMap[item.GetGuildId()] {
				guildBusinessSports.Available = true
			}
			if guildAmuseMap[item.GetGuildId()] {
				guildBusinessAmuse.Available = true
			}
			topGuild.BusinessList = append(topGuild.BusinessList, guildBusinessYuyin)
			topGuild.BusinessList = append(topGuild.BusinessList, guildBusinessSports)
			topGuild.BusinessList = append(topGuild.BusinessList, guildBusinessAmuse)
			resp.GuildList = append(resp.GetGuildList(), topGuild)
		}
	}

	sort.Slice(resp.GuildList, func(i, j int) bool {
		return resp.GuildList[i].Rank < resp.GuildList[j].Rank
	})

	log.InfoWithCtx(ctx, "GetRecommendTopGuildList uid:%d isCoopGuildOwner:%v tmpResp:%v sameResp:%v authInfo:%+v req:%+v resp:%+v ", uid, isCoopGuildOwner, tmpResp, sameResp, authInfo, req, resp)
	_ = web.ServeAPIJson(w, resp)
}

func GetUserContractInfo(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()
	uid := authInfo.UserID

	var req contract_http_logic.GetUserContractInfoReq
	resp := &contract_http_logic.GetUserContractInfoResp{}
	err := json.Unmarshal(authInfo.Body, &req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserContractInfo Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		_ = web.ServeAPICodeJson(w, -2, "参数错误", nil)
		return
	}

	contractInfo, sErr := client.AnchorContractCli.GetUserContractCacheInfo(ctx, uid, uid)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "GetUserContractInfo failed uid:%d authInfo:%+v req:%+v sErr:%v", uid, authInfo, req, sErr)
		_ = web.ServeAPICodeJson(w, -2, "获取签约信息失败", nil)
		return
	}

	if contractInfo.GetContract().GetGuildId() != 0 && contractInfo.GetContract().GetExpireTime() >= uint32(time.Now().Unix()) {
		inviteResp, sErr := client.AnchorContractCli.GetUserPromoteInviteInfo(ctx, &contractPb.GetUserPromoteInviteInfoReq{
			Uid:     uid,
			GuildId: contractInfo.GetContract().GetGuildId(),
		},
		)
		if sErr != nil {
			log.ErrorWithCtx(ctx, "GetUserPromoteInviteInfo failed uid:%d authInfo:%+v req:%+v sErr:%v", uid, authInfo, req, sErr)
			_ = web.ServeAPICodeJson(w, -2, "获取签约信息失败", nil)
			return
		}

		if inviteResp.GetInfo().GetId() != 0 {
			resp.PromoteInfo = &contract_http_logic.PromoteInfo{
				Id:         inviteResp.GetInfo().GetId(),
				SignMonths: inviteResp.GetInfo().GetSignMonths(),
			}
		}
	}

	resp.ContractInfo = &contract_http_logic.UserContractInfo{
		WorkerType: contractInfo.GetContract().GetWorkerType(),
	}

	log.DebugWithCtx(ctx, "GetUserContractInfo uid:%d authInfo:%+v req:%+v resp:%+v ", uid, authInfo, req, resp)
	_ = web.ServeAPIJson(w, resp)
}

func ProcPromoteInvite(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()
	uid := authInfo.UserID

	var req contract_http_logic.ProcPromoteInviteReq
	resp := &contract_http_logic.ProcPromoteInviteResp{}
	err := json.Unmarshal(authInfo.Body, &req)
	if err != nil {
		log.ErrorWithCtx(ctx, "ProcPromoteInvite Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		_ = web.ServeAPICodeJson(w, -2, "参数错误", nil)
		return
	}

	_, sErr := client.AnchorContractCli.ProcPromoteInvite(ctx, &contractPb.ProcPromoteInviteReq{
		ProcRes:    req.GetProcRes(),
		Id:         req.GetId(),
		InvitedUid: uid,
	})
	if sErr != nil {
		log.ErrorWithCtx(ctx, "ProcPromoteInvite failed uid:%d authInfo:%+v req:%+v sErr:%v", uid, authInfo, req, sErr)
		_ = web.ServeAPICodeJson(w, -2, sErr.Message(), nil)
		return

	}

	log.DebugWithCtx(ctx, "ProcPromoteInvite uid:%d authInfo:%+v req:%+v resp:%+v ", uid, authInfo, req, resp)
	_ = web.ServeAPIJson(w, resp)
}

func GetGuildMgrList(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()
	uid := authInfo.UserID

	var req contract_http_logic.GetGuildMgrListReq
	resp := &contract_http_logic.GetGuildMgrListResp{}
	err := json.Unmarshal(authInfo.Body, &req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildMgrList Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		_ = web.ServeAPICodeJson(w, -2, "参数错误", nil)
		return
	}

	var page, pageSize uint32 = 0, 500
	for {
		contractResp, sErr := client.AnchorContractCli.GetGuildContractByCond(ctx, &contractPb.GetGuildContractByCondReq{
			GuildId:        req.GetGuildId(),
			Page:           page,
			PageSize:       pageSize,
			WorkerType:     uint32(contractPb.ContractWorkerType_ContractWorkerType_Manager),
			IsSelectWorker: true,
		})
		if sErr != nil {
			log.ErrorWithCtx(ctx, "GetGuildMgrList failed uid:%d authInfo:%+v req:%+v sErr:%v", uid, authInfo, req, sErr)
			_ = web.ServeAPICodeJson(w, -2, "获取列表失败", nil)
			return
		}

		for _, item := range contractResp.GetContractList() {
			resp.UidList = append(resp.UidList, item.GetActorUid())
		}

		if len(contractResp.GetContractList()) < int(pageSize) {
			break
		}

		page++
	}

	log.DebugWithCtx(ctx, "GetGuildMgrList uid:%d authInfo:%+v req:%+v resp:%+v ", uid, authInfo, req, resp)
	_ = web.ServeAPIJson(w, resp)
}

func GetNeedConfirmWorkerType(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()
	uid := authInfo.UserID

	var req contract_http_logic.GetNeedConfirmWorkerTypeReq
	resp := &contract_http_logic.GetNeedConfirmWorkerTypeResp{}
	err := json.Unmarshal(authInfo.Body, &req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildMgrList Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		_ = web.ServeAPICodeJson(w, -2, "参数错误", nil)
		return
	}

	contractResp, sErr := client.AnchorContractCli.GetNeedConfirmWorkerType(ctx, &contractPb.GetNeedConfirmWorkerTypeReq{Uid: uid, GuildId: req.GetGuildId()})
	if sErr != nil {
		log.ErrorWithCtx(ctx, "GetNeedConfirmWorkerType failed uid:%d authInfo:%+v req:%+v sErr:%v", uid, authInfo, req, sErr)
		_ = web.ServeAPICodeJson(w, -2, "获取列表失败", nil)
		return
	}

	resp.NeedConfirm = contractResp.GetNeedConfirm()

	log.DebugWithCtx(ctx, "GetNeedConfirmWorkerType uid:%d authInfo:%+v req:%+v resp:%+v ", uid, authInfo, req, resp)
	_ = web.ServeAPIJson(w, resp)
}

func ModifyWorkerType(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()
	uid := authInfo.UserID

	var req contract_http_logic.ModifyWorkerTypeReq
	resp := &contract_http_logic.ModifyWorkerTypeResp{}
	err := json.Unmarshal(authInfo.Body, &req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildMgrList Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		_ = web.ServeAPICodeJson(w, -2, "参数错误", nil)
		return
	}

	_, sErr := client.AnchorContractCli.ModifyWorkerType(ctx, &contractPb.ModifyWorkerTypeReq{Uid: uid, WorkerType: req.GetWorkerType()})
	if sErr != nil {
		log.ErrorWithCtx(ctx, "ModifyWorkerType failed uid:%d authInfo:%+v req:%+v sErr:%v", uid, authInfo, req, sErr)
		_ = web.ServeAPICodeJson(w, -2, "获取列表失败", nil)
		return
	}

	log.DebugWithCtx(ctx, "ModifyWorkerType uid:%d authInfo:%+v req:%+v resp:%+v ", uid, authInfo, req, resp)
	_ = web.ServeAPIJson(w, resp)
}

func GetAnchorLevelNewTaskHandler(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	uid := authInfo.UserID

	type UidReq struct {
		AnchorUid uint32 `json:"anchor_uid"`
	}

	req := UidReq{}
	json.Unmarshal(authInfo.Body, &req)

	log.Debugf("GetAnchorLevelNewTaskHandler uid %d anchor_uid %d, body %s", uid, req.AnchorUid, authInfo.Body)

	if req.AnchorUid != 0 {
		uid = req.AnchorUid
	}

	task, err := client.AnchorLevelCli.GetAnchorLevelNewTask(ctx, uid)
	if err != nil {
		log.Errorf("AnchorLevelClient.GetAnchorLevelNewTask err:%v", err)
		_ = web.ServeAPICodeJson(w, -2, err.Error(), nil)
		return
	}

	httpResp := contract_http_logic.AnchorLevelNewTask{
		RemainTime:             task.GetRemainTime(),
		NeedWeekActiveDays:     task.GetNeedWeekActiveDays(),
		RemainWeekActiveDays:   task.GetRemainWeekActiveDays(),
		NeedWeekPlatformDays:   task.GetNeedWeekPlatformDays(),
		RemainWeekPlatformDays: task.GetRemainWeekPlatformDays(),
		WeekGiftValue:          task.GetWeekGiftValue(),
		AnchorCheckLevel:       task.GetAnchorCheckLevel(),
		WeekNum:                task.GetWeekNum(),
		TaskType:               task.GetTaskType(),
	}

	_ = web.ServeAPIJson(w, &httpResp)

	log.Debugf("GetAnchorLevelNewTaskHandler uid %d anchor_uid %d resp:%v", uid, req.AnchorUid, httpResp)
}

func GetContractChangeInfo(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	uid := authInfo.UserID

	req := &contract_http_logic.GetContractChangeInfoReq{}
	json.Unmarshal(authInfo.Body, &req)

	log.Debugf("GetContractChangeInfo uid %d anchor_uid %d, body %s", uid, req.Id, authInfo.Body)

	resp, err := client.AnchorContractCli.GetContractChangeInfo(ctx, &contractPb.GetContractChangeInfoReq{Id: req.GetId()})
	if err != nil {
		log.Errorf("AnchorLevelClient.GetAnchorLevelNewTask err:%v", err)
		_ = web.ServeAPICodeJson(w, -2, err.Error(), nil)
		return
	}

	httpResp := contract_http_logic.GetContractChangeInfoResp{
		GroupList:   make([]*contract_http_logic.ContractPrivilegeGroupChange, 0),
		CancelTypes: make([]*contract_http_logic.CanCancelContractChange, 0),
		IsAccept:    resp.GetIsAccept(),
	}

	for _, group := range resp.GetGroupList() {
		httpGroup := &contract_http_logic.ContractPrivilegeGroupChange{
			Name:                 group.GetName(),
			PrivilegeList:        make([]*contract_http_logic.ContractPrivilege, 0),
			RemovedPrivilegeList: make([]*contract_http_logic.ContractPrivilege, 0),
		}

		// 从小到大
		sort.Slice(group.PrivilegeList, func(i, j int) bool {
			return group.PrivilegeList[i].Sort < group.PrivilegeList[j].Sort
		})

		for _, privilege := range group.GetPrivilegeList() {
			httpPrivilege := &contract_http_logic.ContractPrivilege{
				Name:     privilege.GetName(),
				Icon:     privilege.GetIcon(),
				IsSelect: privilege.GetIsSelect(),
				Note:     privilege.GetNote(),
				IsNew:    privilege.GetIsNew(),
			}
			httpGroup.PrivilegeList = append(httpGroup.PrivilegeList, httpPrivilege)
		}

		sort.Slice(group.RemovedPrivilegeList, func(i, j int) bool {
			return group.RemovedPrivilegeList[i].Sort < group.RemovedPrivilegeList[j].Sort
		})

		for _, privilege := range group.GetRemovedPrivilegeList() {
			httpPrivilege := &contract_http_logic.ContractPrivilege{
				Name:     privilege.GetName(),
				Icon:     privilege.GetIcon(),
				IsSelect: privilege.GetIsSelect(),
				Note:     privilege.GetNote(),
				IsNew:    privilege.GetIsNew(),
			}
			httpGroup.RemovedPrivilegeList = append(httpGroup.RemovedPrivilegeList, httpPrivilege)
		}
		httpResp.GroupList = append(httpResp.GroupList, httpGroup)
	}

	for _, cancelType := range resp.GetCancelTypes() {
		httpCancelType := &contract_http_logic.CanCancelContractChange{
			CancelTypes:        make([]*contract_http_logic.CanCancelContract, 0),
			RemovedCancelTypes: make([]*contract_http_logic.CanCancelContract, 0),
		}

		for _, tmpCancel := range cancelType.GetCancelTypes() {
			httpCancel := &contract_http_logic.CanCancelContract{
				CancelType: tmpCancel.GetCancelType(),
				Enabled:    tmpCancel.GetIsSelect(),
				Name:       tmpCancel.GetTilte(),
				Desc:       tmpCancel.GetDesc(),
				IsNew:      tmpCancel.GetIsNew(),
			}
			httpCancelType.CancelTypes = append(httpCancelType.CancelTypes, httpCancel)
		}

		for _, tmpCancel := range cancelType.GetRemovedCancelTypes() {
			httpCancel := &contract_http_logic.CanCancelContract{
				CancelType: tmpCancel.GetCancelType(),
				Enabled:    tmpCancel.GetIsSelect(),
				Name:       tmpCancel.GetTilte(),
				Desc:       tmpCancel.GetDesc(),
				IsNew:      tmpCancel.GetIsNew(),
			}
			httpCancelType.RemovedCancelTypes = append(httpCancelType.RemovedCancelTypes, httpCancel)
		}

		httpResp.CancelTypes = append(httpResp.CancelTypes, httpCancelType)
	}

	_ = web.ServeAPIJson(w, &httpResp)

	log.Debugf("GetAnchorLevelNewTaskHandler uid %d id %d resp:%v", uid, req.GetId(), httpResp)
}

func HandleContractChange(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	uid := authInfo.UserID

	req := &contract_http_logic.HandleContractChangeReq{}
	json.Unmarshal(authInfo.Body, &req)

	log.Debugf("HandleContractChange uid %d anchor_uid %d, body %s", uid, req.Id, authInfo.Body)

	_, err := client.AnchorContractCli.HandleContractChange(ctx, &contractPb.HandleContractChangeReq{Id: req.GetId(), IsAgree: req.GetIsAgree(), Uid: uid})
	if err != nil {
		log.Errorf("AnchorContractCli.HandleContractChange err:%v", err)
		_ = web.ServeAPICodeJson(w, -2, err.Error(), nil)
		return
	}

	httpResp := contract_http_logic.HandleContractChangeResp{}

	_ = web.ServeAPIJson(w, &httpResp)

	log.Debugf("GetAnchorLevelNewTaskHandler uid %d id %d resp:%v", uid, req.GetId(), httpResp)
}

func GetRejectReason(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	uid := authInfo.UserID

	req := &contract_http_logic.GetRejectReasonReq{}
	json.Unmarshal(authInfo.Body, &req)

	log.Debugf("GetRejectReason uid %d id %d, body %s", uid, req.Id, authInfo.Body)

	resp, err := client.AnchorContractCli.GetRejectReason(ctx, &contractPb.GetRejectReasonReq{Id: req.GetId(), Uid: uid})
	if err != nil {
		log.Errorf("AnchorContractCli.GetRejectReason err:%v", err)
		_ = web.ServeAPICodeJson(w, -2, err.Error(), nil)
		return
	}

	httpResp := contract_http_logic.GetRejectReasonResp{ProofList: make([]*contract_http_logic.ProofShowContent, 0)}

	for _, reason := range resp.GetProofList() {
		httpProof := &contract_http_logic.ProofShowContent{
			Type: contract_http_logic.ProofShowContent_ProofType(reason.GetType()),
			Url:  reason.GetUrl(),
		}
		httpResp.ProofList = append(httpResp.ProofList, httpProof)
	}

	httpResp.RejectReason = resp.GetRejectReason()

	_ = web.ServeAPIJson(w, &httpResp)

	log.Debugf("GetRejectReason uid %d id %d resp:%v", uid, req.GetId(), httpResp)
}

func GetNegotiateReasonType(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	uid := authInfo.UserID

	req := &contract_http_logic.GetNegotiateReasonTypeReq{}
	json.Unmarshal(authInfo.Body, &req)

	log.Debugf("GetNegotiateReasonType uid %d body %s", uid, authInfo.Body)

	resp, err := client.AnchorContractCli.GetNegotiateReasonType(ctx, &contractPb.GetNegotiateReasonTypeReq{})
	if err != nil {
		log.Errorf("AnchorContractCli.GetRejectReason err:%v", err)
		_ = web.ServeAPICodeJson(w, -2, err.Error(), nil)
		return
	}

	httpResp := contract_http_logic.GetNegotiateReasonTypeResp{
		NegotiateReasonType: resp.GetNegotiateReasonType(),
	}

	_ = web.ServeAPIJson(w, &httpResp)

	log.Debugf("GetRejectReason uid %d resp:%v", uid, httpResp)
}
