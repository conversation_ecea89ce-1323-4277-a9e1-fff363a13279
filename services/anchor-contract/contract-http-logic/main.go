package main

import (
	"fmt"
	"golang.52tt.com/services/anchor-contract/contract-http-logic/control/esport"
	"net/http"
	_ "net/http/pprof"
	"os"
	"time"

	"github.com/gorilla/mux"
	"github.com/urfave/cli"
	"golang.52tt.com/pkg/admin"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/versioning/svn" // code revision
	"golang.52tt.com/pkg/web"
	"golang.52tt.com/services/anchor-contract/contract-http-logic/client"
	"golang.52tt.com/services/anchor-contract/contract-http-logic/conf"
	"golang.52tt.com/services/anchor-contract/contract-http-logic/control/contract"
	hall_task "golang.52tt.com/services/anchor-contract/contract-http-logic/control/hall-task"
)

func main() {
	app := cli.NewApp()
	app.Name = "channel-live-http-logic"
	app.Version = svn.CodeRevision
	app.Compiled = time.Now()
	app.Copyright = "(c) 2023 TT"
	app.Usage = "tt revenue-base http service."
	app.Flags = []cli.Flag{
		cli.StringFlag{
			Name:  "config",
			Value: "",
			Usage: "config path",
		},
	}

	cli.VersionPrinter = func(c *cli.Context) {
		fmt.Fprintf(os.Stdout, "%s\n%s\n", c.App.Name, c.App.Version)
	}

	app.Action = func(c *cli.Context) error {
		log.SetLevel(log.DebugLevel)
		configPath := c.String("config")

		cfg, err := config.NewConfig("json", configPath)
		if err != nil {
			log.Fatalln("Failed to NewConfig: ", err)
			panic(err)
		}

		if err = conf.GetServerConfig().Parse(cfg); err != nil {
			log.Fatalln("Parse config fail: %v ", err)
			panic(err)
		}

		if err = client.Setup(); err != nil {
			log.Fatalln("Setup client fail: %v ", err)
			panic(err)
		}

		if err = conf.SetupDynamicConfig(); err != nil {
			log.Fatalln("Setup dynamic config fail: %v ", err)
			panic(err)
		}

		r := mux.NewRouter()

		auth := web.NewAuth(&web.UidAuthVerify{}, conf.GetServerConfig().GetValidateToken())
		handler := web.NewHandler(auth, conf.GetServerConfig().GetCors(), true)
		//noAuthHandler := web.NewHandler(nil, models.GetModelServer().GetServerConfig().GetCors(), true)

		// 外网接口
		p := r.PathPrefix("/contract-http-logic").Subrouter()
		hallTaskRouter := p.PathPrefix("/hall").Subrouter()
		hallTaskRouter.Handle("/getHallTask", handler.SetHandler(hall_task.GetHallTask))
		hallTaskRouter.Handle("/getHallTaskHistory", handler.SetHandler(hall_task.GetHallTaskHistory))

		eSportRouter := p.PathPrefix("/esport").Subrouter()
		eSportRouter.Handle("/getCoachInfo", handler.SetHandler(esport.GetESportCoachInfo))               // 获取电竞指导信息
		eSportRouter.Handle("/getESportUserSkillInfo", handler.SetHandler(esport.GetESportUserSkillInfo)) // 获取电竞指导信息

		contractRouter := p.PathPrefix("/contract").Subrouter()
		contractRouter.Handle("/signIdentityPreCheck", handler.SetHandler(contract.SignIdentityPreCheck))
		contractRouter.Handle("/applySignRiskCheck", handler.SetHandler(contract.ApplySignRiskCheck))             //统一风控触发协议
		contractRouter.Handle("/applySign", handler.SetHandler(contract.ApplySign))                               //签约申请身份
		contractRouter.Handle("/getRecommendTopGuildList", handler.SetHandler(contract.GetRecommendTopGuildList)) //获取合约信息
		contractRouter.Handle("/applySignContract", handler.SetHandler(contract.ApplySignContract))               //申请签约-娱乐厅最新
		contractRouter.Handle("/contractPrivilegeList", handler.SetHandler(contract.GetContractPrivilegeList))    //获取权益信息
		contractRouter.Handle("/GetUserContractInfo", handler.SetHandler(contract.GetUserContractInfo))           //获取用户签约信息
		contractRouter.Handle("/ProcPromoteInvite", handler.SetHandler(contract.ProcPromoteInvite))               //处理晋升邀请
		contractRouter.Handle("/GetGuildMgrList", handler.SetHandler(contract.GetGuildMgrList))                   //获取公会的所有管理员列表
		contractRouter.Handle("/getNeedConfirmWorkerType", handler.SetHandler(contract.GetNeedConfirmWorkerType))
		contractRouter.Handle("/modifyWorkerType", handler.SetHandler(contract.ModifyWorkerType))
		contractRouter.Handle("/getAnchorLevelNewTask", handler.SetHandler(contract.GetAnchorLevelNewTaskHandler)) //获取合约列表

		contractRouter.Handle("/getContractChangeInfo", handler.SetHandler(contract.GetContractChangeInfo))   // 获取合约变更信息
		contractRouter.Handle("/handleContractChange", handler.SetHandler(contract.HandleContractChange))     // 处理合约变更
		contractRouter.Handle("/getRejectReason", handler.SetHandler(contract.GetRejectReason))               //  获取拒绝理由
		contractRouter.Handle("/getNegotiateReasonType", handler.SetHandler(contract.GetNegotiateReasonType)) // 获取协商理由类型

		cancelRouter := p.PathPrefix("/cancelcontract").Subrouter()
		cancelRouter.Handle("/getCancelContractTypes", handler.SetHandler(contract.GetCancelContractTypes)) //获取解约类型
		cancelRouter.Handle("/checkCanCancelContract", handler.SetHandler(contract.CheckCanCancelContract)) //检查是否可以解约
		cancelRouter.Handle("/getApplyCancelList", handler.SetHandler(contract.GetApplyCancelContractList)) //获取解约申请列表
		cancelRouter.Handle("/applyCancel", handler.SetHandler(contract.ApplyCancelContract))               //申请解约-按解约方式
		cancelRouter.Handle("/acceptApplyCancel", handler.SetHandler(contract.AcceptApplyCancel))           //会长同意解约
		cancelRouter.Handle("/rejectApplyCancel", handler.SetHandler(contract.RejectApplyCancel))           //会长拒绝解约
		cancelRouter.Handle("/lockPayCancelAmount", handler.SetHandler(contract.LockPayCancelAmount))       //锁定付费解约金额
		cancelRouter.Handle("/censorVideo", handler.SetHandler(contract.CensorVideo))                       //审核视频

		addr := conf.GetServerConfig().GetAddr()
		log.Infof("server run at %s\n", addr)
		http.Handle("/", r)

		closer := admin.ListenAndServe()
		defer closer()
		return http.ListenAndServe(addr, nil)
	}

	_ = app.Run(os.Args)
}
