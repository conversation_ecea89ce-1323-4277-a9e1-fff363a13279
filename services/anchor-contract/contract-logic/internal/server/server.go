package server

import (
	"context"
	"fmt"
	"time"

	"golang.52tt.com/protocol/services/demo/echo"

	"golang.52tt.com/clients/account"
	anchorcontractgo "golang.52tt.com/clients/anchorcontract-go"
	"golang.52tt.com/clients/guild"
	sign_anchor_stats "golang.52tt.com/clients/sign-anchor-stats"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app"
	auth "golang.52tt.com/protocol/app/auth"
	pb "golang.52tt.com/protocol/app/contract-logic"
	"golang.52tt.com/protocol/common/status"
	anchorcontractpb "golang.52tt.com/protocol/services/anchorcontract-go"
	signAnchorPb "golang.52tt.com/protocol/services/sign-anchor-stats"
	"golang.52tt.com/services/anchor-contract/contract-logic/internal/conf"
)

type StartConfig struct {
	// [optional] from startup arguments

	// from config file
	*conf.ServiceConfigT
}

type ContractLogicImpl struct {
	sc *conf.ServiceConfigT

	signAnchorStatCli sign_anchor_stats.IClient
	accountCli        account.IClient
	anchorContractCli anchorcontractgo.IClient
	guildCli          guild.IClient
}

func NewContractLogicImpl(config *StartConfig) (*ContractLogicImpl, error) {
	sc := config.ServiceConfigT

	log.Infof("NewContractLogicImpl sc:%v", sc)
	if sc == nil {
		return nil, fmt.Errorf("no find conf")
	}

	signAnchorStatCli := sign_anchor_stats.NewIClient()
	accountCli := account.NewIClient()
	anchorContractCli := anchorcontractgo.NewIClient()

	s := &ContractLogicImpl{
		sc:                sc,
		signAnchorStatCli: signAnchorStatCli,
		accountCli:        accountCli,
		anchorContractCli: anchorContractCli,
		guildCli:          guild.NewIClient(),
	}

	return s, nil
}

func (s *ContractLogicImpl) ShutDown() {
	// now no thing to do
}

func (s *ContractLogicImpl) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	log.DebugWithCtx(ctx, "Echo req:[%+v]", req)
	return req, nil
}

func (s *ContractLogicImpl) CheckUserInteractEntry(ctx context.Context, in *pb.CheckUserInteractEntryRequest) (*pb.CheckUserInteractEntryResponse, error) {
	out := &pb.CheckUserInteractEntryResponse{}

	serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
	uid := serviceInfo.UserID

	signResp, err := s.signAnchorStatCli.CheckUserInteractEntry(ctx, &signAnchorPb.CheckUserInteractEntryReq{
		SceneType:     in.GetSceneType(),
		Uid:           uid,
		InteractUid:   in.GetInteractUid(),
		ChannelId:     in.GetChannelId(),
		ChannelLiveId: in.GetChannelLiveId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckUserInteractEntry failed uid:%d in:%v err:%v", uid, in, err)
		return out, err
	}

	out.IsHasEntry = signResp.GetIsHasEntry()
	out.EntryText = signResp.GetEntryText()

	log.DebugWithCtx(ctx, "CheckUserInteractEntry end uid:%d in:%v out:%v", uid, in, out)
	return out, nil
}

func (s *ContractLogicImpl) GetUserInteractInfo(ctx context.Context, in *pb.GetUserInteractInfoRequest) (*pb.GetUserInteractInfoResponse, error) {
	out := &pb.GetUserInteractInfoResponse{
		InfoList: make([]*pb.InteractInfo, 0),
		CertInfo: &pb.InteractCertInfo{
			FansInfo: &app.ChannelLiveFansInfo{},
		},
	}

	serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
	uid := serviceInfo.UserID

	signResp, err := s.signAnchorStatCli.GetUserInteractInfo(ctx, &signAnchorPb.GetUserInteractInfoReq{
		SceneType:     in.GetSceneType(),
		Uid:           uid,
		InteractUid:   in.GetInteractUid(),
		ChannelId:     in.GetChannelId(),
		ChannelLiveId: in.GetChannelLiveId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckUserInteractEntry failed uid:%d in:%v err:%v", uid, in, err)
		return out, err
	}

	userMap, err := s.accountCli.GetUsersMap(ctx, []uint32{uid, in.GetInteractUid()})
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckUserInteractEntry GetUsersMap uid:%d in:%v err:%v", uid, in, err)
		return out, err
	}

	out.UserType = signResp.GetUserType()
	out.CertInfo.CertMsg = signResp.GetCertInfo().GetCertMsg()
	out.Account = userMap[uid].GetUsername()
	out.InteractAccount = userMap[in.GetInteractUid()].GetUsername()
	out.InteractNickname = userMap[in.GetInteractUid()].GetNickname()

	for _, info := range signResp.GetInfoList() {
		out.InfoList = append(out.InfoList, &pb.InteractInfo{
			InteractType:  info.GetInteractType(),
			InteractValue: info.GetInteractValue(),
		})
	}

	if out.UserType == uint32(pb.InteractUserType_INTERACT_USER_TYPE_INTERACTUSER_EXPIRE_FANS) {
		out.CertInfo.FansInfo = &app.ChannelLiveFansInfo{
			FansLevel: signResp.GetCertInfo().GetFansInfo().GetFansLevel(),
			IsValid:   signResp.GetCertInfo().GetFansInfo().GetIsValid(),
			PlateInfo: fillFansPlateConfigMsg(signResp.GetCertInfo().GetFansInfo().GetPlateInfo()),
			GroupName: signResp.GetCertInfo().GetFansInfo().GetGroupName(),
		}
	}

	log.DebugWithCtx(ctx, "GetUserInteractInfo end uid:%d in:%v out:%v", uid, in, out)
	return out, nil
}

func (s *ContractLogicImpl) GetUserInteractViewPer(ctx context.Context, in *pb.GetUserInteractViewPerRequest) (*pb.GetUserInteractViewPerResponse, error) {
	out := &pb.GetUserInteractViewPerResponse{}

	serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
	uid := serviceInfo.UserID

	signResp, err := s.signAnchorStatCli.GetUserInteractViewPer(ctx, &signAnchorPb.GetUserInteractViewPerReq{
		Uid: uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserInteractViewPer failed uid:%d in:%v err:%v", uid, in, err)
		return out, err
	}

	out.IsOpen = signResp.GetIsOpen()

	log.DebugWithCtx(ctx, "GetUserInteractInfo end uid:%d in:%v out:%v", uid, in, out)
	return out, nil
}

func (s *ContractLogicImpl) SetUserInteractViewPer(ctx context.Context, in *pb.SetUserInteractViewPerRequest) (*pb.SetUserInteractViewPerResponse, error) {
	out := &pb.SetUserInteractViewPerResponse{}

	serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
	uid := serviceInfo.UserID

	_, err := s.signAnchorStatCli.SetUserInteractViewPer(ctx, &signAnchorPb.SetUserInteractViewPerReq{
		Uid:    uid,
		IsOpen: in.GetIsOpen(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SetUserInteractViewPer failed uid:%d in:%v err:%v", uid, in, err)
		return out, err
	}

	log.DebugWithCtx(ctx, "SetUserInteractViewPer end uid:%d in:%v out:%v", uid, in, out)
	return out, nil
}

func (s *ContractLogicImpl) GetMultiPlayerHallTaskEntry(ctx context.Context, in *pb.GetMultiPlayerHallTaskEntryRequest) (
	*pb.GetMultiPlayerHallTaskEntryResponse, error) {
	out := &pb.GetMultiPlayerHallTaskEntryResponse{}
	si, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetMultiPlayerHallTaskEntry ctx:%+v", ctx)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	uid := si.UserID
	channelId := in.ChannelId
	defer func() {
		log.DebugfWithCtx(ctx, "GetMultiPlayerHallTaskEntry uid=%d cid=%d si=%s out=%s", uid, channelId, si.String(), out.String())
	}()

	if channelId == 0 {
		return out, nil
	}

	// 先查签约过滤一下多人互动
	contract, err := s.anchorContractCli.GetUserContractCacheInfo(ctx, 0, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiPlayerHallTaskEntry failed to GetUserContractCacheInfo %v, uid %d", err, uid)
		return out, err
	}
	log.DebugfWithCtx(ctx, "GetMultiPlayerHallTaskEntry uid=%d GetUserContractCacheInfo=%s", uid, contract.String())
	if contract.GetContract().GetGuildId() == 0 || len(contract.GetAnchorIdentityList()) == 0 {
		return out, nil
	}
	var multiPlayer = false
	for _, identity := range contract.GetAnchorIdentityList() {
		if identity == uint32(anchorcontractpb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER) {
			multiPlayer = true
			break
		}
	}
	if !multiPlayer {
		return out, nil
	}

	// 查任务详情
	task, err := s.signAnchorStatCli.GetHallTaskCacheInfo(ctx, uid, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiPlayerHallTaskEntry failed to GetHallTask %v, uid %d", err, uid)
		return out, err
	}
	log.DebugfWithCtx(ctx, "GetMultiPlayerHallTaskEntry uid=%d GetHallTask=%s", uid, task.String())
	if task.GetJumpUrl() == "" {
		return out, nil
	}
	for _, info := range task.DayTaskList {
		if info.Rate == 1 {
			continue
		}
		out.List = append(out.List, &pb.MultiPlayerHallTask{
			TaskName:     info.TaskName,
			TaskProgress: info.TaskProgress,
			Rate:         info.Rate,
		})
	}
	for _, info := range task.WeekTaskList {
		if info.Rate == 1 {
			continue
		}
		out.List = append(out.List, &pb.MultiPlayerHallTask{
			TaskName:     info.TaskName,
			TaskProgress: info.TaskProgress,
			Rate:         info.Rate,
		})
	}
	out.JumpUrl = task.JumpUrl
	out.IsDone = len(out.List) == 0
	return out, nil
}

func (s *ContractLogicImpl) GetContractInfo(ctx context.Context, in *auth.GetContractInfoReq) (*auth.GetContractInfoResp, error) {
	out := &auth.GetContractInfoResp{
		ContractStatus: 0,
		StatusTip:      "",
	}
	si, _ := protogrpc.ServiceInfoFromContext(ctx)

	contract, err := s.anchorContractCli.GetUserContractCacheInfo(ctx, 0, in.ActorUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetContractInfo GetUserContractCacheInfo fail %v, uid=%d", err, in.ActorUid)
		return out, err
	}

	if contract.GetContract().GetGuildId() > 0 && contract.GetContract().GetExpireTime() > uint32(time.Now().Unix()) {

		guildId := contract.GetContract().GetGuildId()
		guildInfo, err := s.guildCli.GetGuild(ctx, guildId)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetContractInfo GetGuild fail %v, uid=%d", err, in.ActorUid)
			return out, nil
		}

		out.ContractStatus = uint32(auth.ENUM_ContractStatus_ENUM_ContractStatus_Sign)
		out.StatusTip = "已签约"
		out.ContractInfo = &auth.ContractInfo{
			GuildId:        guildId,
			GuildDispalyId: guildInfo.GetShortId(),
			GuildName:      guildInfo.GetName(),
		}

		if len(contract.GetAnchorIdentityList()) == 1 && contract.GetAnchorIdentityList()[0] == uint32(anchorcontractpb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_E_SPORTS) {
			log.InfoWithCtx(ctx, "GetContractInfo ignore esport. uid=%d", in.ActorUid)
			//return out, nil
		} else {
			out.AnchorIdentityList = contract.GetAnchorIdentityList()
		}

	}

	log.DebugWithCtx(ctx, "GetContractInfo si=%s uid=%d out=%s", si.String(), in.ActorUid, out.String())
	return out, nil
}

func (s *ContractLogicImpl) ContractClaimObsToken(c context.Context, req *pb.ContractClaimObsTokenRequest) (*pb.ContractClaimObsTokenResponse, error) {
	resp, err := s.anchorContractCli.ContractClaimObsToken(c, &anchorcontractpb.ContractClaimObsTokenReq{
		Expiration: uint32(req.GetExpiration()),
	})

	if err != nil {
		log.ErrorWithCtx(c, "ContractClaimObsToken failed req:%v err:%v", req, err)
		return &pb.ContractClaimObsTokenResponse{}, err
	}
	return &pb.ContractClaimObsTokenResponse{
		Token:    resp.GetToken(),
		ExpireAt: resp.GetExpireAt(),
	}, err
}
