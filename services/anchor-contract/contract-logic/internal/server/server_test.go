package server

import (
	"context"
	"encoding/json"

	//"fmt"
	"reflect"
	"testing"

	//"time"

	"github.com/golang/mock/gomock"
	anchorcontractgo "golang.52tt.com/clients/anchorcontract-go"
	"golang.52tt.com/clients/mocks/account"
	mocksanchorcontractgo "golang.52tt.com/clients/mocks/anchorcontract-go"
	sign_anchor_stats "golang.52tt.com/clients/mocks/sign-anchor-stats"
	sign_anchor_stats2 "golang.52tt.com/clients/sign-anchor-stats"

	//"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/protocol/grpc"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app"
	pb "golang.52tt.com/protocol/app/contract-logic"
	accountPB "golang.52tt.com/protocol/services/accountsvr"
	anchorcontractpb "golang.52tt.com/protocol/services/anchorcontract-go"
	signAnchorPb "golang.52tt.com/protocol/services/sign-anchor-stats"
	"golang.52tt.com/services/anchor-contract/contract-logic/internal/conf"
	//grpc2 "golang.52tt.com/services/channelrank-group/channel-rank-logic/internal/tool/grpc"
	//"google.golang.org/grpc/metadata"
)

/*
// go test -timeout 30s -run ^TestGetMultiPlayerHallTaskEntryx$ golang.52tt.com/services/anchor-contract/contract-logic/server -v -count=1
func TestGetMultiPlayerHallTaskEntryx(t *testing.T) {
	return

	addr := "*************:80"
	authority := "testing-apiv2.ttyuyin.com"
	cmd := uint32(32010)
	method := "/ga.api.contract.ContractLogicImplService/GetMultiPlayerHallTaskEntry"
	uid := uint32(2523781)
	cid := uint32(2258709)
	req := &pb.GetMultiPlayerHallTaskEntryRequest{
		BaseReq:   &app.BaseReq{},
		ChannelId: cid,
	}
	resp := &pb.GetMultiPlayerHallTaskEntryResponse{}

	cc, err := grpc2.NewGrpcClient(addr, authority)
	if err != nil {
		return
	}
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()
	// x-qw-traffic-mark:quicksilver-main-subenv
	ctx = metadata.AppendToOutgoingContext(ctx, "x-qw-traffic-mark", "quicksilver-main-subenv")
	err = cc.Invoke(ctx, method, uint32(uid), cmd, req, resp)
	if err != nil {
		return
	}
	fmt.Printf("resp=\n%s\n", grpc2.Json(resp))
}
*/

func Json(in interface{}) string {
	s, _ := json.MarshalIndent(in, "", "  ")
	return string(s)
}

func TestContractLogicImpl_CheckUserInteractEntry(t *testing.T) {
	type fields struct {
		signAnchorStatCli *sign_anchor_stats.MockIClient
	}
	type args struct {
		ctx context.Context
		in  *pb.CheckUserInteractEntryRequest
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockSign := sign_anchor_stats.NewMockIClient(ctl)

	gomock.InOrder(
		mockSign.EXPECT().CheckUserInteractEntry(gomock.Any(), gomock.Any()).Return(&signAnchorPb.CheckUserInteractEntryResp{
			IsHasEntry: false,
			EntryText:  "ddd",
		}, nil),
	)

	ctx := protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{
		SessionKey:    nil,
		DeviceID:      nil,
		ClientType:    0,
		TerminalType:  0,
		UserID:        2212797,
		ClientIP:      0,
		CommandID:     0,
		ClientVersion: 0,
		MarketID:      0,
		IsRobot:       false,
		RequestID:     "",
		DyeId:         0,
		PrefixDict:    nil,
	})

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.CheckUserInteractEntryResponse
		wantErr bool
	}{
		{
			name:   "CheckUserInteractEntry",
			fields: fields{signAnchorStatCli: mockSign},
			args: args{
				ctx: ctx,
				in: &pb.CheckUserInteractEntryRequest{
					BaseReq:     nil,
					SceneType:   3,
					InteractUid: 2212797,
				},
			},
			want: &pb.CheckUserInteractEntryResponse{
				BaseResp:   nil,
				IsHasEntry: false,
				EntryText:  "ddd",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ContractLogicImpl{
				signAnchorStatCli: tt.fields.signAnchorStatCli,
			}
			got, err := s.CheckUserInteractEntry(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckUserInteractEntry() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CheckUserInteractEntry() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestContractLogicImpl_GetUserInteractInfo(t *testing.T) {
	type fields struct {
		signAnchorStatCli *sign_anchor_stats.MockIClient
		accountCli        *account.MockIClient
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockSign := sign_anchor_stats.NewMockIClient(ctl)
	mockAccount := account.NewMockIClient(ctl)

	gomock.InOrder(
		mockSign.EXPECT().GetUserInteractInfo(gomock.Any(), gomock.Any()).Return(&signAnchorPb.GetUserInteractInfoResp{
			UserType: 1,
			InfoList: []*signAnchorPb.InteractInfo{
				&signAnchorPb.InteractInfo{
					InteractType:  "互动时长",
					InteractValue: "1分钟",
				},
			},
			CertInfo: nil,
		}, nil),
		mockAccount.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(map[uint32]*accountPB.UserResp{
			2212797: &accountPB.UserResp{Username: "111", Nickname: "111"},
			2185921: {Username: "222", Nickname: "222"},
		}, nil),
	)

	ctx := protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{
		SessionKey:    nil,
		DeviceID:      nil,
		ClientType:    0,
		TerminalType:  0,
		UserID:        2212797,
		ClientIP:      0,
		CommandID:     0,
		ClientVersion: 0,
		MarketID:      0,
		IsRobot:       false,
		RequestID:     "",
		DyeId:         0,
		PrefixDict:    nil,
	})

	type args struct {
		ctx context.Context
		in  *pb.GetUserInteractInfoRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetUserInteractInfoResponse
		wantErr bool
	}{
		{
			name: "GetUserInteractInfo",
			fields: fields{
				signAnchorStatCli: mockSign,
				accountCli:        mockAccount,
			},
			args: args{
				ctx: ctx,
				in: &pb.GetUserInteractInfoRequest{
					BaseReq:     nil,
					SceneType:   3,
					InteractUid: 2185921,
				},
			},
			want: &pb.GetUserInteractInfoResponse{
				BaseResp: nil,
				UserType: 1,
				InfoList: []*pb.InteractInfo{
					&pb.InteractInfo{
						InteractType:  "互动时长",
						InteractValue: "1分钟",
					},
				},
				CertInfo: &pb.InteractCertInfo{
					FansInfo: &app.ChannelLiveFansInfo{},
					CertMsg:  "",
				},
				Account:          "111",
				InteractAccount:  "222",
				InteractNickname: "222",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ContractLogicImpl{
				signAnchorStatCli: tt.fields.signAnchorStatCli,
				accountCli:        tt.fields.accountCli,
			}
			got, err := s.GetUserInteractInfo(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserInteractInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetUserInteractInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestContractLogicImpl_GetUserInteractViewPer(t *testing.T) {

	type fields struct {
		signAnchorStatCli *sign_anchor_stats.MockIClient
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockSign := sign_anchor_stats.NewMockIClient(ctl)

	gomock.InOrder(
		mockSign.EXPECT().GetUserInteractViewPer(gomock.Any(), gomock.Any()).Return(&signAnchorPb.GetUserInteractViewPerResp{
			IsOpen: false,
		}, nil),
	)

	ctx := protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{
		SessionKey:    nil,
		DeviceID:      nil,
		ClientType:    0,
		TerminalType:  0,
		UserID:        2212797,
		ClientIP:      0,
		CommandID:     0,
		ClientVersion: 0,
		MarketID:      0,
		IsRobot:       false,
		RequestID:     "",
		DyeId:         0,
		PrefixDict:    nil,
	})

	type args struct {
		ctx context.Context
		in  *pb.GetUserInteractViewPerRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetUserInteractViewPerResponse
		wantErr bool
	}{
		{
			name:   "GetUserInteractViewPer",
			fields: fields{signAnchorStatCli: mockSign},
			args: args{
				ctx: ctx,
				in: &pb.GetUserInteractViewPerRequest{
					BaseReq: nil,
				},
			},
			want: &pb.GetUserInteractViewPerResponse{
				BaseResp: nil,
				IsOpen:   false,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ContractLogicImpl{
				signAnchorStatCli: tt.fields.signAnchorStatCli,
			}
			got, err := s.GetUserInteractViewPer(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserInteractViewPer() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetUserInteractViewPer() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestContractLogicImpl_SetUserInteractViewPer(t *testing.T) {
	type fields struct {
		signAnchorStatCli *sign_anchor_stats.MockIClient
	}
	type args struct {
		ctx context.Context
		in  *pb.SetUserInteractViewPerRequest
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockSign := sign_anchor_stats.NewMockIClient(ctl)

	gomock.InOrder(
		mockSign.EXPECT().SetUserInteractViewPer(gomock.Any(), gomock.Any()),
	)

	ctx := protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{
		SessionKey:    nil,
		DeviceID:      nil,
		ClientType:    0,
		TerminalType:  0,
		UserID:        2212797,
		ClientIP:      0,
		CommandID:     0,
		ClientVersion: 0,
		MarketID:      0,
		IsRobot:       false,
		RequestID:     "",
		DyeId:         0,
		PrefixDict:    nil,
	})

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.SetUserInteractViewPerResponse
		wantErr bool
	}{
		{
			name:   "SetUserInteractViewPer",
			fields: fields{signAnchorStatCli: mockSign},
			args: args{
				ctx: ctx,
				in: &pb.SetUserInteractViewPerRequest{
					BaseReq: nil,
					IsOpen:  false,
				},
			},
			want: &pb.SetUserInteractViewPerResponse{
				BaseResp: nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ContractLogicImpl{
				signAnchorStatCli: tt.fields.signAnchorStatCli,
			}
			got, err := s.SetUserInteractViewPer(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("SetUserInteractViewPer() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SetUserInteractViewPer() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNewContractLogicImpl(t *testing.T) {
	type args struct {
		config *StartConfig
	}
	tests := []struct {
		name    string
		args    args
		want    *ContractLogicImpl
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewContractLogicImpl(tt.args.config)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewContractLogicImpl() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewContractLogicImpl() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestContractLogicImpl_GetMultiPlayerHallTaskEntry(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mocksignCli := sign_anchor_stats.NewMockIClient(ctl)
	mockanchorcontractCli := mocksanchorcontractgo.NewMockIClient(ctl)

	uid := uint32(123)
	ctx := context.Background()
	ctx = grpc.WithServiceInfo(ctx, &grpc.ServiceInfo{
		UserID: uid,
	})
	contractpb := &anchorcontractpb.ContractCacheInfo{
		Contract: &anchorcontractpb.ContractInfo{
			GuildId: 1,
		},
		AnchorIdentityList: []uint32{0},
	}
	taskResp := &signAnchorPb.GetHallTaskResp{
		JumpUrl:      "1",
		DayTaskList:  []*signAnchorPb.HallTaskDetial{{TaskName: "1"}},
		WeekTaskList: []*signAnchorPb.HallTaskDetial{{TaskName: "1"}},
	}

	gomock.InOrder(
		mockanchorcontractCli.EXPECT().GetUserContractCacheInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(contractpb, nil),
		mocksignCli.EXPECT().GetHallTaskCacheInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(taskResp, nil),
	)

	type fields struct {
		sc                *conf.ServiceConfigT
		signAnchorStatCli sign_anchor_stats2.IClient
		anchorContractCli anchorcontractgo.IClient
	}
	type args struct {
		ctx context.Context
		in  *pb.GetMultiPlayerHallTaskEntryRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetMultiPlayerHallTaskEntryResponse
		wantErr bool
	}{
		{
			fields: fields{
				anchorContractCli: mockanchorcontractCli,
				signAnchorStatCli: mocksignCli,
			},
			args: args{ctx: ctx, in: &pb.GetMultiPlayerHallTaskEntryRequest{ChannelId: 1}},
			want: &pb.GetMultiPlayerHallTaskEntryResponse{
				JumpUrl: "1",
				List: []*pb.MultiPlayerHallTask{
					{
						TaskName: "1",
					},
					{
						TaskName: "1",
					}},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ContractLogicImpl{
				sc:                tt.fields.sc,
				signAnchorStatCli: tt.fields.signAnchorStatCli,

				anchorContractCli: tt.fields.anchorContractCli,
			}
			got, err := s.GetMultiPlayerHallTaskEntry(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("ContractLogicImpl.GetMultiPlayerHallTaskEntry() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ContractLogicImpl.GetMultiPlayerHallTaskEntry() = %v, want %v", got, tt.want)
			}
		})
	}
}
