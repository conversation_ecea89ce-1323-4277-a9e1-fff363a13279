syntax="proto3";

package fcu;


option go_package="golang.52tt.com/protocol/services/flow_controller_unit";
import "google/api/annotations.proto";

// 发布检测接口
// 示例: curl flow-controller-unit.cn-bj.rcmd-tt-testing.skyengine.net.cn:8000/deploy-check -H 'Content-type: application/json' \
//   -d '{"target_env":"prod","service_name":"pqv2","current_tag":"V20221115182819-testing-67d172a3b","target_tag":"V20221115182819-testing-b3a271d76"}'
service Fcu {
  rpc DeployCheck(DeployCheckReq) returns(DeployCheckRsp) {
    option (google.api.http) = {
      post: "/deploy-check",
      body: "*",
    };
  }

}


message DeployCheckReq {
  // 发布环境
  string target_env = 1;
  // 服务名
  string service_name = 2;
  // 服务所属仓库
  string repo = 5;
  // 当前image tag
  string current_tag = 3;
  // 目标image tag
  string target_tag = 4;
}

message DeployCheckRsp {
  // 检查是否通过, true 通过
  bool ok = 1;
  // 如果不通过, 简短说明原因
  string message = 2;
}
