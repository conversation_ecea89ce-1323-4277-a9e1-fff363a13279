syntax = "proto3";

package ga.game_tmp_channel;

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/game_tmp_channel";

//赛事按钮信息
message ButtonInfo {
    enum ButtonStatus {
        Hide = 0;
        Unclickable = 1;
        Clickable = 2;
    }
    ButtonStatus button_status = 1;
    string text = 2;
    string url = 3;
}
//玩家信息
message PlayerLabel {
    uint32 uid = 1;
    string label = 2;
    string ttid = 3; //转换为ttid给客户端用
}
//赛事信息
message ContestInfo {
    string title = 1;
    string sub_title = 2;
    string url = 3;
    string begin_time = 4;
}
//房间公告
message AnnouncementInfo {
    string title = 1;
    string content = 2;
}


//赛事信息变更通知
message ContestInfoChangeNotify {
    ContestInfo contest_info = 1;
}
//玩家名单变更通知
message PlayerChangeNotify {
    uint32 capacity = 1; //队伍最大人数
    repeated PlayerLabel players = 2;
}
//赛事按钮变更通知
message ButtonChangeNotify {
    ButtonInfo button_info = 1;
}
