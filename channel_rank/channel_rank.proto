syntax = "proto3";

package ga.channel_rank;

import "ga_base.proto";
 
option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/channel_rank";


message GetLiveFansWeekRankRequest {
    ga.BaseReq base_req = 1;
    uint32 anchor_uid=2;

    // 获取本周周榜才传以下字段
    uint32 begin_id = 3;  // 开始查询的索引编号 用于翻页 第一页可以填0
    uint32 req_cnt = 4;  // 每次获取列表最多需要多少条 一般填50
}
message GetLiveFansWeekRankResponse {
    ga.BaseResp base_resp = 1;
    repeated LiveFansWeekRankInfo last_week_top3=2; // 上周TOP3
    repeated LiveFansWeekRankInfo this_week_rank=3; // 本周周榜

    bool in_settle=4; // 如果为true,表示上周top3在结算中,展示默认图

    LiveFansWeekRankInfo my_info = 5; // 我的信息
}
message LiveFansWeekRankInfo {
    ga.UserProfile user_info = 1; 
    uint32 rank=2; // 我的排名，为0则不上榜
    uint32 rank_value=3;  // 榜单数值

    // my_info里才有值
    uint32 d_value=4; // 差值, 如果是第一名，就是比第二名高多少; 否则就是距上一名还差多少，或者距上榜还差多少

    // 仅获取本周周榜会填充以下字段
    uint32 nobility_level=5; //贵族等级 6.49.0客户端开始使用新的nobility_info字段
    uint32 rich=6; // 财富等级
    uint32 charm=7; // 魅力等级
    uint32 channel_mem_level=8; //房间等级
    uint32 group_fans_level=9;// 粉丝等级
    ga.FansPlateInfo plate_info=10;// 粉丝铭牌信息
    string group_name = 11;                   // 团名
    ga.NobilityInfo nobility_info = 12;     // 贵族信息

}