// Code generated by protoc-gen-go. DO NOT EDIT.
// source: wealth_god_logic/wealth_god_logic.proto

package wealth_god_logic // import "golang.52tt.com/protocol/app/wealth_god_logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type WealthGodBoxType int32

const (
	WealthGodBoxType_WEALTH_GOD_BOX_TYPE_UNSPECIFIED WealthGodBoxType = 0
	WealthGodBoxType_WEALTH_GOD_BOX_TYPE_S           WealthGodBoxType = 1
	WealthGodBoxType_WEALTH_GOD_BOX_TYPE_A_PLUS      WealthGodBoxType = 2
	WealthGodBoxType_WEALTH_GOD_BOX_TYPE_A           WealthGodBoxType = 3
)

var WealthGodBoxType_name = map[int32]string{
	0: "WEALTH_GOD_BOX_TYPE_UNSPECIFIED",
	1: "WEALTH_GOD_BOX_TYPE_S",
	2: "WEALTH_GOD_BOX_TYPE_A_PLUS",
	3: "WEALTH_GOD_BOX_TYPE_A",
}
var WealthGodBoxType_value = map[string]int32{
	"WEALTH_GOD_BOX_TYPE_UNSPECIFIED": 0,
	"WEALTH_GOD_BOX_TYPE_S":           1,
	"WEALTH_GOD_BOX_TYPE_A_PLUS":      2,
	"WEALTH_GOD_BOX_TYPE_A":           3,
}

func (x WealthGodBoxType) String() string {
	return proto.EnumName(WealthGodBoxType_name, int32(x))
}
func (WealthGodBoxType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_logic_b557952d03dcf17e, []int{0}
}

type WealthMissionType int32

const (
	WealthMissionType_WEALTH_MISSION_TYPE_UNSPECIFIED     WealthMissionType = 0
	WealthMissionType_WEALTH_MISSION_TYPE_STAY_IN_GAME    WealthMissionType = 1
	WealthMissionType_WEALTH_MISSION_TYPE_SEND_TBEAN_GIFT WealthMissionType = 2
)

var WealthMissionType_name = map[int32]string{
	0: "WEALTH_MISSION_TYPE_UNSPECIFIED",
	1: "WEALTH_MISSION_TYPE_STAY_IN_GAME",
	2: "WEALTH_MISSION_TYPE_SEND_TBEAN_GIFT",
}
var WealthMissionType_value = map[string]int32{
	"WEALTH_MISSION_TYPE_UNSPECIFIED":     0,
	"WEALTH_MISSION_TYPE_STAY_IN_GAME":    1,
	"WEALTH_MISSION_TYPE_SEND_TBEAN_GIFT": 2,
}

func (x WealthMissionType) String() string {
	return proto.EnumName(WealthMissionType_name, int32(x))
}
func (WealthMissionType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_logic_b557952d03dcf17e, []int{1}
}

type WealthGod struct {
	GodId                     string   `protobuf:"bytes,1,opt,name=god_id,json=godId,proto3" json:"god_id,omitempty"`
	ChannelId                 uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	StartTs                   int64    `protobuf:"varint,3,opt,name=start_ts,json=startTs,proto3" json:"start_ts,omitempty"`
	EndTs                     int64    `protobuf:"varint,4,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	MissionEndTs              int64    `protobuf:"varint,5,opt,name=mission_end_ts,json=missionEndTs,proto3" json:"mission_end_ts,omitempty"`
	IsStayRoomMissionFinished bool     `protobuf:"varint,6,opt,name=is_stay_room_mission_finished,json=isStayRoomMissionFinished,proto3" json:"is_stay_room_mission_finished,omitempty"`
	XXX_NoUnkeyedLiteral      struct{} `json:"-"`
	XXX_unrecognized          []byte   `json:"-"`
	XXX_sizecache             int32    `json:"-"`
}

func (m *WealthGod) Reset()         { *m = WealthGod{} }
func (m *WealthGod) String() string { return proto.CompactTextString(m) }
func (*WealthGod) ProtoMessage()    {}
func (*WealthGod) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_logic_b557952d03dcf17e, []int{0}
}
func (m *WealthGod) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WealthGod.Unmarshal(m, b)
}
func (m *WealthGod) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WealthGod.Marshal(b, m, deterministic)
}
func (dst *WealthGod) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WealthGod.Merge(dst, src)
}
func (m *WealthGod) XXX_Size() int {
	return xxx_messageInfo_WealthGod.Size(m)
}
func (m *WealthGod) XXX_DiscardUnknown() {
	xxx_messageInfo_WealthGod.DiscardUnknown(m)
}

var xxx_messageInfo_WealthGod proto.InternalMessageInfo

func (m *WealthGod) GetGodId() string {
	if m != nil {
		return m.GodId
	}
	return ""
}

func (m *WealthGod) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *WealthGod) GetStartTs() int64 {
	if m != nil {
		return m.StartTs
	}
	return 0
}

func (m *WealthGod) GetEndTs() int64 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *WealthGod) GetMissionEndTs() int64 {
	if m != nil {
		return m.MissionEndTs
	}
	return 0
}

func (m *WealthGod) GetIsStayRoomMissionFinished() bool {
	if m != nil {
		return m.IsStayRoomMissionFinished
	}
	return false
}

type WealthGodBox struct {
	BoxType              uint32                       `protobuf:"varint,1,opt,name=box_type,json=boxType,proto3" json:"box_type,omitempty"`
	RewardList           []*WealthGodBoxRewardPreview `protobuf:"bytes,2,rep,name=reward_list,json=rewardList,proto3" json:"reward_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *WealthGodBox) Reset()         { *m = WealthGodBox{} }
func (m *WealthGodBox) String() string { return proto.CompactTextString(m) }
func (*WealthGodBox) ProtoMessage()    {}
func (*WealthGodBox) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_logic_b557952d03dcf17e, []int{1}
}
func (m *WealthGodBox) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WealthGodBox.Unmarshal(m, b)
}
func (m *WealthGodBox) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WealthGodBox.Marshal(b, m, deterministic)
}
func (dst *WealthGodBox) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WealthGodBox.Merge(dst, src)
}
func (m *WealthGodBox) XXX_Size() int {
	return xxx_messageInfo_WealthGodBox.Size(m)
}
func (m *WealthGodBox) XXX_DiscardUnknown() {
	xxx_messageInfo_WealthGodBox.DiscardUnknown(m)
}

var xxx_messageInfo_WealthGodBox proto.InternalMessageInfo

func (m *WealthGodBox) GetBoxType() uint32 {
	if m != nil {
		return m.BoxType
	}
	return 0
}

func (m *WealthGodBox) GetRewardList() []*WealthGodBoxRewardPreview {
	if m != nil {
		return m.RewardList
	}
	return nil
}

type GetWealthGodEntryRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetWealthGodEntryRequest) Reset()         { *m = GetWealthGodEntryRequest{} }
func (m *GetWealthGodEntryRequest) String() string { return proto.CompactTextString(m) }
func (*GetWealthGodEntryRequest) ProtoMessage()    {}
func (*GetWealthGodEntryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_logic_b557952d03dcf17e, []int{2}
}
func (m *GetWealthGodEntryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWealthGodEntryRequest.Unmarshal(m, b)
}
func (m *GetWealthGodEntryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWealthGodEntryRequest.Marshal(b, m, deterministic)
}
func (dst *GetWealthGodEntryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWealthGodEntryRequest.Merge(dst, src)
}
func (m *GetWealthGodEntryRequest) XXX_Size() int {
	return xxx_messageInfo_GetWealthGodEntryRequest.Size(m)
}
func (m *GetWealthGodEntryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWealthGodEntryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWealthGodEntryRequest proto.InternalMessageInfo

func (m *GetWealthGodEntryRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetWealthGodEntryRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetWealthGodEntryResponse struct {
	BaseResp                  *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	CurrentChannelGodList     []*WealthGod  `protobuf:"bytes,2,rep,name=current_channel_god_list,json=currentChannelGodList,proto3" json:"current_channel_god_list,omitempty"`
	LastGodEndTs              int64         `protobuf:"varint,3,opt,name=last_god_end_ts,json=lastGodEndTs,proto3" json:"last_god_end_ts,omitempty"`
	ActivityIsOpen            bool          `protobuf:"varint,4,opt,name=activity_is_open,json=activityIsOpen,proto3" json:"activity_is_open,omitempty"`
	IsStayRoomMissionFinished bool          `protobuf:"varint,5,opt,name=is_stay_room_mission_finished,json=isStayRoomMissionFinished,proto3" json:"is_stay_room_mission_finished,omitempty"` // Deprecated: Do not use.
	XXX_NoUnkeyedLiteral      struct{}      `json:"-"`
	XXX_unrecognized          []byte        `json:"-"`
	XXX_sizecache             int32         `json:"-"`
}

func (m *GetWealthGodEntryResponse) Reset()         { *m = GetWealthGodEntryResponse{} }
func (m *GetWealthGodEntryResponse) String() string { return proto.CompactTextString(m) }
func (*GetWealthGodEntryResponse) ProtoMessage()    {}
func (*GetWealthGodEntryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_logic_b557952d03dcf17e, []int{3}
}
func (m *GetWealthGodEntryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWealthGodEntryResponse.Unmarshal(m, b)
}
func (m *GetWealthGodEntryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWealthGodEntryResponse.Marshal(b, m, deterministic)
}
func (dst *GetWealthGodEntryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWealthGodEntryResponse.Merge(dst, src)
}
func (m *GetWealthGodEntryResponse) XXX_Size() int {
	return xxx_messageInfo_GetWealthGodEntryResponse.Size(m)
}
func (m *GetWealthGodEntryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWealthGodEntryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWealthGodEntryResponse proto.InternalMessageInfo

func (m *GetWealthGodEntryResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetWealthGodEntryResponse) GetCurrentChannelGodList() []*WealthGod {
	if m != nil {
		return m.CurrentChannelGodList
	}
	return nil
}

func (m *GetWealthGodEntryResponse) GetLastGodEndTs() int64 {
	if m != nil {
		return m.LastGodEndTs
	}
	return 0
}

func (m *GetWealthGodEntryResponse) GetActivityIsOpen() bool {
	if m != nil {
		return m.ActivityIsOpen
	}
	return false
}

// Deprecated: Do not use.
func (m *GetWealthGodEntryResponse) GetIsStayRoomMissionFinished() bool {
	if m != nil {
		return m.IsStayRoomMissionFinished
	}
	return false
}

// 获取一个财神降临房间
type GetOneWealthGodChannelReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetOneWealthGodChannelReq) Reset()         { *m = GetOneWealthGodChannelReq{} }
func (m *GetOneWealthGodChannelReq) String() string { return proto.CompactTextString(m) }
func (*GetOneWealthGodChannelReq) ProtoMessage()    {}
func (*GetOneWealthGodChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_logic_b557952d03dcf17e, []int{4}
}
func (m *GetOneWealthGodChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOneWealthGodChannelReq.Unmarshal(m, b)
}
func (m *GetOneWealthGodChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOneWealthGodChannelReq.Marshal(b, m, deterministic)
}
func (dst *GetOneWealthGodChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOneWealthGodChannelReq.Merge(dst, src)
}
func (m *GetOneWealthGodChannelReq) XXX_Size() int {
	return xxx_messageInfo_GetOneWealthGodChannelReq.Size(m)
}
func (m *GetOneWealthGodChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOneWealthGodChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOneWealthGodChannelReq proto.InternalMessageInfo

func (m *GetOneWealthGodChannelReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetOneWealthGodChannelResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChannelId            uint32        `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetOneWealthGodChannelResp) Reset()         { *m = GetOneWealthGodChannelResp{} }
func (m *GetOneWealthGodChannelResp) String() string { return proto.CompactTextString(m) }
func (*GetOneWealthGodChannelResp) ProtoMessage()    {}
func (*GetOneWealthGodChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_logic_b557952d03dcf17e, []int{5}
}
func (m *GetOneWealthGodChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOneWealthGodChannelResp.Unmarshal(m, b)
}
func (m *GetOneWealthGodChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOneWealthGodChannelResp.Marshal(b, m, deterministic)
}
func (dst *GetOneWealthGodChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOneWealthGodChannelResp.Merge(dst, src)
}
func (m *GetOneWealthGodChannelResp) XXX_Size() int {
	return xxx_messageInfo_GetOneWealthGodChannelResp.Size(m)
}
func (m *GetOneWealthGodChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOneWealthGodChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOneWealthGodChannelResp proto.InternalMessageInfo

func (m *GetOneWealthGodChannelResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetOneWealthGodChannelResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetWealthGodActivityInfoRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetWealthGodActivityInfoRequest) Reset()         { *m = GetWealthGodActivityInfoRequest{} }
func (m *GetWealthGodActivityInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetWealthGodActivityInfoRequest) ProtoMessage()    {}
func (*GetWealthGodActivityInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_logic_b557952d03dcf17e, []int{6}
}
func (m *GetWealthGodActivityInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWealthGodActivityInfoRequest.Unmarshal(m, b)
}
func (m *GetWealthGodActivityInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWealthGodActivityInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetWealthGodActivityInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWealthGodActivityInfoRequest.Merge(dst, src)
}
func (m *GetWealthGodActivityInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetWealthGodActivityInfoRequest.Size(m)
}
func (m *GetWealthGodActivityInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWealthGodActivityInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWealthGodActivityInfoRequest proto.InternalMessageInfo

func (m *GetWealthGodActivityInfoRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetWealthGodActivityInfoResponse struct {
	BaseResp             *app.BaseResp                                        `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	TriggerList          []*GetWealthGodActivityInfoResponse_WealthGodTrigger `protobuf:"bytes,2,rep,name=trigger_list,json=triggerList,proto3" json:"trigger_list,omitempty"`
	PlayingInstruction   string                                               `protobuf:"bytes,3,opt,name=playing_instruction,json=playingInstruction,proto3" json:"playing_instruction,omitempty"`
	GodBoxTitle          string                                               `protobuf:"bytes,4,opt,name=god_box_title,json=godBoxTitle,proto3" json:"god_box_title,omitempty"`
	GodBoxDesc           string                                               `protobuf:"bytes,5,opt,name=god_box_desc,json=godBoxDesc,proto3" json:"god_box_desc,omitempty"`
	GodBoxIcon           string                                               `protobuf:"bytes,6,opt,name=god_box_icon,json=godBoxIcon,proto3" json:"god_box_icon,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                             `json:"-"`
	XXX_unrecognized     []byte                                               `json:"-"`
	XXX_sizecache        int32                                                `json:"-"`
}

func (m *GetWealthGodActivityInfoResponse) Reset()         { *m = GetWealthGodActivityInfoResponse{} }
func (m *GetWealthGodActivityInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetWealthGodActivityInfoResponse) ProtoMessage()    {}
func (*GetWealthGodActivityInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_logic_b557952d03dcf17e, []int{7}
}
func (m *GetWealthGodActivityInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWealthGodActivityInfoResponse.Unmarshal(m, b)
}
func (m *GetWealthGodActivityInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWealthGodActivityInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetWealthGodActivityInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWealthGodActivityInfoResponse.Merge(dst, src)
}
func (m *GetWealthGodActivityInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetWealthGodActivityInfoResponse.Size(m)
}
func (m *GetWealthGodActivityInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWealthGodActivityInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWealthGodActivityInfoResponse proto.InternalMessageInfo

func (m *GetWealthGodActivityInfoResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetWealthGodActivityInfoResponse) GetTriggerList() []*GetWealthGodActivityInfoResponse_WealthGodTrigger {
	if m != nil {
		return m.TriggerList
	}
	return nil
}

func (m *GetWealthGodActivityInfoResponse) GetPlayingInstruction() string {
	if m != nil {
		return m.PlayingInstruction
	}
	return ""
}

func (m *GetWealthGodActivityInfoResponse) GetGodBoxTitle() string {
	if m != nil {
		return m.GodBoxTitle
	}
	return ""
}

func (m *GetWealthGodActivityInfoResponse) GetGodBoxDesc() string {
	if m != nil {
		return m.GodBoxDesc
	}
	return ""
}

func (m *GetWealthGodActivityInfoResponse) GetGodBoxIcon() string {
	if m != nil {
		return m.GodBoxIcon
	}
	return ""
}

type GetWealthGodActivityInfoResponse_WealthGodTrigger struct {
	TriggerType          uint32   `protobuf:"varint,1,opt,name=trigger_type,json=triggerType,proto3" json:"trigger_type,omitempty"`
	TriggerId            uint32   `protobuf:"varint,2,opt,name=trigger_id,json=triggerId,proto3" json:"trigger_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWealthGodActivityInfoResponse_WealthGodTrigger) Reset() {
	*m = GetWealthGodActivityInfoResponse_WealthGodTrigger{}
}
func (m *GetWealthGodActivityInfoResponse_WealthGodTrigger) String() string {
	return proto.CompactTextString(m)
}
func (*GetWealthGodActivityInfoResponse_WealthGodTrigger) ProtoMessage() {}
func (*GetWealthGodActivityInfoResponse_WealthGodTrigger) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_logic_b557952d03dcf17e, []int{7, 0}
}
func (m *GetWealthGodActivityInfoResponse_WealthGodTrigger) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWealthGodActivityInfoResponse_WealthGodTrigger.Unmarshal(m, b)
}
func (m *GetWealthGodActivityInfoResponse_WealthGodTrigger) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWealthGodActivityInfoResponse_WealthGodTrigger.Marshal(b, m, deterministic)
}
func (dst *GetWealthGodActivityInfoResponse_WealthGodTrigger) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWealthGodActivityInfoResponse_WealthGodTrigger.Merge(dst, src)
}
func (m *GetWealthGodActivityInfoResponse_WealthGodTrigger) XXX_Size() int {
	return xxx_messageInfo_GetWealthGodActivityInfoResponse_WealthGodTrigger.Size(m)
}
func (m *GetWealthGodActivityInfoResponse_WealthGodTrigger) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWealthGodActivityInfoResponse_WealthGodTrigger.DiscardUnknown(m)
}

var xxx_messageInfo_GetWealthGodActivityInfoResponse_WealthGodTrigger proto.InternalMessageInfo

func (m *GetWealthGodActivityInfoResponse_WealthGodTrigger) GetTriggerType() uint32 {
	if m != nil {
		return m.TriggerType
	}
	return 0
}

func (m *GetWealthGodActivityInfoResponse_WealthGodTrigger) GetTriggerId() uint32 {
	if m != nil {
		return m.TriggerId
	}
	return 0
}

type GetWealthGodDetailRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GodId                string       `protobuf:"bytes,3,opt,name=god_id,json=godId,proto3" json:"god_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetWealthGodDetailRequest) Reset()         { *m = GetWealthGodDetailRequest{} }
func (m *GetWealthGodDetailRequest) String() string { return proto.CompactTextString(m) }
func (*GetWealthGodDetailRequest) ProtoMessage()    {}
func (*GetWealthGodDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_logic_b557952d03dcf17e, []int{8}
}
func (m *GetWealthGodDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWealthGodDetailRequest.Unmarshal(m, b)
}
func (m *GetWealthGodDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWealthGodDetailRequest.Marshal(b, m, deterministic)
}
func (dst *GetWealthGodDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWealthGodDetailRequest.Merge(dst, src)
}
func (m *GetWealthGodDetailRequest) XXX_Size() int {
	return xxx_messageInfo_GetWealthGodDetailRequest.Size(m)
}
func (m *GetWealthGodDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWealthGodDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWealthGodDetailRequest proto.InternalMessageInfo

func (m *GetWealthGodDetailRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetWealthGodDetailRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetWealthGodDetailRequest) GetGodId() string {
	if m != nil {
		return m.GodId
	}
	return ""
}

type GetWealthGodDetailResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	GodInfo              *WealthGod    `protobuf:"bytes,2,opt,name=god_info,json=godInfo,proto3" json:"god_info,omitempty"`
	BoxInfo              *WealthGodBox `protobuf:"bytes,3,opt,name=box_info,json=boxInfo,proto3" json:"box_info,omitempty"`
	AvailableOpenCnt     uint32        `protobuf:"varint,4,opt,name=available_open_cnt,json=availableOpenCnt,proto3" json:"available_open_cnt,omitempty"`
	GetOpenCnt           uint32        `protobuf:"varint,5,opt,name=get_open_cnt,json=getOpenCnt,proto3" json:"get_open_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetWealthGodDetailResponse) Reset()         { *m = GetWealthGodDetailResponse{} }
func (m *GetWealthGodDetailResponse) String() string { return proto.CompactTextString(m) }
func (*GetWealthGodDetailResponse) ProtoMessage()    {}
func (*GetWealthGodDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_logic_b557952d03dcf17e, []int{9}
}
func (m *GetWealthGodDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWealthGodDetailResponse.Unmarshal(m, b)
}
func (m *GetWealthGodDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWealthGodDetailResponse.Marshal(b, m, deterministic)
}
func (dst *GetWealthGodDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWealthGodDetailResponse.Merge(dst, src)
}
func (m *GetWealthGodDetailResponse) XXX_Size() int {
	return xxx_messageInfo_GetWealthGodDetailResponse.Size(m)
}
func (m *GetWealthGodDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWealthGodDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWealthGodDetailResponse proto.InternalMessageInfo

func (m *GetWealthGodDetailResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetWealthGodDetailResponse) GetGodInfo() *WealthGod {
	if m != nil {
		return m.GodInfo
	}
	return nil
}

func (m *GetWealthGodDetailResponse) GetBoxInfo() *WealthGodBox {
	if m != nil {
		return m.BoxInfo
	}
	return nil
}

func (m *GetWealthGodDetailResponse) GetAvailableOpenCnt() uint32 {
	if m != nil {
		return m.AvailableOpenCnt
	}
	return 0
}

func (m *GetWealthGodDetailResponse) GetGetOpenCnt() uint32 {
	if m != nil {
		return m.GetOpenCnt
	}
	return 0
}

type WealthGodBoxRewardPreview struct {
	IsRare               bool     `protobuf:"varint,1,opt,name=is_rare,json=isRare,proto3" json:"is_rare,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	SubTitle             string   `protobuf:"bytes,3,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	IconUrl              string   `protobuf:"bytes,4,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	CornerText           string   `protobuf:"bytes,5,opt,name=corner_text,json=cornerText,proto3" json:"corner_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WealthGodBoxRewardPreview) Reset()         { *m = WealthGodBoxRewardPreview{} }
func (m *WealthGodBoxRewardPreview) String() string { return proto.CompactTextString(m) }
func (*WealthGodBoxRewardPreview) ProtoMessage()    {}
func (*WealthGodBoxRewardPreview) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_logic_b557952d03dcf17e, []int{10}
}
func (m *WealthGodBoxRewardPreview) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WealthGodBoxRewardPreview.Unmarshal(m, b)
}
func (m *WealthGodBoxRewardPreview) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WealthGodBoxRewardPreview.Marshal(b, m, deterministic)
}
func (dst *WealthGodBoxRewardPreview) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WealthGodBoxRewardPreview.Merge(dst, src)
}
func (m *WealthGodBoxRewardPreview) XXX_Size() int {
	return xxx_messageInfo_WealthGodBoxRewardPreview.Size(m)
}
func (m *WealthGodBoxRewardPreview) XXX_DiscardUnknown() {
	xxx_messageInfo_WealthGodBoxRewardPreview.DiscardUnknown(m)
}

var xxx_messageInfo_WealthGodBoxRewardPreview proto.InternalMessageInfo

func (m *WealthGodBoxRewardPreview) GetIsRare() bool {
	if m != nil {
		return m.IsRare
	}
	return false
}

func (m *WealthGodBoxRewardPreview) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *WealthGodBoxRewardPreview) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *WealthGodBoxRewardPreview) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *WealthGodBoxRewardPreview) GetCornerText() string {
	if m != nil {
		return m.CornerText
	}
	return ""
}

type OpenWealthGodBoxRewardRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GodId                string       `protobuf:"bytes,3,opt,name=god_id,json=godId,proto3" json:"god_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *OpenWealthGodBoxRewardRequest) Reset()         { *m = OpenWealthGodBoxRewardRequest{} }
func (m *OpenWealthGodBoxRewardRequest) String() string { return proto.CompactTextString(m) }
func (*OpenWealthGodBoxRewardRequest) ProtoMessage()    {}
func (*OpenWealthGodBoxRewardRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_logic_b557952d03dcf17e, []int{11}
}
func (m *OpenWealthGodBoxRewardRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpenWealthGodBoxRewardRequest.Unmarshal(m, b)
}
func (m *OpenWealthGodBoxRewardRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpenWealthGodBoxRewardRequest.Marshal(b, m, deterministic)
}
func (dst *OpenWealthGodBoxRewardRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenWealthGodBoxRewardRequest.Merge(dst, src)
}
func (m *OpenWealthGodBoxRewardRequest) XXX_Size() int {
	return xxx_messageInfo_OpenWealthGodBoxRewardRequest.Size(m)
}
func (m *OpenWealthGodBoxRewardRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenWealthGodBoxRewardRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OpenWealthGodBoxRewardRequest proto.InternalMessageInfo

func (m *OpenWealthGodBoxRewardRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *OpenWealthGodBoxRewardRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *OpenWealthGodBoxRewardRequest) GetGodId() string {
	if m != nil {
		return m.GodId
	}
	return ""
}

type OpenWealthGodBoxRewardResponse struct {
	BaseResp             *app.BaseResp                `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RewardList           []*WealthGodBoxRewardPreview `protobuf:"bytes,2,rep,name=reward_list,json=rewardList,proto3" json:"reward_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *OpenWealthGodBoxRewardResponse) Reset()         { *m = OpenWealthGodBoxRewardResponse{} }
func (m *OpenWealthGodBoxRewardResponse) String() string { return proto.CompactTextString(m) }
func (*OpenWealthGodBoxRewardResponse) ProtoMessage()    {}
func (*OpenWealthGodBoxRewardResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_logic_b557952d03dcf17e, []int{12}
}
func (m *OpenWealthGodBoxRewardResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpenWealthGodBoxRewardResponse.Unmarshal(m, b)
}
func (m *OpenWealthGodBoxRewardResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpenWealthGodBoxRewardResponse.Marshal(b, m, deterministic)
}
func (dst *OpenWealthGodBoxRewardResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenWealthGodBoxRewardResponse.Merge(dst, src)
}
func (m *OpenWealthGodBoxRewardResponse) XXX_Size() int {
	return xxx_messageInfo_OpenWealthGodBoxRewardResponse.Size(m)
}
func (m *OpenWealthGodBoxRewardResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenWealthGodBoxRewardResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OpenWealthGodBoxRewardResponse proto.InternalMessageInfo

func (m *OpenWealthGodBoxRewardResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *OpenWealthGodBoxRewardResponse) GetRewardList() []*WealthGodBoxRewardPreview {
	if m != nil {
		return m.RewardList
	}
	return nil
}

type ReportStayRoomMissionFinishRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	GodId                string       `protobuf:"bytes,2,opt,name=god_id,json=godId,proto3" json:"god_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ReportStayRoomMissionFinishRequest) Reset()         { *m = ReportStayRoomMissionFinishRequest{} }
func (m *ReportStayRoomMissionFinishRequest) String() string { return proto.CompactTextString(m) }
func (*ReportStayRoomMissionFinishRequest) ProtoMessage()    {}
func (*ReportStayRoomMissionFinishRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_logic_b557952d03dcf17e, []int{13}
}
func (m *ReportStayRoomMissionFinishRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportStayRoomMissionFinishRequest.Unmarshal(m, b)
}
func (m *ReportStayRoomMissionFinishRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportStayRoomMissionFinishRequest.Marshal(b, m, deterministic)
}
func (dst *ReportStayRoomMissionFinishRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportStayRoomMissionFinishRequest.Merge(dst, src)
}
func (m *ReportStayRoomMissionFinishRequest) XXX_Size() int {
	return xxx_messageInfo_ReportStayRoomMissionFinishRequest.Size(m)
}
func (m *ReportStayRoomMissionFinishRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportStayRoomMissionFinishRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ReportStayRoomMissionFinishRequest proto.InternalMessageInfo

func (m *ReportStayRoomMissionFinishRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ReportStayRoomMissionFinishRequest) GetGodId() string {
	if m != nil {
		return m.GodId
	}
	return ""
}

type ReportStayRoomMissionFinishResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ReportStayRoomMissionFinishResponse) Reset()         { *m = ReportStayRoomMissionFinishResponse{} }
func (m *ReportStayRoomMissionFinishResponse) String() string { return proto.CompactTextString(m) }
func (*ReportStayRoomMissionFinishResponse) ProtoMessage()    {}
func (*ReportStayRoomMissionFinishResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_logic_b557952d03dcf17e, []int{14}
}
func (m *ReportStayRoomMissionFinishResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportStayRoomMissionFinishResponse.Unmarshal(m, b)
}
func (m *ReportStayRoomMissionFinishResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportStayRoomMissionFinishResponse.Marshal(b, m, deterministic)
}
func (dst *ReportStayRoomMissionFinishResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportStayRoomMissionFinishResponse.Merge(dst, src)
}
func (m *ReportStayRoomMissionFinishResponse) XXX_Size() int {
	return xxx_messageInfo_ReportStayRoomMissionFinishResponse.Size(m)
}
func (m *ReportStayRoomMissionFinishResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportStayRoomMissionFinishResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ReportStayRoomMissionFinishResponse proto.InternalMessageInfo

func (m *ReportStayRoomMissionFinishResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 新生成财神通知
type NewWealthGodNotify struct {
	// god_info.mission_end_ts > 本地last_god_end_ts，则更新本地last_god_end_ts
	GodInfo              *WealthGod `protobuf:"bytes,1,opt,name=god_info,json=godInfo,proto3" json:"god_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *NewWealthGodNotify) Reset()         { *m = NewWealthGodNotify{} }
func (m *NewWealthGodNotify) String() string { return proto.CompactTextString(m) }
func (*NewWealthGodNotify) ProtoMessage()    {}
func (*NewWealthGodNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_logic_b557952d03dcf17e, []int{15}
}
func (m *NewWealthGodNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewWealthGodNotify.Unmarshal(m, b)
}
func (m *NewWealthGodNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewWealthGodNotify.Marshal(b, m, deterministic)
}
func (dst *NewWealthGodNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewWealthGodNotify.Merge(dst, src)
}
func (m *NewWealthGodNotify) XXX_Size() int {
	return xxx_messageInfo_NewWealthGodNotify.Size(m)
}
func (m *NewWealthGodNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_NewWealthGodNotify.DiscardUnknown(m)
}

var xxx_messageInfo_NewWealthGodNotify proto.InternalMessageInfo

func (m *NewWealthGodNotify) GetGodInfo() *WealthGod {
	if m != nil {
		return m.GodInfo
	}
	return nil
}

// 房间新生成财神宝箱通知
type ChannelNewWealthGodBoxNotify struct {
	GodInfo              *WealthGod    `protobuf:"bytes,1,opt,name=god_info,json=godInfo,proto3" json:"god_info,omitempty"`
	BoxInfo              *WealthGodBox `protobuf:"bytes,2,opt,name=box_info,json=boxInfo,proto3" json:"box_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ChannelNewWealthGodBoxNotify) Reset()         { *m = ChannelNewWealthGodBoxNotify{} }
func (m *ChannelNewWealthGodBoxNotify) String() string { return proto.CompactTextString(m) }
func (*ChannelNewWealthGodBoxNotify) ProtoMessage()    {}
func (*ChannelNewWealthGodBoxNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_logic_b557952d03dcf17e, []int{16}
}
func (m *ChannelNewWealthGodBoxNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelNewWealthGodBoxNotify.Unmarshal(m, b)
}
func (m *ChannelNewWealthGodBoxNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelNewWealthGodBoxNotify.Marshal(b, m, deterministic)
}
func (dst *ChannelNewWealthGodBoxNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelNewWealthGodBoxNotify.Merge(dst, src)
}
func (m *ChannelNewWealthGodBoxNotify) XXX_Size() int {
	return xxx_messageInfo_ChannelNewWealthGodBoxNotify.Size(m)
}
func (m *ChannelNewWealthGodBoxNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelNewWealthGodBoxNotify.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelNewWealthGodBoxNotify proto.InternalMessageInfo

func (m *ChannelNewWealthGodBoxNotify) GetGodInfo() *WealthGod {
	if m != nil {
		return m.GodInfo
	}
	return nil
}

func (m *ChannelNewWealthGodBoxNotify) GetBoxInfo() *WealthGodBox {
	if m != nil {
		return m.BoxInfo
	}
	return nil
}

type WealthEntryIcon struct {
	BottomEntryIcon        string   `protobuf:"bytes,1,opt,name=bottom_entry_icon,json=bottomEntryIcon,proto3" json:"bottom_entry_icon,omitempty"`
	DynamicWidgetEntryIcon string   `protobuf:"bytes,2,opt,name=dynamic_widget_entry_icon,json=dynamicWidgetEntryIcon,proto3" json:"dynamic_widget_entry_icon,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *WealthEntryIcon) Reset()         { *m = WealthEntryIcon{} }
func (m *WealthEntryIcon) String() string { return proto.CompactTextString(m) }
func (*WealthEntryIcon) ProtoMessage()    {}
func (*WealthEntryIcon) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_logic_b557952d03dcf17e, []int{17}
}
func (m *WealthEntryIcon) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WealthEntryIcon.Unmarshal(m, b)
}
func (m *WealthEntryIcon) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WealthEntryIcon.Marshal(b, m, deterministic)
}
func (dst *WealthEntryIcon) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WealthEntryIcon.Merge(dst, src)
}
func (m *WealthEntryIcon) XXX_Size() int {
	return xxx_messageInfo_WealthEntryIcon.Size(m)
}
func (m *WealthEntryIcon) XXX_DiscardUnknown() {
	xxx_messageInfo_WealthEntryIcon.DiscardUnknown(m)
}

var xxx_messageInfo_WealthEntryIcon proto.InternalMessageInfo

func (m *WealthEntryIcon) GetBottomEntryIcon() string {
	if m != nil {
		return m.BottomEntryIcon
	}
	return ""
}

func (m *WealthEntryIcon) GetDynamicWidgetEntryIcon() string {
	if m != nil {
		return m.DynamicWidgetEntryIcon
	}
	return ""
}

// 任务完成时通知
type WealthMissionFinishNotify struct {
	AvailableOpenCnt     uint32            `protobuf:"varint,1,opt,name=available_open_cnt,json=availableOpenCnt,proto3" json:"available_open_cnt,omitempty"`
	MissionCurrentCntMap map[uint32]uint32 `protobuf:"bytes,2,rep,name=mission_current_cnt_map,json=missionCurrentCntMap,proto3" json:"mission_current_cnt_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	GodId                string            `protobuf:"bytes,3,opt,name=god_id,json=godId,proto3" json:"god_id,omitempty"`
	GetOpenCnt           uint32            `protobuf:"varint,4,opt,name=get_open_cnt,json=getOpenCnt,proto3" json:"get_open_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *WealthMissionFinishNotify) Reset()         { *m = WealthMissionFinishNotify{} }
func (m *WealthMissionFinishNotify) String() string { return proto.CompactTextString(m) }
func (*WealthMissionFinishNotify) ProtoMessage()    {}
func (*WealthMissionFinishNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_logic_b557952d03dcf17e, []int{18}
}
func (m *WealthMissionFinishNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WealthMissionFinishNotify.Unmarshal(m, b)
}
func (m *WealthMissionFinishNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WealthMissionFinishNotify.Marshal(b, m, deterministic)
}
func (dst *WealthMissionFinishNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WealthMissionFinishNotify.Merge(dst, src)
}
func (m *WealthMissionFinishNotify) XXX_Size() int {
	return xxx_messageInfo_WealthMissionFinishNotify.Size(m)
}
func (m *WealthMissionFinishNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_WealthMissionFinishNotify.DiscardUnknown(m)
}

var xxx_messageInfo_WealthMissionFinishNotify proto.InternalMessageInfo

func (m *WealthMissionFinishNotify) GetAvailableOpenCnt() uint32 {
	if m != nil {
		return m.AvailableOpenCnt
	}
	return 0
}

func (m *WealthMissionFinishNotify) GetMissionCurrentCntMap() map[uint32]uint32 {
	if m != nil {
		return m.MissionCurrentCntMap
	}
	return nil
}

func (m *WealthMissionFinishNotify) GetGodId() string {
	if m != nil {
		return m.GodId
	}
	return ""
}

func (m *WealthMissionFinishNotify) GetGetOpenCnt() uint32 {
	if m != nil {
		return m.GetOpenCnt
	}
	return 0
}

// 自动开启财神宝箱奖励结果通知
type AutoOpenWealthGodBoxRewardResultNotify struct {
	RewardList           []*WealthGodBoxRewardPreview `protobuf:"bytes,1,rep,name=reward_list,json=rewardList,proto3" json:"reward_list,omitempty"`
	ChannelId            uint32                       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *AutoOpenWealthGodBoxRewardResultNotify) Reset() {
	*m = AutoOpenWealthGodBoxRewardResultNotify{}
}
func (m *AutoOpenWealthGodBoxRewardResultNotify) String() string { return proto.CompactTextString(m) }
func (*AutoOpenWealthGodBoxRewardResultNotify) ProtoMessage()    {}
func (*AutoOpenWealthGodBoxRewardResultNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_logic_b557952d03dcf17e, []int{19}
}
func (m *AutoOpenWealthGodBoxRewardResultNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AutoOpenWealthGodBoxRewardResultNotify.Unmarshal(m, b)
}
func (m *AutoOpenWealthGodBoxRewardResultNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AutoOpenWealthGodBoxRewardResultNotify.Marshal(b, m, deterministic)
}
func (dst *AutoOpenWealthGodBoxRewardResultNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AutoOpenWealthGodBoxRewardResultNotify.Merge(dst, src)
}
func (m *AutoOpenWealthGodBoxRewardResultNotify) XXX_Size() int {
	return xxx_messageInfo_AutoOpenWealthGodBoxRewardResultNotify.Size(m)
}
func (m *AutoOpenWealthGodBoxRewardResultNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_AutoOpenWealthGodBoxRewardResultNotify.DiscardUnknown(m)
}

var xxx_messageInfo_AutoOpenWealthGodBoxRewardResultNotify proto.InternalMessageInfo

func (m *AutoOpenWealthGodBoxRewardResultNotify) GetRewardList() []*WealthGodBoxRewardPreview {
	if m != nil {
		return m.RewardList
	}
	return nil
}

func (m *AutoOpenWealthGodBoxRewardResultNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// 登录时调用
type GetWealthGodCommonCfgRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetWealthGodCommonCfgRequest) Reset()         { *m = GetWealthGodCommonCfgRequest{} }
func (m *GetWealthGodCommonCfgRequest) String() string { return proto.CompactTextString(m) }
func (*GetWealthGodCommonCfgRequest) ProtoMessage()    {}
func (*GetWealthGodCommonCfgRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_logic_b557952d03dcf17e, []int{20}
}
func (m *GetWealthGodCommonCfgRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWealthGodCommonCfgRequest.Unmarshal(m, b)
}
func (m *GetWealthGodCommonCfgRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWealthGodCommonCfgRequest.Marshal(b, m, deterministic)
}
func (dst *GetWealthGodCommonCfgRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWealthGodCommonCfgRequest.Merge(dst, src)
}
func (m *GetWealthGodCommonCfgRequest) XXX_Size() int {
	return xxx_messageInfo_GetWealthGodCommonCfgRequest.Size(m)
}
func (m *GetWealthGodCommonCfgRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWealthGodCommonCfgRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWealthGodCommonCfgRequest proto.InternalMessageInfo

func (m *GetWealthGodCommonCfgRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

// 通用动画结构
type WealthGodCommonAnimation struct {
	AnimationUrl         string   `protobuf:"bytes,1,opt,name=animation_url,json=animationUrl,proto3" json:"animation_url,omitempty"`
	AnimationMd5         string   `protobuf:"bytes,2,opt,name=animation_md5,json=animationMd5,proto3" json:"animation_md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WealthGodCommonAnimation) Reset()         { *m = WealthGodCommonAnimation{} }
func (m *WealthGodCommonAnimation) String() string { return proto.CompactTextString(m) }
func (*WealthGodCommonAnimation) ProtoMessage()    {}
func (*WealthGodCommonAnimation) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_logic_b557952d03dcf17e, []int{21}
}
func (m *WealthGodCommonAnimation) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WealthGodCommonAnimation.Unmarshal(m, b)
}
func (m *WealthGodCommonAnimation) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WealthGodCommonAnimation.Marshal(b, m, deterministic)
}
func (dst *WealthGodCommonAnimation) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WealthGodCommonAnimation.Merge(dst, src)
}
func (m *WealthGodCommonAnimation) XXX_Size() int {
	return xxx_messageInfo_WealthGodCommonAnimation.Size(m)
}
func (m *WealthGodCommonAnimation) XXX_DiscardUnknown() {
	xxx_messageInfo_WealthGodCommonAnimation.DiscardUnknown(m)
}

var xxx_messageInfo_WealthGodCommonAnimation proto.InternalMessageInfo

func (m *WealthGodCommonAnimation) GetAnimationUrl() string {
	if m != nil {
		return m.AnimationUrl
	}
	return ""
}

func (m *WealthGodCommonAnimation) GetAnimationMd5() string {
	if m != nil {
		return m.AnimationMd5
	}
	return ""
}

type GetWealthGodCommonCfgResponse struct {
	BaseResp                    *app.BaseResp                        `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RushInfo                    *app.RushInfo                        `protobuf:"bytes,2,opt,name=rush_info,json=rushInfo,proto3" json:"rush_info,omitempty"`
	NormalEntryIcon             *WealthEntryIcon                     `protobuf:"bytes,3,opt,name=normal_entry_icon,json=normalEntryIcon,proto3" json:"normal_entry_icon,omitempty"`
	DividingGiftEntryIcon       *WealthEntryIcon                     `protobuf:"bytes,4,opt,name=dividing_gift_entry_icon,json=dividingGiftEntryIcon,proto3" json:"dividing_gift_entry_icon,omitempty"`
	OtherRoomGodEntryIcon       *WealthEntryIcon                     `protobuf:"bytes,5,opt,name=other_room_god_entry_icon,json=otherRoomGodEntryIcon,proto3" json:"other_room_god_entry_icon,omitempty"`
	BoxOpenAnimationMap         map[uint32]*WealthGodCommonAnimation `protobuf:"bytes,6,rep,name=box_open_animation_map,json=boxOpenAnimationMap,proto3" json:"box_open_animation_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	FixedEntryShowDuration      uint32                               `protobuf:"varint,7,opt,name=fixed_entry_show_duration,json=fixedEntryShowDuration,proto3" json:"fixed_entry_show_duration,omitempty"`
	DoingMissionEntryIcon       *WealthEntryIcon                     `protobuf:"bytes,8,opt,name=doing_mission_entry_icon,json=doingMissionEntryIcon,proto3" json:"doing_mission_entry_icon,omitempty"`
	BoxRewardWindowShowDuration uint32                               `protobuf:"varint,9,opt,name=box_reward_window_show_duration,json=boxRewardWindowShowDuration,proto3" json:"box_reward_window_show_duration,omitempty"`
	NeedStayRoomSecond          uint32                               `protobuf:"varint,10,opt,name=need_stay_room_second,json=needStayRoomSecond,proto3" json:"need_stay_room_second,omitempty"`
	BoxCountDownAnimation       *WealthGodCommonAnimation            `protobuf:"bytes,11,opt,name=box_count_down_animation,json=boxCountDownAnimation,proto3" json:"box_count_down_animation,omitempty"`
	GodShowAnimation            *WealthGodCommonAnimation            `protobuf:"bytes,12,opt,name=god_show_animation,json=godShowAnimation,proto3" json:"god_show_animation,omitempty"`
	GodEndPictureUrl            string                               `protobuf:"bytes,13,opt,name=god_end_picture_url,json=godEndPictureUrl,proto3" json:"god_end_picture_url,omitempty"`
	FixedEntryIconUrl           string                               `protobuf:"bytes,14,opt,name=fixed_entry_icon_url,json=fixedEntryIconUrl,proto3" json:"fixed_entry_icon_url,omitempty"`
	BoxChangingAnimation        *WealthGodCommonAnimation            `protobuf:"bytes,15,opt,name=box_changing_animation,json=boxChangingAnimation,proto3" json:"box_changing_animation,omitempty"`
	BoxWaitOpenAnimationMap     map[uint32]*WealthGodCommonAnimation `protobuf:"bytes,16,rep,name=box_wait_open_animation_map,json=boxWaitOpenAnimationMap,proto3" json:"box_wait_open_animation_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral        struct{}                             `json:"-"`
	XXX_unrecognized            []byte                               `json:"-"`
	XXX_sizecache               int32                                `json:"-"`
}

func (m *GetWealthGodCommonCfgResponse) Reset()         { *m = GetWealthGodCommonCfgResponse{} }
func (m *GetWealthGodCommonCfgResponse) String() string { return proto.CompactTextString(m) }
func (*GetWealthGodCommonCfgResponse) ProtoMessage()    {}
func (*GetWealthGodCommonCfgResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_logic_b557952d03dcf17e, []int{22}
}
func (m *GetWealthGodCommonCfgResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWealthGodCommonCfgResponse.Unmarshal(m, b)
}
func (m *GetWealthGodCommonCfgResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWealthGodCommonCfgResponse.Marshal(b, m, deterministic)
}
func (dst *GetWealthGodCommonCfgResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWealthGodCommonCfgResponse.Merge(dst, src)
}
func (m *GetWealthGodCommonCfgResponse) XXX_Size() int {
	return xxx_messageInfo_GetWealthGodCommonCfgResponse.Size(m)
}
func (m *GetWealthGodCommonCfgResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWealthGodCommonCfgResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWealthGodCommonCfgResponse proto.InternalMessageInfo

func (m *GetWealthGodCommonCfgResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetWealthGodCommonCfgResponse) GetRushInfo() *app.RushInfo {
	if m != nil {
		return m.RushInfo
	}
	return nil
}

func (m *GetWealthGodCommonCfgResponse) GetNormalEntryIcon() *WealthEntryIcon {
	if m != nil {
		return m.NormalEntryIcon
	}
	return nil
}

func (m *GetWealthGodCommonCfgResponse) GetDividingGiftEntryIcon() *WealthEntryIcon {
	if m != nil {
		return m.DividingGiftEntryIcon
	}
	return nil
}

func (m *GetWealthGodCommonCfgResponse) GetOtherRoomGodEntryIcon() *WealthEntryIcon {
	if m != nil {
		return m.OtherRoomGodEntryIcon
	}
	return nil
}

func (m *GetWealthGodCommonCfgResponse) GetBoxOpenAnimationMap() map[uint32]*WealthGodCommonAnimation {
	if m != nil {
		return m.BoxOpenAnimationMap
	}
	return nil
}

func (m *GetWealthGodCommonCfgResponse) GetFixedEntryShowDuration() uint32 {
	if m != nil {
		return m.FixedEntryShowDuration
	}
	return 0
}

func (m *GetWealthGodCommonCfgResponse) GetDoingMissionEntryIcon() *WealthEntryIcon {
	if m != nil {
		return m.DoingMissionEntryIcon
	}
	return nil
}

func (m *GetWealthGodCommonCfgResponse) GetBoxRewardWindowShowDuration() uint32 {
	if m != nil {
		return m.BoxRewardWindowShowDuration
	}
	return 0
}

func (m *GetWealthGodCommonCfgResponse) GetNeedStayRoomSecond() uint32 {
	if m != nil {
		return m.NeedStayRoomSecond
	}
	return 0
}

func (m *GetWealthGodCommonCfgResponse) GetBoxCountDownAnimation() *WealthGodCommonAnimation {
	if m != nil {
		return m.BoxCountDownAnimation
	}
	return nil
}

func (m *GetWealthGodCommonCfgResponse) GetGodShowAnimation() *WealthGodCommonAnimation {
	if m != nil {
		return m.GodShowAnimation
	}
	return nil
}

func (m *GetWealthGodCommonCfgResponse) GetGodEndPictureUrl() string {
	if m != nil {
		return m.GodEndPictureUrl
	}
	return ""
}

func (m *GetWealthGodCommonCfgResponse) GetFixedEntryIconUrl() string {
	if m != nil {
		return m.FixedEntryIconUrl
	}
	return ""
}

func (m *GetWealthGodCommonCfgResponse) GetBoxChangingAnimation() *WealthGodCommonAnimation {
	if m != nil {
		return m.BoxChangingAnimation
	}
	return nil
}

func (m *GetWealthGodCommonCfgResponse) GetBoxWaitOpenAnimationMap() map[uint32]*WealthGodCommonAnimation {
	if m != nil {
		return m.BoxWaitOpenAnimationMap
	}
	return nil
}

// 通知财神活动开始
type NotifyWealthActivityStart struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NotifyWealthActivityStart) Reset()         { *m = NotifyWealthActivityStart{} }
func (m *NotifyWealthActivityStart) String() string { return proto.CompactTextString(m) }
func (*NotifyWealthActivityStart) ProtoMessage()    {}
func (*NotifyWealthActivityStart) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_logic_b557952d03dcf17e, []int{23}
}
func (m *NotifyWealthActivityStart) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotifyWealthActivityStart.Unmarshal(m, b)
}
func (m *NotifyWealthActivityStart) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotifyWealthActivityStart.Marshal(b, m, deterministic)
}
func (dst *NotifyWealthActivityStart) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotifyWealthActivityStart.Merge(dst, src)
}
func (m *NotifyWealthActivityStart) XXX_Size() int {
	return xxx_messageInfo_NotifyWealthActivityStart.Size(m)
}
func (m *NotifyWealthActivityStart) XXX_DiscardUnknown() {
	xxx_messageInfo_NotifyWealthActivityStart.DiscardUnknown(m)
}

var xxx_messageInfo_NotifyWealthActivityStart proto.InternalMessageInfo

func init() {
	proto.RegisterType((*WealthGod)(nil), "ga.wealth_god_logic.WealthGod")
	proto.RegisterType((*WealthGodBox)(nil), "ga.wealth_god_logic.WealthGodBox")
	proto.RegisterType((*GetWealthGodEntryRequest)(nil), "ga.wealth_god_logic.GetWealthGodEntryRequest")
	proto.RegisterType((*GetWealthGodEntryResponse)(nil), "ga.wealth_god_logic.GetWealthGodEntryResponse")
	proto.RegisterType((*GetOneWealthGodChannelReq)(nil), "ga.wealth_god_logic.GetOneWealthGodChannelReq")
	proto.RegisterType((*GetOneWealthGodChannelResp)(nil), "ga.wealth_god_logic.GetOneWealthGodChannelResp")
	proto.RegisterType((*GetWealthGodActivityInfoRequest)(nil), "ga.wealth_god_logic.GetWealthGodActivityInfoRequest")
	proto.RegisterType((*GetWealthGodActivityInfoResponse)(nil), "ga.wealth_god_logic.GetWealthGodActivityInfoResponse")
	proto.RegisterType((*GetWealthGodActivityInfoResponse_WealthGodTrigger)(nil), "ga.wealth_god_logic.GetWealthGodActivityInfoResponse.WealthGodTrigger")
	proto.RegisterType((*GetWealthGodDetailRequest)(nil), "ga.wealth_god_logic.GetWealthGodDetailRequest")
	proto.RegisterType((*GetWealthGodDetailResponse)(nil), "ga.wealth_god_logic.GetWealthGodDetailResponse")
	proto.RegisterType((*WealthGodBoxRewardPreview)(nil), "ga.wealth_god_logic.WealthGodBoxRewardPreview")
	proto.RegisterType((*OpenWealthGodBoxRewardRequest)(nil), "ga.wealth_god_logic.OpenWealthGodBoxRewardRequest")
	proto.RegisterType((*OpenWealthGodBoxRewardResponse)(nil), "ga.wealth_god_logic.OpenWealthGodBoxRewardResponse")
	proto.RegisterType((*ReportStayRoomMissionFinishRequest)(nil), "ga.wealth_god_logic.ReportStayRoomMissionFinishRequest")
	proto.RegisterType((*ReportStayRoomMissionFinishResponse)(nil), "ga.wealth_god_logic.ReportStayRoomMissionFinishResponse")
	proto.RegisterType((*NewWealthGodNotify)(nil), "ga.wealth_god_logic.NewWealthGodNotify")
	proto.RegisterType((*ChannelNewWealthGodBoxNotify)(nil), "ga.wealth_god_logic.ChannelNewWealthGodBoxNotify")
	proto.RegisterType((*WealthEntryIcon)(nil), "ga.wealth_god_logic.WealthEntryIcon")
	proto.RegisterType((*WealthMissionFinishNotify)(nil), "ga.wealth_god_logic.WealthMissionFinishNotify")
	proto.RegisterMapType((map[uint32]uint32)(nil), "ga.wealth_god_logic.WealthMissionFinishNotify.MissionCurrentCntMapEntry")
	proto.RegisterType((*AutoOpenWealthGodBoxRewardResultNotify)(nil), "ga.wealth_god_logic.AutoOpenWealthGodBoxRewardResultNotify")
	proto.RegisterType((*GetWealthGodCommonCfgRequest)(nil), "ga.wealth_god_logic.GetWealthGodCommonCfgRequest")
	proto.RegisterType((*WealthGodCommonAnimation)(nil), "ga.wealth_god_logic.WealthGodCommonAnimation")
	proto.RegisterType((*GetWealthGodCommonCfgResponse)(nil), "ga.wealth_god_logic.GetWealthGodCommonCfgResponse")
	proto.RegisterMapType((map[uint32]*WealthGodCommonAnimation)(nil), "ga.wealth_god_logic.GetWealthGodCommonCfgResponse.BoxOpenAnimationMapEntry")
	proto.RegisterMapType((map[uint32]*WealthGodCommonAnimation)(nil), "ga.wealth_god_logic.GetWealthGodCommonCfgResponse.BoxWaitOpenAnimationMapEntry")
	proto.RegisterType((*NotifyWealthActivityStart)(nil), "ga.wealth_god_logic.NotifyWealthActivityStart")
	proto.RegisterEnum("ga.wealth_god_logic.WealthGodBoxType", WealthGodBoxType_name, WealthGodBoxType_value)
	proto.RegisterEnum("ga.wealth_god_logic.WealthMissionType", WealthMissionType_name, WealthMissionType_value)
}

func init() {
	proto.RegisterFile("wealth_god_logic/wealth_god_logic.proto", fileDescriptor_wealth_god_logic_b557952d03dcf17e)
}

var fileDescriptor_wealth_god_logic_b557952d03dcf17e = []byte{
	// 1756 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x58, 0xdd, 0x6e, 0x1b, 0xb9,
	0x15, 0xee, 0xc8, 0xb1, 0x2d, 0x1d, 0xcb, 0xb1, 0x32, 0x89, 0x13, 0xc9, 0x89, 0x13, 0xef, 0x38,
	0xdd, 0xf5, 0x06, 0x8d, 0x8c, 0xa6, 0x08, 0xd0, 0x2d, 0x7a, 0x51, 0xfd, 0xd9, 0x2b, 0x34, 0xfe,
	0xc1, 0x48, 0x81, 0xbb, 0x2d, 0x5a, 0x82, 0x9a, 0xa1, 0x64, 0xa2, 0x23, 0x72, 0x32, 0xa4, 0x2c,
	0xa9, 0x40, 0xd1, 0xde, 0xf5, 0x6a, 0x81, 0xde, 0xb5, 0x17, 0xbd, 0xeb, 0x45, 0xfb, 0x04, 0x7d,
	0x8a, 0xbe, 0x41, 0x5f, 0xa6, 0x20, 0x67, 0x46, 0x33, 0x96, 0x47, 0x76, 0x95, 0xdd, 0xee, 0x9d,
	0x86, 0xe7, 0xf0, 0x1c, 0xf2, 0x9c, 0xef, 0xfb, 0x48, 0x0a, 0x3e, 0x1b, 0x13, 0xec, 0xc9, 0x4b,
	0x34, 0xe0, 0x2e, 0xf2, 0xf8, 0x80, 0x3a, 0x87, 0xf3, 0x03, 0x55, 0x3f, 0xe0, 0x92, 0x9b, 0x0f,
	0x07, 0xb8, 0x3a, 0x6f, 0xda, 0xd9, 0x1c, 0x60, 0xd4, 0xc3, 0x82, 0x84, 0x3e, 0xd6, 0x7f, 0x0c,
	0x28, 0x5c, 0x68, 0x9f, 0x63, 0xee, 0x9a, 0xdb, 0xb0, 0xa6, 0x3c, 0xa9, 0x5b, 0x36, 0xf6, 0x8c,
	0x83, 0x82, 0xbd, 0x3a, 0xe0, 0x6e, 0xdb, 0x35, 0x77, 0x01, 0x9c, 0x4b, 0xcc, 0x18, 0xf1, 0x94,
	0x29, 0xb7, 0x67, 0x1c, 0x6c, 0xda, 0x85, 0x68, 0xa4, 0xed, 0x9a, 0x15, 0xc8, 0x0b, 0x89, 0x03,
	0x89, 0xa4, 0x28, 0xaf, 0xec, 0x19, 0x07, 0x2b, 0xf6, 0xba, 0xfe, 0xee, 0x0a, 0x15, 0x90, 0x30,
	0x57, 0x19, 0xee, 0x69, 0xc3, 0x2a, 0x61, 0x6e, 0x57, 0x98, 0x2f, 0xe1, 0xfe, 0x90, 0x0a, 0x41,
	0x39, 0x43, 0x91, 0x79, 0x55, 0x9b, 0x8b, 0xd1, 0x68, 0x4b, 0x7b, 0xfd, 0x0c, 0x76, 0xa9, 0x40,
	0x42, 0xe2, 0x29, 0x0a, 0x38, 0x1f, 0xa2, 0x78, 0x4a, 0x9f, 0x32, 0x2a, 0x2e, 0x89, 0x5b, 0x5e,
	0xdb, 0x33, 0x0e, 0xf2, 0x76, 0x85, 0x8a, 0x8e, 0xc4, 0x53, 0x9b, 0xf3, 0xe1, 0x49, 0xe8, 0x71,
	0x14, 0x39, 0x58, 0xbf, 0x83, 0xe2, 0x6c, 0x73, 0x75, 0x3e, 0x51, 0x2b, 0xed, 0xf1, 0x09, 0x92,
	0x53, 0x9f, 0xe8, 0x1d, 0x6e, 0xda, 0xeb, 0x3d, 0x3e, 0xe9, 0x4e, 0x7d, 0x62, 0x9e, 0xc1, 0x46,
	0x40, 0xc6, 0x38, 0x70, 0x91, 0x47, 0x85, 0x2c, 0xe7, 0xf6, 0x56, 0x0e, 0x36, 0xde, 0x54, 0xab,
	0x19, 0x25, 0xac, 0xa6, 0x43, 0xda, 0x7a, 0xce, 0x79, 0x40, 0xae, 0x28, 0x19, 0xdb, 0x10, 0x86,
	0x78, 0x47, 0x85, 0xb4, 0x30, 0x94, 0x8f, 0x89, 0x9c, 0xf9, 0xb6, 0x98, 0x0c, 0xa6, 0x36, 0xf9,
	0x30, 0x22, 0x42, 0x9a, 0x9f, 0x42, 0x5e, 0xf5, 0x00, 0x05, 0xe4, 0x83, 0x5e, 0xc7, 0xc6, 0x9b,
	0x0d, 0x95, 0xa9, 0x8e, 0x05, 0xb1, 0xc9, 0x07, 0x7b, 0xbd, 0x17, 0xfe, 0xb8, 0xa3, 0xf0, 0xd6,
	0xbf, 0x72, 0x50, 0xc9, 0xc8, 0x21, 0x7c, 0xce, 0x04, 0x31, 0x3f, 0x87, 0x42, 0x94, 0x44, 0xf8,
	0x51, 0x96, 0x62, 0x92, 0x45, 0xf8, 0x76, 0xbe, 0x17, 0xfd, 0x32, 0x2f, 0xa0, 0xec, 0x8c, 0x82,
	0x80, 0x30, 0x89, 0xe2, 0x7c, 0x7a, 0xbb, 0x49, 0x25, 0x9e, 0xdf, 0x5e, 0x09, 0x7b, 0x3b, 0x9a,
	0xdf, 0x08, 0xa7, 0x1f, 0x73, 0x5d, 0x04, 0xf3, 0xfb, 0xb0, 0xe5, 0x61, 0x21, 0xf5, 0x94, 0xa8,
	0xd3, 0x21, 0x42, 0x8a, 0x6a, 0x58, 0x2f, 0x59, 0x75, 0xfa, 0x00, 0x4a, 0xd8, 0x91, 0xf4, 0x8a,
	0xca, 0x29, 0xa2, 0x02, 0x71, 0x9f, 0x30, 0x0d, 0x98, 0xbc, 0x7d, 0x3f, 0x1e, 0x6f, 0x8b, 0x33,
	0x9f, 0x30, 0xb3, 0x79, 0x17, 0x26, 0x14, 0x90, 0xf2, 0xf5, 0x5c, 0xd9, 0xb8, 0x0d, 0x17, 0x0d,
	0x5d, 0xb7, 0x33, 0x46, 0x66, 0x1b, 0x88, 0xd6, 0xad, 0x8a, 0xfe, 0x3f, 0x36, 0xc7, 0xea, 0xc3,
	0xce, 0xa2, 0x20, 0xc2, 0x5f, 0xa6, 0xfa, 0x77, 0x74, 0xb9, 0x0d, 0x2f, 0xd2, 0x4d, 0xae, 0xc5,
	0x05, 0x61, 0x7d, 0xbe, 0x24, 0x9e, 0xac, 0x7f, 0xac, 0xc0, 0xde, 0xe2, 0x58, 0xcb, 0xe3, 0x86,
	0x42, 0x51, 0x06, 0x74, 0x30, 0x20, 0x41, 0x1a, 0x2b, 0x47, 0x99, 0x58, 0xb9, 0x2b, 0x6f, 0x02,
	0xa6, 0x6e, 0x18, 0xd2, 0xde, 0x88, 0x62, 0x6b, 0x24, 0x1d, 0xc2, 0x43, 0xdf, 0xc3, 0x53, 0xca,
	0x06, 0x88, 0x32, 0x21, 0x83, 0x91, 0x23, 0x29, 0x67, 0x1a, 0x4d, 0x05, 0xdb, 0x8c, 0x4c, 0xed,
	0xc4, 0x62, 0x5a, 0xb0, 0xa9, 0x92, 0x6b, 0xbe, 0x53, 0xe9, 0x11, 0x0d, 0xa8, 0x82, 0xbd, 0x31,
	0xd0, 0xbc, 0xed, 0xaa, 0x21, 0x73, 0x0f, 0x8a, 0xb1, 0x8f, 0x4b, 0x84, 0xa3, 0xc1, 0x53, 0xb0,
	0x21, 0x74, 0x69, 0x12, 0xe1, 0xa4, 0x3d, 0xa8, 0xc3, 0x99, 0x96, 0x9c, 0x99, 0x47, 0xdb, 0xe1,
	0x6c, 0xa7, 0x0b, 0xa5, 0xf9, 0x95, 0x9b, 0x9f, 0x24, 0x75, 0x49, 0x69, 0x4d, 0xbc, 0x1f, 0xad,
	0x37, 0xbb, 0x00, 0xb1, 0x4b, 0xd2, 0xf4, 0x68, 0xa4, 0xed, 0x5a, 0xd3, 0xeb, 0xcc, 0x6e, 0x12,
	0x89, 0xa9, 0xf7, 0xed, 0xca, 0x47, 0x4a, 0xed, 0x57, 0x52, 0x6a, 0x6f, 0xfd, 0x39, 0xa7, 0x81,
	0x7d, 0x23, 0xf7, 0xf2, 0xf0, 0xf8, 0x02, 0xf2, 0x3a, 0x01, 0xeb, 0x73, 0x9d, 0xfd, 0x6e, 0x19,
	0x59, 0x57, 0x4b, 0x60, 0x7d, 0x6e, 0xfe, 0x34, 0x54, 0x6a, 0x3d, 0x75, 0x45, 0x4f, 0xfd, 0xe4,
	0x6e, 0x2d, 0x56, 0x62, 0xae, 0x67, 0xff, 0x00, 0x4c, 0x7c, 0x85, 0xa9, 0x87, 0x7b, 0x1e, 0xd1,
	0x6a, 0x82, 0x1c, 0x26, 0x35, 0x00, 0x36, 0xed, 0xd2, 0xcc, 0xa2, 0x04, 0xa5, 0xc1, 0xa4, 0xee,
	0x31, 0x91, 0x89, 0xdf, 0xaa, 0xf6, 0x83, 0x01, 0x91, 0x91, 0x87, 0xf5, 0x77, 0x03, 0x2a, 0x0b,
	0x55, 0xdf, 0x7c, 0x02, 0xeb, 0x54, 0xa0, 0x00, 0x07, 0x61, 0xa3, 0xf3, 0xf6, 0x1a, 0x15, 0x36,
	0x0e, 0x88, 0xf9, 0x08, 0x56, 0x43, 0xe8, 0xe5, 0xc2, 0xfa, 0xea, 0x0f, 0xf3, 0x29, 0x14, 0xc4,
	0xa8, 0x17, 0x81, 0x32, 0xac, 0x7c, 0x5e, 0x8c, 0x7a, 0x21, 0x22, 0x2b, 0x90, 0x57, 0x38, 0x43,
	0xa3, 0xc0, 0x8b, 0x00, 0xbb, 0xae, 0xbe, 0xdf, 0x07, 0x9e, 0xf9, 0x02, 0x36, 0x1c, 0x1e, 0x30,
	0x85, 0x29, 0x32, 0x91, 0x31, 0x56, 0xc3, 0xa1, 0x2e, 0x99, 0x48, 0xeb, 0xf7, 0xb0, 0xab, 0x16,
	0x7c, 0x73, 0xa1, 0xdf, 0x0d, 0x6e, 0xfe, 0x66, 0xc0, 0xf3, 0x45, 0xf9, 0x97, 0xc7, 0xce, 0xb7,
	0x7e, 0x1e, 0x3b, 0x60, 0xd9, 0xc4, 0xe7, 0x81, 0xcc, 0x3c, 0x14, 0x96, 0x2d, 0x51, 0x52, 0x83,
	0x5c, 0xba, 0x06, 0xe7, 0xb0, 0x7f, 0x6b, 0x92, 0xa5, 0xeb, 0x60, 0x9d, 0x81, 0x79, 0x4a, 0xc6,
	0xb3, 0x2d, 0x9e, 0x72, 0x49, 0xfb, 0xd3, 0x6b, 0xcc, 0x32, 0x96, 0x62, 0x96, 0xf5, 0x17, 0x03,
	0x9e, 0x45, 0x07, 0x55, 0x3a, 0x70, 0x9d, 0x4f, 0xbe, 0x71, 0xec, 0x6b, 0xac, 0xcd, 0x2d, 0xcb,
	0x5a, 0x6b, 0x02, 0x5b, 0xa1, 0x41, 0xdf, 0x63, 0x94, 0xb8, 0x9a, 0xaf, 0xe0, 0x41, 0x8f, 0x4b,
	0xc9, 0x87, 0x88, 0xa8, 0xb1, 0x50, 0x83, 0xc3, 0xbb, 0xe9, 0x56, 0x68, 0x48, 0x7c, 0xbf, 0x80,
	0x8a, 0x3b, 0x65, 0x78, 0x48, 0x1d, 0x34, 0xa6, 0xae, 0x62, 0x74, 0x6a, 0x4e, 0xd8, 0xa5, 0xc7,
	0x91, 0xc3, 0x85, 0xb6, 0xcf, 0xa6, 0x5a, 0xff, 0xce, 0xc5, 0xfc, 0xbe, 0xd6, 0xaf, 0xa8, 0x20,
	0xd9, 0x6a, 0x62, 0x2c, 0x50, 0x93, 0x3f, 0xc0, 0x93, 0xf8, 0x52, 0x32, 0xbb, 0x53, 0x31, 0x89,
	0x86, 0xd8, 0x8f, 0x40, 0xfc, 0xe5, 0x2d, 0x25, 0xc9, 0x48, 0x5f, 0x8d, 0xc6, 0x1a, 0xd1, 0x05,
	0x8b, 0xc9, 0x13, 0xec, 0x87, 0x37, 0xbd, 0x47, 0xc3, 0x0c, 0xd3, 0x02, 0x7a, 0xde, 0x50, 0xb9,
	0x7b, 0xf3, 0x2a, 0xb7, 0x73, 0x0c, 0x95, 0x85, 0xb9, 0xcc, 0x12, 0xac, 0xfc, 0x96, 0x4c, 0xa3,
	0x5d, 0xab, 0x9f, 0x4a, 0xdd, 0xae, 0xb0, 0x37, 0x22, 0x91, 0x40, 0x84, 0x1f, 0x3f, 0xc9, 0xfd,
	0xd8, 0xb0, 0xfe, 0x6a, 0xc0, 0xa7, 0xb5, 0x91, 0xe4, 0x0b, 0xd5, 0x60, 0xe4, 0xc9, 0xa8, 0xb6,
	0x73, 0x34, 0x37, 0xbe, 0x29, 0xcd, 0xef, 0xba, 0x4c, 0x1d, 0xc1, 0xb3, 0xf4, 0xd9, 0xd6, 0xe0,
	0xc3, 0x21, 0x67, 0x8d, 0xfe, 0x60, 0xd9, 0x9b, 0x94, 0x0b, 0xe5, 0xb9, 0x20, 0x35, 0x46, 0x87,
	0x58, 0xdf, 0x3c, 0xf6, 0x61, 0x13, 0xc7, 0x1f, 0x5a, 0xc8, 0x43, 0xc0, 0x16, 0x67, 0x83, 0x4a,
	0xcd, 0xaf, 0x39, 0x0d, 0xdd, 0xb7, 0x11, 0x42, 0x13, 0xa7, 0x13, 0xf7, 0xad, 0xf5, 0xcf, 0x22,
	0xec, 0x2e, 0x58, 0xee, 0xf2, 0x8a, 0xfa, 0x39, 0x14, 0x82, 0x91, 0xb8, 0x4c, 0xb3, 0x53, 0xbb,
	0xda, 0x23, 0x71, 0xa9, 0x2f, 0x62, 0xf9, 0x20, 0xfa, 0x65, 0x9e, 0xc3, 0x03, 0xc6, 0x83, 0x21,
	0xf6, 0xd2, 0x14, 0x0a, 0x8f, 0xe1, 0x97, 0xb7, 0xf4, 0x66, 0x46, 0x28, 0x7b, 0x2b, 0x9c, 0x9e,
	0x90, 0xf3, 0xd7, 0x50, 0x76, 0xe9, 0x15, 0x75, 0xd5, 0xfd, 0x6d, 0x40, 0xfb, 0xd7, 0xb8, 0x79,
	0x6f, 0x89, 0xc0, 0xdb, 0x71, 0x94, 0x63, 0xda, 0x4f, 0x08, 0x6c, 0xfe, 0x06, 0x2a, 0x5c, 0x5e,
	0x92, 0x20, 0x7c, 0x14, 0x84, 0xaf, 0x8d, 0x59, 0xfc, 0xd5, 0x65, 0xe2, 0xeb, 0x30, 0x4a, 0xb9,
	0xe3, 0xf7, 0x94, 0x8e, 0xff, 0x47, 0x03, 0x1e, 0x2b, 0x65, 0xd3, 0xec, 0x49, 0xf5, 0x0d, 0xfb,
	0xe5, 0x35, 0x0d, 0xd9, 0x9f, 0xdf, 0x79, 0xe7, 0xbd, 0xd1, 0xbb, 0x6a, 0x9d, 0x4f, 0x14, 0x43,
	0x66, 0xc0, 0x99, 0xf1, 0xfa, 0x61, 0xef, 0xa6, 0x45, 0xc9, 0x5b, 0x9f, 0x4e, 0x48, 0xbc, 0x33,
	0x71, 0xc9, 0xc7, 0xc8, 0x1d, 0x05, 0xda, 0x5e, 0x5e, 0xd7, 0x38, 0x7f, 0xac, 0x1d, 0x74, 0x8c,
	0xce, 0x25, 0x1f, 0x37, 0x23, 0xab, 0x2e, 0x3e, 0x57, 0x95, 0x4f, 0x1e, 0xdd, 0xb3, 0xe2, 0xe4,
	0x97, 0x2a, 0xbe, 0x8a, 0x72, 0x12, 0xbf, 0xd1, 0xe3, 0xe2, 0x34, 0xe1, 0x85, 0xaa, 0x4d, 0xc4,
	0xe3, 0x31, 0x65, 0x2e, 0x1f, 0xcf, 0xad, 0xaf, 0xa0, 0xd7, 0xf7, 0xb4, 0x17, 0x33, 0xf7, 0x42,
	0x3b, 0x5d, 0x5b, 0xe4, 0x0f, 0x61, 0x9b, 0x11, 0xe2, 0xa6, 0xde, 0x76, 0x82, 0x38, 0x9c, 0xb9,
	0x65, 0xd0, 0x73, 0x4d, 0x65, 0x8c, 0x4f, 0xd5, 0x8e, 0xb6, 0x98, 0x7d, 0x28, 0xab, 0xc4, 0x0e,
	0x1f, 0x31, 0x89, 0x5c, 0x3e, 0x4e, 0xb5, 0xa6, 0xbc, 0xa1, 0xf7, 0xf5, 0xfa, 0x76, 0x25, 0x99,
	0x63, 0xae, 0xbd, 0xdd, 0xe3, 0x93, 0x86, 0x8a, 0xd6, 0xe4, 0xe3, 0x14, 0xa1, 0x7f, 0x05, 0xa6,
	0x9a, 0xac, 0xb7, 0x94, 0x64, 0x28, 0x7e, 0x4c, 0x86, 0xd2, 0x80, 0xbb, 0x6a, 0xdb, 0x49, 0xf0,
	0xd7, 0xf0, 0x30, 0x7e, 0x1d, 0xfb, 0xd4, 0x91, 0xa3, 0x80, 0x68, 0xcd, 0xd8, 0xd4, 0x72, 0xa0,
	0xdc, 0x5b, 0xcc, 0x3d, 0x0f, 0x0d, 0x4a, 0x37, 0x0e, 0xe1, 0x51, 0x1a, 0x06, 0xb3, 0xcb, 0xe2,
	0x7d, 0xed, 0xff, 0x20, 0x41, 0x40, 0x3b, 0xba, 0x36, 0x3a, 0x21, 0x72, 0x95, 0x04, 0x0e, 0x14,
	0x06, 0x92, 0x0d, 0x6c, 0x7d, 0xcc, 0x06, 0x1e, 0xa9, 0x12, 0x45, 0xb1, 0x92, 0x4d, 0x7c, 0x6d,
	0x80, 0x6a, 0x2e, 0x1a, 0x63, 0x2a, 0xb3, 0x48, 0x52, 0xd2, 0x24, 0x39, 0xfb, 0x38, 0x92, 0x5c,
	0x60, 0x2a, 0xb3, 0x89, 0xf2, 0xa4, 0x97, 0x6d, 0xdd, 0x19, 0x41, 0x79, 0x11, 0xbb, 0x32, 0x4e,
	0xb2, 0x46, 0xfa, 0x24, 0x5b, 0xba, 0x22, 0xc9, 0xc1, 0xb7, 0x33, 0x85, 0x67, 0xb7, 0xad, 0xf7,
	0xff, 0x98, 0xda, 0x7a, 0x0a, 0x95, 0xf0, 0x48, 0x0d, 0x9d, 0xe3, 0x17, 0x76, 0x47, 0xe2, 0x40,
	0xbe, 0xfa, 0xda, 0x48, 0x3d, 0x52, 0xeb, 0xd1, 0x3f, 0x5e, 0xfb, 0xf0, 0xe2, 0xa2, 0x55, 0x7b,
	0xd7, 0xfd, 0x12, 0x1d, 0x9f, 0x35, 0x51, 0xfd, 0xec, 0x17, 0xa8, 0xfb, 0xd5, 0x79, 0x0b, 0xbd,
	0x3f, 0xed, 0x9c, 0xb7, 0x1a, 0xed, 0xa3, 0x76, 0xab, 0x59, 0xfa, 0x9e, 0x59, 0x81, 0xed, 0x2c,
	0xa7, 0x4e, 0xc9, 0x30, 0x9f, 0xc3, 0x4e, 0x96, 0xa9, 0x86, 0xce, 0xdf, 0xbd, 0xef, 0x94, 0x72,
	0x8b, 0xa6, 0xd6, 0x4a, 0x2b, 0xaf, 0xfe, 0x64, 0xc0, 0x83, 0x6b, 0x17, 0x9e, 0xb9, 0x05, 0x9d,
	0xb4, 0x3b, 0x9d, 0xf6, 0xd9, 0x69, 0xd6, 0x82, 0x5e, 0xc2, 0x5e, 0x96, 0x53, 0xa7, 0x5b, 0xfb,
	0x0a, 0xb5, 0x4f, 0xd1, 0x71, 0xed, 0xa4, 0x55, 0x32, 0xcc, 0xcf, 0x60, 0x3f, 0xd3, 0xab, 0x75,
	0xda, 0x44, 0xdd, 0x7a, 0xab, 0x76, 0x8a, 0x8e, 0xdb, 0x47, 0xdd, 0x52, 0xae, 0xde, 0x86, 0xb2,
	0xc3, 0x87, 0xd5, 0x29, 0x9d, 0xf2, 0x91, 0xaa, 0xfd, 0x90, 0xbb, 0xc4, 0x0b, 0xff, 0x1b, 0xfd,
	0xe5, 0xeb, 0x01, 0xf7, 0x30, 0x1b, 0x54, 0xdf, 0xbe, 0x91, 0xb2, 0xea, 0xf0, 0xe1, 0xa1, 0x1e,
	0x76, 0xb8, 0x77, 0x88, 0x7d, 0xff, 0xc6, 0x9f, 0xae, 0xbd, 0x35, 0x6d, 0xfe, 0xd1, 0x7f, 0x03,
	0x00, 0x00, 0xff, 0xff, 0x33, 0x02, 0xc8, 0x69, 0xa0, 0x15, 0x00, 0x00,
}
