// Code generated by protoc-gen-go. DO NOT EDIT.
// source: present_go_logic/present-go-logic_.proto

package present_go_logic // import "golang.52tt.com/protocol/app/present-go-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"
import sync "golang.52tt.com/protocol/app/sync"
import userpresent "golang.52tt.com/protocol/app/userpresent"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type CustomPreviewType int32

const (
	CustomPreviewType_CustomPreviewImg CustomPreviewType = 0
	CustomPreviewType_CustomPreviewMp4 CustomPreviewType = 1
)

var CustomPreviewType_name = map[int32]string{
	0: "CustomPreviewImg",
	1: "CustomPreviewMp4",
}
var CustomPreviewType_value = map[string]int32{
	"CustomPreviewImg": 0,
	"CustomPreviewMp4": 1,
}

func (x CustomPreviewType) String() string {
	return proto.EnumName(CustomPreviewType_name, int32(x))
}
func (CustomPreviewType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{0}
}

type PresentEnterType int32

const (
	PresentEnterType_PresentEnterTypeUnknown  PresentEnterType = 0
	PresentEnterType_PresentEnterTypeShelf    PresentEnterType = 1
	PresentEnterType_PresentEnterTypeLottery  PresentEnterType = 2
	PresentEnterType_PresentEnterTypeWishList PresentEnterType = 3
)

var PresentEnterType_name = map[int32]string{
	0: "PresentEnterTypeUnknown",
	1: "PresentEnterTypeShelf",
	2: "PresentEnterTypeLottery",
	3: "PresentEnterTypeWishList",
}
var PresentEnterType_value = map[string]int32{
	"PresentEnterTypeUnknown":  0,
	"PresentEnterTypeShelf":    1,
	"PresentEnterTypeLottery":  2,
	"PresentEnterTypeWishList": 3,
}

func (x PresentEnterType) String() string {
	return proto.EnumName(PresentEnterType_name, int32(x))
}
func (PresentEnterType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{1}
}

// 礼物套组礼物的稀有度等级
type PresentSetItemRareLevel int32

const (
	PresentSetItemRareLevel_PresentSetItemRareLevelUnknown           PresentSetItemRareLevel = 0
	PresentSetItemRareLevel_PresentSetItemRareLevelRare              PresentSetItemRareLevel = 1
	PresentSetItemRareLevel_PresentSetItemRareLevelSuperRare         PresentSetItemRareLevel = 2
	PresentSetItemRareLevel_PresentSetItemRareLevelSuperiorSuperRare PresentSetItemRareLevel = 3
)

var PresentSetItemRareLevel_name = map[int32]string{
	0: "PresentSetItemRareLevelUnknown",
	1: "PresentSetItemRareLevelRare",
	2: "PresentSetItemRareLevelSuperRare",
	3: "PresentSetItemRareLevelSuperiorSuperRare",
}
var PresentSetItemRareLevel_value = map[string]int32{
	"PresentSetItemRareLevelUnknown":           0,
	"PresentSetItemRareLevelRare":              1,
	"PresentSetItemRareLevelSuperRare":         2,
	"PresentSetItemRareLevelSuperiorSuperRare": 3,
}

func (x PresentSetItemRareLevel) String() string {
	return proto.EnumName(PresentSetItemRareLevel_name, int32(x))
}
func (PresentSetItemRareLevel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{2}
}

type PresentSetActivityUrlType int32

const (
	PresentSetActivityUrlType_PresentSetActivityTypeUnknown    PresentSetActivityUrlType = 0
	PresentSetActivityUrlType_PresentSetActivityTypeFullScreen PresentSetActivityUrlType = 1
	PresentSetActivityUrlType_PresentSetActivityTypeHalfScreen PresentSetActivityUrlType = 2
)

var PresentSetActivityUrlType_name = map[int32]string{
	0: "PresentSetActivityTypeUnknown",
	1: "PresentSetActivityTypeFullScreen",
	2: "PresentSetActivityTypeHalfScreen",
}
var PresentSetActivityUrlType_value = map[string]int32{
	"PresentSetActivityTypeUnknown":    0,
	"PresentSetActivityTypeFullScreen": 1,
	"PresentSetActivityTypeHalfScreen": 2,
}

func (x PresentSetActivityUrlType) String() string {
	return proto.EnumName(PresentSetActivityUrlType_name, int32(x))
}
func (PresentSetActivityUrlType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{3}
}

// 奖励类型枚举， 包括包裹，大V认证、个人铭牌、麦位框、坐骑、主页飘、房间资料卡
type PresentSetCollectionAwardType int32

const (
	PresentSetCollectionAwardType_PresentSetCollectionAwardTypeUnknown                PresentSetCollectionAwardType = 0
	PresentSetCollectionAwardType_PresentSetCollectionAwardTypePackage                PresentSetCollectionAwardType = 1
	PresentSetCollectionAwardType_PresentSetCollectionAwardTypeOfficialCert           PresentSetCollectionAwardType = 2
	PresentSetCollectionAwardType_PresentSetCollectionAwardTypeNamePlate              PresentSetCollectionAwardType = 3
	PresentSetCollectionAwardType_PresentSetCollectionAwardTypeHeadWear               PresentSetCollectionAwardType = 4
	PresentSetCollectionAwardType_PresentSetCollectionAwardTypeChannelPersonalization PresentSetCollectionAwardType = 5
	PresentSetCollectionAwardType_PresentSetCollectionAwardTypeFloat                  PresentSetCollectionAwardType = 6
	PresentSetCollectionAwardType_PresentSetCollectionAwardTypeChannelInfoCard        PresentSetCollectionAwardType = 7
)

var PresentSetCollectionAwardType_name = map[int32]string{
	0: "PresentSetCollectionAwardTypeUnknown",
	1: "PresentSetCollectionAwardTypePackage",
	2: "PresentSetCollectionAwardTypeOfficialCert",
	3: "PresentSetCollectionAwardTypeNamePlate",
	4: "PresentSetCollectionAwardTypeHeadWear",
	5: "PresentSetCollectionAwardTypeChannelPersonalization",
	6: "PresentSetCollectionAwardTypeFloat",
	7: "PresentSetCollectionAwardTypeChannelInfoCard",
}
var PresentSetCollectionAwardType_value = map[string]int32{
	"PresentSetCollectionAwardTypeUnknown":                0,
	"PresentSetCollectionAwardTypePackage":                1,
	"PresentSetCollectionAwardTypeOfficialCert":           2,
	"PresentSetCollectionAwardTypeNamePlate":              3,
	"PresentSetCollectionAwardTypeHeadWear":               4,
	"PresentSetCollectionAwardTypeChannelPersonalization": 5,
	"PresentSetCollectionAwardTypeFloat":                  6,
	"PresentSetCollectionAwardTypeChannelInfoCard":        7,
}

func (x PresentSetCollectionAwardType) String() string {
	return proto.EnumName(PresentSetCollectionAwardType_name, int32(x))
}
func (PresentSetCollectionAwardType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{4}
}

// 预览类型枚举， 无/链接/视频
type PresentPreviewType int32

const (
	PresentPreviewType_PresentPreviewTypeNone  PresentPreviewType = 0
	PresentPreviewType_PresentPreviewTypeUrl   PresentPreviewType = 1
	PresentPreviewType_PresentPreviewTypeVideo PresentPreviewType = 2
)

var PresentPreviewType_name = map[int32]string{
	0: "PresentPreviewTypeNone",
	1: "PresentPreviewTypeUrl",
	2: "PresentPreviewTypeVideo",
}
var PresentPreviewType_value = map[string]int32{
	"PresentPreviewTypeNone":  0,
	"PresentPreviewTypeUrl":   1,
	"PresentPreviewTypeVideo": 2,
}

func (x PresentPreviewType) String() string {
	return proto.EnumName(PresentPreviewType_name, int32(x))
}
func (PresentPreviewType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{5}
}

type EmperorSetConfig_EmperorSetJumpType int32

const (
	EmperorSetConfig_EmperorSetJumpTypeNone     EmperorSetConfig_EmperorSetJumpType = 0
	EmperorSetConfig_EmperorSetJumpTypeUrl      EmperorSetConfig_EmperorSetJumpType = 1
	EmperorSetConfig_EmperorSetJumpTypeActivity EmperorSetConfig_EmperorSetJumpType = 2
)

var EmperorSetConfig_EmperorSetJumpType_name = map[int32]string{
	0: "EmperorSetJumpTypeNone",
	1: "EmperorSetJumpTypeUrl",
	2: "EmperorSetJumpTypeActivity",
}
var EmperorSetConfig_EmperorSetJumpType_value = map[string]int32{
	"EmperorSetJumpTypeNone":     0,
	"EmperorSetJumpTypeUrl":      1,
	"EmperorSetJumpTypeActivity": 2,
}

func (x EmperorSetConfig_EmperorSetJumpType) String() string {
	return proto.EnumName(EmperorSetConfig_EmperorSetJumpType_name, int32(x))
}
func (EmperorSetConfig_EmperorSetJumpType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{56, 0}
}

type AchievementArea struct {
	Title                string   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	JumpUrl              string   `protobuf:"bytes,2,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	PicUrl               string   `protobuf:"bytes,3,opt,name=pic_url,json=picUrl,proto3" json:"pic_url,omitempty"`
	Status               uint32   `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AchievementArea) Reset()         { *m = AchievementArea{} }
func (m *AchievementArea) String() string { return proto.CompactTextString(m) }
func (*AchievementArea) ProtoMessage()    {}
func (*AchievementArea) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{0}
}
func (m *AchievementArea) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AchievementArea.Unmarshal(m, b)
}
func (m *AchievementArea) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AchievementArea.Marshal(b, m, deterministic)
}
func (dst *AchievementArea) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AchievementArea.Merge(dst, src)
}
func (m *AchievementArea) XXX_Size() int {
	return xxx_messageInfo_AchievementArea.Size(m)
}
func (m *AchievementArea) XXX_DiscardUnknown() {
	xxx_messageInfo_AchievementArea.DiscardUnknown(m)
}

var xxx_messageInfo_AchievementArea proto.InternalMessageInfo

func (m *AchievementArea) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *AchievementArea) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *AchievementArea) GetPicUrl() string {
	if m != nil {
		return m.PicUrl
	}
	return ""
}

func (m *AchievementArea) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type ActPresentDetail struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Num                  uint32   `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ActPresentDetail) Reset()         { *m = ActPresentDetail{} }
func (m *ActPresentDetail) String() string { return proto.CompactTextString(m) }
func (*ActPresentDetail) ProtoMessage()    {}
func (*ActPresentDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{1}
}
func (m *ActPresentDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActPresentDetail.Unmarshal(m, b)
}
func (m *ActPresentDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActPresentDetail.Marshal(b, m, deterministic)
}
func (dst *ActPresentDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActPresentDetail.Merge(dst, src)
}
func (m *ActPresentDetail) XXX_Size() int {
	return xxx_messageInfo_ActPresentDetail.Size(m)
}
func (m *ActPresentDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_ActPresentDetail.DiscardUnknown(m)
}

var xxx_messageInfo_ActPresentDetail proto.InternalMessageInfo

func (m *ActPresentDetail) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *ActPresentDetail) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

type ActPresentArea struct {
	Title                string              `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	JumpUrl              string              `protobuf:"bytes,2,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	Detail               []*ActPresentDetail `protobuf:"bytes,3,rep,name=detail,proto3" json:"detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *ActPresentArea) Reset()         { *m = ActPresentArea{} }
func (m *ActPresentArea) String() string { return proto.CompactTextString(m) }
func (*ActPresentArea) ProtoMessage()    {}
func (*ActPresentArea) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{2}
}
func (m *ActPresentArea) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActPresentArea.Unmarshal(m, b)
}
func (m *ActPresentArea) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActPresentArea.Marshal(b, m, deterministic)
}
func (dst *ActPresentArea) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActPresentArea.Merge(dst, src)
}
func (m *ActPresentArea) XXX_Size() int {
	return xxx_messageInfo_ActPresentArea.Size(m)
}
func (m *ActPresentArea) XXX_DiscardUnknown() {
	xxx_messageInfo_ActPresentArea.DiscardUnknown(m)
}

var xxx_messageInfo_ActPresentArea proto.InternalMessageInfo

func (m *ActPresentArea) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ActPresentArea) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *ActPresentArea) GetDetail() []*ActPresentDetail {
	if m != nil {
		return m.Detail
	}
	return nil
}

type ActFrameConf struct {
	Title                string   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	ResourcesUrl         string   `protobuf:"bytes,2,opt,name=resources_url,json=resourcesUrl,proto3" json:"resources_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ActFrameConf) Reset()         { *m = ActFrameConf{} }
func (m *ActFrameConf) String() string { return proto.CompactTextString(m) }
func (*ActFrameConf) ProtoMessage()    {}
func (*ActFrameConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{3}
}
func (m *ActFrameConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActFrameConf.Unmarshal(m, b)
}
func (m *ActFrameConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActFrameConf.Marshal(b, m, deterministic)
}
func (dst *ActFrameConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActFrameConf.Merge(dst, src)
}
func (m *ActFrameConf) XXX_Size() int {
	return xxx_messageInfo_ActFrameConf.Size(m)
}
func (m *ActFrameConf) XXX_DiscardUnknown() {
	xxx_messageInfo_ActFrameConf.DiscardUnknown(m)
}

var xxx_messageInfo_ActFrameConf proto.InternalMessageInfo

func (m *ActFrameConf) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ActFrameConf) GetResourcesUrl() string {
	if m != nil {
		return m.ResourcesUrl
	}
	return ""
}

type ActRemindConf struct {
	BeginTs              uint32   `protobuf:"varint,1,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,2,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	RemindText           string   `protobuf:"bytes,3,opt,name=remind_text,json=remindText,proto3" json:"remind_text,omitempty"`
	Duration             uint32   `protobuf:"varint,4,opt,name=duration,proto3" json:"duration,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ActRemindConf) Reset()         { *m = ActRemindConf{} }
func (m *ActRemindConf) String() string { return proto.CompactTextString(m) }
func (*ActRemindConf) ProtoMessage()    {}
func (*ActRemindConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{4}
}
func (m *ActRemindConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActRemindConf.Unmarshal(m, b)
}
func (m *ActRemindConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActRemindConf.Marshal(b, m, deterministic)
}
func (dst *ActRemindConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActRemindConf.Merge(dst, src)
}
func (m *ActRemindConf) XXX_Size() int {
	return xxx_messageInfo_ActRemindConf.Size(m)
}
func (m *ActRemindConf) XXX_DiscardUnknown() {
	xxx_messageInfo_ActRemindConf.DiscardUnknown(m)
}

var xxx_messageInfo_ActRemindConf proto.InternalMessageInfo

func (m *ActRemindConf) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *ActRemindConf) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *ActRemindConf) GetRemindText() string {
	if m != nil {
		return m.RemindText
	}
	return ""
}

func (m *ActRemindConf) GetDuration() uint32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

type GetUserActPresentAreaReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserActPresentAreaReq) Reset()         { *m = GetUserActPresentAreaReq{} }
func (m *GetUserActPresentAreaReq) String() string { return proto.CompactTextString(m) }
func (*GetUserActPresentAreaReq) ProtoMessage()    {}
func (*GetUserActPresentAreaReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{5}
}
func (m *GetUserActPresentAreaReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserActPresentAreaReq.Unmarshal(m, b)
}
func (m *GetUserActPresentAreaReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserActPresentAreaReq.Marshal(b, m, deterministic)
}
func (dst *GetUserActPresentAreaReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserActPresentAreaReq.Merge(dst, src)
}
func (m *GetUserActPresentAreaReq) XXX_Size() int {
	return xxx_messageInfo_GetUserActPresentAreaReq.Size(m)
}
func (m *GetUserActPresentAreaReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserActPresentAreaReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserActPresentAreaReq proto.InternalMessageInfo

func (m *GetUserActPresentAreaReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserActPresentAreaReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserActPresentAreaResp struct {
	BaseResp             *app.BaseResp    `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Frame                *ActFrameConf    `protobuf:"bytes,3,opt,name=frame,proto3" json:"frame,omitempty"`
	PresentArea          *ActPresentArea  `protobuf:"bytes,4,opt,name=present_area,json=presentArea,proto3" json:"present_area,omitempty"`
	Achievement          *AchievementArea `protobuf:"bytes,5,opt,name=achievement,proto3" json:"achievement,omitempty"`
	Remind               *ActRemindConf   `protobuf:"bytes,6,opt,name=remind,proto3" json:"remind,omitempty"`
	AreaSwitch           bool             `protobuf:"varint,7,opt,name=area_switch,json=areaSwitch,proto3" json:"area_switch,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetUserActPresentAreaResp) Reset()         { *m = GetUserActPresentAreaResp{} }
func (m *GetUserActPresentAreaResp) String() string { return proto.CompactTextString(m) }
func (*GetUserActPresentAreaResp) ProtoMessage()    {}
func (*GetUserActPresentAreaResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{6}
}
func (m *GetUserActPresentAreaResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserActPresentAreaResp.Unmarshal(m, b)
}
func (m *GetUserActPresentAreaResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserActPresentAreaResp.Marshal(b, m, deterministic)
}
func (dst *GetUserActPresentAreaResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserActPresentAreaResp.Merge(dst, src)
}
func (m *GetUserActPresentAreaResp) XXX_Size() int {
	return xxx_messageInfo_GetUserActPresentAreaResp.Size(m)
}
func (m *GetUserActPresentAreaResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserActPresentAreaResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserActPresentAreaResp proto.InternalMessageInfo

func (m *GetUserActPresentAreaResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserActPresentAreaResp) GetFrame() *ActFrameConf {
	if m != nil {
		return m.Frame
	}
	return nil
}

func (m *GetUserActPresentAreaResp) GetPresentArea() *ActPresentArea {
	if m != nil {
		return m.PresentArea
	}
	return nil
}

func (m *GetUserActPresentAreaResp) GetAchievement() *AchievementArea {
	if m != nil {
		return m.Achievement
	}
	return nil
}

func (m *GetUserActPresentAreaResp) GetRemind() *ActRemindConf {
	if m != nil {
		return m.Remind
	}
	return nil
}

func (m *GetUserActPresentAreaResp) GetAreaSwitch() bool {
	if m != nil {
		return m.AreaSwitch
	}
	return false
}

// 获取送礼需要弹窗的礼物列表
type GetNeedPopUpPresentListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetNeedPopUpPresentListReq) Reset()         { *m = GetNeedPopUpPresentListReq{} }
func (m *GetNeedPopUpPresentListReq) String() string { return proto.CompactTextString(m) }
func (*GetNeedPopUpPresentListReq) ProtoMessage()    {}
func (*GetNeedPopUpPresentListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{7}
}
func (m *GetNeedPopUpPresentListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNeedPopUpPresentListReq.Unmarshal(m, b)
}
func (m *GetNeedPopUpPresentListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNeedPopUpPresentListReq.Marshal(b, m, deterministic)
}
func (dst *GetNeedPopUpPresentListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNeedPopUpPresentListReq.Merge(dst, src)
}
func (m *GetNeedPopUpPresentListReq) XXX_Size() int {
	return xxx_messageInfo_GetNeedPopUpPresentListReq.Size(m)
}
func (m *GetNeedPopUpPresentListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNeedPopUpPresentListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNeedPopUpPresentListReq proto.InternalMessageInfo

func (m *GetNeedPopUpPresentListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetNeedPopUpPresentListResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	IdList               []uint32      `protobuf:"varint,2,rep,packed,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetNeedPopUpPresentListResp) Reset()         { *m = GetNeedPopUpPresentListResp{} }
func (m *GetNeedPopUpPresentListResp) String() string { return proto.CompactTextString(m) }
func (*GetNeedPopUpPresentListResp) ProtoMessage()    {}
func (*GetNeedPopUpPresentListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{8}
}
func (m *GetNeedPopUpPresentListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNeedPopUpPresentListResp.Unmarshal(m, b)
}
func (m *GetNeedPopUpPresentListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNeedPopUpPresentListResp.Marshal(b, m, deterministic)
}
func (dst *GetNeedPopUpPresentListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNeedPopUpPresentListResp.Merge(dst, src)
}
func (m *GetNeedPopUpPresentListResp) XXX_Size() int {
	return xxx_messageInfo_GetNeedPopUpPresentListResp.Size(m)
}
func (m *GetNeedPopUpPresentListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNeedPopUpPresentListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNeedPopUpPresentListResp proto.InternalMessageInfo

func (m *GetNeedPopUpPresentListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetNeedPopUpPresentListResp) GetIdList() []uint32 {
	if m != nil {
		return m.IdList
	}
	return nil
}

// 获取礼物的额外配置
type GetPresentExtraConfigReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetPresentExtraConfigReq) Reset()         { *m = GetPresentExtraConfigReq{} }
func (m *GetPresentExtraConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentExtraConfigReq) ProtoMessage()    {}
func (*GetPresentExtraConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{9}
}
func (m *GetPresentExtraConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentExtraConfigReq.Unmarshal(m, b)
}
func (m *GetPresentExtraConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentExtraConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentExtraConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentExtraConfigReq.Merge(dst, src)
}
func (m *GetPresentExtraConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentExtraConfigReq.Size(m)
}
func (m *GetPresentExtraConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentExtraConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentExtraConfigReq proto.InternalMessageInfo

func (m *GetPresentExtraConfigReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetPresentExtraConfigResp struct {
	BaseResp               *app.BaseResp           `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	LayerInfos             []*PresentFloatLayer    `protobuf:"bytes,2,rep,name=layer_infos,json=layerInfos,proto3" json:"layer_infos,omitempty"`
	FlashEffects           []*PresentFlashEffect   `protobuf:"bytes,3,rep,name=flash_effects,json=flashEffects,proto3" json:"flash_effects,omitempty"`
	FlashEffectConfigs     []*FlashEffectConfig    `protobuf:"bytes,4,rep,name=flash_effect_configs,json=flashEffectConfigs,proto3" json:"flash_effect_configs,omitempty"`
	LastUpdateTime         uint32                  `protobuf:"varint,5,opt,name=last_update_time,json=lastUpdateTime,proto3" json:"last_update_time,omitempty"`
	PresentEffectTimeInfos []*PresentEffectTime    `protobuf:"bytes,6,rep,name=present_effect_time_infos,json=presentEffectTimeInfos,proto3" json:"present_effect_time_infos,omitempty"`
	PrivilegePresentInfo   []*PrivilegePresentInfo `protobuf:"bytes,7,rep,name=privilege_present_info,json=privilegePresentInfo,proto3" json:"privilege_present_info,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}                `json:"-"`
	XXX_unrecognized       []byte                  `json:"-"`
	XXX_sizecache          int32                   `json:"-"`
}

func (m *GetPresentExtraConfigResp) Reset()         { *m = GetPresentExtraConfigResp{} }
func (m *GetPresentExtraConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentExtraConfigResp) ProtoMessage()    {}
func (*GetPresentExtraConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{10}
}
func (m *GetPresentExtraConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentExtraConfigResp.Unmarshal(m, b)
}
func (m *GetPresentExtraConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentExtraConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentExtraConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentExtraConfigResp.Merge(dst, src)
}
func (m *GetPresentExtraConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentExtraConfigResp.Size(m)
}
func (m *GetPresentExtraConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentExtraConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentExtraConfigResp proto.InternalMessageInfo

func (m *GetPresentExtraConfigResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetPresentExtraConfigResp) GetLayerInfos() []*PresentFloatLayer {
	if m != nil {
		return m.LayerInfos
	}
	return nil
}

func (m *GetPresentExtraConfigResp) GetFlashEffects() []*PresentFlashEffect {
	if m != nil {
		return m.FlashEffects
	}
	return nil
}

func (m *GetPresentExtraConfigResp) GetFlashEffectConfigs() []*FlashEffectConfig {
	if m != nil {
		return m.FlashEffectConfigs
	}
	return nil
}

func (m *GetPresentExtraConfigResp) GetLastUpdateTime() uint32 {
	if m != nil {
		return m.LastUpdateTime
	}
	return 0
}

func (m *GetPresentExtraConfigResp) GetPresentEffectTimeInfos() []*PresentEffectTime {
	if m != nil {
		return m.PresentEffectTimeInfos
	}
	return nil
}

func (m *GetPresentExtraConfigResp) GetPrivilegePresentInfo() []*PrivilegePresentInfo {
	if m != nil {
		return m.PrivilegePresentInfo
	}
	return nil
}

type PrivilegePresentInfo struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	EndTime              uint32   `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PrivilegePresentInfo) Reset()         { *m = PrivilegePresentInfo{} }
func (m *PrivilegePresentInfo) String() string { return proto.CompactTextString(m) }
func (*PrivilegePresentInfo) ProtoMessage()    {}
func (*PrivilegePresentInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{11}
}
func (m *PrivilegePresentInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PrivilegePresentInfo.Unmarshal(m, b)
}
func (m *PrivilegePresentInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PrivilegePresentInfo.Marshal(b, m, deterministic)
}
func (dst *PrivilegePresentInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PrivilegePresentInfo.Merge(dst, src)
}
func (m *PrivilegePresentInfo) XXX_Size() int {
	return xxx_messageInfo_PrivilegePresentInfo.Size(m)
}
func (m *PrivilegePresentInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PrivilegePresentInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PrivilegePresentInfo proto.InternalMessageInfo

func (m *PrivilegePresentInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *PrivilegePresentInfo) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type PresentFloatLayer struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	FloatImageUrl        string   `protobuf:"bytes,2,opt,name=float_image_url,json=floatImageUrl,proto3" json:"float_image_url,omitempty"`
	JumpUrl              string   `protobuf:"bytes,3,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	IsActivityUrl        bool     `protobuf:"varint,4,opt,name=is_activity_url,json=isActivityUrl,proto3" json:"is_activity_url,omitempty"`
	ShowChannelType      []uint32 `protobuf:"varint,5,rep,packed,name=show_channel_type,json=showChannelType,proto3" json:"show_channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentFloatLayer) Reset()         { *m = PresentFloatLayer{} }
func (m *PresentFloatLayer) String() string { return proto.CompactTextString(m) }
func (*PresentFloatLayer) ProtoMessage()    {}
func (*PresentFloatLayer) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{12}
}
func (m *PresentFloatLayer) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentFloatLayer.Unmarshal(m, b)
}
func (m *PresentFloatLayer) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentFloatLayer.Marshal(b, m, deterministic)
}
func (dst *PresentFloatLayer) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentFloatLayer.Merge(dst, src)
}
func (m *PresentFloatLayer) XXX_Size() int {
	return xxx_messageInfo_PresentFloatLayer.Size(m)
}
func (m *PresentFloatLayer) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentFloatLayer.DiscardUnknown(m)
}

var xxx_messageInfo_PresentFloatLayer proto.InternalMessageInfo

func (m *PresentFloatLayer) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *PresentFloatLayer) GetFloatImageUrl() string {
	if m != nil {
		return m.FloatImageUrl
	}
	return ""
}

func (m *PresentFloatLayer) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *PresentFloatLayer) GetIsActivityUrl() bool {
	if m != nil {
		return m.IsActivityUrl
	}
	return false
}

func (m *PresentFloatLayer) GetShowChannelType() []uint32 {
	if m != nil {
		return m.ShowChannelType
	}
	return nil
}

type PresentFlashEffect struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	FlashId              uint32   `protobuf:"varint,2,opt,name=flash_id,json=flashId,proto3" json:"flash_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentFlashEffect) Reset()         { *m = PresentFlashEffect{} }
func (m *PresentFlashEffect) String() string { return proto.CompactTextString(m) }
func (*PresentFlashEffect) ProtoMessage()    {}
func (*PresentFlashEffect) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{13}
}
func (m *PresentFlashEffect) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentFlashEffect.Unmarshal(m, b)
}
func (m *PresentFlashEffect) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentFlashEffect.Marshal(b, m, deterministic)
}
func (dst *PresentFlashEffect) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentFlashEffect.Merge(dst, src)
}
func (m *PresentFlashEffect) XXX_Size() int {
	return xxx_messageInfo_PresentFlashEffect.Size(m)
}
func (m *PresentFlashEffect) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentFlashEffect.DiscardUnknown(m)
}

var xxx_messageInfo_PresentFlashEffect proto.InternalMessageInfo

func (m *PresentFlashEffect) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *PresentFlashEffect) GetFlashId() uint32 {
	if m != nil {
		return m.FlashId
	}
	return 0
}

type FlashEffectConfig struct {
	FlashId              uint32   `protobuf:"varint,1,opt,name=flash_id,json=flashId,proto3" json:"flash_id,omitempty"`
	FlashName            string   `protobuf:"bytes,2,opt,name=flash_name,json=flashName,proto3" json:"flash_name,omitempty"`
	FlashUrl             string   `protobuf:"bytes,3,opt,name=flash_url,json=flashUrl,proto3" json:"flash_url,omitempty"`
	FlashMd5             string   `protobuf:"bytes,4,opt,name=flash_md5,json=flashMd5,proto3" json:"flash_md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FlashEffectConfig) Reset()         { *m = FlashEffectConfig{} }
func (m *FlashEffectConfig) String() string { return proto.CompactTextString(m) }
func (*FlashEffectConfig) ProtoMessage()    {}
func (*FlashEffectConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{14}
}
func (m *FlashEffectConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FlashEffectConfig.Unmarshal(m, b)
}
func (m *FlashEffectConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FlashEffectConfig.Marshal(b, m, deterministic)
}
func (dst *FlashEffectConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FlashEffectConfig.Merge(dst, src)
}
func (m *FlashEffectConfig) XXX_Size() int {
	return xxx_messageInfo_FlashEffectConfig.Size(m)
}
func (m *FlashEffectConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_FlashEffectConfig.DiscardUnknown(m)
}

var xxx_messageInfo_FlashEffectConfig proto.InternalMessageInfo

func (m *FlashEffectConfig) GetFlashId() uint32 {
	if m != nil {
		return m.FlashId
	}
	return 0
}

func (m *FlashEffectConfig) GetFlashName() string {
	if m != nil {
		return m.FlashName
	}
	return ""
}

func (m *FlashEffectConfig) GetFlashUrl() string {
	if m != nil {
		return m.FlashUrl
	}
	return ""
}

func (m *FlashEffectConfig) GetFlashMd5() string {
	if m != nil {
		return m.FlashMd5
	}
	return ""
}

type PresentEffectTimePush struct {
	GiftId               uint32                 `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	EffectEnd            uint32                 `protobuf:"varint,2,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	EffectInfo           *PresentEffectTimeInfo `protobuf:"bytes,3,opt,name=effect_info,json=effectInfo,proto3" json:"effect_info,omitempty"`
	IsNewLevel           bool                   `protobuf:"varint,4,opt,name=is_new_level,json=isNewLevel,proto3" json:"is_new_level,omitempty"`
	NewLevelDayCount     uint32                 `protobuf:"varint,5,opt,name=new_level_day_count,json=newLevelDayCount,proto3" json:"new_level_day_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *PresentEffectTimePush) Reset()         { *m = PresentEffectTimePush{} }
func (m *PresentEffectTimePush) String() string { return proto.CompactTextString(m) }
func (*PresentEffectTimePush) ProtoMessage()    {}
func (*PresentEffectTimePush) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{15}
}
func (m *PresentEffectTimePush) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentEffectTimePush.Unmarshal(m, b)
}
func (m *PresentEffectTimePush) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentEffectTimePush.Marshal(b, m, deterministic)
}
func (dst *PresentEffectTimePush) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentEffectTimePush.Merge(dst, src)
}
func (m *PresentEffectTimePush) XXX_Size() int {
	return xxx_messageInfo_PresentEffectTimePush.Size(m)
}
func (m *PresentEffectTimePush) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentEffectTimePush.DiscardUnknown(m)
}

var xxx_messageInfo_PresentEffectTimePush proto.InternalMessageInfo

func (m *PresentEffectTimePush) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *PresentEffectTimePush) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

func (m *PresentEffectTimePush) GetEffectInfo() *PresentEffectTimeInfo {
	if m != nil {
		return m.EffectInfo
	}
	return nil
}

func (m *PresentEffectTimePush) GetIsNewLevel() bool {
	if m != nil {
		return m.IsNewLevel
	}
	return false
}

func (m *PresentEffectTimePush) GetNewLevelDayCount() uint32 {
	if m != nil {
		return m.NewLevelDayCount
	}
	return 0
}

type PresentEffectTime struct {
	GiftId               uint32                 `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	EffectEnd            uint32                 `protobuf:"varint,2,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	EffectInfo           *PresentEffectTimeInfo `protobuf:"bytes,3,opt,name=effect_info,json=effectInfo,proto3" json:"effect_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *PresentEffectTime) Reset()         { *m = PresentEffectTime{} }
func (m *PresentEffectTime) String() string { return proto.CompactTextString(m) }
func (*PresentEffectTime) ProtoMessage()    {}
func (*PresentEffectTime) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{16}
}
func (m *PresentEffectTime) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentEffectTime.Unmarshal(m, b)
}
func (m *PresentEffectTime) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentEffectTime.Marshal(b, m, deterministic)
}
func (dst *PresentEffectTime) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentEffectTime.Merge(dst, src)
}
func (m *PresentEffectTime) XXX_Size() int {
	return xxx_messageInfo_PresentEffectTime.Size(m)
}
func (m *PresentEffectTime) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentEffectTime.DiscardUnknown(m)
}

var xxx_messageInfo_PresentEffectTime proto.InternalMessageInfo

func (m *PresentEffectTime) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *PresentEffectTime) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

func (m *PresentEffectTime) GetEffectInfo() *PresentEffectTimeInfo {
	if m != nil {
		return m.EffectInfo
	}
	return nil
}

type PresentEffectTimeInfo struct {
	NowCount              uint32   `protobuf:"varint,1,opt,name=now_count,json=nowCount,proto3" json:"now_count,omitempty"`
	NextLevelSendCount    uint32   `protobuf:"varint,2,opt,name=next_level_send_count,json=nextLevelSendCount,proto3" json:"next_level_send_count,omitempty"`
	NextLevelDayCount     uint32   `protobuf:"varint,3,opt,name=next_level_day_count,json=nextLevelDayCount,proto3" json:"next_level_day_count,omitempty"`
	MaxLevelSendCount     uint32   `protobuf:"varint,4,opt,name=max_level_send_count,json=maxLevelSendCount,proto3" json:"max_level_send_count,omitempty"`
	IsMaxLevel            bool     `protobuf:"varint,5,opt,name=is_max_level,json=isMaxLevel,proto3" json:"is_max_level,omitempty"`
	NoLimitExpireDayCount uint32   `protobuf:"varint,6,opt,name=no_limit_expire_day_count,json=noLimitExpireDayCount,proto3" json:"no_limit_expire_day_count,omitempty"`
	LastSendTs            uint32   `protobuf:"varint,7,opt,name=last_send_ts,json=lastSendTs,proto3" json:"last_send_ts,omitempty"`
	MaxLevelDayCount      uint32   `protobuf:"varint,8,opt,name=max_level_day_count,json=maxLevelDayCount,proto3" json:"max_level_day_count,omitempty"`
	EffectEndOnShelf      uint32   `protobuf:"varint,9,opt,name=effect_end_on_shelf,json=effectEndOnShelf,proto3" json:"effect_end_on_shelf,omitempty"`
	NowLevelDayCount      uint32   `protobuf:"varint,10,opt,name=now_level_day_count,json=nowLevelDayCount,proto3" json:"now_level_day_count,omitempty"`
	NoticeNoLimitExpire   bool     `protobuf:"varint,11,opt,name=notice_no_limit_expire,json=noticeNoLimitExpire,proto3" json:"notice_no_limit_expire,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *PresentEffectTimeInfo) Reset()         { *m = PresentEffectTimeInfo{} }
func (m *PresentEffectTimeInfo) String() string { return proto.CompactTextString(m) }
func (*PresentEffectTimeInfo) ProtoMessage()    {}
func (*PresentEffectTimeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{17}
}
func (m *PresentEffectTimeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentEffectTimeInfo.Unmarshal(m, b)
}
func (m *PresentEffectTimeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentEffectTimeInfo.Marshal(b, m, deterministic)
}
func (dst *PresentEffectTimeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentEffectTimeInfo.Merge(dst, src)
}
func (m *PresentEffectTimeInfo) XXX_Size() int {
	return xxx_messageInfo_PresentEffectTimeInfo.Size(m)
}
func (m *PresentEffectTimeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentEffectTimeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PresentEffectTimeInfo proto.InternalMessageInfo

func (m *PresentEffectTimeInfo) GetNowCount() uint32 {
	if m != nil {
		return m.NowCount
	}
	return 0
}

func (m *PresentEffectTimeInfo) GetNextLevelSendCount() uint32 {
	if m != nil {
		return m.NextLevelSendCount
	}
	return 0
}

func (m *PresentEffectTimeInfo) GetNextLevelDayCount() uint32 {
	if m != nil {
		return m.NextLevelDayCount
	}
	return 0
}

func (m *PresentEffectTimeInfo) GetMaxLevelSendCount() uint32 {
	if m != nil {
		return m.MaxLevelSendCount
	}
	return 0
}

func (m *PresentEffectTimeInfo) GetIsMaxLevel() bool {
	if m != nil {
		return m.IsMaxLevel
	}
	return false
}

func (m *PresentEffectTimeInfo) GetNoLimitExpireDayCount() uint32 {
	if m != nil {
		return m.NoLimitExpireDayCount
	}
	return 0
}

func (m *PresentEffectTimeInfo) GetLastSendTs() uint32 {
	if m != nil {
		return m.LastSendTs
	}
	return 0
}

func (m *PresentEffectTimeInfo) GetMaxLevelDayCount() uint32 {
	if m != nil {
		return m.MaxLevelDayCount
	}
	return 0
}

func (m *PresentEffectTimeInfo) GetEffectEndOnShelf() uint32 {
	if m != nil {
		return m.EffectEndOnShelf
	}
	return 0
}

func (m *PresentEffectTimeInfo) GetNowLevelDayCount() uint32 {
	if m != nil {
		return m.NowLevelDayCount
	}
	return 0
}

func (m *PresentEffectTimeInfo) GetNoticeNoLimitExpire() bool {
	if m != nil {
		return m.NoticeNoLimitExpire
	}
	return false
}

type PresentEffectTimeLevelInfo struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	LevelSendCount       uint32   `protobuf:"varint,2,opt,name=level_send_count,json=levelSendCount,proto3" json:"level_send_count,omitempty"`
	LevelDayCount        uint32   `protobuf:"varint,3,opt,name=level_day_count,json=levelDayCount,proto3" json:"level_day_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentEffectTimeLevelInfo) Reset()         { *m = PresentEffectTimeLevelInfo{} }
func (m *PresentEffectTimeLevelInfo) String() string { return proto.CompactTextString(m) }
func (*PresentEffectTimeLevelInfo) ProtoMessage()    {}
func (*PresentEffectTimeLevelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{18}
}
func (m *PresentEffectTimeLevelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentEffectTimeLevelInfo.Unmarshal(m, b)
}
func (m *PresentEffectTimeLevelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentEffectTimeLevelInfo.Marshal(b, m, deterministic)
}
func (dst *PresentEffectTimeLevelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentEffectTimeLevelInfo.Merge(dst, src)
}
func (m *PresentEffectTimeLevelInfo) XXX_Size() int {
	return xxx_messageInfo_PresentEffectTimeLevelInfo.Size(m)
}
func (m *PresentEffectTimeLevelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentEffectTimeLevelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PresentEffectTimeLevelInfo proto.InternalMessageInfo

func (m *PresentEffectTimeLevelInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *PresentEffectTimeLevelInfo) GetLevelSendCount() uint32 {
	if m != nil {
		return m.LevelSendCount
	}
	return 0
}

func (m *PresentEffectTimeLevelInfo) GetLevelDayCount() uint32 {
	if m != nil {
		return m.LevelDayCount
	}
	return 0
}

type GetPresentEffectTimeDetailReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	GiftId               uint32       `protobuf:"varint,2,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetPresentEffectTimeDetailReq) Reset()         { *m = GetPresentEffectTimeDetailReq{} }
func (m *GetPresentEffectTimeDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentEffectTimeDetailReq) ProtoMessage()    {}
func (*GetPresentEffectTimeDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{19}
}
func (m *GetPresentEffectTimeDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentEffectTimeDetailReq.Unmarshal(m, b)
}
func (m *GetPresentEffectTimeDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentEffectTimeDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentEffectTimeDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentEffectTimeDetailReq.Merge(dst, src)
}
func (m *GetPresentEffectTimeDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentEffectTimeDetailReq.Size(m)
}
func (m *GetPresentEffectTimeDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentEffectTimeDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentEffectTimeDetailReq proto.InternalMessageInfo

func (m *GetPresentEffectTimeDetailReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetPresentEffectTimeDetailReq) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

type GetPresentEffectTimeDetailResp struct {
	BaseResp              *app.BaseResp                 `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	GiftId                uint32                        `protobuf:"varint,2,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	NowCount              uint32                        `protobuf:"varint,3,opt,name=now_count,json=nowCount,proto3" json:"now_count,omitempty"`
	LevelInfo             []*PresentEffectTimeLevelInfo `protobuf:"bytes,4,rep,name=level_info,json=levelInfo,proto3" json:"level_info,omitempty"`
	NoLimitExpireDayCount uint32                        `protobuf:"varint,5,opt,name=no_limit_expire_day_count,json=noLimitExpireDayCount,proto3" json:"no_limit_expire_day_count,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}                      `json:"-"`
	XXX_unrecognized      []byte                        `json:"-"`
	XXX_sizecache         int32                         `json:"-"`
}

func (m *GetPresentEffectTimeDetailResp) Reset()         { *m = GetPresentEffectTimeDetailResp{} }
func (m *GetPresentEffectTimeDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentEffectTimeDetailResp) ProtoMessage()    {}
func (*GetPresentEffectTimeDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{20}
}
func (m *GetPresentEffectTimeDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentEffectTimeDetailResp.Unmarshal(m, b)
}
func (m *GetPresentEffectTimeDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentEffectTimeDetailResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentEffectTimeDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentEffectTimeDetailResp.Merge(dst, src)
}
func (m *GetPresentEffectTimeDetailResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentEffectTimeDetailResp.Size(m)
}
func (m *GetPresentEffectTimeDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentEffectTimeDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentEffectTimeDetailResp proto.InternalMessageInfo

func (m *GetPresentEffectTimeDetailResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetPresentEffectTimeDetailResp) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *GetPresentEffectTimeDetailResp) GetNowCount() uint32 {
	if m != nil {
		return m.NowCount
	}
	return 0
}

func (m *GetPresentEffectTimeDetailResp) GetLevelInfo() []*PresentEffectTimeLevelInfo {
	if m != nil {
		return m.LevelInfo
	}
	return nil
}

func (m *GetPresentEffectTimeDetailResp) GetNoLimitExpireDayCount() uint32 {
	if m != nil {
		return m.NoLimitExpireDayCount
	}
	return 0
}

type GetCustomizedPresentListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetCustomizedPresentListReq) Reset()         { *m = GetCustomizedPresentListReq{} }
func (m *GetCustomizedPresentListReq) String() string { return proto.CompactTextString(m) }
func (*GetCustomizedPresentListReq) ProtoMessage()    {}
func (*GetCustomizedPresentListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{21}
}
func (m *GetCustomizedPresentListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCustomizedPresentListReq.Unmarshal(m, b)
}
func (m *GetCustomizedPresentListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCustomizedPresentListReq.Marshal(b, m, deterministic)
}
func (dst *GetCustomizedPresentListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCustomizedPresentListReq.Merge(dst, src)
}
func (m *GetCustomizedPresentListReq) XXX_Size() int {
	return xxx_messageInfo_GetCustomizedPresentListReq.Size(m)
}
func (m *GetCustomizedPresentListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCustomizedPresentListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCustomizedPresentListReq proto.InternalMessageInfo

func (m *GetCustomizedPresentListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetCustomizedPresentListResp struct {
	BaseResp             *app.BaseResp            `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	PresentInfo          []*CustomizedPresentInfo `protobuf:"bytes,2,rep,name=present_info,json=presentInfo,proto3" json:"present_info,omitempty"`
	LastUpdateTs         uint32                   `protobuf:"varint,3,opt,name=last_update_ts,json=lastUpdateTs,proto3" json:"last_update_ts,omitempty"`
	PresentEffectAppend  []*PresentEffectAppend   `protobuf:"bytes,4,rep,name=present_effect_append,json=presentEffectAppend,proto3" json:"present_effect_append,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetCustomizedPresentListResp) Reset()         { *m = GetCustomizedPresentListResp{} }
func (m *GetCustomizedPresentListResp) String() string { return proto.CompactTextString(m) }
func (*GetCustomizedPresentListResp) ProtoMessage()    {}
func (*GetCustomizedPresentListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{22}
}
func (m *GetCustomizedPresentListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCustomizedPresentListResp.Unmarshal(m, b)
}
func (m *GetCustomizedPresentListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCustomizedPresentListResp.Marshal(b, m, deterministic)
}
func (dst *GetCustomizedPresentListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCustomizedPresentListResp.Merge(dst, src)
}
func (m *GetCustomizedPresentListResp) XXX_Size() int {
	return xxx_messageInfo_GetCustomizedPresentListResp.Size(m)
}
func (m *GetCustomizedPresentListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCustomizedPresentListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCustomizedPresentListResp proto.InternalMessageInfo

func (m *GetCustomizedPresentListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetCustomizedPresentListResp) GetPresentInfo() []*CustomizedPresentInfo {
	if m != nil {
		return m.PresentInfo
	}
	return nil
}

func (m *GetCustomizedPresentListResp) GetLastUpdateTs() uint32 {
	if m != nil {
		return m.LastUpdateTs
	}
	return 0
}

func (m *GetCustomizedPresentListResp) GetPresentEffectAppend() []*PresentEffectAppend {
	if m != nil {
		return m.PresentEffectAppend
	}
	return nil
}

// 自定义礼物的信息，部分礼物本身的配置信息可以直接从礼物缓存中获取
type CustomizedPresentInfo struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CustomOption         []string `protobuf:"bytes,2,rep,name=custom_option,json=customOption,proto3" json:"custom_option,omitempty"`
	PresentId            uint32   `protobuf:"varint,3,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	CmsUrl               string   `protobuf:"bytes,4,opt,name=cms_url,json=cmsUrl,proto3" json:"cms_url,omitempty"`
	HasAuthority         bool     `protobuf:"varint,5,opt,name=has_authority,json=hasAuthority,proto3" json:"has_authority,omitempty"`
	HasNewCustom         bool     `protobuf:"varint,6,opt,name=has_new_custom,json=hasNewCustom,proto3" json:"has_new_custom,omitempty"`
	EffectBegin          uint32   `protobuf:"varint,7,opt,name=effect_begin,json=effectBegin,proto3" json:"effect_begin,omitempty"`
	EffectEnd            uint32   `protobuf:"varint,8,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CustomizedPresentInfo) Reset()         { *m = CustomizedPresentInfo{} }
func (m *CustomizedPresentInfo) String() string { return proto.CompactTextString(m) }
func (*CustomizedPresentInfo) ProtoMessage()    {}
func (*CustomizedPresentInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{23}
}
func (m *CustomizedPresentInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CustomizedPresentInfo.Unmarshal(m, b)
}
func (m *CustomizedPresentInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CustomizedPresentInfo.Marshal(b, m, deterministic)
}
func (dst *CustomizedPresentInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomizedPresentInfo.Merge(dst, src)
}
func (m *CustomizedPresentInfo) XXX_Size() int {
	return xxx_messageInfo_CustomizedPresentInfo.Size(m)
}
func (m *CustomizedPresentInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomizedPresentInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CustomizedPresentInfo proto.InternalMessageInfo

func (m *CustomizedPresentInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CustomizedPresentInfo) GetCustomOption() []string {
	if m != nil {
		return m.CustomOption
	}
	return nil
}

func (m *CustomizedPresentInfo) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

func (m *CustomizedPresentInfo) GetCmsUrl() string {
	if m != nil {
		return m.CmsUrl
	}
	return ""
}

func (m *CustomizedPresentInfo) GetHasAuthority() bool {
	if m != nil {
		return m.HasAuthority
	}
	return false
}

func (m *CustomizedPresentInfo) GetHasNewCustom() bool {
	if m != nil {
		return m.HasNewCustom
	}
	return false
}

func (m *CustomizedPresentInfo) GetEffectBegin() uint32 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *CustomizedPresentInfo) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

type GetCustomizedPresentDetailReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Id                   uint32       `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetCustomizedPresentDetailReq) Reset()         { *m = GetCustomizedPresentDetailReq{} }
func (m *GetCustomizedPresentDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetCustomizedPresentDetailReq) ProtoMessage()    {}
func (*GetCustomizedPresentDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{24}
}
func (m *GetCustomizedPresentDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCustomizedPresentDetailReq.Unmarshal(m, b)
}
func (m *GetCustomizedPresentDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCustomizedPresentDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetCustomizedPresentDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCustomizedPresentDetailReq.Merge(dst, src)
}
func (m *GetCustomizedPresentDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetCustomizedPresentDetailReq.Size(m)
}
func (m *GetCustomizedPresentDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCustomizedPresentDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCustomizedPresentDetailReq proto.InternalMessageInfo

func (m *GetCustomizedPresentDetailReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetCustomizedPresentDetailReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetCustomizedPresentDetailResp struct {
	BaseResp             *app.BaseResp            `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	PresentDetail        *CustomizedPresentDetail `protobuf:"bytes,2,opt,name=present_detail,json=presentDetail,proto3" json:"present_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetCustomizedPresentDetailResp) Reset()         { *m = GetCustomizedPresentDetailResp{} }
func (m *GetCustomizedPresentDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetCustomizedPresentDetailResp) ProtoMessage()    {}
func (*GetCustomizedPresentDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{25}
}
func (m *GetCustomizedPresentDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCustomizedPresentDetailResp.Unmarshal(m, b)
}
func (m *GetCustomizedPresentDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCustomizedPresentDetailResp.Marshal(b, m, deterministic)
}
func (dst *GetCustomizedPresentDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCustomizedPresentDetailResp.Merge(dst, src)
}
func (m *GetCustomizedPresentDetailResp) XXX_Size() int {
	return xxx_messageInfo_GetCustomizedPresentDetailResp.Size(m)
}
func (m *GetCustomizedPresentDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCustomizedPresentDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCustomizedPresentDetailResp proto.InternalMessageInfo

func (m *GetCustomizedPresentDetailResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetCustomizedPresentDetailResp) GetPresentDetail() *CustomizedPresentDetail {
	if m != nil {
		return m.PresentDetail
	}
	return nil
}

type CustomizedPresentDetail struct {
	Id           uint32          `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CustomOption []*CustomOption `protobuf:"bytes,2,rep,name=custom_option,json=customOption,proto3" json:"custom_option,omitempty"`
	// uint32 present_id = 3; // 子礼物id
	// 另一个做法，客户端本地计算，好处延迟低，坏处是逻辑不方便更改：
	CustomMethod map[string]uint32 `protobuf:"bytes,3,rep,name=custom_method,json=customMethod,proto3" json:"custom_method,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	// 组件选择与子礼物id的对应公式, key: 类似 1_2_3 的字符串，下划线分割，代表组件id1、2、3分别选择样式1、2、3; value: 对应的子礼物id
	UserLevel            uint32                           `protobuf:"varint,4,opt,name=user_level,json=userLevel,proto3" json:"user_level,omitempty"`
	LevelText            string                           `protobuf:"bytes,5,opt,name=level_text,json=levelText,proto3" json:"level_text,omitempty"`
	ColorfulText         string                           `protobuf:"bytes,6,opt,name=colorful_text,json=colorfulText,proto3" json:"colorful_text,omitempty"`
	PreviewMap           map[uint32]*CustomPresentPreview `protobuf:"bytes,7,rep,name=preview_map,json=previewMap,proto3" json:"preview_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *CustomizedPresentDetail) Reset()         { *m = CustomizedPresentDetail{} }
func (m *CustomizedPresentDetail) String() string { return proto.CompactTextString(m) }
func (*CustomizedPresentDetail) ProtoMessage()    {}
func (*CustomizedPresentDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{26}
}
func (m *CustomizedPresentDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CustomizedPresentDetail.Unmarshal(m, b)
}
func (m *CustomizedPresentDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CustomizedPresentDetail.Marshal(b, m, deterministic)
}
func (dst *CustomizedPresentDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomizedPresentDetail.Merge(dst, src)
}
func (m *CustomizedPresentDetail) XXX_Size() int {
	return xxx_messageInfo_CustomizedPresentDetail.Size(m)
}
func (m *CustomizedPresentDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomizedPresentDetail.DiscardUnknown(m)
}

var xxx_messageInfo_CustomizedPresentDetail proto.InternalMessageInfo

func (m *CustomizedPresentDetail) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CustomizedPresentDetail) GetCustomOption() []*CustomOption {
	if m != nil {
		return m.CustomOption
	}
	return nil
}

func (m *CustomizedPresentDetail) GetCustomMethod() map[string]uint32 {
	if m != nil {
		return m.CustomMethod
	}
	return nil
}

func (m *CustomizedPresentDetail) GetUserLevel() uint32 {
	if m != nil {
		return m.UserLevel
	}
	return 0
}

func (m *CustomizedPresentDetail) GetLevelText() string {
	if m != nil {
		return m.LevelText
	}
	return ""
}

func (m *CustomizedPresentDetail) GetColorfulText() string {
	if m != nil {
		return m.ColorfulText
	}
	return ""
}

func (m *CustomizedPresentDetail) GetPreviewMap() map[uint32]*CustomPresentPreview {
	if m != nil {
		return m.PreviewMap
	}
	return nil
}

type CustomPresentPreview struct {
	PreviewType          uint32   `protobuf:"varint,1,opt,name=preview_type,json=previewType,proto3" json:"preview_type,omitempty"`
	PreviewUrl           string   `protobuf:"bytes,2,opt,name=preview_url,json=previewUrl,proto3" json:"preview_url,omitempty"`
	PreviewMd5           string   `protobuf:"bytes,3,opt,name=preview_md5,json=previewMd5,proto3" json:"preview_md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CustomPresentPreview) Reset()         { *m = CustomPresentPreview{} }
func (m *CustomPresentPreview) String() string { return proto.CompactTextString(m) }
func (*CustomPresentPreview) ProtoMessage()    {}
func (*CustomPresentPreview) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{27}
}
func (m *CustomPresentPreview) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CustomPresentPreview.Unmarshal(m, b)
}
func (m *CustomPresentPreview) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CustomPresentPreview.Marshal(b, m, deterministic)
}
func (dst *CustomPresentPreview) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomPresentPreview.Merge(dst, src)
}
func (m *CustomPresentPreview) XXX_Size() int {
	return xxx_messageInfo_CustomPresentPreview.Size(m)
}
func (m *CustomPresentPreview) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomPresentPreview.DiscardUnknown(m)
}

var xxx_messageInfo_CustomPresentPreview proto.InternalMessageInfo

func (m *CustomPresentPreview) GetPreviewType() uint32 {
	if m != nil {
		return m.PreviewType
	}
	return 0
}

func (m *CustomPresentPreview) GetPreviewUrl() string {
	if m != nil {
		return m.PreviewUrl
	}
	return ""
}

func (m *CustomPresentPreview) GetPreviewMd5() string {
	if m != nil {
		return m.PreviewMd5
	}
	return ""
}

type CustomOption struct {
	CustomId             uint32        `protobuf:"varint,1,opt,name=custom_id,json=customId,proto3" json:"custom_id,omitempty"`
	CustomName           string        `protobuf:"bytes,2,opt,name=custom_name,json=customName,proto3" json:"custom_name,omitempty"`
	OptionInfo           []*OptionInfo `protobuf:"bytes,3,rep,name=option_info,json=optionInfo,proto3" json:"option_info,omitempty"`
	CustomText           string        `protobuf:"bytes,4,opt,name=custom_text,json=customText,proto3" json:"custom_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CustomOption) Reset()         { *m = CustomOption{} }
func (m *CustomOption) String() string { return proto.CompactTextString(m) }
func (*CustomOption) ProtoMessage()    {}
func (*CustomOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{28}
}
func (m *CustomOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CustomOption.Unmarshal(m, b)
}
func (m *CustomOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CustomOption.Marshal(b, m, deterministic)
}
func (dst *CustomOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomOption.Merge(dst, src)
}
func (m *CustomOption) XXX_Size() int {
	return xxx_messageInfo_CustomOption.Size(m)
}
func (m *CustomOption) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomOption.DiscardUnknown(m)
}

var xxx_messageInfo_CustomOption proto.InternalMessageInfo

func (m *CustomOption) GetCustomId() uint32 {
	if m != nil {
		return m.CustomId
	}
	return 0
}

func (m *CustomOption) GetCustomName() string {
	if m != nil {
		return m.CustomName
	}
	return ""
}

func (m *CustomOption) GetOptionInfo() []*OptionInfo {
	if m != nil {
		return m.OptionInfo
	}
	return nil
}

func (m *CustomOption) GetCustomText() string {
	if m != nil {
		return m.CustomText
	}
	return ""
}

type OptionInfo struct {
	OptionId             uint32   `protobuf:"varint,1,opt,name=option_id,json=optionId,proto3" json:"option_id,omitempty"`
	OptionName           string   `protobuf:"bytes,2,opt,name=option_name,json=optionName,proto3" json:"option_name,omitempty"`
	OptionLevel          uint32   `protobuf:"varint,3,opt,name=option_level,json=optionLevel,proto3" json:"option_level,omitempty"`
	IsNew                bool     `protobuf:"varint,4,opt,name=is_new,json=isNew,proto3" json:"is_new,omitempty"`
	IsActive             bool     `protobuf:"varint,5,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OptionInfo) Reset()         { *m = OptionInfo{} }
func (m *OptionInfo) String() string { return proto.CompactTextString(m) }
func (*OptionInfo) ProtoMessage()    {}
func (*OptionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{29}
}
func (m *OptionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OptionInfo.Unmarshal(m, b)
}
func (m *OptionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OptionInfo.Marshal(b, m, deterministic)
}
func (dst *OptionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OptionInfo.Merge(dst, src)
}
func (m *OptionInfo) XXX_Size() int {
	return xxx_messageInfo_OptionInfo.Size(m)
}
func (m *OptionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OptionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OptionInfo proto.InternalMessageInfo

func (m *OptionInfo) GetOptionId() uint32 {
	if m != nil {
		return m.OptionId
	}
	return 0
}

func (m *OptionInfo) GetOptionName() string {
	if m != nil {
		return m.OptionName
	}
	return ""
}

func (m *OptionInfo) GetOptionLevel() uint32 {
	if m != nil {
		return m.OptionLevel
	}
	return 0
}

func (m *OptionInfo) GetIsNew() bool {
	if m != nil {
		return m.IsNew
	}
	return false
}

func (m *OptionInfo) GetIsActive() bool {
	if m != nil {
		return m.IsActive
	}
	return false
}

type ReportCustomOptionChooseReq struct {
	BaseReq              *app.BaseReq  `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Id                   uint32        `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	CustomPair           []*CustomPair `protobuf:"bytes,3,rep,name=custom_pair,json=customPair,proto3" json:"custom_pair,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ReportCustomOptionChooseReq) Reset()         { *m = ReportCustomOptionChooseReq{} }
func (m *ReportCustomOptionChooseReq) String() string { return proto.CompactTextString(m) }
func (*ReportCustomOptionChooseReq) ProtoMessage()    {}
func (*ReportCustomOptionChooseReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{30}
}
func (m *ReportCustomOptionChooseReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportCustomOptionChooseReq.Unmarshal(m, b)
}
func (m *ReportCustomOptionChooseReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportCustomOptionChooseReq.Marshal(b, m, deterministic)
}
func (dst *ReportCustomOptionChooseReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportCustomOptionChooseReq.Merge(dst, src)
}
func (m *ReportCustomOptionChooseReq) XXX_Size() int {
	return xxx_messageInfo_ReportCustomOptionChooseReq.Size(m)
}
func (m *ReportCustomOptionChooseReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportCustomOptionChooseReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportCustomOptionChooseReq proto.InternalMessageInfo

func (m *ReportCustomOptionChooseReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ReportCustomOptionChooseReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ReportCustomOptionChooseReq) GetCustomPair() []*CustomPair {
	if m != nil {
		return m.CustomPair
	}
	return nil
}

type ReportCustomOptionChooseResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ReportCustomOptionChooseResp) Reset()         { *m = ReportCustomOptionChooseResp{} }
func (m *ReportCustomOptionChooseResp) String() string { return proto.CompactTextString(m) }
func (*ReportCustomOptionChooseResp) ProtoMessage()    {}
func (*ReportCustomOptionChooseResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{31}
}
func (m *ReportCustomOptionChooseResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportCustomOptionChooseResp.Unmarshal(m, b)
}
func (m *ReportCustomOptionChooseResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportCustomOptionChooseResp.Marshal(b, m, deterministic)
}
func (dst *ReportCustomOptionChooseResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportCustomOptionChooseResp.Merge(dst, src)
}
func (m *ReportCustomOptionChooseResp) XXX_Size() int {
	return xxx_messageInfo_ReportCustomOptionChooseResp.Size(m)
}
func (m *ReportCustomOptionChooseResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportCustomOptionChooseResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportCustomOptionChooseResp proto.InternalMessageInfo

func (m *ReportCustomOptionChooseResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type CustomPair struct {
	CustomId             uint32   `protobuf:"varint,1,opt,name=custom_id,json=customId,proto3" json:"custom_id,omitempty"`
	OptionId             uint32   `protobuf:"varint,2,opt,name=option_id,json=optionId,proto3" json:"option_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CustomPair) Reset()         { *m = CustomPair{} }
func (m *CustomPair) String() string { return proto.CompactTextString(m) }
func (*CustomPair) ProtoMessage()    {}
func (*CustomPair) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{32}
}
func (m *CustomPair) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CustomPair.Unmarshal(m, b)
}
func (m *CustomPair) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CustomPair.Marshal(b, m, deterministic)
}
func (dst *CustomPair) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomPair.Merge(dst, src)
}
func (m *CustomPair) XXX_Size() int {
	return xxx_messageInfo_CustomPair.Size(m)
}
func (m *CustomPair) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomPair.DiscardUnknown(m)
}

var xxx_messageInfo_CustomPair proto.InternalMessageInfo

func (m *CustomPair) GetCustomId() uint32 {
	if m != nil {
		return m.CustomId
	}
	return 0
}

func (m *CustomPair) GetOptionId() uint32 {
	if m != nil {
		return m.OptionId
	}
	return 0
}

// 礼物权限发放
type PresentEffectAppend struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	EffectBegin          uint32   `protobuf:"varint,2,opt,name=effect_begin,json=effectBegin,proto3" json:"effect_begin,omitempty"`
	EffectEnd            uint32   `protobuf:"varint,3,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentEffectAppend) Reset()         { *m = PresentEffectAppend{} }
func (m *PresentEffectAppend) String() string { return proto.CompactTextString(m) }
func (*PresentEffectAppend) ProtoMessage()    {}
func (*PresentEffectAppend) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{33}
}
func (m *PresentEffectAppend) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentEffectAppend.Unmarshal(m, b)
}
func (m *PresentEffectAppend) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentEffectAppend.Marshal(b, m, deterministic)
}
func (dst *PresentEffectAppend) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentEffectAppend.Merge(dst, src)
}
func (m *PresentEffectAppend) XXX_Size() int {
	return xxx_messageInfo_PresentEffectAppend.Size(m)
}
func (m *PresentEffectAppend) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentEffectAppend.DiscardUnknown(m)
}

var xxx_messageInfo_PresentEffectAppend proto.InternalMessageInfo

func (m *PresentEffectAppend) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *PresentEffectAppend) GetEffectBegin() uint32 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *PresentEffectAppend) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

type CommonSendPresentReq struct {
	BaseReq              *app.BaseReq                    `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ItemId               uint32                          `protobuf:"varint,2,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ChannelId            uint32                          `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Count                uint32                          `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	SendSource           uint32                          `protobuf:"varint,5,opt,name=send_source,json=sendSource,proto3" json:"send_source,omitempty"`
	ItemSource           uint32                          `protobuf:"varint,6,opt,name=item_source,json=itemSource,proto3" json:"item_source,omitempty"`
	SourceId             uint32                          `protobuf:"varint,7,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	SendType             uint32                          `protobuf:"varint,8,opt,name=send_type,json=sendType,proto3" json:"send_type,omitempty"`
	DrawPresentPic       *userpresent.DrawPresentPicture `protobuf:"bytes,9,opt,name=draw_present_pic,json=drawPresentPic,proto3" json:"draw_present_pic,omitempty"`
	UidList              []uint32                        `protobuf:"varint,10,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *CommonSendPresentReq) Reset()         { *m = CommonSendPresentReq{} }
func (m *CommonSendPresentReq) String() string { return proto.CompactTextString(m) }
func (*CommonSendPresentReq) ProtoMessage()    {}
func (*CommonSendPresentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{34}
}
func (m *CommonSendPresentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonSendPresentReq.Unmarshal(m, b)
}
func (m *CommonSendPresentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonSendPresentReq.Marshal(b, m, deterministic)
}
func (dst *CommonSendPresentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonSendPresentReq.Merge(dst, src)
}
func (m *CommonSendPresentReq) XXX_Size() int {
	return xxx_messageInfo_CommonSendPresentReq.Size(m)
}
func (m *CommonSendPresentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonSendPresentReq.DiscardUnknown(m)
}

var xxx_messageInfo_CommonSendPresentReq proto.InternalMessageInfo

func (m *CommonSendPresentReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CommonSendPresentReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *CommonSendPresentReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CommonSendPresentReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *CommonSendPresentReq) GetSendSource() uint32 {
	if m != nil {
		return m.SendSource
	}
	return 0
}

func (m *CommonSendPresentReq) GetItemSource() uint32 {
	if m != nil {
		return m.ItemSource
	}
	return 0
}

func (m *CommonSendPresentReq) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *CommonSendPresentReq) GetSendType() uint32 {
	if m != nil {
		return m.SendType
	}
	return 0
}

func (m *CommonSendPresentReq) GetDrawPresentPic() *userpresent.DrawPresentPicture {
	if m != nil {
		return m.DrawPresentPic
	}
	return nil
}

func (m *CommonSendPresentReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type CommonSendPresentResp struct {
	BaseResp             *app.BaseResp                        `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	BatchMsgInfo         *userpresent.PresentBatchInfoMsg     `protobuf:"bytes,2,opt,name=batch_msg_info,json=batchMsgInfo,proto3" json:"batch_msg_info,omitempty"`
	MsgInfo              *userpresent.PresentSendMsg          `protobuf:"bytes,3,opt,name=msg_info,json=msgInfo,proto3" json:"msg_info,omitempty"`
	CurTbeans            uint64                               `protobuf:"varint,4,opt,name=cur_tbeans,json=curTbeans,proto3" json:"cur_tbeans,omitempty"`
	ItemSource           uint32                               `protobuf:"varint,5,opt,name=item_source,json=itemSource,proto3" json:"item_source,omitempty"`
	SourceId             uint32                               `protobuf:"varint,6,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	SourceRemain         uint32                               `protobuf:"varint,7,opt,name=source_remain,json=sourceRemain,proto3" json:"source_remain,omitempty"`
	TargetList           []*userpresent.PresentTargetUserInfo `protobuf:"bytes,8,rep,name=target_list,json=targetList,proto3" json:"target_list,omitempty"`
	ItemInfo             *userpresent.PresentSendItemInfo     `protobuf:"bytes,9,opt,name=item_info,json=itemInfo,proto3" json:"item_info,omitempty"`
	ExpireTime           uint32                               `protobuf:"varint,10,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	BoxDetail            *userpresent.PresentBoxDetail        `protobuf:"bytes,11,opt,name=box_detail,json=boxDetail,proto3" json:"box_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-"`
	XXX_unrecognized     []byte                               `json:"-"`
	XXX_sizecache        int32                                `json:"-"`
}

func (m *CommonSendPresentResp) Reset()         { *m = CommonSendPresentResp{} }
func (m *CommonSendPresentResp) String() string { return proto.CompactTextString(m) }
func (*CommonSendPresentResp) ProtoMessage()    {}
func (*CommonSendPresentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{35}
}
func (m *CommonSendPresentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonSendPresentResp.Unmarshal(m, b)
}
func (m *CommonSendPresentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonSendPresentResp.Marshal(b, m, deterministic)
}
func (dst *CommonSendPresentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonSendPresentResp.Merge(dst, src)
}
func (m *CommonSendPresentResp) XXX_Size() int {
	return xxx_messageInfo_CommonSendPresentResp.Size(m)
}
func (m *CommonSendPresentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonSendPresentResp.DiscardUnknown(m)
}

var xxx_messageInfo_CommonSendPresentResp proto.InternalMessageInfo

func (m *CommonSendPresentResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CommonSendPresentResp) GetBatchMsgInfo() *userpresent.PresentBatchInfoMsg {
	if m != nil {
		return m.BatchMsgInfo
	}
	return nil
}

func (m *CommonSendPresentResp) GetMsgInfo() *userpresent.PresentSendMsg {
	if m != nil {
		return m.MsgInfo
	}
	return nil
}

func (m *CommonSendPresentResp) GetCurTbeans() uint64 {
	if m != nil {
		return m.CurTbeans
	}
	return 0
}

func (m *CommonSendPresentResp) GetItemSource() uint32 {
	if m != nil {
		return m.ItemSource
	}
	return 0
}

func (m *CommonSendPresentResp) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *CommonSendPresentResp) GetSourceRemain() uint32 {
	if m != nil {
		return m.SourceRemain
	}
	return 0
}

func (m *CommonSendPresentResp) GetTargetList() []*userpresent.PresentTargetUserInfo {
	if m != nil {
		return m.TargetList
	}
	return nil
}

func (m *CommonSendPresentResp) GetItemInfo() *userpresent.PresentSendItemInfo {
	if m != nil {
		return m.ItemInfo
	}
	return nil
}

func (m *CommonSendPresentResp) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *CommonSendPresentResp) GetBoxDetail() *userpresent.PresentBoxDetail {
	if m != nil {
		return m.BoxDetail
	}
	return nil
}

type TreasurePrivilegeChannelMsg struct {
	User                 *app.UserProfile `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	GiftName             string           `protobuf:"bytes,2,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`
	GiftPrice            string           `protobuf:"bytes,3,opt,name=gift_price,json=giftPrice,proto3" json:"gift_price,omitempty"`
	ButtonText           string           `protobuf:"bytes,4,opt,name=button_text,json=buttonText,proto3" json:"button_text,omitempty"`
	JumpUrl              string           `protobuf:"bytes,5,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *TreasurePrivilegeChannelMsg) Reset()         { *m = TreasurePrivilegeChannelMsg{} }
func (m *TreasurePrivilegeChannelMsg) String() string { return proto.CompactTextString(m) }
func (*TreasurePrivilegeChannelMsg) ProtoMessage()    {}
func (*TreasurePrivilegeChannelMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{36}
}
func (m *TreasurePrivilegeChannelMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TreasurePrivilegeChannelMsg.Unmarshal(m, b)
}
func (m *TreasurePrivilegeChannelMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TreasurePrivilegeChannelMsg.Marshal(b, m, deterministic)
}
func (dst *TreasurePrivilegeChannelMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TreasurePrivilegeChannelMsg.Merge(dst, src)
}
func (m *TreasurePrivilegeChannelMsg) XXX_Size() int {
	return xxx_messageInfo_TreasurePrivilegeChannelMsg.Size(m)
}
func (m *TreasurePrivilegeChannelMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_TreasurePrivilegeChannelMsg.DiscardUnknown(m)
}

var xxx_messageInfo_TreasurePrivilegeChannelMsg proto.InternalMessageInfo

func (m *TreasurePrivilegeChannelMsg) GetUser() *app.UserProfile {
	if m != nil {
		return m.User
	}
	return nil
}

func (m *TreasurePrivilegeChannelMsg) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *TreasurePrivilegeChannelMsg) GetGiftPrice() string {
	if m != nil {
		return m.GiftPrice
	}
	return ""
}

func (m *TreasurePrivilegeChannelMsg) GetButtonText() string {
	if m != nil {
		return m.ButtonText
	}
	return ""
}

func (m *TreasurePrivilegeChannelMsg) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

type PresentConfigSyncReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	LastUpdateTime       uint32       `protobuf:"varint,2,opt,name=last_update_time,json=lastUpdateTime,proto3" json:"last_update_time,omitempty"`
	FlowUpdateTime       uint32       `protobuf:"varint,3,opt,name=flow_update_time,json=flowUpdateTime,proto3" json:"flow_update_time,omitempty"`
	DynamicUpdateTime    uint32       `protobuf:"varint,4,opt,name=dynamic_update_time,json=dynamicUpdateTime,proto3" json:"dynamic_update_time,omitempty"`
	OnlyPresent          bool         `protobuf:"varint,5,opt,name=only_present,json=onlyPresent,proto3" json:"only_present,omitempty"`
	EmperorSetUpdateTime uint32       `protobuf:"varint,6,opt,name=emperor_set_update_time,json=emperorSetUpdateTime,proto3" json:"emperor_set_update_time,omitempty"`
	WithEmperorSet       bool         `protobuf:"varint,7,opt,name=with_emperor_set,json=withEmperorSet,proto3" json:"with_emperor_set,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PresentConfigSyncReq) Reset()         { *m = PresentConfigSyncReq{} }
func (m *PresentConfigSyncReq) String() string { return proto.CompactTextString(m) }
func (*PresentConfigSyncReq) ProtoMessage()    {}
func (*PresentConfigSyncReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{37}
}
func (m *PresentConfigSyncReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentConfigSyncReq.Unmarshal(m, b)
}
func (m *PresentConfigSyncReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentConfigSyncReq.Marshal(b, m, deterministic)
}
func (dst *PresentConfigSyncReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentConfigSyncReq.Merge(dst, src)
}
func (m *PresentConfigSyncReq) XXX_Size() int {
	return xxx_messageInfo_PresentConfigSyncReq.Size(m)
}
func (m *PresentConfigSyncReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentConfigSyncReq.DiscardUnknown(m)
}

var xxx_messageInfo_PresentConfigSyncReq proto.InternalMessageInfo

func (m *PresentConfigSyncReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PresentConfigSyncReq) GetLastUpdateTime() uint32 {
	if m != nil {
		return m.LastUpdateTime
	}
	return 0
}

func (m *PresentConfigSyncReq) GetFlowUpdateTime() uint32 {
	if m != nil {
		return m.FlowUpdateTime
	}
	return 0
}

func (m *PresentConfigSyncReq) GetDynamicUpdateTime() uint32 {
	if m != nil {
		return m.DynamicUpdateTime
	}
	return 0
}

func (m *PresentConfigSyncReq) GetOnlyPresent() bool {
	if m != nil {
		return m.OnlyPresent
	}
	return false
}

func (m *PresentConfigSyncReq) GetEmperorSetUpdateTime() uint32 {
	if m != nil {
		return m.EmperorSetUpdateTime
	}
	return 0
}

func (m *PresentConfigSyncReq) GetWithEmperorSet() bool {
	if m != nil {
		return m.WithEmperorSet
	}
	return false
}

type PresentConfigSyncResp struct {
	BaseResp             *app.BaseResp                                  `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	LastUpdateTime       uint32                                         `protobuf:"varint,2,opt,name=last_update_time,json=lastUpdateTime,proto3" json:"last_update_time,omitempty"`
	ConfigList           []*app.PresentItemConfig                       `protobuf:"bytes,3,rep,name=config_list,json=configList,proto3" json:"config_list,omitempty"`
	EnterBlackList       []*PresentEnterBlacklist                       `protobuf:"bytes,4,rep,name=enter_black_list,json=enterBlackList,proto3" json:"enter_black_list,omitempty"`
	FlowSync             *sync.AdvancedConfigPresentFlowSync            `protobuf:"bytes,5,opt,name=flow_sync,json=flowSync,proto3" json:"flow_sync,omitempty"`
	DynamicTemplateSync  *sync.AdvancedConfigPresentDynamicTemplateSync `protobuf:"bytes,6,opt,name=dynamic_template_sync,json=dynamicTemplateSync,proto3" json:"dynamic_template_sync,omitempty"`
	EmperorSetConfig     *EmperorSetConfigSync                          `protobuf:"bytes,7,opt,name=emperor_set_config,json=emperorSetConfig,proto3" json:"emperor_set_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                       `json:"-"`
	XXX_unrecognized     []byte                                         `json:"-"`
	XXX_sizecache        int32                                          `json:"-"`
}

func (m *PresentConfigSyncResp) Reset()         { *m = PresentConfigSyncResp{} }
func (m *PresentConfigSyncResp) String() string { return proto.CompactTextString(m) }
func (*PresentConfigSyncResp) ProtoMessage()    {}
func (*PresentConfigSyncResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{38}
}
func (m *PresentConfigSyncResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentConfigSyncResp.Unmarshal(m, b)
}
func (m *PresentConfigSyncResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentConfigSyncResp.Marshal(b, m, deterministic)
}
func (dst *PresentConfigSyncResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentConfigSyncResp.Merge(dst, src)
}
func (m *PresentConfigSyncResp) XXX_Size() int {
	return xxx_messageInfo_PresentConfigSyncResp.Size(m)
}
func (m *PresentConfigSyncResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentConfigSyncResp.DiscardUnknown(m)
}

var xxx_messageInfo_PresentConfigSyncResp proto.InternalMessageInfo

func (m *PresentConfigSyncResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PresentConfigSyncResp) GetLastUpdateTime() uint32 {
	if m != nil {
		return m.LastUpdateTime
	}
	return 0
}

func (m *PresentConfigSyncResp) GetConfigList() []*app.PresentItemConfig {
	if m != nil {
		return m.ConfigList
	}
	return nil
}

func (m *PresentConfigSyncResp) GetEnterBlackList() []*PresentEnterBlacklist {
	if m != nil {
		return m.EnterBlackList
	}
	return nil
}

func (m *PresentConfigSyncResp) GetFlowSync() *sync.AdvancedConfigPresentFlowSync {
	if m != nil {
		return m.FlowSync
	}
	return nil
}

func (m *PresentConfigSyncResp) GetDynamicTemplateSync() *sync.AdvancedConfigPresentDynamicTemplateSync {
	if m != nil {
		return m.DynamicTemplateSync
	}
	return nil
}

func (m *PresentConfigSyncResp) GetEmperorSetConfig() *EmperorSetConfigSync {
	if m != nil {
		return m.EmperorSetConfig
	}
	return nil
}

type PresentEnterBlacklist struct {
	EnterType            PresentEnterType `protobuf:"varint,1,opt,name=enter_type,json=enterType,proto3,enum=ga.present_go_logic.PresentEnterType" json:"enter_type,omitempty"`
	GiftItemList         []uint32         `protobuf:"varint,2,rep,packed,name=gift_item_list,json=giftItemList,proto3" json:"gift_item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *PresentEnterBlacklist) Reset()         { *m = PresentEnterBlacklist{} }
func (m *PresentEnterBlacklist) String() string { return proto.CompactTextString(m) }
func (*PresentEnterBlacklist) ProtoMessage()    {}
func (*PresentEnterBlacklist) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{39}
}
func (m *PresentEnterBlacklist) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentEnterBlacklist.Unmarshal(m, b)
}
func (m *PresentEnterBlacklist) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentEnterBlacklist.Marshal(b, m, deterministic)
}
func (dst *PresentEnterBlacklist) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentEnterBlacklist.Merge(dst, src)
}
func (m *PresentEnterBlacklist) XXX_Size() int {
	return xxx_messageInfo_PresentEnterBlacklist.Size(m)
}
func (m *PresentEnterBlacklist) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentEnterBlacklist.DiscardUnknown(m)
}

var xxx_messageInfo_PresentEnterBlacklist proto.InternalMessageInfo

func (m *PresentEnterBlacklist) GetEnterType() PresentEnterType {
	if m != nil {
		return m.EnterType
	}
	return PresentEnterType_PresentEnterTypeUnknown
}

func (m *PresentEnterBlacklist) GetGiftItemList() []uint32 {
	if m != nil {
		return m.GiftItemList
	}
	return nil
}

type GetPresentSetInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetPresentSetInfoReq) Reset()         { *m = GetPresentSetInfoReq{} }
func (m *GetPresentSetInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentSetInfoReq) ProtoMessage()    {}
func (*GetPresentSetInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{40}
}
func (m *GetPresentSetInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentSetInfoReq.Unmarshal(m, b)
}
func (m *GetPresentSetInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentSetInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentSetInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentSetInfoReq.Merge(dst, src)
}
func (m *GetPresentSetInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentSetInfoReq.Size(m)
}
func (m *GetPresentSetInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentSetInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentSetInfoReq proto.InternalMessageInfo

func (m *GetPresentSetInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetPresentSetInfoResp struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	SetList              []*PresentSetInfo `protobuf:"bytes,2,rep,name=set_list,json=setList,proto3" json:"set_list,omitempty"`
	LastUpdateTime       uint32            `protobuf:"varint,3,opt,name=last_update_time,json=lastUpdateTime,proto3" json:"last_update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetPresentSetInfoResp) Reset()         { *m = GetPresentSetInfoResp{} }
func (m *GetPresentSetInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentSetInfoResp) ProtoMessage()    {}
func (*GetPresentSetInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{41}
}
func (m *GetPresentSetInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentSetInfoResp.Unmarshal(m, b)
}
func (m *GetPresentSetInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentSetInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentSetInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentSetInfoResp.Merge(dst, src)
}
func (m *GetPresentSetInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentSetInfoResp.Size(m)
}
func (m *GetPresentSetInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentSetInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentSetInfoResp proto.InternalMessageInfo

func (m *GetPresentSetInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetPresentSetInfoResp) GetSetList() []*PresentSetInfo {
	if m != nil {
		return m.SetList
	}
	return nil
}

func (m *GetPresentSetInfoResp) GetLastUpdateTime() uint32 {
	if m != nil {
		return m.LastUpdateTime
	}
	return 0
}

type GetPresentSetDetailReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SetId                uint32       `protobuf:"varint,2,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetPresentSetDetailReq) Reset()         { *m = GetPresentSetDetailReq{} }
func (m *GetPresentSetDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentSetDetailReq) ProtoMessage()    {}
func (*GetPresentSetDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{42}
}
func (m *GetPresentSetDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentSetDetailReq.Unmarshal(m, b)
}
func (m *GetPresentSetDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentSetDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentSetDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentSetDetailReq.Merge(dst, src)
}
func (m *GetPresentSetDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentSetDetailReq.Size(m)
}
func (m *GetPresentSetDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentSetDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentSetDetailReq proto.InternalMessageInfo

func (m *GetPresentSetDetailReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetPresentSetDetailReq) GetSetId() uint32 {
	if m != nil {
		return m.SetId
	}
	return 0
}

type GetPresentSetDetailResp struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	SetDetail            *PresentSetDetail `protobuf:"bytes,2,opt,name=set_detail,json=setDetail,proto3" json:"set_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetPresentSetDetailResp) Reset()         { *m = GetPresentSetDetailResp{} }
func (m *GetPresentSetDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentSetDetailResp) ProtoMessage()    {}
func (*GetPresentSetDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{43}
}
func (m *GetPresentSetDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentSetDetailResp.Unmarshal(m, b)
}
func (m *GetPresentSetDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentSetDetailResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentSetDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentSetDetailResp.Merge(dst, src)
}
func (m *GetPresentSetDetailResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentSetDetailResp.Size(m)
}
func (m *GetPresentSetDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentSetDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentSetDetailResp proto.InternalMessageInfo

func (m *GetPresentSetDetailResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetPresentSetDetailResp) GetSetDetail() *PresentSetDetail {
	if m != nil {
		return m.SetDetail
	}
	return nil
}

type PresentSetInfo struct {
	SetId                uint32            `protobuf:"varint,1,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	SetItemList          []*PresentSetItem `protobuf:"bytes,2,rep,name=set_item_list,json=setItemList,proto3" json:"set_item_list,omitempty"`
	DefaultItemId        uint32            `protobuf:"varint,3,opt,name=default_item_id,json=defaultItemId,proto3" json:"default_item_id,omitempty"`
	BeginTime            uint32            `protobuf:"varint,4,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32            `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	IsPermanent          bool              `protobuf:"varint,6,opt,name=is_permanent,json=isPermanent,proto3" json:"is_permanent,omitempty"`
	Rank                 float32           `protobuf:"fixed32,7,opt,name=rank,proto3" json:"rank,omitempty"`
	IsBatch              bool              `protobuf:"varint,8,opt,name=is_batch,json=isBatch,proto3" json:"is_batch,omitempty"`
	IsNew                bool              `protobuf:"varint,9,opt,name=is_new,json=isNew,proto3" json:"is_new,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *PresentSetInfo) Reset()         { *m = PresentSetInfo{} }
func (m *PresentSetInfo) String() string { return proto.CompactTextString(m) }
func (*PresentSetInfo) ProtoMessage()    {}
func (*PresentSetInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{44}
}
func (m *PresentSetInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentSetInfo.Unmarshal(m, b)
}
func (m *PresentSetInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentSetInfo.Marshal(b, m, deterministic)
}
func (dst *PresentSetInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentSetInfo.Merge(dst, src)
}
func (m *PresentSetInfo) XXX_Size() int {
	return xxx_messageInfo_PresentSetInfo.Size(m)
}
func (m *PresentSetInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentSetInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PresentSetInfo proto.InternalMessageInfo

func (m *PresentSetInfo) GetSetId() uint32 {
	if m != nil {
		return m.SetId
	}
	return 0
}

func (m *PresentSetInfo) GetSetItemList() []*PresentSetItem {
	if m != nil {
		return m.SetItemList
	}
	return nil
}

func (m *PresentSetInfo) GetDefaultItemId() uint32 {
	if m != nil {
		return m.DefaultItemId
	}
	return 0
}

func (m *PresentSetInfo) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *PresentSetInfo) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *PresentSetInfo) GetIsPermanent() bool {
	if m != nil {
		return m.IsPermanent
	}
	return false
}

func (m *PresentSetInfo) GetRank() float32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *PresentSetInfo) GetIsBatch() bool {
	if m != nil {
		return m.IsBatch
	}
	return false
}

func (m *PresentSetInfo) GetIsNew() bool {
	if m != nil {
		return m.IsNew
	}
	return false
}

type PresentSetItem struct {
	ItemId               uint32                  `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	RareLevel            PresentSetItemRareLevel `protobuf:"varint,2,opt,name=rare_level,json=rareLevel,proto3,enum=ga.present_go_logic.PresentSetItemRareLevel" json:"rare_level,omitempty"`
	IsUnlocked           bool                    `protobuf:"varint,3,opt,name=is_unlocked,json=isUnlocked,proto3" json:"is_unlocked,omitempty"`
	ExtendText           string                  `protobuf:"bytes,4,opt,name=extend_text,json=extendText,proto3" json:"extend_text,omitempty"`
	IsNew                bool                    `protobuf:"varint,5,opt,name=is_new,json=isNew,proto3" json:"is_new,omitempty"`
	BackgroundUrl        string                  `protobuf:"bytes,6,opt,name=background_url,json=backgroundUrl,proto3" json:"background_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *PresentSetItem) Reset()         { *m = PresentSetItem{} }
func (m *PresentSetItem) String() string { return proto.CompactTextString(m) }
func (*PresentSetItem) ProtoMessage()    {}
func (*PresentSetItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{45}
}
func (m *PresentSetItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentSetItem.Unmarshal(m, b)
}
func (m *PresentSetItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentSetItem.Marshal(b, m, deterministic)
}
func (dst *PresentSetItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentSetItem.Merge(dst, src)
}
func (m *PresentSetItem) XXX_Size() int {
	return xxx_messageInfo_PresentSetItem.Size(m)
}
func (m *PresentSetItem) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentSetItem.DiscardUnknown(m)
}

var xxx_messageInfo_PresentSetItem proto.InternalMessageInfo

func (m *PresentSetItem) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *PresentSetItem) GetRareLevel() PresentSetItemRareLevel {
	if m != nil {
		return m.RareLevel
	}
	return PresentSetItemRareLevel_PresentSetItemRareLevelUnknown
}

func (m *PresentSetItem) GetIsUnlocked() bool {
	if m != nil {
		return m.IsUnlocked
	}
	return false
}

func (m *PresentSetItem) GetExtendText() string {
	if m != nil {
		return m.ExtendText
	}
	return ""
}

func (m *PresentSetItem) GetIsNew() bool {
	if m != nil {
		return m.IsNew
	}
	return false
}

func (m *PresentSetItem) GetBackgroundUrl() string {
	if m != nil {
		return m.BackgroundUrl
	}
	return ""
}

type PresentSetDetail struct {
	SetId                  uint32                     `protobuf:"varint,1,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	SetName                string                     `protobuf:"bytes,2,opt,name=set_name,json=setName,proto3" json:"set_name,omitempty"`
	BeginTime              uint32                     `protobuf:"varint,3,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime                uint32                     `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	IsPermanent            bool                       `protobuf:"varint,5,opt,name=is_permanent,json=isPermanent,proto3" json:"is_permanent,omitempty"`
	UserBroadcast          []*PresentSetUserBroadcast `protobuf:"bytes,6,rep,name=user_broadcast,json=userBroadcast,proto3" json:"user_broadcast,omitempty"`
	CollectionAward        *PresentSetCollectionAward `protobuf:"bytes,7,opt,name=collection_award,json=collectionAward,proto3" json:"collection_award,omitempty"`
	CollectCount           uint32                     `protobuf:"varint,8,opt,name=collect_count,json=collectCount,proto3" json:"collect_count,omitempty"`
	TotalCount             uint32                     `protobuf:"varint,9,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	SetItemList            []*PresentSetItem          `protobuf:"bytes,10,rep,name=set_item_list,json=setItemList,proto3" json:"set_item_list,omitempty"`
	ActivityUrl            string                     `protobuf:"bytes,11,opt,name=activity_url,json=activityUrl,proto3" json:"activity_url,omitempty"`
	ActivityIcon           string                     `protobuf:"bytes,12,opt,name=activity_icon,json=activityIcon,proto3" json:"activity_icon,omitempty"`
	CmsUrl                 string                     `protobuf:"bytes,13,opt,name=cms_url,json=cmsUrl,proto3" json:"cms_url,omitempty"`
	DefaultItemId          uint32                     `protobuf:"varint,14,opt,name=default_item_id,json=defaultItemId,proto3" json:"default_item_id,omitempty"`
	Source                 *PresentSetDetailResource  `protobuf:"bytes,15,opt,name=source,proto3" json:"source,omitempty"`
	ActivityUrlType        PresentSetActivityUrlType  `protobuf:"varint,16,opt,name=activity_url_type,json=activityUrlType,proto3,enum=ga.present_go_logic.PresentSetActivityUrlType" json:"activity_url_type,omitempty"`
	EmperorSetId           uint32                     `protobuf:"varint,17,opt,name=emperor_set_id,json=emperorSetId,proto3" json:"emperor_set_id,omitempty"`
	SendAllEmperorSetText  string                     `protobuf:"bytes,18,opt,name=send_all_emperor_set_text,json=sendAllEmperorSetText,proto3" json:"send_all_emperor_set_text,omitempty"`
	SendAllEmperorSetPrice uint32                     `protobuf:"varint,19,opt,name=send_all_emperor_set_price,json=sendAllEmperorSetPrice,proto3" json:"send_all_emperor_set_price,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}                   `json:"-"`
	XXX_unrecognized       []byte                     `json:"-"`
	XXX_sizecache          int32                      `json:"-"`
}

func (m *PresentSetDetail) Reset()         { *m = PresentSetDetail{} }
func (m *PresentSetDetail) String() string { return proto.CompactTextString(m) }
func (*PresentSetDetail) ProtoMessage()    {}
func (*PresentSetDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{46}
}
func (m *PresentSetDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentSetDetail.Unmarshal(m, b)
}
func (m *PresentSetDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentSetDetail.Marshal(b, m, deterministic)
}
func (dst *PresentSetDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentSetDetail.Merge(dst, src)
}
func (m *PresentSetDetail) XXX_Size() int {
	return xxx_messageInfo_PresentSetDetail.Size(m)
}
func (m *PresentSetDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentSetDetail.DiscardUnknown(m)
}

var xxx_messageInfo_PresentSetDetail proto.InternalMessageInfo

func (m *PresentSetDetail) GetSetId() uint32 {
	if m != nil {
		return m.SetId
	}
	return 0
}

func (m *PresentSetDetail) GetSetName() string {
	if m != nil {
		return m.SetName
	}
	return ""
}

func (m *PresentSetDetail) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *PresentSetDetail) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *PresentSetDetail) GetIsPermanent() bool {
	if m != nil {
		return m.IsPermanent
	}
	return false
}

func (m *PresentSetDetail) GetUserBroadcast() []*PresentSetUserBroadcast {
	if m != nil {
		return m.UserBroadcast
	}
	return nil
}

func (m *PresentSetDetail) GetCollectionAward() *PresentSetCollectionAward {
	if m != nil {
		return m.CollectionAward
	}
	return nil
}

func (m *PresentSetDetail) GetCollectCount() uint32 {
	if m != nil {
		return m.CollectCount
	}
	return 0
}

func (m *PresentSetDetail) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *PresentSetDetail) GetSetItemList() []*PresentSetItem {
	if m != nil {
		return m.SetItemList
	}
	return nil
}

func (m *PresentSetDetail) GetActivityUrl() string {
	if m != nil {
		return m.ActivityUrl
	}
	return ""
}

func (m *PresentSetDetail) GetActivityIcon() string {
	if m != nil {
		return m.ActivityIcon
	}
	return ""
}

func (m *PresentSetDetail) GetCmsUrl() string {
	if m != nil {
		return m.CmsUrl
	}
	return ""
}

func (m *PresentSetDetail) GetDefaultItemId() uint32 {
	if m != nil {
		return m.DefaultItemId
	}
	return 0
}

func (m *PresentSetDetail) GetSource() *PresentSetDetailResource {
	if m != nil {
		return m.Source
	}
	return nil
}

func (m *PresentSetDetail) GetActivityUrlType() PresentSetActivityUrlType {
	if m != nil {
		return m.ActivityUrlType
	}
	return PresentSetActivityUrlType_PresentSetActivityTypeUnknown
}

func (m *PresentSetDetail) GetEmperorSetId() uint32 {
	if m != nil {
		return m.EmperorSetId
	}
	return 0
}

func (m *PresentSetDetail) GetSendAllEmperorSetText() string {
	if m != nil {
		return m.SendAllEmperorSetText
	}
	return ""
}

func (m *PresentSetDetail) GetSendAllEmperorSetPrice() uint32 {
	if m != nil {
		return m.SendAllEmperorSetPrice
	}
	return 0
}

type PresentSetDetailResource struct {
	BackgroundUrl          string   `protobuf:"bytes,1,opt,name=background_url,json=backgroundUrl,proto3" json:"background_url,omitempty"`
	FrameUrl               string   `protobuf:"bytes,2,opt,name=frame_url,json=frameUrl,proto3" json:"frame_url,omitempty"`
	NoAwardFrameUrl        string   `protobuf:"bytes,3,opt,name=no_award_frame_url,json=noAwardFrameUrl,proto3" json:"no_award_frame_url,omitempty"`
	AwardPreviewIconUrl    string   `protobuf:"bytes,4,opt,name=award_preview_icon_url,json=awardPreviewIconUrl,proto3" json:"award_preview_icon_url,omitempty"`
	IntroductionIconUrl    string   `protobuf:"bytes,5,opt,name=introduction_icon_url,json=introductionIconUrl,proto3" json:"introduction_icon_url,omitempty"`
	AwardBackgroundUrl     string   `protobuf:"bytes,6,opt,name=award_background_url,json=awardBackgroundUrl,proto3" json:"award_background_url,omitempty"`
	AwardFrameShadow       string   `protobuf:"bytes,7,opt,name=award_frame_shadow,json=awardFrameShadow,proto3" json:"award_frame_shadow,omitempty"`
	BackgroundUrlNine      string   `protobuf:"bytes,8,opt,name=background_url_nine,json=backgroundUrlNine,proto3" json:"background_url_nine,omitempty"`
	FrameUrlNine           string   `protobuf:"bytes,9,opt,name=frame_url_nine,json=frameUrlNine,proto3" json:"frame_url_nine,omitempty"`
	SendEmperorSetFrameUrl string   `protobuf:"bytes,10,opt,name=send_emperor_set_frame_url,json=sendEmperorSetFrameUrl,proto3" json:"send_emperor_set_frame_url,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *PresentSetDetailResource) Reset()         { *m = PresentSetDetailResource{} }
func (m *PresentSetDetailResource) String() string { return proto.CompactTextString(m) }
func (*PresentSetDetailResource) ProtoMessage()    {}
func (*PresentSetDetailResource) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{47}
}
func (m *PresentSetDetailResource) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentSetDetailResource.Unmarshal(m, b)
}
func (m *PresentSetDetailResource) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentSetDetailResource.Marshal(b, m, deterministic)
}
func (dst *PresentSetDetailResource) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentSetDetailResource.Merge(dst, src)
}
func (m *PresentSetDetailResource) XXX_Size() int {
	return xxx_messageInfo_PresentSetDetailResource.Size(m)
}
func (m *PresentSetDetailResource) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentSetDetailResource.DiscardUnknown(m)
}

var xxx_messageInfo_PresentSetDetailResource proto.InternalMessageInfo

func (m *PresentSetDetailResource) GetBackgroundUrl() string {
	if m != nil {
		return m.BackgroundUrl
	}
	return ""
}

func (m *PresentSetDetailResource) GetFrameUrl() string {
	if m != nil {
		return m.FrameUrl
	}
	return ""
}

func (m *PresentSetDetailResource) GetNoAwardFrameUrl() string {
	if m != nil {
		return m.NoAwardFrameUrl
	}
	return ""
}

func (m *PresentSetDetailResource) GetAwardPreviewIconUrl() string {
	if m != nil {
		return m.AwardPreviewIconUrl
	}
	return ""
}

func (m *PresentSetDetailResource) GetIntroductionIconUrl() string {
	if m != nil {
		return m.IntroductionIconUrl
	}
	return ""
}

func (m *PresentSetDetailResource) GetAwardBackgroundUrl() string {
	if m != nil {
		return m.AwardBackgroundUrl
	}
	return ""
}

func (m *PresentSetDetailResource) GetAwardFrameShadow() string {
	if m != nil {
		return m.AwardFrameShadow
	}
	return ""
}

func (m *PresentSetDetailResource) GetBackgroundUrlNine() string {
	if m != nil {
		return m.BackgroundUrlNine
	}
	return ""
}

func (m *PresentSetDetailResource) GetFrameUrlNine() string {
	if m != nil {
		return m.FrameUrlNine
	}
	return ""
}

func (m *PresentSetDetailResource) GetSendEmperorSetFrameUrl() string {
	if m != nil {
		return m.SendEmperorSetFrameUrl
	}
	return ""
}

type PresentSetUserBroadcast struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	HeadMd5              string   `protobuf:"bytes,4,opt,name=head_md5,json=headMd5,proto3" json:"head_md5,omitempty"`
	ItemId               uint32   `protobuf:"varint,5,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentSetUserBroadcast) Reset()         { *m = PresentSetUserBroadcast{} }
func (m *PresentSetUserBroadcast) String() string { return proto.CompactTextString(m) }
func (*PresentSetUserBroadcast) ProtoMessage()    {}
func (*PresentSetUserBroadcast) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{48}
}
func (m *PresentSetUserBroadcast) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentSetUserBroadcast.Unmarshal(m, b)
}
func (m *PresentSetUserBroadcast) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentSetUserBroadcast.Marshal(b, m, deterministic)
}
func (dst *PresentSetUserBroadcast) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentSetUserBroadcast.Merge(dst, src)
}
func (m *PresentSetUserBroadcast) XXX_Size() int {
	return xxx_messageInfo_PresentSetUserBroadcast.Size(m)
}
func (m *PresentSetUserBroadcast) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentSetUserBroadcast.DiscardUnknown(m)
}

var xxx_messageInfo_PresentSetUserBroadcast proto.InternalMessageInfo

func (m *PresentSetUserBroadcast) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PresentSetUserBroadcast) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *PresentSetUserBroadcast) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *PresentSetUserBroadcast) GetHeadMd5() string {
	if m != nil {
		return m.HeadMd5
	}
	return ""
}

func (m *PresentSetUserBroadcast) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

type PresentSetCollectionAward struct {
	AwardText            string                           `protobuf:"bytes,1,opt,name=award_text,json=awardText,proto3" json:"award_text,omitempty"`
	AwardList            []*PresentSetCollectionAwardItem `protobuf:"bytes,2,rep,name=award_list,json=awardList,proto3" json:"award_list,omitempty"`
	PreviewType          PresentPreviewType               `protobuf:"varint,3,opt,name=preview_type,json=previewType,proto3,enum=ga.present_go_logic.PresentPreviewType" json:"preview_type,omitempty"`
	PreviewUrl           string                           `protobuf:"bytes,4,opt,name=preview_url,json=previewUrl,proto3" json:"preview_url,omitempty"`
	PreviewMd5           string                           `protobuf:"bytes,5,opt,name=preview_md5,json=previewMd5,proto3" json:"preview_md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *PresentSetCollectionAward) Reset()         { *m = PresentSetCollectionAward{} }
func (m *PresentSetCollectionAward) String() string { return proto.CompactTextString(m) }
func (*PresentSetCollectionAward) ProtoMessage()    {}
func (*PresentSetCollectionAward) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{49}
}
func (m *PresentSetCollectionAward) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentSetCollectionAward.Unmarshal(m, b)
}
func (m *PresentSetCollectionAward) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentSetCollectionAward.Marshal(b, m, deterministic)
}
func (dst *PresentSetCollectionAward) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentSetCollectionAward.Merge(dst, src)
}
func (m *PresentSetCollectionAward) XXX_Size() int {
	return xxx_messageInfo_PresentSetCollectionAward.Size(m)
}
func (m *PresentSetCollectionAward) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentSetCollectionAward.DiscardUnknown(m)
}

var xxx_messageInfo_PresentSetCollectionAward proto.InternalMessageInfo

func (m *PresentSetCollectionAward) GetAwardText() string {
	if m != nil {
		return m.AwardText
	}
	return ""
}

func (m *PresentSetCollectionAward) GetAwardList() []*PresentSetCollectionAwardItem {
	if m != nil {
		return m.AwardList
	}
	return nil
}

func (m *PresentSetCollectionAward) GetPreviewType() PresentPreviewType {
	if m != nil {
		return m.PreviewType
	}
	return PresentPreviewType_PresentPreviewTypeNone
}

func (m *PresentSetCollectionAward) GetPreviewUrl() string {
	if m != nil {
		return m.PreviewUrl
	}
	return ""
}

func (m *PresentSetCollectionAward) GetPreviewMd5() string {
	if m != nil {
		return m.PreviewMd5
	}
	return ""
}

type PresentSetCollectionAwardItem struct {
	IconUrl              string                        `protobuf:"bytes,1,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	AwardType            PresentSetCollectionAwardType `protobuf:"varint,2,opt,name=award_type,json=awardType,proto3,enum=ga.present_go_logic.PresentSetCollectionAwardType" json:"award_type,omitempty"`
	AwardName            string                        `protobuf:"bytes,3,opt,name=award_name,json=awardName,proto3" json:"award_name,omitempty"`
	AwardDesc            string                        `protobuf:"bytes,4,opt,name=award_desc,json=awardDesc,proto3" json:"award_desc,omitempty"`
	MarkText             string                        `protobuf:"bytes,5,opt,name=mark_text,json=markText,proto3" json:"mark_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *PresentSetCollectionAwardItem) Reset()         { *m = PresentSetCollectionAwardItem{} }
func (m *PresentSetCollectionAwardItem) String() string { return proto.CompactTextString(m) }
func (*PresentSetCollectionAwardItem) ProtoMessage()    {}
func (*PresentSetCollectionAwardItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{50}
}
func (m *PresentSetCollectionAwardItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentSetCollectionAwardItem.Unmarshal(m, b)
}
func (m *PresentSetCollectionAwardItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentSetCollectionAwardItem.Marshal(b, m, deterministic)
}
func (dst *PresentSetCollectionAwardItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentSetCollectionAwardItem.Merge(dst, src)
}
func (m *PresentSetCollectionAwardItem) XXX_Size() int {
	return xxx_messageInfo_PresentSetCollectionAwardItem.Size(m)
}
func (m *PresentSetCollectionAwardItem) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentSetCollectionAwardItem.DiscardUnknown(m)
}

var xxx_messageInfo_PresentSetCollectionAwardItem proto.InternalMessageInfo

func (m *PresentSetCollectionAwardItem) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *PresentSetCollectionAwardItem) GetAwardType() PresentSetCollectionAwardType {
	if m != nil {
		return m.AwardType
	}
	return PresentSetCollectionAwardType_PresentSetCollectionAwardTypeUnknown
}

func (m *PresentSetCollectionAwardItem) GetAwardName() string {
	if m != nil {
		return m.AwardName
	}
	return ""
}

func (m *PresentSetCollectionAwardItem) GetAwardDesc() string {
	if m != nil {
		return m.AwardDesc
	}
	return ""
}

func (m *PresentSetCollectionAwardItem) GetMarkText() string {
	if m != nil {
		return m.MarkText
	}
	return ""
}

type PresentSetUnlockMsg struct {
	UnlockList           []*PresentSetUnlockItem    `protobuf:"bytes,1,rep,name=unlock_list,json=unlockList,proto3" json:"unlock_list,omitempty"`
	CollectCount         uint32                     `protobuf:"varint,2,opt,name=collect_count,json=collectCount,proto3" json:"collect_count,omitempty"`
	TotalCount           uint32                     `protobuf:"varint,3,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	IsAll                bool                       `protobuf:"varint,4,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
	AllUnlockList        []*PresentSetUnlockItem    `protobuf:"bytes,5,rep,name=all_unlock_list,json=allUnlockList,proto3" json:"all_unlock_list,omitempty"`
	BroadCastList        []*PresentSetUserBroadcast `protobuf:"bytes,6,rep,name=broad_cast_list,json=broadCastList,proto3" json:"broad_cast_list,omitempty"`
	SetId                uint32                     `protobuf:"varint,7,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *PresentSetUnlockMsg) Reset()         { *m = PresentSetUnlockMsg{} }
func (m *PresentSetUnlockMsg) String() string { return proto.CompactTextString(m) }
func (*PresentSetUnlockMsg) ProtoMessage()    {}
func (*PresentSetUnlockMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{51}
}
func (m *PresentSetUnlockMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentSetUnlockMsg.Unmarshal(m, b)
}
func (m *PresentSetUnlockMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentSetUnlockMsg.Marshal(b, m, deterministic)
}
func (dst *PresentSetUnlockMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentSetUnlockMsg.Merge(dst, src)
}
func (m *PresentSetUnlockMsg) XXX_Size() int {
	return xxx_messageInfo_PresentSetUnlockMsg.Size(m)
}
func (m *PresentSetUnlockMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentSetUnlockMsg.DiscardUnknown(m)
}

var xxx_messageInfo_PresentSetUnlockMsg proto.InternalMessageInfo

func (m *PresentSetUnlockMsg) GetUnlockList() []*PresentSetUnlockItem {
	if m != nil {
		return m.UnlockList
	}
	return nil
}

func (m *PresentSetUnlockMsg) GetCollectCount() uint32 {
	if m != nil {
		return m.CollectCount
	}
	return 0
}

func (m *PresentSetUnlockMsg) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *PresentSetUnlockMsg) GetIsAll() bool {
	if m != nil {
		return m.IsAll
	}
	return false
}

func (m *PresentSetUnlockMsg) GetAllUnlockList() []*PresentSetUnlockItem {
	if m != nil {
		return m.AllUnlockList
	}
	return nil
}

func (m *PresentSetUnlockMsg) GetBroadCastList() []*PresentSetUserBroadcast {
	if m != nil {
		return m.BroadCastList
	}
	return nil
}

func (m *PresentSetUnlockMsg) GetSetId() uint32 {
	if m != nil {
		return m.SetId
	}
	return 0
}

type PresentSetUnlockItem struct {
	SetId                uint32                  `protobuf:"varint,1,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	ItemId               uint32                  `protobuf:"varint,2,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	RareLevel            PresentSetItemRareLevel `protobuf:"varint,3,opt,name=rare_level,json=rareLevel,proto3,enum=ga.present_go_logic.PresentSetItemRareLevel" json:"rare_level,omitempty"`
	UnlockText           string                  `protobuf:"bytes,4,opt,name=unlock_text,json=unlockText,proto3" json:"unlock_text,omitempty"`
	ExtendText           string                  `protobuf:"bytes,5,opt,name=extend_text,json=extendText,proto3" json:"extend_text,omitempty"`
	BackgroundUrl        string                  `protobuf:"bytes,6,opt,name=background_url,json=backgroundUrl,proto3" json:"background_url,omitempty"`
	GetAllBackgroundUrl  string                  `protobuf:"bytes,7,opt,name=get_all_background_url,json=getAllBackgroundUrl,proto3" json:"get_all_background_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *PresentSetUnlockItem) Reset()         { *m = PresentSetUnlockItem{} }
func (m *PresentSetUnlockItem) String() string { return proto.CompactTextString(m) }
func (*PresentSetUnlockItem) ProtoMessage()    {}
func (*PresentSetUnlockItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{52}
}
func (m *PresentSetUnlockItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentSetUnlockItem.Unmarshal(m, b)
}
func (m *PresentSetUnlockItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentSetUnlockItem.Marshal(b, m, deterministic)
}
func (dst *PresentSetUnlockItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentSetUnlockItem.Merge(dst, src)
}
func (m *PresentSetUnlockItem) XXX_Size() int {
	return xxx_messageInfo_PresentSetUnlockItem.Size(m)
}
func (m *PresentSetUnlockItem) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentSetUnlockItem.DiscardUnknown(m)
}

var xxx_messageInfo_PresentSetUnlockItem proto.InternalMessageInfo

func (m *PresentSetUnlockItem) GetSetId() uint32 {
	if m != nil {
		return m.SetId
	}
	return 0
}

func (m *PresentSetUnlockItem) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *PresentSetUnlockItem) GetRareLevel() PresentSetItemRareLevel {
	if m != nil {
		return m.RareLevel
	}
	return PresentSetItemRareLevel_PresentSetItemRareLevelUnknown
}

func (m *PresentSetUnlockItem) GetUnlockText() string {
	if m != nil {
		return m.UnlockText
	}
	return ""
}

func (m *PresentSetUnlockItem) GetExtendText() string {
	if m != nil {
		return m.ExtendText
	}
	return ""
}

func (m *PresentSetUnlockItem) GetBackgroundUrl() string {
	if m != nil {
		return m.BackgroundUrl
	}
	return ""
}

func (m *PresentSetUnlockItem) GetGetAllBackgroundUrl() string {
	if m != nil {
		return m.GetAllBackgroundUrl
	}
	return ""
}

type EmperorSetSendReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	EmperorSetId         uint32       `protobuf:"varint,2,opt,name=emperor_set_id,json=emperorSetId,proto3" json:"emperor_set_id,omitempty"`
	ChannelId            uint32       `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SendSource           uint32       `protobuf:"varint,4,opt,name=send_source,json=sendSource,proto3" json:"send_source,omitempty"`
	SendType             uint32       `protobuf:"varint,5,opt,name=send_type,json=sendType,proto3" json:"send_type,omitempty"`
	ToUid                uint32       `protobuf:"varint,6,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *EmperorSetSendReq) Reset()         { *m = EmperorSetSendReq{} }
func (m *EmperorSetSendReq) String() string { return proto.CompactTextString(m) }
func (*EmperorSetSendReq) ProtoMessage()    {}
func (*EmperorSetSendReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{53}
}
func (m *EmperorSetSendReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EmperorSetSendReq.Unmarshal(m, b)
}
func (m *EmperorSetSendReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EmperorSetSendReq.Marshal(b, m, deterministic)
}
func (dst *EmperorSetSendReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EmperorSetSendReq.Merge(dst, src)
}
func (m *EmperorSetSendReq) XXX_Size() int {
	return xxx_messageInfo_EmperorSetSendReq.Size(m)
}
func (m *EmperorSetSendReq) XXX_DiscardUnknown() {
	xxx_messageInfo_EmperorSetSendReq.DiscardUnknown(m)
}

var xxx_messageInfo_EmperorSetSendReq proto.InternalMessageInfo

func (m *EmperorSetSendReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *EmperorSetSendReq) GetEmperorSetId() uint32 {
	if m != nil {
		return m.EmperorSetId
	}
	return 0
}

func (m *EmperorSetSendReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *EmperorSetSendReq) GetSendSource() uint32 {
	if m != nil {
		return m.SendSource
	}
	return 0
}

func (m *EmperorSetSendReq) GetSendType() uint32 {
	if m != nil {
		return m.SendType
	}
	return 0
}

func (m *EmperorSetSendReq) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

type EmperorSetSendResp struct {
	BaseResp             *app.BaseResp                      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MsgInfo              *EmperorSetPresentMsg              `protobuf:"bytes,2,opt,name=msg_info,json=msgInfo,proto3" json:"msg_info,omitempty"`
	CurTbeans            uint64                             `protobuf:"varint,3,opt,name=cur_tbeans,json=curTbeans,proto3" json:"cur_tbeans,omitempty"`
	ItemSource           uint32                             `protobuf:"varint,4,opt,name=item_source,json=itemSource,proto3" json:"item_source,omitempty"`
	TargetInfo           *userpresent.PresentTargetUserInfo `protobuf:"bytes,5,opt,name=target_info,json=targetInfo,proto3" json:"target_info,omitempty"`
	BoxDetail            *EmperorBoxDetail                  `protobuf:"bytes,6,opt,name=box_detail,json=boxDetail,proto3" json:"box_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *EmperorSetSendResp) Reset()         { *m = EmperorSetSendResp{} }
func (m *EmperorSetSendResp) String() string { return proto.CompactTextString(m) }
func (*EmperorSetSendResp) ProtoMessage()    {}
func (*EmperorSetSendResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{54}
}
func (m *EmperorSetSendResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EmperorSetSendResp.Unmarshal(m, b)
}
func (m *EmperorSetSendResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EmperorSetSendResp.Marshal(b, m, deterministic)
}
func (dst *EmperorSetSendResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EmperorSetSendResp.Merge(dst, src)
}
func (m *EmperorSetSendResp) XXX_Size() int {
	return xxx_messageInfo_EmperorSetSendResp.Size(m)
}
func (m *EmperorSetSendResp) XXX_DiscardUnknown() {
	xxx_messageInfo_EmperorSetSendResp.DiscardUnknown(m)
}

var xxx_messageInfo_EmperorSetSendResp proto.InternalMessageInfo

func (m *EmperorSetSendResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *EmperorSetSendResp) GetMsgInfo() *EmperorSetPresentMsg {
	if m != nil {
		return m.MsgInfo
	}
	return nil
}

func (m *EmperorSetSendResp) GetCurTbeans() uint64 {
	if m != nil {
		return m.CurTbeans
	}
	return 0
}

func (m *EmperorSetSendResp) GetItemSource() uint32 {
	if m != nil {
		return m.ItemSource
	}
	return 0
}

func (m *EmperorSetSendResp) GetTargetInfo() *userpresent.PresentTargetUserInfo {
	if m != nil {
		return m.TargetInfo
	}
	return nil
}

func (m *EmperorSetSendResp) GetBoxDetail() *EmperorBoxDetail {
	if m != nil {
		return m.BoxDetail
	}
	return nil
}

type EmperorSetConfigSync struct {
	EmperorSetConfig     []*EmperorSetConfig `protobuf:"bytes,1,rep,name=emperor_set_config,json=emperorSetConfig,proto3" json:"emperor_set_config,omitempty"`
	LastUpdateTime       uint32              `protobuf:"varint,2,opt,name=last_update_time,json=lastUpdateTime,proto3" json:"last_update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *EmperorSetConfigSync) Reset()         { *m = EmperorSetConfigSync{} }
func (m *EmperorSetConfigSync) String() string { return proto.CompactTextString(m) }
func (*EmperorSetConfigSync) ProtoMessage()    {}
func (*EmperorSetConfigSync) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{55}
}
func (m *EmperorSetConfigSync) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EmperorSetConfigSync.Unmarshal(m, b)
}
func (m *EmperorSetConfigSync) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EmperorSetConfigSync.Marshal(b, m, deterministic)
}
func (dst *EmperorSetConfigSync) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EmperorSetConfigSync.Merge(dst, src)
}
func (m *EmperorSetConfigSync) XXX_Size() int {
	return xxx_messageInfo_EmperorSetConfigSync.Size(m)
}
func (m *EmperorSetConfigSync) XXX_DiscardUnknown() {
	xxx_messageInfo_EmperorSetConfigSync.DiscardUnknown(m)
}

var xxx_messageInfo_EmperorSetConfigSync proto.InternalMessageInfo

func (m *EmperorSetConfigSync) GetEmperorSetConfig() []*EmperorSetConfig {
	if m != nil {
		return m.EmperorSetConfig
	}
	return nil
}

func (m *EmperorSetConfigSync) GetLastUpdateTime() uint32 {
	if m != nil {
		return m.LastUpdateTime
	}
	return 0
}

type EmperorSetConfig struct {
	EmperorSetId            uint32                              `protobuf:"varint,1,opt,name=emperor_set_id,json=emperorSetId,proto3" json:"emperor_set_id,omitempty"`
	EmperorSetName          string                              `protobuf:"bytes,2,opt,name=emperor_set_name,json=emperorSetName,proto3" json:"emperor_set_name,omitempty"`
	EmperorSetIcon          string                              `protobuf:"bytes,3,opt,name=emperor_set_icon,json=emperorSetIcon,proto3" json:"emperor_set_icon,omitempty"`
	EffectBegin             uint32                              `protobuf:"varint,4,opt,name=effect_begin,json=effectBegin,proto3" json:"effect_begin,omitempty"`
	EffectEnd               uint32                              `protobuf:"varint,5,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	IsShow                  bool                                `protobuf:"varint,6,opt,name=is_show,json=isShow,proto3" json:"is_show,omitempty"`
	ShowEffectEnd           bool                                `protobuf:"varint,7,opt,name=show_effect_end,json=showEffectEnd,proto3" json:"show_effect_end,omitempty"`
	Rank                    float32                             `protobuf:"fixed32,8,opt,name=rank,proto3" json:"rank,omitempty"`
	FloatJumpType           EmperorSetConfig_EmperorSetJumpType `protobuf:"varint,9,opt,name=float_jump_type,json=floatJumpType,proto3,enum=ga.present_go_logic.EmperorSetConfig_EmperorSetJumpType" json:"float_jump_type,omitempty"`
	FloatImageUrl           string                              `protobuf:"bytes,10,opt,name=float_image_url,json=floatImageUrl,proto3" json:"float_image_url,omitempty"`
	FloatJumpUrl            string                              `protobuf:"bytes,11,opt,name=float_jump_url,json=floatJumpUrl,proto3" json:"float_jump_url,omitempty"`
	TotalPrice              uint32                              `protobuf:"varint,12,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`
	EmperorEffectUrl        string                              `protobuf:"bytes,13,opt,name=emperor_effect_url,json=emperorEffectUrl,proto3" json:"emperor_effect_url,omitempty"`
	EmperorEffectMd5        string                              `protobuf:"bytes,14,opt,name=emperor_effect_md5,json=emperorEffectMd5,proto3" json:"emperor_effect_md5,omitempty"`
	PresentIdList           []uint32                            `protobuf:"varint,15,rep,packed,name=present_id_list,json=presentIdList,proto3" json:"present_id_list,omitempty"`
	UpdateTime              uint32                              `protobuf:"varint,16,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	IsDelete                bool                                `protobuf:"varint,17,opt,name=is_delete,json=isDelete,proto3" json:"is_delete,omitempty"`
	IsBoxBreaking           bool                                `protobuf:"varint,18,opt,name=is_box_breaking,json=isBoxBreaking,proto3" json:"is_box_breaking,omitempty"`
	EmperorViewingSourceUrl string                              `protobuf:"bytes,19,opt,name=emperor_viewing_source_url,json=emperorViewingSourceUrl,proto3" json:"emperor_viewing_source_url,omitempty"`
	EmperorViewingSourceMd5 string                              `protobuf:"bytes,20,opt,name=emperor_viewing_source_md5,json=emperorViewingSourceMd5,proto3" json:"emperor_viewing_source_md5,omitempty"`
	EmperorFlowSourceUrl    string                              `protobuf:"bytes,21,opt,name=emperor_flow_source_url,json=emperorFlowSourceUrl,proto3" json:"emperor_flow_source_url,omitempty"`
	EmperorFlowSourceMd5    string                              `protobuf:"bytes,22,opt,name=emperor_flow_source_md5,json=emperorFlowSourceMd5,proto3" json:"emperor_flow_source_md5,omitempty"`
	XXX_NoUnkeyedLiteral    struct{}                            `json:"-"`
	XXX_unrecognized        []byte                              `json:"-"`
	XXX_sizecache           int32                               `json:"-"`
}

func (m *EmperorSetConfig) Reset()         { *m = EmperorSetConfig{} }
func (m *EmperorSetConfig) String() string { return proto.CompactTextString(m) }
func (*EmperorSetConfig) ProtoMessage()    {}
func (*EmperorSetConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{56}
}
func (m *EmperorSetConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EmperorSetConfig.Unmarshal(m, b)
}
func (m *EmperorSetConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EmperorSetConfig.Marshal(b, m, deterministic)
}
func (dst *EmperorSetConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EmperorSetConfig.Merge(dst, src)
}
func (m *EmperorSetConfig) XXX_Size() int {
	return xxx_messageInfo_EmperorSetConfig.Size(m)
}
func (m *EmperorSetConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_EmperorSetConfig.DiscardUnknown(m)
}

var xxx_messageInfo_EmperorSetConfig proto.InternalMessageInfo

func (m *EmperorSetConfig) GetEmperorSetId() uint32 {
	if m != nil {
		return m.EmperorSetId
	}
	return 0
}

func (m *EmperorSetConfig) GetEmperorSetName() string {
	if m != nil {
		return m.EmperorSetName
	}
	return ""
}

func (m *EmperorSetConfig) GetEmperorSetIcon() string {
	if m != nil {
		return m.EmperorSetIcon
	}
	return ""
}

func (m *EmperorSetConfig) GetEffectBegin() uint32 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *EmperorSetConfig) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

func (m *EmperorSetConfig) GetIsShow() bool {
	if m != nil {
		return m.IsShow
	}
	return false
}

func (m *EmperorSetConfig) GetShowEffectEnd() bool {
	if m != nil {
		return m.ShowEffectEnd
	}
	return false
}

func (m *EmperorSetConfig) GetRank() float32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *EmperorSetConfig) GetFloatJumpType() EmperorSetConfig_EmperorSetJumpType {
	if m != nil {
		return m.FloatJumpType
	}
	return EmperorSetConfig_EmperorSetJumpTypeNone
}

func (m *EmperorSetConfig) GetFloatImageUrl() string {
	if m != nil {
		return m.FloatImageUrl
	}
	return ""
}

func (m *EmperorSetConfig) GetFloatJumpUrl() string {
	if m != nil {
		return m.FloatJumpUrl
	}
	return ""
}

func (m *EmperorSetConfig) GetTotalPrice() uint32 {
	if m != nil {
		return m.TotalPrice
	}
	return 0
}

func (m *EmperorSetConfig) GetEmperorEffectUrl() string {
	if m != nil {
		return m.EmperorEffectUrl
	}
	return ""
}

func (m *EmperorSetConfig) GetEmperorEffectMd5() string {
	if m != nil {
		return m.EmperorEffectMd5
	}
	return ""
}

func (m *EmperorSetConfig) GetPresentIdList() []uint32 {
	if m != nil {
		return m.PresentIdList
	}
	return nil
}

func (m *EmperorSetConfig) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *EmperorSetConfig) GetIsDelete() bool {
	if m != nil {
		return m.IsDelete
	}
	return false
}

func (m *EmperorSetConfig) GetIsBoxBreaking() bool {
	if m != nil {
		return m.IsBoxBreaking
	}
	return false
}

func (m *EmperorSetConfig) GetEmperorViewingSourceUrl() string {
	if m != nil {
		return m.EmperorViewingSourceUrl
	}
	return ""
}

func (m *EmperorSetConfig) GetEmperorViewingSourceMd5() string {
	if m != nil {
		return m.EmperorViewingSourceMd5
	}
	return ""
}

func (m *EmperorSetConfig) GetEmperorFlowSourceUrl() string {
	if m != nil {
		return m.EmperorFlowSourceUrl
	}
	return ""
}

func (m *EmperorSetConfig) GetEmperorFlowSourceMd5() string {
	if m != nil {
		return m.EmperorFlowSourceMd5
	}
	return ""
}

type EmperorSetPresentInfo struct {
	PresentList          []*userpresent.PresentSendItemInfo `protobuf:"bytes,1,rep,name=present_list,json=presentList,proto3" json:"present_list,omitempty"`
	EmperorSetId         uint32                             `protobuf:"varint,2,opt,name=emperor_set_id,json=emperorSetId,proto3" json:"emperor_set_id,omitempty"`
	EmperorEffectJson    string                             `protobuf:"bytes,3,opt,name=emperor_effect_json,json=emperorEffectJson,proto3" json:"emperor_effect_json,omitempty"`
	ViewingEffectJson    string                             `protobuf:"bytes,4,opt,name=viewing_effect_json,json=viewingEffectJson,proto3" json:"viewing_effect_json,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *EmperorSetPresentInfo) Reset()         { *m = EmperorSetPresentInfo{} }
func (m *EmperorSetPresentInfo) String() string { return proto.CompactTextString(m) }
func (*EmperorSetPresentInfo) ProtoMessage()    {}
func (*EmperorSetPresentInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{57}
}
func (m *EmperorSetPresentInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EmperorSetPresentInfo.Unmarshal(m, b)
}
func (m *EmperorSetPresentInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EmperorSetPresentInfo.Marshal(b, m, deterministic)
}
func (dst *EmperorSetPresentInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EmperorSetPresentInfo.Merge(dst, src)
}
func (m *EmperorSetPresentInfo) XXX_Size() int {
	return xxx_messageInfo_EmperorSetPresentInfo.Size(m)
}
func (m *EmperorSetPresentInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_EmperorSetPresentInfo.DiscardUnknown(m)
}

var xxx_messageInfo_EmperorSetPresentInfo proto.InternalMessageInfo

func (m *EmperorSetPresentInfo) GetPresentList() []*userpresent.PresentSendItemInfo {
	if m != nil {
		return m.PresentList
	}
	return nil
}

func (m *EmperorSetPresentInfo) GetEmperorSetId() uint32 {
	if m != nil {
		return m.EmperorSetId
	}
	return 0
}

func (m *EmperorSetPresentInfo) GetEmperorEffectJson() string {
	if m != nil {
		return m.EmperorEffectJson
	}
	return ""
}

func (m *EmperorSetPresentInfo) GetViewingEffectJson() string {
	if m != nil {
		return m.ViewingEffectJson
	}
	return ""
}

type EmperorSetPresentMsg struct {
	PresentList          []*userpresent.PresentSendItemInfo `protobuf:"bytes,1,rep,name=present_list,json=presentList,proto3" json:"present_list,omitempty"`
	SendTime             uint64                             `protobuf:"varint,2,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	ChannelId            uint32                             `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SendUid              uint32                             `protobuf:"varint,4,opt,name=send_uid,json=sendUid,proto3" json:"send_uid,omitempty"`
	SendAccount          string                             `protobuf:"bytes,5,opt,name=send_account,json=sendAccount,proto3" json:"send_account,omitempty"`
	SendNickname         string                             `protobuf:"bytes,6,opt,name=send_nickname,json=sendNickname,proto3" json:"send_nickname,omitempty"`
	TargetUid            uint32                             `protobuf:"varint,7,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	TargetAccount        string                             `protobuf:"bytes,8,opt,name=target_account,json=targetAccount,proto3" json:"target_account,omitempty"`
	TargetNickname       string                             `protobuf:"bytes,9,opt,name=target_nickname,json=targetNickname,proto3" json:"target_nickname,omitempty"`
	ExtendJson           string                             `protobuf:"bytes,10,opt,name=extend_json,json=extendJson,proto3" json:"extend_json,omitempty"`
	FromUserProfile      *app.UserProfile                   `protobuf:"bytes,11,opt,name=from_user_profile,json=fromUserProfile,proto3" json:"from_user_profile,omitempty"`
	ToUserProfile        *app.UserProfile                   `protobuf:"bytes,12,opt,name=to_user_profile,json=toUserProfile,proto3" json:"to_user_profile,omitempty"`
	EmperorSetId         uint32                             `protobuf:"varint,13,opt,name=emperor_set_id,json=emperorSetId,proto3" json:"emperor_set_id,omitempty"`
	EmperorEffectJson    string                             `protobuf:"bytes,14,opt,name=emperor_effect_json,json=emperorEffectJson,proto3" json:"emperor_effect_json,omitempty"`
	IsBoxBreaking        bool                               `protobuf:"varint,15,opt,name=is_box_breaking,json=isBoxBreaking,proto3" json:"is_box_breaking,omitempty"`
	DelayTime            uint64                             `protobuf:"varint,16,opt,name=delay_time,json=delayTime,proto3" json:"delay_time,omitempty"`
	ViewingEffectJson    string                             `protobuf:"bytes,17,opt,name=viewing_effect_json,json=viewingEffectJson,proto3" json:"viewing_effect_json,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *EmperorSetPresentMsg) Reset()         { *m = EmperorSetPresentMsg{} }
func (m *EmperorSetPresentMsg) String() string { return proto.CompactTextString(m) }
func (*EmperorSetPresentMsg) ProtoMessage()    {}
func (*EmperorSetPresentMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{58}
}
func (m *EmperorSetPresentMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EmperorSetPresentMsg.Unmarshal(m, b)
}
func (m *EmperorSetPresentMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EmperorSetPresentMsg.Marshal(b, m, deterministic)
}
func (dst *EmperorSetPresentMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EmperorSetPresentMsg.Merge(dst, src)
}
func (m *EmperorSetPresentMsg) XXX_Size() int {
	return xxx_messageInfo_EmperorSetPresentMsg.Size(m)
}
func (m *EmperorSetPresentMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_EmperorSetPresentMsg.DiscardUnknown(m)
}

var xxx_messageInfo_EmperorSetPresentMsg proto.InternalMessageInfo

func (m *EmperorSetPresentMsg) GetPresentList() []*userpresent.PresentSendItemInfo {
	if m != nil {
		return m.PresentList
	}
	return nil
}

func (m *EmperorSetPresentMsg) GetSendTime() uint64 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *EmperorSetPresentMsg) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *EmperorSetPresentMsg) GetSendUid() uint32 {
	if m != nil {
		return m.SendUid
	}
	return 0
}

func (m *EmperorSetPresentMsg) GetSendAccount() string {
	if m != nil {
		return m.SendAccount
	}
	return ""
}

func (m *EmperorSetPresentMsg) GetSendNickname() string {
	if m != nil {
		return m.SendNickname
	}
	return ""
}

func (m *EmperorSetPresentMsg) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *EmperorSetPresentMsg) GetTargetAccount() string {
	if m != nil {
		return m.TargetAccount
	}
	return ""
}

func (m *EmperorSetPresentMsg) GetTargetNickname() string {
	if m != nil {
		return m.TargetNickname
	}
	return ""
}

func (m *EmperorSetPresentMsg) GetExtendJson() string {
	if m != nil {
		return m.ExtendJson
	}
	return ""
}

func (m *EmperorSetPresentMsg) GetFromUserProfile() *app.UserProfile {
	if m != nil {
		return m.FromUserProfile
	}
	return nil
}

func (m *EmperorSetPresentMsg) GetToUserProfile() *app.UserProfile {
	if m != nil {
		return m.ToUserProfile
	}
	return nil
}

func (m *EmperorSetPresentMsg) GetEmperorSetId() uint32 {
	if m != nil {
		return m.EmperorSetId
	}
	return 0
}

func (m *EmperorSetPresentMsg) GetEmperorEffectJson() string {
	if m != nil {
		return m.EmperorEffectJson
	}
	return ""
}

func (m *EmperorSetPresentMsg) GetIsBoxBreaking() bool {
	if m != nil {
		return m.IsBoxBreaking
	}
	return false
}

func (m *EmperorSetPresentMsg) GetDelayTime() uint64 {
	if m != nil {
		return m.DelayTime
	}
	return 0
}

func (m *EmperorSetPresentMsg) GetViewingEffectJson() string {
	if m != nil {
		return m.ViewingEffectJson
	}
	return ""
}

type GetEmperorSetConfigByIdReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	EmperorSetId         uint32       `protobuf:"varint,2,opt,name=emperor_set_id,json=emperorSetId,proto3" json:"emperor_set_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetEmperorSetConfigByIdReq) Reset()         { *m = GetEmperorSetConfigByIdReq{} }
func (m *GetEmperorSetConfigByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetEmperorSetConfigByIdReq) ProtoMessage()    {}
func (*GetEmperorSetConfigByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{59}
}
func (m *GetEmperorSetConfigByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEmperorSetConfigByIdReq.Unmarshal(m, b)
}
func (m *GetEmperorSetConfigByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEmperorSetConfigByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetEmperorSetConfigByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEmperorSetConfigByIdReq.Merge(dst, src)
}
func (m *GetEmperorSetConfigByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetEmperorSetConfigByIdReq.Size(m)
}
func (m *GetEmperorSetConfigByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEmperorSetConfigByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetEmperorSetConfigByIdReq proto.InternalMessageInfo

func (m *GetEmperorSetConfigByIdReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetEmperorSetConfigByIdReq) GetEmperorSetId() uint32 {
	if m != nil {
		return m.EmperorSetId
	}
	return 0
}

type GetEmperorSetConfigByIdResp struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ConfigList           *EmperorSetConfig `protobuf:"bytes,2,opt,name=config_list,json=configList,proto3" json:"config_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetEmperorSetConfigByIdResp) Reset()         { *m = GetEmperorSetConfigByIdResp{} }
func (m *GetEmperorSetConfigByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetEmperorSetConfigByIdResp) ProtoMessage()    {}
func (*GetEmperorSetConfigByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{60}
}
func (m *GetEmperorSetConfigByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEmperorSetConfigByIdResp.Unmarshal(m, b)
}
func (m *GetEmperorSetConfigByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEmperorSetConfigByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetEmperorSetConfigByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEmperorSetConfigByIdResp.Merge(dst, src)
}
func (m *GetEmperorSetConfigByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetEmperorSetConfigByIdResp.Size(m)
}
func (m *GetEmperorSetConfigByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEmperorSetConfigByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetEmperorSetConfigByIdResp proto.InternalMessageInfo

func (m *GetEmperorSetConfigByIdResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetEmperorSetConfigByIdResp) GetConfigList() *EmperorSetConfig {
	if m != nil {
		return m.ConfigList
	}
	return nil
}

// 展示帝王套礼物盒子会用到的信息
type EmperorBoxInfo struct {
	ItemMsg              *EmperorSetPresentMsg `protobuf:"bytes,1,opt,name=item_msg,json=itemMsg,proto3" json:"item_msg,omitempty"`
	BoxDetail            *EmperorBoxDetail     `protobuf:"bytes,2,opt,name=box_detail,json=boxDetail,proto3" json:"box_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *EmperorBoxInfo) Reset()         { *m = EmperorBoxInfo{} }
func (m *EmperorBoxInfo) String() string { return proto.CompactTextString(m) }
func (*EmperorBoxInfo) ProtoMessage()    {}
func (*EmperorBoxInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{61}
}
func (m *EmperorBoxInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EmperorBoxInfo.Unmarshal(m, b)
}
func (m *EmperorBoxInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EmperorBoxInfo.Marshal(b, m, deterministic)
}
func (dst *EmperorBoxInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EmperorBoxInfo.Merge(dst, src)
}
func (m *EmperorBoxInfo) XXX_Size() int {
	return xxx_messageInfo_EmperorBoxInfo.Size(m)
}
func (m *EmperorBoxInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_EmperorBoxInfo.DiscardUnknown(m)
}

var xxx_messageInfo_EmperorBoxInfo proto.InternalMessageInfo

func (m *EmperorBoxInfo) GetItemMsg() *EmperorSetPresentMsg {
	if m != nil {
		return m.ItemMsg
	}
	return nil
}

func (m *EmperorBoxInfo) GetBoxDetail() *EmperorBoxDetail {
	if m != nil {
		return m.BoxDetail
	}
	return nil
}

// 帝王套的开盒信息，为了兼容单独定义
type EmperorBoxDetail struct {
	BoxId                string           `protobuf:"bytes,1,opt,name=box_id,json=boxId,proto3" json:"box_id,omitempty"`
	FromUserProfile      *app.UserProfile `protobuf:"bytes,2,opt,name=from_user_profile,json=fromUserProfile,proto3" json:"from_user_profile,omitempty"`
	ToUserProfile        *app.UserProfile `protobuf:"bytes,3,opt,name=to_user_profile,json=toUserProfile,proto3" json:"to_user_profile,omitempty"`
	EmperorSetId         uint32           `protobuf:"varint,4,opt,name=emperor_set_id,json=emperorSetId,proto3" json:"emperor_set_id,omitempty"`
	EmperorSetName       string           `protobuf:"bytes,5,opt,name=emperor_set_name,json=emperorSetName,proto3" json:"emperor_set_name,omitempty"`
	SendTime             uint64           `protobuf:"varint,6,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	ExtendJson           string           `protobuf:"bytes,7,opt,name=extend_json,json=extendJson,proto3" json:"extend_json,omitempty"`
	DelayTime            uint64           `protobuf:"varint,8,opt,name=delay_time,json=delayTime,proto3" json:"delay_time,omitempty"`
	IsVisibleToSender    bool             `protobuf:"varint,9,opt,name=is_visible_to_sender,json=isVisibleToSender,proto3" json:"is_visible_to_sender,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *EmperorBoxDetail) Reset()         { *m = EmperorBoxDetail{} }
func (m *EmperorBoxDetail) String() string { return proto.CompactTextString(m) }
func (*EmperorBoxDetail) ProtoMessage()    {}
func (*EmperorBoxDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{62}
}
func (m *EmperorBoxDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EmperorBoxDetail.Unmarshal(m, b)
}
func (m *EmperorBoxDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EmperorBoxDetail.Marshal(b, m, deterministic)
}
func (dst *EmperorBoxDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EmperorBoxDetail.Merge(dst, src)
}
func (m *EmperorBoxDetail) XXX_Size() int {
	return xxx_messageInfo_EmperorBoxDetail.Size(m)
}
func (m *EmperorBoxDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_EmperorBoxDetail.DiscardUnknown(m)
}

var xxx_messageInfo_EmperorBoxDetail proto.InternalMessageInfo

func (m *EmperorBoxDetail) GetBoxId() string {
	if m != nil {
		return m.BoxId
	}
	return ""
}

func (m *EmperorBoxDetail) GetFromUserProfile() *app.UserProfile {
	if m != nil {
		return m.FromUserProfile
	}
	return nil
}

func (m *EmperorBoxDetail) GetToUserProfile() *app.UserProfile {
	if m != nil {
		return m.ToUserProfile
	}
	return nil
}

func (m *EmperorBoxDetail) GetEmperorSetId() uint32 {
	if m != nil {
		return m.EmperorSetId
	}
	return 0
}

func (m *EmperorBoxDetail) GetEmperorSetName() string {
	if m != nil {
		return m.EmperorSetName
	}
	return ""
}

func (m *EmperorBoxDetail) GetSendTime() uint64 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *EmperorBoxDetail) GetExtendJson() string {
	if m != nil {
		return m.ExtendJson
	}
	return ""
}

func (m *EmperorBoxDetail) GetDelayTime() uint64 {
	if m != nil {
		return m.DelayTime
	}
	return 0
}

func (m *EmperorBoxDetail) GetIsVisibleToSender() bool {
	if m != nil {
		return m.IsVisibleToSender
	}
	return false
}

// 全服礼物前置特效 - 开盒
type UnpackEmperorBoxReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	BoxId                string       `protobuf:"bytes,2,opt,name=box_id,json=boxId,proto3" json:"box_id,omitempty"`
	ChannelId            uint32       `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UnpackEmperorBoxReq) Reset()         { *m = UnpackEmperorBoxReq{} }
func (m *UnpackEmperorBoxReq) String() string { return proto.CompactTextString(m) }
func (*UnpackEmperorBoxReq) ProtoMessage()    {}
func (*UnpackEmperorBoxReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{63}
}
func (m *UnpackEmperorBoxReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnpackEmperorBoxReq.Unmarshal(m, b)
}
func (m *UnpackEmperorBoxReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnpackEmperorBoxReq.Marshal(b, m, deterministic)
}
func (dst *UnpackEmperorBoxReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnpackEmperorBoxReq.Merge(dst, src)
}
func (m *UnpackEmperorBoxReq) XXX_Size() int {
	return xxx_messageInfo_UnpackEmperorBoxReq.Size(m)
}
func (m *UnpackEmperorBoxReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UnpackEmperorBoxReq.DiscardUnknown(m)
}

var xxx_messageInfo_UnpackEmperorBoxReq proto.InternalMessageInfo

func (m *UnpackEmperorBoxReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UnpackEmperorBoxReq) GetBoxId() string {
	if m != nil {
		return m.BoxId
	}
	return ""
}

func (m *UnpackEmperorBoxReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type UnpackEmperorBoxResp struct {
	BaseResp             *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	BoxInfo              *EmperorBoxInfo `protobuf:"bytes,2,opt,name=box_info,json=boxInfo,proto3" json:"box_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *UnpackEmperorBoxResp) Reset()         { *m = UnpackEmperorBoxResp{} }
func (m *UnpackEmperorBoxResp) String() string { return proto.CompactTextString(m) }
func (*UnpackEmperorBoxResp) ProtoMessage()    {}
func (*UnpackEmperorBoxResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{64}
}
func (m *UnpackEmperorBoxResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnpackEmperorBoxResp.Unmarshal(m, b)
}
func (m *UnpackEmperorBoxResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnpackEmperorBoxResp.Marshal(b, m, deterministic)
}
func (dst *UnpackEmperorBoxResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnpackEmperorBoxResp.Merge(dst, src)
}
func (m *UnpackEmperorBoxResp) XXX_Size() int {
	return xxx_messageInfo_UnpackEmperorBoxResp.Size(m)
}
func (m *UnpackEmperorBoxResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UnpackEmperorBoxResp.DiscardUnknown(m)
}

var xxx_messageInfo_UnpackEmperorBoxResp proto.InternalMessageInfo

func (m *UnpackEmperorBoxResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *UnpackEmperorBoxResp) GetBoxInfo() *EmperorBoxInfo {
	if m != nil {
		return m.BoxInfo
	}
	return nil
}

// 展示礼物盒会用到的信息
type EmperorBoxOpenMsg struct {
	BoxId                string   `protobuf:"bytes,1,opt,name=box_id,json=boxId,proto3" json:"box_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EmperorBoxOpenMsg) Reset()         { *m = EmperorBoxOpenMsg{} }
func (m *EmperorBoxOpenMsg) String() string { return proto.CompactTextString(m) }
func (*EmperorBoxOpenMsg) ProtoMessage()    {}
func (*EmperorBoxOpenMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_go_logic__12c128bd7bc35ccc, []int{65}
}
func (m *EmperorBoxOpenMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EmperorBoxOpenMsg.Unmarshal(m, b)
}
func (m *EmperorBoxOpenMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EmperorBoxOpenMsg.Marshal(b, m, deterministic)
}
func (dst *EmperorBoxOpenMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EmperorBoxOpenMsg.Merge(dst, src)
}
func (m *EmperorBoxOpenMsg) XXX_Size() int {
	return xxx_messageInfo_EmperorBoxOpenMsg.Size(m)
}
func (m *EmperorBoxOpenMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_EmperorBoxOpenMsg.DiscardUnknown(m)
}

var xxx_messageInfo_EmperorBoxOpenMsg proto.InternalMessageInfo

func (m *EmperorBoxOpenMsg) GetBoxId() string {
	if m != nil {
		return m.BoxId
	}
	return ""
}

func init() {
	proto.RegisterType((*AchievementArea)(nil), "ga.present_go_logic.AchievementArea")
	proto.RegisterType((*ActPresentDetail)(nil), "ga.present_go_logic.ActPresentDetail")
	proto.RegisterType((*ActPresentArea)(nil), "ga.present_go_logic.ActPresentArea")
	proto.RegisterType((*ActFrameConf)(nil), "ga.present_go_logic.ActFrameConf")
	proto.RegisterType((*ActRemindConf)(nil), "ga.present_go_logic.ActRemindConf")
	proto.RegisterType((*GetUserActPresentAreaReq)(nil), "ga.present_go_logic.GetUserActPresentAreaReq")
	proto.RegisterType((*GetUserActPresentAreaResp)(nil), "ga.present_go_logic.GetUserActPresentAreaResp")
	proto.RegisterType((*GetNeedPopUpPresentListReq)(nil), "ga.present_go_logic.GetNeedPopUpPresentListReq")
	proto.RegisterType((*GetNeedPopUpPresentListResp)(nil), "ga.present_go_logic.GetNeedPopUpPresentListResp")
	proto.RegisterType((*GetPresentExtraConfigReq)(nil), "ga.present_go_logic.GetPresentExtraConfigReq")
	proto.RegisterType((*GetPresentExtraConfigResp)(nil), "ga.present_go_logic.GetPresentExtraConfigResp")
	proto.RegisterType((*PrivilegePresentInfo)(nil), "ga.present_go_logic.PrivilegePresentInfo")
	proto.RegisterType((*PresentFloatLayer)(nil), "ga.present_go_logic.PresentFloatLayer")
	proto.RegisterType((*PresentFlashEffect)(nil), "ga.present_go_logic.PresentFlashEffect")
	proto.RegisterType((*FlashEffectConfig)(nil), "ga.present_go_logic.FlashEffectConfig")
	proto.RegisterType((*PresentEffectTimePush)(nil), "ga.present_go_logic.PresentEffectTimePush")
	proto.RegisterType((*PresentEffectTime)(nil), "ga.present_go_logic.PresentEffectTime")
	proto.RegisterType((*PresentEffectTimeInfo)(nil), "ga.present_go_logic.PresentEffectTimeInfo")
	proto.RegisterType((*PresentEffectTimeLevelInfo)(nil), "ga.present_go_logic.PresentEffectTimeLevelInfo")
	proto.RegisterType((*GetPresentEffectTimeDetailReq)(nil), "ga.present_go_logic.GetPresentEffectTimeDetailReq")
	proto.RegisterType((*GetPresentEffectTimeDetailResp)(nil), "ga.present_go_logic.GetPresentEffectTimeDetailResp")
	proto.RegisterType((*GetCustomizedPresentListReq)(nil), "ga.present_go_logic.GetCustomizedPresentListReq")
	proto.RegisterType((*GetCustomizedPresentListResp)(nil), "ga.present_go_logic.GetCustomizedPresentListResp")
	proto.RegisterType((*CustomizedPresentInfo)(nil), "ga.present_go_logic.CustomizedPresentInfo")
	proto.RegisterType((*GetCustomizedPresentDetailReq)(nil), "ga.present_go_logic.GetCustomizedPresentDetailReq")
	proto.RegisterType((*GetCustomizedPresentDetailResp)(nil), "ga.present_go_logic.GetCustomizedPresentDetailResp")
	proto.RegisterType((*CustomizedPresentDetail)(nil), "ga.present_go_logic.CustomizedPresentDetail")
	proto.RegisterMapType((map[string]uint32)(nil), "ga.present_go_logic.CustomizedPresentDetail.CustomMethodEntry")
	proto.RegisterMapType((map[uint32]*CustomPresentPreview)(nil), "ga.present_go_logic.CustomizedPresentDetail.PreviewMapEntry")
	proto.RegisterType((*CustomPresentPreview)(nil), "ga.present_go_logic.CustomPresentPreview")
	proto.RegisterType((*CustomOption)(nil), "ga.present_go_logic.CustomOption")
	proto.RegisterType((*OptionInfo)(nil), "ga.present_go_logic.OptionInfo")
	proto.RegisterType((*ReportCustomOptionChooseReq)(nil), "ga.present_go_logic.ReportCustomOptionChooseReq")
	proto.RegisterType((*ReportCustomOptionChooseResp)(nil), "ga.present_go_logic.ReportCustomOptionChooseResp")
	proto.RegisterType((*CustomPair)(nil), "ga.present_go_logic.CustomPair")
	proto.RegisterType((*PresentEffectAppend)(nil), "ga.present_go_logic.PresentEffectAppend")
	proto.RegisterType((*CommonSendPresentReq)(nil), "ga.present_go_logic.CommonSendPresentReq")
	proto.RegisterType((*CommonSendPresentResp)(nil), "ga.present_go_logic.CommonSendPresentResp")
	proto.RegisterType((*TreasurePrivilegeChannelMsg)(nil), "ga.present_go_logic.TreasurePrivilegeChannelMsg")
	proto.RegisterType((*PresentConfigSyncReq)(nil), "ga.present_go_logic.PresentConfigSyncReq")
	proto.RegisterType((*PresentConfigSyncResp)(nil), "ga.present_go_logic.PresentConfigSyncResp")
	proto.RegisterType((*PresentEnterBlacklist)(nil), "ga.present_go_logic.PresentEnterBlacklist")
	proto.RegisterType((*GetPresentSetInfoReq)(nil), "ga.present_go_logic.GetPresentSetInfoReq")
	proto.RegisterType((*GetPresentSetInfoResp)(nil), "ga.present_go_logic.GetPresentSetInfoResp")
	proto.RegisterType((*GetPresentSetDetailReq)(nil), "ga.present_go_logic.GetPresentSetDetailReq")
	proto.RegisterType((*GetPresentSetDetailResp)(nil), "ga.present_go_logic.GetPresentSetDetailResp")
	proto.RegisterType((*PresentSetInfo)(nil), "ga.present_go_logic.PresentSetInfo")
	proto.RegisterType((*PresentSetItem)(nil), "ga.present_go_logic.PresentSetItem")
	proto.RegisterType((*PresentSetDetail)(nil), "ga.present_go_logic.PresentSetDetail")
	proto.RegisterType((*PresentSetDetailResource)(nil), "ga.present_go_logic.PresentSetDetailResource")
	proto.RegisterType((*PresentSetUserBroadcast)(nil), "ga.present_go_logic.PresentSetUserBroadcast")
	proto.RegisterType((*PresentSetCollectionAward)(nil), "ga.present_go_logic.PresentSetCollectionAward")
	proto.RegisterType((*PresentSetCollectionAwardItem)(nil), "ga.present_go_logic.PresentSetCollectionAwardItem")
	proto.RegisterType((*PresentSetUnlockMsg)(nil), "ga.present_go_logic.PresentSetUnlockMsg")
	proto.RegisterType((*PresentSetUnlockItem)(nil), "ga.present_go_logic.PresentSetUnlockItem")
	proto.RegisterType((*EmperorSetSendReq)(nil), "ga.present_go_logic.EmperorSetSendReq")
	proto.RegisterType((*EmperorSetSendResp)(nil), "ga.present_go_logic.EmperorSetSendResp")
	proto.RegisterType((*EmperorSetConfigSync)(nil), "ga.present_go_logic.EmperorSetConfigSync")
	proto.RegisterType((*EmperorSetConfig)(nil), "ga.present_go_logic.EmperorSetConfig")
	proto.RegisterType((*EmperorSetPresentInfo)(nil), "ga.present_go_logic.EmperorSetPresentInfo")
	proto.RegisterType((*EmperorSetPresentMsg)(nil), "ga.present_go_logic.EmperorSetPresentMsg")
	proto.RegisterType((*GetEmperorSetConfigByIdReq)(nil), "ga.present_go_logic.GetEmperorSetConfigByIdReq")
	proto.RegisterType((*GetEmperorSetConfigByIdResp)(nil), "ga.present_go_logic.GetEmperorSetConfigByIdResp")
	proto.RegisterType((*EmperorBoxInfo)(nil), "ga.present_go_logic.EmperorBoxInfo")
	proto.RegisterType((*EmperorBoxDetail)(nil), "ga.present_go_logic.EmperorBoxDetail")
	proto.RegisterType((*UnpackEmperorBoxReq)(nil), "ga.present_go_logic.UnpackEmperorBoxReq")
	proto.RegisterType((*UnpackEmperorBoxResp)(nil), "ga.present_go_logic.UnpackEmperorBoxResp")
	proto.RegisterType((*EmperorBoxOpenMsg)(nil), "ga.present_go_logic.EmperorBoxOpenMsg")
	proto.RegisterEnum("ga.present_go_logic.CustomPreviewType", CustomPreviewType_name, CustomPreviewType_value)
	proto.RegisterEnum("ga.present_go_logic.PresentEnterType", PresentEnterType_name, PresentEnterType_value)
	proto.RegisterEnum("ga.present_go_logic.PresentSetItemRareLevel", PresentSetItemRareLevel_name, PresentSetItemRareLevel_value)
	proto.RegisterEnum("ga.present_go_logic.PresentSetActivityUrlType", PresentSetActivityUrlType_name, PresentSetActivityUrlType_value)
	proto.RegisterEnum("ga.present_go_logic.PresentSetCollectionAwardType", PresentSetCollectionAwardType_name, PresentSetCollectionAwardType_value)
	proto.RegisterEnum("ga.present_go_logic.PresentPreviewType", PresentPreviewType_name, PresentPreviewType_value)
	proto.RegisterEnum("ga.present_go_logic.EmperorSetConfig_EmperorSetJumpType", EmperorSetConfig_EmperorSetJumpType_name, EmperorSetConfig_EmperorSetJumpType_value)
}

func init() {
	proto.RegisterFile("present_go_logic/present-go-logic_.proto", fileDescriptor_present_go_logic__12c128bd7bc35ccc)
}

var fileDescriptor_present_go_logic__12c128bd7bc35ccc = []byte{
	// 5049 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x3c, 0x4d, 0x6f, 0x24, 0x49,
	0x56, 0x5b, 0x55, 0x2e, 0xbb, 0xea, 0x55, 0x95, 0xab, 0x9c, 0xb6, 0x7b, 0xdc, 0xee, 0xe9, 0x99,
	0x9e, 0xec, 0x99, 0x5e, 0x4f, 0xd3, 0xed, 0xde, 0xed, 0x61, 0x98, 0x61, 0x96, 0x9d, 0x59, 0xdb,
	0xdd, 0x3d, 0xe3, 0xd9, 0x76, 0x8f, 0x49, 0xdb, 0xd3, 0xb0, 0x02, 0xe5, 0x86, 0x33, 0xc3, 0x55,
	0x39, 0xce, 0xca, 0xcc, 0xc9, 0xcc, 0x6a, 0xdb, 0x8b, 0x84, 0x40, 0x80, 0x10, 0x0b, 0x07, 0x38,
	0xac, 0x40, 0x62, 0x0f, 0x70, 0x40, 0xe2, 0x80, 0x84, 0x00, 0x89, 0x03, 0x1c, 0x57, 0xe2, 0x8c,
	0x84, 0xf8, 0x09, 0x20, 0x0e, 0x70, 0xe0, 0xc2, 0x8d, 0x0f, 0xbd, 0x17, 0x91, 0x99, 0x91, 0x59,
	0x59, 0x65, 0x57, 0xb3, 0x87, 0xbd, 0xb4, 0x2a, 0x5f, 0xbc, 0xf7, 0x22, 0xe2, 0xbd, 0x17, 0xef,
	0x2b, 0xc2, 0x0d, 0x1b, 0x41, 0xc8, 0x23, 0xee, 0xc5, 0x66, 0xdf, 0x37, 0x5d, 0xbf, 0xef, 0x58,
	0x0f, 0x24, 0xe0, 0x7e, 0xdf, 0xbf, 0x4f, 0x00, 0x73, 0x33, 0x08, 0xfd, 0xd8, 0xd7, 0x96, 0xfb,
	0x6c, 0xb3, 0x88, 0xbc, 0xde, 0xe9, 0x33, 0xf3, 0x98, 0x45, 0x5c, 0xe0, 0xac, 0xbf, 0x36, 0x8a,
	0x78, 0x28, 0x91, 0x1e, 0x28, 0xbf, 0x25, 0x8f, 0xf5, 0x6e, 0x74, 0xe1, 0x59, 0x0f, 0xf0, 0x1f,
	0x01, 0xd0, 0x23, 0xe8, 0x6e, 0x59, 0x03, 0x87, 0xbf, 0xe0, 0x43, 0xee, 0xc5, 0x5b, 0x21, 0x67,
	0xda, 0x0a, 0xd4, 0x63, 0x27, 0x76, 0xf9, 0x5a, 0xe5, 0x56, 0x65, 0xa3, 0x69, 0x88, 0x0f, 0xed,
	0x3a, 0x34, 0xbe, 0x18, 0x0d, 0x03, 0x73, 0x14, 0xba, 0x6b, 0x55, 0x1a, 0x58, 0xc0, 0xef, 0xa3,
	0xd0, 0xd5, 0x5e, 0x81, 0x85, 0xc0, 0xb1, 0x68, 0xa4, 0x46, 0x23, 0xf3, 0x81, 0x63, 0xe1, 0xc0,
	0x35, 0x98, 0x8f, 0x62, 0x16, 0x8f, 0xa2, 0xb5, 0xb9, 0x5b, 0x95, 0x8d, 0x8e, 0x21, 0xbf, 0xf4,
	0x6f, 0x42, 0x6f, 0xcb, 0x8a, 0xf7, 0xc5, 0xd2, 0x1e, 0xf1, 0x98, 0x39, 0xc4, 0xc4, 0x89, 0xf9,
	0xd0, 0x74, 0x6c, 0x9a, 0xb7, 0x63, 0xcc, 0xe3, 0xe7, 0xae, 0xad, 0xf5, 0xa0, 0xe6, 0x8d, 0x86,
	0x34, 0x67, 0xc7, 0xc0, 0x9f, 0xfa, 0xaf, 0x55, 0x60, 0x31, 0xa3, 0x7f, 0xb9, 0x35, 0x7f, 0x13,
	0xe6, 0x6d, 0x9a, 0x78, 0xad, 0x76, 0xab, 0xb6, 0xd1, 0x7a, 0xf8, 0xd6, 0x66, 0x89, 0x74, 0x37,
	0x8b, 0xab, 0x34, 0x24, 0x91, 0xbe, 0x0b, 0xed, 0x2d, 0x2b, 0x7e, 0x12, 0xb2, 0x21, 0xdf, 0xf1,
	0xbd, 0x93, 0x09, 0xf3, 0xdf, 0x86, 0x4e, 0xc8, 0x23, 0x7f, 0x14, 0x5a, 0x3c, 0x52, 0x16, 0xd1,
	0x4e, 0x81, 0x47, 0xa1, 0xab, 0xff, 0x2a, 0x74, 0xb6, 0xac, 0xd8, 0xe0, 0x43, 0xc7, 0xb3, 0x89,
	0xd7, 0x75, 0x68, 0x1c, 0xf3, 0xbe, 0xe3, 0x99, 0x71, 0x24, 0x45, 0xb1, 0x40, 0xdf, 0x87, 0x91,
	0xb6, 0x0a, 0xf3, 0xdc, 0xb3, 0x71, 0x40, 0x88, 0xa3, 0xce, 0x3d, 0xfb, 0x30, 0xd2, 0x5e, 0x87,
	0x56, 0x48, 0xf4, 0x66, 0xcc, 0xcf, 0x63, 0xa9, 0x04, 0x10, 0xa0, 0x43, 0x7e, 0x1e, 0x6b, 0xeb,
	0xd0, 0xb0, 0x47, 0x21, 0x8b, 0x1d, 0xdf, 0x93, 0xaa, 0x48, 0xbf, 0xf5, 0x43, 0x58, 0xfb, 0x98,
	0xc7, 0x47, 0x11, 0x0f, 0xf3, 0x32, 0x35, 0xf8, 0x97, 0xda, 0x1d, 0x68, 0xa0, 0x71, 0x99, 0x21,
	0xff, 0x92, 0x96, 0xd2, 0x7a, 0xd8, 0x42, 0x39, 0x6d, 0xb3, 0x88, 0x1b, 0xfc, 0x4b, 0x63, 0xe1,
	0x58, 0xfc, 0x40, 0x1d, 0x8d, 0x1c, 0x3b, 0xd1, 0xd1, 0xc8, 0xb1, 0xf5, 0x7f, 0xaf, 0xc2, 0xf5,
	0x09, 0x6c, 0xa3, 0x40, 0x7b, 0x1b, 0x9a, 0x92, 0x6f, 0x14, 0x48, 0xc6, 0xed, 0x8c, 0x71, 0x14,
	0x18, 0x8d, 0x63, 0xf9, 0x4b, 0x7b, 0x0f, 0xea, 0x27, 0x28, 0x66, 0xda, 0x55, 0xeb, 0xe1, 0x1b,
	0x93, 0xf4, 0x94, 0xea, 0xc2, 0x10, 0xf8, 0xda, 0x13, 0x68, 0x27, 0x78, 0x2c, 0xe4, 0x8c, 0xf6,
	0xdd, 0x7a, 0x78, 0xfb, 0x12, 0x3d, 0xd3, 0x12, 0x5b, 0x81, 0x62, 0x5a, 0x4f, 0xa0, 0xc5, 0xb2,
	0x13, 0xb2, 0x56, 0x27, 0x36, 0x6f, 0x4e, 0x60, 0x93, 0x3b, 0x49, 0x86, 0x4a, 0xa8, 0x7d, 0x00,
	0xf3, 0x42, 0x23, 0x6b, 0xf3, 0xc4, 0x42, 0x9f, 0xb4, 0x92, 0xcc, 0x14, 0x0c, 0x49, 0x81, 0x0a,
	0xc6, 0x3d, 0x98, 0xd1, 0x99, 0x13, 0x5b, 0x83, 0xb5, 0x85, 0x5b, 0x95, 0x8d, 0x86, 0x01, 0x08,
	0x3a, 0x20, 0x88, 0xfe, 0x08, 0xd6, 0x3f, 0xe6, 0xf1, 0x33, 0xce, 0xed, 0x7d, 0x3f, 0x38, 0x0a,
	0xe4, 0x66, 0x9e, 0x3a, 0x51, 0x3c, 0x83, 0x1a, 0x75, 0x06, 0x37, 0x26, 0x72, 0x99, 0x4d, 0x6b,
	0x78, 0x9a, 0x6d, 0xd3, 0x75, 0xa2, 0x78, 0xad, 0x7a, 0xab, 0x46, 0xa7, 0xd9, 0x46, 0x3e, 0xfa,
	0x36, 0x59, 0x9b, 0xe4, 0xfc, 0xf8, 0x3c, 0x0e, 0x19, 0x6e, 0xd4, 0xe9, 0xcf, 0xb2, 0xcc, 0x3f,
	0x9b, 0x23, 0xdb, 0x2a, 0x63, 0x32, 0xdb, 0x2a, 0x3f, 0x86, 0x96, 0xcb, 0x2e, 0x78, 0x68, 0x3a,
	0xde, 0x89, 0x1f, 0xd1, 0x4a, 0x5b, 0x0f, 0xef, 0x94, 0xea, 0x45, 0x4e, 0xf6, 0xc4, 0xf5, 0x59,
	0xfc, 0x14, 0x69, 0x0c, 0x20, 0xd2, 0x5d, 0xa4, 0xd4, 0x9e, 0x42, 0xe7, 0xc4, 0x65, 0xd1, 0xc0,
	0xe4, 0x27, 0x27, 0xdc, 0x8a, 0x23, 0xe9, 0x54, 0xbe, 0x3a, 0x9d, 0x15, 0x8b, 0x06, 0x8f, 0x09,
	0xdf, 0x68, 0x9f, 0x64, 0x1f, 0x91, 0xf6, 0x0b, 0xb0, 0xa2, 0x72, 0x33, 0x2d, 0xda, 0x1c, 0x3a,
	0xd1, 0xc9, 0xeb, 0x53, 0xb8, 0x49, 0x59, 0x68, 0x27, 0x45, 0x50, 0xa4, 0x6d, 0x40, 0xcf, 0x65,
	0x51, 0x6c, 0x8e, 0x02, 0x9b, 0xc5, 0xdc, 0x8c, 0x9d, 0x21, 0x27, 0x83, 0xee, 0x18, 0x8b, 0x08,
	0x3f, 0x22, 0xf0, 0xa1, 0x33, 0xe4, 0x1a, 0x83, 0xeb, 0xc9, 0x1c, 0x72, 0x15, 0x88, 0x2c, 0x05,
	0x35, 0x7f, 0xb9, 0xa0, 0xc4, 0xbc, 0xc8, 0xca, 0xb8, 0x16, 0x14, 0x41, 0x42, 0x68, 0x26, 0x5c,
	0x0b, 0x42, 0xe7, 0x85, 0xe3, 0xf2, 0x3e, 0x37, 0x13, 0x3e, 0x38, 0xc1, 0xda, 0x02, 0xf1, 0x7f,
	0x7b, 0x02, 0x7f, 0x49, 0x22, 0x27, 0x42, 0x5e, 0xc6, 0x4a, 0x50, 0x02, 0xd5, 0x3f, 0x85, 0x95,
	0x32, 0x6c, 0x34, 0xce, 0xbe, 0x73, 0x12, 0x2b, 0xa1, 0x06, 0x3f, 0x77, 0x6d, 0xf4, 0xbc, 0xe4,
	0x5e, 0x51, 0x2c, 0xc2, 0x97, 0x2d, 0xa0, 0x83, 0x75, 0x86, 0x5c, 0xff, 0x51, 0x05, 0x96, 0xc6,
	0x6c, 0x60, 0x32, 0xa7, 0x3b, 0xd0, 0x3d, 0x41, 0x34, 0xd3, 0x19, 0xb2, 0x3e, 0x57, 0x7c, 0x7f,
	0x87, 0xc0, 0xbb, 0x08, 0xc5, 0x30, 0xa4, 0x46, 0xa8, 0x5a, 0x3e, 0x42, 0xdd, 0x81, 0xae, 0x13,
	0x99, 0xcc, 0x8a, 0x9d, 0x17, 0x4e, 0x7c, 0x41, 0x18, 0x73, 0x74, 0xee, 0x3b, 0x4e, 0xb4, 0x25,
	0xa1, 0x88, 0x77, 0x17, 0x96, 0xa2, 0x81, 0x7f, 0x66, 0x5a, 0x03, 0xe6, 0x79, 0xdc, 0x35, 0xe3,
	0x8b, 0x00, 0x95, 0x8a, 0x87, 0xae, 0x8b, 0x03, 0x3b, 0x02, 0x7e, 0x78, 0x11, 0x70, 0xfd, 0x13,
	0xd0, 0xc6, 0xad, 0x6f, 0xaa, 0x3c, 0x84, 0x21, 0xa6, 0xbe, 0x7d, 0x81, 0xbe, 0x77, 0x6d, 0xfd,
	0xb7, 0x2b, 0xb0, 0x34, 0x66, 0x73, 0x39, 0x82, 0x4a, 0x8e, 0x40, 0xbb, 0x09, 0x20, 0x86, 0x3c,
	0x26, 0xa5, 0xdb, 0x34, 0x9a, 0x04, 0x79, 0x86, 0xde, 0xfa, 0x06, 0x88, 0x0f, 0x45, 0x12, 0x82,
	0x15, 0x6e, 0x31, 0x1d, 0x1c, 0xda, 0xef, 0x92, 0x10, 0x92, 0xc1, 0x3d, 0xfb, 0x5d, 0xfd, 0x3f,
	0x2a, 0xb0, 0x3a, 0x66, 0x74, 0xfb, 0xa3, 0x68, 0x30, 0x79, 0x5f, 0x37, 0x01, 0xa4, 0x51, 0x73,
	0x2f, 0xd9, 0x59, 0x53, 0x40, 0x1e, 0x7b, 0xb6, 0xf6, 0x6d, 0x68, 0xc9, 0x61, 0xb2, 0x46, 0x11,
	0x78, 0xee, 0x5e, 0xcd, 0xda, 0xc9, 0x1c, 0x25, 0x77, 0x32, 0xb6, 0x5b, 0xd0, 0x76, 0x22, 0xd3,
	0xe3, 0x67, 0xa6, 0xcb, 0x5f, 0xf0, 0x44, 0x87, 0xe0, 0x44, 0xcf, 0xf8, 0xd9, 0x53, 0x84, 0x68,
	0xf7, 0x61, 0x39, 0x1d, 0x36, 0x6d, 0x76, 0x61, 0x5a, 0xfe, 0x48, 0x06, 0x9a, 0x8e, 0xd1, 0xf3,
	0x24, 0xda, 0x23, 0x76, 0xb1, 0x83, 0x70, 0xfd, 0x8f, 0x32, 0x4b, 0xcc, 0xa6, 0xfd, 0x89, 0xd8,
	0xab, 0xfe, 0x3b, 0x73, 0x25, 0xaa, 0x20, 0x29, 0xdc, 0x80, 0xa6, 0x87, 0x36, 0x4a, 0x3b, 0x13,
	0x0b, 0x6c, 0x78, 0xfe, 0x19, 0xed, 0x48, 0xfb, 0x3a, 0xac, 0x7a, 0xfc, 0x3c, 0x96, 0x12, 0x88,
	0xf0, 0x08, 0x0a, 0x44, 0xb1, 0x5a, 0x0d, 0x07, 0x49, 0x06, 0x07, 0x1c, 0xe3, 0x22, 0x92, 0x3c,
	0x80, 0x15, 0x85, 0x24, 0x13, 0x5a, 0x8d, 0x28, 0x96, 0x52, 0x8a, 0x44, 0x6a, 0x48, 0x30, 0x64,
	0xe7, 0xe3, 0x53, 0x88, 0x6c, 0x68, 0x69, 0xc8, 0xce, 0x0b, 0x33, 0x08, 0xbd, 0xa5, 0x34, 0xa4,
	0x0e, 0xd2, 0xdb, 0x9e, 0x44, 0xd5, 0xde, 0x87, 0xeb, 0x9e, 0x6f, 0xba, 0xce, 0xd0, 0x89, 0x4d,
	0x7e, 0x1e, 0x38, 0x21, 0x57, 0x16, 0x32, 0x4f, 0x7c, 0x57, 0x3d, 0xff, 0x29, 0x8e, 0x3f, 0xa6,
	0xe1, 0x74, 0x31, 0xb7, 0xa0, 0x4d, 0x6e, 0x38, 0x92, 0xc9, 0xdc, 0x02, 0x21, 0x03, 0xc2, 0x0e,
	0x44, 0x46, 0x77, 0x1f, 0x96, 0xb3, 0xe5, 0x66, 0x5c, 0x1b, 0xc2, 0x26, 0x92, 0xd5, 0xa6, 0x0c,
	0xef, 0xc3, 0x72, 0xa6, 0x64, 0xd3, 0xf7, 0xcc, 0x68, 0xc0, 0xdd, 0x93, 0xb5, 0xa6, 0x40, 0x4f,
	0xb5, 0xfd, 0x99, 0x77, 0x80, 0x70, 0xb2, 0x38, 0x7f, 0xdc, 0xe2, 0x40, 0x5a, 0x9c, 0x9f, 0xb7,
	0x38, 0xed, 0x1d, 0xb8, 0xe6, 0xf9, 0xb1, 0x63, 0x71, 0xb3, 0xb0, 0xdf, 0xb5, 0x16, 0x09, 0x65,
	0x59, 0x8c, 0x3e, 0x53, 0xf7, 0xaa, 0xff, 0x66, 0x05, 0xd6, 0xc7, 0x6c, 0x81, 0xf8, 0x92, 0x41,
	0xac, 0x40, 0x5d, 0xc8, 0x55, 0x18, 0x83, 0xf8, 0xa0, 0xf8, 0x54, 0x6e, 0x04, 0x8b, 0x6e, 0x5e,
	0x3d, 0x77, 0xa0, 0x5b, 0xae, 0xfb, 0x8e, 0x9b, 0x3b, 0x2d, 0xdf, 0x85, 0x9b, 0x4a, 0xaa, 0x90,
	0x2e, 0x44, 0xa6, 0xf3, 0x33, 0xa4, 0xb8, 0xca, 0x01, 0xab, 0xaa, 0x07, 0x4c, 0xff, 0xdd, 0x2a,
	0xbc, 0x36, 0x6d, 0x8a, 0x99, 0x13, 0xa7, 0xd2, 0x69, 0xf2, 0x27, 0xa8, 0x56, 0x38, 0x41, 0xcf,
	0x00, 0x84, 0x34, 0xe8, 0x10, 0x8b, 0x3c, 0xe1, 0xc1, 0xd5, 0x0e, 0x71, 0xaa, 0x12, 0xa3, 0xe9,
	0xa6, 0xda, 0x99, 0x6a, 0xda, 0xf5, 0x29, 0xa6, 0xad, 0x3f, 0xa6, 0x14, 0x72, 0x67, 0x14, 0xc5,
	0xfe, 0xd0, 0xf9, 0x1e, 0xb7, 0x5f, 0x32, 0x13, 0xfd, 0x61, 0x15, 0x5e, 0x9d, 0xcc, 0x67, 0x36,
	0x91, 0xee, 0x65, 0x85, 0x00, 0x89, 0x47, 0xa4, 0x79, 0xe5, 0x3e, 0x6e, 0x6c, 0x42, 0x92, 0x4c,
	0x52, 0x0f, 0x90, 0x6c, 0xde, 0x84, 0xc5, 0x5c, 0x0e, 0x15, 0x49, 0x6d, 0xb4, 0x95, 0x0c, 0x2a,
	0xd2, 0x7e, 0x09, 0x56, 0x0b, 0xf9, 0x13, 0x0b, 0x02, 0xf4, 0xc0, 0x42, 0x39, 0x1b, 0x97, 0x2b,
	0x67, 0x8b, 0xf0, 0x8d, 0xe5, 0x60, 0x1c, 0xa8, 0xff, 0x41, 0x15, 0x56, 0x4b, 0x97, 0xaa, 0x2d,
	0x42, 0x35, 0x0d, 0x01, 0x55, 0xc7, 0xc6, 0x12, 0xd4, 0x22, 0x44, 0xd3, 0x0f, 0xa8, 0xfc, 0xc3,
	0xdd, 0x37, 0x8d, 0xb6, 0x00, 0x7e, 0x46, 0x30, 0x8c, 0x11, 0xa9, 0x84, 0x6c, 0xb9, 0x9d, 0x66,
	0xb2, 0x67, 0x1b, 0x6d, 0xd2, 0x1a, 0x46, 0x69, 0x06, 0xd2, 0x34, 0xe6, 0xad, 0x21, 0x96, 0xae,
	0xc8, 0x7c, 0xc0, 0x22, 0x93, 0x8d, 0xe2, 0x81, 0x1f, 0x3a, 0xf1, 0x85, 0x74, 0x92, 0xed, 0x01,
	0x8b, 0xb6, 0x12, 0x18, 0xca, 0x0b, 0x91, 0x30, 0xc4, 0x89, 0x49, 0xc9, 0x37, 0x0a, 0xac, 0x67,
	0xfc, 0x4c, 0x6c, 0x43, 0x7b, 0x03, 0xda, 0x52, 0x4e, 0x54, 0xeb, 0x4a, 0x97, 0x28, 0x63, 0xd3,
	0x36, 0x82, 0x0a, 0x91, 0xac, 0x51, 0x88, 0x64, 0xfa, 0x73, 0x3a, 0xe9, 0x63, 0x52, 0x99, 0xfd,
	0xa4, 0x0b, 0x11, 0x56, 0x13, 0x11, 0xea, 0x7f, 0x52, 0xa1, 0x03, 0x3e, 0x91, 0xf3, 0x6c, 0xd6,
	0x78, 0x00, 0x8b, 0x89, 0xac, 0x65, 0x03, 0xa2, 0x4a, 0xf8, 0xf7, 0xae, 0x66, 0x8f, 0x72, 0xd2,
	0x4e, 0xa0, 0x7e, 0xea, 0x3f, 0x9a, 0x83, 0x57, 0x26, 0xa0, 0x8e, 0x59, 0xc4, 0x93, 0x32, 0x8b,
	0x98, 0x54, 0x58, 0xef, 0x28, 0x66, 0x52, 0x30, 0x1a, 0x2b, 0xe5, 0x33, 0xe4, 0xf1, 0xc0, 0xb7,
	0x65, 0xcd, 0xf3, 0xe1, 0x2c, 0xfb, 0x90, 0xf0, 0x3d, 0x62, 0xf0, 0xd8, 0x8b, 0xc3, 0x8b, 0x64,
	0x12, 0x01, 0x42, 0x9d, 0x8f, 0x22, 0x1e, 0x2a, 0xb9, 0x53, 0xc7, 0x68, 0x22, 0x44, 0x84, 0xe0,
	0x9b, 0x89, 0xdf, 0xa3, 0xbe, 0x47, 0x5d, 0x24, 0x95, 0x04, 0xa1, 0xb6, 0x07, 0x1a, 0xbf, 0xef,
	0xfa, 0xe1, 0xc9, 0x48, 0x62, 0xcc, 0x8b, 0xfe, 0x4b, 0x02, 0x24, 0xa4, 0x5f, 0x06, 0x3c, 0xde,
	0x2f, 0x1c, 0x7e, 0x66, 0x0e, 0x59, 0x20, 0x6b, 0x8f, 0x9f, 0x9b, 0x69, 0x17, 0xfb, 0x82, 0x7e,
	0x8f, 0x05, 0x62, 0x0f, 0x10, 0xa4, 0x80, 0xf5, 0x8f, 0x60, 0x69, 0x6c, 0x93, 0x5a, 0x0f, 0x6a,
	0xa7, 0xfc, 0x42, 0x36, 0x8b, 0xf0, 0x27, 0xc6, 0xc3, 0x17, 0xcc, 0x1d, 0x25, 0x75, 0x87, 0xf8,
	0xf8, 0xa0, 0xfa, 0x7e, 0x65, 0x7d, 0x00, 0xdd, 0x02, 0x7f, 0x95, 0xbc, 0x23, 0xc8, 0x3f, 0x52,
	0xc9, 0x27, 0x95, 0x4e, 0x62, 0x1d, 0x72, 0xe9, 0x92, 0xa7, 0x32, 0x93, 0xfe, 0x2b, 0xb0, 0x52,
	0x86, 0x82, 0x67, 0x33, 0x91, 0x10, 0x15, 0x17, 0x62, 0xde, 0x44, 0x6a, 0x58, 0x58, 0x68, 0xaf,
	0x67, 0x42, 0xcc, 0x6a, 0x9d, 0x44, 0x0c, 0xe8, 0x2a, 0x14, 0x04, 0x4c, 0xe2, 0x6b, 0x39, 0x04,
	0x4c, 0xe3, 0xff, 0xb2, 0x02, 0x6d, 0xd5, 0xda, 0x30, 0xe0, 0x49, 0xfb, 0x4a, 0xcd, 0xb7, 0x21,
	0x00, 0xbb, 0xd4, 0x10, 0x91, 0x83, 0x4a, 0x39, 0x01, 0x02, 0x44, 0xf5, 0xc4, 0xb7, 0xa0, 0x25,
	0xcc, 0x3b, 0xc9, 0x6b, 0x51, 0xab, 0xaf, 0x97, 0x8a, 0x45, 0xcc, 0x27, 0x92, 0x59, 0x3f, 0xfd,
	0xad, 0x4c, 0x41, 0xa6, 0x33, 0xa7, 0x4e, 0x81, 0x86, 0xa3, 0xff, 0x69, 0x05, 0x20, 0xa3, 0xc5,
	0xf5, 0x26, 0x33, 0xa6, 0xeb, 0x95, 0xec, 0x68, 0xbd, 0x72, 0x50, 0x5d, 0xaf, 0x00, 0xd1, 0x7a,
	0xdf, 0x80, 0xb6, 0x44, 0x10, 0xa6, 0x2e, 0x9c, 0xb0, 0x24, 0x12, 0xc6, 0xbe, 0x0a, 0xf3, 0xa2,
	0x92, 0x90, 0x35, 0x44, 0x9d, 0x6a, 0x08, 0x9c, 0x37, 0xa9, 0x13, 0xb9, 0x74, 0xc0, 0x0d, 0x59,
	0x21, 0x72, 0xfd, 0x0f, 0x2b, 0x70, 0xc3, 0xe0, 0x81, 0x1f, 0xc6, 0xaa, 0x6c, 0x77, 0x06, 0xbe,
	0x2f, 0x7c, 0xdd, 0x4b, 0xfa, 0x44, 0x14, 0xaf, 0x14, 0x4e, 0xc0, 0x9c, 0x70, 0xaa, 0x78, 0xa5,
	0x49, 0x31, 0x27, 0x4c, 0xa4, 0x87, 0xbf, 0xf5, 0x5d, 0x78, 0x75, 0xf2, 0xc2, 0x66, 0x72, 0xa9,
	0xfa, 0x13, 0x80, 0x6c, 0x92, 0xe9, 0x76, 0x93, 0x53, 0x52, 0x35, 0xaf, 0x24, 0x3d, 0x80, 0xe5,
	0x92, 0x08, 0x3c, 0xb9, 0xb4, 0x2a, 0xc6, 0xac, 0xea, 0x65, 0x31, 0xab, 0x56, 0x8c, 0x59, 0xff,
	0x59, 0x85, 0x95, 0x1d, 0x7f, 0x38, 0xf4, 0x3d, 0xcc, 0x6c, 0xe5, 0xe4, 0x33, 0x66, 0xa5, 0x49,
	0xd7, 0xbc, 0x9a, 0xeb, 0x9a, 0xdf, 0x04, 0x48, 0x1a, 0x02, 0x59, 0x48, 0x97, 0x90, 0x5d, 0x1b,
	0xdd, 0x8d, 0x5a, 0xff, 0x88, 0x0f, 0xb4, 0x52, 0x4a, 0xbc, 0x45, 0x77, 0x5a, 0x26, 0x7a, 0x80,
	0xa0, 0x03, 0x82, 0x20, 0x02, 0x4d, 0x27, 0x11, 0x44, 0x91, 0x03, 0x08, 0x92, 0x08, 0x37, 0xa0,
	0x29, 0xc6, 0x70, 0x56, 0x11, 0xc3, 0x1b, 0x02, 0x20, 0x84, 0x2f, 0x2a, 0x1e, 0x74, 0x22, 0x0d,
	0x39, 0x88, 0xf5, 0x0e, 0x7a, 0x90, 0xa7, 0xd0, 0xb3, 0x43, 0x76, 0x96, 0x36, 0x82, 0x02, 0xc7,
	0xa2, 0xfa, 0x45, 0x36, 0x4a, 0x95, 0xbb, 0x8c, 0xcd, 0x47, 0x21, 0x3b, 0x4b, 0x5c, 0x94, 0x63,
	0xc5, 0xa3, 0x90, 0x1b, 0x8b, 0x76, 0x0e, 0xa6, 0x5d, 0x87, 0xc6, 0x28, 0x69, 0x40, 0x02, 0xf5,
	0x42, 0x16, 0x46, 0xb2, 0x03, 0xf9, 0xb7, 0x73, 0xb0, 0x5a, 0x22, 0xf3, 0xd9, 0xa2, 0xf8, 0x2e,
	0x2c, 0x1e, 0xb3, 0xd8, 0x1a, 0x98, 0xc3, 0xa8, 0x9f, 0x64, 0x95, 0x69, 0x7b, 0x59, 0x5d, 0xab,
	0xe4, 0xbf, 0x8d, 0xc8, 0xe8, 0x26, 0xf6, 0xa2, 0xbe, 0xd1, 0x26, 0xd2, 0xbd, 0xa8, 0x4f, 0x7e,
	0xe3, 0x67, 0xa1, 0x91, 0x32, 0x11, 0xe5, 0xf7, 0x6b, 0x13, 0x98, 0xe0, 0x7a, 0x91, 0x7e, 0x61,
	0x28, 0x49, 0x51, 0xc9, 0xa3, 0xd0, 0x8c, 0x8f, 0x39, 0xf3, 0xc4, 0x1d, 0xcb, 0x9c, 0xd1, 0xb4,
	0x46, 0xe1, 0x21, 0x01, 0x8a, 0xda, 0xaa, 0x4f, 0xd7, 0xd6, 0x7c, 0x41, 0x5b, 0xb7, 0xa1, 0x23,
	0x07, 0x43, 0x3e, 0x64, 0x69, 0x4a, 0xd6, 0x16, 0x40, 0x83, 0x60, 0xda, 0x13, 0x68, 0xc5, 0x2c,
	0xec, 0xf3, 0x58, 0x88, 0xba, 0x91, 0xdd, 0xa5, 0x94, 0xac, 0xff, 0x90, 0x30, 0x8f, 0x22, 0xd1,
	0x35, 0x35, 0x40, 0x50, 0xa2, 0x52, 0xb4, 0x6f, 0x41, 0x53, 0xd8, 0x31, 0x4a, 0xa1, 0x39, 0x55,
	0x94, 0x28, 0x85, 0x5d, 0x34, 0x72, 0xe4, 0xd1, 0x70, 0xe4, 0x2f, 0xdc, 0xac, 0xac, 0x54, 0xa8,
	0x7d, 0x27, 0x6a, 0x59, 0x10, 0x20, 0xea, 0x90, 0x7c, 0x04, 0x70, 0xec, 0x9f, 0x27, 0x49, 0x57,
	0x8b, 0xe6, 0xb8, 0x35, 0x49, 0x5d, 0xfe, 0xb9, 0x4c, 0xb4, 0x9a, 0xc7, 0xc9, 0x4f, 0xfd, 0xef,
	0x2a, 0x70, 0xe3, 0x30, 0xe4, 0x2c, 0x1a, 0x85, 0x3c, 0xed, 0x2b, 0xca, 0xee, 0xda, 0x5e, 0xd4,
	0xd7, 0x6e, 0xc3, 0x1c, 0xb2, 0x92, 0x96, 0xd3, 0x45, 0xd6, 0xb8, 0xd7, 0xfd, 0xd0, 0x3f, 0x71,
	0x5c, 0x6e, 0xd0, 0x20, 0x8a, 0x9c, 0x9c, 0x89, 0x12, 0x06, 0x1a, 0x08, 0xa0, 0x20, 0x70, 0x13,
	0x80, 0x06, 0x83, 0xd0, 0xb1, 0xb8, 0x8c, 0x91, 0x84, 0xbe, 0x8f, 0x00, 0xdc, 0xe2, 0xf1, 0x28,
	0x8e, 0x7d, 0x2f, 0x17, 0x91, 0x04, 0x88, 0x52, 0x19, 0xb5, 0x9b, 0x58, 0xcf, 0x75, 0x13, 0xf5,
	0x7f, 0xa8, 0xc2, 0x8a, 0xdc, 0x9c, 0xe8, 0xd5, 0x1d, 0x5c, 0x78, 0xd6, 0x2c, 0x9e, 0xa6, 0xac,
	0x75, 0x5c, 0x2d, 0x6d, 0x1d, 0x6f, 0x40, 0xef, 0xc4, 0xf5, 0xcf, 0x72, 0x98, 0xc2, 0x01, 0x2d,
	0x22, 0x5c, 0xc1, 0xdc, 0x84, 0x65, 0xfb, 0xc2, 0x63, 0x43, 0xc7, 0xca, 0x21, 0xcb, 0x9e, 0x8c,
	0x1c, 0x52, 0xf0, 0x31, 0x48, 0x7a, 0xee, 0x45, 0xe2, 0x23, 0x64, 0xb4, 0x6b, 0x21, 0x4c, 0xee,
	0x4d, 0x7b, 0x17, 0x5e, 0xe1, 0xc3, 0x80, 0x87, 0x7e, 0x68, 0x46, 0x3c, 0xbf, 0x5a, 0x61, 0xe0,
	0x2b, 0x72, 0xf8, 0x80, 0x17, 0xd6, 0x7c, 0xe6, 0xc4, 0x03, 0x53, 0xa1, 0x95, 0xb7, 0x2c, 0x8b,
	0x08, 0x7f, 0x9c, 0xd2, 0xe8, 0xff, 0x5b, 0x4b, 0x7b, 0x5c, 0xaa, 0x20, 0x67, 0x73, 0x1f, 0x57,
	0x17, 0xe6, 0xcf, 0x40, 0x4b, 0xb4, 0xff, 0xc5, 0x01, 0x13, 0x81, 0x76, 0x15, 0xd9, 0x26, 0x55,
	0x5f, 0xcc, 0x87, 0xb2, 0xe3, 0x0f, 0x02, 0x93, 0x0e, 0xd4, 0x21, 0xf4, 0xb8, 0x17, 0xf3, 0xd0,
	0x3c, 0x76, 0x99, 0x75, 0x2a, 0x88, 0xe7, 0xa6, 0x14, 0xbe, 0x49, 0xe0, 0x43, 0x9a, 0x6d, 0x24,
	0x41, 0x0a, 0x63, 0x91, 0xa7, 0xdf, 0xc4, 0x75, 0x07, 0x9a, 0xa4, 0xda, 0xe8, 0xc2, 0xb3, 0xe4,
	0x4d, 0x18, 0xdd, 0x02, 0xd0, 0x85, 0xf2, 0x96, 0xfd, 0x82, 0x79, 0x16, 0xb7, 0xc5, 0x6a, 0xb2,
	0x66, 0xf9, 0x19, 0x49, 0xa8, 0x71, 0x22, 0x7f, 0x69, 0x1c, 0x56, 0x13, 0xad, 0xc7, 0x7c, 0x18,
	0xb8, 0x28, 0x01, 0x62, 0x28, 0xee, 0xc5, 0xbe, 0x3e, 0x9d, 0xe1, 0x23, 0x41, 0x7a, 0x28, 0x29,
	0x89, 0x77, 0x62, 0x45, 0x2a, 0x50, 0x7b, 0x0e, 0x9a, 0x6a, 0x09, 0x42, 0x36, 0xa4, 0xd4, 0x49,
	0xf9, 0x71, 0xa6, 0x65, 0x45, 0xb3, 0x3d, 0x5e, 0x80, 0xea, 0xbf, 0xa1, 0x34, 0x9c, 0x73, 0xe2,
	0xd2, 0x1e, 0x01, 0x08, 0xa1, 0xa7, 0x69, 0xf2, 0xe2, 0x84, 0x8b, 0x65, 0x95, 0x1e, 0xc3, 0x9f,
	0xd1, 0xe4, 0xc9, 0x4f, 0x2c, 0x98, 0x45, 0xbe, 0x81, 0x0e, 0x51, 0xb9, 0x42, 0x6b, 0x53, 0xda,
	0x11, 0xf3, 0x21, 0x85, 0xb1, 0x0f, 0x61, 0x25, 0xeb, 0x3a, 0x1d, 0x70, 0xd1, 0xa8, 0x98, 0xa1,
	0xc3, 0xf2, 0x17, 0x15, 0x58, 0x2d, 0x61, 0x30, 0x9b, 0x1d, 0x7f, 0x08, 0x8d, 0x28, 0xf1, 0xfd,
	0xa2, 0x8c, 0xbc, 0x3d, 0x6d, 0xbb, 0xc9, 0x2c, 0x0b, 0x91, 0x74, 0xfb, 0x65, 0xe7, 0xa0, 0x56,
	0x76, 0x0e, 0xf4, 0xe7, 0x70, 0x2d, 0xb7, 0xda, 0xd9, 0xcb, 0xfa, 0x55, 0x98, 0xc7, 0xb5, 0xa6,
	0x99, 0x52, 0x3d, 0xe2, 0xf1, 0xae, 0xad, 0x7f, 0xbf, 0x02, 0xaf, 0x94, 0x72, 0x9e, 0x4d, 0x12,
	0x8f, 0x00, 0x90, 0x7b, 0xae, 0xa4, 0x7f, 0xeb, 0x12, 0x59, 0x24, 0x21, 0x26, 0x4a, 0x7e, 0xea,
	0x7f, 0x5f, 0x85, 0xc5, 0xbc, 0xac, 0x94, 0x65, 0x57, 0x94, 0x65, 0x6b, 0x1f, 0x43, 0x87, 0xc0,
	0x39, 0x1b, 0xb9, 0x82, 0xf8, 0x63, 0x3e, 0x34, 0x5a, 0x11, 0x4f, 0xed, 0x48, 0xbb, 0x03, 0x5d,
	0x9b, 0x9f, 0xb0, 0x91, 0x2b, 0x99, 0xa5, 0xd9, 0x62, 0x47, 0x82, 0x77, 0xd3, 0x84, 0x52, 0xbe,
	0x4a, 0xc8, 0x5c, 0x74, 0x53, 0xbc, 0x4b, 0x40, 0x3f, 0xa5, 0x5e, 0x9d, 0xd5, 0x73, 0x57, 0x67,
	0xe8, 0xb5, 0x9d, 0xc8, 0x0c, 0x78, 0x38, 0x64, 0x1e, 0x97, 0xad, 0xf1, 0x86, 0xd1, 0x72, 0xa2,
	0xfd, 0x04, 0xa4, 0x69, 0x30, 0x17, 0x32, 0xef, 0x94, 0x4e, 0x67, 0xd5, 0xa0, 0xdf, 0xc8, 0xd1,
	0x89, 0x4c, 0x4a, 0x95, 0x28, 0x59, 0x6c, 0x18, 0x0b, 0x4e, 0x44, 0x79, 0x94, 0x52, 0x09, 0x35,
	0x95, 0x4a, 0x48, 0xff, 0xaf, 0x4a, 0x4e, 0x7a, 0x31, 0x1f, 0x4e, 0x7e, 0x55, 0xf2, 0x6d, 0x80,
	0x90, 0x85, 0x5c, 0x56, 0x5b, 0x55, 0x3a, 0xaa, 0xf7, 0xae, 0x22, 0x3c, 0x16, 0x8a, 0x96, 0xa9,
	0xd1, 0x0c, 0x93, 0x9f, 0x94, 0x68, 0x45, 0xe6, 0xc8, 0x73, 0x7d, 0xeb, 0x94, 0x0b, 0xf9, 0xd1,
	0x55, 0xc1, 0x91, 0x84, 0x88, 0xe4, 0x24, 0xe6, 0xc9, 0x03, 0x0d, 0x19, 0xb9, 0x05, 0x88, 0x22,
	0x77, 0xb6, 0xa3, 0xba, 0x5a, 0xdb, 0xbd, 0x85, 0x69, 0xa6, 0x75, 0xda, 0x0f, 0xfd, 0x91, 0x67,
	0x53, 0x58, 0x17, 0x1d, 0x8c, 0x4e, 0x06, 0xc5, 0xe0, 0xfe, 0xe7, 0x0b, 0xd0, 0x2b, 0x9a, 0xd5,
	0x24, 0xc3, 0xb9, 0x2e, 0x8e, 0xac, 0x92, 0x7f, 0xe0, 0x69, 0x4c, 0xd2, 0x0f, 0x45, 0xc5, 0xb5,
	0x69, 0x2a, 0x9e, 0x9b, 0xae, 0xe2, 0xfa, 0xb8, 0x8a, 0x0f, 0x60, 0x91, 0x3a, 0x39, 0xc7, 0xa1,
	0xcf, 0x6c, 0x8b, 0x45, 0xb1, 0xbc, 0x45, 0xbe, 0x4c, 0xe8, 0x98, 0x42, 0x6d, 0x27, 0x34, 0x46,
	0x67, 0xa4, 0x7e, 0x6a, 0xbf, 0x08, 0x3d, 0xcb, 0x77, 0x5d, 0x6e, 0x51, 0x49, 0xc7, 0xce, 0x58,
	0x68, 0x4b, 0x0f, 0xbf, 0x79, 0x09, 0xdb, 0x9d, 0x94, 0x6c, 0x0b, 0xa9, 0x8c, 0xae, 0x95, 0x07,
	0xc8, 0xde, 0x91, 0x2b, 0xee, 0xdf, 0xb3, 0xbb, 0x97, 0xb6, 0x04, 0xee, 0x24, 0x05, 0x53, 0xec,
	0xc7, 0xcc, 0x95, 0x28, 0xe2, 0xbe, 0x05, 0x08, 0x24, 0x10, 0xc6, 0x8e, 0x29, 0xbc, 0xe4, 0x31,
	0x7d, 0x03, 0xda, 0xb9, 0xab, 0xe0, 0x16, 0xa9, 0xae, 0xc5, 0x94, 0x8b, 0xe0, 0xdb, 0xd0, 0x49,
	0x51, 0x1c, 0xcb, 0xf7, 0xd6, 0xda, 0xa2, 0xdb, 0x95, 0x00, 0x77, 0x2d, 0xdf, 0x53, 0x7b, 0xb9,
	0x9d, 0x5c, 0x2f, 0xb7, 0xc4, 0x0f, 0x2c, 0x96, 0xf9, 0x81, 0xc7, 0x30, 0x2f, 0xeb, 0x89, 0x2e,
	0x09, 0xfa, 0xfe, 0xd5, 0x9c, 0x9c, 0x7c, 0xf1, 0x64, 0x48, 0x62, 0xed, 0x3b, 0xb0, 0xa4, 0xee,
	0x47, 0x44, 0xcc, 0x1e, 0x1d, 0xc3, 0xcb, 0x54, 0xa7, 0x5c, 0x7e, 0x53, 0xe8, 0xec, 0xb2, 0x3c,
	0x00, 0x03, 0xa8, 0x1a, 0xf9, 0x1d, 0x7b, 0x6d, 0x49, 0xe8, 0x2e, 0x0b, 0xe5, 0xbb, 0xb6, 0xf6,
	0x3e, 0x5c, 0xa7, 0x6a, 0x94, 0xb9, 0xae, 0x9a, 0xf6, 0x89, 0x13, 0xaa, 0x91, 0x6c, 0x56, 0x11,
	0x61, 0xcb, 0x75, 0xb3, 0xc4, 0x80, 0x0e, 0xeb, 0x07, 0xb0, 0x5e, 0x4a, 0x29, 0xd2, 0xf6, 0x65,
	0x9a, 0xeb, 0xda, 0x18, 0x29, 0xe5, 0xf0, 0xfa, 0xbf, 0xd6, 0x60, 0x6d, 0x92, 0x70, 0x4a, 0x8e,
	0x7b, 0xa5, 0xe4, 0xb8, 0xd3, 0x75, 0x78, 0xc8, 0x86, 0xea, 0xb3, 0x82, 0x06, 0x01, 0x70, 0xf0,
	0xa7, 0x40, 0xf3, 0x7c, 0x71, 0x14, 0xcc, 0x0c, 0x4b, 0xd4, 0x12, 0x5d, 0xcf, 0x27, 0xe3, 0x7e,
	0x92, 0x20, 0xbf, 0x03, 0xd7, 0x04, 0x66, 0xd2, 0x9b, 0x43, 0xbb, 0x51, 0x1a, 0xfd, 0xcb, 0x34,
	0x2a, 0xfb, 0x80, 0x68, 0x3f, 0x48, 0xf4, 0x10, 0x56, 0x1d, 0x2f, 0x0e, 0x7d, 0x7b, 0x24, 0x8e,
	0x5d, 0x4a, 0x23, 0x4a, 0x8e, 0x65, 0x75, 0x30, 0xa1, 0xf9, 0x1a, 0xac, 0x88, 0x89, 0x4a, 0xdd,
	0x99, 0x46, 0x63, 0xdb, 0xb9, 0x4d, 0xde, 0x03, 0x4d, 0xdd, 0x44, 0x34, 0x60, 0xb6, 0x7f, 0x46,
	0x87, 0xbb, 0x69, 0xf4, 0x58, 0xba, 0x8b, 0x03, 0x82, 0x63, 0x25, 0x91, 0xe7, 0x6c, 0x7a, 0x8e,
	0x27, 0x9a, 0x0c, 0x4d, 0x63, 0x29, 0x27, 0xbe, 0x67, 0x8e, 0x47, 0x26, 0x92, 0x0a, 0x47, 0xa0,
	0x36, 0xc5, 0x61, 0x49, 0xe4, 0x48, 0x58, 0x89, 0xa2, 0x55, 0x25, 0x67, 0x32, 0x05, 0xa2, 0x20,
	0x45, 0x67, 0x5a, 0x4e, 0x44, 0xab, 0xff, 0xa0, 0x02, 0xaf, 0x4c, 0xf0, 0x62, 0xc9, 0x73, 0xb9,
	0x4a, 0xfa, 0x5c, 0x4e, 0x5b, 0x83, 0x05, 0x66, 0x65, 0xf7, 0x9d, 0x4d, 0x23, 0xf9, 0xd4, 0xd6,
	0xa1, 0xe1, 0x39, 0xd6, 0xa9, 0x97, 0x3c, 0x81, 0x6b, 0x1a, 0xe9, 0x37, 0x7a, 0xe4, 0x01, 0x67,
	0xb6, 0xf2, 0x2c, 0x62, 0x01, 0xbf, 0xf7, 0xec, 0x77, 0xd5, 0xc0, 0x57, 0x57, 0x03, 0x9f, 0xfe,
	0xc7, 0x55, 0xb8, 0x3e, 0xd1, 0x0d, 0x62, 0x08, 0x10, 0x52, 0xa7, 0x53, 0x20, 0xac, 0xaf, 0x49,
	0x10, 0xb2, 0xfc, 0x9f, 0x4f, 0x86, 0x95, 0x94, 0xe3, 0xe1, 0x6c, 0x9e, 0x96, 0x5c, 0x9b, 0x60,
	0x49, 0x8e, 0xed, 0xd3, 0x42, 0x73, 0xb9, 0x46, 0x3e, 0x60, 0xea, 0xcb, 0xa9, 0xfd, 0xac, 0xf1,
	0x3c, 0xb5, 0x0b, 0x3d, 0x77, 0x59, 0x17, 0xba, 0x3e, 0xd6, 0x85, 0xfe, 0xb7, 0x0a, 0xdc, 0x9c,
	0xba, 0x74, 0x4a, 0x4b, 0x12, 0x83, 0x17, 0xf2, 0x59, 0x70, 0xa4, 0x91, 0xa7, 0xd2, 0xa1, 0x8d,
	0x88, 0x9c, 0x62, 0x46, 0xe9, 0x88, 0x5a, 0x80, 0x25, 0x3f, 0x33, 0x7d, 0x28, 0xfa, 0x17, 0xc3,
	0x49, 0xc4, 0x16, 0xc3, 0x36, 0x8f, 0x2c, 0xb9, 0x5f, 0x31, 0xfc, 0x88, 0x47, 0x16, 0x3a, 0x8a,
	0x21, 0x0b, 0x4f, 0xd5, 0xdb, 0x91, 0x06, 0x02, 0xa8, 0x7d, 0xfd, 0x3f, 0xd5, 0xb4, 0xdd, 0x89,
	0x06, 0x4a, 0xa9, 0xca, 0x5e, 0xd4, 0xd7, 0x3e, 0x85, 0x96, 0xc8, 0x64, 0x84, 0x92, 0x2b, 0x53,
	0xdf, 0x62, 0xe5, 0xc9, 0x49, 0xb7, 0x20, 0xa8, 0x49, 0xb9, 0x63, 0x41, 0xb4, 0x7a, 0x79, 0x10,
	0xad, 0x8d, 0x05, 0x51, 0x91, 0x1c, 0x31, 0xd7, 0xcd, 0x1a, 0xdf, 0x5b, 0x2e, 0x8a, 0xbb, 0x8b,
	0x1e, 0x58, 0x5d, 0x6c, 0x7d, 0xd6, 0xc5, 0x76, 0x98, 0xeb, 0x1e, 0x65, 0xeb, 0x3d, 0x84, 0x2e,
	0xe5, 0x27, 0x26, 0x1e, 0x53, 0xc1, 0xf2, 0xa5, 0xb2, 0x14, 0x62, 0xb2, 0xc3, 0x22, 0x51, 0xe5,
	0x64, 0x99, 0xd8, 0x82, 0x5a, 0x79, 0xfc, 0x55, 0xd6, 0x92, 0xc9, 0x2d, 0x6a, 0x52, 0xe6, 0x36,
	0xb1, 0xd7, 0x9b, 0xcf, 0x65, 0x6b, 0xff, 0xef, 0x5c, 0x56, 0x4a, 0x54, 0x4d, 0x55, 0x05, 0x88,
	0x7c, 0x40, 0x21, 0x97, 0xad, 0x8f, 0xe5, 0xb2, 0x57, 0x4b, 0x5a, 0x31, 0xf6, 0xf4, 0x79, 0x4c,
	0x41, 0xb4, 0x80, 0x2e, 0x9c, 0xfc, 0x72, 0x9f, 0xc7, 0x5b, 0xae, 0x9b, 0x8b, 0x0a, 0xfa, 0x3f,
	0x57, 0x60, 0x29, 0x73, 0xb6, 0x07, 0xdc, 0xb3, 0x67, 0x29, 0x01, 0xc7, 0x13, 0x83, 0x6a, 0x49,
	0x62, 0x70, 0x49, 0xeb, 0xbc, 0xd0, 0x24, 0x9f, 0x1b, 0x6b, 0x92, 0xe7, 0xda, 0xdc, 0xf5, 0x42,
	0x9b, 0x7b, 0x15, 0xe6, 0x63, 0xdf, 0x1c, 0xa5, 0xfd, 0xd6, 0x7a, 0xec, 0x1f, 0x39, 0xb6, 0xfe,
	0x4f, 0x55, 0xd0, 0x8a, 0xfb, 0x9a, 0xb5, 0x00, 0x6d, 0x14, 0x7a, 0xd1, 0x97, 0x35, 0x39, 0xa4,
	0x31, 0x4c, 0xe9, 0x28, 0xd7, 0x2e, 0xe9, 0x28, 0xcf, 0x8d, 0x75, 0x94, 0xb3, 0x7e, 0x30, 0x2d,
	0xa4, 0x9e, 0xd5, 0xc1, 0x57, 0xee, 0x07, 0xd3, 0x3a, 0x1e, 0xe5, 0x9a, 0xb5, 0xf3, 0x53, 0xca,
	0x69, 0xb9, 0x9f, 0xd2, 0x8e, 0xed, 0x0f, 0x2a, 0xb0, 0x52, 0xd6, 0xd4, 0xd1, 0x0e, 0x4a, 0x7b,
	0x43, 0x95, 0x29, 0x7f, 0x09, 0x50, 0x64, 0x33, 0xde, 0x17, 0xba, 0x7a, 0x53, 0x4f, 0xff, 0x9b,
	0x06, 0xf4, 0x8a, 0x0c, 0x4b, 0x8c, 0xb3, 0x52, 0x62, 0x9c, 0x1b, 0xd0, 0x53, 0xb1, 0x94, 0x32,
	0x6e, 0x31, 0xc3, 0xa3, 0xd8, 0x50, 0xc0, 0xa4, 0x8a, 0xa0, 0x56, 0xc4, 0xa4, 0x9a, 0xa0, 0x78,
	0x8f, 0x35, 0x77, 0xd9, 0x3d, 0x56, 0xbd, 0xf8, 0x8a, 0x10, 0x5d, 0x53, 0x64, 0x46, 0x03, 0xff,
	0x4c, 0x56, 0xf7, 0xf3, 0x4e, 0x74, 0x30, 0xf0, 0xcf, 0xb0, 0xaa, 0xa0, 0xc7, 0xa9, 0x0a, 0xb1,
	0x68, 0xab, 0x76, 0x10, 0xfc, 0x38, 0x65, 0x90, 0x34, 0x00, 0x1a, 0x4a, 0x03, 0xe0, 0xbb, 0xc9,
	0x1b, 0x5a, 0xea, 0x69, 0xd3, 0x69, 0x6a, 0x92, 0x6f, 0x7b, 0xff, 0x4a, 0x1a, 0x52, 0x00, 0x9f,
	0x8e, 0x86, 0x01, 0x45, 0x56, 0xf1, 0xfa, 0x36, 0xf9, 0x2c, 0x7b, 0xa5, 0x0b, 0x65, 0xaf, 0x74,
	0x31, 0x5b, 0xcc, 0x56, 0x92, 0x95, 0x5f, 0xed, 0x94, 0x9d, 0x4c, 0x2e, 0x44, 0x1c, 0x13, 0x75,
	0x40, 0x5b, 0x89, 0x63, 0xa2, 0x7f, 0x7f, 0x2f, 0xb3, 0x3a, 0x29, 0x8f, 0xac, 0x0c, 0x4b, 0x74,
	0x25, 0x44, 0x22, 0x13, 0xe0, 0x02, 0x36, 0xa6, 0x2c, 0x8b, 0x25, 0xd8, 0x98, 0xef, 0xdd, 0x81,
	0x6e, 0xf6, 0x84, 0x47, 0x44, 0xae, 0x2e, 0x75, 0x0d, 0x3b, 0xe9, 0x3b, 0x1e, 0x8a, 0x45, 0xe8,
	0xde, 0x15, 0xfb, 0xec, 0x89, 0x45, 0x8e, 0xb2, 0x86, 0xb3, 0xb8, 0x4e, 0xb6, 0xb9, 0xcb, 0x63,
	0x4e, 0x75, 0x13, 0x5d, 0x27, 0x3f, 0xa2, 0x6f, 0xf9, 0x26, 0x19, 0x4f, 0xe6, 0x71, 0xc8, 0xd9,
	0xa9, 0xe3, 0xf5, 0xa9, 0x52, 0xa2, 0x37, 0xc9, 0xdb, 0xfe, 0xf9, 0xb6, 0x04, 0x6a, 0xdf, 0x80,
	0xf5, 0x64, 0xed, 0x98, 0x59, 0x39, 0x5e, 0x5f, 0xba, 0x0c, 0xda, 0xf1, 0x32, 0xed, 0x21, 0xe9,
	0xd3, 0x7f, 0x2e, 0x10, 0x84, 0x03, 0xc1, 0x8d, 0x4f, 0x26, 0x46, 0x01, 0xac, 0x4c, 0x26, 0x46,
	0x39, 0x28, 0xfd, 0x7f, 0xd1, 0xa9, 0xce, 0xa6, 0x5d, 0x25, 0xca, 0xa4, 0xff, 0x4f, 0x8d, 0xe9,
	0x74, 0xce, 0x09, 0x64, 0x38, 0xe1, 0xb5, 0x09, 0x64, 0x98, 0x2e, 0x9e, 0xaa, 0x5e, 0x3b, 0x35,
	0xab, 0x75, 0xb8, 0x36, 0x0e, 0x7d, 0xe6, 0x7b, 0xbc, 0xf7, 0x15, 0xed, 0x3a, 0xac, 0x8e, 0x8f,
	0x1d, 0x85, 0x6e, 0xaf, 0xa2, 0xbd, 0x06, 0xeb, 0xe3, 0x43, 0x49, 0xb1, 0xdb, 0xab, 0xea, 0xff,
	0x52, 0x51, 0x69, 0xd5, 0x47, 0x5f, 0xca, 0x9f, 0xba, 0x28, 0x39, 0xdb, 0x95, 0x2e, 0xd0, 0x92,
	0xa7, 0x6d, 0x64, 0x1c, 0x57, 0x8b, 0x8f, 0x9b, 0xb0, 0x5c, 0x30, 0xcc, 0x2f, 0xa2, 0xd4, 0xb7,
	0x2c, 0xe5, 0x2c, 0xf3, 0xd3, 0xc8, 0xf7, 0x10, 0x3f, 0xd1, 0xa3, 0x8a, 0x2f, 0x32, 0x8b, 0x25,
	0x39, 0x94, 0xe1, 0xeb, 0xff, 0x58, 0x57, 0xbd, 0x76, 0x16, 0xa5, 0x7e, 0x6c, 0xdb, 0x4c, 0x03,
	0x74, 0xe2, 0xa1, 0xe7, 0x64, 0x80, 0x76, 0x44, 0x4a, 0x3d, 0x2d, 0xfa, 0x53, 0xfb, 0x0c, 0xf3,
	0x14, 0xc7, 0x4e, 0x9a, 0x60, 0xf8, 0x7d, 0xe4, 0xd0, 0x73, 0x00, 0xd1, 0x16, 0xb0, 0xb2, 0x77,
	0x92, 0x4d, 0x83, 0x92, 0x85, 0x2d, 0x59, 0xcc, 0xdd, 0x86, 0x0e, 0xa1, 0xa4, 0x15, 0x9d, 0x7c,
	0x90, 0x84, 0xc0, 0x67, 0x49, 0x55, 0x77, 0x13, 0x64, 0x24, 0xa4, 0x49, 0x44, 0xca, 0xd8, 0x14,
	0x10, 0x9c, 0xe6, 0x2d, 0x58, 0x94, 0xc3, 0xc9, 0x44, 0xa2, 0xca, 0xed, 0x08, 0x68, 0x32, 0xd5,
	0x57, 0xa1, 0x2b, 0xd1, 0xd2, 0xc9, 0x44, 0x89, 0x2b, 0xa9, 0xd3, 0xe9, 0xb2, 0x7c, 0x8e, 0xd4,
	0x02, 0x6a, 0x3e, 0x47, 0xfa, 0xfb, 0x06, 0x2c, 0x9d, 0x84, 0xfe, 0xd0, 0xa4, 0xf6, 0x5d, 0x20,
	0x6e, 0x33, 0xe5, 0xfd, 0xe9, 0xd8, 0x25, 0x67, 0x17, 0x31, 0x15, 0x80, 0xf6, 0x1e, 0x74, 0x31,
	0xdf, 0x51, 0x49, 0xdb, 0xe5, 0xa4, 0x9d, 0xd8, 0x57, 0x09, 0xc7, 0x6d, 0xb1, 0x73, 0x75, 0x5b,
	0x5c, 0x9c, 0x64, 0x8b, 0x25, 0x0e, 0xac, 0x5b, 0xe6, 0xc0, 0x6e, 0x02, 0xd8, 0xdc, 0x65, 0x17,
	0x99, 0x97, 0x9c, 0x33, 0x9a, 0x04, 0x49, 0x2e, 0x2e, 0xcb, 0x4c, 0x7a, 0x69, 0x92, 0x49, 0x7f,
	0x41, 0x7f, 0x9e, 0x55, 0x8c, 0x50, 0xdb, 0x17, 0xbb, 0x3f, 0xfe, 0xf4, 0x55, 0xff, 0xfd, 0x0a,
	0x3d, 0xc1, 0x2d, 0x9f, 0x6c, 0xb6, 0x9c, 0xf2, 0x49, 0xfe, 0xf2, 0xb1, 0x7a, 0x79, 0x1a, 0x96,
	0xe5, 0x47, 0xca, 0x65, 0xa4, 0xfe, 0xc3, 0x0a, 0x2c, 0x66, 0x79, 0x9a, 0x4c, 0xf0, 0xe8, 0xea,
	0xde, 0x1c, 0x46, 0x7d, 0xb9, 0x88, 0x59, 0xd2, 0x55, 0x24, 0x45, 0x8f, 0x90, 0x4f, 0x13, 0xab,
	0x2f, 0x99, 0x26, 0xfe, 0x77, 0x35, 0x4d, 0xc7, 0xd2, 0x71, 0x4c, 0xd4, 0x91, 0xb5, 0x4c, 0xc3,
	0x9a, 0x46, 0xfd, 0xd8, 0x3f, 0xdf, 0xb5, 0xcb, 0x0f, 0x43, 0xf5, 0xe5, 0x0f, 0x43, 0xed, 0x25,
	0x0f, 0xc3, 0xdc, 0x15, 0x73, 0xc3, 0x7a, 0x69, 0x6e, 0x98, 0xf3, 0x80, 0xf3, 0x05, 0x0f, 0x58,
	0x70, 0x08, 0x0b, 0x63, 0x0e, 0x21, 0x7f, 0x38, 0x1a, 0xc5, 0xc3, 0xf1, 0x00, 0x56, 0x9c, 0xc8,
	0x7c, 0xe1, 0x44, 0xce, 0xb1, 0xcb, 0xcd, 0xd8, 0xa7, 0xc7, 0xfc, 0x3c, 0x94, 0x77, 0x35, 0x4b,
	0x4e, 0xf4, 0xb9, 0x18, 0x3a, 0xf4, 0x0f, 0x68, 0x40, 0x8f, 0x60, 0xf9, 0xc8, 0x0b, 0x98, 0x75,
	0x9a, 0x29, 0x61, 0xc6, 0x8b, 0x3d, 0xa9, 0xa9, 0xaa, 0xaa, 0xa9, 0xe9, 0x8e, 0x5c, 0xff, 0xf5,
	0x0a, 0xac, 0x8c, 0xcf, 0x3a, 0xf3, 0xf5, 0x27, 0xcd, 0x5c, 0x78, 0xff, 0x33, 0xc5, 0xf8, 0xc4,
	0xf5, 0xe7, 0xb1, 0xf8, 0xa1, 0xdf, 0x4d, 0x8b, 0xd9, 0x6d, 0xff, 0xfc, 0xb3, 0x80, 0x7b, 0x68,
	0xd3, 0xe5, 0x86, 0x77, 0x37, 0x7d, 0x47, 0xaa, 0x74, 0xbf, 0xb4, 0x15, 0xe8, 0xe5, 0x80, 0xbb,
	0xc3, 0x7e, 0xef, 0x2b, 0x63, 0xd0, 0xbd, 0xe0, 0xa7, 0x7b, 0x95, 0xbb, 0xbf, 0x55, 0x49, 0x2f,
	0x89, 0xd2, 0x6b, 0x67, 0xed, 0x46, 0xda, 0xa4, 0x4c, 0x61, 0x47, 0xde, 0xa9, 0xe7, 0x9f, 0x79,
	0x22, 0x57, 0x29, 0x0e, 0xd2, 0xdf, 0x8f, 0xf4, 0x2a, 0x65, 0x74, 0x4f, 0xfd, 0x38, 0xe6, 0xe1,
	0x45, 0xaf, 0xaa, 0xbd, 0x9a, 0xb6, 0xb8, 0xd3, 0xc1, 0xe7, 0x4e, 0x34, 0x40, 0x57, 0xd0, 0xab,
	0xdd, 0xfd, 0xeb, 0x5c, 0x63, 0x34, 0xd7, 0x87, 0xd0, 0x74, 0x78, 0x6d, 0xc2, 0x50, 0xb6, 0xaa,
	0xd7, 0xe1, 0xc6, 0xa4, 0x36, 0x06, 0x0b, 0x79, 0xaf, 0xa2, 0xbd, 0x09, 0xb7, 0x26, 0x20, 0x1c,
	0x8c, 0x02, 0x1e, 0x12, 0x56, 0x55, 0xbb, 0x07, 0x1b, 0xd3, 0xb0, 0x1c, 0x3f, 0xcc, 0xb0, 0x6b,
	0x77, 0xbf, 0x5f, 0x51, 0xbb, 0xa6, 0x85, 0x1b, 0x08, 0xed, 0x0d, 0xb5, 0x69, 0x98, 0x0c, 0xe6,
	0x65, 0x99, 0x5b, 0x94, 0x8a, 0xf2, 0x64, 0xe4, 0xba, 0x07, 0x56, 0xc8, 0xb9, 0x57, 0x5c, 0xba,
	0x8a, 0xf5, 0x09, 0x73, 0x4f, 0x24, 0x56, 0xf5, 0xee, 0xef, 0xd5, 0xa6, 0x34, 0x29, 0x69, 0x41,
	0x1b, 0xf0, 0xe6, 0x54, 0x84, 0x6c, 0x5d, 0x97, 0x61, 0xee, 0x33, 0xeb, 0x94, 0xf5, 0x51, 0xac,
	0xf7, 0xe1, 0xed, 0xa9, 0x98, 0x9f, 0x9d, 0x9c, 0x38, 0x96, 0xc3, 0xdc, 0x1d, 0x1e, 0xc6, 0xbd,
	0xaa, 0x76, 0x17, 0xee, 0x4c, 0x45, 0x47, 0x3f, 0xb4, 0xef, 0xb2, 0x98, 0xf7, 0x6a, 0xda, 0xdb,
	0xf0, 0xd6, 0x54, 0xdc, 0x4f, 0x38, 0xb3, 0x9f, 0x73, 0x16, 0xf6, 0xe6, 0xb4, 0xf7, 0xe0, 0x9d,
	0xa9, 0xa8, 0xf2, 0x3d, 0xd6, 0x3e, 0x0f, 0x23, 0xdf, 0x63, 0xae, 0xf3, 0x3d, 0xfa, 0x23, 0xf7,
	0x5e, 0x5d, 0xbb, 0x03, 0xfa, 0x54, 0x42, 0xfa, 0xab, 0xce, 0xde, 0xbc, 0xf6, 0x35, 0xb8, 0x77,
	0x95, 0x09, 0xf0, 0xfc, 0xee, 0xb0, 0xd0, 0xee, 0x2d, 0xdc, 0x1d, 0xa4, 0x7f, 0x54, 0xa9, 0x1e,
	0xcd, 0x75, 0xb8, 0x36, 0x0e, 0xcd, 0x8a, 0x80, 0xf1, 0x31, 0x51, 0x04, 0x64, 0x07, 0x4b, 0x19,
	0xfa, 0xdc, 0xb1, 0xb9, 0xdf, 0xab, 0x6e, 0xef, 0xc2, 0x9a, 0xe5, 0x0f, 0x37, 0x2f, 0x9c, 0x0b,
	0x7f, 0x84, 0x8e, 0x66, 0xe8, 0xdb, 0xdc, 0x15, 0xff, 0x91, 0xc3, 0x77, 0xee, 0xf7, 0x7d, 0x97,
	0x79, 0xfd, 0xcd, 0x77, 0x1f, 0xc6, 0xf1, 0xa6, 0xe5, 0x0f, 0x1f, 0x10, 0xd8, 0xf2, 0xdd, 0x07,
	0x2c, 0x08, 0xc6, 0xfe, 0x4f, 0x89, 0xe3, 0x79, 0x1a, 0x7e, 0xe7, 0xff, 0x02, 0x00, 0x00, 0xff,
	0xff, 0x8f, 0x37, 0x58, 0x99, 0x7f, 0x42, 0x00, 0x00,
}
