// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/channel_wedding_logic/grpc_channel_wedding.proto

package channel_wedding_logic // import "golang.52tt.com/protocol/app/api/channel_wedding_logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import channel_wedding_logic "golang.52tt.com/protocol/app/channel_wedding_logic"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelWeddingLogicClient is the client API for ChannelWeddingLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelWeddingLogicClient interface {
	// 获取婚礼房阶段信息
	GetGetWeddingInfo(ctx context.Context, in *channel_wedding_logic.GetChannelWeddingInfoRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetChannelWeddingInfoResponse, error)
	// 切换婚礼房阶段
	SwitchWeddingStage(ctx context.Context, in *channel_wedding_logic.SwitchWeddingStageRequest, opts ...grpc.CallOption) (*channel_wedding_logic.SwitchWeddingStageResponse, error)
	// 拍婚礼合照
	TakeWeddingGroupPhoto(ctx context.Context, in *channel_wedding_logic.TakeWeddingGroupPhotoRequest, opts ...grpc.CallOption) (*channel_wedding_logic.TakeWeddingGroupPhotoResponse, error)
	// 申请参加/取消申请
	ApplyToJoinChairGame(ctx context.Context, in *channel_wedding_logic.ApplyToJoinChairGameRequest, opts ...grpc.CallOption) (*channel_wedding_logic.ApplyToJoinChairGameResponse, error)
	// 获取报名列表
	GetChairGameApplyList(ctx context.Context, in *channel_wedding_logic.GetChairGameApplyListRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetChairGameApplyListResponse, error)
	// 获取游戏信息（进程、奖励、参与人员）
	GetChairGameInfo(ctx context.Context, in *channel_wedding_logic.GetChairGameInfoRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetChairGameInfoResponse, error)
	// 玩家抢椅子
	GrabChair(ctx context.Context, in *channel_wedding_logic.GrabChairRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GrabChairResponse, error)
	// 开启新一局抢椅子游戏
	StartChairGame(ctx context.Context, in *channel_wedding_logic.StartChairGameRequest, opts ...grpc.CallOption) (*channel_wedding_logic.StartChairGameResponse, error)
	// 进入下一轮
	SetChairGameToNextRound(ctx context.Context, in *channel_wedding_logic.SetChairGameToNextRoundRequest, opts ...grpc.CallOption) (*channel_wedding_logic.SetChairGameToNextRoundResponse, error)
	// 本轮开抢
	StartGrabChair(ctx context.Context, in *channel_wedding_logic.StartGrabChairRequest, opts ...grpc.CallOption) (*channel_wedding_logic.StartGrabChairResponse, error)
	// 获取用户的婚礼姿势组件
	GetUserWeddingPose(ctx context.Context, in *channel_wedding_logic.GetUserWeddingPoseRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetUserWeddingPoseResponse, error)
	// 设置用户使用的婚礼姿势组件
	SetUserInuseWeddingPose(ctx context.Context, in *channel_wedding_logic.SetUserInuseWeddingPoseRequest, opts ...grpc.CallOption) (*channel_wedding_logic.SetUserInuseWeddingPoseResponse, error)
	// 批量获取用户使用中的婚礼姿势组件
	BatchGetUserInuseWeddingPose(ctx context.Context, in *channel_wedding_logic.BatchGetUserInuseWeddingPoseRequest, opts ...grpc.CallOption) (*channel_wedding_logic.BatchGetUserInuseWeddingPoseResponse, error)
	// 获取房间合照麦位位置映射请求
	GetWeddingGroupPhotoSeatMap(ctx context.Context, in *channel_wedding_logic.GetWeddingGroupPhotoSeatMapRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetWeddingGroupPhotoSeatMapResponse, error)
	// 设置用户合照位置
	SetUserWeddingGroupPhotoSeat(ctx context.Context, in *channel_wedding_logic.SetUserWeddingGroupPhotoSeatRequest, opts ...grpc.CallOption) (*channel_wedding_logic.SetUserWeddingGroupPhotoSeatResponse, error)
	// 新人设置抢椅子游戏奖励
	SetChairGameReward(ctx context.Context, in *channel_wedding_logic.SetChairGameRewardRequest, opts ...grpc.CallOption) (*channel_wedding_logic.SetChairGameRewardResponse, error)
	// 批量获取用户服装信息请求
	BatchGetUserWeddingClothes(ctx context.Context, in *channel_wedding_logic.BatchGetUserWeddingClothesRequest, opts ...grpc.CallOption) (*channel_wedding_logic.BatchGetUserWeddingClothesResponse, error)
	// 获取抢椅子游戏奖励设置
	GetChairGameRewardSetting(ctx context.Context, in *channel_wedding_logic.GetChairGameRewardSettingRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetChairGameRewardSettingResponse, error)
	// 设置用户使用的婚礼形象朝向
	SetUserWeddingOrientation(ctx context.Context, in *channel_wedding_logic.SetUserWeddingOrientationRequest, opts ...grpc.CallOption) (*channel_wedding_logic.SetUserWeddingOrientationResponse, error)
	// 获取婚礼页信息
	GetWeddingSchedulePageInfo(ctx context.Context, in *channel_wedding_logic.GetWeddingSchedulePageInfoRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetWeddingSchedulePageInfoResponse, error)
	// 购买婚礼
	BuyWedding(ctx context.Context, in *channel_wedding_logic.BuyWeddingRequest, opts ...grpc.CallOption) (*channel_wedding_logic.BuyWeddingResponse, error)
	// 取消婚礼
	CancelWedding(ctx context.Context, in *channel_wedding_logic.CancelWeddingRequest, opts ...grpc.CallOption) (*channel_wedding_logic.CancelWeddingResponse, error)
	// 获取仪式大厅列表
	GetWeddingHallList(ctx context.Context, in *channel_wedding_logic.GetWeddingHallListRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetWeddingHallListResponse, error)
	// 订阅婚礼（跟预约婚礼用不同叫法）
	SubscribeWedding(ctx context.Context, in *channel_wedding_logic.SubscribeWeddingRequest, opts ...grpc.CallOption) (*channel_wedding_logic.SubscribeWeddingResponse, error)
	// 获取婚礼房入口开关
	GetWeddingEntrySwitch(ctx context.Context, in *channel_wedding_logic.GetWeddingEntrySwitchRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetWeddingEntrySwitchResponse, error)
	// 获取婚礼大屏
	GetWeddingBigScreen(ctx context.Context, in *channel_wedding_logic.GetWeddingBigScreenRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetWeddingBigScreenResponse, error)
	// 保存婚礼大屏
	SaveWeddingBigScreen(ctx context.Context, in *channel_wedding_logic.SaveWeddingBigScreenRequest, opts ...grpc.CallOption) (*channel_wedding_logic.SaveWeddingBigScreenResponse, error)
	// GetWeddingInviteInfo 获取婚礼邀请信息
	GetWeddingInviteInfo(ctx context.Context, in *channel_wedding_logic.GetWeddingInviteInfoRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetWeddingInviteInfoResponse, error)
	// HandleWeddingInvite 处理婚礼邀请
	HandleWeddingInvite(ctx context.Context, in *channel_wedding_logic.HandleWeddingInviteRequest, opts ...grpc.CallOption) (*channel_wedding_logic.HandleWeddingInviteResponse, error)
	// ApplyEndWeddingRelationship 申请结束婚礼关系
	ApplyEndWeddingRelationship(ctx context.Context, in *channel_wedding_logic.ApplyEndWeddingRelationshipRequest, opts ...grpc.CallOption) (*channel_wedding_logic.ApplyEndWeddingRelationshipResponse, error)
	// CancelEndWeddingRelationship 取消申请结束婚礼关系
	CancelEndWeddingRelationship(ctx context.Context, in *channel_wedding_logic.CancelEndWeddingRelationshipRequest, opts ...grpc.CallOption) (*channel_wedding_logic.CancelEndWeddingRelationshipResponse, error)
	// deprecated DirectEndWeddingRelationship 直接结束婚礼关系
	DirectEndWeddingRelationship(ctx context.Context, in *channel_wedding_logic.DirectEndWeddingRelationshipRequest, opts ...grpc.CallOption) (*channel_wedding_logic.DirectEndWeddingRelationshipResponse, error)
	// GetProposeList 获取求婚用户列表
	GetProposeList(ctx context.Context, in *channel_wedding_logic.GetProposeListRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetProposeListResponse, error)
	// SendPropose 发送求婚请求
	SendPropose(ctx context.Context, in *channel_wedding_logic.SendProposeRequest, opts ...grpc.CallOption) (*channel_wedding_logic.SendProposeResponse, error)
	// HandlePropose 处理求婚请求
	HandlePropose(ctx context.Context, in *channel_wedding_logic.HandleProposeRequest, opts ...grpc.CallOption) (*channel_wedding_logic.HandleProposeResponse, error)
	GetProposeById(ctx context.Context, in *channel_wedding_logic.GetProposeByIdRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetProposeByIdResponse, error)
	GetSendPropose(ctx context.Context, in *channel_wedding_logic.GetSendProposeRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetSendProposeResponse, error)
	// 获取用户婚礼沉淀信息
	GetUserWeddingPrecipitation(ctx context.Context, in *channel_wedding_logic.GetUserWeddingPrecipitationRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetUserWeddingPrecipitationResponse, error)
	// 上报婚礼场景片段图片
	ReportWeddingScenePic(ctx context.Context, in *channel_wedding_logic.ReportWeddingScenePicRequest, opts ...grpc.CallOption) (*channel_wedding_logic.ReportWeddingScenePicResponse, error)
	// 手动隐藏婚礼关系
	HideWeddingRelation(ctx context.Context, in *channel_wedding_logic.HideWeddingRelationRequest, opts ...grpc.CallOption) (*channel_wedding_logic.HideWeddingRelationResponse, error)
	// 获取婚礼预览资源
	GetWeddingPreviewResource(ctx context.Context, in *channel_wedding_logic.GetWeddingPreviewResourceRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetWeddingPreviewResourceResponse, error)
	// 获取婚礼高光时刻礼物
	GetWeddingHighLightPresent(ctx context.Context, in *channel_wedding_logic.GetWeddingHighLightPresentRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetWeddingHighLightPresentResponse, error)
	// 获取房间爱侣榜入口
	GetWeddingRankEntry(ctx context.Context, in *channel_wedding_logic.GetWeddingRankEntryRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetWeddingRankEntryResponse, error)
	// 发送付费预约礼物
	SendWeddingReservePresent(ctx context.Context, in *channel_wedding_logic.SendWeddingReservePresentRequest, opts ...grpc.CallOption) (*channel_wedding_logic.SendWeddingReservePresentResponse, error)
	// 获取婚礼主题列表请求
	GetWeddingThemeCfgList(ctx context.Context, in *channel_wedding_logic.GetWeddingThemeCfgListRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetWeddingThemeCfgListResponse, error)
	// 撤回求婚
	RevokePropose(ctx context.Context, in *channel_wedding_logic.RevokeProposeRequest, opts ...grpc.CallOption) (*channel_wedding_logic.RevokeProposeResponse, error)
	// 批量查询用户的在房状态
	GetUserInRoomStatus(ctx context.Context, in *channel_wedding_logic.GetUserInRoomStatusRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetUserInRoomStatusResponse, error)
}

type channelWeddingLogicClient struct {
	cc *grpc.ClientConn
}

func NewChannelWeddingLogicClient(cc *grpc.ClientConn) ChannelWeddingLogicClient {
	return &channelWeddingLogicClient{cc}
}

func (c *channelWeddingLogicClient) GetGetWeddingInfo(ctx context.Context, in *channel_wedding_logic.GetChannelWeddingInfoRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetChannelWeddingInfoResponse, error) {
	out := new(channel_wedding_logic.GetChannelWeddingInfoResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetGetWeddingInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) SwitchWeddingStage(ctx context.Context, in *channel_wedding_logic.SwitchWeddingStageRequest, opts ...grpc.CallOption) (*channel_wedding_logic.SwitchWeddingStageResponse, error) {
	out := new(channel_wedding_logic.SwitchWeddingStageResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/SwitchWeddingStage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) TakeWeddingGroupPhoto(ctx context.Context, in *channel_wedding_logic.TakeWeddingGroupPhotoRequest, opts ...grpc.CallOption) (*channel_wedding_logic.TakeWeddingGroupPhotoResponse, error) {
	out := new(channel_wedding_logic.TakeWeddingGroupPhotoResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/TakeWeddingGroupPhoto", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) ApplyToJoinChairGame(ctx context.Context, in *channel_wedding_logic.ApplyToJoinChairGameRequest, opts ...grpc.CallOption) (*channel_wedding_logic.ApplyToJoinChairGameResponse, error) {
	out := new(channel_wedding_logic.ApplyToJoinChairGameResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/ApplyToJoinChairGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) GetChairGameApplyList(ctx context.Context, in *channel_wedding_logic.GetChairGameApplyListRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetChairGameApplyListResponse, error) {
	out := new(channel_wedding_logic.GetChairGameApplyListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetChairGameApplyList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) GetChairGameInfo(ctx context.Context, in *channel_wedding_logic.GetChairGameInfoRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetChairGameInfoResponse, error) {
	out := new(channel_wedding_logic.GetChairGameInfoResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetChairGameInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) GrabChair(ctx context.Context, in *channel_wedding_logic.GrabChairRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GrabChairResponse, error) {
	out := new(channel_wedding_logic.GrabChairResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GrabChair", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) StartChairGame(ctx context.Context, in *channel_wedding_logic.StartChairGameRequest, opts ...grpc.CallOption) (*channel_wedding_logic.StartChairGameResponse, error) {
	out := new(channel_wedding_logic.StartChairGameResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/StartChairGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) SetChairGameToNextRound(ctx context.Context, in *channel_wedding_logic.SetChairGameToNextRoundRequest, opts ...grpc.CallOption) (*channel_wedding_logic.SetChairGameToNextRoundResponse, error) {
	out := new(channel_wedding_logic.SetChairGameToNextRoundResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/SetChairGameToNextRound", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) StartGrabChair(ctx context.Context, in *channel_wedding_logic.StartGrabChairRequest, opts ...grpc.CallOption) (*channel_wedding_logic.StartGrabChairResponse, error) {
	out := new(channel_wedding_logic.StartGrabChairResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/StartGrabChair", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) GetUserWeddingPose(ctx context.Context, in *channel_wedding_logic.GetUserWeddingPoseRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetUserWeddingPoseResponse, error) {
	out := new(channel_wedding_logic.GetUserWeddingPoseResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetUserWeddingPose", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) SetUserInuseWeddingPose(ctx context.Context, in *channel_wedding_logic.SetUserInuseWeddingPoseRequest, opts ...grpc.CallOption) (*channel_wedding_logic.SetUserInuseWeddingPoseResponse, error) {
	out := new(channel_wedding_logic.SetUserInuseWeddingPoseResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/SetUserInuseWeddingPose", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) BatchGetUserInuseWeddingPose(ctx context.Context, in *channel_wedding_logic.BatchGetUserInuseWeddingPoseRequest, opts ...grpc.CallOption) (*channel_wedding_logic.BatchGetUserInuseWeddingPoseResponse, error) {
	out := new(channel_wedding_logic.BatchGetUserInuseWeddingPoseResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/BatchGetUserInuseWeddingPose", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) GetWeddingGroupPhotoSeatMap(ctx context.Context, in *channel_wedding_logic.GetWeddingGroupPhotoSeatMapRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetWeddingGroupPhotoSeatMapResponse, error) {
	out := new(channel_wedding_logic.GetWeddingGroupPhotoSeatMapResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingGroupPhotoSeatMap", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) SetUserWeddingGroupPhotoSeat(ctx context.Context, in *channel_wedding_logic.SetUserWeddingGroupPhotoSeatRequest, opts ...grpc.CallOption) (*channel_wedding_logic.SetUserWeddingGroupPhotoSeatResponse, error) {
	out := new(channel_wedding_logic.SetUserWeddingGroupPhotoSeatResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/SetUserWeddingGroupPhotoSeat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) SetChairGameReward(ctx context.Context, in *channel_wedding_logic.SetChairGameRewardRequest, opts ...grpc.CallOption) (*channel_wedding_logic.SetChairGameRewardResponse, error) {
	out := new(channel_wedding_logic.SetChairGameRewardResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/SetChairGameReward", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) BatchGetUserWeddingClothes(ctx context.Context, in *channel_wedding_logic.BatchGetUserWeddingClothesRequest, opts ...grpc.CallOption) (*channel_wedding_logic.BatchGetUserWeddingClothesResponse, error) {
	out := new(channel_wedding_logic.BatchGetUserWeddingClothesResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/BatchGetUserWeddingClothes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) GetChairGameRewardSetting(ctx context.Context, in *channel_wedding_logic.GetChairGameRewardSettingRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetChairGameRewardSettingResponse, error) {
	out := new(channel_wedding_logic.GetChairGameRewardSettingResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetChairGameRewardSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) SetUserWeddingOrientation(ctx context.Context, in *channel_wedding_logic.SetUserWeddingOrientationRequest, opts ...grpc.CallOption) (*channel_wedding_logic.SetUserWeddingOrientationResponse, error) {
	out := new(channel_wedding_logic.SetUserWeddingOrientationResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/SetUserWeddingOrientation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) GetWeddingSchedulePageInfo(ctx context.Context, in *channel_wedding_logic.GetWeddingSchedulePageInfoRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetWeddingSchedulePageInfoResponse, error) {
	out := new(channel_wedding_logic.GetWeddingSchedulePageInfoResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingSchedulePageInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) BuyWedding(ctx context.Context, in *channel_wedding_logic.BuyWeddingRequest, opts ...grpc.CallOption) (*channel_wedding_logic.BuyWeddingResponse, error) {
	out := new(channel_wedding_logic.BuyWeddingResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/BuyWedding", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) CancelWedding(ctx context.Context, in *channel_wedding_logic.CancelWeddingRequest, opts ...grpc.CallOption) (*channel_wedding_logic.CancelWeddingResponse, error) {
	out := new(channel_wedding_logic.CancelWeddingResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/CancelWedding", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) GetWeddingHallList(ctx context.Context, in *channel_wedding_logic.GetWeddingHallListRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetWeddingHallListResponse, error) {
	out := new(channel_wedding_logic.GetWeddingHallListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingHallList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) SubscribeWedding(ctx context.Context, in *channel_wedding_logic.SubscribeWeddingRequest, opts ...grpc.CallOption) (*channel_wedding_logic.SubscribeWeddingResponse, error) {
	out := new(channel_wedding_logic.SubscribeWeddingResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/SubscribeWedding", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) GetWeddingEntrySwitch(ctx context.Context, in *channel_wedding_logic.GetWeddingEntrySwitchRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetWeddingEntrySwitchResponse, error) {
	out := new(channel_wedding_logic.GetWeddingEntrySwitchResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingEntrySwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) GetWeddingBigScreen(ctx context.Context, in *channel_wedding_logic.GetWeddingBigScreenRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetWeddingBigScreenResponse, error) {
	out := new(channel_wedding_logic.GetWeddingBigScreenResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingBigScreen", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) SaveWeddingBigScreen(ctx context.Context, in *channel_wedding_logic.SaveWeddingBigScreenRequest, opts ...grpc.CallOption) (*channel_wedding_logic.SaveWeddingBigScreenResponse, error) {
	out := new(channel_wedding_logic.SaveWeddingBigScreenResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/SaveWeddingBigScreen", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) GetWeddingInviteInfo(ctx context.Context, in *channel_wedding_logic.GetWeddingInviteInfoRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetWeddingInviteInfoResponse, error) {
	out := new(channel_wedding_logic.GetWeddingInviteInfoResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingInviteInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) HandleWeddingInvite(ctx context.Context, in *channel_wedding_logic.HandleWeddingInviteRequest, opts ...grpc.CallOption) (*channel_wedding_logic.HandleWeddingInviteResponse, error) {
	out := new(channel_wedding_logic.HandleWeddingInviteResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/HandleWeddingInvite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) ApplyEndWeddingRelationship(ctx context.Context, in *channel_wedding_logic.ApplyEndWeddingRelationshipRequest, opts ...grpc.CallOption) (*channel_wedding_logic.ApplyEndWeddingRelationshipResponse, error) {
	out := new(channel_wedding_logic.ApplyEndWeddingRelationshipResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/ApplyEndWeddingRelationship", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) CancelEndWeddingRelationship(ctx context.Context, in *channel_wedding_logic.CancelEndWeddingRelationshipRequest, opts ...grpc.CallOption) (*channel_wedding_logic.CancelEndWeddingRelationshipResponse, error) {
	out := new(channel_wedding_logic.CancelEndWeddingRelationshipResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/CancelEndWeddingRelationship", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) DirectEndWeddingRelationship(ctx context.Context, in *channel_wedding_logic.DirectEndWeddingRelationshipRequest, opts ...grpc.CallOption) (*channel_wedding_logic.DirectEndWeddingRelationshipResponse, error) {
	out := new(channel_wedding_logic.DirectEndWeddingRelationshipResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/DirectEndWeddingRelationship", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) GetProposeList(ctx context.Context, in *channel_wedding_logic.GetProposeListRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetProposeListResponse, error) {
	out := new(channel_wedding_logic.GetProposeListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetProposeList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) SendPropose(ctx context.Context, in *channel_wedding_logic.SendProposeRequest, opts ...grpc.CallOption) (*channel_wedding_logic.SendProposeResponse, error) {
	out := new(channel_wedding_logic.SendProposeResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/SendPropose", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) HandlePropose(ctx context.Context, in *channel_wedding_logic.HandleProposeRequest, opts ...grpc.CallOption) (*channel_wedding_logic.HandleProposeResponse, error) {
	out := new(channel_wedding_logic.HandleProposeResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/HandlePropose", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) GetProposeById(ctx context.Context, in *channel_wedding_logic.GetProposeByIdRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetProposeByIdResponse, error) {
	out := new(channel_wedding_logic.GetProposeByIdResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetProposeById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) GetSendPropose(ctx context.Context, in *channel_wedding_logic.GetSendProposeRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetSendProposeResponse, error) {
	out := new(channel_wedding_logic.GetSendProposeResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetSendPropose", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) GetUserWeddingPrecipitation(ctx context.Context, in *channel_wedding_logic.GetUserWeddingPrecipitationRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetUserWeddingPrecipitationResponse, error) {
	out := new(channel_wedding_logic.GetUserWeddingPrecipitationResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetUserWeddingPrecipitation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) ReportWeddingScenePic(ctx context.Context, in *channel_wedding_logic.ReportWeddingScenePicRequest, opts ...grpc.CallOption) (*channel_wedding_logic.ReportWeddingScenePicResponse, error) {
	out := new(channel_wedding_logic.ReportWeddingScenePicResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/ReportWeddingScenePic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) HideWeddingRelation(ctx context.Context, in *channel_wedding_logic.HideWeddingRelationRequest, opts ...grpc.CallOption) (*channel_wedding_logic.HideWeddingRelationResponse, error) {
	out := new(channel_wedding_logic.HideWeddingRelationResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/HideWeddingRelation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) GetWeddingPreviewResource(ctx context.Context, in *channel_wedding_logic.GetWeddingPreviewResourceRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetWeddingPreviewResourceResponse, error) {
	out := new(channel_wedding_logic.GetWeddingPreviewResourceResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingPreviewResource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) GetWeddingHighLightPresent(ctx context.Context, in *channel_wedding_logic.GetWeddingHighLightPresentRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetWeddingHighLightPresentResponse, error) {
	out := new(channel_wedding_logic.GetWeddingHighLightPresentResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingHighLightPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) GetWeddingRankEntry(ctx context.Context, in *channel_wedding_logic.GetWeddingRankEntryRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetWeddingRankEntryResponse, error) {
	out := new(channel_wedding_logic.GetWeddingRankEntryResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingRankEntry", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) SendWeddingReservePresent(ctx context.Context, in *channel_wedding_logic.SendWeddingReservePresentRequest, opts ...grpc.CallOption) (*channel_wedding_logic.SendWeddingReservePresentResponse, error) {
	out := new(channel_wedding_logic.SendWeddingReservePresentResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/SendWeddingReservePresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) GetWeddingThemeCfgList(ctx context.Context, in *channel_wedding_logic.GetWeddingThemeCfgListRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetWeddingThemeCfgListResponse, error) {
	out := new(channel_wedding_logic.GetWeddingThemeCfgListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingThemeCfgList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) RevokePropose(ctx context.Context, in *channel_wedding_logic.RevokeProposeRequest, opts ...grpc.CallOption) (*channel_wedding_logic.RevokeProposeResponse, error) {
	out := new(channel_wedding_logic.RevokeProposeResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/RevokePropose", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingLogicClient) GetUserInRoomStatus(ctx context.Context, in *channel_wedding_logic.GetUserInRoomStatusRequest, opts ...grpc.CallOption) (*channel_wedding_logic.GetUserInRoomStatusResponse, error) {
	out := new(channel_wedding_logic.GetUserInRoomStatusResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetUserInRoomStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelWeddingLogicServer is the server API for ChannelWeddingLogic service.
type ChannelWeddingLogicServer interface {
	// 获取婚礼房阶段信息
	GetGetWeddingInfo(context.Context, *channel_wedding_logic.GetChannelWeddingInfoRequest) (*channel_wedding_logic.GetChannelWeddingInfoResponse, error)
	// 切换婚礼房阶段
	SwitchWeddingStage(context.Context, *channel_wedding_logic.SwitchWeddingStageRequest) (*channel_wedding_logic.SwitchWeddingStageResponse, error)
	// 拍婚礼合照
	TakeWeddingGroupPhoto(context.Context, *channel_wedding_logic.TakeWeddingGroupPhotoRequest) (*channel_wedding_logic.TakeWeddingGroupPhotoResponse, error)
	// 申请参加/取消申请
	ApplyToJoinChairGame(context.Context, *channel_wedding_logic.ApplyToJoinChairGameRequest) (*channel_wedding_logic.ApplyToJoinChairGameResponse, error)
	// 获取报名列表
	GetChairGameApplyList(context.Context, *channel_wedding_logic.GetChairGameApplyListRequest) (*channel_wedding_logic.GetChairGameApplyListResponse, error)
	// 获取游戏信息（进程、奖励、参与人员）
	GetChairGameInfo(context.Context, *channel_wedding_logic.GetChairGameInfoRequest) (*channel_wedding_logic.GetChairGameInfoResponse, error)
	// 玩家抢椅子
	GrabChair(context.Context, *channel_wedding_logic.GrabChairRequest) (*channel_wedding_logic.GrabChairResponse, error)
	// 开启新一局抢椅子游戏
	StartChairGame(context.Context, *channel_wedding_logic.StartChairGameRequest) (*channel_wedding_logic.StartChairGameResponse, error)
	// 进入下一轮
	SetChairGameToNextRound(context.Context, *channel_wedding_logic.SetChairGameToNextRoundRequest) (*channel_wedding_logic.SetChairGameToNextRoundResponse, error)
	// 本轮开抢
	StartGrabChair(context.Context, *channel_wedding_logic.StartGrabChairRequest) (*channel_wedding_logic.StartGrabChairResponse, error)
	// 获取用户的婚礼姿势组件
	GetUserWeddingPose(context.Context, *channel_wedding_logic.GetUserWeddingPoseRequest) (*channel_wedding_logic.GetUserWeddingPoseResponse, error)
	// 设置用户使用的婚礼姿势组件
	SetUserInuseWeddingPose(context.Context, *channel_wedding_logic.SetUserInuseWeddingPoseRequest) (*channel_wedding_logic.SetUserInuseWeddingPoseResponse, error)
	// 批量获取用户使用中的婚礼姿势组件
	BatchGetUserInuseWeddingPose(context.Context, *channel_wedding_logic.BatchGetUserInuseWeddingPoseRequest) (*channel_wedding_logic.BatchGetUserInuseWeddingPoseResponse, error)
	// 获取房间合照麦位位置映射请求
	GetWeddingGroupPhotoSeatMap(context.Context, *channel_wedding_logic.GetWeddingGroupPhotoSeatMapRequest) (*channel_wedding_logic.GetWeddingGroupPhotoSeatMapResponse, error)
	// 设置用户合照位置
	SetUserWeddingGroupPhotoSeat(context.Context, *channel_wedding_logic.SetUserWeddingGroupPhotoSeatRequest) (*channel_wedding_logic.SetUserWeddingGroupPhotoSeatResponse, error)
	// 新人设置抢椅子游戏奖励
	SetChairGameReward(context.Context, *channel_wedding_logic.SetChairGameRewardRequest) (*channel_wedding_logic.SetChairGameRewardResponse, error)
	// 批量获取用户服装信息请求
	BatchGetUserWeddingClothes(context.Context, *channel_wedding_logic.BatchGetUserWeddingClothesRequest) (*channel_wedding_logic.BatchGetUserWeddingClothesResponse, error)
	// 获取抢椅子游戏奖励设置
	GetChairGameRewardSetting(context.Context, *channel_wedding_logic.GetChairGameRewardSettingRequest) (*channel_wedding_logic.GetChairGameRewardSettingResponse, error)
	// 设置用户使用的婚礼形象朝向
	SetUserWeddingOrientation(context.Context, *channel_wedding_logic.SetUserWeddingOrientationRequest) (*channel_wedding_logic.SetUserWeddingOrientationResponse, error)
	// 获取婚礼页信息
	GetWeddingSchedulePageInfo(context.Context, *channel_wedding_logic.GetWeddingSchedulePageInfoRequest) (*channel_wedding_logic.GetWeddingSchedulePageInfoResponse, error)
	// 购买婚礼
	BuyWedding(context.Context, *channel_wedding_logic.BuyWeddingRequest) (*channel_wedding_logic.BuyWeddingResponse, error)
	// 取消婚礼
	CancelWedding(context.Context, *channel_wedding_logic.CancelWeddingRequest) (*channel_wedding_logic.CancelWeddingResponse, error)
	// 获取仪式大厅列表
	GetWeddingHallList(context.Context, *channel_wedding_logic.GetWeddingHallListRequest) (*channel_wedding_logic.GetWeddingHallListResponse, error)
	// 订阅婚礼（跟预约婚礼用不同叫法）
	SubscribeWedding(context.Context, *channel_wedding_logic.SubscribeWeddingRequest) (*channel_wedding_logic.SubscribeWeddingResponse, error)
	// 获取婚礼房入口开关
	GetWeddingEntrySwitch(context.Context, *channel_wedding_logic.GetWeddingEntrySwitchRequest) (*channel_wedding_logic.GetWeddingEntrySwitchResponse, error)
	// 获取婚礼大屏
	GetWeddingBigScreen(context.Context, *channel_wedding_logic.GetWeddingBigScreenRequest) (*channel_wedding_logic.GetWeddingBigScreenResponse, error)
	// 保存婚礼大屏
	SaveWeddingBigScreen(context.Context, *channel_wedding_logic.SaveWeddingBigScreenRequest) (*channel_wedding_logic.SaveWeddingBigScreenResponse, error)
	// GetWeddingInviteInfo 获取婚礼邀请信息
	GetWeddingInviteInfo(context.Context, *channel_wedding_logic.GetWeddingInviteInfoRequest) (*channel_wedding_logic.GetWeddingInviteInfoResponse, error)
	// HandleWeddingInvite 处理婚礼邀请
	HandleWeddingInvite(context.Context, *channel_wedding_logic.HandleWeddingInviteRequest) (*channel_wedding_logic.HandleWeddingInviteResponse, error)
	// ApplyEndWeddingRelationship 申请结束婚礼关系
	ApplyEndWeddingRelationship(context.Context, *channel_wedding_logic.ApplyEndWeddingRelationshipRequest) (*channel_wedding_logic.ApplyEndWeddingRelationshipResponse, error)
	// CancelEndWeddingRelationship 取消申请结束婚礼关系
	CancelEndWeddingRelationship(context.Context, *channel_wedding_logic.CancelEndWeddingRelationshipRequest) (*channel_wedding_logic.CancelEndWeddingRelationshipResponse, error)
	// deprecated DirectEndWeddingRelationship 直接结束婚礼关系
	DirectEndWeddingRelationship(context.Context, *channel_wedding_logic.DirectEndWeddingRelationshipRequest) (*channel_wedding_logic.DirectEndWeddingRelationshipResponse, error)
	// GetProposeList 获取求婚用户列表
	GetProposeList(context.Context, *channel_wedding_logic.GetProposeListRequest) (*channel_wedding_logic.GetProposeListResponse, error)
	// SendPropose 发送求婚请求
	SendPropose(context.Context, *channel_wedding_logic.SendProposeRequest) (*channel_wedding_logic.SendProposeResponse, error)
	// HandlePropose 处理求婚请求
	HandlePropose(context.Context, *channel_wedding_logic.HandleProposeRequest) (*channel_wedding_logic.HandleProposeResponse, error)
	GetProposeById(context.Context, *channel_wedding_logic.GetProposeByIdRequest) (*channel_wedding_logic.GetProposeByIdResponse, error)
	GetSendPropose(context.Context, *channel_wedding_logic.GetSendProposeRequest) (*channel_wedding_logic.GetSendProposeResponse, error)
	// 获取用户婚礼沉淀信息
	GetUserWeddingPrecipitation(context.Context, *channel_wedding_logic.GetUserWeddingPrecipitationRequest) (*channel_wedding_logic.GetUserWeddingPrecipitationResponse, error)
	// 上报婚礼场景片段图片
	ReportWeddingScenePic(context.Context, *channel_wedding_logic.ReportWeddingScenePicRequest) (*channel_wedding_logic.ReportWeddingScenePicResponse, error)
	// 手动隐藏婚礼关系
	HideWeddingRelation(context.Context, *channel_wedding_logic.HideWeddingRelationRequest) (*channel_wedding_logic.HideWeddingRelationResponse, error)
	// 获取婚礼预览资源
	GetWeddingPreviewResource(context.Context, *channel_wedding_logic.GetWeddingPreviewResourceRequest) (*channel_wedding_logic.GetWeddingPreviewResourceResponse, error)
	// 获取婚礼高光时刻礼物
	GetWeddingHighLightPresent(context.Context, *channel_wedding_logic.GetWeddingHighLightPresentRequest) (*channel_wedding_logic.GetWeddingHighLightPresentResponse, error)
	// 获取房间爱侣榜入口
	GetWeddingRankEntry(context.Context, *channel_wedding_logic.GetWeddingRankEntryRequest) (*channel_wedding_logic.GetWeddingRankEntryResponse, error)
	// 发送付费预约礼物
	SendWeddingReservePresent(context.Context, *channel_wedding_logic.SendWeddingReservePresentRequest) (*channel_wedding_logic.SendWeddingReservePresentResponse, error)
	// 获取婚礼主题列表请求
	GetWeddingThemeCfgList(context.Context, *channel_wedding_logic.GetWeddingThemeCfgListRequest) (*channel_wedding_logic.GetWeddingThemeCfgListResponse, error)
	// 撤回求婚
	RevokePropose(context.Context, *channel_wedding_logic.RevokeProposeRequest) (*channel_wedding_logic.RevokeProposeResponse, error)
	// 批量查询用户的在房状态
	GetUserInRoomStatus(context.Context, *channel_wedding_logic.GetUserInRoomStatusRequest) (*channel_wedding_logic.GetUserInRoomStatusResponse, error)
}

func RegisterChannelWeddingLogicServer(s *grpc.Server, srv ChannelWeddingLogicServer) {
	s.RegisterService(&_ChannelWeddingLogic_serviceDesc, srv)
}

func _ChannelWeddingLogic_GetGetWeddingInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.GetChannelWeddingInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).GetGetWeddingInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetGetWeddingInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).GetGetWeddingInfo(ctx, req.(*channel_wedding_logic.GetChannelWeddingInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_SwitchWeddingStage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.SwitchWeddingStageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).SwitchWeddingStage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/SwitchWeddingStage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).SwitchWeddingStage(ctx, req.(*channel_wedding_logic.SwitchWeddingStageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_TakeWeddingGroupPhoto_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.TakeWeddingGroupPhotoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).TakeWeddingGroupPhoto(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/TakeWeddingGroupPhoto",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).TakeWeddingGroupPhoto(ctx, req.(*channel_wedding_logic.TakeWeddingGroupPhotoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_ApplyToJoinChairGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.ApplyToJoinChairGameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).ApplyToJoinChairGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/ApplyToJoinChairGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).ApplyToJoinChairGame(ctx, req.(*channel_wedding_logic.ApplyToJoinChairGameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_GetChairGameApplyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.GetChairGameApplyListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).GetChairGameApplyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetChairGameApplyList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).GetChairGameApplyList(ctx, req.(*channel_wedding_logic.GetChairGameApplyListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_GetChairGameInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.GetChairGameInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).GetChairGameInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetChairGameInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).GetChairGameInfo(ctx, req.(*channel_wedding_logic.GetChairGameInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_GrabChair_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.GrabChairRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).GrabChair(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GrabChair",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).GrabChair(ctx, req.(*channel_wedding_logic.GrabChairRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_StartChairGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.StartChairGameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).StartChairGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/StartChairGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).StartChairGame(ctx, req.(*channel_wedding_logic.StartChairGameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_SetChairGameToNextRound_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.SetChairGameToNextRoundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).SetChairGameToNextRound(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/SetChairGameToNextRound",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).SetChairGameToNextRound(ctx, req.(*channel_wedding_logic.SetChairGameToNextRoundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_StartGrabChair_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.StartGrabChairRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).StartGrabChair(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/StartGrabChair",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).StartGrabChair(ctx, req.(*channel_wedding_logic.StartGrabChairRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_GetUserWeddingPose_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.GetUserWeddingPoseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).GetUserWeddingPose(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetUserWeddingPose",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).GetUserWeddingPose(ctx, req.(*channel_wedding_logic.GetUserWeddingPoseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_SetUserInuseWeddingPose_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.SetUserInuseWeddingPoseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).SetUserInuseWeddingPose(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/SetUserInuseWeddingPose",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).SetUserInuseWeddingPose(ctx, req.(*channel_wedding_logic.SetUserInuseWeddingPoseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_BatchGetUserInuseWeddingPose_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.BatchGetUserInuseWeddingPoseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).BatchGetUserInuseWeddingPose(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/BatchGetUserInuseWeddingPose",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).BatchGetUserInuseWeddingPose(ctx, req.(*channel_wedding_logic.BatchGetUserInuseWeddingPoseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_GetWeddingGroupPhotoSeatMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.GetWeddingGroupPhotoSeatMapRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).GetWeddingGroupPhotoSeatMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingGroupPhotoSeatMap",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).GetWeddingGroupPhotoSeatMap(ctx, req.(*channel_wedding_logic.GetWeddingGroupPhotoSeatMapRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_SetUserWeddingGroupPhotoSeat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.SetUserWeddingGroupPhotoSeatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).SetUserWeddingGroupPhotoSeat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/SetUserWeddingGroupPhotoSeat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).SetUserWeddingGroupPhotoSeat(ctx, req.(*channel_wedding_logic.SetUserWeddingGroupPhotoSeatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_SetChairGameReward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.SetChairGameRewardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).SetChairGameReward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/SetChairGameReward",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).SetChairGameReward(ctx, req.(*channel_wedding_logic.SetChairGameRewardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_BatchGetUserWeddingClothes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.BatchGetUserWeddingClothesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).BatchGetUserWeddingClothes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/BatchGetUserWeddingClothes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).BatchGetUserWeddingClothes(ctx, req.(*channel_wedding_logic.BatchGetUserWeddingClothesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_GetChairGameRewardSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.GetChairGameRewardSettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).GetChairGameRewardSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetChairGameRewardSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).GetChairGameRewardSetting(ctx, req.(*channel_wedding_logic.GetChairGameRewardSettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_SetUserWeddingOrientation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.SetUserWeddingOrientationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).SetUserWeddingOrientation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/SetUserWeddingOrientation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).SetUserWeddingOrientation(ctx, req.(*channel_wedding_logic.SetUserWeddingOrientationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_GetWeddingSchedulePageInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.GetWeddingSchedulePageInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).GetWeddingSchedulePageInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingSchedulePageInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).GetWeddingSchedulePageInfo(ctx, req.(*channel_wedding_logic.GetWeddingSchedulePageInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_BuyWedding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.BuyWeddingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).BuyWedding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/BuyWedding",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).BuyWedding(ctx, req.(*channel_wedding_logic.BuyWeddingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_CancelWedding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.CancelWeddingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).CancelWedding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/CancelWedding",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).CancelWedding(ctx, req.(*channel_wedding_logic.CancelWeddingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_GetWeddingHallList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.GetWeddingHallListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).GetWeddingHallList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingHallList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).GetWeddingHallList(ctx, req.(*channel_wedding_logic.GetWeddingHallListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_SubscribeWedding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.SubscribeWeddingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).SubscribeWedding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/SubscribeWedding",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).SubscribeWedding(ctx, req.(*channel_wedding_logic.SubscribeWeddingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_GetWeddingEntrySwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.GetWeddingEntrySwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).GetWeddingEntrySwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingEntrySwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).GetWeddingEntrySwitch(ctx, req.(*channel_wedding_logic.GetWeddingEntrySwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_GetWeddingBigScreen_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.GetWeddingBigScreenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).GetWeddingBigScreen(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingBigScreen",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).GetWeddingBigScreen(ctx, req.(*channel_wedding_logic.GetWeddingBigScreenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_SaveWeddingBigScreen_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.SaveWeddingBigScreenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).SaveWeddingBigScreen(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/SaveWeddingBigScreen",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).SaveWeddingBigScreen(ctx, req.(*channel_wedding_logic.SaveWeddingBigScreenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_GetWeddingInviteInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.GetWeddingInviteInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).GetWeddingInviteInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingInviteInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).GetWeddingInviteInfo(ctx, req.(*channel_wedding_logic.GetWeddingInviteInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_HandleWeddingInvite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.HandleWeddingInviteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).HandleWeddingInvite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/HandleWeddingInvite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).HandleWeddingInvite(ctx, req.(*channel_wedding_logic.HandleWeddingInviteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_ApplyEndWeddingRelationship_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.ApplyEndWeddingRelationshipRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).ApplyEndWeddingRelationship(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/ApplyEndWeddingRelationship",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).ApplyEndWeddingRelationship(ctx, req.(*channel_wedding_logic.ApplyEndWeddingRelationshipRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_CancelEndWeddingRelationship_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.CancelEndWeddingRelationshipRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).CancelEndWeddingRelationship(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/CancelEndWeddingRelationship",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).CancelEndWeddingRelationship(ctx, req.(*channel_wedding_logic.CancelEndWeddingRelationshipRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_DirectEndWeddingRelationship_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.DirectEndWeddingRelationshipRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).DirectEndWeddingRelationship(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/DirectEndWeddingRelationship",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).DirectEndWeddingRelationship(ctx, req.(*channel_wedding_logic.DirectEndWeddingRelationshipRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_GetProposeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.GetProposeListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).GetProposeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetProposeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).GetProposeList(ctx, req.(*channel_wedding_logic.GetProposeListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_SendPropose_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.SendProposeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).SendPropose(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/SendPropose",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).SendPropose(ctx, req.(*channel_wedding_logic.SendProposeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_HandlePropose_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.HandleProposeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).HandlePropose(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/HandlePropose",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).HandlePropose(ctx, req.(*channel_wedding_logic.HandleProposeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_GetProposeById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.GetProposeByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).GetProposeById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetProposeById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).GetProposeById(ctx, req.(*channel_wedding_logic.GetProposeByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_GetSendPropose_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.GetSendProposeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).GetSendPropose(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetSendPropose",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).GetSendPropose(ctx, req.(*channel_wedding_logic.GetSendProposeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_GetUserWeddingPrecipitation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.GetUserWeddingPrecipitationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).GetUserWeddingPrecipitation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetUserWeddingPrecipitation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).GetUserWeddingPrecipitation(ctx, req.(*channel_wedding_logic.GetUserWeddingPrecipitationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_ReportWeddingScenePic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.ReportWeddingScenePicRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).ReportWeddingScenePic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/ReportWeddingScenePic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).ReportWeddingScenePic(ctx, req.(*channel_wedding_logic.ReportWeddingScenePicRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_HideWeddingRelation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.HideWeddingRelationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).HideWeddingRelation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/HideWeddingRelation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).HideWeddingRelation(ctx, req.(*channel_wedding_logic.HideWeddingRelationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_GetWeddingPreviewResource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.GetWeddingPreviewResourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).GetWeddingPreviewResource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingPreviewResource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).GetWeddingPreviewResource(ctx, req.(*channel_wedding_logic.GetWeddingPreviewResourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_GetWeddingHighLightPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.GetWeddingHighLightPresentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).GetWeddingHighLightPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingHighLightPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).GetWeddingHighLightPresent(ctx, req.(*channel_wedding_logic.GetWeddingHighLightPresentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_GetWeddingRankEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.GetWeddingRankEntryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).GetWeddingRankEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingRankEntry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).GetWeddingRankEntry(ctx, req.(*channel_wedding_logic.GetWeddingRankEntryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_SendWeddingReservePresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.SendWeddingReservePresentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).SendWeddingReservePresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/SendWeddingReservePresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).SendWeddingReservePresent(ctx, req.(*channel_wedding_logic.SendWeddingReservePresentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_GetWeddingThemeCfgList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.GetWeddingThemeCfgListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).GetWeddingThemeCfgList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingThemeCfgList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).GetWeddingThemeCfgList(ctx, req.(*channel_wedding_logic.GetWeddingThemeCfgListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_RevokePropose_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.RevokeProposeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).RevokePropose(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/RevokePropose",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).RevokePropose(ctx, req.(*channel_wedding_logic.RevokeProposeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingLogic_GetUserInRoomStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_wedding_logic.GetUserInRoomStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingLogicServer).GetUserInRoomStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_wedding_logic.ChannelWeddingLogic/GetUserInRoomStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingLogicServer).GetUserInRoomStatus(ctx, req.(*channel_wedding_logic.GetUserInRoomStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelWeddingLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.channel_wedding_logic.ChannelWeddingLogic",
	HandlerType: (*ChannelWeddingLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetGetWeddingInfo",
			Handler:    _ChannelWeddingLogic_GetGetWeddingInfo_Handler,
		},
		{
			MethodName: "SwitchWeddingStage",
			Handler:    _ChannelWeddingLogic_SwitchWeddingStage_Handler,
		},
		{
			MethodName: "TakeWeddingGroupPhoto",
			Handler:    _ChannelWeddingLogic_TakeWeddingGroupPhoto_Handler,
		},
		{
			MethodName: "ApplyToJoinChairGame",
			Handler:    _ChannelWeddingLogic_ApplyToJoinChairGame_Handler,
		},
		{
			MethodName: "GetChairGameApplyList",
			Handler:    _ChannelWeddingLogic_GetChairGameApplyList_Handler,
		},
		{
			MethodName: "GetChairGameInfo",
			Handler:    _ChannelWeddingLogic_GetChairGameInfo_Handler,
		},
		{
			MethodName: "GrabChair",
			Handler:    _ChannelWeddingLogic_GrabChair_Handler,
		},
		{
			MethodName: "StartChairGame",
			Handler:    _ChannelWeddingLogic_StartChairGame_Handler,
		},
		{
			MethodName: "SetChairGameToNextRound",
			Handler:    _ChannelWeddingLogic_SetChairGameToNextRound_Handler,
		},
		{
			MethodName: "StartGrabChair",
			Handler:    _ChannelWeddingLogic_StartGrabChair_Handler,
		},
		{
			MethodName: "GetUserWeddingPose",
			Handler:    _ChannelWeddingLogic_GetUserWeddingPose_Handler,
		},
		{
			MethodName: "SetUserInuseWeddingPose",
			Handler:    _ChannelWeddingLogic_SetUserInuseWeddingPose_Handler,
		},
		{
			MethodName: "BatchGetUserInuseWeddingPose",
			Handler:    _ChannelWeddingLogic_BatchGetUserInuseWeddingPose_Handler,
		},
		{
			MethodName: "GetWeddingGroupPhotoSeatMap",
			Handler:    _ChannelWeddingLogic_GetWeddingGroupPhotoSeatMap_Handler,
		},
		{
			MethodName: "SetUserWeddingGroupPhotoSeat",
			Handler:    _ChannelWeddingLogic_SetUserWeddingGroupPhotoSeat_Handler,
		},
		{
			MethodName: "SetChairGameReward",
			Handler:    _ChannelWeddingLogic_SetChairGameReward_Handler,
		},
		{
			MethodName: "BatchGetUserWeddingClothes",
			Handler:    _ChannelWeddingLogic_BatchGetUserWeddingClothes_Handler,
		},
		{
			MethodName: "GetChairGameRewardSetting",
			Handler:    _ChannelWeddingLogic_GetChairGameRewardSetting_Handler,
		},
		{
			MethodName: "SetUserWeddingOrientation",
			Handler:    _ChannelWeddingLogic_SetUserWeddingOrientation_Handler,
		},
		{
			MethodName: "GetWeddingSchedulePageInfo",
			Handler:    _ChannelWeddingLogic_GetWeddingSchedulePageInfo_Handler,
		},
		{
			MethodName: "BuyWedding",
			Handler:    _ChannelWeddingLogic_BuyWedding_Handler,
		},
		{
			MethodName: "CancelWedding",
			Handler:    _ChannelWeddingLogic_CancelWedding_Handler,
		},
		{
			MethodName: "GetWeddingHallList",
			Handler:    _ChannelWeddingLogic_GetWeddingHallList_Handler,
		},
		{
			MethodName: "SubscribeWedding",
			Handler:    _ChannelWeddingLogic_SubscribeWedding_Handler,
		},
		{
			MethodName: "GetWeddingEntrySwitch",
			Handler:    _ChannelWeddingLogic_GetWeddingEntrySwitch_Handler,
		},
		{
			MethodName: "GetWeddingBigScreen",
			Handler:    _ChannelWeddingLogic_GetWeddingBigScreen_Handler,
		},
		{
			MethodName: "SaveWeddingBigScreen",
			Handler:    _ChannelWeddingLogic_SaveWeddingBigScreen_Handler,
		},
		{
			MethodName: "GetWeddingInviteInfo",
			Handler:    _ChannelWeddingLogic_GetWeddingInviteInfo_Handler,
		},
		{
			MethodName: "HandleWeddingInvite",
			Handler:    _ChannelWeddingLogic_HandleWeddingInvite_Handler,
		},
		{
			MethodName: "ApplyEndWeddingRelationship",
			Handler:    _ChannelWeddingLogic_ApplyEndWeddingRelationship_Handler,
		},
		{
			MethodName: "CancelEndWeddingRelationship",
			Handler:    _ChannelWeddingLogic_CancelEndWeddingRelationship_Handler,
		},
		{
			MethodName: "DirectEndWeddingRelationship",
			Handler:    _ChannelWeddingLogic_DirectEndWeddingRelationship_Handler,
		},
		{
			MethodName: "GetProposeList",
			Handler:    _ChannelWeddingLogic_GetProposeList_Handler,
		},
		{
			MethodName: "SendPropose",
			Handler:    _ChannelWeddingLogic_SendPropose_Handler,
		},
		{
			MethodName: "HandlePropose",
			Handler:    _ChannelWeddingLogic_HandlePropose_Handler,
		},
		{
			MethodName: "GetProposeById",
			Handler:    _ChannelWeddingLogic_GetProposeById_Handler,
		},
		{
			MethodName: "GetSendPropose",
			Handler:    _ChannelWeddingLogic_GetSendPropose_Handler,
		},
		{
			MethodName: "GetUserWeddingPrecipitation",
			Handler:    _ChannelWeddingLogic_GetUserWeddingPrecipitation_Handler,
		},
		{
			MethodName: "ReportWeddingScenePic",
			Handler:    _ChannelWeddingLogic_ReportWeddingScenePic_Handler,
		},
		{
			MethodName: "HideWeddingRelation",
			Handler:    _ChannelWeddingLogic_HideWeddingRelation_Handler,
		},
		{
			MethodName: "GetWeddingPreviewResource",
			Handler:    _ChannelWeddingLogic_GetWeddingPreviewResource_Handler,
		},
		{
			MethodName: "GetWeddingHighLightPresent",
			Handler:    _ChannelWeddingLogic_GetWeddingHighLightPresent_Handler,
		},
		{
			MethodName: "GetWeddingRankEntry",
			Handler:    _ChannelWeddingLogic_GetWeddingRankEntry_Handler,
		},
		{
			MethodName: "SendWeddingReservePresent",
			Handler:    _ChannelWeddingLogic_SendWeddingReservePresent_Handler,
		},
		{
			MethodName: "GetWeddingThemeCfgList",
			Handler:    _ChannelWeddingLogic_GetWeddingThemeCfgList_Handler,
		},
		{
			MethodName: "RevokePropose",
			Handler:    _ChannelWeddingLogic_RevokePropose_Handler,
		},
		{
			MethodName: "GetUserInRoomStatus",
			Handler:    _ChannelWeddingLogic_GetUserInRoomStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/channel_wedding_logic/grpc_channel_wedding.proto",
}

func init() {
	proto.RegisterFile("api/channel_wedding_logic/grpc_channel_wedding.proto", fileDescriptor_grpc_channel_wedding_d2bf2fc82a0a8955)
}

var fileDescriptor_grpc_channel_wedding_d2bf2fc82a0a8955 = []byte{
	// 1414 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x99, 0xdb, 0x6f, 0xdc, 0x44,
	0x14, 0xc6, 0xe5, 0x1a, 0xa1, 0x62, 0x04, 0x82, 0x29, 0x2d, 0x22, 0x2d, 0x52, 0xc4, 0x23, 0xb4,
	0xde, 0xde, 0x29, 0x4d, 0x5b, 0x89, 0x2c, 0xd5, 0xb6, 0x28, 0xc0, 0x6a, 0x1d, 0x84, 0xc4, 0x4b,
	0x35, 0xf1, 0x4e, 0xbd, 0xa3, 0x38, 0x33, 0x8e, 0x3d, 0x9b, 0x34, 0x48, 0x48, 0x21, 0xe5, 0xd6,
	0x52, 0x5a, 0x7b, 0xa1, 0x08, 0xf1, 0x80, 0x04, 0x42, 0xe2, 0xcf, 0xe3, 0x7e, 0xbf, 0x6a, 0x7d,
	0x59, 0xfb, 0xf8, 0x32, 0x3b, 0xbb, 0x6f, 0xd1, 0xee, 0xf7, 0x9b, 0xf3, 0xf9, 0xcc, 0x78, 0xce,
	0x39, 0x59, 0xe3, 0x34, 0xf6, 0x68, 0xcb, 0x1e, 0x60, 0xc6, 0x88, 0x7b, 0x6d, 0x9b, 0xf4, 0xfb,
	0x94, 0x39, 0xd7, 0x5c, 0xee, 0x50, 0xbb, 0xe5, 0xf8, 0x9e, 0x7d, 0xad, 0xf4, 0x95, 0xe9, 0xf9,
	0x5c, 0x70, 0x74, 0xc4, 0xc1, 0x26, 0xf6, 0xa8, 0x59, 0x0b, 0x2e, 0x9c, 0xa8, 0x5f, 0xaf, 0xf6,
	0xd3, 0x64, 0xc1, 0x85, 0xa7, 0xc7, 0x36, 0xc8, 0x0d, 0x41, 0x58, 0x40, 0x39, 0xcb, 0xff, 0x4a,
	0xbe, 0x3e, 0xf9, 0xc5, 0x59, 0xe3, 0x40, 0x3b, 0xc1, 0xdf, 0x48, 0xe8, 0x95, 0x31, 0x8c, 0xee,
	0x6a, 0xc6, 0xe3, 0x1d, 0x22, 0x3a, 0x44, 0xa4, 0x1f, 0x5f, 0x65, 0xd7, 0x39, 0x3a, 0x6b, 0x3a,
	0xb8, 0xde, 0x9a, 0xd9, 0x21, 0x02, 0xae, 0x33, 0x06, 0x7a, 0x64, 0x73, 0x48, 0x02, 0xb1, 0xf0,
	0xfc, 0xcc, 0x5c, 0xe0, 0x71, 0x16, 0x90, 0x67, 0xf6, 0xef, 0xed, 0x2e, 0x3e, 0xb0, 0xff, 0xbb,
	0x50, 0x47, 0xb7, 0x34, 0x03, 0x59, 0xdb, 0x54, 0xd8, 0x83, 0x54, 0x67, 0x09, 0xec, 0x10, 0x74,
	0xaa, 0x79, 0xe5, 0xaa, 0x3a, 0xb3, 0x73, 0x7a, 0x36, 0x08, 0x78, 0xf9, 0x3e, 0xd4, 0xd1, 0x48,
	0x33, 0x0e, 0xae, 0xe2, 0x75, 0x92, 0xca, 0x3a, 0x3e, 0x1f, 0x7a, 0xdd, 0xc1, 0x78, 0xfb, 0x24,
	0x09, 0xaa, 0x05, 0x14, 0x12, 0xd4, 0xc0, 0x01, 0x53, 0x3f, 0x84, 0x3a, 0xba, 0xa7, 0x19, 0x4f,
	0xbc, 0xe8, 0x79, 0xee, 0xce, 0x2a, 0x7f, 0x99, 0x53, 0xd6, 0x1e, 0x60, 0xea, 0x77, 0xf0, 0x06,
	0x41, 0x67, 0x9a, 0xd7, 0xae, 0xd3, 0x67, 0x96, 0xce, 0xce, 0x8a, 0x01, 0x47, 0x3f, 0xa6, 0x69,
	0x4a, 0xb6, 0x37, 0x91, 0xc4, 0xd8, 0x0a, 0x0d, 0xc4, 0xf4, 0x73, 0x54, 0x02, 0x94, 0xcf, 0x51,
	0x85, 0x03, 0xa6, 0x7e, 0x0a, 0x75, 0x74, 0x53, 0x33, 0x1e, 0x2b, 0x6a, 0xe3, 0x73, 0x7d, 0x42,
	0x6d, 0xdd, 0xe2, 0x91, 0x3e, 0x39, 0x0b, 0x02, 0x5c, 0xfc, 0x1c, 0xea, 0x88, 0x19, 0x0f, 0x75,
	0x7c, 0xbc, 0x16, 0xcb, 0xd0, 0xb3, 0x92, 0xa5, 0x32, 0x51, 0x16, 0xf6, 0x39, 0x25, 0x2d, 0x88,
	0xf7, 0x4b, 0xa8, 0xa3, 0xb7, 0x8d, 0x47, 0x2d, 0x81, 0xfd, 0xdc, 0x17, 0x6a, 0x49, 0xde, 0x01,
	0xa0, 0xcc, 0x22, 0x1f, 0x57, 0x07, 0x40, 0xf8, 0x5f, 0x43, 0x1d, 0x7d, 0xae, 0x19, 0x4f, 0x5a,
	0x85, 0xac, 0xac, 0xf2, 0x57, 0xc9, 0x0d, 0xd1, 0xe3, 0x43, 0xd6, 0x47, 0xe7, 0x24, 0xeb, 0xd6,
	0x23, 0x99, 0xa3, 0x17, 0xe6, 0x20, 0x81, 0xb5, 0xdf, 0x0a, 0x99, 0xc9, 0xb7, 0x63, 0x5a, 0x66,
	0x2a, 0x7b, 0x72, 0x5c, 0x1d, 0x00, 0xe1, 0x7f, 0x4f, 0xaf, 0xb5, 0x0e, 0x11, 0xaf, 0x07, 0xc4,
	0x4f, 0x5f, 0xf2, 0x2e, 0x0f, 0xa4, 0xd7, 0x5a, 0x55, 0xad, 0x70, 0xad, 0xd5, 0x41, 0xc0, 0xcb,
	0x1f, 0xf9, 0x2e, 0x8d, 0x85, 0x57, 0xd9, 0x30, 0x20, 0x45, 0x43, 0xf2, 0x5d, 0xaa, 0x43, 0xd4,
	0x76, 0xa9, 0x9e, 0x04, 0xd6, 0xfe, 0x0c, 0x75, 0xf4, 0xad, 0x66, 0x1c, 0x59, 0xc6, 0xc2, 0x1e,
	0x74, 0x1a, 0xfc, 0x5d, 0x6c, 0x8e, 0x22, 0xe3, 0x32, 0x93, 0x97, 0xe6, 0xc5, 0x81, 0xd3, 0xbf,
	0x42, 0x1d, 0x7d, 0xa3, 0x19, 0x87, 0xf3, 0xaa, 0x99, 0xdf, 0xd8, 0x16, 0xc1, 0xe2, 0x15, 0xec,
	0xa1, 0x0b, 0xd2, 0x4d, 0x6a, 0xc2, 0x32, 0x9f, 0x17, 0xe7, 0xa4, 0x81, 0xcd, 0xbf, 0xd3, 0x84,
	0x5a, 0xe0, 0x50, 0x40, 0x4a, 0x96, 0x50, 0x19, 0xa7, 0x90, 0x50, 0x39, 0x0e, 0x9c, 0xfe, 0x93,
	0x15, 0x7e, 0x52, 0xbc, 0x5e, 0xb6, 0xb1, 0xdf, 0x97, 0x16, 0xfe, 0x8a, 0x5a, 0xa5, 0xf0, 0xd7,
	0x40, 0xc0, 0xcb, 0xbf, 0xa1, 0x8e, 0xbe, 0xd2, 0x8c, 0x85, 0xe2, 0x79, 0x48, 0x9f, 0xa1, 0xed,
	0x72, 0x31, 0x20, 0x01, 0x5a, 0x52, 0x3b, 0x45, 0x90, 0xca, 0xbc, 0x5d, 0x98, 0x0f, 0x06, 0x1e,
	0xff, 0x0b, 0x75, 0xf4, 0xa5, 0x66, 0x3c, 0xd5, 0xa9, 0x3c, 0x8c, 0x45, 0x84, 0xa0, 0xcc, 0x41,
	0xe7, 0xd5, 0xca, 0x16, 0x80, 0x32, 0x87, 0x4b, 0x73, 0xb1, 0xc0, 0xe0, 0x6e, 0x94, 0x18, 0x84,
	0x67, 0xe0, 0x35, 0x9f, 0x12, 0x26, 0xb0, 0xa0, 0x9c, 0xc9, 0x0c, 0x36, 0x42, 0x0a, 0x06, 0x25,
	0x2c, 0x30, 0xf8, 0x4e, 0x94, 0xec, 0x72, 0xfe, 0x36, 0x59, 0xf6, 0x80, 0xf4, 0x87, 0x2e, 0xe9,
	0x62, 0x27, 0x69, 0x16, 0x96, 0x54, 0xde, 0xc1, 0x32, 0xa5, 0xb0, 0xcb, 0x32, 0x18, 0x78, 0xdc,
	0x8b, 0x74, 0xb4, 0x69, 0x18, 0xcb, 0xc3, 0x9d, 0x54, 0x8f, 0x24, 0x5d, 0x41, 0xae, 0xca, 0x2c,
	0x1c, 0x55, 0x13, 0x83, 0x90, 0x37, 0x23, 0x1d, 0xbd, 0x65, 0x3c, 0xd2, 0xc6, 0xcc, 0x9e, 0x34,
	0xea, 0xc8, 0x6c, 0x5e, 0x08, 0x08, 0xb3, 0xc0, 0x2d, 0x65, 0x3d, 0x88, 0xfd, 0x6e, 0x34, 0x29,
	0x93, 0xa9, 0xe0, 0x0a, 0x76, 0xdd, 0xb8, 0x8f, 0x3c, 0xa5, 0x92, 0xcd, 0x4c, 0xad, 0x56, 0x26,
	0x2b, 0x10, 0xf0, 0xf2, 0x5e, 0x94, 0x74, 0x90, 0xd6, 0x70, 0x2d, 0xb0, 0x7d, 0xba, 0x96, 0x15,
	0x03, 0x59, 0x07, 0x59, 0xd6, 0x2a, 0x74, 0x90, 0x55, 0x04, 0xb8, 0x78, 0x3f, 0x9a, 0x34, 0xd7,
	0xa9, 0xe0, 0x32, 0x13, 0xfe, 0x4e, 0x32, 0xbb, 0x4c, 0x69, 0xae, 0xab, 0x80, 0x5a, 0x73, 0x5d,
	0xc7, 0x01, 0x53, 0x1f, 0x44, 0x3a, 0xba, 0xa3, 0x19, 0x07, 0x72, 0xed, 0x32, 0x75, 0x2c, 0xdb,
	0x27, 0x84, 0x21, 0xa5, 0x94, 0x4f, 0xe4, 0x99, 0xa1, 0x33, 0x33, 0x52, 0xc0, 0xce, 0x87, 0x51,
	0x32, 0x12, 0x59, 0x78, 0x8b, 0x54, 0xfc, 0x48, 0x56, 0xae, 0xd3, 0x2b, 0x8c, 0x44, 0xf5, 0x18,
	0x70, 0x74, 0x2b, 0x75, 0x54, 0x9c, 0xa9, 0xb7, 0xa8, 0x48, 0x2e, 0x15, 0xa5, 0x67, 0xcd, 0xf5,
	0x0a, 0x8e, 0xea, 0x31, 0xe0, 0xe8, 0x76, 0xba, 0x65, 0x57, 0x30, 0xeb, 0xbb, 0x04, 0xa8, 0x65,
	0x5b, 0x56, 0x23, 0x57, 0xd8, 0xb2, 0x5a, 0x0a, 0xd8, 0xf9, 0x28, 0x4a, 0xda, 0xa7, 0x78, 0x7c,
	0xbb, 0xcc, 0xfa, 0x93, 0xc3, 0xef, 0xc6, 0x37, 0x75, 0x30, 0xa0, 0xd2, 0xf6, 0x49, 0x82, 0x29,
	0xb4, 0x4f, 0x52, 0x1a, 0xd8, 0xbc, 0x13, 0x25, 0xed, 0x53, 0x72, 0x67, 0x35, 0xf8, 0xbc, 0x38,
	0xed, 0xae, 0x93, 0x1b, 0xbd, 0x34, 0x2f, 0x0e, 0x9c, 0x7e, 0x9c, 0x3a, 0x7d, 0x89, 0xfa, 0xc4,
	0x16, 0xb3, 0x3b, 0x95, 0x71, 0x0a, 0x4e, 0xe5, 0x38, 0x70, 0x7a, 0x37, 0x8a, 0x27, 0xb1, 0x0e,
	0x11, 0x5d, 0x9f, 0x7b, 0x3c, 0x20, 0xf1, 0xf5, 0xde, 0x92, 0x9e, 0xee, 0x82, 0x52, 0x61, 0x12,
	0x2b, 0x03, 0x20, 0xfc, 0xbd, 0x48, 0x47, 0xc2, 0x78, 0xd8, 0x22, 0xac, 0x9f, 0x8a, 0xd0, 0x51,
	0x59, 0x2f, 0x31, 0x91, 0x65, 0x81, 0x8f, 0x29, 0xaa, 0x41, 0xd4, 0x30, 0x29, 0xaa, 0xc9, 0x8b,
	0x91, 0xc5, 0x35, 0xa7, 0xbd, 0x41, 0xa5, 0xc8, 0x2d, 0x65, 0x3d, 0x88, 0x1d, 0x95, 0x13, 0xbe,
	0xbc, 0x73, 0xb5, 0xaf, 0x96, 0xf0, 0xb1, 0x72, 0xa6, 0x84, 0x27, 0x00, 0x08, 0x3f, 0x9a, 0x84,
	0x2f, 0xe6, 0x5c, 0x1e, 0xbe, 0x26, 0xed, 0xc7, 0xd5, 0x01, 0x10, 0xfe, 0x93, 0x68, 0x32, 0xa8,
	0x15, 0xc7, 0x62, 0x9f, 0xd8, 0xd4, 0xa3, 0x69, 0x23, 0x7a, 0x41, 0x79, 0x9a, 0x2e, 0x62, 0x6a,
	0x83, 0x5a, 0x33, 0x0d, 0x6c, 0x7e, 0x9a, 0xd6, 0xf9, 0x1e, 0xf1, 0xb8, 0x9f, 0x37, 0x87, 0x84,
	0x91, 0x2e, 0xb5, 0x65, 0x75, 0xbe, 0x16, 0x50, 0xa8, 0xf3, 0x0d, 0x1c, 0x30, 0x75, 0x3f, 0x2b,
	0x1a, 0xb4, 0x4f, 0x4a, 0x2f, 0xb6, 0xb4, 0x68, 0x54, 0xe5, 0x2a, 0x45, 0xa3, 0x8e, 0x02, 0x76,
	0x3e, 0x8b, 0x26, 0x23, 0x4f, 0x9e, 0xd1, 0x2d, 0x4a, 0xb6, 0x7b, 0x24, 0xe0, 0x43, 0xdf, 0x26,
	0x53, 0x46, 0x9e, 0x7a, 0x48, 0x6d, 0xe4, 0x69, 0x62, 0x61, 0xfb, 0x3a, 0x2a, 0x4f, 0x14, 0x57,
	0xa8, 0x33, 0x58, 0xa1, 0xce, 0x40, 0x74, 0x7d, 0x12, 0x10, 0x26, 0xd4, 0x26, 0x8a, 0x32, 0x35,
	0xd3, 0x44, 0x51, 0x85, 0x61, 0x5b, 0x3b, 0x2a, 0xf7, 0x6e, 0x3d, 0xcc, 0xd6, 0xe3, 0x5e, 0x4f,
	0xad, 0x77, 0x9b, 0xc8, 0x67, 0xea, 0xdd, 0x0a, 0x14, 0xec, 0x6f, 0x47, 0xd9, 0x94, 0x58, 0xa8,
	0x1d, 0x01, 0xf1, 0xb7, 0x48, 0x96, 0xb1, 0xf3, 0xf2, 0xfb, 0xb6, 0x16, 0x52, 0x9a, 0x12, 0x1b,
	0x59, 0xd8, 0xeb, 0x8e, 0x74, 0x74, 0x5f, 0x33, 0x0e, 0xe5, 0x8f, 0xb2, 0x3a, 0x20, 0x1b, 0xa4,
	0x7d, 0xdd, 0x89, 0xeb, 0x96, 0x52, 0x27, 0x5d, 0x24, 0x32, 0x6b, 0xe7, 0x66, 0x07, 0x61, 0xd3,
	0x3b, 0x8a, 0x2b, 0x4a, 0x8f, 0x6c, 0xf1, 0x75, 0x95, 0x8a, 0x02, 0x84, 0x0a, 0x15, 0xa5, 0xa4,
	0x87, 0xed, 0x6d, 0x7e, 0x86, 0x92, 0x7f, 0x95, 0xf5, 0x38, 0xdf, 0xb0, 0x04, 0x16, 0xc3, 0x00,
	0x4d, 0xff, 0xcf, 0x64, 0x51, 0xae, 0x76, 0x86, 0xaa, 0x14, 0xec, 0x6d, 0x47, 0xfa, 0xc2, 0xa5,
	0xbd, 0xdd, 0xc5, 0x83, 0xe9, 0x02, 0xc7, 0xd2, 0x05, 0x8e, 0xc5, 0x0b, 0xdc, 0xde, 0x5d, 0xdc,
	0xe7, 0xf0, 0xd1, 0xee, 0xe2, 0xe1, 0x56, 0xda, 0x55, 0x55, 0x7f, 0x03, 0x6b, 0x2d, 0xbb, 0xc6,
	0x21, 0x9b, 0x6f, 0x98, 0x9b, 0xc3, 0x6d, 0xcc, 0x4c, 0x21, 0x92, 0x9f, 0xcc, 0x4c, 0xec, 0xd1,
	0x37, 0x57, 0x1c, 0xee, 0x62, 0xe6, 0x98, 0x67, 0x4e, 0x0a, 0x61, 0xda, 0x7c, 0xa3, 0x15, 0x7f,
	0x65, 0x73, 0xb7, 0x85, 0x3d, 0xaf, 0xd5, 0xf8, 0xcb, 0xdf, 0x52, 0xed, 0xa7, 0x5f, 0xef, 0xd3,
	0x7b, 0xdd, 0xf6, 0xda, 0x83, 0xf1, 0x1a, 0xa7, 0xfe, 0x0f, 0x00, 0x00, 0xff, 0xff, 0x15, 0x1b,
	0xc0, 0x6f, 0x39, 0x1c, 0x00, 0x00,
}
