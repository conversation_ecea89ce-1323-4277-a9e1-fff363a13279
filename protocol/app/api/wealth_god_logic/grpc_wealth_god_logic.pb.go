// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/wealth_god_logic/grpc_wealth_god_logic.proto

package wealth_god_logic // import "golang.52tt.com/protocol/app/api/wealth_god_logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import wealth_god_logic "golang.52tt.com/protocol/app/wealth_god_logic"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// WealthGodLogicClient is the client API for WealthGodLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type WealthGodLogicClient interface {
	// 获取财神入口信息
	GetWealthGodEntry(ctx context.Context, in *wealth_god_logic.GetWealthGodEntryRequest, opts ...grpc.CallOption) (*wealth_god_logic.GetWealthGodEntryResponse, error)
	// 获取一个财神降临房间
	GetOneWealthGodChannel(ctx context.Context, in *wealth_god_logic.GetOneWealthGodChannelReq, opts ...grpc.CallOption) (*wealth_god_logic.GetOneWealthGodChannelResp, error)
	// 获取财神活动介绍
	GetWealthGodActivityInfo(ctx context.Context, in *wealth_god_logic.GetWealthGodActivityInfoRequest, opts ...grpc.CallOption) (*wealth_god_logic.GetWealthGodActivityInfoResponse, error)
	// 获取财神详情
	GetWealthGodDetail(ctx context.Context, in *wealth_god_logic.GetWealthGodDetailRequest, opts ...grpc.CallOption) (*wealth_god_logic.GetWealthGodDetailResponse, error)
	// 开启财神宝箱奖励
	OpenWealthGodBoxReward(ctx context.Context, in *wealth_god_logic.OpenWealthGodBoxRewardRequest, opts ...grpc.CallOption) (*wealth_god_logic.OpenWealthGodBoxRewardResponse, error)
	// 上报财神降临房间任务完成
	ReportStayRoomMissionFinish(ctx context.Context, in *wealth_god_logic.ReportStayRoomMissionFinishRequest, opts ...grpc.CallOption) (*wealth_god_logic.ReportStayRoomMissionFinishResponse, error)
	// 获取财神通用配置
	GetWealthGodCommonCfg(ctx context.Context, in *wealth_god_logic.GetWealthGodCommonCfgRequest, opts ...grpc.CallOption) (*wealth_god_logic.GetWealthGodCommonCfgResponse, error)
}

type wealthGodLogicClient struct {
	cc *grpc.ClientConn
}

func NewWealthGodLogicClient(cc *grpc.ClientConn) WealthGodLogicClient {
	return &wealthGodLogicClient{cc}
}

func (c *wealthGodLogicClient) GetWealthGodEntry(ctx context.Context, in *wealth_god_logic.GetWealthGodEntryRequest, opts ...grpc.CallOption) (*wealth_god_logic.GetWealthGodEntryResponse, error) {
	out := new(wealth_god_logic.GetWealthGodEntryResponse)
	err := c.cc.Invoke(ctx, "/ga.api.wealth_god_logic.WealthGodLogic/GetWealthGodEntry", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wealthGodLogicClient) GetOneWealthGodChannel(ctx context.Context, in *wealth_god_logic.GetOneWealthGodChannelReq, opts ...grpc.CallOption) (*wealth_god_logic.GetOneWealthGodChannelResp, error) {
	out := new(wealth_god_logic.GetOneWealthGodChannelResp)
	err := c.cc.Invoke(ctx, "/ga.api.wealth_god_logic.WealthGodLogic/GetOneWealthGodChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wealthGodLogicClient) GetWealthGodActivityInfo(ctx context.Context, in *wealth_god_logic.GetWealthGodActivityInfoRequest, opts ...grpc.CallOption) (*wealth_god_logic.GetWealthGodActivityInfoResponse, error) {
	out := new(wealth_god_logic.GetWealthGodActivityInfoResponse)
	err := c.cc.Invoke(ctx, "/ga.api.wealth_god_logic.WealthGodLogic/GetWealthGodActivityInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wealthGodLogicClient) GetWealthGodDetail(ctx context.Context, in *wealth_god_logic.GetWealthGodDetailRequest, opts ...grpc.CallOption) (*wealth_god_logic.GetWealthGodDetailResponse, error) {
	out := new(wealth_god_logic.GetWealthGodDetailResponse)
	err := c.cc.Invoke(ctx, "/ga.api.wealth_god_logic.WealthGodLogic/GetWealthGodDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wealthGodLogicClient) OpenWealthGodBoxReward(ctx context.Context, in *wealth_god_logic.OpenWealthGodBoxRewardRequest, opts ...grpc.CallOption) (*wealth_god_logic.OpenWealthGodBoxRewardResponse, error) {
	out := new(wealth_god_logic.OpenWealthGodBoxRewardResponse)
	err := c.cc.Invoke(ctx, "/ga.api.wealth_god_logic.WealthGodLogic/OpenWealthGodBoxReward", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wealthGodLogicClient) ReportStayRoomMissionFinish(ctx context.Context, in *wealth_god_logic.ReportStayRoomMissionFinishRequest, opts ...grpc.CallOption) (*wealth_god_logic.ReportStayRoomMissionFinishResponse, error) {
	out := new(wealth_god_logic.ReportStayRoomMissionFinishResponse)
	err := c.cc.Invoke(ctx, "/ga.api.wealth_god_logic.WealthGodLogic/ReportStayRoomMissionFinish", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wealthGodLogicClient) GetWealthGodCommonCfg(ctx context.Context, in *wealth_god_logic.GetWealthGodCommonCfgRequest, opts ...grpc.CallOption) (*wealth_god_logic.GetWealthGodCommonCfgResponse, error) {
	out := new(wealth_god_logic.GetWealthGodCommonCfgResponse)
	err := c.cc.Invoke(ctx, "/ga.api.wealth_god_logic.WealthGodLogic/GetWealthGodCommonCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WealthGodLogicServer is the server API for WealthGodLogic service.
type WealthGodLogicServer interface {
	// 获取财神入口信息
	GetWealthGodEntry(context.Context, *wealth_god_logic.GetWealthGodEntryRequest) (*wealth_god_logic.GetWealthGodEntryResponse, error)
	// 获取一个财神降临房间
	GetOneWealthGodChannel(context.Context, *wealth_god_logic.GetOneWealthGodChannelReq) (*wealth_god_logic.GetOneWealthGodChannelResp, error)
	// 获取财神活动介绍
	GetWealthGodActivityInfo(context.Context, *wealth_god_logic.GetWealthGodActivityInfoRequest) (*wealth_god_logic.GetWealthGodActivityInfoResponse, error)
	// 获取财神详情
	GetWealthGodDetail(context.Context, *wealth_god_logic.GetWealthGodDetailRequest) (*wealth_god_logic.GetWealthGodDetailResponse, error)
	// 开启财神宝箱奖励
	OpenWealthGodBoxReward(context.Context, *wealth_god_logic.OpenWealthGodBoxRewardRequest) (*wealth_god_logic.OpenWealthGodBoxRewardResponse, error)
	// 上报财神降临房间任务完成
	ReportStayRoomMissionFinish(context.Context, *wealth_god_logic.ReportStayRoomMissionFinishRequest) (*wealth_god_logic.ReportStayRoomMissionFinishResponse, error)
	// 获取财神通用配置
	GetWealthGodCommonCfg(context.Context, *wealth_god_logic.GetWealthGodCommonCfgRequest) (*wealth_god_logic.GetWealthGodCommonCfgResponse, error)
}

func RegisterWealthGodLogicServer(s *grpc.Server, srv WealthGodLogicServer) {
	s.RegisterService(&_WealthGodLogic_serviceDesc, srv)
}

func _WealthGodLogic_GetWealthGodEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(wealth_god_logic.GetWealthGodEntryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WealthGodLogicServer).GetWealthGodEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.wealth_god_logic.WealthGodLogic/GetWealthGodEntry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WealthGodLogicServer).GetWealthGodEntry(ctx, req.(*wealth_god_logic.GetWealthGodEntryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WealthGodLogic_GetOneWealthGodChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(wealth_god_logic.GetOneWealthGodChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WealthGodLogicServer).GetOneWealthGodChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.wealth_god_logic.WealthGodLogic/GetOneWealthGodChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WealthGodLogicServer).GetOneWealthGodChannel(ctx, req.(*wealth_god_logic.GetOneWealthGodChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _WealthGodLogic_GetWealthGodActivityInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(wealth_god_logic.GetWealthGodActivityInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WealthGodLogicServer).GetWealthGodActivityInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.wealth_god_logic.WealthGodLogic/GetWealthGodActivityInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WealthGodLogicServer).GetWealthGodActivityInfo(ctx, req.(*wealth_god_logic.GetWealthGodActivityInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WealthGodLogic_GetWealthGodDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(wealth_god_logic.GetWealthGodDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WealthGodLogicServer).GetWealthGodDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.wealth_god_logic.WealthGodLogic/GetWealthGodDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WealthGodLogicServer).GetWealthGodDetail(ctx, req.(*wealth_god_logic.GetWealthGodDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WealthGodLogic_OpenWealthGodBoxReward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(wealth_god_logic.OpenWealthGodBoxRewardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WealthGodLogicServer).OpenWealthGodBoxReward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.wealth_god_logic.WealthGodLogic/OpenWealthGodBoxReward",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WealthGodLogicServer).OpenWealthGodBoxReward(ctx, req.(*wealth_god_logic.OpenWealthGodBoxRewardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WealthGodLogic_ReportStayRoomMissionFinish_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(wealth_god_logic.ReportStayRoomMissionFinishRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WealthGodLogicServer).ReportStayRoomMissionFinish(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.wealth_god_logic.WealthGodLogic/ReportStayRoomMissionFinish",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WealthGodLogicServer).ReportStayRoomMissionFinish(ctx, req.(*wealth_god_logic.ReportStayRoomMissionFinishRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WealthGodLogic_GetWealthGodCommonCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(wealth_god_logic.GetWealthGodCommonCfgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WealthGodLogicServer).GetWealthGodCommonCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.wealth_god_logic.WealthGodLogic/GetWealthGodCommonCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WealthGodLogicServer).GetWealthGodCommonCfg(ctx, req.(*wealth_god_logic.GetWealthGodCommonCfgRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _WealthGodLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.wealth_god_logic.WealthGodLogic",
	HandlerType: (*WealthGodLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetWealthGodEntry",
			Handler:    _WealthGodLogic_GetWealthGodEntry_Handler,
		},
		{
			MethodName: "GetOneWealthGodChannel",
			Handler:    _WealthGodLogic_GetOneWealthGodChannel_Handler,
		},
		{
			MethodName: "GetWealthGodActivityInfo",
			Handler:    _WealthGodLogic_GetWealthGodActivityInfo_Handler,
		},
		{
			MethodName: "GetWealthGodDetail",
			Handler:    _WealthGodLogic_GetWealthGodDetail_Handler,
		},
		{
			MethodName: "OpenWealthGodBoxReward",
			Handler:    _WealthGodLogic_OpenWealthGodBoxReward_Handler,
		},
		{
			MethodName: "ReportStayRoomMissionFinish",
			Handler:    _WealthGodLogic_ReportStayRoomMissionFinish_Handler,
		},
		{
			MethodName: "GetWealthGodCommonCfg",
			Handler:    _WealthGodLogic_GetWealthGodCommonCfg_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/wealth_god_logic/grpc_wealth_god_logic.proto",
}

func init() {
	proto.RegisterFile("api/wealth_god_logic/grpc_wealth_god_logic.proto", fileDescriptor_grpc_wealth_god_logic_b2d13325269c05b2)
}

var fileDescriptor_grpc_wealth_god_logic_b2d13325269c05b2 = []byte{
	// 465 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x94, 0xcf, 0x8b, 0xd3, 0x40,
	0x14, 0xc7, 0xc9, 0x46, 0xa4, 0xcc, 0x41, 0x74, 0xc0, 0x2a, 0x11, 0xa1, 0x78, 0xf1, 0xd4, 0x19,
	0xed, 0xee, 0xa2, 0xe0, 0xc9, 0x56, 0x2d, 0x82, 0xb2, 0x12, 0x0f, 0x82, 0x97, 0x32, 0x9b, 0xce,
	0x4e, 0x07, 0x92, 0x79, 0xd3, 0xe4, 0xad, 0xdd, 0x82, 0x60, 0x59, 0x2f, 0xa2, 0x27, 0x07, 0xbc,
	0x78, 0xf4, 0xec, 0xef, 0xbf, 0x50, 0xda, 0x9a, 0xda, 0xa4, 0x71, 0x77, 0x73, 0x0b, 0x33, 0x9f,
	0xef, 0x37, 0x9f, 0x3c, 0xc2, 0x23, 0xb7, 0x84, 0xd5, 0x7c, 0x22, 0x45, 0x8c, 0xa3, 0x81, 0x82,
	0xe1, 0x20, 0x06, 0xa5, 0x23, 0xae, 0x52, 0x1b, 0x0d, 0xca, 0xa7, 0xcc, 0xa6, 0x80, 0x40, 0xaf,
	0x28, 0xc1, 0x84, 0xd5, 0xac, 0x7c, 0x1d, 0xdc, 0xdc, 0xa8, 0xa9, 0x6e, 0x08, 0xae, 0xcf, 0xdf,
	0x29, 0x8f, 0x50, 0x9a, 0x4c, 0x83, 0xf9, 0xf7, 0xb4, 0xbc, 0xee, 0x7c, 0x6a, 0x90, 0x0b, 0x2f,
	0x16, 0xc9, 0x3e, 0x0c, 0x9f, 0xcc, 0x73, 0xf4, 0x35, 0xb9, 0xd4, 0x97, 0xb8, 0x3a, 0x7c, 0x68,
	0x30, 0x9d, 0xd2, 0x36, 0x53, 0x62, 0xc3, 0x82, 0x6d, 0x70, 0xa1, 0x1c, 0x1f, 0xca, 0x0c, 0x03,
	0x76, 0x56, 0x3c, 0xb3, 0x60, 0x32, 0x79, 0xa3, 0x71, 0x3c, 0x6b, 0x9d, 0x6b, 0x7c, 0x73, 0x3e,
	0x7d, 0xeb, 0x91, 0x66, 0x5f, 0xe2, 0x9e, 0x91, 0x2b, 0xb4, 0x37, 0x12, 0xc6, 0xc8, 0x98, 0xfe,
	0xb7, 0xb4, 0x02, 0x0e, 0xe5, 0x38, 0xe0, 0xb5, 0xf8, 0xcc, 0xfe, 0xb5, 0xf8, 0xee, 0x7c, 0xfa,
	0xd1, 0x23, 0x57, 0xd7, 0x6d, 0xef, 0x47, 0xa8, 0x5f, 0x69, 0x9c, 0x3e, 0x36, 0x07, 0x40, 0x77,
	0x4e, 0xfd, 0xb8, 0x75, 0x3c, 0x1f, 0xc9, 0x6e, 0xcd, 0x54, 0x61, 0x32, 0x3f, 0x9c, 0x4f, 0xdf,
	0x10, 0xba, 0x4e, 0x3f, 0x90, 0x28, 0xf4, 0x09, 0x43, 0x29, 0x81, 0xb9, 0x06, 0x3f, 0x33, 0x5f,
	0x10, 0xf8, 0xe9, 0x7c, 0xfa, 0xc1, 0x23, 0xcd, 0x3d, 0x2b, 0xcd, 0x8a, 0xec, 0xc2, 0x51, 0x28,
	0x27, 0x22, 0x1d, 0xd2, 0x4e, 0x65, 0x6b, 0x35, 0x9c, 0x9b, 0x6c, 0xd7, 0xca, 0x14, 0x6c, 0x7e,
	0x39, 0x9f, 0x7e, 0xf6, 0xc8, 0xb5, 0x50, 0x5a, 0x48, 0xf1, 0x39, 0x8a, 0x69, 0x08, 0x90, 0x3c,
	0xd5, 0xd9, 0xfc, 0xcf, 0x7e, 0xa4, 0x8d, 0xce, 0x46, 0xf4, 0x4e, 0x65, 0xfd, 0x09, 0x89, 0xdc,
	0xeb, 0x6e, 0xfd, 0x60, 0x41, 0xee, 0xb7, 0xf3, 0xe9, 0x3b, 0x8f, 0x5c, 0x5e, 0x9f, 0x69, 0x0f,
	0x92, 0x04, 0x4c, 0xef, 0x40, 0xd1, 0xdb, 0xa7, 0xce, 0x7f, 0xc5, 0xe6, 0x42, 0x9d, 0x3a, 0x91,
	0x82, 0xca, 0x57, 0xe7, 0x07, 0x3b, 0xc7, 0xb3, 0xd6, 0xc5, 0x65, 0xba, 0xad, 0x60, 0xd8, 0x5e,
	0xa4, 0xdf, 0xcf, 0x5a, 0x5b, 0x0a, 0xdc, 0xac, 0xd5, 0xe4, 0xcb, 0xb6, 0xe2, 0x0e, 0xe0, 0xdd,
	0x7d, 0xd2, 0x8c, 0x20, 0x61, 0xe3, 0xc3, 0x89, 0x30, 0x0c, 0x71, 0xb9, 0x2d, 0xe6, 0xab, 0xe8,
	0x65, 0x57, 0x41, 0x2c, 0x8c, 0x62, 0xbb, 0x1d, 0x44, 0x16, 0x41, 0xc2, 0x17, 0x57, 0x11, 0xc4,
	0x5c, 0x58, 0xcb, 0xab, 0x36, 0xdc, 0xbd, 0xf2, 0xc1, 0x97, 0x2d, 0x3f, 0x7c, 0xd6, 0xdb, 0x3f,
	0xbf, 0x48, 0x6e, 0xff, 0x09, 0x00, 0x00, 0xff, 0xff, 0x2a, 0x99, 0xa5, 0x3a, 0x17, 0x05, 0x00,
	0x00,
}
