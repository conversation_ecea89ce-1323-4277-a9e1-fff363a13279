package guildrecruit

// Code generated by protoc-gen-svrkit-go. DO NOT EDIT.
// source: services/guildrecruit/guildrecruit.proto

/*
 This is a generated svrkit golang dev toolkit.
*/

import svrkit "gitlab.ttyuyin.com/golang/svrkit"

import context "golang.org/x/net/context"

import tlvpickle "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

var _ = context.TODO

// Magic Number for Guildrecruit service
const GuildrecruitMagic = uint16(15257)

// Client API for Guildrecruit service

type GuildrecruitClientInterface interface {
	UpdateGuildRecruitInfo(ctx context.Context, uin uint32, in *GuildRecruitInfo, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DelGuildRecruit(ctx context.Context, uin uint32, in *DelGuildRecruitReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGuildRecruitByGameId(ctx context.Context, uin uint32, in *GetGuildRecruitByGameIdReq, opts ...svrkit.CallOption) (*GuildRecruitInfo, error)
	GetGuildRecruitAll(ctx context.Context, uin uint32, in *tlvpickle.SKBuiltinEmpty_PB, opts ...svrkit.CallOption) (*GetGuildRecruitAllResp, error)
	UpdateAwardIntro(ctx context.Context, uin uint32, in *UpdateAwardIntroReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	UpdateAwardInfo(ctx context.Context, uin uint32, in *AwardInfo, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DelAwardInfo(ctx context.Context, uin uint32, in *DelAwardInfoReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
}

type GuildrecruitClient struct {
	cc *svrkit.ClientConn
}

func NewGuildrecruitClient(cc *svrkit.ClientConn) GuildrecruitClientInterface {
	return &GuildrecruitClient{cc}
}

const (
	commandguildrecruitGetSelfSvnInfo          = 9995
	commandguildrecruitEcho                    = 9999
	commandguildrecruitUpdateGuildRecruitInfo  = 1
	commandguildrecruitDelGuildRecruit         = 2
	commandguildrecruitGetGuildRecruitByGameId = 3
	commandguildrecruitGetGuildRecruitAll      = 4
	commandguildrecruitUpdateAwardIntro        = 5
	commandguildrecruitUpdateAwardInfo         = 6
	commandguildrecruitDelAwardInfo            = 7
)

func (c *GuildrecruitClient) UpdateGuildRecruitInfo(ctx context.Context, uin uint32, in *GuildRecruitInfo, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandguildrecruitUpdateGuildRecruitInfo, "/guildrecruit.Guildrecruit/UpdateGuildRecruitInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildrecruitClient) DelGuildRecruit(ctx context.Context, uin uint32, in *DelGuildRecruitReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandguildrecruitDelGuildRecruit, "/guildrecruit.Guildrecruit/DelGuildRecruit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildrecruitClient) GetGuildRecruitByGameId(ctx context.Context, uin uint32, in *GetGuildRecruitByGameIdReq, opts ...svrkit.CallOption) (*GuildRecruitInfo, error) {
	out := new(GuildRecruitInfo)
	err := c.cc.Invoke(ctx, uin, commandguildrecruitGetGuildRecruitByGameId, "/guildrecruit.Guildrecruit/GetGuildRecruitByGameId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildrecruitClient) GetGuildRecruitAll(ctx context.Context, uin uint32, in *tlvpickle.SKBuiltinEmpty_PB, opts ...svrkit.CallOption) (*GetGuildRecruitAllResp, error) {
	out := new(GetGuildRecruitAllResp)
	err := c.cc.Invoke(ctx, uin, commandguildrecruitGetGuildRecruitAll, "/guildrecruit.Guildrecruit/GetGuildRecruitAll", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildrecruitClient) UpdateAwardIntro(ctx context.Context, uin uint32, in *UpdateAwardIntroReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandguildrecruitUpdateAwardIntro, "/guildrecruit.Guildrecruit/UpdateAwardIntro", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildrecruitClient) UpdateAwardInfo(ctx context.Context, uin uint32, in *AwardInfo, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandguildrecruitUpdateAwardInfo, "/guildrecruit.Guildrecruit/UpdateAwardInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildrecruitClient) DelAwardInfo(ctx context.Context, uin uint32, in *DelAwardInfoReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandguildrecruitDelAwardInfo, "/guildrecruit.Guildrecruit/DelAwardInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}
