// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/channel-wedding-plan/channel-wedding-plan.proto

package channel_wedding_plan

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	grpc "google.golang.org/grpc"
)

// MockChannelWeddingPlanClient is a mock of ChannelWeddingPlanClient interface.
type MockChannelWeddingPlanClient struct {
	ctrl     *gomock.Controller
	recorder *MockChannelWeddingPlanClientMockRecorder
}

// MockChannelWeddingPlanClientMockRecorder is the mock recorder for MockChannelWeddingPlanClient.
type MockChannelWeddingPlanClientMockRecorder struct {
	mock *MockChannelWeddingPlanClient
}

// NewMockChannelWeddingPlanClient creates a new mock instance.
func NewMockChannelWeddingPlanClient(ctrl *gomock.Controller) *MockChannelWeddingPlanClient {
	mock := &MockChannelWeddingPlanClient{ctrl: ctrl}
	mock.recorder = &MockChannelWeddingPlanClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChannelWeddingPlanClient) EXPECT() *MockChannelWeddingPlanClientMockRecorder {
	return m.recorder
}

// AddThemeCfg mocks base method.
func (m *MockChannelWeddingPlanClient) AddThemeCfg(ctx context.Context, in *AddThemeCfgRequest, opts ...grpc.CallOption) (*AddThemeCfgResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddThemeCfg", varargs...)
	ret0, _ := ret[0].(*AddThemeCfgResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddThemeCfg indicates an expected call of AddThemeCfg.
func (mr *MockChannelWeddingPlanClientMockRecorder) AddThemeCfg(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddThemeCfg", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).AddThemeCfg), varargs...)
}

// AdminCancelWedding mocks base method.
func (m *MockChannelWeddingPlanClient) AdminCancelWedding(ctx context.Context, in *AdminCancelWeddingRequest, opts ...grpc.CallOption) (*AdminCancelWeddingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AdminCancelWedding", varargs...)
	ret0, _ := ret[0].(*AdminCancelWeddingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AdminCancelWedding indicates an expected call of AdminCancelWedding.
func (mr *MockChannelWeddingPlanClientMockRecorder) AdminCancelWedding(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AdminCancelWedding", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).AdminCancelWedding), varargs...)
}

// ApplyEndWeddingRelation mocks base method.
func (m *MockChannelWeddingPlanClient) ApplyEndWeddingRelation(ctx context.Context, in *ApplyEndWeddingRelationRequest, opts ...grpc.CallOption) (*ApplyEndWeddingRelationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ApplyEndWeddingRelation", varargs...)
	ret0, _ := ret[0].(*ApplyEndWeddingRelationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplyEndWeddingRelation indicates an expected call of ApplyEndWeddingRelation.
func (mr *MockChannelWeddingPlanClientMockRecorder) ApplyEndWeddingRelation(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplyEndWeddingRelation", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).ApplyEndWeddingRelation), varargs...)
}

// ArrangeWeddingReserve mocks base method.
func (m *MockChannelWeddingPlanClient) ArrangeWeddingReserve(ctx context.Context, in *ArrangeWeddingReserveRequest, opts ...grpc.CallOption) (*ArrangeWeddingReserveResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ArrangeWeddingReserve", varargs...)
	ret0, _ := ret[0].(*ArrangeWeddingReserveResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ArrangeWeddingReserve indicates an expected call of ArrangeWeddingReserve.
func (mr *MockChannelWeddingPlanClientMockRecorder) ArrangeWeddingReserve(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ArrangeWeddingReserve", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).ArrangeWeddingReserve), varargs...)
}

// BatGetWeddingInfoById mocks base method.
func (m *MockChannelWeddingPlanClient) BatGetWeddingInfoById(ctx context.Context, in *BatGetWeddingInfoByIdRequest, opts ...grpc.CallOption) (*BatGetWeddingInfoByIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatGetWeddingInfoById", varargs...)
	ret0, _ := ret[0].(*BatGetWeddingInfoByIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetWeddingInfoById indicates an expected call of BatGetWeddingInfoById.
func (mr *MockChannelWeddingPlanClientMockRecorder) BatGetWeddingInfoById(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetWeddingInfoById", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).BatGetWeddingInfoById), varargs...)
}

// BatGetWeddingSubscribeStatus mocks base method.
func (m *MockChannelWeddingPlanClient) BatGetWeddingSubscribeStatus(ctx context.Context, in *BatGetWeddingSubscribeStatusRequest, opts ...grpc.CallOption) (*BatGetWeddingSubscribeStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatGetWeddingSubscribeStatus", varargs...)
	ret0, _ := ret[0].(*BatGetWeddingSubscribeStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetWeddingSubscribeStatus indicates an expected call of BatGetWeddingSubscribeStatus.
func (mr *MockChannelWeddingPlanClientMockRecorder) BatGetWeddingSubscribeStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetWeddingSubscribeStatus", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).BatGetWeddingSubscribeStatus), varargs...)
}

// BatchGetMarriageInfo mocks base method.
func (m *MockChannelWeddingPlanClient) BatchGetMarriageInfo(ctx context.Context, in *BatchGetMarriageInfoRequest, opts ...grpc.CallOption) (*BatchGetMarriageInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetMarriageInfo", varargs...)
	ret0, _ := ret[0].(*BatchGetMarriageInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetMarriageInfo indicates an expected call of BatchGetMarriageInfo.
func (mr *MockChannelWeddingPlanClientMockRecorder) BatchGetMarriageInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetMarriageInfo", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).BatchGetMarriageInfo), varargs...)
}

// BatchGetWeddingRole mocks base method.
func (m *MockChannelWeddingPlanClient) BatchGetWeddingRole(ctx context.Context, in *BatchGetWeddingRoleRequest, opts ...grpc.CallOption) (*BatchGetWeddingRoleResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetWeddingRole", varargs...)
	ret0, _ := ret[0].(*BatchGetWeddingRoleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetWeddingRole indicates an expected call of BatchGetWeddingRole.
func (mr *MockChannelWeddingPlanClientMockRecorder) BatchGetWeddingRole(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetWeddingRole", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).BatchGetWeddingRole), varargs...)
}

// BatchHotWeddingPlan mocks base method.
func (m *MockChannelWeddingPlanClient) BatchHotWeddingPlan(ctx context.Context, in *BatchHotWeddingPlanRequest, opts ...grpc.CallOption) (*BatchHotWeddingPlanResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchHotWeddingPlan", varargs...)
	ret0, _ := ret[0].(*BatchHotWeddingPlanResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchHotWeddingPlan indicates an expected call of BatchHotWeddingPlan.
func (mr *MockChannelWeddingPlanClientMockRecorder) BatchHotWeddingPlan(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchHotWeddingPlan", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).BatchHotWeddingPlan), varargs...)
}

// BuyWedding mocks base method.
func (m *MockChannelWeddingPlanClient) BuyWedding(ctx context.Context, in *BuyWeddingRequest, opts ...grpc.CallOption) (*BuyWeddingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BuyWedding", varargs...)
	ret0, _ := ret[0].(*BuyWeddingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BuyWedding indicates an expected call of BuyWedding.
func (mr *MockChannelWeddingPlanClientMockRecorder) BuyWedding(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BuyWedding", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).BuyWedding), varargs...)
}

// CancelEndWeddingRelation mocks base method.
func (m *MockChannelWeddingPlanClient) CancelEndWeddingRelation(ctx context.Context, in *CancelEndWeddingRelationRequest, opts ...grpc.CallOption) (*CancelEndWeddingRelationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CancelEndWeddingRelation", varargs...)
	ret0, _ := ret[0].(*CancelEndWeddingRelationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancelEndWeddingRelation indicates an expected call of CancelEndWeddingRelation.
func (mr *MockChannelWeddingPlanClientMockRecorder) CancelEndWeddingRelation(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelEndWeddingRelation", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).CancelEndWeddingRelation), varargs...)
}

// CancelWedding mocks base method.
func (m *MockChannelWeddingPlanClient) CancelWedding(ctx context.Context, in *CancelWeddingRequest, opts ...grpc.CallOption) (*CancelWeddingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CancelWedding", varargs...)
	ret0, _ := ret[0].(*CancelWeddingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancelWedding indicates an expected call of CancelWedding.
func (mr *MockChannelWeddingPlanClientMockRecorder) CancelWedding(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelWedding", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).CancelWedding), varargs...)
}

// ChangeWeddingReserveInfo mocks base method.
func (m *MockChannelWeddingPlanClient) ChangeWeddingReserveInfo(ctx context.Context, in *ChangeWeddingReserveRequest, opts ...grpc.CallOption) (*ChangeWeddingReserveInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ChangeWeddingReserveInfo", varargs...)
	ret0, _ := ret[0].(*ChangeWeddingReserveInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChangeWeddingReserveInfo indicates an expected call of ChangeWeddingReserveInfo.
func (mr *MockChannelWeddingPlanClientMockRecorder) ChangeWeddingReserveInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChangeWeddingReserveInfo", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).ChangeWeddingReserveInfo), varargs...)
}

// ConsultWeddingReserve mocks base method.
func (m *MockChannelWeddingPlanClient) ConsultWeddingReserve(ctx context.Context, in *ConsultWeddingReserveRequest, opts ...grpc.CallOption) (*ConsultWeddingReserveResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ConsultWeddingReserve", varargs...)
	ret0, _ := ret[0].(*ConsultWeddingReserveResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConsultWeddingReserve indicates an expected call of ConsultWeddingReserve.
func (mr *MockChannelWeddingPlanClientMockRecorder) ConsultWeddingReserve(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConsultWeddingReserve", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).ConsultWeddingReserve), varargs...)
}

// DelWeddingGuest mocks base method.
func (m *MockChannelWeddingPlanClient) DelWeddingGuest(ctx context.Context, in *DelWeddingGuestRequest, opts ...grpc.CallOption) (*DelWeddingGuestResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelWeddingGuest", varargs...)
	ret0, _ := ret[0].(*DelWeddingGuestResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelWeddingGuest indicates an expected call of DelWeddingGuest.
func (mr *MockChannelWeddingPlanClientMockRecorder) DelWeddingGuest(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelWeddingGuest", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).DelWeddingGuest), varargs...)
}

// DeleteThemeCfg mocks base method.
func (m *MockChannelWeddingPlanClient) DeleteThemeCfg(ctx context.Context, in *DeleteThemeCfgRequest, opts ...grpc.CallOption) (*DeleteThemeCfgResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteThemeCfg", varargs...)
	ret0, _ := ret[0].(*DeleteThemeCfgResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteThemeCfg indicates an expected call of DeleteThemeCfg.
func (mr *MockChannelWeddingPlanClientMockRecorder) DeleteThemeCfg(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteThemeCfg", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).DeleteThemeCfg), varargs...)
}

// Divorce mocks base method.
func (m *MockChannelWeddingPlanClient) Divorce(ctx context.Context, in *DivorceRequest, opts ...grpc.CallOption) (*DivorceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Divorce", varargs...)
	ret0, _ := ret[0].(*DivorceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Divorce indicates an expected call of Divorce.
func (mr *MockChannelWeddingPlanClientMockRecorder) Divorce(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Divorce", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).Divorce), varargs...)
}

// GetAllThemeCfg mocks base method.
func (m *MockChannelWeddingPlanClient) GetAllThemeCfg(ctx context.Context, in *GetAllThemeCfgRequest, opts ...grpc.CallOption) (*GetAllThemeCfgResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllThemeCfg", varargs...)
	ret0, _ := ret[0].(*GetAllThemeCfgResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllThemeCfg indicates an expected call of GetAllThemeCfg.
func (mr *MockChannelWeddingPlanClientMockRecorder) GetAllThemeCfg(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllThemeCfg", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).GetAllThemeCfg), varargs...)
}

// GetChannelReserveTimeSectionConf mocks base method.
func (m *MockChannelWeddingPlanClient) GetChannelReserveTimeSectionConf(ctx context.Context, in *GetChannelReserveTimeSectionConfRequest, opts ...grpc.CallOption) (*GetChannelReserveTimeSectionConfResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelReserveTimeSectionConf", varargs...)
	ret0, _ := ret[0].(*GetChannelReserveTimeSectionConfResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelReserveTimeSectionConf indicates an expected call of GetChannelReserveTimeSectionConf.
func (mr *MockChannelWeddingPlanClientMockRecorder) GetChannelReserveTimeSectionConf(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelReserveTimeSectionConf", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).GetChannelReserveTimeSectionConf), varargs...)
}

// GetChannelReservedInfo mocks base method.
func (m *MockChannelWeddingPlanClient) GetChannelReservedInfo(ctx context.Context, in *GetChannelReservedInfoRequest, opts ...grpc.CallOption) (*GetChannelReservedInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelReservedInfo", varargs...)
	ret0, _ := ret[0].(*GetChannelReservedInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelReservedInfo indicates an expected call of GetChannelReservedInfo.
func (mr *MockChannelWeddingPlanClientMockRecorder) GetChannelReservedInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelReservedInfo", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).GetChannelReservedInfo), varargs...)
}

// GetGroomsmanAndBridesmaidInfo mocks base method.
func (m *MockChannelWeddingPlanClient) GetGroomsmanAndBridesmaidInfo(ctx context.Context, in *GetGroomsmanAndBridesmaidInfoRequest, opts ...grpc.CallOption) (*GetGroomsmanAndBridesmaidInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGroomsmanAndBridesmaidInfo", varargs...)
	ret0, _ := ret[0].(*GetGroomsmanAndBridesmaidInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGroomsmanAndBridesmaidInfo indicates an expected call of GetGroomsmanAndBridesmaidInfo.
func (mr *MockChannelWeddingPlanClientMockRecorder) GetGroomsmanAndBridesmaidInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroomsmanAndBridesmaidInfo", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).GetGroomsmanAndBridesmaidInfo), varargs...)
}

// GetMarriageStatus mocks base method.
func (m *MockChannelWeddingPlanClient) GetMarriageStatus(ctx context.Context, in *GetMarriageStatusRequest, opts ...grpc.CallOption) (*GetMarriageStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMarriageStatus", varargs...)
	ret0, _ := ret[0].(*GetMarriageStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMarriageStatus indicates an expected call of GetMarriageStatus.
func (mr *MockChannelWeddingPlanClientMockRecorder) GetMarriageStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMarriageStatus", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).GetMarriageStatus), varargs...)
}

// GetMyWeddingInfo mocks base method.
func (m *MockChannelWeddingPlanClient) GetMyWeddingInfo(ctx context.Context, in *GetMyWeddingInfoRequest, opts ...grpc.CallOption) (*GetMyWeddingInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMyWeddingInfo", varargs...)
	ret0, _ := ret[0].(*GetMyWeddingInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMyWeddingInfo indicates an expected call of GetMyWeddingInfo.
func (mr *MockChannelWeddingPlanClientMockRecorder) GetMyWeddingInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMyWeddingInfo", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).GetMyWeddingInfo), varargs...)
}

// GetMyWeddingReserveInfo mocks base method.
func (m *MockChannelWeddingPlanClient) GetMyWeddingReserveInfo(ctx context.Context, in *GetMyWeddingReserveInfoRequest, opts ...grpc.CallOption) (*GetMyWeddingReserveInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMyWeddingReserveInfo", varargs...)
	ret0, _ := ret[0].(*GetMyWeddingReserveInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMyWeddingReserveInfo indicates an expected call of GetMyWeddingReserveInfo.
func (mr *MockChannelWeddingPlanClientMockRecorder) GetMyWeddingReserveInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMyWeddingReserveInfo", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).GetMyWeddingReserveInfo), varargs...)
}

// GetMyWeddingRole mocks base method.
func (m *MockChannelWeddingPlanClient) GetMyWeddingRole(ctx context.Context, in *GetMyWeddingRoleRequest, opts ...grpc.CallOption) (*GetMyWeddingRoleResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMyWeddingRole", varargs...)
	ret0, _ := ret[0].(*GetMyWeddingRoleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMyWeddingRole indicates an expected call of GetMyWeddingRole.
func (mr *MockChannelWeddingPlanClientMockRecorder) GetMyWeddingRole(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMyWeddingRole", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).GetMyWeddingRole), varargs...)
}

// GetProposeById mocks base method.
func (m *MockChannelWeddingPlanClient) GetProposeById(ctx context.Context, in *GetProposeByIdRequest, opts ...grpc.CallOption) (*GetProposeByIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetProposeById", varargs...)
	ret0, _ := ret[0].(*GetProposeByIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProposeById indicates an expected call of GetProposeById.
func (mr *MockChannelWeddingPlanClientMockRecorder) GetProposeById(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProposeById", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).GetProposeById), varargs...)
}

// GetProposeList mocks base method.
func (m *MockChannelWeddingPlanClient) GetProposeList(ctx context.Context, in *GetProposeListRequest, opts ...grpc.CallOption) (*GetProposeListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetProposeList", varargs...)
	ret0, _ := ret[0].(*GetProposeListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProposeList indicates an expected call of GetProposeList.
func (mr *MockChannelWeddingPlanClientMockRecorder) GetProposeList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProposeList", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).GetProposeList), varargs...)
}

// GetReservedWedding mocks base method.
func (m *MockChannelWeddingPlanClient) GetReservedWedding(ctx context.Context, in *GetReservedWeddingRequest, opts ...grpc.CallOption) (*GetReservedWeddingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetReservedWedding", varargs...)
	ret0, _ := ret[0].(*GetReservedWeddingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReservedWedding indicates an expected call of GetReservedWedding.
func (mr *MockChannelWeddingPlanClientMockRecorder) GetReservedWedding(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReservedWedding", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).GetReservedWedding), varargs...)
}

// GetSendPropose mocks base method.
func (m *MockChannelWeddingPlanClient) GetSendPropose(ctx context.Context, in *GetSendProposeRequest, opts ...grpc.CallOption) (*GetSendProposeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSendPropose", varargs...)
	ret0, _ := ret[0].(*GetSendProposeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSendPropose indicates an expected call of GetSendPropose.
func (mr *MockChannelWeddingPlanClientMockRecorder) GetSendPropose(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSendPropose", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).GetSendPropose), varargs...)
}

// GetSimpleWeddingPlanInfo mocks base method.
func (m *MockChannelWeddingPlanClient) GetSimpleWeddingPlanInfo(ctx context.Context, in *GetSimpleWeddingPlanInfoRequest, opts ...grpc.CallOption) (*GetSimpleWeddingPlanInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSimpleWeddingPlanInfo", varargs...)
	ret0, _ := ret[0].(*GetSimpleWeddingPlanInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSimpleWeddingPlanInfo indicates an expected call of GetSimpleWeddingPlanInfo.
func (mr *MockChannelWeddingPlanClientMockRecorder) GetSimpleWeddingPlanInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSimpleWeddingPlanInfo", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).GetSimpleWeddingPlanInfo), varargs...)
}

// GetTBeanOrderIds mocks base method.
func (m *MockChannelWeddingPlanClient) GetTBeanOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTBeanOrderIds", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTBeanOrderIds indicates an expected call of GetTBeanOrderIds.
func (mr *MockChannelWeddingPlanClientMockRecorder) GetTBeanOrderIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTBeanOrderIds", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).GetTBeanOrderIds), varargs...)
}

// GetTBeanTotalCount mocks base method.
func (m *MockChannelWeddingPlanClient) GetTBeanTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTBeanTotalCount", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTBeanTotalCount indicates an expected call of GetTBeanTotalCount.
func (mr *MockChannelWeddingPlanClientMockRecorder) GetTBeanTotalCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTBeanTotalCount", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).GetTBeanTotalCount), varargs...)
}

// GetThemeCfgById mocks base method.
func (m *MockChannelWeddingPlanClient) GetThemeCfgById(ctx context.Context, in *GetThemeCfgByIdRequest, opts ...grpc.CallOption) (*GetThemeCfgByIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetThemeCfgById", varargs...)
	ret0, _ := ret[0].(*GetThemeCfgByIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetThemeCfgById indicates an expected call of GetThemeCfgById.
func (mr *MockChannelWeddingPlanClientMockRecorder) GetThemeCfgById(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetThemeCfgById", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).GetThemeCfgById), varargs...)
}

// GetTodayAllComingWeddingList mocks base method.
func (m *MockChannelWeddingPlanClient) GetTodayAllComingWeddingList(ctx context.Context, in *GetTodayAllComingWeddingListRequest, opts ...grpc.CallOption) (*GetTodayAllComingWeddingListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTodayAllComingWeddingList", varargs...)
	ret0, _ := ret[0].(*GetTodayAllComingWeddingListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTodayAllComingWeddingList indicates an expected call of GetTodayAllComingWeddingList.
func (mr *MockChannelWeddingPlanClientMockRecorder) GetTodayAllComingWeddingList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTodayAllComingWeddingList", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).GetTodayAllComingWeddingList), varargs...)
}

// GetUserDivorceStatus mocks base method.
func (m *MockChannelWeddingPlanClient) GetUserDivorceStatus(ctx context.Context, in *GetUserDivorceStatusRequest, opts ...grpc.CallOption) (*GetUserDivorceStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserDivorceStatus", varargs...)
	ret0, _ := ret[0].(*GetUserDivorceStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserDivorceStatus indicates an expected call of GetUserDivorceStatus.
func (mr *MockChannelWeddingPlanClientMockRecorder) GetUserDivorceStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserDivorceStatus", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).GetUserDivorceStatus), varargs...)
}

// GetWeddingBigScreen mocks base method.
func (m *MockChannelWeddingPlanClient) GetWeddingBigScreen(ctx context.Context, in *GetWeddingBigScreenRequest, opts ...grpc.CallOption) (*GetWeddingBigScreenResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetWeddingBigScreen", varargs...)
	ret0, _ := ret[0].(*GetWeddingBigScreenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingBigScreen indicates an expected call of GetWeddingBigScreen.
func (mr *MockChannelWeddingPlanClientMockRecorder) GetWeddingBigScreen(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingBigScreen", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).GetWeddingBigScreen), varargs...)
}

// GetWeddingFriendInfo mocks base method.
func (m *MockChannelWeddingPlanClient) GetWeddingFriendInfo(ctx context.Context, in *GetWeddingFriendInfoRequest, opts ...grpc.CallOption) (*GetWeddingFriendInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetWeddingFriendInfo", varargs...)
	ret0, _ := ret[0].(*GetWeddingFriendInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingFriendInfo indicates an expected call of GetWeddingFriendInfo.
func (mr *MockChannelWeddingPlanClientMockRecorder) GetWeddingFriendInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingFriendInfo", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).GetWeddingFriendInfo), varargs...)
}

// GetWeddingInviteInfo mocks base method.
func (m *MockChannelWeddingPlanClient) GetWeddingInviteInfo(ctx context.Context, in *GetWeddingInviteInfoRequest, opts ...grpc.CallOption) (*GetWeddingInviteInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetWeddingInviteInfo", varargs...)
	ret0, _ := ret[0].(*GetWeddingInviteInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingInviteInfo indicates an expected call of GetWeddingInviteInfo.
func (mr *MockChannelWeddingPlanClientMockRecorder) GetWeddingInviteInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingInviteInfo", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).GetWeddingInviteInfo), varargs...)
}

// GetWeddingPlanBaseInfo mocks base method.
func (m *MockChannelWeddingPlanClient) GetWeddingPlanBaseInfo(ctx context.Context, in *GetWeddingPlanBaseInfoRequest, opts ...grpc.CallOption) (*GetWeddingPlanBaseInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetWeddingPlanBaseInfo", varargs...)
	ret0, _ := ret[0].(*GetWeddingPlanBaseInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingPlanBaseInfo indicates an expected call of GetWeddingPlanBaseInfo.
func (mr *MockChannelWeddingPlanClientMockRecorder) GetWeddingPlanBaseInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingPlanBaseInfo", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).GetWeddingPlanBaseInfo), varargs...)
}

// GetWeddingReserveInfo mocks base method.
func (m *MockChannelWeddingPlanClient) GetWeddingReserveInfo(ctx context.Context, in *GetWeddingReserveInfoRequest, opts ...grpc.CallOption) (*GetWeddingReserveInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetWeddingReserveInfo", varargs...)
	ret0, _ := ret[0].(*GetWeddingReserveInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingReserveInfo indicates an expected call of GetWeddingReserveInfo.
func (mr *MockChannelWeddingPlanClientMockRecorder) GetWeddingReserveInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingReserveInfo", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).GetWeddingReserveInfo), varargs...)
}

// HandlePropose mocks base method.
func (m *MockChannelWeddingPlanClient) HandlePropose(ctx context.Context, in *HandleProposeRequest, opts ...grpc.CallOption) (*HandleProposeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HandlePropose", varargs...)
	ret0, _ := ret[0].(*HandleProposeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandlePropose indicates an expected call of HandlePropose.
func (mr *MockChannelWeddingPlanClientMockRecorder) HandlePropose(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandlePropose", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).HandlePropose), varargs...)
}

// HandleWeddingInvite mocks base method.
func (m *MockChannelWeddingPlanClient) HandleWeddingInvite(ctx context.Context, in *HandleWeddingInviteRequest, opts ...grpc.CallOption) (*HandleWeddingInviteResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HandleWeddingInvite", varargs...)
	ret0, _ := ret[0].(*HandleWeddingInviteResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandleWeddingInvite indicates an expected call of HandleWeddingInvite.
func (mr *MockChannelWeddingPlanClientMockRecorder) HandleWeddingInvite(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleWeddingInvite", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).HandleWeddingInvite), varargs...)
}

// InviteWeddingGuest mocks base method.
func (m *MockChannelWeddingPlanClient) InviteWeddingGuest(ctx context.Context, in *InviteWeddingGuestRequest, opts ...grpc.CallOption) (*InviteWeddingGuestResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InviteWeddingGuest", varargs...)
	ret0, _ := ret[0].(*InviteWeddingGuestResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InviteWeddingGuest indicates an expected call of InviteWeddingGuest.
func (mr *MockChannelWeddingPlanClientMockRecorder) InviteWeddingGuest(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InviteWeddingGuest", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).InviteWeddingGuest), varargs...)
}

// ManualNotifyWeddingStart mocks base method.
func (m *MockChannelWeddingPlanClient) ManualNotifyWeddingStart(ctx context.Context, in *ManualNotifyWeddingStartRequest, opts ...grpc.CallOption) (*ManualNotifyWeddingStartResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ManualNotifyWeddingStart", varargs...)
	ret0, _ := ret[0].(*ManualNotifyWeddingStartResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ManualNotifyWeddingStart indicates an expected call of ManualNotifyWeddingStart.
func (mr *MockChannelWeddingPlanClientMockRecorder) ManualNotifyWeddingStart(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ManualNotifyWeddingStart", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).ManualNotifyWeddingStart), varargs...)
}

// PageGetComingWeddingList mocks base method.
func (m *MockChannelWeddingPlanClient) PageGetComingWeddingList(ctx context.Context, in *PageGetComingWeddingListRequest, opts ...grpc.CallOption) (*PageGetComingWeddingListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PageGetComingWeddingList", varargs...)
	ret0, _ := ret[0].(*PageGetComingWeddingListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PageGetComingWeddingList indicates an expected call of PageGetComingWeddingList.
func (mr *MockChannelWeddingPlanClientMockRecorder) PageGetComingWeddingList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PageGetComingWeddingList", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).PageGetComingWeddingList), varargs...)
}

// RevokePropose mocks base method.
func (m *MockChannelWeddingPlanClient) RevokePropose(ctx context.Context, in *RevokeProposeRequest, opts ...grpc.CallOption) (*RevokeProposeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RevokePropose", varargs...)
	ret0, _ := ret[0].(*RevokeProposeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RevokePropose indicates an expected call of RevokePropose.
func (mr *MockChannelWeddingPlanClientMockRecorder) RevokePropose(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RevokePropose", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).RevokePropose), varargs...)
}

// SaveWeddingBigScreen mocks base method.
func (m *MockChannelWeddingPlanClient) SaveWeddingBigScreen(ctx context.Context, in *SaveWeddingBigScreenRequest, opts ...grpc.CallOption) (*SaveWeddingBigScreenResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SaveWeddingBigScreen", varargs...)
	ret0, _ := ret[0].(*SaveWeddingBigScreenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveWeddingBigScreen indicates an expected call of SaveWeddingBigScreen.
func (mr *MockChannelWeddingPlanClientMockRecorder) SaveWeddingBigScreen(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveWeddingBigScreen", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).SaveWeddingBigScreen), varargs...)
}

// SaveWeddingReserveInfo mocks base method.
func (m *MockChannelWeddingPlanClient) SaveWeddingReserveInfo(ctx context.Context, in *SaveWeddingReserveRequest, opts ...grpc.CallOption) (*SaveWeddingReserveInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SaveWeddingReserveInfo", varargs...)
	ret0, _ := ret[0].(*SaveWeddingReserveInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveWeddingReserveInfo indicates an expected call of SaveWeddingReserveInfo.
func (mr *MockChannelWeddingPlanClientMockRecorder) SaveWeddingReserveInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveWeddingReserveInfo", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).SaveWeddingReserveInfo), varargs...)
}

// SendPropose mocks base method.
func (m *MockChannelWeddingPlanClient) SendPropose(ctx context.Context, in *SendProposeRequest, opts ...grpc.CallOption) (*SendProposeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendPropose", varargs...)
	ret0, _ := ret[0].(*SendProposeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendPropose indicates an expected call of SendPropose.
func (mr *MockChannelWeddingPlanClientMockRecorder) SendPropose(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendPropose", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).SendPropose), varargs...)
}

// SetAdminChannelReserveTimeSectionSwitch mocks base method.
func (m *MockChannelWeddingPlanClient) SetAdminChannelReserveTimeSectionSwitch(ctx context.Context, in *SetAdminChannelReserveTimeSectionSwitchRequest, opts ...grpc.CallOption) (*SetAdminChannelReserveTimeSectionSwitchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetAdminChannelReserveTimeSectionSwitch", varargs...)
	ret0, _ := ret[0].(*SetAdminChannelReserveTimeSectionSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetAdminChannelReserveTimeSectionSwitch indicates an expected call of SetAdminChannelReserveTimeSectionSwitch.
func (mr *MockChannelWeddingPlanClientMockRecorder) SetAdminChannelReserveTimeSectionSwitch(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAdminChannelReserveTimeSectionSwitch", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).SetAdminChannelReserveTimeSectionSwitch), varargs...)
}

// SetUserRelationHideStatus mocks base method.
func (m *MockChannelWeddingPlanClient) SetUserRelationHideStatus(ctx context.Context, in *SetUserRelationHideStatusRequest, opts ...grpc.CallOption) (*SetUserRelationHideStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetUserRelationHideStatus", varargs...)
	ret0, _ := ret[0].(*SetUserRelationHideStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserRelationHideStatus indicates an expected call of SetUserRelationHideStatus.
func (mr *MockChannelWeddingPlanClientMockRecorder) SetUserRelationHideStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserRelationHideStatus", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).SetUserRelationHideStatus), varargs...)
}

// SetWeddingHost mocks base method.
func (m *MockChannelWeddingPlanClient) SetWeddingHost(ctx context.Context, in *SetWeddingHostRequest, opts ...grpc.CallOption) (*SetWeddingHostResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetWeddingHost", varargs...)
	ret0, _ := ret[0].(*SetWeddingHostResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetWeddingHost indicates an expected call of SetWeddingHost.
func (mr *MockChannelWeddingPlanClientMockRecorder) SetWeddingHost(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetWeddingHost", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).SetWeddingHost), varargs...)
}

// SubscribeWedding mocks base method.
func (m *MockChannelWeddingPlanClient) SubscribeWedding(ctx context.Context, in *SubscribeWeddingRequest, opts ...grpc.CallOption) (*SubscribeWeddingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SubscribeWedding", varargs...)
	ret0, _ := ret[0].(*SubscribeWeddingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubscribeWedding indicates an expected call of SubscribeWedding.
func (mr *MockChannelWeddingPlanClientMockRecorder) SubscribeWedding(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubscribeWedding", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).SubscribeWedding), varargs...)
}

// TestWeddingAnniversaryPopup mocks base method.
func (m *MockChannelWeddingPlanClient) TestWeddingAnniversaryPopup(ctx context.Context, in *TestWeddingAnniversaryPopupRequest, opts ...grpc.CallOption) (*TestWeddingAnniversaryPopupResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TestWeddingAnniversaryPopup", varargs...)
	ret0, _ := ret[0].(*TestWeddingAnniversaryPopupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestWeddingAnniversaryPopup indicates an expected call of TestWeddingAnniversaryPopup.
func (mr *MockChannelWeddingPlanClientMockRecorder) TestWeddingAnniversaryPopup(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestWeddingAnniversaryPopup", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).TestWeddingAnniversaryPopup), varargs...)
}

// UpdateThemeCfg mocks base method.
func (m *MockChannelWeddingPlanClient) UpdateThemeCfg(ctx context.Context, in *UpdateThemeCfgRequest, opts ...grpc.CallOption) (*UpdateThemeCfgResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateThemeCfg", varargs...)
	ret0, _ := ret[0].(*UpdateThemeCfgResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateThemeCfg indicates an expected call of UpdateThemeCfg.
func (mr *MockChannelWeddingPlanClientMockRecorder) UpdateThemeCfg(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateThemeCfg", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).UpdateThemeCfg), varargs...)
}

// UpdateWeddingBigScreenReviewStatus mocks base method.
func (m *MockChannelWeddingPlanClient) UpdateWeddingBigScreenReviewStatus(ctx context.Context, in *UpdateWeddingBigScreenReviewStatusRequest, opts ...grpc.CallOption) (*UpdateWeddingBigScreenReviewStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateWeddingBigScreenReviewStatus", varargs...)
	ret0, _ := ret[0].(*UpdateWeddingBigScreenReviewStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateWeddingBigScreenReviewStatus indicates an expected call of UpdateWeddingBigScreenReviewStatus.
func (mr *MockChannelWeddingPlanClientMockRecorder) UpdateWeddingBigScreenReviewStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWeddingBigScreenReviewStatus", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).UpdateWeddingBigScreenReviewStatus), varargs...)
}

// UpdateWeddingPlanStatus mocks base method.
func (m *MockChannelWeddingPlanClient) UpdateWeddingPlanStatus(ctx context.Context, in *UpdateWeddingPlanStatusRequest, opts ...grpc.CallOption) (*UpdateWeddingPlanStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateWeddingPlanStatus", varargs...)
	ret0, _ := ret[0].(*UpdateWeddingPlanStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateWeddingPlanStatus indicates an expected call of UpdateWeddingPlanStatus.
func (mr *MockChannelWeddingPlanClientMockRecorder) UpdateWeddingPlanStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWeddingPlanStatus", reflect.TypeOf((*MockChannelWeddingPlanClient)(nil).UpdateWeddingPlanStatus), varargs...)
}

// MockChannelWeddingPlanServer is a mock of ChannelWeddingPlanServer interface.
type MockChannelWeddingPlanServer struct {
	ctrl     *gomock.Controller
	recorder *MockChannelWeddingPlanServerMockRecorder
}

// MockChannelWeddingPlanServerMockRecorder is the mock recorder for MockChannelWeddingPlanServer.
type MockChannelWeddingPlanServerMockRecorder struct {
	mock *MockChannelWeddingPlanServer
}

// NewMockChannelWeddingPlanServer creates a new mock instance.
func NewMockChannelWeddingPlanServer(ctrl *gomock.Controller) *MockChannelWeddingPlanServer {
	mock := &MockChannelWeddingPlanServer{ctrl: ctrl}
	mock.recorder = &MockChannelWeddingPlanServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChannelWeddingPlanServer) EXPECT() *MockChannelWeddingPlanServerMockRecorder {
	return m.recorder
}

// AddThemeCfg mocks base method.
func (m *MockChannelWeddingPlanServer) AddThemeCfg(ctx context.Context, in *AddThemeCfgRequest) (*AddThemeCfgResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddThemeCfg", ctx, in)
	ret0, _ := ret[0].(*AddThemeCfgResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddThemeCfg indicates an expected call of AddThemeCfg.
func (mr *MockChannelWeddingPlanServerMockRecorder) AddThemeCfg(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddThemeCfg", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).AddThemeCfg), ctx, in)
}

// AdminCancelWedding mocks base method.
func (m *MockChannelWeddingPlanServer) AdminCancelWedding(ctx context.Context, in *AdminCancelWeddingRequest) (*AdminCancelWeddingResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AdminCancelWedding", ctx, in)
	ret0, _ := ret[0].(*AdminCancelWeddingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AdminCancelWedding indicates an expected call of AdminCancelWedding.
func (mr *MockChannelWeddingPlanServerMockRecorder) AdminCancelWedding(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AdminCancelWedding", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).AdminCancelWedding), ctx, in)
}

// ApplyEndWeddingRelation mocks base method.
func (m *MockChannelWeddingPlanServer) ApplyEndWeddingRelation(ctx context.Context, in *ApplyEndWeddingRelationRequest) (*ApplyEndWeddingRelationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplyEndWeddingRelation", ctx, in)
	ret0, _ := ret[0].(*ApplyEndWeddingRelationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplyEndWeddingRelation indicates an expected call of ApplyEndWeddingRelation.
func (mr *MockChannelWeddingPlanServerMockRecorder) ApplyEndWeddingRelation(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplyEndWeddingRelation", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).ApplyEndWeddingRelation), ctx, in)
}

// ArrangeWeddingReserve mocks base method.
func (m *MockChannelWeddingPlanServer) ArrangeWeddingReserve(ctx context.Context, in *ArrangeWeddingReserveRequest) (*ArrangeWeddingReserveResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ArrangeWeddingReserve", ctx, in)
	ret0, _ := ret[0].(*ArrangeWeddingReserveResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ArrangeWeddingReserve indicates an expected call of ArrangeWeddingReserve.
func (mr *MockChannelWeddingPlanServerMockRecorder) ArrangeWeddingReserve(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ArrangeWeddingReserve", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).ArrangeWeddingReserve), ctx, in)
}

// BatGetWeddingInfoById mocks base method.
func (m *MockChannelWeddingPlanServer) BatGetWeddingInfoById(ctx context.Context, in *BatGetWeddingInfoByIdRequest) (*BatGetWeddingInfoByIdResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetWeddingInfoById", ctx, in)
	ret0, _ := ret[0].(*BatGetWeddingInfoByIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetWeddingInfoById indicates an expected call of BatGetWeddingInfoById.
func (mr *MockChannelWeddingPlanServerMockRecorder) BatGetWeddingInfoById(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetWeddingInfoById", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).BatGetWeddingInfoById), ctx, in)
}

// BatGetWeddingSubscribeStatus mocks base method.
func (m *MockChannelWeddingPlanServer) BatGetWeddingSubscribeStatus(ctx context.Context, in *BatGetWeddingSubscribeStatusRequest) (*BatGetWeddingSubscribeStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetWeddingSubscribeStatus", ctx, in)
	ret0, _ := ret[0].(*BatGetWeddingSubscribeStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetWeddingSubscribeStatus indicates an expected call of BatGetWeddingSubscribeStatus.
func (mr *MockChannelWeddingPlanServerMockRecorder) BatGetWeddingSubscribeStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetWeddingSubscribeStatus", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).BatGetWeddingSubscribeStatus), ctx, in)
}

// BatchGetMarriageInfo mocks base method.
func (m *MockChannelWeddingPlanServer) BatchGetMarriageInfo(ctx context.Context, in *BatchGetMarriageInfoRequest) (*BatchGetMarriageInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetMarriageInfo", ctx, in)
	ret0, _ := ret[0].(*BatchGetMarriageInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetMarriageInfo indicates an expected call of BatchGetMarriageInfo.
func (mr *MockChannelWeddingPlanServerMockRecorder) BatchGetMarriageInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetMarriageInfo", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).BatchGetMarriageInfo), ctx, in)
}

// BatchGetWeddingRole mocks base method.
func (m *MockChannelWeddingPlanServer) BatchGetWeddingRole(ctx context.Context, in *BatchGetWeddingRoleRequest) (*BatchGetWeddingRoleResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetWeddingRole", ctx, in)
	ret0, _ := ret[0].(*BatchGetWeddingRoleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetWeddingRole indicates an expected call of BatchGetWeddingRole.
func (mr *MockChannelWeddingPlanServerMockRecorder) BatchGetWeddingRole(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetWeddingRole", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).BatchGetWeddingRole), ctx, in)
}

// BatchHotWeddingPlan mocks base method.
func (m *MockChannelWeddingPlanServer) BatchHotWeddingPlan(ctx context.Context, in *BatchHotWeddingPlanRequest) (*BatchHotWeddingPlanResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchHotWeddingPlan", ctx, in)
	ret0, _ := ret[0].(*BatchHotWeddingPlanResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchHotWeddingPlan indicates an expected call of BatchHotWeddingPlan.
func (mr *MockChannelWeddingPlanServerMockRecorder) BatchHotWeddingPlan(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchHotWeddingPlan", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).BatchHotWeddingPlan), ctx, in)
}

// BuyWedding mocks base method.
func (m *MockChannelWeddingPlanServer) BuyWedding(ctx context.Context, in *BuyWeddingRequest) (*BuyWeddingResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BuyWedding", ctx, in)
	ret0, _ := ret[0].(*BuyWeddingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BuyWedding indicates an expected call of BuyWedding.
func (mr *MockChannelWeddingPlanServerMockRecorder) BuyWedding(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BuyWedding", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).BuyWedding), ctx, in)
}

// CancelEndWeddingRelation mocks base method.
func (m *MockChannelWeddingPlanServer) CancelEndWeddingRelation(ctx context.Context, in *CancelEndWeddingRelationRequest) (*CancelEndWeddingRelationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelEndWeddingRelation", ctx, in)
	ret0, _ := ret[0].(*CancelEndWeddingRelationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancelEndWeddingRelation indicates an expected call of CancelEndWeddingRelation.
func (mr *MockChannelWeddingPlanServerMockRecorder) CancelEndWeddingRelation(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelEndWeddingRelation", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).CancelEndWeddingRelation), ctx, in)
}

// CancelWedding mocks base method.
func (m *MockChannelWeddingPlanServer) CancelWedding(ctx context.Context, in *CancelWeddingRequest) (*CancelWeddingResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelWedding", ctx, in)
	ret0, _ := ret[0].(*CancelWeddingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancelWedding indicates an expected call of CancelWedding.
func (mr *MockChannelWeddingPlanServerMockRecorder) CancelWedding(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelWedding", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).CancelWedding), ctx, in)
}

// ChangeWeddingReserveInfo mocks base method.
func (m *MockChannelWeddingPlanServer) ChangeWeddingReserveInfo(ctx context.Context, in *ChangeWeddingReserveRequest) (*ChangeWeddingReserveInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChangeWeddingReserveInfo", ctx, in)
	ret0, _ := ret[0].(*ChangeWeddingReserveInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChangeWeddingReserveInfo indicates an expected call of ChangeWeddingReserveInfo.
func (mr *MockChannelWeddingPlanServerMockRecorder) ChangeWeddingReserveInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChangeWeddingReserveInfo", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).ChangeWeddingReserveInfo), ctx, in)
}

// ConsultWeddingReserve mocks base method.
func (m *MockChannelWeddingPlanServer) ConsultWeddingReserve(ctx context.Context, in *ConsultWeddingReserveRequest) (*ConsultWeddingReserveResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConsultWeddingReserve", ctx, in)
	ret0, _ := ret[0].(*ConsultWeddingReserveResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConsultWeddingReserve indicates an expected call of ConsultWeddingReserve.
func (mr *MockChannelWeddingPlanServerMockRecorder) ConsultWeddingReserve(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConsultWeddingReserve", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).ConsultWeddingReserve), ctx, in)
}

// DelWeddingGuest mocks base method.
func (m *MockChannelWeddingPlanServer) DelWeddingGuest(ctx context.Context, in *DelWeddingGuestRequest) (*DelWeddingGuestResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelWeddingGuest", ctx, in)
	ret0, _ := ret[0].(*DelWeddingGuestResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelWeddingGuest indicates an expected call of DelWeddingGuest.
func (mr *MockChannelWeddingPlanServerMockRecorder) DelWeddingGuest(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelWeddingGuest", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).DelWeddingGuest), ctx, in)
}

// DeleteThemeCfg mocks base method.
func (m *MockChannelWeddingPlanServer) DeleteThemeCfg(ctx context.Context, in *DeleteThemeCfgRequest) (*DeleteThemeCfgResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteThemeCfg", ctx, in)
	ret0, _ := ret[0].(*DeleteThemeCfgResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteThemeCfg indicates an expected call of DeleteThemeCfg.
func (mr *MockChannelWeddingPlanServerMockRecorder) DeleteThemeCfg(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteThemeCfg", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).DeleteThemeCfg), ctx, in)
}

// Divorce mocks base method.
func (m *MockChannelWeddingPlanServer) Divorce(ctx context.Context, in *DivorceRequest) (*DivorceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Divorce", ctx, in)
	ret0, _ := ret[0].(*DivorceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Divorce indicates an expected call of Divorce.
func (mr *MockChannelWeddingPlanServerMockRecorder) Divorce(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Divorce", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).Divorce), ctx, in)
}

// GetAllThemeCfg mocks base method.
func (m *MockChannelWeddingPlanServer) GetAllThemeCfg(ctx context.Context, in *GetAllThemeCfgRequest) (*GetAllThemeCfgResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllThemeCfg", ctx, in)
	ret0, _ := ret[0].(*GetAllThemeCfgResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllThemeCfg indicates an expected call of GetAllThemeCfg.
func (mr *MockChannelWeddingPlanServerMockRecorder) GetAllThemeCfg(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllThemeCfg", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).GetAllThemeCfg), ctx, in)
}

// GetChannelReserveTimeSectionConf mocks base method.
func (m *MockChannelWeddingPlanServer) GetChannelReserveTimeSectionConf(ctx context.Context, in *GetChannelReserveTimeSectionConfRequest) (*GetChannelReserveTimeSectionConfResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelReserveTimeSectionConf", ctx, in)
	ret0, _ := ret[0].(*GetChannelReserveTimeSectionConfResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelReserveTimeSectionConf indicates an expected call of GetChannelReserveTimeSectionConf.
func (mr *MockChannelWeddingPlanServerMockRecorder) GetChannelReserveTimeSectionConf(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelReserveTimeSectionConf", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).GetChannelReserveTimeSectionConf), ctx, in)
}

// GetChannelReservedInfo mocks base method.
func (m *MockChannelWeddingPlanServer) GetChannelReservedInfo(ctx context.Context, in *GetChannelReservedInfoRequest) (*GetChannelReservedInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelReservedInfo", ctx, in)
	ret0, _ := ret[0].(*GetChannelReservedInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelReservedInfo indicates an expected call of GetChannelReservedInfo.
func (mr *MockChannelWeddingPlanServerMockRecorder) GetChannelReservedInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelReservedInfo", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).GetChannelReservedInfo), ctx, in)
}

// GetGroomsmanAndBridesmaidInfo mocks base method.
func (m *MockChannelWeddingPlanServer) GetGroomsmanAndBridesmaidInfo(ctx context.Context, in *GetGroomsmanAndBridesmaidInfoRequest) (*GetGroomsmanAndBridesmaidInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGroomsmanAndBridesmaidInfo", ctx, in)
	ret0, _ := ret[0].(*GetGroomsmanAndBridesmaidInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGroomsmanAndBridesmaidInfo indicates an expected call of GetGroomsmanAndBridesmaidInfo.
func (mr *MockChannelWeddingPlanServerMockRecorder) GetGroomsmanAndBridesmaidInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroomsmanAndBridesmaidInfo", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).GetGroomsmanAndBridesmaidInfo), ctx, in)
}

// GetMarriageStatus mocks base method.
func (m *MockChannelWeddingPlanServer) GetMarriageStatus(ctx context.Context, in *GetMarriageStatusRequest) (*GetMarriageStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMarriageStatus", ctx, in)
	ret0, _ := ret[0].(*GetMarriageStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMarriageStatus indicates an expected call of GetMarriageStatus.
func (mr *MockChannelWeddingPlanServerMockRecorder) GetMarriageStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMarriageStatus", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).GetMarriageStatus), ctx, in)
}

// GetMyWeddingInfo mocks base method.
func (m *MockChannelWeddingPlanServer) GetMyWeddingInfo(ctx context.Context, in *GetMyWeddingInfoRequest) (*GetMyWeddingInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMyWeddingInfo", ctx, in)
	ret0, _ := ret[0].(*GetMyWeddingInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMyWeddingInfo indicates an expected call of GetMyWeddingInfo.
func (mr *MockChannelWeddingPlanServerMockRecorder) GetMyWeddingInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMyWeddingInfo", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).GetMyWeddingInfo), ctx, in)
}

// GetMyWeddingReserveInfo mocks base method.
func (m *MockChannelWeddingPlanServer) GetMyWeddingReserveInfo(ctx context.Context, in *GetMyWeddingReserveInfoRequest) (*GetMyWeddingReserveInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMyWeddingReserveInfo", ctx, in)
	ret0, _ := ret[0].(*GetMyWeddingReserveInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMyWeddingReserveInfo indicates an expected call of GetMyWeddingReserveInfo.
func (mr *MockChannelWeddingPlanServerMockRecorder) GetMyWeddingReserveInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMyWeddingReserveInfo", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).GetMyWeddingReserveInfo), ctx, in)
}

// GetMyWeddingRole mocks base method.
func (m *MockChannelWeddingPlanServer) GetMyWeddingRole(ctx context.Context, in *GetMyWeddingRoleRequest) (*GetMyWeddingRoleResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMyWeddingRole", ctx, in)
	ret0, _ := ret[0].(*GetMyWeddingRoleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMyWeddingRole indicates an expected call of GetMyWeddingRole.
func (mr *MockChannelWeddingPlanServerMockRecorder) GetMyWeddingRole(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMyWeddingRole", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).GetMyWeddingRole), ctx, in)
}

// GetProposeById mocks base method.
func (m *MockChannelWeddingPlanServer) GetProposeById(ctx context.Context, in *GetProposeByIdRequest) (*GetProposeByIdResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProposeById", ctx, in)
	ret0, _ := ret[0].(*GetProposeByIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProposeById indicates an expected call of GetProposeById.
func (mr *MockChannelWeddingPlanServerMockRecorder) GetProposeById(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProposeById", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).GetProposeById), ctx, in)
}

// GetProposeList mocks base method.
func (m *MockChannelWeddingPlanServer) GetProposeList(ctx context.Context, in *GetProposeListRequest) (*GetProposeListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProposeList", ctx, in)
	ret0, _ := ret[0].(*GetProposeListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProposeList indicates an expected call of GetProposeList.
func (mr *MockChannelWeddingPlanServerMockRecorder) GetProposeList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProposeList", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).GetProposeList), ctx, in)
}

// GetReservedWedding mocks base method.
func (m *MockChannelWeddingPlanServer) GetReservedWedding(ctx context.Context, in *GetReservedWeddingRequest) (*GetReservedWeddingResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReservedWedding", ctx, in)
	ret0, _ := ret[0].(*GetReservedWeddingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReservedWedding indicates an expected call of GetReservedWedding.
func (mr *MockChannelWeddingPlanServerMockRecorder) GetReservedWedding(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReservedWedding", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).GetReservedWedding), ctx, in)
}

// GetSendPropose mocks base method.
func (m *MockChannelWeddingPlanServer) GetSendPropose(ctx context.Context, in *GetSendProposeRequest) (*GetSendProposeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSendPropose", ctx, in)
	ret0, _ := ret[0].(*GetSendProposeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSendPropose indicates an expected call of GetSendPropose.
func (mr *MockChannelWeddingPlanServerMockRecorder) GetSendPropose(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSendPropose", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).GetSendPropose), ctx, in)
}

// GetSimpleWeddingPlanInfo mocks base method.
func (m *MockChannelWeddingPlanServer) GetSimpleWeddingPlanInfo(ctx context.Context, in *GetSimpleWeddingPlanInfoRequest) (*GetSimpleWeddingPlanInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSimpleWeddingPlanInfo", ctx, in)
	ret0, _ := ret[0].(*GetSimpleWeddingPlanInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSimpleWeddingPlanInfo indicates an expected call of GetSimpleWeddingPlanInfo.
func (mr *MockChannelWeddingPlanServerMockRecorder) GetSimpleWeddingPlanInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSimpleWeddingPlanInfo", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).GetSimpleWeddingPlanInfo), ctx, in)
}

// GetTBeanOrderIds mocks base method.
func (m *MockChannelWeddingPlanServer) GetTBeanOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTBeanOrderIds", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTBeanOrderIds indicates an expected call of GetTBeanOrderIds.
func (mr *MockChannelWeddingPlanServerMockRecorder) GetTBeanOrderIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTBeanOrderIds", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).GetTBeanOrderIds), ctx, in)
}

// GetTBeanTotalCount mocks base method.
func (m *MockChannelWeddingPlanServer) GetTBeanTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTBeanTotalCount", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTBeanTotalCount indicates an expected call of GetTBeanTotalCount.
func (mr *MockChannelWeddingPlanServerMockRecorder) GetTBeanTotalCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTBeanTotalCount", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).GetTBeanTotalCount), ctx, in)
}

// GetThemeCfgById mocks base method.
func (m *MockChannelWeddingPlanServer) GetThemeCfgById(ctx context.Context, in *GetThemeCfgByIdRequest) (*GetThemeCfgByIdResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetThemeCfgById", ctx, in)
	ret0, _ := ret[0].(*GetThemeCfgByIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetThemeCfgById indicates an expected call of GetThemeCfgById.
func (mr *MockChannelWeddingPlanServerMockRecorder) GetThemeCfgById(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetThemeCfgById", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).GetThemeCfgById), ctx, in)
}

// GetTodayAllComingWeddingList mocks base method.
func (m *MockChannelWeddingPlanServer) GetTodayAllComingWeddingList(ctx context.Context, in *GetTodayAllComingWeddingListRequest) (*GetTodayAllComingWeddingListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTodayAllComingWeddingList", ctx, in)
	ret0, _ := ret[0].(*GetTodayAllComingWeddingListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTodayAllComingWeddingList indicates an expected call of GetTodayAllComingWeddingList.
func (mr *MockChannelWeddingPlanServerMockRecorder) GetTodayAllComingWeddingList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTodayAllComingWeddingList", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).GetTodayAllComingWeddingList), ctx, in)
}

// GetUserDivorceStatus mocks base method.
func (m *MockChannelWeddingPlanServer) GetUserDivorceStatus(ctx context.Context, in *GetUserDivorceStatusRequest) (*GetUserDivorceStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserDivorceStatus", ctx, in)
	ret0, _ := ret[0].(*GetUserDivorceStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserDivorceStatus indicates an expected call of GetUserDivorceStatus.
func (mr *MockChannelWeddingPlanServerMockRecorder) GetUserDivorceStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserDivorceStatus", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).GetUserDivorceStatus), ctx, in)
}

// GetWeddingBigScreen mocks base method.
func (m *MockChannelWeddingPlanServer) GetWeddingBigScreen(ctx context.Context, in *GetWeddingBigScreenRequest) (*GetWeddingBigScreenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingBigScreen", ctx, in)
	ret0, _ := ret[0].(*GetWeddingBigScreenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingBigScreen indicates an expected call of GetWeddingBigScreen.
func (mr *MockChannelWeddingPlanServerMockRecorder) GetWeddingBigScreen(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingBigScreen", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).GetWeddingBigScreen), ctx, in)
}

// GetWeddingFriendInfo mocks base method.
func (m *MockChannelWeddingPlanServer) GetWeddingFriendInfo(ctx context.Context, in *GetWeddingFriendInfoRequest) (*GetWeddingFriendInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingFriendInfo", ctx, in)
	ret0, _ := ret[0].(*GetWeddingFriendInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingFriendInfo indicates an expected call of GetWeddingFriendInfo.
func (mr *MockChannelWeddingPlanServerMockRecorder) GetWeddingFriendInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingFriendInfo", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).GetWeddingFriendInfo), ctx, in)
}

// GetWeddingInviteInfo mocks base method.
func (m *MockChannelWeddingPlanServer) GetWeddingInviteInfo(ctx context.Context, in *GetWeddingInviteInfoRequest) (*GetWeddingInviteInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingInviteInfo", ctx, in)
	ret0, _ := ret[0].(*GetWeddingInviteInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingInviteInfo indicates an expected call of GetWeddingInviteInfo.
func (mr *MockChannelWeddingPlanServerMockRecorder) GetWeddingInviteInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingInviteInfo", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).GetWeddingInviteInfo), ctx, in)
}

// GetWeddingPlanBaseInfo mocks base method.
func (m *MockChannelWeddingPlanServer) GetWeddingPlanBaseInfo(ctx context.Context, in *GetWeddingPlanBaseInfoRequest) (*GetWeddingPlanBaseInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingPlanBaseInfo", ctx, in)
	ret0, _ := ret[0].(*GetWeddingPlanBaseInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingPlanBaseInfo indicates an expected call of GetWeddingPlanBaseInfo.
func (mr *MockChannelWeddingPlanServerMockRecorder) GetWeddingPlanBaseInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingPlanBaseInfo", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).GetWeddingPlanBaseInfo), ctx, in)
}

// GetWeddingReserveInfo mocks base method.
func (m *MockChannelWeddingPlanServer) GetWeddingReserveInfo(ctx context.Context, in *GetWeddingReserveInfoRequest) (*GetWeddingReserveInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingReserveInfo", ctx, in)
	ret0, _ := ret[0].(*GetWeddingReserveInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingReserveInfo indicates an expected call of GetWeddingReserveInfo.
func (mr *MockChannelWeddingPlanServerMockRecorder) GetWeddingReserveInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingReserveInfo", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).GetWeddingReserveInfo), ctx, in)
}

// HandlePropose mocks base method.
func (m *MockChannelWeddingPlanServer) HandlePropose(ctx context.Context, in *HandleProposeRequest) (*HandleProposeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandlePropose", ctx, in)
	ret0, _ := ret[0].(*HandleProposeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandlePropose indicates an expected call of HandlePropose.
func (mr *MockChannelWeddingPlanServerMockRecorder) HandlePropose(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandlePropose", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).HandlePropose), ctx, in)
}

// HandleWeddingInvite mocks base method.
func (m *MockChannelWeddingPlanServer) HandleWeddingInvite(ctx context.Context, in *HandleWeddingInviteRequest) (*HandleWeddingInviteResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleWeddingInvite", ctx, in)
	ret0, _ := ret[0].(*HandleWeddingInviteResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandleWeddingInvite indicates an expected call of HandleWeddingInvite.
func (mr *MockChannelWeddingPlanServerMockRecorder) HandleWeddingInvite(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleWeddingInvite", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).HandleWeddingInvite), ctx, in)
}

// InviteWeddingGuest mocks base method.
func (m *MockChannelWeddingPlanServer) InviteWeddingGuest(ctx context.Context, in *InviteWeddingGuestRequest) (*InviteWeddingGuestResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InviteWeddingGuest", ctx, in)
	ret0, _ := ret[0].(*InviteWeddingGuestResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InviteWeddingGuest indicates an expected call of InviteWeddingGuest.
func (mr *MockChannelWeddingPlanServerMockRecorder) InviteWeddingGuest(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InviteWeddingGuest", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).InviteWeddingGuest), ctx, in)
}

// ManualNotifyWeddingStart mocks base method.
func (m *MockChannelWeddingPlanServer) ManualNotifyWeddingStart(ctx context.Context, in *ManualNotifyWeddingStartRequest) (*ManualNotifyWeddingStartResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ManualNotifyWeddingStart", ctx, in)
	ret0, _ := ret[0].(*ManualNotifyWeddingStartResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ManualNotifyWeddingStart indicates an expected call of ManualNotifyWeddingStart.
func (mr *MockChannelWeddingPlanServerMockRecorder) ManualNotifyWeddingStart(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ManualNotifyWeddingStart", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).ManualNotifyWeddingStart), ctx, in)
}

// PageGetComingWeddingList mocks base method.
func (m *MockChannelWeddingPlanServer) PageGetComingWeddingList(ctx context.Context, in *PageGetComingWeddingListRequest) (*PageGetComingWeddingListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PageGetComingWeddingList", ctx, in)
	ret0, _ := ret[0].(*PageGetComingWeddingListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PageGetComingWeddingList indicates an expected call of PageGetComingWeddingList.
func (mr *MockChannelWeddingPlanServerMockRecorder) PageGetComingWeddingList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PageGetComingWeddingList", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).PageGetComingWeddingList), ctx, in)
}

// RevokePropose mocks base method.
func (m *MockChannelWeddingPlanServer) RevokePropose(ctx context.Context, in *RevokeProposeRequest) (*RevokeProposeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RevokePropose", ctx, in)
	ret0, _ := ret[0].(*RevokeProposeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RevokePropose indicates an expected call of RevokePropose.
func (mr *MockChannelWeddingPlanServerMockRecorder) RevokePropose(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RevokePropose", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).RevokePropose), ctx, in)
}

// SaveWeddingBigScreen mocks base method.
func (m *MockChannelWeddingPlanServer) SaveWeddingBigScreen(ctx context.Context, in *SaveWeddingBigScreenRequest) (*SaveWeddingBigScreenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveWeddingBigScreen", ctx, in)
	ret0, _ := ret[0].(*SaveWeddingBigScreenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveWeddingBigScreen indicates an expected call of SaveWeddingBigScreen.
func (mr *MockChannelWeddingPlanServerMockRecorder) SaveWeddingBigScreen(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveWeddingBigScreen", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).SaveWeddingBigScreen), ctx, in)
}

// SaveWeddingReserveInfo mocks base method.
func (m *MockChannelWeddingPlanServer) SaveWeddingReserveInfo(ctx context.Context, in *SaveWeddingReserveRequest) (*SaveWeddingReserveInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveWeddingReserveInfo", ctx, in)
	ret0, _ := ret[0].(*SaveWeddingReserveInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveWeddingReserveInfo indicates an expected call of SaveWeddingReserveInfo.
func (mr *MockChannelWeddingPlanServerMockRecorder) SaveWeddingReserveInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveWeddingReserveInfo", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).SaveWeddingReserveInfo), ctx, in)
}

// SendPropose mocks base method.
func (m *MockChannelWeddingPlanServer) SendPropose(ctx context.Context, in *SendProposeRequest) (*SendProposeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendPropose", ctx, in)
	ret0, _ := ret[0].(*SendProposeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendPropose indicates an expected call of SendPropose.
func (mr *MockChannelWeddingPlanServerMockRecorder) SendPropose(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendPropose", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).SendPropose), ctx, in)
}

// SetAdminChannelReserveTimeSectionSwitch mocks base method.
func (m *MockChannelWeddingPlanServer) SetAdminChannelReserveTimeSectionSwitch(ctx context.Context, in *SetAdminChannelReserveTimeSectionSwitchRequest) (*SetAdminChannelReserveTimeSectionSwitchResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetAdminChannelReserveTimeSectionSwitch", ctx, in)
	ret0, _ := ret[0].(*SetAdminChannelReserveTimeSectionSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetAdminChannelReserveTimeSectionSwitch indicates an expected call of SetAdminChannelReserveTimeSectionSwitch.
func (mr *MockChannelWeddingPlanServerMockRecorder) SetAdminChannelReserveTimeSectionSwitch(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAdminChannelReserveTimeSectionSwitch", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).SetAdminChannelReserveTimeSectionSwitch), ctx, in)
}

// SetUserRelationHideStatus mocks base method.
func (m *MockChannelWeddingPlanServer) SetUserRelationHideStatus(ctx context.Context, in *SetUserRelationHideStatusRequest) (*SetUserRelationHideStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserRelationHideStatus", ctx, in)
	ret0, _ := ret[0].(*SetUserRelationHideStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserRelationHideStatus indicates an expected call of SetUserRelationHideStatus.
func (mr *MockChannelWeddingPlanServerMockRecorder) SetUserRelationHideStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserRelationHideStatus", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).SetUserRelationHideStatus), ctx, in)
}

// SetWeddingHost mocks base method.
func (m *MockChannelWeddingPlanServer) SetWeddingHost(ctx context.Context, in *SetWeddingHostRequest) (*SetWeddingHostResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetWeddingHost", ctx, in)
	ret0, _ := ret[0].(*SetWeddingHostResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetWeddingHost indicates an expected call of SetWeddingHost.
func (mr *MockChannelWeddingPlanServerMockRecorder) SetWeddingHost(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetWeddingHost", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).SetWeddingHost), ctx, in)
}

// SubscribeWedding mocks base method.
func (m *MockChannelWeddingPlanServer) SubscribeWedding(ctx context.Context, in *SubscribeWeddingRequest) (*SubscribeWeddingResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubscribeWedding", ctx, in)
	ret0, _ := ret[0].(*SubscribeWeddingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubscribeWedding indicates an expected call of SubscribeWedding.
func (mr *MockChannelWeddingPlanServerMockRecorder) SubscribeWedding(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubscribeWedding", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).SubscribeWedding), ctx, in)
}

// TestWeddingAnniversaryPopup mocks base method.
func (m *MockChannelWeddingPlanServer) TestWeddingAnniversaryPopup(ctx context.Context, in *TestWeddingAnniversaryPopupRequest) (*TestWeddingAnniversaryPopupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TestWeddingAnniversaryPopup", ctx, in)
	ret0, _ := ret[0].(*TestWeddingAnniversaryPopupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestWeddingAnniversaryPopup indicates an expected call of TestWeddingAnniversaryPopup.
func (mr *MockChannelWeddingPlanServerMockRecorder) TestWeddingAnniversaryPopup(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestWeddingAnniversaryPopup", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).TestWeddingAnniversaryPopup), ctx, in)
}

// UpdateThemeCfg mocks base method.
func (m *MockChannelWeddingPlanServer) UpdateThemeCfg(ctx context.Context, in *UpdateThemeCfgRequest) (*UpdateThemeCfgResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateThemeCfg", ctx, in)
	ret0, _ := ret[0].(*UpdateThemeCfgResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateThemeCfg indicates an expected call of UpdateThemeCfg.
func (mr *MockChannelWeddingPlanServerMockRecorder) UpdateThemeCfg(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateThemeCfg", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).UpdateThemeCfg), ctx, in)
}

// UpdateWeddingBigScreenReviewStatus mocks base method.
func (m *MockChannelWeddingPlanServer) UpdateWeddingBigScreenReviewStatus(ctx context.Context, in *UpdateWeddingBigScreenReviewStatusRequest) (*UpdateWeddingBigScreenReviewStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateWeddingBigScreenReviewStatus", ctx, in)
	ret0, _ := ret[0].(*UpdateWeddingBigScreenReviewStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateWeddingBigScreenReviewStatus indicates an expected call of UpdateWeddingBigScreenReviewStatus.
func (mr *MockChannelWeddingPlanServerMockRecorder) UpdateWeddingBigScreenReviewStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWeddingBigScreenReviewStatus", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).UpdateWeddingBigScreenReviewStatus), ctx, in)
}

// UpdateWeddingPlanStatus mocks base method.
func (m *MockChannelWeddingPlanServer) UpdateWeddingPlanStatus(ctx context.Context, in *UpdateWeddingPlanStatusRequest) (*UpdateWeddingPlanStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateWeddingPlanStatus", ctx, in)
	ret0, _ := ret[0].(*UpdateWeddingPlanStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateWeddingPlanStatus indicates an expected call of UpdateWeddingPlanStatus.
func (mr *MockChannelWeddingPlanServerMockRecorder) UpdateWeddingPlanStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWeddingPlanStatus", reflect.TypeOf((*MockChannelWeddingPlanServer)(nil).UpdateWeddingPlanStatus), ctx, in)
}
