// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/guild-robot/guild-robot.proto

package guild_robot

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 知识类型
type KnowledgeType int32

const (
	KnowledgeType_KNOWLEDGE_TYPE_UNSPECIFIED           KnowledgeType = 0
	KnowledgeType_KNOWLEDGE_TYPE_RULES_AND_POLICIES    KnowledgeType = 1
	KnowledgeType_KNOWLEDGE_TYPE_ACTIVITY              KnowledgeType = 2
	KnowledgeType_KNOWLEDGE_TYPE_DATA_ASSESSMENT       KnowledgeType = 3
	KnowledgeType_KNOWLEDGE_TYPE_VIOLATION_HANDLING    KnowledgeType = 4
	KnowledgeType_KNOWLEDGE_TYPE_AMUSE                 KnowledgeType = 5
	KnowledgeType_KNOWLEDGE_TYPE_ACTIVITY_AND_FUNCTION KnowledgeType = 6
	KnowledgeType_KNOWLEDGE_TYPE_ASSESSMENT_AND_POINTS KnowledgeType = 7
	KnowledgeType_KNOWLEDGE_TYPE_ECOLOGICAL_SECURITY   KnowledgeType = 8
	KnowledgeType_KNOWLEDGE_TYPE_ANCHOR                KnowledgeType = 9
	KnowledgeType_KNOWLEDGE_TYPE_GUILD                 KnowledgeType = 10
)

var KnowledgeType_name = map[int32]string{
	0:  "KNOWLEDGE_TYPE_UNSPECIFIED",
	1:  "KNOWLEDGE_TYPE_RULES_AND_POLICIES",
	2:  "KNOWLEDGE_TYPE_ACTIVITY",
	3:  "KNOWLEDGE_TYPE_DATA_ASSESSMENT",
	4:  "KNOWLEDGE_TYPE_VIOLATION_HANDLING",
	5:  "KNOWLEDGE_TYPE_AMUSE",
	6:  "KNOWLEDGE_TYPE_ACTIVITY_AND_FUNCTION",
	7:  "KNOWLEDGE_TYPE_ASSESSMENT_AND_POINTS",
	8:  "KNOWLEDGE_TYPE_ECOLOGICAL_SECURITY",
	9:  "KNOWLEDGE_TYPE_ANCHOR",
	10: "KNOWLEDGE_TYPE_GUILD",
}
var KnowledgeType_value = map[string]int32{
	"KNOWLEDGE_TYPE_UNSPECIFIED":           0,
	"KNOWLEDGE_TYPE_RULES_AND_POLICIES":    1,
	"KNOWLEDGE_TYPE_ACTIVITY":              2,
	"KNOWLEDGE_TYPE_DATA_ASSESSMENT":       3,
	"KNOWLEDGE_TYPE_VIOLATION_HANDLING":    4,
	"KNOWLEDGE_TYPE_AMUSE":                 5,
	"KNOWLEDGE_TYPE_ACTIVITY_AND_FUNCTION": 6,
	"KNOWLEDGE_TYPE_ASSESSMENT_AND_POINTS": 7,
	"KNOWLEDGE_TYPE_ECOLOGICAL_SECURITY":   8,
	"KNOWLEDGE_TYPE_ANCHOR":                9,
	"KNOWLEDGE_TYPE_GUILD":                 10,
}

func (x KnowledgeType) String() string {
	return proto.EnumName(KnowledgeType_name, int32(x))
}
func (KnowledgeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_robot_32d3e7e87dd21a13, []int{0}
}

// 机器人类型
type RobotType int32

const (
	RobotType_ROBOT_TYPE_UNSPECIFIED RobotType = 0
	RobotType_ROBOT_TYPE_AMUSE       RobotType = 4
	RobotType_ROBOT_TYPE_YUYIN       RobotType = 7
	RobotType_ROBOT_TYPE_ESPORT      RobotType = 8
)

var RobotType_name = map[int32]string{
	0: "ROBOT_TYPE_UNSPECIFIED",
	4: "ROBOT_TYPE_AMUSE",
	7: "ROBOT_TYPE_YUYIN",
	8: "ROBOT_TYPE_ESPORT",
}
var RobotType_value = map[string]int32{
	"ROBOT_TYPE_UNSPECIFIED": 0,
	"ROBOT_TYPE_AMUSE":       4,
	"ROBOT_TYPE_YUYIN":       7,
	"ROBOT_TYPE_ESPORT":      8,
}

func (x RobotType) String() string {
	return proto.EnumName(RobotType_name, int32(x))
}
func (RobotType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_robot_32d3e7e87dd21a13, []int{1}
}

type KnowledgeFile struct {
	RobotType            RobotType     `protobuf:"varint,1,opt,name=robot_type,json=robotType,proto3,enum=guild_robot.RobotType" json:"robot_type,omitempty"`
	Id                   uint32        `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	FileName             string        `protobuf:"bytes,3,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	KnowledgeType        KnowledgeType `protobuf:"varint,4,opt,name=knowledge_type,json=knowledgeType,proto3,enum=guild_robot.KnowledgeType" json:"knowledge_type,omitempty"`
	FileUrl              string        `protobuf:"bytes,5,opt,name=file_url,json=fileUrl,proto3" json:"file_url,omitempty"`
	CreateTime           uint64        `protobuf:"varint,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Creator              string        `protobuf:"bytes,7,opt,name=creator,proto3" json:"creator,omitempty"`
	UpdateTime           uint64        `protobuf:"varint,8,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	Updater              string        `protobuf:"bytes,9,opt,name=updater,proto3" json:"updater,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *KnowledgeFile) Reset()         { *m = KnowledgeFile{} }
func (m *KnowledgeFile) String() string { return proto.CompactTextString(m) }
func (*KnowledgeFile) ProtoMessage()    {}
func (*KnowledgeFile) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_robot_32d3e7e87dd21a13, []int{0}
}
func (m *KnowledgeFile) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KnowledgeFile.Unmarshal(m, b)
}
func (m *KnowledgeFile) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KnowledgeFile.Marshal(b, m, deterministic)
}
func (dst *KnowledgeFile) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KnowledgeFile.Merge(dst, src)
}
func (m *KnowledgeFile) XXX_Size() int {
	return xxx_messageInfo_KnowledgeFile.Size(m)
}
func (m *KnowledgeFile) XXX_DiscardUnknown() {
	xxx_messageInfo_KnowledgeFile.DiscardUnknown(m)
}

var xxx_messageInfo_KnowledgeFile proto.InternalMessageInfo

func (m *KnowledgeFile) GetRobotType() RobotType {
	if m != nil {
		return m.RobotType
	}
	return RobotType_ROBOT_TYPE_UNSPECIFIED
}

func (m *KnowledgeFile) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *KnowledgeFile) GetFileName() string {
	if m != nil {
		return m.FileName
	}
	return ""
}

func (m *KnowledgeFile) GetKnowledgeType() KnowledgeType {
	if m != nil {
		return m.KnowledgeType
	}
	return KnowledgeType_KNOWLEDGE_TYPE_UNSPECIFIED
}

func (m *KnowledgeFile) GetFileUrl() string {
	if m != nil {
		return m.FileUrl
	}
	return ""
}

func (m *KnowledgeFile) GetCreateTime() uint64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *KnowledgeFile) GetCreator() string {
	if m != nil {
		return m.Creator
	}
	return ""
}

func (m *KnowledgeFile) GetUpdateTime() uint64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *KnowledgeFile) GetUpdater() string {
	if m != nil {
		return m.Updater
	}
	return ""
}

type UploadKnowledgeFileUrlReq struct {
	RobotType            RobotType     `protobuf:"varint,1,opt,name=robot_type,json=robotType,proto3,enum=guild_robot.RobotType" json:"robot_type,omitempty"`
	Id                   uint32        `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	FileName             string        `protobuf:"bytes,3,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	KnowledgeType        KnowledgeType `protobuf:"varint,4,opt,name=knowledge_type,json=knowledgeType,proto3,enum=guild_robot.KnowledgeType" json:"knowledge_type,omitempty"`
	FileUrl              string        `protobuf:"bytes,5,opt,name=file_url,json=fileUrl,proto3" json:"file_url,omitempty"`
	Operator             string        `protobuf:"bytes,6,opt,name=operator,proto3" json:"operator,omitempty"`
	UpdateTime           uint64        `protobuf:"varint,7,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UploadKnowledgeFileUrlReq) Reset()         { *m = UploadKnowledgeFileUrlReq{} }
func (m *UploadKnowledgeFileUrlReq) String() string { return proto.CompactTextString(m) }
func (*UploadKnowledgeFileUrlReq) ProtoMessage()    {}
func (*UploadKnowledgeFileUrlReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_robot_32d3e7e87dd21a13, []int{1}
}
func (m *UploadKnowledgeFileUrlReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UploadKnowledgeFileUrlReq.Unmarshal(m, b)
}
func (m *UploadKnowledgeFileUrlReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UploadKnowledgeFileUrlReq.Marshal(b, m, deterministic)
}
func (dst *UploadKnowledgeFileUrlReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UploadKnowledgeFileUrlReq.Merge(dst, src)
}
func (m *UploadKnowledgeFileUrlReq) XXX_Size() int {
	return xxx_messageInfo_UploadKnowledgeFileUrlReq.Size(m)
}
func (m *UploadKnowledgeFileUrlReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UploadKnowledgeFileUrlReq.DiscardUnknown(m)
}

var xxx_messageInfo_UploadKnowledgeFileUrlReq proto.InternalMessageInfo

func (m *UploadKnowledgeFileUrlReq) GetRobotType() RobotType {
	if m != nil {
		return m.RobotType
	}
	return RobotType_ROBOT_TYPE_UNSPECIFIED
}

func (m *UploadKnowledgeFileUrlReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UploadKnowledgeFileUrlReq) GetFileName() string {
	if m != nil {
		return m.FileName
	}
	return ""
}

func (m *UploadKnowledgeFileUrlReq) GetKnowledgeType() KnowledgeType {
	if m != nil {
		return m.KnowledgeType
	}
	return KnowledgeType_KNOWLEDGE_TYPE_UNSPECIFIED
}

func (m *UploadKnowledgeFileUrlReq) GetFileUrl() string {
	if m != nil {
		return m.FileUrl
	}
	return ""
}

func (m *UploadKnowledgeFileUrlReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *UploadKnowledgeFileUrlReq) GetUpdateTime() uint64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type UploadKnowledgeFileUrlResp struct {
	KnowledgeFile        *KnowledgeFile `protobuf:"bytes,1,opt,name=knowledge_file,json=knowledgeFile,proto3" json:"knowledge_file,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UploadKnowledgeFileUrlResp) Reset()         { *m = UploadKnowledgeFileUrlResp{} }
func (m *UploadKnowledgeFileUrlResp) String() string { return proto.CompactTextString(m) }
func (*UploadKnowledgeFileUrlResp) ProtoMessage()    {}
func (*UploadKnowledgeFileUrlResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_robot_32d3e7e87dd21a13, []int{2}
}
func (m *UploadKnowledgeFileUrlResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UploadKnowledgeFileUrlResp.Unmarshal(m, b)
}
func (m *UploadKnowledgeFileUrlResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UploadKnowledgeFileUrlResp.Marshal(b, m, deterministic)
}
func (dst *UploadKnowledgeFileUrlResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UploadKnowledgeFileUrlResp.Merge(dst, src)
}
func (m *UploadKnowledgeFileUrlResp) XXX_Size() int {
	return xxx_messageInfo_UploadKnowledgeFileUrlResp.Size(m)
}
func (m *UploadKnowledgeFileUrlResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UploadKnowledgeFileUrlResp.DiscardUnknown(m)
}

var xxx_messageInfo_UploadKnowledgeFileUrlResp proto.InternalMessageInfo

func (m *UploadKnowledgeFileUrlResp) GetKnowledgeFile() *KnowledgeFile {
	if m != nil {
		return m.KnowledgeFile
	}
	return nil
}

type GetKnowledgeFileListReq struct {
	RobotType            RobotType     `protobuf:"varint,1,opt,name=robot_type,json=robotType,proto3,enum=guild_robot.RobotType" json:"robot_type,omitempty"`
	KnowledgeType        KnowledgeType `protobuf:"varint,2,opt,name=knowledge_type,json=knowledgeType,proto3,enum=guild_robot.KnowledgeType" json:"knowledge_type,omitempty"`
	Page                 uint32        `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32        `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	SearchQuery          string        `protobuf:"bytes,5,opt,name=search_query,json=searchQuery,proto3" json:"search_query,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetKnowledgeFileListReq) Reset()         { *m = GetKnowledgeFileListReq{} }
func (m *GetKnowledgeFileListReq) String() string { return proto.CompactTextString(m) }
func (*GetKnowledgeFileListReq) ProtoMessage()    {}
func (*GetKnowledgeFileListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_robot_32d3e7e87dd21a13, []int{3}
}
func (m *GetKnowledgeFileListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetKnowledgeFileListReq.Unmarshal(m, b)
}
func (m *GetKnowledgeFileListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetKnowledgeFileListReq.Marshal(b, m, deterministic)
}
func (dst *GetKnowledgeFileListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetKnowledgeFileListReq.Merge(dst, src)
}
func (m *GetKnowledgeFileListReq) XXX_Size() int {
	return xxx_messageInfo_GetKnowledgeFileListReq.Size(m)
}
func (m *GetKnowledgeFileListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetKnowledgeFileListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetKnowledgeFileListReq proto.InternalMessageInfo

func (m *GetKnowledgeFileListReq) GetRobotType() RobotType {
	if m != nil {
		return m.RobotType
	}
	return RobotType_ROBOT_TYPE_UNSPECIFIED
}

func (m *GetKnowledgeFileListReq) GetKnowledgeType() KnowledgeType {
	if m != nil {
		return m.KnowledgeType
	}
	return KnowledgeType_KNOWLEDGE_TYPE_UNSPECIFIED
}

func (m *GetKnowledgeFileListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetKnowledgeFileListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetKnowledgeFileListReq) GetSearchQuery() string {
	if m != nil {
		return m.SearchQuery
	}
	return ""
}

type GetKnowledgeFileListResp struct {
	KnowledgeFiles       []*KnowledgeFile `protobuf:"bytes,1,rep,name=knowledge_files,json=knowledgeFiles,proto3" json:"knowledge_files,omitempty"`
	TotalCount           uint32           `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetKnowledgeFileListResp) Reset()         { *m = GetKnowledgeFileListResp{} }
func (m *GetKnowledgeFileListResp) String() string { return proto.CompactTextString(m) }
func (*GetKnowledgeFileListResp) ProtoMessage()    {}
func (*GetKnowledgeFileListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_robot_32d3e7e87dd21a13, []int{4}
}
func (m *GetKnowledgeFileListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetKnowledgeFileListResp.Unmarshal(m, b)
}
func (m *GetKnowledgeFileListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetKnowledgeFileListResp.Marshal(b, m, deterministic)
}
func (dst *GetKnowledgeFileListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetKnowledgeFileListResp.Merge(dst, src)
}
func (m *GetKnowledgeFileListResp) XXX_Size() int {
	return xxx_messageInfo_GetKnowledgeFileListResp.Size(m)
}
func (m *GetKnowledgeFileListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetKnowledgeFileListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetKnowledgeFileListResp proto.InternalMessageInfo

func (m *GetKnowledgeFileListResp) GetKnowledgeFiles() []*KnowledgeFile {
	if m != nil {
		return m.KnowledgeFiles
	}
	return nil
}

func (m *GetKnowledgeFileListResp) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

type DeleteKnowledgeFileReq struct {
	RobotType            RobotType `protobuf:"varint,1,opt,name=robot_type,json=robotType,proto3,enum=guild_robot.RobotType" json:"robot_type,omitempty"`
	Id                   uint32    `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *DeleteKnowledgeFileReq) Reset()         { *m = DeleteKnowledgeFileReq{} }
func (m *DeleteKnowledgeFileReq) String() string { return proto.CompactTextString(m) }
func (*DeleteKnowledgeFileReq) ProtoMessage()    {}
func (*DeleteKnowledgeFileReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_robot_32d3e7e87dd21a13, []int{5}
}
func (m *DeleteKnowledgeFileReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteKnowledgeFileReq.Unmarshal(m, b)
}
func (m *DeleteKnowledgeFileReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteKnowledgeFileReq.Marshal(b, m, deterministic)
}
func (dst *DeleteKnowledgeFileReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteKnowledgeFileReq.Merge(dst, src)
}
func (m *DeleteKnowledgeFileReq) XXX_Size() int {
	return xxx_messageInfo_DeleteKnowledgeFileReq.Size(m)
}
func (m *DeleteKnowledgeFileReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteKnowledgeFileReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteKnowledgeFileReq proto.InternalMessageInfo

func (m *DeleteKnowledgeFileReq) GetRobotType() RobotType {
	if m != nil {
		return m.RobotType
	}
	return RobotType_ROBOT_TYPE_UNSPECIFIED
}

func (m *DeleteKnowledgeFileReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DeleteKnowledgeFileResp struct {
	Success              bool     `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteKnowledgeFileResp) Reset()         { *m = DeleteKnowledgeFileResp{} }
func (m *DeleteKnowledgeFileResp) String() string { return proto.CompactTextString(m) }
func (*DeleteKnowledgeFileResp) ProtoMessage()    {}
func (*DeleteKnowledgeFileResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_robot_32d3e7e87dd21a13, []int{6}
}
func (m *DeleteKnowledgeFileResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteKnowledgeFileResp.Unmarshal(m, b)
}
func (m *DeleteKnowledgeFileResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteKnowledgeFileResp.Marshal(b, m, deterministic)
}
func (dst *DeleteKnowledgeFileResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteKnowledgeFileResp.Merge(dst, src)
}
func (m *DeleteKnowledgeFileResp) XXX_Size() int {
	return xxx_messageInfo_DeleteKnowledgeFileResp.Size(m)
}
func (m *DeleteKnowledgeFileResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteKnowledgeFileResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteKnowledgeFileResp proto.InternalMessageInfo

func (m *DeleteKnowledgeFileResp) GetSuccess() bool {
	if m != nil {
		return m.Success
	}
	return false
}

func (m *DeleteKnowledgeFileResp) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func init() {
	proto.RegisterType((*KnowledgeFile)(nil), "guild_robot.KnowledgeFile")
	proto.RegisterType((*UploadKnowledgeFileUrlReq)(nil), "guild_robot.UploadKnowledgeFileUrlReq")
	proto.RegisterType((*UploadKnowledgeFileUrlResp)(nil), "guild_robot.UploadKnowledgeFileUrlResp")
	proto.RegisterType((*GetKnowledgeFileListReq)(nil), "guild_robot.GetKnowledgeFileListReq")
	proto.RegisterType((*GetKnowledgeFileListResp)(nil), "guild_robot.GetKnowledgeFileListResp")
	proto.RegisterType((*DeleteKnowledgeFileReq)(nil), "guild_robot.DeleteKnowledgeFileReq")
	proto.RegisterType((*DeleteKnowledgeFileResp)(nil), "guild_robot.DeleteKnowledgeFileResp")
	proto.RegisterEnum("guild_robot.KnowledgeType", KnowledgeType_name, KnowledgeType_value)
	proto.RegisterEnum("guild_robot.RobotType", RobotType_name, RobotType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GuildRobotClient is the client API for GuildRobot service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GuildRobotClient interface {
	UploadKnowledgeFileUrl(ctx context.Context, in *UploadKnowledgeFileUrlReq, opts ...grpc.CallOption) (*UploadKnowledgeFileUrlResp, error)
	GetKnowledgeFileList(ctx context.Context, in *GetKnowledgeFileListReq, opts ...grpc.CallOption) (*GetKnowledgeFileListResp, error)
	DeleteKnowledgeFile(ctx context.Context, in *DeleteKnowledgeFileReq, opts ...grpc.CallOption) (*DeleteKnowledgeFileResp, error)
}

type guildRobotClient struct {
	cc *grpc.ClientConn
}

func NewGuildRobotClient(cc *grpc.ClientConn) GuildRobotClient {
	return &guildRobotClient{cc}
}

func (c *guildRobotClient) UploadKnowledgeFileUrl(ctx context.Context, in *UploadKnowledgeFileUrlReq, opts ...grpc.CallOption) (*UploadKnowledgeFileUrlResp, error) {
	out := new(UploadKnowledgeFileUrlResp)
	err := c.cc.Invoke(ctx, "/guild_robot.GuildRobot/UploadKnowledgeFileUrl", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildRobotClient) GetKnowledgeFileList(ctx context.Context, in *GetKnowledgeFileListReq, opts ...grpc.CallOption) (*GetKnowledgeFileListResp, error) {
	out := new(GetKnowledgeFileListResp)
	err := c.cc.Invoke(ctx, "/guild_robot.GuildRobot/GetKnowledgeFileList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildRobotClient) DeleteKnowledgeFile(ctx context.Context, in *DeleteKnowledgeFileReq, opts ...grpc.CallOption) (*DeleteKnowledgeFileResp, error) {
	out := new(DeleteKnowledgeFileResp)
	err := c.cc.Invoke(ctx, "/guild_robot.GuildRobot/DeleteKnowledgeFile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GuildRobotServer is the server API for GuildRobot service.
type GuildRobotServer interface {
	UploadKnowledgeFileUrl(context.Context, *UploadKnowledgeFileUrlReq) (*UploadKnowledgeFileUrlResp, error)
	GetKnowledgeFileList(context.Context, *GetKnowledgeFileListReq) (*GetKnowledgeFileListResp, error)
	DeleteKnowledgeFile(context.Context, *DeleteKnowledgeFileReq) (*DeleteKnowledgeFileResp, error)
}

func RegisterGuildRobotServer(s *grpc.Server, srv GuildRobotServer) {
	s.RegisterService(&_GuildRobot_serviceDesc, srv)
}

func _GuildRobot_UploadKnowledgeFileUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadKnowledgeFileUrlReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildRobotServer).UploadKnowledgeFileUrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_robot.GuildRobot/UploadKnowledgeFileUrl",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildRobotServer).UploadKnowledgeFileUrl(ctx, req.(*UploadKnowledgeFileUrlReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildRobot_GetKnowledgeFileList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKnowledgeFileListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildRobotServer).GetKnowledgeFileList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_robot.GuildRobot/GetKnowledgeFileList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildRobotServer).GetKnowledgeFileList(ctx, req.(*GetKnowledgeFileListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildRobot_DeleteKnowledgeFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteKnowledgeFileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildRobotServer).DeleteKnowledgeFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_robot.GuildRobot/DeleteKnowledgeFile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildRobotServer).DeleteKnowledgeFile(ctx, req.(*DeleteKnowledgeFileReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GuildRobot_serviceDesc = grpc.ServiceDesc{
	ServiceName: "guild_robot.GuildRobot",
	HandlerType: (*GuildRobotServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UploadKnowledgeFileUrl",
			Handler:    _GuildRobot_UploadKnowledgeFileUrl_Handler,
		},
		{
			MethodName: "GetKnowledgeFileList",
			Handler:    _GuildRobot_GetKnowledgeFileList_Handler,
		},
		{
			MethodName: "DeleteKnowledgeFile",
			Handler:    _GuildRobot_DeleteKnowledgeFile_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/guild-robot/guild-robot.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/guild-robot/guild-robot.proto", fileDescriptor_guild_robot_32d3e7e87dd21a13)
}

var fileDescriptor_guild_robot_32d3e7e87dd21a13 = []byte{
	// 795 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x56, 0xdd, 0x6e, 0xeb, 0x44,
	0x10, 0x3e, 0x76, 0x72, 0xf2, 0x33, 0x21, 0xc1, 0x2c, 0x3d, 0xa9, 0xeb, 0x23, 0x1d, 0x72, 0x4c,
	0x5b, 0xa2, 0x0a, 0x5a, 0xa9, 0x88, 0x07, 0x30, 0x8e, 0x9b, 0x5a, 0x75, 0xed, 0xe0, 0x9f, 0xa2,
	0x5c, 0xad, 0xd2, 0x64, 0x29, 0x26, 0x4e, 0xed, 0xda, 0x0e, 0xa8, 0xbd, 0xe2, 0x96, 0x57, 0xe0,
	0x79, 0x90, 0x78, 0x14, 0x5e, 0x03, 0xed, 0x3a, 0x89, 0x62, 0xe3, 0xa8, 0x45, 0x70, 0x75, 0x6e,
	0x92, 0x9d, 0x6f, 0x67, 0xe6, 0x9b, 0xfd, 0x66, 0x76, 0x65, 0xf8, 0x32, 0x4d, 0xcf, 0x1e, 0x96,
	0xfe, 0x74, 0x9e, 0xf8, 0xc1, 0xcf, 0x24, 0x3e, 0xbb, 0x5b, 0xfa, 0xc1, 0xec, 0xab, 0x38, 0xbc,
	0x0d, 0xd3, 0xed, 0xf5, 0x69, 0x14, 0x87, 0x69, 0x88, 0x5a, 0x0c, 0xc2, 0x0c, 0x92, 0xff, 0xe4,
	0xa1, 0x7d, 0x75, 0x1f, 0xfe, 0x12, 0x90, 0xd9, 0x1d, 0xb9, 0xf0, 0x03, 0x82, 0xbe, 0x01, 0x60,
	0x5b, 0x38, 0x7d, 0x8c, 0x88, 0xc8, 0xf5, 0xb8, 0x7e, 0xe7, 0xbc, 0x7b, 0xba, 0x15, 0x73, 0x6a,
	0xd3, 0x5f, 0xf7, 0x31, 0x22, 0x76, 0x33, 0x5e, 0x2f, 0x51, 0x07, 0x78, 0x7f, 0x26, 0xf2, 0x3d,
	0xae, 0xdf, 0xb6, 0x79, 0x7f, 0x86, 0xde, 0x42, 0xf3, 0x07, 0x3f, 0x20, 0xf8, 0x7e, 0xb2, 0x20,
	0x62, 0xa5, 0xc7, 0xf5, 0x9b, 0x76, 0x83, 0x02, 0xe6, 0x64, 0x41, 0x90, 0x02, 0x9d, 0xf9, 0x9a,
	0x34, 0xe3, 0xa9, 0x32, 0x1e, 0x29, 0xc7, 0xb3, 0xa9, 0x8b, 0x71, 0xb5, 0xe7, 0xdb, 0x26, 0x3a,
	0x00, 0x96, 0x0e, 0x2f, 0xe3, 0x40, 0x7c, 0xcd, 0xd2, 0xd7, 0xa9, 0xed, 0xc5, 0x01, 0xfa, 0x0c,
	0x5a, 0xd3, 0x98, 0x4c, 0x52, 0x82, 0x53, 0x7f, 0x41, 0xc4, 0x5a, 0x8f, 0xeb, 0x57, 0x6d, 0xc8,
	0x20, 0xd7, 0x5f, 0x10, 0x24, 0x42, 0x9d, 0x59, 0x61, 0x2c, 0xd6, 0xb3, 0xd0, 0x95, 0x49, 0x43,
	0x97, 0xd1, 0x6c, 0x13, 0xda, 0xc8, 0x42, 0x33, 0x68, 0x1d, 0x9a, 0x59, 0xb1, 0xd8, 0xcc, 0x42,
	0x57, 0xa6, 0xfc, 0x3b, 0x0f, 0x07, 0x5e, 0x14, 0x84, 0x93, 0x59, 0x4e, 0x4f, 0x2f, 0x0e, 0x6c,
	0xf2, 0xf0, 0x01, 0xa8, 0x2a, 0x41, 0x23, 0x8c, 0x48, 0xcc, 0x54, 0xab, 0x65, 0xcc, 0x6b, 0xbb,
	0x28, 0x5b, 0xbd, 0x28, 0x9b, 0x8c, 0x41, 0xda, 0xa5, 0x4d, 0x12, 0xe5, 0x0b, 0xa7, 0x7c, 0x4c,
	0xa0, 0xd6, 0xae, 0xc2, 0x69, 0xe8, 0x56, 0xe1, 0xd4, 0x94, 0xff, 0xe2, 0x60, 0x7f, 0x48, 0xd2,
	0x9c, 0x8f, 0xe1, 0x27, 0xe9, 0x7f, 0xd0, 0xfe, 0x9f, 0x72, 0xf2, 0xff, 0x56, 0x4e, 0x04, 0xd5,
	0x68, 0x72, 0x97, 0x75, 0xaa, 0x6d, 0xb3, 0x35, 0x6d, 0x21, 0xfd, 0xc7, 0x89, 0xff, 0x94, 0x35,
	0xa8, 0x6d, 0x37, 0x28, 0xe0, 0xf8, 0x4f, 0x04, 0xbd, 0x87, 0x8f, 0x12, 0x32, 0x89, 0xa7, 0x3f,
	0xe2, 0x87, 0x25, 0x89, 0x1f, 0x57, 0x3d, 0x68, 0x65, 0xd8, 0x77, 0x14, 0x92, 0x7f, 0xe5, 0x40,
	0x2c, 0x3f, 0x69, 0x12, 0x21, 0x15, 0x3e, 0xce, 0x2b, 0x99, 0x88, 0x5c, 0xaf, 0xf2, 0x8c, 0x94,
	0x9d, 0x9c, 0x94, 0x09, 0xed, 0x66, 0x1a, 0xa6, 0x93, 0x00, 0x4f, 0xc3, 0xe5, 0x7d, 0xba, 0x9a,
	0x3e, 0x60, 0x90, 0x4a, 0x11, 0x19, 0x43, 0x77, 0x40, 0x02, 0x92, 0x92, 0x7c, 0x9e, 0xff, 0x6d,
	0xcc, 0xe5, 0x6b, 0xd8, 0x2f, 0x25, 0x48, 0x22, 0x7a, 0x01, 0x93, 0xe5, 0x74, 0x4a, 0x92, 0x84,
	0xa5, 0x6f, 0xd8, 0x6b, 0x93, 0xee, 0x2c, 0x48, 0x92, 0x50, 0xbd, 0xf9, 0x6c, 0x74, 0x57, 0xe6,
	0xc9, 0x6f, 0x95, 0xad, 0x47, 0x8e, 0x11, 0xbe, 0x03, 0xe9, 0xca, 0xb4, 0xbe, 0x37, 0xb4, 0xc1,
	0x50, 0xc3, 0xee, 0x78, 0xa4, 0x61, 0xcf, 0x74, 0x46, 0x9a, 0xaa, 0x5f, 0xe8, 0xda, 0x40, 0x78,
	0x85, 0x8e, 0xe0, 0x7d, 0x61, 0xdf, 0xf6, 0x0c, 0xcd, 0xc1, 0x8a, 0x39, 0xc0, 0x23, 0xcb, 0xd0,
	0x55, 0x5d, 0x73, 0x04, 0x0e, 0xbd, 0x85, 0xfd, 0x82, 0x9b, 0xa2, 0xba, 0xfa, 0x8d, 0xee, 0x8e,
	0x05, 0x1e, 0xc9, 0xf0, 0xae, 0xb0, 0x39, 0x50, 0x5c, 0x05, 0x2b, 0x8e, 0xa3, 0x39, 0xce, 0xb5,
	0x66, 0xba, 0x42, 0xa5, 0x84, 0xe7, 0x46, 0xb7, 0x0c, 0xc5, 0xd5, 0x2d, 0x13, 0x5f, 0x2a, 0xe6,
	0xc0, 0xd0, 0xcd, 0xa1, 0x50, 0x45, 0x22, 0xec, 0x15, 0x79, 0xae, 0x3d, 0x47, 0x13, 0x5e, 0xa3,
	0x3e, 0x1c, 0xee, 0xa8, 0x80, 0xd5, 0x7a, 0xe1, 0x99, 0x2a, 0x4d, 0x26, 0xd4, 0xca, 0x3c, 0x37,
	0x95, 0xac, 0xce, 0xa5, 0x9b, 0xae, 0x23, 0xd4, 0xd1, 0x31, 0xc8, 0x05, 0x4f, 0x4d, 0xb5, 0x0c,
	0x6b, 0xa8, 0xab, 0x8a, 0x81, 0x1d, 0x4d, 0xf5, 0x6c, 0x7a, 0xc0, 0x06, 0x3a, 0x80, 0x37, 0xc5,
	0x8c, 0xa6, 0x7a, 0x69, 0xd9, 0x42, 0xb3, 0xa4, 0xe0, 0xa1, 0xa7, 0x1b, 0x03, 0x01, 0x4e, 0x7e,
	0x82, 0xe6, 0x66, 0x04, 0x90, 0x04, 0x5d, 0xdb, 0xfa, 0xd6, 0x72, 0xcb, 0x5a, 0xb0, 0x07, 0xc2,
	0xd6, 0x5e, 0x76, 0xde, 0x6a, 0x01, 0x1d, 0x7b, 0x63, 0xdd, 0x14, 0xea, 0xe8, 0x0d, 0x7c, 0xb2,
	0x85, 0x6a, 0xce, 0xc8, 0xb2, 0x5d, 0xa1, 0x71, 0xfe, 0x07, 0x0f, 0x30, 0xa4, 0xb3, 0xc7, 0x18,
	0xd1, 0x1c, 0xba, 0xe5, 0x8f, 0x10, 0x3a, 0xce, 0x8d, 0xe8, 0xce, 0x57, 0x5c, 0xfa, 0xe2, 0x45,
	0x7e, 0x49, 0x24, 0xbf, 0x42, 0x04, 0xf6, 0xca, 0x6e, 0x29, 0x3a, 0xcc, 0xa5, 0xd8, 0xf1, 0x64,
	0x49, 0x47, 0x2f, 0xf0, 0x62, 0x34, 0xb7, 0xf0, 0x69, 0xc9, 0x4d, 0x41, 0x9f, 0xe7, 0xe2, 0xcb,
	0x2f, 0xab, 0x74, 0xf8, 0xbc, 0x13, 0xe5, 0xb8, 0xad, 0xb1, 0xef, 0x86, 0xaf, 0xff, 0x0e, 0x00,
	0x00, 0xff, 0xff, 0xfb, 0x47, 0x95, 0x77, 0x67, 0x08, 0x00, 0x00,
}
