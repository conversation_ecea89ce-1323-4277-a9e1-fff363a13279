package channeldegrader

// Code generated by protoc-gen-svrkit-go. DO NOT EDIT.
// source: services/channeldegrader/channeldegrader.proto

/*
 This is a generated svrkit golang dev toolkit.
*/

import svrkit "gitlab.ttyuyin.com/golang/svrkit"

import context "golang.org/x/net/context"

import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

var _ = context.TODO

// Magic Number for Channeldegrader service
const ChanneldegraderMagic = uint16(15650)

// Client API for Channeldegrader service

type ChanneldegraderClientInterface interface {
	//
	CheckCmdDegradation(ctx context.Context, uin uint32, in *CheckCmdDegradationReq, opts ...svrkit.CallOption) (*CheckCmdDegradationResp, error)
}

type ChanneldegraderClient struct {
	cc *svrkit.ClientConn
}

func NewChanneldegraderClient(cc *svrkit.ClientConn) ChanneldegraderClientInterface {
	return &ChanneldegraderClient{cc}
}

const (
	commandchanneldegraderGetSelfSvnInfo      = 9995
	commandchanneldegraderEcho                = 9999
	commandchanneldegraderCheckCmdDegradation = 1
)

func (c *ChanneldegraderClient) CheckCmdDegradation(ctx context.Context, uin uint32, in *CheckCmdDegradationReq, opts ...svrkit.CallOption) (*CheckCmdDegradationResp, error) {
	out := new(CheckCmdDegradationResp)
	err := c.cc.Invoke(ctx, uin, commandchanneldegraderCheckCmdDegradation, "/channeldegrader.Channeldegrader/CheckCmdDegradation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}
