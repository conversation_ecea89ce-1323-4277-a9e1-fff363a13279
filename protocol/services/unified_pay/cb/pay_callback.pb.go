// Code generated by protoc-gen-go. DO NOT EDIT.
// source: unified_pay/cb/pay_callback.proto

package cb // import "golang.52tt.com/protocol/services/unified_pay/cb"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type FeeType int32

const (
	FeeType_UNKNOWN     FeeType = 0
	FeeType_RED_DIAMOND FeeType = 1
	FeeType_TBEAN       FeeType = 2
)

var FeeType_name = map[int32]string{
	0: "UNKNOWN",
	1: "RED_DIAMOND",
	2: "TBEAN",
}
var FeeType_value = map[string]int32{
	"UNKNOWN":     0,
	"RED_DIAMOND": 1,
	"TBEAN":       2,
}

func (x FeeType) String() string {
	return proto.EnumName(FeeType_name, int32(x))
}
func (FeeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pay_callback_e0ff4a16f9066514, []int{0}
}

type Op int32

const (
	Op_COMMIT   Op = 0
	Op_ROLLBACK Op = 1
)

var Op_name = map[int32]string{
	0: "COMMIT",
	1: "ROLLBACK",
}
var Op_value = map[string]int32{
	"COMMIT":   0,
	"ROLLBACK": 1,
}

func (x Op) String() string {
	return proto.EnumName(Op_name, int32(x))
}
func (Op) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pay_callback_e0ff4a16f9066514, []int{1}
}

type PayNotify struct {
	AppId                string   `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	OutTradeNo           string   `protobuf:"bytes,2,opt,name=out_trade_no,json=outTradeNo,proto3" json:"out_trade_no,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	UserName             string   `protobuf:"bytes,4,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	FeeType              FeeType  `protobuf:"varint,5,opt,name=fee_type,json=feeType,proto3,enum=unified_pay.cb.FeeType" json:"fee_type,omitempty"`
	TotalFee             uint32   `protobuf:"varint,6,opt,name=total_fee,json=totalFee,proto3" json:"total_fee,omitempty"`
	Body                 string   `protobuf:"bytes,7,opt,name=body,proto3" json:"body,omitempty"`
	Detail               string   `protobuf:"bytes,8,opt,name=detail,proto3" json:"detail,omitempty"`
	CreateAt             uint32   `protobuf:"varint,9,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	TradeNo              string   `protobuf:"bytes,10,opt,name=trade_no,json=tradeNo,proto3" json:"trade_no,omitempty"`
	Rollbackable         bool     `protobuf:"varint,11,opt,name=rollbackable,proto3" json:"rollbackable,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayNotify) Reset()         { *m = PayNotify{} }
func (m *PayNotify) String() string { return proto.CompactTextString(m) }
func (*PayNotify) ProtoMessage()    {}
func (*PayNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_pay_callback_e0ff4a16f9066514, []int{0}
}
func (m *PayNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayNotify.Unmarshal(m, b)
}
func (m *PayNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayNotify.Marshal(b, m, deterministic)
}
func (dst *PayNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayNotify.Merge(dst, src)
}
func (m *PayNotify) XXX_Size() int {
	return xxx_messageInfo_PayNotify.Size(m)
}
func (m *PayNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_PayNotify.DiscardUnknown(m)
}

var xxx_messageInfo_PayNotify proto.InternalMessageInfo

func (m *PayNotify) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *PayNotify) GetOutTradeNo() string {
	if m != nil {
		return m.OutTradeNo
	}
	return ""
}

func (m *PayNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PayNotify) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *PayNotify) GetFeeType() FeeType {
	if m != nil {
		return m.FeeType
	}
	return FeeType_UNKNOWN
}

func (m *PayNotify) GetTotalFee() uint32 {
	if m != nil {
		return m.TotalFee
	}
	return 0
}

func (m *PayNotify) GetBody() string {
	if m != nil {
		return m.Body
	}
	return ""
}

func (m *PayNotify) GetDetail() string {
	if m != nil {
		return m.Detail
	}
	return ""
}

func (m *PayNotify) GetCreateAt() uint32 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *PayNotify) GetTradeNo() string {
	if m != nil {
		return m.TradeNo
	}
	return ""
}

func (m *PayNotify) GetRollbackable() bool {
	if m != nil {
		return m.Rollbackable
	}
	return false
}

type PayNotifyResponse struct {
	Confirmed            bool     `protobuf:"varint,1,opt,name=confirmed,proto3" json:"confirmed,omitempty"`
	Op                   Op       `protobuf:"varint,2,opt,name=op,proto3,enum=unified_pay.cb.Op" json:"op,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayNotifyResponse) Reset()         { *m = PayNotifyResponse{} }
func (m *PayNotifyResponse) String() string { return proto.CompactTextString(m) }
func (*PayNotifyResponse) ProtoMessage()    {}
func (*PayNotifyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_pay_callback_e0ff4a16f9066514, []int{1}
}
func (m *PayNotifyResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayNotifyResponse.Unmarshal(m, b)
}
func (m *PayNotifyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayNotifyResponse.Marshal(b, m, deterministic)
}
func (dst *PayNotifyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayNotifyResponse.Merge(dst, src)
}
func (m *PayNotifyResponse) XXX_Size() int {
	return xxx_messageInfo_PayNotifyResponse.Size(m)
}
func (m *PayNotifyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PayNotifyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PayNotifyResponse proto.InternalMessageInfo

func (m *PayNotifyResponse) GetConfirmed() bool {
	if m != nil {
		return m.Confirmed
	}
	return false
}

func (m *PayNotifyResponse) GetOp() Op {
	if m != nil {
		return m.Op
	}
	return Op_COMMIT
}

func init() {
	proto.RegisterType((*PayNotify)(nil), "unified_pay.cb.PayNotify")
	proto.RegisterType((*PayNotifyResponse)(nil), "unified_pay.cb.PayNotifyResponse")
	proto.RegisterEnum("unified_pay.cb.FeeType", FeeType_name, FeeType_value)
	proto.RegisterEnum("unified_pay.cb.Op", Op_name, Op_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PayCallbackClient is the client API for PayCallback service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PayCallbackClient interface {
	Notify(ctx context.Context, in *PayNotify, opts ...grpc.CallOption) (*PayNotifyResponse, error)
}

type payCallbackClient struct {
	cc *grpc.ClientConn
}

func NewPayCallbackClient(cc *grpc.ClientConn) PayCallbackClient {
	return &payCallbackClient{cc}
}

func (c *payCallbackClient) Notify(ctx context.Context, in *PayNotify, opts ...grpc.CallOption) (*PayNotifyResponse, error) {
	out := new(PayNotifyResponse)
	err := c.cc.Invoke(ctx, "/unified_pay.cb.PayCallback/Notify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PayCallbackServer is the server API for PayCallback service.
type PayCallbackServer interface {
	Notify(context.Context, *PayNotify) (*PayNotifyResponse, error)
}

func RegisterPayCallbackServer(s *grpc.Server, srv PayCallbackServer) {
	s.RegisterService(&_PayCallback_serviceDesc, srv)
}

func _PayCallback_Notify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayNotify)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayCallbackServer).Notify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/unified_pay.cb.PayCallback/Notify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayCallbackServer).Notify(ctx, req.(*PayNotify))
	}
	return interceptor(ctx, in, info, handler)
}

var _PayCallback_serviceDesc = grpc.ServiceDesc{
	ServiceName: "unified_pay.cb.PayCallback",
	HandlerType: (*PayCallbackServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Notify",
			Handler:    _PayCallback_Notify_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "unified_pay/cb/pay_callback.proto",
}

func init() {
	proto.RegisterFile("unified_pay/cb/pay_callback.proto", fileDescriptor_pay_callback_e0ff4a16f9066514)
}

var fileDescriptor_pay_callback_e0ff4a16f9066514 = []byte{
	// 504 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x74, 0x52, 0xed, 0x6e, 0xd3, 0x40,
	0x10, 0xac, 0xdd, 0xd6, 0xb1, 0x37, 0x21, 0x98, 0x93, 0x00, 0x37, 0x20, 0x9a, 0xe6, 0x57, 0x54,
	0x09, 0x1b, 0x8c, 0x78, 0x80, 0x7c, 0xb4, 0x22, 0x6a, 0x63, 0x57, 0x56, 0x22, 0x24, 0xfe, 0x58,
	0x67, 0x7b, 0x5d, 0x59, 0x38, 0xbe, 0x93, 0x7d, 0x41, 0xf8, 0x15, 0x78, 0x36, 0x7e, 0xf1, 0x42,
	0xa0, 0x9c, 0x93, 0xd0, 0x54, 0xf0, 0xcb, 0xeb, 0xd9, 0x9d, 0xdd, 0x9b, 0xd1, 0xc0, 0xc5, 0xba,
	0xc8, 0xd2, 0x0c, 0x93, 0x90, 0xd3, 0xda, 0x89, 0x23, 0x87, 0xd3, 0x3a, 0x8c, 0x69, 0x9e, 0x47,
	0x34, 0xfe, 0x6a, 0xf3, 0x92, 0x09, 0x46, 0xba, 0x0f, 0x46, 0xec, 0x38, 0xea, 0x9d, 0xe3, 0x77,
	0x81, 0x45, 0x95, 0xb1, 0xc2, 0x61, 0x5c, 0x64, 0xac, 0xa8, 0x76, 0xdf, 0x86, 0x30, 0xf8, 0xa9,
	0x82, 0x71, 0x47, 0x6b, 0x8f, 0x89, 0x2c, 0xad, 0xc9, 0x73, 0xd0, 0x28, 0xe7, 0x61, 0x96, 0x58,
	0x4a, 0x5f, 0x19, 0x1a, 0xc1, 0x29, 0xe5, 0x7c, 0x96, 0x90, 0x3e, 0x74, 0xd8, 0x5a, 0x84, 0xa2,
	0xa4, 0x09, 0x86, 0x05, 0xb3, 0x54, 0xd9, 0x04, 0xb6, 0x16, 0x8b, 0x0d, 0xe4, 0x31, 0x62, 0xc2,
	0xf1, 0x3a, 0x4b, 0xac, 0xe3, 0xbe, 0x32, 0x7c, 0x12, 0x6c, 0x4a, 0xf2, 0x0a, 0x8c, 0x75, 0x85,
	0x65, 0x58, 0xd0, 0x15, 0x5a, 0x27, 0x92, 0xa0, 0x6f, 0x00, 0x8f, 0xae, 0x90, 0xb8, 0xa0, 0xa7,
	0x88, 0xa1, 0xa8, 0x39, 0x5a, 0xa7, 0x7d, 0x65, 0xd8, 0x75, 0x5f, 0xda, 0x87, 0x2f, 0xb7, 0xaf,
	0x11, 0x17, 0x35, 0xc7, 0xa0, 0x95, 0x36, 0xc5, 0x66, 0xa1, 0x60, 0x82, 0xe6, 0x61, 0x8a, 0x68,
	0x69, 0xf2, 0x90, 0x2e, 0x81, 0x6b, 0x44, 0x42, 0xe0, 0x24, 0x62, 0x49, 0x6d, 0xb5, 0xe4, 0x21,
	0x59, 0x93, 0x17, 0xa0, 0x25, 0x28, 0x68, 0x96, 0x5b, 0xba, 0x44, 0xb7, 0x7f, 0x9b, 0x45, 0x71,
	0x89, 0x54, 0x60, 0x48, 0x85, 0x65, 0x34, 0x8b, 0x1a, 0x60, 0x24, 0xc8, 0x19, 0xe8, 0x7b, 0x99,
	0x20, 0x69, 0x2d, 0xb1, 0xd5, 0x38, 0x80, 0x4e, 0xc9, 0x1a, 0xb7, 0x69, 0x94, 0xa3, 0xd5, 0xee,
	0x2b, 0x43, 0x3d, 0x38, 0xc0, 0x06, 0x4b, 0x78, 0xb6, 0x77, 0x33, 0xc0, 0x8a, 0xb3, 0xa2, 0x42,
	0xf2, 0x1a, 0x8c, 0x98, 0x15, 0x69, 0x56, 0xae, 0xb0, 0x31, 0x56, 0x0f, 0xfe, 0x02, 0x64, 0x00,
	0x2a, 0xe3, 0xd2, 0xd2, 0xae, 0x4b, 0x1e, 0xbb, 0xe0, 0xf3, 0x40, 0x65, 0xfc, 0xd2, 0x85, 0xd6,
	0xd6, 0x0f, 0xd2, 0x86, 0xd6, 0xd2, 0xbb, 0xf1, 0xfc, 0xcf, 0x9e, 0x79, 0x44, 0x9e, 0x42, 0x3b,
	0xb8, 0x9a, 0x86, 0xd3, 0xd9, 0x68, 0xee, 0x7b, 0x53, 0x53, 0x21, 0x06, 0x9c, 0x2e, 0xc6, 0x57,
	0x23, 0xcf, 0x54, 0x2f, 0xdf, 0x80, 0xea, 0x73, 0x02, 0xa0, 0x4d, 0xfc, 0xf9, 0x7c, 0xb6, 0x30,
	0x8f, 0x48, 0x07, 0xf4, 0xc0, 0xbf, 0xbd, 0x1d, 0x8f, 0x26, 0x37, 0xa6, 0xe2, 0xfe, 0x50, 0xa0,
	0x7d, 0x47, 0xeb, 0xc9, 0x36, 0x40, 0xe4, 0x13, 0x68, 0xdb, 0x14, 0x9c, 0x3d, 0x7e, 0xc5, 0x5e,
	0x52, 0xef, 0xe2, 0xbf, 0xad, 0x9d, 0xda, 0xc1, 0x51, 0xef, 0xfd, 0xaf, 0xdf, 0xe7, 0x6f, 0x97,
	0xcd, 0xdc, 0x83, 0x0b, 0xff, 0xa0, 0xee, 0x5a, 0x63, 0xf7, 0xcb, 0xbb, 0x7b, 0x96, 0xd3, 0xe2,
	0xde, 0xfe, 0xe8, 0x0a, 0x61, 0xc7, 0x6c, 0xe5, 0xc8, 0x7c, 0xc6, 0x2c, 0x77, 0x2a, 0x2c, 0xbf,
	0x65, 0x31, 0x56, 0xce, 0x61, 0xfc, 0x23, 0x4d, 0x4e, 0x7c, 0xf8, 0x13, 0x00, 0x00, 0xff, 0xff,
	0xf0, 0x83, 0x69, 0x4f, 0x17, 0x03, 0x00, 0x00,
}
