package AsyncJob

// Code generated by protoc-gen-svrkit-go. DO NOT EDIT.
// source: services/asyncjob/asyncjob.proto

/*
 This is a generated svrkit golang dev toolkit.
*/

import svrkit "gitlab.ttyuyin.com/golang/svrkit"

import context "golang.org/x/net/context"

import tlvpickle "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

var _ = context.TODO

// Magic Number for AsyncJob service
const AsyncJobMagic = uint16(14315)

// Client API for AsyncJob service

type AsyncJobClientInterface interface {
	AddJob(ctx context.Context, uin uint32, in *AsyncJobRequest, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
}

type AsyncJobClient struct {
	cc *svrkit.ClientConn
}

func NewAsyncJobClient(cc *svrkit.ClientConn) AsyncJobClientInterface {
	return &AsyncJobClient{cc}
}

const (
	commandAsyncJobGetSelfSvnInfo = 9995
	commandAsyncJobEcho           = 9999
	commandAsyncJobAddJob         = 1
)

func (c *AsyncJobClient) AddJob(ctx context.Context, uin uint32, in *AsyncJobRequest, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandAsyncJobAddJob, "/AsyncJob.AsyncJob/AddJob", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}
