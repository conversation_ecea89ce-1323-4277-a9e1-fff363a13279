// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/anchorcontract-go/anchorcontract-go.proto

package anchorcontract_go

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockAnchorContractGoClient is a mock of AnchorContractGoClient interface.
type MockAnchorContractGoClient struct {
	ctrl     *gomock.Controller
	recorder *MockAnchorContractGoClientMockRecorder
}

// MockAnchorContractGoClientMockRecorder is the mock recorder for MockAnchorContractGoClient.
type MockAnchorContractGoClientMockRecorder struct {
	mock *MockAnchorContractGoClient
}

// NewMockAnchorContractGoClient creates a new mock instance.
func NewMockAnchorContractGoClient(ctrl *gomock.Controller) *MockAnchorContractGoClient {
	mock := &MockAnchorContractGoClient{ctrl: ctrl}
	mock.recorder = &MockAnchorContractGoClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAnchorContractGoClient) EXPECT() *MockAnchorContractGoClientMockRecorder {
	return m.recorder
}

// ActorHandleExtensionContract mocks base method.
func (m *MockAnchorContractGoClient) ActorHandleExtensionContract(ctx context.Context, in *ActorHandleExtensionContractReq, opts ...grpc.CallOption) (*ActorHandleExtensionContractResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ActorHandleExtensionContract", varargs...)
	ret0, _ := ret[0].(*ActorHandleExtensionContractResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ActorHandleExtensionContract indicates an expected call of ActorHandleExtensionContract.
func (mr *MockAnchorContractGoClientMockRecorder) ActorHandleExtensionContract(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ActorHandleExtensionContract", reflect.TypeOf((*MockAnchorContractGoClient)(nil).ActorHandleExtensionContract), varargs...)
}

// AddAnchorCertContent mocks base method.
func (m *MockAnchorContractGoClient) AddAnchorCertContent(ctx context.Context, in *AddAnchorCertContentReq, opts ...grpc.CallOption) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddAnchorCertContent", varargs...)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddAnchorCertContent indicates an expected call of AddAnchorCertContent.
func (mr *MockAnchorContractGoClientMockRecorder) AddAnchorCertContent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAnchorCertContent", reflect.TypeOf((*MockAnchorContractGoClient)(nil).AddAnchorCertContent), varargs...)
}

// AddAnchorCertUpgradeTask mocks base method.
func (m *MockAnchorContractGoClient) AddAnchorCertUpgradeTask(ctx context.Context, in *AddAnchorCertUpgradeTaskReq, opts ...grpc.CallOption) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddAnchorCertUpgradeTask", varargs...)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddAnchorCertUpgradeTask indicates an expected call of AddAnchorCertUpgradeTask.
func (mr *MockAnchorContractGoClientMockRecorder) AddAnchorCertUpgradeTask(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAnchorCertUpgradeTask", reflect.TypeOf((*MockAnchorContractGoClient)(nil).AddAnchorCertUpgradeTask), varargs...)
}

// AddChildExamineCert mocks base method.
func (m *MockAnchorContractGoClient) AddChildExamineCert(ctx context.Context, in *ExamineCertInfo, opts ...grpc.CallOption) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddChildExamineCert", varargs...)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddChildExamineCert indicates an expected call of AddChildExamineCert.
func (mr *MockAnchorContractGoClientMockRecorder) AddChildExamineCert(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddChildExamineCert", reflect.TypeOf((*MockAnchorContractGoClient)(nil).AddChildExamineCert), varargs...)
}

// AddGuildSignAnchorInfo mocks base method.
func (m *MockAnchorContractGoClient) AddGuildSignAnchorInfo(ctx context.Context, in *AddGuildSignAnchorInfoReq, opts ...grpc.CallOption) (*AddGuildSignAnchorInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddGuildSignAnchorInfo", varargs...)
	ret0, _ := ret[0].(*AddGuildSignAnchorInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddGuildSignAnchorInfo indicates an expected call of AddGuildSignAnchorInfo.
func (mr *MockAnchorContractGoClientMockRecorder) AddGuildSignAnchorInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddGuildSignAnchorInfo", reflect.TypeOf((*MockAnchorContractGoClient)(nil).AddGuildSignAnchorInfo), varargs...)
}

// AddNeedConfirmWorkerType mocks base method.
func (m *MockAnchorContractGoClient) AddNeedConfirmWorkerType(ctx context.Context, in *AddNeedConfirmWorkerTypeReq, opts ...grpc.CallOption) (*AddNeedConfirmWorkerTypeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddNeedConfirmWorkerType", varargs...)
	ret0, _ := ret[0].(*AddNeedConfirmWorkerTypeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddNeedConfirmWorkerType indicates an expected call of AddNeedConfirmWorkerType.
func (mr *MockAnchorContractGoClientMockRecorder) AddNeedConfirmWorkerType(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddNeedConfirmWorkerType", reflect.TypeOf((*MockAnchorContractGoClient)(nil).AddNeedConfirmWorkerType), varargs...)
}

// AddParentExamineCert mocks base method.
func (m *MockAnchorContractGoClient) AddParentExamineCert(ctx context.Context, in *ExamineCertInfo, opts ...grpc.CallOption) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddParentExamineCert", varargs...)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddParentExamineCert indicates an expected call of AddParentExamineCert.
func (mr *MockAnchorContractGoClientMockRecorder) AddParentExamineCert(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddParentExamineCert", reflect.TypeOf((*MockAnchorContractGoClient)(nil).AddParentExamineCert), varargs...)
}

// AddSignRight mocks base method.
func (m *MockAnchorContractGoClient) AddSignRight(ctx context.Context, in *AddSignRightReq, opts ...grpc.CallOption) (*AddSignRightResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddSignRight", varargs...)
	ret0, _ := ret[0].(*AddSignRightResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddSignRight indicates an expected call of AddSignRight.
func (mr *MockAnchorContractGoClientMockRecorder) AddSignRight(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddSignRight", reflect.TypeOf((*MockAnchorContractGoClient)(nil).AddSignRight), varargs...)
}

// AddSignWhiteUid mocks base method.
func (m *MockAnchorContractGoClient) AddSignWhiteUid(ctx context.Context, in *AddSignWhiteUidReq, opts ...grpc.CallOption) (*AddSignWhiteUidResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddSignWhiteUid", varargs...)
	ret0, _ := ret[0].(*AddSignWhiteUidResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddSignWhiteUid indicates an expected call of AddSignWhiteUid.
func (mr *MockAnchorContractGoClientMockRecorder) AddSignWhiteUid(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddSignWhiteUid", reflect.TypeOf((*MockAnchorContractGoClient)(nil).AddSignWhiteUid), varargs...)
}

// ApplyCancelContract mocks base method.
func (m *MockAnchorContractGoClient) ApplyCancelContract(ctx context.Context, in *ApplyCancelContractReq, opts ...grpc.CallOption) (*ApplyCancelContractResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ApplyCancelContract", varargs...)
	ret0, _ := ret[0].(*ApplyCancelContractResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplyCancelContract indicates an expected call of ApplyCancelContract.
func (mr *MockAnchorContractGoClientMockRecorder) ApplyCancelContract(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplyCancelContract", reflect.TypeOf((*MockAnchorContractGoClient)(nil).ApplyCancelContract), varargs...)
}

// ApplyCancelContractNew mocks base method.
func (m *MockAnchorContractGoClient) ApplyCancelContractNew(ctx context.Context, in *ApplyCancelContractNewReq, opts ...grpc.CallOption) (*ApplyCancelContractNewResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ApplyCancelContractNew", varargs...)
	ret0, _ := ret[0].(*ApplyCancelContractNewResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplyCancelContractNew indicates an expected call of ApplyCancelContractNew.
func (mr *MockAnchorContractGoClientMockRecorder) ApplyCancelContractNew(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplyCancelContractNew", reflect.TypeOf((*MockAnchorContractGoClient)(nil).ApplyCancelContractNew), varargs...)
}

// ApplyCancelContractV2 mocks base method.
func (m *MockAnchorContractGoClient) ApplyCancelContractV2(ctx context.Context, in *ApplyCancelContractV2Req, opts ...grpc.CallOption) (*ApplyCancelContractResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ApplyCancelContractV2", varargs...)
	ret0, _ := ret[0].(*ApplyCancelContractResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplyCancelContractV2 indicates an expected call of ApplyCancelContractV2.
func (mr *MockAnchorContractGoClientMockRecorder) ApplyCancelContractV2(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplyCancelContractV2", reflect.TypeOf((*MockAnchorContractGoClient)(nil).ApplyCancelContractV2), varargs...)
}

// ApplySignContract mocks base method.
func (m *MockAnchorContractGoClient) ApplySignContract(ctx context.Context, in *ApplySignContractReq, opts ...grpc.CallOption) (*ApplySignContractResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ApplySignContract", varargs...)
	ret0, _ := ret[0].(*ApplySignContractResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplySignContract indicates an expected call of ApplySignContract.
func (mr *MockAnchorContractGoClientMockRecorder) ApplySignContract(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplySignContract", reflect.TypeOf((*MockAnchorContractGoClient)(nil).ApplySignContract), varargs...)
}

// ApplySignDoyen mocks base method.
func (m *MockAnchorContractGoClient) ApplySignDoyen(ctx context.Context, in *ApplySignDoyenReq, opts ...grpc.CallOption) (*ApplySignDoyenResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ApplySignDoyen", varargs...)
	ret0, _ := ret[0].(*ApplySignDoyenResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplySignDoyen indicates an expected call of ApplySignDoyen.
func (mr *MockAnchorContractGoClientMockRecorder) ApplySignDoyen(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplySignDoyen", reflect.TypeOf((*MockAnchorContractGoClient)(nil).ApplySignDoyen), varargs...)
}

// ApplySignEsport mocks base method.
func (m *MockAnchorContractGoClient) ApplySignEsport(ctx context.Context, in *ApplySignEsportReq, opts ...grpc.CallOption) (*ApplySignEsportResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ApplySignEsport", varargs...)
	ret0, _ := ret[0].(*ApplySignEsportResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplySignEsport indicates an expected call of ApplySignEsport.
func (mr *MockAnchorContractGoClientMockRecorder) ApplySignEsport(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplySignEsport", reflect.TypeOf((*MockAnchorContractGoClient)(nil).ApplySignEsport), varargs...)
}

// BatchCheckUserExamineCert mocks base method.
func (m *MockAnchorContractGoClient) BatchCheckUserExamineCert(ctx context.Context, in *BatchSetUserExamineCertReq, opts ...grpc.CallOption) (*BatchSetUserExamineCertResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchCheckUserExamineCert", varargs...)
	ret0, _ := ret[0].(*BatchSetUserExamineCertResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCheckUserExamineCert indicates an expected call of BatchCheckUserExamineCert.
func (mr *MockAnchorContractGoClientMockRecorder) BatchCheckUserExamineCert(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCheckUserExamineCert", reflect.TypeOf((*MockAnchorContractGoClient)(nil).BatchCheckUserExamineCert), varargs...)
}

// BatchDelUserExamineCert mocks base method.
func (m *MockAnchorContractGoClient) BatchDelUserExamineCert(ctx context.Context, in *BatchDelUserExamineCertReq, opts ...grpc.CallOption) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchDelUserExamineCert", varargs...)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchDelUserExamineCert indicates an expected call of BatchDelUserExamineCert.
func (mr *MockAnchorContractGoClientMockRecorder) BatchDelUserExamineCert(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDelUserExamineCert", reflect.TypeOf((*MockAnchorContractGoClient)(nil).BatchDelUserExamineCert), varargs...)
}

// BatchGetAnchorIdentity mocks base method.
func (m *MockAnchorContractGoClient) BatchGetAnchorIdentity(ctx context.Context, in *BatchGetAnchorIdentityReq, opts ...grpc.CallOption) (*BatchGetAnchorIdentityResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetAnchorIdentity", varargs...)
	ret0, _ := ret[0].(*BatchGetAnchorIdentityResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetAnchorIdentity indicates an expected call of BatchGetAnchorIdentity.
func (mr *MockAnchorContractGoClientMockRecorder) BatchGetAnchorIdentity(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAnchorIdentity", reflect.TypeOf((*MockAnchorContractGoClient)(nil).BatchGetAnchorIdentity), varargs...)
}

// BatchGetApplyBlacklist mocks base method.
func (m *MockAnchorContractGoClient) BatchGetApplyBlacklist(ctx context.Context, in *BatchGetApplyBlacklistReq, opts ...grpc.CallOption) (*BatchGetApplyBlacklistResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetApplyBlacklist", varargs...)
	ret0, _ := ret[0].(*BatchGetApplyBlacklistResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetApplyBlacklist indicates an expected call of BatchGetApplyBlacklist.
func (mr *MockAnchorContractGoClientMockRecorder) BatchGetApplyBlacklist(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetApplyBlacklist", reflect.TypeOf((*MockAnchorContractGoClient)(nil).BatchGetApplyBlacklist), varargs...)
}

// BatchGetContractInfo mocks base method.
func (m *MockAnchorContractGoClient) BatchGetContractInfo(ctx context.Context, in *BatchGetContractInfoReq, opts ...grpc.CallOption) (*BatchGetContractInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetContractInfo", varargs...)
	ret0, _ := ret[0].(*BatchGetContractInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetContractInfo indicates an expected call of BatchGetContractInfo.
func (mr *MockAnchorContractGoClientMockRecorder) BatchGetContractInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetContractInfo", reflect.TypeOf((*MockAnchorContractGoClient)(nil).BatchGetContractInfo), varargs...)
}

// BatchGetGuildUserScore mocks base method.
func (m *MockAnchorContractGoClient) BatchGetGuildUserScore(ctx context.Context, in *BatchGetGuildUserScoreReq, opts ...grpc.CallOption) (*BatchGetGuildUserScoreResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetGuildUserScore", varargs...)
	ret0, _ := ret[0].(*BatchGetGuildUserScoreResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetGuildUserScore indicates an expected call of BatchGetGuildUserScore.
func (mr *MockAnchorContractGoClientMockRecorder) BatchGetGuildUserScore(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetGuildUserScore", reflect.TypeOf((*MockAnchorContractGoClient)(nil).BatchGetGuildUserScore), varargs...)
}

// BatchGetLiveAnchorCert mocks base method.
func (m *MockAnchorContractGoClient) BatchGetLiveAnchorCert(ctx context.Context, in *BatchGetLiveAnchorCertReq, opts ...grpc.CallOption) (*BatchGetLiveAnchorCertResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetLiveAnchorCert", varargs...)
	ret0, _ := ret[0].(*BatchGetLiveAnchorCertResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetLiveAnchorCert indicates an expected call of BatchGetLiveAnchorCert.
func (mr *MockAnchorContractGoClientMockRecorder) BatchGetLiveAnchorCert(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetLiveAnchorCert", reflect.TypeOf((*MockAnchorContractGoClient)(nil).BatchGetLiveAnchorCert), varargs...)
}

// BatchGetUserAnchorIdentityLog mocks base method.
func (m *MockAnchorContractGoClient) BatchGetUserAnchorIdentityLog(ctx context.Context, in *BatchGetUserAnchorIdentityLogReq, opts ...grpc.CallOption) (*BatchGetUserAnchorIdentityLogResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetUserAnchorIdentityLog", varargs...)
	ret0, _ := ret[0].(*BatchGetUserAnchorIdentityLogResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserAnchorIdentityLog indicates an expected call of BatchGetUserAnchorIdentityLog.
func (mr *MockAnchorContractGoClientMockRecorder) BatchGetUserAnchorIdentityLog(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserAnchorIdentityLog", reflect.TypeOf((*MockAnchorContractGoClient)(nil).BatchGetUserAnchorIdentityLog), varargs...)
}

// BatchGetUserApplySignRecord mocks base method.
func (m *MockAnchorContractGoClient) BatchGetUserApplySignRecord(ctx context.Context, in *BatchGetUserApplySignRecordReq, opts ...grpc.CallOption) (*BatchGetUserApplySignRecordResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetUserApplySignRecord", varargs...)
	ret0, _ := ret[0].(*BatchGetUserApplySignRecordResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserApplySignRecord indicates an expected call of BatchGetUserApplySignRecord.
func (mr *MockAnchorContractGoClientMockRecorder) BatchGetUserApplySignRecord(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserApplySignRecord", reflect.TypeOf((*MockAnchorContractGoClient)(nil).BatchGetUserApplySignRecord), varargs...)
}

// BatchGetUserContract mocks base method.
func (m *MockAnchorContractGoClient) BatchGetUserContract(ctx context.Context, in *BatchGetUserContractReq, opts ...grpc.CallOption) (*BatchGetUserContractResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetUserContract", varargs...)
	ret0, _ := ret[0].(*BatchGetUserContractResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserContract indicates an expected call of BatchGetUserContract.
func (mr *MockAnchorContractGoClientMockRecorder) BatchGetUserContract(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserContract", reflect.TypeOf((*MockAnchorContractGoClient)(nil).BatchGetUserContract), varargs...)
}

// BatchGetUserContractCacheInfo mocks base method.
func (m *MockAnchorContractGoClient) BatchGetUserContractCacheInfo(ctx context.Context, in *BatchGetUserContractCacheInfoReq, opts ...grpc.CallOption) (*BatchGetUserContractCacheInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetUserContractCacheInfo", varargs...)
	ret0, _ := ret[0].(*BatchGetUserContractCacheInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserContractCacheInfo indicates an expected call of BatchGetUserContractCacheInfo.
func (mr *MockAnchorContractGoClientMockRecorder) BatchGetUserContractCacheInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserContractCacheInfo", reflect.TypeOf((*MockAnchorContractGoClient)(nil).BatchGetUserContractCacheInfo), varargs...)
}

// BatchGetUserExamineCert mocks base method.
func (m *MockAnchorContractGoClient) BatchGetUserExamineCert(ctx context.Context, in *BatchGetUserExamineCertReq, opts ...grpc.CallOption) (*BatchGetUserExamineCertResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetUserExamineCert", varargs...)
	ret0, _ := ret[0].(*BatchGetUserExamineCertResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserExamineCert indicates an expected call of BatchGetUserExamineCert.
func (mr *MockAnchorContractGoClientMockRecorder) BatchGetUserExamineCert(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserExamineCert", reflect.TypeOf((*MockAnchorContractGoClient)(nil).BatchGetUserExamineCert), varargs...)
}

// BatchGetUserLiveAnchorExamine mocks base method.
func (m *MockAnchorContractGoClient) BatchGetUserLiveAnchorExamine(ctx context.Context, in *BatchGetUserLiveAnchorExamineReq, opts ...grpc.CallOption) (*BatchGetUserLiveAnchorExamineResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetUserLiveAnchorExamine", varargs...)
	ret0, _ := ret[0].(*BatchGetUserLiveAnchorExamineResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserLiveAnchorExamine indicates an expected call of BatchGetUserLiveAnchorExamine.
func (mr *MockAnchorContractGoClientMockRecorder) BatchGetUserLiveAnchorExamine(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserLiveAnchorExamine", reflect.TypeOf((*MockAnchorContractGoClient)(nil).BatchGetUserLiveAnchorExamine), varargs...)
}

// BatchGuildExtensionContract mocks base method.
func (m *MockAnchorContractGoClient) BatchGuildExtensionContract(ctx context.Context, in *BatchGuildExtensionContractReq, opts ...grpc.CallOption) (*BatchGuildExtensionContractResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGuildExtensionContract", varargs...)
	ret0, _ := ret[0].(*BatchGuildExtensionContractResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGuildExtensionContract indicates an expected call of BatchGuildExtensionContract.
func (mr *MockAnchorContractGoClientMockRecorder) BatchGuildExtensionContract(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGuildExtensionContract", reflect.TypeOf((*MockAnchorContractGoClient)(nil).BatchGuildExtensionContract), varargs...)
}

// BatchSetUserExamineCert mocks base method.
func (m *MockAnchorContractGoClient) BatchSetUserExamineCert(ctx context.Context, in *BatchSetUserExamineCertReq, opts ...grpc.CallOption) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchSetUserExamineCert", varargs...)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchSetUserExamineCert indicates an expected call of BatchSetUserExamineCert.
func (mr *MockAnchorContractGoClientMockRecorder) BatchSetUserExamineCert(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSetUserExamineCert", reflect.TypeOf((*MockAnchorContractGoClient)(nil).BatchSetUserExamineCert), varargs...)
}

// CancelContractByUid mocks base method.
func (m *MockAnchorContractGoClient) CancelContractByUid(ctx context.Context, in *CancelContractByUidReq, opts ...grpc.CallOption) (*CancelContractByUidResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CancelContractByUid", varargs...)
	ret0, _ := ret[0].(*CancelContractByUidResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancelContractByUid indicates an expected call of CancelContractByUid.
func (mr *MockAnchorContractGoClientMockRecorder) CancelContractByUid(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelContractByUid", reflect.TypeOf((*MockAnchorContractGoClient)(nil).CancelContractByUid), varargs...)
}

// CensorVideo mocks base method.
func (m *MockAnchorContractGoClient) CensorVideo(ctx context.Context, in *CensorVideoReq, opts ...grpc.CallOption) (*CensorVideoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CensorVideo", varargs...)
	ret0, _ := ret[0].(*CensorVideoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CensorVideo indicates an expected call of CensorVideo.
func (mr *MockAnchorContractGoClientMockRecorder) CensorVideo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CensorVideo", reflect.TypeOf((*MockAnchorContractGoClient)(nil).CensorVideo), varargs...)
}

// CheckCanApplyCancelContract mocks base method.
func (m *MockAnchorContractGoClient) CheckCanApplyCancelContract(ctx context.Context, in *CheckCanApplyCancelContractReq, opts ...grpc.CallOption) (*CheckCanApplyCancelContractResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckCanApplyCancelContract", varargs...)
	ret0, _ := ret[0].(*CheckCanApplyCancelContractResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckCanApplyCancelContract indicates an expected call of CheckCanApplyCancelContract.
func (mr *MockAnchorContractGoClientMockRecorder) CheckCanApplyCancelContract(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckCanApplyCancelContract", reflect.TypeOf((*MockAnchorContractGoClient)(nil).CheckCanApplyCancelContract), varargs...)
}

// CheckCanApplyCancelContractV2 mocks base method.
func (m *MockAnchorContractGoClient) CheckCanApplyCancelContractV2(ctx context.Context, in *CheckCanApplyCancelContractV2Req, opts ...grpc.CallOption) (*CheckCanApplyCancelContractV2Resp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckCanApplyCancelContractV2", varargs...)
	ret0, _ := ret[0].(*CheckCanApplyCancelContractV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckCanApplyCancelContractV2 indicates an expected call of CheckCanApplyCancelContractV2.
func (mr *MockAnchorContractGoClientMockRecorder) CheckCanApplyCancelContractV2(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckCanApplyCancelContractV2", reflect.TypeOf((*MockAnchorContractGoClient)(nil).CheckCanApplyCancelContractV2), varargs...)
}

// CheckCanApplySign mocks base method.
func (m *MockAnchorContractGoClient) CheckCanApplySign(ctx context.Context, in *CheckCanApplySignReq, opts ...grpc.CallOption) (*CheckCanApplySignResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckCanApplySign", varargs...)
	ret0, _ := ret[0].(*CheckCanApplySignResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckCanApplySign indicates an expected call of CheckCanApplySign.
func (mr *MockAnchorContractGoClientMockRecorder) CheckCanApplySign(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckCanApplySign", reflect.TypeOf((*MockAnchorContractGoClient)(nil).CheckCanApplySign), varargs...)
}

// CheckIfGreatLiveAnchor mocks base method.
func (m *MockAnchorContractGoClient) CheckIfGreatLiveAnchor(ctx context.Context, in *CheckIfGreatLiveAnchorReq, opts ...grpc.CallOption) (*CheckIfGreatLiveAnchorResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckIfGreatLiveAnchor", varargs...)
	ret0, _ := ret[0].(*CheckIfGreatLiveAnchorResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIfGreatLiveAnchor indicates an expected call of CheckIfGreatLiveAnchor.
func (mr *MockAnchorContractGoClientMockRecorder) CheckIfGreatLiveAnchor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIfGreatLiveAnchor", reflect.TypeOf((*MockAnchorContractGoClient)(nil).CheckIfGreatLiveAnchor), varargs...)
}

// CheckIsSignWhiteUid mocks base method.
func (m *MockAnchorContractGoClient) CheckIsSignWhiteUid(ctx context.Context, in *CheckIsSignWhiteUidReq, opts ...grpc.CallOption) (*CheckIsSignWhiteUidResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckIsSignWhiteUid", varargs...)
	ret0, _ := ret[0].(*CheckIsSignWhiteUidResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIsSignWhiteUid indicates an expected call of CheckIsSignWhiteUid.
func (mr *MockAnchorContractGoClientMockRecorder) CheckIsSignWhiteUid(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIsSignWhiteUid", reflect.TypeOf((*MockAnchorContractGoClient)(nil).CheckIsSignWhiteUid), varargs...)
}

// CheckIsTotalNewMultiAnchor mocks base method.
func (m *MockAnchorContractGoClient) CheckIsTotalNewMultiAnchor(ctx context.Context, in *CheckIsTotalNewMultiAnchorReq, opts ...grpc.CallOption) (*CheckIsTotalNewMultiAnchorResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckIsTotalNewMultiAnchor", varargs...)
	ret0, _ := ret[0].(*CheckIsTotalNewMultiAnchorResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIsTotalNewMultiAnchor indicates an expected call of CheckIsTotalNewMultiAnchor.
func (mr *MockAnchorContractGoClientMockRecorder) CheckIsTotalNewMultiAnchor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIsTotalNewMultiAnchor", reflect.TypeOf((*MockAnchorContractGoClient)(nil).CheckIsTotalNewMultiAnchor), varargs...)
}

// CheckUserGreatLiveAnchor mocks base method.
func (m *MockAnchorContractGoClient) CheckUserGreatLiveAnchor(ctx context.Context, in *CheckUserGreatLiveAnchorReq, opts ...grpc.CallOption) (*CheckUserGreatLiveAnchorResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckUserGreatLiveAnchor", varargs...)
	ret0, _ := ret[0].(*CheckUserGreatLiveAnchorResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckUserGreatLiveAnchor indicates an expected call of CheckUserGreatLiveAnchor.
func (mr *MockAnchorContractGoClientMockRecorder) CheckUserGreatLiveAnchor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUserGreatLiveAnchor", reflect.TypeOf((*MockAnchorContractGoClient)(nil).CheckUserGreatLiveAnchor), varargs...)
}

// ContractClaimObsToken mocks base method.
func (m *MockAnchorContractGoClient) ContractClaimObsToken(ctx context.Context, in *ContractClaimObsTokenReq, opts ...grpc.CallOption) (*ContractClaimObsTokenResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ContractClaimObsToken", varargs...)
	ret0, _ := ret[0].(*ContractClaimObsTokenResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ContractClaimObsToken indicates an expected call of ContractClaimObsToken.
func (mr *MockAnchorContractGoClientMockRecorder) ContractClaimObsToken(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContractClaimObsToken", reflect.TypeOf((*MockAnchorContractGoClient)(nil).ContractClaimObsToken), varargs...)
}

// DelAnchorCertUpgradeTask mocks base method.
func (m *MockAnchorContractGoClient) DelAnchorCertUpgradeTask(ctx context.Context, in *DelAnchorCertUpgradeTaskReq, opts ...grpc.CallOption) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelAnchorCertUpgradeTask", varargs...)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelAnchorCertUpgradeTask indicates an expected call of DelAnchorCertUpgradeTask.
func (mr *MockAnchorContractGoClientMockRecorder) DelAnchorCertUpgradeTask(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelAnchorCertUpgradeTask", reflect.TypeOf((*MockAnchorContractGoClient)(nil).DelAnchorCertUpgradeTask), varargs...)
}

// DelSignDoyen mocks base method.
func (m *MockAnchorContractGoClient) DelSignDoyen(ctx context.Context, in *DelSignDoyenReq, opts ...grpc.CallOption) (*DelSignDoyenResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelSignDoyen", varargs...)
	ret0, _ := ret[0].(*DelSignDoyenResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelSignDoyen indicates an expected call of DelSignDoyen.
func (mr *MockAnchorContractGoClientMockRecorder) DelSignDoyen(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelSignDoyen", reflect.TypeOf((*MockAnchorContractGoClient)(nil).DelSignDoyen), varargs...)
}

// DelSignRight mocks base method.
func (m *MockAnchorContractGoClient) DelSignRight(ctx context.Context, in *DelSignRightReq, opts ...grpc.CallOption) (*DelSignRightResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelSignRight", varargs...)
	ret0, _ := ret[0].(*DelSignRightResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelSignRight indicates an expected call of DelSignRight.
func (mr *MockAnchorContractGoClientMockRecorder) DelSignRight(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelSignRight", reflect.TypeOf((*MockAnchorContractGoClient)(nil).DelSignRight), varargs...)
}

// DelSignWhiteUid mocks base method.
func (m *MockAnchorContractGoClient) DelSignWhiteUid(ctx context.Context, in *DelSignWhiteUidReq, opts ...grpc.CallOption) (*DelSignWhiteUidResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelSignWhiteUid", varargs...)
	ret0, _ := ret[0].(*DelSignWhiteUidResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelSignWhiteUid indicates an expected call of DelSignWhiteUid.
func (mr *MockAnchorContractGoClientMockRecorder) DelSignWhiteUid(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelSignWhiteUid", reflect.TypeOf((*MockAnchorContractGoClient)(nil).DelSignWhiteUid), varargs...)
}

// DelUserExamineCert mocks base method.
func (m *MockAnchorContractGoClient) DelUserExamineCert(ctx context.Context, in *DelUserExamineCertReq, opts ...grpc.CallOption) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelUserExamineCert", varargs...)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelUserExamineCert indicates an expected call of DelUserExamineCert.
func (mr *MockAnchorContractGoClientMockRecorder) DelUserExamineCert(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserExamineCert", reflect.TypeOf((*MockAnchorContractGoClient)(nil).DelUserExamineCert), varargs...)
}

// DeleteChildExamineCert mocks base method.
func (m *MockAnchorContractGoClient) DeleteChildExamineCert(ctx context.Context, in *CertItemReq, opts ...grpc.CallOption) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteChildExamineCert", varargs...)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteChildExamineCert indicates an expected call of DeleteChildExamineCert.
func (mr *MockAnchorContractGoClientMockRecorder) DeleteChildExamineCert(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteChildExamineCert", reflect.TypeOf((*MockAnchorContractGoClient)(nil).DeleteChildExamineCert), varargs...)
}

// DeleteParentExamineCert mocks base method.
func (m *MockAnchorContractGoClient) DeleteParentExamineCert(ctx context.Context, in *CertItemReq, opts ...grpc.CallOption) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteParentExamineCert", varargs...)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteParentExamineCert indicates an expected call of DeleteParentExamineCert.
func (mr *MockAnchorContractGoClientMockRecorder) DeleteParentExamineCert(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteParentExamineCert", reflect.TypeOf((*MockAnchorContractGoClient)(nil).DeleteParentExamineCert), varargs...)
}

// GetAllApplyBlacklist mocks base method.
func (m *MockAnchorContractGoClient) GetAllApplyBlacklist(ctx context.Context, in *GetAllApplyBlacklistReq, opts ...grpc.CallOption) (*GetAllApplyBlacklistResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllApplyBlacklist", varargs...)
	ret0, _ := ret[0].(*GetAllApplyBlacklistResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllApplyBlacklist indicates an expected call of GetAllApplyBlacklist.
func (mr *MockAnchorContractGoClientMockRecorder) GetAllApplyBlacklist(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllApplyBlacklist", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetAllApplyBlacklist), varargs...)
}

// GetAllApplySignRecord mocks base method.
func (m *MockAnchorContractGoClient) GetAllApplySignRecord(ctx context.Context, in *GetAllApplySignRecordReq, opts ...grpc.CallOption) (*GetAllApplySignRecordResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllApplySignRecord", varargs...)
	ret0, _ := ret[0].(*GetAllApplySignRecordResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllApplySignRecord indicates an expected call of GetAllApplySignRecord.
func (mr *MockAnchorContractGoClientMockRecorder) GetAllApplySignRecord(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllApplySignRecord", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetAllApplySignRecord), varargs...)
}

// GetAllLiveAnchorExamine mocks base method.
func (m *MockAnchorContractGoClient) GetAllLiveAnchorExamine(ctx context.Context, in *GetAllLiveAnchorExamineReq, opts ...grpc.CallOption) (*GetAllLiveAnchorExamineResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllLiveAnchorExamine", varargs...)
	ret0, _ := ret[0].(*GetAllLiveAnchorExamineResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllLiveAnchorExamine indicates an expected call of GetAllLiveAnchorExamine.
func (mr *MockAnchorContractGoClientMockRecorder) GetAllLiveAnchorExamine(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllLiveAnchorExamine", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetAllLiveAnchorExamine), varargs...)
}

// GetAnchorAgentUid mocks base method.
func (m *MockAnchorContractGoClient) GetAnchorAgentUid(ctx context.Context, in *GetAnchorAgentUidReq, opts ...grpc.CallOption) (*GetAnchorAgentUidResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAnchorAgentUid", varargs...)
	ret0, _ := ret[0].(*GetAnchorAgentUidResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorAgentUid indicates an expected call of GetAnchorAgentUid.
func (mr *MockAnchorContractGoClientMockRecorder) GetAnchorAgentUid(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorAgentUid", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetAnchorAgentUid), varargs...)
}

// GetAnchorCertListByItemId mocks base method.
func (m *MockAnchorContractGoClient) GetAnchorCertListByItemId(ctx context.Context, in *GetAnchorCertListByItemIdReq, opts ...grpc.CallOption) (*GetAnchorCertListByItemIdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAnchorCertListByItemId", varargs...)
	ret0, _ := ret[0].(*GetAnchorCertListByItemIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorCertListByItemId indicates an expected call of GetAnchorCertListByItemId.
func (mr *MockAnchorContractGoClientMockRecorder) GetAnchorCertListByItemId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorCertListByItemId", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetAnchorCertListByItemId), varargs...)
}

// GetAnchorCertTaskInfo mocks base method.
func (m *MockAnchorContractGoClient) GetAnchorCertTaskInfo(ctx context.Context, in *GetAnchorCertTaskInfoReq, opts ...grpc.CallOption) (*GetAnchorCertTaskInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAnchorCertTaskInfo", varargs...)
	ret0, _ := ret[0].(*GetAnchorCertTaskInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorCertTaskInfo indicates an expected call of GetAnchorCertTaskInfo.
func (mr *MockAnchorContractGoClientMockRecorder) GetAnchorCertTaskInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorCertTaskInfo", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetAnchorCertTaskInfo), varargs...)
}

// GetAnchorExtraCertHistory mocks base method.
func (m *MockAnchorContractGoClient) GetAnchorExtraCertHistory(ctx context.Context, in *GetAnchorExtraCertHistoryReq, opts ...grpc.CallOption) (*GetAnchorExtraCertHistoryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAnchorExtraCertHistory", varargs...)
	ret0, _ := ret[0].(*GetAnchorExtraCertHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorExtraCertHistory indicates an expected call of GetAnchorExtraCertHistory.
func (mr *MockAnchorContractGoClientMockRecorder) GetAnchorExtraCertHistory(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorExtraCertHistory", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetAnchorExtraCertHistory), varargs...)
}

// GetAnchorNoticeHandleHistory mocks base method.
func (m *MockAnchorContractGoClient) GetAnchorNoticeHandleHistory(ctx context.Context, in *GetAnchorNoticeHandleHistoryReq, opts ...grpc.CallOption) (*GetAnchorNoticeHandleHistoryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAnchorNoticeHandleHistory", varargs...)
	ret0, _ := ret[0].(*GetAnchorNoticeHandleHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorNoticeHandleHistory indicates an expected call of GetAnchorNoticeHandleHistory.
func (mr *MockAnchorContractGoClientMockRecorder) GetAnchorNoticeHandleHistory(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorNoticeHandleHistory", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetAnchorNoticeHandleHistory), varargs...)
}

// GetCancelContractApplyList mocks base method.
func (m *MockAnchorContractGoClient) GetCancelContractApplyList(ctx context.Context, in *GetCancelContractApplyListReq, opts ...grpc.CallOption) (*GetCancelContractApplyListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCancelContractApplyList", varargs...)
	ret0, _ := ret[0].(*GetCancelContractApplyListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCancelContractApplyList indicates an expected call of GetCancelContractApplyList.
func (mr *MockAnchorContractGoClientMockRecorder) GetCancelContractApplyList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCancelContractApplyList", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetCancelContractApplyList), varargs...)
}

// GetCancelContractTypeList mocks base method.
func (m *MockAnchorContractGoClient) GetCancelContractTypeList(ctx context.Context, in *GetCancelContractTypeListReq, opts ...grpc.CallOption) (*GetCancelContractTypeListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCancelContractTypeList", varargs...)
	ret0, _ := ret[0].(*GetCancelContractTypeListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCancelContractTypeList indicates an expected call of GetCancelContractTypeList.
func (mr *MockAnchorContractGoClientMockRecorder) GetCancelContractTypeList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCancelContractTypeList", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetCancelContractTypeList), varargs...)
}

// GetCancelPayAmount mocks base method.
func (m *MockAnchorContractGoClient) GetCancelPayAmount(ctx context.Context, in *GetCancelPayAmountReq, opts ...grpc.CallOption) (*GetCancelPayAmountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCancelPayAmount", varargs...)
	ret0, _ := ret[0].(*GetCancelPayAmountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCancelPayAmount indicates an expected call of GetCancelPayAmount.
func (mr *MockAnchorContractGoClientMockRecorder) GetCancelPayAmount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCancelPayAmount", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetCancelPayAmount), varargs...)
}

// GetChildExamineCertList mocks base method.
func (m *MockAnchorContractGoClient) GetChildExamineCertList(ctx context.Context, in *CertItemReq, opts ...grpc.CallOption) (*ChildExamineCertList, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChildExamineCertList", varargs...)
	ret0, _ := ret[0].(*ChildExamineCertList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChildExamineCertList indicates an expected call of GetChildExamineCertList.
func (mr *MockAnchorContractGoClientMockRecorder) GetChildExamineCertList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChildExamineCertList", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetChildExamineCertList), varargs...)
}

// GetContract mocks base method.
func (m *MockAnchorContractGoClient) GetContract(ctx context.Context, in *GetContractReq, opts ...grpc.CallOption) (*GetContractResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetContract", varargs...)
	ret0, _ := ret[0].(*GetContractResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetContract indicates an expected call of GetContract.
func (mr *MockAnchorContractGoClientMockRecorder) GetContract(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetContract", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetContract), varargs...)
}

// GetContractChangeInfo mocks base method.
func (m *MockAnchorContractGoClient) GetContractChangeInfo(ctx context.Context, in *GetContractChangeInfoReq, opts ...grpc.CallOption) (*GetContractChangeInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetContractChangeInfo", varargs...)
	ret0, _ := ret[0].(*GetContractChangeInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetContractChangeInfo indicates an expected call of GetContractChangeInfo.
func (mr *MockAnchorContractGoClientMockRecorder) GetContractChangeInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetContractChangeInfo", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetContractChangeInfo), varargs...)
}

// GetContractWithIdentity mocks base method.
func (m *MockAnchorContractGoClient) GetContractWithIdentity(ctx context.Context, in *GetContractWithIdentityReq, opts ...grpc.CallOption) (*GetContractWithIdentityResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetContractWithIdentity", varargs...)
	ret0, _ := ret[0].(*GetContractWithIdentityResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetContractWithIdentity indicates an expected call of GetContractWithIdentity.
func (mr *MockAnchorContractGoClientMockRecorder) GetContractWithIdentity(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetContractWithIdentity", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetContractWithIdentity), varargs...)
}

// GetContractWorkerConfigs mocks base method.
func (m *MockAnchorContractGoClient) GetContractWorkerConfigs(ctx context.Context, in *GetContractWorkerConfigsReq, opts ...grpc.CallOption) (*GetContractWorkerConfigsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetContractWorkerConfigs", varargs...)
	ret0, _ := ret[0].(*GetContractWorkerConfigsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetContractWorkerConfigs indicates an expected call of GetContractWorkerConfigs.
func (mr *MockAnchorContractGoClientMockRecorder) GetContractWorkerConfigs(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetContractWorkerConfigs", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetContractWorkerConfigs), varargs...)
}

// GetGuildAnchorExtInfoList mocks base method.
func (m *MockAnchorContractGoClient) GetGuildAnchorExtInfoList(ctx context.Context, in *GetGuildAnchorExtInfoListReq, opts ...grpc.CallOption) (*GetGuildAnchorExtInfoListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGuildAnchorExtInfoList", varargs...)
	ret0, _ := ret[0].(*GetGuildAnchorExtInfoListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildAnchorExtInfoList indicates an expected call of GetGuildAnchorExtInfoList.
func (mr *MockAnchorContractGoClientMockRecorder) GetGuildAnchorExtInfoList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildAnchorExtInfoList", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetGuildAnchorExtInfoList), varargs...)
}

// GetGuildAnchorIdentity mocks base method.
func (m *MockAnchorContractGoClient) GetGuildAnchorIdentity(ctx context.Context, in *GetGuildAnchorIdentityReq, opts ...grpc.CallOption) (*GetGuildAnchorIdentityResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGuildAnchorIdentity", varargs...)
	ret0, _ := ret[0].(*GetGuildAnchorIdentityResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildAnchorIdentity indicates an expected call of GetGuildAnchorIdentity.
func (mr *MockAnchorContractGoClientMockRecorder) GetGuildAnchorIdentity(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildAnchorIdentity", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetGuildAnchorIdentity), varargs...)
}

// GetGuildAnchorIdentityLog mocks base method.
func (m *MockAnchorContractGoClient) GetGuildAnchorIdentityLog(ctx context.Context, in *GetGuildAnchorIdentityLogReq, opts ...grpc.CallOption) (*GetGuildAnchorIdentityLogResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGuildAnchorIdentityLog", varargs...)
	ret0, _ := ret[0].(*GetGuildAnchorIdentityLogResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildAnchorIdentityLog indicates an expected call of GetGuildAnchorIdentityLog.
func (mr *MockAnchorContractGoClientMockRecorder) GetGuildAnchorIdentityLog(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildAnchorIdentityLog", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetGuildAnchorIdentityLog), varargs...)
}

// GetGuildApplySignRecord mocks base method.
func (m *MockAnchorContractGoClient) GetGuildApplySignRecord(ctx context.Context, in *GetGuildApplySignRecordReq, opts ...grpc.CallOption) (*GetGuildApplySignRecordResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGuildApplySignRecord", varargs...)
	ret0, _ := ret[0].(*GetGuildApplySignRecordResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildApplySignRecord indicates an expected call of GetGuildApplySignRecord.
func (mr *MockAnchorContractGoClientMockRecorder) GetGuildApplySignRecord(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildApplySignRecord", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetGuildApplySignRecord), varargs...)
}

// GetGuildApplySignRecordCnt mocks base method.
func (m *MockAnchorContractGoClient) GetGuildApplySignRecordCnt(ctx context.Context, in *GetGuildApplySignRecordCntReq, opts ...grpc.CallOption) (*GetGuildApplySignRecordCntResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGuildApplySignRecordCnt", varargs...)
	ret0, _ := ret[0].(*GetGuildApplySignRecordCntResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildApplySignRecordCnt indicates an expected call of GetGuildApplySignRecordCnt.
func (mr *MockAnchorContractGoClientMockRecorder) GetGuildApplySignRecordCnt(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildApplySignRecordCnt", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetGuildApplySignRecordCnt), varargs...)
}

// GetGuildApplySignRecordList mocks base method.
func (m *MockAnchorContractGoClient) GetGuildApplySignRecordList(ctx context.Context, in *GetGuildApplySignRecordListReq, opts ...grpc.CallOption) (*GetGuildApplySignRecordListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGuildApplySignRecordList", varargs...)
	ret0, _ := ret[0].(*GetGuildApplySignRecordListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildApplySignRecordList indicates an expected call of GetGuildApplySignRecordList.
func (mr *MockAnchorContractGoClientMockRecorder) GetGuildApplySignRecordList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildApplySignRecordList", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetGuildApplySignRecordList), varargs...)
}

// GetGuildCancelSignRecordList mocks base method.
func (m *MockAnchorContractGoClient) GetGuildCancelSignRecordList(ctx context.Context, in *GetGuildCancelSignRecordListReq, opts ...grpc.CallOption) (*GetGuildCancelSignRecordListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGuildCancelSignRecordList", varargs...)
	ret0, _ := ret[0].(*GetGuildCancelSignRecordListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildCancelSignRecordList indicates an expected call of GetGuildCancelSignRecordList.
func (mr *MockAnchorContractGoClientMockRecorder) GetGuildCancelSignRecordList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildCancelSignRecordList", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetGuildCancelSignRecordList), varargs...)
}

// GetGuildContract mocks base method.
func (m *MockAnchorContractGoClient) GetGuildContract(ctx context.Context, in *GetGuildContractReq, opts ...grpc.CallOption) (*GetGuildContractResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGuildContract", varargs...)
	ret0, _ := ret[0].(*GetGuildContractResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildContract indicates an expected call of GetGuildContract.
func (mr *MockAnchorContractGoClientMockRecorder) GetGuildContract(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildContract", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetGuildContract), varargs...)
}

// GetGuildContractByCond mocks base method.
func (m *MockAnchorContractGoClient) GetGuildContractByCond(ctx context.Context, in *GetGuildContractByCondReq, opts ...grpc.CallOption) (*GetGuildContractByCondResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGuildContractByCond", varargs...)
	ret0, _ := ret[0].(*GetGuildContractByCondResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildContractByCond indicates an expected call of GetGuildContractByCond.
func (mr *MockAnchorContractGoClientMockRecorder) GetGuildContractByCond(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildContractByCond", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetGuildContractByCond), varargs...)
}

// GetGuildContractByIdentity mocks base method.
func (m *MockAnchorContractGoClient) GetGuildContractByIdentity(ctx context.Context, in *GetGuildContractByIdentityReq, opts ...grpc.CallOption) (*GetGuildContractByIdentityResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGuildContractByIdentity", varargs...)
	ret0, _ := ret[0].(*GetGuildContractByIdentityResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildContractByIdentity indicates an expected call of GetGuildContractByIdentity.
func (mr *MockAnchorContractGoClientMockRecorder) GetGuildContractByIdentity(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildContractByIdentity", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetGuildContractByIdentity), varargs...)
}

// GetGuildContractSum mocks base method.
func (m *MockAnchorContractGoClient) GetGuildContractSum(ctx context.Context, in *GetGuildContractSumReq, opts ...grpc.CallOption) (*GetGuildContractSumResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGuildContractSum", varargs...)
	ret0, _ := ret[0].(*GetGuildContractSumResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildContractSum indicates an expected call of GetGuildContractSum.
func (mr *MockAnchorContractGoClientMockRecorder) GetGuildContractSum(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildContractSum", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetGuildContractSum), varargs...)
}

// GetGuildEsportScore mocks base method.
func (m *MockAnchorContractGoClient) GetGuildEsportScore(ctx context.Context, in *GetGuildEsportScoreReq, opts ...grpc.CallOption) (*GetGuildEsportScoreResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGuildEsportScore", varargs...)
	ret0, _ := ret[0].(*GetGuildEsportScoreResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildEsportScore indicates an expected call of GetGuildEsportScore.
func (mr *MockAnchorContractGoClientMockRecorder) GetGuildEsportScore(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildEsportScore", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetGuildEsportScore), varargs...)
}

// GetGuildLevel mocks base method.
func (m *MockAnchorContractGoClient) GetGuildLevel(ctx context.Context, in *GetGuildLevelReq, opts ...grpc.CallOption) (*GetGuildLevelResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGuildLevel", varargs...)
	ret0, _ := ret[0].(*GetGuildLevelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildLevel indicates an expected call of GetGuildLevel.
func (mr *MockAnchorContractGoClientMockRecorder) GetGuildLevel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildLevel", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetGuildLevel), varargs...)
}

// GetGuildLiveAnchorExamine mocks base method.
func (m *MockAnchorContractGoClient) GetGuildLiveAnchorExamine(ctx context.Context, in *GetGuildLiveAnchorExamineReq, opts ...grpc.CallOption) (*GetGuildLiveAnchorExamineResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGuildLiveAnchorExamine", varargs...)
	ret0, _ := ret[0].(*GetGuildLiveAnchorExamineResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildLiveAnchorExamine indicates an expected call of GetGuildLiveAnchorExamine.
func (mr *MockAnchorContractGoClientMockRecorder) GetGuildLiveAnchorExamine(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildLiveAnchorExamine", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetGuildLiveAnchorExamine), varargs...)
}

// GetGuildSignAnchorInfo mocks base method.
func (m *MockAnchorContractGoClient) GetGuildSignAnchorInfo(ctx context.Context, in *GetGuildSignAnchorInfoReq, opts ...grpc.CallOption) (*GetGuildSignAnchorInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGuildSignAnchorInfo", varargs...)
	ret0, _ := ret[0].(*GetGuildSignAnchorInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildSignAnchorInfo indicates an expected call of GetGuildSignAnchorInfo.
func (mr *MockAnchorContractGoClientMockRecorder) GetGuildSignAnchorInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildSignAnchorInfo", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetGuildSignAnchorInfo), varargs...)
}

// GetGuildSignRight mocks base method.
func (m *MockAnchorContractGoClient) GetGuildSignRight(ctx context.Context, in *GetGuildSignRightReq, opts ...grpc.CallOption) (*GetGuildSignRightResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGuildSignRight", varargs...)
	ret0, _ := ret[0].(*GetGuildSignRightResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildSignRight indicates an expected call of GetGuildSignRight.
func (mr *MockAnchorContractGoClientMockRecorder) GetGuildSignRight(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildSignRight", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetGuildSignRight), varargs...)
}

// GetIdentityChangeHistory mocks base method.
func (m *MockAnchorContractGoClient) GetIdentityChangeHistory(ctx context.Context, in *GetIdentityChangeHistoryReq, opts ...grpc.CallOption) (*GetIdentityChangeHistoryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetIdentityChangeHistory", varargs...)
	ret0, _ := ret[0].(*GetIdentityChangeHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIdentityChangeHistory indicates an expected call of GetIdentityChangeHistory.
func (mr *MockAnchorContractGoClientMockRecorder) GetIdentityChangeHistory(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIdentityChangeHistory", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetIdentityChangeHistory), varargs...)
}

// GetLiveAnchorExamineNotUpload mocks base method.
func (m *MockAnchorContractGoClient) GetLiveAnchorExamineNotUpload(ctx context.Context, in *GetLiveAnchorExamineNotUploadReq, opts ...grpc.CallOption) (*GetLiveAnchorExamineNotUploadResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLiveAnchorExamineNotUpload", varargs...)
	ret0, _ := ret[0].(*GetLiveAnchorExamineNotUploadResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLiveAnchorExamineNotUpload indicates an expected call of GetLiveAnchorExamineNotUpload.
func (mr *MockAnchorContractGoClientMockRecorder) GetLiveAnchorExamineNotUpload(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLiveAnchorExamineNotUpload", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetLiveAnchorExamineNotUpload), varargs...)
}

// GetMultiPlayerCenterEntry mocks base method.
func (m *MockAnchorContractGoClient) GetMultiPlayerCenterEntry(ctx context.Context, in *GetMultiPlayerCenterEntryReq, opts ...grpc.CallOption) (*GetMultiPlayerCenterEntryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMultiPlayerCenterEntry", varargs...)
	ret0, _ := ret[0].(*GetMultiPlayerCenterEntryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMultiPlayerCenterEntry indicates an expected call of GetMultiPlayerCenterEntry.
func (mr *MockAnchorContractGoClientMockRecorder) GetMultiPlayerCenterEntry(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiPlayerCenterEntry", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetMultiPlayerCenterEntry), varargs...)
}

// GetNeedConfirmWorkerType mocks base method.
func (m *MockAnchorContractGoClient) GetNeedConfirmWorkerType(ctx context.Context, in *GetNeedConfirmWorkerTypeReq, opts ...grpc.CallOption) (*GetNeedConfirmWorkerTypeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNeedConfirmWorkerType", varargs...)
	ret0, _ := ret[0].(*GetNeedConfirmWorkerTypeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNeedConfirmWorkerType indicates an expected call of GetNeedConfirmWorkerType.
func (mr *MockAnchorContractGoClientMockRecorder) GetNeedConfirmWorkerType(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNeedConfirmWorkerType", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetNeedConfirmWorkerType), varargs...)
}

// GetNegotiateReasonType mocks base method.
func (m *MockAnchorContractGoClient) GetNegotiateReasonType(ctx context.Context, in *GetNegotiateReasonTypeReq, opts ...grpc.CallOption) (*GetNegotiateReasonTypeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNegotiateReasonType", varargs...)
	ret0, _ := ret[0].(*GetNegotiateReasonTypeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNegotiateReasonType indicates an expected call of GetNegotiateReasonType.
func (mr *MockAnchorContractGoClientMockRecorder) GetNegotiateReasonType(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNegotiateReasonType", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetNegotiateReasonType), varargs...)
}

// GetNotUploadExamineAnchorList mocks base method.
func (m *MockAnchorContractGoClient) GetNotUploadExamineAnchorList(ctx context.Context, in *GetNotUploadExamineAnchorListReq, opts ...grpc.CallOption) (*GetNotUploadExamineAnchorListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNotUploadExamineAnchorList", varargs...)
	ret0, _ := ret[0].(*GetNotUploadExamineAnchorListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNotUploadExamineAnchorList indicates an expected call of GetNotUploadExamineAnchorList.
func (mr *MockAnchorContractGoClientMockRecorder) GetNotUploadExamineAnchorList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNotUploadExamineAnchorList", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetNotUploadExamineAnchorList), varargs...)
}

// GetOfficialCancelSignList mocks base method.
func (m *MockAnchorContractGoClient) GetOfficialCancelSignList(ctx context.Context, in *GetOfficialCancelSignListReq, opts ...grpc.CallOption) (*GetOfficialCancelSignListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetOfficialCancelSignList", varargs...)
	ret0, _ := ret[0].(*GetOfficialCancelSignListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOfficialCancelSignList indicates an expected call of GetOfficialCancelSignList.
func (mr *MockAnchorContractGoClientMockRecorder) GetOfficialCancelSignList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOfficialCancelSignList", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetOfficialCancelSignList), varargs...)
}

// GetParentExamineCertList mocks base method.
func (m *MockAnchorContractGoClient) GetParentExamineCertList(ctx context.Context, in *CertOffsetTypeReq, opts ...grpc.CallOption) (*ParentExamineCertList, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetParentExamineCertList", varargs...)
	ret0, _ := ret[0].(*ParentExamineCertList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParentExamineCertList indicates an expected call of GetParentExamineCertList.
func (mr *MockAnchorContractGoClientMockRecorder) GetParentExamineCertList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParentExamineCertList", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetParentExamineCertList), varargs...)
}

// GetRadioLiveAnchorExamine mocks base method.
func (m *MockAnchorContractGoClient) GetRadioLiveAnchorExamine(ctx context.Context, in *GetRadioLiveAnchorExamineReq, opts ...grpc.CallOption) (*GetRadioLiveAnchorExamineResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRadioLiveAnchorExamine", varargs...)
	ret0, _ := ret[0].(*GetRadioLiveAnchorExamineResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRadioLiveAnchorExamine indicates an expected call of GetRadioLiveAnchorExamine.
func (mr *MockAnchorContractGoClientMockRecorder) GetRadioLiveAnchorExamine(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRadioLiveAnchorExamine", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetRadioLiveAnchorExamine), varargs...)
}

// GetRecommendTopGuildList mocks base method.
func (m *MockAnchorContractGoClient) GetRecommendTopGuildList(ctx context.Context, in *GetRecommendTopGuildListReq, opts ...grpc.CallOption) (*GetRecommendTopGuildListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRecommendTopGuildList", varargs...)
	ret0, _ := ret[0].(*GetRecommendTopGuildListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecommendTopGuildList indicates an expected call of GetRecommendTopGuildList.
func (mr *MockAnchorContractGoClientMockRecorder) GetRecommendTopGuildList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecommendTopGuildList", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetRecommendTopGuildList), varargs...)
}

// GetRejectReason mocks base method.
func (m *MockAnchorContractGoClient) GetRejectReason(ctx context.Context, in *GetRejectReasonReq, opts ...grpc.CallOption) (*GetRejectReasonResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRejectReason", varargs...)
	ret0, _ := ret[0].(*GetRejectReasonResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRejectReason indicates an expected call of GetRejectReason.
func (mr *MockAnchorContractGoClientMockRecorder) GetRejectReason(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRejectReason", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetRejectReason), varargs...)
}

// GetSignEsportAuditToken mocks base method.
func (m *MockAnchorContractGoClient) GetSignEsportAuditToken(ctx context.Context, in *GetSignEsportAuditTokenReq, opts ...grpc.CallOption) (*GetSignEsportAuditTokenResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSignEsportAuditToken", varargs...)
	ret0, _ := ret[0].(*GetSignEsportAuditTokenResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSignEsportAuditToken indicates an expected call of GetSignEsportAuditToken.
func (mr *MockAnchorContractGoClientMockRecorder) GetSignEsportAuditToken(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSignEsportAuditToken", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetSignEsportAuditToken), varargs...)
}

// GetSignRightList mocks base method.
func (m *MockAnchorContractGoClient) GetSignRightList(ctx context.Context, in *GetSignRightListReq, opts ...grpc.CallOption) (*GetSignRightListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSignRightList", varargs...)
	ret0, _ := ret[0].(*GetSignRightListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSignRightList indicates an expected call of GetSignRightList.
func (mr *MockAnchorContractGoClientMockRecorder) GetSignRightList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSignRightList", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetSignRightList), varargs...)
}

// GetUserAnchorIdentityLog mocks base method.
func (m *MockAnchorContractGoClient) GetUserAnchorIdentityLog(ctx context.Context, in *GetUserAnchorIdentityLogReq, opts ...grpc.CallOption) (*GetUserAnchorIdentityLogResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserAnchorIdentityLog", varargs...)
	ret0, _ := ret[0].(*GetUserAnchorIdentityLogResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAnchorIdentityLog indicates an expected call of GetUserAnchorIdentityLog.
func (mr *MockAnchorContractGoClientMockRecorder) GetUserAnchorIdentityLog(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAnchorIdentityLog", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetUserAnchorIdentityLog), varargs...)
}

// GetUserApplySignRecord mocks base method.
func (m *MockAnchorContractGoClient) GetUserApplySignRecord(ctx context.Context, in *GetUserApplySignRecordReq, opts ...grpc.CallOption) (*GetUserApplySignRecordResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserApplySignRecord", varargs...)
	ret0, _ := ret[0].(*GetUserApplySignRecordResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserApplySignRecord indicates an expected call of GetUserApplySignRecord.
func (mr *MockAnchorContractGoClientMockRecorder) GetUserApplySignRecord(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserApplySignRecord", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetUserApplySignRecord), varargs...)
}

// GetUserContract mocks base method.
func (m *MockAnchorContractGoClient) GetUserContract(ctx context.Context, in *GetUserContractReq, opts ...grpc.CallOption) (*GetUserContractResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserContract", varargs...)
	ret0, _ := ret[0].(*GetUserContractResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserContract indicates an expected call of GetUserContract.
func (mr *MockAnchorContractGoClientMockRecorder) GetUserContract(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserContract", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetUserContract), varargs...)
}

// GetUserContractCacheInfo mocks base method.
func (m *MockAnchorContractGoClient) GetUserContractCacheInfo(ctx context.Context, in *GetUserContractCacheInfoReq, opts ...grpc.CallOption) (*ContractCacheInfo, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserContractCacheInfo", varargs...)
	ret0, _ := ret[0].(*ContractCacheInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserContractCacheInfo indicates an expected call of GetUserContractCacheInfo.
func (mr *MockAnchorContractGoClientMockRecorder) GetUserContractCacheInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserContractCacheInfo", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetUserContractCacheInfo), varargs...)
}

// GetUserExamineCert mocks base method.
func (m *MockAnchorContractGoClient) GetUserExamineCert(ctx context.Context, in *GetUserExamineCertReq, opts ...grpc.CallOption) (*GetUserExamineCertResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserExamineCert", varargs...)
	ret0, _ := ret[0].(*GetUserExamineCertResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserExamineCert indicates an expected call of GetUserExamineCert.
func (mr *MockAnchorContractGoClientMockRecorder) GetUserExamineCert(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserExamineCert", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetUserExamineCert), varargs...)
}

// GetUserPromoteInviteInfo mocks base method.
func (m *MockAnchorContractGoClient) GetUserPromoteInviteInfo(ctx context.Context, in *GetUserPromoteInviteInfoReq, opts ...grpc.CallOption) (*GetUserPromoteInviteInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserPromoteInviteInfo", varargs...)
	ret0, _ := ret[0].(*GetUserPromoteInviteInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPromoteInviteInfo indicates an expected call of GetUserPromoteInviteInfo.
func (mr *MockAnchorContractGoClientMockRecorder) GetUserPromoteInviteInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPromoteInviteInfo", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GetUserPromoteInviteInfo), varargs...)
}

// GuildExtensionContract mocks base method.
func (m *MockAnchorContractGoClient) GuildExtensionContract(ctx context.Context, in *GuildExtensionContractReq, opts ...grpc.CallOption) (*GuildExtensionContractResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GuildExtensionContract", varargs...)
	ret0, _ := ret[0].(*GuildExtensionContractResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GuildExtensionContract indicates an expected call of GuildExtensionContract.
func (mr *MockAnchorContractGoClientMockRecorder) GuildExtensionContract(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GuildExtensionContract", reflect.TypeOf((*MockAnchorContractGoClient)(nil).GuildExtensionContract), varargs...)
}

// HandleApplyBlackInfo mocks base method.
func (m *MockAnchorContractGoClient) HandleApplyBlackInfo(ctx context.Context, in *HandleApplyBlackInfoReq, opts ...grpc.CallOption) (*HandleApplyBlackInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HandleApplyBlackInfo", varargs...)
	ret0, _ := ret[0].(*HandleApplyBlackInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandleApplyBlackInfo indicates an expected call of HandleApplyBlackInfo.
func (mr *MockAnchorContractGoClientMockRecorder) HandleApplyBlackInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleApplyBlackInfo", reflect.TypeOf((*MockAnchorContractGoClient)(nil).HandleApplyBlackInfo), varargs...)
}

// HandleContractChange mocks base method.
func (m *MockAnchorContractGoClient) HandleContractChange(ctx context.Context, in *HandleContractChangeReq, opts ...grpc.CallOption) (*HandleContractChangeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HandleContractChange", varargs...)
	ret0, _ := ret[0].(*HandleContractChangeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandleContractChange indicates an expected call of HandleContractChange.
func (mr *MockAnchorContractGoClientMockRecorder) HandleContractChange(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleContractChange", reflect.TypeOf((*MockAnchorContractGoClient)(nil).HandleContractChange), varargs...)
}

// HandleFocusAnchor mocks base method.
func (m *MockAnchorContractGoClient) HandleFocusAnchor(ctx context.Context, in *HandleFocusAnchorReq, opts ...grpc.CallOption) (*HandleFocusAnchorResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HandleFocusAnchor", varargs...)
	ret0, _ := ret[0].(*HandleFocusAnchorResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandleFocusAnchor indicates an expected call of HandleFocusAnchor.
func (mr *MockAnchorContractGoClientMockRecorder) HandleFocusAnchor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleFocusAnchor", reflect.TypeOf((*MockAnchorContractGoClient)(nil).HandleFocusAnchor), varargs...)
}

// HandlerCancelContractApply mocks base method.
func (m *MockAnchorContractGoClient) HandlerCancelContractApply(ctx context.Context, in *HandlerCancelContractApplyReq, opts ...grpc.CallOption) (*HandlerCancelContractApplyResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HandlerCancelContractApply", varargs...)
	ret0, _ := ret[0].(*HandlerCancelContractApplyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandlerCancelContractApply indicates an expected call of HandlerCancelContractApply.
func (mr *MockAnchorContractGoClientMockRecorder) HandlerCancelContractApply(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandlerCancelContractApply", reflect.TypeOf((*MockAnchorContractGoClient)(nil).HandlerCancelContractApply), varargs...)
}

// InviteMemberChangeWorkerType mocks base method.
func (m *MockAnchorContractGoClient) InviteMemberChangeWorkerType(ctx context.Context, in *InviteMemberChangeWorkerTypeReq, opts ...grpc.CallOption) (*InviteMemberChangeWorkerTypeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InviteMemberChangeWorkerType", varargs...)
	ret0, _ := ret[0].(*InviteMemberChangeWorkerTypeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InviteMemberChangeWorkerType indicates an expected call of InviteMemberChangeWorkerType.
func (mr *MockAnchorContractGoClientMockRecorder) InviteMemberChangeWorkerType(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InviteMemberChangeWorkerType", reflect.TypeOf((*MockAnchorContractGoClient)(nil).InviteMemberChangeWorkerType), varargs...)
}

// InvitePromote mocks base method.
func (m *MockAnchorContractGoClient) InvitePromote(ctx context.Context, in *InvitePromoteReq, opts ...grpc.CallOption) (*InvitePromoteResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InvitePromote", varargs...)
	ret0, _ := ret[0].(*InvitePromoteResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InvitePromote indicates an expected call of InvitePromote.
func (mr *MockAnchorContractGoClientMockRecorder) InvitePromote(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InvitePromote", reflect.TypeOf((*MockAnchorContractGoClient)(nil).InvitePromote), varargs...)
}

// ListAnchorCertContent mocks base method.
func (m *MockAnchorContractGoClient) ListAnchorCertContent(ctx context.Context, in *ListAnchorCertContentReq, opts ...grpc.CallOption) (*ListAnchorCertContentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListAnchorCertContent", varargs...)
	ret0, _ := ret[0].(*ListAnchorCertContentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAnchorCertContent indicates an expected call of ListAnchorCertContent.
func (mr *MockAnchorContractGoClientMockRecorder) ListAnchorCertContent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAnchorCertContent", reflect.TypeOf((*MockAnchorContractGoClient)(nil).ListAnchorCertContent), varargs...)
}

// ListAnchorCertUpgradeTask mocks base method.
func (m *MockAnchorContractGoClient) ListAnchorCertUpgradeTask(ctx context.Context, in *ListAnchorCertUpgradeTaskReq, opts ...grpc.CallOption) (*ListAnchorCertUpgradeTaskResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListAnchorCertUpgradeTask", varargs...)
	ret0, _ := ret[0].(*ListAnchorCertUpgradeTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAnchorCertUpgradeTask indicates an expected call of ListAnchorCertUpgradeTask.
func (mr *MockAnchorContractGoClientMockRecorder) ListAnchorCertUpgradeTask(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAnchorCertUpgradeTask", reflect.TypeOf((*MockAnchorContractGoClient)(nil).ListAnchorCertUpgradeTask), varargs...)
}

// ListExamineCert mocks base method.
func (m *MockAnchorContractGoClient) ListExamineCert(ctx context.Context, in *ListExamineCertReq, opts ...grpc.CallOption) (*ListExamineCertResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListExamineCert", varargs...)
	ret0, _ := ret[0].(*ListExamineCertResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListExamineCert indicates an expected call of ListExamineCert.
func (mr *MockAnchorContractGoClientMockRecorder) ListExamineCert(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListExamineCert", reflect.TypeOf((*MockAnchorContractGoClient)(nil).ListExamineCert), varargs...)
}

// ListGuildSignAnchorInfo mocks base method.
func (m *MockAnchorContractGoClient) ListGuildSignAnchorInfo(ctx context.Context, in *ListGuildSignAnchorInfoReq, opts ...grpc.CallOption) (*ListGuildSignAnchorInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListGuildSignAnchorInfo", varargs...)
	ret0, _ := ret[0].(*ListGuildSignAnchorInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListGuildSignAnchorInfo indicates an expected call of ListGuildSignAnchorInfo.
func (mr *MockAnchorContractGoClientMockRecorder) ListGuildSignAnchorInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListGuildSignAnchorInfo", reflect.TypeOf((*MockAnchorContractGoClient)(nil).ListGuildSignAnchorInfo), varargs...)
}

// ListUserExamineCert mocks base method.
func (m *MockAnchorContractGoClient) ListUserExamineCert(ctx context.Context, in *ListUserExamineCertReq, opts ...grpc.CallOption) (*ListUserExamineCertResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListUserExamineCert", varargs...)
	ret0, _ := ret[0].(*ListUserExamineCertResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListUserExamineCert indicates an expected call of ListUserExamineCert.
func (mr *MockAnchorContractGoClientMockRecorder) ListUserExamineCert(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListUserExamineCert", reflect.TypeOf((*MockAnchorContractGoClient)(nil).ListUserExamineCert), varargs...)
}

// LockCancelPayAmount mocks base method.
func (m *MockAnchorContractGoClient) LockCancelPayAmount(ctx context.Context, in *LockCancelPayAmountReq, opts ...grpc.CallOption) (*LockCancelPayAmountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LockCancelPayAmount", varargs...)
	ret0, _ := ret[0].(*LockCancelPayAmountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LockCancelPayAmount indicates an expected call of LockCancelPayAmount.
func (mr *MockAnchorContractGoClientMockRecorder) LockCancelPayAmount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LockCancelPayAmount", reflect.TypeOf((*MockAnchorContractGoClient)(nil).LockCancelPayAmount), varargs...)
}

// ModifyWorkerType mocks base method.
func (m *MockAnchorContractGoClient) ModifyWorkerType(ctx context.Context, in *ModifyWorkerTypeReq, opts ...grpc.CallOption) (*ModifyWorkerTypeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ModifyWorkerType", varargs...)
	ret0, _ := ret[0].(*ModifyWorkerTypeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyWorkerType indicates an expected call of ModifyWorkerType.
func (mr *MockAnchorContractGoClientMockRecorder) ModifyWorkerType(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyWorkerType", reflect.TypeOf((*MockAnchorContractGoClient)(nil).ModifyWorkerType), varargs...)
}

// OfficialHandleApplySign mocks base method.
func (m *MockAnchorContractGoClient) OfficialHandleApplySign(ctx context.Context, in *OfficialHandleApplySignReq, opts ...grpc.CallOption) (*OfficialHandleApplySignResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "OfficialHandleApplySign", varargs...)
	ret0, _ := ret[0].(*OfficialHandleApplySignResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OfficialHandleApplySign indicates an expected call of OfficialHandleApplySign.
func (mr *MockAnchorContractGoClientMockRecorder) OfficialHandleApplySign(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OfficialHandleApplySign", reflect.TypeOf((*MockAnchorContractGoClient)(nil).OfficialHandleApplySign), varargs...)
}

// OfficialHandleApplySignEsport mocks base method.
func (m *MockAnchorContractGoClient) OfficialHandleApplySignEsport(ctx context.Context, in *OfficialHandleApplySignEsportReq, opts ...grpc.CallOption) (*OfficialHandleApplySignEsportResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "OfficialHandleApplySignEsport", varargs...)
	ret0, _ := ret[0].(*OfficialHandleApplySignEsportResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OfficialHandleApplySignEsport indicates an expected call of OfficialHandleApplySignEsport.
func (mr *MockAnchorContractGoClientMockRecorder) OfficialHandleApplySignEsport(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OfficialHandleApplySignEsport", reflect.TypeOf((*MockAnchorContractGoClient)(nil).OfficialHandleApplySignEsport), varargs...)
}

// OfficialHandleCancelSign mocks base method.
func (m *MockAnchorContractGoClient) OfficialHandleCancelSign(ctx context.Context, in *OfficialHandleCancelSignReq, opts ...grpc.CallOption) (*OfficialHandleCancelSignResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "OfficialHandleCancelSign", varargs...)
	ret0, _ := ret[0].(*OfficialHandleCancelSignResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OfficialHandleCancelSign indicates an expected call of OfficialHandleCancelSign.
func (mr *MockAnchorContractGoClientMockRecorder) OfficialHandleCancelSign(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OfficialHandleCancelSign", reflect.TypeOf((*MockAnchorContractGoClient)(nil).OfficialHandleCancelSign), varargs...)
}

// OfficialRemarkPayCancelSign mocks base method.
func (m *MockAnchorContractGoClient) OfficialRemarkPayCancelSign(ctx context.Context, in *OfficialRemarkPayCancelSignReq, opts ...grpc.CallOption) (*OfficialRemarkPayCancelSignResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "OfficialRemarkPayCancelSign", varargs...)
	ret0, _ := ret[0].(*OfficialRemarkPayCancelSignResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OfficialRemarkPayCancelSign indicates an expected call of OfficialRemarkPayCancelSign.
func (mr *MockAnchorContractGoClientMockRecorder) OfficialRemarkPayCancelSign(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OfficialRemarkPayCancelSign", reflect.TypeOf((*MockAnchorContractGoClient)(nil).OfficialRemarkPayCancelSign), varargs...)
}

// PresidentHandleAllApplySign mocks base method.
func (m *MockAnchorContractGoClient) PresidentHandleAllApplySign(ctx context.Context, in *PresidentHandleAllApplySignReq, opts ...grpc.CallOption) (*PresidentHandleAllApplySignResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PresidentHandleAllApplySign", varargs...)
	ret0, _ := ret[0].(*PresidentHandleAllApplySignResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PresidentHandleAllApplySign indicates an expected call of PresidentHandleAllApplySign.
func (mr *MockAnchorContractGoClientMockRecorder) PresidentHandleAllApplySign(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PresidentHandleAllApplySign", reflect.TypeOf((*MockAnchorContractGoClient)(nil).PresidentHandleAllApplySign), varargs...)
}

// PresidentHandleApplySign mocks base method.
func (m *MockAnchorContractGoClient) PresidentHandleApplySign(ctx context.Context, in *PresidentHandleApplySignReq, opts ...grpc.CallOption) (*PresidentHandleApplySignResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PresidentHandleApplySign", varargs...)
	ret0, _ := ret[0].(*PresidentHandleApplySignResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PresidentHandleApplySign indicates an expected call of PresidentHandleApplySign.
func (mr *MockAnchorContractGoClientMockRecorder) PresidentHandleApplySign(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PresidentHandleApplySign", reflect.TypeOf((*MockAnchorContractGoClient)(nil).PresidentHandleApplySign), varargs...)
}

// ProcPromoteInvite mocks base method.
func (m *MockAnchorContractGoClient) ProcPromoteInvite(ctx context.Context, in *ProcPromoteInviteReq, opts ...grpc.CallOption) (*ProcPromoteInviteResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcPromoteInvite", varargs...)
	ret0, _ := ret[0].(*ProcPromoteInviteResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcPromoteInvite indicates an expected call of ProcPromoteInvite.
func (mr *MockAnchorContractGoClientMockRecorder) ProcPromoteInvite(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcPromoteInvite", reflect.TypeOf((*MockAnchorContractGoClient)(nil).ProcPromoteInvite), varargs...)
}

// ReclaimAnchorIdentity mocks base method.
func (m *MockAnchorContractGoClient) ReclaimAnchorIdentity(ctx context.Context, in *ReclaimAnchorIdentityReq, opts ...grpc.CallOption) (*ReclaimAnchorIdentityResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReclaimAnchorIdentity", varargs...)
	ret0, _ := ret[0].(*ReclaimAnchorIdentityResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReclaimAnchorIdentity indicates an expected call of ReclaimAnchorIdentity.
func (mr *MockAnchorContractGoClientMockRecorder) ReclaimAnchorIdentity(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReclaimAnchorIdentity", reflect.TypeOf((*MockAnchorContractGoClient)(nil).ReclaimAnchorIdentity), varargs...)
}

// ReclaimGuildAllAnchorIdentity mocks base method.
func (m *MockAnchorContractGoClient) ReclaimGuildAllAnchorIdentity(ctx context.Context, in *ReclaimGuildAllAnchorIdentityReq, opts ...grpc.CallOption) (*ReclaimGuildAllAnchorIdentityResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReclaimGuildAllAnchorIdentity", varargs...)
	ret0, _ := ret[0].(*ReclaimGuildAllAnchorIdentityResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReclaimGuildAllAnchorIdentity indicates an expected call of ReclaimGuildAllAnchorIdentity.
func (mr *MockAnchorContractGoClientMockRecorder) ReclaimGuildAllAnchorIdentity(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReclaimGuildAllAnchorIdentity", reflect.TypeOf((*MockAnchorContractGoClient)(nil).ReclaimGuildAllAnchorIdentity), varargs...)
}

// RecordAnchorNoticeHandle mocks base method.
func (m *MockAnchorContractGoClient) RecordAnchorNoticeHandle(ctx context.Context, in *RecordAnchorNoticeHandleReq, opts ...grpc.CallOption) (*RecordAnchorNoticeHandleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RecordAnchorNoticeHandle", varargs...)
	ret0, _ := ret[0].(*RecordAnchorNoticeHandleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecordAnchorNoticeHandle indicates an expected call of RecordAnchorNoticeHandle.
func (mr *MockAnchorContractGoClientMockRecorder) RecordAnchorNoticeHandle(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordAnchorNoticeHandle", reflect.TypeOf((*MockAnchorContractGoClient)(nil).RecordAnchorNoticeHandle), varargs...)
}

// SendImExamineCert mocks base method.
func (m *MockAnchorContractGoClient) SendImExamineCert(ctx context.Context, in *SendImExamineCertReq, opts ...grpc.CallOption) (*SendImExamineCertResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendImExamineCert", varargs...)
	ret0, _ := ret[0].(*SendImExamineCertResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendImExamineCert indicates an expected call of SendImExamineCert.
func (mr *MockAnchorContractGoClientMockRecorder) SendImExamineCert(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendImExamineCert", reflect.TypeOf((*MockAnchorContractGoClient)(nil).SendImExamineCert), varargs...)
}

// SetAnchorCertWhiteList mocks base method.
func (m *MockAnchorContractGoClient) SetAnchorCertWhiteList(ctx context.Context, in *SetAnchorCertWhiteListReq, opts ...grpc.CallOption) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetAnchorCertWhiteList", varargs...)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetAnchorCertWhiteList indicates an expected call of SetAnchorCertWhiteList.
func (mr *MockAnchorContractGoClientMockRecorder) SetAnchorCertWhiteList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAnchorCertWhiteList", reflect.TypeOf((*MockAnchorContractGoClient)(nil).SetAnchorCertWhiteList), varargs...)
}

// SetAnchorExtraCert mocks base method.
func (m *MockAnchorContractGoClient) SetAnchorExtraCert(ctx context.Context, in *SetAnchorExtraCertReq, opts ...grpc.CallOption) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetAnchorExtraCert", varargs...)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetAnchorExtraCert indicates an expected call of SetAnchorExtraCert.
func (mr *MockAnchorContractGoClientMockRecorder) SetAnchorExtraCert(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAnchorExtraCert", reflect.TypeOf((*MockAnchorContractGoClient)(nil).SetAnchorExtraCert), varargs...)
}

// SetGuildCancelContractType mocks base method.
func (m *MockAnchorContractGoClient) SetGuildCancelContractType(ctx context.Context, in *SetGuildCancelContractTypeReq, opts ...grpc.CallOption) (*SetGuildCancelContractTypeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetGuildCancelContractType", varargs...)
	ret0, _ := ret[0].(*SetGuildCancelContractTypeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetGuildCancelContractType indicates an expected call of SetGuildCancelContractType.
func (mr *MockAnchorContractGoClientMockRecorder) SetGuildCancelContractType(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetGuildCancelContractType", reflect.TypeOf((*MockAnchorContractGoClient)(nil).SetGuildCancelContractType), varargs...)
}

// SetUserExamineCert mocks base method.
func (m *MockAnchorContractGoClient) SetUserExamineCert(ctx context.Context, in *UserExamineCertInfo, opts ...grpc.CallOption) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetUserExamineCert", varargs...)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserExamineCert indicates an expected call of SetUserExamineCert.
func (mr *MockAnchorContractGoClientMockRecorder) SetUserExamineCert(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserExamineCert", reflect.TypeOf((*MockAnchorContractGoClient)(nil).SetUserExamineCert), varargs...)
}

// TestHandleYearBanUser mocks base method.
func (m *MockAnchorContractGoClient) TestHandleYearBanUser(ctx context.Context, in *TestHandleYearBanUserReq, opts ...grpc.CallOption) (*TestHandleYearBanUserResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TestHandleYearBanUser", varargs...)
	ret0, _ := ret[0].(*TestHandleYearBanUserResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestHandleYearBanUser indicates an expected call of TestHandleYearBanUser.
func (mr *MockAnchorContractGoClientMockRecorder) TestHandleYearBanUser(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestHandleYearBanUser", reflect.TypeOf((*MockAnchorContractGoClient)(nil).TestHandleYearBanUser), varargs...)
}

// TestNotifyNegotiateExpire mocks base method.
func (m *MockAnchorContractGoClient) TestNotifyNegotiateExpire(ctx context.Context, in *TestNotifyNegotiateExpireReq, opts ...grpc.CallOption) (*TestNotifyNegotiateExpireResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TestNotifyNegotiateExpire", varargs...)
	ret0, _ := ret[0].(*TestNotifyNegotiateExpireResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestNotifyNegotiateExpire indicates an expected call of TestNotifyNegotiateExpire.
func (mr *MockAnchorContractGoClientMockRecorder) TestNotifyNegotiateExpire(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestNotifyNegotiateExpire", reflect.TypeOf((*MockAnchorContractGoClient)(nil).TestNotifyNegotiateExpire), varargs...)
}

// TestSignContract mocks base method.
func (m *MockAnchorContractGoClient) TestSignContract(ctx context.Context, in *TestSignContractReq, opts ...grpc.CallOption) (*TestSignContractResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TestSignContract", varargs...)
	ret0, _ := ret[0].(*TestSignContractResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestSignContract indicates an expected call of TestSignContract.
func (mr *MockAnchorContractGoClientMockRecorder) TestSignContract(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestSignContract", reflect.TypeOf((*MockAnchorContractGoClient)(nil).TestSignContract), varargs...)
}

// TriggerTimer mocks base method.
func (m *MockAnchorContractGoClient) TriggerTimer(ctx context.Context, in *TriggerTimerReq, opts ...grpc.CallOption) (*TriggerTimerResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TriggerTimer", varargs...)
	ret0, _ := ret[0].(*TriggerTimerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TriggerTimer indicates an expected call of TriggerTimer.
func (mr *MockAnchorContractGoClientMockRecorder) TriggerTimer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TriggerTimer", reflect.TypeOf((*MockAnchorContractGoClient)(nil).TriggerTimer), varargs...)
}

// UpdateAnchorCertContent mocks base method.
func (m *MockAnchorContractGoClient) UpdateAnchorCertContent(ctx context.Context, in *UpdateAnchorCertContentReq, opts ...grpc.CallOption) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateAnchorCertContent", varargs...)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAnchorCertContent indicates an expected call of UpdateAnchorCertContent.
func (mr *MockAnchorContractGoClientMockRecorder) UpdateAnchorCertContent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAnchorCertContent", reflect.TypeOf((*MockAnchorContractGoClient)(nil).UpdateAnchorCertContent), varargs...)
}

// UpdateAnchorCertUpgradeTask mocks base method.
func (m *MockAnchorContractGoClient) UpdateAnchorCertUpgradeTask(ctx context.Context, in *UpdateAnchorCertUpgradeTaskReq, opts ...grpc.CallOption) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateAnchorCertUpgradeTask", varargs...)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAnchorCertUpgradeTask indicates an expected call of UpdateAnchorCertUpgradeTask.
func (mr *MockAnchorContractGoClientMockRecorder) UpdateAnchorCertUpgradeTask(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAnchorCertUpgradeTask", reflect.TypeOf((*MockAnchorContractGoClient)(nil).UpdateAnchorCertUpgradeTask), varargs...)
}

// UpdateChildExamineCert mocks base method.
func (m *MockAnchorContractGoClient) UpdateChildExamineCert(ctx context.Context, in *ExamineCertInfo, opts ...grpc.CallOption) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateChildExamineCert", varargs...)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateChildExamineCert indicates an expected call of UpdateChildExamineCert.
func (mr *MockAnchorContractGoClientMockRecorder) UpdateChildExamineCert(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateChildExamineCert", reflect.TypeOf((*MockAnchorContractGoClient)(nil).UpdateChildExamineCert), varargs...)
}

// UpdateGuildSignRight mocks base method.
func (m *MockAnchorContractGoClient) UpdateGuildSignRight(ctx context.Context, in *UpdateGuildSignRightReq, opts ...grpc.CallOption) (*UpdateGuildSignRightResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateGuildSignRight", varargs...)
	ret0, _ := ret[0].(*UpdateGuildSignRightResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateGuildSignRight indicates an expected call of UpdateGuildSignRight.
func (mr *MockAnchorContractGoClientMockRecorder) UpdateGuildSignRight(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateGuildSignRight", reflect.TypeOf((*MockAnchorContractGoClient)(nil).UpdateGuildSignRight), varargs...)
}

// UpdateLiveAnchorExamineStatus mocks base method.
func (m *MockAnchorContractGoClient) UpdateLiveAnchorExamineStatus(ctx context.Context, in *UpdateLiveAnchorExamineStatusReq, opts ...grpc.CallOption) (*UpdateLiveAnchorExamineStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateLiveAnchorExamineStatus", varargs...)
	ret0, _ := ret[0].(*UpdateLiveAnchorExamineStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateLiveAnchorExamineStatus indicates an expected call of UpdateLiveAnchorExamineStatus.
func (mr *MockAnchorContractGoClientMockRecorder) UpdateLiveAnchorExamineStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLiveAnchorExamineStatus", reflect.TypeOf((*MockAnchorContractGoClient)(nil).UpdateLiveAnchorExamineStatus), varargs...)
}

// UpdateLiveAnchorExamineTime mocks base method.
func (m *MockAnchorContractGoClient) UpdateLiveAnchorExamineTime(ctx context.Context, in *UpdateLiveAnchorExamineTimeReq, opts ...grpc.CallOption) (*UpdateLiveAnchorExamineTimeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateLiveAnchorExamineTime", varargs...)
	ret0, _ := ret[0].(*UpdateLiveAnchorExamineTimeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateLiveAnchorExamineTime indicates an expected call of UpdateLiveAnchorExamineTime.
func (mr *MockAnchorContractGoClientMockRecorder) UpdateLiveAnchorExamineTime(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLiveAnchorExamineTime", reflect.TypeOf((*MockAnchorContractGoClient)(nil).UpdateLiveAnchorExamineTime), varargs...)
}

// UpdateParentExamineCert mocks base method.
func (m *MockAnchorContractGoClient) UpdateParentExamineCert(ctx context.Context, in *ExamineCertInfo, opts ...grpc.CallOption) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateParentExamineCert", varargs...)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateParentExamineCert indicates an expected call of UpdateParentExamineCert.
func (mr *MockAnchorContractGoClientMockRecorder) UpdateParentExamineCert(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateParentExamineCert", reflect.TypeOf((*MockAnchorContractGoClient)(nil).UpdateParentExamineCert), varargs...)
}

// UpdateRadioLiveAnchorExamine mocks base method.
func (m *MockAnchorContractGoClient) UpdateRadioLiveAnchorExamine(ctx context.Context, in *UpdateRadioLiveAnchorExamineReq, opts ...grpc.CallOption) (*UpdateRadioLiveAnchorExamineResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateRadioLiveAnchorExamine", varargs...)
	ret0, _ := ret[0].(*UpdateRadioLiveAnchorExamineResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateRadioLiveAnchorExamine indicates an expected call of UpdateRadioLiveAnchorExamine.
func (mr *MockAnchorContractGoClientMockRecorder) UpdateRadioLiveAnchorExamine(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRadioLiveAnchorExamine", reflect.TypeOf((*MockAnchorContractGoClient)(nil).UpdateRadioLiveAnchorExamine), varargs...)
}

// UpdateRemark mocks base method.
func (m *MockAnchorContractGoClient) UpdateRemark(ctx context.Context, in *UpdateRemarkReq, opts ...grpc.CallOption) (*UpdateRemarkResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateRemark", varargs...)
	ret0, _ := ret[0].(*UpdateRemarkResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateRemark indicates an expected call of UpdateRemark.
func (mr *MockAnchorContractGoClientMockRecorder) UpdateRemark(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRemark", reflect.TypeOf((*MockAnchorContractGoClient)(nil).UpdateRemark), varargs...)
}

// UpdateSignRight mocks base method.
func (m *MockAnchorContractGoClient) UpdateSignRight(ctx context.Context, in *UpdateSignRightReq, opts ...grpc.CallOption) (*UpdateSignRightResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateSignRight", varargs...)
	ret0, _ := ret[0].(*UpdateSignRightResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateSignRight indicates an expected call of UpdateSignRight.
func (mr *MockAnchorContractGoClientMockRecorder) UpdateSignRight(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSignRight", reflect.TypeOf((*MockAnchorContractGoClient)(nil).UpdateSignRight), varargs...)
}

// UpdateSignedAnchorAgentId mocks base method.
func (m *MockAnchorContractGoClient) UpdateSignedAnchorAgentId(ctx context.Context, in *UpdateSignedAnchorAgentIdReq, opts ...grpc.CallOption) (*UpdateSignedAnchorAgentIdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateSignedAnchorAgentId", varargs...)
	ret0, _ := ret[0].(*UpdateSignedAnchorAgentIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateSignedAnchorAgentId indicates an expected call of UpdateSignedAnchorAgentId.
func (mr *MockAnchorContractGoClientMockRecorder) UpdateSignedAnchorAgentId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSignedAnchorAgentId", reflect.TypeOf((*MockAnchorContractGoClient)(nil).UpdateSignedAnchorAgentId), varargs...)
}

// UpdateUserExamineCert mocks base method.
func (m *MockAnchorContractGoClient) UpdateUserExamineCert(ctx context.Context, in *UserExamineCertInfo, opts ...grpc.CallOption) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateUserExamineCert", varargs...)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateUserExamineCert indicates an expected call of UpdateUserExamineCert.
func (mr *MockAnchorContractGoClientMockRecorder) UpdateUserExamineCert(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserExamineCert", reflect.TypeOf((*MockAnchorContractGoClient)(nil).UpdateUserExamineCert), varargs...)
}

// WithdrawApplySign mocks base method.
func (m *MockAnchorContractGoClient) WithdrawApplySign(ctx context.Context, in *WithdrawApplySignReq, opts ...grpc.CallOption) (*WithdrawApplySignResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "WithdrawApplySign", varargs...)
	ret0, _ := ret[0].(*WithdrawApplySignResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// WithdrawApplySign indicates an expected call of WithdrawApplySign.
func (mr *MockAnchorContractGoClientMockRecorder) WithdrawApplySign(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithdrawApplySign", reflect.TypeOf((*MockAnchorContractGoClient)(nil).WithdrawApplySign), varargs...)
}

// MockAnchorContractGoServer is a mock of AnchorContractGoServer interface.
type MockAnchorContractGoServer struct {
	ctrl     *gomock.Controller
	recorder *MockAnchorContractGoServerMockRecorder
}

// MockAnchorContractGoServerMockRecorder is the mock recorder for MockAnchorContractGoServer.
type MockAnchorContractGoServerMockRecorder struct {
	mock *MockAnchorContractGoServer
}

// NewMockAnchorContractGoServer creates a new mock instance.
func NewMockAnchorContractGoServer(ctrl *gomock.Controller) *MockAnchorContractGoServer {
	mock := &MockAnchorContractGoServer{ctrl: ctrl}
	mock.recorder = &MockAnchorContractGoServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAnchorContractGoServer) EXPECT() *MockAnchorContractGoServerMockRecorder {
	return m.recorder
}

// ActorHandleExtensionContract mocks base method.
func (m *MockAnchorContractGoServer) ActorHandleExtensionContract(ctx context.Context, in *ActorHandleExtensionContractReq) (*ActorHandleExtensionContractResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ActorHandleExtensionContract", ctx, in)
	ret0, _ := ret[0].(*ActorHandleExtensionContractResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ActorHandleExtensionContract indicates an expected call of ActorHandleExtensionContract.
func (mr *MockAnchorContractGoServerMockRecorder) ActorHandleExtensionContract(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ActorHandleExtensionContract", reflect.TypeOf((*MockAnchorContractGoServer)(nil).ActorHandleExtensionContract), ctx, in)
}

// AddAnchorCertContent mocks base method.
func (m *MockAnchorContractGoServer) AddAnchorCertContent(ctx context.Context, in *AddAnchorCertContentReq) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddAnchorCertContent", ctx, in)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddAnchorCertContent indicates an expected call of AddAnchorCertContent.
func (mr *MockAnchorContractGoServerMockRecorder) AddAnchorCertContent(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAnchorCertContent", reflect.TypeOf((*MockAnchorContractGoServer)(nil).AddAnchorCertContent), ctx, in)
}

// AddAnchorCertUpgradeTask mocks base method.
func (m *MockAnchorContractGoServer) AddAnchorCertUpgradeTask(ctx context.Context, in *AddAnchorCertUpgradeTaskReq) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddAnchorCertUpgradeTask", ctx, in)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddAnchorCertUpgradeTask indicates an expected call of AddAnchorCertUpgradeTask.
func (mr *MockAnchorContractGoServerMockRecorder) AddAnchorCertUpgradeTask(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAnchorCertUpgradeTask", reflect.TypeOf((*MockAnchorContractGoServer)(nil).AddAnchorCertUpgradeTask), ctx, in)
}

// AddChildExamineCert mocks base method.
func (m *MockAnchorContractGoServer) AddChildExamineCert(ctx context.Context, in *ExamineCertInfo) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddChildExamineCert", ctx, in)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddChildExamineCert indicates an expected call of AddChildExamineCert.
func (mr *MockAnchorContractGoServerMockRecorder) AddChildExamineCert(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddChildExamineCert", reflect.TypeOf((*MockAnchorContractGoServer)(nil).AddChildExamineCert), ctx, in)
}

// AddGuildSignAnchorInfo mocks base method.
func (m *MockAnchorContractGoServer) AddGuildSignAnchorInfo(ctx context.Context, in *AddGuildSignAnchorInfoReq) (*AddGuildSignAnchorInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddGuildSignAnchorInfo", ctx, in)
	ret0, _ := ret[0].(*AddGuildSignAnchorInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddGuildSignAnchorInfo indicates an expected call of AddGuildSignAnchorInfo.
func (mr *MockAnchorContractGoServerMockRecorder) AddGuildSignAnchorInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddGuildSignAnchorInfo", reflect.TypeOf((*MockAnchorContractGoServer)(nil).AddGuildSignAnchorInfo), ctx, in)
}

// AddNeedConfirmWorkerType mocks base method.
func (m *MockAnchorContractGoServer) AddNeedConfirmWorkerType(ctx context.Context, in *AddNeedConfirmWorkerTypeReq) (*AddNeedConfirmWorkerTypeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddNeedConfirmWorkerType", ctx, in)
	ret0, _ := ret[0].(*AddNeedConfirmWorkerTypeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddNeedConfirmWorkerType indicates an expected call of AddNeedConfirmWorkerType.
func (mr *MockAnchorContractGoServerMockRecorder) AddNeedConfirmWorkerType(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddNeedConfirmWorkerType", reflect.TypeOf((*MockAnchorContractGoServer)(nil).AddNeedConfirmWorkerType), ctx, in)
}

// AddParentExamineCert mocks base method.
func (m *MockAnchorContractGoServer) AddParentExamineCert(ctx context.Context, in *ExamineCertInfo) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddParentExamineCert", ctx, in)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddParentExamineCert indicates an expected call of AddParentExamineCert.
func (mr *MockAnchorContractGoServerMockRecorder) AddParentExamineCert(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddParentExamineCert", reflect.TypeOf((*MockAnchorContractGoServer)(nil).AddParentExamineCert), ctx, in)
}

// AddSignRight mocks base method.
func (m *MockAnchorContractGoServer) AddSignRight(ctx context.Context, in *AddSignRightReq) (*AddSignRightResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddSignRight", ctx, in)
	ret0, _ := ret[0].(*AddSignRightResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddSignRight indicates an expected call of AddSignRight.
func (mr *MockAnchorContractGoServerMockRecorder) AddSignRight(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddSignRight", reflect.TypeOf((*MockAnchorContractGoServer)(nil).AddSignRight), ctx, in)
}

// AddSignWhiteUid mocks base method.
func (m *MockAnchorContractGoServer) AddSignWhiteUid(ctx context.Context, in *AddSignWhiteUidReq) (*AddSignWhiteUidResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddSignWhiteUid", ctx, in)
	ret0, _ := ret[0].(*AddSignWhiteUidResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddSignWhiteUid indicates an expected call of AddSignWhiteUid.
func (mr *MockAnchorContractGoServerMockRecorder) AddSignWhiteUid(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddSignWhiteUid", reflect.TypeOf((*MockAnchorContractGoServer)(nil).AddSignWhiteUid), ctx, in)
}

// ApplyCancelContract mocks base method.
func (m *MockAnchorContractGoServer) ApplyCancelContract(ctx context.Context, in *ApplyCancelContractReq) (*ApplyCancelContractResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplyCancelContract", ctx, in)
	ret0, _ := ret[0].(*ApplyCancelContractResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplyCancelContract indicates an expected call of ApplyCancelContract.
func (mr *MockAnchorContractGoServerMockRecorder) ApplyCancelContract(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplyCancelContract", reflect.TypeOf((*MockAnchorContractGoServer)(nil).ApplyCancelContract), ctx, in)
}

// ApplyCancelContractNew mocks base method.
func (m *MockAnchorContractGoServer) ApplyCancelContractNew(ctx context.Context, in *ApplyCancelContractNewReq) (*ApplyCancelContractNewResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplyCancelContractNew", ctx, in)
	ret0, _ := ret[0].(*ApplyCancelContractNewResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplyCancelContractNew indicates an expected call of ApplyCancelContractNew.
func (mr *MockAnchorContractGoServerMockRecorder) ApplyCancelContractNew(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplyCancelContractNew", reflect.TypeOf((*MockAnchorContractGoServer)(nil).ApplyCancelContractNew), ctx, in)
}

// ApplyCancelContractV2 mocks base method.
func (m *MockAnchorContractGoServer) ApplyCancelContractV2(ctx context.Context, in *ApplyCancelContractV2Req) (*ApplyCancelContractResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplyCancelContractV2", ctx, in)
	ret0, _ := ret[0].(*ApplyCancelContractResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplyCancelContractV2 indicates an expected call of ApplyCancelContractV2.
func (mr *MockAnchorContractGoServerMockRecorder) ApplyCancelContractV2(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplyCancelContractV2", reflect.TypeOf((*MockAnchorContractGoServer)(nil).ApplyCancelContractV2), ctx, in)
}

// ApplySignContract mocks base method.
func (m *MockAnchorContractGoServer) ApplySignContract(ctx context.Context, in *ApplySignContractReq) (*ApplySignContractResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplySignContract", ctx, in)
	ret0, _ := ret[0].(*ApplySignContractResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplySignContract indicates an expected call of ApplySignContract.
func (mr *MockAnchorContractGoServerMockRecorder) ApplySignContract(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplySignContract", reflect.TypeOf((*MockAnchorContractGoServer)(nil).ApplySignContract), ctx, in)
}

// ApplySignDoyen mocks base method.
func (m *MockAnchorContractGoServer) ApplySignDoyen(ctx context.Context, in *ApplySignDoyenReq) (*ApplySignDoyenResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplySignDoyen", ctx, in)
	ret0, _ := ret[0].(*ApplySignDoyenResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplySignDoyen indicates an expected call of ApplySignDoyen.
func (mr *MockAnchorContractGoServerMockRecorder) ApplySignDoyen(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplySignDoyen", reflect.TypeOf((*MockAnchorContractGoServer)(nil).ApplySignDoyen), ctx, in)
}

// ApplySignEsport mocks base method.
func (m *MockAnchorContractGoServer) ApplySignEsport(ctx context.Context, in *ApplySignEsportReq) (*ApplySignEsportResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplySignEsport", ctx, in)
	ret0, _ := ret[0].(*ApplySignEsportResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplySignEsport indicates an expected call of ApplySignEsport.
func (mr *MockAnchorContractGoServerMockRecorder) ApplySignEsport(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplySignEsport", reflect.TypeOf((*MockAnchorContractGoServer)(nil).ApplySignEsport), ctx, in)
}

// BatchCheckUserExamineCert mocks base method.
func (m *MockAnchorContractGoServer) BatchCheckUserExamineCert(ctx context.Context, in *BatchSetUserExamineCertReq) (*BatchSetUserExamineCertResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCheckUserExamineCert", ctx, in)
	ret0, _ := ret[0].(*BatchSetUserExamineCertResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCheckUserExamineCert indicates an expected call of BatchCheckUserExamineCert.
func (mr *MockAnchorContractGoServerMockRecorder) BatchCheckUserExamineCert(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCheckUserExamineCert", reflect.TypeOf((*MockAnchorContractGoServer)(nil).BatchCheckUserExamineCert), ctx, in)
}

// BatchDelUserExamineCert mocks base method.
func (m *MockAnchorContractGoServer) BatchDelUserExamineCert(ctx context.Context, in *BatchDelUserExamineCertReq) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDelUserExamineCert", ctx, in)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchDelUserExamineCert indicates an expected call of BatchDelUserExamineCert.
func (mr *MockAnchorContractGoServerMockRecorder) BatchDelUserExamineCert(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDelUserExamineCert", reflect.TypeOf((*MockAnchorContractGoServer)(nil).BatchDelUserExamineCert), ctx, in)
}

// BatchGetAnchorIdentity mocks base method.
func (m *MockAnchorContractGoServer) BatchGetAnchorIdentity(ctx context.Context, in *BatchGetAnchorIdentityReq) (*BatchGetAnchorIdentityResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetAnchorIdentity", ctx, in)
	ret0, _ := ret[0].(*BatchGetAnchorIdentityResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetAnchorIdentity indicates an expected call of BatchGetAnchorIdentity.
func (mr *MockAnchorContractGoServerMockRecorder) BatchGetAnchorIdentity(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAnchorIdentity", reflect.TypeOf((*MockAnchorContractGoServer)(nil).BatchGetAnchorIdentity), ctx, in)
}

// BatchGetApplyBlacklist mocks base method.
func (m *MockAnchorContractGoServer) BatchGetApplyBlacklist(ctx context.Context, in *BatchGetApplyBlacklistReq) (*BatchGetApplyBlacklistResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetApplyBlacklist", ctx, in)
	ret0, _ := ret[0].(*BatchGetApplyBlacklistResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetApplyBlacklist indicates an expected call of BatchGetApplyBlacklist.
func (mr *MockAnchorContractGoServerMockRecorder) BatchGetApplyBlacklist(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetApplyBlacklist", reflect.TypeOf((*MockAnchorContractGoServer)(nil).BatchGetApplyBlacklist), ctx, in)
}

// BatchGetContractInfo mocks base method.
func (m *MockAnchorContractGoServer) BatchGetContractInfo(ctx context.Context, in *BatchGetContractInfoReq) (*BatchGetContractInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetContractInfo", ctx, in)
	ret0, _ := ret[0].(*BatchGetContractInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetContractInfo indicates an expected call of BatchGetContractInfo.
func (mr *MockAnchorContractGoServerMockRecorder) BatchGetContractInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetContractInfo", reflect.TypeOf((*MockAnchorContractGoServer)(nil).BatchGetContractInfo), ctx, in)
}

// BatchGetGuildUserScore mocks base method.
func (m *MockAnchorContractGoServer) BatchGetGuildUserScore(ctx context.Context, in *BatchGetGuildUserScoreReq) (*BatchGetGuildUserScoreResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetGuildUserScore", ctx, in)
	ret0, _ := ret[0].(*BatchGetGuildUserScoreResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetGuildUserScore indicates an expected call of BatchGetGuildUserScore.
func (mr *MockAnchorContractGoServerMockRecorder) BatchGetGuildUserScore(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetGuildUserScore", reflect.TypeOf((*MockAnchorContractGoServer)(nil).BatchGetGuildUserScore), ctx, in)
}

// BatchGetLiveAnchorCert mocks base method.
func (m *MockAnchorContractGoServer) BatchGetLiveAnchorCert(ctx context.Context, in *BatchGetLiveAnchorCertReq) (*BatchGetLiveAnchorCertResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetLiveAnchorCert", ctx, in)
	ret0, _ := ret[0].(*BatchGetLiveAnchorCertResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetLiveAnchorCert indicates an expected call of BatchGetLiveAnchorCert.
func (mr *MockAnchorContractGoServerMockRecorder) BatchGetLiveAnchorCert(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetLiveAnchorCert", reflect.TypeOf((*MockAnchorContractGoServer)(nil).BatchGetLiveAnchorCert), ctx, in)
}

// BatchGetUserAnchorIdentityLog mocks base method.
func (m *MockAnchorContractGoServer) BatchGetUserAnchorIdentityLog(ctx context.Context, in *BatchGetUserAnchorIdentityLogReq) (*BatchGetUserAnchorIdentityLogResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserAnchorIdentityLog", ctx, in)
	ret0, _ := ret[0].(*BatchGetUserAnchorIdentityLogResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserAnchorIdentityLog indicates an expected call of BatchGetUserAnchorIdentityLog.
func (mr *MockAnchorContractGoServerMockRecorder) BatchGetUserAnchorIdentityLog(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserAnchorIdentityLog", reflect.TypeOf((*MockAnchorContractGoServer)(nil).BatchGetUserAnchorIdentityLog), ctx, in)
}

// BatchGetUserApplySignRecord mocks base method.
func (m *MockAnchorContractGoServer) BatchGetUserApplySignRecord(ctx context.Context, in *BatchGetUserApplySignRecordReq) (*BatchGetUserApplySignRecordResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserApplySignRecord", ctx, in)
	ret0, _ := ret[0].(*BatchGetUserApplySignRecordResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserApplySignRecord indicates an expected call of BatchGetUserApplySignRecord.
func (mr *MockAnchorContractGoServerMockRecorder) BatchGetUserApplySignRecord(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserApplySignRecord", reflect.TypeOf((*MockAnchorContractGoServer)(nil).BatchGetUserApplySignRecord), ctx, in)
}

// BatchGetUserContract mocks base method.
func (m *MockAnchorContractGoServer) BatchGetUserContract(ctx context.Context, in *BatchGetUserContractReq) (*BatchGetUserContractResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserContract", ctx, in)
	ret0, _ := ret[0].(*BatchGetUserContractResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserContract indicates an expected call of BatchGetUserContract.
func (mr *MockAnchorContractGoServerMockRecorder) BatchGetUserContract(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserContract", reflect.TypeOf((*MockAnchorContractGoServer)(nil).BatchGetUserContract), ctx, in)
}

// BatchGetUserContractCacheInfo mocks base method.
func (m *MockAnchorContractGoServer) BatchGetUserContractCacheInfo(ctx context.Context, in *BatchGetUserContractCacheInfoReq) (*BatchGetUserContractCacheInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserContractCacheInfo", ctx, in)
	ret0, _ := ret[0].(*BatchGetUserContractCacheInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserContractCacheInfo indicates an expected call of BatchGetUserContractCacheInfo.
func (mr *MockAnchorContractGoServerMockRecorder) BatchGetUserContractCacheInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserContractCacheInfo", reflect.TypeOf((*MockAnchorContractGoServer)(nil).BatchGetUserContractCacheInfo), ctx, in)
}

// BatchGetUserExamineCert mocks base method.
func (m *MockAnchorContractGoServer) BatchGetUserExamineCert(ctx context.Context, in *BatchGetUserExamineCertReq) (*BatchGetUserExamineCertResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserExamineCert", ctx, in)
	ret0, _ := ret[0].(*BatchGetUserExamineCertResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserExamineCert indicates an expected call of BatchGetUserExamineCert.
func (mr *MockAnchorContractGoServerMockRecorder) BatchGetUserExamineCert(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserExamineCert", reflect.TypeOf((*MockAnchorContractGoServer)(nil).BatchGetUserExamineCert), ctx, in)
}

// BatchGetUserLiveAnchorExamine mocks base method.
func (m *MockAnchorContractGoServer) BatchGetUserLiveAnchorExamine(ctx context.Context, in *BatchGetUserLiveAnchorExamineReq) (*BatchGetUserLiveAnchorExamineResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserLiveAnchorExamine", ctx, in)
	ret0, _ := ret[0].(*BatchGetUserLiveAnchorExamineResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserLiveAnchorExamine indicates an expected call of BatchGetUserLiveAnchorExamine.
func (mr *MockAnchorContractGoServerMockRecorder) BatchGetUserLiveAnchorExamine(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserLiveAnchorExamine", reflect.TypeOf((*MockAnchorContractGoServer)(nil).BatchGetUserLiveAnchorExamine), ctx, in)
}

// BatchGuildExtensionContract mocks base method.
func (m *MockAnchorContractGoServer) BatchGuildExtensionContract(ctx context.Context, in *BatchGuildExtensionContractReq) (*BatchGuildExtensionContractResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGuildExtensionContract", ctx, in)
	ret0, _ := ret[0].(*BatchGuildExtensionContractResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGuildExtensionContract indicates an expected call of BatchGuildExtensionContract.
func (mr *MockAnchorContractGoServerMockRecorder) BatchGuildExtensionContract(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGuildExtensionContract", reflect.TypeOf((*MockAnchorContractGoServer)(nil).BatchGuildExtensionContract), ctx, in)
}

// BatchSetUserExamineCert mocks base method.
func (m *MockAnchorContractGoServer) BatchSetUserExamineCert(ctx context.Context, in *BatchSetUserExamineCertReq) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchSetUserExamineCert", ctx, in)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchSetUserExamineCert indicates an expected call of BatchSetUserExamineCert.
func (mr *MockAnchorContractGoServerMockRecorder) BatchSetUserExamineCert(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSetUserExamineCert", reflect.TypeOf((*MockAnchorContractGoServer)(nil).BatchSetUserExamineCert), ctx, in)
}

// CancelContractByUid mocks base method.
func (m *MockAnchorContractGoServer) CancelContractByUid(ctx context.Context, in *CancelContractByUidReq) (*CancelContractByUidResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelContractByUid", ctx, in)
	ret0, _ := ret[0].(*CancelContractByUidResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancelContractByUid indicates an expected call of CancelContractByUid.
func (mr *MockAnchorContractGoServerMockRecorder) CancelContractByUid(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelContractByUid", reflect.TypeOf((*MockAnchorContractGoServer)(nil).CancelContractByUid), ctx, in)
}

// CensorVideo mocks base method.
func (m *MockAnchorContractGoServer) CensorVideo(ctx context.Context, in *CensorVideoReq) (*CensorVideoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CensorVideo", ctx, in)
	ret0, _ := ret[0].(*CensorVideoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CensorVideo indicates an expected call of CensorVideo.
func (mr *MockAnchorContractGoServerMockRecorder) CensorVideo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CensorVideo", reflect.TypeOf((*MockAnchorContractGoServer)(nil).CensorVideo), ctx, in)
}

// CheckCanApplyCancelContract mocks base method.
func (m *MockAnchorContractGoServer) CheckCanApplyCancelContract(ctx context.Context, in *CheckCanApplyCancelContractReq) (*CheckCanApplyCancelContractResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckCanApplyCancelContract", ctx, in)
	ret0, _ := ret[0].(*CheckCanApplyCancelContractResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckCanApplyCancelContract indicates an expected call of CheckCanApplyCancelContract.
func (mr *MockAnchorContractGoServerMockRecorder) CheckCanApplyCancelContract(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckCanApplyCancelContract", reflect.TypeOf((*MockAnchorContractGoServer)(nil).CheckCanApplyCancelContract), ctx, in)
}

// CheckCanApplyCancelContractV2 mocks base method.
func (m *MockAnchorContractGoServer) CheckCanApplyCancelContractV2(ctx context.Context, in *CheckCanApplyCancelContractV2Req) (*CheckCanApplyCancelContractV2Resp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckCanApplyCancelContractV2", ctx, in)
	ret0, _ := ret[0].(*CheckCanApplyCancelContractV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckCanApplyCancelContractV2 indicates an expected call of CheckCanApplyCancelContractV2.
func (mr *MockAnchorContractGoServerMockRecorder) CheckCanApplyCancelContractV2(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckCanApplyCancelContractV2", reflect.TypeOf((*MockAnchorContractGoServer)(nil).CheckCanApplyCancelContractV2), ctx, in)
}

// CheckCanApplySign mocks base method.
func (m *MockAnchorContractGoServer) CheckCanApplySign(ctx context.Context, in *CheckCanApplySignReq) (*CheckCanApplySignResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckCanApplySign", ctx, in)
	ret0, _ := ret[0].(*CheckCanApplySignResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckCanApplySign indicates an expected call of CheckCanApplySign.
func (mr *MockAnchorContractGoServerMockRecorder) CheckCanApplySign(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckCanApplySign", reflect.TypeOf((*MockAnchorContractGoServer)(nil).CheckCanApplySign), ctx, in)
}

// CheckIfGreatLiveAnchor mocks base method.
func (m *MockAnchorContractGoServer) CheckIfGreatLiveAnchor(ctx context.Context, in *CheckIfGreatLiveAnchorReq) (*CheckIfGreatLiveAnchorResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIfGreatLiveAnchor", ctx, in)
	ret0, _ := ret[0].(*CheckIfGreatLiveAnchorResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIfGreatLiveAnchor indicates an expected call of CheckIfGreatLiveAnchor.
func (mr *MockAnchorContractGoServerMockRecorder) CheckIfGreatLiveAnchor(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIfGreatLiveAnchor", reflect.TypeOf((*MockAnchorContractGoServer)(nil).CheckIfGreatLiveAnchor), ctx, in)
}

// CheckIsSignWhiteUid mocks base method.
func (m *MockAnchorContractGoServer) CheckIsSignWhiteUid(ctx context.Context, in *CheckIsSignWhiteUidReq) (*CheckIsSignWhiteUidResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIsSignWhiteUid", ctx, in)
	ret0, _ := ret[0].(*CheckIsSignWhiteUidResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIsSignWhiteUid indicates an expected call of CheckIsSignWhiteUid.
func (mr *MockAnchorContractGoServerMockRecorder) CheckIsSignWhiteUid(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIsSignWhiteUid", reflect.TypeOf((*MockAnchorContractGoServer)(nil).CheckIsSignWhiteUid), ctx, in)
}

// CheckIsTotalNewMultiAnchor mocks base method.
func (m *MockAnchorContractGoServer) CheckIsTotalNewMultiAnchor(ctx context.Context, in *CheckIsTotalNewMultiAnchorReq) (*CheckIsTotalNewMultiAnchorResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIsTotalNewMultiAnchor", ctx, in)
	ret0, _ := ret[0].(*CheckIsTotalNewMultiAnchorResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIsTotalNewMultiAnchor indicates an expected call of CheckIsTotalNewMultiAnchor.
func (mr *MockAnchorContractGoServerMockRecorder) CheckIsTotalNewMultiAnchor(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIsTotalNewMultiAnchor", reflect.TypeOf((*MockAnchorContractGoServer)(nil).CheckIsTotalNewMultiAnchor), ctx, in)
}

// CheckUserGreatLiveAnchor mocks base method.
func (m *MockAnchorContractGoServer) CheckUserGreatLiveAnchor(ctx context.Context, in *CheckUserGreatLiveAnchorReq) (*CheckUserGreatLiveAnchorResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckUserGreatLiveAnchor", ctx, in)
	ret0, _ := ret[0].(*CheckUserGreatLiveAnchorResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckUserGreatLiveAnchor indicates an expected call of CheckUserGreatLiveAnchor.
func (mr *MockAnchorContractGoServerMockRecorder) CheckUserGreatLiveAnchor(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUserGreatLiveAnchor", reflect.TypeOf((*MockAnchorContractGoServer)(nil).CheckUserGreatLiveAnchor), ctx, in)
}

// ContractClaimObsToken mocks base method.
func (m *MockAnchorContractGoServer) ContractClaimObsToken(ctx context.Context, in *ContractClaimObsTokenReq) (*ContractClaimObsTokenResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ContractClaimObsToken", ctx, in)
	ret0, _ := ret[0].(*ContractClaimObsTokenResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ContractClaimObsToken indicates an expected call of ContractClaimObsToken.
func (mr *MockAnchorContractGoServerMockRecorder) ContractClaimObsToken(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContractClaimObsToken", reflect.TypeOf((*MockAnchorContractGoServer)(nil).ContractClaimObsToken), ctx, in)
}

// DelAnchorCertUpgradeTask mocks base method.
func (m *MockAnchorContractGoServer) DelAnchorCertUpgradeTask(ctx context.Context, in *DelAnchorCertUpgradeTaskReq) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelAnchorCertUpgradeTask", ctx, in)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelAnchorCertUpgradeTask indicates an expected call of DelAnchorCertUpgradeTask.
func (mr *MockAnchorContractGoServerMockRecorder) DelAnchorCertUpgradeTask(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelAnchorCertUpgradeTask", reflect.TypeOf((*MockAnchorContractGoServer)(nil).DelAnchorCertUpgradeTask), ctx, in)
}

// DelSignDoyen mocks base method.
func (m *MockAnchorContractGoServer) DelSignDoyen(ctx context.Context, in *DelSignDoyenReq) (*DelSignDoyenResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelSignDoyen", ctx, in)
	ret0, _ := ret[0].(*DelSignDoyenResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelSignDoyen indicates an expected call of DelSignDoyen.
func (mr *MockAnchorContractGoServerMockRecorder) DelSignDoyen(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelSignDoyen", reflect.TypeOf((*MockAnchorContractGoServer)(nil).DelSignDoyen), ctx, in)
}

// DelSignRight mocks base method.
func (m *MockAnchorContractGoServer) DelSignRight(ctx context.Context, in *DelSignRightReq) (*DelSignRightResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelSignRight", ctx, in)
	ret0, _ := ret[0].(*DelSignRightResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelSignRight indicates an expected call of DelSignRight.
func (mr *MockAnchorContractGoServerMockRecorder) DelSignRight(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelSignRight", reflect.TypeOf((*MockAnchorContractGoServer)(nil).DelSignRight), ctx, in)
}

// DelSignWhiteUid mocks base method.
func (m *MockAnchorContractGoServer) DelSignWhiteUid(ctx context.Context, in *DelSignWhiteUidReq) (*DelSignWhiteUidResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelSignWhiteUid", ctx, in)
	ret0, _ := ret[0].(*DelSignWhiteUidResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelSignWhiteUid indicates an expected call of DelSignWhiteUid.
func (mr *MockAnchorContractGoServerMockRecorder) DelSignWhiteUid(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelSignWhiteUid", reflect.TypeOf((*MockAnchorContractGoServer)(nil).DelSignWhiteUid), ctx, in)
}

// DelUserExamineCert mocks base method.
func (m *MockAnchorContractGoServer) DelUserExamineCert(ctx context.Context, in *DelUserExamineCertReq) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelUserExamineCert", ctx, in)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelUserExamineCert indicates an expected call of DelUserExamineCert.
func (mr *MockAnchorContractGoServerMockRecorder) DelUserExamineCert(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserExamineCert", reflect.TypeOf((*MockAnchorContractGoServer)(nil).DelUserExamineCert), ctx, in)
}

// DeleteChildExamineCert mocks base method.
func (m *MockAnchorContractGoServer) DeleteChildExamineCert(ctx context.Context, in *CertItemReq) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteChildExamineCert", ctx, in)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteChildExamineCert indicates an expected call of DeleteChildExamineCert.
func (mr *MockAnchorContractGoServerMockRecorder) DeleteChildExamineCert(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteChildExamineCert", reflect.TypeOf((*MockAnchorContractGoServer)(nil).DeleteChildExamineCert), ctx, in)
}

// DeleteParentExamineCert mocks base method.
func (m *MockAnchorContractGoServer) DeleteParentExamineCert(ctx context.Context, in *CertItemReq) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteParentExamineCert", ctx, in)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteParentExamineCert indicates an expected call of DeleteParentExamineCert.
func (mr *MockAnchorContractGoServerMockRecorder) DeleteParentExamineCert(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteParentExamineCert", reflect.TypeOf((*MockAnchorContractGoServer)(nil).DeleteParentExamineCert), ctx, in)
}

// GetAllApplyBlacklist mocks base method.
func (m *MockAnchorContractGoServer) GetAllApplyBlacklist(ctx context.Context, in *GetAllApplyBlacklistReq) (*GetAllApplyBlacklistResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllApplyBlacklist", ctx, in)
	ret0, _ := ret[0].(*GetAllApplyBlacklistResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllApplyBlacklist indicates an expected call of GetAllApplyBlacklist.
func (mr *MockAnchorContractGoServerMockRecorder) GetAllApplyBlacklist(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllApplyBlacklist", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetAllApplyBlacklist), ctx, in)
}

// GetAllApplySignRecord mocks base method.
func (m *MockAnchorContractGoServer) GetAllApplySignRecord(ctx context.Context, in *GetAllApplySignRecordReq) (*GetAllApplySignRecordResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllApplySignRecord", ctx, in)
	ret0, _ := ret[0].(*GetAllApplySignRecordResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllApplySignRecord indicates an expected call of GetAllApplySignRecord.
func (mr *MockAnchorContractGoServerMockRecorder) GetAllApplySignRecord(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllApplySignRecord", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetAllApplySignRecord), ctx, in)
}

// GetAllLiveAnchorExamine mocks base method.
func (m *MockAnchorContractGoServer) GetAllLiveAnchorExamine(ctx context.Context, in *GetAllLiveAnchorExamineReq) (*GetAllLiveAnchorExamineResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllLiveAnchorExamine", ctx, in)
	ret0, _ := ret[0].(*GetAllLiveAnchorExamineResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllLiveAnchorExamine indicates an expected call of GetAllLiveAnchorExamine.
func (mr *MockAnchorContractGoServerMockRecorder) GetAllLiveAnchorExamine(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllLiveAnchorExamine", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetAllLiveAnchorExamine), ctx, in)
}

// GetAnchorAgentUid mocks base method.
func (m *MockAnchorContractGoServer) GetAnchorAgentUid(ctx context.Context, in *GetAnchorAgentUidReq) (*GetAnchorAgentUidResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorAgentUid", ctx, in)
	ret0, _ := ret[0].(*GetAnchorAgentUidResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorAgentUid indicates an expected call of GetAnchorAgentUid.
func (mr *MockAnchorContractGoServerMockRecorder) GetAnchorAgentUid(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorAgentUid", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetAnchorAgentUid), ctx, in)
}

// GetAnchorCertListByItemId mocks base method.
func (m *MockAnchorContractGoServer) GetAnchorCertListByItemId(ctx context.Context, in *GetAnchorCertListByItemIdReq) (*GetAnchorCertListByItemIdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorCertListByItemId", ctx, in)
	ret0, _ := ret[0].(*GetAnchorCertListByItemIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorCertListByItemId indicates an expected call of GetAnchorCertListByItemId.
func (mr *MockAnchorContractGoServerMockRecorder) GetAnchorCertListByItemId(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorCertListByItemId", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetAnchorCertListByItemId), ctx, in)
}

// GetAnchorCertTaskInfo mocks base method.
func (m *MockAnchorContractGoServer) GetAnchorCertTaskInfo(ctx context.Context, in *GetAnchorCertTaskInfoReq) (*GetAnchorCertTaskInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorCertTaskInfo", ctx, in)
	ret0, _ := ret[0].(*GetAnchorCertTaskInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorCertTaskInfo indicates an expected call of GetAnchorCertTaskInfo.
func (mr *MockAnchorContractGoServerMockRecorder) GetAnchorCertTaskInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorCertTaskInfo", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetAnchorCertTaskInfo), ctx, in)
}

// GetAnchorExtraCertHistory mocks base method.
func (m *MockAnchorContractGoServer) GetAnchorExtraCertHistory(ctx context.Context, in *GetAnchorExtraCertHistoryReq) (*GetAnchorExtraCertHistoryResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorExtraCertHistory", ctx, in)
	ret0, _ := ret[0].(*GetAnchorExtraCertHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorExtraCertHistory indicates an expected call of GetAnchorExtraCertHistory.
func (mr *MockAnchorContractGoServerMockRecorder) GetAnchorExtraCertHistory(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorExtraCertHistory", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetAnchorExtraCertHistory), ctx, in)
}

// GetAnchorNoticeHandleHistory mocks base method.
func (m *MockAnchorContractGoServer) GetAnchorNoticeHandleHistory(ctx context.Context, in *GetAnchorNoticeHandleHistoryReq) (*GetAnchorNoticeHandleHistoryResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorNoticeHandleHistory", ctx, in)
	ret0, _ := ret[0].(*GetAnchorNoticeHandleHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorNoticeHandleHistory indicates an expected call of GetAnchorNoticeHandleHistory.
func (mr *MockAnchorContractGoServerMockRecorder) GetAnchorNoticeHandleHistory(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorNoticeHandleHistory", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetAnchorNoticeHandleHistory), ctx, in)
}

// GetCancelContractApplyList mocks base method.
func (m *MockAnchorContractGoServer) GetCancelContractApplyList(ctx context.Context, in *GetCancelContractApplyListReq) (*GetCancelContractApplyListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCancelContractApplyList", ctx, in)
	ret0, _ := ret[0].(*GetCancelContractApplyListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCancelContractApplyList indicates an expected call of GetCancelContractApplyList.
func (mr *MockAnchorContractGoServerMockRecorder) GetCancelContractApplyList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCancelContractApplyList", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetCancelContractApplyList), ctx, in)
}

// GetCancelContractTypeList mocks base method.
func (m *MockAnchorContractGoServer) GetCancelContractTypeList(ctx context.Context, in *GetCancelContractTypeListReq) (*GetCancelContractTypeListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCancelContractTypeList", ctx, in)
	ret0, _ := ret[0].(*GetCancelContractTypeListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCancelContractTypeList indicates an expected call of GetCancelContractTypeList.
func (mr *MockAnchorContractGoServerMockRecorder) GetCancelContractTypeList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCancelContractTypeList", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetCancelContractTypeList), ctx, in)
}

// GetCancelPayAmount mocks base method.
func (m *MockAnchorContractGoServer) GetCancelPayAmount(ctx context.Context, in *GetCancelPayAmountReq) (*GetCancelPayAmountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCancelPayAmount", ctx, in)
	ret0, _ := ret[0].(*GetCancelPayAmountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCancelPayAmount indicates an expected call of GetCancelPayAmount.
func (mr *MockAnchorContractGoServerMockRecorder) GetCancelPayAmount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCancelPayAmount", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetCancelPayAmount), ctx, in)
}

// GetChildExamineCertList mocks base method.
func (m *MockAnchorContractGoServer) GetChildExamineCertList(ctx context.Context, in *CertItemReq) (*ChildExamineCertList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChildExamineCertList", ctx, in)
	ret0, _ := ret[0].(*ChildExamineCertList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChildExamineCertList indicates an expected call of GetChildExamineCertList.
func (mr *MockAnchorContractGoServerMockRecorder) GetChildExamineCertList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChildExamineCertList", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetChildExamineCertList), ctx, in)
}

// GetContract mocks base method.
func (m *MockAnchorContractGoServer) GetContract(ctx context.Context, in *GetContractReq) (*GetContractResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetContract", ctx, in)
	ret0, _ := ret[0].(*GetContractResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetContract indicates an expected call of GetContract.
func (mr *MockAnchorContractGoServerMockRecorder) GetContract(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetContract", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetContract), ctx, in)
}

// GetContractChangeInfo mocks base method.
func (m *MockAnchorContractGoServer) GetContractChangeInfo(ctx context.Context, in *GetContractChangeInfoReq) (*GetContractChangeInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetContractChangeInfo", ctx, in)
	ret0, _ := ret[0].(*GetContractChangeInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetContractChangeInfo indicates an expected call of GetContractChangeInfo.
func (mr *MockAnchorContractGoServerMockRecorder) GetContractChangeInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetContractChangeInfo", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetContractChangeInfo), ctx, in)
}

// GetContractWithIdentity mocks base method.
func (m *MockAnchorContractGoServer) GetContractWithIdentity(ctx context.Context, in *GetContractWithIdentityReq) (*GetContractWithIdentityResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetContractWithIdentity", ctx, in)
	ret0, _ := ret[0].(*GetContractWithIdentityResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetContractWithIdentity indicates an expected call of GetContractWithIdentity.
func (mr *MockAnchorContractGoServerMockRecorder) GetContractWithIdentity(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetContractWithIdentity", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetContractWithIdentity), ctx, in)
}

// GetContractWorkerConfigs mocks base method.
func (m *MockAnchorContractGoServer) GetContractWorkerConfigs(ctx context.Context, in *GetContractWorkerConfigsReq) (*GetContractWorkerConfigsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetContractWorkerConfigs", ctx, in)
	ret0, _ := ret[0].(*GetContractWorkerConfigsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetContractWorkerConfigs indicates an expected call of GetContractWorkerConfigs.
func (mr *MockAnchorContractGoServerMockRecorder) GetContractWorkerConfigs(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetContractWorkerConfigs", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetContractWorkerConfigs), ctx, in)
}

// GetGuildAnchorExtInfoList mocks base method.
func (m *MockAnchorContractGoServer) GetGuildAnchorExtInfoList(ctx context.Context, in *GetGuildAnchorExtInfoListReq) (*GetGuildAnchorExtInfoListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildAnchorExtInfoList", ctx, in)
	ret0, _ := ret[0].(*GetGuildAnchorExtInfoListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildAnchorExtInfoList indicates an expected call of GetGuildAnchorExtInfoList.
func (mr *MockAnchorContractGoServerMockRecorder) GetGuildAnchorExtInfoList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildAnchorExtInfoList", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetGuildAnchorExtInfoList), ctx, in)
}

// GetGuildAnchorIdentity mocks base method.
func (m *MockAnchorContractGoServer) GetGuildAnchorIdentity(ctx context.Context, in *GetGuildAnchorIdentityReq) (*GetGuildAnchorIdentityResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildAnchorIdentity", ctx, in)
	ret0, _ := ret[0].(*GetGuildAnchorIdentityResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildAnchorIdentity indicates an expected call of GetGuildAnchorIdentity.
func (mr *MockAnchorContractGoServerMockRecorder) GetGuildAnchorIdentity(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildAnchorIdentity", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetGuildAnchorIdentity), ctx, in)
}

// GetGuildAnchorIdentityLog mocks base method.
func (m *MockAnchorContractGoServer) GetGuildAnchorIdentityLog(ctx context.Context, in *GetGuildAnchorIdentityLogReq) (*GetGuildAnchorIdentityLogResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildAnchorIdentityLog", ctx, in)
	ret0, _ := ret[0].(*GetGuildAnchorIdentityLogResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildAnchorIdentityLog indicates an expected call of GetGuildAnchorIdentityLog.
func (mr *MockAnchorContractGoServerMockRecorder) GetGuildAnchorIdentityLog(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildAnchorIdentityLog", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetGuildAnchorIdentityLog), ctx, in)
}

// GetGuildApplySignRecord mocks base method.
func (m *MockAnchorContractGoServer) GetGuildApplySignRecord(ctx context.Context, in *GetGuildApplySignRecordReq) (*GetGuildApplySignRecordResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildApplySignRecord", ctx, in)
	ret0, _ := ret[0].(*GetGuildApplySignRecordResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildApplySignRecord indicates an expected call of GetGuildApplySignRecord.
func (mr *MockAnchorContractGoServerMockRecorder) GetGuildApplySignRecord(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildApplySignRecord", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetGuildApplySignRecord), ctx, in)
}

// GetGuildApplySignRecordCnt mocks base method.
func (m *MockAnchorContractGoServer) GetGuildApplySignRecordCnt(ctx context.Context, in *GetGuildApplySignRecordCntReq) (*GetGuildApplySignRecordCntResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildApplySignRecordCnt", ctx, in)
	ret0, _ := ret[0].(*GetGuildApplySignRecordCntResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildApplySignRecordCnt indicates an expected call of GetGuildApplySignRecordCnt.
func (mr *MockAnchorContractGoServerMockRecorder) GetGuildApplySignRecordCnt(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildApplySignRecordCnt", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetGuildApplySignRecordCnt), ctx, in)
}

// GetGuildApplySignRecordList mocks base method.
func (m *MockAnchorContractGoServer) GetGuildApplySignRecordList(ctx context.Context, in *GetGuildApplySignRecordListReq) (*GetGuildApplySignRecordListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildApplySignRecordList", ctx, in)
	ret0, _ := ret[0].(*GetGuildApplySignRecordListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildApplySignRecordList indicates an expected call of GetGuildApplySignRecordList.
func (mr *MockAnchorContractGoServerMockRecorder) GetGuildApplySignRecordList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildApplySignRecordList", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetGuildApplySignRecordList), ctx, in)
}

// GetGuildCancelSignRecordList mocks base method.
func (m *MockAnchorContractGoServer) GetGuildCancelSignRecordList(ctx context.Context, in *GetGuildCancelSignRecordListReq) (*GetGuildCancelSignRecordListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildCancelSignRecordList", ctx, in)
	ret0, _ := ret[0].(*GetGuildCancelSignRecordListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildCancelSignRecordList indicates an expected call of GetGuildCancelSignRecordList.
func (mr *MockAnchorContractGoServerMockRecorder) GetGuildCancelSignRecordList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildCancelSignRecordList", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetGuildCancelSignRecordList), ctx, in)
}

// GetGuildContract mocks base method.
func (m *MockAnchorContractGoServer) GetGuildContract(ctx context.Context, in *GetGuildContractReq) (*GetGuildContractResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildContract", ctx, in)
	ret0, _ := ret[0].(*GetGuildContractResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildContract indicates an expected call of GetGuildContract.
func (mr *MockAnchorContractGoServerMockRecorder) GetGuildContract(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildContract", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetGuildContract), ctx, in)
}

// GetGuildContractByCond mocks base method.
func (m *MockAnchorContractGoServer) GetGuildContractByCond(ctx context.Context, in *GetGuildContractByCondReq) (*GetGuildContractByCondResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildContractByCond", ctx, in)
	ret0, _ := ret[0].(*GetGuildContractByCondResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildContractByCond indicates an expected call of GetGuildContractByCond.
func (mr *MockAnchorContractGoServerMockRecorder) GetGuildContractByCond(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildContractByCond", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetGuildContractByCond), ctx, in)
}

// GetGuildContractByIdentity mocks base method.
func (m *MockAnchorContractGoServer) GetGuildContractByIdentity(ctx context.Context, in *GetGuildContractByIdentityReq) (*GetGuildContractByIdentityResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildContractByIdentity", ctx, in)
	ret0, _ := ret[0].(*GetGuildContractByIdentityResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildContractByIdentity indicates an expected call of GetGuildContractByIdentity.
func (mr *MockAnchorContractGoServerMockRecorder) GetGuildContractByIdentity(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildContractByIdentity", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetGuildContractByIdentity), ctx, in)
}

// GetGuildContractSum mocks base method.
func (m *MockAnchorContractGoServer) GetGuildContractSum(ctx context.Context, in *GetGuildContractSumReq) (*GetGuildContractSumResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildContractSum", ctx, in)
	ret0, _ := ret[0].(*GetGuildContractSumResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildContractSum indicates an expected call of GetGuildContractSum.
func (mr *MockAnchorContractGoServerMockRecorder) GetGuildContractSum(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildContractSum", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetGuildContractSum), ctx, in)
}

// GetGuildEsportScore mocks base method.
func (m *MockAnchorContractGoServer) GetGuildEsportScore(ctx context.Context, in *GetGuildEsportScoreReq) (*GetGuildEsportScoreResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildEsportScore", ctx, in)
	ret0, _ := ret[0].(*GetGuildEsportScoreResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildEsportScore indicates an expected call of GetGuildEsportScore.
func (mr *MockAnchorContractGoServerMockRecorder) GetGuildEsportScore(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildEsportScore", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetGuildEsportScore), ctx, in)
}

// GetGuildLevel mocks base method.
func (m *MockAnchorContractGoServer) GetGuildLevel(ctx context.Context, in *GetGuildLevelReq) (*GetGuildLevelResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildLevel", ctx, in)
	ret0, _ := ret[0].(*GetGuildLevelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildLevel indicates an expected call of GetGuildLevel.
func (mr *MockAnchorContractGoServerMockRecorder) GetGuildLevel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildLevel", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetGuildLevel), ctx, in)
}

// GetGuildLiveAnchorExamine mocks base method.
func (m *MockAnchorContractGoServer) GetGuildLiveAnchorExamine(ctx context.Context, in *GetGuildLiveAnchorExamineReq) (*GetGuildLiveAnchorExamineResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildLiveAnchorExamine", ctx, in)
	ret0, _ := ret[0].(*GetGuildLiveAnchorExamineResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildLiveAnchorExamine indicates an expected call of GetGuildLiveAnchorExamine.
func (mr *MockAnchorContractGoServerMockRecorder) GetGuildLiveAnchorExamine(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildLiveAnchorExamine", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetGuildLiveAnchorExamine), ctx, in)
}

// GetGuildSignAnchorInfo mocks base method.
func (m *MockAnchorContractGoServer) GetGuildSignAnchorInfo(ctx context.Context, in *GetGuildSignAnchorInfoReq) (*GetGuildSignAnchorInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildSignAnchorInfo", ctx, in)
	ret0, _ := ret[0].(*GetGuildSignAnchorInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildSignAnchorInfo indicates an expected call of GetGuildSignAnchorInfo.
func (mr *MockAnchorContractGoServerMockRecorder) GetGuildSignAnchorInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildSignAnchorInfo", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetGuildSignAnchorInfo), ctx, in)
}

// GetGuildSignRight mocks base method.
func (m *MockAnchorContractGoServer) GetGuildSignRight(ctx context.Context, in *GetGuildSignRightReq) (*GetGuildSignRightResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildSignRight", ctx, in)
	ret0, _ := ret[0].(*GetGuildSignRightResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildSignRight indicates an expected call of GetGuildSignRight.
func (mr *MockAnchorContractGoServerMockRecorder) GetGuildSignRight(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildSignRight", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetGuildSignRight), ctx, in)
}

// GetIdentityChangeHistory mocks base method.
func (m *MockAnchorContractGoServer) GetIdentityChangeHistory(ctx context.Context, in *GetIdentityChangeHistoryReq) (*GetIdentityChangeHistoryResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIdentityChangeHistory", ctx, in)
	ret0, _ := ret[0].(*GetIdentityChangeHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIdentityChangeHistory indicates an expected call of GetIdentityChangeHistory.
func (mr *MockAnchorContractGoServerMockRecorder) GetIdentityChangeHistory(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIdentityChangeHistory", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetIdentityChangeHistory), ctx, in)
}

// GetLiveAnchorExamineNotUpload mocks base method.
func (m *MockAnchorContractGoServer) GetLiveAnchorExamineNotUpload(ctx context.Context, in *GetLiveAnchorExamineNotUploadReq) (*GetLiveAnchorExamineNotUploadResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLiveAnchorExamineNotUpload", ctx, in)
	ret0, _ := ret[0].(*GetLiveAnchorExamineNotUploadResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLiveAnchorExamineNotUpload indicates an expected call of GetLiveAnchorExamineNotUpload.
func (mr *MockAnchorContractGoServerMockRecorder) GetLiveAnchorExamineNotUpload(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLiveAnchorExamineNotUpload", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetLiveAnchorExamineNotUpload), ctx, in)
}

// GetMultiPlayerCenterEntry mocks base method.
func (m *MockAnchorContractGoServer) GetMultiPlayerCenterEntry(ctx context.Context, in *GetMultiPlayerCenterEntryReq) (*GetMultiPlayerCenterEntryResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMultiPlayerCenterEntry", ctx, in)
	ret0, _ := ret[0].(*GetMultiPlayerCenterEntryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMultiPlayerCenterEntry indicates an expected call of GetMultiPlayerCenterEntry.
func (mr *MockAnchorContractGoServerMockRecorder) GetMultiPlayerCenterEntry(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiPlayerCenterEntry", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetMultiPlayerCenterEntry), ctx, in)
}

// GetNeedConfirmWorkerType mocks base method.
func (m *MockAnchorContractGoServer) GetNeedConfirmWorkerType(ctx context.Context, in *GetNeedConfirmWorkerTypeReq) (*GetNeedConfirmWorkerTypeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNeedConfirmWorkerType", ctx, in)
	ret0, _ := ret[0].(*GetNeedConfirmWorkerTypeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNeedConfirmWorkerType indicates an expected call of GetNeedConfirmWorkerType.
func (mr *MockAnchorContractGoServerMockRecorder) GetNeedConfirmWorkerType(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNeedConfirmWorkerType", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetNeedConfirmWorkerType), ctx, in)
}

// GetNegotiateReasonType mocks base method.
func (m *MockAnchorContractGoServer) GetNegotiateReasonType(ctx context.Context, in *GetNegotiateReasonTypeReq) (*GetNegotiateReasonTypeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNegotiateReasonType", ctx, in)
	ret0, _ := ret[0].(*GetNegotiateReasonTypeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNegotiateReasonType indicates an expected call of GetNegotiateReasonType.
func (mr *MockAnchorContractGoServerMockRecorder) GetNegotiateReasonType(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNegotiateReasonType", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetNegotiateReasonType), ctx, in)
}

// GetNotUploadExamineAnchorList mocks base method.
func (m *MockAnchorContractGoServer) GetNotUploadExamineAnchorList(ctx context.Context, in *GetNotUploadExamineAnchorListReq) (*GetNotUploadExamineAnchorListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNotUploadExamineAnchorList", ctx, in)
	ret0, _ := ret[0].(*GetNotUploadExamineAnchorListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNotUploadExamineAnchorList indicates an expected call of GetNotUploadExamineAnchorList.
func (mr *MockAnchorContractGoServerMockRecorder) GetNotUploadExamineAnchorList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNotUploadExamineAnchorList", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetNotUploadExamineAnchorList), ctx, in)
}

// GetOfficialCancelSignList mocks base method.
func (m *MockAnchorContractGoServer) GetOfficialCancelSignList(ctx context.Context, in *GetOfficialCancelSignListReq) (*GetOfficialCancelSignListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOfficialCancelSignList", ctx, in)
	ret0, _ := ret[0].(*GetOfficialCancelSignListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOfficialCancelSignList indicates an expected call of GetOfficialCancelSignList.
func (mr *MockAnchorContractGoServerMockRecorder) GetOfficialCancelSignList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOfficialCancelSignList", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetOfficialCancelSignList), ctx, in)
}

// GetParentExamineCertList mocks base method.
func (m *MockAnchorContractGoServer) GetParentExamineCertList(ctx context.Context, in *CertOffsetTypeReq) (*ParentExamineCertList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetParentExamineCertList", ctx, in)
	ret0, _ := ret[0].(*ParentExamineCertList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParentExamineCertList indicates an expected call of GetParentExamineCertList.
func (mr *MockAnchorContractGoServerMockRecorder) GetParentExamineCertList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParentExamineCertList", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetParentExamineCertList), ctx, in)
}

// GetRadioLiveAnchorExamine mocks base method.
func (m *MockAnchorContractGoServer) GetRadioLiveAnchorExamine(ctx context.Context, in *GetRadioLiveAnchorExamineReq) (*GetRadioLiveAnchorExamineResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRadioLiveAnchorExamine", ctx, in)
	ret0, _ := ret[0].(*GetRadioLiveAnchorExamineResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRadioLiveAnchorExamine indicates an expected call of GetRadioLiveAnchorExamine.
func (mr *MockAnchorContractGoServerMockRecorder) GetRadioLiveAnchorExamine(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRadioLiveAnchorExamine", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetRadioLiveAnchorExamine), ctx, in)
}

// GetRecommendTopGuildList mocks base method.
func (m *MockAnchorContractGoServer) GetRecommendTopGuildList(ctx context.Context, in *GetRecommendTopGuildListReq) (*GetRecommendTopGuildListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecommendTopGuildList", ctx, in)
	ret0, _ := ret[0].(*GetRecommendTopGuildListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecommendTopGuildList indicates an expected call of GetRecommendTopGuildList.
func (mr *MockAnchorContractGoServerMockRecorder) GetRecommendTopGuildList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecommendTopGuildList", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetRecommendTopGuildList), ctx, in)
}

// GetRejectReason mocks base method.
func (m *MockAnchorContractGoServer) GetRejectReason(ctx context.Context, in *GetRejectReasonReq) (*GetRejectReasonResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRejectReason", ctx, in)
	ret0, _ := ret[0].(*GetRejectReasonResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRejectReason indicates an expected call of GetRejectReason.
func (mr *MockAnchorContractGoServerMockRecorder) GetRejectReason(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRejectReason", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetRejectReason), ctx, in)
}

// GetSignEsportAuditToken mocks base method.
func (m *MockAnchorContractGoServer) GetSignEsportAuditToken(ctx context.Context, in *GetSignEsportAuditTokenReq) (*GetSignEsportAuditTokenResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSignEsportAuditToken", ctx, in)
	ret0, _ := ret[0].(*GetSignEsportAuditTokenResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSignEsportAuditToken indicates an expected call of GetSignEsportAuditToken.
func (mr *MockAnchorContractGoServerMockRecorder) GetSignEsportAuditToken(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSignEsportAuditToken", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetSignEsportAuditToken), ctx, in)
}

// GetSignRightList mocks base method.
func (m *MockAnchorContractGoServer) GetSignRightList(ctx context.Context, in *GetSignRightListReq) (*GetSignRightListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSignRightList", ctx, in)
	ret0, _ := ret[0].(*GetSignRightListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSignRightList indicates an expected call of GetSignRightList.
func (mr *MockAnchorContractGoServerMockRecorder) GetSignRightList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSignRightList", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetSignRightList), ctx, in)
}

// GetUserAnchorIdentityLog mocks base method.
func (m *MockAnchorContractGoServer) GetUserAnchorIdentityLog(ctx context.Context, in *GetUserAnchorIdentityLogReq) (*GetUserAnchorIdentityLogResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserAnchorIdentityLog", ctx, in)
	ret0, _ := ret[0].(*GetUserAnchorIdentityLogResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAnchorIdentityLog indicates an expected call of GetUserAnchorIdentityLog.
func (mr *MockAnchorContractGoServerMockRecorder) GetUserAnchorIdentityLog(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAnchorIdentityLog", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetUserAnchorIdentityLog), ctx, in)
}

// GetUserApplySignRecord mocks base method.
func (m *MockAnchorContractGoServer) GetUserApplySignRecord(ctx context.Context, in *GetUserApplySignRecordReq) (*GetUserApplySignRecordResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserApplySignRecord", ctx, in)
	ret0, _ := ret[0].(*GetUserApplySignRecordResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserApplySignRecord indicates an expected call of GetUserApplySignRecord.
func (mr *MockAnchorContractGoServerMockRecorder) GetUserApplySignRecord(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserApplySignRecord", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetUserApplySignRecord), ctx, in)
}

// GetUserContract mocks base method.
func (m *MockAnchorContractGoServer) GetUserContract(ctx context.Context, in *GetUserContractReq) (*GetUserContractResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserContract", ctx, in)
	ret0, _ := ret[0].(*GetUserContractResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserContract indicates an expected call of GetUserContract.
func (mr *MockAnchorContractGoServerMockRecorder) GetUserContract(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserContract", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetUserContract), ctx, in)
}

// GetUserContractCacheInfo mocks base method.
func (m *MockAnchorContractGoServer) GetUserContractCacheInfo(ctx context.Context, in *GetUserContractCacheInfoReq) (*ContractCacheInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserContractCacheInfo", ctx, in)
	ret0, _ := ret[0].(*ContractCacheInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserContractCacheInfo indicates an expected call of GetUserContractCacheInfo.
func (mr *MockAnchorContractGoServerMockRecorder) GetUserContractCacheInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserContractCacheInfo", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetUserContractCacheInfo), ctx, in)
}

// GetUserExamineCert mocks base method.
func (m *MockAnchorContractGoServer) GetUserExamineCert(ctx context.Context, in *GetUserExamineCertReq) (*GetUserExamineCertResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserExamineCert", ctx, in)
	ret0, _ := ret[0].(*GetUserExamineCertResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserExamineCert indicates an expected call of GetUserExamineCert.
func (mr *MockAnchorContractGoServerMockRecorder) GetUserExamineCert(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserExamineCert", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetUserExamineCert), ctx, in)
}

// GetUserPromoteInviteInfo mocks base method.
func (m *MockAnchorContractGoServer) GetUserPromoteInviteInfo(ctx context.Context, in *GetUserPromoteInviteInfoReq) (*GetUserPromoteInviteInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPromoteInviteInfo", ctx, in)
	ret0, _ := ret[0].(*GetUserPromoteInviteInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPromoteInviteInfo indicates an expected call of GetUserPromoteInviteInfo.
func (mr *MockAnchorContractGoServerMockRecorder) GetUserPromoteInviteInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPromoteInviteInfo", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GetUserPromoteInviteInfo), ctx, in)
}

// GuildExtensionContract mocks base method.
func (m *MockAnchorContractGoServer) GuildExtensionContract(ctx context.Context, in *GuildExtensionContractReq) (*GuildExtensionContractResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GuildExtensionContract", ctx, in)
	ret0, _ := ret[0].(*GuildExtensionContractResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GuildExtensionContract indicates an expected call of GuildExtensionContract.
func (mr *MockAnchorContractGoServerMockRecorder) GuildExtensionContract(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GuildExtensionContract", reflect.TypeOf((*MockAnchorContractGoServer)(nil).GuildExtensionContract), ctx, in)
}

// HandleApplyBlackInfo mocks base method.
func (m *MockAnchorContractGoServer) HandleApplyBlackInfo(ctx context.Context, in *HandleApplyBlackInfoReq) (*HandleApplyBlackInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleApplyBlackInfo", ctx, in)
	ret0, _ := ret[0].(*HandleApplyBlackInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandleApplyBlackInfo indicates an expected call of HandleApplyBlackInfo.
func (mr *MockAnchorContractGoServerMockRecorder) HandleApplyBlackInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleApplyBlackInfo", reflect.TypeOf((*MockAnchorContractGoServer)(nil).HandleApplyBlackInfo), ctx, in)
}

// HandleContractChange mocks base method.
func (m *MockAnchorContractGoServer) HandleContractChange(ctx context.Context, in *HandleContractChangeReq) (*HandleContractChangeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleContractChange", ctx, in)
	ret0, _ := ret[0].(*HandleContractChangeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandleContractChange indicates an expected call of HandleContractChange.
func (mr *MockAnchorContractGoServerMockRecorder) HandleContractChange(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleContractChange", reflect.TypeOf((*MockAnchorContractGoServer)(nil).HandleContractChange), ctx, in)
}

// HandleFocusAnchor mocks base method.
func (m *MockAnchorContractGoServer) HandleFocusAnchor(ctx context.Context, in *HandleFocusAnchorReq) (*HandleFocusAnchorResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleFocusAnchor", ctx, in)
	ret0, _ := ret[0].(*HandleFocusAnchorResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandleFocusAnchor indicates an expected call of HandleFocusAnchor.
func (mr *MockAnchorContractGoServerMockRecorder) HandleFocusAnchor(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleFocusAnchor", reflect.TypeOf((*MockAnchorContractGoServer)(nil).HandleFocusAnchor), ctx, in)
}

// HandlerCancelContractApply mocks base method.
func (m *MockAnchorContractGoServer) HandlerCancelContractApply(ctx context.Context, in *HandlerCancelContractApplyReq) (*HandlerCancelContractApplyResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandlerCancelContractApply", ctx, in)
	ret0, _ := ret[0].(*HandlerCancelContractApplyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandlerCancelContractApply indicates an expected call of HandlerCancelContractApply.
func (mr *MockAnchorContractGoServerMockRecorder) HandlerCancelContractApply(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandlerCancelContractApply", reflect.TypeOf((*MockAnchorContractGoServer)(nil).HandlerCancelContractApply), ctx, in)
}

// InviteMemberChangeWorkerType mocks base method.
func (m *MockAnchorContractGoServer) InviteMemberChangeWorkerType(ctx context.Context, in *InviteMemberChangeWorkerTypeReq) (*InviteMemberChangeWorkerTypeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InviteMemberChangeWorkerType", ctx, in)
	ret0, _ := ret[0].(*InviteMemberChangeWorkerTypeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InviteMemberChangeWorkerType indicates an expected call of InviteMemberChangeWorkerType.
func (mr *MockAnchorContractGoServerMockRecorder) InviteMemberChangeWorkerType(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InviteMemberChangeWorkerType", reflect.TypeOf((*MockAnchorContractGoServer)(nil).InviteMemberChangeWorkerType), ctx, in)
}

// InvitePromote mocks base method.
func (m *MockAnchorContractGoServer) InvitePromote(ctx context.Context, in *InvitePromoteReq) (*InvitePromoteResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InvitePromote", ctx, in)
	ret0, _ := ret[0].(*InvitePromoteResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InvitePromote indicates an expected call of InvitePromote.
func (mr *MockAnchorContractGoServerMockRecorder) InvitePromote(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InvitePromote", reflect.TypeOf((*MockAnchorContractGoServer)(nil).InvitePromote), ctx, in)
}

// ListAnchorCertContent mocks base method.
func (m *MockAnchorContractGoServer) ListAnchorCertContent(ctx context.Context, in *ListAnchorCertContentReq) (*ListAnchorCertContentResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAnchorCertContent", ctx, in)
	ret0, _ := ret[0].(*ListAnchorCertContentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAnchorCertContent indicates an expected call of ListAnchorCertContent.
func (mr *MockAnchorContractGoServerMockRecorder) ListAnchorCertContent(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAnchorCertContent", reflect.TypeOf((*MockAnchorContractGoServer)(nil).ListAnchorCertContent), ctx, in)
}

// ListAnchorCertUpgradeTask mocks base method.
func (m *MockAnchorContractGoServer) ListAnchorCertUpgradeTask(ctx context.Context, in *ListAnchorCertUpgradeTaskReq) (*ListAnchorCertUpgradeTaskResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAnchorCertUpgradeTask", ctx, in)
	ret0, _ := ret[0].(*ListAnchorCertUpgradeTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAnchorCertUpgradeTask indicates an expected call of ListAnchorCertUpgradeTask.
func (mr *MockAnchorContractGoServerMockRecorder) ListAnchorCertUpgradeTask(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAnchorCertUpgradeTask", reflect.TypeOf((*MockAnchorContractGoServer)(nil).ListAnchorCertUpgradeTask), ctx, in)
}

// ListExamineCert mocks base method.
func (m *MockAnchorContractGoServer) ListExamineCert(ctx context.Context, in *ListExamineCertReq) (*ListExamineCertResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListExamineCert", ctx, in)
	ret0, _ := ret[0].(*ListExamineCertResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListExamineCert indicates an expected call of ListExamineCert.
func (mr *MockAnchorContractGoServerMockRecorder) ListExamineCert(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListExamineCert", reflect.TypeOf((*MockAnchorContractGoServer)(nil).ListExamineCert), ctx, in)
}

// ListGuildSignAnchorInfo mocks base method.
func (m *MockAnchorContractGoServer) ListGuildSignAnchorInfo(ctx context.Context, in *ListGuildSignAnchorInfoReq) (*ListGuildSignAnchorInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListGuildSignAnchorInfo", ctx, in)
	ret0, _ := ret[0].(*ListGuildSignAnchorInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListGuildSignAnchorInfo indicates an expected call of ListGuildSignAnchorInfo.
func (mr *MockAnchorContractGoServerMockRecorder) ListGuildSignAnchorInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListGuildSignAnchorInfo", reflect.TypeOf((*MockAnchorContractGoServer)(nil).ListGuildSignAnchorInfo), ctx, in)
}

// ListUserExamineCert mocks base method.
func (m *MockAnchorContractGoServer) ListUserExamineCert(ctx context.Context, in *ListUserExamineCertReq) (*ListUserExamineCertResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListUserExamineCert", ctx, in)
	ret0, _ := ret[0].(*ListUserExamineCertResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListUserExamineCert indicates an expected call of ListUserExamineCert.
func (mr *MockAnchorContractGoServerMockRecorder) ListUserExamineCert(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListUserExamineCert", reflect.TypeOf((*MockAnchorContractGoServer)(nil).ListUserExamineCert), ctx, in)
}

// LockCancelPayAmount mocks base method.
func (m *MockAnchorContractGoServer) LockCancelPayAmount(ctx context.Context, in *LockCancelPayAmountReq) (*LockCancelPayAmountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LockCancelPayAmount", ctx, in)
	ret0, _ := ret[0].(*LockCancelPayAmountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LockCancelPayAmount indicates an expected call of LockCancelPayAmount.
func (mr *MockAnchorContractGoServerMockRecorder) LockCancelPayAmount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LockCancelPayAmount", reflect.TypeOf((*MockAnchorContractGoServer)(nil).LockCancelPayAmount), ctx, in)
}

// ModifyWorkerType mocks base method.
func (m *MockAnchorContractGoServer) ModifyWorkerType(ctx context.Context, in *ModifyWorkerTypeReq) (*ModifyWorkerTypeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyWorkerType", ctx, in)
	ret0, _ := ret[0].(*ModifyWorkerTypeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyWorkerType indicates an expected call of ModifyWorkerType.
func (mr *MockAnchorContractGoServerMockRecorder) ModifyWorkerType(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyWorkerType", reflect.TypeOf((*MockAnchorContractGoServer)(nil).ModifyWorkerType), ctx, in)
}

// OfficialHandleApplySign mocks base method.
func (m *MockAnchorContractGoServer) OfficialHandleApplySign(ctx context.Context, in *OfficialHandleApplySignReq) (*OfficialHandleApplySignResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OfficialHandleApplySign", ctx, in)
	ret0, _ := ret[0].(*OfficialHandleApplySignResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OfficialHandleApplySign indicates an expected call of OfficialHandleApplySign.
func (mr *MockAnchorContractGoServerMockRecorder) OfficialHandleApplySign(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OfficialHandleApplySign", reflect.TypeOf((*MockAnchorContractGoServer)(nil).OfficialHandleApplySign), ctx, in)
}

// OfficialHandleApplySignEsport mocks base method.
func (m *MockAnchorContractGoServer) OfficialHandleApplySignEsport(ctx context.Context, in *OfficialHandleApplySignEsportReq) (*OfficialHandleApplySignEsportResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OfficialHandleApplySignEsport", ctx, in)
	ret0, _ := ret[0].(*OfficialHandleApplySignEsportResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OfficialHandleApplySignEsport indicates an expected call of OfficialHandleApplySignEsport.
func (mr *MockAnchorContractGoServerMockRecorder) OfficialHandleApplySignEsport(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OfficialHandleApplySignEsport", reflect.TypeOf((*MockAnchorContractGoServer)(nil).OfficialHandleApplySignEsport), ctx, in)
}

// OfficialHandleCancelSign mocks base method.
func (m *MockAnchorContractGoServer) OfficialHandleCancelSign(ctx context.Context, in *OfficialHandleCancelSignReq) (*OfficialHandleCancelSignResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OfficialHandleCancelSign", ctx, in)
	ret0, _ := ret[0].(*OfficialHandleCancelSignResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OfficialHandleCancelSign indicates an expected call of OfficialHandleCancelSign.
func (mr *MockAnchorContractGoServerMockRecorder) OfficialHandleCancelSign(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OfficialHandleCancelSign", reflect.TypeOf((*MockAnchorContractGoServer)(nil).OfficialHandleCancelSign), ctx, in)
}

// OfficialRemarkPayCancelSign mocks base method.
func (m *MockAnchorContractGoServer) OfficialRemarkPayCancelSign(ctx context.Context, in *OfficialRemarkPayCancelSignReq) (*OfficialRemarkPayCancelSignResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OfficialRemarkPayCancelSign", ctx, in)
	ret0, _ := ret[0].(*OfficialRemarkPayCancelSignResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OfficialRemarkPayCancelSign indicates an expected call of OfficialRemarkPayCancelSign.
func (mr *MockAnchorContractGoServerMockRecorder) OfficialRemarkPayCancelSign(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OfficialRemarkPayCancelSign", reflect.TypeOf((*MockAnchorContractGoServer)(nil).OfficialRemarkPayCancelSign), ctx, in)
}

// PresidentHandleAllApplySign mocks base method.
func (m *MockAnchorContractGoServer) PresidentHandleAllApplySign(ctx context.Context, in *PresidentHandleAllApplySignReq) (*PresidentHandleAllApplySignResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PresidentHandleAllApplySign", ctx, in)
	ret0, _ := ret[0].(*PresidentHandleAllApplySignResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PresidentHandleAllApplySign indicates an expected call of PresidentHandleAllApplySign.
func (mr *MockAnchorContractGoServerMockRecorder) PresidentHandleAllApplySign(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PresidentHandleAllApplySign", reflect.TypeOf((*MockAnchorContractGoServer)(nil).PresidentHandleAllApplySign), ctx, in)
}

// PresidentHandleApplySign mocks base method.
func (m *MockAnchorContractGoServer) PresidentHandleApplySign(ctx context.Context, in *PresidentHandleApplySignReq) (*PresidentHandleApplySignResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PresidentHandleApplySign", ctx, in)
	ret0, _ := ret[0].(*PresidentHandleApplySignResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PresidentHandleApplySign indicates an expected call of PresidentHandleApplySign.
func (mr *MockAnchorContractGoServerMockRecorder) PresidentHandleApplySign(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PresidentHandleApplySign", reflect.TypeOf((*MockAnchorContractGoServer)(nil).PresidentHandleApplySign), ctx, in)
}

// ProcPromoteInvite mocks base method.
func (m *MockAnchorContractGoServer) ProcPromoteInvite(ctx context.Context, in *ProcPromoteInviteReq) (*ProcPromoteInviteResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcPromoteInvite", ctx, in)
	ret0, _ := ret[0].(*ProcPromoteInviteResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcPromoteInvite indicates an expected call of ProcPromoteInvite.
func (mr *MockAnchorContractGoServerMockRecorder) ProcPromoteInvite(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcPromoteInvite", reflect.TypeOf((*MockAnchorContractGoServer)(nil).ProcPromoteInvite), ctx, in)
}

// ReclaimAnchorIdentity mocks base method.
func (m *MockAnchorContractGoServer) ReclaimAnchorIdentity(ctx context.Context, in *ReclaimAnchorIdentityReq) (*ReclaimAnchorIdentityResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReclaimAnchorIdentity", ctx, in)
	ret0, _ := ret[0].(*ReclaimAnchorIdentityResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReclaimAnchorIdentity indicates an expected call of ReclaimAnchorIdentity.
func (mr *MockAnchorContractGoServerMockRecorder) ReclaimAnchorIdentity(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReclaimAnchorIdentity", reflect.TypeOf((*MockAnchorContractGoServer)(nil).ReclaimAnchorIdentity), ctx, in)
}

// ReclaimGuildAllAnchorIdentity mocks base method.
func (m *MockAnchorContractGoServer) ReclaimGuildAllAnchorIdentity(ctx context.Context, in *ReclaimGuildAllAnchorIdentityReq) (*ReclaimGuildAllAnchorIdentityResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReclaimGuildAllAnchorIdentity", ctx, in)
	ret0, _ := ret[0].(*ReclaimGuildAllAnchorIdentityResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReclaimGuildAllAnchorIdentity indicates an expected call of ReclaimGuildAllAnchorIdentity.
func (mr *MockAnchorContractGoServerMockRecorder) ReclaimGuildAllAnchorIdentity(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReclaimGuildAllAnchorIdentity", reflect.TypeOf((*MockAnchorContractGoServer)(nil).ReclaimGuildAllAnchorIdentity), ctx, in)
}

// RecordAnchorNoticeHandle mocks base method.
func (m *MockAnchorContractGoServer) RecordAnchorNoticeHandle(ctx context.Context, in *RecordAnchorNoticeHandleReq) (*RecordAnchorNoticeHandleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordAnchorNoticeHandle", ctx, in)
	ret0, _ := ret[0].(*RecordAnchorNoticeHandleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecordAnchorNoticeHandle indicates an expected call of RecordAnchorNoticeHandle.
func (mr *MockAnchorContractGoServerMockRecorder) RecordAnchorNoticeHandle(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordAnchorNoticeHandle", reflect.TypeOf((*MockAnchorContractGoServer)(nil).RecordAnchorNoticeHandle), ctx, in)
}

// SendImExamineCert mocks base method.
func (m *MockAnchorContractGoServer) SendImExamineCert(ctx context.Context, in *SendImExamineCertReq) (*SendImExamineCertResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendImExamineCert", ctx, in)
	ret0, _ := ret[0].(*SendImExamineCertResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendImExamineCert indicates an expected call of SendImExamineCert.
func (mr *MockAnchorContractGoServerMockRecorder) SendImExamineCert(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendImExamineCert", reflect.TypeOf((*MockAnchorContractGoServer)(nil).SendImExamineCert), ctx, in)
}

// SetAnchorCertWhiteList mocks base method.
func (m *MockAnchorContractGoServer) SetAnchorCertWhiteList(ctx context.Context, in *SetAnchorCertWhiteListReq) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetAnchorCertWhiteList", ctx, in)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetAnchorCertWhiteList indicates an expected call of SetAnchorCertWhiteList.
func (mr *MockAnchorContractGoServerMockRecorder) SetAnchorCertWhiteList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAnchorCertWhiteList", reflect.TypeOf((*MockAnchorContractGoServer)(nil).SetAnchorCertWhiteList), ctx, in)
}

// SetAnchorExtraCert mocks base method.
func (m *MockAnchorContractGoServer) SetAnchorExtraCert(ctx context.Context, in *SetAnchorExtraCertReq) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetAnchorExtraCert", ctx, in)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetAnchorExtraCert indicates an expected call of SetAnchorExtraCert.
func (mr *MockAnchorContractGoServerMockRecorder) SetAnchorExtraCert(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAnchorExtraCert", reflect.TypeOf((*MockAnchorContractGoServer)(nil).SetAnchorExtraCert), ctx, in)
}

// SetGuildCancelContractType mocks base method.
func (m *MockAnchorContractGoServer) SetGuildCancelContractType(ctx context.Context, in *SetGuildCancelContractTypeReq) (*SetGuildCancelContractTypeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetGuildCancelContractType", ctx, in)
	ret0, _ := ret[0].(*SetGuildCancelContractTypeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetGuildCancelContractType indicates an expected call of SetGuildCancelContractType.
func (mr *MockAnchorContractGoServerMockRecorder) SetGuildCancelContractType(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetGuildCancelContractType", reflect.TypeOf((*MockAnchorContractGoServer)(nil).SetGuildCancelContractType), ctx, in)
}

// SetUserExamineCert mocks base method.
func (m *MockAnchorContractGoServer) SetUserExamineCert(ctx context.Context, in *UserExamineCertInfo) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserExamineCert", ctx, in)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserExamineCert indicates an expected call of SetUserExamineCert.
func (mr *MockAnchorContractGoServerMockRecorder) SetUserExamineCert(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserExamineCert", reflect.TypeOf((*MockAnchorContractGoServer)(nil).SetUserExamineCert), ctx, in)
}

// TestHandleYearBanUser mocks base method.
func (m *MockAnchorContractGoServer) TestHandleYearBanUser(ctx context.Context, in *TestHandleYearBanUserReq) (*TestHandleYearBanUserResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TestHandleYearBanUser", ctx, in)
	ret0, _ := ret[0].(*TestHandleYearBanUserResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestHandleYearBanUser indicates an expected call of TestHandleYearBanUser.
func (mr *MockAnchorContractGoServerMockRecorder) TestHandleYearBanUser(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestHandleYearBanUser", reflect.TypeOf((*MockAnchorContractGoServer)(nil).TestHandleYearBanUser), ctx, in)
}

// TestNotifyNegotiateExpire mocks base method.
func (m *MockAnchorContractGoServer) TestNotifyNegotiateExpire(ctx context.Context, in *TestNotifyNegotiateExpireReq) (*TestNotifyNegotiateExpireResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TestNotifyNegotiateExpire", ctx, in)
	ret0, _ := ret[0].(*TestNotifyNegotiateExpireResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestNotifyNegotiateExpire indicates an expected call of TestNotifyNegotiateExpire.
func (mr *MockAnchorContractGoServerMockRecorder) TestNotifyNegotiateExpire(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestNotifyNegotiateExpire", reflect.TypeOf((*MockAnchorContractGoServer)(nil).TestNotifyNegotiateExpire), ctx, in)
}

// TestSignContract mocks base method.
func (m *MockAnchorContractGoServer) TestSignContract(ctx context.Context, in *TestSignContractReq) (*TestSignContractResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TestSignContract", ctx, in)
	ret0, _ := ret[0].(*TestSignContractResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestSignContract indicates an expected call of TestSignContract.
func (mr *MockAnchorContractGoServerMockRecorder) TestSignContract(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestSignContract", reflect.TypeOf((*MockAnchorContractGoServer)(nil).TestSignContract), ctx, in)
}

// TriggerTimer mocks base method.
func (m *MockAnchorContractGoServer) TriggerTimer(ctx context.Context, in *TriggerTimerReq) (*TriggerTimerResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TriggerTimer", ctx, in)
	ret0, _ := ret[0].(*TriggerTimerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TriggerTimer indicates an expected call of TriggerTimer.
func (mr *MockAnchorContractGoServerMockRecorder) TriggerTimer(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TriggerTimer", reflect.TypeOf((*MockAnchorContractGoServer)(nil).TriggerTimer), ctx, in)
}

// UpdateAnchorCertContent mocks base method.
func (m *MockAnchorContractGoServer) UpdateAnchorCertContent(ctx context.Context, in *UpdateAnchorCertContentReq) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAnchorCertContent", ctx, in)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAnchorCertContent indicates an expected call of UpdateAnchorCertContent.
func (mr *MockAnchorContractGoServerMockRecorder) UpdateAnchorCertContent(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAnchorCertContent", reflect.TypeOf((*MockAnchorContractGoServer)(nil).UpdateAnchorCertContent), ctx, in)
}

// UpdateAnchorCertUpgradeTask mocks base method.
func (m *MockAnchorContractGoServer) UpdateAnchorCertUpgradeTask(ctx context.Context, in *UpdateAnchorCertUpgradeTaskReq) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAnchorCertUpgradeTask", ctx, in)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAnchorCertUpgradeTask indicates an expected call of UpdateAnchorCertUpgradeTask.
func (mr *MockAnchorContractGoServerMockRecorder) UpdateAnchorCertUpgradeTask(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAnchorCertUpgradeTask", reflect.TypeOf((*MockAnchorContractGoServer)(nil).UpdateAnchorCertUpgradeTask), ctx, in)
}

// UpdateChildExamineCert mocks base method.
func (m *MockAnchorContractGoServer) UpdateChildExamineCert(ctx context.Context, in *ExamineCertInfo) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateChildExamineCert", ctx, in)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateChildExamineCert indicates an expected call of UpdateChildExamineCert.
func (mr *MockAnchorContractGoServerMockRecorder) UpdateChildExamineCert(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateChildExamineCert", reflect.TypeOf((*MockAnchorContractGoServer)(nil).UpdateChildExamineCert), ctx, in)
}

// UpdateGuildSignRight mocks base method.
func (m *MockAnchorContractGoServer) UpdateGuildSignRight(ctx context.Context, in *UpdateGuildSignRightReq) (*UpdateGuildSignRightResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateGuildSignRight", ctx, in)
	ret0, _ := ret[0].(*UpdateGuildSignRightResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateGuildSignRight indicates an expected call of UpdateGuildSignRight.
func (mr *MockAnchorContractGoServerMockRecorder) UpdateGuildSignRight(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateGuildSignRight", reflect.TypeOf((*MockAnchorContractGoServer)(nil).UpdateGuildSignRight), ctx, in)
}

// UpdateLiveAnchorExamineStatus mocks base method.
func (m *MockAnchorContractGoServer) UpdateLiveAnchorExamineStatus(ctx context.Context, in *UpdateLiveAnchorExamineStatusReq) (*UpdateLiveAnchorExamineStatusResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateLiveAnchorExamineStatus", ctx, in)
	ret0, _ := ret[0].(*UpdateLiveAnchorExamineStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateLiveAnchorExamineStatus indicates an expected call of UpdateLiveAnchorExamineStatus.
func (mr *MockAnchorContractGoServerMockRecorder) UpdateLiveAnchorExamineStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLiveAnchorExamineStatus", reflect.TypeOf((*MockAnchorContractGoServer)(nil).UpdateLiveAnchorExamineStatus), ctx, in)
}

// UpdateLiveAnchorExamineTime mocks base method.
func (m *MockAnchorContractGoServer) UpdateLiveAnchorExamineTime(ctx context.Context, in *UpdateLiveAnchorExamineTimeReq) (*UpdateLiveAnchorExamineTimeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateLiveAnchorExamineTime", ctx, in)
	ret0, _ := ret[0].(*UpdateLiveAnchorExamineTimeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateLiveAnchorExamineTime indicates an expected call of UpdateLiveAnchorExamineTime.
func (mr *MockAnchorContractGoServerMockRecorder) UpdateLiveAnchorExamineTime(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLiveAnchorExamineTime", reflect.TypeOf((*MockAnchorContractGoServer)(nil).UpdateLiveAnchorExamineTime), ctx, in)
}

// UpdateParentExamineCert mocks base method.
func (m *MockAnchorContractGoServer) UpdateParentExamineCert(ctx context.Context, in *ExamineCertInfo) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateParentExamineCert", ctx, in)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateParentExamineCert indicates an expected call of UpdateParentExamineCert.
func (mr *MockAnchorContractGoServerMockRecorder) UpdateParentExamineCert(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateParentExamineCert", reflect.TypeOf((*MockAnchorContractGoServer)(nil).UpdateParentExamineCert), ctx, in)
}

// UpdateRadioLiveAnchorExamine mocks base method.
func (m *MockAnchorContractGoServer) UpdateRadioLiveAnchorExamine(ctx context.Context, in *UpdateRadioLiveAnchorExamineReq) (*UpdateRadioLiveAnchorExamineResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRadioLiveAnchorExamine", ctx, in)
	ret0, _ := ret[0].(*UpdateRadioLiveAnchorExamineResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateRadioLiveAnchorExamine indicates an expected call of UpdateRadioLiveAnchorExamine.
func (mr *MockAnchorContractGoServerMockRecorder) UpdateRadioLiveAnchorExamine(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRadioLiveAnchorExamine", reflect.TypeOf((*MockAnchorContractGoServer)(nil).UpdateRadioLiveAnchorExamine), ctx, in)
}

// UpdateRemark mocks base method.
func (m *MockAnchorContractGoServer) UpdateRemark(ctx context.Context, in *UpdateRemarkReq) (*UpdateRemarkResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRemark", ctx, in)
	ret0, _ := ret[0].(*UpdateRemarkResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateRemark indicates an expected call of UpdateRemark.
func (mr *MockAnchorContractGoServerMockRecorder) UpdateRemark(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRemark", reflect.TypeOf((*MockAnchorContractGoServer)(nil).UpdateRemark), ctx, in)
}

// UpdateSignRight mocks base method.
func (m *MockAnchorContractGoServer) UpdateSignRight(ctx context.Context, in *UpdateSignRightReq) (*UpdateSignRightResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSignRight", ctx, in)
	ret0, _ := ret[0].(*UpdateSignRightResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateSignRight indicates an expected call of UpdateSignRight.
func (mr *MockAnchorContractGoServerMockRecorder) UpdateSignRight(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSignRight", reflect.TypeOf((*MockAnchorContractGoServer)(nil).UpdateSignRight), ctx, in)
}

// UpdateSignedAnchorAgentId mocks base method.
func (m *MockAnchorContractGoServer) UpdateSignedAnchorAgentId(ctx context.Context, in *UpdateSignedAnchorAgentIdReq) (*UpdateSignedAnchorAgentIdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSignedAnchorAgentId", ctx, in)
	ret0, _ := ret[0].(*UpdateSignedAnchorAgentIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateSignedAnchorAgentId indicates an expected call of UpdateSignedAnchorAgentId.
func (mr *MockAnchorContractGoServerMockRecorder) UpdateSignedAnchorAgentId(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSignedAnchorAgentId", reflect.TypeOf((*MockAnchorContractGoServer)(nil).UpdateSignedAnchorAgentId), ctx, in)
}

// UpdateUserExamineCert mocks base method.
func (m *MockAnchorContractGoServer) UpdateUserExamineCert(ctx context.Context, in *UserExamineCertInfo) (*CertEmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUserExamineCert", ctx, in)
	ret0, _ := ret[0].(*CertEmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateUserExamineCert indicates an expected call of UpdateUserExamineCert.
func (mr *MockAnchorContractGoServerMockRecorder) UpdateUserExamineCert(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserExamineCert", reflect.TypeOf((*MockAnchorContractGoServer)(nil).UpdateUserExamineCert), ctx, in)
}

// WithdrawApplySign mocks base method.
func (m *MockAnchorContractGoServer) WithdrawApplySign(ctx context.Context, in *WithdrawApplySignReq) (*WithdrawApplySignResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithdrawApplySign", ctx, in)
	ret0, _ := ret[0].(*WithdrawApplySignResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// WithdrawApplySign indicates an expected call of WithdrawApplySign.
func (mr *MockAnchorContractGoServerMockRecorder) WithdrawApplySign(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithdrawApplySign", reflect.TypeOf((*MockAnchorContractGoServer)(nil).WithdrawApplySign), ctx, in)
}
