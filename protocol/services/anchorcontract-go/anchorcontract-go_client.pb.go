// Code generated by protoc-gen-tyr-client. DO NOT EDIT.
// versions:
// - protoc-gen-tyr-client v0.0.1
// - protoc                   (unknown)
// source: tt/quicksilver/anchorcontract-go/anchorcontract-go.proto

package anchorcontract_go

import (
	client "gitlab.ttyuyin.com/avengers/tyr/core/service/grpc/client"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

const serviceName = "anchorcontract-go"

type Client struct {
	AnchorContractGoClient

	cc *grpc.ClientConn
}

func MustNewClient(ctx context.Context, opts ...grpc.DialOption) *Client {
	c, err := NewClient(ctx, opts...)
	if err != nil {
		panic(err)
	}
	return c
}

func NewClient(ctx context.Context, opts ...grpc.DialOption) (*Client, error) {
	c := &Client{}
	if err := client.DialContextWithConnUpdate(ctx, serviceName, opts, func(cc *grpc.ClientConn) *grpc.ClientConn {
		c.cc, cc = cc, c.cc
		c.AnchorContractGoClient = NewAnchorContractGoClient(c.cc)
		return cc
	}); err != nil {
		return nil, err
	}
	return c, nil
}

func MustNewClientTo(ctx context.Context, target string, opts ...grpc.DialOption) *Client {
	cc, err := client.DialContextTo(ctx, target, opts...)
	if err != nil {
		panic(err)
	}
	return &Client{
		AnchorContractGoClient: NewAnchorContractGoClient(cc),
	}
}
