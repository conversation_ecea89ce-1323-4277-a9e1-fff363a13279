// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/wealth-god/wealth-god.proto

package wealth_god // import "golang.52tt.com/protocol/services/wealth-god"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type WealthGodBoxType int32

const (
	WealthGodBoxType_WEALTH_GOD_BOX_TYPE_UNSPECIFIED WealthGodBoxType = 0
	WealthGodBoxType_WEALTH_GOD_BOX_TYPE_S           WealthGodBoxType = 1
	WealthGodBoxType_WEALTH_GOD_BOX_TYPE_A_PLUS      WealthGodBoxType = 2
	WealthGodBoxType_WEALTH_GOD_BOX_TYPE_A           WealthGodBoxType = 3
)

var WealthGodBoxType_name = map[int32]string{
	0: "WEALTH_GOD_BOX_TYPE_UNSPECIFIED",
	1: "WEALTH_GOD_BOX_TYPE_S",
	2: "WEALTH_GOD_BOX_TYPE_A_PLUS",
	3: "WEALTH_GOD_BOX_TYPE_A",
}
var WealthGodBoxType_value = map[string]int32{
	"WEALTH_GOD_BOX_TYPE_UNSPECIFIED": 0,
	"WEALTH_GOD_BOX_TYPE_S":           1,
	"WEALTH_GOD_BOX_TYPE_A_PLUS":      2,
	"WEALTH_GOD_BOX_TYPE_A":           3,
}

func (x WealthGodBoxType) String() string {
	return proto.EnumName(WealthGodBoxType_name, int32(x))
}
func (WealthGodBoxType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_7fc04b858b2f3559, []int{0}
}

type TriggerType int32

const (
	TriggerType_TRIGGER_TYPE_UNSPECIFIED TriggerType = 0
	TriggerType_TRIGGER_TYPE_PRESENT     TriggerType = 1
	TriggerType_TRIGGER_TYPE_PRESENT_SET TriggerType = 2
)

var TriggerType_name = map[int32]string{
	0: "TRIGGER_TYPE_UNSPECIFIED",
	1: "TRIGGER_TYPE_PRESENT",
	2: "TRIGGER_TYPE_PRESENT_SET",
}
var TriggerType_value = map[string]int32{
	"TRIGGER_TYPE_UNSPECIFIED": 0,
	"TRIGGER_TYPE_PRESENT":     1,
	"TRIGGER_TYPE_PRESENT_SET": 2,
}

func (x TriggerType) String() string {
	return proto.EnumName(TriggerType_name, int32(x))
}
func (TriggerType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_7fc04b858b2f3559, []int{1}
}

type RewardType int32

const (
	RewardType_REWARD_TYPE_UNSPECIFIED        RewardType = 0
	RewardType_REWARD_TYPE_PACKAGE            RewardType = 1
	RewardType_REWARD_TYPE_HORSE              RewardType = 2
	RewardType_REWARD_TYPE_MIC_STYLE          RewardType = 3
	RewardType_REWARD_TYPE_DECORATION         RewardType = 4
	RewardType_REWARD_TYPE_NAMEPLATE          RewardType = 5
	RewardType_REWARD_TYPE_CHANNEL_BACKGROUND RewardType = 6
)

var RewardType_name = map[int32]string{
	0: "REWARD_TYPE_UNSPECIFIED",
	1: "REWARD_TYPE_PACKAGE",
	2: "REWARD_TYPE_HORSE",
	3: "REWARD_TYPE_MIC_STYLE",
	4: "REWARD_TYPE_DECORATION",
	5: "REWARD_TYPE_NAMEPLATE",
	6: "REWARD_TYPE_CHANNEL_BACKGROUND",
}
var RewardType_value = map[string]int32{
	"REWARD_TYPE_UNSPECIFIED":        0,
	"REWARD_TYPE_PACKAGE":            1,
	"REWARD_TYPE_HORSE":              2,
	"REWARD_TYPE_MIC_STYLE":          3,
	"REWARD_TYPE_DECORATION":         4,
	"REWARD_TYPE_NAMEPLATE":          5,
	"REWARD_TYPE_CHANNEL_BACKGROUND": 6,
}

func (x RewardType) String() string {
	return proto.EnumName(RewardType_name, int32(x))
}
func (RewardType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_7fc04b858b2f3559, []int{2}
}

type WealthGod struct {
	GodId                     string   `protobuf:"bytes,1,opt,name=god_id,json=godId,proto3" json:"god_id,omitempty"`
	ChannelId                 uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	StartTs                   int64    `protobuf:"varint,3,opt,name=start_ts,json=startTs,proto3" json:"start_ts,omitempty"`
	EndTs                     int64    `protobuf:"varint,4,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	MissionEndTs              int64    `protobuf:"varint,5,opt,name=mission_end_ts,json=missionEndTs,proto3" json:"mission_end_ts,omitempty"`
	IsStayRoomMissionFinished bool     `protobuf:"varint,6,opt,name=is_stay_room_mission_finished,json=isStayRoomMissionFinished,proto3" json:"is_stay_room_mission_finished,omitempty"`
	XXX_NoUnkeyedLiteral      struct{} `json:"-"`
	XXX_unrecognized          []byte   `json:"-"`
	XXX_sizecache             int32    `json:"-"`
}

func (m *WealthGod) Reset()         { *m = WealthGod{} }
func (m *WealthGod) String() string { return proto.CompactTextString(m) }
func (*WealthGod) ProtoMessage()    {}
func (*WealthGod) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_7fc04b858b2f3559, []int{0}
}
func (m *WealthGod) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WealthGod.Unmarshal(m, b)
}
func (m *WealthGod) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WealthGod.Marshal(b, m, deterministic)
}
func (dst *WealthGod) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WealthGod.Merge(dst, src)
}
func (m *WealthGod) XXX_Size() int {
	return xxx_messageInfo_WealthGod.Size(m)
}
func (m *WealthGod) XXX_DiscardUnknown() {
	xxx_messageInfo_WealthGod.DiscardUnknown(m)
}

var xxx_messageInfo_WealthGod proto.InternalMessageInfo

func (m *WealthGod) GetGodId() string {
	if m != nil {
		return m.GodId
	}
	return ""
}

func (m *WealthGod) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *WealthGod) GetStartTs() int64 {
	if m != nil {
		return m.StartTs
	}
	return 0
}

func (m *WealthGod) GetEndTs() int64 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *WealthGod) GetMissionEndTs() int64 {
	if m != nil {
		return m.MissionEndTs
	}
	return 0
}

func (m *WealthGod) GetIsStayRoomMissionFinished() bool {
	if m != nil {
		return m.IsStayRoomMissionFinished
	}
	return false
}

type WealthGodBox struct {
	BoxType              uint32                       `protobuf:"varint,1,opt,name=box_type,json=boxType,proto3" json:"box_type,omitempty"`
	RewardList           []*WealthGodBoxRewardPreview `protobuf:"bytes,2,rep,name=reward_list,json=rewardList,proto3" json:"reward_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *WealthGodBox) Reset()         { *m = WealthGodBox{} }
func (m *WealthGodBox) String() string { return proto.CompactTextString(m) }
func (*WealthGodBox) ProtoMessage()    {}
func (*WealthGodBox) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_7fc04b858b2f3559, []int{1}
}
func (m *WealthGodBox) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WealthGodBox.Unmarshal(m, b)
}
func (m *WealthGodBox) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WealthGodBox.Marshal(b, m, deterministic)
}
func (dst *WealthGodBox) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WealthGodBox.Merge(dst, src)
}
func (m *WealthGodBox) XXX_Size() int {
	return xxx_messageInfo_WealthGodBox.Size(m)
}
func (m *WealthGodBox) XXX_DiscardUnknown() {
	xxx_messageInfo_WealthGodBox.DiscardUnknown(m)
}

var xxx_messageInfo_WealthGodBox proto.InternalMessageInfo

func (m *WealthGodBox) GetBoxType() uint32 {
	if m != nil {
		return m.BoxType
	}
	return 0
}

func (m *WealthGodBox) GetRewardList() []*WealthGodBoxRewardPreview {
	if m != nil {
		return m.RewardList
	}
	return nil
}

type WealthGodBoxRewardPreview struct {
	IsRare               bool     `protobuf:"varint,1,opt,name=is_rare,json=isRare,proto3" json:"is_rare,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	SubTitle             string   `protobuf:"bytes,3,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	IconUrl              string   `protobuf:"bytes,4,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	CornerText           string   `protobuf:"bytes,5,opt,name=corner_text,json=cornerText,proto3" json:"corner_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WealthGodBoxRewardPreview) Reset()         { *m = WealthGodBoxRewardPreview{} }
func (m *WealthGodBoxRewardPreview) String() string { return proto.CompactTextString(m) }
func (*WealthGodBoxRewardPreview) ProtoMessage()    {}
func (*WealthGodBoxRewardPreview) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_7fc04b858b2f3559, []int{2}
}
func (m *WealthGodBoxRewardPreview) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WealthGodBoxRewardPreview.Unmarshal(m, b)
}
func (m *WealthGodBoxRewardPreview) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WealthGodBoxRewardPreview.Marshal(b, m, deterministic)
}
func (dst *WealthGodBoxRewardPreview) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WealthGodBoxRewardPreview.Merge(dst, src)
}
func (m *WealthGodBoxRewardPreview) XXX_Size() int {
	return xxx_messageInfo_WealthGodBoxRewardPreview.Size(m)
}
func (m *WealthGodBoxRewardPreview) XXX_DiscardUnknown() {
	xxx_messageInfo_WealthGodBoxRewardPreview.DiscardUnknown(m)
}

var xxx_messageInfo_WealthGodBoxRewardPreview proto.InternalMessageInfo

func (m *WealthGodBoxRewardPreview) GetIsRare() bool {
	if m != nil {
		return m.IsRare
	}
	return false
}

func (m *WealthGodBoxRewardPreview) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *WealthGodBoxRewardPreview) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *WealthGodBoxRewardPreview) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *WealthGodBoxRewardPreview) GetCornerText() string {
	if m != nil {
		return m.CornerText
	}
	return ""
}

type GetWealthGodDetailRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GodId                string   `protobuf:"bytes,3,opt,name=god_id,json=godId,proto3" json:"god_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWealthGodDetailRequest) Reset()         { *m = GetWealthGodDetailRequest{} }
func (m *GetWealthGodDetailRequest) String() string { return proto.CompactTextString(m) }
func (*GetWealthGodDetailRequest) ProtoMessage()    {}
func (*GetWealthGodDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_7fc04b858b2f3559, []int{3}
}
func (m *GetWealthGodDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWealthGodDetailRequest.Unmarshal(m, b)
}
func (m *GetWealthGodDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWealthGodDetailRequest.Marshal(b, m, deterministic)
}
func (dst *GetWealthGodDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWealthGodDetailRequest.Merge(dst, src)
}
func (m *GetWealthGodDetailRequest) XXX_Size() int {
	return xxx_messageInfo_GetWealthGodDetailRequest.Size(m)
}
func (m *GetWealthGodDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWealthGodDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWealthGodDetailRequest proto.InternalMessageInfo

func (m *GetWealthGodDetailRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetWealthGodDetailRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetWealthGodDetailRequest) GetGodId() string {
	if m != nil {
		return m.GodId
	}
	return ""
}

type GetWealthGodDetailResponse struct {
	GodInfo              *WealthGod    `protobuf:"bytes,1,opt,name=god_info,json=godInfo,proto3" json:"god_info,omitempty"`
	BoxInfo              *WealthGodBox `protobuf:"bytes,2,opt,name=box_info,json=boxInfo,proto3" json:"box_info,omitempty"`
	AvailableOpenCnt     uint32        `protobuf:"varint,3,opt,name=available_open_cnt,json=availableOpenCnt,proto3" json:"available_open_cnt,omitempty"`
	GetOpenCnt           uint32        `protobuf:"varint,4,opt,name=get_open_cnt,json=getOpenCnt,proto3" json:"get_open_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetWealthGodDetailResponse) Reset()         { *m = GetWealthGodDetailResponse{} }
func (m *GetWealthGodDetailResponse) String() string { return proto.CompactTextString(m) }
func (*GetWealthGodDetailResponse) ProtoMessage()    {}
func (*GetWealthGodDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_7fc04b858b2f3559, []int{4}
}
func (m *GetWealthGodDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWealthGodDetailResponse.Unmarshal(m, b)
}
func (m *GetWealthGodDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWealthGodDetailResponse.Marshal(b, m, deterministic)
}
func (dst *GetWealthGodDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWealthGodDetailResponse.Merge(dst, src)
}
func (m *GetWealthGodDetailResponse) XXX_Size() int {
	return xxx_messageInfo_GetWealthGodDetailResponse.Size(m)
}
func (m *GetWealthGodDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWealthGodDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWealthGodDetailResponse proto.InternalMessageInfo

func (m *GetWealthGodDetailResponse) GetGodInfo() *WealthGod {
	if m != nil {
		return m.GodInfo
	}
	return nil
}

func (m *GetWealthGodDetailResponse) GetBoxInfo() *WealthGodBox {
	if m != nil {
		return m.BoxInfo
	}
	return nil
}

func (m *GetWealthGodDetailResponse) GetAvailableOpenCnt() uint32 {
	if m != nil {
		return m.AvailableOpenCnt
	}
	return 0
}

func (m *GetWealthGodDetailResponse) GetGetOpenCnt() uint32 {
	if m != nil {
		return m.GetOpenCnt
	}
	return 0
}

type OpenWealthGodBoxRewardRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GodId                string   `protobuf:"bytes,3,opt,name=god_id,json=godId,proto3" json:"god_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OpenWealthGodBoxRewardRequest) Reset()         { *m = OpenWealthGodBoxRewardRequest{} }
func (m *OpenWealthGodBoxRewardRequest) String() string { return proto.CompactTextString(m) }
func (*OpenWealthGodBoxRewardRequest) ProtoMessage()    {}
func (*OpenWealthGodBoxRewardRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_7fc04b858b2f3559, []int{5}
}
func (m *OpenWealthGodBoxRewardRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpenWealthGodBoxRewardRequest.Unmarshal(m, b)
}
func (m *OpenWealthGodBoxRewardRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpenWealthGodBoxRewardRequest.Marshal(b, m, deterministic)
}
func (dst *OpenWealthGodBoxRewardRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenWealthGodBoxRewardRequest.Merge(dst, src)
}
func (m *OpenWealthGodBoxRewardRequest) XXX_Size() int {
	return xxx_messageInfo_OpenWealthGodBoxRewardRequest.Size(m)
}
func (m *OpenWealthGodBoxRewardRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenWealthGodBoxRewardRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OpenWealthGodBoxRewardRequest proto.InternalMessageInfo

func (m *OpenWealthGodBoxRewardRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *OpenWealthGodBoxRewardRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *OpenWealthGodBoxRewardRequest) GetGodId() string {
	if m != nil {
		return m.GodId
	}
	return ""
}

type OpenWealthGodBoxRewardResponse struct {
	RewardList           []*WealthGodBoxRewardPreview `protobuf:"bytes,1,rep,name=reward_list,json=rewardList,proto3" json:"reward_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *OpenWealthGodBoxRewardResponse) Reset()         { *m = OpenWealthGodBoxRewardResponse{} }
func (m *OpenWealthGodBoxRewardResponse) String() string { return proto.CompactTextString(m) }
func (*OpenWealthGodBoxRewardResponse) ProtoMessage()    {}
func (*OpenWealthGodBoxRewardResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_7fc04b858b2f3559, []int{6}
}
func (m *OpenWealthGodBoxRewardResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpenWealthGodBoxRewardResponse.Unmarshal(m, b)
}
func (m *OpenWealthGodBoxRewardResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpenWealthGodBoxRewardResponse.Marshal(b, m, deterministic)
}
func (dst *OpenWealthGodBoxRewardResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenWealthGodBoxRewardResponse.Merge(dst, src)
}
func (m *OpenWealthGodBoxRewardResponse) XXX_Size() int {
	return xxx_messageInfo_OpenWealthGodBoxRewardResponse.Size(m)
}
func (m *OpenWealthGodBoxRewardResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenWealthGodBoxRewardResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OpenWealthGodBoxRewardResponse proto.InternalMessageInfo

func (m *OpenWealthGodBoxRewardResponse) GetRewardList() []*WealthGodBoxRewardPreview {
	if m != nil {
		return m.RewardList
	}
	return nil
}

type SysOpenWealthGodBoxRewardRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GodInfoJson          string   `protobuf:"bytes,2,opt,name=god_info_json,json=godInfoJson,proto3" json:"god_info_json,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SysOpenWealthGodBoxRewardRequest) Reset()         { *m = SysOpenWealthGodBoxRewardRequest{} }
func (m *SysOpenWealthGodBoxRewardRequest) String() string { return proto.CompactTextString(m) }
func (*SysOpenWealthGodBoxRewardRequest) ProtoMessage()    {}
func (*SysOpenWealthGodBoxRewardRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_7fc04b858b2f3559, []int{7}
}
func (m *SysOpenWealthGodBoxRewardRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SysOpenWealthGodBoxRewardRequest.Unmarshal(m, b)
}
func (m *SysOpenWealthGodBoxRewardRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SysOpenWealthGodBoxRewardRequest.Marshal(b, m, deterministic)
}
func (dst *SysOpenWealthGodBoxRewardRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SysOpenWealthGodBoxRewardRequest.Merge(dst, src)
}
func (m *SysOpenWealthGodBoxRewardRequest) XXX_Size() int {
	return xxx_messageInfo_SysOpenWealthGodBoxRewardRequest.Size(m)
}
func (m *SysOpenWealthGodBoxRewardRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SysOpenWealthGodBoxRewardRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SysOpenWealthGodBoxRewardRequest proto.InternalMessageInfo

func (m *SysOpenWealthGodBoxRewardRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SysOpenWealthGodBoxRewardRequest) GetGodInfoJson() string {
	if m != nil {
		return m.GodInfoJson
	}
	return ""
}

type SysOpenWealthGodBoxRewardResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SysOpenWealthGodBoxRewardResponse) Reset()         { *m = SysOpenWealthGodBoxRewardResponse{} }
func (m *SysOpenWealthGodBoxRewardResponse) String() string { return proto.CompactTextString(m) }
func (*SysOpenWealthGodBoxRewardResponse) ProtoMessage()    {}
func (*SysOpenWealthGodBoxRewardResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_7fc04b858b2f3559, []int{8}
}
func (m *SysOpenWealthGodBoxRewardResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SysOpenWealthGodBoxRewardResponse.Unmarshal(m, b)
}
func (m *SysOpenWealthGodBoxRewardResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SysOpenWealthGodBoxRewardResponse.Marshal(b, m, deterministic)
}
func (dst *SysOpenWealthGodBoxRewardResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SysOpenWealthGodBoxRewardResponse.Merge(dst, src)
}
func (m *SysOpenWealthGodBoxRewardResponse) XXX_Size() int {
	return xxx_messageInfo_SysOpenWealthGodBoxRewardResponse.Size(m)
}
func (m *SysOpenWealthGodBoxRewardResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SysOpenWealthGodBoxRewardResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SysOpenWealthGodBoxRewardResponse proto.InternalMessageInfo

type WealthGodBoxRewardItem struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	IconUrl              string   `protobuf:"bytes,2,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	Title                string   `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	SubTitle             string   `protobuf:"bytes,4,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	IsRare               bool     `protobuf:"varint,5,opt,name=is_rare,json=isRare,proto3" json:"is_rare,omitempty"`
	TimeStr              string   `protobuf:"bytes,6,opt,name=time_str,json=timeStr,proto3" json:"time_str,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WealthGodBoxRewardItem) Reset()         { *m = WealthGodBoxRewardItem{} }
func (m *WealthGodBoxRewardItem) String() string { return proto.CompactTextString(m) }
func (*WealthGodBoxRewardItem) ProtoMessage()    {}
func (*WealthGodBoxRewardItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_7fc04b858b2f3559, []int{9}
}
func (m *WealthGodBoxRewardItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WealthGodBoxRewardItem.Unmarshal(m, b)
}
func (m *WealthGodBoxRewardItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WealthGodBoxRewardItem.Marshal(b, m, deterministic)
}
func (dst *WealthGodBoxRewardItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WealthGodBoxRewardItem.Merge(dst, src)
}
func (m *WealthGodBoxRewardItem) XXX_Size() int {
	return xxx_messageInfo_WealthGodBoxRewardItem.Size(m)
}
func (m *WealthGodBoxRewardItem) XXX_DiscardUnknown() {
	xxx_messageInfo_WealthGodBoxRewardItem.DiscardUnknown(m)
}

var xxx_messageInfo_WealthGodBoxRewardItem proto.InternalMessageInfo

func (m *WealthGodBoxRewardItem) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *WealthGodBoxRewardItem) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *WealthGodBoxRewardItem) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *WealthGodBoxRewardItem) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *WealthGodBoxRewardItem) GetIsRare() bool {
	if m != nil {
		return m.IsRare
	}
	return false
}

func (m *WealthGodBoxRewardItem) GetTimeStr() string {
	if m != nil {
		return m.TimeStr
	}
	return ""
}

type GetWealthGodBoxRewardListRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Page                 uint32   `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWealthGodBoxRewardListRequest) Reset()         { *m = GetWealthGodBoxRewardListRequest{} }
func (m *GetWealthGodBoxRewardListRequest) String() string { return proto.CompactTextString(m) }
func (*GetWealthGodBoxRewardListRequest) ProtoMessage()    {}
func (*GetWealthGodBoxRewardListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_7fc04b858b2f3559, []int{10}
}
func (m *GetWealthGodBoxRewardListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWealthGodBoxRewardListRequest.Unmarshal(m, b)
}
func (m *GetWealthGodBoxRewardListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWealthGodBoxRewardListRequest.Marshal(b, m, deterministic)
}
func (dst *GetWealthGodBoxRewardListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWealthGodBoxRewardListRequest.Merge(dst, src)
}
func (m *GetWealthGodBoxRewardListRequest) XXX_Size() int {
	return xxx_messageInfo_GetWealthGodBoxRewardListRequest.Size(m)
}
func (m *GetWealthGodBoxRewardListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWealthGodBoxRewardListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWealthGodBoxRewardListRequest proto.InternalMessageInfo

func (m *GetWealthGodBoxRewardListRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetWealthGodBoxRewardListRequest) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetWealthGodBoxRewardListRequest) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetWealthGodBoxRewardListResponse struct {
	RewardList           []*WealthGodBoxRewardItem `protobuf:"bytes,1,rep,name=reward_list,json=rewardList,proto3" json:"reward_list,omitempty"`
	HasMore              bool                      `protobuf:"varint,2,opt,name=has_more,json=hasMore,proto3" json:"has_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetWealthGodBoxRewardListResponse) Reset()         { *m = GetWealthGodBoxRewardListResponse{} }
func (m *GetWealthGodBoxRewardListResponse) String() string { return proto.CompactTextString(m) }
func (*GetWealthGodBoxRewardListResponse) ProtoMessage()    {}
func (*GetWealthGodBoxRewardListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_7fc04b858b2f3559, []int{11}
}
func (m *GetWealthGodBoxRewardListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWealthGodBoxRewardListResponse.Unmarshal(m, b)
}
func (m *GetWealthGodBoxRewardListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWealthGodBoxRewardListResponse.Marshal(b, m, deterministic)
}
func (dst *GetWealthGodBoxRewardListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWealthGodBoxRewardListResponse.Merge(dst, src)
}
func (m *GetWealthGodBoxRewardListResponse) XXX_Size() int {
	return xxx_messageInfo_GetWealthGodBoxRewardListResponse.Size(m)
}
func (m *GetWealthGodBoxRewardListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWealthGodBoxRewardListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWealthGodBoxRewardListResponse proto.InternalMessageInfo

func (m *GetWealthGodBoxRewardListResponse) GetRewardList() []*WealthGodBoxRewardItem {
	if m != nil {
		return m.RewardList
	}
	return nil
}

func (m *GetWealthGodBoxRewardListResponse) GetHasMore() bool {
	if m != nil {
		return m.HasMore
	}
	return false
}

type ReportStayRoomMissionFinishRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GodId                string   `protobuf:"bytes,2,opt,name=god_id,json=godId,proto3" json:"god_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportStayRoomMissionFinishRequest) Reset()         { *m = ReportStayRoomMissionFinishRequest{} }
func (m *ReportStayRoomMissionFinishRequest) String() string { return proto.CompactTextString(m) }
func (*ReportStayRoomMissionFinishRequest) ProtoMessage()    {}
func (*ReportStayRoomMissionFinishRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_7fc04b858b2f3559, []int{12}
}
func (m *ReportStayRoomMissionFinishRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportStayRoomMissionFinishRequest.Unmarshal(m, b)
}
func (m *ReportStayRoomMissionFinishRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportStayRoomMissionFinishRequest.Marshal(b, m, deterministic)
}
func (dst *ReportStayRoomMissionFinishRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportStayRoomMissionFinishRequest.Merge(dst, src)
}
func (m *ReportStayRoomMissionFinishRequest) XXX_Size() int {
	return xxx_messageInfo_ReportStayRoomMissionFinishRequest.Size(m)
}
func (m *ReportStayRoomMissionFinishRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportStayRoomMissionFinishRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ReportStayRoomMissionFinishRequest proto.InternalMessageInfo

func (m *ReportStayRoomMissionFinishRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReportStayRoomMissionFinishRequest) GetGodId() string {
	if m != nil {
		return m.GodId
	}
	return ""
}

type ReportStayRoomMissionFinishResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportStayRoomMissionFinishResponse) Reset()         { *m = ReportStayRoomMissionFinishResponse{} }
func (m *ReportStayRoomMissionFinishResponse) String() string { return proto.CompactTextString(m) }
func (*ReportStayRoomMissionFinishResponse) ProtoMessage()    {}
func (*ReportStayRoomMissionFinishResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_7fc04b858b2f3559, []int{13}
}
func (m *ReportStayRoomMissionFinishResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportStayRoomMissionFinishResponse.Unmarshal(m, b)
}
func (m *ReportStayRoomMissionFinishResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportStayRoomMissionFinishResponse.Marshal(b, m, deterministic)
}
func (dst *ReportStayRoomMissionFinishResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportStayRoomMissionFinishResponse.Merge(dst, src)
}
func (m *ReportStayRoomMissionFinishResponse) XXX_Size() int {
	return xxx_messageInfo_ReportStayRoomMissionFinishResponse.Size(m)
}
func (m *ReportStayRoomMissionFinishResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportStayRoomMissionFinishResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ReportStayRoomMissionFinishResponse proto.InternalMessageInfo

type GetWealthMissionInfoRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GodId                string   `protobuf:"bytes,2,opt,name=god_id,json=godId,proto3" json:"god_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWealthMissionInfoRequest) Reset()         { *m = GetWealthMissionInfoRequest{} }
func (m *GetWealthMissionInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetWealthMissionInfoRequest) ProtoMessage()    {}
func (*GetWealthMissionInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_7fc04b858b2f3559, []int{14}
}
func (m *GetWealthMissionInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWealthMissionInfoRequest.Unmarshal(m, b)
}
func (m *GetWealthMissionInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWealthMissionInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetWealthMissionInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWealthMissionInfoRequest.Merge(dst, src)
}
func (m *GetWealthMissionInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetWealthMissionInfoRequest.Size(m)
}
func (m *GetWealthMissionInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWealthMissionInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWealthMissionInfoRequest proto.InternalMessageInfo

func (m *GetWealthMissionInfoRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetWealthMissionInfoRequest) GetGodId() string {
	if m != nil {
		return m.GodId
	}
	return ""
}

type WealthMissionInfo struct {
	MissionType          uint32   `protobuf:"varint,1,opt,name=mission_type,json=missionType,proto3" json:"mission_type,omitempty"`
	MissionIcon          string   `protobuf:"bytes,2,opt,name=mission_icon,json=missionIcon,proto3" json:"mission_icon,omitempty"`
	MissionName          string   `protobuf:"bytes,3,opt,name=mission_name,json=missionName,proto3" json:"mission_name,omitempty"`
	MissionDesc          string   `protobuf:"bytes,4,opt,name=mission_desc,json=missionDesc,proto3" json:"mission_desc,omitempty"`
	MissionCntUint       string   `protobuf:"bytes,5,opt,name=mission_cnt_uint,json=missionCntUint,proto3" json:"mission_cnt_uint,omitempty"`
	NeedMissionCnt       uint32   `protobuf:"varint,6,opt,name=need_mission_cnt,json=needMissionCnt,proto3" json:"need_mission_cnt,omitempty"`
	CurrentCnt           uint32   `protobuf:"varint,7,opt,name=current_cnt,json=currentCnt,proto3" json:"current_cnt,omitempty"`
	IsReachLimit         bool     `protobuf:"varint,8,opt,name=is_reach_limit,json=isReachLimit,proto3" json:"is_reach_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WealthMissionInfo) Reset()         { *m = WealthMissionInfo{} }
func (m *WealthMissionInfo) String() string { return proto.CompactTextString(m) }
func (*WealthMissionInfo) ProtoMessage()    {}
func (*WealthMissionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_7fc04b858b2f3559, []int{15}
}
func (m *WealthMissionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WealthMissionInfo.Unmarshal(m, b)
}
func (m *WealthMissionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WealthMissionInfo.Marshal(b, m, deterministic)
}
func (dst *WealthMissionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WealthMissionInfo.Merge(dst, src)
}
func (m *WealthMissionInfo) XXX_Size() int {
	return xxx_messageInfo_WealthMissionInfo.Size(m)
}
func (m *WealthMissionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WealthMissionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WealthMissionInfo proto.InternalMessageInfo

func (m *WealthMissionInfo) GetMissionType() uint32 {
	if m != nil {
		return m.MissionType
	}
	return 0
}

func (m *WealthMissionInfo) GetMissionIcon() string {
	if m != nil {
		return m.MissionIcon
	}
	return ""
}

func (m *WealthMissionInfo) GetMissionName() string {
	if m != nil {
		return m.MissionName
	}
	return ""
}

func (m *WealthMissionInfo) GetMissionDesc() string {
	if m != nil {
		return m.MissionDesc
	}
	return ""
}

func (m *WealthMissionInfo) GetMissionCntUint() string {
	if m != nil {
		return m.MissionCntUint
	}
	return ""
}

func (m *WealthMissionInfo) GetNeedMissionCnt() uint32 {
	if m != nil {
		return m.NeedMissionCnt
	}
	return 0
}

func (m *WealthMissionInfo) GetCurrentCnt() uint32 {
	if m != nil {
		return m.CurrentCnt
	}
	return 0
}

func (m *WealthMissionInfo) GetIsReachLimit() bool {
	if m != nil {
		return m.IsReachLimit
	}
	return false
}

type GetWealthMissionInfoResponse struct {
	WealthMissionInfoList []*WealthMissionInfo `protobuf:"bytes,1,rep,name=wealth_mission_info_list,json=wealthMissionInfoList,proto3" json:"wealth_mission_info_list,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}             `json:"-"`
	XXX_unrecognized      []byte               `json:"-"`
	XXX_sizecache         int32                `json:"-"`
}

func (m *GetWealthMissionInfoResponse) Reset()         { *m = GetWealthMissionInfoResponse{} }
func (m *GetWealthMissionInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetWealthMissionInfoResponse) ProtoMessage()    {}
func (*GetWealthMissionInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_7fc04b858b2f3559, []int{16}
}
func (m *GetWealthMissionInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWealthMissionInfoResponse.Unmarshal(m, b)
}
func (m *GetWealthMissionInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWealthMissionInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetWealthMissionInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWealthMissionInfoResponse.Merge(dst, src)
}
func (m *GetWealthMissionInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetWealthMissionInfoResponse.Size(m)
}
func (m *GetWealthMissionInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWealthMissionInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWealthMissionInfoResponse proto.InternalMessageInfo

func (m *GetWealthMissionInfoResponse) GetWealthMissionInfoList() []*WealthMissionInfo {
	if m != nil {
		return m.WealthMissionInfoList
	}
	return nil
}

type GetOneWealthGodChannelRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOneWealthGodChannelRequest) Reset()         { *m = GetOneWealthGodChannelRequest{} }
func (m *GetOneWealthGodChannelRequest) String() string { return proto.CompactTextString(m) }
func (*GetOneWealthGodChannelRequest) ProtoMessage()    {}
func (*GetOneWealthGodChannelRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_7fc04b858b2f3559, []int{17}
}
func (m *GetOneWealthGodChannelRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOneWealthGodChannelRequest.Unmarshal(m, b)
}
func (m *GetOneWealthGodChannelRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOneWealthGodChannelRequest.Marshal(b, m, deterministic)
}
func (dst *GetOneWealthGodChannelRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOneWealthGodChannelRequest.Merge(dst, src)
}
func (m *GetOneWealthGodChannelRequest) XXX_Size() int {
	return xxx_messageInfo_GetOneWealthGodChannelRequest.Size(m)
}
func (m *GetOneWealthGodChannelRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOneWealthGodChannelRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetOneWealthGodChannelRequest proto.InternalMessageInfo

type GetOneWealthGodChannelResponse struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOneWealthGodChannelResponse) Reset()         { *m = GetOneWealthGodChannelResponse{} }
func (m *GetOneWealthGodChannelResponse) String() string { return proto.CompactTextString(m) }
func (*GetOneWealthGodChannelResponse) ProtoMessage()    {}
func (*GetOneWealthGodChannelResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_7fc04b858b2f3559, []int{18}
}
func (m *GetOneWealthGodChannelResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOneWealthGodChannelResponse.Unmarshal(m, b)
}
func (m *GetOneWealthGodChannelResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOneWealthGodChannelResponse.Marshal(b, m, deterministic)
}
func (dst *GetOneWealthGodChannelResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOneWealthGodChannelResponse.Merge(dst, src)
}
func (m *GetOneWealthGodChannelResponse) XXX_Size() int {
	return xxx_messageInfo_GetOneWealthGodChannelResponse.Size(m)
}
func (m *GetOneWealthGodChannelResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOneWealthGodChannelResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetOneWealthGodChannelResponse proto.InternalMessageInfo

func (m *GetOneWealthGodChannelResponse) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetWealthGodEntryRequest struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWealthGodEntryRequest) Reset()         { *m = GetWealthGodEntryRequest{} }
func (m *GetWealthGodEntryRequest) String() string { return proto.CompactTextString(m) }
func (*GetWealthGodEntryRequest) ProtoMessage()    {}
func (*GetWealthGodEntryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_7fc04b858b2f3559, []int{19}
}
func (m *GetWealthGodEntryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWealthGodEntryRequest.Unmarshal(m, b)
}
func (m *GetWealthGodEntryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWealthGodEntryRequest.Marshal(b, m, deterministic)
}
func (dst *GetWealthGodEntryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWealthGodEntryRequest.Merge(dst, src)
}
func (m *GetWealthGodEntryRequest) XXX_Size() int {
	return xxx_messageInfo_GetWealthGodEntryRequest.Size(m)
}
func (m *GetWealthGodEntryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWealthGodEntryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWealthGodEntryRequest proto.InternalMessageInfo

func (m *GetWealthGodEntryRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetWealthGodEntryRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetWealthGodEntryResponse struct {
	CurrentChannelGodList []*WealthGod `protobuf:"bytes,1,rep,name=current_channel_god_list,json=currentChannelGodList,proto3" json:"current_channel_god_list,omitempty"`
	LastGodEndTs          int64        `protobuf:"varint,2,opt,name=last_god_end_ts,json=lastGodEndTs,proto3" json:"last_god_end_ts,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}     `json:"-"`
	XXX_unrecognized      []byte       `json:"-"`
	XXX_sizecache         int32        `json:"-"`
}

func (m *GetWealthGodEntryResponse) Reset()         { *m = GetWealthGodEntryResponse{} }
func (m *GetWealthGodEntryResponse) String() string { return proto.CompactTextString(m) }
func (*GetWealthGodEntryResponse) ProtoMessage()    {}
func (*GetWealthGodEntryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_7fc04b858b2f3559, []int{20}
}
func (m *GetWealthGodEntryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWealthGodEntryResponse.Unmarshal(m, b)
}
func (m *GetWealthGodEntryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWealthGodEntryResponse.Marshal(b, m, deterministic)
}
func (dst *GetWealthGodEntryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWealthGodEntryResponse.Merge(dst, src)
}
func (m *GetWealthGodEntryResponse) XXX_Size() int {
	return xxx_messageInfo_GetWealthGodEntryResponse.Size(m)
}
func (m *GetWealthGodEntryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWealthGodEntryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWealthGodEntryResponse proto.InternalMessageInfo

func (m *GetWealthGodEntryResponse) GetCurrentChannelGodList() []*WealthGod {
	if m != nil {
		return m.CurrentChannelGodList
	}
	return nil
}

func (m *GetWealthGodEntryResponse) GetLastGodEndTs() int64 {
	if m != nil {
		return m.LastGodEndTs
	}
	return 0
}

type GetWealthGodCommonCfgRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWealthGodCommonCfgRequest) Reset()         { *m = GetWealthGodCommonCfgRequest{} }
func (m *GetWealthGodCommonCfgRequest) String() string { return proto.CompactTextString(m) }
func (*GetWealthGodCommonCfgRequest) ProtoMessage()    {}
func (*GetWealthGodCommonCfgRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_7fc04b858b2f3559, []int{21}
}
func (m *GetWealthGodCommonCfgRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWealthGodCommonCfgRequest.Unmarshal(m, b)
}
func (m *GetWealthGodCommonCfgRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWealthGodCommonCfgRequest.Marshal(b, m, deterministic)
}
func (dst *GetWealthGodCommonCfgRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWealthGodCommonCfgRequest.Merge(dst, src)
}
func (m *GetWealthGodCommonCfgRequest) XXX_Size() int {
	return xxx_messageInfo_GetWealthGodCommonCfgRequest.Size(m)
}
func (m *GetWealthGodCommonCfgRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWealthGodCommonCfgRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWealthGodCommonCfgRequest proto.InternalMessageInfo

type GetWealthGodCommonCfgResponse struct {
	TriggerList          []*GetWealthGodCommonCfgResponse_WealthGodTrigger `protobuf:"bytes,1,rep,name=trigger_list,json=triggerList,proto3" json:"trigger_list,omitempty"`
	NeedStayRoomSecond   uint32                                            `protobuf:"varint,2,opt,name=need_stay_room_second,json=needStayRoomSecond,proto3" json:"need_stay_room_second,omitempty"`
	ActivityStartTs      int64                                             `protobuf:"varint,3,opt,name=activity_start_ts,json=activityStartTs,proto3" json:"activity_start_ts,omitempty"`
	ActivityEndTs        int64                                             `protobuf:"varint,4,opt,name=activity_end_ts,json=activityEndTs,proto3" json:"activity_end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                          `json:"-"`
	XXX_unrecognized     []byte                                            `json:"-"`
	XXX_sizecache        int32                                             `json:"-"`
}

func (m *GetWealthGodCommonCfgResponse) Reset()         { *m = GetWealthGodCommonCfgResponse{} }
func (m *GetWealthGodCommonCfgResponse) String() string { return proto.CompactTextString(m) }
func (*GetWealthGodCommonCfgResponse) ProtoMessage()    {}
func (*GetWealthGodCommonCfgResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_7fc04b858b2f3559, []int{22}
}
func (m *GetWealthGodCommonCfgResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWealthGodCommonCfgResponse.Unmarshal(m, b)
}
func (m *GetWealthGodCommonCfgResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWealthGodCommonCfgResponse.Marshal(b, m, deterministic)
}
func (dst *GetWealthGodCommonCfgResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWealthGodCommonCfgResponse.Merge(dst, src)
}
func (m *GetWealthGodCommonCfgResponse) XXX_Size() int {
	return xxx_messageInfo_GetWealthGodCommonCfgResponse.Size(m)
}
func (m *GetWealthGodCommonCfgResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWealthGodCommonCfgResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWealthGodCommonCfgResponse proto.InternalMessageInfo

func (m *GetWealthGodCommonCfgResponse) GetTriggerList() []*GetWealthGodCommonCfgResponse_WealthGodTrigger {
	if m != nil {
		return m.TriggerList
	}
	return nil
}

func (m *GetWealthGodCommonCfgResponse) GetNeedStayRoomSecond() uint32 {
	if m != nil {
		return m.NeedStayRoomSecond
	}
	return 0
}

func (m *GetWealthGodCommonCfgResponse) GetActivityStartTs() int64 {
	if m != nil {
		return m.ActivityStartTs
	}
	return 0
}

func (m *GetWealthGodCommonCfgResponse) GetActivityEndTs() int64 {
	if m != nil {
		return m.ActivityEndTs
	}
	return 0
}

type GetWealthGodCommonCfgResponse_WealthGodTrigger struct {
	TriggerType          uint32   `protobuf:"varint,1,opt,name=trigger_type,json=triggerType,proto3" json:"trigger_type,omitempty"`
	TriggerId            uint32   `protobuf:"varint,2,opt,name=trigger_id,json=triggerId,proto3" json:"trigger_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWealthGodCommonCfgResponse_WealthGodTrigger) Reset() {
	*m = GetWealthGodCommonCfgResponse_WealthGodTrigger{}
}
func (m *GetWealthGodCommonCfgResponse_WealthGodTrigger) String() string {
	return proto.CompactTextString(m)
}
func (*GetWealthGodCommonCfgResponse_WealthGodTrigger) ProtoMessage() {}
func (*GetWealthGodCommonCfgResponse_WealthGodTrigger) Descriptor() ([]byte, []int) {
	return fileDescriptor_wealth_god_7fc04b858b2f3559, []int{22, 0}
}
func (m *GetWealthGodCommonCfgResponse_WealthGodTrigger) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWealthGodCommonCfgResponse_WealthGodTrigger.Unmarshal(m, b)
}
func (m *GetWealthGodCommonCfgResponse_WealthGodTrigger) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWealthGodCommonCfgResponse_WealthGodTrigger.Marshal(b, m, deterministic)
}
func (dst *GetWealthGodCommonCfgResponse_WealthGodTrigger) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWealthGodCommonCfgResponse_WealthGodTrigger.Merge(dst, src)
}
func (m *GetWealthGodCommonCfgResponse_WealthGodTrigger) XXX_Size() int {
	return xxx_messageInfo_GetWealthGodCommonCfgResponse_WealthGodTrigger.Size(m)
}
func (m *GetWealthGodCommonCfgResponse_WealthGodTrigger) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWealthGodCommonCfgResponse_WealthGodTrigger.DiscardUnknown(m)
}

var xxx_messageInfo_GetWealthGodCommonCfgResponse_WealthGodTrigger proto.InternalMessageInfo

func (m *GetWealthGodCommonCfgResponse_WealthGodTrigger) GetTriggerType() uint32 {
	if m != nil {
		return m.TriggerType
	}
	return 0
}

func (m *GetWealthGodCommonCfgResponse_WealthGodTrigger) GetTriggerId() uint32 {
	if m != nil {
		return m.TriggerId
	}
	return 0
}

func init() {
	proto.RegisterType((*WealthGod)(nil), "wealth_god.WealthGod")
	proto.RegisterType((*WealthGodBox)(nil), "wealth_god.WealthGodBox")
	proto.RegisterType((*WealthGodBoxRewardPreview)(nil), "wealth_god.WealthGodBoxRewardPreview")
	proto.RegisterType((*GetWealthGodDetailRequest)(nil), "wealth_god.GetWealthGodDetailRequest")
	proto.RegisterType((*GetWealthGodDetailResponse)(nil), "wealth_god.GetWealthGodDetailResponse")
	proto.RegisterType((*OpenWealthGodBoxRewardRequest)(nil), "wealth_god.OpenWealthGodBoxRewardRequest")
	proto.RegisterType((*OpenWealthGodBoxRewardResponse)(nil), "wealth_god.OpenWealthGodBoxRewardResponse")
	proto.RegisterType((*SysOpenWealthGodBoxRewardRequest)(nil), "wealth_god.SysOpenWealthGodBoxRewardRequest")
	proto.RegisterType((*SysOpenWealthGodBoxRewardResponse)(nil), "wealth_god.SysOpenWealthGodBoxRewardResponse")
	proto.RegisterType((*WealthGodBoxRewardItem)(nil), "wealth_god.WealthGodBoxRewardItem")
	proto.RegisterType((*GetWealthGodBoxRewardListRequest)(nil), "wealth_god.GetWealthGodBoxRewardListRequest")
	proto.RegisterType((*GetWealthGodBoxRewardListResponse)(nil), "wealth_god.GetWealthGodBoxRewardListResponse")
	proto.RegisterType((*ReportStayRoomMissionFinishRequest)(nil), "wealth_god.ReportStayRoomMissionFinishRequest")
	proto.RegisterType((*ReportStayRoomMissionFinishResponse)(nil), "wealth_god.ReportStayRoomMissionFinishResponse")
	proto.RegisterType((*GetWealthMissionInfoRequest)(nil), "wealth_god.GetWealthMissionInfoRequest")
	proto.RegisterType((*WealthMissionInfo)(nil), "wealth_god.WealthMissionInfo")
	proto.RegisterType((*GetWealthMissionInfoResponse)(nil), "wealth_god.GetWealthMissionInfoResponse")
	proto.RegisterType((*GetOneWealthGodChannelRequest)(nil), "wealth_god.GetOneWealthGodChannelRequest")
	proto.RegisterType((*GetOneWealthGodChannelResponse)(nil), "wealth_god.GetOneWealthGodChannelResponse")
	proto.RegisterType((*GetWealthGodEntryRequest)(nil), "wealth_god.GetWealthGodEntryRequest")
	proto.RegisterType((*GetWealthGodEntryResponse)(nil), "wealth_god.GetWealthGodEntryResponse")
	proto.RegisterType((*GetWealthGodCommonCfgRequest)(nil), "wealth_god.GetWealthGodCommonCfgRequest")
	proto.RegisterType((*GetWealthGodCommonCfgResponse)(nil), "wealth_god.GetWealthGodCommonCfgResponse")
	proto.RegisterType((*GetWealthGodCommonCfgResponse_WealthGodTrigger)(nil), "wealth_god.GetWealthGodCommonCfgResponse.WealthGodTrigger")
	proto.RegisterEnum("wealth_god.WealthGodBoxType", WealthGodBoxType_name, WealthGodBoxType_value)
	proto.RegisterEnum("wealth_god.TriggerType", TriggerType_name, TriggerType_value)
	proto.RegisterEnum("wealth_god.RewardType", RewardType_name, RewardType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// WealthGodServiceClient is the client API for WealthGodService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type WealthGodServiceClient interface {
	// 获取财神详情
	GetWealthGodDetail(ctx context.Context, in *GetWealthGodDetailRequest, opts ...grpc.CallOption) (*GetWealthGodDetailResponse, error)
	// 开启财神宝箱奖励
	OpenWealthGodBoxReward(ctx context.Context, in *OpenWealthGodBoxRewardRequest, opts ...grpc.CallOption) (*OpenWealthGodBoxRewardResponse, error)
	// 系统开启财神宝箱奖励
	SysOpenWealthGodBoxReward(ctx context.Context, in *SysOpenWealthGodBoxRewardRequest, opts ...grpc.CallOption) (*SysOpenWealthGodBoxRewardResponse, error)
	// 获取财神宝箱奖励列表
	GetWealthGodBoxRewardList(ctx context.Context, in *GetWealthGodBoxRewardListRequest, opts ...grpc.CallOption) (*GetWealthGodBoxRewardListResponse, error)
	// 上报财神降临房间任务完成
	ReportStayRoomMissionFinish(ctx context.Context, in *ReportStayRoomMissionFinishRequest, opts ...grpc.CallOption) (*ReportStayRoomMissionFinishResponse, error)
	// 获取财神任务信息
	GetWealthMissionInfo(ctx context.Context, in *GetWealthMissionInfoRequest, opts ...grpc.CallOption) (*GetWealthMissionInfoResponse, error)
	// 获取一个财神房间
	GetOneWealthGodChannel(ctx context.Context, in *GetOneWealthGodChannelRequest, opts ...grpc.CallOption) (*GetOneWealthGodChannelResponse, error)
	// 获取财神入口信息
	GetWealthGodEntry(ctx context.Context, in *GetWealthGodEntryRequest, opts ...grpc.CallOption) (*GetWealthGodEntryResponse, error)
	// 获取财神通用配置
	GetWealthGodCommonCfg(ctx context.Context, in *GetWealthGodCommonCfgRequest, opts ...grpc.CallOption) (*GetWealthGodCommonCfgResponse, error)
}

type wealthGodServiceClient struct {
	cc *grpc.ClientConn
}

func NewWealthGodServiceClient(cc *grpc.ClientConn) WealthGodServiceClient {
	return &wealthGodServiceClient{cc}
}

func (c *wealthGodServiceClient) GetWealthGodDetail(ctx context.Context, in *GetWealthGodDetailRequest, opts ...grpc.CallOption) (*GetWealthGodDetailResponse, error) {
	out := new(GetWealthGodDetailResponse)
	err := c.cc.Invoke(ctx, "/wealth_god.WealthGodService/GetWealthGodDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wealthGodServiceClient) OpenWealthGodBoxReward(ctx context.Context, in *OpenWealthGodBoxRewardRequest, opts ...grpc.CallOption) (*OpenWealthGodBoxRewardResponse, error) {
	out := new(OpenWealthGodBoxRewardResponse)
	err := c.cc.Invoke(ctx, "/wealth_god.WealthGodService/OpenWealthGodBoxReward", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wealthGodServiceClient) SysOpenWealthGodBoxReward(ctx context.Context, in *SysOpenWealthGodBoxRewardRequest, opts ...grpc.CallOption) (*SysOpenWealthGodBoxRewardResponse, error) {
	out := new(SysOpenWealthGodBoxRewardResponse)
	err := c.cc.Invoke(ctx, "/wealth_god.WealthGodService/SysOpenWealthGodBoxReward", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wealthGodServiceClient) GetWealthGodBoxRewardList(ctx context.Context, in *GetWealthGodBoxRewardListRequest, opts ...grpc.CallOption) (*GetWealthGodBoxRewardListResponse, error) {
	out := new(GetWealthGodBoxRewardListResponse)
	err := c.cc.Invoke(ctx, "/wealth_god.WealthGodService/GetWealthGodBoxRewardList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wealthGodServiceClient) ReportStayRoomMissionFinish(ctx context.Context, in *ReportStayRoomMissionFinishRequest, opts ...grpc.CallOption) (*ReportStayRoomMissionFinishResponse, error) {
	out := new(ReportStayRoomMissionFinishResponse)
	err := c.cc.Invoke(ctx, "/wealth_god.WealthGodService/ReportStayRoomMissionFinish", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wealthGodServiceClient) GetWealthMissionInfo(ctx context.Context, in *GetWealthMissionInfoRequest, opts ...grpc.CallOption) (*GetWealthMissionInfoResponse, error) {
	out := new(GetWealthMissionInfoResponse)
	err := c.cc.Invoke(ctx, "/wealth_god.WealthGodService/GetWealthMissionInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wealthGodServiceClient) GetOneWealthGodChannel(ctx context.Context, in *GetOneWealthGodChannelRequest, opts ...grpc.CallOption) (*GetOneWealthGodChannelResponse, error) {
	out := new(GetOneWealthGodChannelResponse)
	err := c.cc.Invoke(ctx, "/wealth_god.WealthGodService/GetOneWealthGodChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wealthGodServiceClient) GetWealthGodEntry(ctx context.Context, in *GetWealthGodEntryRequest, opts ...grpc.CallOption) (*GetWealthGodEntryResponse, error) {
	out := new(GetWealthGodEntryResponse)
	err := c.cc.Invoke(ctx, "/wealth_god.WealthGodService/GetWealthGodEntry", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wealthGodServiceClient) GetWealthGodCommonCfg(ctx context.Context, in *GetWealthGodCommonCfgRequest, opts ...grpc.CallOption) (*GetWealthGodCommonCfgResponse, error) {
	out := new(GetWealthGodCommonCfgResponse)
	err := c.cc.Invoke(ctx, "/wealth_god.WealthGodService/GetWealthGodCommonCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WealthGodServiceServer is the server API for WealthGodService service.
type WealthGodServiceServer interface {
	// 获取财神详情
	GetWealthGodDetail(context.Context, *GetWealthGodDetailRequest) (*GetWealthGodDetailResponse, error)
	// 开启财神宝箱奖励
	OpenWealthGodBoxReward(context.Context, *OpenWealthGodBoxRewardRequest) (*OpenWealthGodBoxRewardResponse, error)
	// 系统开启财神宝箱奖励
	SysOpenWealthGodBoxReward(context.Context, *SysOpenWealthGodBoxRewardRequest) (*SysOpenWealthGodBoxRewardResponse, error)
	// 获取财神宝箱奖励列表
	GetWealthGodBoxRewardList(context.Context, *GetWealthGodBoxRewardListRequest) (*GetWealthGodBoxRewardListResponse, error)
	// 上报财神降临房间任务完成
	ReportStayRoomMissionFinish(context.Context, *ReportStayRoomMissionFinishRequest) (*ReportStayRoomMissionFinishResponse, error)
	// 获取财神任务信息
	GetWealthMissionInfo(context.Context, *GetWealthMissionInfoRequest) (*GetWealthMissionInfoResponse, error)
	// 获取一个财神房间
	GetOneWealthGodChannel(context.Context, *GetOneWealthGodChannelRequest) (*GetOneWealthGodChannelResponse, error)
	// 获取财神入口信息
	GetWealthGodEntry(context.Context, *GetWealthGodEntryRequest) (*GetWealthGodEntryResponse, error)
	// 获取财神通用配置
	GetWealthGodCommonCfg(context.Context, *GetWealthGodCommonCfgRequest) (*GetWealthGodCommonCfgResponse, error)
}

func RegisterWealthGodServiceServer(s *grpc.Server, srv WealthGodServiceServer) {
	s.RegisterService(&_WealthGodService_serviceDesc, srv)
}

func _WealthGodService_GetWealthGodDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWealthGodDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WealthGodServiceServer).GetWealthGodDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/wealth_god.WealthGodService/GetWealthGodDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WealthGodServiceServer).GetWealthGodDetail(ctx, req.(*GetWealthGodDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WealthGodService_OpenWealthGodBoxReward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OpenWealthGodBoxRewardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WealthGodServiceServer).OpenWealthGodBoxReward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/wealth_god.WealthGodService/OpenWealthGodBoxReward",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WealthGodServiceServer).OpenWealthGodBoxReward(ctx, req.(*OpenWealthGodBoxRewardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WealthGodService_SysOpenWealthGodBoxReward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SysOpenWealthGodBoxRewardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WealthGodServiceServer).SysOpenWealthGodBoxReward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/wealth_god.WealthGodService/SysOpenWealthGodBoxReward",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WealthGodServiceServer).SysOpenWealthGodBoxReward(ctx, req.(*SysOpenWealthGodBoxRewardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WealthGodService_GetWealthGodBoxRewardList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWealthGodBoxRewardListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WealthGodServiceServer).GetWealthGodBoxRewardList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/wealth_god.WealthGodService/GetWealthGodBoxRewardList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WealthGodServiceServer).GetWealthGodBoxRewardList(ctx, req.(*GetWealthGodBoxRewardListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WealthGodService_ReportStayRoomMissionFinish_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportStayRoomMissionFinishRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WealthGodServiceServer).ReportStayRoomMissionFinish(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/wealth_god.WealthGodService/ReportStayRoomMissionFinish",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WealthGodServiceServer).ReportStayRoomMissionFinish(ctx, req.(*ReportStayRoomMissionFinishRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WealthGodService_GetWealthMissionInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWealthMissionInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WealthGodServiceServer).GetWealthMissionInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/wealth_god.WealthGodService/GetWealthMissionInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WealthGodServiceServer).GetWealthMissionInfo(ctx, req.(*GetWealthMissionInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WealthGodService_GetOneWealthGodChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOneWealthGodChannelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WealthGodServiceServer).GetOneWealthGodChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/wealth_god.WealthGodService/GetOneWealthGodChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WealthGodServiceServer).GetOneWealthGodChannel(ctx, req.(*GetOneWealthGodChannelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WealthGodService_GetWealthGodEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWealthGodEntryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WealthGodServiceServer).GetWealthGodEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/wealth_god.WealthGodService/GetWealthGodEntry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WealthGodServiceServer).GetWealthGodEntry(ctx, req.(*GetWealthGodEntryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WealthGodService_GetWealthGodCommonCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWealthGodCommonCfgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WealthGodServiceServer).GetWealthGodCommonCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/wealth_god.WealthGodService/GetWealthGodCommonCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WealthGodServiceServer).GetWealthGodCommonCfg(ctx, req.(*GetWealthGodCommonCfgRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _WealthGodService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "wealth_god.WealthGodService",
	HandlerType: (*WealthGodServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetWealthGodDetail",
			Handler:    _WealthGodService_GetWealthGodDetail_Handler,
		},
		{
			MethodName: "OpenWealthGodBoxReward",
			Handler:    _WealthGodService_OpenWealthGodBoxReward_Handler,
		},
		{
			MethodName: "SysOpenWealthGodBoxReward",
			Handler:    _WealthGodService_SysOpenWealthGodBoxReward_Handler,
		},
		{
			MethodName: "GetWealthGodBoxRewardList",
			Handler:    _WealthGodService_GetWealthGodBoxRewardList_Handler,
		},
		{
			MethodName: "ReportStayRoomMissionFinish",
			Handler:    _WealthGodService_ReportStayRoomMissionFinish_Handler,
		},
		{
			MethodName: "GetWealthMissionInfo",
			Handler:    _WealthGodService_GetWealthMissionInfo_Handler,
		},
		{
			MethodName: "GetOneWealthGodChannel",
			Handler:    _WealthGodService_GetOneWealthGodChannel_Handler,
		},
		{
			MethodName: "GetWealthGodEntry",
			Handler:    _WealthGodService_GetWealthGodEntry_Handler,
		},
		{
			MethodName: "GetWealthGodCommonCfg",
			Handler:    _WealthGodService_GetWealthGodCommonCfg_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/wealth-god/wealth-god.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/wealth-god/wealth-god.proto", fileDescriptor_wealth_god_7fc04b858b2f3559)
}

var fileDescriptor_wealth_god_7fc04b858b2f3559 = []byte{
	// 1589 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x58, 0x4f, 0x73, 0x1b, 0x4b,
	0x11, 0x67, 0x25, 0xff, 0x91, 0xdb, 0x76, 0x9e, 0x3c, 0xc4, 0x89, 0xac, 0x3c, 0x3b, 0xce, 0x26,
	0x7e, 0xf8, 0xb9, 0xf2, 0x6c, 0xc8, 0x2b, 0x2e, 0x5c, 0x40, 0x96, 0x37, 0x8a, 0x88, 0x2d, 0xbb,
	0x46, 0x6b, 0xf2, 0x1e, 0x55, 0xd4, 0xb0, 0xde, 0x9d, 0xc8, 0x03, 0xd2, 0x8c, 0xb2, 0x33, 0x72,
	0xec, 0x77, 0xe0, 0xc2, 0x99, 0x0b, 0x57, 0x8e, 0xdc, 0xf8, 0x02, 0x54, 0xf1, 0x19, 0xa8, 0xe2,
	0xc0, 0xf1, 0x7d, 0x13, 0x4e, 0xd4, 0xce, 0xce, 0x4a, 0xa3, 0xff, 0x0e, 0xc5, 0x49, 0x52, 0xf7,
	0x6f, 0xba, 0x7b, 0xba, 0x7f, 0xd3, 0xdd, 0x25, 0x38, 0x50, 0xea, 0xe8, 0x43, 0x8f, 0x85, 0xbf,
	0x97, 0xac, 0x7d, 0x43, 0xe3, 0xa3, 0x8f, 0x34, 0x68, 0xab, 0xeb, 0xaf, 0x5a, 0x22, 0xb2, 0xbe,
	0x1e, 0x76, 0x63, 0xa1, 0x04, 0x82, 0x54, 0x42, 0x5a, 0x22, 0x2a, 0x1f, 0x8e, 0x9c, 0xa3, 0xb7,
	0x8a, 0x72, 0xc9, 0x04, 0x3f, 0x12, 0x5d, 0xc5, 0x04, 0x97, 0xd9, 0x67, 0x7a, 0xd6, 0xfd, 0xde,
	0x81, 0x95, 0x77, 0xfa, 0x78, 0x4d, 0x44, 0x68, 0x13, 0x96, 0x5a, 0x22, 0x22, 0x2c, 0x2a, 0x39,
	0xbb, 0xce, 0xfe, 0x0a, 0x5e, 0x6c, 0x89, 0xa8, 0x1e, 0xa1, 0x6d, 0x80, 0xf0, 0x3a, 0xe0, 0x9c,
	0xb6, 0x13, 0x55, 0x6e, 0xd7, 0xd9, 0x5f, 0xc7, 0x2b, 0x46, 0x52, 0x8f, 0xd0, 0x16, 0x14, 0xa4,
	0x0a, 0x62, 0x45, 0x94, 0x2c, 0xe5, 0x77, 0x9d, 0xfd, 0x3c, 0x5e, 0xd6, 0xbf, 0x7d, 0x99, 0x18,
	0xa4, 0x3c, 0x4a, 0x14, 0x0b, 0x5a, 0xb1, 0x48, 0x79, 0xe4, 0x4b, 0xf4, 0x02, 0x1e, 0x74, 0x98,
	0x4c, 0xc2, 0x22, 0x46, 0xbd, 0xa8, 0xd5, 0x6b, 0x46, 0xea, 0x69, 0xd4, 0x2f, 0x60, 0x9b, 0x49,
	0x22, 0x55, 0x70, 0x47, 0x62, 0x21, 0x3a, 0x24, 0x3b, 0xf2, 0x9e, 0x71, 0x26, 0xaf, 0x69, 0x54,
	0x5a, 0xda, 0x75, 0xf6, 0x0b, 0x78, 0x8b, 0xc9, 0xa6, 0x0a, 0xee, 0xb0, 0x10, 0x9d, 0xb3, 0x14,
	0xf1, 0xda, 0x00, 0xdc, 0x0f, 0xb0, 0xd6, 0xbf, 0xdc, 0xb1, 0xb8, 0x4d, 0x22, 0xbd, 0x12, 0xb7,
	0x44, 0xdd, 0x75, 0xa9, 0xbe, 0xe1, 0x3a, 0x5e, 0xbe, 0x12, 0xb7, 0xfe, 0x5d, 0x97, 0xa2, 0xd7,
	0xb0, 0x1a, 0xd3, 0x8f, 0x41, 0x1c, 0x91, 0x36, 0x93, 0xaa, 0x94, 0xdb, 0xcd, 0xef, 0xaf, 0xbe,
	0xda, 0x3b, 0x1c, 0xa4, 0xf6, 0xd0, 0xb6, 0x84, 0x35, 0xf4, 0x22, 0xa6, 0x37, 0x8c, 0x7e, 0xc4,
	0x90, 0x9e, 0x3c, 0x65, 0x52, 0xb9, 0x7f, 0x75, 0x60, 0x6b, 0x2a, 0x12, 0x3d, 0x86, 0x65, 0x26,
	0x49, 0x1c, 0xc4, 0xa9, 0xff, 0x02, 0x5e, 0x62, 0x12, 0x07, 0x31, 0x45, 0x0f, 0x61, 0x51, 0x31,
	0xd5, 0xa6, 0x3a, 0xbb, 0x2b, 0x38, 0xfd, 0x81, 0x9e, 0xc0, 0x8a, 0xec, 0x5d, 0x91, 0x54, 0x93,
	0xd7, 0x9a, 0x82, 0xec, 0x5d, 0xf9, 0x5a, 0xb9, 0x05, 0x05, 0x16, 0x0a, 0x4e, 0x7a, 0x71, 0x5b,
	0x67, 0x77, 0x05, 0x2f, 0x27, 0xbf, 0x2f, 0xe3, 0x36, 0x7a, 0x0a, 0xab, 0xa1, 0x88, 0x39, 0x8d,
	0x89, 0xa2, 0xb7, 0x4a, 0x27, 0x77, 0x05, 0x43, 0x2a, 0xf2, 0xe9, 0xad, 0x72, 0x43, 0xd8, 0xaa,
	0x51, 0xd5, 0x8f, 0xf3, 0x84, 0xaa, 0x80, 0xb5, 0x31, 0xfd, 0xd0, 0xa3, 0x52, 0xa1, 0x22, 0xe4,
	0x7b, 0x86, 0x02, 0xeb, 0x38, 0xf9, 0x3a, 0x8f, 0x00, 0x03, 0xda, 0xe4, 0x2d, 0xda, 0xb8, 0xff,
	0x76, 0xa0, 0x3c, 0xc9, 0x8b, 0xec, 0x0a, 0x2e, 0x29, 0xfa, 0x31, 0x14, 0xf4, 0x29, 0xfe, 0x5e,
	0x68, 0x5f, 0xab, 0xaf, 0x36, 0x27, 0xa6, 0x1b, 0x2f, 0x27, 0xe6, 0xf8, 0x7b, 0x81, 0xbe, 0x4e,
	0xcb, 0xa7, 0x4f, 0xe4, 0xf4, 0x89, 0xd2, 0xd4, 0x02, 0x25, 0x85, 0xd5, 0x87, 0x5e, 0x02, 0x0a,
	0x6e, 0x02, 0xd6, 0x0e, 0xae, 0xda, 0x94, 0x88, 0x2e, 0xe5, 0x24, 0xe4, 0x4a, 0x07, 0xba, 0x8e,
	0x8b, 0x7d, 0xcd, 0x79, 0x97, 0xf2, 0x2a, 0x57, 0x68, 0x17, 0xd6, 0x5a, 0x54, 0x0d, 0x70, 0x0b,
	0x1a, 0x07, 0x2d, 0xaa, 0x0c, 0xc2, 0x6d, 0xc1, 0x76, 0xf2, 0x75, 0xbc, 0xc6, 0xff, 0xef, 0xf4,
	0x5d, 0xc3, 0xce, 0x34, 0x47, 0x26, 0x83, 0x23, 0x9c, 0x75, 0xfe, 0x57, 0xce, 0x7e, 0x03, 0xbb,
	0xcd, 0x3b, 0xf9, 0xa9, 0xb7, 0x72, 0x61, 0x3d, 0xab, 0x1f, 0xf9, 0x9d, 0x14, 0xdc, 0x50, 0x77,
	0xd5, 0x54, 0xeb, 0x97, 0x52, 0x70, 0xf7, 0x39, 0x3c, 0x9b, 0x61, 0x39, 0xbd, 0x86, 0xfb, 0x37,
	0x07, 0x1e, 0x8d, 0xab, 0xeb, 0x8a, 0x76, 0xd0, 0x03, 0xc8, 0x19, 0xa7, 0x79, 0x9c, 0x63, 0xd1,
	0x10, 0xe7, 0x73, 0xc3, 0x9c, 0xef, 0xbf, 0xa0, 0xfc, 0xd4, 0x17, 0xb4, 0x30, 0xf2, 0x82, 0xac,
	0xd7, 0xb8, 0x38, 0xf4, 0x1a, 0xb7, 0xa0, 0xa0, 0x58, 0x87, 0x12, 0xa9, 0x62, 0xdd, 0x64, 0x56,
	0xf0, 0x72, 0xf2, 0xbb, 0xa9, 0x62, 0x97, 0xc2, 0xae, 0xcd, 0xe9, 0x7e, 0xb8, 0x49, 0x22, 0xa7,
	0xe7, 0x0a, 0xc1, 0x42, 0x37, 0x68, 0x51, 0x53, 0x7b, 0xfd, 0x3d, 0x09, 0x2d, 0xf9, 0x24, 0x92,
	0x7d, 0x47, 0x0d, 0x1f, 0x0b, 0x89, 0xa0, 0xc9, 0xbe, 0xa3, 0xee, 0x1f, 0x1d, 0x78, 0x36, 0xc3,
	0x8f, 0x21, 0x40, 0x75, 0x12, 0x01, 0xdc, 0xd9, 0x04, 0x48, 0xf2, 0x6a, 0x57, 0x3f, 0xb9, 0xec,
	0x75, 0x20, 0x49, 0x47, 0xc4, 0x69, 0x7c, 0x05, 0xbc, 0x7c, 0x1d, 0xc8, 0x33, 0x11, 0x53, 0xf7,
	0x0c, 0x5c, 0x4c, 0xbb, 0x22, 0x56, 0x13, 0x1b, 0xec, 0xf4, 0xeb, 0x0e, 0x18, 0x9d, 0xb3, 0x19,
	0xbd, 0x07, 0xcf, 0x67, 0x9a, 0x33, 0x7c, 0x78, 0x0d, 0x4f, 0xfa, 0x57, 0x37, 0x88, 0x84, 0x50,
	0x9f, 0xec, 0xee, 0x1f, 0x39, 0xd8, 0x18, 0xb3, 0x82, 0x9e, 0x41, 0x36, 0x65, 0xec, 0x39, 0xb0,
	0x6a, 0x64, 0x7a, 0x16, 0x58, 0x90, 0x84, 0x5d, 0x19, 0xb1, 0x8d, 0xac, 0x1e, 0x0a, 0x6e, 0x43,
	0x78, 0xd0, 0xc9, 0x48, 0x97, 0x41, 0x1a, 0x41, 0x67, 0xc8, 0x4a, 0x44, 0x65, 0x68, 0xd8, 0x97,
	0x41, 0x4e, 0xa8, 0x0c, 0xd1, 0x3e, 0x14, 0x33, 0x48, 0xc8, 0x15, 0xe9, 0x31, 0x9e, 0x35, 0xeb,
	0x6c, 0x3e, 0x56, 0xb9, 0xba, 0x64, 0x5c, 0x25, 0x48, 0x4e, 0x69, 0x44, 0x2c, 0xb8, 0x66, 0xe6,
	0x3a, 0x7e, 0x90, 0xc8, 0xcf, 0xfa, 0x68, 0xdd, 0xfb, 0x7b, 0x71, 0x4c, 0xb9, 0xd2, 0xa0, 0xe5,
	0xb4, 0x81, 0x19, 0x51, 0x02, 0x78, 0x01, 0x0f, 0x12, 0xd6, 0xd3, 0x20, 0xbc, 0x26, 0x6d, 0xd6,
	0x61, 0xaa, 0x54, 0xd0, 0x55, 0x5f, 0x63, 0x12, 0x27, 0xc2, 0xd3, 0x44, 0xe6, 0xde, 0xc0, 0xe7,
	0x93, 0x8b, 0x60, 0xa8, 0xf7, 0x2b, 0x28, 0x19, 0x9a, 0xf5, 0x53, 0x95, 0x34, 0x02, 0x8b, 0x87,
	0xdb, 0xe3, 0x3c, 0xb4, 0x0d, 0x6d, 0x7e, 0x1c, 0x15, 0xe9, 0x5e, 0xf4, 0x14, 0xb6, 0x6b, 0x54,
	0x9d, 0x73, 0xda, 0x67, 0x6e, 0x35, 0x6d, 0x94, 0xa6, 0xfc, 0xee, 0xcf, 0x61, 0x67, 0x1a, 0xc0,
	0x84, 0x36, 0xdc, 0x6e, 0x9d, 0x91, 0x76, 0xeb, 0xbe, 0x85, 0x92, 0xfd, 0xb2, 0x3c, 0xae, 0xe2,
	0xbb, 0x8c, 0x5b, 0xb3, 0x8f, 0x66, 0xd4, 0xcb, 0xf5, 0xa9, 0xe7, 0xfe, 0xd9, 0x19, 0x9e, 0xa4,
	0xc6, 0x9a, 0x89, 0xa4, 0x01, 0xa5, 0x7e, 0x2d, 0x8c, 0xd9, 0x84, 0xa8, 0x56, 0x92, 0xa6, 0x8c,
	0xbc, 0xcd, 0xac, 0x5e, 0xe9, 0xa9, 0x9a, 0x48, 0x9f, 0xea, 0x1e, 0x7c, 0xd6, 0x0e, 0xa4, 0xd2,
	0x46, 0xcc, 0xe2, 0x94, 0x4b, 0x17, 0xa7, 0x44, 0xac, 0xdd, 0x47, 0xbe, 0x74, 0x77, 0xac, 0xda,
	0x25, 0xf9, 0x11, 0x9d, 0x8e, 0xe0, 0xd5, 0xf7, 0xad, 0x2c, 0x85, 0xdf, 0xe7, 0x74, 0x92, 0x27,
	0x01, 0x4c, 0xe0, 0xbf, 0x81, 0x35, 0x15, 0xb3, 0x56, 0x8b, 0xc6, 0x76, 0xb0, 0x3f, 0xb3, 0x83,
	0x9d, 0x69, 0x60, 0x70, 0x15, 0x3f, 0x35, 0x83, 0x57, 0x8d, 0x3d, 0x7d, 0x8f, 0x9f, 0xc0, 0xa6,
	0x66, 0xf3, 0x60, 0xb7, 0x93, 0x34, 0x14, 0x3c, 0xcb, 0x2c, 0x4a, 0x94, 0x59, 0x8f, 0x68, 0x6a,
	0x0d, 0x3a, 0x80, 0x8d, 0x20, 0x54, 0xec, 0x86, 0xa9, 0x3b, 0x32, 0xb2, 0x6d, 0x7e, 0x96, 0x29,
	0x9a, 0x66, 0xeb, 0xfc, 0x02, 0xfa, 0x22, 0x32, 0xb4, 0x7e, 0xae, 0x67, 0x62, 0x9d, 0xa7, 0xb2,
	0x0f, 0xc5, 0xd1, 0x38, 0x93, 0x57, 0x9b, 0xdd, 0xdc, 0x6e, 0x0f, 0x46, 0xa6, 0xdb, 0xc3, 0x36,
	0x40, 0x06, 0x19, 0x8c, 0x73, 0x23, 0xa9, 0x47, 0x07, 0x7f, 0x72, 0x2c, 0xb3, 0xc7, 0x66, 0xbd,
	0x7c, 0x0e, 0x4f, 0xdf, 0x79, 0x95, 0x53, 0xff, 0x0d, 0xa9, 0x9d, 0x9f, 0x90, 0xe3, 0xf3, 0x6f,
	0x88, 0xff, 0xed, 0x85, 0x47, 0x2e, 0x1b, 0xcd, 0x0b, 0xaf, 0x5a, 0x7f, 0x5d, 0xf7, 0x4e, 0x8a,
	0x3f, 0x40, 0x5b, 0xb0, 0x39, 0x09, 0xd4, 0x2c, 0x3a, 0x68, 0x07, 0xca, 0x93, 0x54, 0x15, 0x72,
	0x71, 0x7a, 0xd9, 0x2c, 0xe6, 0xa6, 0x1d, 0xad, 0x14, 0xf3, 0x07, 0x21, 0xac, 0xfa, 0x56, 0xf4,
	0x9f, 0x43, 0xc9, 0xc7, 0xf5, 0x5a, 0xcd, 0xc3, 0x93, 0x42, 0x28, 0xc1, 0xc3, 0x21, 0xed, 0x05,
	0xf6, 0x9a, 0x5e, 0xc3, 0x2f, 0x3a, 0x63, 0xe7, 0x8c, 0x86, 0x34, 0x3d, 0xbf, 0x98, 0x3b, 0xf8,
	0x97, 0x03, 0x90, 0xce, 0x17, 0xed, 0xe4, 0x09, 0x3c, 0xc6, 0xde, 0xbb, 0x0a, 0x3e, 0x99, 0xe4,
	0xe3, 0x31, 0xfc, 0xd0, 0x56, 0x5e, 0x54, 0xaa, 0x6f, 0x2b, 0x35, 0xaf, 0xe8, 0xa0, 0x4d, 0xd8,
	0xb0, 0x15, 0x6f, 0xce, 0x71, 0xd3, 0x4b, 0xef, 0x66, 0x8b, 0xcf, 0xea, 0x55, 0xd2, 0xf4, 0xbf,
	0x3d, 0xf5, 0x8a, 0x79, 0x54, 0x86, 0x47, 0xb6, 0xea, 0xc4, 0xab, 0x9e, 0xe3, 0x8a, 0x5f, 0x3f,
	0x6f, 0x14, 0x17, 0x46, 0x8f, 0x35, 0x2a, 0x67, 0xde, 0xc5, 0x69, 0xc5, 0xf7, 0x8a, 0x8b, 0xc8,
	0x85, 0x1d, 0x5b, 0x55, 0x7d, 0x53, 0x69, 0x34, 0xbc, 0x53, 0x72, 0x5c, 0xa9, 0xbe, 0xad, 0xe1,
	0xf3, 0xcb, 0xc6, 0x49, 0x71, 0xe9, 0xd5, 0x5f, 0x0a, 0x56, 0x19, 0x9b, 0x34, 0xbe, 0x61, 0x21,
	0x45, 0x21, 0xa0, 0xf1, 0x8d, 0x16, 0xed, 0x4d, 0x7b, 0x17, 0x43, 0x7b, 0x75, 0xf9, 0x8b, 0x79,
	0x30, 0xf3, 0xf8, 0x04, 0x3c, 0x9a, 0xbc, 0x31, 0xa1, 0x2f, 0x6d, 0x0b, 0x33, 0xf7, 0xb5, 0xf2,
	0xc1, 0x7d, 0xa0, 0xc6, 0xe1, 0x2d, 0x6c, 0x4d, 0xdd, 0xd2, 0xd0, 0x4b, 0xdb, 0xd0, 0xbc, 0x35,
	0xb1, 0xfc, 0xd5, 0x3d, 0xd1, 0x03, 0xcf, 0x53, 0xb7, 0x9c, 0x61, 0xcf, 0xf3, 0x96, 0xae, 0x61,
	0xcf, 0xf3, 0x57, 0xa7, 0x3f, 0xc0, 0x93, 0x19, 0xbb, 0x08, 0x3a, 0xb4, 0xad, 0xcd, 0xdf, 0x81,
	0xca, 0x47, 0xf7, 0xc6, 0x1b, 0xff, 0x0c, 0x1e, 0x4e, 0x9a, 0xaf, 0xe8, 0x47, 0x13, 0xaf, 0x31,
	0xbe, 0x06, 0x95, 0xf7, 0xe7, 0x03, 0x07, 0x7c, 0x9a, 0x3c, 0x31, 0x87, 0xf9, 0x34, 0x73, 0xec,
	0x0e, 0xf3, 0x69, 0xce, 0x00, 0xfe, 0x2d, 0x6c, 0x8c, 0xcd, 0x44, 0xf4, 0x62, 0x5a, 0x7d, 0xec,
	0x01, 0x5c, 0xde, 0x9b, 0x83, 0x32, 0x1e, 0xda, 0xb0, 0x39, 0x71, 0xfe, 0xa0, 0xfd, 0x7b, 0x8c,
	0xa8, 0xd4, 0xd3, 0x97, 0xf7, 0x1e, 0x66, 0xe5, 0x8d, 0xff, 0xfc, 0xfd, 0x9f, 0xfe, 0x1a, 0xc0,
	0xe0, 0x9f, 0x97, 0xe3, 0xc3, 0x5f, 0xbf, 0x6c, 0x89, 0x76, 0xc0, 0x5b, 0x87, 0x3f, 0x7d, 0xa5,
	0xd4, 0x61, 0x28, 0x3a, 0x47, 0xfa, 0x0f, 0x95, 0x50, 0xb4, 0x8f, 0x64, 0xda, 0x2c, 0xa4, 0xf5,
	0x4f, 0xcd, 0xd5, 0x92, 0xd6, 0x7e, 0xfd, 0xdf, 0x00, 0x00, 0x00, 0xff, 0xff, 0x7d, 0x77, 0xe1,
	0x9c, 0xd8, 0x11, 0x00, 0x00,
}
