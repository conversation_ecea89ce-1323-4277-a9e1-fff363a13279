// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/wealth-god/wealth-god.proto

package wealth_god

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockWealthGodServiceClient is a mock of WealthGodServiceClient interface.
type MockWealthGodServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockWealthGodServiceClientMockRecorder
}

// MockWealthGodServiceClientMockRecorder is the mock recorder for MockWealthGodServiceClient.
type MockWealthGodServiceClientMockRecorder struct {
	mock *MockWealthGodServiceClient
}

// NewMockWealthGodServiceClient creates a new mock instance.
func NewMockWealthGodServiceClient(ctrl *gomock.Controller) *MockWealthGodServiceClient {
	mock := &MockWealthGodServiceClient{ctrl: ctrl}
	mock.recorder = &MockWealthGodServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWealthGodServiceClient) EXPECT() *MockWealthGodServiceClientMockRecorder {
	return m.recorder
}

// GetOneWealthGodChannel mocks base method.
func (m *MockWealthGodServiceClient) GetOneWealthGodChannel(ctx context.Context, in *GetOneWealthGodChannelRequest, opts ...grpc.CallOption) (*GetOneWealthGodChannelResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetOneWealthGodChannel", varargs...)
	ret0, _ := ret[0].(*GetOneWealthGodChannelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOneWealthGodChannel indicates an expected call of GetOneWealthGodChannel.
func (mr *MockWealthGodServiceClientMockRecorder) GetOneWealthGodChannel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOneWealthGodChannel", reflect.TypeOf((*MockWealthGodServiceClient)(nil).GetOneWealthGodChannel), varargs...)
}

// GetWealthGodBoxRewardList mocks base method.
func (m *MockWealthGodServiceClient) GetWealthGodBoxRewardList(ctx context.Context, in *GetWealthGodBoxRewardListRequest, opts ...grpc.CallOption) (*GetWealthGodBoxRewardListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetWealthGodBoxRewardList", varargs...)
	ret0, _ := ret[0].(*GetWealthGodBoxRewardListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWealthGodBoxRewardList indicates an expected call of GetWealthGodBoxRewardList.
func (mr *MockWealthGodServiceClientMockRecorder) GetWealthGodBoxRewardList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWealthGodBoxRewardList", reflect.TypeOf((*MockWealthGodServiceClient)(nil).GetWealthGodBoxRewardList), varargs...)
}

// GetWealthGodCommonCfg mocks base method.
func (m *MockWealthGodServiceClient) GetWealthGodCommonCfg(ctx context.Context, in *GetWealthGodCommonCfgRequest, opts ...grpc.CallOption) (*GetWealthGodCommonCfgResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetWealthGodCommonCfg", varargs...)
	ret0, _ := ret[0].(*GetWealthGodCommonCfgResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWealthGodCommonCfg indicates an expected call of GetWealthGodCommonCfg.
func (mr *MockWealthGodServiceClientMockRecorder) GetWealthGodCommonCfg(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWealthGodCommonCfg", reflect.TypeOf((*MockWealthGodServiceClient)(nil).GetWealthGodCommonCfg), varargs...)
}

// GetWealthGodDetail mocks base method.
func (m *MockWealthGodServiceClient) GetWealthGodDetail(ctx context.Context, in *GetWealthGodDetailRequest, opts ...grpc.CallOption) (*GetWealthGodDetailResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetWealthGodDetail", varargs...)
	ret0, _ := ret[0].(*GetWealthGodDetailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWealthGodDetail indicates an expected call of GetWealthGodDetail.
func (mr *MockWealthGodServiceClientMockRecorder) GetWealthGodDetail(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWealthGodDetail", reflect.TypeOf((*MockWealthGodServiceClient)(nil).GetWealthGodDetail), varargs...)
}

// GetWealthGodEntry mocks base method.
func (m *MockWealthGodServiceClient) GetWealthGodEntry(ctx context.Context, in *GetWealthGodEntryRequest, opts ...grpc.CallOption) (*GetWealthGodEntryResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetWealthGodEntry", varargs...)
	ret0, _ := ret[0].(*GetWealthGodEntryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWealthGodEntry indicates an expected call of GetWealthGodEntry.
func (mr *MockWealthGodServiceClientMockRecorder) GetWealthGodEntry(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWealthGodEntry", reflect.TypeOf((*MockWealthGodServiceClient)(nil).GetWealthGodEntry), varargs...)
}

// GetWealthMissionInfo mocks base method.
func (m *MockWealthGodServiceClient) GetWealthMissionInfo(ctx context.Context, in *GetWealthMissionInfoRequest, opts ...grpc.CallOption) (*GetWealthMissionInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetWealthMissionInfo", varargs...)
	ret0, _ := ret[0].(*GetWealthMissionInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWealthMissionInfo indicates an expected call of GetWealthMissionInfo.
func (mr *MockWealthGodServiceClientMockRecorder) GetWealthMissionInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWealthMissionInfo", reflect.TypeOf((*MockWealthGodServiceClient)(nil).GetWealthMissionInfo), varargs...)
}

// OpenWealthGodBoxReward mocks base method.
func (m *MockWealthGodServiceClient) OpenWealthGodBoxReward(ctx context.Context, in *OpenWealthGodBoxRewardRequest, opts ...grpc.CallOption) (*OpenWealthGodBoxRewardResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "OpenWealthGodBoxReward", varargs...)
	ret0, _ := ret[0].(*OpenWealthGodBoxRewardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OpenWealthGodBoxReward indicates an expected call of OpenWealthGodBoxReward.
func (mr *MockWealthGodServiceClientMockRecorder) OpenWealthGodBoxReward(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OpenWealthGodBoxReward", reflect.TypeOf((*MockWealthGodServiceClient)(nil).OpenWealthGodBoxReward), varargs...)
}

// ReportStayRoomMissionFinish mocks base method.
func (m *MockWealthGodServiceClient) ReportStayRoomMissionFinish(ctx context.Context, in *ReportStayRoomMissionFinishRequest, opts ...grpc.CallOption) (*ReportStayRoomMissionFinishResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReportStayRoomMissionFinish", varargs...)
	ret0, _ := ret[0].(*ReportStayRoomMissionFinishResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReportStayRoomMissionFinish indicates an expected call of ReportStayRoomMissionFinish.
func (mr *MockWealthGodServiceClientMockRecorder) ReportStayRoomMissionFinish(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportStayRoomMissionFinish", reflect.TypeOf((*MockWealthGodServiceClient)(nil).ReportStayRoomMissionFinish), varargs...)
}

// SysOpenWealthGodBoxReward mocks base method.
func (m *MockWealthGodServiceClient) SysOpenWealthGodBoxReward(ctx context.Context, in *SysOpenWealthGodBoxRewardRequest, opts ...grpc.CallOption) (*SysOpenWealthGodBoxRewardResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SysOpenWealthGodBoxReward", varargs...)
	ret0, _ := ret[0].(*SysOpenWealthGodBoxRewardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SysOpenWealthGodBoxReward indicates an expected call of SysOpenWealthGodBoxReward.
func (mr *MockWealthGodServiceClientMockRecorder) SysOpenWealthGodBoxReward(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SysOpenWealthGodBoxReward", reflect.TypeOf((*MockWealthGodServiceClient)(nil).SysOpenWealthGodBoxReward), varargs...)
}

// MockWealthGodServiceServer is a mock of WealthGodServiceServer interface.
type MockWealthGodServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockWealthGodServiceServerMockRecorder
}

// MockWealthGodServiceServerMockRecorder is the mock recorder for MockWealthGodServiceServer.
type MockWealthGodServiceServerMockRecorder struct {
	mock *MockWealthGodServiceServer
}

// NewMockWealthGodServiceServer creates a new mock instance.
func NewMockWealthGodServiceServer(ctrl *gomock.Controller) *MockWealthGodServiceServer {
	mock := &MockWealthGodServiceServer{ctrl: ctrl}
	mock.recorder = &MockWealthGodServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWealthGodServiceServer) EXPECT() *MockWealthGodServiceServerMockRecorder {
	return m.recorder
}

// GetOneWealthGodChannel mocks base method.
func (m *MockWealthGodServiceServer) GetOneWealthGodChannel(ctx context.Context, in *GetOneWealthGodChannelRequest) (*GetOneWealthGodChannelResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOneWealthGodChannel", ctx, in)
	ret0, _ := ret[0].(*GetOneWealthGodChannelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOneWealthGodChannel indicates an expected call of GetOneWealthGodChannel.
func (mr *MockWealthGodServiceServerMockRecorder) GetOneWealthGodChannel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOneWealthGodChannel", reflect.TypeOf((*MockWealthGodServiceServer)(nil).GetOneWealthGodChannel), ctx, in)
}

// GetWealthGodBoxRewardList mocks base method.
func (m *MockWealthGodServiceServer) GetWealthGodBoxRewardList(ctx context.Context, in *GetWealthGodBoxRewardListRequest) (*GetWealthGodBoxRewardListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWealthGodBoxRewardList", ctx, in)
	ret0, _ := ret[0].(*GetWealthGodBoxRewardListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWealthGodBoxRewardList indicates an expected call of GetWealthGodBoxRewardList.
func (mr *MockWealthGodServiceServerMockRecorder) GetWealthGodBoxRewardList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWealthGodBoxRewardList", reflect.TypeOf((*MockWealthGodServiceServer)(nil).GetWealthGodBoxRewardList), ctx, in)
}

// GetWealthGodCommonCfg mocks base method.
func (m *MockWealthGodServiceServer) GetWealthGodCommonCfg(ctx context.Context, in *GetWealthGodCommonCfgRequest) (*GetWealthGodCommonCfgResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWealthGodCommonCfg", ctx, in)
	ret0, _ := ret[0].(*GetWealthGodCommonCfgResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWealthGodCommonCfg indicates an expected call of GetWealthGodCommonCfg.
func (mr *MockWealthGodServiceServerMockRecorder) GetWealthGodCommonCfg(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWealthGodCommonCfg", reflect.TypeOf((*MockWealthGodServiceServer)(nil).GetWealthGodCommonCfg), ctx, in)
}

// GetWealthGodDetail mocks base method.
func (m *MockWealthGodServiceServer) GetWealthGodDetail(ctx context.Context, in *GetWealthGodDetailRequest) (*GetWealthGodDetailResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWealthGodDetail", ctx, in)
	ret0, _ := ret[0].(*GetWealthGodDetailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWealthGodDetail indicates an expected call of GetWealthGodDetail.
func (mr *MockWealthGodServiceServerMockRecorder) GetWealthGodDetail(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWealthGodDetail", reflect.TypeOf((*MockWealthGodServiceServer)(nil).GetWealthGodDetail), ctx, in)
}

// GetWealthGodEntry mocks base method.
func (m *MockWealthGodServiceServer) GetWealthGodEntry(ctx context.Context, in *GetWealthGodEntryRequest) (*GetWealthGodEntryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWealthGodEntry", ctx, in)
	ret0, _ := ret[0].(*GetWealthGodEntryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWealthGodEntry indicates an expected call of GetWealthGodEntry.
func (mr *MockWealthGodServiceServerMockRecorder) GetWealthGodEntry(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWealthGodEntry", reflect.TypeOf((*MockWealthGodServiceServer)(nil).GetWealthGodEntry), ctx, in)
}

// GetWealthMissionInfo mocks base method.
func (m *MockWealthGodServiceServer) GetWealthMissionInfo(ctx context.Context, in *GetWealthMissionInfoRequest) (*GetWealthMissionInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWealthMissionInfo", ctx, in)
	ret0, _ := ret[0].(*GetWealthMissionInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWealthMissionInfo indicates an expected call of GetWealthMissionInfo.
func (mr *MockWealthGodServiceServerMockRecorder) GetWealthMissionInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWealthMissionInfo", reflect.TypeOf((*MockWealthGodServiceServer)(nil).GetWealthMissionInfo), ctx, in)
}

// OpenWealthGodBoxReward mocks base method.
func (m *MockWealthGodServiceServer) OpenWealthGodBoxReward(ctx context.Context, in *OpenWealthGodBoxRewardRequest) (*OpenWealthGodBoxRewardResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OpenWealthGodBoxReward", ctx, in)
	ret0, _ := ret[0].(*OpenWealthGodBoxRewardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OpenWealthGodBoxReward indicates an expected call of OpenWealthGodBoxReward.
func (mr *MockWealthGodServiceServerMockRecorder) OpenWealthGodBoxReward(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OpenWealthGodBoxReward", reflect.TypeOf((*MockWealthGodServiceServer)(nil).OpenWealthGodBoxReward), ctx, in)
}

// ReportStayRoomMissionFinish mocks base method.
func (m *MockWealthGodServiceServer) ReportStayRoomMissionFinish(ctx context.Context, in *ReportStayRoomMissionFinishRequest) (*ReportStayRoomMissionFinishResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportStayRoomMissionFinish", ctx, in)
	ret0, _ := ret[0].(*ReportStayRoomMissionFinishResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReportStayRoomMissionFinish indicates an expected call of ReportStayRoomMissionFinish.
func (mr *MockWealthGodServiceServerMockRecorder) ReportStayRoomMissionFinish(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportStayRoomMissionFinish", reflect.TypeOf((*MockWealthGodServiceServer)(nil).ReportStayRoomMissionFinish), ctx, in)
}

// SysOpenWealthGodBoxReward mocks base method.
func (m *MockWealthGodServiceServer) SysOpenWealthGodBoxReward(ctx context.Context, in *SysOpenWealthGodBoxRewardRequest) (*SysOpenWealthGodBoxRewardResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SysOpenWealthGodBoxReward", ctx, in)
	ret0, _ := ret[0].(*SysOpenWealthGodBoxRewardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SysOpenWealthGodBoxReward indicates an expected call of SysOpenWealthGodBoxReward.
func (mr *MockWealthGodServiceServerMockRecorder) SysOpenWealthGodBoxReward(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SysOpenWealthGodBoxReward", reflect.TypeOf((*MockWealthGodServiceServer)(nil).SysOpenWealthGodBoxReward), ctx, in)
}
