// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/public-notice/public-notice.proto

package public_notice

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockPublicNoticeClient is a mock of PublicNoticeClient interface.
type MockPublicNoticeClient struct {
	ctrl     *gomock.Controller
	recorder *MockPublicNoticeClientMockRecorder
}

// MockPublicNoticeClientMockRecorder is the mock recorder for MockPublicNoticeClient.
type MockPublicNoticeClientMockRecorder struct {
	mock *MockPublicNoticeClient
}

// NewMockPublicNoticeClient creates a new mock instance.
func NewMockPublicNoticeClient(ctrl *gomock.Controller) *MockPublicNoticeClient {
	mock := &MockPublicNoticeClient{ctrl: ctrl}
	mock.recorder = &MockPublicNoticeClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPublicNoticeClient) EXPECT() *MockPublicNoticeClientMockRecorder {
	return m.recorder
}

// AddBreakingNewsConfig mocks base method.
func (m *MockPublicNoticeClient) AddBreakingNewsConfig(ctx context.Context, in *AddBreakingNewsConfigReq, opts ...grpc.CallOption) (*AddBreakingNewsConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddBreakingNewsConfig", varargs...)
	ret0, _ := ret[0].(*AddBreakingNewsConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddBreakingNewsConfig indicates an expected call of AddBreakingNewsConfig.
func (mr *MockPublicNoticeClientMockRecorder) AddBreakingNewsConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddBreakingNewsConfig", reflect.TypeOf((*MockPublicNoticeClient)(nil).AddBreakingNewsConfig), varargs...)
}

// AddStickBreakingNews mocks base method.
func (m *MockPublicNoticeClient) AddStickBreakingNews(ctx context.Context, in *AddStickBreakingNewsReq, opts ...grpc.CallOption) (*AddStickBreakingNewsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddStickBreakingNews", varargs...)
	ret0, _ := ret[0].(*AddStickBreakingNewsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddStickBreakingNews indicates an expected call of AddStickBreakingNews.
func (mr *MockPublicNoticeClientMockRecorder) AddStickBreakingNews(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddStickBreakingNews", reflect.TypeOf((*MockPublicNoticeClient)(nil).AddStickBreakingNews), varargs...)
}

// BatchDelBreakingNewsConfig mocks base method.
func (m *MockPublicNoticeClient) BatchDelBreakingNewsConfig(ctx context.Context, in *BatchDelBreakingNewsConfigReq, opts ...grpc.CallOption) (*BatchDelBreakingNewsConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchDelBreakingNewsConfig", varargs...)
	ret0, _ := ret[0].(*BatchDelBreakingNewsConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchDelBreakingNewsConfig indicates an expected call of BatchDelBreakingNewsConfig.
func (mr *MockPublicNoticeClientMockRecorder) BatchDelBreakingNewsConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDelBreakingNewsConfig", reflect.TypeOf((*MockPublicNoticeClient)(nil).BatchDelBreakingNewsConfig), varargs...)
}

// BatchGetBreakingNewsConfig mocks base method.
func (m *MockPublicNoticeClient) BatchGetBreakingNewsConfig(ctx context.Context, in *BatchGetBreakingNewsConfigReq, opts ...grpc.CallOption) (*BatchGetBreakingNewsConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetBreakingNewsConfig", varargs...)
	ret0, _ := ret[0].(*BatchGetBreakingNewsConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetBreakingNewsConfig indicates an expected call of BatchGetBreakingNewsConfig.
func (mr *MockPublicNoticeClientMockRecorder) BatchGetBreakingNewsConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetBreakingNewsConfig", reflect.TypeOf((*MockPublicNoticeClient)(nil).BatchGetBreakingNewsConfig), varargs...)
}

// CheckIfCouldOperateStickBreakingNews mocks base method.
func (m *MockPublicNoticeClient) CheckIfCouldOperateStickBreakingNews(ctx context.Context, in *CheckIfCouldOperateStickBreakingNewsReq, opts ...grpc.CallOption) (*CheckIfCouldOperateStickBreakingNewsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckIfCouldOperateStickBreakingNews", varargs...)
	ret0, _ := ret[0].(*CheckIfCouldOperateStickBreakingNewsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIfCouldOperateStickBreakingNews indicates an expected call of CheckIfCouldOperateStickBreakingNews.
func (mr *MockPublicNoticeClientMockRecorder) CheckIfCouldOperateStickBreakingNews(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIfCouldOperateStickBreakingNews", reflect.TypeOf((*MockPublicNoticeClient)(nil).CheckIfCouldOperateStickBreakingNews), varargs...)
}

// CommonPublicMsgPush mocks base method.
func (m *MockPublicNoticeClient) CommonPublicMsgPush(ctx context.Context, in *CommonPublicMsgPushReq, opts ...grpc.CallOption) (*CommonPublicMsgPushResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CommonPublicMsgPush", varargs...)
	ret0, _ := ret[0].(*CommonPublicMsgPushResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CommonPublicMsgPush indicates an expected call of CommonPublicMsgPush.
func (mr *MockPublicNoticeClientMockRecorder) CommonPublicMsgPush(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CommonPublicMsgPush", reflect.TypeOf((*MockPublicNoticeClient)(nil).CommonPublicMsgPush), varargs...)
}

// DelStickBreakingNews mocks base method.
func (m *MockPublicNoticeClient) DelStickBreakingNews(ctx context.Context, in *DelStickBreakingNewsReq, opts ...grpc.CallOption) (*DelStickBreakingNewsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelStickBreakingNews", varargs...)
	ret0, _ := ret[0].(*DelStickBreakingNewsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelStickBreakingNews indicates an expected call of DelStickBreakingNews.
func (mr *MockPublicNoticeClientMockRecorder) DelStickBreakingNews(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelStickBreakingNews", reflect.TypeOf((*MockPublicNoticeClient)(nil).DelStickBreakingNews), varargs...)
}

// GetAllStickBreakingNews mocks base method.
func (m *MockPublicNoticeClient) GetAllStickBreakingNews(ctx context.Context, in *GetAllStickBreakingNewsReq, opts ...grpc.CallOption) (*GetAllStickBreakingNewsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllStickBreakingNews", varargs...)
	ret0, _ := ret[0].(*GetAllStickBreakingNewsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllStickBreakingNews indicates an expected call of GetAllStickBreakingNews.
func (mr *MockPublicNoticeClientMockRecorder) GetAllStickBreakingNews(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllStickBreakingNews", reflect.TypeOf((*MockPublicNoticeClient)(nil).GetAllStickBreakingNews), varargs...)
}

// PushBreakingNews mocks base method.
func (m *MockPublicNoticeClient) PushBreakingNews(ctx context.Context, in *PushBreakingNewsReq, opts ...grpc.CallOption) (*PushBreakingNewsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PushBreakingNews", varargs...)
	ret0, _ := ret[0].(*PushBreakingNewsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PushBreakingNews indicates an expected call of PushBreakingNews.
func (mr *MockPublicNoticeClientMockRecorder) PushBreakingNews(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushBreakingNews", reflect.TypeOf((*MockPublicNoticeClient)(nil).PushBreakingNews), varargs...)
}

// TestPushRichTextBreakingNews mocks base method.
func (m *MockPublicNoticeClient) TestPushRichTextBreakingNews(ctx context.Context, in *TestPushRichTextBreakingNewsReq, opts ...grpc.CallOption) (*TestPushRichTextBreakingNewsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TestPushRichTextBreakingNews", varargs...)
	ret0, _ := ret[0].(*TestPushRichTextBreakingNewsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestPushRichTextBreakingNews indicates an expected call of TestPushRichTextBreakingNews.
func (mr *MockPublicNoticeClientMockRecorder) TestPushRichTextBreakingNews(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestPushRichTextBreakingNews", reflect.TypeOf((*MockPublicNoticeClient)(nil).TestPushRichTextBreakingNews), varargs...)
}

// UpdateBreakingNewsConfig mocks base method.
func (m *MockPublicNoticeClient) UpdateBreakingNewsConfig(ctx context.Context, in *UpdateBreakingNewsConfigReq, opts ...grpc.CallOption) (*UpdateBreakingNewsConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateBreakingNewsConfig", varargs...)
	ret0, _ := ret[0].(*UpdateBreakingNewsConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateBreakingNewsConfig indicates an expected call of UpdateBreakingNewsConfig.
func (mr *MockPublicNoticeClientMockRecorder) UpdateBreakingNewsConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBreakingNewsConfig", reflect.TypeOf((*MockPublicNoticeClient)(nil).UpdateBreakingNewsConfig), varargs...)
}

// UpdateStickBreakingNews mocks base method.
func (m *MockPublicNoticeClient) UpdateStickBreakingNews(ctx context.Context, in *UpdateStickBreakingNewsReq, opts ...grpc.CallOption) (*UpdateStickBreakingNewsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateStickBreakingNews", varargs...)
	ret0, _ := ret[0].(*UpdateStickBreakingNewsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateStickBreakingNews indicates an expected call of UpdateStickBreakingNews.
func (mr *MockPublicNoticeClientMockRecorder) UpdateStickBreakingNews(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStickBreakingNews", reflect.TypeOf((*MockPublicNoticeClient)(nil).UpdateStickBreakingNews), varargs...)
}

// MockPublicNoticeServer is a mock of PublicNoticeServer interface.
type MockPublicNoticeServer struct {
	ctrl     *gomock.Controller
	recorder *MockPublicNoticeServerMockRecorder
}

// MockPublicNoticeServerMockRecorder is the mock recorder for MockPublicNoticeServer.
type MockPublicNoticeServerMockRecorder struct {
	mock *MockPublicNoticeServer
}

// NewMockPublicNoticeServer creates a new mock instance.
func NewMockPublicNoticeServer(ctrl *gomock.Controller) *MockPublicNoticeServer {
	mock := &MockPublicNoticeServer{ctrl: ctrl}
	mock.recorder = &MockPublicNoticeServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPublicNoticeServer) EXPECT() *MockPublicNoticeServerMockRecorder {
	return m.recorder
}

// AddBreakingNewsConfig mocks base method.
func (m *MockPublicNoticeServer) AddBreakingNewsConfig(ctx context.Context, in *AddBreakingNewsConfigReq) (*AddBreakingNewsConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddBreakingNewsConfig", ctx, in)
	ret0, _ := ret[0].(*AddBreakingNewsConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddBreakingNewsConfig indicates an expected call of AddBreakingNewsConfig.
func (mr *MockPublicNoticeServerMockRecorder) AddBreakingNewsConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddBreakingNewsConfig", reflect.TypeOf((*MockPublicNoticeServer)(nil).AddBreakingNewsConfig), ctx, in)
}

// AddStickBreakingNews mocks base method.
func (m *MockPublicNoticeServer) AddStickBreakingNews(ctx context.Context, in *AddStickBreakingNewsReq) (*AddStickBreakingNewsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddStickBreakingNews", ctx, in)
	ret0, _ := ret[0].(*AddStickBreakingNewsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddStickBreakingNews indicates an expected call of AddStickBreakingNews.
func (mr *MockPublicNoticeServerMockRecorder) AddStickBreakingNews(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddStickBreakingNews", reflect.TypeOf((*MockPublicNoticeServer)(nil).AddStickBreakingNews), ctx, in)
}

// BatchDelBreakingNewsConfig mocks base method.
func (m *MockPublicNoticeServer) BatchDelBreakingNewsConfig(ctx context.Context, in *BatchDelBreakingNewsConfigReq) (*BatchDelBreakingNewsConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDelBreakingNewsConfig", ctx, in)
	ret0, _ := ret[0].(*BatchDelBreakingNewsConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchDelBreakingNewsConfig indicates an expected call of BatchDelBreakingNewsConfig.
func (mr *MockPublicNoticeServerMockRecorder) BatchDelBreakingNewsConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDelBreakingNewsConfig", reflect.TypeOf((*MockPublicNoticeServer)(nil).BatchDelBreakingNewsConfig), ctx, in)
}

// BatchGetBreakingNewsConfig mocks base method.
func (m *MockPublicNoticeServer) BatchGetBreakingNewsConfig(ctx context.Context, in *BatchGetBreakingNewsConfigReq) (*BatchGetBreakingNewsConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetBreakingNewsConfig", ctx, in)
	ret0, _ := ret[0].(*BatchGetBreakingNewsConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetBreakingNewsConfig indicates an expected call of BatchGetBreakingNewsConfig.
func (mr *MockPublicNoticeServerMockRecorder) BatchGetBreakingNewsConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetBreakingNewsConfig", reflect.TypeOf((*MockPublicNoticeServer)(nil).BatchGetBreakingNewsConfig), ctx, in)
}

// CheckIfCouldOperateStickBreakingNews mocks base method.
func (m *MockPublicNoticeServer) CheckIfCouldOperateStickBreakingNews(ctx context.Context, in *CheckIfCouldOperateStickBreakingNewsReq) (*CheckIfCouldOperateStickBreakingNewsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIfCouldOperateStickBreakingNews", ctx, in)
	ret0, _ := ret[0].(*CheckIfCouldOperateStickBreakingNewsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIfCouldOperateStickBreakingNews indicates an expected call of CheckIfCouldOperateStickBreakingNews.
func (mr *MockPublicNoticeServerMockRecorder) CheckIfCouldOperateStickBreakingNews(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIfCouldOperateStickBreakingNews", reflect.TypeOf((*MockPublicNoticeServer)(nil).CheckIfCouldOperateStickBreakingNews), ctx, in)
}

// CommonPublicMsgPush mocks base method.
func (m *MockPublicNoticeServer) CommonPublicMsgPush(ctx context.Context, in *CommonPublicMsgPushReq) (*CommonPublicMsgPushResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CommonPublicMsgPush", ctx, in)
	ret0, _ := ret[0].(*CommonPublicMsgPushResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CommonPublicMsgPush indicates an expected call of CommonPublicMsgPush.
func (mr *MockPublicNoticeServerMockRecorder) CommonPublicMsgPush(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CommonPublicMsgPush", reflect.TypeOf((*MockPublicNoticeServer)(nil).CommonPublicMsgPush), ctx, in)
}

// DelStickBreakingNews mocks base method.
func (m *MockPublicNoticeServer) DelStickBreakingNews(ctx context.Context, in *DelStickBreakingNewsReq) (*DelStickBreakingNewsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelStickBreakingNews", ctx, in)
	ret0, _ := ret[0].(*DelStickBreakingNewsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelStickBreakingNews indicates an expected call of DelStickBreakingNews.
func (mr *MockPublicNoticeServerMockRecorder) DelStickBreakingNews(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelStickBreakingNews", reflect.TypeOf((*MockPublicNoticeServer)(nil).DelStickBreakingNews), ctx, in)
}

// GetAllStickBreakingNews mocks base method.
func (m *MockPublicNoticeServer) GetAllStickBreakingNews(ctx context.Context, in *GetAllStickBreakingNewsReq) (*GetAllStickBreakingNewsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllStickBreakingNews", ctx, in)
	ret0, _ := ret[0].(*GetAllStickBreakingNewsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllStickBreakingNews indicates an expected call of GetAllStickBreakingNews.
func (mr *MockPublicNoticeServerMockRecorder) GetAllStickBreakingNews(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllStickBreakingNews", reflect.TypeOf((*MockPublicNoticeServer)(nil).GetAllStickBreakingNews), ctx, in)
}

// PushBreakingNews mocks base method.
func (m *MockPublicNoticeServer) PushBreakingNews(ctx context.Context, in *PushBreakingNewsReq) (*PushBreakingNewsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushBreakingNews", ctx, in)
	ret0, _ := ret[0].(*PushBreakingNewsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PushBreakingNews indicates an expected call of PushBreakingNews.
func (mr *MockPublicNoticeServerMockRecorder) PushBreakingNews(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushBreakingNews", reflect.TypeOf((*MockPublicNoticeServer)(nil).PushBreakingNews), ctx, in)
}

// TestPushRichTextBreakingNews mocks base method.
func (m *MockPublicNoticeServer) TestPushRichTextBreakingNews(ctx context.Context, in *TestPushRichTextBreakingNewsReq) (*TestPushRichTextBreakingNewsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TestPushRichTextBreakingNews", ctx, in)
	ret0, _ := ret[0].(*TestPushRichTextBreakingNewsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestPushRichTextBreakingNews indicates an expected call of TestPushRichTextBreakingNews.
func (mr *MockPublicNoticeServerMockRecorder) TestPushRichTextBreakingNews(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestPushRichTextBreakingNews", reflect.TypeOf((*MockPublicNoticeServer)(nil).TestPushRichTextBreakingNews), ctx, in)
}

// UpdateBreakingNewsConfig mocks base method.
func (m *MockPublicNoticeServer) UpdateBreakingNewsConfig(ctx context.Context, in *UpdateBreakingNewsConfigReq) (*UpdateBreakingNewsConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateBreakingNewsConfig", ctx, in)
	ret0, _ := ret[0].(*UpdateBreakingNewsConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateBreakingNewsConfig indicates an expected call of UpdateBreakingNewsConfig.
func (mr *MockPublicNoticeServerMockRecorder) UpdateBreakingNewsConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBreakingNewsConfig", reflect.TypeOf((*MockPublicNoticeServer)(nil).UpdateBreakingNewsConfig), ctx, in)
}

// UpdateStickBreakingNews mocks base method.
func (m *MockPublicNoticeServer) UpdateStickBreakingNews(ctx context.Context, in *UpdateStickBreakingNewsReq) (*UpdateStickBreakingNewsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateStickBreakingNews", ctx, in)
	ret0, _ := ret[0].(*UpdateStickBreakingNewsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateStickBreakingNews indicates an expected call of UpdateStickBreakingNews.
func (mr *MockPublicNoticeServerMockRecorder) UpdateStickBreakingNews(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStickBreakingNews", reflect.TypeOf((*MockPublicNoticeServer)(nil).UpdateStickBreakingNews), ctx, in)
}
