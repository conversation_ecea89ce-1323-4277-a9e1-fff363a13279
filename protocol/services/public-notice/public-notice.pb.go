// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/public-notice/public-notice.proto

package public_notice // import "golang.52tt.com/protocol/services/public-notice"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 置顶公告状态
type StickStatus int32

const (
	StickStatus_STICK_STATUS_UNSPECIFIED StickStatus = 0
	StickStatus_STICK_STATUS_WAITING     StickStatus = 1
	StickStatus_STICK_STATUS_EFFECTIVE   StickStatus = 2
	StickStatus_STICK_STATUS_EXPIRED     StickStatus = 3
)

var StickStatus_name = map[int32]string{
	0: "STICK_STATUS_UNSPECIFIED",
	1: "STICK_STATUS_WAITING",
	2: "STICK_STATUS_EFFECTIVE",
	3: "STICK_STATUS_EXPIRED",
}
var StickStatus_value = map[string]int32{
	"STICK_STATUS_UNSPECIFIED": 0,
	"STICK_STATUS_WAITING":     1,
	"STICK_STATUS_EFFECTIVE":   2,
	"STICK_STATUS_EXPIRED":     3,
}

func (x StickStatus) String() string {
	return proto.EnumName(StickStatus_name, int32(x))
}
func (StickStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{0}
}

// buf:lint:ignore ENUM_PASCAL_CASE
type PushBreakingNewsReq_BREAKING_CMD_TYPE int32

const (
	PushBreakingNewsReq_COMMON_BREAKING_EVENT_V3 PushBreakingNewsReq_BREAKING_CMD_TYPE = 0
	PushBreakingNewsReq_SMASH_EGG_BREAKING       PushBreakingNewsReq_BREAKING_CMD_TYPE = 1
)

var PushBreakingNewsReq_BREAKING_CMD_TYPE_name = map[int32]string{
	0: "COMMON_BREAKING_EVENT_V3",
	1: "SMASH_EGG_BREAKING",
}
var PushBreakingNewsReq_BREAKING_CMD_TYPE_value = map[string]int32{
	"COMMON_BREAKING_EVENT_V3": 0,
	"SMASH_EGG_BREAKING":       1,
}

func (x PushBreakingNewsReq_BREAKING_CMD_TYPE) String() string {
	return proto.EnumName(PushBreakingNewsReq_BREAKING_CMD_TYPE_name, int32(x))
}
func (PushBreakingNewsReq_BREAKING_CMD_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{2, 0}
}

// buf:lint:ignore ENUM_PASCAL_CASE
type BreakingNewsConfig_NEWS_INFO_TYPE int32

const (
	BreakingNewsConfig_NEWS_INFO_TYPE_UNKNOWN               BreakingNewsConfig_NEWS_INFO_TYPE = 0
	BreakingNewsConfig_NEWS_INFO_TYPE_CUSTOM                BreakingNewsConfig_NEWS_INFO_TYPE = 100
	BreakingNewsConfig_NEWS_INFO_TYPE_CUSTOM_TEXT           BreakingNewsConfig_NEWS_INFO_TYPE = 101
	BreakingNewsConfig_NEWS_INFO_TYPE_CUSTOM_LEVEL_ICON     BreakingNewsConfig_NEWS_INFO_TYPE = 102
	BreakingNewsConfig_NEWS_INFO_TYPE_CUSTOM_LEVEL_NAME     BreakingNewsConfig_NEWS_INFO_TYPE = 103
	BreakingNewsConfig_NEWS_INFO_TYPE_CUSTOM_NUM            BreakingNewsConfig_NEWS_INFO_TYPE = 104
	BreakingNewsConfig_NEWS_INFO_TYPE_CUSTOM_JUMP_LINK      BreakingNewsConfig_NEWS_INFO_TYPE = 105
	BreakingNewsConfig_NEWS_INFO_TYPE_USER_INFO             BreakingNewsConfig_NEWS_INFO_TYPE = 200
	BreakingNewsConfig_NEWS_INFO_TYPE_USER_FROM_ACCOUNT     BreakingNewsConfig_NEWS_INFO_TYPE = 201
	BreakingNewsConfig_NEWS_INFO_TYPE_USER_TO_ACCOUNT       BreakingNewsConfig_NEWS_INFO_TYPE = 202
	BreakingNewsConfig_NEWS_INFO_TYPE_USER_FROM_NICKNAME    BreakingNewsConfig_NEWS_INFO_TYPE = 203
	BreakingNewsConfig_NEWS_INFO_TYPE_USER_TO_NICKNAME      BreakingNewsConfig_NEWS_INFO_TYPE = 204
	BreakingNewsConfig_NEWS_INFO_TYPE_USER_FROM_HEAD        BreakingNewsConfig_NEWS_INFO_TYPE = 205
	BreakingNewsConfig_NEWS_INFO_TYPE_USER_TO_HEAD          BreakingNewsConfig_NEWS_INFO_TYPE = 206
	BreakingNewsConfig_NEWS_INFO_TYPE_USER_FROM_UKW_ACCOUNT BreakingNewsConfig_NEWS_INFO_TYPE = 207
	BreakingNewsConfig_NEWS_INFO_TYPE_USER_TO_UKW_ACCOUNT   BreakingNewsConfig_NEWS_INFO_TYPE = 208
	BreakingNewsConfig_NEWS_INFO_TYPE_CHANNEL               BreakingNewsConfig_NEWS_INFO_TYPE = 300
	BreakingNewsConfig_NEWS_INFO_TYPE_CHANNEL_NAME          BreakingNewsConfig_NEWS_INFO_TYPE = 301
	BreakingNewsConfig_NEWS_INFO_TYPE_CHANNEL_ID            BreakingNewsConfig_NEWS_INFO_TYPE = 302
	BreakingNewsConfig_NEWS_INFO_TYPE_GIFT                  BreakingNewsConfig_NEWS_INFO_TYPE = 400
	BreakingNewsConfig_NEWS_INFO_TYPE_GIFT_ICON             BreakingNewsConfig_NEWS_INFO_TYPE = 401
	BreakingNewsConfig_NEWS_INFO_TYPE_GIFT_NAME             BreakingNewsConfig_NEWS_INFO_TYPE = 402
	BreakingNewsConfig_NEWS_INFO_TYPE_GIFT_PRICE            BreakingNewsConfig_NEWS_INFO_TYPE = 403
	BreakingNewsConfig_NEWS_INFO_TYPE_GUILD                 BreakingNewsConfig_NEWS_INFO_TYPE = 500
	BreakingNewsConfig_NEWS_INFO_TYPE_GUILD_ICON            BreakingNewsConfig_NEWS_INFO_TYPE = 501
	BreakingNewsConfig_NEWS_INFO_TYPE_GUILD_NAME            BreakingNewsConfig_NEWS_INFO_TYPE = 502
	BreakingNewsConfig_NEWS_INFO_TYPE_GUILD_ID              BreakingNewsConfig_NEWS_INFO_TYPE = 503
	BreakingNewsConfig_NEWS_INFO_TYPE_FULL_TEXT_OUTER       BreakingNewsConfig_NEWS_INFO_TYPE = 9999
)

var BreakingNewsConfig_NEWS_INFO_TYPE_name = map[int32]string{
	0:    "NEWS_INFO_TYPE_UNKNOWN",
	100:  "NEWS_INFO_TYPE_CUSTOM",
	101:  "NEWS_INFO_TYPE_CUSTOM_TEXT",
	102:  "NEWS_INFO_TYPE_CUSTOM_LEVEL_ICON",
	103:  "NEWS_INFO_TYPE_CUSTOM_LEVEL_NAME",
	104:  "NEWS_INFO_TYPE_CUSTOM_NUM",
	105:  "NEWS_INFO_TYPE_CUSTOM_JUMP_LINK",
	200:  "NEWS_INFO_TYPE_USER_INFO",
	201:  "NEWS_INFO_TYPE_USER_FROM_ACCOUNT",
	202:  "NEWS_INFO_TYPE_USER_TO_ACCOUNT",
	203:  "NEWS_INFO_TYPE_USER_FROM_NICKNAME",
	204:  "NEWS_INFO_TYPE_USER_TO_NICKNAME",
	205:  "NEWS_INFO_TYPE_USER_FROM_HEAD",
	206:  "NEWS_INFO_TYPE_USER_TO_HEAD",
	207:  "NEWS_INFO_TYPE_USER_FROM_UKW_ACCOUNT",
	208:  "NEWS_INFO_TYPE_USER_TO_UKW_ACCOUNT",
	300:  "NEWS_INFO_TYPE_CHANNEL",
	301:  "NEWS_INFO_TYPE_CHANNEL_NAME",
	302:  "NEWS_INFO_TYPE_CHANNEL_ID",
	400:  "NEWS_INFO_TYPE_GIFT",
	401:  "NEWS_INFO_TYPE_GIFT_ICON",
	402:  "NEWS_INFO_TYPE_GIFT_NAME",
	403:  "NEWS_INFO_TYPE_GIFT_PRICE",
	500:  "NEWS_INFO_TYPE_GUILD",
	501:  "NEWS_INFO_TYPE_GUILD_ICON",
	502:  "NEWS_INFO_TYPE_GUILD_NAME",
	503:  "NEWS_INFO_TYPE_GUILD_ID",
	9999: "NEWS_INFO_TYPE_FULL_TEXT_OUTER",
}
var BreakingNewsConfig_NEWS_INFO_TYPE_value = map[string]int32{
	"NEWS_INFO_TYPE_UNKNOWN":               0,
	"NEWS_INFO_TYPE_CUSTOM":                100,
	"NEWS_INFO_TYPE_CUSTOM_TEXT":           101,
	"NEWS_INFO_TYPE_CUSTOM_LEVEL_ICON":     102,
	"NEWS_INFO_TYPE_CUSTOM_LEVEL_NAME":     103,
	"NEWS_INFO_TYPE_CUSTOM_NUM":            104,
	"NEWS_INFO_TYPE_CUSTOM_JUMP_LINK":      105,
	"NEWS_INFO_TYPE_USER_INFO":             200,
	"NEWS_INFO_TYPE_USER_FROM_ACCOUNT":     201,
	"NEWS_INFO_TYPE_USER_TO_ACCOUNT":       202,
	"NEWS_INFO_TYPE_USER_FROM_NICKNAME":    203,
	"NEWS_INFO_TYPE_USER_TO_NICKNAME":      204,
	"NEWS_INFO_TYPE_USER_FROM_HEAD":        205,
	"NEWS_INFO_TYPE_USER_TO_HEAD":          206,
	"NEWS_INFO_TYPE_USER_FROM_UKW_ACCOUNT": 207,
	"NEWS_INFO_TYPE_USER_TO_UKW_ACCOUNT":   208,
	"NEWS_INFO_TYPE_CHANNEL":               300,
	"NEWS_INFO_TYPE_CHANNEL_NAME":          301,
	"NEWS_INFO_TYPE_CHANNEL_ID":            302,
	"NEWS_INFO_TYPE_GIFT":                  400,
	"NEWS_INFO_TYPE_GIFT_ICON":             401,
	"NEWS_INFO_TYPE_GIFT_NAME":             402,
	"NEWS_INFO_TYPE_GIFT_PRICE":            403,
	"NEWS_INFO_TYPE_GUILD":                 500,
	"NEWS_INFO_TYPE_GUILD_ICON":            501,
	"NEWS_INFO_TYPE_GUILD_NAME":            502,
	"NEWS_INFO_TYPE_GUILD_ID":              503,
	"NEWS_INFO_TYPE_FULL_TEXT_OUTER":       9999,
}

func (x BreakingNewsConfig_NEWS_INFO_TYPE) String() string {
	return proto.EnumName(BreakingNewsConfig_NEWS_INFO_TYPE_name, int32(x))
}
func (BreakingNewsConfig_NEWS_INFO_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{15, 0}
}

// buf:lint:ignore ENUM_PASCAL_CASE
type BreakingNewsConfig_ANIME_TYPE int32

const (
	BreakingNewsConfig_ANIME_TYPE_UNKNOWN       BreakingNewsConfig_ANIME_TYPE = 0
	BreakingNewsConfig_ANIME_TYPE_LOTTIE        BreakingNewsConfig_ANIME_TYPE = 1
	BreakingNewsConfig_ANIME_TYPE_VAP           BreakingNewsConfig_ANIME_TYPE = 2
	BreakingNewsConfig_ANIME_TYPE_LOTTIE_FUSION BreakingNewsConfig_ANIME_TYPE = 3
	BreakingNewsConfig_ANIME_TYPE_VAP_FUSION    BreakingNewsConfig_ANIME_TYPE = 4
)

var BreakingNewsConfig_ANIME_TYPE_name = map[int32]string{
	0: "ANIME_TYPE_UNKNOWN",
	1: "ANIME_TYPE_LOTTIE",
	2: "ANIME_TYPE_VAP",
	3: "ANIME_TYPE_LOTTIE_FUSION",
	4: "ANIME_TYPE_VAP_FUSION",
}
var BreakingNewsConfig_ANIME_TYPE_value = map[string]int32{
	"ANIME_TYPE_UNKNOWN":       0,
	"ANIME_TYPE_LOTTIE":        1,
	"ANIME_TYPE_VAP":           2,
	"ANIME_TYPE_LOTTIE_FUSION": 3,
	"ANIME_TYPE_VAP_FUSION":    4,
}

func (x BreakingNewsConfig_ANIME_TYPE) String() string {
	return proto.EnumName(BreakingNewsConfig_ANIME_TYPE_name, int32(x))
}
func (BreakingNewsConfig_ANIME_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{15, 1}
}

// 公告范围       枚举值采用二进制的向左移一位方式                 1(1), 10(2), 100(4)
// buf:lint:ignore ENUM_PASCAL_CASE
type BreakingNewsConfig_ANNOUNCE_SCOPE int32

const (
	BreakingNewsConfig_ANNOUNCE_SCOPE_UNKNOWN BreakingNewsConfig_ANNOUNCE_SCOPE = 0
	BreakingNewsConfig_INSIDE_CHANNEL         BreakingNewsConfig_ANNOUNCE_SCOPE = 1
	BreakingNewsConfig_OUTSIDE_CHANNEL        BreakingNewsConfig_ANNOUNCE_SCOPE = 2
	BreakingNewsConfig_PUBLIC_GUILD_CHANNEL   BreakingNewsConfig_ANNOUNCE_SCOPE = 4
	BreakingNewsConfig_THIS_CHANNEL           BreakingNewsConfig_ANNOUNCE_SCOPE = 8
)

var BreakingNewsConfig_ANNOUNCE_SCOPE_name = map[int32]string{
	0: "ANNOUNCE_SCOPE_UNKNOWN",
	1: "INSIDE_CHANNEL",
	2: "OUTSIDE_CHANNEL",
	4: "PUBLIC_GUILD_CHANNEL",
	8: "THIS_CHANNEL",
}
var BreakingNewsConfig_ANNOUNCE_SCOPE_value = map[string]int32{
	"ANNOUNCE_SCOPE_UNKNOWN": 0,
	"INSIDE_CHANNEL":         1,
	"OUTSIDE_CHANNEL":        2,
	"PUBLIC_GUILD_CHANNEL":   4,
	"THIS_CHANNEL":           8,
}

func (x BreakingNewsConfig_ANNOUNCE_SCOPE) String() string {
	return proto.EnumName(BreakingNewsConfig_ANNOUNCE_SCOPE_name, int32(x))
}
func (BreakingNewsConfig_ANNOUNCE_SCOPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{15, 2}
}

// 公告位置
// buf:lint:ignore ENUM_PASCAL_CASE
type BreakingNewsConfig_ANNOUNCE_POSITION int32

const (
	BreakingNewsConfig_ANNOUNCE_POSITION_UNKNOWN BreakingNewsConfig_ANNOUNCE_POSITION = 0
	BreakingNewsConfig_UPPER                     BreakingNewsConfig_ANNOUNCE_POSITION = 1
	BreakingNewsConfig_MIDDLE                    BreakingNewsConfig_ANNOUNCE_POSITION = 2
)

var BreakingNewsConfig_ANNOUNCE_POSITION_name = map[int32]string{
	0: "ANNOUNCE_POSITION_UNKNOWN",
	1: "UPPER",
	2: "MIDDLE",
}
var BreakingNewsConfig_ANNOUNCE_POSITION_value = map[string]int32{
	"ANNOUNCE_POSITION_UNKNOWN": 0,
	"UPPER":                     1,
	"MIDDLE":                    2,
}

func (x BreakingNewsConfig_ANNOUNCE_POSITION) String() string {
	return proto.EnumName(BreakingNewsConfig_ANNOUNCE_POSITION_name, int32(x))
}
func (BreakingNewsConfig_ANNOUNCE_POSITION) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{15, 3}
}

// 点击跳转类型        3:房间内外     枚举值采用二进制的向左移一位方式                 1(1), 10(2), 100(4)
// buf:lint:ignore ENUM_PASCAL_CASE
type BreakingNewsConfig_JUMP_TYPE int32

const (
	BreakingNewsConfig_NO_JUMP_CLICK      BreakingNewsConfig_JUMP_TYPE = 0
	BreakingNewsConfig_JUMP_CLICK_INSIDE  BreakingNewsConfig_JUMP_TYPE = 1
	BreakingNewsConfig_JUMP_ClICK_OUTSIDE BreakingNewsConfig_JUMP_TYPE = 2
)

var BreakingNewsConfig_JUMP_TYPE_name = map[int32]string{
	0: "NO_JUMP_CLICK",
	1: "JUMP_CLICK_INSIDE",
	2: "JUMP_ClICK_OUTSIDE",
}
var BreakingNewsConfig_JUMP_TYPE_value = map[string]int32{
	"NO_JUMP_CLICK":      0,
	"JUMP_CLICK_INSIDE":  1,
	"JUMP_ClICK_OUTSIDE": 2,
}

func (x BreakingNewsConfig_JUMP_TYPE) String() string {
	return proto.EnumName(BreakingNewsConfig_JUMP_TYPE_name, int32(x))
}
func (BreakingNewsConfig_JUMP_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{15, 4}
}

// 通用全服推送
type CommonPublicMsgPushReq struct {
	OptData              []byte   `protobuf:"bytes,1,opt,name=opt_data,json=optData,proto3" json:"opt_data,omitempty"`
	AnnounceScope        uint32   `protobuf:"varint,2,opt,name=announce_scope,json=announceScope,proto3" json:"announce_scope,omitempty"`
	CmdType              uint32   `protobuf:"varint,3,opt,name=cmd_type,json=cmdType,proto3" json:"cmd_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommonPublicMsgPushReq) Reset()         { *m = CommonPublicMsgPushReq{} }
func (m *CommonPublicMsgPushReq) String() string { return proto.CompactTextString(m) }
func (*CommonPublicMsgPushReq) ProtoMessage()    {}
func (*CommonPublicMsgPushReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{0}
}
func (m *CommonPublicMsgPushReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonPublicMsgPushReq.Unmarshal(m, b)
}
func (m *CommonPublicMsgPushReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonPublicMsgPushReq.Marshal(b, m, deterministic)
}
func (dst *CommonPublicMsgPushReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonPublicMsgPushReq.Merge(dst, src)
}
func (m *CommonPublicMsgPushReq) XXX_Size() int {
	return xxx_messageInfo_CommonPublicMsgPushReq.Size(m)
}
func (m *CommonPublicMsgPushReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonPublicMsgPushReq.DiscardUnknown(m)
}

var xxx_messageInfo_CommonPublicMsgPushReq proto.InternalMessageInfo

func (m *CommonPublicMsgPushReq) GetOptData() []byte {
	if m != nil {
		return m.OptData
	}
	return nil
}

func (m *CommonPublicMsgPushReq) GetAnnounceScope() uint32 {
	if m != nil {
		return m.AnnounceScope
	}
	return 0
}

func (m *CommonPublicMsgPushReq) GetCmdType() uint32 {
	if m != nil {
		return m.CmdType
	}
	return 0
}

type CommonPublicMsgPushResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommonPublicMsgPushResp) Reset()         { *m = CommonPublicMsgPushResp{} }
func (m *CommonPublicMsgPushResp) String() string { return proto.CompactTextString(m) }
func (*CommonPublicMsgPushResp) ProtoMessage()    {}
func (*CommonPublicMsgPushResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{1}
}
func (m *CommonPublicMsgPushResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonPublicMsgPushResp.Unmarshal(m, b)
}
func (m *CommonPublicMsgPushResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonPublicMsgPushResp.Marshal(b, m, deterministic)
}
func (dst *CommonPublicMsgPushResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonPublicMsgPushResp.Merge(dst, src)
}
func (m *CommonPublicMsgPushResp) XXX_Size() int {
	return xxx_messageInfo_CommonPublicMsgPushResp.Size(m)
}
func (m *CommonPublicMsgPushResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonPublicMsgPushResp.DiscardUnknown(m)
}

var xxx_messageInfo_CommonPublicMsgPushResp proto.InternalMessageInfo

type PushBreakingNewsReq struct {
	BreakingCmdType      PushBreakingNewsReq_BREAKING_CMD_TYPE `protobuf:"varint,1,opt,name=breaking_cmd_type,json=breakingCmdType,proto3,enum=public_notice.PushBreakingNewsReq_BREAKING_CMD_TYPE" json:"breaking_cmd_type,omitempty"`
	CommonBreakingNews   *CommonBreakingNewsV3                 `protobuf:"bytes,2,opt,name=common_breaking_news,json=commonBreakingNews,proto3" json:"common_breaking_news,omitempty"`
	SmashEggBreakingNews *SmashEggBreakingNews                 `protobuf:"bytes,3,opt,name=smash_egg_breaking_news,json=smashEggBreakingNews,proto3" json:"smash_egg_breaking_news,omitempty"`
	RichTextNews         *RichTextNews                         `protobuf:"bytes,4,opt,name=rich_text_news,json=richTextNews,proto3" json:"rich_text_news,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                              `json:"-"`
	XXX_unrecognized     []byte                                `json:"-"`
	XXX_sizecache        int32                                 `json:"-"`
}

func (m *PushBreakingNewsReq) Reset()         { *m = PushBreakingNewsReq{} }
func (m *PushBreakingNewsReq) String() string { return proto.CompactTextString(m) }
func (*PushBreakingNewsReq) ProtoMessage()    {}
func (*PushBreakingNewsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{2}
}
func (m *PushBreakingNewsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushBreakingNewsReq.Unmarshal(m, b)
}
func (m *PushBreakingNewsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushBreakingNewsReq.Marshal(b, m, deterministic)
}
func (dst *PushBreakingNewsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushBreakingNewsReq.Merge(dst, src)
}
func (m *PushBreakingNewsReq) XXX_Size() int {
	return xxx_messageInfo_PushBreakingNewsReq.Size(m)
}
func (m *PushBreakingNewsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PushBreakingNewsReq.DiscardUnknown(m)
}

var xxx_messageInfo_PushBreakingNewsReq proto.InternalMessageInfo

func (m *PushBreakingNewsReq) GetBreakingCmdType() PushBreakingNewsReq_BREAKING_CMD_TYPE {
	if m != nil {
		return m.BreakingCmdType
	}
	return PushBreakingNewsReq_COMMON_BREAKING_EVENT_V3
}

func (m *PushBreakingNewsReq) GetCommonBreakingNews() *CommonBreakingNewsV3 {
	if m != nil {
		return m.CommonBreakingNews
	}
	return nil
}

func (m *PushBreakingNewsReq) GetSmashEggBreakingNews() *SmashEggBreakingNews {
	if m != nil {
		return m.SmashEggBreakingNews
	}
	return nil
}

func (m *PushBreakingNewsReq) GetRichTextNews() *RichTextNews {
	if m != nil {
		return m.RichTextNews
	}
	return nil
}

type RichTextNews struct {
	NewsId               uint32   `protobuf:"varint,1,opt,name=news_id,json=newsId,proto3" json:"news_id,omitempty"`
	CustomBizName        string   `protobuf:"bytes,2,opt,name=custom_biz_name,json=customBizName,proto3" json:"custom_biz_name,omitempty"`
	CustomBizLevelIcon   string   `protobuf:"bytes,3,opt,name=custom_biz_level_icon,json=customBizLevelIcon,proto3" json:"custom_biz_level_icon,omitempty"`
	CustomBizLevelName   string   `protobuf:"bytes,4,opt,name=custom_biz_level_name,json=customBizLevelName,proto3" json:"custom_biz_level_name,omitempty"`
	CustomBizNum         string   `protobuf:"bytes,5,opt,name=custom_biz_num,json=customBizNum,proto3" json:"custom_biz_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RichTextNews) Reset()         { *m = RichTextNews{} }
func (m *RichTextNews) String() string { return proto.CompactTextString(m) }
func (*RichTextNews) ProtoMessage()    {}
func (*RichTextNews) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{3}
}
func (m *RichTextNews) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RichTextNews.Unmarshal(m, b)
}
func (m *RichTextNews) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RichTextNews.Marshal(b, m, deterministic)
}
func (dst *RichTextNews) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RichTextNews.Merge(dst, src)
}
func (m *RichTextNews) XXX_Size() int {
	return xxx_messageInfo_RichTextNews.Size(m)
}
func (m *RichTextNews) XXX_DiscardUnknown() {
	xxx_messageInfo_RichTextNews.DiscardUnknown(m)
}

var xxx_messageInfo_RichTextNews proto.InternalMessageInfo

func (m *RichTextNews) GetNewsId() uint32 {
	if m != nil {
		return m.NewsId
	}
	return 0
}

func (m *RichTextNews) GetCustomBizName() string {
	if m != nil {
		return m.CustomBizName
	}
	return ""
}

func (m *RichTextNews) GetCustomBizLevelIcon() string {
	if m != nil {
		return m.CustomBizLevelIcon
	}
	return ""
}

func (m *RichTextNews) GetCustomBizLevelName() string {
	if m != nil {
		return m.CustomBizLevelName
	}
	return ""
}

func (m *RichTextNews) GetCustomBizNum() string {
	if m != nil {
		return m.CustomBizNum
	}
	return ""
}

type PushBreakingNewsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushBreakingNewsResp) Reset()         { *m = PushBreakingNewsResp{} }
func (m *PushBreakingNewsResp) String() string { return proto.CompactTextString(m) }
func (*PushBreakingNewsResp) ProtoMessage()    {}
func (*PushBreakingNewsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{4}
}
func (m *PushBreakingNewsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushBreakingNewsResp.Unmarshal(m, b)
}
func (m *PushBreakingNewsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushBreakingNewsResp.Marshal(b, m, deterministic)
}
func (dst *PushBreakingNewsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushBreakingNewsResp.Merge(dst, src)
}
func (m *PushBreakingNewsResp) XXX_Size() int {
	return xxx_messageInfo_PushBreakingNewsResp.Size(m)
}
func (m *PushBreakingNewsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PushBreakingNewsResp.DiscardUnknown(m)
}

var xxx_messageInfo_PushBreakingNewsResp proto.InternalMessageInfo

// 魔力转转大事件
type SmashEggBreakingNews struct {
	CurrentHits          uint32   `protobuf:"varint,1,opt,name=current_hits,json=currentHits,proto3" json:"current_hits,omitempty"`
	MorphHits            uint32   `protobuf:"varint,2,opt,name=morph_hits,json=morphHits,proto3" json:"morph_hits,omitempty"`
	MorphFlag            uint32   `protobuf:"varint,3,opt,name=morph_flag,json=morphFlag,proto3" json:"morph_flag,omitempty"`
	MorphEndTime         uint32   `protobuf:"varint,4,opt,name=morph_end_time,json=morphEndTime,proto3" json:"morph_end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SmashEggBreakingNews) Reset()         { *m = SmashEggBreakingNews{} }
func (m *SmashEggBreakingNews) String() string { return proto.CompactTextString(m) }
func (*SmashEggBreakingNews) ProtoMessage()    {}
func (*SmashEggBreakingNews) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{5}
}
func (m *SmashEggBreakingNews) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SmashEggBreakingNews.Unmarshal(m, b)
}
func (m *SmashEggBreakingNews) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SmashEggBreakingNews.Marshal(b, m, deterministic)
}
func (dst *SmashEggBreakingNews) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SmashEggBreakingNews.Merge(dst, src)
}
func (m *SmashEggBreakingNews) XXX_Size() int {
	return xxx_messageInfo_SmashEggBreakingNews.Size(m)
}
func (m *SmashEggBreakingNews) XXX_DiscardUnknown() {
	xxx_messageInfo_SmashEggBreakingNews.DiscardUnknown(m)
}

var xxx_messageInfo_SmashEggBreakingNews proto.InternalMessageInfo

func (m *SmashEggBreakingNews) GetCurrentHits() uint32 {
	if m != nil {
		return m.CurrentHits
	}
	return 0
}

func (m *SmashEggBreakingNews) GetMorphHits() uint32 {
	if m != nil {
		return m.MorphHits
	}
	return 0
}

func (m *SmashEggBreakingNews) GetMorphFlag() uint32 {
	if m != nil {
		return m.MorphFlag
	}
	return 0
}

func (m *SmashEggBreakingNews) GetMorphEndTime() uint32 {
	if m != nil {
		return m.MorphEndTime
	}
	return 0
}

type UserInfo struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Nick                 string   `protobuf:"bytes,2,opt,name=nick,proto3" json:"nick,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserInfo) Reset()         { *m = UserInfo{} }
func (m *UserInfo) String() string { return proto.CompactTextString(m) }
func (*UserInfo) ProtoMessage()    {}
func (*UserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{6}
}
func (m *UserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfo.Unmarshal(m, b)
}
func (m *UserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfo.Marshal(b, m, deterministic)
}
func (dst *UserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfo.Merge(dst, src)
}
func (m *UserInfo) XXX_Size() int {
	return xxx_messageInfo_UserInfo.Size(m)
}
func (m *UserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfo proto.InternalMessageInfo

func (m *UserInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserInfo) GetNick() string {
	if m != nil {
		return m.Nick
	}
	return ""
}

type ChannelInfo struct {
	ChannelDisplayid     uint32   `protobuf:"varint,1,opt,name=channel_displayid,json=channelDisplayid,proto3" json:"channel_displayid,omitempty"`
	ChannelBindid        uint32   `protobuf:"varint,2,opt,name=channel_bindid,json=channelBindid,proto3" json:"channel_bindid,omitempty"`
	ChannelType          uint32   `protobuf:"varint,3,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	ChannelName          string   `protobuf:"bytes,4,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	ChannelIconMd5       string   `protobuf:"bytes,5,opt,name=channel_icon_md5,json=channelIconMd5,proto3" json:"channel_icon_md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelInfo) Reset()         { *m = ChannelInfo{} }
func (m *ChannelInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelInfo) ProtoMessage()    {}
func (*ChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{7}
}
func (m *ChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelInfo.Unmarshal(m, b)
}
func (m *ChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelInfo.Merge(dst, src)
}
func (m *ChannelInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelInfo.Size(m)
}
func (m *ChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelInfo proto.InternalMessageInfo

func (m *ChannelInfo) GetChannelDisplayid() uint32 {
	if m != nil {
		return m.ChannelDisplayid
	}
	return 0
}

func (m *ChannelInfo) GetChannelBindid() uint32 {
	if m != nil {
		return m.ChannelBindid
	}
	return 0
}

func (m *ChannelInfo) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *ChannelInfo) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *ChannelInfo) GetChannelIconMd5() string {
	if m != nil {
		return m.ChannelIconMd5
	}
	return ""
}

type GuildInfo struct {
	GuildName            string   `protobuf:"bytes,1,opt,name=guild_name,json=guildName,proto3" json:"guild_name,omitempty"`
	GuildDisplayId       uint32   `protobuf:"varint,2,opt,name=guild_display_id,json=guildDisplayId,proto3" json:"guild_display_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildInfo) Reset()         { *m = GuildInfo{} }
func (m *GuildInfo) String() string { return proto.CompactTextString(m) }
func (*GuildInfo) ProtoMessage()    {}
func (*GuildInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{8}
}
func (m *GuildInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildInfo.Unmarshal(m, b)
}
func (m *GuildInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildInfo.Marshal(b, m, deterministic)
}
func (dst *GuildInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildInfo.Merge(dst, src)
}
func (m *GuildInfo) XXX_Size() int {
	return xxx_messageInfo_GuildInfo.Size(m)
}
func (m *GuildInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GuildInfo proto.InternalMessageInfo

func (m *GuildInfo) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

func (m *GuildInfo) GetGuildDisplayId() uint32 {
	if m != nil {
		return m.GuildDisplayId
	}
	return 0
}

// 通用全服大事件V3
type CommonBreakingNewsV3 struct {
	FromUid              uint32                      `protobuf:"varint,1,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	FromUserInfo         *UserInfo                   `protobuf:"bytes,2,opt,name=from_user_info,json=fromUserInfo,proto3" json:"from_user_info,omitempty"`
	TargetUid            uint32                      `protobuf:"varint,3,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	TargetUserInfo       *UserInfo                   `protobuf:"bytes,4,opt,name=target_user_info,json=targetUserInfo,proto3" json:"target_user_info,omitempty"`
	ChannelId            uint32                      `protobuf:"varint,5,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelInfo          *ChannelInfo                `protobuf:"bytes,6,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	GuildId              uint32                      `protobuf:"varint,7,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GuildInfo            *GuildInfo                  `protobuf:"bytes,8,opt,name=guild_info,json=guildInfo,proto3" json:"guild_info,omitempty"`
	NewsContent          string                      `protobuf:"bytes,9,opt,name=news_content,json=newsContent,proto3" json:"news_content,omitempty"`
	JumpUrl              string                      `protobuf:"bytes,10,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	DelaySecs            uint32                      `protobuf:"varint,11,opt,name=delay_secs,json=delaySecs,proto3" json:"delay_secs,omitempty"`
	NewsPrefix           string                      `protobuf:"bytes,12,opt,name=news_prefix,json=newsPrefix,proto3" json:"news_prefix,omitempty"`
	DatingSceneName      string                      `protobuf:"bytes,13,opt,name=dating_scene_name,json=datingSceneName,proto3" json:"dating_scene_name,omitempty"`
	BreakingNewsBaseOpt  *CommBreakingNewsBaseOpt    `protobuf:"bytes,14,opt,name=breaking_news_base_opt,json=breakingNewsBaseOpt,proto3" json:"breaking_news_base_opt,omitempty"`
	PresentNewsBaseOpt   *PresentBreakingNewsBaseOpt `protobuf:"bytes,15,opt,name=present_news_base_opt,json=presentNewsBaseOpt,proto3" json:"present_news_base_opt,omitempty"`
	RichLevel            uint32                      `protobuf:"varint,16,opt,name=rich_level,json=richLevel,proto3" json:"rich_level,omitempty"`
	IsOldDeal            uint32                      `protobuf:"varint,17,opt,name=is_old_deal,json=isOldDeal,proto3" json:"is_old_deal,omitempty"`
	OldNewsContent       string                      `protobuf:"bytes,18,opt,name=old_news_content,json=oldNewsContent,proto3" json:"old_news_content,omitempty"`
	TagId                uint32                      `protobuf:"varint,19,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	NobilityLevel        uint32                      `protobuf:"varint,20,opt,name=nobility_level,json=nobilityLevel,proto3" json:"nobility_level,omitempty"`
	NobilityLevelName    string                      `protobuf:"bytes,21,opt,name=nobility_level_name,json=nobilityLevelName,proto3" json:"nobility_level_name,omitempty"`
	NeedMonsterInfo      bool                        `protobuf:"varint,22,opt,name=need_monster_info,json=needMonsterInfo,proto3" json:"need_monster_info,omitempty"`
	NobilityExtentCnt    uint32                      `protobuf:"varint,23,opt,name=nobility_extent_cnt,json=nobilityExtentCnt,proto3" json:"nobility_extent_cnt,omitempty"`
	OptData              []byte                      `protobuf:"bytes,24,opt,name=opt_data,json=optData,proto3" json:"opt_data,omitempty"`
	HardUrl              string                      `protobuf:"bytes,25,opt,name=hard_url,json=hardUrl,proto3" json:"hard_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *CommonBreakingNewsV3) Reset()         { *m = CommonBreakingNewsV3{} }
func (m *CommonBreakingNewsV3) String() string { return proto.CompactTextString(m) }
func (*CommonBreakingNewsV3) ProtoMessage()    {}
func (*CommonBreakingNewsV3) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{9}
}
func (m *CommonBreakingNewsV3) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonBreakingNewsV3.Unmarshal(m, b)
}
func (m *CommonBreakingNewsV3) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonBreakingNewsV3.Marshal(b, m, deterministic)
}
func (dst *CommonBreakingNewsV3) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonBreakingNewsV3.Merge(dst, src)
}
func (m *CommonBreakingNewsV3) XXX_Size() int {
	return xxx_messageInfo_CommonBreakingNewsV3.Size(m)
}
func (m *CommonBreakingNewsV3) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonBreakingNewsV3.DiscardUnknown(m)
}

var xxx_messageInfo_CommonBreakingNewsV3 proto.InternalMessageInfo

func (m *CommonBreakingNewsV3) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *CommonBreakingNewsV3) GetFromUserInfo() *UserInfo {
	if m != nil {
		return m.FromUserInfo
	}
	return nil
}

func (m *CommonBreakingNewsV3) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *CommonBreakingNewsV3) GetTargetUserInfo() *UserInfo {
	if m != nil {
		return m.TargetUserInfo
	}
	return nil
}

func (m *CommonBreakingNewsV3) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CommonBreakingNewsV3) GetChannelInfo() *ChannelInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

func (m *CommonBreakingNewsV3) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *CommonBreakingNewsV3) GetGuildInfo() *GuildInfo {
	if m != nil {
		return m.GuildInfo
	}
	return nil
}

func (m *CommonBreakingNewsV3) GetNewsContent() string {
	if m != nil {
		return m.NewsContent
	}
	return ""
}

func (m *CommonBreakingNewsV3) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *CommonBreakingNewsV3) GetDelaySecs() uint32 {
	if m != nil {
		return m.DelaySecs
	}
	return 0
}

func (m *CommonBreakingNewsV3) GetNewsPrefix() string {
	if m != nil {
		return m.NewsPrefix
	}
	return ""
}

func (m *CommonBreakingNewsV3) GetDatingSceneName() string {
	if m != nil {
		return m.DatingSceneName
	}
	return ""
}

func (m *CommonBreakingNewsV3) GetBreakingNewsBaseOpt() *CommBreakingNewsBaseOpt {
	if m != nil {
		return m.BreakingNewsBaseOpt
	}
	return nil
}

func (m *CommonBreakingNewsV3) GetPresentNewsBaseOpt() *PresentBreakingNewsBaseOpt {
	if m != nil {
		return m.PresentNewsBaseOpt
	}
	return nil
}

func (m *CommonBreakingNewsV3) GetRichLevel() uint32 {
	if m != nil {
		return m.RichLevel
	}
	return 0
}

func (m *CommonBreakingNewsV3) GetIsOldDeal() uint32 {
	if m != nil {
		return m.IsOldDeal
	}
	return 0
}

func (m *CommonBreakingNewsV3) GetOldNewsContent() string {
	if m != nil {
		return m.OldNewsContent
	}
	return ""
}

func (m *CommonBreakingNewsV3) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *CommonBreakingNewsV3) GetNobilityLevel() uint32 {
	if m != nil {
		return m.NobilityLevel
	}
	return 0
}

func (m *CommonBreakingNewsV3) GetNobilityLevelName() string {
	if m != nil {
		return m.NobilityLevelName
	}
	return ""
}

func (m *CommonBreakingNewsV3) GetNeedMonsterInfo() bool {
	if m != nil {
		return m.NeedMonsterInfo
	}
	return false
}

func (m *CommonBreakingNewsV3) GetNobilityExtentCnt() uint32 {
	if m != nil {
		return m.NobilityExtentCnt
	}
	return 0
}

func (m *CommonBreakingNewsV3) GetOptData() []byte {
	if m != nil {
		return m.OptData
	}
	return nil
}

func (m *CommonBreakingNewsV3) GetHardUrl() string {
	if m != nil {
		return m.HardUrl
	}
	return ""
}

type CommBreakingNewsBaseOpt struct {
	TriggerType          uint32   `protobuf:"varint,1,opt,name=trigger_type,json=triggerType,proto3" json:"trigger_type,omitempty"`
	RollingCount         uint32   `protobuf:"varint,2,opt,name=rolling_count,json=rollingCount,proto3" json:"rolling_count,omitempty"`
	RollingTime          uint32   `protobuf:"varint,3,opt,name=rolling_time,json=rollingTime,proto3" json:"rolling_time,omitempty"`
	AnnounceScope        uint32   `protobuf:"varint,4,opt,name=announce_scope,json=announceScope,proto3" json:"announce_scope,omitempty"`
	AnnouncePosition     uint32   `protobuf:"varint,5,opt,name=announce_position,json=announcePosition,proto3" json:"announce_position,omitempty"`
	JumpType             uint32   `protobuf:"varint,6,opt,name=jump_type,json=jumpType,proto3" json:"jump_type,omitempty"`
	JumpPosition         uint32   `protobuf:"varint,7,opt,name=jump_position,json=jumpPosition,proto3" json:"jump_position,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommBreakingNewsBaseOpt) Reset()         { *m = CommBreakingNewsBaseOpt{} }
func (m *CommBreakingNewsBaseOpt) String() string { return proto.CompactTextString(m) }
func (*CommBreakingNewsBaseOpt) ProtoMessage()    {}
func (*CommBreakingNewsBaseOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{10}
}
func (m *CommBreakingNewsBaseOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommBreakingNewsBaseOpt.Unmarshal(m, b)
}
func (m *CommBreakingNewsBaseOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommBreakingNewsBaseOpt.Marshal(b, m, deterministic)
}
func (dst *CommBreakingNewsBaseOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommBreakingNewsBaseOpt.Merge(dst, src)
}
func (m *CommBreakingNewsBaseOpt) XXX_Size() int {
	return xxx_messageInfo_CommBreakingNewsBaseOpt.Size(m)
}
func (m *CommBreakingNewsBaseOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_CommBreakingNewsBaseOpt.DiscardUnknown(m)
}

var xxx_messageInfo_CommBreakingNewsBaseOpt proto.InternalMessageInfo

func (m *CommBreakingNewsBaseOpt) GetTriggerType() uint32 {
	if m != nil {
		return m.TriggerType
	}
	return 0
}

func (m *CommBreakingNewsBaseOpt) GetRollingCount() uint32 {
	if m != nil {
		return m.RollingCount
	}
	return 0
}

func (m *CommBreakingNewsBaseOpt) GetRollingTime() uint32 {
	if m != nil {
		return m.RollingTime
	}
	return 0
}

func (m *CommBreakingNewsBaseOpt) GetAnnounceScope() uint32 {
	if m != nil {
		return m.AnnounceScope
	}
	return 0
}

func (m *CommBreakingNewsBaseOpt) GetAnnouncePosition() uint32 {
	if m != nil {
		return m.AnnouncePosition
	}
	return 0
}

func (m *CommBreakingNewsBaseOpt) GetJumpType() uint32 {
	if m != nil {
		return m.JumpType
	}
	return 0
}

func (m *CommBreakingNewsBaseOpt) GetJumpPosition() uint32 {
	if m != nil {
		return m.JumpPosition
	}
	return 0
}

// 送礼的一些基本属性
type PresentBreakingNewsBaseOpt struct {
	GiftName             string   `protobuf:"bytes,1,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`
	GiftId               uint32   `protobuf:"varint,2,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftCount            uint32   `protobuf:"varint,3,opt,name=gift_count,json=giftCount,proto3" json:"gift_count,omitempty"`
	GiftIconUrl          string   `protobuf:"bytes,4,opt,name=gift_icon_url,json=giftIconUrl,proto3" json:"gift_icon_url,omitempty"`
	GiftWorth            uint32   `protobuf:"varint,5,opt,name=gift_worth,json=giftWorth,proto3" json:"gift_worth,omitempty"`
	MagicId              uint32   `protobuf:"varint,6,opt,name=magic_id,json=magicId,proto3" json:"magic_id,omitempty"`
	MagicName            string   `protobuf:"bytes,7,opt,name=magic_name,json=magicName,proto3" json:"magic_name,omitempty"`
	MagicIcon            string   `protobuf:"bytes,8,opt,name=magic_icon,json=magicIcon,proto3" json:"magic_icon,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentBreakingNewsBaseOpt) Reset()         { *m = PresentBreakingNewsBaseOpt{} }
func (m *PresentBreakingNewsBaseOpt) String() string { return proto.CompactTextString(m) }
func (*PresentBreakingNewsBaseOpt) ProtoMessage()    {}
func (*PresentBreakingNewsBaseOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{11}
}
func (m *PresentBreakingNewsBaseOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentBreakingNewsBaseOpt.Unmarshal(m, b)
}
func (m *PresentBreakingNewsBaseOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentBreakingNewsBaseOpt.Marshal(b, m, deterministic)
}
func (dst *PresentBreakingNewsBaseOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentBreakingNewsBaseOpt.Merge(dst, src)
}
func (m *PresentBreakingNewsBaseOpt) XXX_Size() int {
	return xxx_messageInfo_PresentBreakingNewsBaseOpt.Size(m)
}
func (m *PresentBreakingNewsBaseOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentBreakingNewsBaseOpt.DiscardUnknown(m)
}

var xxx_messageInfo_PresentBreakingNewsBaseOpt proto.InternalMessageInfo

func (m *PresentBreakingNewsBaseOpt) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *PresentBreakingNewsBaseOpt) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *PresentBreakingNewsBaseOpt) GetGiftCount() uint32 {
	if m != nil {
		return m.GiftCount
	}
	return 0
}

func (m *PresentBreakingNewsBaseOpt) GetGiftIconUrl() string {
	if m != nil {
		return m.GiftIconUrl
	}
	return ""
}

func (m *PresentBreakingNewsBaseOpt) GetGiftWorth() uint32 {
	if m != nil {
		return m.GiftWorth
	}
	return 0
}

func (m *PresentBreakingNewsBaseOpt) GetMagicId() uint32 {
	if m != nil {
		return m.MagicId
	}
	return 0
}

func (m *PresentBreakingNewsBaseOpt) GetMagicName() string {
	if m != nil {
		return m.MagicName
	}
	return ""
}

func (m *PresentBreakingNewsBaseOpt) GetMagicIcon() string {
	if m != nil {
		return m.MagicIcon
	}
	return ""
}

// rush机制信息
type RushInfo struct {
	RushType             uint32   `protobuf:"varint,1,opt,name=rush_type,json=rushType,proto3" json:"rush_type,omitempty"`
	RushMaxRandTs        uint32   `protobuf:"varint,2,opt,name=rush_max_rand_ts,json=rushMaxRandTs,proto3" json:"rush_max_rand_ts,omitempty"`
	RushWaitTs           uint32   `protobuf:"varint,3,opt,name=rush_wait_ts,json=rushWaitTs,proto3" json:"rush_wait_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RushInfo) Reset()         { *m = RushInfo{} }
func (m *RushInfo) String() string { return proto.CompactTextString(m) }
func (*RushInfo) ProtoMessage()    {}
func (*RushInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{12}
}
func (m *RushInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RushInfo.Unmarshal(m, b)
}
func (m *RushInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RushInfo.Marshal(b, m, deterministic)
}
func (dst *RushInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RushInfo.Merge(dst, src)
}
func (m *RushInfo) XXX_Size() int {
	return xxx_messageInfo_RushInfo.Size(m)
}
func (m *RushInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RushInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RushInfo proto.InternalMessageInfo

func (m *RushInfo) GetRushType() uint32 {
	if m != nil {
		return m.RushType
	}
	return 0
}

func (m *RushInfo) GetRushMaxRandTs() uint32 {
	if m != nil {
		return m.RushMaxRandTs
	}
	return 0
}

func (m *RushInfo) GetRushWaitTs() uint32 {
	if m != nil {
		return m.RushWaitTs
	}
	return 0
}

type TestPushRichTextBreakingNewsReq struct {
	NewsId               uint32   `protobuf:"varint,1,opt,name=news_id,json=newsId,proto3" json:"news_id,omitempty"`
	FromUid              uint32   `protobuf:"varint,2,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	ToUid                uint32   `protobuf:"varint,3,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GiftId               uint32   `protobuf:"varint,5,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	CustomJumpLink       string   `protobuf:"bytes,6,opt,name=custom_jump_link,json=customJumpLink,proto3" json:"custom_jump_link,omitempty"`
	CustomBizName        string   `protobuf:"bytes,7,opt,name=custom_biz_name,json=customBizName,proto3" json:"custom_biz_name,omitempty"`
	CustomBizIcon        string   `protobuf:"bytes,8,opt,name=custom_biz_icon,json=customBizIcon,proto3" json:"custom_biz_icon,omitempty"`
	CustomBizLevel       string   `protobuf:"bytes,9,opt,name=custom_biz_level,json=customBizLevel,proto3" json:"custom_biz_level,omitempty"`
	CustomBizNum         string   `protobuf:"bytes,10,opt,name=custom_biz_num,json=customBizNum,proto3" json:"custom_biz_num,omitempty"`
	GuildId              uint32   `protobuf:"varint,11,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestPushRichTextBreakingNewsReq) Reset()         { *m = TestPushRichTextBreakingNewsReq{} }
func (m *TestPushRichTextBreakingNewsReq) String() string { return proto.CompactTextString(m) }
func (*TestPushRichTextBreakingNewsReq) ProtoMessage()    {}
func (*TestPushRichTextBreakingNewsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{13}
}
func (m *TestPushRichTextBreakingNewsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestPushRichTextBreakingNewsReq.Unmarshal(m, b)
}
func (m *TestPushRichTextBreakingNewsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestPushRichTextBreakingNewsReq.Marshal(b, m, deterministic)
}
func (dst *TestPushRichTextBreakingNewsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestPushRichTextBreakingNewsReq.Merge(dst, src)
}
func (m *TestPushRichTextBreakingNewsReq) XXX_Size() int {
	return xxx_messageInfo_TestPushRichTextBreakingNewsReq.Size(m)
}
func (m *TestPushRichTextBreakingNewsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TestPushRichTextBreakingNewsReq.DiscardUnknown(m)
}

var xxx_messageInfo_TestPushRichTextBreakingNewsReq proto.InternalMessageInfo

func (m *TestPushRichTextBreakingNewsReq) GetNewsId() uint32 {
	if m != nil {
		return m.NewsId
	}
	return 0
}

func (m *TestPushRichTextBreakingNewsReq) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *TestPushRichTextBreakingNewsReq) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *TestPushRichTextBreakingNewsReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *TestPushRichTextBreakingNewsReq) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *TestPushRichTextBreakingNewsReq) GetCustomJumpLink() string {
	if m != nil {
		return m.CustomJumpLink
	}
	return ""
}

func (m *TestPushRichTextBreakingNewsReq) GetCustomBizName() string {
	if m != nil {
		return m.CustomBizName
	}
	return ""
}

func (m *TestPushRichTextBreakingNewsReq) GetCustomBizIcon() string {
	if m != nil {
		return m.CustomBizIcon
	}
	return ""
}

func (m *TestPushRichTextBreakingNewsReq) GetCustomBizLevel() string {
	if m != nil {
		return m.CustomBizLevel
	}
	return ""
}

func (m *TestPushRichTextBreakingNewsReq) GetCustomBizNum() string {
	if m != nil {
		return m.CustomBizNum
	}
	return ""
}

func (m *TestPushRichTextBreakingNewsReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type TestPushRichTextBreakingNewsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestPushRichTextBreakingNewsResp) Reset()         { *m = TestPushRichTextBreakingNewsResp{} }
func (m *TestPushRichTextBreakingNewsResp) String() string { return proto.CompactTextString(m) }
func (*TestPushRichTextBreakingNewsResp) ProtoMessage()    {}
func (*TestPushRichTextBreakingNewsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{14}
}
func (m *TestPushRichTextBreakingNewsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestPushRichTextBreakingNewsResp.Unmarshal(m, b)
}
func (m *TestPushRichTextBreakingNewsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestPushRichTextBreakingNewsResp.Marshal(b, m, deterministic)
}
func (dst *TestPushRichTextBreakingNewsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestPushRichTextBreakingNewsResp.Merge(dst, src)
}
func (m *TestPushRichTextBreakingNewsResp) XXX_Size() int {
	return xxx_messageInfo_TestPushRichTextBreakingNewsResp.Size(m)
}
func (m *TestPushRichTextBreakingNewsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TestPushRichTextBreakingNewsResp.DiscardUnknown(m)
}

var xxx_messageInfo_TestPushRichTextBreakingNewsResp proto.InternalMessageInfo

type BreakingNewsConfig struct {
	NewsId                uint32              `protobuf:"varint,1,opt,name=news_id,json=newsId,proto3" json:"news_id,omitempty"`
	NewsName              string              `protobuf:"bytes,2,opt,name=news_name,json=newsName,proto3" json:"news_name,omitempty"`
	NewsTemplateList      []*NewsTemplateItem `protobuf:"bytes,3,rep,name=news_template_list,json=newsTemplateList,proto3" json:"news_template_list,omitempty"`
	AnnounceScope         uint32              `protobuf:"varint,5,opt,name=announce_scope,json=announceScope,proto3" json:"announce_scope,omitempty"`
	RollingCount          uint32              `protobuf:"varint,6,opt,name=rolling_count,json=rollingCount,proto3" json:"rolling_count,omitempty"`
	RollingTime           uint32              `protobuf:"varint,7,opt,name=rolling_time,json=rollingTime,proto3" json:"rolling_time,omitempty"`
	JumpType              uint32              `protobuf:"varint,8,opt,name=jump_type,json=jumpType,proto3" json:"jump_type,omitempty"`
	MainBodyFontColor     string              `protobuf:"bytes,9,opt,name=main_body_font_color,json=mainBodyFontColor,proto3" json:"main_body_font_color,omitempty"`
	NameFontColor         string              `protobuf:"bytes,10,opt,name=name_font_color,json=nameFontColor,proto3" json:"name_font_color,omitempty"`
	FontShadowColor       string              `protobuf:"bytes,11,opt,name=font_shadow_color,json=fontShadowColor,proto3" json:"font_shadow_color,omitempty"`
	ShowToCircusee        bool                `protobuf:"varint,12,opt,name=show_to_circusee,json=showToCircusee,proto3" json:"show_to_circusee,omitempty"`
	ToCircuseeFontColor   string              `protobuf:"bytes,13,opt,name=to_circusee_font_color,json=toCircuseeFontColor,proto3" json:"to_circusee_font_color,omitempty"`
	ToCircuseeShadowColor string              `protobuf:"bytes,14,opt,name=to_circusee_shadow_color,json=toCircuseeShadowColor,proto3" json:"to_circusee_shadow_color,omitempty"`
	MarginLeft            uint32              `protobuf:"varint,15,opt,name=margin_left,json=marginLeft,proto3" json:"margin_left,omitempty"`
	AnimeType             uint32              `protobuf:"varint,16,opt,name=anime_type,json=animeType,proto3" json:"anime_type,omitempty"`
	AnimeZip              string              `protobuf:"bytes,17,opt,name=anime_zip,json=animeZip,proto3" json:"anime_zip,omitempty"`
	AnimeMd5              string              `protobuf:"bytes,21,opt,name=anime_md5,json=animeMd5,proto3" json:"anime_md5,omitempty"`
	AnimePreview          string              `protobuf:"bytes,18,opt,name=anime_preview,json=animePreview,proto3" json:"anime_preview,omitempty"`
	IsHeadNickJump        bool                `protobuf:"varint,19,opt,name=is_head_nick_jump,json=isHeadNickJump,proto3" json:"is_head_nick_jump,omitempty"`
	CustomJump            string              `protobuf:"bytes,20,opt,name=custom_jump,json=customJump,proto3" json:"custom_jump,omitempty"`
	GoToWatchText         string              `protobuf:"bytes,22,opt,name=go_to_watch_text,json=goToWatchText,proto3" json:"go_to_watch_text,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}            `json:"-"`
	XXX_unrecognized      []byte              `json:"-"`
	XXX_sizecache         int32               `json:"-"`
}

func (m *BreakingNewsConfig) Reset()         { *m = BreakingNewsConfig{} }
func (m *BreakingNewsConfig) String() string { return proto.CompactTextString(m) }
func (*BreakingNewsConfig) ProtoMessage()    {}
func (*BreakingNewsConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{15}
}
func (m *BreakingNewsConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BreakingNewsConfig.Unmarshal(m, b)
}
func (m *BreakingNewsConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BreakingNewsConfig.Marshal(b, m, deterministic)
}
func (dst *BreakingNewsConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BreakingNewsConfig.Merge(dst, src)
}
func (m *BreakingNewsConfig) XXX_Size() int {
	return xxx_messageInfo_BreakingNewsConfig.Size(m)
}
func (m *BreakingNewsConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_BreakingNewsConfig.DiscardUnknown(m)
}

var xxx_messageInfo_BreakingNewsConfig proto.InternalMessageInfo

func (m *BreakingNewsConfig) GetNewsId() uint32 {
	if m != nil {
		return m.NewsId
	}
	return 0
}

func (m *BreakingNewsConfig) GetNewsName() string {
	if m != nil {
		return m.NewsName
	}
	return ""
}

func (m *BreakingNewsConfig) GetNewsTemplateList() []*NewsTemplateItem {
	if m != nil {
		return m.NewsTemplateList
	}
	return nil
}

func (m *BreakingNewsConfig) GetAnnounceScope() uint32 {
	if m != nil {
		return m.AnnounceScope
	}
	return 0
}

func (m *BreakingNewsConfig) GetRollingCount() uint32 {
	if m != nil {
		return m.RollingCount
	}
	return 0
}

func (m *BreakingNewsConfig) GetRollingTime() uint32 {
	if m != nil {
		return m.RollingTime
	}
	return 0
}

func (m *BreakingNewsConfig) GetJumpType() uint32 {
	if m != nil {
		return m.JumpType
	}
	return 0
}

func (m *BreakingNewsConfig) GetMainBodyFontColor() string {
	if m != nil {
		return m.MainBodyFontColor
	}
	return ""
}

func (m *BreakingNewsConfig) GetNameFontColor() string {
	if m != nil {
		return m.NameFontColor
	}
	return ""
}

func (m *BreakingNewsConfig) GetFontShadowColor() string {
	if m != nil {
		return m.FontShadowColor
	}
	return ""
}

func (m *BreakingNewsConfig) GetShowToCircusee() bool {
	if m != nil {
		return m.ShowToCircusee
	}
	return false
}

func (m *BreakingNewsConfig) GetToCircuseeFontColor() string {
	if m != nil {
		return m.ToCircuseeFontColor
	}
	return ""
}

func (m *BreakingNewsConfig) GetToCircuseeShadowColor() string {
	if m != nil {
		return m.ToCircuseeShadowColor
	}
	return ""
}

func (m *BreakingNewsConfig) GetMarginLeft() uint32 {
	if m != nil {
		return m.MarginLeft
	}
	return 0
}

func (m *BreakingNewsConfig) GetAnimeType() uint32 {
	if m != nil {
		return m.AnimeType
	}
	return 0
}

func (m *BreakingNewsConfig) GetAnimeZip() string {
	if m != nil {
		return m.AnimeZip
	}
	return ""
}

func (m *BreakingNewsConfig) GetAnimeMd5() string {
	if m != nil {
		return m.AnimeMd5
	}
	return ""
}

func (m *BreakingNewsConfig) GetAnimePreview() string {
	if m != nil {
		return m.AnimePreview
	}
	return ""
}

func (m *BreakingNewsConfig) GetIsHeadNickJump() bool {
	if m != nil {
		return m.IsHeadNickJump
	}
	return false
}

func (m *BreakingNewsConfig) GetCustomJump() string {
	if m != nil {
		return m.CustomJump
	}
	return ""
}

func (m *BreakingNewsConfig) GetGoToWatchText() string {
	if m != nil {
		return m.GoToWatchText
	}
	return ""
}

type NewsTemplateItem struct {
	NewsInfoTypeList     []uint32 `protobuf:"varint,1,rep,packed,name=news_info_type_list,json=newsInfoTypeList,proto3" json:"news_info_type_list,omitempty"`
	Template             string   `protobuf:"bytes,2,opt,name=template,proto3" json:"template,omitempty"`
	Value                string   `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewsTemplateItem) Reset()         { *m = NewsTemplateItem{} }
func (m *NewsTemplateItem) String() string { return proto.CompactTextString(m) }
func (*NewsTemplateItem) ProtoMessage()    {}
func (*NewsTemplateItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{16}
}
func (m *NewsTemplateItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewsTemplateItem.Unmarshal(m, b)
}
func (m *NewsTemplateItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewsTemplateItem.Marshal(b, m, deterministic)
}
func (dst *NewsTemplateItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewsTemplateItem.Merge(dst, src)
}
func (m *NewsTemplateItem) XXX_Size() int {
	return xxx_messageInfo_NewsTemplateItem.Size(m)
}
func (m *NewsTemplateItem) XXX_DiscardUnknown() {
	xxx_messageInfo_NewsTemplateItem.DiscardUnknown(m)
}

var xxx_messageInfo_NewsTemplateItem proto.InternalMessageInfo

func (m *NewsTemplateItem) GetNewsInfoTypeList() []uint32 {
	if m != nil {
		return m.NewsInfoTypeList
	}
	return nil
}

func (m *NewsTemplateItem) GetTemplate() string {
	if m != nil {
		return m.Template
	}
	return ""
}

func (m *NewsTemplateItem) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

type AddBreakingNewsConfigReq struct {
	BreakingNewsConfig   *BreakingNewsConfig `protobuf:"bytes,1,opt,name=breaking_news_config,json=breakingNewsConfig,proto3" json:"breaking_news_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *AddBreakingNewsConfigReq) Reset()         { *m = AddBreakingNewsConfigReq{} }
func (m *AddBreakingNewsConfigReq) String() string { return proto.CompactTextString(m) }
func (*AddBreakingNewsConfigReq) ProtoMessage()    {}
func (*AddBreakingNewsConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{17}
}
func (m *AddBreakingNewsConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddBreakingNewsConfigReq.Unmarshal(m, b)
}
func (m *AddBreakingNewsConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddBreakingNewsConfigReq.Marshal(b, m, deterministic)
}
func (dst *AddBreakingNewsConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddBreakingNewsConfigReq.Merge(dst, src)
}
func (m *AddBreakingNewsConfigReq) XXX_Size() int {
	return xxx_messageInfo_AddBreakingNewsConfigReq.Size(m)
}
func (m *AddBreakingNewsConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddBreakingNewsConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddBreakingNewsConfigReq proto.InternalMessageInfo

func (m *AddBreakingNewsConfigReq) GetBreakingNewsConfig() *BreakingNewsConfig {
	if m != nil {
		return m.BreakingNewsConfig
	}
	return nil
}

type AddBreakingNewsConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddBreakingNewsConfigResp) Reset()         { *m = AddBreakingNewsConfigResp{} }
func (m *AddBreakingNewsConfigResp) String() string { return proto.CompactTextString(m) }
func (*AddBreakingNewsConfigResp) ProtoMessage()    {}
func (*AddBreakingNewsConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{18}
}
func (m *AddBreakingNewsConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddBreakingNewsConfigResp.Unmarshal(m, b)
}
func (m *AddBreakingNewsConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddBreakingNewsConfigResp.Marshal(b, m, deterministic)
}
func (dst *AddBreakingNewsConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddBreakingNewsConfigResp.Merge(dst, src)
}
func (m *AddBreakingNewsConfigResp) XXX_Size() int {
	return xxx_messageInfo_AddBreakingNewsConfigResp.Size(m)
}
func (m *AddBreakingNewsConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddBreakingNewsConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddBreakingNewsConfigResp proto.InternalMessageInfo

type UpdateBreakingNewsConfigReq struct {
	BreakingNewsConfig   *BreakingNewsConfig `protobuf:"bytes,1,opt,name=breaking_news_config,json=breakingNewsConfig,proto3" json:"breaking_news_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *UpdateBreakingNewsConfigReq) Reset()         { *m = UpdateBreakingNewsConfigReq{} }
func (m *UpdateBreakingNewsConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpdateBreakingNewsConfigReq) ProtoMessage()    {}
func (*UpdateBreakingNewsConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{19}
}
func (m *UpdateBreakingNewsConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateBreakingNewsConfigReq.Unmarshal(m, b)
}
func (m *UpdateBreakingNewsConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateBreakingNewsConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpdateBreakingNewsConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateBreakingNewsConfigReq.Merge(dst, src)
}
func (m *UpdateBreakingNewsConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpdateBreakingNewsConfigReq.Size(m)
}
func (m *UpdateBreakingNewsConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateBreakingNewsConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateBreakingNewsConfigReq proto.InternalMessageInfo

func (m *UpdateBreakingNewsConfigReq) GetBreakingNewsConfig() *BreakingNewsConfig {
	if m != nil {
		return m.BreakingNewsConfig
	}
	return nil
}

type UpdateBreakingNewsConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateBreakingNewsConfigResp) Reset()         { *m = UpdateBreakingNewsConfigResp{} }
func (m *UpdateBreakingNewsConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpdateBreakingNewsConfigResp) ProtoMessage()    {}
func (*UpdateBreakingNewsConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{20}
}
func (m *UpdateBreakingNewsConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateBreakingNewsConfigResp.Unmarshal(m, b)
}
func (m *UpdateBreakingNewsConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateBreakingNewsConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpdateBreakingNewsConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateBreakingNewsConfigResp.Merge(dst, src)
}
func (m *UpdateBreakingNewsConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpdateBreakingNewsConfigResp.Size(m)
}
func (m *UpdateBreakingNewsConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateBreakingNewsConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateBreakingNewsConfigResp proto.InternalMessageInfo

type BatchDelBreakingNewsConfigReq struct {
	NewsIdList           []uint32 `protobuf:"varint,1,rep,packed,name=news_id_list,json=newsIdList,proto3" json:"news_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDelBreakingNewsConfigReq) Reset()         { *m = BatchDelBreakingNewsConfigReq{} }
func (m *BatchDelBreakingNewsConfigReq) String() string { return proto.CompactTextString(m) }
func (*BatchDelBreakingNewsConfigReq) ProtoMessage()    {}
func (*BatchDelBreakingNewsConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{21}
}
func (m *BatchDelBreakingNewsConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelBreakingNewsConfigReq.Unmarshal(m, b)
}
func (m *BatchDelBreakingNewsConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelBreakingNewsConfigReq.Marshal(b, m, deterministic)
}
func (dst *BatchDelBreakingNewsConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelBreakingNewsConfigReq.Merge(dst, src)
}
func (m *BatchDelBreakingNewsConfigReq) XXX_Size() int {
	return xxx_messageInfo_BatchDelBreakingNewsConfigReq.Size(m)
}
func (m *BatchDelBreakingNewsConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelBreakingNewsConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelBreakingNewsConfigReq proto.InternalMessageInfo

func (m *BatchDelBreakingNewsConfigReq) GetNewsIdList() []uint32 {
	if m != nil {
		return m.NewsIdList
	}
	return nil
}

type BatchDelBreakingNewsConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDelBreakingNewsConfigResp) Reset()         { *m = BatchDelBreakingNewsConfigResp{} }
func (m *BatchDelBreakingNewsConfigResp) String() string { return proto.CompactTextString(m) }
func (*BatchDelBreakingNewsConfigResp) ProtoMessage()    {}
func (*BatchDelBreakingNewsConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{22}
}
func (m *BatchDelBreakingNewsConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelBreakingNewsConfigResp.Unmarshal(m, b)
}
func (m *BatchDelBreakingNewsConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelBreakingNewsConfigResp.Marshal(b, m, deterministic)
}
func (dst *BatchDelBreakingNewsConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelBreakingNewsConfigResp.Merge(dst, src)
}
func (m *BatchDelBreakingNewsConfigResp) XXX_Size() int {
	return xxx_messageInfo_BatchDelBreakingNewsConfigResp.Size(m)
}
func (m *BatchDelBreakingNewsConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelBreakingNewsConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelBreakingNewsConfigResp proto.InternalMessageInfo

type BatchGetBreakingNewsConfigReq struct {
	SearchNewsId         uint32   `protobuf:"varint,1,opt,name=search_news_id,json=searchNewsId,proto3" json:"search_news_id,omitempty"`
	SearchKeyword        string   `protobuf:"bytes,2,opt,name=search_keyword,json=searchKeyword,proto3" json:"search_keyword,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetBreakingNewsConfigReq) Reset()         { *m = BatchGetBreakingNewsConfigReq{} }
func (m *BatchGetBreakingNewsConfigReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetBreakingNewsConfigReq) ProtoMessage()    {}
func (*BatchGetBreakingNewsConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{23}
}
func (m *BatchGetBreakingNewsConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetBreakingNewsConfigReq.Unmarshal(m, b)
}
func (m *BatchGetBreakingNewsConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetBreakingNewsConfigReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetBreakingNewsConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetBreakingNewsConfigReq.Merge(dst, src)
}
func (m *BatchGetBreakingNewsConfigReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetBreakingNewsConfigReq.Size(m)
}
func (m *BatchGetBreakingNewsConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetBreakingNewsConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetBreakingNewsConfigReq proto.InternalMessageInfo

func (m *BatchGetBreakingNewsConfigReq) GetSearchNewsId() uint32 {
	if m != nil {
		return m.SearchNewsId
	}
	return 0
}

func (m *BatchGetBreakingNewsConfigReq) GetSearchKeyword() string {
	if m != nil {
		return m.SearchKeyword
	}
	return ""
}

type BatchGetBreakingNewsConfigResp struct {
	BreakingNewsConfigList []*BreakingNewsConfig `protobuf:"bytes,1,rep,name=breaking_news_config_list,json=breakingNewsConfigList,proto3" json:"breaking_news_config_list,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}              `json:"-"`
	XXX_unrecognized       []byte                `json:"-"`
	XXX_sizecache          int32                 `json:"-"`
}

func (m *BatchGetBreakingNewsConfigResp) Reset()         { *m = BatchGetBreakingNewsConfigResp{} }
func (m *BatchGetBreakingNewsConfigResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetBreakingNewsConfigResp) ProtoMessage()    {}
func (*BatchGetBreakingNewsConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{24}
}
func (m *BatchGetBreakingNewsConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetBreakingNewsConfigResp.Unmarshal(m, b)
}
func (m *BatchGetBreakingNewsConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetBreakingNewsConfigResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetBreakingNewsConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetBreakingNewsConfigResp.Merge(dst, src)
}
func (m *BatchGetBreakingNewsConfigResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetBreakingNewsConfigResp.Size(m)
}
func (m *BatchGetBreakingNewsConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetBreakingNewsConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetBreakingNewsConfigResp proto.InternalMessageInfo

func (m *BatchGetBreakingNewsConfigResp) GetBreakingNewsConfigList() []*BreakingNewsConfig {
	if m != nil {
		return m.BreakingNewsConfigList
	}
	return nil
}

// =================== 置顶公告相关 ==========================
type AddStickBreakingNewsReq struct {
	NewsId               uint32   `protobuf:"varint,1,opt,name=news_id,json=newsId,proto3" json:"news_id,omitempty"`
	Rank                 float32  `protobuf:"fixed32,2,opt,name=rank,proto3" json:"rank,omitempty"`
	BeginTime            int64    `protobuf:"varint,3,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddStickBreakingNewsReq) Reset()         { *m = AddStickBreakingNewsReq{} }
func (m *AddStickBreakingNewsReq) String() string { return proto.CompactTextString(m) }
func (*AddStickBreakingNewsReq) ProtoMessage()    {}
func (*AddStickBreakingNewsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{25}
}
func (m *AddStickBreakingNewsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddStickBreakingNewsReq.Unmarshal(m, b)
}
func (m *AddStickBreakingNewsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddStickBreakingNewsReq.Marshal(b, m, deterministic)
}
func (dst *AddStickBreakingNewsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddStickBreakingNewsReq.Merge(dst, src)
}
func (m *AddStickBreakingNewsReq) XXX_Size() int {
	return xxx_messageInfo_AddStickBreakingNewsReq.Size(m)
}
func (m *AddStickBreakingNewsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddStickBreakingNewsReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddStickBreakingNewsReq proto.InternalMessageInfo

func (m *AddStickBreakingNewsReq) GetNewsId() uint32 {
	if m != nil {
		return m.NewsId
	}
	return 0
}

func (m *AddStickBreakingNewsReq) GetRank() float32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *AddStickBreakingNewsReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *AddStickBreakingNewsReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

//
type AddStickBreakingNewsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddStickBreakingNewsResp) Reset()         { *m = AddStickBreakingNewsResp{} }
func (m *AddStickBreakingNewsResp) String() string { return proto.CompactTextString(m) }
func (*AddStickBreakingNewsResp) ProtoMessage()    {}
func (*AddStickBreakingNewsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{26}
}
func (m *AddStickBreakingNewsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddStickBreakingNewsResp.Unmarshal(m, b)
}
func (m *AddStickBreakingNewsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddStickBreakingNewsResp.Marshal(b, m, deterministic)
}
func (dst *AddStickBreakingNewsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddStickBreakingNewsResp.Merge(dst, src)
}
func (m *AddStickBreakingNewsResp) XXX_Size() int {
	return xxx_messageInfo_AddStickBreakingNewsResp.Size(m)
}
func (m *AddStickBreakingNewsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddStickBreakingNewsResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddStickBreakingNewsResp proto.InternalMessageInfo

// 删除置顶公告请求
type DelStickBreakingNewsReq struct {
	RecordId             uint32   `protobuf:"varint,1,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelStickBreakingNewsReq) Reset()         { *m = DelStickBreakingNewsReq{} }
func (m *DelStickBreakingNewsReq) String() string { return proto.CompactTextString(m) }
func (*DelStickBreakingNewsReq) ProtoMessage()    {}
func (*DelStickBreakingNewsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{27}
}
func (m *DelStickBreakingNewsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelStickBreakingNewsReq.Unmarshal(m, b)
}
func (m *DelStickBreakingNewsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelStickBreakingNewsReq.Marshal(b, m, deterministic)
}
func (dst *DelStickBreakingNewsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelStickBreakingNewsReq.Merge(dst, src)
}
func (m *DelStickBreakingNewsReq) XXX_Size() int {
	return xxx_messageInfo_DelStickBreakingNewsReq.Size(m)
}
func (m *DelStickBreakingNewsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelStickBreakingNewsReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelStickBreakingNewsReq proto.InternalMessageInfo

func (m *DelStickBreakingNewsReq) GetRecordId() uint32 {
	if m != nil {
		return m.RecordId
	}
	return 0
}

// 删除置顶公告返回
type DelStickBreakingNewsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelStickBreakingNewsResp) Reset()         { *m = DelStickBreakingNewsResp{} }
func (m *DelStickBreakingNewsResp) String() string { return proto.CompactTextString(m) }
func (*DelStickBreakingNewsResp) ProtoMessage()    {}
func (*DelStickBreakingNewsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{28}
}
func (m *DelStickBreakingNewsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelStickBreakingNewsResp.Unmarshal(m, b)
}
func (m *DelStickBreakingNewsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelStickBreakingNewsResp.Marshal(b, m, deterministic)
}
func (dst *DelStickBreakingNewsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelStickBreakingNewsResp.Merge(dst, src)
}
func (m *DelStickBreakingNewsResp) XXX_Size() int {
	return xxx_messageInfo_DelStickBreakingNewsResp.Size(m)
}
func (m *DelStickBreakingNewsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelStickBreakingNewsResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelStickBreakingNewsResp proto.InternalMessageInfo

// 更新置顶公告请求
type UpdateStickBreakingNewsReq struct {
	RecordId             uint32   `protobuf:"varint,1,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	Rank                 float32  `protobuf:"fixed32,2,opt,name=rank,proto3" json:"rank,omitempty"`
	BeginTime            int64    `protobuf:"varint,3,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateStickBreakingNewsReq) Reset()         { *m = UpdateStickBreakingNewsReq{} }
func (m *UpdateStickBreakingNewsReq) String() string { return proto.CompactTextString(m) }
func (*UpdateStickBreakingNewsReq) ProtoMessage()    {}
func (*UpdateStickBreakingNewsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{29}
}
func (m *UpdateStickBreakingNewsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateStickBreakingNewsReq.Unmarshal(m, b)
}
func (m *UpdateStickBreakingNewsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateStickBreakingNewsReq.Marshal(b, m, deterministic)
}
func (dst *UpdateStickBreakingNewsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateStickBreakingNewsReq.Merge(dst, src)
}
func (m *UpdateStickBreakingNewsReq) XXX_Size() int {
	return xxx_messageInfo_UpdateStickBreakingNewsReq.Size(m)
}
func (m *UpdateStickBreakingNewsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateStickBreakingNewsReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateStickBreakingNewsReq proto.InternalMessageInfo

func (m *UpdateStickBreakingNewsReq) GetRecordId() uint32 {
	if m != nil {
		return m.RecordId
	}
	return 0
}

func (m *UpdateStickBreakingNewsReq) GetRank() float32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *UpdateStickBreakingNewsReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *UpdateStickBreakingNewsReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

// 更新置顶公告返回
type UpdateStickBreakingNewsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateStickBreakingNewsResp) Reset()         { *m = UpdateStickBreakingNewsResp{} }
func (m *UpdateStickBreakingNewsResp) String() string { return proto.CompactTextString(m) }
func (*UpdateStickBreakingNewsResp) ProtoMessage()    {}
func (*UpdateStickBreakingNewsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{30}
}
func (m *UpdateStickBreakingNewsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateStickBreakingNewsResp.Unmarshal(m, b)
}
func (m *UpdateStickBreakingNewsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateStickBreakingNewsResp.Marshal(b, m, deterministic)
}
func (dst *UpdateStickBreakingNewsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateStickBreakingNewsResp.Merge(dst, src)
}
func (m *UpdateStickBreakingNewsResp) XXX_Size() int {
	return xxx_messageInfo_UpdateStickBreakingNewsResp.Size(m)
}
func (m *UpdateStickBreakingNewsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateStickBreakingNewsResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateStickBreakingNewsResp proto.InternalMessageInfo

// 获取全部置顶公告记录
type GetAllStickBreakingNewsReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllStickBreakingNewsReq) Reset()         { *m = GetAllStickBreakingNewsReq{} }
func (m *GetAllStickBreakingNewsReq) String() string { return proto.CompactTextString(m) }
func (*GetAllStickBreakingNewsReq) ProtoMessage()    {}
func (*GetAllStickBreakingNewsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{31}
}
func (m *GetAllStickBreakingNewsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllStickBreakingNewsReq.Unmarshal(m, b)
}
func (m *GetAllStickBreakingNewsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllStickBreakingNewsReq.Marshal(b, m, deterministic)
}
func (dst *GetAllStickBreakingNewsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllStickBreakingNewsReq.Merge(dst, src)
}
func (m *GetAllStickBreakingNewsReq) XXX_Size() int {
	return xxx_messageInfo_GetAllStickBreakingNewsReq.Size(m)
}
func (m *GetAllStickBreakingNewsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllStickBreakingNewsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllStickBreakingNewsReq proto.InternalMessageInfo

// 置顶公告
type StickBreakingNews struct {
	RecordId             uint32      `protobuf:"varint,1,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	NewsId               uint32      `protobuf:"varint,2,opt,name=news_id,json=newsId,proto3" json:"news_id,omitempty"`
	NewsName             string      `protobuf:"bytes,3,opt,name=news_name,json=newsName,proto3" json:"news_name,omitempty"`
	AnimeName            string      `protobuf:"bytes,4,opt,name=anime_name,json=animeName,proto3" json:"anime_name,omitempty"`
	AnimeZip             string      `protobuf:"bytes,5,opt,name=anime_zip,json=animeZip,proto3" json:"anime_zip,omitempty"`
	AnimeType            uint32      `protobuf:"varint,6,opt,name=anime_type,json=animeType,proto3" json:"anime_type,omitempty"`
	AnimePreview         string      `protobuf:"bytes,7,opt,name=anime_preview,json=animePreview,proto3" json:"anime_preview,omitempty"`
	Rank                 string      `protobuf:"bytes,8,opt,name=rank,proto3" json:"rank,omitempty"`
	BeginTime            int64       `protobuf:"varint,9,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64       `protobuf:"varint,10,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Status               StickStatus `protobuf:"varint,11,opt,name=status,proto3,enum=public_notice.StickStatus" json:"status,omitempty"`
	IsPermanent          bool        `protobuf:"varint,12,opt,name=is_permanent,json=isPermanent,proto3" json:"is_permanent,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *StickBreakingNews) Reset()         { *m = StickBreakingNews{} }
func (m *StickBreakingNews) String() string { return proto.CompactTextString(m) }
func (*StickBreakingNews) ProtoMessage()    {}
func (*StickBreakingNews) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{32}
}
func (m *StickBreakingNews) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StickBreakingNews.Unmarshal(m, b)
}
func (m *StickBreakingNews) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StickBreakingNews.Marshal(b, m, deterministic)
}
func (dst *StickBreakingNews) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StickBreakingNews.Merge(dst, src)
}
func (m *StickBreakingNews) XXX_Size() int {
	return xxx_messageInfo_StickBreakingNews.Size(m)
}
func (m *StickBreakingNews) XXX_DiscardUnknown() {
	xxx_messageInfo_StickBreakingNews.DiscardUnknown(m)
}

var xxx_messageInfo_StickBreakingNews proto.InternalMessageInfo

func (m *StickBreakingNews) GetRecordId() uint32 {
	if m != nil {
		return m.RecordId
	}
	return 0
}

func (m *StickBreakingNews) GetNewsId() uint32 {
	if m != nil {
		return m.NewsId
	}
	return 0
}

func (m *StickBreakingNews) GetNewsName() string {
	if m != nil {
		return m.NewsName
	}
	return ""
}

func (m *StickBreakingNews) GetAnimeName() string {
	if m != nil {
		return m.AnimeName
	}
	return ""
}

func (m *StickBreakingNews) GetAnimeZip() string {
	if m != nil {
		return m.AnimeZip
	}
	return ""
}

func (m *StickBreakingNews) GetAnimeType() uint32 {
	if m != nil {
		return m.AnimeType
	}
	return 0
}

func (m *StickBreakingNews) GetAnimePreview() string {
	if m != nil {
		return m.AnimePreview
	}
	return ""
}

func (m *StickBreakingNews) GetRank() string {
	if m != nil {
		return m.Rank
	}
	return ""
}

func (m *StickBreakingNews) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *StickBreakingNews) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *StickBreakingNews) GetStatus() StickStatus {
	if m != nil {
		return m.Status
	}
	return StickStatus_STICK_STATUS_UNSPECIFIED
}

func (m *StickBreakingNews) GetIsPermanent() bool {
	if m != nil {
		return m.IsPermanent
	}
	return false
}

// 获取置顶公告返回
type GetAllStickBreakingNewsResp struct {
	StickBreakingNewsList []*StickBreakingNews `protobuf:"bytes,1,rep,name=stick_breaking_news_list,json=stickBreakingNewsList,proto3" json:"stick_breaking_news_list,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}             `json:"-"`
	XXX_unrecognized      []byte               `json:"-"`
	XXX_sizecache         int32                `json:"-"`
}

func (m *GetAllStickBreakingNewsResp) Reset()         { *m = GetAllStickBreakingNewsResp{} }
func (m *GetAllStickBreakingNewsResp) String() string { return proto.CompactTextString(m) }
func (*GetAllStickBreakingNewsResp) ProtoMessage()    {}
func (*GetAllStickBreakingNewsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{33}
}
func (m *GetAllStickBreakingNewsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllStickBreakingNewsResp.Unmarshal(m, b)
}
func (m *GetAllStickBreakingNewsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllStickBreakingNewsResp.Marshal(b, m, deterministic)
}
func (dst *GetAllStickBreakingNewsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllStickBreakingNewsResp.Merge(dst, src)
}
func (m *GetAllStickBreakingNewsResp) XXX_Size() int {
	return xxx_messageInfo_GetAllStickBreakingNewsResp.Size(m)
}
func (m *GetAllStickBreakingNewsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllStickBreakingNewsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllStickBreakingNewsResp proto.InternalMessageInfo

func (m *GetAllStickBreakingNewsResp) GetStickBreakingNewsList() []*StickBreakingNews {
	if m != nil {
		return m.StickBreakingNewsList
	}
	return nil
}

// 检查是否能操作置顶公告请求
type CheckIfCouldOperateStickBreakingNewsReq struct {
	NewsId               uint32   `protobuf:"varint,1,opt,name=news_id,json=newsId,proto3" json:"news_id,omitempty"`
	RecordId             uint32   `protobuf:"varint,2,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	BeginTime            int64    `protobuf:"varint,3,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckIfCouldOperateStickBreakingNewsReq) Reset() {
	*m = CheckIfCouldOperateStickBreakingNewsReq{}
}
func (m *CheckIfCouldOperateStickBreakingNewsReq) String() string { return proto.CompactTextString(m) }
func (*CheckIfCouldOperateStickBreakingNewsReq) ProtoMessage()    {}
func (*CheckIfCouldOperateStickBreakingNewsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{34}
}
func (m *CheckIfCouldOperateStickBreakingNewsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckIfCouldOperateStickBreakingNewsReq.Unmarshal(m, b)
}
func (m *CheckIfCouldOperateStickBreakingNewsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckIfCouldOperateStickBreakingNewsReq.Marshal(b, m, deterministic)
}
func (dst *CheckIfCouldOperateStickBreakingNewsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckIfCouldOperateStickBreakingNewsReq.Merge(dst, src)
}
func (m *CheckIfCouldOperateStickBreakingNewsReq) XXX_Size() int {
	return xxx_messageInfo_CheckIfCouldOperateStickBreakingNewsReq.Size(m)
}
func (m *CheckIfCouldOperateStickBreakingNewsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckIfCouldOperateStickBreakingNewsReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckIfCouldOperateStickBreakingNewsReq proto.InternalMessageInfo

func (m *CheckIfCouldOperateStickBreakingNewsReq) GetNewsId() uint32 {
	if m != nil {
		return m.NewsId
	}
	return 0
}

func (m *CheckIfCouldOperateStickBreakingNewsReq) GetRecordId() uint32 {
	if m != nil {
		return m.RecordId
	}
	return 0
}

func (m *CheckIfCouldOperateStickBreakingNewsReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *CheckIfCouldOperateStickBreakingNewsReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

// 失败直接接口报错
type CheckIfCouldOperateStickBreakingNewsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckIfCouldOperateStickBreakingNewsResp) Reset() {
	*m = CheckIfCouldOperateStickBreakingNewsResp{}
}
func (m *CheckIfCouldOperateStickBreakingNewsResp) String() string { return proto.CompactTextString(m) }
func (*CheckIfCouldOperateStickBreakingNewsResp) ProtoMessage()    {}
func (*CheckIfCouldOperateStickBreakingNewsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_notice_c71e307d33723c97, []int{35}
}
func (m *CheckIfCouldOperateStickBreakingNewsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckIfCouldOperateStickBreakingNewsResp.Unmarshal(m, b)
}
func (m *CheckIfCouldOperateStickBreakingNewsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckIfCouldOperateStickBreakingNewsResp.Marshal(b, m, deterministic)
}
func (dst *CheckIfCouldOperateStickBreakingNewsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckIfCouldOperateStickBreakingNewsResp.Merge(dst, src)
}
func (m *CheckIfCouldOperateStickBreakingNewsResp) XXX_Size() int {
	return xxx_messageInfo_CheckIfCouldOperateStickBreakingNewsResp.Size(m)
}
func (m *CheckIfCouldOperateStickBreakingNewsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckIfCouldOperateStickBreakingNewsResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckIfCouldOperateStickBreakingNewsResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*CommonPublicMsgPushReq)(nil), "public_notice.CommonPublicMsgPushReq")
	proto.RegisterType((*CommonPublicMsgPushResp)(nil), "public_notice.CommonPublicMsgPushResp")
	proto.RegisterType((*PushBreakingNewsReq)(nil), "public_notice.PushBreakingNewsReq")
	proto.RegisterType((*RichTextNews)(nil), "public_notice.RichTextNews")
	proto.RegisterType((*PushBreakingNewsResp)(nil), "public_notice.PushBreakingNewsResp")
	proto.RegisterType((*SmashEggBreakingNews)(nil), "public_notice.SmashEggBreakingNews")
	proto.RegisterType((*UserInfo)(nil), "public_notice.UserInfo")
	proto.RegisterType((*ChannelInfo)(nil), "public_notice.ChannelInfo")
	proto.RegisterType((*GuildInfo)(nil), "public_notice.GuildInfo")
	proto.RegisterType((*CommonBreakingNewsV3)(nil), "public_notice.CommonBreakingNewsV3")
	proto.RegisterType((*CommBreakingNewsBaseOpt)(nil), "public_notice.CommBreakingNewsBaseOpt")
	proto.RegisterType((*PresentBreakingNewsBaseOpt)(nil), "public_notice.PresentBreakingNewsBaseOpt")
	proto.RegisterType((*RushInfo)(nil), "public_notice.RushInfo")
	proto.RegisterType((*TestPushRichTextBreakingNewsReq)(nil), "public_notice.TestPushRichTextBreakingNewsReq")
	proto.RegisterType((*TestPushRichTextBreakingNewsResp)(nil), "public_notice.TestPushRichTextBreakingNewsResp")
	proto.RegisterType((*BreakingNewsConfig)(nil), "public_notice.BreakingNewsConfig")
	proto.RegisterType((*NewsTemplateItem)(nil), "public_notice.NewsTemplateItem")
	proto.RegisterType((*AddBreakingNewsConfigReq)(nil), "public_notice.AddBreakingNewsConfigReq")
	proto.RegisterType((*AddBreakingNewsConfigResp)(nil), "public_notice.AddBreakingNewsConfigResp")
	proto.RegisterType((*UpdateBreakingNewsConfigReq)(nil), "public_notice.UpdateBreakingNewsConfigReq")
	proto.RegisterType((*UpdateBreakingNewsConfigResp)(nil), "public_notice.UpdateBreakingNewsConfigResp")
	proto.RegisterType((*BatchDelBreakingNewsConfigReq)(nil), "public_notice.BatchDelBreakingNewsConfigReq")
	proto.RegisterType((*BatchDelBreakingNewsConfigResp)(nil), "public_notice.BatchDelBreakingNewsConfigResp")
	proto.RegisterType((*BatchGetBreakingNewsConfigReq)(nil), "public_notice.BatchGetBreakingNewsConfigReq")
	proto.RegisterType((*BatchGetBreakingNewsConfigResp)(nil), "public_notice.BatchGetBreakingNewsConfigResp")
	proto.RegisterType((*AddStickBreakingNewsReq)(nil), "public_notice.AddStickBreakingNewsReq")
	proto.RegisterType((*AddStickBreakingNewsResp)(nil), "public_notice.AddStickBreakingNewsResp")
	proto.RegisterType((*DelStickBreakingNewsReq)(nil), "public_notice.DelStickBreakingNewsReq")
	proto.RegisterType((*DelStickBreakingNewsResp)(nil), "public_notice.DelStickBreakingNewsResp")
	proto.RegisterType((*UpdateStickBreakingNewsReq)(nil), "public_notice.UpdateStickBreakingNewsReq")
	proto.RegisterType((*UpdateStickBreakingNewsResp)(nil), "public_notice.UpdateStickBreakingNewsResp")
	proto.RegisterType((*GetAllStickBreakingNewsReq)(nil), "public_notice.GetAllStickBreakingNewsReq")
	proto.RegisterType((*StickBreakingNews)(nil), "public_notice.StickBreakingNews")
	proto.RegisterType((*GetAllStickBreakingNewsResp)(nil), "public_notice.GetAllStickBreakingNewsResp")
	proto.RegisterType((*CheckIfCouldOperateStickBreakingNewsReq)(nil), "public_notice.CheckIfCouldOperateStickBreakingNewsReq")
	proto.RegisterType((*CheckIfCouldOperateStickBreakingNewsResp)(nil), "public_notice.CheckIfCouldOperateStickBreakingNewsResp")
	proto.RegisterEnum("public_notice.StickStatus", StickStatus_name, StickStatus_value)
	proto.RegisterEnum("public_notice.PushBreakingNewsReq_BREAKING_CMD_TYPE", PushBreakingNewsReq_BREAKING_CMD_TYPE_name, PushBreakingNewsReq_BREAKING_CMD_TYPE_value)
	proto.RegisterEnum("public_notice.BreakingNewsConfig_NEWS_INFO_TYPE", BreakingNewsConfig_NEWS_INFO_TYPE_name, BreakingNewsConfig_NEWS_INFO_TYPE_value)
	proto.RegisterEnum("public_notice.BreakingNewsConfig_ANIME_TYPE", BreakingNewsConfig_ANIME_TYPE_name, BreakingNewsConfig_ANIME_TYPE_value)
	proto.RegisterEnum("public_notice.BreakingNewsConfig_ANNOUNCE_SCOPE", BreakingNewsConfig_ANNOUNCE_SCOPE_name, BreakingNewsConfig_ANNOUNCE_SCOPE_value)
	proto.RegisterEnum("public_notice.BreakingNewsConfig_ANNOUNCE_POSITION", BreakingNewsConfig_ANNOUNCE_POSITION_name, BreakingNewsConfig_ANNOUNCE_POSITION_value)
	proto.RegisterEnum("public_notice.BreakingNewsConfig_JUMP_TYPE", BreakingNewsConfig_JUMP_TYPE_name, BreakingNewsConfig_JUMP_TYPE_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PublicNoticeClient is the client API for PublicNotice service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PublicNoticeClient interface {
	PushBreakingNews(ctx context.Context, in *PushBreakingNewsReq, opts ...grpc.CallOption) (*PushBreakingNewsResp, error)
	TestPushRichTextBreakingNews(ctx context.Context, in *TestPushRichTextBreakingNewsReq, opts ...grpc.CallOption) (*TestPushRichTextBreakingNewsResp, error)
	// =================== 运营后台相关 ==========================
	AddBreakingNewsConfig(ctx context.Context, in *AddBreakingNewsConfigReq, opts ...grpc.CallOption) (*AddBreakingNewsConfigResp, error)
	UpdateBreakingNewsConfig(ctx context.Context, in *UpdateBreakingNewsConfigReq, opts ...grpc.CallOption) (*UpdateBreakingNewsConfigResp, error)
	BatchDelBreakingNewsConfig(ctx context.Context, in *BatchDelBreakingNewsConfigReq, opts ...grpc.CallOption) (*BatchDelBreakingNewsConfigResp, error)
	BatchGetBreakingNewsConfig(ctx context.Context, in *BatchGetBreakingNewsConfigReq, opts ...grpc.CallOption) (*BatchGetBreakingNewsConfigResp, error)
	// =================== 置顶公告相关 ==========================
	AddStickBreakingNews(ctx context.Context, in *AddStickBreakingNewsReq, opts ...grpc.CallOption) (*AddStickBreakingNewsResp, error)
	DelStickBreakingNews(ctx context.Context, in *DelStickBreakingNewsReq, opts ...grpc.CallOption) (*DelStickBreakingNewsResp, error)
	UpdateStickBreakingNews(ctx context.Context, in *UpdateStickBreakingNewsReq, opts ...grpc.CallOption) (*UpdateStickBreakingNewsResp, error)
	// 获取全部置顶公告记录
	GetAllStickBreakingNews(ctx context.Context, in *GetAllStickBreakingNewsReq, opts ...grpc.CallOption) (*GetAllStickBreakingNewsResp, error)
	// 检查是否能操作置顶公告
	CheckIfCouldOperateStickBreakingNews(ctx context.Context, in *CheckIfCouldOperateStickBreakingNewsReq, opts ...grpc.CallOption) (*CheckIfCouldOperateStickBreakingNewsResp, error)
	// =================== 置顶公告相关 ==========================
	// 通用全服推送
	CommonPublicMsgPush(ctx context.Context, in *CommonPublicMsgPushReq, opts ...grpc.CallOption) (*CommonPublicMsgPushResp, error)
}

type publicNoticeClient struct {
	cc *grpc.ClientConn
}

func NewPublicNoticeClient(cc *grpc.ClientConn) PublicNoticeClient {
	return &publicNoticeClient{cc}
}

func (c *publicNoticeClient) PushBreakingNews(ctx context.Context, in *PushBreakingNewsReq, opts ...grpc.CallOption) (*PushBreakingNewsResp, error) {
	out := new(PushBreakingNewsResp)
	err := c.cc.Invoke(ctx, "/public_notice.PublicNotice/PushBreakingNews", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicNoticeClient) TestPushRichTextBreakingNews(ctx context.Context, in *TestPushRichTextBreakingNewsReq, opts ...grpc.CallOption) (*TestPushRichTextBreakingNewsResp, error) {
	out := new(TestPushRichTextBreakingNewsResp)
	err := c.cc.Invoke(ctx, "/public_notice.PublicNotice/TestPushRichTextBreakingNews", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicNoticeClient) AddBreakingNewsConfig(ctx context.Context, in *AddBreakingNewsConfigReq, opts ...grpc.CallOption) (*AddBreakingNewsConfigResp, error) {
	out := new(AddBreakingNewsConfigResp)
	err := c.cc.Invoke(ctx, "/public_notice.PublicNotice/AddBreakingNewsConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicNoticeClient) UpdateBreakingNewsConfig(ctx context.Context, in *UpdateBreakingNewsConfigReq, opts ...grpc.CallOption) (*UpdateBreakingNewsConfigResp, error) {
	out := new(UpdateBreakingNewsConfigResp)
	err := c.cc.Invoke(ctx, "/public_notice.PublicNotice/UpdateBreakingNewsConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicNoticeClient) BatchDelBreakingNewsConfig(ctx context.Context, in *BatchDelBreakingNewsConfigReq, opts ...grpc.CallOption) (*BatchDelBreakingNewsConfigResp, error) {
	out := new(BatchDelBreakingNewsConfigResp)
	err := c.cc.Invoke(ctx, "/public_notice.PublicNotice/BatchDelBreakingNewsConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicNoticeClient) BatchGetBreakingNewsConfig(ctx context.Context, in *BatchGetBreakingNewsConfigReq, opts ...grpc.CallOption) (*BatchGetBreakingNewsConfigResp, error) {
	out := new(BatchGetBreakingNewsConfigResp)
	err := c.cc.Invoke(ctx, "/public_notice.PublicNotice/BatchGetBreakingNewsConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicNoticeClient) AddStickBreakingNews(ctx context.Context, in *AddStickBreakingNewsReq, opts ...grpc.CallOption) (*AddStickBreakingNewsResp, error) {
	out := new(AddStickBreakingNewsResp)
	err := c.cc.Invoke(ctx, "/public_notice.PublicNotice/AddStickBreakingNews", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicNoticeClient) DelStickBreakingNews(ctx context.Context, in *DelStickBreakingNewsReq, opts ...grpc.CallOption) (*DelStickBreakingNewsResp, error) {
	out := new(DelStickBreakingNewsResp)
	err := c.cc.Invoke(ctx, "/public_notice.PublicNotice/DelStickBreakingNews", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicNoticeClient) UpdateStickBreakingNews(ctx context.Context, in *UpdateStickBreakingNewsReq, opts ...grpc.CallOption) (*UpdateStickBreakingNewsResp, error) {
	out := new(UpdateStickBreakingNewsResp)
	err := c.cc.Invoke(ctx, "/public_notice.PublicNotice/UpdateStickBreakingNews", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicNoticeClient) GetAllStickBreakingNews(ctx context.Context, in *GetAllStickBreakingNewsReq, opts ...grpc.CallOption) (*GetAllStickBreakingNewsResp, error) {
	out := new(GetAllStickBreakingNewsResp)
	err := c.cc.Invoke(ctx, "/public_notice.PublicNotice/GetAllStickBreakingNews", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicNoticeClient) CheckIfCouldOperateStickBreakingNews(ctx context.Context, in *CheckIfCouldOperateStickBreakingNewsReq, opts ...grpc.CallOption) (*CheckIfCouldOperateStickBreakingNewsResp, error) {
	out := new(CheckIfCouldOperateStickBreakingNewsResp)
	err := c.cc.Invoke(ctx, "/public_notice.PublicNotice/CheckIfCouldOperateStickBreakingNews", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicNoticeClient) CommonPublicMsgPush(ctx context.Context, in *CommonPublicMsgPushReq, opts ...grpc.CallOption) (*CommonPublicMsgPushResp, error) {
	out := new(CommonPublicMsgPushResp)
	err := c.cc.Invoke(ctx, "/public_notice.PublicNotice/CommonPublicMsgPush", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PublicNoticeServer is the server API for PublicNotice service.
type PublicNoticeServer interface {
	PushBreakingNews(context.Context, *PushBreakingNewsReq) (*PushBreakingNewsResp, error)
	TestPushRichTextBreakingNews(context.Context, *TestPushRichTextBreakingNewsReq) (*TestPushRichTextBreakingNewsResp, error)
	// =================== 运营后台相关 ==========================
	AddBreakingNewsConfig(context.Context, *AddBreakingNewsConfigReq) (*AddBreakingNewsConfigResp, error)
	UpdateBreakingNewsConfig(context.Context, *UpdateBreakingNewsConfigReq) (*UpdateBreakingNewsConfigResp, error)
	BatchDelBreakingNewsConfig(context.Context, *BatchDelBreakingNewsConfigReq) (*BatchDelBreakingNewsConfigResp, error)
	BatchGetBreakingNewsConfig(context.Context, *BatchGetBreakingNewsConfigReq) (*BatchGetBreakingNewsConfigResp, error)
	// =================== 置顶公告相关 ==========================
	AddStickBreakingNews(context.Context, *AddStickBreakingNewsReq) (*AddStickBreakingNewsResp, error)
	DelStickBreakingNews(context.Context, *DelStickBreakingNewsReq) (*DelStickBreakingNewsResp, error)
	UpdateStickBreakingNews(context.Context, *UpdateStickBreakingNewsReq) (*UpdateStickBreakingNewsResp, error)
	// 获取全部置顶公告记录
	GetAllStickBreakingNews(context.Context, *GetAllStickBreakingNewsReq) (*GetAllStickBreakingNewsResp, error)
	// 检查是否能操作置顶公告
	CheckIfCouldOperateStickBreakingNews(context.Context, *CheckIfCouldOperateStickBreakingNewsReq) (*CheckIfCouldOperateStickBreakingNewsResp, error)
	// =================== 置顶公告相关 ==========================
	// 通用全服推送
	CommonPublicMsgPush(context.Context, *CommonPublicMsgPushReq) (*CommonPublicMsgPushResp, error)
}

func RegisterPublicNoticeServer(s *grpc.Server, srv PublicNoticeServer) {
	s.RegisterService(&_PublicNotice_serviceDesc, srv)
}

func _PublicNotice_PushBreakingNews_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushBreakingNewsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicNoticeServer).PushBreakingNews(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/public_notice.PublicNotice/PushBreakingNews",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicNoticeServer).PushBreakingNews(ctx, req.(*PushBreakingNewsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicNotice_TestPushRichTextBreakingNews_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestPushRichTextBreakingNewsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicNoticeServer).TestPushRichTextBreakingNews(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/public_notice.PublicNotice/TestPushRichTextBreakingNews",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicNoticeServer).TestPushRichTextBreakingNews(ctx, req.(*TestPushRichTextBreakingNewsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicNotice_AddBreakingNewsConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddBreakingNewsConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicNoticeServer).AddBreakingNewsConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/public_notice.PublicNotice/AddBreakingNewsConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicNoticeServer).AddBreakingNewsConfig(ctx, req.(*AddBreakingNewsConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicNotice_UpdateBreakingNewsConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBreakingNewsConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicNoticeServer).UpdateBreakingNewsConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/public_notice.PublicNotice/UpdateBreakingNewsConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicNoticeServer).UpdateBreakingNewsConfig(ctx, req.(*UpdateBreakingNewsConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicNotice_BatchDelBreakingNewsConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDelBreakingNewsConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicNoticeServer).BatchDelBreakingNewsConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/public_notice.PublicNotice/BatchDelBreakingNewsConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicNoticeServer).BatchDelBreakingNewsConfig(ctx, req.(*BatchDelBreakingNewsConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicNotice_BatchGetBreakingNewsConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetBreakingNewsConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicNoticeServer).BatchGetBreakingNewsConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/public_notice.PublicNotice/BatchGetBreakingNewsConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicNoticeServer).BatchGetBreakingNewsConfig(ctx, req.(*BatchGetBreakingNewsConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicNotice_AddStickBreakingNews_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddStickBreakingNewsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicNoticeServer).AddStickBreakingNews(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/public_notice.PublicNotice/AddStickBreakingNews",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicNoticeServer).AddStickBreakingNews(ctx, req.(*AddStickBreakingNewsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicNotice_DelStickBreakingNews_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelStickBreakingNewsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicNoticeServer).DelStickBreakingNews(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/public_notice.PublicNotice/DelStickBreakingNews",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicNoticeServer).DelStickBreakingNews(ctx, req.(*DelStickBreakingNewsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicNotice_UpdateStickBreakingNews_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateStickBreakingNewsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicNoticeServer).UpdateStickBreakingNews(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/public_notice.PublicNotice/UpdateStickBreakingNews",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicNoticeServer).UpdateStickBreakingNews(ctx, req.(*UpdateStickBreakingNewsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicNotice_GetAllStickBreakingNews_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllStickBreakingNewsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicNoticeServer).GetAllStickBreakingNews(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/public_notice.PublicNotice/GetAllStickBreakingNews",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicNoticeServer).GetAllStickBreakingNews(ctx, req.(*GetAllStickBreakingNewsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicNotice_CheckIfCouldOperateStickBreakingNews_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckIfCouldOperateStickBreakingNewsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicNoticeServer).CheckIfCouldOperateStickBreakingNews(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/public_notice.PublicNotice/CheckIfCouldOperateStickBreakingNews",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicNoticeServer).CheckIfCouldOperateStickBreakingNews(ctx, req.(*CheckIfCouldOperateStickBreakingNewsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicNotice_CommonPublicMsgPush_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonPublicMsgPushReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicNoticeServer).CommonPublicMsgPush(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/public_notice.PublicNotice/CommonPublicMsgPush",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicNoticeServer).CommonPublicMsgPush(ctx, req.(*CommonPublicMsgPushReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _PublicNotice_serviceDesc = grpc.ServiceDesc{
	ServiceName: "public_notice.PublicNotice",
	HandlerType: (*PublicNoticeServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PushBreakingNews",
			Handler:    _PublicNotice_PushBreakingNews_Handler,
		},
		{
			MethodName: "TestPushRichTextBreakingNews",
			Handler:    _PublicNotice_TestPushRichTextBreakingNews_Handler,
		},
		{
			MethodName: "AddBreakingNewsConfig",
			Handler:    _PublicNotice_AddBreakingNewsConfig_Handler,
		},
		{
			MethodName: "UpdateBreakingNewsConfig",
			Handler:    _PublicNotice_UpdateBreakingNewsConfig_Handler,
		},
		{
			MethodName: "BatchDelBreakingNewsConfig",
			Handler:    _PublicNotice_BatchDelBreakingNewsConfig_Handler,
		},
		{
			MethodName: "BatchGetBreakingNewsConfig",
			Handler:    _PublicNotice_BatchGetBreakingNewsConfig_Handler,
		},
		{
			MethodName: "AddStickBreakingNews",
			Handler:    _PublicNotice_AddStickBreakingNews_Handler,
		},
		{
			MethodName: "DelStickBreakingNews",
			Handler:    _PublicNotice_DelStickBreakingNews_Handler,
		},
		{
			MethodName: "UpdateStickBreakingNews",
			Handler:    _PublicNotice_UpdateStickBreakingNews_Handler,
		},
		{
			MethodName: "GetAllStickBreakingNews",
			Handler:    _PublicNotice_GetAllStickBreakingNews_Handler,
		},
		{
			MethodName: "CheckIfCouldOperateStickBreakingNews",
			Handler:    _PublicNotice_CheckIfCouldOperateStickBreakingNews_Handler,
		},
		{
			MethodName: "CommonPublicMsgPush",
			Handler:    _PublicNotice_CommonPublicMsgPush_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/public-notice/public-notice.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/public-notice/public-notice.proto", fileDescriptor_public_notice_c71e307d33723c97)
}

var fileDescriptor_public_notice_c71e307d33723c97 = []byte{
	// 3235 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x5a, 0xcd, 0x73, 0xdb, 0x58,
	0x72, 0x37, 0x45, 0x7d, 0x90, 0x2d, 0x92, 0x86, 0x9e, 0xbe, 0x28, 0xca, 0xb2, 0x65, 0xf8, 0x4b,
	0xf6, 0x64, 0xe4, 0x8c, 0x1d, 0xcf, 0xe4, 0x92, 0x03, 0x45, 0x51, 0x36, 0xc6, 0x22, 0xc8, 0x02,
	0x49, 0x7b, 0x66, 0x32, 0x29, 0x04, 0x02, 0x9e, 0x48, 0x44, 0x20, 0x80, 0x01, 0x40, 0xcb, 0x9e,
	0xa9, 0x4a, 0x65, 0x4e, 0x49, 0x2a, 0x95, 0x4a, 0x32, 0x39, 0x4d, 0xaa, 0xf2, 0x1f, 0xec, 0xee,
	0x79, 0x6f, 0x5b, 0x7b, 0xdb, 0xef, 0xaf, 0xeb, 0xfe, 0x1b, 0xfb, 0x71, 0xdd, 0x7a, 0xfd, 0x00,
	0x12, 0x20, 0x41, 0x59, 0x33, 0xb5, 0xb5, 0x37, 0xbd, 0x5f, 0x37, 0xba, 0xfb, 0x75, 0xbf, 0xd7,
	0xfd, 0x03, 0x28, 0xf8, 0xeb, 0x20, 0x78, 0xf8, 0xd9, 0xd0, 0xd4, 0xcf, 0x7c, 0xd3, 0x7a, 0x45,
	0xbd, 0x87, 0xee, 0xf0, 0xc4, 0x32, 0xf5, 0x77, 0x6d, 0x27, 0x30, 0x75, 0x9a, 0x5c, 0xed, 0xbb,
	0x9e, 0x13, 0x38, 0xa4, 0xc8, 0x41, 0x95, 0x83, 0xe2, 0x10, 0x36, 0x6a, 0xce, 0x60, 0xe0, 0xd8,
	0x2d, 0x84, 0x1b, 0x7e, 0xaf, 0x35, 0xf4, 0xfb, 0x0a, 0xfd, 0x8c, 0x6c, 0x41, 0xce, 0x71, 0x03,
	0xd5, 0xd0, 0x02, 0xad, 0x9c, 0xd9, 0xcd, 0xec, 0x15, 0x94, 0x25, 0xc7, 0x0d, 0x0e, 0xb5, 0x40,
	0x23, 0x77, 0xa0, 0xa4, 0xd9, 0xb6, 0x33, 0xb4, 0x75, 0xaa, 0xfa, 0xba, 0xe3, 0xd2, 0xf2, 0xdc,
	0x6e, 0x66, 0xaf, 0xa8, 0x14, 0x23, 0xb4, 0xcd, 0x40, 0x66, 0x41, 0x1f, 0x18, 0x6a, 0xf0, 0xc6,
	0xa5, 0xe5, 0x2c, 0x2a, 0x2c, 0xe9, 0x03, 0xa3, 0xf3, 0xc6, 0xa5, 0xe2, 0x16, 0x6c, 0xa6, 0xba,
	0xf5, 0x5d, 0xf1, 0xfb, 0x59, 0x58, 0x65, 0x8b, 0x03, 0x8f, 0x6a, 0x67, 0xa6, 0xdd, 0x93, 0xe9,
	0xb9, 0xcf, 0xe2, 0xf9, 0x47, 0x58, 0x39, 0x09, 0x21, 0x75, 0x64, 0x96, 0x05, 0x56, 0x7a, 0xf4,
	0x37, 0xfb, 0x89, 0x4d, 0xed, 0xa7, 0x3c, 0xbe, 0x7f, 0xa0, 0xd4, 0xab, 0xcf, 0x25, 0xf9, 0xa9,
	0x5a, 0x6b, 0x1c, 0xaa, 0x9d, 0x8f, 0x5b, 0x75, 0xe5, 0x6a, 0x64, 0xae, 0xc6, 0x83, 0x22, 0x5d,
	0x58, 0xd3, 0x31, 0x28, 0x75, 0xe4, 0xc8, 0xa6, 0xe7, 0x3e, 0x6e, 0x6e, 0xf9, 0xd1, 0xad, 0x09,
	0x27, 0x3c, 0xfe, 0xb8, 0x9b, 0x17, 0x8f, 0x15, 0xa2, 0x4f, 0xa1, 0xe4, 0x13, 0xd8, 0xf4, 0x07,
	0x9a, 0xdf, 0x57, 0x69, 0xaf, 0x37, 0x61, 0x39, 0x9b, 0x6a, 0xb9, 0xcd, 0xb4, 0xeb, 0xbd, 0x5e,
	0x62, 0x0b, 0x6b, 0x7e, 0x0a, 0x4a, 0xaa, 0x50, 0xf2, 0x4c, 0xbd, 0xaf, 0x06, 0xf4, 0x75, 0xc0,
	0x4d, 0xce, 0xa3, 0xc9, 0xed, 0x09, 0x93, 0x8a, 0xa9, 0xf7, 0x3b, 0xf4, 0x75, 0x80, 0xa6, 0x0a,
	0x5e, 0x6c, 0x25, 0x4a, 0xb0, 0x32, 0x95, 0x1b, 0x72, 0x0d, 0xca, 0xb5, 0x66, 0xa3, 0xd1, 0x94,
	0xd5, 0x91, 0xac, 0xfe, 0xa2, 0x2e, 0x77, 0xd4, 0x17, 0x8f, 0x85, 0x2b, 0x64, 0x03, 0x48, 0xbb,
	0x51, 0x6d, 0x3f, 0x53, 0xeb, 0x4f, 0x9f, 0x8e, 0x14, 0x84, 0x8c, 0xf8, 0xdb, 0x0c, 0x14, 0xe2,
	0x9e, 0xc8, 0x26, 0x2c, 0xb1, 0xa0, 0x54, 0xd3, 0xc0, 0x4a, 0x15, 0x95, 0x45, 0xb6, 0x94, 0x0c,
	0x72, 0x17, 0xae, 0xea, 0x43, 0x3f, 0x70, 0x06, 0xea, 0x89, 0xf9, 0xb9, 0x6a, 0x6b, 0x03, 0x7e,
	0x84, 0xf2, 0x4a, 0x91, 0xc3, 0x07, 0xe6, 0xe7, 0xb2, 0x36, 0xa0, 0xe4, 0x3d, 0x58, 0x8f, 0xe9,
	0x59, 0xf4, 0x15, 0xb5, 0x54, 0x53, 0x77, 0x6c, 0xcc, 0x5c, 0x5e, 0x21, 0x23, 0xed, 0x63, 0x26,
	0x92, 0x74, 0xc7, 0x4e, 0x7d, 0x04, 0x1d, 0xcc, 0xa7, 0x3d, 0x82, 0x5e, 0x6e, 0x43, 0x29, 0x1e,
	0xcd, 0x70, 0x50, 0x5e, 0x40, 0xdd, 0xc2, 0x38, 0x98, 0xe1, 0x40, 0xdc, 0x80, 0xb5, 0xe9, 0x83,
	0xe5, 0xbb, 0xe2, 0xff, 0x67, 0x60, 0x2d, 0xad, 0x64, 0xe4, 0x26, 0x14, 0xf4, 0xa1, 0xe7, 0x51,
	0x3b, 0x50, 0xfb, 0x66, 0xe0, 0x87, 0x29, 0x58, 0x0e, 0xb1, 0x67, 0x66, 0xe0, 0x93, 0x1d, 0x80,
	0x81, 0xe3, 0xb9, 0x7d, 0xae, 0xc0, 0x6f, 0x51, 0x1e, 0x91, 0xa4, 0xf8, 0xd4, 0xd2, 0x7a, 0xe1,
	0x1d, 0xe2, 0xe2, 0x23, 0x4b, 0xeb, 0xb1, 0xb8, 0xb9, 0x98, 0xda, 0x86, 0x1a, 0x98, 0xe1, 0x1e,
	0x8b, 0x4a, 0x01, 0xd1, 0xba, 0x6d, 0x74, 0xcc, 0x01, 0x15, 0xff, 0x16, 0x72, 0x5d, 0x9f, 0x7a,
	0x92, 0x7d, 0xea, 0x90, 0x32, 0x2c, 0x69, 0xba, 0xee, 0x0c, 0xed, 0x00, 0xa3, 0xc9, 0x2b, 0xd1,
	0x92, 0x10, 0x98, 0xb7, 0x4d, 0xfd, 0x2c, 0x2c, 0x03, 0xfe, 0x2d, 0xfe, 0x3a, 0x03, 0xcb, 0xb5,
	0xbe, 0x66, 0xdb, 0xd4, 0xc2, 0xa7, 0xdf, 0x81, 0x15, 0x9d, 0x2f, 0x55, 0xc3, 0xf4, 0x5d, 0x4b,
	0x7b, 0x33, 0x2a, 0xac, 0x10, 0x0a, 0x0e, 0x23, 0x9c, 0x35, 0x89, 0x48, 0xf9, 0xc4, 0xb4, 0x0d,
	0xd3, 0x88, 0x9a, 0x44, 0x88, 0x1e, 0x20, 0x88, 0x49, 0x0a, 0xd5, 0x62, 0x8d, 0x62, 0x39, 0xc4,
	0xf0, 0x5e, 0xc6, 0x54, 0x62, 0x85, 0x8c, 0x54, 0xb0, 0x82, 0x7b, 0x10, 0x05, 0x80, 0xc7, 0x43,
	0x1d, 0x18, 0x4f, 0xc2, 0x1a, 0x46, 0x41, 0xb0, 0xb3, 0xd1, 0x30, 0x9e, 0x88, 0x1d, 0xc8, 0x3f,
	0x1d, 0x9a, 0x96, 0x81, 0x1b, 0xda, 0x01, 0xe8, 0xb1, 0x05, 0xb7, 0xcb, 0x33, 0x92, 0x47, 0x24,
	0xb2, 0xca, 0xc5, 0xe1, 0x6e, 0xd5, 0xd1, 0x26, 0x4a, 0x88, 0x87, 0x9b, 0x95, 0x0c, 0xf1, 0x37,
	0x39, 0x58, 0x4b, 0x6b, 0x08, 0xac, 0x07, 0x9e, 0x7a, 0xce, 0x40, 0x1d, 0x8e, 0x32, 0xb5, 0xc4,
	0xd6, 0x5d, 0xd3, 0x20, 0x7f, 0x07, 0x25, 0x2e, 0xf2, 0xa9, 0xa7, 0x9a, 0xf6, 0xa9, 0x13, 0x36,
	0x9a, 0xcd, 0x89, 0xbb, 0x1b, 0x15, 0x4f, 0x29, 0xe0, 0x93, 0x51, 0x29, 0x77, 0x00, 0x02, 0xcd,
	0xeb, 0xd1, 0x00, 0x6d, 0x87, 0x67, 0x83, 0x23, 0xcc, 0x7a, 0x15, 0x84, 0x48, 0x3c, 0xb2, 0x3f,
	0x7f, 0xb1, 0xfd, 0x52, 0xf8, 0x74, 0xcc, 0xc3, 0x28, 0xa9, 0x06, 0xa6, 0xb3, 0xa8, 0xe4, 0xa3,
	0x74, 0xb2, 0xf8, 0x47, 0x65, 0x41, 0xeb, 0x8b, 0x68, 0xbd, 0x32, 0xd9, 0x26, 0xc7, 0xe7, 0x67,
	0x54, 0x32, 0xb4, 0xbe, 0x05, 0x39, 0x9e, 0x5c, 0xd3, 0x28, 0x2f, 0xf1, 0xcc, 0xe0, 0x5a, 0x32,
	0xc8, 0x07, 0x51, 0x59, 0xd0, 0x6e, 0x0e, 0xed, 0x96, 0x27, 0xec, 0x8e, 0x8a, 0x18, 0x16, 0x0c,
	0x6d, 0xde, 0x84, 0x02, 0xf6, 0x1b, 0xdd, 0xb1, 0x03, 0x6a, 0x07, 0xe5, 0x3c, 0x3f, 0x29, 0x0c,
	0xab, 0x71, 0x88, 0xb9, 0xfd, 0xa7, 0xe1, 0xc0, 0x55, 0x87, 0x9e, 0x55, 0x06, 0x7e, 0x05, 0xd8,
	0xba, 0xeb, 0x59, 0x6c, 0xbf, 0x06, 0x65, 0x65, 0xf6, 0xa9, 0xee, 0x97, 0x97, 0xf9, 0x7e, 0x11,
	0x69, 0x53, 0xdd, 0x27, 0x37, 0x00, 0x0d, 0xa9, 0xae, 0x47, 0x4f, 0xcd, 0xd7, 0xe5, 0x02, 0x3e,
	0x0c, 0x0c, 0x6a, 0x21, 0x42, 0x1e, 0xc0, 0x8a, 0xa1, 0x05, 0xac, 0xb9, 0xfb, 0x3a, 0xb5, 0x29,
	0x3f, 0x54, 0x45, 0x54, 0xbb, 0xca, 0x05, 0x6d, 0x86, 0xe3, 0xd1, 0xfa, 0x7b, 0xd8, 0x48, 0x8c,
	0x02, 0xf5, 0x44, 0xf3, 0xa9, 0xea, 0xb8, 0x41, 0xb9, 0x84, 0xdb, 0xbd, 0x9b, 0x32, 0x6d, 0xe2,
	0x47, 0xeb, 0x40, 0xf3, 0x69, 0xd3, 0x0d, 0x94, 0xd5, 0x93, 0x69, 0x90, 0x7c, 0x0a, 0xeb, 0xae,
	0x47, 0x7d, 0xd6, 0x78, 0x92, 0xb6, 0xaf, 0xa2, 0xed, 0xfb, 0x93, 0xe3, 0x92, 0xeb, 0xa6, 0x99,
	0x27, 0xa1, 0x9d, 0xb8, 0xf5, 0x1d, 0x00, 0x9c, 0x39, 0xd8, 0x5a, 0xcb, 0x02, 0x4f, 0x13, 0x43,
	0xb0, 0xa1, 0x92, 0xeb, 0xb0, 0x6c, 0xfa, 0xaa, 0xc3, 0x6e, 0x0d, 0xd5, 0xac, 0xf2, 0x0a, 0x97,
	0x9b, 0x7e, 0xd3, 0x32, 0x0e, 0xa9, 0x66, 0xb1, 0x4b, 0xc5, 0x84, 0x89, 0x3a, 0x11, 0x7e, 0x55,
	0x1d, 0xcb, 0x90, 0x63, 0xa5, 0x5a, 0x87, 0xc5, 0x40, 0xeb, 0xb1, 0xf3, 0xb1, 0x8a, 0x46, 0x16,
	0x02, 0xad, 0x27, 0x61, 0x63, 0xb1, 0x9d, 0x13, 0xd3, 0x32, 0x83, 0x37, 0x61, 0x0c, 0x6b, 0xbc,
	0xb1, 0x44, 0x28, 0x8f, 0x63, 0x1f, 0x56, 0x93, 0x6a, 0xbc, 0x1e, 0xeb, 0xe8, 0x6a, 0x25, 0xa1,
	0x8b, 0x15, 0x79, 0x00, 0x2b, 0x36, 0xa5, 0x86, 0x3a, 0x70, 0x6c, 0x3f, 0x88, 0x6e, 0xcc, 0xc6,
	0x6e, 0x66, 0x2f, 0xa7, 0x5c, 0x65, 0x82, 0x06, 0xc7, 0xf1, 0x9c, 0xc5, 0x6d, 0xd3, 0xd7, 0x2c,
	0x58, 0x55, 0xb7, 0x83, 0xf2, 0x26, 0xc6, 0x31, 0xb2, 0x5d, 0x47, 0x49, 0x8d, 0x1f, 0xba, 0x11,
	0x97, 0x2a, 0x27, 0xb9, 0xd4, 0x16, 0xe4, 0xfa, 0x9a, 0x67, 0xe0, 0x79, 0xdc, 0xe2, 0xe7, 0x91,
	0xad, 0xbb, 0x9e, 0x25, 0xfe, 0xdf, 0x1c, 0x67, 0x49, 0x29, 0x85, 0x61, 0x27, 0x3d, 0xf0, 0xcc,
	0x5e, 0x8f, 0x7a, 0x63, 0x22, 0x54, 0x54, 0x96, 0x43, 0x0c, 0xdb, 0xe6, 0x2d, 0x28, 0x7a, 0x8e,
	0x65, 0x21, 0x5f, 0xc2, 0x8e, 0xcf, 0x5b, 0x57, 0x21, 0x04, 0x6b, 0xd8, 0xf6, 0x6f, 0x42, 0xb4,
	0xe6, 0x03, 0x24, 0x6c, 0xbf, 0x21, 0xc6, 0xe6, 0x47, 0x0a, 0xdb, 0x9b, 0x4f, 0x63, 0x7b, 0xef,
	0xc0, 0xca, 0x48, 0xcd, 0x75, 0x7c, 0x33, 0x30, 0x1d, 0x3b, 0x6c, 0x1a, 0x42, 0x24, 0x68, 0x85,
	0x38, 0xd9, 0x86, 0x3c, 0xde, 0x42, 0x8c, 0x7d, 0x11, 0x95, 0xf0, 0x5a, 0x46, 0x81, 0xa3, 0x70,
	0x64, 0x85, 0xb7, 0x87, 0x02, 0x03, 0x23, 0x0b, 0xe2, 0x7f, 0xce, 0x41, 0x65, 0xf6, 0xc1, 0x65,
	0x0e, 0x7a, 0xe6, 0x69, 0x10, 0x6f, 0xec, 0x39, 0x06, 0x60, 0xa9, 0x37, 0x61, 0x09, 0x85, 0xa3,
	0x76, 0xbe, 0xc8, 0x96, 0x92, 0x81, 0xf3, 0x80, 0x09, 0x78, 0xbe, 0xc2, 0x9e, 0xca, 0x10, 0x9e,
	0x2c, 0x11, 0x8a, 0xfc, 0x39, 0x36, 0x62, 0x58, 0xc1, 0xc2, 0x49, 0x84, 0x4f, 0xeb, 0x8e, 0x1d,
	0x36, 0x11, 0xd4, 0x39, 0x77, 0xbc, 0xa0, 0x1f, 0x35, 0x4d, 0x86, 0xbc, 0x64, 0x00, 0x2b, 0xf7,
	0x40, 0xeb, 0x99, 0x3a, 0xf3, 0xcd, 0xf7, 0xbd, 0x84, 0x6b, 0xee, 0x9c, 0x8b, 0x30, 0xe6, 0x25,
	0x3e, 0x8c, 0x10, 0xc1, 0xa0, 0x47, 0x62, 0xe4, 0x3f, 0xb9, 0x98, 0x98, 0xb9, 0x16, 0x3d, 0xc8,
	0x29, 0x43, 0xbf, 0x8f, 0xc7, 0x73, 0x1b, 0xf2, 0xde, 0xd0, 0xef, 0xc7, 0x4f, 0x46, 0x8e, 0x01,
	0x98, 0xdd, 0x7b, 0x20, 0xa0, 0x70, 0xa0, 0xbd, 0x56, 0x3d, 0x8d, 0x11, 0x87, 0x88, 0x78, 0x14,
	0x19, 0xde, 0xd0, 0x5e, 0x2b, 0x9a, 0x6d, 0x74, 0x7c, 0xb2, 0x0b, 0x05, 0x54, 0x3c, 0xd7, 0xcc,
	0x80, 0x29, 0xf1, 0x74, 0x00, 0xc3, 0x5e, 0x6a, 0x66, 0xd0, 0xf1, 0xc5, 0x7f, 0xcf, 0xc2, 0x8d,
	0x0e, 0xf5, 0x03, 0xe4, 0xee, 0x21, 0xef, 0x9b, 0xa4, 0xed, 0x33, 0x29, 0x60, 0x7c, 0x32, 0xce,
	0x25, 0x27, 0x23, 0xbb, 0xf8, 0x4e, 0x6c, 0xac, 0x2d, 0x04, 0x0e, 0x83, 0x93, 0xf3, 0x68, 0x7e,
	0x72, 0x1e, 0xc5, 0xaa, 0xba, 0x90, 0xa8, 0x2a, 0x23, 0x07, 0x9c, 0xde, 0xe1, 0xb1, 0xb2, 0x4c,
	0xfb, 0x0c, 0x73, 0xcf, 0xc8, 0x01, 0xe2, 0x1f, 0x0e, 0x07, 0xee, 0xb1, 0x69, 0x9f, 0xa5, 0xd1,
	0xd2, 0xa5, 0x34, 0x5a, 0x9a, 0xd4, 0x8b, 0x15, 0x64, 0xac, 0x87, 0x5c, 0x74, 0xec, 0x79, 0xc4,
	0x45, 0xc3, 0x99, 0x54, 0x4a, 0xd2, 0xd0, 0x14, 0x0a, 0x0a, 0xd3, 0x14, 0x34, 0x31, 0x33, 0x97,
	0x13, 0x33, 0x53, 0x14, 0x61, 0xf7, 0xe2, 0x52, 0xf8, 0xae, 0xf8, 0xc3, 0x15, 0x20, 0x71, 0xb0,
	0xe6, 0xd8, 0xa7, 0x66, 0x6f, 0x76, 0x89, 0xb6, 0x21, 0x8f, 0x82, 0x18, 0x3f, 0xcf, 0x31, 0x00,
	0x73, 0xd0, 0x00, 0x82, 0xc2, 0x80, 0x0e, 0x5c, 0x4b, 0x0b, 0xa8, 0x6a, 0x99, 0x3e, 0xbb, 0x33,
	0xd9, 0xbd, 0xe5, 0x47, 0x37, 0x26, 0x26, 0x0c, 0x73, 0xd6, 0x09, 0xf5, 0xa4, 0x80, 0x0e, 0x14,
	0xc1, 0x8e, 0x21, 0xc7, 0xa6, 0x1f, 0xa4, 0x74, 0x99, 0x85, 0xb4, 0x2e, 0x33, 0xd5, 0xd4, 0x16,
	0x2f, 0xd1, 0xd4, 0x96, 0xa6, 0x9b, 0x5a, 0xa2, 0x01, 0xe5, 0x26, 0x1a, 0xd0, 0x43, 0x58, 0x1b,
	0x68, 0xa6, 0xad, 0x9e, 0x38, 0xc6, 0x1b, 0xf5, 0xd4, 0x61, 0xdd, 0xdd, 0xb1, 0x1c, 0x2f, 0x2c,
	0xdd, 0x0a, 0x93, 0x1d, 0x38, 0xc6, 0x9b, 0x23, 0xc7, 0x0e, 0x6a, 0x4c, 0xc0, 0xce, 0x03, 0xcb,
	0x51, 0x5c, 0x97, 0x97, 0xaf, 0xc8, 0xe0, 0xb1, 0xde, 0x03, 0x58, 0x41, 0x15, 0xbf, 0xaf, 0x19,
	0xce, 0x79, 0xa8, 0xb9, 0xcc, 0x19, 0x02, 0x13, 0xb4, 0x11, 0xe7, 0xba, 0x7b, 0x20, 0xf8, 0x7d,
	0xe7, 0x5c, 0x0d, 0x1c, 0x55, 0x37, 0x3d, 0x7d, 0xe8, 0x53, 0x8a, 0x9c, 0x23, 0xa7, 0x94, 0x18,
	0xde, 0x71, 0x6a, 0x21, 0x4a, 0x1e, 0xc3, 0x46, 0x4c, 0x29, 0x1e, 0x04, 0x27, 0x1f, 0xab, 0xc1,
	0x48, 0x77, 0x1c, 0xca, 0x07, 0x50, 0x8e, 0x3f, 0x94, 0x88, 0xa8, 0x84, 0x8f, 0xad, 0x8f, 0x1f,
	0x8b, 0xc7, 0x75, 0x03, 0x96, 0x07, 0x9a, 0xd7, 0x33, 0x6d, 0xd5, 0xa2, 0xa7, 0x9c, 0x52, 0x14,
	0x15, 0xe0, 0xd0, 0x31, 0x3d, 0x45, 0x7e, 0xa0, 0xd9, 0xe6, 0x80, 0xf2, 0xdc, 0x86, 0xfc, 0x00,
	0x11, 0x4c, 0xee, 0x36, 0xf0, 0x85, 0xfa, 0xb9, 0xe9, 0x22, 0x3b, 0xc8, 0x2b, 0x39, 0x04, 0x3e,
	0x31, 0xdd, 0xb1, 0x90, 0x11, 0xf8, 0xf5, 0x98, 0xb0, 0x61, 0x3c, 0x61, 0xb5, 0xe7, 0x42, 0xd7,
	0xa3, 0xaf, 0x4c, 0x7a, 0x1e, 0xd2, 0x86, 0x02, 0x82, 0x2d, 0x8e, 0x91, 0xfb, 0xb0, 0x62, 0xfa,
	0x6a, 0x9f, 0x6a, 0x86, 0xca, 0xde, 0x61, 0xf0, 0xca, 0x23, 0x7f, 0xc8, 0x29, 0x25, 0xd3, 0x7f,
	0x46, 0x35, 0x43, 0x36, 0xf5, 0x33, 0x76, 0xe3, 0xd9, 0x4e, 0x62, 0x7d, 0x01, 0x59, 0x44, 0x5e,
	0x81, 0x71, 0x4b, 0x60, 0xad, 0xb2, 0xe7, 0xb0, 0x02, 0x9c, 0x6b, 0x41, 0xf8, 0x92, 0x8d, 0x8c,
	0x20, 0xaf, 0x14, 0x7b, 0x4e, 0xc7, 0x79, 0xc9, 0x50, 0x76, 0xd1, 0xc4, 0x1f, 0x2c, 0x41, 0x49,
	0xae, 0xbf, 0x6c, 0xab, 0x92, 0x7c, 0xd4, 0xe4, 0x6f, 0xd0, 0x15, 0xd8, 0x48, 0x22, 0x6a, 0x57,
	0x7e, 0x2e, 0x37, 0x5f, 0xca, 0xc2, 0x15, 0xb2, 0x05, 0xeb, 0x13, 0xb2, 0x5a, 0xb7, 0xdd, 0x69,
	0x36, 0x04, 0x83, 0x5c, 0x87, 0x4a, 0xaa, 0x48, 0xed, 0xd4, 0x3f, 0xea, 0x08, 0xec, 0x55, 0x75,
	0x37, 0x5d, 0x7e, 0x5c, 0x7f, 0x51, 0x3f, 0x56, 0xa5, 0x5a, 0x53, 0x16, 0x4e, 0xdf, 0xa6, 0x25,
	0x57, 0x1b, 0x75, 0xa1, 0x47, 0x76, 0x60, 0x2b, 0x5d, 0x4b, 0xee, 0x36, 0x84, 0x3e, 0xb9, 0x05,
	0x37, 0xd2, 0xc5, 0x1f, 0x76, 0x1b, 0x2d, 0xf5, 0x58, 0x92, 0x9f, 0x0b, 0x26, 0xd9, 0x81, 0xf2,
	0xe4, 0x36, 0xdb, 0x75, 0x05, 0x97, 0xc2, 0x8f, 0x32, 0xe4, 0xce, 0x54, 0x20, 0x28, 0x3e, 0x52,
	0x9a, 0x0d, 0xb5, 0x5a, 0xab, 0x35, 0xbb, 0x72, 0x47, 0xf8, 0x71, 0x86, 0xdc, 0x82, 0xeb, 0x69,
	0x6a, 0x9d, 0xe6, 0x48, 0xe9, 0x27, 0x19, 0x72, 0x17, 0x6e, 0xce, 0xb4, 0x25, 0x4b, 0xb5, 0xe7,
	0xb8, 0xab, 0x9f, 0x66, 0xc8, 0xed, 0xa9, 0xb8, 0x23, 0x63, 0x23, 0xad, 0x9f, 0x65, 0x88, 0x08,
	0x3b, 0x33, 0xad, 0x3d, 0xab, 0x57, 0x0f, 0x85, 0x9f, 0x67, 0xc8, 0x2e, 0x6c, 0xcf, 0xb0, 0x84,
	0x1a, 0xbf, 0xc8, 0x90, 0xfb, 0x70, 0x7b, 0xa6, 0x95, 0xee, 0xf3, 0x97, 0xa3, 0xf0, 0x7f, 0x99,
	0x21, 0xf7, 0x40, 0x9c, 0x61, 0x2c, 0xae, 0xf8, 0xab, 0x0c, 0xd9, 0x9e, 0x3a, 0x39, 0xb5, 0x67,
	0x55, 0x59, 0xae, 0x1f, 0x0b, 0xdf, 0x99, 0x4b, 0x09, 0x29, 0x14, 0xf2, 0xa2, 0x7e, 0x77, 0x8e,
	0x5c, 0x9f, 0xae, 0x6a, 0xa8, 0x21, 0x1d, 0x0a, 0xdf, 0x9b, 0x23, 0x65, 0x58, 0x9d, 0x90, 0x3f,
	0x95, 0x8e, 0x3a, 0xc2, 0x7f, 0x67, 0x53, 0x6a, 0xc9, 0x24, 0xfc, 0x4c, 0xfd, 0xcf, 0x4c, 0x31,
	0xfa, 0xfd, 0x2a, 0x9b, 0xe2, 0x17, 0xc5, 0x2d, 0x45, 0xaa, 0xd5, 0x85, 0xff, 0xcd, 0x92, 0x2d,
	0x58, 0x9b, 0x94, 0x77, 0xa5, 0xe3, 0x43, 0xe1, 0x77, 0xa9, 0x8f, 0x32, 0x11, 0xf7, 0xfc, 0xfb,
	0xd9, 0x72, 0x74, 0xfd, 0x87, 0x2c, 0xb9, 0x06, 0x9b, 0xe9, 0xcf, 0x1f, 0x0a, 0x7f, 0xcc, 0xa6,
	0x1c, 0xae, 0xa3, 0xee, 0xf1, 0x31, 0x5e, 0x28, 0xb5, 0xd9, 0xed, 0xd4, 0x15, 0xe1, 0xbf, 0x64,
	0xf1, 0xdf, 0x32, 0x00, 0x55, 0x59, 0x6a, 0xd4, 0xf9, 0xed, 0xdd, 0x00, 0x32, 0x5e, 0xc5, 0x6e,
	0xee, 0x3a, 0xac, 0xc4, 0xf0, 0xe3, 0x66, 0xa7, 0x23, 0xd5, 0x85, 0x0c, 0x21, 0x50, 0x8a, 0xc1,
	0x2f, 0xaa, 0x2d, 0x61, 0x8e, 0x5c, 0x83, 0xf2, 0x94, 0xaa, 0x7a, 0xd4, 0x6d, 0x4b, 0x4d, 0x59,
	0x60, 0xd9, 0x58, 0x4f, 0x3e, 0x11, 0x89, 0xe6, 0xc5, 0x2f, 0x33, 0xcc, 0x9a, 0xdc, 0xec, 0xca,
	0xb5, 0xba, 0xda, 0xae, 0x35, 0x79, 0x33, 0x49, 0x22, 0xb1, 0x90, 0x08, 0x94, 0x24, 0xb9, 0x2d,
	0x1d, 0x8e, 0x8f, 0x49, 0x86, 0xac, 0xc2, 0xd5, 0x66, 0xb7, 0x93, 0x00, 0x59, 0xe1, 0xd7, 0x5a,
	0xdd, 0x83, 0x63, 0xa9, 0x16, 0x66, 0x27, 0x92, 0xcc, 0x13, 0x01, 0x0a, 0x9d, 0x67, 0x52, 0x7b,
	0x84, 0xe4, 0x44, 0x89, 0xed, 0x33, 0x74, 0xd8, 0x6a, 0xb6, 0xa5, 0x8e, 0xd4, 0x94, 0x59, 0xbf,
	0x98, 0x02, 0x63, 0x81, 0xe4, 0x61, 0xa1, 0xdb, 0x6a, 0xd5, 0x15, 0x21, 0x43, 0x00, 0x16, 0x1b,
	0xd2, 0xe1, 0xe1, 0x71, 0x5d, 0x98, 0x13, 0x1b, 0x90, 0xc7, 0x86, 0x81, 0x79, 0x5d, 0x81, 0xa2,
	0xdc, 0xe4, 0x0d, 0xa4, 0x76, 0x2c, 0xd5, 0x9e, 0xf3, 0x94, 0x8e, 0xd7, 0x2a, 0xdf, 0x8a, 0x90,
	0x61, 0x15, 0xe0, 0xb0, 0xc5, 0xe0, 0x70, 0x37, 0xc2, 0x9c, 0xe8, 0x83, 0x30, 0xc9, 0x26, 0xc8,
	0xbb, 0xb0, 0xca, 0x09, 0x8c, 0x7d, 0xea, 0xe0, 0xd4, 0xe1, 0x5c, 0x24, 0xb3, 0x9b, 0x65, 0x2f,
	0x1f, 0x48, 0x66, 0xec, 0x53, 0x87, 0x4d, 0x1f, 0xa4, 0x1a, 0x15, 0xc8, 0x45, 0xa4, 0x25, 0x62,
	0x35, 0xd1, 0x9a, 0xac, 0xc1, 0xc2, 0x2b, 0xcd, 0x1a, 0xd2, 0xf0, 0x03, 0x23, 0x5f, 0x88, 0x0e,
	0x94, 0xab, 0x86, 0x31, 0x4d, 0x9d, 0x18, 0xc1, 0x6d, 0xc3, 0x5a, 0xf2, 0x4d, 0x5e, 0x47, 0x11,
	0x52, 0xa9, 0xe5, 0x47, 0x37, 0x27, 0x98, 0x50, 0x8a, 0x0d, 0x72, 0x32, 0x85, 0x89, 0xdb, 0xb0,
	0x35, 0xc3, 0xa1, 0xef, 0x8a, 0x1e, 0x6c, 0x77, 0x5d, 0x43, 0x0b, 0xe8, 0x5f, 0x30, 0xa0, 0xeb,
	0x70, 0x6d, 0xb6, 0x4f, 0xdf, 0x15, 0xab, 0xb0, 0x73, 0xc0, 0xc6, 0xe1, 0x21, 0xb5, 0xd2, 0xa3,
	0xda, 0x0d, 0x3f, 0xcd, 0x98, 0x46, 0xbc, 0x38, 0xc0, 0x99, 0x26, 0x2b, 0x8b, 0xb8, 0x0b, 0xd7,
	0x2f, 0x32, 0xe1, 0xbb, 0xa2, 0x15, 0x3a, 0x79, 0x4a, 0x83, 0x74, 0x27, 0xb7, 0xa1, 0xe4, 0x53,
	0xcd, 0xd3, 0xfb, 0x6a, 0x92, 0xd0, 0x16, 0x38, 0x2a, 0x73, 0x5a, 0x7b, 0x67, 0xa4, 0x75, 0x46,
	0xdf, 0x9c, 0x3b, 0x9e, 0x11, 0x7d, 0x7b, 0xe6, 0xe8, 0x73, 0x0e, 0x8a, 0xff, 0x1c, 0xc6, 0x33,
	0xc3, 0x9b, 0xef, 0x92, 0x4f, 0x61, 0x2b, 0x2d, 0xd3, 0xe3, 0x0d, 0x5e, 0x2a, 0xdd, 0x1b, 0xd3,
	0xe9, 0xc6, 0x7c, 0xfc, 0x4b, 0x06, 0x36, 0xab, 0x86, 0xd1, 0x0e, 0x4c, 0xfd, 0xec, 0xd2, 0x6f,
	0x55, 0x04, 0xe6, 0x3d, 0xcd, 0xe6, 0x9f, 0x71, 0xe7, 0x14, 0xfc, 0x9b, 0x11, 0xb2, 0x13, 0xca,
	0x08, 0xdb, 0xe8, 0x0d, 0x3f, 0xab, 0xe4, 0x11, 0x41, 0x2a, 0xbc, 0x05, 0xb9, 0xc4, 0xf7, 0xe3,
	0xac, 0xb2, 0x44, 0xc3, 0x4f, 0xc7, 0x15, 0x3c, 0xf7, 0x29, 0x11, 0xf8, 0xae, 0xf8, 0x3e, 0x6c,
	0x1e, 0x52, 0x2b, 0x35, 0x3a, 0xf6, 0xfe, 0x49, 0x75, 0xc7, 0x33, 0xc6, 0xf1, 0xe5, 0x38, 0x20,
	0x19, 0xcc, 0x66, 0xfa, 0x73, 0xbe, 0x2b, 0xfe, 0x6b, 0x06, 0x2a, 0xfc, 0x98, 0x7d, 0x63, 0xbb,
	0x7f, 0xe6, 0x9d, 0xef, 0x44, 0x77, 0x2c, 0x3d, 0xd0, 0x6b, 0x50, 0x79, 0x4a, 0x83, 0xaa, 0x95,
	0xba, 0x7f, 0xf1, 0x3f, 0xb2, 0xb0, 0x32, 0x25, 0xb8, 0x38, 0xfa, 0x58, 0x41, 0xe7, 0x66, 0xbf,
	0x83, 0x65, 0x27, 0xde, 0xc1, 0x46, 0x54, 0x3b, 0xf6, 0x5d, 0x9c, 0x13, 0x68, 0x14, 0x27, 0xa8,
	0xf6, 0xc2, 0x04, 0xd5, 0x4e, 0xd2, 0xf4, 0xc5, 0x49, 0x9a, 0x3e, 0x45, 0xb6, 0x97, 0x52, 0xc8,
	0x76, 0x94, 0x73, 0xfe, 0xf2, 0x9b, 0x96, 0xf3, 0xfc, 0x45, 0x39, 0x87, 0x44, 0xce, 0xc9, 0x23,
	0x58, 0xf4, 0x03, 0x2d, 0x18, 0xf2, 0x6f, 0xaf, 0xa5, 0xa9, 0x4f, 0xc9, 0x98, 0xd2, 0x36, 0x6a,
	0x28, 0xa1, 0x26, 0x7b, 0xd5, 0x33, 0x7d, 0xd5, 0xa5, 0xde, 0x40, 0xb3, 0xa9, 0x1d, 0x84, 0x6f,
	0x48, 0xcb, 0xa6, 0xdf, 0x8a, 0x20, 0xf1, 0x35, 0x6c, 0xcf, 0xac, 0x95, 0xef, 0x92, 0x8f, 0xa1,
	0xec, 0x33, 0x41, 0xf2, 0xa7, 0xb9, 0xf8, 0x1d, 0xde, 0x4d, 0x8b, 0x23, 0x61, 0x67, 0xdd, 0x9f,
	0x84, 0xf0, 0x06, 0x7f, 0x9d, 0x81, 0x7b, 0xb5, 0x3e, 0xd5, 0xcf, 0xa4, 0xd3, 0x9a, 0x33, 0xb4,
	0x8c, 0xa6, 0x4b, 0xbd, 0x59, 0x67, 0xfb, 0xa2, 0x97, 0xf0, 0xf1, 0xb1, 0x99, 0x9b, 0x38, 0x36,
	0xdf, 0xfe, 0x80, 0x3f, 0x80, 0xbd, 0xcb, 0x85, 0xe6, 0xbb, 0x0f, 0xbe, 0x80, 0xe5, 0x58, 0xee,
	0x19, 0xb3, 0x69, 0x77, 0xd8, 0x54, 0x6e, 0x77, 0xaa, 0x9d, 0x6e, 0x5b, 0xed, 0xca, 0xed, 0x56,
	0xbd, 0x26, 0x1d, 0x49, 0xf5, 0x43, 0xe1, 0x0a, 0xa3, 0x19, 0x09, 0xe9, 0xcb, 0xaa, 0xd4, 0xc1,
	0x9f, 0x07, 0x19, 0x8b, 0x49, 0x48, 0xea, 0x47, 0x47, 0xf5, 0x5a, 0x47, 0x7a, 0x51, 0xe7, 0xe4,
	0x24, 0x29, 0xfb, 0xa8, 0x25, 0x29, 0xf5, 0x43, 0x21, 0xfb, 0xe8, 0xab, 0x65, 0x28, 0xf0, 0x5f,
	0x89, 0x65, 0x4c, 0x3f, 0xf9, 0x07, 0x10, 0x26, 0x7f, 0x87, 0x23, 0xe2, 0xdb, 0x7f, 0x01, 0xae,
	0xdc, 0x7a, 0xab, 0x8e, 0xef, 0x8a, 0x57, 0xc8, 0x97, 0x19, 0xb8, 0x76, 0xd1, 0x97, 0x14, 0xb2,
	0x3f, 0x61, 0xe7, 0x2d, 0x5f, 0xc0, 0x2a, 0x0f, 0xbf, 0x91, 0x3e, 0xc6, 0x60, 0xc1, 0x7a, 0xea,
	0xf8, 0x27, 0xf7, 0x26, 0x6c, 0xcd, 0x62, 0x25, 0x95, 0xbd, 0xcb, 0x29, 0xa2, 0xb7, 0x21, 0x94,
	0x67, 0xcd, 0x76, 0xf2, 0x60, 0xf2, 0xc7, 0xa2, 0xd9, 0xc4, 0xa3, 0xf2, 0xce, 0xa5, 0x75, 0xd1,
	0xed, 0x17, 0x50, 0x99, 0x3d, 0xef, 0xc9, 0x5f, 0x4d, 0x0e, 0xce, 0x8b, 0xd8, 0x45, 0xe5, 0xdd,
	0x6f, 0xa0, 0x9d, 0x70, 0x9e, 0x3a, 0xdc, 0xd3, 0x9d, 0xcf, 0x62, 0x1d, 0xe9, 0xce, 0x67, 0xb2,
	0x06, 0xf1, 0x0a, 0x31, 0x61, 0x2d, 0x6d, 0xac, 0x92, 0xbb, 0xd3, 0x45, 0x4b, 0xeb, 0x15, 0x95,
	0x7b, 0x97, 0xd2, 0x8b, 0x5c, 0xa5, 0x4d, 0xdb, 0x29, 0x57, 0x33, 0x46, 0xf9, 0x94, 0xab, 0x99,
	0xa3, 0xfb, 0x0a, 0xf1, 0x60, 0x73, 0xc6, 0xc8, 0x24, 0xf7, 0x53, 0x4f, 0x46, 0xaa, 0xc3, 0x07,
	0x97, 0x55, 0x8d, 0x7c, 0xce, 0xe8, 0xed, 0x53, 0x3e, 0x67, 0xcf, 0xeb, 0x29, 0x9f, 0x17, 0x8c,
	0x0b, 0xf1, 0x0a, 0xf9, 0x3a, 0x03, 0xb7, 0x2f, 0xd3, 0x3a, 0xc9, 0xfb, 0x53, 0x3f, 0x85, 0x5e,
	0x6a, 0x14, 0x54, 0x3e, 0xf8, 0x56, 0xcf, 0x61, 0x6c, 0xa7, 0xb0, 0x9a, 0xf2, 0x7f, 0x35, 0xe4,
	0x4e, 0xea, 0xff, 0xae, 0x4c, 0xfe, 0xcb, 0x4f, 0xe5, 0xee, 0x65, 0xd4, 0x98, 0x9f, 0x83, 0xf7,
	0x3e, 0x79, 0xd8, 0x73, 0x2c, 0xcd, 0xee, 0xed, 0x3f, 0x79, 0x14, 0x04, 0xfb, 0xba, 0x33, 0x78,
	0x88, 0xff, 0x5e, 0xa4, 0x3b, 0xd6, 0x43, 0x9f, 0x7a, 0xaf, 0x4c, 0x9d, 0xfa, 0xc9, 0x7f, 0x3f,
	0x3a, 0x59, 0x44, 0x85, 0xc7, 0x7f, 0x0a, 0x00, 0x00, 0xff, 0xff, 0x84, 0x77, 0x74, 0xb8, 0xb3,
	0x24, 0x00, 0x00,
}
