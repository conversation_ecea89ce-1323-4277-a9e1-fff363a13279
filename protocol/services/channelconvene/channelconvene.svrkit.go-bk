package channelconvene

// Code generated by protoc-gen-svrkit-go. DO NOT EDIT.
// source: services/channelconvene/channelconvene.proto

/*
 This is a generated svrkit golang dev toolkit.
*/

import svrkit "gitlab.ttyuyin.com/golang/svrkit"

import context "golang.org/x/net/context"

import tlvpickle "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

var _ = context.TODO

// Magic Number for ChannelConvene service
const ChannelConveneMagic = uint16(15560)

// Client API for ChannelConvene service

type ChannelConveneClientInterface interface {
	CollectChannel(ctx context.Context, uin uint32, in *CollectChannelReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	RemoveChannelCollection(ctx context.Context, uin uint32, in *RemoveChannelCollectionReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetChannelCollectionListByUid(ctx context.Context, uin uint32, in *GetChannelCollectionListByUidReq, opts ...svrkit.CallOption) (*GetChannelCollectionListByUidResp, error)
	GetChannelCollectionMemberList(ctx context.Context, uin uint32, in *GetChannelCollectionMemberListReq, opts ...svrkit.CallOption) (*GetChannelCollectionMemberListResp, error)
	GetChannelConveneInfo(ctx context.Context, uin uint32, in *GetChannelConveneInfoReq, opts ...svrkit.CallOption) (*GetChannelConveneInfoResp, error)
	ConveneChannel(ctx context.Context, uin uint32, in *ConveneChannelReq, opts ...svrkit.CallOption) (*ConveneChannelResp, error)
	ConveneChannelInTurn(ctx context.Context, uin uint32, in *ConveneChannelInTurnReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DisableConveneChannel(ctx context.Context, uin uint32, in *DisableConveneChannelReq, opts ...svrkit.CallOption) (*GetChannelConveneInfoResp, error)
	DisableConveneChannelInTurn(ctx context.Context, uin uint32, in *DisableConveneChannelInTurnReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	InformCreateChannel(ctx context.Context, uin uint32, in *InformCreateChannelReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	InformDismissChannel(ctx context.Context, uin uint32, in *InformDismissChannelReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	ConfirmChannelConvene(ctx context.Context, uin uint32, in *ConfirmChannelConveneReq, opts ...svrkit.CallOption) (*ConfirmChannelConveneResp, error)
	InformUserEnterChannel(ctx context.Context, uin uint32, in *InformUserEnterChannelReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	InformUserExitChannel(ctx context.Context, uin uint32, in *InformUserExitChannelReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	UpdateUserConveneStatus(ctx context.Context, uin uint32, in *UpdateUserConveneStatusReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	HasCollectChannel(ctx context.Context, uin uint32, in *HasCollectChannelReq, opts ...svrkit.CallOption) (*HasCollectChannelResp, error)
	GetConveneMemberList(ctx context.Context, uin uint32, in *GetConveneMemberListReq, opts ...svrkit.CallOption) (*GetConveneMemberListResp, error)
	GetUserConveneInfo(ctx context.Context, uin uint32, in *GetUserConveneInfoReq, opts ...svrkit.CallOption) (*GetUserConveneInfoResp, error)
	GetConfirmCountByStatus(ctx context.Context, uin uint32, in *GetConfirmCountByStatusReq, opts ...svrkit.CallOption) (*GetConfirmCountByStatusResp, error)
	DismissChannelInTurn(ctx context.Context, uin uint32, in *DismissChannelInTurnReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetChannelCollectionMemberCount(ctx context.Context, uin uint32, in *GetChannelCollectionMemberCountReq, opts ...svrkit.CallOption) (*GetChannelCollectionMemberCountResp, error)
	BatchHasCollectChannel(ctx context.Context, uin uint32, in *BatchHasCollectChannelReq, opts ...svrkit.CallOption) (*BatchHasCollectChannelResp, error)
}

type ChannelConveneClient struct {
	cc *svrkit.ClientConn
}

func NewChannelConveneClient(cc *svrkit.ClientConn) ChannelConveneClientInterface {
	return &ChannelConveneClient{cc}
}

const (
	commandChannelConveneGetSelfSvnInfo                  = 9995
	commandChannelConveneEcho                            = 9999
	commandChannelConveneCollectChannel                  = 1
	commandChannelConveneRemoveChannelCollection         = 2
	commandChannelConveneGetChannelCollectionListByUid   = 3
	commandChannelConveneGetChannelCollectionMemberList  = 4
	commandChannelConveneGetChannelConveneInfo           = 5
	commandChannelConveneConveneChannel                  = 6
	commandChannelConveneConveneChannelInTurn            = 7
	commandChannelConveneDisableConveneChannel           = 8
	commandChannelConveneDisableConveneChannelInTurn     = 9
	commandChannelConveneInformCreateChannel             = 10
	commandChannelConveneInformDismissChannel            = 11
	commandChannelConveneConfirmChannelConvene           = 12
	commandChannelConveneInformUserEnterChannel          = 13
	commandChannelConveneInformUserExitChannel           = 14
	commandChannelConveneUpdateUserConveneStatus         = 15
	commandChannelConveneHasCollectChannel               = 16
	commandChannelConveneGetConveneMemberList            = 17
	commandChannelConveneGetUserConveneInfo              = 18
	commandChannelConveneGetConfirmCountByStatus         = 19
	commandChannelConveneDismissChannelInTurn            = 20
	commandChannelConveneGetChannelCollectionMemberCount = 21
	commandChannelConveneBatchHasCollectChannel          = 22
)

func (c *ChannelConveneClient) CollectChannel(ctx context.Context, uin uint32, in *CollectChannelReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandChannelConveneCollectChannel, "/channelconvene.ChannelConvene/CollectChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelConveneClient) RemoveChannelCollection(ctx context.Context, uin uint32, in *RemoveChannelCollectionReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandChannelConveneRemoveChannelCollection, "/channelconvene.ChannelConvene/RemoveChannelCollection", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelConveneClient) GetChannelCollectionListByUid(ctx context.Context, uin uint32, in *GetChannelCollectionListByUidReq, opts ...svrkit.CallOption) (*GetChannelCollectionListByUidResp, error) {
	out := new(GetChannelCollectionListByUidResp)
	err := c.cc.Invoke(ctx, uin, commandChannelConveneGetChannelCollectionListByUid, "/channelconvene.ChannelConvene/GetChannelCollectionListByUid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelConveneClient) GetChannelCollectionMemberList(ctx context.Context, uin uint32, in *GetChannelCollectionMemberListReq, opts ...svrkit.CallOption) (*GetChannelCollectionMemberListResp, error) {
	out := new(GetChannelCollectionMemberListResp)
	err := c.cc.Invoke(ctx, uin, commandChannelConveneGetChannelCollectionMemberList, "/channelconvene.ChannelConvene/GetChannelCollectionMemberList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelConveneClient) GetChannelConveneInfo(ctx context.Context, uin uint32, in *GetChannelConveneInfoReq, opts ...svrkit.CallOption) (*GetChannelConveneInfoResp, error) {
	out := new(GetChannelConveneInfoResp)
	err := c.cc.Invoke(ctx, uin, commandChannelConveneGetChannelConveneInfo, "/channelconvene.ChannelConvene/GetChannelConveneInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelConveneClient) ConveneChannel(ctx context.Context, uin uint32, in *ConveneChannelReq, opts ...svrkit.CallOption) (*ConveneChannelResp, error) {
	out := new(ConveneChannelResp)
	err := c.cc.Invoke(ctx, uin, commandChannelConveneConveneChannel, "/channelconvene.ChannelConvene/ConveneChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelConveneClient) ConveneChannelInTurn(ctx context.Context, uin uint32, in *ConveneChannelInTurnReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandChannelConveneConveneChannelInTurn, "/channelconvene.ChannelConvene/ConveneChannelInTurn", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelConveneClient) DisableConveneChannel(ctx context.Context, uin uint32, in *DisableConveneChannelReq, opts ...svrkit.CallOption) (*GetChannelConveneInfoResp, error) {
	out := new(GetChannelConveneInfoResp)
	err := c.cc.Invoke(ctx, uin, commandChannelConveneDisableConveneChannel, "/channelconvene.ChannelConvene/DisableConveneChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelConveneClient) DisableConveneChannelInTurn(ctx context.Context, uin uint32, in *DisableConveneChannelInTurnReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandChannelConveneDisableConveneChannelInTurn, "/channelconvene.ChannelConvene/DisableConveneChannelInTurn", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelConveneClient) InformCreateChannel(ctx context.Context, uin uint32, in *InformCreateChannelReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandChannelConveneInformCreateChannel, "/channelconvene.ChannelConvene/InformCreateChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelConveneClient) InformDismissChannel(ctx context.Context, uin uint32, in *InformDismissChannelReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandChannelConveneInformDismissChannel, "/channelconvene.ChannelConvene/InformDismissChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelConveneClient) ConfirmChannelConvene(ctx context.Context, uin uint32, in *ConfirmChannelConveneReq, opts ...svrkit.CallOption) (*ConfirmChannelConveneResp, error) {
	out := new(ConfirmChannelConveneResp)
	err := c.cc.Invoke(ctx, uin, commandChannelConveneConfirmChannelConvene, "/channelconvene.ChannelConvene/ConfirmChannelConvene", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelConveneClient) InformUserEnterChannel(ctx context.Context, uin uint32, in *InformUserEnterChannelReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandChannelConveneInformUserEnterChannel, "/channelconvene.ChannelConvene/InformUserEnterChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelConveneClient) InformUserExitChannel(ctx context.Context, uin uint32, in *InformUserExitChannelReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandChannelConveneInformUserExitChannel, "/channelconvene.ChannelConvene/InformUserExitChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelConveneClient) UpdateUserConveneStatus(ctx context.Context, uin uint32, in *UpdateUserConveneStatusReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandChannelConveneUpdateUserConveneStatus, "/channelconvene.ChannelConvene/UpdateUserConveneStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelConveneClient) HasCollectChannel(ctx context.Context, uin uint32, in *HasCollectChannelReq, opts ...svrkit.CallOption) (*HasCollectChannelResp, error) {
	out := new(HasCollectChannelResp)
	err := c.cc.Invoke(ctx, uin, commandChannelConveneHasCollectChannel, "/channelconvene.ChannelConvene/HasCollectChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelConveneClient) GetConveneMemberList(ctx context.Context, uin uint32, in *GetConveneMemberListReq, opts ...svrkit.CallOption) (*GetConveneMemberListResp, error) {
	out := new(GetConveneMemberListResp)
	err := c.cc.Invoke(ctx, uin, commandChannelConveneGetConveneMemberList, "/channelconvene.ChannelConvene/GetConveneMemberList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelConveneClient) GetUserConveneInfo(ctx context.Context, uin uint32, in *GetUserConveneInfoReq, opts ...svrkit.CallOption) (*GetUserConveneInfoResp, error) {
	out := new(GetUserConveneInfoResp)
	err := c.cc.Invoke(ctx, uin, commandChannelConveneGetUserConveneInfo, "/channelconvene.ChannelConvene/GetUserConveneInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelConveneClient) GetConfirmCountByStatus(ctx context.Context, uin uint32, in *GetConfirmCountByStatusReq, opts ...svrkit.CallOption) (*GetConfirmCountByStatusResp, error) {
	out := new(GetConfirmCountByStatusResp)
	err := c.cc.Invoke(ctx, uin, commandChannelConveneGetConfirmCountByStatus, "/channelconvene.ChannelConvene/GetConfirmCountByStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelConveneClient) DismissChannelInTurn(ctx context.Context, uin uint32, in *DismissChannelInTurnReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandChannelConveneDismissChannelInTurn, "/channelconvene.ChannelConvene/DismissChannelInTurn", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelConveneClient) GetChannelCollectionMemberCount(ctx context.Context, uin uint32, in *GetChannelCollectionMemberCountReq, opts ...svrkit.CallOption) (*GetChannelCollectionMemberCountResp, error) {
	out := new(GetChannelCollectionMemberCountResp)
	err := c.cc.Invoke(ctx, uin, commandChannelConveneGetChannelCollectionMemberCount, "/channelconvene.ChannelConvene/GetChannelCollectionMemberCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelConveneClient) BatchHasCollectChannel(ctx context.Context, uin uint32, in *BatchHasCollectChannelReq, opts ...svrkit.CallOption) (*BatchHasCollectChannelResp, error) {
	out := new(BatchHasCollectChannelResp)
	err := c.cc.Invoke(ctx, uin, commandChannelConveneBatchHasCollectChannel, "/channelconvene.ChannelConvene/BatchHasCollectChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}
