// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/virtual-image-resource/virtual-image-resource.proto

package virtual_image_resource

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockVirtualImageResourceClient is a mock of VirtualImageResourceClient interface.
type MockVirtualImageResourceClient struct {
	ctrl     *gomock.Controller
	recorder *MockVirtualImageResourceClientMockRecorder
}

// MockVirtualImageResourceClientMockRecorder is the mock recorder for MockVirtualImageResourceClient.
type MockVirtualImageResourceClientMockRecorder struct {
	mock *MockVirtualImageResourceClient
}

// NewMockVirtualImageResourceClient creates a new mock instance.
func NewMockVirtualImageResourceClient(ctrl *gomock.Controller) *MockVirtualImageResourceClient {
	mock := &MockVirtualImageResourceClient{ctrl: ctrl}
	mock.recorder = &MockVirtualImageResourceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockVirtualImageResourceClient) EXPECT() *MockVirtualImageResourceClientMockRecorder {
	return m.recorder
}

// AddLevelConfig mocks base method.
func (m *MockVirtualImageResourceClient) AddLevelConfig(ctx context.Context, in *AddLevelConfigRequest, opts ...grpc.CallOption) (*AddLevelConfigResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddLevelConfig", varargs...)
	ret0, _ := ret[0].(*AddLevelConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddLevelConfig indicates an expected call of AddLevelConfig.
func (mr *MockVirtualImageResourceClientMockRecorder) AddLevelConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddLevelConfig", reflect.TypeOf((*MockVirtualImageResourceClient)(nil).AddLevelConfig), varargs...)
}

// AddVirtualImageResource mocks base method.
func (m *MockVirtualImageResourceClient) AddVirtualImageResource(ctx context.Context, in *AddVirtualImageResourceRequest, opts ...grpc.CallOption) (*AddVirtualImageResourceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddVirtualImageResource", varargs...)
	ret0, _ := ret[0].(*AddVirtualImageResourceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddVirtualImageResource indicates an expected call of AddVirtualImageResource.
func (mr *MockVirtualImageResourceClientMockRecorder) AddVirtualImageResource(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddVirtualImageResource", reflect.TypeOf((*MockVirtualImageResourceClient)(nil).AddVirtualImageResource), varargs...)
}

// BatchUpdateIcon mocks base method.
func (m *MockVirtualImageResourceClient) BatchUpdateIcon(ctx context.Context, in *BatchUpdateIconRequest, opts ...grpc.CallOption) (*BatchUpdateIconResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchUpdateIcon", varargs...)
	ret0, _ := ret[0].(*BatchUpdateIconResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchUpdateIcon indicates an expected call of BatchUpdateIcon.
func (mr *MockVirtualImageResourceClientMockRecorder) BatchUpdateIcon(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateIcon", reflect.TypeOf((*MockVirtualImageResourceClient)(nil).BatchUpdateIcon), varargs...)
}

// CloneVirtualImageResource mocks base method.
func (m *MockVirtualImageResourceClient) CloneVirtualImageResource(ctx context.Context, in *CloneVirtualImageResourceRequest, opts ...grpc.CallOption) (*CloneVirtualImageResourceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CloneVirtualImageResource", varargs...)
	ret0, _ := ret[0].(*CloneVirtualImageResourceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CloneVirtualImageResource indicates an expected call of CloneVirtualImageResource.
func (mr *MockVirtualImageResourceClientMockRecorder) CloneVirtualImageResource(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CloneVirtualImageResource", reflect.TypeOf((*MockVirtualImageResourceClient)(nil).CloneVirtualImageResource), varargs...)
}

// DeleteVirtualImageResource mocks base method.
func (m *MockVirtualImageResourceClient) DeleteVirtualImageResource(ctx context.Context, in *DeleteVirtualImageResourceRequest, opts ...grpc.CallOption) (*DeleteVirtualImageResourceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteVirtualImageResource", varargs...)
	ret0, _ := ret[0].(*DeleteVirtualImageResourceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteVirtualImageResource indicates an expected call of DeleteVirtualImageResource.
func (mr *MockVirtualImageResourceClientMockRecorder) DeleteVirtualImageResource(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteVirtualImageResource", reflect.TypeOf((*MockVirtualImageResourceClient)(nil).DeleteVirtualImageResource), varargs...)
}

// EditVirtualImageResource mocks base method.
func (m *MockVirtualImageResourceClient) EditVirtualImageResource(ctx context.Context, in *EditVirtualImageResourceRequest, opts ...grpc.CallOption) (*EditVirtualImageResourceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "EditVirtualImageResource", varargs...)
	ret0, _ := ret[0].(*EditVirtualImageResourceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EditVirtualImageResource indicates an expected call of EditVirtualImageResource.
func (mr *MockVirtualImageResourceClientMockRecorder) EditVirtualImageResource(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EditVirtualImageResource", reflect.TypeOf((*MockVirtualImageResourceClient)(nil).EditVirtualImageResource), varargs...)
}

// GetActionResourceMap mocks base method.
func (m *MockVirtualImageResourceClient) GetActionResourceMap(ctx context.Context, in *GetActionResourceMapRequest, opts ...grpc.CallOption) (*GetActionResourceMapResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetActionResourceMap", varargs...)
	ret0, _ := ret[0].(*GetActionResourceMapResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActionResourceMap indicates an expected call of GetActionResourceMap.
func (mr *MockVirtualImageResourceClientMockRecorder) GetActionResourceMap(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActionResourceMap", reflect.TypeOf((*MockVirtualImageResourceClient)(nil).GetActionResourceMap), varargs...)
}

// GetClientListByPage mocks base method.
func (m *MockVirtualImageResourceClient) GetClientListByPage(ctx context.Context, in *GetClientListByPageRequest, opts ...grpc.CallOption) (*GetClientListByPageResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetClientListByPage", varargs...)
	ret0, _ := ret[0].(*GetClientListByPageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientListByPage indicates an expected call of GetClientListByPage.
func (mr *MockVirtualImageResourceClientMockRecorder) GetClientListByPage(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientListByPage", reflect.TypeOf((*MockVirtualImageResourceClient)(nil).GetClientListByPage), varargs...)
}

// GetDefaultResourceList mocks base method.
func (m *MockVirtualImageResourceClient) GetDefaultResourceList(ctx context.Context, in *GetDefaultResourceListRequest, opts ...grpc.CallOption) (*GetDefaultResourceListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDefaultResourceList", varargs...)
	ret0, _ := ret[0].(*GetDefaultResourceListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDefaultResourceList indicates an expected call of GetDefaultResourceList.
func (mr *MockVirtualImageResourceClientMockRecorder) GetDefaultResourceList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDefaultResourceList", reflect.TypeOf((*MockVirtualImageResourceClient)(nil).GetDefaultResourceList), varargs...)
}

// GetLevelConfig mocks base method.
func (m *MockVirtualImageResourceClient) GetLevelConfig(ctx context.Context, in *GetLevelConfigRequest, opts ...grpc.CallOption) (*GetLevelConfigResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLevelConfig", varargs...)
	ret0, _ := ret[0].(*GetLevelConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLevelConfig indicates an expected call of GetLevelConfig.
func (mr *MockVirtualImageResourceClientMockRecorder) GetLevelConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLevelConfig", reflect.TypeOf((*MockVirtualImageResourceClient)(nil).GetLevelConfig), varargs...)
}

// GetNoticeCfg mocks base method.
func (m *MockVirtualImageResourceClient) GetNoticeCfg(ctx context.Context, in *GetNoticeCfgRequest, opts ...grpc.CallOption) (*GetNoticeCfgResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNoticeCfg", varargs...)
	ret0, _ := ret[0].(*GetNoticeCfgResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNoticeCfg indicates an expected call of GetNoticeCfg.
func (mr *MockVirtualImageResourceClientMockRecorder) GetNoticeCfg(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNoticeCfg", reflect.TypeOf((*MockVirtualImageResourceClient)(nil).GetNoticeCfg), varargs...)
}

// GetNoticeCfgCache mocks base method.
func (m *MockVirtualImageResourceClient) GetNoticeCfgCache(ctx context.Context, in *GetNoticeCfgCacheRequest, opts ...grpc.CallOption) (*GetNoticeCfgCacheResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNoticeCfgCache", varargs...)
	ret0, _ := ret[0].(*GetNoticeCfgCacheResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNoticeCfgCache indicates an expected call of GetNoticeCfgCache.
func (mr *MockVirtualImageResourceClientMockRecorder) GetNoticeCfgCache(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNoticeCfgCache", reflect.TypeOf((*MockVirtualImageResourceClient)(nil).GetNoticeCfgCache), varargs...)
}

// GetVirtualImageResourceBySuit mocks base method.
func (m *MockVirtualImageResourceClient) GetVirtualImageResourceBySuit(ctx context.Context, in *GetVirtualImageResourceBySuitRequest, opts ...grpc.CallOption) (*GetVirtualImageResourceBySuitResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetVirtualImageResourceBySuit", varargs...)
	ret0, _ := ret[0].(*GetVirtualImageResourceBySuitResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVirtualImageResourceBySuit indicates an expected call of GetVirtualImageResourceBySuit.
func (mr *MockVirtualImageResourceClientMockRecorder) GetVirtualImageResourceBySuit(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualImageResourceBySuit", reflect.TypeOf((*MockVirtualImageResourceClient)(nil).GetVirtualImageResourceBySuit), varargs...)
}

// GetVirtualImageResourceCategory mocks base method.
func (m *MockVirtualImageResourceClient) GetVirtualImageResourceCategory(ctx context.Context, in *GetVirtualImageResourceCategoryRequest, opts ...grpc.CallOption) (*GetVirtualImageResourceCategoryResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetVirtualImageResourceCategory", varargs...)
	ret0, _ := ret[0].(*GetVirtualImageResourceCategoryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVirtualImageResourceCategory indicates an expected call of GetVirtualImageResourceCategory.
func (mr *MockVirtualImageResourceClientMockRecorder) GetVirtualImageResourceCategory(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualImageResourceCategory", reflect.TypeOf((*MockVirtualImageResourceClient)(nil).GetVirtualImageResourceCategory), varargs...)
}

// GetVirtualImageResourcesByIds mocks base method.
func (m *MockVirtualImageResourceClient) GetVirtualImageResourcesByIds(ctx context.Context, in *GetVirtualImageResourcesByIdsRequest, opts ...grpc.CallOption) (*GetVirtualImageResourcesByIdsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetVirtualImageResourcesByIds", varargs...)
	ret0, _ := ret[0].(*GetVirtualImageResourcesByIdsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVirtualImageResourcesByIds indicates an expected call of GetVirtualImageResourcesByIds.
func (mr *MockVirtualImageResourceClientMockRecorder) GetVirtualImageResourcesByIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualImageResourcesByIds", reflect.TypeOf((*MockVirtualImageResourceClient)(nil).GetVirtualImageResourcesByIds), varargs...)
}

// SearchVirtualImageResource mocks base method.
func (m *MockVirtualImageResourceClient) SearchVirtualImageResource(ctx context.Context, in *SearchVirtualImageResourceRequest, opts ...grpc.CallOption) (*SearchVirtualImageResourceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SearchVirtualImageResource", varargs...)
	ret0, _ := ret[0].(*SearchVirtualImageResourceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchVirtualImageResource indicates an expected call of SearchVirtualImageResource.
func (mr *MockVirtualImageResourceClientMockRecorder) SearchVirtualImageResource(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchVirtualImageResource", reflect.TypeOf((*MockVirtualImageResourceClient)(nil).SearchVirtualImageResource), varargs...)
}

// SetVirtualImageResourceForSale mocks base method.
func (m *MockVirtualImageResourceClient) SetVirtualImageResourceForSale(ctx context.Context, in *SetVirtualImageResourceForSaleRequest, opts ...grpc.CallOption) (*SetVirtualImageResourceForSaleResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetVirtualImageResourceForSale", varargs...)
	ret0, _ := ret[0].(*SetVirtualImageResourceForSaleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetVirtualImageResourceForSale indicates an expected call of SetVirtualImageResourceForSale.
func (mr *MockVirtualImageResourceClientMockRecorder) SetVirtualImageResourceForSale(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetVirtualImageResourceForSale", reflect.TypeOf((*MockVirtualImageResourceClient)(nil).SetVirtualImageResourceForSale), varargs...)
}

// UpdateLevelConfig mocks base method.
func (m *MockVirtualImageResourceClient) UpdateLevelConfig(ctx context.Context, in *UpdateLevelConfigRequest, opts ...grpc.CallOption) (*UpdateLevelConfigResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateLevelConfig", varargs...)
	ret0, _ := ret[0].(*UpdateLevelConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateLevelConfig indicates an expected call of UpdateLevelConfig.
func (mr *MockVirtualImageResourceClientMockRecorder) UpdateLevelConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLevelConfig", reflect.TypeOf((*MockVirtualImageResourceClient)(nil).UpdateLevelConfig), varargs...)
}

// UpdateNoticeCfg mocks base method.
func (m *MockVirtualImageResourceClient) UpdateNoticeCfg(ctx context.Context, in *UpdateNoticeCfgRequest, opts ...grpc.CallOption) (*UpdateNoticeCfgResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateNoticeCfg", varargs...)
	ret0, _ := ret[0].(*UpdateNoticeCfgResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateNoticeCfg indicates an expected call of UpdateNoticeCfg.
func (mr *MockVirtualImageResourceClientMockRecorder) UpdateNoticeCfg(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateNoticeCfg", reflect.TypeOf((*MockVirtualImageResourceClient)(nil).UpdateNoticeCfg), varargs...)
}

// UpdateVirtualImageResourceUrl mocks base method.
func (m *MockVirtualImageResourceClient) UpdateVirtualImageResourceUrl(ctx context.Context, in *UpdateVirtualImageResourceUrlRequest, opts ...grpc.CallOption) (*UpdateVirtualImageResourceUrlResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateVirtualImageResourceUrl", varargs...)
	ret0, _ := ret[0].(*UpdateVirtualImageResourceUrlResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateVirtualImageResourceUrl indicates an expected call of UpdateVirtualImageResourceUrl.
func (mr *MockVirtualImageResourceClientMockRecorder) UpdateVirtualImageResourceUrl(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateVirtualImageResourceUrl", reflect.TypeOf((*MockVirtualImageResourceClient)(nil).UpdateVirtualImageResourceUrl), varargs...)
}

// MockVirtualImageResourceServer is a mock of VirtualImageResourceServer interface.
type MockVirtualImageResourceServer struct {
	ctrl     *gomock.Controller
	recorder *MockVirtualImageResourceServerMockRecorder
}

// MockVirtualImageResourceServerMockRecorder is the mock recorder for MockVirtualImageResourceServer.
type MockVirtualImageResourceServerMockRecorder struct {
	mock *MockVirtualImageResourceServer
}

// NewMockVirtualImageResourceServer creates a new mock instance.
func NewMockVirtualImageResourceServer(ctrl *gomock.Controller) *MockVirtualImageResourceServer {
	mock := &MockVirtualImageResourceServer{ctrl: ctrl}
	mock.recorder = &MockVirtualImageResourceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockVirtualImageResourceServer) EXPECT() *MockVirtualImageResourceServerMockRecorder {
	return m.recorder
}

// AddLevelConfig mocks base method.
func (m *MockVirtualImageResourceServer) AddLevelConfig(ctx context.Context, in *AddLevelConfigRequest) (*AddLevelConfigResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddLevelConfig", ctx, in)
	ret0, _ := ret[0].(*AddLevelConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddLevelConfig indicates an expected call of AddLevelConfig.
func (mr *MockVirtualImageResourceServerMockRecorder) AddLevelConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddLevelConfig", reflect.TypeOf((*MockVirtualImageResourceServer)(nil).AddLevelConfig), ctx, in)
}

// AddVirtualImageResource mocks base method.
func (m *MockVirtualImageResourceServer) AddVirtualImageResource(ctx context.Context, in *AddVirtualImageResourceRequest) (*AddVirtualImageResourceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddVirtualImageResource", ctx, in)
	ret0, _ := ret[0].(*AddVirtualImageResourceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddVirtualImageResource indicates an expected call of AddVirtualImageResource.
func (mr *MockVirtualImageResourceServerMockRecorder) AddVirtualImageResource(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddVirtualImageResource", reflect.TypeOf((*MockVirtualImageResourceServer)(nil).AddVirtualImageResource), ctx, in)
}

// BatchUpdateIcon mocks base method.
func (m *MockVirtualImageResourceServer) BatchUpdateIcon(ctx context.Context, in *BatchUpdateIconRequest) (*BatchUpdateIconResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdateIcon", ctx, in)
	ret0, _ := ret[0].(*BatchUpdateIconResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchUpdateIcon indicates an expected call of BatchUpdateIcon.
func (mr *MockVirtualImageResourceServerMockRecorder) BatchUpdateIcon(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateIcon", reflect.TypeOf((*MockVirtualImageResourceServer)(nil).BatchUpdateIcon), ctx, in)
}

// CloneVirtualImageResource mocks base method.
func (m *MockVirtualImageResourceServer) CloneVirtualImageResource(ctx context.Context, in *CloneVirtualImageResourceRequest) (*CloneVirtualImageResourceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CloneVirtualImageResource", ctx, in)
	ret0, _ := ret[0].(*CloneVirtualImageResourceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CloneVirtualImageResource indicates an expected call of CloneVirtualImageResource.
func (mr *MockVirtualImageResourceServerMockRecorder) CloneVirtualImageResource(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CloneVirtualImageResource", reflect.TypeOf((*MockVirtualImageResourceServer)(nil).CloneVirtualImageResource), ctx, in)
}

// DeleteVirtualImageResource mocks base method.
func (m *MockVirtualImageResourceServer) DeleteVirtualImageResource(ctx context.Context, in *DeleteVirtualImageResourceRequest) (*DeleteVirtualImageResourceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteVirtualImageResource", ctx, in)
	ret0, _ := ret[0].(*DeleteVirtualImageResourceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteVirtualImageResource indicates an expected call of DeleteVirtualImageResource.
func (mr *MockVirtualImageResourceServerMockRecorder) DeleteVirtualImageResource(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteVirtualImageResource", reflect.TypeOf((*MockVirtualImageResourceServer)(nil).DeleteVirtualImageResource), ctx, in)
}

// EditVirtualImageResource mocks base method.
func (m *MockVirtualImageResourceServer) EditVirtualImageResource(ctx context.Context, in *EditVirtualImageResourceRequest) (*EditVirtualImageResourceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EditVirtualImageResource", ctx, in)
	ret0, _ := ret[0].(*EditVirtualImageResourceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EditVirtualImageResource indicates an expected call of EditVirtualImageResource.
func (mr *MockVirtualImageResourceServerMockRecorder) EditVirtualImageResource(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EditVirtualImageResource", reflect.TypeOf((*MockVirtualImageResourceServer)(nil).EditVirtualImageResource), ctx, in)
}

// GetActionResourceMap mocks base method.
func (m *MockVirtualImageResourceServer) GetActionResourceMap(ctx context.Context, in *GetActionResourceMapRequest) (*GetActionResourceMapResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActionResourceMap", ctx, in)
	ret0, _ := ret[0].(*GetActionResourceMapResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActionResourceMap indicates an expected call of GetActionResourceMap.
func (mr *MockVirtualImageResourceServerMockRecorder) GetActionResourceMap(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActionResourceMap", reflect.TypeOf((*MockVirtualImageResourceServer)(nil).GetActionResourceMap), ctx, in)
}

// GetClientListByPage mocks base method.
func (m *MockVirtualImageResourceServer) GetClientListByPage(ctx context.Context, in *GetClientListByPageRequest) (*GetClientListByPageResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClientListByPage", ctx, in)
	ret0, _ := ret[0].(*GetClientListByPageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientListByPage indicates an expected call of GetClientListByPage.
func (mr *MockVirtualImageResourceServerMockRecorder) GetClientListByPage(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientListByPage", reflect.TypeOf((*MockVirtualImageResourceServer)(nil).GetClientListByPage), ctx, in)
}

// GetDefaultResourceList mocks base method.
func (m *MockVirtualImageResourceServer) GetDefaultResourceList(ctx context.Context, in *GetDefaultResourceListRequest) (*GetDefaultResourceListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDefaultResourceList", ctx, in)
	ret0, _ := ret[0].(*GetDefaultResourceListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDefaultResourceList indicates an expected call of GetDefaultResourceList.
func (mr *MockVirtualImageResourceServerMockRecorder) GetDefaultResourceList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDefaultResourceList", reflect.TypeOf((*MockVirtualImageResourceServer)(nil).GetDefaultResourceList), ctx, in)
}

// GetLevelConfig mocks base method.
func (m *MockVirtualImageResourceServer) GetLevelConfig(ctx context.Context, in *GetLevelConfigRequest) (*GetLevelConfigResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLevelConfig", ctx, in)
	ret0, _ := ret[0].(*GetLevelConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLevelConfig indicates an expected call of GetLevelConfig.
func (mr *MockVirtualImageResourceServerMockRecorder) GetLevelConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLevelConfig", reflect.TypeOf((*MockVirtualImageResourceServer)(nil).GetLevelConfig), ctx, in)
}

// GetNoticeCfg mocks base method.
func (m *MockVirtualImageResourceServer) GetNoticeCfg(ctx context.Context, in *GetNoticeCfgRequest) (*GetNoticeCfgResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNoticeCfg", ctx, in)
	ret0, _ := ret[0].(*GetNoticeCfgResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNoticeCfg indicates an expected call of GetNoticeCfg.
func (mr *MockVirtualImageResourceServerMockRecorder) GetNoticeCfg(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNoticeCfg", reflect.TypeOf((*MockVirtualImageResourceServer)(nil).GetNoticeCfg), ctx, in)
}

// GetNoticeCfgCache mocks base method.
func (m *MockVirtualImageResourceServer) GetNoticeCfgCache(ctx context.Context, in *GetNoticeCfgCacheRequest) (*GetNoticeCfgCacheResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNoticeCfgCache", ctx, in)
	ret0, _ := ret[0].(*GetNoticeCfgCacheResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNoticeCfgCache indicates an expected call of GetNoticeCfgCache.
func (mr *MockVirtualImageResourceServerMockRecorder) GetNoticeCfgCache(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNoticeCfgCache", reflect.TypeOf((*MockVirtualImageResourceServer)(nil).GetNoticeCfgCache), ctx, in)
}

// GetVirtualImageResourceBySuit mocks base method.
func (m *MockVirtualImageResourceServer) GetVirtualImageResourceBySuit(ctx context.Context, in *GetVirtualImageResourceBySuitRequest) (*GetVirtualImageResourceBySuitResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVirtualImageResourceBySuit", ctx, in)
	ret0, _ := ret[0].(*GetVirtualImageResourceBySuitResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVirtualImageResourceBySuit indicates an expected call of GetVirtualImageResourceBySuit.
func (mr *MockVirtualImageResourceServerMockRecorder) GetVirtualImageResourceBySuit(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualImageResourceBySuit", reflect.TypeOf((*MockVirtualImageResourceServer)(nil).GetVirtualImageResourceBySuit), ctx, in)
}

// GetVirtualImageResourceCategory mocks base method.
func (m *MockVirtualImageResourceServer) GetVirtualImageResourceCategory(ctx context.Context, in *GetVirtualImageResourceCategoryRequest) (*GetVirtualImageResourceCategoryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVirtualImageResourceCategory", ctx, in)
	ret0, _ := ret[0].(*GetVirtualImageResourceCategoryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVirtualImageResourceCategory indicates an expected call of GetVirtualImageResourceCategory.
func (mr *MockVirtualImageResourceServerMockRecorder) GetVirtualImageResourceCategory(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualImageResourceCategory", reflect.TypeOf((*MockVirtualImageResourceServer)(nil).GetVirtualImageResourceCategory), ctx, in)
}

// GetVirtualImageResourcesByIds mocks base method.
func (m *MockVirtualImageResourceServer) GetVirtualImageResourcesByIds(ctx context.Context, in *GetVirtualImageResourcesByIdsRequest) (*GetVirtualImageResourcesByIdsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVirtualImageResourcesByIds", ctx, in)
	ret0, _ := ret[0].(*GetVirtualImageResourcesByIdsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVirtualImageResourcesByIds indicates an expected call of GetVirtualImageResourcesByIds.
func (mr *MockVirtualImageResourceServerMockRecorder) GetVirtualImageResourcesByIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualImageResourcesByIds", reflect.TypeOf((*MockVirtualImageResourceServer)(nil).GetVirtualImageResourcesByIds), ctx, in)
}

// SearchVirtualImageResource mocks base method.
func (m *MockVirtualImageResourceServer) SearchVirtualImageResource(ctx context.Context, in *SearchVirtualImageResourceRequest) (*SearchVirtualImageResourceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchVirtualImageResource", ctx, in)
	ret0, _ := ret[0].(*SearchVirtualImageResourceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchVirtualImageResource indicates an expected call of SearchVirtualImageResource.
func (mr *MockVirtualImageResourceServerMockRecorder) SearchVirtualImageResource(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchVirtualImageResource", reflect.TypeOf((*MockVirtualImageResourceServer)(nil).SearchVirtualImageResource), ctx, in)
}

// SetVirtualImageResourceForSale mocks base method.
func (m *MockVirtualImageResourceServer) SetVirtualImageResourceForSale(ctx context.Context, in *SetVirtualImageResourceForSaleRequest) (*SetVirtualImageResourceForSaleResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetVirtualImageResourceForSale", ctx, in)
	ret0, _ := ret[0].(*SetVirtualImageResourceForSaleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetVirtualImageResourceForSale indicates an expected call of SetVirtualImageResourceForSale.
func (mr *MockVirtualImageResourceServerMockRecorder) SetVirtualImageResourceForSale(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetVirtualImageResourceForSale", reflect.TypeOf((*MockVirtualImageResourceServer)(nil).SetVirtualImageResourceForSale), ctx, in)
}

// UpdateLevelConfig mocks base method.
func (m *MockVirtualImageResourceServer) UpdateLevelConfig(ctx context.Context, in *UpdateLevelConfigRequest) (*UpdateLevelConfigResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateLevelConfig", ctx, in)
	ret0, _ := ret[0].(*UpdateLevelConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateLevelConfig indicates an expected call of UpdateLevelConfig.
func (mr *MockVirtualImageResourceServerMockRecorder) UpdateLevelConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLevelConfig", reflect.TypeOf((*MockVirtualImageResourceServer)(nil).UpdateLevelConfig), ctx, in)
}

// UpdateNoticeCfg mocks base method.
func (m *MockVirtualImageResourceServer) UpdateNoticeCfg(ctx context.Context, in *UpdateNoticeCfgRequest) (*UpdateNoticeCfgResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateNoticeCfg", ctx, in)
	ret0, _ := ret[0].(*UpdateNoticeCfgResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateNoticeCfg indicates an expected call of UpdateNoticeCfg.
func (mr *MockVirtualImageResourceServerMockRecorder) UpdateNoticeCfg(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateNoticeCfg", reflect.TypeOf((*MockVirtualImageResourceServer)(nil).UpdateNoticeCfg), ctx, in)
}

// UpdateVirtualImageResourceUrl mocks base method.
func (m *MockVirtualImageResourceServer) UpdateVirtualImageResourceUrl(ctx context.Context, in *UpdateVirtualImageResourceUrlRequest) (*UpdateVirtualImageResourceUrlResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateVirtualImageResourceUrl", ctx, in)
	ret0, _ := ret[0].(*UpdateVirtualImageResourceUrlResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateVirtualImageResourceUrl indicates an expected call of UpdateVirtualImageResourceUrl.
func (mr *MockVirtualImageResourceServerMockRecorder) UpdateVirtualImageResourceUrl(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateVirtualImageResourceUrl", reflect.TypeOf((*MockVirtualImageResourceServer)(nil).UpdateVirtualImageResourceUrl), ctx, in)
}
