// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/virtual-image-resource/virtual-image-resource.proto

package virtual_image_resource // import "golang.52tt.com/protocol/services/virtual-image-resource"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type VirtualImageResourceStatus int32

const (
	VirtualImageResourceStatus_VIRTUAL_IMAGE_RESOURCE_STATUS_UNKNOWN                  VirtualImageResourceStatus = 0
	VirtualImageResourceStatus_VIRTUAL_IMAGE_RESOURCE_STATUS_NORMAL                   VirtualImageResourceStatus = 1
	VirtualImageResourceStatus_VIRTUAL_IMAGE_RESOURCE_STATUS_DELETED                  VirtualImageResourceStatus = 2
	VirtualImageResourceStatus_VIRTUAL_IMAGE_RESOURCE_STATUS_UPLOADED_PENDING_TEST    VirtualImageResourceStatus = 3
	VirtualImageResourceStatus_VIRTUAL_IMAGE_RESOURCE_STATUS_TESTING                  VirtualImageResourceStatus = 4
	VirtualImageResourceStatus_VIRTUAL_IMAGE_RESOURCE_STATUS_RE_UPLOADED_PENDING_TEST VirtualImageResourceStatus = 5
	VirtualImageResourceStatus_VIRTUAL_IMAGE_RESOURCE_STATUS_ISSUE_PENDING_FIX        VirtualImageResourceStatus = 6
	VirtualImageResourceStatus_VIRTUAL_IMAGE_RESOURCE_STATUS_ORIGINAL_OUTPUT          VirtualImageResourceStatus = 7
	VirtualImageResourceStatus_VIRTUAL_IMAGE_RESOURCE_STATUS_TEST_PAUSED              VirtualImageResourceStatus = 8
	VirtualImageResourceStatus_VIRTUAL_IMAGE_RESOURCE_STATUS_ACTION_IN_PROGRESS       VirtualImageResourceStatus = 9
	VirtualImageResourceStatus_VIRTUAL_IMAGE_RESOURCE_STATUS_ORIGINAL_IN_PROGRESS     VirtualImageResourceStatus = 10
	VirtualImageResourceStatus_VIRTUAL_IMAGE_RESOURCE_STATUS_PENDING_PRODUCTION       VirtualImageResourceStatus = 11
	VirtualImageResourceStatus_VIRTUAL_IMAGE_RESOURCE_STATUS_TEST_COMPLETED           VirtualImageResourceStatus = 12
)

var VirtualImageResourceStatus_name = map[int32]string{
	0:  "VIRTUAL_IMAGE_RESOURCE_STATUS_UNKNOWN",
	1:  "VIRTUAL_IMAGE_RESOURCE_STATUS_NORMAL",
	2:  "VIRTUAL_IMAGE_RESOURCE_STATUS_DELETED",
	3:  "VIRTUAL_IMAGE_RESOURCE_STATUS_UPLOADED_PENDING_TEST",
	4:  "VIRTUAL_IMAGE_RESOURCE_STATUS_TESTING",
	5:  "VIRTUAL_IMAGE_RESOURCE_STATUS_RE_UPLOADED_PENDING_TEST",
	6:  "VIRTUAL_IMAGE_RESOURCE_STATUS_ISSUE_PENDING_FIX",
	7:  "VIRTUAL_IMAGE_RESOURCE_STATUS_ORIGINAL_OUTPUT",
	8:  "VIRTUAL_IMAGE_RESOURCE_STATUS_TEST_PAUSED",
	9:  "VIRTUAL_IMAGE_RESOURCE_STATUS_ACTION_IN_PROGRESS",
	10: "VIRTUAL_IMAGE_RESOURCE_STATUS_ORIGINAL_IN_PROGRESS",
	11: "VIRTUAL_IMAGE_RESOURCE_STATUS_PENDING_PRODUCTION",
	12: "VIRTUAL_IMAGE_RESOURCE_STATUS_TEST_COMPLETED",
}
var VirtualImageResourceStatus_value = map[string]int32{
	"VIRTUAL_IMAGE_RESOURCE_STATUS_UNKNOWN":                  0,
	"VIRTUAL_IMAGE_RESOURCE_STATUS_NORMAL":                   1,
	"VIRTUAL_IMAGE_RESOURCE_STATUS_DELETED":                  2,
	"VIRTUAL_IMAGE_RESOURCE_STATUS_UPLOADED_PENDING_TEST":    3,
	"VIRTUAL_IMAGE_RESOURCE_STATUS_TESTING":                  4,
	"VIRTUAL_IMAGE_RESOURCE_STATUS_RE_UPLOADED_PENDING_TEST": 5,
	"VIRTUAL_IMAGE_RESOURCE_STATUS_ISSUE_PENDING_FIX":        6,
	"VIRTUAL_IMAGE_RESOURCE_STATUS_ORIGINAL_OUTPUT":          7,
	"VIRTUAL_IMAGE_RESOURCE_STATUS_TEST_PAUSED":              8,
	"VIRTUAL_IMAGE_RESOURCE_STATUS_ACTION_IN_PROGRESS":       9,
	"VIRTUAL_IMAGE_RESOURCE_STATUS_ORIGINAL_IN_PROGRESS":     10,
	"VIRTUAL_IMAGE_RESOURCE_STATUS_PENDING_PRODUCTION":       11,
	"VIRTUAL_IMAGE_RESOURCE_STATUS_TEST_COMPLETED":           12,
}

func (x VirtualImageResourceStatus) String() string {
	return proto.EnumName(VirtualImageResourceStatus_name, int32(x))
}
func (VirtualImageResourceStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{0}
}

//
// 一级分类
// 套装 - 物品没有套装
// 捏脸
// 服饰
// 氛围
type VirtualImageResourceCategory int32

const (
	VirtualImageResourceCategory_VIRTUAL_IMAGE_RESOURCE_CATEGORY_UNKNOWN      VirtualImageResourceCategory = 0
	VirtualImageResourceCategory_VIRTUAL_IMAGE_RESOURCE_CATEGORY_FACE_SUIT    VirtualImageResourceCategory = 1
	VirtualImageResourceCategory_VIRTUAL_IMAGE_RESOURCE_CATEGORY_FACE_SHAPING VirtualImageResourceCategory = 2
	VirtualImageResourceCategory_VIRTUAL_IMAGE_RESOURCE_CATEGORY_DRESS        VirtualImageResourceCategory = 3
	VirtualImageResourceCategory_VIRTUAL_IMAGE_RESOURCE_CATEGORY_ATMOSPHERE   VirtualImageResourceCategory = 4
)

var VirtualImageResourceCategory_name = map[int32]string{
	0: "VIRTUAL_IMAGE_RESOURCE_CATEGORY_UNKNOWN",
	1: "VIRTUAL_IMAGE_RESOURCE_CATEGORY_FACE_SUIT",
	2: "VIRTUAL_IMAGE_RESOURCE_CATEGORY_FACE_SHAPING",
	3: "VIRTUAL_IMAGE_RESOURCE_CATEGORY_DRESS",
	4: "VIRTUAL_IMAGE_RESOURCE_CATEGORY_ATMOSPHERE",
}
var VirtualImageResourceCategory_value = map[string]int32{
	"VIRTUAL_IMAGE_RESOURCE_CATEGORY_UNKNOWN":      0,
	"VIRTUAL_IMAGE_RESOURCE_CATEGORY_FACE_SUIT":    1,
	"VIRTUAL_IMAGE_RESOURCE_CATEGORY_FACE_SHAPING": 2,
	"VIRTUAL_IMAGE_RESOURCE_CATEGORY_DRESS":        3,
	"VIRTUAL_IMAGE_RESOURCE_CATEGORY_ATMOSPHERE":   4,
}

func (x VirtualImageResourceCategory) String() string {
	return proto.EnumName(VirtualImageResourceCategory_name, int32(x))
}
func (VirtualImageResourceCategory) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{1}
}

//
// see https://q9jvw0u5f5.feishu.cn/mindnotes/OkSKbOXctmHrv4nd6Fqcp4mMnxh
type VirtualImageResourceSubCategory int32

const (
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_UNKNOWN               VirtualImageResourceSubCategory = 0
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SUIT                  VirtualImageResourceSubCategory = 1000
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_FACE_TEMPLATE         VirtualImageResourceSubCategory = 1001
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_FACE_SHAPE            VirtualImageResourceSubCategory = 1002
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_HAIRSTYLE             VirtualImageResourceSubCategory = 1003
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_EYEBROWS              VirtualImageResourceSubCategory = 1004
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_EYES                  VirtualImageResourceSubCategory = 1005
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_MOUTH                 VirtualImageResourceSubCategory = 1006
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_MAKEUP                VirtualImageResourceSubCategory = 1007
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_FULL_OUTFIT           VirtualImageResourceSubCategory = 1008
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_UPPER_OUTFIT          VirtualImageResourceSubCategory = 1009
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_LOWER_OUTFIT          VirtualImageResourceSubCategory = 1010
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SHOES                 VirtualImageResourceSubCategory = 1011
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SOCKS                 VirtualImageResourceSubCategory = 1012
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_HAT                   VirtualImageResourceSubCategory = 1013
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_EARRINGS              VirtualImageResourceSubCategory = 1014
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_FACE_ACCESSORIES      VirtualImageResourceSubCategory = 1015
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_NECK_ACCESSORIES      VirtualImageResourceSubCategory = 1016
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_HANDBAG               VirtualImageResourceSubCategory = 1017
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_HANDHELD_ITEMS        VirtualImageResourceSubCategory = 1018
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_WINGS                 VirtualImageResourceSubCategory = 1019
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_BODY_ACCESSORIES      VirtualImageResourceSubCategory = 1020
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SITTING_POSE          VirtualImageResourceSubCategory = 1021
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_CHAIR                 VirtualImageResourceSubCategory = 1022
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_BACKGROUND            VirtualImageResourceSubCategory = 1023
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_ROOM_ENTRANCE_EFFECTS VirtualImageResourceSubCategory = 1024
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TONG_KONG             VirtualImageResourceSubCategory = 1025
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_OTHER                 VirtualImageResourceSubCategory = 1026
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_ACCESSORY             VirtualImageResourceSubCategory = 1027
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_WAI_TAO               VirtualImageResourceSubCategory = 1028
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_PI_FENG               VirtualImageResourceSubCategory = 1029
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TOU_SHI               VirtualImageResourceSubCategory = 1030
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SHOU_SHI              VirtualImageResourceSubCategory = 1031
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_JIE_ZHI               VirtualImageResourceSubCategory = 1032
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TUI_SHI               VirtualImageResourceSubCategory = 1033
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SHANG_SHEN            VirtualImageResourceSubCategory = 1500
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_BO_ZI                 VirtualImageResourceSubCategory = 1501
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_XIA_SHEN              VirtualImageResourceSubCategory = 1502
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_XIONG_BU              VirtualImageResourceSubCategory = 1503
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TUI                   VirtualImageResourceSubCategory = 1504
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SHOU_L                VirtualImageResourceSubCategory = 1507
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SHOU_R                VirtualImageResourceSubCategory = 1508
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_ER_DUO                VirtualImageResourceSubCategory = 1509
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_BI_ZI                 VirtualImageResourceSubCategory = 1510
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_LEG_L                 VirtualImageResourceSubCategory = 1511
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_LEG_R                 VirtualImageResourceSubCategory = 1512
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_FOOT_L                VirtualImageResourceSubCategory = 1513
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_FOOT_R                VirtualImageResourceSubCategory = 1514
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_HAND_L                VirtualImageResourceSubCategory = 1515
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_HAND_R                VirtualImageResourceSubCategory = 1516
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SHOW_VAP              VirtualImageResourceSubCategory = 10000
	VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_OTHER_VAP             VirtualImageResourceSubCategory = 10001
)

var VirtualImageResourceSubCategory_name = map[int32]string{
	0:     "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_UNKNOWN",
	1000:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SUIT",
	1001:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_FACE_TEMPLATE",
	1002:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_FACE_SHAPE",
	1003:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_HAIRSTYLE",
	1004:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_EYEBROWS",
	1005:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_EYES",
	1006:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_MOUTH",
	1007:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_MAKEUP",
	1008:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_FULL_OUTFIT",
	1009:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_UPPER_OUTFIT",
	1010:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_LOWER_OUTFIT",
	1011:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SHOES",
	1012:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SOCKS",
	1013:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_HAT",
	1014:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_EARRINGS",
	1015:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_FACE_ACCESSORIES",
	1016:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_NECK_ACCESSORIES",
	1017:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_HANDBAG",
	1018:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_HANDHELD_ITEMS",
	1019:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_WINGS",
	1020:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_BODY_ACCESSORIES",
	1021:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SITTING_POSE",
	1022:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_CHAIR",
	1023:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_BACKGROUND",
	1024:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_ROOM_ENTRANCE_EFFECTS",
	1025:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TONG_KONG",
	1026:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_OTHER",
	1027:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_ACCESSORY",
	1028:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_WAI_TAO",
	1029:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_PI_FENG",
	1030:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TOU_SHI",
	1031:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SHOU_SHI",
	1032:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_JIE_ZHI",
	1033:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TUI_SHI",
	1500:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SHANG_SHEN",
	1501:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_BO_ZI",
	1502:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_XIA_SHEN",
	1503:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_XIONG_BU",
	1504:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TUI",
	1507:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SHOU_L",
	1508:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SHOU_R",
	1509:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_ER_DUO",
	1510:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_BI_ZI",
	1511:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_LEG_L",
	1512:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_LEG_R",
	1513:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_FOOT_L",
	1514:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_FOOT_R",
	1515:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_HAND_L",
	1516:  "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_HAND_R",
	10000: "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SHOW_VAP",
	10001: "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_OTHER_VAP",
}
var VirtualImageResourceSubCategory_value = map[string]int32{
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_UNKNOWN":               0,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SUIT":                  1000,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_FACE_TEMPLATE":         1001,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_FACE_SHAPE":            1002,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_HAIRSTYLE":             1003,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_EYEBROWS":              1004,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_EYES":                  1005,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_MOUTH":                 1006,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_MAKEUP":                1007,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_FULL_OUTFIT":           1008,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_UPPER_OUTFIT":          1009,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_LOWER_OUTFIT":          1010,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SHOES":                 1011,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SOCKS":                 1012,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_HAT":                   1013,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_EARRINGS":              1014,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_FACE_ACCESSORIES":      1015,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_NECK_ACCESSORIES":      1016,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_HANDBAG":               1017,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_HANDHELD_ITEMS":        1018,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_WINGS":                 1019,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_BODY_ACCESSORIES":      1020,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SITTING_POSE":          1021,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_CHAIR":                 1022,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_BACKGROUND":            1023,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_ROOM_ENTRANCE_EFFECTS": 1024,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TONG_KONG":             1025,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_OTHER":                 1026,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_ACCESSORY":             1027,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_WAI_TAO":               1028,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_PI_FENG":               1029,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TOU_SHI":               1030,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SHOU_SHI":              1031,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_JIE_ZHI":               1032,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TUI_SHI":               1033,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SHANG_SHEN":            1500,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_BO_ZI":                 1501,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_XIA_SHEN":              1502,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_XIONG_BU":              1503,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TUI":                   1504,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SHOU_L":                1507,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SHOU_R":                1508,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_ER_DUO":                1509,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_BI_ZI":                 1510,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_LEG_L":                 1511,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_LEG_R":                 1512,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_FOOT_L":                1513,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_FOOT_R":                1514,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_HAND_L":                1515,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_HAND_R":                1516,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SHOW_VAP":              10000,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_OTHER_VAP":             10001,
}

func (x VirtualImageResourceSubCategory) String() string {
	return proto.EnumName(VirtualImageResourceSubCategory_name, int32(x))
}
func (VirtualImageResourceSubCategory) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{2}
}

type VirtualImageResourceType int32

const (
	VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_UNKNOWN                VirtualImageResourceType = 0
	VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_SKELETON               VirtualImageResourceType = 1
	VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_SKIN                   VirtualImageResourceType = 2
	VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_BACKGROUND             VirtualImageResourceType = 3
	VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_FOLLOW                 VirtualImageResourceType = 4
	VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_SHOW_VAP               VirtualImageResourceType = 5
	VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_DEFAULT_BACKGROUND_PNG VirtualImageResourceType = 6
	VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_ANIMATION              VirtualImageResourceType = 7
	VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_SKIN_FULL_OUTFIT       VirtualImageResourceType = 8
	VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_INFORMATION_CARD_VAP   VirtualImageResourceType = 9
	VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_CP_SKELETON            VirtualImageResourceType = 10
	VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_SIDE_SKELETON          VirtualImageResourceType = 11
	VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_CP_SKIN                VirtualImageResourceType = 12
	VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_BASE_SKELETON          VirtualImageResourceType = 13
	VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_BASE_SIDE_SKELETON     VirtualImageResourceType = 14
	VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_BASE_CP_SKELETON       VirtualImageResourceType = 15
)

var VirtualImageResourceType_name = map[int32]string{
	0:  "VIRTUAL_IMAGE_RESOURCE_TYPE_UNKNOWN",
	1:  "VIRTUAL_IMAGE_RESOURCE_TYPE_SKELETON",
	2:  "VIRTUAL_IMAGE_RESOURCE_TYPE_SKIN",
	3:  "VIRTUAL_IMAGE_RESOURCE_TYPE_BACKGROUND",
	4:  "VIRTUAL_IMAGE_RESOURCE_TYPE_FOLLOW",
	5:  "VIRTUAL_IMAGE_RESOURCE_TYPE_SHOW_VAP",
	6:  "VIRTUAL_IMAGE_RESOURCE_TYPE_DEFAULT_BACKGROUND_PNG",
	7:  "VIRTUAL_IMAGE_RESOURCE_TYPE_ANIMATION",
	8:  "VIRTUAL_IMAGE_RESOURCE_TYPE_SKIN_FULL_OUTFIT",
	9:  "VIRTUAL_IMAGE_RESOURCE_TYPE_INFORMATION_CARD_VAP",
	10: "VIRTUAL_IMAGE_RESOURCE_TYPE_CP_SKELETON",
	11: "VIRTUAL_IMAGE_RESOURCE_TYPE_SIDE_SKELETON",
	12: "VIRTUAL_IMAGE_RESOURCE_TYPE_CP_SKIN",
	13: "VIRTUAL_IMAGE_RESOURCE_TYPE_BASE_SKELETON",
	14: "VIRTUAL_IMAGE_RESOURCE_TYPE_BASE_SIDE_SKELETON",
	15: "VIRTUAL_IMAGE_RESOURCE_TYPE_BASE_CP_SKELETON",
}
var VirtualImageResourceType_value = map[string]int32{
	"VIRTUAL_IMAGE_RESOURCE_TYPE_UNKNOWN":                0,
	"VIRTUAL_IMAGE_RESOURCE_TYPE_SKELETON":               1,
	"VIRTUAL_IMAGE_RESOURCE_TYPE_SKIN":                   2,
	"VIRTUAL_IMAGE_RESOURCE_TYPE_BACKGROUND":             3,
	"VIRTUAL_IMAGE_RESOURCE_TYPE_FOLLOW":                 4,
	"VIRTUAL_IMAGE_RESOURCE_TYPE_SHOW_VAP":               5,
	"VIRTUAL_IMAGE_RESOURCE_TYPE_DEFAULT_BACKGROUND_PNG": 6,
	"VIRTUAL_IMAGE_RESOURCE_TYPE_ANIMATION":              7,
	"VIRTUAL_IMAGE_RESOURCE_TYPE_SKIN_FULL_OUTFIT":       8,
	"VIRTUAL_IMAGE_RESOURCE_TYPE_INFORMATION_CARD_VAP":   9,
	"VIRTUAL_IMAGE_RESOURCE_TYPE_CP_SKELETON":            10,
	"VIRTUAL_IMAGE_RESOURCE_TYPE_SIDE_SKELETON":          11,
	"VIRTUAL_IMAGE_RESOURCE_TYPE_CP_SKIN":                12,
	"VIRTUAL_IMAGE_RESOURCE_TYPE_BASE_SKELETON":          13,
	"VIRTUAL_IMAGE_RESOURCE_TYPE_BASE_SIDE_SKELETON":     14,
	"VIRTUAL_IMAGE_RESOURCE_TYPE_BASE_CP_SKELETON":       15,
}

func (x VirtualImageResourceType) String() string {
	return proto.EnumName(VirtualImageResourceType_name, int32(x))
}
func (VirtualImageResourceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{3}
}

type VirtualImageResourceSex int32

const (
	VirtualImageResourceSex_VIRTUAL_IMAGE_RESOURCE_SEX_UNKNOWN                   VirtualImageResourceSex = 0
	VirtualImageResourceSex_VIRTUAL_IMAGE_RESOURCE_SEX_MALE                      VirtualImageResourceSex = 1
	VirtualImageResourceSex_VIRTUAL_IMAGE_RESOURCE_SEX_FEMALE                    VirtualImageResourceSex = 2
	VirtualImageResourceSex_VIRTUAL_IMAGE_RESOURCE_SEX_COMMON                    VirtualImageResourceSex = 3
	VirtualImageResourceSex_VIRTUAL_IMAGE_RESOURCE_SEX_CP_FRONT                  VirtualImageResourceSex = 4
	VirtualImageResourceSex_VIRTUAL_IMAGE_RESOURCE_SEX_CP_SIDE                   VirtualImageResourceSex = 5
	VirtualImageResourceSex_VIRTUAL_IMAGE_RESOURCE_SEX_MALE_FRONT_FEMALE_SIDE    VirtualImageResourceSex = 6
	VirtualImageResourceSex_VIRTUAL_IMAGE_RESOURCE_SEX_CP_MALE_SIDE_FEMALE_FRONT VirtualImageResourceSex = 7
)

var VirtualImageResourceSex_name = map[int32]string{
	0: "VIRTUAL_IMAGE_RESOURCE_SEX_UNKNOWN",
	1: "VIRTUAL_IMAGE_RESOURCE_SEX_MALE",
	2: "VIRTUAL_IMAGE_RESOURCE_SEX_FEMALE",
	3: "VIRTUAL_IMAGE_RESOURCE_SEX_COMMON",
	4: "VIRTUAL_IMAGE_RESOURCE_SEX_CP_FRONT",
	5: "VIRTUAL_IMAGE_RESOURCE_SEX_CP_SIDE",
	6: "VIRTUAL_IMAGE_RESOURCE_SEX_MALE_FRONT_FEMALE_SIDE",
	7: "VIRTUAL_IMAGE_RESOURCE_SEX_CP_MALE_SIDE_FEMALE_FRONT",
}
var VirtualImageResourceSex_value = map[string]int32{
	"VIRTUAL_IMAGE_RESOURCE_SEX_UNKNOWN":                   0,
	"VIRTUAL_IMAGE_RESOURCE_SEX_MALE":                      1,
	"VIRTUAL_IMAGE_RESOURCE_SEX_FEMALE":                    2,
	"VIRTUAL_IMAGE_RESOURCE_SEX_COMMON":                    3,
	"VIRTUAL_IMAGE_RESOURCE_SEX_CP_FRONT":                  4,
	"VIRTUAL_IMAGE_RESOURCE_SEX_CP_SIDE":                   5,
	"VIRTUAL_IMAGE_RESOURCE_SEX_MALE_FRONT_FEMALE_SIDE":    6,
	"VIRTUAL_IMAGE_RESOURCE_SEX_CP_MALE_SIDE_FEMALE_FRONT": 7,
}

func (x VirtualImageResourceSex) String() string {
	return proto.EnumName(VirtualImageResourceSex_name, int32(x))
}
func (VirtualImageResourceSex) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{4}
}

type VirtualImageResourceCommodityFilter int32

const (
	VirtualImageResourceCommodityFilter_VIRTUAL_IMAGE_RESOURCE_COMMODITY_FILTER_UNKNOWN       VirtualImageResourceCommodityFilter = 0
	VirtualImageResourceCommodityFilter_VIRTUAL_IMAGE_RESOURCE_COMMODITY_FILTER_COMMODITY     VirtualImageResourceCommodityFilter = 1
	VirtualImageResourceCommodityFilter_VIRTUAL_IMAGE_RESOURCE_COMMODITY_FILTER_NOT_COMMODITY VirtualImageResourceCommodityFilter = 2
	VirtualImageResourceCommodityFilter_VIRTUAL_IMAGE_RESOURCE_COMMODITY_FILTER_ALL           VirtualImageResourceCommodityFilter = 3
)

var VirtualImageResourceCommodityFilter_name = map[int32]string{
	0: "VIRTUAL_IMAGE_RESOURCE_COMMODITY_FILTER_UNKNOWN",
	1: "VIRTUAL_IMAGE_RESOURCE_COMMODITY_FILTER_COMMODITY",
	2: "VIRTUAL_IMAGE_RESOURCE_COMMODITY_FILTER_NOT_COMMODITY",
	3: "VIRTUAL_IMAGE_RESOURCE_COMMODITY_FILTER_ALL",
}
var VirtualImageResourceCommodityFilter_value = map[string]int32{
	"VIRTUAL_IMAGE_RESOURCE_COMMODITY_FILTER_UNKNOWN":       0,
	"VIRTUAL_IMAGE_RESOURCE_COMMODITY_FILTER_COMMODITY":     1,
	"VIRTUAL_IMAGE_RESOURCE_COMMODITY_FILTER_NOT_COMMODITY": 2,
	"VIRTUAL_IMAGE_RESOURCE_COMMODITY_FILTER_ALL":           3,
}

func (x VirtualImageResourceCommodityFilter) String() string {
	return proto.EnumName(VirtualImageResourceCommodityFilter_name, int32(x))
}
func (VirtualImageResourceCommodityFilter) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{5}
}

type VirtualImageResourceInfo struct {
	Id               uint32            `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	SkinName         string            `protobuf:"bytes,2,opt,name=skin_name,json=skinName,proto3" json:"skin_name,omitempty"`
	ResourceUrl      string            `protobuf:"bytes,3,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url,omitempty"`
	Essential        bool              `protobuf:"varint,4,opt,name=essential,proto3" json:"essential,omitempty"`
	EncryptKey       string            `protobuf:"bytes,5,opt,name=encrypt_key,json=encryptKey,proto3" json:"encrypt_key,omitempty"`
	Md5              string            `protobuf:"bytes,6,opt,name=md5,proto3" json:"md5,omitempty"`
	ResourceType     uint32            `protobuf:"varint,7,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	Category         uint32            `protobuf:"varint,8,opt,name=category,proto3" json:"category,omitempty"`
	SubCategory      uint32            `protobuf:"varint,9,opt,name=sub_category,json=subCategory,proto3" json:"sub_category,omitempty"`
	SplineVersion    string            `protobuf:"bytes,10,opt,name=spline_version,json=splineVersion,proto3" json:"spline_version,omitempty"`
	DisplayName      string            `protobuf:"bytes,11,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	IconUrl          string            `protobuf:"bytes,12,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	Sex              uint32            `protobuf:"varint,13,opt,name=sex,proto3" json:"sex,omitempty"`
	LevelIcon        string            `protobuf:"bytes,14,opt,name=level_icon,json=levelIcon,proto3" json:"level_icon,omitempty"`
	Level            uint32            `protobuf:"varint,15,opt,name=level,proto3" json:"level,omitempty"`
	Status           uint32            `protobuf:"varint,16,opt,name=status,proto3" json:"status,omitempty"`
	DefaultSuit      string            `protobuf:"bytes,17,opt,name=default_suit,json=defaultSuit,proto3" json:"default_suit,omitempty"`
	ShelfTime        uint32            `protobuf:"varint,18,opt,name=shelf_time,json=shelfTime,proto3" json:"shelf_time,omitempty"`
	ExpireTime       uint32            `protobuf:"varint,19,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	Operator         string            `protobuf:"bytes,20,opt,name=operator,proto3" json:"operator,omitempty"`
	CreateTime       uint32            `protobuf:"varint,21,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UpdateTime       uint32            `protobuf:"varint,22,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	IsOnline         bool              `protobuf:"varint,23,opt,name=is_online,json=isOnline,proto3" json:"is_online,omitempty"`
	IsCommodity      bool              `protobuf:"varint,24,opt,name=is_commodity,json=isCommodity,proto3" json:"is_commodity,omitempty"`
	ResourceName     string            `protobuf:"bytes,25,opt,name=resource_name,json=resourceName,proto3" json:"resource_name,omitempty"`
	Version          uint32            `protobuf:"varint,26,opt,name=version,proto3" json:"version,omitempty"`
	IosMd5           string            `protobuf:"bytes,27,opt,name=ios_md5,json=iosMd5,proto3" json:"ios_md5,omitempty"`
	IosResourceUrl   string            `protobuf:"bytes,28,opt,name=ios_resource_url,json=iosResourceUrl,proto3" json:"ios_resource_url,omitempty"`
	LevelWebp        string            `protobuf:"bytes,29,opt,name=level_webp,json=levelWebp,proto3" json:"level_webp,omitempty"`
	ScaleAble        bool              `protobuf:"varint,30,opt,name=scale_able,json=scaleAble,proto3" json:"scale_able,omitempty"`
	DefaultAnimation string            `protobuf:"bytes,31,opt,name=default_animation,json=defaultAnimation,proto3" json:"default_animation,omitempty"`
	IsDeleted        bool              `protobuf:"varint,32,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted,omitempty"`
	AttachmentName   string            `protobuf:"bytes,33,opt,name=attachment_name,json=attachmentName,proto3" json:"attachment_name,omitempty"`
	SkinFacing       uint32            `protobuf:"varint,34,opt,name=skin_facing,json=skinFacing,proto3" json:"skin_facing,omitempty"`
	ResourcePrefix   string            `protobuf:"bytes,35,opt,name=resource_prefix,json=resourcePrefix,proto3" json:"resource_prefix,omitempty"`
	CustomMap        map[string]string `protobuf:"bytes,36,rep,name=custom_map,json=customMap,proto3" json:"custom_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 按照骨骼名称作为key区分皮肤资源信息
	// 例如:
	// 1. 正身皮肤资源信息, key: base_boy.zip, base_girl.zip
	// 2. 侧身皮肤资源信息, key: base_cboy.zip, base_cgirl.zip
	// 3. 双人正正皮肤资源信息, key: base_zboy_zgirl.zip
	// 4. 双人正侧皮肤资源信息, key: base_zboy_cgirl.zip
	// 5. 双人侧侧皮肤资源信息, key: base_cboy_cgirl.zip
	SkinMap       map[string]*SkinInfo `protobuf:"bytes,37,rep,name=skin_map,json=skinMap,proto3" json:"skin_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	IosSkinMap    map[string]*SkinInfo `protobuf:"bytes,38,rep,name=ios_skin_map,json=iosSkinMap,proto3" json:"ios_skin_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	IosVersion    uint32               `protobuf:"varint,39,opt,name=ios_version,json=iosVersion,proto3" json:"ios_version,omitempty"`
	IsNewResource bool                 `protobuf:"varint,40,opt,name=is_new_resource,json=isNewResource,proto3" json:"is_new_resource,omitempty"`
	// texture资源地址map   ios_low                        : url - astc(zip)
	//                    ios_high
	//                    android_low                    : url -  astc(zip)
	//                    pc_low                         : url -  ktx2
	TextureUrlMap        map[string]string `protobuf:"bytes,41,rep,name=texture_url_map,json=textureUrlMap,proto3" json:"texture_url_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *VirtualImageResourceInfo) Reset()         { *m = VirtualImageResourceInfo{} }
func (m *VirtualImageResourceInfo) String() string { return proto.CompactTextString(m) }
func (*VirtualImageResourceInfo) ProtoMessage()    {}
func (*VirtualImageResourceInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{0}
}
func (m *VirtualImageResourceInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VirtualImageResourceInfo.Unmarshal(m, b)
}
func (m *VirtualImageResourceInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VirtualImageResourceInfo.Marshal(b, m, deterministic)
}
func (dst *VirtualImageResourceInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VirtualImageResourceInfo.Merge(dst, src)
}
func (m *VirtualImageResourceInfo) XXX_Size() int {
	return xxx_messageInfo_VirtualImageResourceInfo.Size(m)
}
func (m *VirtualImageResourceInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_VirtualImageResourceInfo.DiscardUnknown(m)
}

var xxx_messageInfo_VirtualImageResourceInfo proto.InternalMessageInfo

func (m *VirtualImageResourceInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *VirtualImageResourceInfo) GetSkinName() string {
	if m != nil {
		return m.SkinName
	}
	return ""
}

func (m *VirtualImageResourceInfo) GetResourceUrl() string {
	if m != nil {
		return m.ResourceUrl
	}
	return ""
}

func (m *VirtualImageResourceInfo) GetEssential() bool {
	if m != nil {
		return m.Essential
	}
	return false
}

func (m *VirtualImageResourceInfo) GetEncryptKey() string {
	if m != nil {
		return m.EncryptKey
	}
	return ""
}

func (m *VirtualImageResourceInfo) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

func (m *VirtualImageResourceInfo) GetResourceType() uint32 {
	if m != nil {
		return m.ResourceType
	}
	return 0
}

func (m *VirtualImageResourceInfo) GetCategory() uint32 {
	if m != nil {
		return m.Category
	}
	return 0
}

func (m *VirtualImageResourceInfo) GetSubCategory() uint32 {
	if m != nil {
		return m.SubCategory
	}
	return 0
}

func (m *VirtualImageResourceInfo) GetSplineVersion() string {
	if m != nil {
		return m.SplineVersion
	}
	return ""
}

func (m *VirtualImageResourceInfo) GetDisplayName() string {
	if m != nil {
		return m.DisplayName
	}
	return ""
}

func (m *VirtualImageResourceInfo) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *VirtualImageResourceInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *VirtualImageResourceInfo) GetLevelIcon() string {
	if m != nil {
		return m.LevelIcon
	}
	return ""
}

func (m *VirtualImageResourceInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *VirtualImageResourceInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *VirtualImageResourceInfo) GetDefaultSuit() string {
	if m != nil {
		return m.DefaultSuit
	}
	return ""
}

func (m *VirtualImageResourceInfo) GetShelfTime() uint32 {
	if m != nil {
		return m.ShelfTime
	}
	return 0
}

func (m *VirtualImageResourceInfo) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *VirtualImageResourceInfo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *VirtualImageResourceInfo) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *VirtualImageResourceInfo) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *VirtualImageResourceInfo) GetIsOnline() bool {
	if m != nil {
		return m.IsOnline
	}
	return false
}

func (m *VirtualImageResourceInfo) GetIsCommodity() bool {
	if m != nil {
		return m.IsCommodity
	}
	return false
}

func (m *VirtualImageResourceInfo) GetResourceName() string {
	if m != nil {
		return m.ResourceName
	}
	return ""
}

func (m *VirtualImageResourceInfo) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *VirtualImageResourceInfo) GetIosMd5() string {
	if m != nil {
		return m.IosMd5
	}
	return ""
}

func (m *VirtualImageResourceInfo) GetIosResourceUrl() string {
	if m != nil {
		return m.IosResourceUrl
	}
	return ""
}

func (m *VirtualImageResourceInfo) GetLevelWebp() string {
	if m != nil {
		return m.LevelWebp
	}
	return ""
}

func (m *VirtualImageResourceInfo) GetScaleAble() bool {
	if m != nil {
		return m.ScaleAble
	}
	return false
}

func (m *VirtualImageResourceInfo) GetDefaultAnimation() string {
	if m != nil {
		return m.DefaultAnimation
	}
	return ""
}

func (m *VirtualImageResourceInfo) GetIsDeleted() bool {
	if m != nil {
		return m.IsDeleted
	}
	return false
}

func (m *VirtualImageResourceInfo) GetAttachmentName() string {
	if m != nil {
		return m.AttachmentName
	}
	return ""
}

func (m *VirtualImageResourceInfo) GetSkinFacing() uint32 {
	if m != nil {
		return m.SkinFacing
	}
	return 0
}

func (m *VirtualImageResourceInfo) GetResourcePrefix() string {
	if m != nil {
		return m.ResourcePrefix
	}
	return ""
}

func (m *VirtualImageResourceInfo) GetCustomMap() map[string]string {
	if m != nil {
		return m.CustomMap
	}
	return nil
}

func (m *VirtualImageResourceInfo) GetSkinMap() map[string]*SkinInfo {
	if m != nil {
		return m.SkinMap
	}
	return nil
}

func (m *VirtualImageResourceInfo) GetIosSkinMap() map[string]*SkinInfo {
	if m != nil {
		return m.IosSkinMap
	}
	return nil
}

func (m *VirtualImageResourceInfo) GetIosVersion() uint32 {
	if m != nil {
		return m.IosVersion
	}
	return 0
}

func (m *VirtualImageResourceInfo) GetIsNewResource() bool {
	if m != nil {
		return m.IsNewResource
	}
	return false
}

func (m *VirtualImageResourceInfo) GetTextureUrlMap() map[string]string {
	if m != nil {
		return m.TextureUrlMap
	}
	return nil
}

type SkinInfo struct {
	Url                  string   `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	MinBonesVersion      uint32   `protobuf:"varint,2,opt,name=min_bones_version,json=minBonesVersion,proto3" json:"min_bones_version,omitempty"`
	Md5                  string   `protobuf:"bytes,3,opt,name=md5,proto3" json:"md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SkinInfo) Reset()         { *m = SkinInfo{} }
func (m *SkinInfo) String() string { return proto.CompactTextString(m) }
func (*SkinInfo) ProtoMessage()    {}
func (*SkinInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{1}
}
func (m *SkinInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SkinInfo.Unmarshal(m, b)
}
func (m *SkinInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SkinInfo.Marshal(b, m, deterministic)
}
func (dst *SkinInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SkinInfo.Merge(dst, src)
}
func (m *SkinInfo) XXX_Size() int {
	return xxx_messageInfo_SkinInfo.Size(m)
}
func (m *SkinInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SkinInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SkinInfo proto.InternalMessageInfo

func (m *SkinInfo) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *SkinInfo) GetMinBonesVersion() uint32 {
	if m != nil {
		return m.MinBonesVersion
	}
	return 0
}

func (m *SkinInfo) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

type AddVirtualImageResourceRequest struct {
	Resources            []*VirtualImageResourceInfo `protobuf:"bytes,1,rep,name=resources,proto3" json:"resources,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *AddVirtualImageResourceRequest) Reset()         { *m = AddVirtualImageResourceRequest{} }
func (m *AddVirtualImageResourceRequest) String() string { return proto.CompactTextString(m) }
func (*AddVirtualImageResourceRequest) ProtoMessage()    {}
func (*AddVirtualImageResourceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{2}
}
func (m *AddVirtualImageResourceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddVirtualImageResourceRequest.Unmarshal(m, b)
}
func (m *AddVirtualImageResourceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddVirtualImageResourceRequest.Marshal(b, m, deterministic)
}
func (dst *AddVirtualImageResourceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddVirtualImageResourceRequest.Merge(dst, src)
}
func (m *AddVirtualImageResourceRequest) XXX_Size() int {
	return xxx_messageInfo_AddVirtualImageResourceRequest.Size(m)
}
func (m *AddVirtualImageResourceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddVirtualImageResourceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddVirtualImageResourceRequest proto.InternalMessageInfo

func (m *AddVirtualImageResourceRequest) GetResources() []*VirtualImageResourceInfo {
	if m != nil {
		return m.Resources
	}
	return nil
}

type AddVirtualImageResourceResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddVirtualImageResourceResponse) Reset()         { *m = AddVirtualImageResourceResponse{} }
func (m *AddVirtualImageResourceResponse) String() string { return proto.CompactTextString(m) }
func (*AddVirtualImageResourceResponse) ProtoMessage()    {}
func (*AddVirtualImageResourceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{3}
}
func (m *AddVirtualImageResourceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddVirtualImageResourceResponse.Unmarshal(m, b)
}
func (m *AddVirtualImageResourceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddVirtualImageResourceResponse.Marshal(b, m, deterministic)
}
func (dst *AddVirtualImageResourceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddVirtualImageResourceResponse.Merge(dst, src)
}
func (m *AddVirtualImageResourceResponse) XXX_Size() int {
	return xxx_messageInfo_AddVirtualImageResourceResponse.Size(m)
}
func (m *AddVirtualImageResourceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddVirtualImageResourceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddVirtualImageResourceResponse proto.InternalMessageInfo

type CloneVirtualImageResourceRequest struct {
	Resources            []*VirtualImageResourceInfo `protobuf:"bytes,1,rep,name=resources,proto3" json:"resources,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *CloneVirtualImageResourceRequest) Reset()         { *m = CloneVirtualImageResourceRequest{} }
func (m *CloneVirtualImageResourceRequest) String() string { return proto.CompactTextString(m) }
func (*CloneVirtualImageResourceRequest) ProtoMessage()    {}
func (*CloneVirtualImageResourceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{4}
}
func (m *CloneVirtualImageResourceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CloneVirtualImageResourceRequest.Unmarshal(m, b)
}
func (m *CloneVirtualImageResourceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CloneVirtualImageResourceRequest.Marshal(b, m, deterministic)
}
func (dst *CloneVirtualImageResourceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CloneVirtualImageResourceRequest.Merge(dst, src)
}
func (m *CloneVirtualImageResourceRequest) XXX_Size() int {
	return xxx_messageInfo_CloneVirtualImageResourceRequest.Size(m)
}
func (m *CloneVirtualImageResourceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CloneVirtualImageResourceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CloneVirtualImageResourceRequest proto.InternalMessageInfo

func (m *CloneVirtualImageResourceRequest) GetResources() []*VirtualImageResourceInfo {
	if m != nil {
		return m.Resources
	}
	return nil
}

type CloneVirtualImageResourceResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CloneVirtualImageResourceResponse) Reset()         { *m = CloneVirtualImageResourceResponse{} }
func (m *CloneVirtualImageResourceResponse) String() string { return proto.CompactTextString(m) }
func (*CloneVirtualImageResourceResponse) ProtoMessage()    {}
func (*CloneVirtualImageResourceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{5}
}
func (m *CloneVirtualImageResourceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CloneVirtualImageResourceResponse.Unmarshal(m, b)
}
func (m *CloneVirtualImageResourceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CloneVirtualImageResourceResponse.Marshal(b, m, deterministic)
}
func (dst *CloneVirtualImageResourceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CloneVirtualImageResourceResponse.Merge(dst, src)
}
func (m *CloneVirtualImageResourceResponse) XXX_Size() int {
	return xxx_messageInfo_CloneVirtualImageResourceResponse.Size(m)
}
func (m *CloneVirtualImageResourceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CloneVirtualImageResourceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CloneVirtualImageResourceResponse proto.InternalMessageInfo

type SearchVirtualImageResourceRequest struct {
	Id          []uint32 `protobuf:"varint,1,rep,packed,name=id,proto3" json:"id,omitempty"`
	Name        []string `protobuf:"bytes,2,rep,name=name,proto3" json:"name,omitempty"`
	Category    uint32   `protobuf:"varint,3,opt,name=category,proto3" json:"category,omitempty"`
	SubCategory uint32   `protobuf:"varint,4,opt,name=sub_category,json=subCategory,proto3" json:"sub_category,omitempty"`
	Sex         uint32   `protobuf:"varint,5,opt,name=sex,proto3" json:"sex,omitempty"`
	Level       uint32   `protobuf:"varint,6,opt,name=level,proto3" json:"level,omitempty"`
	Suit        string   `protobuf:"bytes,7,opt,name=suit,proto3" json:"suit,omitempty"`
	// bool is_commodity = 8;      // 是否已配置过商品
	Offset               uint32   `protobuf:"varint,9,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,10,opt,name=limit,proto3" json:"limit,omitempty"`
	SkinName             string   `protobuf:"bytes,11,opt,name=skin_name,json=skinName,proto3" json:"skin_name,omitempty"`
	CommodityFilter      uint32   `protobuf:"varint,12,opt,name=commodity_filter,json=commodityFilter,proto3" json:"commodity_filter,omitempty"`
	IsEssential          uint32   `protobuf:"varint,13,opt,name=is_essential,json=isEssential,proto3" json:"is_essential,omitempty"`
	ResourceType         []uint32 `protobuf:"varint,14,rep,packed,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	SkinNameList         []string `protobuf:"bytes,15,rep,name=skin_name_list,json=skinNameList,proto3" json:"skin_name_list,omitempty"`
	ResourceNameList     []string `protobuf:"bytes,16,rep,name=resource_name_list,json=resourceNameList,proto3" json:"resource_name_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchVirtualImageResourceRequest) Reset()         { *m = SearchVirtualImageResourceRequest{} }
func (m *SearchVirtualImageResourceRequest) String() string { return proto.CompactTextString(m) }
func (*SearchVirtualImageResourceRequest) ProtoMessage()    {}
func (*SearchVirtualImageResourceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{6}
}
func (m *SearchVirtualImageResourceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchVirtualImageResourceRequest.Unmarshal(m, b)
}
func (m *SearchVirtualImageResourceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchVirtualImageResourceRequest.Marshal(b, m, deterministic)
}
func (dst *SearchVirtualImageResourceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchVirtualImageResourceRequest.Merge(dst, src)
}
func (m *SearchVirtualImageResourceRequest) XXX_Size() int {
	return xxx_messageInfo_SearchVirtualImageResourceRequest.Size(m)
}
func (m *SearchVirtualImageResourceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchVirtualImageResourceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SearchVirtualImageResourceRequest proto.InternalMessageInfo

func (m *SearchVirtualImageResourceRequest) GetId() []uint32 {
	if m != nil {
		return m.Id
	}
	return nil
}

func (m *SearchVirtualImageResourceRequest) GetName() []string {
	if m != nil {
		return m.Name
	}
	return nil
}

func (m *SearchVirtualImageResourceRequest) GetCategory() uint32 {
	if m != nil {
		return m.Category
	}
	return 0
}

func (m *SearchVirtualImageResourceRequest) GetSubCategory() uint32 {
	if m != nil {
		return m.SubCategory
	}
	return 0
}

func (m *SearchVirtualImageResourceRequest) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *SearchVirtualImageResourceRequest) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *SearchVirtualImageResourceRequest) GetSuit() string {
	if m != nil {
		return m.Suit
	}
	return ""
}

func (m *SearchVirtualImageResourceRequest) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *SearchVirtualImageResourceRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *SearchVirtualImageResourceRequest) GetSkinName() string {
	if m != nil {
		return m.SkinName
	}
	return ""
}

func (m *SearchVirtualImageResourceRequest) GetCommodityFilter() uint32 {
	if m != nil {
		return m.CommodityFilter
	}
	return 0
}

func (m *SearchVirtualImageResourceRequest) GetIsEssential() uint32 {
	if m != nil {
		return m.IsEssential
	}
	return 0
}

func (m *SearchVirtualImageResourceRequest) GetResourceType() []uint32 {
	if m != nil {
		return m.ResourceType
	}
	return nil
}

func (m *SearchVirtualImageResourceRequest) GetSkinNameList() []string {
	if m != nil {
		return m.SkinNameList
	}
	return nil
}

func (m *SearchVirtualImageResourceRequest) GetResourceNameList() []string {
	if m != nil {
		return m.ResourceNameList
	}
	return nil
}

type SearchVirtualImageResourceResponse struct {
	Offset               uint32                      `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32                      `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Resources            []*VirtualImageResourceInfo `protobuf:"bytes,3,rep,name=resources,proto3" json:"resources,omitempty"`
	Total                uint32                      `protobuf:"varint,4,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *SearchVirtualImageResourceResponse) Reset()         { *m = SearchVirtualImageResourceResponse{} }
func (m *SearchVirtualImageResourceResponse) String() string { return proto.CompactTextString(m) }
func (*SearchVirtualImageResourceResponse) ProtoMessage()    {}
func (*SearchVirtualImageResourceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{7}
}
func (m *SearchVirtualImageResourceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchVirtualImageResourceResponse.Unmarshal(m, b)
}
func (m *SearchVirtualImageResourceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchVirtualImageResourceResponse.Marshal(b, m, deterministic)
}
func (dst *SearchVirtualImageResourceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchVirtualImageResourceResponse.Merge(dst, src)
}
func (m *SearchVirtualImageResourceResponse) XXX_Size() int {
	return xxx_messageInfo_SearchVirtualImageResourceResponse.Size(m)
}
func (m *SearchVirtualImageResourceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchVirtualImageResourceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SearchVirtualImageResourceResponse proto.InternalMessageInfo

func (m *SearchVirtualImageResourceResponse) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *SearchVirtualImageResourceResponse) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *SearchVirtualImageResourceResponse) GetResources() []*VirtualImageResourceInfo {
	if m != nil {
		return m.Resources
	}
	return nil
}

func (m *SearchVirtualImageResourceResponse) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetVirtualImageResourceBySuitRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVirtualImageResourceBySuitRequest) Reset()         { *m = GetVirtualImageResourceBySuitRequest{} }
func (m *GetVirtualImageResourceBySuitRequest) String() string { return proto.CompactTextString(m) }
func (*GetVirtualImageResourceBySuitRequest) ProtoMessage()    {}
func (*GetVirtualImageResourceBySuitRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{8}
}
func (m *GetVirtualImageResourceBySuitRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVirtualImageResourceBySuitRequest.Unmarshal(m, b)
}
func (m *GetVirtualImageResourceBySuitRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVirtualImageResourceBySuitRequest.Marshal(b, m, deterministic)
}
func (dst *GetVirtualImageResourceBySuitRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVirtualImageResourceBySuitRequest.Merge(dst, src)
}
func (m *GetVirtualImageResourceBySuitRequest) XXX_Size() int {
	return xxx_messageInfo_GetVirtualImageResourceBySuitRequest.Size(m)
}
func (m *GetVirtualImageResourceBySuitRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVirtualImageResourceBySuitRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetVirtualImageResourceBySuitRequest proto.InternalMessageInfo

type VirtualImageResourceSuitInfo struct {
	Suit                 string                      `protobuf:"bytes,1,opt,name=suit,proto3" json:"suit,omitempty"`
	Resources            []*VirtualImageResourceInfo `protobuf:"bytes,2,rep,name=resources,proto3" json:"resources,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *VirtualImageResourceSuitInfo) Reset()         { *m = VirtualImageResourceSuitInfo{} }
func (m *VirtualImageResourceSuitInfo) String() string { return proto.CompactTextString(m) }
func (*VirtualImageResourceSuitInfo) ProtoMessage()    {}
func (*VirtualImageResourceSuitInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{9}
}
func (m *VirtualImageResourceSuitInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VirtualImageResourceSuitInfo.Unmarshal(m, b)
}
func (m *VirtualImageResourceSuitInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VirtualImageResourceSuitInfo.Marshal(b, m, deterministic)
}
func (dst *VirtualImageResourceSuitInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VirtualImageResourceSuitInfo.Merge(dst, src)
}
func (m *VirtualImageResourceSuitInfo) XXX_Size() int {
	return xxx_messageInfo_VirtualImageResourceSuitInfo.Size(m)
}
func (m *VirtualImageResourceSuitInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_VirtualImageResourceSuitInfo.DiscardUnknown(m)
}

var xxx_messageInfo_VirtualImageResourceSuitInfo proto.InternalMessageInfo

func (m *VirtualImageResourceSuitInfo) GetSuit() string {
	if m != nil {
		return m.Suit
	}
	return ""
}

func (m *VirtualImageResourceSuitInfo) GetResources() []*VirtualImageResourceInfo {
	if m != nil {
		return m.Resources
	}
	return nil
}

type GetVirtualImageResourceBySuitResponse struct {
	Resources            []*VirtualImageResourceSuitInfo `protobuf:"bytes,1,rep,name=resources,proto3" json:"resources,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *GetVirtualImageResourceBySuitResponse) Reset()         { *m = GetVirtualImageResourceBySuitResponse{} }
func (m *GetVirtualImageResourceBySuitResponse) String() string { return proto.CompactTextString(m) }
func (*GetVirtualImageResourceBySuitResponse) ProtoMessage()    {}
func (*GetVirtualImageResourceBySuitResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{10}
}
func (m *GetVirtualImageResourceBySuitResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVirtualImageResourceBySuitResponse.Unmarshal(m, b)
}
func (m *GetVirtualImageResourceBySuitResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVirtualImageResourceBySuitResponse.Marshal(b, m, deterministic)
}
func (dst *GetVirtualImageResourceBySuitResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVirtualImageResourceBySuitResponse.Merge(dst, src)
}
func (m *GetVirtualImageResourceBySuitResponse) XXX_Size() int {
	return xxx_messageInfo_GetVirtualImageResourceBySuitResponse.Size(m)
}
func (m *GetVirtualImageResourceBySuitResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVirtualImageResourceBySuitResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetVirtualImageResourceBySuitResponse proto.InternalMessageInfo

func (m *GetVirtualImageResourceBySuitResponse) GetResources() []*VirtualImageResourceSuitInfo {
	if m != nil {
		return m.Resources
	}
	return nil
}

type EditVirtualImageResourceRequest struct {
	Resource             *VirtualImageResourceInfo `protobuf:"bytes,1,opt,name=resource,proto3" json:"resource,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *EditVirtualImageResourceRequest) Reset()         { *m = EditVirtualImageResourceRequest{} }
func (m *EditVirtualImageResourceRequest) String() string { return proto.CompactTextString(m) }
func (*EditVirtualImageResourceRequest) ProtoMessage()    {}
func (*EditVirtualImageResourceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{11}
}
func (m *EditVirtualImageResourceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EditVirtualImageResourceRequest.Unmarshal(m, b)
}
func (m *EditVirtualImageResourceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EditVirtualImageResourceRequest.Marshal(b, m, deterministic)
}
func (dst *EditVirtualImageResourceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EditVirtualImageResourceRequest.Merge(dst, src)
}
func (m *EditVirtualImageResourceRequest) XXX_Size() int {
	return xxx_messageInfo_EditVirtualImageResourceRequest.Size(m)
}
func (m *EditVirtualImageResourceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_EditVirtualImageResourceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_EditVirtualImageResourceRequest proto.InternalMessageInfo

func (m *EditVirtualImageResourceRequest) GetResource() *VirtualImageResourceInfo {
	if m != nil {
		return m.Resource
	}
	return nil
}

type EditVirtualImageResourceResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EditVirtualImageResourceResponse) Reset()         { *m = EditVirtualImageResourceResponse{} }
func (m *EditVirtualImageResourceResponse) String() string { return proto.CompactTextString(m) }
func (*EditVirtualImageResourceResponse) ProtoMessage()    {}
func (*EditVirtualImageResourceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{12}
}
func (m *EditVirtualImageResourceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EditVirtualImageResourceResponse.Unmarshal(m, b)
}
func (m *EditVirtualImageResourceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EditVirtualImageResourceResponse.Marshal(b, m, deterministic)
}
func (dst *EditVirtualImageResourceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EditVirtualImageResourceResponse.Merge(dst, src)
}
func (m *EditVirtualImageResourceResponse) XXX_Size() int {
	return xxx_messageInfo_EditVirtualImageResourceResponse.Size(m)
}
func (m *EditVirtualImageResourceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_EditVirtualImageResourceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_EditVirtualImageResourceResponse proto.InternalMessageInfo

type GetClientListByPageRequest struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	LatestId             uint32   `protobuf:"varint,3,opt,name=latest_id,json=latestId,proto3" json:"latest_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetClientListByPageRequest) Reset()         { *m = GetClientListByPageRequest{} }
func (m *GetClientListByPageRequest) String() string { return proto.CompactTextString(m) }
func (*GetClientListByPageRequest) ProtoMessage()    {}
func (*GetClientListByPageRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{13}
}
func (m *GetClientListByPageRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetClientListByPageRequest.Unmarshal(m, b)
}
func (m *GetClientListByPageRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetClientListByPageRequest.Marshal(b, m, deterministic)
}
func (dst *GetClientListByPageRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetClientListByPageRequest.Merge(dst, src)
}
func (m *GetClientListByPageRequest) XXX_Size() int {
	return xxx_messageInfo_GetClientListByPageRequest.Size(m)
}
func (m *GetClientListByPageRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetClientListByPageRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetClientListByPageRequest proto.InternalMessageInfo

func (m *GetClientListByPageRequest) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetClientListByPageRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetClientListByPageRequest) GetLatestId() uint32 {
	if m != nil {
		return m.LatestId
	}
	return 0
}

type GetClientListByPageResponse struct {
	Offset               uint32                      `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32                      `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Resources            []*VirtualImageResourceInfo `protobuf:"bytes,3,rep,name=resources,proto3" json:"resources,omitempty"`
	Total                uint32                      `protobuf:"varint,4,opt,name=total,proto3" json:"total,omitempty"`
	LatestId             uint32                      `protobuf:"varint,5,opt,name=latest_id,json=latestId,proto3" json:"latest_id,omitempty"`
	IsEnd                bool                        `protobuf:"varint,6,opt,name=is_end,json=isEnd,proto3" json:"is_end,omitempty"`
	DownloadUrl          string                      `protobuf:"bytes,7,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"`
	DownloadUrlIos       string                      `protobuf:"bytes,8,opt,name=download_url_ios,json=downloadUrlIos,proto3" json:"download_url_ios,omitempty"`
	DownloadMd5          string                      `protobuf:"bytes,9,opt,name=download_md5,json=downloadMd5,proto3" json:"download_md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetClientListByPageResponse) Reset()         { *m = GetClientListByPageResponse{} }
func (m *GetClientListByPageResponse) String() string { return proto.CompactTextString(m) }
func (*GetClientListByPageResponse) ProtoMessage()    {}
func (*GetClientListByPageResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{14}
}
func (m *GetClientListByPageResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetClientListByPageResponse.Unmarshal(m, b)
}
func (m *GetClientListByPageResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetClientListByPageResponse.Marshal(b, m, deterministic)
}
func (dst *GetClientListByPageResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetClientListByPageResponse.Merge(dst, src)
}
func (m *GetClientListByPageResponse) XXX_Size() int {
	return xxx_messageInfo_GetClientListByPageResponse.Size(m)
}
func (m *GetClientListByPageResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetClientListByPageResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetClientListByPageResponse proto.InternalMessageInfo

func (m *GetClientListByPageResponse) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetClientListByPageResponse) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetClientListByPageResponse) GetResources() []*VirtualImageResourceInfo {
	if m != nil {
		return m.Resources
	}
	return nil
}

func (m *GetClientListByPageResponse) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetClientListByPageResponse) GetLatestId() uint32 {
	if m != nil {
		return m.LatestId
	}
	return 0
}

func (m *GetClientListByPageResponse) GetIsEnd() bool {
	if m != nil {
		return m.IsEnd
	}
	return false
}

func (m *GetClientListByPageResponse) GetDownloadUrl() string {
	if m != nil {
		return m.DownloadUrl
	}
	return ""
}

func (m *GetClientListByPageResponse) GetDownloadUrlIos() string {
	if m != nil {
		return m.DownloadUrlIos
	}
	return ""
}

func (m *GetClientListByPageResponse) GetDownloadMd5() string {
	if m != nil {
		return m.DownloadMd5
	}
	return ""
}

type DeleteVirtualImageResourceRequest struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteVirtualImageResourceRequest) Reset()         { *m = DeleteVirtualImageResourceRequest{} }
func (m *DeleteVirtualImageResourceRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteVirtualImageResourceRequest) ProtoMessage()    {}
func (*DeleteVirtualImageResourceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{15}
}
func (m *DeleteVirtualImageResourceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteVirtualImageResourceRequest.Unmarshal(m, b)
}
func (m *DeleteVirtualImageResourceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteVirtualImageResourceRequest.Marshal(b, m, deterministic)
}
func (dst *DeleteVirtualImageResourceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteVirtualImageResourceRequest.Merge(dst, src)
}
func (m *DeleteVirtualImageResourceRequest) XXX_Size() int {
	return xxx_messageInfo_DeleteVirtualImageResourceRequest.Size(m)
}
func (m *DeleteVirtualImageResourceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteVirtualImageResourceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteVirtualImageResourceRequest proto.InternalMessageInfo

func (m *DeleteVirtualImageResourceRequest) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DeleteVirtualImageResourceResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteVirtualImageResourceResponse) Reset()         { *m = DeleteVirtualImageResourceResponse{} }
func (m *DeleteVirtualImageResourceResponse) String() string { return proto.CompactTextString(m) }
func (*DeleteVirtualImageResourceResponse) ProtoMessage()    {}
func (*DeleteVirtualImageResourceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{16}
}
func (m *DeleteVirtualImageResourceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteVirtualImageResourceResponse.Unmarshal(m, b)
}
func (m *DeleteVirtualImageResourceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteVirtualImageResourceResponse.Marshal(b, m, deterministic)
}
func (dst *DeleteVirtualImageResourceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteVirtualImageResourceResponse.Merge(dst, src)
}
func (m *DeleteVirtualImageResourceResponse) XXX_Size() int {
	return xxx_messageInfo_DeleteVirtualImageResourceResponse.Size(m)
}
func (m *DeleteVirtualImageResourceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteVirtualImageResourceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteVirtualImageResourceResponse proto.InternalMessageInfo

type GetVirtualImageResourcesByIdsRequest struct {
	Ids                  []uint32 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVirtualImageResourcesByIdsRequest) Reset()         { *m = GetVirtualImageResourcesByIdsRequest{} }
func (m *GetVirtualImageResourcesByIdsRequest) String() string { return proto.CompactTextString(m) }
func (*GetVirtualImageResourcesByIdsRequest) ProtoMessage()    {}
func (*GetVirtualImageResourcesByIdsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{17}
}
func (m *GetVirtualImageResourcesByIdsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVirtualImageResourcesByIdsRequest.Unmarshal(m, b)
}
func (m *GetVirtualImageResourcesByIdsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVirtualImageResourcesByIdsRequest.Marshal(b, m, deterministic)
}
func (dst *GetVirtualImageResourcesByIdsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVirtualImageResourcesByIdsRequest.Merge(dst, src)
}
func (m *GetVirtualImageResourcesByIdsRequest) XXX_Size() int {
	return xxx_messageInfo_GetVirtualImageResourcesByIdsRequest.Size(m)
}
func (m *GetVirtualImageResourcesByIdsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVirtualImageResourcesByIdsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetVirtualImageResourcesByIdsRequest proto.InternalMessageInfo

func (m *GetVirtualImageResourcesByIdsRequest) GetIds() []uint32 {
	if m != nil {
		return m.Ids
	}
	return nil
}

type GetVirtualImageResourcesByIdsResponse struct {
	Resources            []*VirtualImageResourceInfo `protobuf:"bytes,1,rep,name=resources,proto3" json:"resources,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetVirtualImageResourcesByIdsResponse) Reset()         { *m = GetVirtualImageResourcesByIdsResponse{} }
func (m *GetVirtualImageResourcesByIdsResponse) String() string { return proto.CompactTextString(m) }
func (*GetVirtualImageResourcesByIdsResponse) ProtoMessage()    {}
func (*GetVirtualImageResourcesByIdsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{18}
}
func (m *GetVirtualImageResourcesByIdsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVirtualImageResourcesByIdsResponse.Unmarshal(m, b)
}
func (m *GetVirtualImageResourcesByIdsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVirtualImageResourcesByIdsResponse.Marshal(b, m, deterministic)
}
func (dst *GetVirtualImageResourcesByIdsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVirtualImageResourcesByIdsResponse.Merge(dst, src)
}
func (m *GetVirtualImageResourcesByIdsResponse) XXX_Size() int {
	return xxx_messageInfo_GetVirtualImageResourcesByIdsResponse.Size(m)
}
func (m *GetVirtualImageResourcesByIdsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVirtualImageResourcesByIdsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetVirtualImageResourcesByIdsResponse proto.InternalMessageInfo

func (m *GetVirtualImageResourcesByIdsResponse) GetResources() []*VirtualImageResourceInfo {
	if m != nil {
		return m.Resources
	}
	return nil
}

type SetVirtualImageResourceForSaleRequest struct {
	Id                   []uint32 `protobuf:"varint,1,rep,packed,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetVirtualImageResourceForSaleRequest) Reset()         { *m = SetVirtualImageResourceForSaleRequest{} }
func (m *SetVirtualImageResourceForSaleRequest) String() string { return proto.CompactTextString(m) }
func (*SetVirtualImageResourceForSaleRequest) ProtoMessage()    {}
func (*SetVirtualImageResourceForSaleRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{19}
}
func (m *SetVirtualImageResourceForSaleRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetVirtualImageResourceForSaleRequest.Unmarshal(m, b)
}
func (m *SetVirtualImageResourceForSaleRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetVirtualImageResourceForSaleRequest.Marshal(b, m, deterministic)
}
func (dst *SetVirtualImageResourceForSaleRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetVirtualImageResourceForSaleRequest.Merge(dst, src)
}
func (m *SetVirtualImageResourceForSaleRequest) XXX_Size() int {
	return xxx_messageInfo_SetVirtualImageResourceForSaleRequest.Size(m)
}
func (m *SetVirtualImageResourceForSaleRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetVirtualImageResourceForSaleRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetVirtualImageResourceForSaleRequest proto.InternalMessageInfo

func (m *SetVirtualImageResourceForSaleRequest) GetId() []uint32 {
	if m != nil {
		return m.Id
	}
	return nil
}

type SetVirtualImageResourceForSaleResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetVirtualImageResourceForSaleResponse) Reset() {
	*m = SetVirtualImageResourceForSaleResponse{}
}
func (m *SetVirtualImageResourceForSaleResponse) String() string { return proto.CompactTextString(m) }
func (*SetVirtualImageResourceForSaleResponse) ProtoMessage()    {}
func (*SetVirtualImageResourceForSaleResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{20}
}
func (m *SetVirtualImageResourceForSaleResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetVirtualImageResourceForSaleResponse.Unmarshal(m, b)
}
func (m *SetVirtualImageResourceForSaleResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetVirtualImageResourceForSaleResponse.Marshal(b, m, deterministic)
}
func (dst *SetVirtualImageResourceForSaleResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetVirtualImageResourceForSaleResponse.Merge(dst, src)
}
func (m *SetVirtualImageResourceForSaleResponse) XXX_Size() int {
	return xxx_messageInfo_SetVirtualImageResourceForSaleResponse.Size(m)
}
func (m *SetVirtualImageResourceForSaleResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetVirtualImageResourceForSaleResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetVirtualImageResourceForSaleResponse proto.InternalMessageInfo

type UpdateVirtualImageResourceUrlRequest struct {
	SkinName             string                      `protobuf:"bytes,1,opt,name=skin_name,json=skinName,proto3" json:"skin_name,omitempty"`
	ResourceUrl          string                      `protobuf:"bytes,2,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url,omitempty"`
	EncryptKey           string                      `protobuf:"bytes,3,opt,name=encrypt_key,json=encryptKey,proto3" json:"encrypt_key,omitempty"`
	Md5                  string                      `protobuf:"bytes,4,opt,name=md5,proto3" json:"md5,omitempty"`
	Resources            []*VirtualImageResourceInfo `protobuf:"bytes,5,rep,name=resources,proto3" json:"resources,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *UpdateVirtualImageResourceUrlRequest) Reset()         { *m = UpdateVirtualImageResourceUrlRequest{} }
func (m *UpdateVirtualImageResourceUrlRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateVirtualImageResourceUrlRequest) ProtoMessage()    {}
func (*UpdateVirtualImageResourceUrlRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{21}
}
func (m *UpdateVirtualImageResourceUrlRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateVirtualImageResourceUrlRequest.Unmarshal(m, b)
}
func (m *UpdateVirtualImageResourceUrlRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateVirtualImageResourceUrlRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateVirtualImageResourceUrlRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateVirtualImageResourceUrlRequest.Merge(dst, src)
}
func (m *UpdateVirtualImageResourceUrlRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateVirtualImageResourceUrlRequest.Size(m)
}
func (m *UpdateVirtualImageResourceUrlRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateVirtualImageResourceUrlRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateVirtualImageResourceUrlRequest proto.InternalMessageInfo

func (m *UpdateVirtualImageResourceUrlRequest) GetSkinName() string {
	if m != nil {
		return m.SkinName
	}
	return ""
}

func (m *UpdateVirtualImageResourceUrlRequest) GetResourceUrl() string {
	if m != nil {
		return m.ResourceUrl
	}
	return ""
}

func (m *UpdateVirtualImageResourceUrlRequest) GetEncryptKey() string {
	if m != nil {
		return m.EncryptKey
	}
	return ""
}

func (m *UpdateVirtualImageResourceUrlRequest) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

func (m *UpdateVirtualImageResourceUrlRequest) GetResources() []*VirtualImageResourceInfo {
	if m != nil {
		return m.Resources
	}
	return nil
}

type UpdateVirtualImageResourceUrlResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateVirtualImageResourceUrlResponse) Reset()         { *m = UpdateVirtualImageResourceUrlResponse{} }
func (m *UpdateVirtualImageResourceUrlResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateVirtualImageResourceUrlResponse) ProtoMessage()    {}
func (*UpdateVirtualImageResourceUrlResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{22}
}
func (m *UpdateVirtualImageResourceUrlResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateVirtualImageResourceUrlResponse.Unmarshal(m, b)
}
func (m *UpdateVirtualImageResourceUrlResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateVirtualImageResourceUrlResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateVirtualImageResourceUrlResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateVirtualImageResourceUrlResponse.Merge(dst, src)
}
func (m *UpdateVirtualImageResourceUrlResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateVirtualImageResourceUrlResponse.Size(m)
}
func (m *UpdateVirtualImageResourceUrlResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateVirtualImageResourceUrlResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateVirtualImageResourceUrlResponse proto.InternalMessageInfo

// 一级品类信息
type VirtualImageParentCategoryInfo struct {
	Category             uint32   `protobuf:"varint,1,opt,name=category,proto3" json:"category,omitempty"`
	CategoryName         string   `protobuf:"bytes,2,opt,name=category_name,json=categoryName,proto3" json:"category_name,omitempty"`
	CategoryType         uint32   `protobuf:"varint,3,opt,name=category_type,json=categoryType,proto3" json:"category_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VirtualImageParentCategoryInfo) Reset()         { *m = VirtualImageParentCategoryInfo{} }
func (m *VirtualImageParentCategoryInfo) String() string { return proto.CompactTextString(m) }
func (*VirtualImageParentCategoryInfo) ProtoMessage()    {}
func (*VirtualImageParentCategoryInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{23}
}
func (m *VirtualImageParentCategoryInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VirtualImageParentCategoryInfo.Unmarshal(m, b)
}
func (m *VirtualImageParentCategoryInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VirtualImageParentCategoryInfo.Marshal(b, m, deterministic)
}
func (dst *VirtualImageParentCategoryInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VirtualImageParentCategoryInfo.Merge(dst, src)
}
func (m *VirtualImageParentCategoryInfo) XXX_Size() int {
	return xxx_messageInfo_VirtualImageParentCategoryInfo.Size(m)
}
func (m *VirtualImageParentCategoryInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_VirtualImageParentCategoryInfo.DiscardUnknown(m)
}

var xxx_messageInfo_VirtualImageParentCategoryInfo proto.InternalMessageInfo

func (m *VirtualImageParentCategoryInfo) GetCategory() uint32 {
	if m != nil {
		return m.Category
	}
	return 0
}

func (m *VirtualImageParentCategoryInfo) GetCategoryName() string {
	if m != nil {
		return m.CategoryName
	}
	return ""
}

func (m *VirtualImageParentCategoryInfo) GetCategoryType() uint32 {
	if m != nil {
		return m.CategoryType
	}
	return 0
}

// 二级品类信息
type VirtualImageSubCategoryInfo struct {
	SubCategory                  uint32   `protobuf:"varint,1,opt,name=sub_category,json=subCategory,proto3" json:"sub_category,omitempty"`
	SubCategoryName              string   `protobuf:"bytes,2,opt,name=sub_category_name,json=subCategoryName,proto3" json:"sub_category_name,omitempty"`
	SubCategoryImgUrl            string   `protobuf:"bytes,3,opt,name=sub_category_img_url,json=subCategoryImgUrl,proto3" json:"sub_category_img_url,omitempty"`
	ParentCategory               uint32   `protobuf:"varint,4,opt,name=parent_category,json=parentCategory,proto3" json:"parent_category,omitempty"`
	SubCategoryType              uint32   `protobuf:"varint,5,opt,name=sub_category_type,json=subCategoryType,proto3" json:"sub_category_type,omitempty"`
	SubCategoryImgUrlSelected    string   `protobuf:"bytes,6,opt,name=sub_category_img_url_selected,json=subCategoryImgUrlSelected,proto3" json:"sub_category_img_url_selected,omitempty"`
	WebSubCategoryImgUrl         string   `protobuf:"bytes,7,opt,name=web_sub_category_img_url,json=webSubCategoryImgUrl,proto3" json:"web_sub_category_img_url,omitempty"`
	WebSubCategoryImgUrlSelected string   `protobuf:"bytes,8,opt,name=web_sub_category_img_url_selected,json=webSubCategoryImgUrlSelected,proto3" json:"web_sub_category_img_url_selected,omitempty"`
	XXX_NoUnkeyedLiteral         struct{} `json:"-"`
	XXX_unrecognized             []byte   `json:"-"`
	XXX_sizecache                int32    `json:"-"`
}

func (m *VirtualImageSubCategoryInfo) Reset()         { *m = VirtualImageSubCategoryInfo{} }
func (m *VirtualImageSubCategoryInfo) String() string { return proto.CompactTextString(m) }
func (*VirtualImageSubCategoryInfo) ProtoMessage()    {}
func (*VirtualImageSubCategoryInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{24}
}
func (m *VirtualImageSubCategoryInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VirtualImageSubCategoryInfo.Unmarshal(m, b)
}
func (m *VirtualImageSubCategoryInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VirtualImageSubCategoryInfo.Marshal(b, m, deterministic)
}
func (dst *VirtualImageSubCategoryInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VirtualImageSubCategoryInfo.Merge(dst, src)
}
func (m *VirtualImageSubCategoryInfo) XXX_Size() int {
	return xxx_messageInfo_VirtualImageSubCategoryInfo.Size(m)
}
func (m *VirtualImageSubCategoryInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_VirtualImageSubCategoryInfo.DiscardUnknown(m)
}

var xxx_messageInfo_VirtualImageSubCategoryInfo proto.InternalMessageInfo

func (m *VirtualImageSubCategoryInfo) GetSubCategory() uint32 {
	if m != nil {
		return m.SubCategory
	}
	return 0
}

func (m *VirtualImageSubCategoryInfo) GetSubCategoryName() string {
	if m != nil {
		return m.SubCategoryName
	}
	return ""
}

func (m *VirtualImageSubCategoryInfo) GetSubCategoryImgUrl() string {
	if m != nil {
		return m.SubCategoryImgUrl
	}
	return ""
}

func (m *VirtualImageSubCategoryInfo) GetParentCategory() uint32 {
	if m != nil {
		return m.ParentCategory
	}
	return 0
}

func (m *VirtualImageSubCategoryInfo) GetSubCategoryType() uint32 {
	if m != nil {
		return m.SubCategoryType
	}
	return 0
}

func (m *VirtualImageSubCategoryInfo) GetSubCategoryImgUrlSelected() string {
	if m != nil {
		return m.SubCategoryImgUrlSelected
	}
	return ""
}

func (m *VirtualImageSubCategoryInfo) GetWebSubCategoryImgUrl() string {
	if m != nil {
		return m.WebSubCategoryImgUrl
	}
	return ""
}

func (m *VirtualImageSubCategoryInfo) GetWebSubCategoryImgUrlSelected() string {
	if m != nil {
		return m.WebSubCategoryImgUrlSelected
	}
	return ""
}

// 资源品类信息
type VirtualImageResourceCategoryInfo struct {
	ParentCategoryInfo   *VirtualImageParentCategoryInfo `protobuf:"bytes,1,opt,name=parent_category_info,json=parentCategoryInfo,proto3" json:"parent_category_info,omitempty"`
	SubCategoryInfoList  []*VirtualImageSubCategoryInfo  `protobuf:"bytes,2,rep,name=sub_category_info_list,json=subCategoryInfoList,proto3" json:"sub_category_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *VirtualImageResourceCategoryInfo) Reset()         { *m = VirtualImageResourceCategoryInfo{} }
func (m *VirtualImageResourceCategoryInfo) String() string { return proto.CompactTextString(m) }
func (*VirtualImageResourceCategoryInfo) ProtoMessage()    {}
func (*VirtualImageResourceCategoryInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{25}
}
func (m *VirtualImageResourceCategoryInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VirtualImageResourceCategoryInfo.Unmarshal(m, b)
}
func (m *VirtualImageResourceCategoryInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VirtualImageResourceCategoryInfo.Marshal(b, m, deterministic)
}
func (dst *VirtualImageResourceCategoryInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VirtualImageResourceCategoryInfo.Merge(dst, src)
}
func (m *VirtualImageResourceCategoryInfo) XXX_Size() int {
	return xxx_messageInfo_VirtualImageResourceCategoryInfo.Size(m)
}
func (m *VirtualImageResourceCategoryInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_VirtualImageResourceCategoryInfo.DiscardUnknown(m)
}

var xxx_messageInfo_VirtualImageResourceCategoryInfo proto.InternalMessageInfo

func (m *VirtualImageResourceCategoryInfo) GetParentCategoryInfo() *VirtualImageParentCategoryInfo {
	if m != nil {
		return m.ParentCategoryInfo
	}
	return nil
}

func (m *VirtualImageResourceCategoryInfo) GetSubCategoryInfoList() []*VirtualImageSubCategoryInfo {
	if m != nil {
		return m.SubCategoryInfoList
	}
	return nil
}

// 获取资源品类列表
type GetVirtualImageResourceCategoryRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVirtualImageResourceCategoryRequest) Reset() {
	*m = GetVirtualImageResourceCategoryRequest{}
}
func (m *GetVirtualImageResourceCategoryRequest) String() string { return proto.CompactTextString(m) }
func (*GetVirtualImageResourceCategoryRequest) ProtoMessage()    {}
func (*GetVirtualImageResourceCategoryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{26}
}
func (m *GetVirtualImageResourceCategoryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVirtualImageResourceCategoryRequest.Unmarshal(m, b)
}
func (m *GetVirtualImageResourceCategoryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVirtualImageResourceCategoryRequest.Marshal(b, m, deterministic)
}
func (dst *GetVirtualImageResourceCategoryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVirtualImageResourceCategoryRequest.Merge(dst, src)
}
func (m *GetVirtualImageResourceCategoryRequest) XXX_Size() int {
	return xxx_messageInfo_GetVirtualImageResourceCategoryRequest.Size(m)
}
func (m *GetVirtualImageResourceCategoryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVirtualImageResourceCategoryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetVirtualImageResourceCategoryRequest proto.InternalMessageInfo

type GetVirtualImageResourceCategoryResponse struct {
	ResourceCategoryInfoList []*VirtualImageResourceCategoryInfo `protobuf:"bytes,2,rep,name=resource_category_info_list,json=resourceCategoryInfoList,proto3" json:"resource_category_info_list,omitempty"`
	XXX_NoUnkeyedLiteral     struct{}                            `json:"-"`
	XXX_unrecognized         []byte                              `json:"-"`
	XXX_sizecache            int32                               `json:"-"`
}

func (m *GetVirtualImageResourceCategoryResponse) Reset() {
	*m = GetVirtualImageResourceCategoryResponse{}
}
func (m *GetVirtualImageResourceCategoryResponse) String() string { return proto.CompactTextString(m) }
func (*GetVirtualImageResourceCategoryResponse) ProtoMessage()    {}
func (*GetVirtualImageResourceCategoryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{27}
}
func (m *GetVirtualImageResourceCategoryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVirtualImageResourceCategoryResponse.Unmarshal(m, b)
}
func (m *GetVirtualImageResourceCategoryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVirtualImageResourceCategoryResponse.Marshal(b, m, deterministic)
}
func (dst *GetVirtualImageResourceCategoryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVirtualImageResourceCategoryResponse.Merge(dst, src)
}
func (m *GetVirtualImageResourceCategoryResponse) XXX_Size() int {
	return xxx_messageInfo_GetVirtualImageResourceCategoryResponse.Size(m)
}
func (m *GetVirtualImageResourceCategoryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVirtualImageResourceCategoryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetVirtualImageResourceCategoryResponse proto.InternalMessageInfo

func (m *GetVirtualImageResourceCategoryResponse) GetResourceCategoryInfoList() []*VirtualImageResourceCategoryInfo {
	if m != nil {
		return m.ResourceCategoryInfoList
	}
	return nil
}

type BatchUpdateIconRequest struct {
	List                 []*VirtualImageResourceInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *BatchUpdateIconRequest) Reset()         { *m = BatchUpdateIconRequest{} }
func (m *BatchUpdateIconRequest) String() string { return proto.CompactTextString(m) }
func (*BatchUpdateIconRequest) ProtoMessage()    {}
func (*BatchUpdateIconRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{28}
}
func (m *BatchUpdateIconRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUpdateIconRequest.Unmarshal(m, b)
}
func (m *BatchUpdateIconRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUpdateIconRequest.Marshal(b, m, deterministic)
}
func (dst *BatchUpdateIconRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUpdateIconRequest.Merge(dst, src)
}
func (m *BatchUpdateIconRequest) XXX_Size() int {
	return xxx_messageInfo_BatchUpdateIconRequest.Size(m)
}
func (m *BatchUpdateIconRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUpdateIconRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUpdateIconRequest proto.InternalMessageInfo

func (m *BatchUpdateIconRequest) GetList() []*VirtualImageResourceInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type BatchUpdateIconResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchUpdateIconResponse) Reset()         { *m = BatchUpdateIconResponse{} }
func (m *BatchUpdateIconResponse) String() string { return proto.CompactTextString(m) }
func (*BatchUpdateIconResponse) ProtoMessage()    {}
func (*BatchUpdateIconResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{29}
}
func (m *BatchUpdateIconResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUpdateIconResponse.Unmarshal(m, b)
}
func (m *BatchUpdateIconResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUpdateIconResponse.Marshal(b, m, deterministic)
}
func (dst *BatchUpdateIconResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUpdateIconResponse.Merge(dst, src)
}
func (m *BatchUpdateIconResponse) XXX_Size() int {
	return xxx_messageInfo_BatchUpdateIconResponse.Size(m)
}
func (m *BatchUpdateIconResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUpdateIconResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUpdateIconResponse proto.InternalMessageInfo

type GetDefaultResourceListRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDefaultResourceListRequest) Reset()         { *m = GetDefaultResourceListRequest{} }
func (m *GetDefaultResourceListRequest) String() string { return proto.CompactTextString(m) }
func (*GetDefaultResourceListRequest) ProtoMessage()    {}
func (*GetDefaultResourceListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{30}
}
func (m *GetDefaultResourceListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDefaultResourceListRequest.Unmarshal(m, b)
}
func (m *GetDefaultResourceListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDefaultResourceListRequest.Marshal(b, m, deterministic)
}
func (dst *GetDefaultResourceListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDefaultResourceListRequest.Merge(dst, src)
}
func (m *GetDefaultResourceListRequest) XXX_Size() int {
	return xxx_messageInfo_GetDefaultResourceListRequest.Size(m)
}
func (m *GetDefaultResourceListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDefaultResourceListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetDefaultResourceListRequest proto.InternalMessageInfo

type GetDefaultResourceListResponse struct {
	MaleResources        []uint32          `protobuf:"varint,1,rep,packed,name=male_resources,json=maleResources,proto3" json:"male_resources,omitempty"`
	FemaleResources      []uint32          `protobuf:"varint,2,rep,packed,name=female_resources,json=femaleResources,proto3" json:"female_resources,omitempty"`
	OutFit               []uint32          `protobuf:"varint,3,rep,packed,name=out_fit,json=outFit,proto3" json:"out_fit,omitempty"`
	MaleAnimationMap     map[uint32]uint32 `protobuf:"bytes,5,rep,name=male_animation_map,json=maleAnimationMap,proto3" json:"male_animation_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	FemaleAnimationMap   map[uint32]uint32 `protobuf:"bytes,6,rep,name=female_animation_map,json=femaleAnimationMap,proto3" json:"female_animation_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	UnShowList           []uint32          `protobuf:"varint,8,rep,packed,name=un_show_list,json=unShowList,proto3" json:"un_show_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetDefaultResourceListResponse) Reset()         { *m = GetDefaultResourceListResponse{} }
func (m *GetDefaultResourceListResponse) String() string { return proto.CompactTextString(m) }
func (*GetDefaultResourceListResponse) ProtoMessage()    {}
func (*GetDefaultResourceListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{31}
}
func (m *GetDefaultResourceListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDefaultResourceListResponse.Unmarshal(m, b)
}
func (m *GetDefaultResourceListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDefaultResourceListResponse.Marshal(b, m, deterministic)
}
func (dst *GetDefaultResourceListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDefaultResourceListResponse.Merge(dst, src)
}
func (m *GetDefaultResourceListResponse) XXX_Size() int {
	return xxx_messageInfo_GetDefaultResourceListResponse.Size(m)
}
func (m *GetDefaultResourceListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDefaultResourceListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetDefaultResourceListResponse proto.InternalMessageInfo

func (m *GetDefaultResourceListResponse) GetMaleResources() []uint32 {
	if m != nil {
		return m.MaleResources
	}
	return nil
}

func (m *GetDefaultResourceListResponse) GetFemaleResources() []uint32 {
	if m != nil {
		return m.FemaleResources
	}
	return nil
}

func (m *GetDefaultResourceListResponse) GetOutFit() []uint32 {
	if m != nil {
		return m.OutFit
	}
	return nil
}

func (m *GetDefaultResourceListResponse) GetMaleAnimationMap() map[uint32]uint32 {
	if m != nil {
		return m.MaleAnimationMap
	}
	return nil
}

func (m *GetDefaultResourceListResponse) GetFemaleAnimationMap() map[uint32]uint32 {
	if m != nil {
		return m.FemaleAnimationMap
	}
	return nil
}

func (m *GetDefaultResourceListResponse) GetUnShowList() []uint32 {
	if m != nil {
		return m.UnShowList
	}
	return nil
}

type LevelConfig struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	LevelIcon            string   `protobuf:"bytes,2,opt,name=level_icon,json=levelIcon,proto3" json:"level_icon,omitempty"`
	LevelWebp            string   `protobuf:"bytes,3,opt,name=level_webp,json=levelWebp,proto3" json:"level_webp,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,4,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LevelConfig) Reset()         { *m = LevelConfig{} }
func (m *LevelConfig) String() string { return proto.CompactTextString(m) }
func (*LevelConfig) ProtoMessage()    {}
func (*LevelConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{32}
}
func (m *LevelConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelConfig.Unmarshal(m, b)
}
func (m *LevelConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelConfig.Marshal(b, m, deterministic)
}
func (dst *LevelConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelConfig.Merge(dst, src)
}
func (m *LevelConfig) XXX_Size() int {
	return xxx_messageInfo_LevelConfig.Size(m)
}
func (m *LevelConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelConfig.DiscardUnknown(m)
}

var xxx_messageInfo_LevelConfig proto.InternalMessageInfo

func (m *LevelConfig) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *LevelConfig) GetLevelIcon() string {
	if m != nil {
		return m.LevelIcon
	}
	return ""
}

func (m *LevelConfig) GetLevelWebp() string {
	if m != nil {
		return m.LevelWebp
	}
	return ""
}

func (m *LevelConfig) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

// 获取等级配置
type GetLevelConfigRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLevelConfigRequest) Reset()         { *m = GetLevelConfigRequest{} }
func (m *GetLevelConfigRequest) String() string { return proto.CompactTextString(m) }
func (*GetLevelConfigRequest) ProtoMessage()    {}
func (*GetLevelConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{33}
}
func (m *GetLevelConfigRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLevelConfigRequest.Unmarshal(m, b)
}
func (m *GetLevelConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLevelConfigRequest.Marshal(b, m, deterministic)
}
func (dst *GetLevelConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLevelConfigRequest.Merge(dst, src)
}
func (m *GetLevelConfigRequest) XXX_Size() int {
	return xxx_messageInfo_GetLevelConfigRequest.Size(m)
}
func (m *GetLevelConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLevelConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetLevelConfigRequest proto.InternalMessageInfo

type GetLevelConfigResponse struct {
	List                 []*LevelConfig `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetLevelConfigResponse) Reset()         { *m = GetLevelConfigResponse{} }
func (m *GetLevelConfigResponse) String() string { return proto.CompactTextString(m) }
func (*GetLevelConfigResponse) ProtoMessage()    {}
func (*GetLevelConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{34}
}
func (m *GetLevelConfigResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLevelConfigResponse.Unmarshal(m, b)
}
func (m *GetLevelConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLevelConfigResponse.Marshal(b, m, deterministic)
}
func (dst *GetLevelConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLevelConfigResponse.Merge(dst, src)
}
func (m *GetLevelConfigResponse) XXX_Size() int {
	return xxx_messageInfo_GetLevelConfigResponse.Size(m)
}
func (m *GetLevelConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLevelConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetLevelConfigResponse proto.InternalMessageInfo

func (m *GetLevelConfigResponse) GetList() []*LevelConfig {
	if m != nil {
		return m.List
	}
	return nil
}

// 更新等级配置
type UpdateLevelConfigRequest struct {
	Cfg                  *LevelConfig `protobuf:"bytes,1,opt,name=cfg,proto3" json:"cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UpdateLevelConfigRequest) Reset()         { *m = UpdateLevelConfigRequest{} }
func (m *UpdateLevelConfigRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateLevelConfigRequest) ProtoMessage()    {}
func (*UpdateLevelConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{35}
}
func (m *UpdateLevelConfigRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateLevelConfigRequest.Unmarshal(m, b)
}
func (m *UpdateLevelConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateLevelConfigRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateLevelConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateLevelConfigRequest.Merge(dst, src)
}
func (m *UpdateLevelConfigRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateLevelConfigRequest.Size(m)
}
func (m *UpdateLevelConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateLevelConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateLevelConfigRequest proto.InternalMessageInfo

func (m *UpdateLevelConfigRequest) GetCfg() *LevelConfig {
	if m != nil {
		return m.Cfg
	}
	return nil
}

type UpdateLevelConfigResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateLevelConfigResponse) Reset()         { *m = UpdateLevelConfigResponse{} }
func (m *UpdateLevelConfigResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateLevelConfigResponse) ProtoMessage()    {}
func (*UpdateLevelConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{36}
}
func (m *UpdateLevelConfigResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateLevelConfigResponse.Unmarshal(m, b)
}
func (m *UpdateLevelConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateLevelConfigResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateLevelConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateLevelConfigResponse.Merge(dst, src)
}
func (m *UpdateLevelConfigResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateLevelConfigResponse.Size(m)
}
func (m *UpdateLevelConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateLevelConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateLevelConfigResponse proto.InternalMessageInfo

// 增加等级配置
type AddLevelConfigRequest struct {
	Cfg                  *LevelConfig `protobuf:"bytes,1,opt,name=cfg,proto3" json:"cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AddLevelConfigRequest) Reset()         { *m = AddLevelConfigRequest{} }
func (m *AddLevelConfigRequest) String() string { return proto.CompactTextString(m) }
func (*AddLevelConfigRequest) ProtoMessage()    {}
func (*AddLevelConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{37}
}
func (m *AddLevelConfigRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddLevelConfigRequest.Unmarshal(m, b)
}
func (m *AddLevelConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddLevelConfigRequest.Marshal(b, m, deterministic)
}
func (dst *AddLevelConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddLevelConfigRequest.Merge(dst, src)
}
func (m *AddLevelConfigRequest) XXX_Size() int {
	return xxx_messageInfo_AddLevelConfigRequest.Size(m)
}
func (m *AddLevelConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddLevelConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddLevelConfigRequest proto.InternalMessageInfo

func (m *AddLevelConfigRequest) GetCfg() *LevelConfig {
	if m != nil {
		return m.Cfg
	}
	return nil
}

type AddLevelConfigResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddLevelConfigResponse) Reset()         { *m = AddLevelConfigResponse{} }
func (m *AddLevelConfigResponse) String() string { return proto.CompactTextString(m) }
func (*AddLevelConfigResponse) ProtoMessage()    {}
func (*AddLevelConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{38}
}
func (m *AddLevelConfigResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddLevelConfigResponse.Unmarshal(m, b)
}
func (m *AddLevelConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddLevelConfigResponse.Marshal(b, m, deterministic)
}
func (dst *AddLevelConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddLevelConfigResponse.Merge(dst, src)
}
func (m *AddLevelConfigResponse) XXX_Size() int {
	return xxx_messageInfo_AddLevelConfigResponse.Size(m)
}
func (m *AddLevelConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddLevelConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddLevelConfigResponse proto.InternalMessageInfo

// 提醒配置
type NoticeCfg struct {
	BeginTime            int64    `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	PublicContent        string   `protobuf:"bytes,3,opt,name=public_content,json=publicContent,proto3" json:"public_content,omitempty"`
	PublicContentColor   string   `protobuf:"bytes,4,opt,name=public_content_color,json=publicContentColor,proto3" json:"public_content_color,omitempty"`
	PublicContentJumpUrl string   `protobuf:"bytes,5,opt,name=public_content_jump_url,json=publicContentJumpUrl,proto3" json:"public_content_jump_url,omitempty"`
	FloatContent         string   `protobuf:"bytes,6,opt,name=float_content,json=floatContent,proto3" json:"float_content,omitempty"`
	FloatContentDuration uint32   `protobuf:"varint,7,opt,name=float_content_duration,json=floatContentDuration,proto3" json:"float_content_duration,omitempty"`
	HasRedDot            bool     `protobuf:"varint,8,opt,name=has_red_dot,json=hasRedDot,proto3" json:"has_red_dot,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NoticeCfg) Reset()         { *m = NoticeCfg{} }
func (m *NoticeCfg) String() string { return proto.CompactTextString(m) }
func (*NoticeCfg) ProtoMessage()    {}
func (*NoticeCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{39}
}
func (m *NoticeCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NoticeCfg.Unmarshal(m, b)
}
func (m *NoticeCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NoticeCfg.Marshal(b, m, deterministic)
}
func (dst *NoticeCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NoticeCfg.Merge(dst, src)
}
func (m *NoticeCfg) XXX_Size() int {
	return xxx_messageInfo_NoticeCfg.Size(m)
}
func (m *NoticeCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_NoticeCfg.DiscardUnknown(m)
}

var xxx_messageInfo_NoticeCfg proto.InternalMessageInfo

func (m *NoticeCfg) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *NoticeCfg) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *NoticeCfg) GetPublicContent() string {
	if m != nil {
		return m.PublicContent
	}
	return ""
}

func (m *NoticeCfg) GetPublicContentColor() string {
	if m != nil {
		return m.PublicContentColor
	}
	return ""
}

func (m *NoticeCfg) GetPublicContentJumpUrl() string {
	if m != nil {
		return m.PublicContentJumpUrl
	}
	return ""
}

func (m *NoticeCfg) GetFloatContent() string {
	if m != nil {
		return m.FloatContent
	}
	return ""
}

func (m *NoticeCfg) GetFloatContentDuration() uint32 {
	if m != nil {
		return m.FloatContentDuration
	}
	return 0
}

func (m *NoticeCfg) GetHasRedDot() bool {
	if m != nil {
		return m.HasRedDot
	}
	return false
}

// 获取提醒配置
type GetNoticeCfgRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNoticeCfgRequest) Reset()         { *m = GetNoticeCfgRequest{} }
func (m *GetNoticeCfgRequest) String() string { return proto.CompactTextString(m) }
func (*GetNoticeCfgRequest) ProtoMessage()    {}
func (*GetNoticeCfgRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{40}
}
func (m *GetNoticeCfgRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNoticeCfgRequest.Unmarshal(m, b)
}
func (m *GetNoticeCfgRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNoticeCfgRequest.Marshal(b, m, deterministic)
}
func (dst *GetNoticeCfgRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNoticeCfgRequest.Merge(dst, src)
}
func (m *GetNoticeCfgRequest) XXX_Size() int {
	return xxx_messageInfo_GetNoticeCfgRequest.Size(m)
}
func (m *GetNoticeCfgRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNoticeCfgRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetNoticeCfgRequest proto.InternalMessageInfo

type GetNoticeCfgResponse struct {
	Cfg                  *NoticeCfg `protobuf:"bytes,1,opt,name=cfg,proto3" json:"cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetNoticeCfgResponse) Reset()         { *m = GetNoticeCfgResponse{} }
func (m *GetNoticeCfgResponse) String() string { return proto.CompactTextString(m) }
func (*GetNoticeCfgResponse) ProtoMessage()    {}
func (*GetNoticeCfgResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{41}
}
func (m *GetNoticeCfgResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNoticeCfgResponse.Unmarshal(m, b)
}
func (m *GetNoticeCfgResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNoticeCfgResponse.Marshal(b, m, deterministic)
}
func (dst *GetNoticeCfgResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNoticeCfgResponse.Merge(dst, src)
}
func (m *GetNoticeCfgResponse) XXX_Size() int {
	return xxx_messageInfo_GetNoticeCfgResponse.Size(m)
}
func (m *GetNoticeCfgResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNoticeCfgResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetNoticeCfgResponse proto.InternalMessageInfo

func (m *GetNoticeCfgResponse) GetCfg() *NoticeCfg {
	if m != nil {
		return m.Cfg
	}
	return nil
}

// 更新提醒配置
type UpdateNoticeCfgRequest struct {
	Cfg                  *NoticeCfg `protobuf:"bytes,1,opt,name=cfg,proto3" json:"cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *UpdateNoticeCfgRequest) Reset()         { *m = UpdateNoticeCfgRequest{} }
func (m *UpdateNoticeCfgRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateNoticeCfgRequest) ProtoMessage()    {}
func (*UpdateNoticeCfgRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{42}
}
func (m *UpdateNoticeCfgRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateNoticeCfgRequest.Unmarshal(m, b)
}
func (m *UpdateNoticeCfgRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateNoticeCfgRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateNoticeCfgRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateNoticeCfgRequest.Merge(dst, src)
}
func (m *UpdateNoticeCfgRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateNoticeCfgRequest.Size(m)
}
func (m *UpdateNoticeCfgRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateNoticeCfgRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateNoticeCfgRequest proto.InternalMessageInfo

func (m *UpdateNoticeCfgRequest) GetCfg() *NoticeCfg {
	if m != nil {
		return m.Cfg
	}
	return nil
}

type UpdateNoticeCfgResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateNoticeCfgResponse) Reset()         { *m = UpdateNoticeCfgResponse{} }
func (m *UpdateNoticeCfgResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateNoticeCfgResponse) ProtoMessage()    {}
func (*UpdateNoticeCfgResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{43}
}
func (m *UpdateNoticeCfgResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateNoticeCfgResponse.Unmarshal(m, b)
}
func (m *UpdateNoticeCfgResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateNoticeCfgResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateNoticeCfgResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateNoticeCfgResponse.Merge(dst, src)
}
func (m *UpdateNoticeCfgResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateNoticeCfgResponse.Size(m)
}
func (m *UpdateNoticeCfgResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateNoticeCfgResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateNoticeCfgResponse proto.InternalMessageInfo

// 获取提醒配置缓存
type GetNoticeCfgCacheRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNoticeCfgCacheRequest) Reset()         { *m = GetNoticeCfgCacheRequest{} }
func (m *GetNoticeCfgCacheRequest) String() string { return proto.CompactTextString(m) }
func (*GetNoticeCfgCacheRequest) ProtoMessage()    {}
func (*GetNoticeCfgCacheRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{44}
}
func (m *GetNoticeCfgCacheRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNoticeCfgCacheRequest.Unmarshal(m, b)
}
func (m *GetNoticeCfgCacheRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNoticeCfgCacheRequest.Marshal(b, m, deterministic)
}
func (dst *GetNoticeCfgCacheRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNoticeCfgCacheRequest.Merge(dst, src)
}
func (m *GetNoticeCfgCacheRequest) XXX_Size() int {
	return xxx_messageInfo_GetNoticeCfgCacheRequest.Size(m)
}
func (m *GetNoticeCfgCacheRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNoticeCfgCacheRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetNoticeCfgCacheRequest proto.InternalMessageInfo

type GetNoticeCfgCacheResponse struct {
	Cfg                  *NoticeCfg `protobuf:"bytes,1,opt,name=cfg,proto3" json:"cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetNoticeCfgCacheResponse) Reset()         { *m = GetNoticeCfgCacheResponse{} }
func (m *GetNoticeCfgCacheResponse) String() string { return proto.CompactTextString(m) }
func (*GetNoticeCfgCacheResponse) ProtoMessage()    {}
func (*GetNoticeCfgCacheResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{45}
}
func (m *GetNoticeCfgCacheResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNoticeCfgCacheResponse.Unmarshal(m, b)
}
func (m *GetNoticeCfgCacheResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNoticeCfgCacheResponse.Marshal(b, m, deterministic)
}
func (dst *GetNoticeCfgCacheResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNoticeCfgCacheResponse.Merge(dst, src)
}
func (m *GetNoticeCfgCacheResponse) XXX_Size() int {
	return xxx_messageInfo_GetNoticeCfgCacheResponse.Size(m)
}
func (m *GetNoticeCfgCacheResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNoticeCfgCacheResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetNoticeCfgCacheResponse proto.InternalMessageInfo

func (m *GetNoticeCfgCacheResponse) GetCfg() *NoticeCfg {
	if m != nil {
		return m.Cfg
	}
	return nil
}

type GetActionResourceMapRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetActionResourceMapRequest) Reset()         { *m = GetActionResourceMapRequest{} }
func (m *GetActionResourceMapRequest) String() string { return proto.CompactTextString(m) }
func (*GetActionResourceMapRequest) ProtoMessage()    {}
func (*GetActionResourceMapRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{46}
}
func (m *GetActionResourceMapRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActionResourceMapRequest.Unmarshal(m, b)
}
func (m *GetActionResourceMapRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActionResourceMapRequest.Marshal(b, m, deterministic)
}
func (dst *GetActionResourceMapRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActionResourceMapRequest.Merge(dst, src)
}
func (m *GetActionResourceMapRequest) XXX_Size() int {
	return xxx_messageInfo_GetActionResourceMapRequest.Size(m)
}
func (m *GetActionResourceMapRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActionResourceMapRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetActionResourceMapRequest proto.InternalMessageInfo

type GetActionResourceMapResponse struct {
	ActionResourceMap    map[uint32]uint32 `protobuf:"bytes,1,rep,name=action_resource_map,json=actionResourceMap,proto3" json:"action_resource_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetActionResourceMapResponse) Reset()         { *m = GetActionResourceMapResponse{} }
func (m *GetActionResourceMapResponse) String() string { return proto.CompactTextString(m) }
func (*GetActionResourceMapResponse) ProtoMessage()    {}
func (*GetActionResourceMapResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{47}
}
func (m *GetActionResourceMapResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActionResourceMapResponse.Unmarshal(m, b)
}
func (m *GetActionResourceMapResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActionResourceMapResponse.Marshal(b, m, deterministic)
}
func (dst *GetActionResourceMapResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActionResourceMapResponse.Merge(dst, src)
}
func (m *GetActionResourceMapResponse) XXX_Size() int {
	return xxx_messageInfo_GetActionResourceMapResponse.Size(m)
}
func (m *GetActionResourceMapResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActionResourceMapResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetActionResourceMapResponse proto.InternalMessageInfo

func (m *GetActionResourceMapResponse) GetActionResourceMap() map[uint32]uint32 {
	if m != nil {
		return m.ActionResourceMap
	}
	return nil
}

type ChangeResourceInfo struct {
	ResourceName         string   `protobuf:"bytes,1,opt,name=resource_name,json=resourceName,proto3" json:"resource_name,omitempty"`
	ModifyReason         string   `protobuf:"bytes,2,opt,name=modify_reason,json=modifyReason,proto3" json:"modify_reason,omitempty"`
	Operator             string   `protobuf:"bytes,3,opt,name=operator,proto3" json:"operator,omitempty"`
	ResourceId           uint32   `protobuf:"varint,4,opt,name=resource_id,json=resourceId,proto3" json:"resource_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeResourceInfo) Reset()         { *m = ChangeResourceInfo{} }
func (m *ChangeResourceInfo) String() string { return proto.CompactTextString(m) }
func (*ChangeResourceInfo) ProtoMessage()    {}
func (*ChangeResourceInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{48}
}
func (m *ChangeResourceInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeResourceInfo.Unmarshal(m, b)
}
func (m *ChangeResourceInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeResourceInfo.Marshal(b, m, deterministic)
}
func (dst *ChangeResourceInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeResourceInfo.Merge(dst, src)
}
func (m *ChangeResourceInfo) XXX_Size() int {
	return xxx_messageInfo_ChangeResourceInfo.Size(m)
}
func (m *ChangeResourceInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeResourceInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeResourceInfo proto.InternalMessageInfo

func (m *ChangeResourceInfo) GetResourceName() string {
	if m != nil {
		return m.ResourceName
	}
	return ""
}

func (m *ChangeResourceInfo) GetModifyReason() string {
	if m != nil {
		return m.ModifyReason
	}
	return ""
}

func (m *ChangeResourceInfo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *ChangeResourceInfo) GetResourceId() uint32 {
	if m != nil {
		return m.ResourceId
	}
	return 0
}

type SetResourceToTestRequest struct {
	ChangeList           []*ChangeResourceInfo `protobuf:"bytes,1,rep,name=change_list,json=changeList,proto3" json:"change_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *SetResourceToTestRequest) Reset()         { *m = SetResourceToTestRequest{} }
func (m *SetResourceToTestRequest) String() string { return proto.CompactTextString(m) }
func (*SetResourceToTestRequest) ProtoMessage()    {}
func (*SetResourceToTestRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{49}
}
func (m *SetResourceToTestRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetResourceToTestRequest.Unmarshal(m, b)
}
func (m *SetResourceToTestRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetResourceToTestRequest.Marshal(b, m, deterministic)
}
func (dst *SetResourceToTestRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetResourceToTestRequest.Merge(dst, src)
}
func (m *SetResourceToTestRequest) XXX_Size() int {
	return xxx_messageInfo_SetResourceToTestRequest.Size(m)
}
func (m *SetResourceToTestRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetResourceToTestRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetResourceToTestRequest proto.InternalMessageInfo

func (m *SetResourceToTestRequest) GetChangeList() []*ChangeResourceInfo {
	if m != nil {
		return m.ChangeList
	}
	return nil
}

type SetResourceToTestResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetResourceToTestResponse) Reset()         { *m = SetResourceToTestResponse{} }
func (m *SetResourceToTestResponse) String() string { return proto.CompactTextString(m) }
func (*SetResourceToTestResponse) ProtoMessage()    {}
func (*SetResourceToTestResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_resource_3001f8b4f1250449, []int{50}
}
func (m *SetResourceToTestResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetResourceToTestResponse.Unmarshal(m, b)
}
func (m *SetResourceToTestResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetResourceToTestResponse.Marshal(b, m, deterministic)
}
func (dst *SetResourceToTestResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetResourceToTestResponse.Merge(dst, src)
}
func (m *SetResourceToTestResponse) XXX_Size() int {
	return xxx_messageInfo_SetResourceToTestResponse.Size(m)
}
func (m *SetResourceToTestResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetResourceToTestResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetResourceToTestResponse proto.InternalMessageInfo

func init() {
	proto.RegisterType((*VirtualImageResourceInfo)(nil), "virtual_image_resource.VirtualImageResourceInfo")
	proto.RegisterMapType((map[string]string)(nil), "virtual_image_resource.VirtualImageResourceInfo.CustomMapEntry")
	proto.RegisterMapType((map[string]*SkinInfo)(nil), "virtual_image_resource.VirtualImageResourceInfo.IosSkinMapEntry")
	proto.RegisterMapType((map[string]*SkinInfo)(nil), "virtual_image_resource.VirtualImageResourceInfo.SkinMapEntry")
	proto.RegisterMapType((map[string]string)(nil), "virtual_image_resource.VirtualImageResourceInfo.TextureUrlMapEntry")
	proto.RegisterType((*SkinInfo)(nil), "virtual_image_resource.SkinInfo")
	proto.RegisterType((*AddVirtualImageResourceRequest)(nil), "virtual_image_resource.AddVirtualImageResourceRequest")
	proto.RegisterType((*AddVirtualImageResourceResponse)(nil), "virtual_image_resource.AddVirtualImageResourceResponse")
	proto.RegisterType((*CloneVirtualImageResourceRequest)(nil), "virtual_image_resource.CloneVirtualImageResourceRequest")
	proto.RegisterType((*CloneVirtualImageResourceResponse)(nil), "virtual_image_resource.CloneVirtualImageResourceResponse")
	proto.RegisterType((*SearchVirtualImageResourceRequest)(nil), "virtual_image_resource.SearchVirtualImageResourceRequest")
	proto.RegisterType((*SearchVirtualImageResourceResponse)(nil), "virtual_image_resource.SearchVirtualImageResourceResponse")
	proto.RegisterType((*GetVirtualImageResourceBySuitRequest)(nil), "virtual_image_resource.GetVirtualImageResourceBySuitRequest")
	proto.RegisterType((*VirtualImageResourceSuitInfo)(nil), "virtual_image_resource.VirtualImageResourceSuitInfo")
	proto.RegisterType((*GetVirtualImageResourceBySuitResponse)(nil), "virtual_image_resource.GetVirtualImageResourceBySuitResponse")
	proto.RegisterType((*EditVirtualImageResourceRequest)(nil), "virtual_image_resource.EditVirtualImageResourceRequest")
	proto.RegisterType((*EditVirtualImageResourceResponse)(nil), "virtual_image_resource.EditVirtualImageResourceResponse")
	proto.RegisterType((*GetClientListByPageRequest)(nil), "virtual_image_resource.GetClientListByPageRequest")
	proto.RegisterType((*GetClientListByPageResponse)(nil), "virtual_image_resource.GetClientListByPageResponse")
	proto.RegisterType((*DeleteVirtualImageResourceRequest)(nil), "virtual_image_resource.DeleteVirtualImageResourceRequest")
	proto.RegisterType((*DeleteVirtualImageResourceResponse)(nil), "virtual_image_resource.DeleteVirtualImageResourceResponse")
	proto.RegisterType((*GetVirtualImageResourcesByIdsRequest)(nil), "virtual_image_resource.GetVirtualImageResourcesByIdsRequest")
	proto.RegisterType((*GetVirtualImageResourcesByIdsResponse)(nil), "virtual_image_resource.GetVirtualImageResourcesByIdsResponse")
	proto.RegisterType((*SetVirtualImageResourceForSaleRequest)(nil), "virtual_image_resource.SetVirtualImageResourceForSaleRequest")
	proto.RegisterType((*SetVirtualImageResourceForSaleResponse)(nil), "virtual_image_resource.SetVirtualImageResourceForSaleResponse")
	proto.RegisterType((*UpdateVirtualImageResourceUrlRequest)(nil), "virtual_image_resource.UpdateVirtualImageResourceUrlRequest")
	proto.RegisterType((*UpdateVirtualImageResourceUrlResponse)(nil), "virtual_image_resource.UpdateVirtualImageResourceUrlResponse")
	proto.RegisterType((*VirtualImageParentCategoryInfo)(nil), "virtual_image_resource.VirtualImageParentCategoryInfo")
	proto.RegisterType((*VirtualImageSubCategoryInfo)(nil), "virtual_image_resource.VirtualImageSubCategoryInfo")
	proto.RegisterType((*VirtualImageResourceCategoryInfo)(nil), "virtual_image_resource.VirtualImageResourceCategoryInfo")
	proto.RegisterType((*GetVirtualImageResourceCategoryRequest)(nil), "virtual_image_resource.GetVirtualImageResourceCategoryRequest")
	proto.RegisterType((*GetVirtualImageResourceCategoryResponse)(nil), "virtual_image_resource.GetVirtualImageResourceCategoryResponse")
	proto.RegisterType((*BatchUpdateIconRequest)(nil), "virtual_image_resource.BatchUpdateIconRequest")
	proto.RegisterType((*BatchUpdateIconResponse)(nil), "virtual_image_resource.BatchUpdateIconResponse")
	proto.RegisterType((*GetDefaultResourceListRequest)(nil), "virtual_image_resource.GetDefaultResourceListRequest")
	proto.RegisterType((*GetDefaultResourceListResponse)(nil), "virtual_image_resource.GetDefaultResourceListResponse")
	proto.RegisterMapType((map[uint32]uint32)(nil), "virtual_image_resource.GetDefaultResourceListResponse.FemaleAnimationMapEntry")
	proto.RegisterMapType((map[uint32]uint32)(nil), "virtual_image_resource.GetDefaultResourceListResponse.MaleAnimationMapEntry")
	proto.RegisterType((*LevelConfig)(nil), "virtual_image_resource.LevelConfig")
	proto.RegisterType((*GetLevelConfigRequest)(nil), "virtual_image_resource.GetLevelConfigRequest")
	proto.RegisterType((*GetLevelConfigResponse)(nil), "virtual_image_resource.GetLevelConfigResponse")
	proto.RegisterType((*UpdateLevelConfigRequest)(nil), "virtual_image_resource.UpdateLevelConfigRequest")
	proto.RegisterType((*UpdateLevelConfigResponse)(nil), "virtual_image_resource.UpdateLevelConfigResponse")
	proto.RegisterType((*AddLevelConfigRequest)(nil), "virtual_image_resource.AddLevelConfigRequest")
	proto.RegisterType((*AddLevelConfigResponse)(nil), "virtual_image_resource.AddLevelConfigResponse")
	proto.RegisterType((*NoticeCfg)(nil), "virtual_image_resource.NoticeCfg")
	proto.RegisterType((*GetNoticeCfgRequest)(nil), "virtual_image_resource.GetNoticeCfgRequest")
	proto.RegisterType((*GetNoticeCfgResponse)(nil), "virtual_image_resource.GetNoticeCfgResponse")
	proto.RegisterType((*UpdateNoticeCfgRequest)(nil), "virtual_image_resource.UpdateNoticeCfgRequest")
	proto.RegisterType((*UpdateNoticeCfgResponse)(nil), "virtual_image_resource.UpdateNoticeCfgResponse")
	proto.RegisterType((*GetNoticeCfgCacheRequest)(nil), "virtual_image_resource.GetNoticeCfgCacheRequest")
	proto.RegisterType((*GetNoticeCfgCacheResponse)(nil), "virtual_image_resource.GetNoticeCfgCacheResponse")
	proto.RegisterType((*GetActionResourceMapRequest)(nil), "virtual_image_resource.GetActionResourceMapRequest")
	proto.RegisterType((*GetActionResourceMapResponse)(nil), "virtual_image_resource.GetActionResourceMapResponse")
	proto.RegisterMapType((map[uint32]uint32)(nil), "virtual_image_resource.GetActionResourceMapResponse.ActionResourceMapEntry")
	proto.RegisterType((*ChangeResourceInfo)(nil), "virtual_image_resource.ChangeResourceInfo")
	proto.RegisterType((*SetResourceToTestRequest)(nil), "virtual_image_resource.SetResourceToTestRequest")
	proto.RegisterType((*SetResourceToTestResponse)(nil), "virtual_image_resource.SetResourceToTestResponse")
	proto.RegisterEnum("virtual_image_resource.VirtualImageResourceStatus", VirtualImageResourceStatus_name, VirtualImageResourceStatus_value)
	proto.RegisterEnum("virtual_image_resource.VirtualImageResourceCategory", VirtualImageResourceCategory_name, VirtualImageResourceCategory_value)
	proto.RegisterEnum("virtual_image_resource.VirtualImageResourceSubCategory", VirtualImageResourceSubCategory_name, VirtualImageResourceSubCategory_value)
	proto.RegisterEnum("virtual_image_resource.VirtualImageResourceType", VirtualImageResourceType_name, VirtualImageResourceType_value)
	proto.RegisterEnum("virtual_image_resource.VirtualImageResourceSex", VirtualImageResourceSex_name, VirtualImageResourceSex_value)
	proto.RegisterEnum("virtual_image_resource.VirtualImageResourceCommodityFilter", VirtualImageResourceCommodityFilter_name, VirtualImageResourceCommodityFilter_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// VirtualImageResourceClient is the client API for VirtualImageResource service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type VirtualImageResourceClient interface {
	// =================== 运营后台接口 ===================
	// 添加虚拟形象资源【git同步资源】
	AddVirtualImageResource(ctx context.Context, in *AddVirtualImageResourceRequest, opts ...grpc.CallOption) (*AddVirtualImageResourceResponse, error)
	// 克隆虚拟形象资源【云测同步资源到线上】
	CloneVirtualImageResource(ctx context.Context, in *CloneVirtualImageResourceRequest, opts ...grpc.CallOption) (*CloneVirtualImageResourceResponse, error)
	// 搜索虚拟形象资源列表
	SearchVirtualImageResource(ctx context.Context, in *SearchVirtualImageResourceRequest, opts ...grpc.CallOption) (*SearchVirtualImageResourceResponse, error)
	// 编辑虚拟形象资源
	EditVirtualImageResource(ctx context.Context, in *EditVirtualImageResourceRequest, opts ...grpc.CallOption) (*EditVirtualImageResourceResponse, error)
	// 删除虚拟形象资源
	DeleteVirtualImageResource(ctx context.Context, in *DeleteVirtualImageResourceRequest, opts ...grpc.CallOption) (*DeleteVirtualImageResourceResponse, error)
	// 设置虚拟形象资源上架到商品
	SetVirtualImageResourceForSale(ctx context.Context, in *SetVirtualImageResourceForSaleRequest, opts ...grpc.CallOption) (*SetVirtualImageResourceForSaleResponse, error)
	// 更新虚拟形象资源下载url
	UpdateVirtualImageResourceUrl(ctx context.Context, in *UpdateVirtualImageResourceUrlRequest, opts ...grpc.CallOption) (*UpdateVirtualImageResourceUrlResponse, error)
	// 获取虚拟形象资源套装列表
	GetVirtualImageResourceBySuit(ctx context.Context, in *GetVirtualImageResourceBySuitRequest, opts ...grpc.CallOption) (*GetVirtualImageResourceBySuitResponse, error)
	// 批量更新图标
	BatchUpdateIcon(ctx context.Context, in *BatchUpdateIconRequest, opts ...grpc.CallOption) (*BatchUpdateIconResponse, error)
	// 获取等级配置
	GetLevelConfig(ctx context.Context, in *GetLevelConfigRequest, opts ...grpc.CallOption) (*GetLevelConfigResponse, error)
	// 更新等级配置
	UpdateLevelConfig(ctx context.Context, in *UpdateLevelConfigRequest, opts ...grpc.CallOption) (*UpdateLevelConfigResponse, error)
	// 增加等级配置
	AddLevelConfig(ctx context.Context, in *AddLevelConfigRequest, opts ...grpc.CallOption) (*AddLevelConfigResponse, error)
	// 获取提醒配置缓存
	GetNoticeCfgCache(ctx context.Context, in *GetNoticeCfgCacheRequest, opts ...grpc.CallOption) (*GetNoticeCfgCacheResponse, error)
	// 根据资源ID获取资源信息
	GetVirtualImageResourcesByIds(ctx context.Context, in *GetVirtualImageResourcesByIdsRequest, opts ...grpc.CallOption) (*GetVirtualImageResourcesByIdsResponse, error)
	// =================== 客户端接口 ===================
	// 获取虚拟形象资源列表
	GetClientListByPage(ctx context.Context, in *GetClientListByPageRequest, opts ...grpc.CallOption) (*GetClientListByPageResponse, error)
	// 获取资源品类列表
	GetVirtualImageResourceCategory(ctx context.Context, in *GetVirtualImageResourceCategoryRequest, opts ...grpc.CallOption) (*GetVirtualImageResourceCategoryResponse, error)
	// 获取默认资源列表
	GetDefaultResourceList(ctx context.Context, in *GetDefaultResourceListRequest, opts ...grpc.CallOption) (*GetDefaultResourceListResponse, error)
	// 获取提醒配置
	GetNoticeCfg(ctx context.Context, in *GetNoticeCfgRequest, opts ...grpc.CallOption) (*GetNoticeCfgResponse, error)
	// 更新提醒配置
	UpdateNoticeCfg(ctx context.Context, in *UpdateNoticeCfgRequest, opts ...grpc.CallOption) (*UpdateNoticeCfgResponse, error)
	// 获取动作资源映射 小-大
	GetActionResourceMap(ctx context.Context, in *GetActionResourceMapRequest, opts ...grpc.CallOption) (*GetActionResourceMapResponse, error)
	// 设置资源为待测试
	SetResourceToTest(ctx context.Context, in *SetResourceToTestRequest, opts ...grpc.CallOption) (*SetResourceToTestResponse, error)
}

type virtualImageResourceClient struct {
	cc *grpc.ClientConn
}

func NewVirtualImageResourceClient(cc *grpc.ClientConn) VirtualImageResourceClient {
	return &virtualImageResourceClient{cc}
}

func (c *virtualImageResourceClient) AddVirtualImageResource(ctx context.Context, in *AddVirtualImageResourceRequest, opts ...grpc.CallOption) (*AddVirtualImageResourceResponse, error) {
	out := new(AddVirtualImageResourceResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_resource.VirtualImageResource/AddVirtualImageResource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageResourceClient) CloneVirtualImageResource(ctx context.Context, in *CloneVirtualImageResourceRequest, opts ...grpc.CallOption) (*CloneVirtualImageResourceResponse, error) {
	out := new(CloneVirtualImageResourceResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_resource.VirtualImageResource/CloneVirtualImageResource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageResourceClient) SearchVirtualImageResource(ctx context.Context, in *SearchVirtualImageResourceRequest, opts ...grpc.CallOption) (*SearchVirtualImageResourceResponse, error) {
	out := new(SearchVirtualImageResourceResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_resource.VirtualImageResource/SearchVirtualImageResource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageResourceClient) EditVirtualImageResource(ctx context.Context, in *EditVirtualImageResourceRequest, opts ...grpc.CallOption) (*EditVirtualImageResourceResponse, error) {
	out := new(EditVirtualImageResourceResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_resource.VirtualImageResource/EditVirtualImageResource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageResourceClient) DeleteVirtualImageResource(ctx context.Context, in *DeleteVirtualImageResourceRequest, opts ...grpc.CallOption) (*DeleteVirtualImageResourceResponse, error) {
	out := new(DeleteVirtualImageResourceResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_resource.VirtualImageResource/DeleteVirtualImageResource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageResourceClient) SetVirtualImageResourceForSale(ctx context.Context, in *SetVirtualImageResourceForSaleRequest, opts ...grpc.CallOption) (*SetVirtualImageResourceForSaleResponse, error) {
	out := new(SetVirtualImageResourceForSaleResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_resource.VirtualImageResource/SetVirtualImageResourceForSale", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageResourceClient) UpdateVirtualImageResourceUrl(ctx context.Context, in *UpdateVirtualImageResourceUrlRequest, opts ...grpc.CallOption) (*UpdateVirtualImageResourceUrlResponse, error) {
	out := new(UpdateVirtualImageResourceUrlResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_resource.VirtualImageResource/UpdateVirtualImageResourceUrl", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageResourceClient) GetVirtualImageResourceBySuit(ctx context.Context, in *GetVirtualImageResourceBySuitRequest, opts ...grpc.CallOption) (*GetVirtualImageResourceBySuitResponse, error) {
	out := new(GetVirtualImageResourceBySuitResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_resource.VirtualImageResource/GetVirtualImageResourceBySuit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageResourceClient) BatchUpdateIcon(ctx context.Context, in *BatchUpdateIconRequest, opts ...grpc.CallOption) (*BatchUpdateIconResponse, error) {
	out := new(BatchUpdateIconResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_resource.VirtualImageResource/BatchUpdateIcon", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageResourceClient) GetLevelConfig(ctx context.Context, in *GetLevelConfigRequest, opts ...grpc.CallOption) (*GetLevelConfigResponse, error) {
	out := new(GetLevelConfigResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_resource.VirtualImageResource/GetLevelConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageResourceClient) UpdateLevelConfig(ctx context.Context, in *UpdateLevelConfigRequest, opts ...grpc.CallOption) (*UpdateLevelConfigResponse, error) {
	out := new(UpdateLevelConfigResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_resource.VirtualImageResource/UpdateLevelConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageResourceClient) AddLevelConfig(ctx context.Context, in *AddLevelConfigRequest, opts ...grpc.CallOption) (*AddLevelConfigResponse, error) {
	out := new(AddLevelConfigResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_resource.VirtualImageResource/AddLevelConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageResourceClient) GetNoticeCfgCache(ctx context.Context, in *GetNoticeCfgCacheRequest, opts ...grpc.CallOption) (*GetNoticeCfgCacheResponse, error) {
	out := new(GetNoticeCfgCacheResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_resource.VirtualImageResource/GetNoticeCfgCache", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageResourceClient) GetVirtualImageResourcesByIds(ctx context.Context, in *GetVirtualImageResourcesByIdsRequest, opts ...grpc.CallOption) (*GetVirtualImageResourcesByIdsResponse, error) {
	out := new(GetVirtualImageResourcesByIdsResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_resource.VirtualImageResource/GetVirtualImageResourcesByIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageResourceClient) GetClientListByPage(ctx context.Context, in *GetClientListByPageRequest, opts ...grpc.CallOption) (*GetClientListByPageResponse, error) {
	out := new(GetClientListByPageResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_resource.VirtualImageResource/GetClientListByPage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageResourceClient) GetVirtualImageResourceCategory(ctx context.Context, in *GetVirtualImageResourceCategoryRequest, opts ...grpc.CallOption) (*GetVirtualImageResourceCategoryResponse, error) {
	out := new(GetVirtualImageResourceCategoryResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_resource.VirtualImageResource/GetVirtualImageResourceCategory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageResourceClient) GetDefaultResourceList(ctx context.Context, in *GetDefaultResourceListRequest, opts ...grpc.CallOption) (*GetDefaultResourceListResponse, error) {
	out := new(GetDefaultResourceListResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_resource.VirtualImageResource/GetDefaultResourceList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageResourceClient) GetNoticeCfg(ctx context.Context, in *GetNoticeCfgRequest, opts ...grpc.CallOption) (*GetNoticeCfgResponse, error) {
	out := new(GetNoticeCfgResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_resource.VirtualImageResource/GetNoticeCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageResourceClient) UpdateNoticeCfg(ctx context.Context, in *UpdateNoticeCfgRequest, opts ...grpc.CallOption) (*UpdateNoticeCfgResponse, error) {
	out := new(UpdateNoticeCfgResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_resource.VirtualImageResource/UpdateNoticeCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageResourceClient) GetActionResourceMap(ctx context.Context, in *GetActionResourceMapRequest, opts ...grpc.CallOption) (*GetActionResourceMapResponse, error) {
	out := new(GetActionResourceMapResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_resource.VirtualImageResource/GetActionResourceMap", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageResourceClient) SetResourceToTest(ctx context.Context, in *SetResourceToTestRequest, opts ...grpc.CallOption) (*SetResourceToTestResponse, error) {
	out := new(SetResourceToTestResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_resource.VirtualImageResource/SetResourceToTest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VirtualImageResourceServer is the server API for VirtualImageResource service.
type VirtualImageResourceServer interface {
	// =================== 运营后台接口 ===================
	// 添加虚拟形象资源【git同步资源】
	AddVirtualImageResource(context.Context, *AddVirtualImageResourceRequest) (*AddVirtualImageResourceResponse, error)
	// 克隆虚拟形象资源【云测同步资源到线上】
	CloneVirtualImageResource(context.Context, *CloneVirtualImageResourceRequest) (*CloneVirtualImageResourceResponse, error)
	// 搜索虚拟形象资源列表
	SearchVirtualImageResource(context.Context, *SearchVirtualImageResourceRequest) (*SearchVirtualImageResourceResponse, error)
	// 编辑虚拟形象资源
	EditVirtualImageResource(context.Context, *EditVirtualImageResourceRequest) (*EditVirtualImageResourceResponse, error)
	// 删除虚拟形象资源
	DeleteVirtualImageResource(context.Context, *DeleteVirtualImageResourceRequest) (*DeleteVirtualImageResourceResponse, error)
	// 设置虚拟形象资源上架到商品
	SetVirtualImageResourceForSale(context.Context, *SetVirtualImageResourceForSaleRequest) (*SetVirtualImageResourceForSaleResponse, error)
	// 更新虚拟形象资源下载url
	UpdateVirtualImageResourceUrl(context.Context, *UpdateVirtualImageResourceUrlRequest) (*UpdateVirtualImageResourceUrlResponse, error)
	// 获取虚拟形象资源套装列表
	GetVirtualImageResourceBySuit(context.Context, *GetVirtualImageResourceBySuitRequest) (*GetVirtualImageResourceBySuitResponse, error)
	// 批量更新图标
	BatchUpdateIcon(context.Context, *BatchUpdateIconRequest) (*BatchUpdateIconResponse, error)
	// 获取等级配置
	GetLevelConfig(context.Context, *GetLevelConfigRequest) (*GetLevelConfigResponse, error)
	// 更新等级配置
	UpdateLevelConfig(context.Context, *UpdateLevelConfigRequest) (*UpdateLevelConfigResponse, error)
	// 增加等级配置
	AddLevelConfig(context.Context, *AddLevelConfigRequest) (*AddLevelConfigResponse, error)
	// 获取提醒配置缓存
	GetNoticeCfgCache(context.Context, *GetNoticeCfgCacheRequest) (*GetNoticeCfgCacheResponse, error)
	// 根据资源ID获取资源信息
	GetVirtualImageResourcesByIds(context.Context, *GetVirtualImageResourcesByIdsRequest) (*GetVirtualImageResourcesByIdsResponse, error)
	// =================== 客户端接口 ===================
	// 获取虚拟形象资源列表
	GetClientListByPage(context.Context, *GetClientListByPageRequest) (*GetClientListByPageResponse, error)
	// 获取资源品类列表
	GetVirtualImageResourceCategory(context.Context, *GetVirtualImageResourceCategoryRequest) (*GetVirtualImageResourceCategoryResponse, error)
	// 获取默认资源列表
	GetDefaultResourceList(context.Context, *GetDefaultResourceListRequest) (*GetDefaultResourceListResponse, error)
	// 获取提醒配置
	GetNoticeCfg(context.Context, *GetNoticeCfgRequest) (*GetNoticeCfgResponse, error)
	// 更新提醒配置
	UpdateNoticeCfg(context.Context, *UpdateNoticeCfgRequest) (*UpdateNoticeCfgResponse, error)
	// 获取动作资源映射 小-大
	GetActionResourceMap(context.Context, *GetActionResourceMapRequest) (*GetActionResourceMapResponse, error)
	// 设置资源为待测试
	SetResourceToTest(context.Context, *SetResourceToTestRequest) (*SetResourceToTestResponse, error)
}

func RegisterVirtualImageResourceServer(s *grpc.Server, srv VirtualImageResourceServer) {
	s.RegisterService(&_VirtualImageResource_serviceDesc, srv)
}

func _VirtualImageResource_AddVirtualImageResource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddVirtualImageResourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageResourceServer).AddVirtualImageResource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_resource.VirtualImageResource/AddVirtualImageResource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageResourceServer).AddVirtualImageResource(ctx, req.(*AddVirtualImageResourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageResource_CloneVirtualImageResource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CloneVirtualImageResourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageResourceServer).CloneVirtualImageResource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_resource.VirtualImageResource/CloneVirtualImageResource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageResourceServer).CloneVirtualImageResource(ctx, req.(*CloneVirtualImageResourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageResource_SearchVirtualImageResource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchVirtualImageResourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageResourceServer).SearchVirtualImageResource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_resource.VirtualImageResource/SearchVirtualImageResource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageResourceServer).SearchVirtualImageResource(ctx, req.(*SearchVirtualImageResourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageResource_EditVirtualImageResource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EditVirtualImageResourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageResourceServer).EditVirtualImageResource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_resource.VirtualImageResource/EditVirtualImageResource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageResourceServer).EditVirtualImageResource(ctx, req.(*EditVirtualImageResourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageResource_DeleteVirtualImageResource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteVirtualImageResourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageResourceServer).DeleteVirtualImageResource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_resource.VirtualImageResource/DeleteVirtualImageResource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageResourceServer).DeleteVirtualImageResource(ctx, req.(*DeleteVirtualImageResourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageResource_SetVirtualImageResourceForSale_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetVirtualImageResourceForSaleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageResourceServer).SetVirtualImageResourceForSale(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_resource.VirtualImageResource/SetVirtualImageResourceForSale",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageResourceServer).SetVirtualImageResourceForSale(ctx, req.(*SetVirtualImageResourceForSaleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageResource_UpdateVirtualImageResourceUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateVirtualImageResourceUrlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageResourceServer).UpdateVirtualImageResourceUrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_resource.VirtualImageResource/UpdateVirtualImageResourceUrl",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageResourceServer).UpdateVirtualImageResourceUrl(ctx, req.(*UpdateVirtualImageResourceUrlRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageResource_GetVirtualImageResourceBySuit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVirtualImageResourceBySuitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageResourceServer).GetVirtualImageResourceBySuit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_resource.VirtualImageResource/GetVirtualImageResourceBySuit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageResourceServer).GetVirtualImageResourceBySuit(ctx, req.(*GetVirtualImageResourceBySuitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageResource_BatchUpdateIcon_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUpdateIconRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageResourceServer).BatchUpdateIcon(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_resource.VirtualImageResource/BatchUpdateIcon",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageResourceServer).BatchUpdateIcon(ctx, req.(*BatchUpdateIconRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageResource_GetLevelConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLevelConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageResourceServer).GetLevelConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_resource.VirtualImageResource/GetLevelConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageResourceServer).GetLevelConfig(ctx, req.(*GetLevelConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageResource_UpdateLevelConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateLevelConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageResourceServer).UpdateLevelConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_resource.VirtualImageResource/UpdateLevelConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageResourceServer).UpdateLevelConfig(ctx, req.(*UpdateLevelConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageResource_AddLevelConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddLevelConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageResourceServer).AddLevelConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_resource.VirtualImageResource/AddLevelConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageResourceServer).AddLevelConfig(ctx, req.(*AddLevelConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageResource_GetNoticeCfgCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNoticeCfgCacheRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageResourceServer).GetNoticeCfgCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_resource.VirtualImageResource/GetNoticeCfgCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageResourceServer).GetNoticeCfgCache(ctx, req.(*GetNoticeCfgCacheRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageResource_GetVirtualImageResourcesByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVirtualImageResourcesByIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageResourceServer).GetVirtualImageResourcesByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_resource.VirtualImageResource/GetVirtualImageResourcesByIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageResourceServer).GetVirtualImageResourcesByIds(ctx, req.(*GetVirtualImageResourcesByIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageResource_GetClientListByPage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClientListByPageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageResourceServer).GetClientListByPage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_resource.VirtualImageResource/GetClientListByPage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageResourceServer).GetClientListByPage(ctx, req.(*GetClientListByPageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageResource_GetVirtualImageResourceCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVirtualImageResourceCategoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageResourceServer).GetVirtualImageResourceCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_resource.VirtualImageResource/GetVirtualImageResourceCategory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageResourceServer).GetVirtualImageResourceCategory(ctx, req.(*GetVirtualImageResourceCategoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageResource_GetDefaultResourceList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDefaultResourceListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageResourceServer).GetDefaultResourceList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_resource.VirtualImageResource/GetDefaultResourceList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageResourceServer).GetDefaultResourceList(ctx, req.(*GetDefaultResourceListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageResource_GetNoticeCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNoticeCfgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageResourceServer).GetNoticeCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_resource.VirtualImageResource/GetNoticeCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageResourceServer).GetNoticeCfg(ctx, req.(*GetNoticeCfgRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageResource_UpdateNoticeCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNoticeCfgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageResourceServer).UpdateNoticeCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_resource.VirtualImageResource/UpdateNoticeCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageResourceServer).UpdateNoticeCfg(ctx, req.(*UpdateNoticeCfgRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageResource_GetActionResourceMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActionResourceMapRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageResourceServer).GetActionResourceMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_resource.VirtualImageResource/GetActionResourceMap",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageResourceServer).GetActionResourceMap(ctx, req.(*GetActionResourceMapRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageResource_SetResourceToTest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetResourceToTestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageResourceServer).SetResourceToTest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_resource.VirtualImageResource/SetResourceToTest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageResourceServer).SetResourceToTest(ctx, req.(*SetResourceToTestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _VirtualImageResource_serviceDesc = grpc.ServiceDesc{
	ServiceName: "virtual_image_resource.VirtualImageResource",
	HandlerType: (*VirtualImageResourceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddVirtualImageResource",
			Handler:    _VirtualImageResource_AddVirtualImageResource_Handler,
		},
		{
			MethodName: "CloneVirtualImageResource",
			Handler:    _VirtualImageResource_CloneVirtualImageResource_Handler,
		},
		{
			MethodName: "SearchVirtualImageResource",
			Handler:    _VirtualImageResource_SearchVirtualImageResource_Handler,
		},
		{
			MethodName: "EditVirtualImageResource",
			Handler:    _VirtualImageResource_EditVirtualImageResource_Handler,
		},
		{
			MethodName: "DeleteVirtualImageResource",
			Handler:    _VirtualImageResource_DeleteVirtualImageResource_Handler,
		},
		{
			MethodName: "SetVirtualImageResourceForSale",
			Handler:    _VirtualImageResource_SetVirtualImageResourceForSale_Handler,
		},
		{
			MethodName: "UpdateVirtualImageResourceUrl",
			Handler:    _VirtualImageResource_UpdateVirtualImageResourceUrl_Handler,
		},
		{
			MethodName: "GetVirtualImageResourceBySuit",
			Handler:    _VirtualImageResource_GetVirtualImageResourceBySuit_Handler,
		},
		{
			MethodName: "BatchUpdateIcon",
			Handler:    _VirtualImageResource_BatchUpdateIcon_Handler,
		},
		{
			MethodName: "GetLevelConfig",
			Handler:    _VirtualImageResource_GetLevelConfig_Handler,
		},
		{
			MethodName: "UpdateLevelConfig",
			Handler:    _VirtualImageResource_UpdateLevelConfig_Handler,
		},
		{
			MethodName: "AddLevelConfig",
			Handler:    _VirtualImageResource_AddLevelConfig_Handler,
		},
		{
			MethodName: "GetNoticeCfgCache",
			Handler:    _VirtualImageResource_GetNoticeCfgCache_Handler,
		},
		{
			MethodName: "GetVirtualImageResourcesByIds",
			Handler:    _VirtualImageResource_GetVirtualImageResourcesByIds_Handler,
		},
		{
			MethodName: "GetClientListByPage",
			Handler:    _VirtualImageResource_GetClientListByPage_Handler,
		},
		{
			MethodName: "GetVirtualImageResourceCategory",
			Handler:    _VirtualImageResource_GetVirtualImageResourceCategory_Handler,
		},
		{
			MethodName: "GetDefaultResourceList",
			Handler:    _VirtualImageResource_GetDefaultResourceList_Handler,
		},
		{
			MethodName: "GetNoticeCfg",
			Handler:    _VirtualImageResource_GetNoticeCfg_Handler,
		},
		{
			MethodName: "UpdateNoticeCfg",
			Handler:    _VirtualImageResource_UpdateNoticeCfg_Handler,
		},
		{
			MethodName: "GetActionResourceMap",
			Handler:    _VirtualImageResource_GetActionResourceMap_Handler,
		},
		{
			MethodName: "SetResourceToTest",
			Handler:    _VirtualImageResource_SetResourceToTest_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/virtual-image-resource/virtual-image-resource.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/virtual-image-resource/virtual-image-resource.proto", fileDescriptor_virtual_image_resource_3001f8b4f1250449)
}

var fileDescriptor_virtual_image_resource_3001f8b4f1250449 = []byte{
	// 4123 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x3b, 0x6b, 0x6f, 0xdb, 0x58,
	0x76, 0x4b, 0xc9, 0xb6, 0xec, 0x23, 0x3f, 0x6e, 0xee, 0x38, 0xb6, 0xac, 0xbc, 0x1c, 0x39, 0x0f,
	0xc7, 0x33, 0x71, 0x32, 0x49, 0x9c, 0x64, 0x06, 0x3b, 0xb3, 0x43, 0x4b, 0xb4, 0xcc, 0xb5, 0x4c,
	0x6a, 0x49, 0x6a, 0x32, 0x19, 0x14, 0x4b, 0xd0, 0xd2, 0xb5, 0xcd, 0x46, 0x22, 0xb5, 0x22, 0x15,
	0xc7, 0x5d, 0xa0, 0xd8, 0xdd, 0xb6, 0x3b, 0x6d, 0x81, 0x02, 0x5b, 0xf4, 0x63, 0x0b, 0x14, 0x28,
	0xfa, 0xa5, 0x3f, 0xa0, 0xc0, 0xfe, 0x85, 0x7e, 0xeb, 0x87, 0xf6, 0x5b, 0x9f, 0xe8, 0x6b, 0x67,
	0x77, 0xfb, 0x6e, 0xd3, 0xe7, 0x4e, 0x0b, 0x5e, 0x3e, 0x4c, 0x4a, 0xa2, 0x44, 0x79, 0x06, 0x45,
	0xbf, 0x89, 0xe7, 0x9e, 0xf7, 0x3d, 0xf7, 0xdc, 0x73, 0xef, 0x3d, 0x82, 0x6d, 0xdb, 0xbe, 0xf7,
	0x8d, 0xae, 0x5e, 0x7f, 0x61, 0xe9, 0xcd, 0x97, 0xa4, 0x73, 0xef, 0xa5, 0xde, 0xb1, 0xbb, 0x5a,
	0xf3, 0xae, 0xde, 0xd2, 0x8e, 0xc8, 0xdd, 0x0e, 0xb1, 0xcc, 0x6e, 0xa7, 0x4e, 0x62, 0xc0, 0x9b,
	0xed, 0x8e, 0x69, 0x9b, 0x78, 0xc9, 0x1b, 0x55, 0xe9, 0xa8, 0xea, 0x8f, 0x16, 0x5e, 0xcf, 0x43,
	0xee, 0x43, 0x77, 0x88, 0x77, 0x46, 0x24, 0x6f, 0x80, 0x37, 0x0e, 0x4d, 0x3c, 0x0f, 0x29, 0xbd,
	0x91, 0x63, 0x56, 0x99, 0xf5, 0x39, 0x29, 0xa5, 0x37, 0xf0, 0x25, 0x98, 0xb1, 0x5e, 0xe8, 0x86,
	0x6a, 0x68, 0x2d, 0x92, 0x4b, 0xad, 0x32, 0xeb, 0x33, 0xd2, 0xb4, 0x03, 0x10, 0xb4, 0x16, 0xc1,
	0xd7, 0x61, 0xd6, 0xe7, 0xaa, 0x76, 0x3b, 0xcd, 0x5c, 0x9a, 0x8e, 0x67, 0x7d, 0x58, 0xad, 0xd3,
	0xc4, 0x97, 0x61, 0x86, 0x58, 0x16, 0x31, 0x6c, 0x5d, 0x6b, 0xe6, 0x26, 0x56, 0x99, 0xf5, 0x69,
	0xe9, 0x0c, 0x80, 0xaf, 0x41, 0x96, 0x18, 0xf5, 0xce, 0x69, 0xdb, 0x56, 0x5f, 0x90, 0xd3, 0xdc,
	0x24, 0xa5, 0x07, 0x0f, 0xb4, 0x47, 0x4e, 0x31, 0x82, 0x74, 0xab, 0xb1, 0x95, 0x9b, 0xa2, 0x03,
	0xce, 0x4f, 0xbc, 0x06, 0x73, 0x81, 0x4c, 0xfb, 0xb4, 0x4d, 0x72, 0x19, 0xaa, 0x6b, 0xa0, 0x88,
	0x72, 0xda, 0x26, 0x38, 0x0f, 0xd3, 0x75, 0xcd, 0x26, 0x47, 0x66, 0xe7, 0x34, 0x37, 0x4d, 0xc7,
	0x83, 0x6f, 0x47, 0x69, 0xab, 0x7b, 0xa0, 0x06, 0xe3, 0x33, 0x74, 0x3c, 0x6b, 0x75, 0x0f, 0x8a,
	0x3e, 0xca, 0x4d, 0x98, 0xb7, 0xda, 0x4d, 0xdd, 0x20, 0xea, 0x4b, 0xd2, 0xb1, 0x74, 0xd3, 0xc8,
	0x01, 0x55, 0x60, 0xce, 0x85, 0x7e, 0xe8, 0x02, 0x1d, 0x4e, 0x0d, 0xdd, 0x6a, 0x37, 0xb5, 0x53,
	0xd7, 0x3d, 0x59, 0xd7, 0x7c, 0x0f, 0x46, 0x3d, 0xb4, 0x02, 0xd3, 0x7a, 0xdd, 0x34, 0xa8, 0x77,
	0x66, 0xe9, 0x70, 0xc6, 0xf9, 0x76, 0x3c, 0x83, 0x20, 0x6d, 0x91, 0x57, 0xb9, 0x39, 0x2a, 0xde,
	0xf9, 0x89, 0xaf, 0x00, 0x34, 0xc9, 0x4b, 0xd2, 0x54, 0x1d, 0x94, 0xdc, 0x3c, 0x45, 0x9f, 0xa1,
	0x10, 0xbe, 0x6e, 0x1a, 0x78, 0x11, 0x26, 0xe9, 0x47, 0x6e, 0x81, 0x92, 0xb8, 0x1f, 0x78, 0x09,
	0xa6, 0x2c, 0x5b, 0xb3, 0xbb, 0x56, 0x0e, 0x51, 0xb0, 0xf7, 0x45, 0x95, 0x23, 0x87, 0x5a, 0xb7,
	0x69, 0xab, 0x56, 0x57, 0xb7, 0x73, 0x17, 0x3c, 0xe5, 0x5c, 0x98, 0xdc, 0xd5, 0x6d, 0x47, 0x9e,
	0x75, 0x4c, 0x9a, 0x87, 0xaa, 0xad, 0xb7, 0x48, 0x0e, 0x53, 0xf2, 0x19, 0x0a, 0x51, 0xf4, 0x16,
	0xa1, 0x93, 0xf3, 0xaa, 0xad, 0x77, 0x88, 0x3b, 0xfe, 0x06, 0x1d, 0x07, 0x17, 0x44, 0x11, 0xf2,
	0x30, 0x6d, 0xb6, 0x49, 0x47, 0xb3, 0xcd, 0x4e, 0x6e, 0xd1, 0x0d, 0x0d, 0xff, 0xdb, 0x21, 0xae,
	0x77, 0x88, 0x66, 0x7b, 0xc4, 0x17, 0x5d, 0x62, 0x17, 0xe4, 0x73, 0xef, 0xb6, 0x1b, 0x01, 0xc2,
	0x92, 0x8b, 0xe0, 0x82, 0x28, 0xc2, 0x25, 0x98, 0xd1, 0x2d, 0xd5, 0x34, 0x1c, 0x8f, 0xe7, 0x96,
	0x69, 0xe4, 0x4c, 0xeb, 0x96, 0x48, 0xbf, 0x1d, 0xeb, 0x74, 0x4b, 0xad, 0x9b, 0xad, 0x96, 0xd9,
	0xd0, 0xed, 0xd3, 0x5c, 0x8e, 0x8e, 0x67, 0x75, 0xab, 0xe8, 0x83, 0x22, 0x81, 0x42, 0xa7, 0x67,
	0x85, 0xaa, 0x18, 0x04, 0x0a, 0x9d, 0x9f, 0x1c, 0x64, 0xfc, 0x29, 0xce, 0x53, 0x0d, 0xfc, 0x4f,
	0xbc, 0x0c, 0x19, 0xdd, 0xb4, 0x54, 0x27, 0xfa, 0x2e, 0x51, 0xc2, 0x29, 0xdd, 0xb4, 0xf6, 0x1b,
	0x5b, 0x78, 0x1d, 0x90, 0x33, 0x10, 0x09, 0xfc, 0xcb, 0x14, 0x63, 0x5e, 0x37, 0x2d, 0x29, 0x14,
	0xfb, 0xc1, 0x7c, 0x9e, 0x90, 0x83, 0x76, 0xee, 0x4a, 0x68, 0x3e, 0x9f, 0x91, 0x83, 0x36, 0x75,
	0x7f, 0x5d, 0x6b, 0x12, 0x55, 0x3b, 0x68, 0x92, 0xdc, 0x55, 0x77, 0x6d, 0x50, 0x08, 0x7b, 0xd0,
	0x24, 0xf8, 0x4d, 0xb8, 0xe0, 0x4f, 0xa0, 0x66, 0xe8, 0x2d, 0xcd, 0x76, 0x94, 0xbc, 0x46, 0x99,
	0x20, 0x6f, 0x80, 0xf5, 0xe1, 0x0e, 0x2f, 0xdd, 0x52, 0x1b, 0xa4, 0x49, 0x6c, 0xd2, 0xc8, 0xad,
	0xba, 0xbc, 0x74, 0xab, 0xe4, 0x02, 0xf0, 0x6d, 0x58, 0xd0, 0x6c, 0x5b, 0xab, 0x1f, 0xb7, 0x88,
	0x61, 0xbb, 0xde, 0xb8, 0xee, 0xaa, 0x7c, 0x06, 0xa6, 0xfe, 0xb8, 0x06, 0x59, 0xba, 0xdc, 0x0f,
	0xb5, 0xba, 0x6e, 0x1c, 0xe5, 0x0a, 0xee, 0xac, 0x38, 0xa0, 0x1d, 0x0a, 0x71, 0x38, 0x05, 0x96,
	0xb7, 0x3b, 0xe4, 0x50, 0x7f, 0x95, 0x5b, 0x73, 0x39, 0xf9, 0xe0, 0x2a, 0x85, 0xe2, 0xaf, 0x03,
	0xd4, 0xbb, 0x96, 0x6d, 0xb6, 0xd4, 0x96, 0xd6, 0xce, 0xdd, 0x58, 0x4d, 0xaf, 0x67, 0x1f, 0x7c,
	0x65, 0x73, 0x70, 0x4a, 0xda, 0x8c, 0x4b, 0x47, 0x9b, 0x45, 0xca, 0x62, 0x5f, 0x6b, 0x73, 0x86,
	0xdd, 0x39, 0x95, 0x66, 0xea, 0xfe, 0x37, 0xfe, 0x08, 0x68, 0x1e, 0xa2, 0xdc, 0x6f, 0x52, 0xee,
	0xef, 0x8d, 0xcd, 0x5d, 0x7e, 0xa1, 0x1b, 0x01, 0xef, 0x8c, 0xe5, 0x7e, 0xe1, 0x03, 0x98, 0x75,
	0x26, 0x38, 0xe0, 0x7e, 0x8b, 0x72, 0xff, 0x60, 0x6c, 0xee, 0xbc, 0x69, 0x45, 0x04, 0x80, 0x1e,
	0x00, 0x1c, 0x3f, 0x3b, 0x32, 0xfc, 0xd8, 0xbb, 0xed, 0xfa, 0x59, 0x37, 0x2d, 0x3f, 0xb7, 0xdc,
	0x82, 0x05, 0xdd, 0x52, 0x0d, 0x72, 0x12, 0x08, 0xca, 0xad, 0xd3, 0x59, 0x9d, 0xd3, 0x2d, 0x81,
	0x9c, 0xf8, 0x82, 0xf0, 0x0b, 0x58, 0xb0, 0xc9, 0x2b, 0xbb, 0xdb, 0xa1, 0x81, 0x48, 0xf5, 0xbd,
	0x43, 0xf5, 0x2d, 0x8e, 0xad, 0xaf, 0xe2, 0xf2, 0xa9, 0x75, 0x9a, 0x81, 0xca, 0x73, 0x76, 0x18,
	0x96, 0xff, 0x32, 0xcc, 0x47, 0x27, 0xc4, 0x49, 0x62, 0x4e, 0xe2, 0x66, 0xdc, 0xfc, 0xfc, 0x82,
	0x9c, 0x3a, 0x59, 0xea, 0xa5, 0xd6, 0xec, 0xfa, 0x9b, 0x85, 0xfb, 0xf1, 0x6e, 0xea, 0x29, 0x93,
	0xff, 0x29, 0x98, 0x0d, 0xfb, 0x63, 0x00, 0xed, 0xe3, 0x30, 0x6d, 0xf6, 0xc1, 0x6a, 0x9c, 0x09,
	0x0e, 0x1b, 0x47, 0xe5, 0x30, 0x77, 0x15, 0x16, 0x7a, 0x1c, 0xfe, 0x05, 0x0b, 0xf8, 0x00, 0x70,
	0xbf, 0x87, 0xc6, 0x71, 0x40, 0xe1, 0x63, 0x98, 0xf6, 0x19, 0x3b, 0x74, 0x4e, 0xe2, 0xf0, 0xe8,
	0xba, 0x9d, 0x26, 0xde, 0x80, 0x0b, 0x2d, 0xdd, 0x50, 0x0f, 0x4c, 0x83, 0x9c, 0x05, 0x46, 0x8a,
	0x06, 0xc6, 0x42, 0x4b, 0x37, 0xb6, 0x1d, 0xb8, 0x1f, 0x1d, 0xde, 0xb6, 0x98, 0x0e, 0xb6, 0xc5,
	0x42, 0x1b, 0xae, 0xb2, 0x8d, 0xc6, 0xa0, 0xb9, 0x95, 0xc8, 0x37, 0xba, 0xc4, 0xb2, 0xb1, 0x00,
	0x33, 0xbe, 0x7d, 0x56, 0x8e, 0xa1, 0x31, 0x72, 0x7f, 0xdc, 0x18, 0x91, 0xce, 0x58, 0x14, 0xae,
	0xc3, 0xb5, 0x58, 0x89, 0x56, 0xdb, 0x34, 0x2c, 0x52, 0xe8, 0xc0, 0x6a, 0xb1, 0x69, 0x1a, 0xe4,
	0xff, 0x52, 0xad, 0x35, 0xb8, 0x3e, 0x44, 0xa6, 0xa7, 0xd8, 0x1f, 0xa4, 0xe1, 0xba, 0x4c, 0xb4,
	0x4e, 0xfd, 0x78, 0x98, 0x6a, 0x7e, 0x2d, 0x94, 0xf6, 0x6a, 0x21, 0x0c, 0x13, 0x5e, 0x19, 0x94,
	0x5e, 0x9f, 0x91, 0xe8, 0xef, 0x48, 0xa5, 0x91, 0x1e, 0x51, 0x69, 0x4c, 0xf4, 0x57, 0x1a, 0x5e,
	0x11, 0x30, 0x79, 0x56, 0x04, 0x04, 0xbb, 0xfc, 0x54, 0x78, 0x97, 0xc7, 0x30, 0x41, 0x77, 0xf1,
	0x0c, 0x9d, 0x71, 0xfa, 0xdb, 0xd9, 0xf9, 0xcd, 0xc3, 0x43, 0x8b, 0xd8, 0x5e, 0x09, 0xe3, 0x7d,
	0x51, 0x0e, 0x7a, 0x4b, 0xb7, 0x69, 0xd1, 0xe2, 0x70, 0x70, 0x3e, 0xa2, 0x85, 0x5c, 0xb6, 0xa7,
	0x90, 0xbb, 0x03, 0x28, 0xd8, 0x4b, 0xd5, 0x43, 0xbd, 0x69, 0x93, 0x0e, 0x2d, 0x57, 0xe6, 0xa4,
	0x85, 0x00, 0xbe, 0x43, 0xc1, 0xde, 0xce, 0x7b, 0x56, 0xd3, 0xb9, 0xf5, 0x4b, 0x56, 0xb7, 0xb8,
	0xa0, 0xaa, 0xeb, 0x2b, 0xd1, 0xe6, 0xa9, 0x0b, 0xa3, 0x25, 0xda, 0x0d, 0x98, 0x0f, 0xf4, 0x51,
	0x9b, 0xba, 0x65, 0xe7, 0x16, 0xa8, 0x5b, 0x67, 0x7d, 0xa5, 0x2a, 0xba, 0x65, 0xe3, 0xb7, 0x00,
	0x47, 0x36, 0x71, 0x17, 0x13, 0x51, 0x4c, 0x14, 0xde, 0xc9, 0x1d, 0xec, 0xc2, 0xf7, 0x19, 0x28,
	0x0c, 0x9b, 0x56, 0x77, 0xf6, 0x43, 0x8e, 0x63, 0x06, 0x3b, 0x2e, 0x15, 0x76, 0x5c, 0x24, 0x40,
	0xd3, 0x9f, 0x3b, 0x40, 0x1d, 0x29, 0xb6, 0x69, 0x7b, 0xd5, 0xf0, 0x9c, 0xe4, 0x7e, 0x14, 0x6e,
	0xc1, 0x8d, 0x32, 0xb1, 0x07, 0xd1, 0x6f, 0x9f, 0x3a, 0xc5, 0x9a, 0x17, 0x93, 0x85, 0xef, 0x30,
	0x70, 0x79, 0x10, 0x96, 0x83, 0x43, 0x13, 0x8b, 0x1f, 0x29, 0x4c, 0x28, 0x52, 0x22, 0x26, 0xa4,
	0x3e, 0xff, 0x1a, 0xfb, 0x26, 0xdc, 0x1c, 0xa1, 0xac, 0xe7, 0x69, 0xa9, 0x7f, 0x71, 0x3f, 0x1a,
	0x47, 0xb0, 0x6f, 0x55, 0x58, 0xb8, 0x09, 0xd7, 0xb8, 0x86, 0x6e, 0x0f, 0x5b, 0xb8, 0x15, 0x98,
	0x0e, 0x76, 0x4d, 0x86, 0x66, 0xfa, 0xf1, 0xcd, 0x0d, 0x38, 0x14, 0x0a, 0xb0, 0x1a, 0x2f, 0xd0,
	0x4b, 0x28, 0x47, 0x90, 0x2f, 0x13, 0xbb, 0xd8, 0xd4, 0x89, 0x61, 0x3b, 0xa1, 0xb8, 0x7d, 0x5a,
	0xa5, 0x68, 0xae, 0x3e, 0xe3, 0x05, 0xdc, 0x25, 0x98, 0x69, 0x6a, 0x36, 0xb1, 0x6c, 0x55, 0x6f,
	0xf8, 0x39, 0xc5, 0x05, 0xf0, 0x8d, 0xc2, 0x1f, 0xa6, 0xe0, 0xd2, 0x40, 0x49, 0xff, 0x7f, 0x63,
	0x3b, 0x6a, 0xd0, 0x64, 0xd4, 0x20, 0x7c, 0x11, 0xa6, 0x9c, 0x7c, 0x62, 0x34, 0x68, 0xc2, 0x9b,
	0x96, 0x26, 0x75, 0x8b, 0x33, 0x1a, 0xf4, 0xf8, 0x62, 0x9e, 0x18, 0x4d, 0x53, 0x6b, 0xd0, 0x0a,
	0x3b, 0xe3, 0x1d, 0x5f, 0x3c, 0x98, 0x53, 0x5e, 0xaf, 0x03, 0x0a, 0xa3, 0xa8, 0xba, 0x69, 0xd1,
	0xc3, 0xde, 0x8c, 0x34, 0x1f, 0x42, 0xe3, 0x4d, 0x2b, 0xc2, 0xcc, 0xd9, 0x37, 0x67, 0xa2, 0xcc,
	0xf6, 0x1b, 0x5b, 0x85, 0x87, 0x70, 0xdd, 0x2d, 0x96, 0x93, 0x6c, 0x08, 0xde, 0xe1, 0xb8, 0x70,
	0x03, 0x0a, 0xc3, 0x88, 0xbc, 0xd8, 0x78, 0x1a, 0xbb, 0xb4, 0xad, 0xed, 0x53, 0xbe, 0x61, 0xf9,
	0xdc, 0x11, 0xa4, 0xf5, 0x86, 0xe5, 0xed, 0x37, 0xce, 0xcf, 0xc2, 0x49, 0xec, 0x3a, 0xf3, 0x29,
	0xbd, 0x59, 0xff, 0xa2, 0x37, 0xd1, 0x27, 0x70, 0x53, 0x1e, 0x2c, 0x78, 0xc7, 0xec, 0xc8, 0x5a,
	0x33, 0x6e, 0x8b, 0x2c, 0xac, 0xc3, 0xad, 0x51, 0x84, 0x9e, 0x57, 0x3e, 0x65, 0xe0, 0x46, 0x8d,
	0x9e, 0xf6, 0x06, 0x61, 0xd7, 0x3a, 0x4d, 0x5f, 0x44, 0x64, 0xe3, 0x62, 0x46, 0xdc, 0x40, 0xa4,
	0xfa, 0x6f, 0x20, 0x7a, 0xee, 0x18, 0xd2, 0x71, 0x77, 0x0c, 0x13, 0x67, 0x77, 0x0c, 0x11, 0x77,
	0x4e, 0x7e, 0x7e, 0x77, 0xde, 0x86, 0x9b, 0x23, 0x4c, 0xf5, 0x9c, 0xf2, 0x5d, 0x06, 0xae, 0x86,
	0x71, 0xaa, 0x5a, 0x87, 0x18, 0xb6, 0x5f, 0x2d, 0xd0, 0xfc, 0x1e, 0x2e, 0x38, 0x98, 0x9e, 0x82,
	0x63, 0x0d, 0xe6, 0xfc, 0xdf, 0xe1, 0x0b, 0x9b, 0x59, 0x1f, 0x48, 0x5d, 0x16, 0x46, 0xa2, 0xbb,
	0xb3, 0x9b, 0x62, 0x02, 0x24, 0x67, 0x77, 0x2e, 0xfc, 0x6e, 0x1a, 0x2e, 0x85, 0x15, 0x91, 0xcf,
	0x6a, 0x16, 0xaa, 0x45, 0x6f, 0x69, 0xc3, 0xf4, 0x97, 0x36, 0x1b, 0x70, 0x21, 0x8c, 0x12, 0x56,
	0x68, 0x21, 0x84, 0x47, 0x75, 0xba, 0x07, 0x8b, 0x11, 0x5c, 0xbd, 0x75, 0x14, 0xba, 0x50, 0xba,
	0x10, 0x42, 0xe7, 0x5b, 0x47, 0xce, 0xa4, 0xde, 0x86, 0x85, 0x36, 0xf5, 0x4d, 0x6f, 0x75, 0x35,
	0xdf, 0x8e, 0xb8, 0xac, 0x4f, 0x0b, 0x6a, 0xb1, 0x9b, 0x83, 0xc2, 0x5a, 0xd0, 0x92, 0xe4, 0x03,
	0xb8, 0x32, 0x48, 0x0b, 0xd5, 0x22, 0x4d, 0x52, 0x77, 0xce, 0xd5, 0xee, 0x35, 0xd4, 0x4a, 0x9f,
	0x3a, 0xb2, 0x87, 0x80, 0x1f, 0x43, 0xee, 0x84, 0x1c, 0xa8, 0x03, 0x6d, 0x71, 0x33, 0xd8, 0xe2,
	0x09, 0x39, 0x90, 0xfb, 0xcc, 0x29, 0xc3, 0xf5, 0x38, 0xba, 0x33, 0xe9, 0x6e, 0x6e, 0xbb, 0x3c,
	0x88, 0x81, 0xaf, 0x40, 0xe1, 0x35, 0x03, 0xab, 0x83, 0x82, 0x2c, 0x32, 0x79, 0xc7, 0xb0, 0xd8,
	0xe3, 0x3c, 0x55, 0x37, 0x0e, 0x4d, 0x6f, 0xab, 0x7c, 0x9c, 0x24, 0xd2, 0xfb, 0x03, 0x53, 0xc2,
	0xed, 0xfe, 0x60, 0x3d, 0x86, 0xa5, 0xa8, 0x4d, 0xc6, 0xa1, 0xe9, 0x96, 0x70, 0x6e, 0x15, 0xf2,
	0x30, 0x89, 0xac, 0x9e, 0xd8, 0x93, 0xde, 0xb0, 0xa2, 0x00, 0x5a, 0xfa, 0xad, 0xc3, 0xad, 0x98,
	0x54, 0xe9, 0xa3, 0xfa, 0x15, 0xd4, 0x6f, 0x31, 0x70, 0x7b, 0x24, 0xaa, 0x97, 0x57, 0x4f, 0xe0,
	0x52, 0x90, 0x5e, 0x62, 0x8d, 0x78, 0x3a, 0x4e, 0x6a, 0x88, 0x58, 0x92, 0xeb, 0x0c, 0x80, 0x52,
	0x73, 0xbe, 0x0e, 0x4b, 0xdb, 0x9a, 0x5d, 0x3f, 0x76, 0xd3, 0x06, 0x5f, 0x37, 0x0d, 0x3f, 0x1d,
	0x96, 0x60, 0x82, 0xca, 0x3e, 0x6f, 0x96, 0xa7, 0xd4, 0x85, 0x15, 0x58, 0xee, 0xe3, 0xef, 0xe5,
	0xa0, 0x6b, 0x70, 0xa5, 0x4c, 0xec, 0x92, 0x7b, 0xc3, 0xe4, 0x93, 0x3a, 0x4a, 0xf9, 0x0e, 0xfc,
	0xfe, 0x04, 0x5c, 0x8d, 0xc3, 0xf0, 0xfc, 0x76, 0x13, 0xe6, 0x5b, 0x5a, 0xf3, 0x4c, 0x1d, 0x7f,
	0x57, 0x9b, 0x6b, 0xb9, 0x5b, 0x80, 0x57, 0x2e, 0xdc, 0x01, 0x74, 0x48, 0x7a, 0x10, 0x53, 0x14,
	0x71, 0xc1, 0x85, 0x9f, 0xa1, 0x2e, 0x43, 0xc6, 0xec, 0xda, 0xea, 0xa1, 0x6e, 0xd3, 0x3a, 0xc5,
	0x29, 0x6c, 0xba, 0xf6, 0x8e, 0x6e, 0xe3, 0x9f, 0x01, 0x4c, 0x39, 0x04, 0x77, 0x64, 0xf4, 0x0e,
	0xc4, 0x4d, 0xda, 0x95, 0x38, 0xef, 0x0c, 0x57, 0x7f, 0x73, 0x5f, 0x6b, 0x92, 0xe0, 0x6e, 0x2d,
	0xb8, 0x0c, 0x41, 0xad, 0x1e, 0x30, 0xfe, 0x16, 0x03, 0x8b, 0x9e, 0x01, 0x51, 0xf1, 0x53, 0x54,
	0xbc, 0x70, 0x4e, 0xf1, 0x3b, 0xa4, 0x35, 0x50, 0x01, 0x7c, 0xd8, 0x37, 0x80, 0x57, 0x61, 0xb6,
	0x6b, 0xa8, 0xd6, 0xb1, 0x79, 0xe2, 0x86, 0xe4, 0x34, 0x75, 0x0e, 0x74, 0x0d, 0xf9, 0xd8, 0x3c,
	0x71, 0xb8, 0xe6, 0x8b, 0x70, 0x71, 0xa0, 0x3d, 0xe1, 0xab, 0x8b, 0xb9, 0x01, 0x57, 0x17, 0x73,
	0xe1, 0xcb, 0x0f, 0x0e, 0x96, 0x63, 0xb4, 0x1a, 0x87, 0x8d, 0x73, 0x7a, 0xc9, 0x56, 0x9c, 0x03,
	0x6d, 0xd1, 0x34, 0x0e, 0xf5, 0xa3, 0xb3, 0xc3, 0x2e, 0x13, 0x3e, 0xec, 0x46, 0xef, 0xc1, 0x53,
	0xbd, 0xf7, 0xe0, 0xd1, 0x6b, 0xd5, 0x74, 0xef, 0xb5, 0x6a, 0xcf, 0xc5, 0xf2, 0x44, 0xef, 0xc5,
	0x72, 0x61, 0x19, 0x2e, 0x96, 0x89, 0x1d, 0x52, 0xc3, 0x0f, 0xec, 0xaf, 0xc1, 0x52, 0xef, 0x80,
	0x17, 0xcf, 0x4f, 0x22, 0x8b, 0x6e, 0x2d, 0x6e, 0x5e, 0xc3, 0xa4, 0xee, 0x3a, 0xfb, 0x1a, 0xe4,
	0xdc, 0x25, 0xd6, 0x2f, 0x0e, 0x6f, 0x41, 0xba, 0x7e, 0x78, 0xe4, 0x65, 0xdd, 0x44, 0x3c, 0x1d,
	0xfc, 0xc2, 0x25, 0x58, 0x19, 0xc0, 0xd2, 0x5b, 0xbc, 0x02, 0x5c, 0x64, 0x1b, 0x8d, 0x2f, 0x4e,
	0x58, 0x0e, 0x96, 0x7a, 0xf9, 0x79, 0x92, 0x7e, 0x3f, 0x05, 0x33, 0x82, 0x69, 0xeb, 0x75, 0x52,
	0x3c, 0x3c, 0x72, 0xe6, 0xe4, 0x80, 0x1c, 0xe9, 0x86, 0xeb, 0x73, 0x47, 0x4a, 0x5a, 0x9a, 0xa1,
	0x10, 0x7a, 0x97, 0xbf, 0x02, 0xd3, 0xc4, 0x68, 0xb8, 0x83, 0x29, 0x3a, 0x98, 0x21, 0x46, 0x83,
	0x0e, 0xdd, 0x84, 0xf9, 0x76, 0xf7, 0xa0, 0xa9, 0xd7, 0xd5, 0xba, 0x69, 0xd8, 0xc4, 0xb0, 0xbd,
	0x19, 0x9d, 0x73, 0xa1, 0x45, 0x17, 0x88, 0xef, 0xc3, 0x62, 0x14, 0x4d, 0xad, 0x9b, 0x4d, 0xb3,
	0xe3, 0x55, 0x6d, 0x38, 0x82, 0x5c, 0x74, 0x46, 0xf0, 0x16, 0x2c, 0xf7, 0x50, 0xfc, 0x74, 0xb7,
	0xd5, 0xa6, 0x5b, 0xb1, 0xfb, 0xce, 0xb4, 0x18, 0x21, 0xfa, 0x6a, 0xb7, 0xd5, 0x76, 0xb6, 0xe2,
	0x35, 0x98, 0x3b, 0x6c, 0x9a, 0x9a, 0x1d, 0xa8, 0xe3, 0x6e, 0xfa, 0xb3, 0x14, 0xe8, 0x6b, 0xf3,
	0x08, 0x96, 0x22, 0x48, 0x6a, 0xa3, 0xdb, 0x71, 0x2f, 0xe8, 0xdd, 0xd7, 0xa8, 0xc5, 0x30, 0x76,
	0xc9, 0x1b, 0xc3, 0x57, 0x21, 0x7b, 0xac, 0x59, 0x6a, 0x87, 0x34, 0xd4, 0x86, 0x69, 0xd3, 0xfd,
	0x7c, 0x5a, 0x9a, 0x39, 0xd6, 0x2c, 0x89, 0x34, 0x4a, 0xa6, 0x5d, 0xb8, 0x08, 0x6f, 0x94, 0x89,
	0x1d, 0x38, 0xd5, 0x0f, 0xcb, 0x3d, 0x58, 0x8c, 0x82, 0xbd, 0xa0, 0x7c, 0x18, 0x9e, 0xd2, 0xeb,
	0x71, 0x53, 0x7a, 0x46, 0x47, 0x27, 0x74, 0x1f, 0x96, 0xdc, 0xe8, 0xe9, 0x15, 0x73, 0x3e, 0x76,
	0x2b, 0xb0, 0xdc, 0xc7, 0xce, 0x0b, 0x90, 0x3c, 0xe4, 0xc2, 0x6a, 0x17, 0xb5, 0xfa, 0xb1, 0x7f,
	0x6c, 0x28, 0x54, 0x61, 0x65, 0xc0, 0xd8, 0xe7, 0xb1, 0xeb, 0x0a, 0x3d, 0x16, 0xb3, 0x75, 0xc7,
	0xd1, 0x7e, 0x52, 0xdd, 0xd7, 0xda, 0xbe, 0xc0, 0x3f, 0x67, 0xe0, 0xf2, 0xe0, 0x71, 0x4f, 0xe8,
	0x37, 0xe1, 0x0d, 0x8d, 0x0e, 0x9e, 0x3d, 0xec, 0x38, 0x89, 0xdc, 0x5d, 0xf0, 0x7b, 0x43, 0x12,
	0x79, 0x2c, 0xcb, 0xcd, 0xbe, 0x11, 0x37, 0x8b, 0x5f, 0xd0, 0x7a, 0xe1, 0xf9, 0x12, 0x2c, 0x0d,
	0x46, 0x1e, 0x2b, 0xb9, 0xfe, 0x3a, 0x03, 0xb8, 0x78, 0xac, 0x19, 0x3d, 0x2f, 0xba, 0x7d, 0xef,
	0x60, 0xcc, 0x80, 0x77, 0xb0, 0x35, 0x98, 0x6b, 0x99, 0x0d, 0xfd, 0xf0, 0x54, 0xed, 0x10, 0xcd,
	0x0a, 0xb2, 0xee, 0xac, 0x0b, 0x94, 0x28, 0x2c, 0xf2, 0xde, 0x97, 0xee, 0x7f, 0xef, 0x0b, 0xa4,
	0xe8, 0x0d, 0x3f, 0xeb, 0xfa, 0x20, 0xbe, 0x51, 0x38, 0x82, 0x9c, 0x4c, 0x82, 0xfd, 0x4e, 0x31,
	0x15, 0x12, 0x54, 0x14, 0x78, 0x0f, 0xb2, 0x75, 0xaa, 0xb8, 0x1a, 0xca, 0xb2, 0x1b, 0x71, 0x4e,
	0xef, 0xb7, 0x51, 0x02, 0x97, 0x9c, 0x96, 0x4e, 0x97, 0x60, 0x65, 0x80, 0x20, 0x77, 0x4e, 0x36,
	0x7e, 0x67, 0x12, 0xf2, 0x03, 0x2f, 0x9a, 0xdc, 0x47, 0xd3, 0x3b, 0x70, 0xf3, 0x43, 0x5e, 0x52,
	0x6a, 0x6c, 0x45, 0xe5, 0xf7, 0xd9, 0x32, 0xa7, 0x4a, 0x9c, 0x2c, 0xd6, 0xa4, 0x22, 0xa7, 0xca,
	0x0a, 0xab, 0xd4, 0x64, 0xb5, 0x26, 0xec, 0x09, 0xe2, 0x33, 0x01, 0x7d, 0x09, 0xaf, 0xc3, 0x8d,
	0xe1, 0xa8, 0x82, 0x28, 0xed, 0xb3, 0x15, 0xc4, 0x8c, 0x66, 0x5a, 0xe2, 0x2a, 0x9c, 0xc2, 0x95,
	0x50, 0x0a, 0x3f, 0x81, 0x87, 0x23, 0xe4, 0x57, 0x2b, 0x22, 0x5b, 0xe2, 0x4a, 0x6a, 0x95, 0x13,
	0x4a, 0xbc, 0x50, 0x56, 0x15, 0x4e, 0x56, 0x50, 0x7a, 0xb4, 0x0c, 0x07, 0x8f, 0x17, 0xca, 0x68,
	0x02, 0xbf, 0x0b, 0x8f, 0x87, 0xa3, 0x4a, 0x5c, 0x8c, 0x98, 0x49, 0xfc, 0x10, 0xee, 0x0d, 0xa7,
	0xe5, 0x65, 0xb9, 0xc6, 0x05, 0x54, 0x3b, 0xfc, 0x47, 0x68, 0x0a, 0xbf, 0x0d, 0x77, 0x87, 0x13,
	0x89, 0x12, 0x5f, 0xe6, 0x05, 0xb6, 0xa2, 0x8a, 0x35, 0xa5, 0x5a, 0x53, 0x50, 0x06, 0xdf, 0x85,
	0x3b, 0xa3, 0xcd, 0x51, 0xab, 0x6c, 0x4d, 0xe6, 0x4a, 0x68, 0x1a, 0x3f, 0x82, 0xfb, 0xc3, 0xd1,
	0xd9, 0xa2, 0xc2, 0x8b, 0x82, 0xca, 0x0b, 0x6a, 0x55, 0x12, 0xcb, 0x12, 0x27, 0xcb, 0x68, 0x06,
	0x3f, 0x86, 0x07, 0x09, 0xf5, 0x0a, 0xd3, 0xc1, 0x68, 0x69, 0xbe, 0xf9, 0x55, 0x49, 0x2c, 0xd5,
	0xa8, 0x64, 0x94, 0xc5, 0xf7, 0xe1, 0xad, 0x04, 0x26, 0x15, 0xc5, 0xfd, 0xaa, 0x1b, 0x0c, 0xb3,
	0x1b, 0x9f, 0xa4, 0x06, 0x5f, 0xf5, 0x06, 0x67, 0xdb, 0x37, 0xe1, 0x76, 0x0c, 0xcb, 0x22, 0xab,
	0x70, 0x65, 0x51, 0x7a, 0x1e, 0x8a, 0xd7, 0x78, 0x97, 0x06, 0xc8, 0x3b, 0xac, 0xa3, 0x4d, 0x8d,
	0x57, 0x10, 0x33, 0x44, 0xdd, 0x1e, 0xf4, 0x5d, 0xb6, 0xea, 0xc4, 0x55, 0x6a, 0x48, 0x08, 0x06,
	0x14, 0x25, 0xea, 0xc1, 0x34, 0xde, 0x84, 0x8d, 0x51, 0xa8, 0xac, 0xb2, 0x2f, 0xca, 0xd5, 0x5d,
	0x4e, 0xe2, 0xd0, 0xc4, 0xc6, 0x67, 0x8b, 0x70, 0x6d, 0xf0, 0xf5, 0xf0, 0xd9, 0x75, 0xc3, 0x3d,
	0x78, 0x33, 0xce, 0xbf, 0xb5, 0xed, 0xc1, 0x0e, 0x59, 0x4f, 0x42, 0x40, 0xfd, 0xf1, 0x83, 0x0c,
	0x7e, 0x0c, 0x6f, 0x27, 0x41, 0xa7, 0x4e, 0x51, 0xb8, 0xfd, 0x6a, 0x85, 0x55, 0x38, 0xf4, 0x69,
	0x06, 0x3f, 0x84, 0xcd, 0xc4, 0x74, 0x8e, 0x33, 0x39, 0xf4, 0xc3, 0x0c, 0x7e, 0x10, 0xbf, 0x64,
	0xc2, 0x44, 0xbb, 0x2c, 0x2f, 0xc9, 0xca, 0xf3, 0x0a, 0x87, 0x7e, 0x94, 0xc1, 0x6f, 0xc7, 0x07,
	0x58, 0x98, 0x86, 0x7b, 0xce, 0x6d, 0x4b, 0xe2, 0x33, 0x19, 0xfd, 0x38, 0x93, 0xd4, 0x05, 0xdc,
	0x73, 0x4e, 0x46, 0x7f, 0x9b, 0xc1, 0x9b, 0xf1, 0xab, 0x32, 0x8c, 0xbe, 0x2f, 0xd6, 0x94, 0x5d,
	0xf4, 0x77, 0x19, 0x7c, 0x2f, 0x76, 0x9a, 0xa3, 0xf8, 0xec, 0x1e, 0x57, 0xab, 0xa2, 0xbf, 0xcf,
	0xe0, 0x47, 0xf1, 0xe9, 0x25, 0xe2, 0xab, 0x5a, 0x85, 0xe6, 0x8a, 0x1d, 0x5e, 0x41, 0xff, 0x90,
	0xc1, 0x5b, 0xf1, 0xeb, 0x31, 0x32, 0xf3, 0xd5, 0x2a, 0x27, 0xf9, 0x64, 0xff, 0x98, 0x98, 0xac,
	0x22, 0x3e, 0x3b, 0x23, 0xfb, 0xa7, 0xc4, 0x4e, 0x90, 0x77, 0x45, 0x4e, 0x46, 0xff, 0x9c, 0x1c,
	0x5f, 0x2c, 0xee, 0xc9, 0xe8, 0x5f, 0x32, 0xf8, 0xad, 0xd8, 0x45, 0xdd, 0x33, 0xf5, 0x0a, 0xfa,
	0xd7, 0xe4, 0x93, 0xce, 0x4a, 0x12, 0x2f, 0x94, 0x65, 0xf4, 0x3a, 0x83, 0xdf, 0x81, 0x47, 0x89,
	0x03, 0x92, 0x2d, 0x16, 0x39, 0x59, 0x16, 0x25, 0x9e, 0x93, 0xd1, 0xbf, 0x25, 0x26, 0x15, 0xb8,
	0xe2, 0x5e, 0x84, 0xf4, 0xdf, 0x33, 0xf8, 0x7e, 0xb2, 0xe5, 0xb9, 0xcb, 0x0a, 0xa5, 0x6d, 0xb6,
	0x8c, 0xfe, 0x23, 0x83, 0x9f, 0xc4, 0xa7, 0xe7, 0x5e, 0x8a, 0x5d, 0xae, 0x52, 0x52, 0x79, 0x85,
	0xdb, 0x97, 0xd1, 0x7f, 0x26, 0xf6, 0xf8, 0x33, 0xea, 0x90, 0xff, 0x4a, 0x6c, 0xd5, 0xb6, 0x58,
	0x7a, 0x1e, 0xb1, 0xea, 0xbf, 0x13, 0xc7, 0x90, 0xcc, 0x2b, 0x0a, 0xdd, 0x10, 0x44, 0x99, 0x43,
	0x3f, 0x49, 0xac, 0x61, 0xd1, 0x59, 0xdf, 0xe8, 0xb3, 0xc4, 0x39, 0x64, 0x9b, 0x2d, 0xee, 0x95,
	0x25, 0xb1, 0x26, 0x94, 0xd0, 0xff, 0x64, 0xf0, 0xfb, 0xf0, 0x4e, 0x12, 0x22, 0x49, 0x14, 0xf7,
	0x55, 0x4e, 0x50, 0x24, 0x56, 0x28, 0x72, 0x2a, 0xb7, 0xb3, 0xc3, 0x15, 0x15, 0x19, 0x7d, 0x6b,
	0x3a, 0x69, 0x0e, 0x52, 0x44, 0xa1, 0xac, 0xee, 0x89, 0x42, 0x19, 0x7d, 0x7b, 0x3a, 0xa9, 0x61,
	0xa2, 0xb2, 0xcb, 0x49, 0xe8, 0x3b, 0x89, 0x65, 0xf8, 0x5e, 0x7f, 0x8e, 0x7e, 0x6e, 0x3a, 0x69,
	0x24, 0x3d, 0x63, 0x79, 0x55, 0x61, 0x45, 0xf4, 0xf3, 0x89, 0x29, 0xaa, 0xbc, 0xba, 0xc3, 0x09,
	0x65, 0xf4, 0x0b, 0x89, 0x29, 0x14, 0xb1, 0xa6, 0xca, 0xbb, 0x3c, 0xfa, 0xee, 0x74, 0xd2, 0x85,
	0x28, 0xef, 0x7a, 0x24, 0x9f, 0x24, 0x16, 0xf2, 0x55, 0x9e, 0x53, 0x3f, 0xde, 0xe5, 0xd1, 0x2f,
	0x26, 0x57, 0xab, 0xc6, 0x53, 0x19, 0xbf, 0x34, 0x9d, 0x34, 0x72, 0xe4, 0x5d, 0x56, 0x28, 0xab,
	0xf2, 0x2e, 0x27, 0xa0, 0x3f, 0xca, 0x26, 0x9d, 0xc5, 0x6d, 0x51, 0xfd, 0x98, 0x47, 0x7f, 0x9c,
	0x4d, 0x6a, 0xfb, 0x47, 0x3c, 0xeb, 0x8a, 0xf8, 0x93, 0x31, 0x48, 0x9c, 0xe8, 0xda, 0xae, 0xa1,
	0x3f, 0xcd, 0x26, 0x4d, 0x8c, 0x4a, 0x8d, 0x47, 0x7f, 0x96, 0x4d, 0xba, 0xf7, 0xd0, 0xf9, 0xa8,
	0xa0, 0xbf, 0x18, 0x8f, 0x40, 0x42, 0x7f, 0x99, 0x98, 0x80, 0x93, 0xd4, 0x52, 0x4d, 0x44, 0x7f,
	0x95, 0xdc, 0xad, 0xbc, 0xe3, 0xd6, 0xbf, 0x4e, 0x8c, 0x5f, 0xe1, 0xca, 0x6a, 0x05, 0xfd, 0xcd,
	0x58, 0xf8, 0x12, 0xfa, 0x41, 0x62, 0x03, 0x76, 0x44, 0x51, 0x51, 0x2b, 0xe8, 0xd3, 0xf1, 0x08,
	0x24, 0xf4, 0xc3, 0xc4, 0x04, 0x4e, 0x0a, 0x57, 0x2b, 0xe8, 0x47, 0xe3, 0x11, 0x48, 0xe8, 0xc7,
	0xd9, 0x31, 0x96, 0xdd, 0x33, 0xf5, 0x43, 0xb6, 0x8a, 0xbe, 0x27, 0x24, 0xcd, 0x39, 0x34, 0x47,
	0x51, 0x9a, 0x5f, 0x15, 0x36, 0x5e, 0x4f, 0x0e, 0x6e, 0x99, 0xa6, 0xcf, 0x46, 0xb7, 0x61, 0x2d,
	0x86, 0xa1, 0xf2, 0xbc, 0xca, 0x25, 0x3a, 0x32, 0x52, 0x44, 0x79, 0xcf, 0x39, 0x07, 0x8a, 0x02,
	0x62, 0xf0, 0x0d, 0x58, 0x1d, 0x8e, 0xc9, 0x0b, 0x28, 0x85, 0x37, 0xe0, 0xd6, 0x30, 0xac, 0xd0,
	0x76, 0x90, 0xc6, 0xb7, 0xa0, 0x30, 0x0c, 0x77, 0x47, 0xac, 0x54, 0xc4, 0x67, 0x68, 0x62, 0xa4,
	0x8e, 0xbe, 0x23, 0x27, 0x87, 0x1c, 0x9f, 0x28, 0x66, 0x89, 0xdb, 0x61, 0x6b, 0x15, 0x25, 0xa4,
	0x85, 0x5a, 0x15, 0xca, 0x68, 0x6a, 0xc8, 0x39, 0x81, 0xd2, 0xb1, 0x02, 0xbf, 0xcf, 0xd2, 0x33,
	0x53, 0x66, 0xc8, 0x21, 0x24, 0x70, 0x43, 0xa4, 0x18, 0x1c, 0x76, 0x12, 0xa4, 0x14, 0xbc, 0xb0,
	0xe3, 0x9c, 0xca, 0xe9, 0x61, 0xb0, 0xc8, 0x4a, 0x25, 0x6a, 0xca, 0xcc, 0x90, 0x83, 0x14, 0xa5,
	0x2a, 0x56, 0xcf, 0xe6, 0x06, 0x86, 0x1c, 0xa4, 0x5c, 0xa5, 0xf8, 0x52, 0x68, 0x2a, 0xb3, 0xa3,
	0xa2, 0x83, 0xf2, 0xe6, 0x05, 0x34, 0x3b, 0x8a, 0xef, 0x36, 0x2b, 0x87, 0xf8, 0xce, 0xe1, 0x07,
	0xb1, 0x99, 0x3d, 0x84, 0x1e, 0xd1, 0x65, 0x7e, 0x94, 0x3f, 0x29, 0x4d, 0xd8, 0xd8, 0x85, 0x8d,
	0xd7, 0x29, 0x58, 0x1e, 0x78, 0xf2, 0x22, 0xaf, 0x86, 0x84, 0x94, 0xcc, 0x7d, 0x14, 0x0a, 0xfb,
	0x35, 0xb8, 0x36, 0x04, 0x6f, 0x9f, 0xad, 0x70, 0x88, 0xc1, 0x37, 0xe1, 0xfa, 0x10, 0xa4, 0x1d,
	0x8e, 0xa2, 0xa5, 0x46, 0xa0, 0x15, 0xc5, 0xfd, 0x7d, 0x51, 0x40, 0xe9, 0x21, 0x4e, 0xa7, 0x68,
	0x55, 0x75, 0x47, 0x12, 0x05, 0x05, 0x4d, 0x8c, 0xb0, 0xc1, 0xf1, 0x05, 0x5f, 0xe2, 0xd0, 0x24,
	0xde, 0x8a, 0x3f, 0xfd, 0x79, 0x36, 0xb8, 0x2c, 0x3d, 0x4d, 0x5d, 0xb2, 0x29, 0xfc, 0x34, 0xbe,
	0xb4, 0x74, 0xd9, 0x07, 0xc8, 0x3e, 0xa1, 0xab, 0x58, 0x66, 0xe3, 0x27, 0x0c, 0xac, 0x0d, 0x3c,
	0xfc, 0xf7, 0xb4, 0xe3, 0xc5, 0xdf, 0xc8, 0x50, 0x67, 0x94, 0x78, 0xe5, 0xb9, 0xba, 0xc3, 0x57,
	0x14, 0x4e, 0x0a, 0xcd, 0x48, 0xbc, 0x35, 0x7d, 0x44, 0x01, 0x00, 0x31, 0xf8, 0x1d, 0xd8, 0x4a,
	0x4a, 0x26, 0x88, 0x4a, 0x88, 0x34, 0x35, 0xe4, 0x74, 0xde, 0x47, 0xca, 0x56, 0x2a, 0x28, 0xfd,
	0xe0, 0xf7, 0x96, 0x60, 0x71, 0x90, 0xfd, 0xf8, 0x97, 0x19, 0x58, 0x8e, 0xe9, 0x3b, 0xc5, 0xb1,
	0x4f, 0xd7, 0xc3, 0x5b, 0x63, 0xf3, 0x4f, 0xc6, 0xa6, 0xf3, 0x6e, 0x8d, 0xbf, 0xc7, 0xc0, 0x4a,
	0x6c, 0xb7, 0x29, 0x8e, 0x7d, 0x18, 0x1e, 0xd5, 0x14, 0x9b, 0x7f, 0xe7, 0x1c, 0x94, 0x9e, 0x4a,
	0xbf, 0xc6, 0x40, 0x3e, 0xbe, 0x07, 0x12, 0xc7, 0x72, 0x1e, 0xd9, 0x0e, 0x9b, 0x7f, 0xf7, 0x3c,
	0xa4, 0x9e, 0x56, 0xbf, 0xc2, 0x40, 0x2e, 0xae, 0x89, 0x0e, 0xc7, 0xba, 0x7f, 0x44, 0x9f, 0x5f,
	0xfe, 0xe9, 0xf8, 0x84, 0x21, 0x2f, 0xc5, 0xb7, 0x6e, 0xc5, 0x7b, 0x69, 0x64, 0x8f, 0x58, 0xbc,
	0x97, 0x46, 0x77, 0x8a, 0xe1, 0xdf, 0x64, 0xe0, 0xea, 0xf0, 0xf6, 0x29, 0xfc, 0x5e, 0xfc, 0x24,
	0x24, 0xe8, 0xd7, 0xca, 0xbf, 0x7f, 0x5e, 0x72, 0x4f, 0xc3, 0xdf, 0x60, 0xe0, 0xca, 0xd0, 0x56,
	0x26, 0xfc, 0xe5, 0x38, 0x09, 0x49, 0x9a, 0xbd, 0xf2, 0xef, 0x9d, 0x93, 0x3a, 0xa4, 0xde, 0xd0,
	0xce, 0xd4, 0x78, 0xf5, 0x92, 0x74, 0xdf, 0xc6, 0xab, 0x97, 0xac, 0x1d, 0xb6, 0x03, 0x0b, 0x3d,
	0x5d, 0x17, 0x78, 0x33, 0x8e, 0xe3, 0xe0, 0xf6, 0x8f, 0xfc, 0xbd, 0xc4, 0xf8, 0x9e, 0x4c, 0x13,
	0xe6, 0xa3, 0x8f, 0xda, 0xf8, 0xee, 0x10, 0x23, 0xfa, 0x5f, 0x8e, 0xf3, 0x9b, 0x49, 0xd1, 0x3d,
	0x81, 0xaf, 0xe0, 0x42, 0xdf, 0xfb, 0x34, 0xbe, 0x3f, 0x7c, 0x5e, 0x07, 0x88, 0x7d, 0x7b, 0x0c,
	0x8a, 0x33, 0x53, 0xa3, 0x8f, 0xd5, 0xf1, 0xa6, 0x0e, 0x7c, 0x24, 0x8f, 0x37, 0x75, 0xf0, 0x1b,
	0xb8, 0x63, 0x6a, 0xdf, 0x33, 0x66, 0xbc, 0xa9, 0x71, 0xaf, 0xa1, 0xf1, 0xa6, 0xc6, 0xbf, 0x91,
	0x0e, 0x09, 0x74, 0xb7, 0x35, 0x74, 0xec, 0x40, 0x8f, 0xf4, 0xa2, 0x8e, 0x1d, 0xe8, 0x3d, 0xfd,
	0xa8, 0x3f, 0x4b, 0x5f, 0xb2, 0x7b, 0x9b, 0x94, 0xf1, 0x83, 0x21, 0x5c, 0x63, 0x7a, 0xa7, 0xf3,
	0x0f, 0xc7, 0xa2, 0xf1, 0xe4, 0xff, 0x36, 0x03, 0xd7, 0x46, 0xf4, 0x78, 0xe1, 0xf7, 0xc7, 0x34,
	0xb1, 0xa7, 0x8f, 0x2c, 0xff, 0x95, 0x73, 0xd3, 0x7b, 0x0f, 0xe4, 0x5f, 0xc2, 0x9f, 0x30, 0xb4,
	0xe3, 0x64, 0x40, 0x2f, 0x10, 0xde, 0x1a, 0xb7, 0x77, 0xc8, 0x55, 0xea, 0xf1, 0xf9, 0x5a, 0x8e,
	0xb0, 0x0e, 0xb3, 0xe1, 0x60, 0xc3, 0x6f, 0x26, 0x09, 0x49, 0x5f, 0xe8, 0x5b, 0xc9, 0x90, 0xcf,
	0x92, 0x60, 0x4f, 0xcb, 0x40, 0x7c, 0x12, 0x1c, 0xdc, 0xaa, 0x10, 0x9f, 0x04, 0x63, 0x7a, 0x11,
	0xf0, 0xb7, 0x19, 0xda, 0x43, 0xd1, 0xf7, 0xc8, 0x8e, 0x1f, 0x8e, 0xf7, 0xb2, 0xef, 0x8a, 0x7f,
	0x74, 0x9e, 0x76, 0x00, 0x27, 0x59, 0xf4, 0xbd, 0x4b, 0xc7, 0x27, 0x8b, 0xb8, 0xb7, 0xf2, 0xf8,
	0x64, 0x11, 0xfb, 0xe8, 0xbd, 0xfd, 0xee, 0xc7, 0x4f, 0x8f, 0xcc, 0xa6, 0x66, 0x1c, 0x6d, 0x6e,
	0x3d, 0xb0, 0xed, 0xcd, 0xba, 0xd9, 0xba, 0x47, 0xff, 0x21, 0x5e, 0x37, 0x9b, 0xf7, 0x2c, 0xd2,
	0x79, 0xa9, 0xd7, 0x89, 0x15, 0xf3, 0x57, 0xf2, 0x83, 0x29, 0x8a, 0xf9, 0xf0, 0x7f, 0x03, 0x00,
	0x00, 0xff, 0xff, 0x8c, 0x08, 0xdf, 0x43, 0x91, 0x3e, 0x00, 0x00,
}
