// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/adventure-activity/adventure-activity.proto

package adventure_activity // import "golang.52tt.com/protocol/services/adventure-activity"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type AwardType int32

const (
	AwardType_AWARD_TYPE_UNSPECIFIED AwardType = 0
	AwardType_AWARD_TYPE_PACKAGE     AwardType = 1
	AwardType_AWARD_TYPE_DRESS       AwardType = 2
)

var AwardType_name = map[int32]string{
	0: "AWARD_TYPE_UNSPECIFIED",
	1: "AWARD_TYPE_PACKAGE",
	2: "AWARD_TYPE_DRESS",
}
var AwardType_value = map[string]int32{
	"AWARD_TYPE_UNSPECIFIED": 0,
	"AWARD_TYPE_PACKAGE":     1,
	"AWARD_TYPE_DRESS":       2,
}

func (x AwardType) String() string {
	return proto.EnumName(AwardType_name, int32(x))
}
func (AwardType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_06afd5ae92173300, []int{0}
}

type DrawResultType int32

const (
	DrawResultType_DRAW_RESULT_UNSPECIFIED     DrawResultType = 0
	DrawResultType_DRAW_RESULT_CARD_LIGHTED    DrawResultType = 1
	DrawResultType_DRAW_RESULT_LEVEL_COMPLETED DrawResultType = 2
	DrawResultType_DRAW_RESULT_PEAK_REACHED    DrawResultType = 3
	DrawResultType_DRAW_RESULT_TOP_N_BOUNS     DrawResultType = 4
)

var DrawResultType_name = map[int32]string{
	0: "DRAW_RESULT_UNSPECIFIED",
	1: "DRAW_RESULT_CARD_LIGHTED",
	2: "DRAW_RESULT_LEVEL_COMPLETED",
	3: "DRAW_RESULT_PEAK_REACHED",
	4: "DRAW_RESULT_TOP_N_BOUNS",
}
var DrawResultType_value = map[string]int32{
	"DRAW_RESULT_UNSPECIFIED":     0,
	"DRAW_RESULT_CARD_LIGHTED":    1,
	"DRAW_RESULT_LEVEL_COMPLETED": 2,
	"DRAW_RESULT_PEAK_REACHED":    3,
	"DRAW_RESULT_TOP_N_BOUNS":     4,
}

func (x DrawResultType) String() string {
	return proto.EnumName(DrawResultType_name, int32(x))
}
func (DrawResultType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_06afd5ae92173300, []int{1}
}

type GetUserAdventureRecordRequest_RecordType int32

const (
	GetUserAdventureRecordRequest_RECORD_TYPE_UNSPECIFIED  GetUserAdventureRecordRequest_RecordType = 0
	GetUserAdventureRecordRequest_RECORD_TYPE_CARD_LIGHTED GetUserAdventureRecordRequest_RecordType = 2
	GetUserAdventureRecordRequest_RECORD_TYPE_COMPLETED    GetUserAdventureRecordRequest_RecordType = 3
)

var GetUserAdventureRecordRequest_RecordType_name = map[int32]string{
	0: "RECORD_TYPE_UNSPECIFIED",
	2: "RECORD_TYPE_CARD_LIGHTED",
	3: "RECORD_TYPE_COMPLETED",
}
var GetUserAdventureRecordRequest_RecordType_value = map[string]int32{
	"RECORD_TYPE_UNSPECIFIED":  0,
	"RECORD_TYPE_CARD_LIGHTED": 2,
	"RECORD_TYPE_COMPLETED":    3,
}

func (x GetUserAdventureRecordRequest_RecordType) String() string {
	return proto.EnumName(GetUserAdventureRecordRequest_RecordType_name, int32(x))
}
func (GetUserAdventureRecordRequest_RecordType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_06afd5ae92173300, []int{27, 0}
}

// 奖励信息
type AwardInfo struct {
	AwardId              string   `protobuf:"bytes,1,opt,name=award_id,json=awardId,proto3" json:"award_id,omitempty"`
	AwardType            uint32   `protobuf:"varint,2,opt,name=award_type,json=awardType,proto3" json:"award_type,omitempty"`
	DressSubType         uint32   `protobuf:"varint,3,opt,name=dress_sub_type,json=dressSubType,proto3" json:"dress_sub_type,omitempty"`
	Amount               uint32   `protobuf:"varint,4,opt,name=amount,proto3" json:"amount,omitempty"`
	AwardWorth           uint32   `protobuf:"varint,5,opt,name=award_worth,json=awardWorth,proto3" json:"award_worth,omitempty"`
	AwardName            string   `protobuf:"bytes,6,opt,name=award_name,json=awardName,proto3" json:"award_name,omitempty"`
	AwardDesc            string   `protobuf:"bytes,7,opt,name=award_desc,json=awardDesc,proto3" json:"award_desc,omitempty"`
	AwardIcon            string   `protobuf:"bytes,8,opt,name=award_icon,json=awardIcon,proto3" json:"award_icon,omitempty"`
	ExpTime              int64    `protobuf:"varint,9,opt,name=exp_time,json=expTime,proto3" json:"exp_time,omitempty"`
	Weight               uint32   `protobuf:"varint,10,opt,name=weight,proto3" json:"weight,omitempty"`
	CardId               uint32   `protobuf:"varint,11,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AwardInfo) Reset()         { *m = AwardInfo{} }
func (m *AwardInfo) String() string { return proto.CompactTextString(m) }
func (*AwardInfo) ProtoMessage()    {}
func (*AwardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_06afd5ae92173300, []int{0}
}
func (m *AwardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardInfo.Unmarshal(m, b)
}
func (m *AwardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardInfo.Marshal(b, m, deterministic)
}
func (dst *AwardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardInfo.Merge(dst, src)
}
func (m *AwardInfo) XXX_Size() int {
	return xxx_messageInfo_AwardInfo.Size(m)
}
func (m *AwardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AwardInfo proto.InternalMessageInfo

func (m *AwardInfo) GetAwardId() string {
	if m != nil {
		return m.AwardId
	}
	return ""
}

func (m *AwardInfo) GetAwardType() uint32 {
	if m != nil {
		return m.AwardType
	}
	return 0
}

func (m *AwardInfo) GetDressSubType() uint32 {
	if m != nil {
		return m.DressSubType
	}
	return 0
}

func (m *AwardInfo) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *AwardInfo) GetAwardWorth() uint32 {
	if m != nil {
		return m.AwardWorth
	}
	return 0
}

func (m *AwardInfo) GetAwardName() string {
	if m != nil {
		return m.AwardName
	}
	return ""
}

func (m *AwardInfo) GetAwardDesc() string {
	if m != nil {
		return m.AwardDesc
	}
	return ""
}

func (m *AwardInfo) GetAwardIcon() string {
	if m != nil {
		return m.AwardIcon
	}
	return ""
}

func (m *AwardInfo) GetExpTime() int64 {
	if m != nil {
		return m.ExpTime
	}
	return 0
}

func (m *AwardInfo) GetWeight() uint32 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *AwardInfo) GetCardId() uint32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

// 关卡信息
type LevelCfg struct {
	LevelId              uint32     `protobuf:"varint,1,opt,name=level_id,json=levelId,proto3" json:"level_id,omitempty"`
	LevelName            string     `protobuf:"bytes,2,opt,name=level_name,json=levelName,proto3" json:"level_name,omitempty"`
	MaxNFixed            uint32     `protobuf:"varint,3,opt,name=max_n_fixed,json=maxNFixed,proto3" json:"max_n_fixed,omitempty"`
	LevelAward           *AwardInfo `protobuf:"bytes,5,opt,name=level_award,json=levelAward,proto3" json:"level_award,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *LevelCfg) Reset()         { *m = LevelCfg{} }
func (m *LevelCfg) String() string { return proto.CompactTextString(m) }
func (*LevelCfg) ProtoMessage()    {}
func (*LevelCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_06afd5ae92173300, []int{1}
}
func (m *LevelCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelCfg.Unmarshal(m, b)
}
func (m *LevelCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelCfg.Marshal(b, m, deterministic)
}
func (dst *LevelCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelCfg.Merge(dst, src)
}
func (m *LevelCfg) XXX_Size() int {
	return xxx_messageInfo_LevelCfg.Size(m)
}
func (m *LevelCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelCfg.DiscardUnknown(m)
}

var xxx_messageInfo_LevelCfg proto.InternalMessageInfo

func (m *LevelCfg) GetLevelId() uint32 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

func (m *LevelCfg) GetLevelName() string {
	if m != nil {
		return m.LevelName
	}
	return ""
}

func (m *LevelCfg) GetMaxNFixed() uint32 {
	if m != nil {
		return m.MaxNFixed
	}
	return 0
}

func (m *LevelCfg) GetLevelAward() *AwardInfo {
	if m != nil {
		return m.LevelAward
	}
	return nil
}

type LevelConfWithPool struct {
	LevelCfg             *LevelCfg    `protobuf:"bytes,1,opt,name=level_cfg,json=levelCfg,proto3" json:"level_cfg,omitempty"`
	AwardList            []*AwardInfo `protobuf:"bytes,2,rep,name=award_list,json=awardList,proto3" json:"award_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *LevelConfWithPool) Reset()         { *m = LevelConfWithPool{} }
func (m *LevelConfWithPool) String() string { return proto.CompactTextString(m) }
func (*LevelConfWithPool) ProtoMessage()    {}
func (*LevelConfWithPool) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_06afd5ae92173300, []int{2}
}
func (m *LevelConfWithPool) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelConfWithPool.Unmarshal(m, b)
}
func (m *LevelConfWithPool) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelConfWithPool.Marshal(b, m, deterministic)
}
func (dst *LevelConfWithPool) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelConfWithPool.Merge(dst, src)
}
func (m *LevelConfWithPool) XXX_Size() int {
	return xxx_messageInfo_LevelConfWithPool.Size(m)
}
func (m *LevelConfWithPool) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelConfWithPool.DiscardUnknown(m)
}

var xxx_messageInfo_LevelConfWithPool proto.InternalMessageInfo

func (m *LevelConfWithPool) GetLevelCfg() *LevelCfg {
	if m != nil {
		return m.LevelCfg
	}
	return nil
}

func (m *LevelConfWithPool) GetAwardList() []*AwardInfo {
	if m != nil {
		return m.AwardList
	}
	return nil
}

// 奖池信息
type PrizePool struct {
	LevelId              uint32       `protobuf:"varint,1,opt,name=level_id,json=levelId,proto3" json:"level_id,omitempty"`
	AwardList            []*AwardInfo `protobuf:"bytes,2,rep,name=award_list,json=awardList,proto3" json:"award_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PrizePool) Reset()         { *m = PrizePool{} }
func (m *PrizePool) String() string { return proto.CompactTextString(m) }
func (*PrizePool) ProtoMessage()    {}
func (*PrizePool) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_06afd5ae92173300, []int{3}
}
func (m *PrizePool) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PrizePool.Unmarshal(m, b)
}
func (m *PrizePool) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PrizePool.Marshal(b, m, deterministic)
}
func (dst *PrizePool) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PrizePool.Merge(dst, src)
}
func (m *PrizePool) XXX_Size() int {
	return xxx_messageInfo_PrizePool.Size(m)
}
func (m *PrizePool) XXX_DiscardUnknown() {
	xxx_messageInfo_PrizePool.DiscardUnknown(m)
}

var xxx_messageInfo_PrizePool proto.InternalMessageInfo

func (m *PrizePool) GetLevelId() uint32 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

func (m *PrizePool) GetAwardList() []*AwardInfo {
	if m != nil {
		return m.AwardList
	}
	return nil
}

type SetLevelConfRequest struct {
	LevelConfList        []*LevelCfg `protobuf:"bytes,1,rep,name=level_conf_list,json=levelConfList,proto3" json:"level_conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *SetLevelConfRequest) Reset()         { *m = SetLevelConfRequest{} }
func (m *SetLevelConfRequest) String() string { return proto.CompactTextString(m) }
func (*SetLevelConfRequest) ProtoMessage()    {}
func (*SetLevelConfRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_06afd5ae92173300, []int{4}
}
func (m *SetLevelConfRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetLevelConfRequest.Unmarshal(m, b)
}
func (m *SetLevelConfRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetLevelConfRequest.Marshal(b, m, deterministic)
}
func (dst *SetLevelConfRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetLevelConfRequest.Merge(dst, src)
}
func (m *SetLevelConfRequest) XXX_Size() int {
	return xxx_messageInfo_SetLevelConfRequest.Size(m)
}
func (m *SetLevelConfRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetLevelConfRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetLevelConfRequest proto.InternalMessageInfo

func (m *SetLevelConfRequest) GetLevelConfList() []*LevelCfg {
	if m != nil {
		return m.LevelConfList
	}
	return nil
}

type SetLevelConfResponse struct {
	Success              bool     `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetLevelConfResponse) Reset()         { *m = SetLevelConfResponse{} }
func (m *SetLevelConfResponse) String() string { return proto.CompactTextString(m) }
func (*SetLevelConfResponse) ProtoMessage()    {}
func (*SetLevelConfResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_06afd5ae92173300, []int{5}
}
func (m *SetLevelConfResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetLevelConfResponse.Unmarshal(m, b)
}
func (m *SetLevelConfResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetLevelConfResponse.Marshal(b, m, deterministic)
}
func (dst *SetLevelConfResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetLevelConfResponse.Merge(dst, src)
}
func (m *SetLevelConfResponse) XXX_Size() int {
	return xxx_messageInfo_SetLevelConfResponse.Size(m)
}
func (m *SetLevelConfResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetLevelConfResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetLevelConfResponse proto.InternalMessageInfo

func (m *SetLevelConfResponse) GetSuccess() bool {
	if m != nil {
		return m.Success
	}
	return false
}

// 设置关卡奖池(支持一个或多个关卡同时设置)
type SetLevelPrizePoolRequest struct {
	PrizePoolList        []*PrizePool `protobuf:"bytes,1,rep,name=prize_pool_list,json=prizePoolList,proto3" json:"prize_pool_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetLevelPrizePoolRequest) Reset()         { *m = SetLevelPrizePoolRequest{} }
func (m *SetLevelPrizePoolRequest) String() string { return proto.CompactTextString(m) }
func (*SetLevelPrizePoolRequest) ProtoMessage()    {}
func (*SetLevelPrizePoolRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_06afd5ae92173300, []int{6}
}
func (m *SetLevelPrizePoolRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetLevelPrizePoolRequest.Unmarshal(m, b)
}
func (m *SetLevelPrizePoolRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetLevelPrizePoolRequest.Marshal(b, m, deterministic)
}
func (dst *SetLevelPrizePoolRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetLevelPrizePoolRequest.Merge(dst, src)
}
func (m *SetLevelPrizePoolRequest) XXX_Size() int {
	return xxx_messageInfo_SetLevelPrizePoolRequest.Size(m)
}
func (m *SetLevelPrizePoolRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetLevelPrizePoolRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetLevelPrizePoolRequest proto.InternalMessageInfo

func (m *SetLevelPrizePoolRequest) GetPrizePoolList() []*PrizePool {
	if m != nil {
		return m.PrizePoolList
	}
	return nil
}

type SetLevelPrizePoolResponse struct {
	Success              bool     `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetLevelPrizePoolResponse) Reset()         { *m = SetLevelPrizePoolResponse{} }
func (m *SetLevelPrizePoolResponse) String() string { return proto.CompactTextString(m) }
func (*SetLevelPrizePoolResponse) ProtoMessage()    {}
func (*SetLevelPrizePoolResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_06afd5ae92173300, []int{7}
}
func (m *SetLevelPrizePoolResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetLevelPrizePoolResponse.Unmarshal(m, b)
}
func (m *SetLevelPrizePoolResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetLevelPrizePoolResponse.Marshal(b, m, deterministic)
}
func (dst *SetLevelPrizePoolResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetLevelPrizePoolResponse.Merge(dst, src)
}
func (m *SetLevelPrizePoolResponse) XXX_Size() int {
	return xxx_messageInfo_SetLevelPrizePoolResponse.Size(m)
}
func (m *SetLevelPrizePoolResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetLevelPrizePoolResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetLevelPrizePoolResponse proto.InternalMessageInfo

func (m *SetLevelPrizePoolResponse) GetSuccess() bool {
	if m != nil {
		return m.Success
	}
	return false
}

// 获取活动所有关卡奖池信息
type GetLevelConfWithPoolRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLevelConfWithPoolRequest) Reset()         { *m = GetLevelConfWithPoolRequest{} }
func (m *GetLevelConfWithPoolRequest) String() string { return proto.CompactTextString(m) }
func (*GetLevelConfWithPoolRequest) ProtoMessage()    {}
func (*GetLevelConfWithPoolRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_06afd5ae92173300, []int{8}
}
func (m *GetLevelConfWithPoolRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLevelConfWithPoolRequest.Unmarshal(m, b)
}
func (m *GetLevelConfWithPoolRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLevelConfWithPoolRequest.Marshal(b, m, deterministic)
}
func (dst *GetLevelConfWithPoolRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLevelConfWithPoolRequest.Merge(dst, src)
}
func (m *GetLevelConfWithPoolRequest) XXX_Size() int {
	return xxx_messageInfo_GetLevelConfWithPoolRequest.Size(m)
}
func (m *GetLevelConfWithPoolRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLevelConfWithPoolRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetLevelConfWithPoolRequest proto.InternalMessageInfo

type GetLevelConfWithPoolResponse struct {
	LevelList            []*LevelConfWithPool `protobuf:"bytes,1,rep,name=level_list,json=levelList,proto3" json:"level_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetLevelConfWithPoolResponse) Reset()         { *m = GetLevelConfWithPoolResponse{} }
func (m *GetLevelConfWithPoolResponse) String() string { return proto.CompactTextString(m) }
func (*GetLevelConfWithPoolResponse) ProtoMessage()    {}
func (*GetLevelConfWithPoolResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_06afd5ae92173300, []int{9}
}
func (m *GetLevelConfWithPoolResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLevelConfWithPoolResponse.Unmarshal(m, b)
}
func (m *GetLevelConfWithPoolResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLevelConfWithPoolResponse.Marshal(b, m, deterministic)
}
func (dst *GetLevelConfWithPoolResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLevelConfWithPoolResponse.Merge(dst, src)
}
func (m *GetLevelConfWithPoolResponse) XXX_Size() int {
	return xxx_messageInfo_GetLevelConfWithPoolResponse.Size(m)
}
func (m *GetLevelConfWithPoolResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLevelConfWithPoolResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetLevelConfWithPoolResponse proto.InternalMessageInfo

func (m *GetLevelConfWithPoolResponse) GetLevelList() []*LevelConfWithPool {
	if m != nil {
		return m.LevelList
	}
	return nil
}

// 设置冒险岛防违规配置
type SetAdventureIslandLimitConfReq struct {
	DailyBuyChanceLimit  uint32   `protobuf:"varint,1,opt,name=daily_buy_chance_limit,json=dailyBuyChanceLimit,proto3" json:"daily_buy_chance_limit,omitempty"`
	DailyUseChanceLimit  uint32   `protobuf:"varint,2,opt,name=daily_use_chance_limit,json=dailyUseChanceLimit,proto3" json:"daily_use_chance_limit,omitempty"`
	SingleBuyChanceLimit uint32   `protobuf:"varint,3,opt,name=single_buy_chance_limit,json=singleBuyChanceLimit,proto3" json:"single_buy_chance_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetAdventureIslandLimitConfReq) Reset()         { *m = SetAdventureIslandLimitConfReq{} }
func (m *SetAdventureIslandLimitConfReq) String() string { return proto.CompactTextString(m) }
func (*SetAdventureIslandLimitConfReq) ProtoMessage()    {}
func (*SetAdventureIslandLimitConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_06afd5ae92173300, []int{10}
}
func (m *SetAdventureIslandLimitConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetAdventureIslandLimitConfReq.Unmarshal(m, b)
}
func (m *SetAdventureIslandLimitConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetAdventureIslandLimitConfReq.Marshal(b, m, deterministic)
}
func (dst *SetAdventureIslandLimitConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetAdventureIslandLimitConfReq.Merge(dst, src)
}
func (m *SetAdventureIslandLimitConfReq) XXX_Size() int {
	return xxx_messageInfo_SetAdventureIslandLimitConfReq.Size(m)
}
func (m *SetAdventureIslandLimitConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetAdventureIslandLimitConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetAdventureIslandLimitConfReq proto.InternalMessageInfo

func (m *SetAdventureIslandLimitConfReq) GetDailyBuyChanceLimit() uint32 {
	if m != nil {
		return m.DailyBuyChanceLimit
	}
	return 0
}

func (m *SetAdventureIslandLimitConfReq) GetDailyUseChanceLimit() uint32 {
	if m != nil {
		return m.DailyUseChanceLimit
	}
	return 0
}

func (m *SetAdventureIslandLimitConfReq) GetSingleBuyChanceLimit() uint32 {
	if m != nil {
		return m.SingleBuyChanceLimit
	}
	return 0
}

type SetAdventureIslandLimitConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetAdventureIslandLimitConfResp) Reset()         { *m = SetAdventureIslandLimitConfResp{} }
func (m *SetAdventureIslandLimitConfResp) String() string { return proto.CompactTextString(m) }
func (*SetAdventureIslandLimitConfResp) ProtoMessage()    {}
func (*SetAdventureIslandLimitConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_06afd5ae92173300, []int{11}
}
func (m *SetAdventureIslandLimitConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetAdventureIslandLimitConfResp.Unmarshal(m, b)
}
func (m *SetAdventureIslandLimitConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetAdventureIslandLimitConfResp.Marshal(b, m, deterministic)
}
func (dst *SetAdventureIslandLimitConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetAdventureIslandLimitConfResp.Merge(dst, src)
}
func (m *SetAdventureIslandLimitConfResp) XXX_Size() int {
	return xxx_messageInfo_SetAdventureIslandLimitConfResp.Size(m)
}
func (m *SetAdventureIslandLimitConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetAdventureIslandLimitConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetAdventureIslandLimitConfResp proto.InternalMessageInfo

// 获取冒险岛防违规配置
type GetAdventureIslandLimitConfReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAdventureIslandLimitConfReq) Reset()         { *m = GetAdventureIslandLimitConfReq{} }
func (m *GetAdventureIslandLimitConfReq) String() string { return proto.CompactTextString(m) }
func (*GetAdventureIslandLimitConfReq) ProtoMessage()    {}
func (*GetAdventureIslandLimitConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_06afd5ae92173300, []int{12}
}
func (m *GetAdventureIslandLimitConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAdventureIslandLimitConfReq.Unmarshal(m, b)
}
func (m *GetAdventureIslandLimitConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAdventureIslandLimitConfReq.Marshal(b, m, deterministic)
}
func (dst *GetAdventureIslandLimitConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAdventureIslandLimitConfReq.Merge(dst, src)
}
func (m *GetAdventureIslandLimitConfReq) XXX_Size() int {
	return xxx_messageInfo_GetAdventureIslandLimitConfReq.Size(m)
}
func (m *GetAdventureIslandLimitConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAdventureIslandLimitConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAdventureIslandLimitConfReq proto.InternalMessageInfo

type GetAdventureIslandLimitConfResp struct {
	DailyBuyChanceLimit  uint32   `protobuf:"varint,1,opt,name=daily_buy_chance_limit,json=dailyBuyChanceLimit,proto3" json:"daily_buy_chance_limit,omitempty"`
	DailyUseChanceLimit  uint32   `protobuf:"varint,2,opt,name=daily_use_chance_limit,json=dailyUseChanceLimit,proto3" json:"daily_use_chance_limit,omitempty"`
	SingleBuyChanceLimit uint32   `protobuf:"varint,3,opt,name=single_buy_chance_limit,json=singleBuyChanceLimit,proto3" json:"single_buy_chance_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAdventureIslandLimitConfResp) Reset()         { *m = GetAdventureIslandLimitConfResp{} }
func (m *GetAdventureIslandLimitConfResp) String() string { return proto.CompactTextString(m) }
func (*GetAdventureIslandLimitConfResp) ProtoMessage()    {}
func (*GetAdventureIslandLimitConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_06afd5ae92173300, []int{13}
}
func (m *GetAdventureIslandLimitConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAdventureIslandLimitConfResp.Unmarshal(m, b)
}
func (m *GetAdventureIslandLimitConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAdventureIslandLimitConfResp.Marshal(b, m, deterministic)
}
func (dst *GetAdventureIslandLimitConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAdventureIslandLimitConfResp.Merge(dst, src)
}
func (m *GetAdventureIslandLimitConfResp) XXX_Size() int {
	return xxx_messageInfo_GetAdventureIslandLimitConfResp.Size(m)
}
func (m *GetAdventureIslandLimitConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAdventureIslandLimitConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAdventureIslandLimitConfResp proto.InternalMessageInfo

func (m *GetAdventureIslandLimitConfResp) GetDailyBuyChanceLimit() uint32 {
	if m != nil {
		return m.DailyBuyChanceLimit
	}
	return 0
}

func (m *GetAdventureIslandLimitConfResp) GetDailyUseChanceLimit() uint32 {
	if m != nil {
		return m.DailyUseChanceLimit
	}
	return 0
}

func (m *GetAdventureIslandLimitConfResp) GetSingleBuyChanceLimit() uint32 {
	if m != nil {
		return m.SingleBuyChanceLimit
	}
	return 0
}

// TOPN 加码奖励信息
type TopNAwardCfg struct {
	TopNLimit            uint32       `protobuf:"varint,1,opt,name=top_n_limit,json=topNLimit,proto3" json:"top_n_limit,omitempty"`
	AwardList            []*AwardInfo `protobuf:"bytes,2,rep,name=award_list,json=awardList,proto3" json:"award_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *TopNAwardCfg) Reset()         { *m = TopNAwardCfg{} }
func (m *TopNAwardCfg) String() string { return proto.CompactTextString(m) }
func (*TopNAwardCfg) ProtoMessage()    {}
func (*TopNAwardCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_06afd5ae92173300, []int{14}
}
func (m *TopNAwardCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopNAwardCfg.Unmarshal(m, b)
}
func (m *TopNAwardCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopNAwardCfg.Marshal(b, m, deterministic)
}
func (dst *TopNAwardCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopNAwardCfg.Merge(dst, src)
}
func (m *TopNAwardCfg) XXX_Size() int {
	return xxx_messageInfo_TopNAwardCfg.Size(m)
}
func (m *TopNAwardCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_TopNAwardCfg.DiscardUnknown(m)
}

var xxx_messageInfo_TopNAwardCfg proto.InternalMessageInfo

func (m *TopNAwardCfg) GetTopNLimit() uint32 {
	if m != nil {
		return m.TopNLimit
	}
	return 0
}

func (m *TopNAwardCfg) GetAwardList() []*AwardInfo {
	if m != nil {
		return m.AwardList
	}
	return nil
}

type UserPlayFile struct {
	LevelId              uint32            `protobuf:"varint,2,opt,name=level_id,json=levelId,proto3" json:"level_id,omitempty"`
	CurrentChance        uint32            `protobuf:"varint,5,opt,name=current_chance,json=currentChance,proto3" json:"current_chance,omitempty"`
	UserN                uint32            `protobuf:"varint,6,opt,name=user_n,json=userN,proto3" json:"user_n,omitempty"`
	CardCollection       map[string]uint32 `protobuf:"bytes,7,rep,name=card_collection,json=cardCollection,proto3" json:"card_collection,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UserPlayFile) Reset()         { *m = UserPlayFile{} }
func (m *UserPlayFile) String() string { return proto.CompactTextString(m) }
func (*UserPlayFile) ProtoMessage()    {}
func (*UserPlayFile) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_06afd5ae92173300, []int{15}
}
func (m *UserPlayFile) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserPlayFile.Unmarshal(m, b)
}
func (m *UserPlayFile) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserPlayFile.Marshal(b, m, deterministic)
}
func (dst *UserPlayFile) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserPlayFile.Merge(dst, src)
}
func (m *UserPlayFile) XXX_Size() int {
	return xxx_messageInfo_UserPlayFile.Size(m)
}
func (m *UserPlayFile) XXX_DiscardUnknown() {
	xxx_messageInfo_UserPlayFile.DiscardUnknown(m)
}

var xxx_messageInfo_UserPlayFile proto.InternalMessageInfo

func (m *UserPlayFile) GetLevelId() uint32 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

func (m *UserPlayFile) GetCurrentChance() uint32 {
	if m != nil {
		return m.CurrentChance
	}
	return 0
}

func (m *UserPlayFile) GetUserN() uint32 {
	if m != nil {
		return m.UserN
	}
	return 0
}

func (m *UserPlayFile) GetCardCollection() map[string]uint32 {
	if m != nil {
		return m.CardCollection
	}
	return nil
}

// 获取活动信息
type GetCurrGameInfoRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCurrGameInfoRequest) Reset()         { *m = GetCurrGameInfoRequest{} }
func (m *GetCurrGameInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetCurrGameInfoRequest) ProtoMessage()    {}
func (*GetCurrGameInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_06afd5ae92173300, []int{16}
}
func (m *GetCurrGameInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCurrGameInfoRequest.Unmarshal(m, b)
}
func (m *GetCurrGameInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCurrGameInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetCurrGameInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCurrGameInfoRequest.Merge(dst, src)
}
func (m *GetCurrGameInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetCurrGameInfoRequest.Size(m)
}
func (m *GetCurrGameInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCurrGameInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCurrGameInfoRequest proto.InternalMessageInfo

func (m *GetCurrGameInfoRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetCurrGameInfoRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// 购买礼包配置信息
type BuyPropCfg struct {
	GiftInfo             *AwardInfo `protobuf:"bytes,1,opt,name=gift_info,json=giftInfo,proto3" json:"gift_info,omitempty"`
	BuyAmountOptions     []uint32   `protobuf:"varint,3,rep,packed,name=buy_amount_options,json=buyAmountOptions,proto3" json:"buy_amount_options,omitempty"`
	UnitPrice            uint32     `protobuf:"varint,4,opt,name=unit_price,json=unitPrice,proto3" json:"unit_price,omitempty"`
	BuyLimitDesc         string     `protobuf:"bytes,5,opt,name=buy_limit_desc,json=buyLimitDesc,proto3" json:"buy_limit_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *BuyPropCfg) Reset()         { *m = BuyPropCfg{} }
func (m *BuyPropCfg) String() string { return proto.CompactTextString(m) }
func (*BuyPropCfg) ProtoMessage()    {}
func (*BuyPropCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_06afd5ae92173300, []int{17}
}
func (m *BuyPropCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuyPropCfg.Unmarshal(m, b)
}
func (m *BuyPropCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuyPropCfg.Marshal(b, m, deterministic)
}
func (dst *BuyPropCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuyPropCfg.Merge(dst, src)
}
func (m *BuyPropCfg) XXX_Size() int {
	return xxx_messageInfo_BuyPropCfg.Size(m)
}
func (m *BuyPropCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_BuyPropCfg.DiscardUnknown(m)
}

var xxx_messageInfo_BuyPropCfg proto.InternalMessageInfo

func (m *BuyPropCfg) GetGiftInfo() *AwardInfo {
	if m != nil {
		return m.GiftInfo
	}
	return nil
}

func (m *BuyPropCfg) GetBuyAmountOptions() []uint32 {
	if m != nil {
		return m.BuyAmountOptions
	}
	return nil
}

func (m *BuyPropCfg) GetUnitPrice() uint32 {
	if m != nil {
		return m.UnitPrice
	}
	return 0
}

func (m *BuyPropCfg) GetBuyLimitDesc() string {
	if m != nil {
		return m.BuyLimitDesc
	}
	return ""
}

type GetCurrGameInfoResponse struct {
	ActivityId    uint32               `protobuf:"varint,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	ActivityName  string               `protobuf:"bytes,2,opt,name=activity_name,json=activityName,proto3" json:"activity_name,omitempty"`
	BeginTime     int64                `protobuf:"varint,3,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime       int64                `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	BuyPropCfg    *BuyPropCfg          `protobuf:"bytes,5,opt,name=buy_prop_cfg,json=buyPropCfg,proto3" json:"buy_prop_cfg,omitempty"`
	LevelList     []*LevelConfWithPool `protobuf:"bytes,6,rep,name=level_list,json=levelList,proto3" json:"level_list,omitempty"`
	UserPlayFile  *UserPlayFile        `protobuf:"bytes,7,opt,name=user_play_file,json=userPlayFile,proto3" json:"user_play_file,omitempty"`
	TopNAwardInfo *TopNAwardCfg        `protobuf:"bytes,8,opt,name=top_n_award_info,json=topNAwardInfo,proto3" json:"top_n_award_info,omitempty"`
	// 无权限的用户展示文案
	NoPermissionText     string   `protobuf:"bytes,9,opt,name=no_permission_text,json=noPermissionText,proto3" json:"no_permission_text,omitempty"`
	HasAccess            bool     `protobuf:"varint,10,opt,name=has_access,json=hasAccess,proto3" json:"has_access,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCurrGameInfoResponse) Reset()         { *m = GetCurrGameInfoResponse{} }
func (m *GetCurrGameInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetCurrGameInfoResponse) ProtoMessage()    {}
func (*GetCurrGameInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_06afd5ae92173300, []int{18}
}
func (m *GetCurrGameInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCurrGameInfoResponse.Unmarshal(m, b)
}
func (m *GetCurrGameInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCurrGameInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetCurrGameInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCurrGameInfoResponse.Merge(dst, src)
}
func (m *GetCurrGameInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetCurrGameInfoResponse.Size(m)
}
func (m *GetCurrGameInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCurrGameInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCurrGameInfoResponse proto.InternalMessageInfo

func (m *GetCurrGameInfoResponse) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *GetCurrGameInfoResponse) GetActivityName() string {
	if m != nil {
		return m.ActivityName
	}
	return ""
}

func (m *GetCurrGameInfoResponse) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetCurrGameInfoResponse) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetCurrGameInfoResponse) GetBuyPropCfg() *BuyPropCfg {
	if m != nil {
		return m.BuyPropCfg
	}
	return nil
}

func (m *GetCurrGameInfoResponse) GetLevelList() []*LevelConfWithPool {
	if m != nil {
		return m.LevelList
	}
	return nil
}

func (m *GetCurrGameInfoResponse) GetUserPlayFile() *UserPlayFile {
	if m != nil {
		return m.UserPlayFile
	}
	return nil
}

func (m *GetCurrGameInfoResponse) GetTopNAwardInfo() *TopNAwardCfg {
	if m != nil {
		return m.TopNAwardInfo
	}
	return nil
}

func (m *GetCurrGameInfoResponse) GetNoPermissionText() string {
	if m != nil {
		return m.NoPermissionText
	}
	return ""
}

func (m *GetCurrGameInfoResponse) GetHasAccess() bool {
	if m != nil {
		return m.HasAccess
	}
	return false
}

// 购买抽奖机会
type BuyChanceRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChanceAmount         uint32   `protobuf:"varint,3,opt,name=chance_amount,json=chanceAmount,proto3" json:"chance_amount,omitempty"`
	Fee                  uint32   `protobuf:"varint,4,opt,name=fee,proto3" json:"fee,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BuyChanceRequest) Reset()         { *m = BuyChanceRequest{} }
func (m *BuyChanceRequest) String() string { return proto.CompactTextString(m) }
func (*BuyChanceRequest) ProtoMessage()    {}
func (*BuyChanceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_06afd5ae92173300, []int{19}
}
func (m *BuyChanceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuyChanceRequest.Unmarshal(m, b)
}
func (m *BuyChanceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuyChanceRequest.Marshal(b, m, deterministic)
}
func (dst *BuyChanceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuyChanceRequest.Merge(dst, src)
}
func (m *BuyChanceRequest) XXX_Size() int {
	return xxx_messageInfo_BuyChanceRequest.Size(m)
}
func (m *BuyChanceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BuyChanceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BuyChanceRequest proto.InternalMessageInfo

func (m *BuyChanceRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BuyChanceRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BuyChanceRequest) GetChanceAmount() uint32 {
	if m != nil {
		return m.ChanceAmount
	}
	return 0
}

func (m *BuyChanceRequest) GetFee() uint32 {
	if m != nil {
		return m.Fee
	}
	return 0
}

type BuyChanceResponse struct {
	FinalChanceAmount    uint32   `protobuf:"varint,1,opt,name=final_chance_amount,json=finalChanceAmount,proto3" json:"final_chance_amount,omitempty"`
	Balance              uint64   `protobuf:"varint,2,opt,name=balance,proto3" json:"balance,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BuyChanceResponse) Reset()         { *m = BuyChanceResponse{} }
func (m *BuyChanceResponse) String() string { return proto.CompactTextString(m) }
func (*BuyChanceResponse) ProtoMessage()    {}
func (*BuyChanceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_06afd5ae92173300, []int{20}
}
func (m *BuyChanceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuyChanceResponse.Unmarshal(m, b)
}
func (m *BuyChanceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuyChanceResponse.Marshal(b, m, deterministic)
}
func (dst *BuyChanceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuyChanceResponse.Merge(dst, src)
}
func (m *BuyChanceResponse) XXX_Size() int {
	return xxx_messageInfo_BuyChanceResponse.Size(m)
}
func (m *BuyChanceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BuyChanceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BuyChanceResponse proto.InternalMessageInfo

func (m *BuyChanceResponse) GetFinalChanceAmount() uint32 {
	if m != nil {
		return m.FinalChanceAmount
	}
	return 0
}

func (m *BuyChanceResponse) GetBalance() uint64 {
	if m != nil {
		return m.Balance
	}
	return 0
}

// 抽奖结果
type DrawResult struct {
	LevelId              uint32       `protobuf:"varint,1,opt,name=level_id,json=levelId,proto3" json:"level_id,omitempty"`
	AwardList            []*AwardInfo `protobuf:"bytes,2,rep,name=award_list,json=awardList,proto3" json:"award_list,omitempty"`
	ResultType           uint32       `protobuf:"varint,3,opt,name=result_type,json=resultType,proto3" json:"result_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *DrawResult) Reset()         { *m = DrawResult{} }
func (m *DrawResult) String() string { return proto.CompactTextString(m) }
func (*DrawResult) ProtoMessage()    {}
func (*DrawResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_06afd5ae92173300, []int{21}
}
func (m *DrawResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DrawResult.Unmarshal(m, b)
}
func (m *DrawResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DrawResult.Marshal(b, m, deterministic)
}
func (dst *DrawResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DrawResult.Merge(dst, src)
}
func (m *DrawResult) XXX_Size() int {
	return xxx_messageInfo_DrawResult.Size(m)
}
func (m *DrawResult) XXX_DiscardUnknown() {
	xxx_messageInfo_DrawResult.DiscardUnknown(m)
}

var xxx_messageInfo_DrawResult proto.InternalMessageInfo

func (m *DrawResult) GetLevelId() uint32 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

func (m *DrawResult) GetAwardList() []*AwardInfo {
	if m != nil {
		return m.AwardList
	}
	return nil
}

func (m *DrawResult) GetResultType() uint32 {
	if m != nil {
		return m.ResultType
	}
	return 0
}

// 抽奖 url: /tt-revenue-http-logic/adventure-island/draw_card
type LotteryDrawRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	LevelId              uint32   `protobuf:"varint,3,opt,name=level_id,json=levelId,proto3" json:"level_id,omitempty"`
	Amount               uint32   `protobuf:"varint,4,opt,name=amount,proto3" json:"amount,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LotteryDrawRequest) Reset()         { *m = LotteryDrawRequest{} }
func (m *LotteryDrawRequest) String() string { return proto.CompactTextString(m) }
func (*LotteryDrawRequest) ProtoMessage()    {}
func (*LotteryDrawRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_06afd5ae92173300, []int{22}
}
func (m *LotteryDrawRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LotteryDrawRequest.Unmarshal(m, b)
}
func (m *LotteryDrawRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LotteryDrawRequest.Marshal(b, m, deterministic)
}
func (dst *LotteryDrawRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LotteryDrawRequest.Merge(dst, src)
}
func (m *LotteryDrawRequest) XXX_Size() int {
	return xxx_messageInfo_LotteryDrawRequest.Size(m)
}
func (m *LotteryDrawRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_LotteryDrawRequest.DiscardUnknown(m)
}

var xxx_messageInfo_LotteryDrawRequest proto.InternalMessageInfo

func (m *LotteryDrawRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *LotteryDrawRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *LotteryDrawRequest) GetLevelId() uint32 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

func (m *LotteryDrawRequest) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

type LotteryDrawResponse struct {
	ResultList           []*DrawResult `protobuf:"bytes,1,rep,name=result_list,json=resultList,proto3" json:"result_list,omitempty"`
	UserPlayFile         *UserPlayFile `protobuf:"bytes,2,opt,name=user_play_file,json=userPlayFile,proto3" json:"user_play_file,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *LotteryDrawResponse) Reset()         { *m = LotteryDrawResponse{} }
func (m *LotteryDrawResponse) String() string { return proto.CompactTextString(m) }
func (*LotteryDrawResponse) ProtoMessage()    {}
func (*LotteryDrawResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_06afd5ae92173300, []int{23}
}
func (m *LotteryDrawResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LotteryDrawResponse.Unmarshal(m, b)
}
func (m *LotteryDrawResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LotteryDrawResponse.Marshal(b, m, deterministic)
}
func (dst *LotteryDrawResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LotteryDrawResponse.Merge(dst, src)
}
func (m *LotteryDrawResponse) XXX_Size() int {
	return xxx_messageInfo_LotteryDrawResponse.Size(m)
}
func (m *LotteryDrawResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_LotteryDrawResponse.DiscardUnknown(m)
}

var xxx_messageInfo_LotteryDrawResponse proto.InternalMessageInfo

func (m *LotteryDrawResponse) GetResultList() []*DrawResult {
	if m != nil {
		return m.ResultList
	}
	return nil
}

func (m *LotteryDrawResponse) GetUserPlayFile() *UserPlayFile {
	if m != nil {
		return m.UserPlayFile
	}
	return nil
}

// 平台中奖记录 轮播用
type PlatformWinningRecord struct {
	Uid                  uint32     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string     `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	LevelId              uint32     `protobuf:"varint,3,opt,name=level_id,json=levelId,proto3" json:"level_id,omitempty"`
	AwardInfo            *AwardInfo `protobuf:"bytes,4,opt,name=award_info,json=awardInfo,proto3" json:"award_info,omitempty"`
	ResultType           uint32     `protobuf:"varint,5,opt,name=result_type,json=resultType,proto3" json:"result_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *PlatformWinningRecord) Reset()         { *m = PlatformWinningRecord{} }
func (m *PlatformWinningRecord) String() string { return proto.CompactTextString(m) }
func (*PlatformWinningRecord) ProtoMessage()    {}
func (*PlatformWinningRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_06afd5ae92173300, []int{24}
}
func (m *PlatformWinningRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlatformWinningRecord.Unmarshal(m, b)
}
func (m *PlatformWinningRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlatformWinningRecord.Marshal(b, m, deterministic)
}
func (dst *PlatformWinningRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlatformWinningRecord.Merge(dst, src)
}
func (m *PlatformWinningRecord) XXX_Size() int {
	return xxx_messageInfo_PlatformWinningRecord.Size(m)
}
func (m *PlatformWinningRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_PlatformWinningRecord.DiscardUnknown(m)
}

var xxx_messageInfo_PlatformWinningRecord proto.InternalMessageInfo

func (m *PlatformWinningRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PlatformWinningRecord) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *PlatformWinningRecord) GetLevelId() uint32 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

func (m *PlatformWinningRecord) GetAwardInfo() *AwardInfo {
	if m != nil {
		return m.AwardInfo
	}
	return nil
}

func (m *PlatformWinningRecord) GetResultType() uint32 {
	if m != nil {
		return m.ResultType
	}
	return 0
}

// 获取平台中奖记录
type GetPlatformWinningRecordRequest struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Version              string   `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPlatformWinningRecordRequest) Reset()         { *m = GetPlatformWinningRecordRequest{} }
func (m *GetPlatformWinningRecordRequest) String() string { return proto.CompactTextString(m) }
func (*GetPlatformWinningRecordRequest) ProtoMessage()    {}
func (*GetPlatformWinningRecordRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_06afd5ae92173300, []int{25}
}
func (m *GetPlatformWinningRecordRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlatformWinningRecordRequest.Unmarshal(m, b)
}
func (m *GetPlatformWinningRecordRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlatformWinningRecordRequest.Marshal(b, m, deterministic)
}
func (dst *GetPlatformWinningRecordRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlatformWinningRecordRequest.Merge(dst, src)
}
func (m *GetPlatformWinningRecordRequest) XXX_Size() int {
	return xxx_messageInfo_GetPlatformWinningRecordRequest.Size(m)
}
func (m *GetPlatformWinningRecordRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlatformWinningRecordRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlatformWinningRecordRequest proto.InternalMessageInfo

func (m *GetPlatformWinningRecordRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetPlatformWinningRecordRequest) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

type GetPlatformWinningRecordResponse struct {
	NewVersion           string                   `protobuf:"bytes,1,opt,name=new_version,json=newVersion,proto3" json:"new_version,omitempty"`
	RecordList           []*PlatformWinningRecord `protobuf:"bytes,2,rep,name=record_list,json=recordList,proto3" json:"record_list,omitempty"`
	TopNUsed             uint32                   `protobuf:"varint,3,opt,name=top_n_used,json=topNUsed,proto3" json:"top_n_used,omitempty"`
	ReqDurationSec       uint32                   `protobuf:"varint,4,opt,name=req_duration_sec,json=reqDurationSec,proto3" json:"req_duration_sec,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetPlatformWinningRecordResponse) Reset()         { *m = GetPlatformWinningRecordResponse{} }
func (m *GetPlatformWinningRecordResponse) String() string { return proto.CompactTextString(m) }
func (*GetPlatformWinningRecordResponse) ProtoMessage()    {}
func (*GetPlatformWinningRecordResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_06afd5ae92173300, []int{26}
}
func (m *GetPlatformWinningRecordResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlatformWinningRecordResponse.Unmarshal(m, b)
}
func (m *GetPlatformWinningRecordResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlatformWinningRecordResponse.Marshal(b, m, deterministic)
}
func (dst *GetPlatformWinningRecordResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlatformWinningRecordResponse.Merge(dst, src)
}
func (m *GetPlatformWinningRecordResponse) XXX_Size() int {
	return xxx_messageInfo_GetPlatformWinningRecordResponse.Size(m)
}
func (m *GetPlatformWinningRecordResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlatformWinningRecordResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlatformWinningRecordResponse proto.InternalMessageInfo

func (m *GetPlatformWinningRecordResponse) GetNewVersion() string {
	if m != nil {
		return m.NewVersion
	}
	return ""
}

func (m *GetPlatformWinningRecordResponse) GetRecordList() []*PlatformWinningRecord {
	if m != nil {
		return m.RecordList
	}
	return nil
}

func (m *GetPlatformWinningRecordResponse) GetTopNUsed() uint32 {
	if m != nil {
		return m.TopNUsed
	}
	return 0
}

func (m *GetPlatformWinningRecordResponse) GetReqDurationSec() uint32 {
	if m != nil {
		return m.ReqDurationSec
	}
	return 0
}

// 获取用户冒险记录
type GetUserAdventureRecordRequest struct {
	RecordType           uint32   `protobuf:"varint,1,opt,name=record_type,json=recordType,proto3" json:"record_type,omitempty"`
	Offset               string   `protobuf:"bytes,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	Uid                  uint32   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserAdventureRecordRequest) Reset()         { *m = GetUserAdventureRecordRequest{} }
func (m *GetUserAdventureRecordRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserAdventureRecordRequest) ProtoMessage()    {}
func (*GetUserAdventureRecordRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_06afd5ae92173300, []int{27}
}
func (m *GetUserAdventureRecordRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAdventureRecordRequest.Unmarshal(m, b)
}
func (m *GetUserAdventureRecordRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAdventureRecordRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserAdventureRecordRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAdventureRecordRequest.Merge(dst, src)
}
func (m *GetUserAdventureRecordRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserAdventureRecordRequest.Size(m)
}
func (m *GetUserAdventureRecordRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAdventureRecordRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAdventureRecordRequest proto.InternalMessageInfo

func (m *GetUserAdventureRecordRequest) GetRecordType() uint32 {
	if m != nil {
		return m.RecordType
	}
	return 0
}

func (m *GetUserAdventureRecordRequest) GetOffset() string {
	if m != nil {
		return m.Offset
	}
	return ""
}

func (m *GetUserAdventureRecordRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetUserAdventureRecordRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserAdventureRecordResponse struct {
	NextOffset           string        `protobuf:"bytes,1,opt,name=next_offset,json=nextOffset,proto3" json:"next_offset,omitempty"`
	RecordList           []*DrawResult `protobuf:"bytes,2,rep,name=record_list,json=recordList,proto3" json:"record_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetUserAdventureRecordResponse) Reset()         { *m = GetUserAdventureRecordResponse{} }
func (m *GetUserAdventureRecordResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserAdventureRecordResponse) ProtoMessage()    {}
func (*GetUserAdventureRecordResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_adventure_activity_06afd5ae92173300, []int{28}
}
func (m *GetUserAdventureRecordResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAdventureRecordResponse.Unmarshal(m, b)
}
func (m *GetUserAdventureRecordResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAdventureRecordResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserAdventureRecordResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAdventureRecordResponse.Merge(dst, src)
}
func (m *GetUserAdventureRecordResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserAdventureRecordResponse.Size(m)
}
func (m *GetUserAdventureRecordResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAdventureRecordResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAdventureRecordResponse proto.InternalMessageInfo

func (m *GetUserAdventureRecordResponse) GetNextOffset() string {
	if m != nil {
		return m.NextOffset
	}
	return ""
}

func (m *GetUserAdventureRecordResponse) GetRecordList() []*DrawResult {
	if m != nil {
		return m.RecordList
	}
	return nil
}

func init() {
	proto.RegisterType((*AwardInfo)(nil), "adventure_activity.AwardInfo")
	proto.RegisterType((*LevelCfg)(nil), "adventure_activity.LevelCfg")
	proto.RegisterType((*LevelConfWithPool)(nil), "adventure_activity.LevelConfWithPool")
	proto.RegisterType((*PrizePool)(nil), "adventure_activity.PrizePool")
	proto.RegisterType((*SetLevelConfRequest)(nil), "adventure_activity.SetLevelConfRequest")
	proto.RegisterType((*SetLevelConfResponse)(nil), "adventure_activity.SetLevelConfResponse")
	proto.RegisterType((*SetLevelPrizePoolRequest)(nil), "adventure_activity.SetLevelPrizePoolRequest")
	proto.RegisterType((*SetLevelPrizePoolResponse)(nil), "adventure_activity.SetLevelPrizePoolResponse")
	proto.RegisterType((*GetLevelConfWithPoolRequest)(nil), "adventure_activity.GetLevelConfWithPoolRequest")
	proto.RegisterType((*GetLevelConfWithPoolResponse)(nil), "adventure_activity.GetLevelConfWithPoolResponse")
	proto.RegisterType((*SetAdventureIslandLimitConfReq)(nil), "adventure_activity.SetAdventureIslandLimitConfReq")
	proto.RegisterType((*SetAdventureIslandLimitConfResp)(nil), "adventure_activity.SetAdventureIslandLimitConfResp")
	proto.RegisterType((*GetAdventureIslandLimitConfReq)(nil), "adventure_activity.GetAdventureIslandLimitConfReq")
	proto.RegisterType((*GetAdventureIslandLimitConfResp)(nil), "adventure_activity.GetAdventureIslandLimitConfResp")
	proto.RegisterType((*TopNAwardCfg)(nil), "adventure_activity.TopNAwardCfg")
	proto.RegisterType((*UserPlayFile)(nil), "adventure_activity.UserPlayFile")
	proto.RegisterMapType((map[string]uint32)(nil), "adventure_activity.UserPlayFile.CardCollectionEntry")
	proto.RegisterType((*GetCurrGameInfoRequest)(nil), "adventure_activity.GetCurrGameInfoRequest")
	proto.RegisterType((*BuyPropCfg)(nil), "adventure_activity.BuyPropCfg")
	proto.RegisterType((*GetCurrGameInfoResponse)(nil), "adventure_activity.GetCurrGameInfoResponse")
	proto.RegisterType((*BuyChanceRequest)(nil), "adventure_activity.BuyChanceRequest")
	proto.RegisterType((*BuyChanceResponse)(nil), "adventure_activity.BuyChanceResponse")
	proto.RegisterType((*DrawResult)(nil), "adventure_activity.DrawResult")
	proto.RegisterType((*LotteryDrawRequest)(nil), "adventure_activity.LotteryDrawRequest")
	proto.RegisterType((*LotteryDrawResponse)(nil), "adventure_activity.LotteryDrawResponse")
	proto.RegisterType((*PlatformWinningRecord)(nil), "adventure_activity.PlatformWinningRecord")
	proto.RegisterType((*GetPlatformWinningRecordRequest)(nil), "adventure_activity.GetPlatformWinningRecordRequest")
	proto.RegisterType((*GetPlatformWinningRecordResponse)(nil), "adventure_activity.GetPlatformWinningRecordResponse")
	proto.RegisterType((*GetUserAdventureRecordRequest)(nil), "adventure_activity.GetUserAdventureRecordRequest")
	proto.RegisterType((*GetUserAdventureRecordResponse)(nil), "adventure_activity.GetUserAdventureRecordResponse")
	proto.RegisterEnum("adventure_activity.AwardType", AwardType_name, AwardType_value)
	proto.RegisterEnum("adventure_activity.DrawResultType", DrawResultType_name, DrawResultType_value)
	proto.RegisterEnum("adventure_activity.GetUserAdventureRecordRequest_RecordType", GetUserAdventureRecordRequest_RecordType_name, GetUserAdventureRecordRequest_RecordType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AdventureActivityClient is the client API for AdventureActivity service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AdventureActivityClient interface {
	// 获取活动信息
	GetCurrGameInfo(ctx context.Context, in *GetCurrGameInfoRequest, opts ...grpc.CallOption) (*GetCurrGameInfoResponse, error)
	// 购买抽奖机会
	BuyChance(ctx context.Context, in *BuyChanceRequest, opts ...grpc.CallOption) (*BuyChanceResponse, error)
	// 进行抽奖
	LotteryDraw(ctx context.Context, in *LotteryDrawRequest, opts ...grpc.CallOption) (*LotteryDrawResponse, error)
	// 获取平台中奖记录
	GetPlatformWinningRecord(ctx context.Context, in *GetPlatformWinningRecordRequest, opts ...grpc.CallOption) (*GetPlatformWinningRecordResponse, error)
	// 获取用户冒险记录
	GetUserAdventureRecord(ctx context.Context, in *GetUserAdventureRecordRequest, opts ...grpc.CallOption) (*GetUserAdventureRecordResponse, error)
	// ==========配置相关接口==========
	// 设置关卡配置
	SetLevelConf(ctx context.Context, in *SetLevelConfRequest, opts ...grpc.CallOption) (*SetLevelConfResponse, error)
	// 设置关卡奖池
	SetLevelPrizePool(ctx context.Context, in *SetLevelPrizePoolRequest, opts ...grpc.CallOption) (*SetLevelPrizePoolResponse, error)
	// 获取活动所有关卡奖池信息
	GetLevelConfWithPool(ctx context.Context, in *GetLevelConfWithPoolRequest, opts ...grpc.CallOption) (*GetLevelConfWithPoolResponse, error)
	// 防违规配置
	SetAdventureIslandLimitConf(ctx context.Context, in *SetAdventureIslandLimitConfReq, opts ...grpc.CallOption) (*SetAdventureIslandLimitConfResp, error)
	GetAdventureIslandLimitConf(ctx context.Context, in *GetAdventureIslandLimitConfReq, opts ...grpc.CallOption) (*GetAdventureIslandLimitConfResp, error)
	// ==========对账接口==========
	// T豆对账
	GetTBeanTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetTBeanOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// 奖励数据对账
	GetAwardTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetAwardOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
}

type adventureActivityClient struct {
	cc *grpc.ClientConn
}

func NewAdventureActivityClient(cc *grpc.ClientConn) AdventureActivityClient {
	return &adventureActivityClient{cc}
}

func (c *adventureActivityClient) GetCurrGameInfo(ctx context.Context, in *GetCurrGameInfoRequest, opts ...grpc.CallOption) (*GetCurrGameInfoResponse, error) {
	out := new(GetCurrGameInfoResponse)
	err := c.cc.Invoke(ctx, "/adventure_activity.AdventureActivity/GetCurrGameInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adventureActivityClient) BuyChance(ctx context.Context, in *BuyChanceRequest, opts ...grpc.CallOption) (*BuyChanceResponse, error) {
	out := new(BuyChanceResponse)
	err := c.cc.Invoke(ctx, "/adventure_activity.AdventureActivity/BuyChance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adventureActivityClient) LotteryDraw(ctx context.Context, in *LotteryDrawRequest, opts ...grpc.CallOption) (*LotteryDrawResponse, error) {
	out := new(LotteryDrawResponse)
	err := c.cc.Invoke(ctx, "/adventure_activity.AdventureActivity/LotteryDraw", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adventureActivityClient) GetPlatformWinningRecord(ctx context.Context, in *GetPlatformWinningRecordRequest, opts ...grpc.CallOption) (*GetPlatformWinningRecordResponse, error) {
	out := new(GetPlatformWinningRecordResponse)
	err := c.cc.Invoke(ctx, "/adventure_activity.AdventureActivity/GetPlatformWinningRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adventureActivityClient) GetUserAdventureRecord(ctx context.Context, in *GetUserAdventureRecordRequest, opts ...grpc.CallOption) (*GetUserAdventureRecordResponse, error) {
	out := new(GetUserAdventureRecordResponse)
	err := c.cc.Invoke(ctx, "/adventure_activity.AdventureActivity/GetUserAdventureRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adventureActivityClient) SetLevelConf(ctx context.Context, in *SetLevelConfRequest, opts ...grpc.CallOption) (*SetLevelConfResponse, error) {
	out := new(SetLevelConfResponse)
	err := c.cc.Invoke(ctx, "/adventure_activity.AdventureActivity/SetLevelConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adventureActivityClient) SetLevelPrizePool(ctx context.Context, in *SetLevelPrizePoolRequest, opts ...grpc.CallOption) (*SetLevelPrizePoolResponse, error) {
	out := new(SetLevelPrizePoolResponse)
	err := c.cc.Invoke(ctx, "/adventure_activity.AdventureActivity/SetLevelPrizePool", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adventureActivityClient) GetLevelConfWithPool(ctx context.Context, in *GetLevelConfWithPoolRequest, opts ...grpc.CallOption) (*GetLevelConfWithPoolResponse, error) {
	out := new(GetLevelConfWithPoolResponse)
	err := c.cc.Invoke(ctx, "/adventure_activity.AdventureActivity/GetLevelConfWithPool", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adventureActivityClient) SetAdventureIslandLimitConf(ctx context.Context, in *SetAdventureIslandLimitConfReq, opts ...grpc.CallOption) (*SetAdventureIslandLimitConfResp, error) {
	out := new(SetAdventureIslandLimitConfResp)
	err := c.cc.Invoke(ctx, "/adventure_activity.AdventureActivity/SetAdventureIslandLimitConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adventureActivityClient) GetAdventureIslandLimitConf(ctx context.Context, in *GetAdventureIslandLimitConfReq, opts ...grpc.CallOption) (*GetAdventureIslandLimitConfResp, error) {
	out := new(GetAdventureIslandLimitConfResp)
	err := c.cc.Invoke(ctx, "/adventure_activity.AdventureActivity/GetAdventureIslandLimitConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adventureActivityClient) GetTBeanTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/adventure_activity.AdventureActivity/GetTBeanTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adventureActivityClient) GetTBeanOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/adventure_activity.AdventureActivity/GetTBeanOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adventureActivityClient) GetAwardTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/adventure_activity.AdventureActivity/GetAwardTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adventureActivityClient) GetAwardOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/adventure_activity.AdventureActivity/GetAwardOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AdventureActivityServer is the server API for AdventureActivity service.
type AdventureActivityServer interface {
	// 获取活动信息
	GetCurrGameInfo(context.Context, *GetCurrGameInfoRequest) (*GetCurrGameInfoResponse, error)
	// 购买抽奖机会
	BuyChance(context.Context, *BuyChanceRequest) (*BuyChanceResponse, error)
	// 进行抽奖
	LotteryDraw(context.Context, *LotteryDrawRequest) (*LotteryDrawResponse, error)
	// 获取平台中奖记录
	GetPlatformWinningRecord(context.Context, *GetPlatformWinningRecordRequest) (*GetPlatformWinningRecordResponse, error)
	// 获取用户冒险记录
	GetUserAdventureRecord(context.Context, *GetUserAdventureRecordRequest) (*GetUserAdventureRecordResponse, error)
	// ==========配置相关接口==========
	// 设置关卡配置
	SetLevelConf(context.Context, *SetLevelConfRequest) (*SetLevelConfResponse, error)
	// 设置关卡奖池
	SetLevelPrizePool(context.Context, *SetLevelPrizePoolRequest) (*SetLevelPrizePoolResponse, error)
	// 获取活动所有关卡奖池信息
	GetLevelConfWithPool(context.Context, *GetLevelConfWithPoolRequest) (*GetLevelConfWithPoolResponse, error)
	// 防违规配置
	SetAdventureIslandLimitConf(context.Context, *SetAdventureIslandLimitConfReq) (*SetAdventureIslandLimitConfResp, error)
	GetAdventureIslandLimitConf(context.Context, *GetAdventureIslandLimitConfReq) (*GetAdventureIslandLimitConfResp, error)
	// ==========对账接口==========
	// T豆对账
	GetTBeanTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetTBeanOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// 奖励数据对账
	GetAwardTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetAwardOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
}

func RegisterAdventureActivityServer(s *grpc.Server, srv AdventureActivityServer) {
	s.RegisterService(&_AdventureActivity_serviceDesc, srv)
}

func _AdventureActivity_GetCurrGameInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCurrGameInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdventureActivityServer).GetCurrGameInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/adventure_activity.AdventureActivity/GetCurrGameInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdventureActivityServer).GetCurrGameInfo(ctx, req.(*GetCurrGameInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdventureActivity_BuyChance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BuyChanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdventureActivityServer).BuyChance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/adventure_activity.AdventureActivity/BuyChance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdventureActivityServer).BuyChance(ctx, req.(*BuyChanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdventureActivity_LotteryDraw_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LotteryDrawRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdventureActivityServer).LotteryDraw(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/adventure_activity.AdventureActivity/LotteryDraw",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdventureActivityServer).LotteryDraw(ctx, req.(*LotteryDrawRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdventureActivity_GetPlatformWinningRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlatformWinningRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdventureActivityServer).GetPlatformWinningRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/adventure_activity.AdventureActivity/GetPlatformWinningRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdventureActivityServer).GetPlatformWinningRecord(ctx, req.(*GetPlatformWinningRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdventureActivity_GetUserAdventureRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserAdventureRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdventureActivityServer).GetUserAdventureRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/adventure_activity.AdventureActivity/GetUserAdventureRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdventureActivityServer).GetUserAdventureRecord(ctx, req.(*GetUserAdventureRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdventureActivity_SetLevelConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetLevelConfRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdventureActivityServer).SetLevelConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/adventure_activity.AdventureActivity/SetLevelConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdventureActivityServer).SetLevelConf(ctx, req.(*SetLevelConfRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdventureActivity_SetLevelPrizePool_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetLevelPrizePoolRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdventureActivityServer).SetLevelPrizePool(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/adventure_activity.AdventureActivity/SetLevelPrizePool",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdventureActivityServer).SetLevelPrizePool(ctx, req.(*SetLevelPrizePoolRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdventureActivity_GetLevelConfWithPool_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLevelConfWithPoolRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdventureActivityServer).GetLevelConfWithPool(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/adventure_activity.AdventureActivity/GetLevelConfWithPool",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdventureActivityServer).GetLevelConfWithPool(ctx, req.(*GetLevelConfWithPoolRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdventureActivity_SetAdventureIslandLimitConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetAdventureIslandLimitConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdventureActivityServer).SetAdventureIslandLimitConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/adventure_activity.AdventureActivity/SetAdventureIslandLimitConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdventureActivityServer).SetAdventureIslandLimitConf(ctx, req.(*SetAdventureIslandLimitConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdventureActivity_GetAdventureIslandLimitConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAdventureIslandLimitConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdventureActivityServer).GetAdventureIslandLimitConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/adventure_activity.AdventureActivity/GetAdventureIslandLimitConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdventureActivityServer).GetAdventureIslandLimitConf(ctx, req.(*GetAdventureIslandLimitConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdventureActivity_GetTBeanTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdventureActivityServer).GetTBeanTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/adventure_activity.AdventureActivity/GetTBeanTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdventureActivityServer).GetTBeanTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdventureActivity_GetTBeanOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdventureActivityServer).GetTBeanOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/adventure_activity.AdventureActivity/GetTBeanOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdventureActivityServer).GetTBeanOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdventureActivity_GetAwardTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdventureActivityServer).GetAwardTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/adventure_activity.AdventureActivity/GetAwardTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdventureActivityServer).GetAwardTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdventureActivity_GetAwardOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdventureActivityServer).GetAwardOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/adventure_activity.AdventureActivity/GetAwardOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdventureActivityServer).GetAwardOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _AdventureActivity_serviceDesc = grpc.ServiceDesc{
	ServiceName: "adventure_activity.AdventureActivity",
	HandlerType: (*AdventureActivityServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCurrGameInfo",
			Handler:    _AdventureActivity_GetCurrGameInfo_Handler,
		},
		{
			MethodName: "BuyChance",
			Handler:    _AdventureActivity_BuyChance_Handler,
		},
		{
			MethodName: "LotteryDraw",
			Handler:    _AdventureActivity_LotteryDraw_Handler,
		},
		{
			MethodName: "GetPlatformWinningRecord",
			Handler:    _AdventureActivity_GetPlatformWinningRecord_Handler,
		},
		{
			MethodName: "GetUserAdventureRecord",
			Handler:    _AdventureActivity_GetUserAdventureRecord_Handler,
		},
		{
			MethodName: "SetLevelConf",
			Handler:    _AdventureActivity_SetLevelConf_Handler,
		},
		{
			MethodName: "SetLevelPrizePool",
			Handler:    _AdventureActivity_SetLevelPrizePool_Handler,
		},
		{
			MethodName: "GetLevelConfWithPool",
			Handler:    _AdventureActivity_GetLevelConfWithPool_Handler,
		},
		{
			MethodName: "SetAdventureIslandLimitConf",
			Handler:    _AdventureActivity_SetAdventureIslandLimitConf_Handler,
		},
		{
			MethodName: "GetAdventureIslandLimitConf",
			Handler:    _AdventureActivity_GetAdventureIslandLimitConf_Handler,
		},
		{
			MethodName: "GetTBeanTotalCount",
			Handler:    _AdventureActivity_GetTBeanTotalCount_Handler,
		},
		{
			MethodName: "GetTBeanOrderIds",
			Handler:    _AdventureActivity_GetTBeanOrderIds_Handler,
		},
		{
			MethodName: "GetAwardTotalCount",
			Handler:    _AdventureActivity_GetAwardTotalCount_Handler,
		},
		{
			MethodName: "GetAwardOrderIds",
			Handler:    _AdventureActivity_GetAwardOrderIds_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/adventure-activity/adventure-activity.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/adventure-activity/adventure-activity.proto", fileDescriptor_adventure_activity_06afd5ae92173300)
}

var fileDescriptor_adventure_activity_06afd5ae92173300 = []byte{
	// 2036 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x59, 0x5f, 0x73, 0xdb, 0x58,
	0x15, 0x5f, 0xd9, 0x69, 0x13, 0x9f, 0xfc, 0xa9, 0x73, 0x93, 0xa6, 0x8e, 0xdb, 0x26, 0x41, 0x6c,
	0x21, 0x94, 0xad, 0xb3, 0xa4, 0x2d, 0x03, 0x1d, 0x86, 0xc5, 0xb1, 0x5d, 0xaf, 0x77, 0x43, 0xe2,
	0x51, 0x9c, 0x66, 0x28, 0xb3, 0x23, 0x64, 0xe9, 0xda, 0xd1, 0x54, 0xbe, 0x52, 0xa5, 0xab, 0xc4,
	0x5e, 0x9e, 0x76, 0x1f, 0xf8, 0x33, 0xc3, 0x67, 0xe0, 0x89, 0x19, 0x3e, 0x02, 0x0f, 0x3c, 0x30,
	0xf0, 0x0c, 0xcf, 0x7c, 0x01, 0x3e, 0x05, 0x4f, 0xcc, 0xfd, 0x23, 0x59, 0x4a, 0x64, 0x27, 0x4d,
	0x61, 0x86, 0xa7, 0xf8, 0x9e, 0x73, 0xef, 0xf9, 0xf3, 0x3b, 0xe7, 0x9e, 0x73, 0x74, 0x03, 0x2f,
	0x28, 0xdd, 0x79, 0x1b, 0xda, 0xe6, 0x9b, 0xc0, 0x76, 0xce, 0xb0, 0xbf, 0x63, 0x58, 0x67, 0x98,
	0xd0, 0xd0, 0xc7, 0x4f, 0x0c, 0x93, 0xda, 0x67, 0x36, 0x1d, 0x65, 0x90, 0x2a, 0x9e, 0xef, 0x52,
	0x17, 0xa1, 0x98, 0xa3, 0x47, 0x9c, 0x72, 0xe5, 0x82, 0x3c, 0x3c, 0xa4, 0x98, 0x04, 0xb6, 0x4b,
	0x76, 0x5c, 0x8f, 0xda, 0x2e, 0x09, 0xa2, 0xbf, 0x42, 0xc6, 0xa5, 0xfd, 0x3e, 0x36, 0x5d, 0x62,
	0xda, 0x0e, 0x7e, 0x72, 0xb6, 0x9b, 0x5a, 0x88, 0xfd, 0xea, 0x3f, 0x72, 0x50, 0xa8, 0x9e, 0x1b,
	0xbe, 0xd5, 0x22, 0x3d, 0x17, 0xad, 0xc3, 0x9c, 0xc1, 0x16, 0xba, 0x6d, 0x95, 0x94, 0x2d, 0x65,
	0xbb, 0xa0, 0xcd, 0xf2, 0x75, 0xcb, 0x42, 0x0f, 0x01, 0x04, 0x8b, 0x8e, 0x3c, 0x5c, 0xca, 0x6d,
	0x29, 0xdb, 0x8b, 0x5a, 0x81, 0x53, 0x3a, 0x23, 0x0f, 0xa3, 0x0f, 0x61, 0xc9, 0xf2, 0x71, 0x10,
	0xe8, 0x41, 0xd8, 0x15, 0x5b, 0xf2, 0x7c, 0xcb, 0x02, 0xa7, 0x1e, 0x85, 0x5d, 0xbe, 0x6b, 0x0d,
	0x6e, 0x1b, 0x03, 0x37, 0x24, 0xb4, 0x34, 0xc3, 0xb9, 0x72, 0x85, 0x36, 0x61, 0x5e, 0x08, 0x3f,
	0x77, 0x7d, 0x7a, 0x5a, 0xba, 0xc5, 0x99, 0x42, 0xdf, 0x09, 0xa3, 0x8c, 0xb5, 0x13, 0x63, 0x80,
	0x4b, 0xb7, 0xb9, 0x69, 0x42, 0xfb, 0x81, 0x31, 0xc0, 0x63, 0xb6, 0x85, 0x03, 0xb3, 0x34, 0x9b,
	0x60, 0xd7, 0x71, 0x60, 0x8e, 0xd9, 0xb6, 0xe9, 0x92, 0xd2, 0x5c, 0x82, 0xdd, 0x32, 0x5d, 0xc2,
	0xbc, 0xc6, 0x43, 0x4f, 0xa7, 0xf6, 0x00, 0x97, 0x0a, 0x5b, 0xca, 0x76, 0x5e, 0x9b, 0xc5, 0x43,
	0xaf, 0x63, 0x0f, 0xb8, 0xc1, 0xe7, 0xd8, 0xee, 0x9f, 0xd2, 0x12, 0x08, 0x83, 0xc5, 0x0a, 0xdd,
	0x83, 0x59, 0x53, 0xe2, 0x34, 0x2f, 0x18, 0x26, 0x87, 0x49, 0xfd, 0x83, 0x02, 0x73, 0xfb, 0xf8,
	0x0c, 0x3b, 0xb5, 0x5e, 0x9f, 0x09, 0x76, 0xd8, 0xef, 0x08, 0xce, 0x45, 0x6d, 0x96, 0xaf, 0x05,
	0x9c, 0x82, 0xc5, 0x1d, 0xca, 0x09, 0x93, 0x38, 0x85, 0x3b, 0xb4, 0x01, 0xf3, 0x03, 0x63, 0xa8,
	0x13, 0xbd, 0x67, 0x0f, 0xb1, 0x25, 0xb1, 0x2c, 0x0c, 0x8c, 0xe1, 0xc1, 0x4b, 0x46, 0x40, 0x3f,
	0x86, 0x79, 0x71, 0x9c, 0x7b, 0xc1, 0x01, 0x9b, 0xdf, 0x7d, 0x58, 0xb9, 0x9c, 0x40, 0x95, 0x38,
	0xb8, 0x9a, 0x50, 0xc8, 0xd7, 0xea, 0xef, 0x14, 0x58, 0x16, 0x66, 0xba, 0xa4, 0x77, 0x62, 0xd3,
	0xd3, 0xb6, 0xeb, 0x3a, 0xe8, 0x87, 0x20, 0x4c, 0xd0, 0xcd, 0x5e, 0x9f, 0x1b, 0x3c, 0xbf, 0xfb,
	0x20, 0x4b, 0x66, 0xe4, 0xa0, 0x26, 0xdc, 0x63, 0xae, 0xfe, 0x28, 0x82, 0xd8, 0xb1, 0x03, 0x5a,
	0xca, 0x6d, 0xe5, 0xaf, 0xb6, 0x47, 0x44, 0x60, 0xdf, 0x0e, 0xa8, 0x6a, 0x41, 0xa1, 0xed, 0xdb,
	0x5f, 0x62, 0x6e, 0xc5, 0x14, 0xd4, 0xde, 0x4f, 0xcb, 0xcf, 0x61, 0xe5, 0x08, 0xd3, 0xd8, 0x6d,
	0x0d, 0xbf, 0x0d, 0x71, 0x40, 0x51, 0x1d, 0xee, 0x48, 0xaf, 0x5d, 0xd2, 0x13, 0x92, 0x15, 0x2e,
	0x79, 0xba, 0xef, 0x8b, 0x4e, 0x24, 0x88, 0x0b, 0xff, 0x18, 0x56, 0xd3, 0xc2, 0x03, 0xcf, 0x25,
	0x01, 0x46, 0x25, 0x98, 0x0d, 0x42, 0xd3, 0xc4, 0x41, 0xc0, 0x9d, 0x99, 0xd3, 0xa2, 0xa5, 0x6a,
	0x40, 0x29, 0x3a, 0x11, 0x3b, 0x1f, 0xd9, 0xd4, 0x80, 0x3b, 0x1e, 0xa3, 0xe9, 0x9e, 0xeb, 0x3a,
	0x49, 0x9b, 0x32, 0xbd, 0x1d, 0x1f, 0x5f, 0xf4, 0xa2, 0x9f, 0xdc, 0xa8, 0xe7, 0xb0, 0x9e, 0xa1,
	0xe2, 0x4a, 0xcb, 0x1e, 0xc2, 0xfd, 0x66, 0xc2, 0x97, 0x28, 0x3f, 0xa4, 0x71, 0xaa, 0x05, 0x0f,
	0xb2, 0xd9, 0x52, 0x70, 0x3d, 0xca, 0xed, 0x84, 0xdd, 0x8f, 0x26, 0x63, 0x99, 0x14, 0x21, 0xf2,
	0x8f, 0xdb, 0xfe, 0x57, 0x05, 0x36, 0x8e, 0x30, 0xad, 0x46, 0xc7, 0x5a, 0x81, 0x63, 0x10, 0x6b,
	0xdf, 0x1e, 0xd8, 0x54, 0x46, 0x0f, 0x3d, 0x85, 0x35, 0xcb, 0xb0, 0x9d, 0x91, 0xde, 0x0d, 0x47,
	0xba, 0x79, 0x6a, 0x10, 0x13, 0xeb, 0x0e, 0xdb, 0x20, 0xf3, 0x66, 0x85, 0x73, 0xf7, 0xc2, 0x51,
	0x8d, 0xf3, 0xf8, 0xd9, 0xf1, 0xa1, 0x30, 0xc0, 0xe9, 0x43, 0xb9, 0xc4, 0xa1, 0xe3, 0x00, 0x27,
	0x0f, 0x3d, 0x87, 0x7b, 0x81, 0x4d, 0xfa, 0x0e, 0xbe, 0xac, 0x4a, 0xdc, 0xcd, 0x55, 0xc1, 0x4e,
	0xeb, 0x52, 0xbf, 0x01, 0x9b, 0x53, 0x5d, 0x08, 0x3c, 0x75, 0x0b, 0x36, 0x9a, 0x53, 0xbd, 0x54,
	0xff, 0xa6, 0xc0, 0x66, 0x73, 0xba, 0x94, 0xff, 0x7f, 0x24, 0x1c, 0x58, 0xe8, 0xb8, 0xde, 0x01,
	0xbf, 0x97, 0xac, 0x5e, 0x6c, 0xc0, 0x3c, 0x75, 0x3d, 0x9d, 0xa4, 0xac, 0x2c, 0x50, 0xd7, 0x3b,
	0x10, 0x6a, 0xde, 0xef, 0xa6, 0xff, 0x36, 0x07, 0x0b, 0xc7, 0x01, 0xf6, 0xdb, 0x8e, 0x31, 0x7a,
	0x69, 0x3b, 0x38, 0x55, 0x53, 0x72, 0xe9, 0x9a, 0xf2, 0x08, 0x96, 0xcc, 0xd0, 0xf7, 0x31, 0xa1,
	0xd2, 0x1b, 0xd9, 0x7e, 0x16, 0x25, 0x55, 0x78, 0x81, 0xee, 0xc2, 0xed, 0x30, 0xc0, 0xbe, 0x4e,
	0x78, 0xf7, 0x59, 0xd4, 0x6e, 0xb1, 0xd5, 0x01, 0xfa, 0x02, 0xee, 0xf0, 0x46, 0x60, 0xba, 0x8e,
	0x83, 0x4d, 0xd6, 0x89, 0x4b, 0xb3, 0xdc, 0xd8, 0x67, 0x59, 0xc6, 0x26, 0x6d, 0xaa, 0xd4, 0x18,
	0x14, 0xf1, 0xb1, 0x06, 0xa1, 0xfe, 0x48, 0x5b, 0x32, 0x53, 0xc4, 0x72, 0x15, 0x56, 0x32, 0xb6,
	0xa1, 0x22, 0xe4, 0xdf, 0xe0, 0x91, 0x6c, 0xd1, 0xec, 0x27, 0x5a, 0x85, 0x5b, 0x67, 0x86, 0x13,
	0x46, 0x9d, 0x59, 0x2c, 0x5e, 0xe4, 0x7e, 0xa0, 0xa8, 0x2d, 0x58, 0x6b, 0x62, 0x5a, 0x0b, 0x7d,
	0xbf, 0x69, 0x0c, 0x30, 0x47, 0x4a, 0x16, 0x99, 0x22, 0xe4, 0xc3, 0xb8, 0xc6, 0xb2, 0x9f, 0xac,
	0x2b, 0x31, 0x0c, 0x48, 0x12, 0xa8, 0x82, 0xa4, 0xb4, 0x2c, 0xf5, 0xcf, 0x0a, 0xc0, 0x5e, 0x38,
	0x6a, 0xfb, 0xae, 0xc7, 0x62, 0xf8, 0x02, 0x0a, 0x7d, 0xbb, 0x47, 0x75, 0x9b, 0xf4, 0x5c, 0xd9,
	0x2e, 0xae, 0x08, 0xd1, 0x1c, 0xdb, 0xcf, 0x27, 0x8d, 0x8f, 0x00, 0xb1, 0xfc, 0x11, 0xfd, 0x5f,
	0x97, 0x33, 0x4c, 0x29, 0xbf, 0x95, 0xdf, 0x5e, 0xd4, 0x8a, 0xdd, 0x70, 0x54, 0xe5, 0x8c, 0x43,
	0x41, 0x67, 0x76, 0x85, 0xc4, 0xa6, 0xba, 0xe7, 0xdb, 0x26, 0x96, 0xb3, 0x43, 0x81, 0x51, 0xda,
	0x8c, 0xc0, 0x86, 0x0f, 0x26, 0x8c, 0xa7, 0x92, 0x18, 0x01, 0x6e, 0x71, 0x64, 0x16, 0xba, 0xe1,
	0x88, 0xa7, 0x13, 0x9b, 0x02, 0xd4, 0xaf, 0x66, 0xe0, 0xde, 0x25, 0x24, 0x64, 0xc9, 0x62, 0x03,
	0x88, 0x34, 0x77, 0xdc, 0x76, 0x20, 0x22, 0xb5, 0x2c, 0xf4, 0x4d, 0x58, 0x8c, 0x37, 0x24, 0x5a,
	0xf6, 0x42, 0x44, 0x8c, 0xc6, 0x90, 0x2e, 0xee, 0xdb, 0x44, 0x8c, 0x12, 0x79, 0x3e, 0x4a, 0x14,
	0x38, 0x85, 0x0f, 0x13, 0x6c, 0xce, 0x20, 0x96, 0x60, 0xce, 0xc8, 0x39, 0x83, 0x58, 0x9c, 0xf5,
	0x13, 0x60, 0xb6, 0xea, 0x9e, 0xef, 0x7a, 0xbc, 0xf9, 0x8a, 0x86, 0xbe, 0x91, 0x85, 0xe6, 0x38,
	0x00, 0x1a, 0x74, 0xc7, 0xc1, 0x48, 0x17, 0xdd, 0xdb, 0x37, 0x2b, 0xba, 0xe8, 0x25, 0x2c, 0xf1,
	0x2c, 0xf7, 0x1c, 0x63, 0xa4, 0xf7, 0x6c, 0x07, 0xf3, 0x61, 0x6a, 0x7e, 0x77, 0xeb, 0xaa, 0x6c,
	0xd6, 0x16, 0xc2, 0xe4, 0x7d, 0x6b, 0x41, 0x51, 0x5c, 0x6f, 0x39, 0x77, 0xb1, 0x0c, 0x99, 0x9b,
	0x2c, 0x29, 0x59, 0x1a, 0xb4, 0x45, 0x1a, 0xad, 0xa2, 0x4c, 0x21, 0xae, 0xee, 0x61, 0x7f, 0x60,
	0x07, 0x6c, 0xf0, 0xd5, 0x29, 0x1e, 0x52, 0x3e, 0xa7, 0x15, 0xb4, 0x22, 0x71, 0xdb, 0x31, 0xa3,
	0x83, 0x87, 0x94, 0x85, 0xe0, 0xd4, 0x08, 0x74, 0x43, 0xf4, 0x35, 0xe0, 0x7d, 0xad, 0x70, 0x6a,
	0x04, 0x55, 0xd1, 0xd9, 0xbe, 0x84, 0x62, 0x5c, 0x98, 0x6e, 0x7a, 0x0d, 0x58, 0x2e, 0xc8, 0xba,
	0x27, 0x87, 0x59, 0x39, 0xea, 0x0a, 0xa2, 0xc8, 0x5c, 0x26, 0xb5, 0x87, 0xa3, 0x5c, 0x65, 0x3f,
	0xd5, 0x2f, 0x60, 0x39, 0xa1, 0x5b, 0x26, 0x5e, 0x05, 0x56, 0x7a, 0x36, 0x31, 0x1c, 0x3d, 0x2d,
	0x51, 0x18, 0xb3, 0xcc, 0x59, 0xb5, 0xa4, 0xd8, 0x12, 0xcc, 0x76, 0x0d, 0x87, 0x97, 0x29, 0x66,
	0xd7, 0x8c, 0x16, 0x2d, 0xd5, 0x5f, 0x29, 0x00, 0x75, 0xdf, 0x38, 0xd7, 0x70, 0x10, 0x3a, 0xf4,
	0x7f, 0x36, 0x45, 0xb1, 0xab, 0xe2, 0x73, 0x15, 0xc9, 0x31, 0x1f, 0x04, 0x89, 0x0d, 0xf9, 0xea,
	0x10, 0xd0, 0xbe, 0x4b, 0x29, 0xf6, 0x47, 0xc2, 0x9c, 0x1b, 0xa2, 0x9c, 0x74, 0x20, 0x9f, 0x76,
	0x60, 0xc2, 0x67, 0x84, 0xfa, 0x7b, 0x05, 0x56, 0x52, 0xaa, 0x25, 0xc8, 0x9f, 0xc4, 0x26, 0x27,
	0x26, 0x92, 0xcc, 0xcb, 0x35, 0x06, 0x30, 0x72, 0x69, 0xc2, 0xb5, 0xc8, 0xdd, 0xe4, 0x5a, 0xa8,
	0x7f, 0x51, 0xe0, 0x6e, 0xdb, 0x31, 0x68, 0xcf, 0xf5, 0x07, 0x27, 0x36, 0x21, 0x36, 0xe9, 0x6b,
	0xd8, 0x74, 0x7d, 0x2b, 0x03, 0x9e, 0x32, 0xcc, 0x11, 0xdb, 0x7c, 0x93, 0x28, 0x36, 0xf1, 0x7a,
	0x1a, 0x36, 0x71, 0x70, 0xf9, 0x9d, 0x9b, 0xb9, 0x4e, 0x55, 0x96, 0x9f, 0x42, 0xec, 0xb2, 0x5d,
	0x08, 0xee, 0xad, 0x4b, 0xc1, 0x7d, 0xcd, 0x67, 0x91, 0x4c, 0x1f, 0xa2, 0x48, 0xa7, 0xe3, 0xaa,
	0x5c, 0x8c, 0x6b, 0x09, 0x66, 0xcf, 0xb0, 0xcf, 0x2e, 0xac, 0x74, 0x2b, 0x5a, 0xaa, 0xff, 0x54,
	0x60, 0x6b, 0xb2, 0xf0, 0x71, 0xa5, 0x26, 0xf8, 0x5c, 0x8f, 0x44, 0x88, 0x16, 0x08, 0x04, 0x9f,
	0xbf, 0x12, 0x14, 0xf4, 0x19, 0x73, 0x81, 0x1d, 0x49, 0xa6, 0xf7, 0x77, 0x32, 0xc7, 0xe6, 0x4c,
	0x45, 0x20, 0x4e, 0xf3, 0xb8, 0x3f, 0x00, 0x10, 0x65, 0x2c, 0x0c, 0xe2, 0xaf, 0xb0, 0x39, 0x56,
	0x9e, 0x8e, 0x03, 0x6c, 0xa1, 0x6d, 0x28, 0xfa, 0xf8, 0xad, 0x6e, 0x85, 0xbe, 0xc1, 0xda, 0x94,
	0x1e, 0x60, 0x53, 0x26, 0xe4, 0x92, 0x8f, 0xdf, 0xd6, 0x25, 0xf9, 0x08, 0x9b, 0xea, 0xbf, 0x14,
	0x78, 0xd8, 0xc4, 0x94, 0x65, 0x46, 0x3c, 0xc6, 0xa5, 0x41, 0xdb, 0x8c, 0xad, 0xe6, 0xc0, 0x2b,
	0x11, 0xf0, 0x8c, 0x14, 0x7d, 0x3a, 0xbb, 0xbd, 0x5e, 0x80, 0xa9, 0x44, 0x4d, 0xae, 0x58, 0xe3,
	0x4f, 0x4e, 0x5f, 0x62, 0x11, 0xa5, 0xd3, 0x4c, 0x9c, 0x4e, 0x6a, 0x17, 0x40, 0x1b, 0x4b, 0xbb,
	0x0f, 0xf7, 0xb4, 0x46, 0xed, 0x50, 0xab, 0xeb, 0x9d, 0x9f, 0xb5, 0x1b, 0xfa, 0xf1, 0xc1, 0x51,
	0xbb, 0x51, 0x6b, 0xbd, 0x6c, 0x35, 0xea, 0xc5, 0x0f, 0xd0, 0x03, 0x28, 0x25, 0x99, 0xb5, 0xaa,
	0x56, 0xd7, 0xf7, 0x5b, 0xcd, 0x4f, 0x3b, 0x8d, 0x7a, 0x31, 0x87, 0xd6, 0xe1, 0x6e, 0x8a, 0x7b,
	0xf8, 0xd3, 0xf6, 0x7e, 0x83, 0xb1, 0xf2, 0xea, 0xd7, 0x0a, 0x1f, 0x66, 0x33, 0xdd, 0x4c, 0x86,
	0x6f, 0x48, 0x75, 0xe9, 0x4b, 0x1c, 0xbe, 0x21, 0x3d, 0x14, 0xfe, 0x7c, 0x92, 0x15, 0xbe, 0x6b,
	0xdc, 0xd5, 0x28, 0x66, 0x8f, 0x8f, 0xe5, 0x83, 0x06, 0xf7, 0xb3, 0x0c, 0x6b, 0xd5, 0x93, 0x6a,
	0xb6, 0x9b, 0x6b, 0x80, 0x12, 0xbc, 0x76, 0xb5, 0xf6, 0x79, 0xb5, 0xd9, 0x28, 0x2a, 0x68, 0x15,
	0x8a, 0x09, 0x7a, 0x5d, 0x6b, 0x1c, 0x1d, 0x15, 0x73, 0x8f, 0xff, 0xa8, 0xc0, 0xd2, 0x58, 0x63,
	0x04, 0x62, 0x5d, 0xab, 0x9e, 0xe8, 0x5a, 0xe3, 0xe8, 0x78, 0xbf, 0x73, 0x19, 0xc4, 0x24, 0x33,
	0x05, 0xa2, 0x82, 0x36, 0xe1, 0x7e, 0x92, 0xbb, 0xdf, 0x78, 0xd5, 0xd8, 0x4f, 0x40, 0x99, 0xbb,
	0x78, 0xbc, 0xdd, 0xa8, 0x7e, 0xae, 0x6b, 0x8d, 0x6a, 0xed, 0x53, 0x06, 0xf4, 0x45, 0xcd, 0x9d,
	0xc3, 0xb6, 0x7e, 0xa0, 0xef, 0x1d, 0x1e, 0x1f, 0x1c, 0x15, 0x67, 0x76, 0xbf, 0x5e, 0x80, 0xe5,
	0x18, 0xfe, 0xaa, 0x44, 0x0b, 0x39, 0x70, 0xe7, 0xc2, 0xf0, 0x83, 0x1e, 0x67, 0xa1, 0x9a, 0x3d,
	0x2b, 0x96, 0xbf, 0x7b, 0xad, 0xbd, 0x22, 0xc8, 0xea, 0x07, 0xe8, 0x35, 0x14, 0xe2, 0x5e, 0x87,
	0x3e, 0x9c, 0x30, 0xc6, 0xa4, 0xda, 0x70, 0xf9, 0xd1, 0x15, 0xbb, 0x62, 0xd9, 0xbf, 0x80, 0xf9,
	0x44, 0x91, 0x47, 0xdf, 0xca, 0x1c, 0x72, 0x2e, 0x35, 0xa0, 0xf2, 0xb7, 0xaf, 0xdc, 0x17, 0x6b,
	0xf8, 0xb5, 0x02, 0xa5, 0x49, 0x85, 0x08, 0x3d, 0x9d, 0x80, 0xc4, 0xb4, 0x9a, 0x58, 0x7e, 0xf6,
	0x6e, 0x87, 0x62, 0x4b, 0xbe, 0x52, 0xf8, 0xf4, 0x9e, 0x71, 0xa3, 0xd0, 0xf7, 0x26, 0x88, 0x9c,
	0x5c, 0x64, 0xca, 0xbb, 0xef, 0x72, 0x24, 0xb6, 0xc1, 0x84, 0x85, 0xe4, 0xcb, 0x06, 0xca, 0x04,
	0x32, 0xe3, 0x61, 0xa5, 0xbc, 0x7d, 0xf5, 0xc6, 0x58, 0x89, 0x0f, 0xcb, 0x97, 0x5e, 0x2a, 0xd0,
	0x47, 0xd3, 0x04, 0x5c, 0x7c, 0x33, 0x29, 0x3f, 0xb9, 0xe6, 0xee, 0x58, 0xe7, 0x2f, 0x61, 0x35,
	0xeb, 0x1d, 0x03, 0xed, 0x4c, 0x80, 0x69, 0xd2, 0x83, 0x48, 0xf9, 0xe3, 0xeb, 0x1f, 0x88, 0x95,
	0xff, 0x46, 0x81, 0xfb, 0x53, 0xde, 0x06, 0xd0, 0xee, 0x04, 0x6f, 0xa6, 0xbc, 0x14, 0x94, 0x9f,
	0xbe, 0xf3, 0x99, 0xc0, 0x93, 0xa6, 0x34, 0xdf, 0xd5, 0x94, 0xe6, 0x0d, 0x4c, 0x69, 0x5e, 0x69,
	0x4a, 0x0b, 0x50, 0x13, 0xd3, 0xce, 0x1e, 0x36, 0x48, 0xc7, 0xa5, 0x86, 0x53, 0xe3, 0x43, 0xef,
	0x7a, 0x45, 0x8b, 0x5e, 0xae, 0x5f, 0xed, 0x56, 0xd8, 0x07, 0x93, 0x66, 0x90, 0x3e, 0xab, 0x1c,
	0xe5, 0xb5, 0x14, 0x8b, 0x6f, 0x97, 0xa2, 0x3e, 0x83, 0x62, 0x24, 0xea, 0xd0, 0xb7, 0xb0, 0xdf,
	0xb2, 0x82, 0x69, 0x82, 0xd2, 0xac, 0xe8, 0x44, 0xca, 0x2c, 0xd1, 0x56, 0xfe, 0x2b, 0x66, 0x71,
	0x51, 0xef, 0x6b, 0x56, 0x79, 0xfd, 0xdf, 0x7f, 0xfa, 0x7b, 0x67, 0x15, 0xd0, 0xe5, 0xff, 0x28,
	0xec, 0x7d, 0xff, 0xf5, 0xb3, 0xbe, 0xeb, 0x18, 0xa4, 0x5f, 0x79, 0xbe, 0x4b, 0x69, 0xc5, 0x74,
	0x07, 0x3b, 0xfc, 0xc1, 0xdf, 0x74, 0x9d, 0x9d, 0x00, 0xfb, 0x67, 0xb6, 0x89, 0x83, 0x8c, 0xff,
	0x44, 0x74, 0x6f, 0xf3, 0x5d, 0x4f, 0xff, 0x13, 0x00, 0x00, 0xff, 0xff, 0xf6, 0x78, 0xcb, 0x31,
	0xc8, 0x18, 0x00, 0x00,
}
