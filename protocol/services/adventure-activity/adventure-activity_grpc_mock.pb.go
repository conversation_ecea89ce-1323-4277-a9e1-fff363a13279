// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/adventure-activity/adventure-activity.proto

package adventure_activity

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	grpc "google.golang.org/grpc"
)

// MockAdventureActivityClient is a mock of AdventureActivityClient interface.
type MockAdventureActivityClient struct {
	ctrl     *gomock.Controller
	recorder *MockAdventureActivityClientMockRecorder
}

// MockAdventureActivityClientMockRecorder is the mock recorder for MockAdventureActivityClient.
type MockAdventureActivityClientMockRecorder struct {
	mock *MockAdventureActivityClient
}

// NewMockAdventureActivityClient creates a new mock instance.
func NewMockAdventureActivityClient(ctrl *gomock.Controller) *MockAdventureActivityClient {
	mock := &MockAdventureActivityClient{ctrl: ctrl}
	mock.recorder = &MockAdventureActivityClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAdventureActivityClient) EXPECT() *MockAdventureActivityClientMockRecorder {
	return m.recorder
}

// BuyChance mocks base method.
func (m *MockAdventureActivityClient) BuyChance(ctx context.Context, in *BuyChanceRequest, opts ...grpc.CallOption) (*BuyChanceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BuyChance", varargs...)
	ret0, _ := ret[0].(*BuyChanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BuyChance indicates an expected call of BuyChance.
func (mr *MockAdventureActivityClientMockRecorder) BuyChance(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BuyChance", reflect.TypeOf((*MockAdventureActivityClient)(nil).BuyChance), varargs...)
}

// GetAdventureIslandLimitConf mocks base method.
func (m *MockAdventureActivityClient) GetAdventureIslandLimitConf(ctx context.Context, in *GetAdventureIslandLimitConfReq, opts ...grpc.CallOption) (*GetAdventureIslandLimitConfResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAdventureIslandLimitConf", varargs...)
	ret0, _ := ret[0].(*GetAdventureIslandLimitConfResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAdventureIslandLimitConf indicates an expected call of GetAdventureIslandLimitConf.
func (mr *MockAdventureActivityClientMockRecorder) GetAdventureIslandLimitConf(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAdventureIslandLimitConf", reflect.TypeOf((*MockAdventureActivityClient)(nil).GetAdventureIslandLimitConf), varargs...)
}

// GetAwardOrderIds mocks base method.
func (m *MockAdventureActivityClient) GetAwardOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAwardOrderIds", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardOrderIds indicates an expected call of GetAwardOrderIds.
func (mr *MockAdventureActivityClientMockRecorder) GetAwardOrderIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardOrderIds", reflect.TypeOf((*MockAdventureActivityClient)(nil).GetAwardOrderIds), varargs...)
}

// GetAwardTotalCount mocks base method.
func (m *MockAdventureActivityClient) GetAwardTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAwardTotalCount", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardTotalCount indicates an expected call of GetAwardTotalCount.
func (mr *MockAdventureActivityClientMockRecorder) GetAwardTotalCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardTotalCount", reflect.TypeOf((*MockAdventureActivityClient)(nil).GetAwardTotalCount), varargs...)
}

// GetCurrGameInfo mocks base method.
func (m *MockAdventureActivityClient) GetCurrGameInfo(ctx context.Context, in *GetCurrGameInfoRequest, opts ...grpc.CallOption) (*GetCurrGameInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCurrGameInfo", varargs...)
	ret0, _ := ret[0].(*GetCurrGameInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCurrGameInfo indicates an expected call of GetCurrGameInfo.
func (mr *MockAdventureActivityClientMockRecorder) GetCurrGameInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurrGameInfo", reflect.TypeOf((*MockAdventureActivityClient)(nil).GetCurrGameInfo), varargs...)
}

// GetLevelConfWithPool mocks base method.
func (m *MockAdventureActivityClient) GetLevelConfWithPool(ctx context.Context, in *GetLevelConfWithPoolRequest, opts ...grpc.CallOption) (*GetLevelConfWithPoolResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLevelConfWithPool", varargs...)
	ret0, _ := ret[0].(*GetLevelConfWithPoolResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLevelConfWithPool indicates an expected call of GetLevelConfWithPool.
func (mr *MockAdventureActivityClientMockRecorder) GetLevelConfWithPool(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLevelConfWithPool", reflect.TypeOf((*MockAdventureActivityClient)(nil).GetLevelConfWithPool), varargs...)
}

// GetPlatformWinningRecord mocks base method.
func (m *MockAdventureActivityClient) GetPlatformWinningRecord(ctx context.Context, in *GetPlatformWinningRecordRequest, opts ...grpc.CallOption) (*GetPlatformWinningRecordResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPlatformWinningRecord", varargs...)
	ret0, _ := ret[0].(*GetPlatformWinningRecordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPlatformWinningRecord indicates an expected call of GetPlatformWinningRecord.
func (mr *MockAdventureActivityClientMockRecorder) GetPlatformWinningRecord(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlatformWinningRecord", reflect.TypeOf((*MockAdventureActivityClient)(nil).GetPlatformWinningRecord), varargs...)
}

// GetTBeanOrderIds mocks base method.
func (m *MockAdventureActivityClient) GetTBeanOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTBeanOrderIds", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTBeanOrderIds indicates an expected call of GetTBeanOrderIds.
func (mr *MockAdventureActivityClientMockRecorder) GetTBeanOrderIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTBeanOrderIds", reflect.TypeOf((*MockAdventureActivityClient)(nil).GetTBeanOrderIds), varargs...)
}

// GetTBeanTotalCount mocks base method.
func (m *MockAdventureActivityClient) GetTBeanTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTBeanTotalCount", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTBeanTotalCount indicates an expected call of GetTBeanTotalCount.
func (mr *MockAdventureActivityClientMockRecorder) GetTBeanTotalCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTBeanTotalCount", reflect.TypeOf((*MockAdventureActivityClient)(nil).GetTBeanTotalCount), varargs...)
}

// GetUserAdventureRecord mocks base method.
func (m *MockAdventureActivityClient) GetUserAdventureRecord(ctx context.Context, in *GetUserAdventureRecordRequest, opts ...grpc.CallOption) (*GetUserAdventureRecordResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserAdventureRecord", varargs...)
	ret0, _ := ret[0].(*GetUserAdventureRecordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAdventureRecord indicates an expected call of GetUserAdventureRecord.
func (mr *MockAdventureActivityClientMockRecorder) GetUserAdventureRecord(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAdventureRecord", reflect.TypeOf((*MockAdventureActivityClient)(nil).GetUserAdventureRecord), varargs...)
}

// LotteryDraw mocks base method.
func (m *MockAdventureActivityClient) LotteryDraw(ctx context.Context, in *LotteryDrawRequest, opts ...grpc.CallOption) (*LotteryDrawResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LotteryDraw", varargs...)
	ret0, _ := ret[0].(*LotteryDrawResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LotteryDraw indicates an expected call of LotteryDraw.
func (mr *MockAdventureActivityClientMockRecorder) LotteryDraw(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LotteryDraw", reflect.TypeOf((*MockAdventureActivityClient)(nil).LotteryDraw), varargs...)
}

// SetAdventureIslandLimitConf mocks base method.
func (m *MockAdventureActivityClient) SetAdventureIslandLimitConf(ctx context.Context, in *SetAdventureIslandLimitConfReq, opts ...grpc.CallOption) (*SetAdventureIslandLimitConfResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetAdventureIslandLimitConf", varargs...)
	ret0, _ := ret[0].(*SetAdventureIslandLimitConfResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetAdventureIslandLimitConf indicates an expected call of SetAdventureIslandLimitConf.
func (mr *MockAdventureActivityClientMockRecorder) SetAdventureIslandLimitConf(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAdventureIslandLimitConf", reflect.TypeOf((*MockAdventureActivityClient)(nil).SetAdventureIslandLimitConf), varargs...)
}

// SetLevelConf mocks base method.
func (m *MockAdventureActivityClient) SetLevelConf(ctx context.Context, in *SetLevelConfRequest, opts ...grpc.CallOption) (*SetLevelConfResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetLevelConf", varargs...)
	ret0, _ := ret[0].(*SetLevelConfResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetLevelConf indicates an expected call of SetLevelConf.
func (mr *MockAdventureActivityClientMockRecorder) SetLevelConf(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetLevelConf", reflect.TypeOf((*MockAdventureActivityClient)(nil).SetLevelConf), varargs...)
}

// SetLevelPrizePool mocks base method.
func (m *MockAdventureActivityClient) SetLevelPrizePool(ctx context.Context, in *SetLevelPrizePoolRequest, opts ...grpc.CallOption) (*SetLevelPrizePoolResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetLevelPrizePool", varargs...)
	ret0, _ := ret[0].(*SetLevelPrizePoolResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetLevelPrizePool indicates an expected call of SetLevelPrizePool.
func (mr *MockAdventureActivityClientMockRecorder) SetLevelPrizePool(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetLevelPrizePool", reflect.TypeOf((*MockAdventureActivityClient)(nil).SetLevelPrizePool), varargs...)
}

// MockAdventureActivityServer is a mock of AdventureActivityServer interface.
type MockAdventureActivityServer struct {
	ctrl     *gomock.Controller
	recorder *MockAdventureActivityServerMockRecorder
}

// MockAdventureActivityServerMockRecorder is the mock recorder for MockAdventureActivityServer.
type MockAdventureActivityServerMockRecorder struct {
	mock *MockAdventureActivityServer
}

// NewMockAdventureActivityServer creates a new mock instance.
func NewMockAdventureActivityServer(ctrl *gomock.Controller) *MockAdventureActivityServer {
	mock := &MockAdventureActivityServer{ctrl: ctrl}
	mock.recorder = &MockAdventureActivityServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAdventureActivityServer) EXPECT() *MockAdventureActivityServerMockRecorder {
	return m.recorder
}

// BuyChance mocks base method.
func (m *MockAdventureActivityServer) BuyChance(ctx context.Context, in *BuyChanceRequest) (*BuyChanceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BuyChance", ctx, in)
	ret0, _ := ret[0].(*BuyChanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BuyChance indicates an expected call of BuyChance.
func (mr *MockAdventureActivityServerMockRecorder) BuyChance(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BuyChance", reflect.TypeOf((*MockAdventureActivityServer)(nil).BuyChance), ctx, in)
}

// GetAdventureIslandLimitConf mocks base method.
func (m *MockAdventureActivityServer) GetAdventureIslandLimitConf(ctx context.Context, in *GetAdventureIslandLimitConfReq) (*GetAdventureIslandLimitConfResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAdventureIslandLimitConf", ctx, in)
	ret0, _ := ret[0].(*GetAdventureIslandLimitConfResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAdventureIslandLimitConf indicates an expected call of GetAdventureIslandLimitConf.
func (mr *MockAdventureActivityServerMockRecorder) GetAdventureIslandLimitConf(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAdventureIslandLimitConf", reflect.TypeOf((*MockAdventureActivityServer)(nil).GetAdventureIslandLimitConf), ctx, in)
}

// GetAwardOrderIds mocks base method.
func (m *MockAdventureActivityServer) GetAwardOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAwardOrderIds", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardOrderIds indicates an expected call of GetAwardOrderIds.
func (mr *MockAdventureActivityServerMockRecorder) GetAwardOrderIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardOrderIds", reflect.TypeOf((*MockAdventureActivityServer)(nil).GetAwardOrderIds), ctx, in)
}

// GetAwardTotalCount mocks base method.
func (m *MockAdventureActivityServer) GetAwardTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAwardTotalCount", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardTotalCount indicates an expected call of GetAwardTotalCount.
func (mr *MockAdventureActivityServerMockRecorder) GetAwardTotalCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardTotalCount", reflect.TypeOf((*MockAdventureActivityServer)(nil).GetAwardTotalCount), ctx, in)
}

// GetCurrGameInfo mocks base method.
func (m *MockAdventureActivityServer) GetCurrGameInfo(ctx context.Context, in *GetCurrGameInfoRequest) (*GetCurrGameInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCurrGameInfo", ctx, in)
	ret0, _ := ret[0].(*GetCurrGameInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCurrGameInfo indicates an expected call of GetCurrGameInfo.
func (mr *MockAdventureActivityServerMockRecorder) GetCurrGameInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurrGameInfo", reflect.TypeOf((*MockAdventureActivityServer)(nil).GetCurrGameInfo), ctx, in)
}

// GetLevelConfWithPool mocks base method.
func (m *MockAdventureActivityServer) GetLevelConfWithPool(ctx context.Context, in *GetLevelConfWithPoolRequest) (*GetLevelConfWithPoolResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLevelConfWithPool", ctx, in)
	ret0, _ := ret[0].(*GetLevelConfWithPoolResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLevelConfWithPool indicates an expected call of GetLevelConfWithPool.
func (mr *MockAdventureActivityServerMockRecorder) GetLevelConfWithPool(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLevelConfWithPool", reflect.TypeOf((*MockAdventureActivityServer)(nil).GetLevelConfWithPool), ctx, in)
}

// GetPlatformWinningRecord mocks base method.
func (m *MockAdventureActivityServer) GetPlatformWinningRecord(ctx context.Context, in *GetPlatformWinningRecordRequest) (*GetPlatformWinningRecordResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPlatformWinningRecord", ctx, in)
	ret0, _ := ret[0].(*GetPlatformWinningRecordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPlatformWinningRecord indicates an expected call of GetPlatformWinningRecord.
func (mr *MockAdventureActivityServerMockRecorder) GetPlatformWinningRecord(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlatformWinningRecord", reflect.TypeOf((*MockAdventureActivityServer)(nil).GetPlatformWinningRecord), ctx, in)
}

// GetTBeanOrderIds mocks base method.
func (m *MockAdventureActivityServer) GetTBeanOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTBeanOrderIds", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTBeanOrderIds indicates an expected call of GetTBeanOrderIds.
func (mr *MockAdventureActivityServerMockRecorder) GetTBeanOrderIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTBeanOrderIds", reflect.TypeOf((*MockAdventureActivityServer)(nil).GetTBeanOrderIds), ctx, in)
}

// GetTBeanTotalCount mocks base method.
func (m *MockAdventureActivityServer) GetTBeanTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTBeanTotalCount", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTBeanTotalCount indicates an expected call of GetTBeanTotalCount.
func (mr *MockAdventureActivityServerMockRecorder) GetTBeanTotalCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTBeanTotalCount", reflect.TypeOf((*MockAdventureActivityServer)(nil).GetTBeanTotalCount), ctx, in)
}

// GetUserAdventureRecord mocks base method.
func (m *MockAdventureActivityServer) GetUserAdventureRecord(ctx context.Context, in *GetUserAdventureRecordRequest) (*GetUserAdventureRecordResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserAdventureRecord", ctx, in)
	ret0, _ := ret[0].(*GetUserAdventureRecordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAdventureRecord indicates an expected call of GetUserAdventureRecord.
func (mr *MockAdventureActivityServerMockRecorder) GetUserAdventureRecord(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAdventureRecord", reflect.TypeOf((*MockAdventureActivityServer)(nil).GetUserAdventureRecord), ctx, in)
}

// LotteryDraw mocks base method.
func (m *MockAdventureActivityServer) LotteryDraw(ctx context.Context, in *LotteryDrawRequest) (*LotteryDrawResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LotteryDraw", ctx, in)
	ret0, _ := ret[0].(*LotteryDrawResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LotteryDraw indicates an expected call of LotteryDraw.
func (mr *MockAdventureActivityServerMockRecorder) LotteryDraw(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LotteryDraw", reflect.TypeOf((*MockAdventureActivityServer)(nil).LotteryDraw), ctx, in)
}

// SetAdventureIslandLimitConf mocks base method.
func (m *MockAdventureActivityServer) SetAdventureIslandLimitConf(ctx context.Context, in *SetAdventureIslandLimitConfReq) (*SetAdventureIslandLimitConfResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetAdventureIslandLimitConf", ctx, in)
	ret0, _ := ret[0].(*SetAdventureIslandLimitConfResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetAdventureIslandLimitConf indicates an expected call of SetAdventureIslandLimitConf.
func (mr *MockAdventureActivityServerMockRecorder) SetAdventureIslandLimitConf(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAdventureIslandLimitConf", reflect.TypeOf((*MockAdventureActivityServer)(nil).SetAdventureIslandLimitConf), ctx, in)
}

// SetLevelConf mocks base method.
func (m *MockAdventureActivityServer) SetLevelConf(ctx context.Context, in *SetLevelConfRequest) (*SetLevelConfResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetLevelConf", ctx, in)
	ret0, _ := ret[0].(*SetLevelConfResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetLevelConf indicates an expected call of SetLevelConf.
func (mr *MockAdventureActivityServerMockRecorder) SetLevelConf(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetLevelConf", reflect.TypeOf((*MockAdventureActivityServer)(nil).SetLevelConf), ctx, in)
}

// SetLevelPrizePool mocks base method.
func (m *MockAdventureActivityServer) SetLevelPrizePool(ctx context.Context, in *SetLevelPrizePoolRequest) (*SetLevelPrizePoolResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetLevelPrizePool", ctx, in)
	ret0, _ := ret[0].(*SetLevelPrizePoolResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetLevelPrizePool indicates an expected call of SetLevelPrizePool.
func (mr *MockAdventureActivityServerMockRecorder) SetLevelPrizePool(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetLevelPrizePool", reflect.TypeOf((*MockAdventureActivityServer)(nil).SetLevelPrizePool), ctx, in)
}
