package KefuStatusManage

// Code generated by protoc-gen-svrkit-go. DO NOT EDIT.
// source: src/kefuSvr/kefustatusmanage.proto

/*
 This is a generated svrkit golang dev toolkit.
*/

import svrkit "gitlab.ttyuyin.com/golang/svrkit"

import context "golang.org/x/net/context"

import tlvpickle "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

var _ = context.TODO

// Magic Number for KefuStatusManage service
const KefuStatusManageMagic = uint16(14088)

// SvrkitClient API for KefuStatusManage service

type KefuStatusManageClientInterface interface {
	// 设置客服状态
	SetKefuStatus(ctx context.Context, uin uint32, in *SetKefuStatusReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 获取全量客服状态列表
	GetAllKefuStatus(ctx context.Context, uin uint32, in *tlvpickle.SKBuiltinEmpty_PB, opts ...svrkit.CallOption) (*GetAllKefuStatusResp, error)
	// 客服消息全存储
	AddMsg2Db(ctx context.Context, uin uint32, in *AddMsgReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetMsgFromDb(ctx context.Context, uin uint32, in *GetMsgReq, opts ...svrkit.CallOption) (*GetMsgResp, error)
	// 获取用户与客服的绑定关系
	GetRelationUserToKefu(ctx context.Context, uin uint32, in *GetRelationUserToKefuReq, opts ...svrkit.CallOption) (*GetRelationUserToKefuResp, error)
}

type KefuStatusManageSvrkitClient struct {
	cc *svrkit.ClientConn
}

func NewKefuStatusManageSvrkitClient(cc *svrkit.ClientConn) KefuStatusManageClientInterface {
	return &KefuStatusManageSvrkitClient{cc}
}

const (
	commandKefuStatusManageGetSelfSvnInfo        = 9995
	commandKefuStatusManageEcho                  = 9999
	commandKefuStatusManageSetKefuStatus         = 1
	commandKefuStatusManageGetAllKefuStatus      = 2
	commandKefuStatusManageAddMsg2Db             = 3
	commandKefuStatusManageGetMsgFromDb          = 4
	commandKefuStatusManageGetRelationUserToKefu = 5
)

func (c *KefuStatusManageSvrkitClient) SetKefuStatus(ctx context.Context, uin uint32, in *SetKefuStatusReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandKefuStatusManageSetKefuStatus, "/KefuStatusManage.KefuStatusManage/SetKefuStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *KefuStatusManageSvrkitClient) GetAllKefuStatus(ctx context.Context, uin uint32, in *tlvpickle.SKBuiltinEmpty_PB, opts ...svrkit.CallOption) (*GetAllKefuStatusResp, error) {
	out := new(GetAllKefuStatusResp)
	err := c.cc.Invoke(ctx, uin, commandKefuStatusManageGetAllKefuStatus, "/KefuStatusManage.KefuStatusManage/GetAllKefuStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *KefuStatusManageSvrkitClient) AddMsg2Db(ctx context.Context, uin uint32, in *AddMsgReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandKefuStatusManageAddMsg2Db, "/KefuStatusManage.KefuStatusManage/AddMsg2Db", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *KefuStatusManageSvrkitClient) GetMsgFromDb(ctx context.Context, uin uint32, in *GetMsgReq, opts ...svrkit.CallOption) (*GetMsgResp, error) {
	out := new(GetMsgResp)
	err := c.cc.Invoke(ctx, uin, commandKefuStatusManageGetMsgFromDb, "/KefuStatusManage.KefuStatusManage/GetMsgFromDb", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *KefuStatusManageSvrkitClient) GetRelationUserToKefu(ctx context.Context, uin uint32, in *GetRelationUserToKefuReq, opts ...svrkit.CallOption) (*GetRelationUserToKefuResp, error) {
	out := new(GetRelationUserToKefuResp)
	err := c.cc.Invoke(ctx, uin, commandKefuStatusManageGetRelationUserToKefu, "/KefuStatusManage.KefuStatusManage/GetRelationUserToKefu", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}
