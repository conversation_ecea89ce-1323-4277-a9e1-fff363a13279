// Code generated by protoc-gen-gogo.
// source: sms/sms-go.proto
// DO NOT EDIT!

/*
	Package sms_go is a generated protocol buffer package.

	namespace

	It is generated from these files:
		sms/sms-go.proto

	It has these top-level messages:
		SendSmsReq
		DirectSendSmsReq
		CreateVerifyCodeReq
		CreateVerifyCodeResp
		ValidateVerifyCodeReq
		CheckUrlValidApkUrlReq
		CheckUrlValidApkUrlResp
		DownLoadUrlReq
		DownLoadUrlResp
		DownLoadUrlByteReq
		DownLoadUrlByteResp
		Foo
		PostUrlDataReq
		PostUrlDataResp
		SendVoiceVerifyCodeReq
		SendVoiceVerifyCodeResp
		SendSmsWithProviderReq
		SendSmsWithProviderResp
		RecordVerifyCodePassReq
		RecordVerifyCodePassResp
		SendMarketingSmsReq
		SendMarketingPhoneErrResult
		SendMarketingSmsResp
		AuthSmsReq
		AuthSmsResp
*/
package sms_go

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import tlvpickle "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 专用错误码从-20开始
type ERR_SMS int32

const (
	ERR_SMS_ERR_SMS_VERFIYCODE_VALIDATE_FAIL   ERR_SMS = -20
	ERR_SMS_ERR_SMS_TOO_MANY_PHONE             ERR_SMS = -21
	ERR_SMS_ERR_SMS_SEND_SMS_FREQ              ERR_SMS = -22
	ERR_SMS_ERR_SMS_TYPE_INVALID               ERR_SMS = -23
	ERR_SMS_ERR_SMS_NO_ENOUGH_PARAMS           ERR_SMS = -24
	ERR_SMS_ERR_SMS_SEND_SMS_THRESHHOLD_EXCEED ERR_SMS = -25
	ERR_SMS_ERR_SMS_INVALID_PHONE              ERR_SMS = -26
	ERR_SMS_ERR_SMS_INVALID_VERIFYCODE         ERR_SMS = -27
	ERR_SMS_ERR_SMS_NOT_SUPPORT_INN            ERR_SMS = -28
	ERR_SMS_ERR_SMS_REDIS_ERROR                ERR_SMS = -29
	ERR_SMS_ERR_SMS_PARSE_FROM_PB_ERROR        ERR_SMS = -30
	ERR_SMS_ERR_VALID_APK_URL_CANNOT_REACH     ERR_SMS = -31
	ERR_SMS_ERR_VALID_APK_URL_NOT_APK          ERR_SMS = -32
	ERR_SMS_ERR_SMS_INVALID_SENDER             ERR_SMS = -33
)

var ERR_SMS_name = map[int32]string{
	-20: "ERR_SMS_VERFIYCODE_VALIDATE_FAIL",
	-21: "ERR_SMS_TOO_MANY_PHONE",
	-22: "ERR_SMS_SEND_SMS_FREQ",
	-23: "ERR_SMS_TYPE_INVALID",
	-24: "ERR_SMS_NO_ENOUGH_PARAMS",
	-25: "ERR_SMS_SEND_SMS_THRESHHOLD_EXCEED",
	-26: "ERR_SMS_INVALID_PHONE",
	-27: "ERR_SMS_INVALID_VERIFYCODE",
	-28: "ERR_SMS_NOT_SUPPORT_INN",
	-29: "ERR_SMS_REDIS_ERROR",
	-30: "ERR_SMS_PARSE_FROM_PB_ERROR",
	-31: "ERR_VALID_APK_URL_CANNOT_REACH",
	-32: "ERR_VALID_APK_URL_NOT_APK",
	-33: "ERR_SMS_INVALID_SENDER",
}
var ERR_SMS_value = map[string]int32{
	"ERR_SMS_VERFIYCODE_VALIDATE_FAIL":   -20,
	"ERR_SMS_TOO_MANY_PHONE":             -21,
	"ERR_SMS_SEND_SMS_FREQ":              -22,
	"ERR_SMS_TYPE_INVALID":               -23,
	"ERR_SMS_NO_ENOUGH_PARAMS":           -24,
	"ERR_SMS_SEND_SMS_THRESHHOLD_EXCEED": -25,
	"ERR_SMS_INVALID_PHONE":              -26,
	"ERR_SMS_INVALID_VERIFYCODE":         -27,
	"ERR_SMS_NOT_SUPPORT_INN":            -28,
	"ERR_SMS_REDIS_ERROR":                -29,
	"ERR_SMS_PARSE_FROM_PB_ERROR":        -30,
	"ERR_VALID_APK_URL_CANNOT_REACH":     -31,
	"ERR_VALID_APK_URL_NOT_APK":          -32,
	"ERR_SMS_INVALID_SENDER":             -33,
}

func (x ERR_SMS) Enum() *ERR_SMS {
	p := new(ERR_SMS)
	*p = x
	return p
}
func (x ERR_SMS) String() string {
	return proto.EnumName(ERR_SMS_name, int32(x))
}
func (x *ERR_SMS) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ERR_SMS_value, data, "ERR_SMS")
	if err != nil {
		return err
	}
	*x = ERR_SMS(value)
	return nil
}
func (ERR_SMS) EnumDescriptor() ([]byte, []int) { return fileDescriptorSmsGo, []int{0} }

type AuthSmsResp_Result int32

const (
	AuthSmsResp_SUCCESS           AuthSmsResp_Result = 0
	AuthSmsResp_INVALID_APP_INFO  AuthSmsResp_Result = 1
	AuthSmsResp_INVALID_CLIENT_IP AuthSmsResp_Result = 2
)

var AuthSmsResp_Result_name = map[int32]string{
	0: "SUCCESS",
	1: "INVALID_APP_INFO",
	2: "INVALID_CLIENT_IP",
}
var AuthSmsResp_Result_value = map[string]int32{
	"SUCCESS":           0,
	"INVALID_APP_INFO":  1,
	"INVALID_CLIENT_IP": 2,
}

func (x AuthSmsResp_Result) Enum() *AuthSmsResp_Result {
	p := new(AuthSmsResp_Result)
	*p = x
	return p
}
func (x AuthSmsResp_Result) String() string {
	return proto.EnumName(AuthSmsResp_Result_name, int32(x))
}
func (x *AuthSmsResp_Result) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(AuthSmsResp_Result_value, data, "AuthSmsResp_Result")
	if err != nil {
		return err
	}
	*x = AuthSmsResp_Result(value)
	return nil
}
func (AuthSmsResp_Result) EnumDescriptor() ([]byte, []int) { return fileDescriptorSmsGo, []int{24, 0} }

// 单发短信
type SendSmsReq struct {
	Phone           string   `protobuf:"bytes,1,req,name=phone" json:"phone"`
	SmsType         uint32   `protobuf:"varint,2,req,name=sms_type,json=smsType" json:"sms_type"`
	ParamList       []string `protobuf:"bytes,3,rep,name=param_list,json=paramList" json:"param_list,omitempty"`
	WithoutCooldown bool     `protobuf:"varint,4,opt,name=without_cooldown,json=withoutCooldown" json:"without_cooldown"`
	VerifyCodeKey   string   `protobuf:"bytes,5,opt,name=verify_code_key,json=verifyCodeKey" json:"verify_code_key"`
	VerifyCodeUsage string   `protobuf:"bytes,6,opt,name=verify_code_usage,json=verifyCodeUsage" json:"verify_code_usage"`
	MarketId        uint32   `protobuf:"varint,7,opt,name=market_id,json=marketId" json:"market_id"`
	// 	required uint32 retry_times = 4;	//当前的重试次数(start with 0)
	BizId      uint32 `protobuf:"varint,8,opt,name=biz_id,json=bizId" json:"biz_id"`
	ExtBizId   uint32 `protobuf:"varint,9,opt,name=ext_biz_id,json=extBizId" json:"ext_biz_id"`
	RequestId  string `protobuf:"bytes,10,opt,name=request_id,json=requestId" json:"request_id"`
	CreateTime uint64 `protobuf:"varint,11,opt,name=create_time,json=createTime" json:"create_time"`
}

func (m *SendSmsReq) Reset()                    { *m = SendSmsReq{} }
func (m *SendSmsReq) String() string            { return proto.CompactTextString(m) }
func (*SendSmsReq) ProtoMessage()               {}
func (*SendSmsReq) Descriptor() ([]byte, []int) { return fileDescriptorSmsGo, []int{0} }

func (m *SendSmsReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *SendSmsReq) GetSmsType() uint32 {
	if m != nil {
		return m.SmsType
	}
	return 0
}

func (m *SendSmsReq) GetParamList() []string {
	if m != nil {
		return m.ParamList
	}
	return nil
}

func (m *SendSmsReq) GetWithoutCooldown() bool {
	if m != nil {
		return m.WithoutCooldown
	}
	return false
}

func (m *SendSmsReq) GetVerifyCodeKey() string {
	if m != nil {
		return m.VerifyCodeKey
	}
	return ""
}

func (m *SendSmsReq) GetVerifyCodeUsage() string {
	if m != nil {
		return m.VerifyCodeUsage
	}
	return ""
}

func (m *SendSmsReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *SendSmsReq) GetBizId() uint32 {
	if m != nil {
		return m.BizId
	}
	return 0
}

func (m *SendSmsReq) GetExtBizId() uint32 {
	if m != nil {
		return m.ExtBizId
	}
	return 0
}

func (m *SendSmsReq) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

func (m *SendSmsReq) GetCreateTime() uint64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

type DirectSendSmsReq struct {
	Phone    string `protobuf:"bytes,1,req,name=phone" json:"phone"`
	Text     string `protobuf:"bytes,2,req,name=text" json:"text"`
	BizId    uint32 `protobuf:"varint,3,opt,name=biz_id,json=bizId" json:"biz_id"`
	ExtBizId uint32 `protobuf:"varint,4,opt,name=ext_biz_id,json=extBizId" json:"ext_biz_id"`
}

func (m *DirectSendSmsReq) Reset()                    { *m = DirectSendSmsReq{} }
func (m *DirectSendSmsReq) String() string            { return proto.CompactTextString(m) }
func (*DirectSendSmsReq) ProtoMessage()               {}
func (*DirectSendSmsReq) Descriptor() ([]byte, []int) { return fileDescriptorSmsGo, []int{1} }

func (m *DirectSendSmsReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *DirectSendSmsReq) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *DirectSendSmsReq) GetBizId() uint32 {
	if m != nil {
		return m.BizId
	}
	return 0
}

func (m *DirectSendSmsReq) GetExtBizId() uint32 {
	if m != nil {
		return m.ExtBizId
	}
	return 0
}

// //////////////////////////////////////////////
// 验证码相关逻辑， 暂时放在此server， 以后独立
// //////////////////////////////////////////////
// 为uid生成验证码
type CreateVerifyCodeReq struct {
	Key     string `protobuf:"bytes,1,req,name=key" json:"key"`
	CodeLen uint32 `protobuf:"varint,2,opt,name=code_len,json=codeLen" json:"code_len"`
}

func (m *CreateVerifyCodeReq) Reset()                    { *m = CreateVerifyCodeReq{} }
func (m *CreateVerifyCodeReq) String() string            { return proto.CompactTextString(m) }
func (*CreateVerifyCodeReq) ProtoMessage()               {}
func (*CreateVerifyCodeReq) Descriptor() ([]byte, []int) { return fileDescriptorSmsGo, []int{2} }

func (m *CreateVerifyCodeReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *CreateVerifyCodeReq) GetCodeLen() uint32 {
	if m != nil {
		return m.CodeLen
	}
	return 0
}

type CreateVerifyCodeResp struct {
	Key        string `protobuf:"bytes,1,req,name=key" json:"key"`
	VerifyCode string `protobuf:"bytes,2,req,name=verify_code,json=verifyCode" json:"verify_code"`
	ExpireTime uint32 `protobuf:"varint,3,req,name=expire_time,json=expireTime" json:"expire_time"`
}

func (m *CreateVerifyCodeResp) Reset()                    { *m = CreateVerifyCodeResp{} }
func (m *CreateVerifyCodeResp) String() string            { return proto.CompactTextString(m) }
func (*CreateVerifyCodeResp) ProtoMessage()               {}
func (*CreateVerifyCodeResp) Descriptor() ([]byte, []int) { return fileDescriptorSmsGo, []int{3} }

func (m *CreateVerifyCodeResp) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *CreateVerifyCodeResp) GetVerifyCode() string {
	if m != nil {
		return m.VerifyCode
	}
	return ""
}

func (m *CreateVerifyCodeResp) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

// 验证验证码是否正确
type ValidateVerifyCodeReq struct {
	Key        string `protobuf:"bytes,1,req,name=key" json:"key"`
	VerifyCode string `protobuf:"bytes,2,req,name=verify_code,json=verifyCode" json:"verify_code"`
}

func (m *ValidateVerifyCodeReq) Reset()                    { *m = ValidateVerifyCodeReq{} }
func (m *ValidateVerifyCodeReq) String() string            { return proto.CompactTextString(m) }
func (*ValidateVerifyCodeReq) ProtoMessage()               {}
func (*ValidateVerifyCodeReq) Descriptor() ([]byte, []int) { return fileDescriptorSmsGo, []int{4} }

func (m *ValidateVerifyCodeReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *ValidateVerifyCodeReq) GetVerifyCode() string {
	if m != nil {
		return m.VerifyCode
	}
	return ""
}

type CheckUrlValidApkUrlReq struct {
	Url string `protobuf:"bytes,1,req,name=url" json:"url"`
}

func (m *CheckUrlValidApkUrlReq) Reset()                    { *m = CheckUrlValidApkUrlReq{} }
func (m *CheckUrlValidApkUrlReq) String() string            { return proto.CompactTextString(m) }
func (*CheckUrlValidApkUrlReq) ProtoMessage()               {}
func (*CheckUrlValidApkUrlReq) Descriptor() ([]byte, []int) { return fileDescriptorSmsGo, []int{5} }

func (m *CheckUrlValidApkUrlReq) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

type CheckUrlValidApkUrlResp struct {
	ContentLength uint32 `protobuf:"varint,1,req,name=content_length,json=contentLength" json:"content_length"`
}

func (m *CheckUrlValidApkUrlResp) Reset()                    { *m = CheckUrlValidApkUrlResp{} }
func (m *CheckUrlValidApkUrlResp) String() string            { return proto.CompactTextString(m) }
func (*CheckUrlValidApkUrlResp) ProtoMessage()               {}
func (*CheckUrlValidApkUrlResp) Descriptor() ([]byte, []int) { return fileDescriptorSmsGo, []int{6} }

func (m *CheckUrlValidApkUrlResp) GetContentLength() uint32 {
	if m != nil {
		return m.ContentLength
	}
	return 0
}

type DownLoadUrlReq struct {
	Url     string `protobuf:"bytes,1,req,name=url" json:"url"`
	Timeout uint32 `protobuf:"varint,2,opt,name=timeout" json:"timeout"`
}

func (m *DownLoadUrlReq) Reset()                    { *m = DownLoadUrlReq{} }
func (m *DownLoadUrlReq) String() string            { return proto.CompactTextString(m) }
func (*DownLoadUrlReq) ProtoMessage()               {}
func (*DownLoadUrlReq) Descriptor() ([]byte, []int) { return fileDescriptorSmsGo, []int{7} }

func (m *DownLoadUrlReq) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *DownLoadUrlReq) GetTimeout() uint32 {
	if m != nil {
		return m.Timeout
	}
	return 0
}

type DownLoadUrlResp struct {
	Msg string `protobuf:"bytes,1,req,name=msg" json:"msg"`
}

func (m *DownLoadUrlResp) Reset()                    { *m = DownLoadUrlResp{} }
func (m *DownLoadUrlResp) String() string            { return proto.CompactTextString(m) }
func (*DownLoadUrlResp) ProtoMessage()               {}
func (*DownLoadUrlResp) Descriptor() ([]byte, []int) { return fileDescriptorSmsGo, []int{8} }

func (m *DownLoadUrlResp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type DownLoadUrlByteReq struct {
	Url     string `protobuf:"bytes,1,req,name=url" json:"url"`
	Timeout uint32 `protobuf:"varint,2,opt,name=timeout" json:"timeout"`
}

func (m *DownLoadUrlByteReq) Reset()                    { *m = DownLoadUrlByteReq{} }
func (m *DownLoadUrlByteReq) String() string            { return proto.CompactTextString(m) }
func (*DownLoadUrlByteReq) ProtoMessage()               {}
func (*DownLoadUrlByteReq) Descriptor() ([]byte, []int) { return fileDescriptorSmsGo, []int{9} }

func (m *DownLoadUrlByteReq) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *DownLoadUrlByteReq) GetTimeout() uint32 {
	if m != nil {
		return m.Timeout
	}
	return 0
}

type DownLoadUrlByteResp struct {
	Msg []byte `protobuf:"bytes,1,req,name=msg" json:"msg"`
}

func (m *DownLoadUrlByteResp) Reset()                    { *m = DownLoadUrlByteResp{} }
func (m *DownLoadUrlByteResp) String() string            { return proto.CompactTextString(m) }
func (*DownLoadUrlByteResp) ProtoMessage()               {}
func (*DownLoadUrlByteResp) Descriptor() ([]byte, []int) { return fileDescriptorSmsGo, []int{10} }

func (m *DownLoadUrlByteResp) GetMsg() []byte {
	if m != nil {
		return m.Msg
	}
	return nil
}

type Foo struct {
}

func (m *Foo) Reset()                    { *m = Foo{} }
func (m *Foo) String() string            { return proto.CompactTextString(m) }
func (*Foo) ProtoMessage()               {}
func (*Foo) Descriptor() ([]byte, []int) { return fileDescriptorSmsGo, []int{11} }

// post url 接口
type PostUrlDataReq struct {
	Url          string   `protobuf:"bytes,1,req,name=url" json:"url"`
	HeadInfoList [][]byte `protobuf:"bytes,2,rep,name=head_info_list,json=headInfoList" json:"head_info_list,omitempty"`
	DataInfo     []byte   `protobuf:"bytes,3,req,name=data_info,json=dataInfo" json:"data_info"`
}

func (m *PostUrlDataReq) Reset()                    { *m = PostUrlDataReq{} }
func (m *PostUrlDataReq) String() string            { return proto.CompactTextString(m) }
func (*PostUrlDataReq) ProtoMessage()               {}
func (*PostUrlDataReq) Descriptor() ([]byte, []int) { return fileDescriptorSmsGo, []int{12} }

func (m *PostUrlDataReq) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *PostUrlDataReq) GetHeadInfoList() [][]byte {
	if m != nil {
		return m.HeadInfoList
	}
	return nil
}

func (m *PostUrlDataReq) GetDataInfo() []byte {
	if m != nil {
		return m.DataInfo
	}
	return nil
}

type PostUrlDataResp struct {
	RespMsg []byte `protobuf:"bytes,1,req,name=resp_msg,json=respMsg" json:"resp_msg"`
}

func (m *PostUrlDataResp) Reset()                    { *m = PostUrlDataResp{} }
func (m *PostUrlDataResp) String() string            { return proto.CompactTextString(m) }
func (*PostUrlDataResp) ProtoMessage()               {}
func (*PostUrlDataResp) Descriptor() ([]byte, []int) { return fileDescriptorSmsGo, []int{13} }

func (m *PostUrlDataResp) GetRespMsg() []byte {
	if m != nil {
		return m.RespMsg
	}
	return nil
}

type SendVoiceVerifyCodeReq struct {
	Phone      string   `protobuf:"bytes,1,req,name=phone" json:"phone"`
	VerifyCode string   `protobuf:"bytes,2,req,name=verify_code,json=verifyCode" json:"verify_code"`
	Uid        uint32   `protobuf:"varint,3,opt,name=uid" json:"uid"`
	NationCode string   `protobuf:"bytes,5,opt,name=nation_code,json=nationCode" json:"nation_code"`
	VoiceType  uint32   `protobuf:"varint,6,opt,name=voice_type,json=voiceType" json:"voice_type"`
	ParamList  []string `protobuf:"bytes,7,rep,name=param_list,json=paramList" json:"param_list,omitempty"`
	BizId      uint32   `protobuf:"varint,8,opt,name=biz_id,json=bizId" json:"biz_id"`
	ExtBizId   uint32   `protobuf:"varint,9,opt,name=ext_biz_id,json=extBizId" json:"ext_biz_id"`
}

func (m *SendVoiceVerifyCodeReq) Reset()                    { *m = SendVoiceVerifyCodeReq{} }
func (m *SendVoiceVerifyCodeReq) String() string            { return proto.CompactTextString(m) }
func (*SendVoiceVerifyCodeReq) ProtoMessage()               {}
func (*SendVoiceVerifyCodeReq) Descriptor() ([]byte, []int) { return fileDescriptorSmsGo, []int{14} }

func (m *SendVoiceVerifyCodeReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *SendVoiceVerifyCodeReq) GetVerifyCode() string {
	if m != nil {
		return m.VerifyCode
	}
	return ""
}

func (m *SendVoiceVerifyCodeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SendVoiceVerifyCodeReq) GetNationCode() string {
	if m != nil {
		return m.NationCode
	}
	return ""
}

func (m *SendVoiceVerifyCodeReq) GetVoiceType() uint32 {
	if m != nil {
		return m.VoiceType
	}
	return 0
}

func (m *SendVoiceVerifyCodeReq) GetParamList() []string {
	if m != nil {
		return m.ParamList
	}
	return nil
}

func (m *SendVoiceVerifyCodeReq) GetBizId() uint32 {
	if m != nil {
		return m.BizId
	}
	return 0
}

func (m *SendVoiceVerifyCodeReq) GetExtBizId() uint32 {
	if m != nil {
		return m.ExtBizId
	}
	return 0
}

type SendVoiceVerifyCodeResp struct {
}

func (m *SendVoiceVerifyCodeResp) Reset()                    { *m = SendVoiceVerifyCodeResp{} }
func (m *SendVoiceVerifyCodeResp) String() string            { return proto.CompactTextString(m) }
func (*SendVoiceVerifyCodeResp) ProtoMessage()               {}
func (*SendVoiceVerifyCodeResp) Descriptor() ([]byte, []int) { return fileDescriptorSmsGo, []int{15} }

type SendSmsWithProviderReq struct {
	Provider  string   `protobuf:"bytes,1,req,name=provider" json:"provider"`
	Phones    []string `protobuf:"bytes,2,rep,name=phones" json:"phones,omitempty"`
	SmsType   uint32   `protobuf:"varint,3,req,name=sms_type,json=smsType" json:"sms_type"`
	ParamList []string `protobuf:"bytes,4,rep,name=param_list,json=paramList" json:"param_list,omitempty"`
	MarketId  uint32   `protobuf:"varint,5,opt,name=market_id,json=marketId" json:"market_id"`
}

func (m *SendSmsWithProviderReq) Reset()                    { *m = SendSmsWithProviderReq{} }
func (m *SendSmsWithProviderReq) String() string            { return proto.CompactTextString(m) }
func (*SendSmsWithProviderReq) ProtoMessage()               {}
func (*SendSmsWithProviderReq) Descriptor() ([]byte, []int) { return fileDescriptorSmsGo, []int{16} }

func (m *SendSmsWithProviderReq) GetProvider() string {
	if m != nil {
		return m.Provider
	}
	return ""
}

func (m *SendSmsWithProviderReq) GetPhones() []string {
	if m != nil {
		return m.Phones
	}
	return nil
}

func (m *SendSmsWithProviderReq) GetSmsType() uint32 {
	if m != nil {
		return m.SmsType
	}
	return 0
}

func (m *SendSmsWithProviderReq) GetParamList() []string {
	if m != nil {
		return m.ParamList
	}
	return nil
}

func (m *SendSmsWithProviderReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

type SendSmsWithProviderResp struct {
}

func (m *SendSmsWithProviderResp) Reset()                    { *m = SendSmsWithProviderResp{} }
func (m *SendSmsWithProviderResp) String() string            { return proto.CompactTextString(m) }
func (*SendSmsWithProviderResp) ProtoMessage()               {}
func (*SendSmsWithProviderResp) Descriptor() ([]byte, []int) { return fileDescriptorSmsGo, []int{17} }

type RecordVerifyCodePassReq struct {
	VerifyCodeKey string `protobuf:"bytes,1,req,name=verify_code_key,json=verifyCodeKey" json:"verify_code_key"`
	VerifyAt      uint32 `protobuf:"varint,2,opt,name=verify_at,json=verifyAt" json:"verify_at"`
}

func (m *RecordVerifyCodePassReq) Reset()                    { *m = RecordVerifyCodePassReq{} }
func (m *RecordVerifyCodePassReq) String() string            { return proto.CompactTextString(m) }
func (*RecordVerifyCodePassReq) ProtoMessage()               {}
func (*RecordVerifyCodePassReq) Descriptor() ([]byte, []int) { return fileDescriptorSmsGo, []int{18} }

func (m *RecordVerifyCodePassReq) GetVerifyCodeKey() string {
	if m != nil {
		return m.VerifyCodeKey
	}
	return ""
}

func (m *RecordVerifyCodePassReq) GetVerifyAt() uint32 {
	if m != nil {
		return m.VerifyAt
	}
	return 0
}

type RecordVerifyCodePassResp struct {
}

func (m *RecordVerifyCodePassResp) Reset()                    { *m = RecordVerifyCodePassResp{} }
func (m *RecordVerifyCodePassResp) String() string            { return proto.CompactTextString(m) }
func (*RecordVerifyCodePassResp) ProtoMessage()               {}
func (*RecordVerifyCodePassResp) Descriptor() ([]byte, []int) { return fileDescriptorSmsGo, []int{19} }

// 多个phone 对应一个message 或者
// 多个phone 对应 同样数量的message，对应关系发送
type SendMarketingSmsReq struct {
	Phones   []string `protobuf:"bytes,1,rep,name=phones" json:"phones,omitempty"`
	Messages []string `protobuf:"bytes,2,rep,name=messages" json:"messages,omitempty"`
	BizId    uint32   `protobuf:"varint,3,opt,name=biz_id,json=bizId" json:"biz_id"`
	ExtBizId uint32   `protobuf:"varint,4,opt,name=ext_biz_id,json=extBizId" json:"ext_biz_id"`
	AppId    string   `protobuf:"bytes,5,opt,name=app_id,json=appId" json:"app_id"`
}

func (m *SendMarketingSmsReq) Reset()                    { *m = SendMarketingSmsReq{} }
func (m *SendMarketingSmsReq) String() string            { return proto.CompactTextString(m) }
func (*SendMarketingSmsReq) ProtoMessage()               {}
func (*SendMarketingSmsReq) Descriptor() ([]byte, []int) { return fileDescriptorSmsGo, []int{20} }

func (m *SendMarketingSmsReq) GetPhones() []string {
	if m != nil {
		return m.Phones
	}
	return nil
}

func (m *SendMarketingSmsReq) GetMessages() []string {
	if m != nil {
		return m.Messages
	}
	return nil
}

func (m *SendMarketingSmsReq) GetBizId() uint32 {
	if m != nil {
		return m.BizId
	}
	return 0
}

func (m *SendMarketingSmsReq) GetExtBizId() uint32 {
	if m != nil {
		return m.ExtBizId
	}
	return 0
}

func (m *SendMarketingSmsReq) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

type SendMarketingPhoneErrResult struct {
	Phone string `protobuf:"bytes,1,opt,name=phone" json:"phone"`
	Code  int32  `protobuf:"varint,2,opt,name=code" json:"code"`
}

func (m *SendMarketingPhoneErrResult) Reset()         { *m = SendMarketingPhoneErrResult{} }
func (m *SendMarketingPhoneErrResult) String() string { return proto.CompactTextString(m) }
func (*SendMarketingPhoneErrResult) ProtoMessage()    {}
func (*SendMarketingPhoneErrResult) Descriptor() ([]byte, []int) {
	return fileDescriptorSmsGo, []int{21}
}

func (m *SendMarketingPhoneErrResult) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *SendMarketingPhoneErrResult) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

type SendMarketingSmsResp struct {
	ReqResult int32                          `protobuf:"varint,1,req,name=req_result,json=reqResult" json:"req_result"`
	ErrPhones []*SendMarketingPhoneErrResult `protobuf:"bytes,2,rep,name=err_phones,json=errPhones" json:"err_phones,omitempty"`
}

func (m *SendMarketingSmsResp) Reset()                    { *m = SendMarketingSmsResp{} }
func (m *SendMarketingSmsResp) String() string            { return proto.CompactTextString(m) }
func (*SendMarketingSmsResp) ProtoMessage()               {}
func (*SendMarketingSmsResp) Descriptor() ([]byte, []int) { return fileDescriptorSmsGo, []int{22} }

func (m *SendMarketingSmsResp) GetReqResult() int32 {
	if m != nil {
		return m.ReqResult
	}
	return 0
}

func (m *SendMarketingSmsResp) GetErrPhones() []*SendMarketingPhoneErrResult {
	if m != nil {
		return m.ErrPhones
	}
	return nil
}

type AuthSmsReq struct {
	AppId     string `protobuf:"bytes,1,opt,name=app_id,json=appId" json:"app_id"`
	AppSecret string `protobuf:"bytes,2,opt,name=app_secret,json=appSecret" json:"app_secret"`
	ClientIp  string `protobuf:"bytes,3,opt,name=client_ip,json=clientIp" json:"client_ip"`
}

func (m *AuthSmsReq) Reset()                    { *m = AuthSmsReq{} }
func (m *AuthSmsReq) String() string            { return proto.CompactTextString(m) }
func (*AuthSmsReq) ProtoMessage()               {}
func (*AuthSmsReq) Descriptor() ([]byte, []int) { return fileDescriptorSmsGo, []int{23} }

func (m *AuthSmsReq) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *AuthSmsReq) GetAppSecret() string {
	if m != nil {
		return m.AppSecret
	}
	return ""
}

func (m *AuthSmsReq) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

type AuthSmsResp struct {
	Result int32  `protobuf:"varint,1,opt,name=result" json:"result"`
	BizId  uint32 `protobuf:"varint,2,opt,name=biz_id,json=bizId" json:"biz_id"`
}

func (m *AuthSmsResp) Reset()                    { *m = AuthSmsResp{} }
func (m *AuthSmsResp) String() string            { return proto.CompactTextString(m) }
func (*AuthSmsResp) ProtoMessage()               {}
func (*AuthSmsResp) Descriptor() ([]byte, []int) { return fileDescriptorSmsGo, []int{24} }

func (m *AuthSmsResp) GetResult() int32 {
	if m != nil {
		return m.Result
	}
	return 0
}

func (m *AuthSmsResp) GetBizId() uint32 {
	if m != nil {
		return m.BizId
	}
	return 0
}

func init() {
	proto.RegisterType((*SendSmsReq)(nil), "sms_go.SendSmsReq")
	proto.RegisterType((*DirectSendSmsReq)(nil), "sms_go.DirectSendSmsReq")
	proto.RegisterType((*CreateVerifyCodeReq)(nil), "sms_go.CreateVerifyCodeReq")
	proto.RegisterType((*CreateVerifyCodeResp)(nil), "sms_go.CreateVerifyCodeResp")
	proto.RegisterType((*ValidateVerifyCodeReq)(nil), "sms_go.ValidateVerifyCodeReq")
	proto.RegisterType((*CheckUrlValidApkUrlReq)(nil), "sms_go.CheckUrlValidApkUrlReq")
	proto.RegisterType((*CheckUrlValidApkUrlResp)(nil), "sms_go.CheckUrlValidApkUrlResp")
	proto.RegisterType((*DownLoadUrlReq)(nil), "sms_go.DownLoadUrlReq")
	proto.RegisterType((*DownLoadUrlResp)(nil), "sms_go.DownLoadUrlResp")
	proto.RegisterType((*DownLoadUrlByteReq)(nil), "sms_go.DownLoadUrlByteReq")
	proto.RegisterType((*DownLoadUrlByteResp)(nil), "sms_go.DownLoadUrlByteResp")
	proto.RegisterType((*Foo)(nil), "sms_go.Foo")
	proto.RegisterType((*PostUrlDataReq)(nil), "sms_go.PostUrlDataReq")
	proto.RegisterType((*PostUrlDataResp)(nil), "sms_go.PostUrlDataResp")
	proto.RegisterType((*SendVoiceVerifyCodeReq)(nil), "sms_go.SendVoiceVerifyCodeReq")
	proto.RegisterType((*SendVoiceVerifyCodeResp)(nil), "sms_go.SendVoiceVerifyCodeResp")
	proto.RegisterType((*SendSmsWithProviderReq)(nil), "sms_go.SendSmsWithProviderReq")
	proto.RegisterType((*SendSmsWithProviderResp)(nil), "sms_go.SendSmsWithProviderResp")
	proto.RegisterType((*RecordVerifyCodePassReq)(nil), "sms_go.RecordVerifyCodePassReq")
	proto.RegisterType((*RecordVerifyCodePassResp)(nil), "sms_go.RecordVerifyCodePassResp")
	proto.RegisterType((*SendMarketingSmsReq)(nil), "sms_go.SendMarketingSmsReq")
	proto.RegisterType((*SendMarketingPhoneErrResult)(nil), "sms_go.SendMarketingPhoneErrResult")
	proto.RegisterType((*SendMarketingSmsResp)(nil), "sms_go.SendMarketingSmsResp")
	proto.RegisterType((*AuthSmsReq)(nil), "sms_go.AuthSmsReq")
	proto.RegisterType((*AuthSmsResp)(nil), "sms_go.AuthSmsResp")
	proto.RegisterEnum("sms_go.ERR_SMS", ERR_SMS_name, ERR_SMS_value)
	proto.RegisterEnum("sms_go.AuthSmsResp_Result", AuthSmsResp_Result_name, AuthSmsResp_Result_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for Sms service

type SmsClient interface {
	SendSms(ctx context.Context, in *SendSmsReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 这个接口测试用, 业务不要调用该接口
	SendSmsWithProvider(ctx context.Context, in *SendSmsWithProviderReq, opts ...grpc.CallOption) (*SendSmsWithProviderResp, error)
	CheckUrlValidApkUrl(ctx context.Context, in *CheckUrlValidApkUrlReq, opts ...grpc.CallOption) (*CheckUrlValidApkUrlResp, error)
	DownLoadUrl(ctx context.Context, in *DownLoadUrlReq, opts ...grpc.CallOption) (*DownLoadUrlResp, error)
	DirectSendSms(ctx context.Context, in *DirectSendSmsReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	PostUrlData(ctx context.Context, in *PostUrlDataReq, opts ...grpc.CallOption) (*PostUrlDataResp, error)
	DownLoadUrlByte(ctx context.Context, in *DownLoadUrlByteReq, opts ...grpc.CallOption) (*DownLoadUrlByteResp, error)
	SendVoiceVerifyCode(ctx context.Context, in *SendVoiceVerifyCodeReq, opts ...grpc.CallOption) (*SendVoiceVerifyCodeResp, error)
	RecordVerifyCodePass(ctx context.Context, in *RecordVerifyCodePassReq, opts ...grpc.CallOption) (*RecordVerifyCodePassResp, error)
	SendMarketingSms(ctx context.Context, in *SendMarketingSmsReq, opts ...grpc.CallOption) (*SendMarketingSmsResp, error)
	AuthSms(ctx context.Context, in *AuthSmsReq, opts ...grpc.CallOption) (*AuthSmsResp, error)
}

type smsClient struct {
	cc *grpc.ClientConn
}

func NewSmsClient(cc *grpc.ClientConn) SmsClient {
	return &smsClient{cc}
}

func (c *smsClient) SendSms(ctx context.Context, in *SendSmsReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/sms_go.Sms/SendSms", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) SendSmsWithProvider(ctx context.Context, in *SendSmsWithProviderReq, opts ...grpc.CallOption) (*SendSmsWithProviderResp, error) {
	out := new(SendSmsWithProviderResp)
	err := grpc.Invoke(ctx, "/sms_go.Sms/SendSmsWithProvider", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) CheckUrlValidApkUrl(ctx context.Context, in *CheckUrlValidApkUrlReq, opts ...grpc.CallOption) (*CheckUrlValidApkUrlResp, error) {
	out := new(CheckUrlValidApkUrlResp)
	err := grpc.Invoke(ctx, "/sms_go.Sms/CheckUrlValidApkUrl", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) DownLoadUrl(ctx context.Context, in *DownLoadUrlReq, opts ...grpc.CallOption) (*DownLoadUrlResp, error) {
	out := new(DownLoadUrlResp)
	err := grpc.Invoke(ctx, "/sms_go.Sms/DownLoadUrl", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) DirectSendSms(ctx context.Context, in *DirectSendSmsReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/sms_go.Sms/DirectSendSms", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) PostUrlData(ctx context.Context, in *PostUrlDataReq, opts ...grpc.CallOption) (*PostUrlDataResp, error) {
	out := new(PostUrlDataResp)
	err := grpc.Invoke(ctx, "/sms_go.Sms/PostUrlData", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) DownLoadUrlByte(ctx context.Context, in *DownLoadUrlByteReq, opts ...grpc.CallOption) (*DownLoadUrlByteResp, error) {
	out := new(DownLoadUrlByteResp)
	err := grpc.Invoke(ctx, "/sms_go.Sms/DownLoadUrlByte", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) SendVoiceVerifyCode(ctx context.Context, in *SendVoiceVerifyCodeReq, opts ...grpc.CallOption) (*SendVoiceVerifyCodeResp, error) {
	out := new(SendVoiceVerifyCodeResp)
	err := grpc.Invoke(ctx, "/sms_go.Sms/SendVoiceVerifyCode", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) RecordVerifyCodePass(ctx context.Context, in *RecordVerifyCodePassReq, opts ...grpc.CallOption) (*RecordVerifyCodePassResp, error) {
	out := new(RecordVerifyCodePassResp)
	err := grpc.Invoke(ctx, "/sms_go.Sms/RecordVerifyCodePass", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) SendMarketingSms(ctx context.Context, in *SendMarketingSmsReq, opts ...grpc.CallOption) (*SendMarketingSmsResp, error) {
	out := new(SendMarketingSmsResp)
	err := grpc.Invoke(ctx, "/sms_go.Sms/SendMarketingSms", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) AuthSms(ctx context.Context, in *AuthSmsReq, opts ...grpc.CallOption) (*AuthSmsResp, error) {
	out := new(AuthSmsResp)
	err := grpc.Invoke(ctx, "/sms_go.Sms/AuthSms", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for Sms service

type SmsServer interface {
	SendSms(context.Context, *SendSmsReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 这个接口测试用, 业务不要调用该接口
	SendSmsWithProvider(context.Context, *SendSmsWithProviderReq) (*SendSmsWithProviderResp, error)
	CheckUrlValidApkUrl(context.Context, *CheckUrlValidApkUrlReq) (*CheckUrlValidApkUrlResp, error)
	DownLoadUrl(context.Context, *DownLoadUrlReq) (*DownLoadUrlResp, error)
	DirectSendSms(context.Context, *DirectSendSmsReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	PostUrlData(context.Context, *PostUrlDataReq) (*PostUrlDataResp, error)
	DownLoadUrlByte(context.Context, *DownLoadUrlByteReq) (*DownLoadUrlByteResp, error)
	SendVoiceVerifyCode(context.Context, *SendVoiceVerifyCodeReq) (*SendVoiceVerifyCodeResp, error)
	RecordVerifyCodePass(context.Context, *RecordVerifyCodePassReq) (*RecordVerifyCodePassResp, error)
	SendMarketingSms(context.Context, *SendMarketingSmsReq) (*SendMarketingSmsResp, error)
	AuthSms(context.Context, *AuthSmsReq) (*AuthSmsResp, error)
}

func RegisterSmsServer(s *grpc.Server, srv SmsServer) {
	s.RegisterService(&_Sms_serviceDesc, srv)
}

func _Sms_SendSms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendSmsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).SendSms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sms_go.Sms/SendSms",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).SendSms(ctx, req.(*SendSmsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_SendSmsWithProvider_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendSmsWithProviderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).SendSmsWithProvider(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sms_go.Sms/SendSmsWithProvider",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).SendSmsWithProvider(ctx, req.(*SendSmsWithProviderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_CheckUrlValidApkUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUrlValidApkUrlReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).CheckUrlValidApkUrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sms_go.Sms/CheckUrlValidApkUrl",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).CheckUrlValidApkUrl(ctx, req.(*CheckUrlValidApkUrlReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_DownLoadUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DownLoadUrlReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).DownLoadUrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sms_go.Sms/DownLoadUrl",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).DownLoadUrl(ctx, req.(*DownLoadUrlReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_DirectSendSms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DirectSendSmsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).DirectSendSms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sms_go.Sms/DirectSendSms",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).DirectSendSms(ctx, req.(*DirectSendSmsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_PostUrlData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PostUrlDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).PostUrlData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sms_go.Sms/PostUrlData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).PostUrlData(ctx, req.(*PostUrlDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_DownLoadUrlByte_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DownLoadUrlByteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).DownLoadUrlByte(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sms_go.Sms/DownLoadUrlByte",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).DownLoadUrlByte(ctx, req.(*DownLoadUrlByteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_SendVoiceVerifyCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendVoiceVerifyCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).SendVoiceVerifyCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sms_go.Sms/SendVoiceVerifyCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).SendVoiceVerifyCode(ctx, req.(*SendVoiceVerifyCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_RecordVerifyCodePass_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordVerifyCodePassReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).RecordVerifyCodePass(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sms_go.Sms/RecordVerifyCodePass",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).RecordVerifyCodePass(ctx, req.(*RecordVerifyCodePassReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_SendMarketingSms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendMarketingSmsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).SendMarketingSms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sms_go.Sms/SendMarketingSms",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).SendMarketingSms(ctx, req.(*SendMarketingSmsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_AuthSms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuthSmsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).AuthSms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sms_go.Sms/AuthSms",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).AuthSms(ctx, req.(*AuthSmsReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Sms_serviceDesc = grpc.ServiceDesc{
	ServiceName: "sms_go.Sms",
	HandlerType: (*SmsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendSms",
			Handler:    _Sms_SendSms_Handler,
		},
		{
			MethodName: "SendSmsWithProvider",
			Handler:    _Sms_SendSmsWithProvider_Handler,
		},
		{
			MethodName: "CheckUrlValidApkUrl",
			Handler:    _Sms_CheckUrlValidApkUrl_Handler,
		},
		{
			MethodName: "DownLoadUrl",
			Handler:    _Sms_DownLoadUrl_Handler,
		},
		{
			MethodName: "DirectSendSms",
			Handler:    _Sms_DirectSendSms_Handler,
		},
		{
			MethodName: "PostUrlData",
			Handler:    _Sms_PostUrlData_Handler,
		},
		{
			MethodName: "DownLoadUrlByte",
			Handler:    _Sms_DownLoadUrlByte_Handler,
		},
		{
			MethodName: "SendVoiceVerifyCode",
			Handler:    _Sms_SendVoiceVerifyCode_Handler,
		},
		{
			MethodName: "RecordVerifyCodePass",
			Handler:    _Sms_RecordVerifyCodePass_Handler,
		},
		{
			MethodName: "SendMarketingSms",
			Handler:    _Sms_SendMarketingSms_Handler,
		},
		{
			MethodName: "AuthSms",
			Handler:    _Sms_AuthSms_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "sms/sms-go.proto",
}

func (m *SendSmsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendSmsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	dAtA[i] = 0x10
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(m.SmsType))
	if len(m.ParamList) > 0 {
		for _, s := range m.ParamList {
			dAtA[i] = 0x1a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x20
	i++
	if m.WithoutCooldown {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x2a
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(len(m.VerifyCodeKey)))
	i += copy(dAtA[i:], m.VerifyCodeKey)
	dAtA[i] = 0x32
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(len(m.VerifyCodeUsage)))
	i += copy(dAtA[i:], m.VerifyCodeUsage)
	dAtA[i] = 0x38
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(m.MarketId))
	dAtA[i] = 0x40
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(m.BizId))
	dAtA[i] = 0x48
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(m.ExtBizId))
	dAtA[i] = 0x52
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(len(m.RequestId)))
	i += copy(dAtA[i:], m.RequestId)
	dAtA[i] = 0x58
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(m.CreateTime))
	return i, nil
}

func (m *DirectSendSmsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DirectSendSmsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	dAtA[i] = 0x12
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(len(m.Text)))
	i += copy(dAtA[i:], m.Text)
	dAtA[i] = 0x18
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(m.BizId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(m.ExtBizId))
	return i, nil
}

func (m *CreateVerifyCodeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateVerifyCodeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(len(m.Key)))
	i += copy(dAtA[i:], m.Key)
	dAtA[i] = 0x10
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(m.CodeLen))
	return i, nil
}

func (m *CreateVerifyCodeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateVerifyCodeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(len(m.Key)))
	i += copy(dAtA[i:], m.Key)
	dAtA[i] = 0x12
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(len(m.VerifyCode)))
	i += copy(dAtA[i:], m.VerifyCode)
	dAtA[i] = 0x18
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(m.ExpireTime))
	return i, nil
}

func (m *ValidateVerifyCodeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ValidateVerifyCodeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(len(m.Key)))
	i += copy(dAtA[i:], m.Key)
	dAtA[i] = 0x12
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(len(m.VerifyCode)))
	i += copy(dAtA[i:], m.VerifyCode)
	return i, nil
}

func (m *CheckUrlValidApkUrlReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUrlValidApkUrlReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(len(m.Url)))
	i += copy(dAtA[i:], m.Url)
	return i, nil
}

func (m *CheckUrlValidApkUrlResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUrlValidApkUrlResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(m.ContentLength))
	return i, nil
}

func (m *DownLoadUrlReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DownLoadUrlReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(len(m.Url)))
	i += copy(dAtA[i:], m.Url)
	dAtA[i] = 0x10
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(m.Timeout))
	return i, nil
}

func (m *DownLoadUrlResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DownLoadUrlResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(len(m.Msg)))
	i += copy(dAtA[i:], m.Msg)
	return i, nil
}

func (m *DownLoadUrlByteReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DownLoadUrlByteReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(len(m.Url)))
	i += copy(dAtA[i:], m.Url)
	dAtA[i] = 0x10
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(m.Timeout))
	return i, nil
}

func (m *DownLoadUrlByteResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DownLoadUrlByteResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Msg != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSmsGo(dAtA, i, uint64(len(m.Msg)))
		i += copy(dAtA[i:], m.Msg)
	}
	return i, nil
}

func (m *Foo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Foo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *PostUrlDataReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PostUrlDataReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(len(m.Url)))
	i += copy(dAtA[i:], m.Url)
	if len(m.HeadInfoList) > 0 {
		for _, b := range m.HeadInfoList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintSmsGo(dAtA, i, uint64(len(b)))
			i += copy(dAtA[i:], b)
		}
	}
	if m.DataInfo != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintSmsGo(dAtA, i, uint64(len(m.DataInfo)))
		i += copy(dAtA[i:], m.DataInfo)
	}
	return i, nil
}

func (m *PostUrlDataResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PostUrlDataResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.RespMsg != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSmsGo(dAtA, i, uint64(len(m.RespMsg)))
		i += copy(dAtA[i:], m.RespMsg)
	}
	return i, nil
}

func (m *SendVoiceVerifyCodeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendVoiceVerifyCodeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	dAtA[i] = 0x12
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(len(m.VerifyCode)))
	i += copy(dAtA[i:], m.VerifyCode)
	dAtA[i] = 0x18
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(len(m.NationCode)))
	i += copy(dAtA[i:], m.NationCode)
	dAtA[i] = 0x30
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(m.VoiceType))
	if len(m.ParamList) > 0 {
		for _, s := range m.ParamList {
			dAtA[i] = 0x3a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x40
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(m.BizId))
	dAtA[i] = 0x48
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(m.ExtBizId))
	return i, nil
}

func (m *SendVoiceVerifyCodeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendVoiceVerifyCodeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *SendSmsWithProviderReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendSmsWithProviderReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(len(m.Provider)))
	i += copy(dAtA[i:], m.Provider)
	if len(m.Phones) > 0 {
		for _, s := range m.Phones {
			dAtA[i] = 0x12
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(m.SmsType))
	if len(m.ParamList) > 0 {
		for _, s := range m.ParamList {
			dAtA[i] = 0x22
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x28
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(m.MarketId))
	return i, nil
}

func (m *SendSmsWithProviderResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendSmsWithProviderResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *RecordVerifyCodePassReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecordVerifyCodePassReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(len(m.VerifyCodeKey)))
	i += copy(dAtA[i:], m.VerifyCodeKey)
	dAtA[i] = 0x10
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(m.VerifyAt))
	return i, nil
}

func (m *RecordVerifyCodePassResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecordVerifyCodePassResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *SendMarketingSmsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendMarketingSmsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Phones) > 0 {
		for _, s := range m.Phones {
			dAtA[i] = 0xa
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if len(m.Messages) > 0 {
		for _, s := range m.Messages {
			dAtA[i] = 0x12
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(m.BizId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(m.ExtBizId))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(len(m.AppId)))
	i += copy(dAtA[i:], m.AppId)
	return i, nil
}

func (m *SendMarketingPhoneErrResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendMarketingPhoneErrResult) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	dAtA[i] = 0x10
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(m.Code))
	return i, nil
}

func (m *SendMarketingSmsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendMarketingSmsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(m.ReqResult))
	if len(m.ErrPhones) > 0 {
		for _, msg := range m.ErrPhones {
			dAtA[i] = 0x12
			i++
			i = encodeVarintSmsGo(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *AuthSmsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AuthSmsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(len(m.AppId)))
	i += copy(dAtA[i:], m.AppId)
	dAtA[i] = 0x12
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(len(m.AppSecret)))
	i += copy(dAtA[i:], m.AppSecret)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(len(m.ClientIp)))
	i += copy(dAtA[i:], m.ClientIp)
	return i, nil
}

func (m *AuthSmsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AuthSmsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(m.Result))
	dAtA[i] = 0x10
	i++
	i = encodeVarintSmsGo(dAtA, i, uint64(m.BizId))
	return i, nil
}

func encodeFixed64SmsGo(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32SmsGo(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintSmsGo(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *SendSmsReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Phone)
	n += 1 + l + sovSmsGo(uint64(l))
	n += 1 + sovSmsGo(uint64(m.SmsType))
	if len(m.ParamList) > 0 {
		for _, s := range m.ParamList {
			l = len(s)
			n += 1 + l + sovSmsGo(uint64(l))
		}
	}
	n += 2
	l = len(m.VerifyCodeKey)
	n += 1 + l + sovSmsGo(uint64(l))
	l = len(m.VerifyCodeUsage)
	n += 1 + l + sovSmsGo(uint64(l))
	n += 1 + sovSmsGo(uint64(m.MarketId))
	n += 1 + sovSmsGo(uint64(m.BizId))
	n += 1 + sovSmsGo(uint64(m.ExtBizId))
	l = len(m.RequestId)
	n += 1 + l + sovSmsGo(uint64(l))
	n += 1 + sovSmsGo(uint64(m.CreateTime))
	return n
}

func (m *DirectSendSmsReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Phone)
	n += 1 + l + sovSmsGo(uint64(l))
	l = len(m.Text)
	n += 1 + l + sovSmsGo(uint64(l))
	n += 1 + sovSmsGo(uint64(m.BizId))
	n += 1 + sovSmsGo(uint64(m.ExtBizId))
	return n
}

func (m *CreateVerifyCodeReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Key)
	n += 1 + l + sovSmsGo(uint64(l))
	n += 1 + sovSmsGo(uint64(m.CodeLen))
	return n
}

func (m *CreateVerifyCodeResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.Key)
	n += 1 + l + sovSmsGo(uint64(l))
	l = len(m.VerifyCode)
	n += 1 + l + sovSmsGo(uint64(l))
	n += 1 + sovSmsGo(uint64(m.ExpireTime))
	return n
}

func (m *ValidateVerifyCodeReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Key)
	n += 1 + l + sovSmsGo(uint64(l))
	l = len(m.VerifyCode)
	n += 1 + l + sovSmsGo(uint64(l))
	return n
}

func (m *CheckUrlValidApkUrlReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Url)
	n += 1 + l + sovSmsGo(uint64(l))
	return n
}

func (m *CheckUrlValidApkUrlResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSmsGo(uint64(m.ContentLength))
	return n
}

func (m *DownLoadUrlReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Url)
	n += 1 + l + sovSmsGo(uint64(l))
	n += 1 + sovSmsGo(uint64(m.Timeout))
	return n
}

func (m *DownLoadUrlResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.Msg)
	n += 1 + l + sovSmsGo(uint64(l))
	return n
}

func (m *DownLoadUrlByteReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Url)
	n += 1 + l + sovSmsGo(uint64(l))
	n += 1 + sovSmsGo(uint64(m.Timeout))
	return n
}

func (m *DownLoadUrlByteResp) Size() (n int) {
	var l int
	_ = l
	if m.Msg != nil {
		l = len(m.Msg)
		n += 1 + l + sovSmsGo(uint64(l))
	}
	return n
}

func (m *Foo) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *PostUrlDataReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Url)
	n += 1 + l + sovSmsGo(uint64(l))
	if len(m.HeadInfoList) > 0 {
		for _, b := range m.HeadInfoList {
			l = len(b)
			n += 1 + l + sovSmsGo(uint64(l))
		}
	}
	if m.DataInfo != nil {
		l = len(m.DataInfo)
		n += 1 + l + sovSmsGo(uint64(l))
	}
	return n
}

func (m *PostUrlDataResp) Size() (n int) {
	var l int
	_ = l
	if m.RespMsg != nil {
		l = len(m.RespMsg)
		n += 1 + l + sovSmsGo(uint64(l))
	}
	return n
}

func (m *SendVoiceVerifyCodeReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Phone)
	n += 1 + l + sovSmsGo(uint64(l))
	l = len(m.VerifyCode)
	n += 1 + l + sovSmsGo(uint64(l))
	n += 1 + sovSmsGo(uint64(m.Uid))
	l = len(m.NationCode)
	n += 1 + l + sovSmsGo(uint64(l))
	n += 1 + sovSmsGo(uint64(m.VoiceType))
	if len(m.ParamList) > 0 {
		for _, s := range m.ParamList {
			l = len(s)
			n += 1 + l + sovSmsGo(uint64(l))
		}
	}
	n += 1 + sovSmsGo(uint64(m.BizId))
	n += 1 + sovSmsGo(uint64(m.ExtBizId))
	return n
}

func (m *SendVoiceVerifyCodeResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *SendSmsWithProviderReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Provider)
	n += 1 + l + sovSmsGo(uint64(l))
	if len(m.Phones) > 0 {
		for _, s := range m.Phones {
			l = len(s)
			n += 1 + l + sovSmsGo(uint64(l))
		}
	}
	n += 1 + sovSmsGo(uint64(m.SmsType))
	if len(m.ParamList) > 0 {
		for _, s := range m.ParamList {
			l = len(s)
			n += 1 + l + sovSmsGo(uint64(l))
		}
	}
	n += 1 + sovSmsGo(uint64(m.MarketId))
	return n
}

func (m *SendSmsWithProviderResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *RecordVerifyCodePassReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.VerifyCodeKey)
	n += 1 + l + sovSmsGo(uint64(l))
	n += 1 + sovSmsGo(uint64(m.VerifyAt))
	return n
}

func (m *RecordVerifyCodePassResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *SendMarketingSmsReq) Size() (n int) {
	var l int
	_ = l
	if len(m.Phones) > 0 {
		for _, s := range m.Phones {
			l = len(s)
			n += 1 + l + sovSmsGo(uint64(l))
		}
	}
	if len(m.Messages) > 0 {
		for _, s := range m.Messages {
			l = len(s)
			n += 1 + l + sovSmsGo(uint64(l))
		}
	}
	n += 1 + sovSmsGo(uint64(m.BizId))
	n += 1 + sovSmsGo(uint64(m.ExtBizId))
	l = len(m.AppId)
	n += 1 + l + sovSmsGo(uint64(l))
	return n
}

func (m *SendMarketingPhoneErrResult) Size() (n int) {
	var l int
	_ = l
	l = len(m.Phone)
	n += 1 + l + sovSmsGo(uint64(l))
	n += 1 + sovSmsGo(uint64(m.Code))
	return n
}

func (m *SendMarketingSmsResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSmsGo(uint64(m.ReqResult))
	if len(m.ErrPhones) > 0 {
		for _, e := range m.ErrPhones {
			l = e.Size()
			n += 1 + l + sovSmsGo(uint64(l))
		}
	}
	return n
}

func (m *AuthSmsReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.AppId)
	n += 1 + l + sovSmsGo(uint64(l))
	l = len(m.AppSecret)
	n += 1 + l + sovSmsGo(uint64(l))
	l = len(m.ClientIp)
	n += 1 + l + sovSmsGo(uint64(l))
	return n
}

func (m *AuthSmsResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSmsGo(uint64(m.Result))
	n += 1 + sovSmsGo(uint64(m.BizId))
	return n
}

func sovSmsGo(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozSmsGo(x uint64) (n int) {
	return sovSmsGo(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *SendSmsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSmsGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendSmsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendSmsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SmsType", wireType)
			}
			m.SmsType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SmsType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParamList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ParamList = append(m.ParamList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field WithoutCooldown", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.WithoutCooldown = bool(v != 0)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VerifyCodeKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VerifyCodeKey = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VerifyCodeUsage", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VerifyCodeUsage = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MarketId", wireType)
			}
			m.MarketId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MarketId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BizId", wireType)
			}
			m.BizId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BizId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtBizId", wireType)
			}
			m.ExtBizId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExtBizId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RequestId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RequestId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTime", wireType)
			}
			m.CreateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSmsGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSmsGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("phone")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("sms_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DirectSendSmsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSmsGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DirectSendSmsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DirectSendSmsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Text", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Text = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BizId", wireType)
			}
			m.BizId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BizId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtBizId", wireType)
			}
			m.ExtBizId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExtBizId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSmsGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSmsGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("phone")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("text")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateVerifyCodeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSmsGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateVerifyCodeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateVerifyCodeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CodeLen", wireType)
			}
			m.CodeLen = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CodeLen |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSmsGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSmsGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("key")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateVerifyCodeResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSmsGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateVerifyCodeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateVerifyCodeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VerifyCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VerifyCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpireTime", wireType)
			}
			m.ExpireTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpireTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipSmsGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSmsGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("key")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("verify_code")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("expire_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ValidateVerifyCodeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSmsGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ValidateVerifyCodeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ValidateVerifyCodeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VerifyCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VerifyCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipSmsGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSmsGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("key")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("verify_code")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUrlValidApkUrlReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSmsGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUrlValidApkUrlReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUrlValidApkUrlReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSmsGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSmsGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("url")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUrlValidApkUrlResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSmsGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUrlValidApkUrlResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUrlValidApkUrlResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContentLength", wireType)
			}
			m.ContentLength = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContentLength |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSmsGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSmsGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("content_length")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DownLoadUrlReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSmsGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DownLoadUrlReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DownLoadUrlReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timeout", wireType)
			}
			m.Timeout = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timeout |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSmsGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSmsGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("url")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DownLoadUrlResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSmsGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DownLoadUrlResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DownLoadUrlResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Msg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSmsGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSmsGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("msg")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DownLoadUrlByteReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSmsGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DownLoadUrlByteReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DownLoadUrlByteReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timeout", wireType)
			}
			m.Timeout = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timeout |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSmsGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSmsGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("url")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DownLoadUrlByteResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSmsGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DownLoadUrlByteResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DownLoadUrlByteResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Msg = append(m.Msg[:0], dAtA[iNdEx:postIndex]...)
			if m.Msg == nil {
				m.Msg = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSmsGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSmsGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("msg")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *Foo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSmsGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Foo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Foo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipSmsGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSmsGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PostUrlDataReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSmsGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PostUrlDataReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PostUrlDataReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HeadInfoList", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.HeadInfoList = append(m.HeadInfoList, make([]byte, postIndex-iNdEx))
			copy(m.HeadInfoList[len(m.HeadInfoList)-1], dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DataInfo", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DataInfo = append(m.DataInfo[:0], dAtA[iNdEx:postIndex]...)
			if m.DataInfo == nil {
				m.DataInfo = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipSmsGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSmsGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("url")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("data_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PostUrlDataResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSmsGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PostUrlDataResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PostUrlDataResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RespMsg", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RespMsg = append(m.RespMsg[:0], dAtA[iNdEx:postIndex]...)
			if m.RespMsg == nil {
				m.RespMsg = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSmsGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSmsGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("resp_msg")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendVoiceVerifyCodeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSmsGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendVoiceVerifyCodeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendVoiceVerifyCodeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VerifyCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VerifyCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NationCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NationCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VoiceType", wireType)
			}
			m.VoiceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VoiceType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParamList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ParamList = append(m.ParamList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BizId", wireType)
			}
			m.BizId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BizId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtBizId", wireType)
			}
			m.ExtBizId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExtBizId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSmsGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSmsGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("phone")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("verify_code")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendVoiceVerifyCodeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSmsGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendVoiceVerifyCodeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendVoiceVerifyCodeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipSmsGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSmsGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendSmsWithProviderReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSmsGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendSmsWithProviderReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendSmsWithProviderReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Provider", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Provider = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phones", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phones = append(m.Phones, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SmsType", wireType)
			}
			m.SmsType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SmsType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParamList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ParamList = append(m.ParamList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MarketId", wireType)
			}
			m.MarketId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MarketId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSmsGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSmsGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("provider")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("sms_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendSmsWithProviderResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSmsGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendSmsWithProviderResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendSmsWithProviderResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipSmsGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSmsGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecordVerifyCodePassReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSmsGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecordVerifyCodePassReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecordVerifyCodePassReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VerifyCodeKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VerifyCodeKey = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VerifyAt", wireType)
			}
			m.VerifyAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VerifyAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSmsGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSmsGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("verify_code_key")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecordVerifyCodePassResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSmsGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecordVerifyCodePassResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecordVerifyCodePassResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipSmsGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSmsGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendMarketingSmsReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSmsGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendMarketingSmsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendMarketingSmsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phones", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phones = append(m.Phones, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Messages", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Messages = append(m.Messages, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BizId", wireType)
			}
			m.BizId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BizId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtBizId", wireType)
			}
			m.ExtBizId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExtBizId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AppId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSmsGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSmsGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendMarketingPhoneErrResult) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSmsGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendMarketingPhoneErrResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendMarketingPhoneErrResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			m.Code = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Code |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSmsGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSmsGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendMarketingSmsResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSmsGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendMarketingSmsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendMarketingSmsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReqResult", wireType)
			}
			m.ReqResult = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReqResult |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ErrPhones", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ErrPhones = append(m.ErrPhones, &SendMarketingPhoneErrResult{})
			if err := m.ErrPhones[len(m.ErrPhones)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSmsGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSmsGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("req_result")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AuthSmsReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSmsGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AuthSmsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AuthSmsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AppId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppSecret", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AppSecret = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientIp", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSmsGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ClientIp = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSmsGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSmsGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AuthSmsResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSmsGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AuthSmsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AuthSmsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			m.Result = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Result |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BizId", wireType)
			}
			m.BizId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BizId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSmsGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSmsGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipSmsGo(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowSmsGo
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowSmsGo
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthSmsGo
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowSmsGo
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipSmsGo(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthSmsGo = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowSmsGo   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("sms/sms-go.proto", fileDescriptorSmsGo) }

var fileDescriptorSmsGo = []byte{
	// 1615 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x58, 0x5b, 0x6f, 0xdb, 0xc8,
	0x15, 0x36, 0x2d, 0xdb, 0xb2, 0x8e, 0x92, 0x98, 0x3b, 0x76, 0x6c, 0xad, 0xbc, 0x75, 0x14, 0x26,
	0x69, 0xd5, 0x6e, 0x63, 0x2f, 0x82, 0xf6, 0xa5, 0x40, 0x81, 0xd5, 0x85, 0xaa, 0x89, 0xc8, 0x12,
	0x4b, 0xca, 0xee, 0xa6, 0x7d, 0x18, 0x30, 0xd2, 0x44, 0x66, 0x2d, 0x5e, 0xc2, 0x19, 0x39, 0xf1,
	0xa2, 0x40, 0x9f, 0x0b, 0xf4, 0xa1, 0xff, 0xa0, 0x8f, 0x45, 0x7f, 0xc0, 0xfe, 0x87, 0x7d, 0x2c,
	0xd0, 0xf7, 0x76, 0x9b, 0xed, 0xbd, 0xfd, 0x0f, 0x2d, 0x66, 0x78, 0x97, 0x68, 0x27, 0x40, 0xf4,
	0x44, 0x9e, 0xf3, 0xcd, 0xb9, 0x7c, 0x67, 0x66, 0xce, 0xa1, 0x40, 0xa6, 0x0e, 0x3d, 0xa2, 0x0e,
	0x7d, 0x3c, 0xf5, 0x0e, 0xfd, 0xc0, 0x63, 0x1e, 0xda, 0xa0, 0x0e, 0xc5, 0x53, 0xaf, 0xfe, 0x70,
	0xec, 0x39, 0x8e, 0xe7, 0x1e, 0xb1, 0xd9, 0xa5, 0x6f, 0x8f, 0x2f, 0x66, 0xe4, 0x88, 0x5e, 0x3c,
	0x9f, 0xdb, 0x33, 0x66, 0xbb, 0xec, 0xca, 0x27, 0x21, 0x5a, 0xf9, 0x7d, 0x09, 0xc0, 0x24, 0xee,
	0xc4, 0x74, 0xa8, 0x41, 0x5e, 0xa2, 0x3a, 0xac, 0xfb, 0xe7, 0x9e, 0x4b, 0x6a, 0x52, 0x63, 0xb5,
	0x59, 0x69, 0xaf, 0x7d, 0xf9, 0xa7, 0x7b, 0x2b, 0x46, 0x28, 0x42, 0xf7, 0x60, 0x93, 0x9b, 0xe6,
	0x8b, 0x6b, 0xab, 0x8d, 0xd5, 0xe6, 0xed, 0x48, 0x5d, 0xa6, 0x0e, 0x1d, 0x5d, 0xf9, 0x04, 0x7d,
	0x03, 0xc0, 0xb7, 0x02, 0xcb, 0xc1, 0x33, 0x9b, 0xb2, 0x5a, 0xa9, 0x51, 0x6a, 0x56, 0x8c, 0x8a,
	0x90, 0xf4, 0x6d, 0xca, 0xd0, 0x11, 0xc8, 0xaf, 0x6c, 0x76, 0xee, 0xcd, 0x19, 0x1e, 0x7b, 0xde,
	0x6c, 0xe2, 0xbd, 0x72, 0x6b, 0x6b, 0x0d, 0xa9, 0xb9, 0x19, 0xd9, 0xd9, 0x8a, 0xb4, 0x9d, 0x48,
	0x89, 0xbe, 0x0b, 0x5b, 0x97, 0x24, 0xb0, 0x5f, 0x5c, 0xe1, 0xb1, 0x37, 0x21, 0xf8, 0x82, 0x5c,
	0xd5, 0xd6, 0x1b, 0x52, 0x12, 0xd6, 0xed, 0x50, 0xd9, 0xf1, 0x26, 0xe4, 0x29, 0xb9, 0x42, 0x9f,
	0xc0, 0x07, 0x59, 0xf4, 0x9c, 0x5a, 0x53, 0x52, 0xdb, 0xc8, 0xe0, 0xb7, 0x52, 0xfc, 0x29, 0x57,
	0xa2, 0xfb, 0x50, 0x71, 0xac, 0xe0, 0x82, 0x30, 0x6c, 0x4f, 0x6a, 0xe5, 0x86, 0x94, 0x64, 0xb4,
	0x19, 0x8a, 0xb5, 0x09, 0xda, 0x87, 0x8d, 0xe7, 0xf6, 0xe7, 0x5c, 0xbf, 0x99, 0xd1, 0xaf, 0x3f,
	0xb7, 0x3f, 0xd7, 0x26, 0x48, 0x01, 0x20, 0xaf, 0x19, 0x8e, 0x00, 0x95, 0xac, 0x01, 0xf2, 0x9a,
	0xb5, 0x05, 0xe6, 0x01, 0x40, 0x40, 0x5e, 0xce, 0x09, 0x15, 0x4e, 0x20, 0x13, 0x4e, 0x25, 0x92,
	0x6b, 0x13, 0xf4, 0x08, 0xaa, 0xe3, 0x80, 0x58, 0x8c, 0x60, 0x66, 0x3b, 0xa4, 0x56, 0x6d, 0x48,
	0xcd, 0xb5, 0x08, 0x05, 0xa1, 0x62, 0x64, 0x3b, 0x44, 0xf9, 0x95, 0x04, 0x72, 0xd7, 0x0e, 0xc8,
	0x98, 0xbd, 0x63, 0xc5, 0x6a, 0xb0, 0xc6, 0xc8, 0x6b, 0x26, 0xaa, 0x15, 0xab, 0x84, 0x24, 0x93,
	0x57, 0xe9, 0x6d, 0x79, 0xad, 0x15, 0xe5, 0xa5, 0x0c, 0x60, 0xbb, 0x23, 0x22, 0x3b, 0x4b, 0x48,
	0xe5, 0xd1, 0xec, 0x42, 0x89, 0x97, 0x29, 0x1b, 0x0b, 0x17, 0xf0, 0xbd, 0x23, 0xaa, 0x32, 0x23,
	0x6e, 0x6d, 0x35, 0x63, 0xb0, 0xcc, 0xa5, 0x7d, 0xe2, 0x2a, 0xbf, 0x80, 0x9d, 0x65, 0x7b, 0xd4,
	0xbf, 0xd6, 0xe0, 0x23, 0xa8, 0x66, 0xaa, 0x9d, 0xcb, 0x10, 0xd2, 0x3a, 0x73, 0x18, 0x79, 0xed,
	0xdb, 0x41, 0xc4, 0x6c, 0x29, 0xb3, 0x6d, 0x21, 0x54, 0x08, 0x66, 0xcf, 0xe0, 0xee, 0x99, 0x35,
	0xb3, 0x27, 0xef, 0x9c, 0xcf, 0xbb, 0xb9, 0x57, 0x3e, 0x81, 0xdd, 0xce, 0x39, 0x19, 0x5f, 0x9c,
	0x06, 0x33, 0x61, 0xbf, 0xe5, 0xf3, 0xc7, 0xc8, 0xf0, 0x3c, 0x98, 0xe5, 0x0d, 0xcf, 0x83, 0x99,
	0xd2, 0x83, 0xbd, 0xc2, 0x15, 0xd4, 0x47, 0x1f, 0xc3, 0x9d, 0xb1, 0xe7, 0x32, 0xe2, 0x32, 0x4e,
	0xe3, 0x94, 0x9d, 0x8b, 0xd5, 0x71, 0x3a, 0xb7, 0x23, 0x5d, 0x5f, 0xa8, 0x94, 0x63, 0xb8, 0xd3,
	0xf5, 0x5e, 0xb9, 0x7d, 0xcf, 0x9a, 0xdc, 0xec, 0x11, 0x1d, 0x40, 0x99, 0x73, 0xe3, 0xcd, 0x59,
	0xbe, 0x32, 0x91, 0x50, 0xf9, 0x36, 0x6c, 0xe5, 0x2c, 0x85, 0x45, 0x71, 0xe8, 0x34, 0x6f, 0xca,
	0xa1, 0x53, 0xa5, 0x0f, 0x28, 0x03, 0x6d, 0x5f, 0x31, 0xf2, 0x3e, 0x8e, 0x1f, 0xc3, 0xf6, 0x92,
	0xb5, 0xbc, 0xf3, 0x5b, 0x59, 0xe7, 0xeb, 0x50, 0xea, 0x79, 0x9e, 0xf2, 0x12, 0xee, 0xe8, 0x1e,
	0x65, 0xa7, 0xc1, 0xac, 0x6b, 0x31, 0xeb, 0x26, 0xff, 0x0f, 0xe1, 0xce, 0x39, 0xb1, 0x26, 0xd8,
	0x76, 0x5f, 0x78, 0xe1, 0x95, 0xb5, 0xda, 0x28, 0x35, 0x6f, 0x19, 0xb7, 0xb8, 0x54, 0x73, 0x5f,
	0x78, 0xe2, 0xd6, 0xba, 0x0f, 0x95, 0x89, 0xc5, 0x2c, 0x81, 0x12, 0xfb, 0x27, 0x76, 0xba, 0xc9,
	0xc5, 0x1c, 0xa6, 0x3c, 0x81, 0xad, 0x9c, 0x4b, 0xea, 0xf3, 0xfd, 0x1e, 0x10, 0xea, 0xe3, 0xc5,
	0x48, 0xcb, 0x5c, 0x7a, 0x42, 0xa7, 0xca, 0x6f, 0x57, 0x61, 0x97, 0x9f, 0xe2, 0x33, 0xcf, 0x1e,
	0x2f, 0xec, 0xb9, 0x9b, 0x4e, 0xf4, 0x3b, 0x6e, 0x7b, 0x9e, 0xf2, 0xc2, 0xd9, 0xe6, 0x02, 0xbe,
	0xdc, 0xb5, 0x98, 0xed, 0xb9, 0xe1, 0xf2, 0xec, 0x6d, 0x0a, 0xa1, 0x42, 0x2c, 0x7f, 0x00, 0x70,
	0xc9, 0xe3, 0x0a, 0xef, 0xfa, 0x8d, 0x8c, 0x95, 0x8a, 0x90, 0x17, 0xdc, 0xf6, 0xe5, 0xc5, 0xdb,
	0xfe, 0x7d, 0x6f, 0x4e, 0xe5, 0x43, 0xd8, 0x2b, 0x24, 0x88, 0xfa, 0xca, 0x17, 0x52, 0x48, 0x9e,
	0xe9, 0xd0, 0x9f, 0xd8, 0xec, 0x5c, 0x0f, 0xbc, 0x4b, 0x7b, 0x42, 0x02, 0x4e, 0x5e, 0x03, 0x36,
	0xfd, 0xe8, 0x35, 0xc7, 0x5f, 0x22, 0x45, 0xbb, 0xb0, 0x21, 0xb8, 0xa4, 0xa2, 0xdc, 0x15, 0x23,
	0x7a, 0xcb, 0xb5, 0xb7, 0xd2, 0xdb, 0xdb, 0xdb, 0xda, 0x62, 0xc2, 0xb9, 0x6e, 0xb2, 0x5e, 0xd4,
	0x4d, 0xe2, 0x94, 0x96, 0xc2, 0xa6, 0xbe, 0xf2, 0x73, 0xd8, 0x33, 0xc8, 0xd8, 0x0b, 0x26, 0x69,
	0xaa, 0xba, 0x45, 0xc5, 0x0d, 0x5f, 0xd0, 0x06, 0xb3, 0x99, 0x2d, 0xb4, 0xc1, 0xfb, 0x50, 0x89,
	0xd0, 0x56, 0xfe, 0x5c, 0x6d, 0x86, 0xe2, 0x16, 0x53, 0xea, 0x50, 0x2b, 0xf6, 0x45, 0x7d, 0xe5,
	0x77, 0x12, 0x6c, 0xf3, 0x18, 0x4f, 0x44, 0xcc, 0xb6, 0x3b, 0x8d, 0xda, 0x4c, 0xca, 0x9a, 0x94,
	0x63, 0xad, 0x0e, 0x9b, 0x0e, 0xa1, 0xbc, 0x9d, 0xc6, 0x7c, 0x26, 0xef, 0xef, 0xdd, 0x64, 0xb8,
	0x01, 0xcb, 0xf7, 0x63, 0x3e, 0x93, 0xa3, 0x60, 0xf9, 0xbe, 0x36, 0x51, 0x4c, 0xd8, 0xcf, 0x05,
	0xaa, 0xf3, 0x80, 0xd4, 0x80, 0xd3, 0x39, 0x9f, 0xb1, 0xec, 0x29, 0x92, 0x0a, 0xfa, 0x62, 0x74,
	0x7c, 0xa4, 0xe6, 0x7a, 0xdc, 0x17, 0xb9, 0x44, 0xf9, 0x25, 0xec, 0x2c, 0x67, 0x4f, 0xfd, 0xa8,
	0x8d, 0xe3, 0x40, 0xd8, 0x16, 0xf4, 0xaf, 0x67, 0xda, 0x78, 0xe4, 0xb2, 0x0d, 0x40, 0x82, 0x00,
	0x67, 0x76, 0x57, 0xf5, 0xc9, 0x83, 0xc3, 0x70, 0x1c, 0x3b, 0xbc, 0x21, 0x56, 0xa3, 0x42, 0x82,
	0x40, 0x88, 0xa8, 0xf2, 0x12, 0xa0, 0x35, 0x67, 0xe7, 0x11, 0xeb, 0x29, 0x01, 0xd2, 0x12, 0x01,
	0x3c, 0x26, 0xae, 0xa4, 0x64, 0x1c, 0x90, 0xb0, 0xd4, 0xc9, 0x68, 0x61, 0xf9, 0xbe, 0x29, 0xc4,
	0x7c, 0x3b, 0x8c, 0x67, 0x36, 0xef, 0x19, 0xb6, 0x2f, 0xca, 0x90, 0x1c, 0x88, 0x50, 0xac, 0xf9,
	0xca, 0xaf, 0x25, 0xa8, 0x26, 0x3e, 0xa9, 0x8f, 0x3e, 0x82, 0x8d, 0x24, 0xcf, 0x94, 0x9f, 0x48,
	0x96, 0x29, 0xea, 0xea, 0x52, 0x51, 0x95, 0x36, 0x6c, 0x44, 0x5c, 0x54, 0xa1, 0x6c, 0x9e, 0x76,
	0x3a, 0xaa, 0x69, 0xca, 0x2b, 0x68, 0x07, 0x64, 0x6d, 0x70, 0xd6, 0xea, 0x6b, 0x5d, 0xdc, 0xd2,
	0x75, 0xac, 0x0d, 0x7a, 0x43, 0x59, 0x42, 0x77, 0xe1, 0x83, 0x58, 0xda, 0xe9, 0x6b, 0xea, 0x60,
	0x84, 0x35, 0x5d, 0x5e, 0xfd, 0xce, 0x17, 0x6b, 0x50, 0x56, 0x0d, 0x03, 0x9b, 0x27, 0x26, 0x7a,
	0x0c, 0x8d, 0xe8, 0x11, 0x9f, 0xa9, 0x46, 0x4f, 0x7b, 0xd6, 0x19, 0x76, 0x55, 0x2c, 0xd6, 0xb4,
	0x46, 0x2a, 0xee, 0xb5, 0xb4, 0xbe, 0xfc, 0xdf, 0xff, 0x45, 0x3f, 0x09, 0x3d, 0x80, 0xdd, 0x18,
	0x3e, 0x1a, 0x0e, 0xf1, 0x49, 0x6b, 0xf0, 0x0c, 0xeb, 0xc7, 0xc3, 0x81, 0x2a, 0xff, 0x27, 0x05,
	0x29, 0x70, 0x37, 0x06, 0x99, 0xea, 0xa0, 0x2b, 0x1e, 0x7a, 0x86, 0xfa, 0x63, 0xf9, 0xdf, 0x29,
	0xe6, 0x3e, 0xec, 0x24, 0x86, 0x9e, 0xe9, 0x2a, 0x8e, 0xe2, 0x94, 0xff, 0x95, 0x42, 0x1e, 0x41,
	0x2d, 0x86, 0x0c, 0x86, 0x58, 0x1d, 0x0c, 0x4f, 0x7f, 0x74, 0x8c, 0xf5, 0x96, 0xd1, 0x3a, 0x31,
	0xe5, 0x7f, 0xa6, 0xb0, 0x23, 0x50, 0x96, 0xbc, 0x8d, 0x8e, 0x0d, 0xd5, 0x3c, 0x3e, 0x1e, 0xf6,
	0xbb, 0x58, 0xfd, 0xac, 0xa3, 0xaa, 0x5d, 0xf9, 0x1f, 0x85, 0xe1, 0xc5, 0xec, 0x84, 0x29, 0xfc,
	0x3d, 0xc5, 0x7c, 0x0b, 0xea, 0x8b, 0x98, 0x33, 0xd5, 0xd0, 0x7a, 0x82, 0x1e, 0xf9, 0x6f, 0x29,
	0xf0, 0x21, 0xec, 0xa5, 0x41, 0x8e, 0xb0, 0x79, 0xaa, 0xeb, 0x43, 0x63, 0x84, 0xb5, 0xc1, 0x40,
	0xfe, 0x6b, 0x8a, 0x6a, 0xc0, 0x76, 0x8c, 0x32, 0xd4, 0xae, 0x66, 0x62, 0xd5, 0x30, 0x86, 0x86,
	0xfc, 0x75, 0x8a, 0x68, 0xc2, 0x7e, 0x8c, 0xd0, 0x5b, 0x86, 0xa9, 0xe2, 0x9e, 0x31, 0x3c, 0xc1,
	0x7a, 0x3b, 0x42, 0xbe, 0x49, 0x91, 0x1f, 0xc3, 0x01, 0x47, 0xc6, 0xc5, 0x7e, 0x8a, 0x4f, 0x8d,
	0x3e, 0xee, 0xb4, 0x06, 0xdc, 0xbd, 0xa1, 0xb6, 0x3a, 0xc7, 0xf2, 0x5f, 0x52, 0xf0, 0x37, 0xe1,
	0xc3, 0x65, 0x30, 0x47, 0xb6, 0xf4, 0xa7, 0xf2, 0x57, 0x85, 0x75, 0x8d, 0xf3, 0xe5, 0x64, 0xaa,
	0x86, 0xfc, 0xe7, 0x04, 0xf4, 0xe4, 0x8f, 0x1b, 0x50, 0x32, 0x1d, 0x8a, 0x7e, 0x08, 0xe5, 0xe8,
	0x92, 0x45, 0x28, 0x7b, 0xf8, 0xc2, 0x23, 0x55, 0xff, 0xe8, 0x30, 0xf9, 0x20, 0x3a, 0x34, 0x9f,
	0xb6, 0xc3, 0x0f, 0x22, 0xd5, 0xf1, 0xd9, 0x15, 0xd6, 0xdb, 0xca, 0x0a, 0xfa, 0x2c, 0xbc, 0xff,
	0x16, 0xee, 0x68, 0x74, 0xb0, 0x60, 0x6a, 0xa1, 0xef, 0xd4, 0xef, 0xdd, 0xa8, 0xa7, 0x7e, 0x68,
	0xb9, 0x60, 0xb4, 0x4b, 0x2d, 0x17, 0x4f, 0x8a, 0xa9, 0xe5, 0x6b, 0xe6, 0x42, 0x65, 0x05, 0x7d,
	0x0a, 0xd5, 0xcc, 0xa4, 0x84, 0x76, 0xe3, 0x15, 0xf9, 0x09, 0xb0, 0xbe, 0x57, 0x28, 0x17, 0x16,
	0x34, 0xb8, 0x9d, 0xfb, 0xb2, 0x40, 0xb5, 0x04, 0xbb, 0xf0, 0xc1, 0xf1, 0x56, 0x02, 0x3f, 0x85,
	0x6a, 0x66, 0x1a, 0x4a, 0x83, 0xc9, 0x4f, 0x65, 0x69, 0x30, 0x0b, 0xa3, 0x93, 0xb2, 0x82, 0xfa,
	0xb9, 0x89, 0x93, 0x0f, 0x7e, 0xa8, 0x5e, 0x10, 0x7a, 0x34, 0x5f, 0xd6, 0xf7, 0xaf, 0xd5, 0xc5,
	0xb4, 0x17, 0xcc, 0x11, 0xf9, 0x82, 0x2e, 0x4f, 0x61, 0xf9, 0x82, 0x16, 0x0d, 0x21, 0x2b, 0xe8,
	0x67, 0xb0, 0x53, 0xd4, 0x47, 0x51, 0xb2, 0xf4, 0x9a, 0x8e, 0x5e, 0x6f, 0xdc, 0x0c, 0x10, 0xc6,
	0x87, 0x20, 0x2f, 0x76, 0x22, 0xb4, 0x5f, 0xd8, 0x4c, 0x92, 0xba, 0x5c, 0xab, 0x14, 0x06, 0xbf,
	0x07, 0xe5, 0xe8, 0x96, 0x4f, 0xcf, 0x45, 0xda, 0x6a, 0xea, 0xdb, 0x4b, 0x32, 0xbe, 0xaa, 0xfd,
	0x83, 0x2f, 0xdf, 0x1c, 0x48, 0x7f, 0x78, 0x73, 0x20, 0x7d, 0xf5, 0xe6, 0x40, 0xfa, 0xcd, 0xd7,
	0x07, 0x2b, 0x3f, 0x6d, 0x4e, 0xbd, 0x99, 0xe5, 0x4e, 0x0f, 0xbf, 0xff, 0x84, 0xb1, 0xc3, 0xb1,
	0xe7, 0x1c, 0x89, 0x3f, 0x12, 0xc6, 0xde, 0xec, 0x88, 0x92, 0xe0, 0xd2, 0x1e, 0x93, 0xf8, 0xff,
	0x88, 0xff, 0x07, 0x00, 0x00, 0xff, 0xff, 0xb2, 0x38, 0xd9, 0x4c, 0x9c, 0x10, 0x00, 0x00,
}
