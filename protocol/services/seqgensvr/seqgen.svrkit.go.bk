package SeqGen

// Code generated by protoc-gen-svrkit-go. DO NOT EDIT.
// source: src/seqgensvr/seqgen.proto

/*
 This is a generated svrkit golang dev toolkit.
*/

import svrkit "gitlab.ttyuyin.com/golang/svrkit"

import context "golang.org/x/net/context"

import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

var _ = context.TODO

// Magic Number for SeqGen service
const SeqGenMagic = uint16(14000)

// SvrkitClient API for SeqGen service

type SeqGenClientInterface interface {
	GenSeqId(ctx context.Context, uin uint32, in *GenSeqReq, opts ...svrkit.CallOption) (*GenSeqRsp, error)
	GetLastestSeq(ctx context.Context, uin uint32, in *GetLastestSeqReq, opts ...svrkit.CallOption) (*GetLastestSeqResp, error)
	GetSeqMap(ctx context.Context, uin uint32, in *GetSeqMapReq, opts ...svrkit.CallOption) (*GetSeqMapResp, error)
	SetSeqMap(ctx context.Context, uin uint32, in *SetSeqMapReq, opts ...svrkit.CallOption) (*SetSeqMapResp, error)
	DelSeqMap(ctx context.Context, uin uint32, in *DelSeqMapReq, opts ...svrkit.CallOption) (*DelSeqMapResp, error)
	BatchSetUserGroupNeedUpdate(ctx context.Context, uin uint32, in *BatchSetUserGroupNeedUpdateReq, opts ...svrkit.CallOption) (*BatchSetUserGroupNeedUpdateResp, error)
	SetSeq(ctx context.Context, uin uint32, in *SetSeqReq, opts ...svrkit.CallOption) (*SetSeqResp, error)
	BatchGetLastestSeq(ctx context.Context, uin uint32, in *BatchGetLastestSeqReq, opts ...svrkit.CallOption) (*BatchGetLastestSeqResp, error)
}

type SeqGenSvrkitClient struct {
	cc *svrkit.ClientConn
}

func NewSeqGenSvrkitClient(cc *svrkit.ClientConn) SeqGenClientInterface {
	return &SeqGenSvrkitClient{cc}
}

const (
	commandSeqGenGetSelfSvnInfo              = 9995
	commandSeqGenEcho                        = 9999
	commandSeqGenGenSeqId                    = 1
	commandSeqGenGetLastestSeq               = 2
	commandSeqGenGetSeqMap                   = 3
	commandSeqGenSetSeqMap                   = 4
	commandSeqGenDelSeqMap                   = 5
	commandSeqGenBatchSetUserGroupNeedUpdate = 6
	commandSeqGenSetSeq                      = 7
	commandSeqGenBatchGetLastestSeq          = 8
)

func (c *SeqGenSvrkitClient) GenSeqId(ctx context.Context, uin uint32, in *GenSeqReq, opts ...svrkit.CallOption) (*GenSeqRsp, error) {
	out := new(GenSeqRsp)
	err := c.cc.Invoke(ctx, uin, commandSeqGenGenSeqId, "/SeqGen.SeqGen/GenSeqId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *SeqGenSvrkitClient) GetLastestSeq(ctx context.Context, uin uint32, in *GetLastestSeqReq, opts ...svrkit.CallOption) (*GetLastestSeqResp, error) {
	out := new(GetLastestSeqResp)
	err := c.cc.Invoke(ctx, uin, commandSeqGenGetLastestSeq, "/SeqGen.SeqGen/GetLastestSeq", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *SeqGenSvrkitClient) GetSeqMap(ctx context.Context, uin uint32, in *GetSeqMapReq, opts ...svrkit.CallOption) (*GetSeqMapResp, error) {
	out := new(GetSeqMapResp)
	err := c.cc.Invoke(ctx, uin, commandSeqGenGetSeqMap, "/SeqGen.SeqGen/GetSeqMap", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *SeqGenSvrkitClient) SetSeqMap(ctx context.Context, uin uint32, in *SetSeqMapReq, opts ...svrkit.CallOption) (*SetSeqMapResp, error) {
	out := new(SetSeqMapResp)
	err := c.cc.Invoke(ctx, uin, commandSeqGenSetSeqMap, "/SeqGen.SeqGen/SetSeqMap", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *SeqGenSvrkitClient) DelSeqMap(ctx context.Context, uin uint32, in *DelSeqMapReq, opts ...svrkit.CallOption) (*DelSeqMapResp, error) {
	out := new(DelSeqMapResp)
	err := c.cc.Invoke(ctx, uin, commandSeqGenDelSeqMap, "/SeqGen.SeqGen/DelSeqMap", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *SeqGenSvrkitClient) BatchSetUserGroupNeedUpdate(ctx context.Context, uin uint32, in *BatchSetUserGroupNeedUpdateReq, opts ...svrkit.CallOption) (*BatchSetUserGroupNeedUpdateResp, error) {
	out := new(BatchSetUserGroupNeedUpdateResp)
	err := c.cc.Invoke(ctx, uin, commandSeqGenBatchSetUserGroupNeedUpdate, "/SeqGen.SeqGen/BatchSetUserGroupNeedUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *SeqGenSvrkitClient) SetSeq(ctx context.Context, uin uint32, in *SetSeqReq, opts ...svrkit.CallOption) (*SetSeqResp, error) {
	out := new(SetSeqResp)
	err := c.cc.Invoke(ctx, uin, commandSeqGenSetSeq, "/SeqGen.SeqGen/SetSeq", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *SeqGenSvrkitClient) BatchGetLastestSeq(ctx context.Context, uin uint32, in *BatchGetLastestSeqReq, opts ...svrkit.CallOption) (*BatchGetLastestSeqResp, error) {
	out := new(BatchGetLastestSeqResp)
	err := c.cc.Invoke(ctx, uin, commandSeqGenBatchGetLastestSeq, "/SeqGen.SeqGen/BatchGetLastestSeq", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}
