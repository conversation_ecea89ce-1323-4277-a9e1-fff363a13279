// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/unified-search/unified-search.proto

package unified_search // import "golang.52tt.com/protocol/services/unified-search"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ObjectType int32

const (
	ObjectType_UNKNOWN ObjectType = 0
	ObjectType_USER    ObjectType = 1
	ObjectType_CHANNEL ObjectType = 2
	ObjectType_GUILD   ObjectType = 3
)

var ObjectType_name = map[int32]string{
	0: "UNKNOWN",
	1: "USER",
	2: "CHANNEL",
	3: "GUILD",
}
var ObjectType_value = map[string]int32{
	"UNKNOWN": 0,
	"USER":    1,
	"CHANNEL": 2,
	"GUILD":   3,
}

func (x ObjectType) String() string {
	return proto.EnumName(ObjectType_name, int32(x))
}
func (ObjectType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_unified_search_a041c5c698873da5, []int{0}
}

type AddRiskyReq_SceneType int32

const (
	AddRiskyReq_UNKNOWN    AddRiskyReq_SceneType = 0
	AddRiskyReq_EGG        AddRiskyReq_SceneType = 1
	AddRiskyReq_OnePiece   AddRiskyReq_SceneType = 2
	AddRiskyReq_StarTrek   AddRiskyReq_SceneType = 3
	AddRiskyReq_CatCanteen AddRiskyReq_SceneType = 4
	AddRiskyReq_StarTrain  AddRiskyReq_SceneType = 5
	// web 营收活动
	AddRiskyReq_WebRevenueActivity AddRiskyReq_SceneType = 6
	AddRiskyReq_WealthGod          AddRiskyReq_SceneType = 7
)

var AddRiskyReq_SceneType_name = map[int32]string{
	0: "UNKNOWN",
	1: "EGG",
	2: "OnePiece",
	3: "StarTrek",
	4: "CatCanteen",
	5: "StarTrain",
	6: "WebRevenueActivity",
	7: "WealthGod",
}
var AddRiskyReq_SceneType_value = map[string]int32{
	"UNKNOWN":            0,
	"EGG":                1,
	"OnePiece":           2,
	"StarTrek":           3,
	"CatCanteen":         4,
	"StarTrain":          5,
	"WebRevenueActivity": 6,
	"WealthGod":          7,
}

func (x AddRiskyReq_SceneType) String() string {
	return proto.EnumName(AddRiskyReq_SceneType_name, int32(x))
}
func (AddRiskyReq_SceneType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_unified_search_a041c5c698873da5, []int{8, 0}
}

type VisitedItem struct {
	Ids                  []string   `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
	ObjectType           ObjectType `protobuf:"varint,2,opt,name=object_type,json=objectType,proto3,enum=unified_search.ObjectType" json:"object_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *VisitedItem) Reset()         { *m = VisitedItem{} }
func (m *VisitedItem) String() string { return proto.CompactTextString(m) }
func (*VisitedItem) ProtoMessage()    {}
func (*VisitedItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search_a041c5c698873da5, []int{0}
}
func (m *VisitedItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VisitedItem.Unmarshal(m, b)
}
func (m *VisitedItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VisitedItem.Marshal(b, m, deterministic)
}
func (dst *VisitedItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VisitedItem.Merge(dst, src)
}
func (m *VisitedItem) XXX_Size() int {
	return xxx_messageInfo_VisitedItem.Size(m)
}
func (m *VisitedItem) XXX_DiscardUnknown() {
	xxx_messageInfo_VisitedItem.DiscardUnknown(m)
}

var xxx_messageInfo_VisitedItem proto.InternalMessageInfo

func (m *VisitedItem) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

func (m *VisitedItem) GetObjectType() ObjectType {
	if m != nil {
		return m.ObjectType
	}
	return ObjectType_UNKNOWN
}

type BatchAddAlreadyVisitedReq struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Items                []*VisitedItem `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *BatchAddAlreadyVisitedReq) Reset()         { *m = BatchAddAlreadyVisitedReq{} }
func (m *BatchAddAlreadyVisitedReq) String() string { return proto.CompactTextString(m) }
func (*BatchAddAlreadyVisitedReq) ProtoMessage()    {}
func (*BatchAddAlreadyVisitedReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search_a041c5c698873da5, []int{1}
}
func (m *BatchAddAlreadyVisitedReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddAlreadyVisitedReq.Unmarshal(m, b)
}
func (m *BatchAddAlreadyVisitedReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddAlreadyVisitedReq.Marshal(b, m, deterministic)
}
func (dst *BatchAddAlreadyVisitedReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddAlreadyVisitedReq.Merge(dst, src)
}
func (m *BatchAddAlreadyVisitedReq) XXX_Size() int {
	return xxx_messageInfo_BatchAddAlreadyVisitedReq.Size(m)
}
func (m *BatchAddAlreadyVisitedReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddAlreadyVisitedReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddAlreadyVisitedReq proto.InternalMessageInfo

func (m *BatchAddAlreadyVisitedReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BatchAddAlreadyVisitedReq) GetItems() []*VisitedItem {
	if m != nil {
		return m.Items
	}
	return nil
}

type BatchAddAlreadyVisitedResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAddAlreadyVisitedResp) Reset()         { *m = BatchAddAlreadyVisitedResp{} }
func (m *BatchAddAlreadyVisitedResp) String() string { return proto.CompactTextString(m) }
func (*BatchAddAlreadyVisitedResp) ProtoMessage()    {}
func (*BatchAddAlreadyVisitedResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search_a041c5c698873da5, []int{2}
}
func (m *BatchAddAlreadyVisitedResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddAlreadyVisitedResp.Unmarshal(m, b)
}
func (m *BatchAddAlreadyVisitedResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddAlreadyVisitedResp.Marshal(b, m, deterministic)
}
func (dst *BatchAddAlreadyVisitedResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddAlreadyVisitedResp.Merge(dst, src)
}
func (m *BatchAddAlreadyVisitedResp) XXX_Size() int {
	return xxx_messageInfo_BatchAddAlreadyVisitedResp.Size(m)
}
func (m *BatchAddAlreadyVisitedResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddAlreadyVisitedResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddAlreadyVisitedResp proto.InternalMessageInfo

type BatchDeleteAlreadyVisitedObjectTypesReq struct {
	Uid                  uint32       `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ObjectType           []ObjectType `protobuf:"varint,3,rep,packed,name=object_type,json=objectType,proto3,enum=unified_search.ObjectType" json:"object_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BatchDeleteAlreadyVisitedObjectTypesReq) Reset() {
	*m = BatchDeleteAlreadyVisitedObjectTypesReq{}
}
func (m *BatchDeleteAlreadyVisitedObjectTypesReq) String() string { return proto.CompactTextString(m) }
func (*BatchDeleteAlreadyVisitedObjectTypesReq) ProtoMessage()    {}
func (*BatchDeleteAlreadyVisitedObjectTypesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search_a041c5c698873da5, []int{3}
}
func (m *BatchDeleteAlreadyVisitedObjectTypesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDeleteAlreadyVisitedObjectTypesReq.Unmarshal(m, b)
}
func (m *BatchDeleteAlreadyVisitedObjectTypesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDeleteAlreadyVisitedObjectTypesReq.Marshal(b, m, deterministic)
}
func (dst *BatchDeleteAlreadyVisitedObjectTypesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDeleteAlreadyVisitedObjectTypesReq.Merge(dst, src)
}
func (m *BatchDeleteAlreadyVisitedObjectTypesReq) XXX_Size() int {
	return xxx_messageInfo_BatchDeleteAlreadyVisitedObjectTypesReq.Size(m)
}
func (m *BatchDeleteAlreadyVisitedObjectTypesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDeleteAlreadyVisitedObjectTypesReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDeleteAlreadyVisitedObjectTypesReq proto.InternalMessageInfo

func (m *BatchDeleteAlreadyVisitedObjectTypesReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BatchDeleteAlreadyVisitedObjectTypesReq) GetObjectType() []ObjectType {
	if m != nil {
		return m.ObjectType
	}
	return nil
}

type BatchDeleteAlreadyVisitedObjectTypesResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDeleteAlreadyVisitedObjectTypesResp) Reset() {
	*m = BatchDeleteAlreadyVisitedObjectTypesResp{}
}
func (m *BatchDeleteAlreadyVisitedObjectTypesResp) String() string { return proto.CompactTextString(m) }
func (*BatchDeleteAlreadyVisitedObjectTypesResp) ProtoMessage()    {}
func (*BatchDeleteAlreadyVisitedObjectTypesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search_a041c5c698873da5, []int{4}
}
func (m *BatchDeleteAlreadyVisitedObjectTypesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDeleteAlreadyVisitedObjectTypesResp.Unmarshal(m, b)
}
func (m *BatchDeleteAlreadyVisitedObjectTypesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDeleteAlreadyVisitedObjectTypesResp.Marshal(b, m, deterministic)
}
func (dst *BatchDeleteAlreadyVisitedObjectTypesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDeleteAlreadyVisitedObjectTypesResp.Merge(dst, src)
}
func (m *BatchDeleteAlreadyVisitedObjectTypesResp) XXX_Size() int {
	return xxx_messageInfo_BatchDeleteAlreadyVisitedObjectTypesResp.Size(m)
}
func (m *BatchDeleteAlreadyVisitedObjectTypesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDeleteAlreadyVisitedObjectTypesResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDeleteAlreadyVisitedObjectTypesResp proto.InternalMessageInfo

type BatchFilterAlreadyVisitedReq struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Items                []*VisitedItem `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *BatchFilterAlreadyVisitedReq) Reset()         { *m = BatchFilterAlreadyVisitedReq{} }
func (m *BatchFilterAlreadyVisitedReq) String() string { return proto.CompactTextString(m) }
func (*BatchFilterAlreadyVisitedReq) ProtoMessage()    {}
func (*BatchFilterAlreadyVisitedReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search_a041c5c698873da5, []int{5}
}
func (m *BatchFilterAlreadyVisitedReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchFilterAlreadyVisitedReq.Unmarshal(m, b)
}
func (m *BatchFilterAlreadyVisitedReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchFilterAlreadyVisitedReq.Marshal(b, m, deterministic)
}
func (dst *BatchFilterAlreadyVisitedReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchFilterAlreadyVisitedReq.Merge(dst, src)
}
func (m *BatchFilterAlreadyVisitedReq) XXX_Size() int {
	return xxx_messageInfo_BatchFilterAlreadyVisitedReq.Size(m)
}
func (m *BatchFilterAlreadyVisitedReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchFilterAlreadyVisitedReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchFilterAlreadyVisitedReq proto.InternalMessageInfo

func (m *BatchFilterAlreadyVisitedReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BatchFilterAlreadyVisitedReq) GetItems() []*VisitedItem {
	if m != nil {
		return m.Items
	}
	return nil
}

type BatchFilterAlreadyVisitedResp struct {
	Items                []*VisitedItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *BatchFilterAlreadyVisitedResp) Reset()         { *m = BatchFilterAlreadyVisitedResp{} }
func (m *BatchFilterAlreadyVisitedResp) String() string { return proto.CompactTextString(m) }
func (*BatchFilterAlreadyVisitedResp) ProtoMessage()    {}
func (*BatchFilterAlreadyVisitedResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search_a041c5c698873da5, []int{6}
}
func (m *BatchFilterAlreadyVisitedResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchFilterAlreadyVisitedResp.Unmarshal(m, b)
}
func (m *BatchFilterAlreadyVisitedResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchFilterAlreadyVisitedResp.Marshal(b, m, deterministic)
}
func (dst *BatchFilterAlreadyVisitedResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchFilterAlreadyVisitedResp.Merge(dst, src)
}
func (m *BatchFilterAlreadyVisitedResp) XXX_Size() int {
	return xxx_messageInfo_BatchFilterAlreadyVisitedResp.Size(m)
}
func (m *BatchFilterAlreadyVisitedResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchFilterAlreadyVisitedResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchFilterAlreadyVisitedResp proto.InternalMessageInfo

func (m *BatchFilterAlreadyVisitedResp) GetItems() []*VisitedItem {
	if m != nil {
		return m.Items
	}
	return nil
}

type RiskyObject struct {
	Id                   uint32     `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ObjectType           ObjectType `protobuf:"varint,2,opt,name=object_type,json=objectType,proto3,enum=unified_search.ObjectType" json:"object_type,omitempty"`
	ExpireSeconds        uint32     `protobuf:"varint,3,opt,name=expire_seconds,json=expireSeconds,proto3" json:"expire_seconds,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *RiskyObject) Reset()         { *m = RiskyObject{} }
func (m *RiskyObject) String() string { return proto.CompactTextString(m) }
func (*RiskyObject) ProtoMessage()    {}
func (*RiskyObject) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search_a041c5c698873da5, []int{7}
}
func (m *RiskyObject) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RiskyObject.Unmarshal(m, b)
}
func (m *RiskyObject) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RiskyObject.Marshal(b, m, deterministic)
}
func (dst *RiskyObject) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RiskyObject.Merge(dst, src)
}
func (m *RiskyObject) XXX_Size() int {
	return xxx_messageInfo_RiskyObject.Size(m)
}
func (m *RiskyObject) XXX_DiscardUnknown() {
	xxx_messageInfo_RiskyObject.DiscardUnknown(m)
}

var xxx_messageInfo_RiskyObject proto.InternalMessageInfo

func (m *RiskyObject) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *RiskyObject) GetObjectType() ObjectType {
	if m != nil {
		return m.ObjectType
	}
	return ObjectType_UNKNOWN
}

func (m *RiskyObject) GetExpireSeconds() uint32 {
	if m != nil {
		return m.ExpireSeconds
	}
	return 0
}

type AddRiskyReq struct {
	Objects []*RiskyObject        `protobuf:"bytes,1,rep,name=objects,proto3" json:"objects,omitempty"`
	Scene   AddRiskyReq_SceneType `protobuf:"varint,2,opt,name=scene,proto3,enum=unified_search.AddRiskyReq_SceneType" json:"scene,omitempty"`
	// 场景详情
	SceneDetail          string   `protobuf:"bytes,3,opt,name=scene_detail,json=sceneDetail,proto3" json:"scene_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddRiskyReq) Reset()         { *m = AddRiskyReq{} }
func (m *AddRiskyReq) String() string { return proto.CompactTextString(m) }
func (*AddRiskyReq) ProtoMessage()    {}
func (*AddRiskyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search_a041c5c698873da5, []int{8}
}
func (m *AddRiskyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddRiskyReq.Unmarshal(m, b)
}
func (m *AddRiskyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddRiskyReq.Marshal(b, m, deterministic)
}
func (dst *AddRiskyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddRiskyReq.Merge(dst, src)
}
func (m *AddRiskyReq) XXX_Size() int {
	return xxx_messageInfo_AddRiskyReq.Size(m)
}
func (m *AddRiskyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddRiskyReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddRiskyReq proto.InternalMessageInfo

func (m *AddRiskyReq) GetObjects() []*RiskyObject {
	if m != nil {
		return m.Objects
	}
	return nil
}

func (m *AddRiskyReq) GetScene() AddRiskyReq_SceneType {
	if m != nil {
		return m.Scene
	}
	return AddRiskyReq_UNKNOWN
}

func (m *AddRiskyReq) GetSceneDetail() string {
	if m != nil {
		return m.SceneDetail
	}
	return ""
}

type AddRiskyResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddRiskyResp) Reset()         { *m = AddRiskyResp{} }
func (m *AddRiskyResp) String() string { return proto.CompactTextString(m) }
func (*AddRiskyResp) ProtoMessage()    {}
func (*AddRiskyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search_a041c5c698873da5, []int{9}
}
func (m *AddRiskyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddRiskyResp.Unmarshal(m, b)
}
func (m *AddRiskyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddRiskyResp.Marshal(b, m, deterministic)
}
func (dst *AddRiskyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddRiskyResp.Merge(dst, src)
}
func (m *AddRiskyResp) XXX_Size() int {
	return xxx_messageInfo_AddRiskyResp.Size(m)
}
func (m *AddRiskyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddRiskyResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddRiskyResp proto.InternalMessageInfo

type RemoveRiskyReq struct {
	Objects              []*RiskyObject `protobuf:"bytes,1,rep,name=objects,proto3" json:"objects,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *RemoveRiskyReq) Reset()         { *m = RemoveRiskyReq{} }
func (m *RemoveRiskyReq) String() string { return proto.CompactTextString(m) }
func (*RemoveRiskyReq) ProtoMessage()    {}
func (*RemoveRiskyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search_a041c5c698873da5, []int{10}
}
func (m *RemoveRiskyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveRiskyReq.Unmarshal(m, b)
}
func (m *RemoveRiskyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveRiskyReq.Marshal(b, m, deterministic)
}
func (dst *RemoveRiskyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveRiskyReq.Merge(dst, src)
}
func (m *RemoveRiskyReq) XXX_Size() int {
	return xxx_messageInfo_RemoveRiskyReq.Size(m)
}
func (m *RemoveRiskyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveRiskyReq.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveRiskyReq proto.InternalMessageInfo

func (m *RemoveRiskyReq) GetObjects() []*RiskyObject {
	if m != nil {
		return m.Objects
	}
	return nil
}

type RemoveRiskyResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveRiskyResp) Reset()         { *m = RemoveRiskyResp{} }
func (m *RemoveRiskyResp) String() string { return proto.CompactTextString(m) }
func (*RemoveRiskyResp) ProtoMessage()    {}
func (*RemoveRiskyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search_a041c5c698873da5, []int{11}
}
func (m *RemoveRiskyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveRiskyResp.Unmarshal(m, b)
}
func (m *RemoveRiskyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveRiskyResp.Marshal(b, m, deterministic)
}
func (dst *RemoveRiskyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveRiskyResp.Merge(dst, src)
}
func (m *RemoveRiskyResp) XXX_Size() int {
	return xxx_messageInfo_RemoveRiskyResp.Size(m)
}
func (m *RemoveRiskyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveRiskyResp.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveRiskyResp proto.InternalMessageInfo

type CheckRiskyReq struct {
	Objects              []*RiskyObject `protobuf:"bytes,1,rep,name=objects,proto3" json:"objects,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *CheckRiskyReq) Reset()         { *m = CheckRiskyReq{} }
func (m *CheckRiskyReq) String() string { return proto.CompactTextString(m) }
func (*CheckRiskyReq) ProtoMessage()    {}
func (*CheckRiskyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search_a041c5c698873da5, []int{12}
}
func (m *CheckRiskyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckRiskyReq.Unmarshal(m, b)
}
func (m *CheckRiskyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckRiskyReq.Marshal(b, m, deterministic)
}
func (dst *CheckRiskyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckRiskyReq.Merge(dst, src)
}
func (m *CheckRiskyReq) XXX_Size() int {
	return xxx_messageInfo_CheckRiskyReq.Size(m)
}
func (m *CheckRiskyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckRiskyReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckRiskyReq proto.InternalMessageInfo

func (m *CheckRiskyReq) GetObjects() []*RiskyObject {
	if m != nil {
		return m.Objects
	}
	return nil
}

type CheckRiskyResp struct {
	Hits                 []bool   `protobuf:"varint,1,rep,packed,name=hits,proto3" json:"hits,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckRiskyResp) Reset()         { *m = CheckRiskyResp{} }
func (m *CheckRiskyResp) String() string { return proto.CompactTextString(m) }
func (*CheckRiskyResp) ProtoMessage()    {}
func (*CheckRiskyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search_a041c5c698873da5, []int{13}
}
func (m *CheckRiskyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckRiskyResp.Unmarshal(m, b)
}
func (m *CheckRiskyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckRiskyResp.Marshal(b, m, deterministic)
}
func (dst *CheckRiskyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckRiskyResp.Merge(dst, src)
}
func (m *CheckRiskyResp) XXX_Size() int {
	return xxx_messageInfo_CheckRiskyResp.Size(m)
}
func (m *CheckRiskyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckRiskyResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckRiskyResp proto.InternalMessageInfo

func (m *CheckRiskyResp) GetHits() []bool {
	if m != nil {
		return m.Hits
	}
	return nil
}

type CheckRiskyByIdReq struct {
	ObjectType           ObjectType `protobuf:"varint,1,opt,name=object_type,json=objectType,proto3,enum=unified_search.ObjectType" json:"object_type,omitempty"`
	Ids                  []uint32   `protobuf:"varint,2,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *CheckRiskyByIdReq) Reset()         { *m = CheckRiskyByIdReq{} }
func (m *CheckRiskyByIdReq) String() string { return proto.CompactTextString(m) }
func (*CheckRiskyByIdReq) ProtoMessage()    {}
func (*CheckRiskyByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search_a041c5c698873da5, []int{14}
}
func (m *CheckRiskyByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckRiskyByIdReq.Unmarshal(m, b)
}
func (m *CheckRiskyByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckRiskyByIdReq.Marshal(b, m, deterministic)
}
func (dst *CheckRiskyByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckRiskyByIdReq.Merge(dst, src)
}
func (m *CheckRiskyByIdReq) XXX_Size() int {
	return xxx_messageInfo_CheckRiskyByIdReq.Size(m)
}
func (m *CheckRiskyByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckRiskyByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckRiskyByIdReq proto.InternalMessageInfo

func (m *CheckRiskyByIdReq) GetObjectType() ObjectType {
	if m != nil {
		return m.ObjectType
	}
	return ObjectType_UNKNOWN
}

func (m *CheckRiskyByIdReq) GetIds() []uint32 {
	if m != nil {
		return m.Ids
	}
	return nil
}

type CheckRiskyByIdResp struct {
	Hits []bool `protobuf:"varint,1,rep,packed,name=hits,proto3" json:"hits,omitempty"`
	// 命中结果， key 为对象id， value 为命中结果， 检查命中才存在 map.
	CheckResultMap       map[uint32]*RiskCheckResult `protobuf:"bytes,2,rep,name=check_result_map,json=checkResultMap,proto3" json:"check_result_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *CheckRiskyByIdResp) Reset()         { *m = CheckRiskyByIdResp{} }
func (m *CheckRiskyByIdResp) String() string { return proto.CompactTextString(m) }
func (*CheckRiskyByIdResp) ProtoMessage()    {}
func (*CheckRiskyByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search_a041c5c698873da5, []int{15}
}
func (m *CheckRiskyByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckRiskyByIdResp.Unmarshal(m, b)
}
func (m *CheckRiskyByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckRiskyByIdResp.Marshal(b, m, deterministic)
}
func (dst *CheckRiskyByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckRiskyByIdResp.Merge(dst, src)
}
func (m *CheckRiskyByIdResp) XXX_Size() int {
	return xxx_messageInfo_CheckRiskyByIdResp.Size(m)
}
func (m *CheckRiskyByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckRiskyByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckRiskyByIdResp proto.InternalMessageInfo

func (m *CheckRiskyByIdResp) GetHits() []bool {
	if m != nil {
		return m.Hits
	}
	return nil
}

func (m *CheckRiskyByIdResp) GetCheckResultMap() map[uint32]*RiskCheckResult {
	if m != nil {
		return m.CheckResultMap
	}
	return nil
}

type RiskCheckResult struct {
	// 命中场景类型， see AddRiskyReq.SceneType
	SceneType uint32 `protobuf:"varint,1,opt,name=scene_type,json=sceneType,proto3" json:"scene_type,omitempty"`
	// 命中场景详情
	SceneDetail          string   `protobuf:"bytes,2,opt,name=scene_detail,json=sceneDetail,proto3" json:"scene_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RiskCheckResult) Reset()         { *m = RiskCheckResult{} }
func (m *RiskCheckResult) String() string { return proto.CompactTextString(m) }
func (*RiskCheckResult) ProtoMessage()    {}
func (*RiskCheckResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search_a041c5c698873da5, []int{16}
}
func (m *RiskCheckResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RiskCheckResult.Unmarshal(m, b)
}
func (m *RiskCheckResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RiskCheckResult.Marshal(b, m, deterministic)
}
func (dst *RiskCheckResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RiskCheckResult.Merge(dst, src)
}
func (m *RiskCheckResult) XXX_Size() int {
	return xxx_messageInfo_RiskCheckResult.Size(m)
}
func (m *RiskCheckResult) XXX_DiscardUnknown() {
	xxx_messageInfo_RiskCheckResult.DiscardUnknown(m)
}

var xxx_messageInfo_RiskCheckResult proto.InternalMessageInfo

func (m *RiskCheckResult) GetSceneType() uint32 {
	if m != nil {
		return m.SceneType
	}
	return 0
}

func (m *RiskCheckResult) GetSceneDetail() string {
	if m != nil {
		return m.SceneDetail
	}
	return ""
}

func init() {
	proto.RegisterType((*VisitedItem)(nil), "unified_search.VisitedItem")
	proto.RegisterType((*BatchAddAlreadyVisitedReq)(nil), "unified_search.BatchAddAlreadyVisitedReq")
	proto.RegisterType((*BatchAddAlreadyVisitedResp)(nil), "unified_search.BatchAddAlreadyVisitedResp")
	proto.RegisterType((*BatchDeleteAlreadyVisitedObjectTypesReq)(nil), "unified_search.BatchDeleteAlreadyVisitedObjectTypesReq")
	proto.RegisterType((*BatchDeleteAlreadyVisitedObjectTypesResp)(nil), "unified_search.BatchDeleteAlreadyVisitedObjectTypesResp")
	proto.RegisterType((*BatchFilterAlreadyVisitedReq)(nil), "unified_search.BatchFilterAlreadyVisitedReq")
	proto.RegisterType((*BatchFilterAlreadyVisitedResp)(nil), "unified_search.BatchFilterAlreadyVisitedResp")
	proto.RegisterType((*RiskyObject)(nil), "unified_search.RiskyObject")
	proto.RegisterType((*AddRiskyReq)(nil), "unified_search.AddRiskyReq")
	proto.RegisterType((*AddRiskyResp)(nil), "unified_search.AddRiskyResp")
	proto.RegisterType((*RemoveRiskyReq)(nil), "unified_search.RemoveRiskyReq")
	proto.RegisterType((*RemoveRiskyResp)(nil), "unified_search.RemoveRiskyResp")
	proto.RegisterType((*CheckRiskyReq)(nil), "unified_search.CheckRiskyReq")
	proto.RegisterType((*CheckRiskyResp)(nil), "unified_search.CheckRiskyResp")
	proto.RegisterType((*CheckRiskyByIdReq)(nil), "unified_search.CheckRiskyByIdReq")
	proto.RegisterType((*CheckRiskyByIdResp)(nil), "unified_search.CheckRiskyByIdResp")
	proto.RegisterMapType((map[uint32]*RiskCheckResult)(nil), "unified_search.CheckRiskyByIdResp.CheckResultMapEntry")
	proto.RegisterType((*RiskCheckResult)(nil), "unified_search.RiskCheckResult")
	proto.RegisterEnum("unified_search.ObjectType", ObjectType_name, ObjectType_value)
	proto.RegisterEnum("unified_search.AddRiskyReq_SceneType", AddRiskyReq_SceneType_name, AddRiskyReq_SceneType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UnifiedSearchClient is the client API for UnifiedSearch service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UnifiedSearchClient interface {
	// 开黑列表自定义筛选器
	AddRisky(ctx context.Context, in *AddRiskyReq, opts ...grpc.CallOption) (*AddRiskyResp, error)
	RemoveRisky(ctx context.Context, in *RemoveRiskyReq, opts ...grpc.CallOption) (*RemoveRiskyResp, error)
	CheckRisky(ctx context.Context, in *CheckRiskyReq, opts ...grpc.CallOption) (*CheckRiskyResp, error)
	CheckRiskyById(ctx context.Context, in *CheckRiskyByIdReq, opts ...grpc.CallOption) (*CheckRiskyByIdResp, error)
	//
	BatchAddAlreadyVisited(ctx context.Context, in *BatchAddAlreadyVisitedReq, opts ...grpc.CallOption) (*BatchAddAlreadyVisitedResp, error)
	BatchDeleteAlreadyVisitedObjectTypes(ctx context.Context, in *BatchDeleteAlreadyVisitedObjectTypesReq, opts ...grpc.CallOption) (*BatchDeleteAlreadyVisitedObjectTypesResp, error)
	BatchFilterAlreadyVisited(ctx context.Context, in *BatchFilterAlreadyVisitedReq, opts ...grpc.CallOption) (*BatchFilterAlreadyVisitedResp, error)
}

type unifiedSearchClient struct {
	cc *grpc.ClientConn
}

func NewUnifiedSearchClient(cc *grpc.ClientConn) UnifiedSearchClient {
	return &unifiedSearchClient{cc}
}

func (c *unifiedSearchClient) AddRisky(ctx context.Context, in *AddRiskyReq, opts ...grpc.CallOption) (*AddRiskyResp, error) {
	out := new(AddRiskyResp)
	err := c.cc.Invoke(ctx, "/unified_search.UnifiedSearch/AddRisky", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *unifiedSearchClient) RemoveRisky(ctx context.Context, in *RemoveRiskyReq, opts ...grpc.CallOption) (*RemoveRiskyResp, error) {
	out := new(RemoveRiskyResp)
	err := c.cc.Invoke(ctx, "/unified_search.UnifiedSearch/RemoveRisky", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *unifiedSearchClient) CheckRisky(ctx context.Context, in *CheckRiskyReq, opts ...grpc.CallOption) (*CheckRiskyResp, error) {
	out := new(CheckRiskyResp)
	err := c.cc.Invoke(ctx, "/unified_search.UnifiedSearch/CheckRisky", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *unifiedSearchClient) CheckRiskyById(ctx context.Context, in *CheckRiskyByIdReq, opts ...grpc.CallOption) (*CheckRiskyByIdResp, error) {
	out := new(CheckRiskyByIdResp)
	err := c.cc.Invoke(ctx, "/unified_search.UnifiedSearch/CheckRiskyById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *unifiedSearchClient) BatchAddAlreadyVisited(ctx context.Context, in *BatchAddAlreadyVisitedReq, opts ...grpc.CallOption) (*BatchAddAlreadyVisitedResp, error) {
	out := new(BatchAddAlreadyVisitedResp)
	err := c.cc.Invoke(ctx, "/unified_search.UnifiedSearch/BatchAddAlreadyVisited", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *unifiedSearchClient) BatchDeleteAlreadyVisitedObjectTypes(ctx context.Context, in *BatchDeleteAlreadyVisitedObjectTypesReq, opts ...grpc.CallOption) (*BatchDeleteAlreadyVisitedObjectTypesResp, error) {
	out := new(BatchDeleteAlreadyVisitedObjectTypesResp)
	err := c.cc.Invoke(ctx, "/unified_search.UnifiedSearch/BatchDeleteAlreadyVisitedObjectTypes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *unifiedSearchClient) BatchFilterAlreadyVisited(ctx context.Context, in *BatchFilterAlreadyVisitedReq, opts ...grpc.CallOption) (*BatchFilterAlreadyVisitedResp, error) {
	out := new(BatchFilterAlreadyVisitedResp)
	err := c.cc.Invoke(ctx, "/unified_search.UnifiedSearch/BatchFilterAlreadyVisited", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UnifiedSearchServer is the server API for UnifiedSearch service.
type UnifiedSearchServer interface {
	// 开黑列表自定义筛选器
	AddRisky(context.Context, *AddRiskyReq) (*AddRiskyResp, error)
	RemoveRisky(context.Context, *RemoveRiskyReq) (*RemoveRiskyResp, error)
	CheckRisky(context.Context, *CheckRiskyReq) (*CheckRiskyResp, error)
	CheckRiskyById(context.Context, *CheckRiskyByIdReq) (*CheckRiskyByIdResp, error)
	//
	BatchAddAlreadyVisited(context.Context, *BatchAddAlreadyVisitedReq) (*BatchAddAlreadyVisitedResp, error)
	BatchDeleteAlreadyVisitedObjectTypes(context.Context, *BatchDeleteAlreadyVisitedObjectTypesReq) (*BatchDeleteAlreadyVisitedObjectTypesResp, error)
	BatchFilterAlreadyVisited(context.Context, *BatchFilterAlreadyVisitedReq) (*BatchFilterAlreadyVisitedResp, error)
}

func RegisterUnifiedSearchServer(s *grpc.Server, srv UnifiedSearchServer) {
	s.RegisterService(&_UnifiedSearch_serviceDesc, srv)
}

func _UnifiedSearch_AddRisky_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddRiskyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UnifiedSearchServer).AddRisky(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/unified_search.UnifiedSearch/AddRisky",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UnifiedSearchServer).AddRisky(ctx, req.(*AddRiskyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UnifiedSearch_RemoveRisky_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveRiskyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UnifiedSearchServer).RemoveRisky(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/unified_search.UnifiedSearch/RemoveRisky",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UnifiedSearchServer).RemoveRisky(ctx, req.(*RemoveRiskyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UnifiedSearch_CheckRisky_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckRiskyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UnifiedSearchServer).CheckRisky(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/unified_search.UnifiedSearch/CheckRisky",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UnifiedSearchServer).CheckRisky(ctx, req.(*CheckRiskyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UnifiedSearch_CheckRiskyById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckRiskyByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UnifiedSearchServer).CheckRiskyById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/unified_search.UnifiedSearch/CheckRiskyById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UnifiedSearchServer).CheckRiskyById(ctx, req.(*CheckRiskyByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UnifiedSearch_BatchAddAlreadyVisited_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAddAlreadyVisitedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UnifiedSearchServer).BatchAddAlreadyVisited(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/unified_search.UnifiedSearch/BatchAddAlreadyVisited",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UnifiedSearchServer).BatchAddAlreadyVisited(ctx, req.(*BatchAddAlreadyVisitedReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UnifiedSearch_BatchDeleteAlreadyVisitedObjectTypes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDeleteAlreadyVisitedObjectTypesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UnifiedSearchServer).BatchDeleteAlreadyVisitedObjectTypes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/unified_search.UnifiedSearch/BatchDeleteAlreadyVisitedObjectTypes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UnifiedSearchServer).BatchDeleteAlreadyVisitedObjectTypes(ctx, req.(*BatchDeleteAlreadyVisitedObjectTypesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UnifiedSearch_BatchFilterAlreadyVisited_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchFilterAlreadyVisitedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UnifiedSearchServer).BatchFilterAlreadyVisited(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/unified_search.UnifiedSearch/BatchFilterAlreadyVisited",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UnifiedSearchServer).BatchFilterAlreadyVisited(ctx, req.(*BatchFilterAlreadyVisitedReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _UnifiedSearch_serviceDesc = grpc.ServiceDesc{
	ServiceName: "unified_search.UnifiedSearch",
	HandlerType: (*UnifiedSearchServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddRisky",
			Handler:    _UnifiedSearch_AddRisky_Handler,
		},
		{
			MethodName: "RemoveRisky",
			Handler:    _UnifiedSearch_RemoveRisky_Handler,
		},
		{
			MethodName: "CheckRisky",
			Handler:    _UnifiedSearch_CheckRisky_Handler,
		},
		{
			MethodName: "CheckRiskyById",
			Handler:    _UnifiedSearch_CheckRiskyById_Handler,
		},
		{
			MethodName: "BatchAddAlreadyVisited",
			Handler:    _UnifiedSearch_BatchAddAlreadyVisited_Handler,
		},
		{
			MethodName: "BatchDeleteAlreadyVisitedObjectTypes",
			Handler:    _UnifiedSearch_BatchDeleteAlreadyVisitedObjectTypes_Handler,
		},
		{
			MethodName: "BatchFilterAlreadyVisited",
			Handler:    _UnifiedSearch_BatchFilterAlreadyVisited_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/unified-search/unified-search.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/unified-search/unified-search.proto", fileDescriptor_unified_search_a041c5c698873da5)
}

var fileDescriptor_unified_search_a041c5c698873da5 = []byte{
	// 873 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x56, 0xdd, 0x6e, 0xdb, 0x56,
	0x0c, 0x9e, 0xec, 0xb8, 0x89, 0xa9, 0x58, 0x55, 0x39, 0xa0, 0xf0, 0xd4, 0xa4, 0x75, 0x85, 0x16,
	0xf3, 0x82, 0xd5, 0xd9, 0x3c, 0x64, 0x2b, 0x96, 0x2b, 0x27, 0x71, 0xbd, 0xa0, 0x9d, 0x33, 0xc8,
	0xcd, 0x02, 0x0c, 0x03, 0x5c, 0x59, 0xe2, 0xea, 0x33, 0xcb, 0xd2, 0x89, 0xce, 0xb1, 0x11, 0x5d,
	0x0e, 0x7b, 0x8a, 0xbd, 0xc2, 0x5e, 0x67, 0xef, 0xb1, 0x67, 0x18, 0x24, 0x39, 0xfe, 0x51, 0xec,
	0xfc, 0xac, 0xe8, 0xdd, 0x11, 0xf9, 0xf1, 0xe3, 0x77, 0x28, 0x52, 0x14, 0xd4, 0xa5, 0xdc, 0x3d,
	0x1f, 0x31, 0x67, 0x20, 0x98, 0x37, 0xa6, 0x70, 0x77, 0xe4, 0xb3, 0xdf, 0x18, 0xb9, 0x2f, 0x04,
	0xd9, 0xa1, 0xd3, 0xcf, 0x3c, 0xd6, 0x78, 0x18, 0xc8, 0x00, 0xb5, 0x89, 0xb5, 0x9b, 0x5a, 0xcd,
	0x5f, 0x41, 0xfd, 0x99, 0x09, 0x26, 0xc9, 0x3d, 0x96, 0x34, 0x44, 0x1d, 0xf2, 0xcc, 0x15, 0x65,
	0xa5, 0x92, 0xaf, 0x16, 0xad, 0xf8, 0x88, 0xfb, 0xa0, 0x06, 0xbd, 0xdf, 0xc9, 0x91, 0x5d, 0x19,
	0x71, 0x2a, 0xe7, 0x2a, 0x4a, 0x55, 0xab, 0x1b, 0xb5, 0x45, 0x9a, 0xda, 0x49, 0x02, 0x79, 0x1b,
	0x71, 0xb2, 0x20, 0x98, 0x9e, 0xcd, 0x77, 0xf0, 0xd9, 0x81, 0x2d, 0x9d, 0x7e, 0xc3, 0x75, 0x1b,
	0x5e, 0x48, 0xb6, 0x1b, 0x4d, 0x92, 0x59, 0x74, 0x1e, 0xe7, 0x1a, 0x31, 0xb7, 0xac, 0x54, 0x94,
	0x6a, 0xc9, 0x8a, 0x8f, 0xf8, 0x35, 0x14, 0x98, 0xa4, 0xa1, 0x28, 0xe7, 0x2a, 0xf9, 0xaa, 0x5a,
	0x7f, 0x94, 0xcd, 0x32, 0xa7, 0xd4, 0x4a, 0x91, 0xe6, 0x16, 0x18, 0xab, 0x32, 0x08, 0x6e, 0x5e,
	0xc0, 0xe7, 0x89, 0xf7, 0x88, 0x3c, 0x92, 0xb4, 0x08, 0x98, 0x49, 0x16, 0xcb, 0xd5, 0x64, 0x6e,
	0x9e, 0xaf, 0xe4, 0xef, 0x70, 0xf3, 0x1d, 0xa8, 0xde, 0x2e, 0xb3, 0xe0, 0xa6, 0x03, 0x5b, 0x09,
	0xf6, 0x15, 0xf3, 0x24, 0x85, 0x1f, 0xa9, 0x50, 0x16, 0x6c, 0x5f, 0x93, 0x44, 0xf0, 0x19, 0xa7,
	0x72, 0x6b, 0xce, 0x3f, 0x14, 0x50, 0x2d, 0x26, 0x06, 0x51, 0x7a, 0x23, 0xd4, 0x20, 0x37, 0xd5,
	0x99, 0xbb, 0x5a, 0xc1, 0x3b, 0xf5, 0x0e, 0x3e, 0x07, 0x8d, 0x2e, 0x38, 0x0b, 0xa9, 0x2b, 0xc8,
	0x09, 0x7c, 0x57, 0x94, 0xf3, 0x09, 0x71, 0x29, 0xb5, 0x76, 0x52, 0xa3, 0xf9, 0x77, 0x0e, 0xd4,
	0x86, 0xeb, 0x26, 0x32, 0xe2, 0x62, 0xed, 0xc1, 0x7a, 0x4a, 0xb2, 0xf2, 0x22, 0x73, 0x8a, 0xad,
	0x4b, 0x2c, 0xee, 0x43, 0x41, 0x38, 0xe4, 0x5f, 0x8a, 0x7c, 0x9e, 0x0d, 0x9a, 0x4b, 0x51, 0xeb,
	0xc4, 0xc0, 0x44, 0x6f, 0x1a, 0x83, 0x4f, 0x61, 0x33, 0x39, 0x74, 0x5d, 0x92, 0x36, 0xf3, 0x12,
	0xa1, 0x45, 0x4b, 0x4d, 0x6c, 0x47, 0x89, 0xc9, 0xfc, 0x53, 0x81, 0xe2, 0x34, 0x0e, 0x55, 0x58,
	0x3f, 0x6d, 0xbf, 0x6e, 0x9f, 0x9c, 0xb5, 0xf5, 0x4f, 0x70, 0x1d, 0xf2, 0xcd, 0x56, 0x4b, 0x57,
	0x70, 0x13, 0x36, 0x4e, 0x7c, 0xfa, 0x89, 0x91, 0x43, 0x7a, 0x2e, 0x7e, 0xea, 0x48, 0x3b, 0x7c,
	0x1b, 0xd2, 0x40, 0xcf, 0xa3, 0x06, 0x70, 0x68, 0xcb, 0x43, 0xdb, 0x97, 0x44, 0xbe, 0xbe, 0x86,
	0x25, 0x28, 0xa6, 0x5e, 0x9b, 0xf9, 0x7a, 0x01, 0x1f, 0x02, 0x9e, 0x51, 0xcf, 0xa2, 0x31, 0xf9,
	0x23, 0x6a, 0x38, 0x92, 0x8d, 0x99, 0x8c, 0xf4, 0x7b, 0x31, 0xec, 0x8c, 0x6c, 0x4f, 0xf6, 0x5b,
	0x81, 0xab, 0xaf, 0x9b, 0x1a, 0x6c, 0xce, 0x2e, 0x22, 0xb8, 0xd9, 0x02, 0xcd, 0xa2, 0x61, 0x30,
	0xa6, 0x0f, 0x2c, 0x9f, 0xf9, 0x00, 0xee, 0x2f, 0x10, 0x09, 0x6e, 0xbe, 0x82, 0xd2, 0x61, 0x9f,
	0x9c, 0xc1, 0x87, 0x52, 0x3f, 0x03, 0x6d, 0x9e, 0x47, 0x70, 0x44, 0x58, 0xeb, 0xb3, 0x09, 0xcb,
	0x86, 0x95, 0x9c, 0xcd, 0x1e, 0x3c, 0x98, 0xa1, 0x0e, 0xa2, 0xe3, 0x64, 0x70, 0x32, 0xfd, 0xa7,
	0xdc, 0xa9, 0xff, 0x26, 0x9f, 0xc2, 0x78, 0xc2, 0x4a, 0xc9, 0xa7, 0xd0, 0xfc, 0x57, 0x01, 0xcc,
	0x26, 0x59, 0x2e, 0x07, 0xdf, 0x81, 0xee, 0xc4, 0xc8, 0x6e, 0x48, 0x62, 0xe4, 0xc9, 0xee, 0xd0,
	0xe6, 0x93, 0x59, 0xfd, 0x36, 0x9b, 0xfe, 0x2a, 0xe3, 0xc4, 0x94, 0x44, 0xfe, 0x68, 0xf3, 0xa6,
	0x2f, 0xc3, 0xc8, 0xd2, 0x9c, 0x05, 0xa3, 0xd1, 0x83, 0x4f, 0x97, 0xc0, 0x62, 0xd5, 0x03, 0x8a,
	0x2e, 0xbf, 0x15, 0x03, 0x8a, 0x70, 0x0f, 0x0a, 0x63, 0xdb, 0x1b, 0xa5, 0x9d, 0xad, 0xd6, 0x9f,
	0x2c, 0x2b, 0xfa, 0x1c, 0x93, 0x95, 0xa2, 0xbf, 0xcf, 0xbd, 0x54, 0xcc, 0x0e, 0xdc, 0xcf, 0x78,
	0x71, 0x1b, 0x20, 0x6d, 0xf5, 0x69, 0x45, 0x4b, 0x56, 0x51, 0x4c, 0x1b, 0x3b, 0x3b, 0x09, 0xb9,
	0x2b, 0x93, 0xb0, 0xb3, 0x0f, 0x30, 0xab, 0xf8, 0xe2, 0x24, 0x6c, 0xc0, 0xda, 0x69, 0xa7, 0x69,
	0xe9, 0x4a, 0x6c, 0x3e, 0xfc, 0xa1, 0xd1, 0x6e, 0x37, 0xdf, 0xe8, 0x39, 0x2c, 0x42, 0xa1, 0x75,
	0x7a, 0xfc, 0xe6, 0x48, 0xcf, 0xd7, 0xff, 0x29, 0x40, 0xe9, 0x34, 0xd5, 0xdf, 0x49, 0xe4, 0x63,
	0x13, 0x36, 0x2e, 0x5b, 0x1a, 0x1f, 0x5d, 0x33, 0xb5, 0xc6, 0xd6, 0x6a, 0xa7, 0xe0, 0xd8, 0x06,
	0x75, 0xae, 0x81, 0xf1, 0xf1, 0x95, 0x2a, 0x2d, 0x8c, 0x89, 0xf1, 0xe4, 0x5a, 0xbf, 0xe0, 0xf8,
	0x1a, 0x60, 0xf6, 0x62, 0x71, 0x7b, 0xf5, 0x4b, 0x8f, 0xd9, 0x1e, 0x5f, 0xe7, 0x16, 0x1c, 0xcf,
	0xe6, 0x47, 0x20, 0xee, 0x12, 0x7c, 0x7a, 0x53, 0x17, 0x9d, 0x1b, 0xe6, 0xcd, 0x8d, 0x86, 0x01,
	0x3c, 0x5c, 0xbe, 0x3d, 0xf1, 0x8b, 0x6c, 0xf4, 0xca, 0x3d, 0x6e, 0xec, 0xdc, 0x16, 0x2a, 0x38,
	0xfe, 0xa5, 0xc0, 0xb3, 0xdb, 0xec, 0x45, 0xfc, 0x6e, 0x29, 0xe9, 0xcd, 0x7b, 0xdc, 0x78, 0xf9,
	0xff, 0x02, 0x05, 0xc7, 0x8b, 0xc9, 0xcf, 0xca, 0xb2, 0x0d, 0x89, 0x5f, 0x2e, 0xa5, 0x5d, 0xb1,
	0xb1, 0x8d, 0x17, 0x77, 0x40, 0x0b, 0x7e, 0x50, 0xff, 0xe5, 0xab, 0xf7, 0x81, 0x67, 0xfb, 0xef,
	0x6b, 0x7b, 0x75, 0x29, 0x6b, 0x4e, 0x30, 0xdc, 0x4d, 0xfe, 0xd6, 0x9c, 0xc0, 0xdb, 0x15, 0x14,
	0x8e, 0x99, 0x43, 0x22, 0xf3, 0x3b, 0xd7, 0xbb, 0x97, 0x20, 0xbe, 0xf9, 0x2f, 0x00, 0x00, 0xff,
	0xff, 0x1e, 0x01, 0x4c, 0x09, 0x05, 0x0a, 0x00, 0x00,
}
