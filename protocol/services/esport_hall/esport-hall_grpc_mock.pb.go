// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/esport-hall/esport-hall.proto

package esport_hall

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockEsportHallClient is a mock of EsportHallClient interface.
type MockEsportHallClient struct {
	ctrl     *gomock.Controller
	recorder *MockEsportHallClientMockRecorder
}

// MockEsportHallClientMockRecorder is the mock recorder for MockEsportHallClient.
type MockEsportHallClientMockRecorder struct {
	mock *MockEsportHallClient
}

// NewMockEsportHallClient creates a new mock instance.
func NewMockEsportHallClient(ctrl *gomock.Controller) *MockEsportHallClient {
	mock := &MockEsportHallClient{ctrl: ctrl}
	mock.recorder = &MockEsportHallClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEsportHallClient) EXPECT() *MockEsportHallClientMockRecorder {
	return m.recorder
}

// AddCoachRecommend mocks base method.
func (m *MockEsportHallClient) AddCoachRecommend(ctx context.Context, in *AddCoachRecommendRequest, opts ...grpc.CallOption) (*AddCoachRecommendResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddCoachRecommend", varargs...)
	ret0, _ := ret[0].(*AddCoachRecommendResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddCoachRecommend indicates an expected call of AddCoachRecommend.
func (mr *MockEsportHallClientMockRecorder) AddCoachRecommend(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddCoachRecommend", reflect.TypeOf((*MockEsportHallClient)(nil).AddCoachRecommend), varargs...)
}

// AddIgnoreRecommendCoach mocks base method.
func (m *MockEsportHallClient) AddIgnoreRecommendCoach(ctx context.Context, in *AddIgnoreRecommendCoachRequest, opts ...grpc.CallOption) (*AddIgnoreRecommendCoachResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddIgnoreRecommendCoach", varargs...)
	ret0, _ := ret[0].(*AddIgnoreRecommendCoachResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddIgnoreRecommendCoach indicates an expected call of AddIgnoreRecommendCoach.
func (mr *MockEsportHallClientMockRecorder) AddIgnoreRecommendCoach(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddIgnoreRecommendCoach", reflect.TypeOf((*MockEsportHallClient)(nil).AddIgnoreRecommendCoach), varargs...)
}

// BatchGetQuickReceiveSwitch mocks base method.
func (m *MockEsportHallClient) BatchGetQuickReceiveSwitch(ctx context.Context, in *BatchGetQuickReceiveSwitchRequest, opts ...grpc.CallOption) (*BatchGetQuickReceiveSwitchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetQuickReceiveSwitch", varargs...)
	ret0, _ := ret[0].(*BatchGetQuickReceiveSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetQuickReceiveSwitch indicates an expected call of BatchGetQuickReceiveSwitch.
func (mr *MockEsportHallClientMockRecorder) BatchGetQuickReceiveSwitch(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetQuickReceiveSwitch", reflect.TypeOf((*MockEsportHallClient)(nil).BatchGetQuickReceiveSwitch), varargs...)
}

// BatchGetSkillProductInfo mocks base method.
func (m *MockEsportHallClient) BatchGetSkillProductInfo(ctx context.Context, in *BatchGetSkillProductInfoRequest, opts ...grpc.CallOption) (*BatchGetSkillProductInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetSkillProductInfo", varargs...)
	ret0, _ := ret[0].(*BatchGetSkillProductInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetSkillProductInfo indicates an expected call of BatchGetSkillProductInfo.
func (mr *MockEsportHallClientMockRecorder) BatchGetSkillProductInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetSkillProductInfo", reflect.TypeOf((*MockEsportHallClient)(nil).BatchGetSkillProductInfo), varargs...)
}

// CheckFirstRoundOrderRight mocks base method.
func (m *MockEsportHallClient) CheckFirstRoundOrderRight(ctx context.Context, in *CheckFirstRoundOrderRightRequest, opts ...grpc.CallOption) (*CheckFirstRoundOrderRightResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckFirstRoundOrderRight", varargs...)
	ret0, _ := ret[0].(*CheckFirstRoundOrderRightResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckFirstRoundOrderRight indicates an expected call of CheckFirstRoundOrderRight.
func (mr *MockEsportHallClientMockRecorder) CheckFirstRoundOrderRight(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckFirstRoundOrderRight", reflect.TypeOf((*MockEsportHallClient)(nil).CheckFirstRoundOrderRight), varargs...)
}

// ClearFirstRoundOrder mocks base method.
func (m *MockEsportHallClient) ClearFirstRoundOrder(ctx context.Context, in *ClearFirstRoundOrderRequest, opts ...grpc.CallOption) (*ClearFirstRoundOrderResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ClearFirstRoundOrder", varargs...)
	ret0, _ := ret[0].(*ClearFirstRoundOrderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ClearFirstRoundOrder indicates an expected call of ClearFirstRoundOrder.
func (mr *MockEsportHallClientMockRecorder) ClearFirstRoundOrder(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearFirstRoundOrder", reflect.TypeOf((*MockEsportHallClient)(nil).ClearFirstRoundOrder), varargs...)
}

// ClearNewCustomerOrder mocks base method.
func (m *MockEsportHallClient) ClearNewCustomerOrder(ctx context.Context, in *ClearNewCustomerOrderRequest, opts ...grpc.CallOption) (*ClearNewCustomerOrderResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ClearNewCustomerOrder", varargs...)
	ret0, _ := ret[0].(*ClearNewCustomerOrderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ClearNewCustomerOrder indicates an expected call of ClearNewCustomerOrder.
func (mr *MockEsportHallClientMockRecorder) ClearNewCustomerOrder(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearNewCustomerOrder", reflect.TypeOf((*MockEsportHallClient)(nil).ClearNewCustomerOrder), varargs...)
}

// CreateEsportGameCard mocks base method.
func (m *MockEsportHallClient) CreateEsportGameCard(ctx context.Context, in *CreateEsportGameCardRequest, opts ...grpc.CallOption) (*CreateEsportGameCardResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateEsportGameCard", varargs...)
	ret0, _ := ret[0].(*CreateEsportGameCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateEsportGameCard indicates an expected call of CreateEsportGameCard.
func (mr *MockEsportHallClientMockRecorder) CreateEsportGameCard(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateEsportGameCard", reflect.TypeOf((*MockEsportHallClient)(nil).CreateEsportGameCard), varargs...)
}

// DelCoachRecommend mocks base method.
func (m *MockEsportHallClient) DelCoachRecommend(ctx context.Context, in *DelCoachRecommendRequest, opts ...grpc.CallOption) (*DelCoachRecommendResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelCoachRecommend", varargs...)
	ret0, _ := ret[0].(*DelCoachRecommendResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelCoachRecommend indicates an expected call of DelCoachRecommend.
func (mr *MockEsportHallClientMockRecorder) DelCoachRecommend(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelCoachRecommend", reflect.TypeOf((*MockEsportHallClient)(nil).DelCoachRecommend), varargs...)
}

// DelSkillProduct mocks base method.
func (m *MockEsportHallClient) DelSkillProduct(ctx context.Context, in *DelSkillProductRequest, opts ...grpc.CallOption) (*DelSkillProductResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelSkillProduct", varargs...)
	ret0, _ := ret[0].(*DelSkillProductResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelSkillProduct indicates an expected call of DelSkillProduct.
func (mr *MockEsportHallClientMockRecorder) DelSkillProduct(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelSkillProduct", reflect.TypeOf((*MockEsportHallClient)(nil).DelSkillProduct), varargs...)
}

// DeleteEsportGameCard mocks base method.
func (m *MockEsportHallClient) DeleteEsportGameCard(ctx context.Context, in *DeleteEsportGameCardRequest, opts ...grpc.CallOption) (*DeleteEsportGameCardResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteEsportGameCard", varargs...)
	ret0, _ := ret[0].(*DeleteEsportGameCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteEsportGameCard indicates an expected call of DeleteEsportGameCard.
func (mr *MockEsportHallClientMockRecorder) DeleteEsportGameCard(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteEsportGameCard", reflect.TypeOf((*MockEsportHallClient)(nil).DeleteEsportGameCard), varargs...)
}

// GetAllSkillList mocks base method.
func (m *MockEsportHallClient) GetAllSkillList(ctx context.Context, in *GetAllSkillListRequest, opts ...grpc.CallOption) (*GetAllSkillListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllSkillList", varargs...)
	ret0, _ := ret[0].(*GetAllSkillListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllSkillList indicates an expected call of GetAllSkillList.
func (mr *MockEsportHallClientMockRecorder) GetAllSkillList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllSkillList", reflect.TypeOf((*MockEsportHallClient)(nil).GetAllSkillList), varargs...)
}

// GetCoachIncentiveAddition mocks base method.
func (m *MockEsportHallClient) GetCoachIncentiveAddition(ctx context.Context, in *GetCoachIncentiveAdditionRequest, opts ...grpc.CallOption) (*GetCoachIncentiveAdditionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCoachIncentiveAddition", varargs...)
	ret0, _ := ret[0].(*GetCoachIncentiveAdditionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCoachIncentiveAddition indicates an expected call of GetCoachIncentiveAddition.
func (mr *MockEsportHallClientMockRecorder) GetCoachIncentiveAddition(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCoachIncentiveAddition", reflect.TypeOf((*MockEsportHallClient)(nil).GetCoachIncentiveAddition), varargs...)
}

// GetCoachIncentiveTaskInfo mocks base method.
func (m *MockEsportHallClient) GetCoachIncentiveTaskInfo(ctx context.Context, in *GetCoachIncentiveTaskInfoRequest, opts ...grpc.CallOption) (*GetCoachIncentiveTaskInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCoachIncentiveTaskInfo", varargs...)
	ret0, _ := ret[0].(*GetCoachIncentiveTaskInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCoachIncentiveTaskInfo indicates an expected call of GetCoachIncentiveTaskInfo.
func (mr *MockEsportHallClientMockRecorder) GetCoachIncentiveTaskInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCoachIncentiveTaskInfo", reflect.TypeOf((*MockEsportHallClient)(nil).GetCoachIncentiveTaskInfo), varargs...)
}

// GetCoachMinPrice mocks base method.
func (m *MockEsportHallClient) GetCoachMinPrice(ctx context.Context, in *GetCoachMinPriceRequest, opts ...grpc.CallOption) (*GetCoachMinPriceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCoachMinPrice", varargs...)
	ret0, _ := ret[0].(*GetCoachMinPriceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCoachMinPrice indicates an expected call of GetCoachMinPrice.
func (mr *MockEsportHallClientMockRecorder) GetCoachMinPrice(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCoachMinPrice", reflect.TypeOf((*MockEsportHallClient)(nil).GetCoachMinPrice), varargs...)
}

// GetCoachRecommend mocks base method.
func (m *MockEsportHallClient) GetCoachRecommend(ctx context.Context, in *GetCoachRecommendRequest, opts ...grpc.CallOption) (*GetCoachRecommendResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCoachRecommend", varargs...)
	ret0, _ := ret[0].(*GetCoachRecommendResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCoachRecommend indicates an expected call of GetCoachRecommend.
func (mr *MockEsportHallClientMockRecorder) GetCoachRecommend(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCoachRecommend", reflect.TypeOf((*MockEsportHallClient)(nil).GetCoachRecommend), varargs...)
}

// GetEsportGameCardInfo mocks base method.
func (m *MockEsportHallClient) GetEsportGameCardInfo(ctx context.Context, in *GetEsportGameCardInfoRequest, opts ...grpc.CallOption) (*GetEsportGameCardInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEsportGameCardInfo", varargs...)
	ret0, _ := ret[0].(*GetEsportGameCardInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEsportGameCardInfo indicates an expected call of GetEsportGameCardInfo.
func (mr *MockEsportHallClientMockRecorder) GetEsportGameCardInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEsportGameCardInfo", reflect.TypeOf((*MockEsportHallClient)(nil).GetEsportGameCardInfo), varargs...)
}

// GetEsportGameCardList mocks base method.
func (m *MockEsportHallClient) GetEsportGameCardList(ctx context.Context, in *GetEsportGameCardListRequest, opts ...grpc.CallOption) (*GetEsportGameCardListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEsportGameCardList", varargs...)
	ret0, _ := ret[0].(*GetEsportGameCardListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEsportGameCardList indicates an expected call of GetEsportGameCardList.
func (mr *MockEsportHallClientMockRecorder) GetEsportGameCardList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEsportGameCardList", reflect.TypeOf((*MockEsportHallClient)(nil).GetEsportGameCardList), varargs...)
}

// GetFirstRoundDiscountGameList mocks base method.
func (m *MockEsportHallClient) GetFirstRoundDiscountGameList(ctx context.Context, in *GetFirstRoundDiscountGameListRequest, opts ...grpc.CallOption) (*GetFirstRoundDiscountGameListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFirstRoundDiscountGameList", varargs...)
	ret0, _ := ret[0].(*GetFirstRoundDiscountGameListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFirstRoundDiscountGameList indicates an expected call of GetFirstRoundDiscountGameList.
func (mr *MockEsportHallClientMockRecorder) GetFirstRoundDiscountGameList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFirstRoundDiscountGameList", reflect.TypeOf((*MockEsportHallClient)(nil).GetFirstRoundDiscountGameList), varargs...)
}

// GetFirstRoundDiscountInfo mocks base method.
func (m *MockEsportHallClient) GetFirstRoundDiscountInfo(ctx context.Context, in *GetFirstRoundDiscountInfoRequest, opts ...grpc.CallOption) (*GetFirstRoundDiscountInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFirstRoundDiscountInfo", varargs...)
	ret0, _ := ret[0].(*GetFirstRoundDiscountInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFirstRoundDiscountInfo indicates an expected call of GetFirstRoundDiscountInfo.
func (mr *MockEsportHallClientMockRecorder) GetFirstRoundDiscountInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFirstRoundDiscountInfo", reflect.TypeOf((*MockEsportHallClient)(nil).GetFirstRoundDiscountInfo), varargs...)
}

// GetFirstRoundLabelBySkill mocks base method.
func (m *MockEsportHallClient) GetFirstRoundLabelBySkill(ctx context.Context, in *GetFirstRoundLabelBySkillRequest, opts ...grpc.CallOption) (*GetFirstRoundLabelBySkillResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFirstRoundLabelBySkill", varargs...)
	ret0, _ := ret[0].(*GetFirstRoundLabelBySkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFirstRoundLabelBySkill indicates an expected call of GetFirstRoundLabelBySkill.
func (mr *MockEsportHallClientMockRecorder) GetFirstRoundLabelBySkill(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFirstRoundLabelBySkill", reflect.TypeOf((*MockEsportHallClient)(nil).GetFirstRoundLabelBySkill), varargs...)
}

// GetFirstRoundLabelByUid mocks base method.
func (m *MockEsportHallClient) GetFirstRoundLabelByUid(ctx context.Context, in *GetFirstRoundLabelByUidRequest, opts ...grpc.CallOption) (*GetFirstRoundLabelByUidResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFirstRoundLabelByUid", varargs...)
	ret0, _ := ret[0].(*GetFirstRoundLabelByUidResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFirstRoundLabelByUid indicates an expected call of GetFirstRoundLabelByUid.
func (mr *MockEsportHallClientMockRecorder) GetFirstRoundLabelByUid(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFirstRoundLabelByUid", reflect.TypeOf((*MockEsportHallClient)(nil).GetFirstRoundLabelByUid), varargs...)
}

// GetFirstRoundOrderRight mocks base method.
func (m *MockEsportHallClient) GetFirstRoundOrderRight(ctx context.Context, in *GetFirstRoundOrderRightRequest, opts ...grpc.CallOption) (*GetFirstRoundOrderRightResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFirstRoundOrderRight", varargs...)
	ret0, _ := ret[0].(*GetFirstRoundOrderRightResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFirstRoundOrderRight indicates an expected call of GetFirstRoundOrderRight.
func (mr *MockEsportHallClientMockRecorder) GetFirstRoundOrderRight(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFirstRoundOrderRight", reflect.TypeOf((*MockEsportHallClient)(nil).GetFirstRoundOrderRight), varargs...)
}

// GetGameCoachList mocks base method.
func (m *MockEsportHallClient) GetGameCoachList(ctx context.Context, in *GetGameCoachListRequest, opts ...grpc.CallOption) (*GetCoachListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGameCoachList", varargs...)
	ret0, _ := ret[0].(*GetCoachListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameCoachList indicates an expected call of GetGameCoachList.
func (mr *MockEsportHallClientMockRecorder) GetGameCoachList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameCoachList", reflect.TypeOf((*MockEsportHallClient)(nil).GetGameCoachList), varargs...)
}

// GetGameCoachListByUid mocks base method.
func (m *MockEsportHallClient) GetGameCoachListByUid(ctx context.Context, in *GetGameCoachListByUidRequest, opts ...grpc.CallOption) (*GetGameCoachListByUidResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGameCoachListByUid", varargs...)
	ret0, _ := ret[0].(*GetGameCoachListByUidResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameCoachListByUid indicates an expected call of GetGameCoachListByUid.
func (mr *MockEsportHallClientMockRecorder) GetGameCoachListByUid(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameCoachListByUid", reflect.TypeOf((*MockEsportHallClient)(nil).GetGameCoachListByUid), varargs...)
}

// GetGamePriceProperty mocks base method.
func (m *MockEsportHallClient) GetGamePriceProperty(ctx context.Context, in *GetGamePricePropertyRequest, opts ...grpc.CallOption) (*GetGamePricePropertyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGamePriceProperty", varargs...)
	ret0, _ := ret[0].(*GetGamePricePropertyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGamePriceProperty indicates an expected call of GetGamePriceProperty.
func (mr *MockEsportHallClientMockRecorder) GetGamePriceProperty(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGamePriceProperty", reflect.TypeOf((*MockEsportHallClient)(nil).GetGamePriceProperty), varargs...)
}

// GetGlobalRcmdCoach mocks base method.
func (m *MockEsportHallClient) GetGlobalRcmdCoach(ctx context.Context, in *GetGlobalRcmdCoachRequest, opts ...grpc.CallOption) (*GetGlobalRcmdCoachResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGlobalRcmdCoach", varargs...)
	ret0, _ := ret[0].(*GetGlobalRcmdCoachResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGlobalRcmdCoach indicates an expected call of GetGlobalRcmdCoach.
func (mr *MockEsportHallClientMockRecorder) GetGlobalRcmdCoach(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGlobalRcmdCoach", reflect.TypeOf((*MockEsportHallClient)(nil).GetGlobalRcmdCoach), varargs...)
}

// GetNewCustomerDiscountInfo mocks base method.
func (m *MockEsportHallClient) GetNewCustomerDiscountInfo(ctx context.Context, in *GetNewCustomerDiscountInfoRequest, opts ...grpc.CallOption) (*GetNewCustomerDiscountInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNewCustomerDiscountInfo", varargs...)
	ret0, _ := ret[0].(*GetNewCustomerDiscountInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewCustomerDiscountInfo indicates an expected call of GetNewCustomerDiscountInfo.
func (mr *MockEsportHallClientMockRecorder) GetNewCustomerDiscountInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewCustomerDiscountInfo", reflect.TypeOf((*MockEsportHallClient)(nil).GetNewCustomerDiscountInfo), varargs...)
}

// GetNewCustomerPriceBySkill mocks base method.
func (m *MockEsportHallClient) GetNewCustomerPriceBySkill(ctx context.Context, in *GetNewCustomerPriceBySkillRequest, opts ...grpc.CallOption) (*GetNewCustomerPriceBySkillResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNewCustomerPriceBySkill", varargs...)
	ret0, _ := ret[0].(*GetNewCustomerPriceBySkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewCustomerPriceBySkill indicates an expected call of GetNewCustomerPriceBySkill.
func (mr *MockEsportHallClientMockRecorder) GetNewCustomerPriceBySkill(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewCustomerPriceBySkill", reflect.TypeOf((*MockEsportHallClient)(nil).GetNewCustomerPriceBySkill), varargs...)
}

// GetNewCustomerPriceByUid mocks base method.
func (m *MockEsportHallClient) GetNewCustomerPriceByUid(ctx context.Context, in *GetNewCustomerPriceByUidRequest, opts ...grpc.CallOption) (*GetNewCustomerPriceByUidResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNewCustomerPriceByUid", varargs...)
	ret0, _ := ret[0].(*GetNewCustomerPriceByUidResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewCustomerPriceByUid indicates an expected call of GetNewCustomerPriceByUid.
func (mr *MockEsportHallClientMockRecorder) GetNewCustomerPriceByUid(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewCustomerPriceByUid", reflect.TypeOf((*MockEsportHallClient)(nil).GetNewCustomerPriceByUid), varargs...)
}

// GetOperationCoachRecommendWithoutExposed mocks base method.
func (m *MockEsportHallClient) GetOperationCoachRecommendWithoutExposed(ctx context.Context, in *GetOperationCoachRecommendWithoutExposedRequest, opts ...grpc.CallOption) (*GetOperationCoachRecommendWithoutExposedResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetOperationCoachRecommendWithoutExposed", varargs...)
	ret0, _ := ret[0].(*GetOperationCoachRecommendWithoutExposedResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOperationCoachRecommendWithoutExposed indicates an expected call of GetOperationCoachRecommendWithoutExposed.
func (mr *MockEsportHallClientMockRecorder) GetOperationCoachRecommendWithoutExposed(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOperationCoachRecommendWithoutExposed", reflect.TypeOf((*MockEsportHallClient)(nil).GetOperationCoachRecommendWithoutExposed), varargs...)
}

// GetQuickReceiveSwitch mocks base method.
func (m *MockEsportHallClient) GetQuickReceiveSwitch(ctx context.Context, in *GetQuickReceiveSwitchRequest, opts ...grpc.CallOption) (*GetQuickReceiveSwitchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetQuickReceiveSwitch", varargs...)
	ret0, _ := ret[0].(*GetQuickReceiveSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetQuickReceiveSwitch indicates an expected call of GetQuickReceiveSwitch.
func (mr *MockEsportHallClientMockRecorder) GetQuickReceiveSwitch(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetQuickReceiveSwitch", reflect.TypeOf((*MockEsportHallClient)(nil).GetQuickReceiveSwitch), varargs...)
}

// GetReCoachForUGC mocks base method.
func (m *MockEsportHallClient) GetReCoachForUGC(ctx context.Context, in *GetReCoachForUGCRequest, opts ...grpc.CallOption) (*GetReCoachForUGCResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetReCoachForUGC", varargs...)
	ret0, _ := ret[0].(*GetReCoachForUGCResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReCoachForUGC indicates an expected call of GetReCoachForUGC.
func (mr *MockEsportHallClientMockRecorder) GetReCoachForUGC(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReCoachForUGC", reflect.TypeOf((*MockEsportHallClient)(nil).GetReCoachForUGC), varargs...)
}

// GetReceiveTimeFrame mocks base method.
func (m *MockEsportHallClient) GetReceiveTimeFrame(ctx context.Context, in *GetReceiveTimeFrameRequest, opts ...grpc.CallOption) (*GetReceiveTimeFrameResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetReceiveTimeFrame", varargs...)
	ret0, _ := ret[0].(*GetReceiveTimeFrameResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReceiveTimeFrame indicates an expected call of GetReceiveTimeFrame.
func (mr *MockEsportHallClientMockRecorder) GetReceiveTimeFrame(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReceiveTimeFrame", reflect.TypeOf((*MockEsportHallClient)(nil).GetReceiveTimeFrame), varargs...)
}

// GetRecommendSkillProduct mocks base method.
func (m *MockEsportHallClient) GetRecommendSkillProduct(ctx context.Context, in *GetRecommendSkillProductReq, opts ...grpc.CallOption) (*GetRecommendSkillProductResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRecommendSkillProduct", varargs...)
	ret0, _ := ret[0].(*GetRecommendSkillProductResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecommendSkillProduct indicates an expected call of GetRecommendSkillProduct.
func (mr *MockEsportHallClientMockRecorder) GetRecommendSkillProduct(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecommendSkillProduct", reflect.TypeOf((*MockEsportHallClient)(nil).GetRecommendSkillProduct), varargs...)
}

// GetSkillProductByUidGameId mocks base method.
func (m *MockEsportHallClient) GetSkillProductByUidGameId(ctx context.Context, in *GetSkillProductByUidGameIdRequest, opts ...grpc.CallOption) (*GetSkillProductByUidGameIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSkillProductByUidGameId", varargs...)
	ret0, _ := ret[0].(*GetSkillProductByUidGameIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSkillProductByUidGameId indicates an expected call of GetSkillProductByUidGameId.
func (mr *MockEsportHallClientMockRecorder) GetSkillProductByUidGameId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSkillProductByUidGameId", reflect.TypeOf((*MockEsportHallClient)(nil).GetSkillProductByUidGameId), varargs...)
}

// GetSkillProductInfo mocks base method.
func (m *MockEsportHallClient) GetSkillProductInfo(ctx context.Context, in *GetSkillProductInfoRequest, opts ...grpc.CallOption) (*GetSkillProductInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSkillProductInfo", varargs...)
	ret0, _ := ret[0].(*GetSkillProductInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSkillProductInfo indicates an expected call of GetSkillProductInfo.
func (mr *MockEsportHallClientMockRecorder) GetSkillProductInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSkillProductInfo", reflect.TypeOf((*MockEsportHallClient)(nil).GetSkillProductInfo), varargs...)
}

// GetSkillProductInfoByGameId mocks base method.
func (m *MockEsportHallClient) GetSkillProductInfoByGameId(ctx context.Context, in *GetSkillProductInfoByGameIdRequest, opts ...grpc.CallOption) (*GetSkillProductInfoByGameIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSkillProductInfoByGameId", varargs...)
	ret0, _ := ret[0].(*GetSkillProductInfoByGameIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSkillProductInfoByGameId indicates an expected call of GetSkillProductInfoByGameId.
func (mr *MockEsportHallClientMockRecorder) GetSkillProductInfoByGameId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSkillProductInfoByGameId", reflect.TypeOf((*MockEsportHallClient)(nil).GetSkillProductInfoByGameId), varargs...)
}

// GetUserTopGamePreSelectConfig mocks base method.
func (m *MockEsportHallClient) GetUserTopGamePreSelectConfig(ctx context.Context, in *GetUserTopGamePreSelectConfigRequest, opts ...grpc.CallOption) (*GetUserTopGamePreSelectConfigResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserTopGamePreSelectConfig", varargs...)
	ret0, _ := ret[0].(*GetUserTopGamePreSelectConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserTopGamePreSelectConfig indicates an expected call of GetUserTopGamePreSelectConfig.
func (mr *MockEsportHallClientMockRecorder) GetUserTopGamePreSelectConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserTopGamePreSelectConfig", reflect.TypeOf((*MockEsportHallClient)(nil).GetUserTopGamePreSelectConfig), varargs...)
}

// GetVisibleSkillProductList mocks base method.
func (m *MockEsportHallClient) GetVisibleSkillProductList(ctx context.Context, in *GetVisibleSkillProductListRequest, opts ...grpc.CallOption) (*GetVisibleSkillProductListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetVisibleSkillProductList", varargs...)
	ret0, _ := ret[0].(*GetVisibleSkillProductListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVisibleSkillProductList indicates an expected call of GetVisibleSkillProductList.
func (mr *MockEsportHallClientMockRecorder) GetVisibleSkillProductList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVisibleSkillProductList", reflect.TypeOf((*MockEsportHallClient)(nil).GetVisibleSkillProductList), varargs...)
}

// HandleInviteOrder mocks base method.
func (m *MockEsportHallClient) HandleInviteOrder(ctx context.Context, in *HandleInviteOrderRequest, opts ...grpc.CallOption) (*HandleInviteOrderResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HandleInviteOrder", varargs...)
	ret0, _ := ret[0].(*HandleInviteOrderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandleInviteOrder indicates an expected call of HandleInviteOrder.
func (mr *MockEsportHallClientMockRecorder) HandleInviteOrder(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleInviteOrder", reflect.TypeOf((*MockEsportHallClient)(nil).HandleInviteOrder), varargs...)
}

// HasFamousPlayer mocks base method.
func (m *MockEsportHallClient) HasFamousPlayer(ctx context.Context, in *HasFamousPlayerRequest, opts ...grpc.CallOption) (*HasFamousPlayerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HasFamousPlayer", varargs...)
	ret0, _ := ret[0].(*HasFamousPlayerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HasFamousPlayer indicates an expected call of HasFamousPlayer.
func (mr *MockEsportHallClientMockRecorder) HasFamousPlayer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HasFamousPlayer", reflect.TypeOf((*MockEsportHallClient)(nil).HasFamousPlayer), varargs...)
}

// InitUserSkillInfo mocks base method.
func (m *MockEsportHallClient) InitUserSkillInfo(ctx context.Context, in *InitUserSkillInfoRequest, opts ...grpc.CallOption) (*InitUserSkillInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InitUserSkillInfo", varargs...)
	ret0, _ := ret[0].(*InitUserSkillInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitUserSkillInfo indicates an expected call of InitUserSkillInfo.
func (mr *MockEsportHallClientMockRecorder) InitUserSkillInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitUserSkillInfo", reflect.TypeOf((*MockEsportHallClient)(nil).InitUserSkillInfo), varargs...)
}

// InviteOrder mocks base method.
func (m *MockEsportHallClient) InviteOrder(ctx context.Context, in *InviteOrderRequest, opts ...grpc.CallOption) (*InviteOrderResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InviteOrder", varargs...)
	ret0, _ := ret[0].(*InviteOrderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InviteOrder indicates an expected call of InviteOrder.
func (mr *MockEsportHallClientMockRecorder) InviteOrder(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InviteOrder", reflect.TypeOf((*MockEsportHallClient)(nil).InviteOrder), varargs...)
}

// ReBuildUserFirstRoundCache mocks base method.
func (m *MockEsportHallClient) ReBuildUserFirstRoundCache(ctx context.Context, in *ReBuildUserFirstRoundCacheRequest, opts ...grpc.CallOption) (*ReBuildUserFirstRoundCacheResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReBuildUserFirstRoundCache", varargs...)
	ret0, _ := ret[0].(*ReBuildUserFirstRoundCacheResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReBuildUserFirstRoundCache indicates an expected call of ReBuildUserFirstRoundCache.
func (mr *MockEsportHallClientMockRecorder) ReBuildUserFirstRoundCache(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReBuildUserFirstRoundCache", reflect.TypeOf((*MockEsportHallClient)(nil).ReBuildUserFirstRoundCache), varargs...)
}

// ReportExposeCoach mocks base method.
func (m *MockEsportHallClient) ReportExposeCoach(ctx context.Context, in *ReportExposeCoachRequest, opts ...grpc.CallOption) (*ReportExposeCoachResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReportExposeCoach", varargs...)
	ret0, _ := ret[0].(*ReportExposeCoachResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReportExposeCoach indicates an expected call of ReportExposeCoach.
func (mr *MockEsportHallClientMockRecorder) ReportExposeCoach(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportExposeCoach", reflect.TypeOf((*MockEsportHallClient)(nil).ReportExposeCoach), varargs...)
}

// SaveUserTopGamePreSelectConfig mocks base method.
func (m *MockEsportHallClient) SaveUserTopGamePreSelectConfig(ctx context.Context, in *SaveUserTopGamePreSelectConfigRequest, opts ...grpc.CallOption) (*SaveUserTopGamePreSelectConfigResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SaveUserTopGamePreSelectConfig", varargs...)
	ret0, _ := ret[0].(*SaveUserTopGamePreSelectConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveUserTopGamePreSelectConfig indicates an expected call of SaveUserTopGamePreSelectConfig.
func (mr *MockEsportHallClientMockRecorder) SaveUserTopGamePreSelectConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveUserTopGamePreSelectConfig", reflect.TypeOf((*MockEsportHallClient)(nil).SaveUserTopGamePreSelectConfig), varargs...)
}

// SearchCoach mocks base method.
func (m *MockEsportHallClient) SearchCoach(ctx context.Context, in *SearchCoachRequest, opts ...grpc.CallOption) (*SearchCoachResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SearchCoach", varargs...)
	ret0, _ := ret[0].(*SearchCoachResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchCoach indicates an expected call of SearchCoach.
func (mr *MockEsportHallClientMockRecorder) SearchCoach(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchCoach", reflect.TypeOf((*MockEsportHallClient)(nil).SearchCoach), varargs...)
}

// SendEsportGameCard mocks base method.
func (m *MockEsportHallClient) SendEsportGameCard(ctx context.Context, in *SendEsportGameCardRequest, opts ...grpc.CallOption) (*SendEsportGameCardResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendEsportGameCard", varargs...)
	ret0, _ := ret[0].(*SendEsportGameCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendEsportGameCard indicates an expected call of SendEsportGameCard.
func (mr *MockEsportHallClientMockRecorder) SendEsportGameCard(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendEsportGameCard", reflect.TypeOf((*MockEsportHallClient)(nil).SendEsportGameCard), varargs...)
}

// SetFirstRoundSwitch mocks base method.
func (m *MockEsportHallClient) SetFirstRoundSwitch(ctx context.Context, in *SetFirstRoundSwitchRequest, opts ...grpc.CallOption) (*SetFirstRoundSwitchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetFirstRoundSwitch", varargs...)
	ret0, _ := ret[0].(*SetFirstRoundSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetFirstRoundSwitch indicates an expected call of SetFirstRoundSwitch.
func (mr *MockEsportHallClientMockRecorder) SetFirstRoundSwitch(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetFirstRoundSwitch", reflect.TypeOf((*MockEsportHallClient)(nil).SetFirstRoundSwitch), varargs...)
}

// SetGuaranteeWinSwitch mocks base method.
func (m *MockEsportHallClient) SetGuaranteeWinSwitch(ctx context.Context, in *SetGuaranteeWinSwitchRequest, opts ...grpc.CallOption) (*SetGuaranteeWinSwitchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetGuaranteeWinSwitch", varargs...)
	ret0, _ := ret[0].(*SetGuaranteeWinSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetGuaranteeWinSwitch indicates an expected call of SetGuaranteeWinSwitch.
func (mr *MockEsportHallClientMockRecorder) SetGuaranteeWinSwitch(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetGuaranteeWinSwitch", reflect.TypeOf((*MockEsportHallClient)(nil).SetGuaranteeWinSwitch), varargs...)
}

// SetNewCustomerSwitch mocks base method.
func (m *MockEsportHallClient) SetNewCustomerSwitch(ctx context.Context, in *SetNewCustomerSwitchRequest, opts ...grpc.CallOption) (*SetNewCustomerSwitchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetNewCustomerSwitch", varargs...)
	ret0, _ := ret[0].(*SetNewCustomerSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetNewCustomerSwitch indicates an expected call of SetNewCustomerSwitch.
func (mr *MockEsportHallClientMockRecorder) SetNewCustomerSwitch(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetNewCustomerSwitch", reflect.TypeOf((*MockEsportHallClient)(nil).SetNewCustomerSwitch), varargs...)
}

// SetQuickReceiveSwitch mocks base method.
func (m *MockEsportHallClient) SetQuickReceiveSwitch(ctx context.Context, in *SetQuickReceiveSwitchRequest, opts ...grpc.CallOption) (*SetQuickReceiveSwitchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetQuickReceiveSwitch", varargs...)
	ret0, _ := ret[0].(*SetQuickReceiveSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetQuickReceiveSwitch indicates an expected call of SetQuickReceiveSwitch.
func (mr *MockEsportHallClientMockRecorder) SetQuickReceiveSwitch(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetQuickReceiveSwitch", reflect.TypeOf((*MockEsportHallClient)(nil).SetQuickReceiveSwitch), varargs...)
}

// SetReceiveTimeFrame mocks base method.
func (m *MockEsportHallClient) SetReceiveTimeFrame(ctx context.Context, in *SetReceiveTimeFrameRequest, opts ...grpc.CallOption) (*SetReceiveTimeFrameResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetReceiveTimeFrame", varargs...)
	ret0, _ := ret[0].(*SetReceiveTimeFrameResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetReceiveTimeFrame indicates an expected call of SetReceiveTimeFrame.
func (mr *MockEsportHallClientMockRecorder) SetReceiveTimeFrame(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetReceiveTimeFrame", reflect.TypeOf((*MockEsportHallClient)(nil).SetReceiveTimeFrame), varargs...)
}

// SetSkillPrice mocks base method.
func (m *MockEsportHallClient) SetSkillPrice(ctx context.Context, in *SetSkillPriceRequest, opts ...grpc.CallOption) (*SetSkillPriceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetSkillPrice", varargs...)
	ret0, _ := ret[0].(*SetSkillPriceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetSkillPrice indicates an expected call of SetSkillPrice.
func (mr *MockEsportHallClientMockRecorder) SetSkillPrice(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetSkillPrice", reflect.TypeOf((*MockEsportHallClient)(nil).SetSkillPrice), varargs...)
}

// SetSkillReceiveSwitch mocks base method.
func (m *MockEsportHallClient) SetSkillReceiveSwitch(ctx context.Context, in *SetSkillReceiveSwitchRequest, opts ...grpc.CallOption) (*SetSkillReceiveSwitchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetSkillReceiveSwitch", varargs...)
	ret0, _ := ret[0].(*SetSkillReceiveSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetSkillReceiveSwitch indicates an expected call of SetSkillReceiveSwitch.
func (mr *MockEsportHallClientMockRecorder) SetSkillReceiveSwitch(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetSkillReceiveSwitch", reflect.TypeOf((*MockEsportHallClient)(nil).SetSkillReceiveSwitch), varargs...)
}

// SetUserToRealCoach mocks base method.
func (m *MockEsportHallClient) SetUserToRealCoach(ctx context.Context, in *SetUserToRealCoachRequest, opts ...grpc.CallOption) (*SetUserToRealCoachResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetUserToRealCoach", varargs...)
	ret0, _ := ret[0].(*SetUserToRealCoachResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserToRealCoach indicates an expected call of SetUserToRealCoach.
func (mr *MockEsportHallClientMockRecorder) SetUserToRealCoach(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserToRealCoach", reflect.TypeOf((*MockEsportHallClient)(nil).SetUserToRealCoach), varargs...)
}

// Test mocks base method.
func (m *MockEsportHallClient) Test(ctx context.Context, in *TestRequest, opts ...grpc.CallOption) (*TestResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Test", varargs...)
	ret0, _ := ret[0].(*TestResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Test indicates an expected call of Test.
func (mr *MockEsportHallClientMockRecorder) Test(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Test", reflect.TypeOf((*MockEsportHallClient)(nil).Test), varargs...)
}

// UpdateCoachRecommend mocks base method.
func (m *MockEsportHallClient) UpdateCoachRecommend(ctx context.Context, in *UpdateCoachRecommendRequest, opts ...grpc.CallOption) (*UpdateCoachRecommendResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateCoachRecommend", varargs...)
	ret0, _ := ret[0].(*UpdateCoachRecommendResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateCoachRecommend indicates an expected call of UpdateCoachRecommend.
func (mr *MockEsportHallClientMockRecorder) UpdateCoachRecommend(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCoachRecommend", reflect.TypeOf((*MockEsportHallClient)(nil).UpdateCoachRecommend), varargs...)
}

// UpdateEsportGameCard mocks base method.
func (m *MockEsportHallClient) UpdateEsportGameCard(ctx context.Context, in *UpdateEsportGameCardRequest, opts ...grpc.CallOption) (*UpdateEsportGameCardResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateEsportGameCard", varargs...)
	ret0, _ := ret[0].(*UpdateEsportGameCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateEsportGameCard indicates an expected call of UpdateEsportGameCard.
func (mr *MockEsportHallClientMockRecorder) UpdateEsportGameCard(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateEsportGameCard", reflect.TypeOf((*MockEsportHallClient)(nil).UpdateEsportGameCard), varargs...)
}

// MockEsportHallServer is a mock of EsportHallServer interface.
type MockEsportHallServer struct {
	ctrl     *gomock.Controller
	recorder *MockEsportHallServerMockRecorder
}

// MockEsportHallServerMockRecorder is the mock recorder for MockEsportHallServer.
type MockEsportHallServerMockRecorder struct {
	mock *MockEsportHallServer
}

// NewMockEsportHallServer creates a new mock instance.
func NewMockEsportHallServer(ctrl *gomock.Controller) *MockEsportHallServer {
	mock := &MockEsportHallServer{ctrl: ctrl}
	mock.recorder = &MockEsportHallServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEsportHallServer) EXPECT() *MockEsportHallServerMockRecorder {
	return m.recorder
}

// AddCoachRecommend mocks base method.
func (m *MockEsportHallServer) AddCoachRecommend(ctx context.Context, in *AddCoachRecommendRequest) (*AddCoachRecommendResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddCoachRecommend", ctx, in)
	ret0, _ := ret[0].(*AddCoachRecommendResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddCoachRecommend indicates an expected call of AddCoachRecommend.
func (mr *MockEsportHallServerMockRecorder) AddCoachRecommend(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddCoachRecommend", reflect.TypeOf((*MockEsportHallServer)(nil).AddCoachRecommend), ctx, in)
}

// AddIgnoreRecommendCoach mocks base method.
func (m *MockEsportHallServer) AddIgnoreRecommendCoach(ctx context.Context, in *AddIgnoreRecommendCoachRequest) (*AddIgnoreRecommendCoachResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddIgnoreRecommendCoach", ctx, in)
	ret0, _ := ret[0].(*AddIgnoreRecommendCoachResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddIgnoreRecommendCoach indicates an expected call of AddIgnoreRecommendCoach.
func (mr *MockEsportHallServerMockRecorder) AddIgnoreRecommendCoach(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddIgnoreRecommendCoach", reflect.TypeOf((*MockEsportHallServer)(nil).AddIgnoreRecommendCoach), ctx, in)
}

// BatchGetQuickReceiveSwitch mocks base method.
func (m *MockEsportHallServer) BatchGetQuickReceiveSwitch(ctx context.Context, in *BatchGetQuickReceiveSwitchRequest) (*BatchGetQuickReceiveSwitchResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetQuickReceiveSwitch", ctx, in)
	ret0, _ := ret[0].(*BatchGetQuickReceiveSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetQuickReceiveSwitch indicates an expected call of BatchGetQuickReceiveSwitch.
func (mr *MockEsportHallServerMockRecorder) BatchGetQuickReceiveSwitch(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetQuickReceiveSwitch", reflect.TypeOf((*MockEsportHallServer)(nil).BatchGetQuickReceiveSwitch), ctx, in)
}

// BatchGetSkillProductInfo mocks base method.
func (m *MockEsportHallServer) BatchGetSkillProductInfo(ctx context.Context, in *BatchGetSkillProductInfoRequest) (*BatchGetSkillProductInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetSkillProductInfo", ctx, in)
	ret0, _ := ret[0].(*BatchGetSkillProductInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetSkillProductInfo indicates an expected call of BatchGetSkillProductInfo.
func (mr *MockEsportHallServerMockRecorder) BatchGetSkillProductInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetSkillProductInfo", reflect.TypeOf((*MockEsportHallServer)(nil).BatchGetSkillProductInfo), ctx, in)
}

// CheckFirstRoundOrderRight mocks base method.
func (m *MockEsportHallServer) CheckFirstRoundOrderRight(ctx context.Context, in *CheckFirstRoundOrderRightRequest) (*CheckFirstRoundOrderRightResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckFirstRoundOrderRight", ctx, in)
	ret0, _ := ret[0].(*CheckFirstRoundOrderRightResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckFirstRoundOrderRight indicates an expected call of CheckFirstRoundOrderRight.
func (mr *MockEsportHallServerMockRecorder) CheckFirstRoundOrderRight(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckFirstRoundOrderRight", reflect.TypeOf((*MockEsportHallServer)(nil).CheckFirstRoundOrderRight), ctx, in)
}

// ClearFirstRoundOrder mocks base method.
func (m *MockEsportHallServer) ClearFirstRoundOrder(ctx context.Context, in *ClearFirstRoundOrderRequest) (*ClearFirstRoundOrderResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearFirstRoundOrder", ctx, in)
	ret0, _ := ret[0].(*ClearFirstRoundOrderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ClearFirstRoundOrder indicates an expected call of ClearFirstRoundOrder.
func (mr *MockEsportHallServerMockRecorder) ClearFirstRoundOrder(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearFirstRoundOrder", reflect.TypeOf((*MockEsportHallServer)(nil).ClearFirstRoundOrder), ctx, in)
}

// ClearNewCustomerOrder mocks base method.
func (m *MockEsportHallServer) ClearNewCustomerOrder(ctx context.Context, in *ClearNewCustomerOrderRequest) (*ClearNewCustomerOrderResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearNewCustomerOrder", ctx, in)
	ret0, _ := ret[0].(*ClearNewCustomerOrderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ClearNewCustomerOrder indicates an expected call of ClearNewCustomerOrder.
func (mr *MockEsportHallServerMockRecorder) ClearNewCustomerOrder(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearNewCustomerOrder", reflect.TypeOf((*MockEsportHallServer)(nil).ClearNewCustomerOrder), ctx, in)
}

// CreateEsportGameCard mocks base method.
func (m *MockEsportHallServer) CreateEsportGameCard(ctx context.Context, in *CreateEsportGameCardRequest) (*CreateEsportGameCardResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateEsportGameCard", ctx, in)
	ret0, _ := ret[0].(*CreateEsportGameCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateEsportGameCard indicates an expected call of CreateEsportGameCard.
func (mr *MockEsportHallServerMockRecorder) CreateEsportGameCard(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateEsportGameCard", reflect.TypeOf((*MockEsportHallServer)(nil).CreateEsportGameCard), ctx, in)
}

// DelCoachRecommend mocks base method.
func (m *MockEsportHallServer) DelCoachRecommend(ctx context.Context, in *DelCoachRecommendRequest) (*DelCoachRecommendResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelCoachRecommend", ctx, in)
	ret0, _ := ret[0].(*DelCoachRecommendResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelCoachRecommend indicates an expected call of DelCoachRecommend.
func (mr *MockEsportHallServerMockRecorder) DelCoachRecommend(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelCoachRecommend", reflect.TypeOf((*MockEsportHallServer)(nil).DelCoachRecommend), ctx, in)
}

// DelSkillProduct mocks base method.
func (m *MockEsportHallServer) DelSkillProduct(ctx context.Context, in *DelSkillProductRequest) (*DelSkillProductResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelSkillProduct", ctx, in)
	ret0, _ := ret[0].(*DelSkillProductResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelSkillProduct indicates an expected call of DelSkillProduct.
func (mr *MockEsportHallServerMockRecorder) DelSkillProduct(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelSkillProduct", reflect.TypeOf((*MockEsportHallServer)(nil).DelSkillProduct), ctx, in)
}

// DeleteEsportGameCard mocks base method.
func (m *MockEsportHallServer) DeleteEsportGameCard(ctx context.Context, in *DeleteEsportGameCardRequest) (*DeleteEsportGameCardResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteEsportGameCard", ctx, in)
	ret0, _ := ret[0].(*DeleteEsportGameCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteEsportGameCard indicates an expected call of DeleteEsportGameCard.
func (mr *MockEsportHallServerMockRecorder) DeleteEsportGameCard(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteEsportGameCard", reflect.TypeOf((*MockEsportHallServer)(nil).DeleteEsportGameCard), ctx, in)
}

// GetAllSkillList mocks base method.
func (m *MockEsportHallServer) GetAllSkillList(ctx context.Context, in *GetAllSkillListRequest) (*GetAllSkillListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllSkillList", ctx, in)
	ret0, _ := ret[0].(*GetAllSkillListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllSkillList indicates an expected call of GetAllSkillList.
func (mr *MockEsportHallServerMockRecorder) GetAllSkillList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllSkillList", reflect.TypeOf((*MockEsportHallServer)(nil).GetAllSkillList), ctx, in)
}

// GetCoachIncentiveAddition mocks base method.
func (m *MockEsportHallServer) GetCoachIncentiveAddition(ctx context.Context, in *GetCoachIncentiveAdditionRequest) (*GetCoachIncentiveAdditionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCoachIncentiveAddition", ctx, in)
	ret0, _ := ret[0].(*GetCoachIncentiveAdditionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCoachIncentiveAddition indicates an expected call of GetCoachIncentiveAddition.
func (mr *MockEsportHallServerMockRecorder) GetCoachIncentiveAddition(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCoachIncentiveAddition", reflect.TypeOf((*MockEsportHallServer)(nil).GetCoachIncentiveAddition), ctx, in)
}

// GetCoachIncentiveTaskInfo mocks base method.
func (m *MockEsportHallServer) GetCoachIncentiveTaskInfo(ctx context.Context, in *GetCoachIncentiveTaskInfoRequest) (*GetCoachIncentiveTaskInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCoachIncentiveTaskInfo", ctx, in)
	ret0, _ := ret[0].(*GetCoachIncentiveTaskInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCoachIncentiveTaskInfo indicates an expected call of GetCoachIncentiveTaskInfo.
func (mr *MockEsportHallServerMockRecorder) GetCoachIncentiveTaskInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCoachIncentiveTaskInfo", reflect.TypeOf((*MockEsportHallServer)(nil).GetCoachIncentiveTaskInfo), ctx, in)
}

// GetCoachMinPrice mocks base method.
func (m *MockEsportHallServer) GetCoachMinPrice(ctx context.Context, in *GetCoachMinPriceRequest) (*GetCoachMinPriceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCoachMinPrice", ctx, in)
	ret0, _ := ret[0].(*GetCoachMinPriceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCoachMinPrice indicates an expected call of GetCoachMinPrice.
func (mr *MockEsportHallServerMockRecorder) GetCoachMinPrice(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCoachMinPrice", reflect.TypeOf((*MockEsportHallServer)(nil).GetCoachMinPrice), ctx, in)
}

// GetCoachRecommend mocks base method.
func (m *MockEsportHallServer) GetCoachRecommend(ctx context.Context, in *GetCoachRecommendRequest) (*GetCoachRecommendResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCoachRecommend", ctx, in)
	ret0, _ := ret[0].(*GetCoachRecommendResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCoachRecommend indicates an expected call of GetCoachRecommend.
func (mr *MockEsportHallServerMockRecorder) GetCoachRecommend(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCoachRecommend", reflect.TypeOf((*MockEsportHallServer)(nil).GetCoachRecommend), ctx, in)
}

// GetEsportGameCardInfo mocks base method.
func (m *MockEsportHallServer) GetEsportGameCardInfo(ctx context.Context, in *GetEsportGameCardInfoRequest) (*GetEsportGameCardInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEsportGameCardInfo", ctx, in)
	ret0, _ := ret[0].(*GetEsportGameCardInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEsportGameCardInfo indicates an expected call of GetEsportGameCardInfo.
func (mr *MockEsportHallServerMockRecorder) GetEsportGameCardInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEsportGameCardInfo", reflect.TypeOf((*MockEsportHallServer)(nil).GetEsportGameCardInfo), ctx, in)
}

// GetEsportGameCardList mocks base method.
func (m *MockEsportHallServer) GetEsportGameCardList(ctx context.Context, in *GetEsportGameCardListRequest) (*GetEsportGameCardListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEsportGameCardList", ctx, in)
	ret0, _ := ret[0].(*GetEsportGameCardListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEsportGameCardList indicates an expected call of GetEsportGameCardList.
func (mr *MockEsportHallServerMockRecorder) GetEsportGameCardList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEsportGameCardList", reflect.TypeOf((*MockEsportHallServer)(nil).GetEsportGameCardList), ctx, in)
}

// GetFirstRoundDiscountGameList mocks base method.
func (m *MockEsportHallServer) GetFirstRoundDiscountGameList(ctx context.Context, in *GetFirstRoundDiscountGameListRequest) (*GetFirstRoundDiscountGameListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFirstRoundDiscountGameList", ctx, in)
	ret0, _ := ret[0].(*GetFirstRoundDiscountGameListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFirstRoundDiscountGameList indicates an expected call of GetFirstRoundDiscountGameList.
func (mr *MockEsportHallServerMockRecorder) GetFirstRoundDiscountGameList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFirstRoundDiscountGameList", reflect.TypeOf((*MockEsportHallServer)(nil).GetFirstRoundDiscountGameList), ctx, in)
}

// GetFirstRoundDiscountInfo mocks base method.
func (m *MockEsportHallServer) GetFirstRoundDiscountInfo(ctx context.Context, in *GetFirstRoundDiscountInfoRequest) (*GetFirstRoundDiscountInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFirstRoundDiscountInfo", ctx, in)
	ret0, _ := ret[0].(*GetFirstRoundDiscountInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFirstRoundDiscountInfo indicates an expected call of GetFirstRoundDiscountInfo.
func (mr *MockEsportHallServerMockRecorder) GetFirstRoundDiscountInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFirstRoundDiscountInfo", reflect.TypeOf((*MockEsportHallServer)(nil).GetFirstRoundDiscountInfo), ctx, in)
}

// GetFirstRoundLabelBySkill mocks base method.
func (m *MockEsportHallServer) GetFirstRoundLabelBySkill(ctx context.Context, in *GetFirstRoundLabelBySkillRequest) (*GetFirstRoundLabelBySkillResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFirstRoundLabelBySkill", ctx, in)
	ret0, _ := ret[0].(*GetFirstRoundLabelBySkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFirstRoundLabelBySkill indicates an expected call of GetFirstRoundLabelBySkill.
func (mr *MockEsportHallServerMockRecorder) GetFirstRoundLabelBySkill(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFirstRoundLabelBySkill", reflect.TypeOf((*MockEsportHallServer)(nil).GetFirstRoundLabelBySkill), ctx, in)
}

// GetFirstRoundLabelByUid mocks base method.
func (m *MockEsportHallServer) GetFirstRoundLabelByUid(ctx context.Context, in *GetFirstRoundLabelByUidRequest) (*GetFirstRoundLabelByUidResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFirstRoundLabelByUid", ctx, in)
	ret0, _ := ret[0].(*GetFirstRoundLabelByUidResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFirstRoundLabelByUid indicates an expected call of GetFirstRoundLabelByUid.
func (mr *MockEsportHallServerMockRecorder) GetFirstRoundLabelByUid(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFirstRoundLabelByUid", reflect.TypeOf((*MockEsportHallServer)(nil).GetFirstRoundLabelByUid), ctx, in)
}

// GetFirstRoundOrderRight mocks base method.
func (m *MockEsportHallServer) GetFirstRoundOrderRight(ctx context.Context, in *GetFirstRoundOrderRightRequest) (*GetFirstRoundOrderRightResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFirstRoundOrderRight", ctx, in)
	ret0, _ := ret[0].(*GetFirstRoundOrderRightResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFirstRoundOrderRight indicates an expected call of GetFirstRoundOrderRight.
func (mr *MockEsportHallServerMockRecorder) GetFirstRoundOrderRight(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFirstRoundOrderRight", reflect.TypeOf((*MockEsportHallServer)(nil).GetFirstRoundOrderRight), ctx, in)
}

// GetGameCoachList mocks base method.
func (m *MockEsportHallServer) GetGameCoachList(ctx context.Context, in *GetGameCoachListRequest) (*GetCoachListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameCoachList", ctx, in)
	ret0, _ := ret[0].(*GetCoachListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameCoachList indicates an expected call of GetGameCoachList.
func (mr *MockEsportHallServerMockRecorder) GetGameCoachList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameCoachList", reflect.TypeOf((*MockEsportHallServer)(nil).GetGameCoachList), ctx, in)
}

// GetGameCoachListByUid mocks base method.
func (m *MockEsportHallServer) GetGameCoachListByUid(ctx context.Context, in *GetGameCoachListByUidRequest) (*GetGameCoachListByUidResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameCoachListByUid", ctx, in)
	ret0, _ := ret[0].(*GetGameCoachListByUidResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameCoachListByUid indicates an expected call of GetGameCoachListByUid.
func (mr *MockEsportHallServerMockRecorder) GetGameCoachListByUid(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameCoachListByUid", reflect.TypeOf((*MockEsportHallServer)(nil).GetGameCoachListByUid), ctx, in)
}

// GetGamePriceProperty mocks base method.
func (m *MockEsportHallServer) GetGamePriceProperty(ctx context.Context, in *GetGamePricePropertyRequest) (*GetGamePricePropertyResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGamePriceProperty", ctx, in)
	ret0, _ := ret[0].(*GetGamePricePropertyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGamePriceProperty indicates an expected call of GetGamePriceProperty.
func (mr *MockEsportHallServerMockRecorder) GetGamePriceProperty(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGamePriceProperty", reflect.TypeOf((*MockEsportHallServer)(nil).GetGamePriceProperty), ctx, in)
}

// GetGlobalRcmdCoach mocks base method.
func (m *MockEsportHallServer) GetGlobalRcmdCoach(ctx context.Context, in *GetGlobalRcmdCoachRequest) (*GetGlobalRcmdCoachResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGlobalRcmdCoach", ctx, in)
	ret0, _ := ret[0].(*GetGlobalRcmdCoachResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGlobalRcmdCoach indicates an expected call of GetGlobalRcmdCoach.
func (mr *MockEsportHallServerMockRecorder) GetGlobalRcmdCoach(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGlobalRcmdCoach", reflect.TypeOf((*MockEsportHallServer)(nil).GetGlobalRcmdCoach), ctx, in)
}

// GetNewCustomerDiscountInfo mocks base method.
func (m *MockEsportHallServer) GetNewCustomerDiscountInfo(ctx context.Context, in *GetNewCustomerDiscountInfoRequest) (*GetNewCustomerDiscountInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNewCustomerDiscountInfo", ctx, in)
	ret0, _ := ret[0].(*GetNewCustomerDiscountInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewCustomerDiscountInfo indicates an expected call of GetNewCustomerDiscountInfo.
func (mr *MockEsportHallServerMockRecorder) GetNewCustomerDiscountInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewCustomerDiscountInfo", reflect.TypeOf((*MockEsportHallServer)(nil).GetNewCustomerDiscountInfo), ctx, in)
}

// GetNewCustomerPriceBySkill mocks base method.
func (m *MockEsportHallServer) GetNewCustomerPriceBySkill(ctx context.Context, in *GetNewCustomerPriceBySkillRequest) (*GetNewCustomerPriceBySkillResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNewCustomerPriceBySkill", ctx, in)
	ret0, _ := ret[0].(*GetNewCustomerPriceBySkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewCustomerPriceBySkill indicates an expected call of GetNewCustomerPriceBySkill.
func (mr *MockEsportHallServerMockRecorder) GetNewCustomerPriceBySkill(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewCustomerPriceBySkill", reflect.TypeOf((*MockEsportHallServer)(nil).GetNewCustomerPriceBySkill), ctx, in)
}

// GetNewCustomerPriceByUid mocks base method.
func (m *MockEsportHallServer) GetNewCustomerPriceByUid(ctx context.Context, in *GetNewCustomerPriceByUidRequest) (*GetNewCustomerPriceByUidResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNewCustomerPriceByUid", ctx, in)
	ret0, _ := ret[0].(*GetNewCustomerPriceByUidResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewCustomerPriceByUid indicates an expected call of GetNewCustomerPriceByUid.
func (mr *MockEsportHallServerMockRecorder) GetNewCustomerPriceByUid(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewCustomerPriceByUid", reflect.TypeOf((*MockEsportHallServer)(nil).GetNewCustomerPriceByUid), ctx, in)
}

// GetOperationCoachRecommendWithoutExposed mocks base method.
func (m *MockEsportHallServer) GetOperationCoachRecommendWithoutExposed(ctx context.Context, in *GetOperationCoachRecommendWithoutExposedRequest) (*GetOperationCoachRecommendWithoutExposedResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOperationCoachRecommendWithoutExposed", ctx, in)
	ret0, _ := ret[0].(*GetOperationCoachRecommendWithoutExposedResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOperationCoachRecommendWithoutExposed indicates an expected call of GetOperationCoachRecommendWithoutExposed.
func (mr *MockEsportHallServerMockRecorder) GetOperationCoachRecommendWithoutExposed(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOperationCoachRecommendWithoutExposed", reflect.TypeOf((*MockEsportHallServer)(nil).GetOperationCoachRecommendWithoutExposed), ctx, in)
}

// GetQuickReceiveSwitch mocks base method.
func (m *MockEsportHallServer) GetQuickReceiveSwitch(ctx context.Context, in *GetQuickReceiveSwitchRequest) (*GetQuickReceiveSwitchResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetQuickReceiveSwitch", ctx, in)
	ret0, _ := ret[0].(*GetQuickReceiveSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetQuickReceiveSwitch indicates an expected call of GetQuickReceiveSwitch.
func (mr *MockEsportHallServerMockRecorder) GetQuickReceiveSwitch(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetQuickReceiveSwitch", reflect.TypeOf((*MockEsportHallServer)(nil).GetQuickReceiveSwitch), ctx, in)
}

// GetReCoachForUGC mocks base method.
func (m *MockEsportHallServer) GetReCoachForUGC(ctx context.Context, in *GetReCoachForUGCRequest) (*GetReCoachForUGCResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReCoachForUGC", ctx, in)
	ret0, _ := ret[0].(*GetReCoachForUGCResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReCoachForUGC indicates an expected call of GetReCoachForUGC.
func (mr *MockEsportHallServerMockRecorder) GetReCoachForUGC(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReCoachForUGC", reflect.TypeOf((*MockEsportHallServer)(nil).GetReCoachForUGC), ctx, in)
}

// GetReceiveTimeFrame mocks base method.
func (m *MockEsportHallServer) GetReceiveTimeFrame(ctx context.Context, in *GetReceiveTimeFrameRequest) (*GetReceiveTimeFrameResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReceiveTimeFrame", ctx, in)
	ret0, _ := ret[0].(*GetReceiveTimeFrameResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReceiveTimeFrame indicates an expected call of GetReceiveTimeFrame.
func (mr *MockEsportHallServerMockRecorder) GetReceiveTimeFrame(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReceiveTimeFrame", reflect.TypeOf((*MockEsportHallServer)(nil).GetReceiveTimeFrame), ctx, in)
}

// GetRecommendSkillProduct mocks base method.
func (m *MockEsportHallServer) GetRecommendSkillProduct(ctx context.Context, in *GetRecommendSkillProductReq) (*GetRecommendSkillProductResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecommendSkillProduct", ctx, in)
	ret0, _ := ret[0].(*GetRecommendSkillProductResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecommendSkillProduct indicates an expected call of GetRecommendSkillProduct.
func (mr *MockEsportHallServerMockRecorder) GetRecommendSkillProduct(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecommendSkillProduct", reflect.TypeOf((*MockEsportHallServer)(nil).GetRecommendSkillProduct), ctx, in)
}

// GetSkillProductByUidGameId mocks base method.
func (m *MockEsportHallServer) GetSkillProductByUidGameId(ctx context.Context, in *GetSkillProductByUidGameIdRequest) (*GetSkillProductByUidGameIdResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSkillProductByUidGameId", ctx, in)
	ret0, _ := ret[0].(*GetSkillProductByUidGameIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSkillProductByUidGameId indicates an expected call of GetSkillProductByUidGameId.
func (mr *MockEsportHallServerMockRecorder) GetSkillProductByUidGameId(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSkillProductByUidGameId", reflect.TypeOf((*MockEsportHallServer)(nil).GetSkillProductByUidGameId), ctx, in)
}

// GetSkillProductInfo mocks base method.
func (m *MockEsportHallServer) GetSkillProductInfo(ctx context.Context, in *GetSkillProductInfoRequest) (*GetSkillProductInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSkillProductInfo", ctx, in)
	ret0, _ := ret[0].(*GetSkillProductInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSkillProductInfo indicates an expected call of GetSkillProductInfo.
func (mr *MockEsportHallServerMockRecorder) GetSkillProductInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSkillProductInfo", reflect.TypeOf((*MockEsportHallServer)(nil).GetSkillProductInfo), ctx, in)
}

// GetSkillProductInfoByGameId mocks base method.
func (m *MockEsportHallServer) GetSkillProductInfoByGameId(ctx context.Context, in *GetSkillProductInfoByGameIdRequest) (*GetSkillProductInfoByGameIdResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSkillProductInfoByGameId", ctx, in)
	ret0, _ := ret[0].(*GetSkillProductInfoByGameIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSkillProductInfoByGameId indicates an expected call of GetSkillProductInfoByGameId.
func (mr *MockEsportHallServerMockRecorder) GetSkillProductInfoByGameId(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSkillProductInfoByGameId", reflect.TypeOf((*MockEsportHallServer)(nil).GetSkillProductInfoByGameId), ctx, in)
}

// GetUserTopGamePreSelectConfig mocks base method.
func (m *MockEsportHallServer) GetUserTopGamePreSelectConfig(ctx context.Context, in *GetUserTopGamePreSelectConfigRequest) (*GetUserTopGamePreSelectConfigResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserTopGamePreSelectConfig", ctx, in)
	ret0, _ := ret[0].(*GetUserTopGamePreSelectConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserTopGamePreSelectConfig indicates an expected call of GetUserTopGamePreSelectConfig.
func (mr *MockEsportHallServerMockRecorder) GetUserTopGamePreSelectConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserTopGamePreSelectConfig", reflect.TypeOf((*MockEsportHallServer)(nil).GetUserTopGamePreSelectConfig), ctx, in)
}

// GetVisibleSkillProductList mocks base method.
func (m *MockEsportHallServer) GetVisibleSkillProductList(ctx context.Context, in *GetVisibleSkillProductListRequest) (*GetVisibleSkillProductListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVisibleSkillProductList", ctx, in)
	ret0, _ := ret[0].(*GetVisibleSkillProductListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVisibleSkillProductList indicates an expected call of GetVisibleSkillProductList.
func (mr *MockEsportHallServerMockRecorder) GetVisibleSkillProductList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVisibleSkillProductList", reflect.TypeOf((*MockEsportHallServer)(nil).GetVisibleSkillProductList), ctx, in)
}

// HandleInviteOrder mocks base method.
func (m *MockEsportHallServer) HandleInviteOrder(ctx context.Context, in *HandleInviteOrderRequest) (*HandleInviteOrderResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleInviteOrder", ctx, in)
	ret0, _ := ret[0].(*HandleInviteOrderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandleInviteOrder indicates an expected call of HandleInviteOrder.
func (mr *MockEsportHallServerMockRecorder) HandleInviteOrder(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleInviteOrder", reflect.TypeOf((*MockEsportHallServer)(nil).HandleInviteOrder), ctx, in)
}

// HasFamousPlayer mocks base method.
func (m *MockEsportHallServer) HasFamousPlayer(ctx context.Context, in *HasFamousPlayerRequest) (*HasFamousPlayerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HasFamousPlayer", ctx, in)
	ret0, _ := ret[0].(*HasFamousPlayerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HasFamousPlayer indicates an expected call of HasFamousPlayer.
func (mr *MockEsportHallServerMockRecorder) HasFamousPlayer(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HasFamousPlayer", reflect.TypeOf((*MockEsportHallServer)(nil).HasFamousPlayer), ctx, in)
}

// InitUserSkillInfo mocks base method.
func (m *MockEsportHallServer) InitUserSkillInfo(ctx context.Context, in *InitUserSkillInfoRequest) (*InitUserSkillInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitUserSkillInfo", ctx, in)
	ret0, _ := ret[0].(*InitUserSkillInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitUserSkillInfo indicates an expected call of InitUserSkillInfo.
func (mr *MockEsportHallServerMockRecorder) InitUserSkillInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitUserSkillInfo", reflect.TypeOf((*MockEsportHallServer)(nil).InitUserSkillInfo), ctx, in)
}

// InviteOrder mocks base method.
func (m *MockEsportHallServer) InviteOrder(ctx context.Context, in *InviteOrderRequest) (*InviteOrderResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InviteOrder", ctx, in)
	ret0, _ := ret[0].(*InviteOrderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InviteOrder indicates an expected call of InviteOrder.
func (mr *MockEsportHallServerMockRecorder) InviteOrder(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InviteOrder", reflect.TypeOf((*MockEsportHallServer)(nil).InviteOrder), ctx, in)
}

// ReBuildUserFirstRoundCache mocks base method.
func (m *MockEsportHallServer) ReBuildUserFirstRoundCache(ctx context.Context, in *ReBuildUserFirstRoundCacheRequest) (*ReBuildUserFirstRoundCacheResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReBuildUserFirstRoundCache", ctx, in)
	ret0, _ := ret[0].(*ReBuildUserFirstRoundCacheResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReBuildUserFirstRoundCache indicates an expected call of ReBuildUserFirstRoundCache.
func (mr *MockEsportHallServerMockRecorder) ReBuildUserFirstRoundCache(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReBuildUserFirstRoundCache", reflect.TypeOf((*MockEsportHallServer)(nil).ReBuildUserFirstRoundCache), ctx, in)
}

// ReportExposeCoach mocks base method.
func (m *MockEsportHallServer) ReportExposeCoach(ctx context.Context, in *ReportExposeCoachRequest) (*ReportExposeCoachResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportExposeCoach", ctx, in)
	ret0, _ := ret[0].(*ReportExposeCoachResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReportExposeCoach indicates an expected call of ReportExposeCoach.
func (mr *MockEsportHallServerMockRecorder) ReportExposeCoach(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportExposeCoach", reflect.TypeOf((*MockEsportHallServer)(nil).ReportExposeCoach), ctx, in)
}

// SaveUserTopGamePreSelectConfig mocks base method.
func (m *MockEsportHallServer) SaveUserTopGamePreSelectConfig(ctx context.Context, in *SaveUserTopGamePreSelectConfigRequest) (*SaveUserTopGamePreSelectConfigResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveUserTopGamePreSelectConfig", ctx, in)
	ret0, _ := ret[0].(*SaveUserTopGamePreSelectConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveUserTopGamePreSelectConfig indicates an expected call of SaveUserTopGamePreSelectConfig.
func (mr *MockEsportHallServerMockRecorder) SaveUserTopGamePreSelectConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveUserTopGamePreSelectConfig", reflect.TypeOf((*MockEsportHallServer)(nil).SaveUserTopGamePreSelectConfig), ctx, in)
}

// SearchCoach mocks base method.
func (m *MockEsportHallServer) SearchCoach(ctx context.Context, in *SearchCoachRequest) (*SearchCoachResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchCoach", ctx, in)
	ret0, _ := ret[0].(*SearchCoachResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchCoach indicates an expected call of SearchCoach.
func (mr *MockEsportHallServerMockRecorder) SearchCoach(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchCoach", reflect.TypeOf((*MockEsportHallServer)(nil).SearchCoach), ctx, in)
}

// SendEsportGameCard mocks base method.
func (m *MockEsportHallServer) SendEsportGameCard(ctx context.Context, in *SendEsportGameCardRequest) (*SendEsportGameCardResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendEsportGameCard", ctx, in)
	ret0, _ := ret[0].(*SendEsportGameCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendEsportGameCard indicates an expected call of SendEsportGameCard.
func (mr *MockEsportHallServerMockRecorder) SendEsportGameCard(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendEsportGameCard", reflect.TypeOf((*MockEsportHallServer)(nil).SendEsportGameCard), ctx, in)
}

// SetFirstRoundSwitch mocks base method.
func (m *MockEsportHallServer) SetFirstRoundSwitch(ctx context.Context, in *SetFirstRoundSwitchRequest) (*SetFirstRoundSwitchResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetFirstRoundSwitch", ctx, in)
	ret0, _ := ret[0].(*SetFirstRoundSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetFirstRoundSwitch indicates an expected call of SetFirstRoundSwitch.
func (mr *MockEsportHallServerMockRecorder) SetFirstRoundSwitch(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetFirstRoundSwitch", reflect.TypeOf((*MockEsportHallServer)(nil).SetFirstRoundSwitch), ctx, in)
}

// SetGuaranteeWinSwitch mocks base method.
func (m *MockEsportHallServer) SetGuaranteeWinSwitch(ctx context.Context, in *SetGuaranteeWinSwitchRequest) (*SetGuaranteeWinSwitchResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetGuaranteeWinSwitch", ctx, in)
	ret0, _ := ret[0].(*SetGuaranteeWinSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetGuaranteeWinSwitch indicates an expected call of SetGuaranteeWinSwitch.
func (mr *MockEsportHallServerMockRecorder) SetGuaranteeWinSwitch(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetGuaranteeWinSwitch", reflect.TypeOf((*MockEsportHallServer)(nil).SetGuaranteeWinSwitch), ctx, in)
}

// SetNewCustomerSwitch mocks base method.
func (m *MockEsportHallServer) SetNewCustomerSwitch(ctx context.Context, in *SetNewCustomerSwitchRequest) (*SetNewCustomerSwitchResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetNewCustomerSwitch", ctx, in)
	ret0, _ := ret[0].(*SetNewCustomerSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetNewCustomerSwitch indicates an expected call of SetNewCustomerSwitch.
func (mr *MockEsportHallServerMockRecorder) SetNewCustomerSwitch(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetNewCustomerSwitch", reflect.TypeOf((*MockEsportHallServer)(nil).SetNewCustomerSwitch), ctx, in)
}

// SetQuickReceiveSwitch mocks base method.
func (m *MockEsportHallServer) SetQuickReceiveSwitch(ctx context.Context, in *SetQuickReceiveSwitchRequest) (*SetQuickReceiveSwitchResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetQuickReceiveSwitch", ctx, in)
	ret0, _ := ret[0].(*SetQuickReceiveSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetQuickReceiveSwitch indicates an expected call of SetQuickReceiveSwitch.
func (mr *MockEsportHallServerMockRecorder) SetQuickReceiveSwitch(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetQuickReceiveSwitch", reflect.TypeOf((*MockEsportHallServer)(nil).SetQuickReceiveSwitch), ctx, in)
}

// SetReceiveTimeFrame mocks base method.
func (m *MockEsportHallServer) SetReceiveTimeFrame(ctx context.Context, in *SetReceiveTimeFrameRequest) (*SetReceiveTimeFrameResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetReceiveTimeFrame", ctx, in)
	ret0, _ := ret[0].(*SetReceiveTimeFrameResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetReceiveTimeFrame indicates an expected call of SetReceiveTimeFrame.
func (mr *MockEsportHallServerMockRecorder) SetReceiveTimeFrame(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetReceiveTimeFrame", reflect.TypeOf((*MockEsportHallServer)(nil).SetReceiveTimeFrame), ctx, in)
}

// SetSkillPrice mocks base method.
func (m *MockEsportHallServer) SetSkillPrice(ctx context.Context, in *SetSkillPriceRequest) (*SetSkillPriceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetSkillPrice", ctx, in)
	ret0, _ := ret[0].(*SetSkillPriceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetSkillPrice indicates an expected call of SetSkillPrice.
func (mr *MockEsportHallServerMockRecorder) SetSkillPrice(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetSkillPrice", reflect.TypeOf((*MockEsportHallServer)(nil).SetSkillPrice), ctx, in)
}

// SetSkillReceiveSwitch mocks base method.
func (m *MockEsportHallServer) SetSkillReceiveSwitch(ctx context.Context, in *SetSkillReceiveSwitchRequest) (*SetSkillReceiveSwitchResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetSkillReceiveSwitch", ctx, in)
	ret0, _ := ret[0].(*SetSkillReceiveSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetSkillReceiveSwitch indicates an expected call of SetSkillReceiveSwitch.
func (mr *MockEsportHallServerMockRecorder) SetSkillReceiveSwitch(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetSkillReceiveSwitch", reflect.TypeOf((*MockEsportHallServer)(nil).SetSkillReceiveSwitch), ctx, in)
}

// SetUserToRealCoach mocks base method.
func (m *MockEsportHallServer) SetUserToRealCoach(ctx context.Context, in *SetUserToRealCoachRequest) (*SetUserToRealCoachResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserToRealCoach", ctx, in)
	ret0, _ := ret[0].(*SetUserToRealCoachResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserToRealCoach indicates an expected call of SetUserToRealCoach.
func (mr *MockEsportHallServerMockRecorder) SetUserToRealCoach(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserToRealCoach", reflect.TypeOf((*MockEsportHallServer)(nil).SetUserToRealCoach), ctx, in)
}

// Test mocks base method.
func (m *MockEsportHallServer) Test(ctx context.Context, in *TestRequest) (*TestResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Test", ctx, in)
	ret0, _ := ret[0].(*TestResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Test indicates an expected call of Test.
func (mr *MockEsportHallServerMockRecorder) Test(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Test", reflect.TypeOf((*MockEsportHallServer)(nil).Test), ctx, in)
}

// UpdateCoachRecommend mocks base method.
func (m *MockEsportHallServer) UpdateCoachRecommend(ctx context.Context, in *UpdateCoachRecommendRequest) (*UpdateCoachRecommendResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCoachRecommend", ctx, in)
	ret0, _ := ret[0].(*UpdateCoachRecommendResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateCoachRecommend indicates an expected call of UpdateCoachRecommend.
func (mr *MockEsportHallServerMockRecorder) UpdateCoachRecommend(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCoachRecommend", reflect.TypeOf((*MockEsportHallServer)(nil).UpdateCoachRecommend), ctx, in)
}

// UpdateEsportGameCard mocks base method.
func (m *MockEsportHallServer) UpdateEsportGameCard(ctx context.Context, in *UpdateEsportGameCardRequest) (*UpdateEsportGameCardResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateEsportGameCard", ctx, in)
	ret0, _ := ret[0].(*UpdateEsportGameCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateEsportGameCard indicates an expected call of UpdateEsportGameCard.
func (mr *MockEsportHallServerMockRecorder) UpdateEsportGameCard(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateEsportGameCard", reflect.TypeOf((*MockEsportHallServer)(nil).UpdateEsportGameCard), ctx, in)
}
