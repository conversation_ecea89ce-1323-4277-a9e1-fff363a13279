@startuml
'https://plantuml.com/sequence-diagram


actor 客户端 as app



group 请求首页
autonumber
app ->AIHubsvr:请求首页
AIHubsvr -> 推荐工程 : cpid
推荐工程 --> AIHubsvr : 卡片列表
AIHubsvr --> app : cp列表+卡片列表+收到的组CP请求
end
group 创建/修改角色卡
autonumber
H5 ->AIHubsvr:获取Cp列表
AIHubsvr --> H5 : Cp列表
H5 -> AIHubsvr : 生成简介/形象图+角色卡id
AIHubsvr -> AIRcmd : 生成简介/形象图
AIRcmd --> AIHubsvr: 返回简介/形象图
AIHubsvr -> T盾: 送审
T盾 --> AIHubsvr
AIHubsvr --> H5:角色卡id
T盾kafka -> subCenter: 审核结果（角色卡id）
subCenter -> AIHubsvr: 处理审核结果（角色卡id）
H5 -> AIHubsvr: 定时请求生成结果（角色卡id）
AIHubsvr --> H5: 返回生成结果
H5 -> AIHubsvr : 创建角色卡+角色卡id
AIHubsvr--> 推荐kafka : 创建事件
AIHubsvr --> H5 : 角色卡
H5 -> AIHubsvr : 修改角色卡+角色卡id
AIHubsvr--> 推荐kafka : 修改事件
AIHubsvr --> H5 : 角色卡


end




@enduml