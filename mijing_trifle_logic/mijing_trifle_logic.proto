syntax = "proto3";

package ga.mijing_trifle_logic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/mijing-trifle-logic";


//---------------------------------------------------------

enum FinishMode {
  FINISH_MODE_UNSPECIFIED = 0;
  FINISH_MODE_ONLY_ONCE_FOREVER = 1;      // 永久单次
  FINISH_MODE_ONLY_ONCE_DAILY = 2;        // 每天一次
  FINISH_MODE_MULTI_TIME_DAILY = 3;       // 可每天多次
}

enum DisplayMode {
  DISPLAY_MODE_UNSPECIFIED = 0;
  DISPLAY_MODE_ALWAYS = 1;         // 常驻
  DISPLAY_MODE_WHEN_FINISHED = 2;  // 神秘小事，完成时才展示
}

message TrifleTask {
  string id = 1;                        // 小事配置id
  string title = 2;                     // 小事标题
  DisplayMode display_mode = 4;         // 是否显示
  FinishMode finish_mode = 5;           // 完成次数的类型
  string homepage_text = 9;             // 首页小事文案内容
  string task_content = 10;             // 任务内容
  string task_rule = 11;                // 任务规则文案
  string homepage_image_uri = 12;       // 小事首页展示图片
  string image_uri = 13;                // 小事展示页图片(已完成)
  string main_page_image_uri = 14;      // 主页图片
  string award_desc = 15;               // 奖励文案
  string channel_share_image_uri = 16;  // 房间分享图片
  string unfinished_image_uri = 17;     // 小事展示页图片(未完成)
}


enum TrifleStatus {
  TRIFLE_STATUS_UNSPECIFIED = 0;
  TRIFLE_STATUS_LOCKED = 1;             // 未解锁
  TRIFLE_STATUS_WAITING_ACCEPT = 2;     // 等待领取
  TRIFLE_STATUS_LIGHTED = 3;            // 已点亮
}

message TrifleRecord {
  TrifleTask task = 1;
  TrifleStatus trifle_status = 3;             //小事状态
  bool already_read = 4;                      //是否已读
  int64 last_finish_at = 5;                   //最后完成时间
  int64 first_finish_at = 6;                  //第一次完成时间
  string trifle_record_id = 9;                //记录id，传给前端传这个


  // -----------------------------------------------------
  // 没用的字段，但是有限制，又删不掉
  uint64 record_id = 2 [deprecated = true];
  repeated string account_list = 7 [deprecated = true];
  repeated string drama_cover_url_list = 8 [deprecated = true];
}

// 获取首页的小事列表
message GetHomepageTrifleInfoRequest {
  ga.BaseReq base_req = 1;
}

message GetHomepageTrifleInfoResponse {
  ga.BaseResp base_resp = 1;
  repeated TrifleTask example_tasks = 2;      // 首页示例任务
  uint32 finished_task_count = 3;             // 完成小事数
  uint32 total_task_count = 4;                // 总小事数
  uint32 unread_task_count = 5;               // 未读小事数
}


// 获取全部小事列表
message GetTrifleRecordListRequest {
  ga.BaseReq base_req = 1;

//  bytes load_more = 11;
}

message GetTrifleRecordListResponse {
  ga.BaseResp base_resp = 1;
  repeated TrifleRecord records = 2;

//  bytes load_more = 11;
}


// 标记小事状态已读
message MarkTrifleReadRequest {
  ga.BaseReq base_req = 1;
  string record_id = 2;
}

message MarkTrifleReadResponse {
  ga.BaseResp base_resp = 1;
}

message NewTrifleRecordPush {
  TrifleRecord record = 1;
}

// 谜之小事视觉风格配置
message TrifleVisualStyle {
  enum Platform {
    PLATFORM_UNSPECIFIED = 0;
    PLATFORM_IOS = 1;
    PLATFORM_ANDROID = 2;
  }

  // 入口动画（有好友）
  message EntranceHasFriends {
    string bubble_body_url = 1;  // 气泡框主体，格式：.jpg、.jpeg、.png
    string bubble_border_url = 2;  // 气泡框边框，格式：.jpg、.jpeg、.png
  }

  // p.s 格式不会进行校验，只是用来做文档说明
  string id = 1;  // 唯一 ID（新增时填空，更新时传递）
  string name = 2;  // 图标名称
  Platform platform = 3;  // 平台
  string background_pic_url = 4;  // 背景图片 URL，格式：.jpg、.jpeg、.png
  string entrance_pic_url = 5;  // 入口图片 URL，格式：.jpg、.jpeg、.png
  string seat_pic_url = 6;  // 队伍座位图片 URL，格式：.jpg、.jpeg、.png
  string invite_pic_url = 7;  // 邀请图片 URL，格式：.jpg、.jpeg、.png
  string entrance_no_friends_url = 8;  // 入口动画（无好友），格式：.zip
  repeated EntranceHasFriends entrance_has_friends_list = 9;  // 入口动画（有好友）列表
  int64 start_at = 10;  // 展示时段开始时间戳，格式：unix 时间戳，e.x：1697009793
  int64 end_at = 11;  // 展示时段结束时间戳，格式：unix 时间戳，e.x：1697009793
  string all_friend_entrance_url = 12;  // 全部好友入口图片 URL，格式：.jpg、.jpeg、.png

  int64 created_at = 101;  // 创建时间，格式：unix 时间戳，e.x：1697009793
  int64 updated_at = 102;  // 更新时间，格式：unix 时间戳，e.x：1697009793
}

// 获取当前生效的谜之小事视觉风格配置列表，p.s 后端根据客户端类型（ios or Android）返回当前正在生效的配置
message GetActiveTrifleVisualStyleRequest {
  ga.BaseReq base_req = 1;
}

message GetActiveTrifleVisualStyleResponse {
  ga.BaseResp base_resp = 1;
  TrifleVisualStyle trifle_visual_style = 2;  // 谜之小事视觉风格配置
}
