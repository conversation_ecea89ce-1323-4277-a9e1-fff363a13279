#!/bin/bash
#echo $1 $2 $3 $4 $5 $6
if [[ -z "$1" ]];then
  echo "empty server values.yaml file"
  echo "usage: chart_merge.sh flake.yaml 1.0.0 v202007310000-master-xxxx generalServer 2.0.0"
  exit 1
fi
if [[ -z "$2" ]];then
  echo "empty chart version">&2
  echo "usage: chart_merge.sh flake.yaml 1.0.0 v202007310000-master-xxxx generalServer 2.0.0"
  exit 1
fi
if [[  -z "$3" ]];then
    echo "empty app version">&2
    echo "usage: chart_merge.sh flake.yaml 1.0.0 v202007310000-master-xxxx generalServer 2.0.0"
    exit 1
fi
if [[  -z "$4" ]];then
    echo "empty dependencies chart name">&2
    echo "usage: chart_merge.sh flake.yaml 1.0.0 v202007310000-master-xxxx generalServer 2.0.0"
    exit 1
fi
if [[  -z "$5" ]];then
    echo $5
    echo "empty dependencies chart version">&2
    echo "usage: chart_merge.sh flake.yaml 1.0.0 v202007310000-master-xxxx generalServer 2.0.0"
    exit 1
fi

#default_values=${DEFAULT_CHART_VALUES}
#if [[ -z "${default_values}" ]];then
#  default_values="kubernetes/prod/defaults.yaml"
#fi

helm_registry=${CHART_REGISTRY}
if [[ -z "${helm3_registry}" ]];then
  helm_registry="tt-testing"
fi

server_values=$1
version=$2
app_version=$3
app_name=$(basename ${server_values} .yaml)

dep_name=${4}
dep_version=${5}

dir=${6}

if [ -z ${dir} ]; then
    dir="${app_name}_tmp"
fi

mkdir -p $dir

echo "helm3 template ci/chart-for-merge -f ./kubernetes/testing/default.yaml -f ${server_values} |grep -v \"# Source\"|grep -v \"\-\-\-\"|sed 's/^/  /'|sed \"1 i\\${dep_name}: \"|sed \"s/tag: latest/tag: ${app_version}/g\" >${dir}/values.yaml"

helm3 template ./ci/chart-for-merge -f ./kubernetes/testing/default.yaml -f ${server_values} |
grep -v "# Source"|grep -v "\-\-\-"|
sed 's/^/  /'|
sed "1 i\\${dep_name}: "|
sed "s/tag: .*$/tag: ${app_version}/g" >${dir}/values.yaml

if [[ $? -ne 0 ]]; then
      echo "helm3 template error"
      exit 1
fi

echo "apiVersion: v1
appVersion: ${app_version}
description: ${app_name}
home: https://gitlab.ttyuyin.com/avengers/quicksilver
name: ${app_name}
version: ${version}
">${dir}/Chart.yaml
echo "dependencies:
- name: ${dep_name}
  version: ${dep_version}
  repository: https://testing-harbor.ttyuyin.com/chartrepo/quicksilver
">${dir}/requirements.yaml

helm3 dep update ${dir} --skip-refresh

helm3 -n quicksilver template ${dir} >/dev/null

if [[ ! $? -eq 0 ]]; then
  echo "valid chart failed"
  rm -rf ${dir}
  exit 1
fi

echo "valid chart [${app_name}] succeed"
helm3 push ${dir} ${helm_registry}
if [[ ! $? -eq 0 ]]; then
  echo "helm3 push failed"
  rm -rf ${dir}
  exit 1
fi

echo "helm3 push [${app_name}] succeed"

rm -rf ${dir}



