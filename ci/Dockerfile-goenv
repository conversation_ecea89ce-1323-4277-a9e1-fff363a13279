############################
# STEP 1 download dependencies 
############################
FROM registry.ttyuyin.com/library/golang:1.12.9-alpine as base_builder

WORKDIR /go/src/golang.52tt.com/

ENV CGO_ENABLED=0
ENV GOPROXY="https://goproxy.ttyuyin.com"
ENV GO111MODULE=on

COPY go.mod .
COPY go.sum .

#This is the 'magic' step that will download all the dependencies that are specified in 
# the go.mod and go.sum file.
# Because of how the layer caching system works in Docker, the  go mod download 
# command will _ only_ be re-run when the go.mod or go.sum file change 
# (or when we add another docker instruction this line)
RUN go mod download
