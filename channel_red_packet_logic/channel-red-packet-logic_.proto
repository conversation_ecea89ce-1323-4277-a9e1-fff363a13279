syntax = "proto3";

// 房间礼物红包玩法
package ga.channel_red_packet_logic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/channel-red-packet-logic";

message RedPacketConf {
  uint32 red_packet_id = 1;
  repeated uint32 gift_id_list = 2;
  uint32 total_price = 3;           // 总价值
}

message RedPacketUserInfo {
  uint32 uid = 1;
  string account = 2;
  string nickname = 3;

  ga.UserUKWInfo user_ukw_info = 4; // 神秘人信息
  ga.UserProfile user_profile  = 5; // 用户信息
}

message RedPacketInfo {
  string order_id = 1;  // 红包订单号
  RedPacketConf red_packet_conf = 2;
  string public_msg = 3;            // 公屏文案

  uint32 begin_time = 4;            // 红包雨开始时间
  uint32 end_time = 5;              // 红包雨结束时间
  RedPacketUserInfo sender_user = 6;// 金主爸爸信息
}

// 获取红包配置列表
message GetRedPacketConfReq {
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
}

message GetRedPacketConfResp {
  ga.BaseResp base_resp = 1;
  repeated RedPacketConf list = 2;
  repeated string default_public_msg_list = 3;
  string invalid_desc = 4;  // 无效红包描述
}

// 发红包
message SendRedPacketReq {
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
  uint32 red_packet_id = 3;
  string public_msg = 4;    // 公屏文案
}

message SendRedPacketResp {
  ga.BaseResp base_resp = 1;
}

// 获取房间排队的红包列表
message GetRedPacketListReq {
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
}

message GetRedPacketListResp {
  ga.BaseResp base_resp = 1;
  repeated RedPacketInfo list = 2;
  uint32 server_ts = 3;
  uint32 settle_duration_sec = 4; // 结算持续时长，客户端在结算上报点击次数时需根据 settle_duration_sec-1 去打散请求
  string rule_desc = 5;
}

// 上报用户红包雨点击次数
message ReportRedPacketClickCntReq {
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
  string order_id = 3;  // 红包订单号
  uint32 click_cnt = 4;
}

message ReportRedPacketClickCntResp {
  ga.BaseResp base_resp = 1;
}

// 红包队列变更push opt
message RedPacketChangeOpt {
  uint32 channel_id = 1;
  repeated RedPacketInfo list = 2;
  uint32 server_ts = 3;
  uint32 settle_duration_sec = 4; // 结算持续时长，客户端在结算上报点击次数时需根据 settle_duration_sec-1 去打散请求
  string rule_desc = 5;
}

// 红包雨变更push opt
message RedPacketRainChangeOpt {
  enum ChangeType {
    unknown = 0;
    begin = 1;  // 红包雨开始
    end = 2;    // 红包雨结束
  }

  uint32 channel_id = 1;
  RedPacketInfo red_packet = 2;
  uint32 change_type = 3;
  uint32 server_ts = 4;
}

message GiftAwardInfo {
  uint32 gift_id = 1;
  uint32 count = 2;
}

message LuckyMemInfo {
  RedPacketUserInfo user_info = 1;
  uint32 award_price = 2; // 得到礼物价值
}

// 红包结算奖励信息push opt
message RedPacketAwardOpt {
  uint32 uid = 1;
  repeated GiftAwardInfo award_list = 2;
  uint32 award_price = 3;                     // 得到礼物价值
  uint32 gift_fin_time = 4;                   // 得到的礼物的过期时间
  RedPacketUserInfo sender_user = 5;          // 金主爸爸信息
  repeated LuckyMemInfo lucky_rank_list = 6;  // 手气最佳列表
  uint32 server_ts = 7;
  string public_msg = 8;                      // 公屏文案
  uint32 award_user_cnt = 9;                  // 获得奖励的用户的数量
}

// 抢红包结算
message RedPacketSettleOpt {
  uint32 channel_id = 1;
  RedPacketInfo red_packet = 2;
  repeated LuckyMemInfo lucky_rank_list = 3;  // 手气最佳列表
  uint32 server_ts = 4;
  repeated uint32 skip_uid_list = 5;          // 不需要展示结算弹窗的用户
  uint32 award_user_cnt = 6;                  // 获得奖励的用户的数量

  bool is_invalid = 7;      // 红包是否无效（参与人数过少则无效）
  string invalid_desc = 8;  // 无效红包描述（弹窗提示文案）
}