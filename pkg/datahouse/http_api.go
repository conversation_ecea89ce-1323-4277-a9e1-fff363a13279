package datahouse

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net"
	"net/http"
	urlutil "net/url"
	"os"
	"strconv"
	"strings"
	"time"

	"golang.52tt.com/pkg/foundation/utils"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/web/metrics"
)

// doc: https://q9jvw0u5f5.feishu.cn/docx/HFaSd2avWoPbp9xuWs3c7lQmnXe

var (
	defaultHTTPClient = &http.Client{ // 这里默认不设置 http.Timeout, 在外部使用ctx设置超时
		Transport: &http.Transport{
			Proxy: http.ProxyFromEnvironment,
			DialContext: (&net.Dialer{
				Timeout:   3 * time.Second, // tcp建立连接超时
				KeepAlive: 30 * time.Second,
			}).DialContext,
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 100,
			IdleConnTimeout:     time.Duration(90) * time.Second,
		},
	}

	Business2Config = map[string]*ReqUrlInfo{}
)

const (
	DataHouseApiSceneOne   = "ApiOne" //  多人互动签约成员的公会和房间维度数据
	DataHouseApiSceneTwo   = "ApiTwo" //  多人互动签约成员和签约主播的公会和个人维度数据
	DataHouseApiSceneThree = "ApiThree"
)

const (
	Testing    = "test"
	Production = "prod"

	/*
		Test_DataHouseHttpPrefixUrl = "http://*************:10086/107"   // 云测
		Test_DataHouseHttpApiToken  = "4798370757f1cce3a7fe5f1c14bec351" // 云测

		Prod_DataHouseHttpPrefixUrl = "http://***********:8080/hela-gateway/45" // 生产
		Prod_DataHouseHttpApiToken  = "add2b0dc69f687f5d7d2e0ac2ea954de"        // 生产

		// 主播中心/会长服务号/娱乐厅从业者中心
		Test_DataHouseCenterHttpPrefixUrl = "http://*************:10086/107"   // 云测
		Test_DataHouseCenterHttpApiToken  = "********************************" // 云测  token
		Prod_DataHouseCenterHttpPrefixUrl = "http://*************:10086/107"   // 生产 todo
		Prod_DataHouseCenterHttpApiToken  = "********************************" // 生产 todo
	*/
)

var env = Production

func init() {

	bin, err := ioutil.ReadFile("/data/oss/conf-center/tt/datahouse.json")
	if err != nil {
		log.Errorln(err)
	} else {
		err = json.Unmarshal(bin, &Business2Config)
		if err != nil {
			log.Errorf("json.Unmarshal fail %v, bin %s", err, bin)
		} else {
			log.Infof("Get config %s, buf %s", Business2Config, bin)
		}
	}

	MY_CLUSTER := os.Getenv("MY_CLUSTER")

	if strings.HasPrefix(MY_CLUSTER, "dev") {
		env = Testing
	} else if strings.HasPrefix(MY_CLUSTER, "test") {
		env = Testing
	} else {
		env = Production
	}

	log.Infof("get dataHouse MY_CLUSTER %s, env %s, mapScene2EnvUrlInfo %s", MY_CLUSTER, env, utils.ToJson(mapScene2EnvUrlInfo))
}

type ReqUrlInfo struct {
	Url   string
	Token string
}

func (c ReqUrlInfo) String() string {
	return fmt.Sprintf("Url:%s Token:%s", c.Url, c.Token)
}

var MapEnv2UserLoginHistoryInfoConf = map[string]ReqUrlInfo{
	Testing:    {Url: "http://*************:10086/107/queryDeviceLastLoginInfo", Token: "9d16e551b3664fbb12a6f84631e364d4"},
	Production: {Url: "http://dsp-hela-gateway.data-service.svc.cluster.local/45/queryDeviceLastLoginInfo", Token: "9d16e551b3664fbb12a6f84631e364d4"},
}

var MapEnv2UserBanHistoryLogConf = map[string]ReqUrlInfo{
	Testing:    {Url: "http://*************:10086/107/queryBanLog", Token: "9d16e551b3664fbb12a6f84631e364d4"},
	Production: {Url: "http://dsp-hela-gateway.data-service.svc.cluster.local/45/queryBanLog", Token: "9d16e551b3664fbb12a6f84631e364d4"},
}

var mapScene2EnvUrlInfo = map[string]map[string]ReqUrlInfo{
	DataHouseApiSceneOne: map[string]ReqUrlInfo{
		Testing: {
			"http://*************:10086/107",
			"4798370757f1cce3a7fe5f1c14bec351",
		},
		Production: {
			"http://tt-activity-data-api.data-service.svc.cluster.local/tt-activity/tt",
			"add2b0dc69f687f5d7d2e0ac2ea954de",
		},
	},
	DataHouseApiSceneTwo: map[string]ReqUrlInfo{
		Testing: {
			"http://*************:10086/107",
			"********************************",
		},
		Production: {
			"http://tt-activity-data-api.data-service.svc.cluster.local/tt-activity/tt",
			"27bd4a08e51a58cd9abd881f6b072d67",
		},
	},
	DataHouseApiSceneThree: map[string]ReqUrlInfo{
		Testing: {
			"http://*************:10086/107",
			"********************************",
		},
		Production: {
			"http://dsp-hela-gateway.data-service.svc.cluster.local/45",
			"27bd4a08e51a58cd9abd881f6b072d67",
		},
	},
}

func GetUrlInfoByApiScene(apiScene string) ReqUrlInfo {
	return mapScene2EnvUrlInfo[apiScene][env]
}

// 从数仓获取用户月维度活跃天数，付费关系链数据
func GetPractitionerGuildMonthlyDataFromDH(guildId uint32, uidList []uint32, startTm, endTm time.Time) (map[uint32]uint32, map[uint32]uint32, map[uint32]uint32, error) {
	client := http.Client{
		Timeout: 5 * time.Second,
	}

	mapUid2FiveHourActiveCnt := make(map[uint32]uint32, 0)
	mapUid2SevenHourActiveCnt := make(map[uint32]uint32, 0)
	mapUid2ConsumeRelationChains := make(map[uint32]uint32, 0)

	if len(uidList) == 0 {
		log.Debugf("GetPractitionerGuildMonthlyDataFromDH is empty guildId:%d uidList:%v startTm:%v endTm:%v",
			guildId, uidList, startTm, endTm)
		return mapUid2FiveHourActiveCnt, mapUid2SevenHourActiveCnt, mapUid2ConsumeRelationChains, nil
	}

	log.Debugf("GetPractitionerGuildMonthlyDataFromDH begin guildId:%d uidList:%v startTm:%v endTm:%v",
		guildId, uidList, startTm, endTm)

	strUidList := make([]string, 0)
	for _, uid := range uidList {
		strUidList = append(strUidList, fmt.Sprintf("%d", uid))
	}

	strUid := strings.Join(strUidList, ",")
	startMonth := fmt.Sprintf("%04d%02d", startTm.Year(), startTm.Month())
	endMonth := fmt.Sprintf("%04d%02d", endTm.Year(), endTm.Month())

	url := fmt.Sprintf("%s/queryPractitionerMonthInfo?start_month=%s&end_month=%s&guild_id=%d&uid=%s&apiToken=%s&pageSize=1000",
		GetUrlInfoByApiScene(DataHouseApiSceneOne).Url, startMonth, endMonth, guildId, strUid, GetUrlInfoByApiScene(DataHouseApiSceneOne).Token)
	resp, err := client.Get(url)
	if err != nil {
		log.Errorf("GetPractitionerGuildMonthlyDataFromDH GET fail url:%s err:%v", url, err)
		return mapUid2FiveHourActiveCnt, mapUid2SevenHourActiveCnt, mapUid2ConsumeRelationChains, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		log.Errorf("GetPractitionerGuildMonthlyDataFromDH GET response not ok:%s url:%s", resp.Status, url)
		return mapUid2FiveHourActiveCnt, mapUid2SevenHourActiveCnt, mapUid2ConsumeRelationChains, errors.New(resp.Status)
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("GetPractitionerGuildMonthlyDataFromDH ioutil.ReadAll failed url:%s err:%v", url, err)
		return mapUid2FiveHourActiveCnt, mapUid2SevenHourActiveCnt, mapUid2ConsumeRelationChains, err
	}
	respData := &struct {
		Code uint32 `json:"code"`
		Msg  string `json:"msg"`

		Data struct {
			Data []struct {
				Uid             uint32 `json:"uid"`
				GuildId         uint32 `json:"guild_id"`
				PayChainCnt     uint32 `json:"pay_chain_cnt"`
				DataMonth       string `json:"data_month"`
				LoginDaysFiveH  uint32 `json:"login_days_5h"`
				LoginDaysSevenH uint32 `json:"login_days_7h"`
			} `json:"data"`
		} `json:"data"`
	}{}

	err = json.Unmarshal(body, respData)
	if err != nil {
		log.Errorf("GetPractitionerGuildMonthlyDataFromDH Unmarshal failed url:%s body:%v err:%v", url, body, err)
		return mapUid2FiveHourActiveCnt, mapUid2SevenHourActiveCnt, mapUid2ConsumeRelationChains, err
	}

	if respData.Code != 200 {
		log.Errorf("GetPractitionerGuildMonthlyDataFromDH no 200 code failed url:%s respData:%v", url, respData)
		return mapUid2FiveHourActiveCnt, mapUid2SevenHourActiveCnt, mapUid2ConsumeRelationChains, errors.New(respData.Msg)
	}

	for _, data := range respData.Data.Data {
		mapUid2ConsumeRelationChains[data.Uid] = data.PayChainCnt
		mapUid2FiveHourActiveCnt[data.Uid] = data.LoginDaysFiveH
		mapUid2SevenHourActiveCnt[data.Uid] = data.LoginDaysSevenH
	}

	log.Debugf("GetPractitionerGuildMonthlyDataFromDH end GetPractitionerMonthlyDataFromDH begin guildId:%d uidList:%v startTm:%v endTm:%v "+
		" respData:%v", guildId, uidList, startTm, endTm, respData)
	return mapUid2FiveHourActiveCnt, mapUid2SevenHourActiveCnt, mapUid2ConsumeRelationChains, nil
}

// 从数仓获取用户日维度TT在线时长， 接档送礼人数， 接档公屏互动数
func GetPractitionerGuildDailyDataFromDH(guildId uint32, uidList []uint32, startTm, endTm time.Time) (map[string][]uint32, error) {
	client := http.Client{
		Timeout: 5 * time.Second,
	}

	mapUid2DailyData := make(map[string][]uint32, 0)

	if len(uidList) == 0 {
		log.Debugf("GetPractitionerGuildDailyDataFromDH is empty guildId:%d uidList:%v startTm:%v endTm:%v",
			guildId, uidList, startTm, endTm)
		return mapUid2DailyData, nil
	}

	for _, uid := range uidList {
		for tm := startTm; tm.Unix() <= endTm.Unix(); tm = tm.AddDate(0, 0, 1) {
			cntList := []uint32{0, 0, 0}
			mapUid2DailyData[fmt.Sprintf("%d-%04d%02d%02d", uid, tm.Year(), tm.Month(), tm.Day())] = cntList
		}
	}

	log.Debugf("GetPractitionerGuildDailyDataFromDH begin guildId:%d uidList:%v startTm:%v endTm:%v",
		guildId, uidList, startTm, endTm)

	strUidList := make([]string, 0)
	for _, uid := range uidList {
		strUidList = append(strUidList, fmt.Sprintf("%d", uid))
	}

	strUid := strings.Join(strUidList, ",")
	startDate := fmt.Sprintf("%04d%02d%02d", startTm.Year(), startTm.Month(), startTm.Day())
	endDate := fmt.Sprintf("%04d%02d%02d", endTm.Year(), endTm.Month(), endTm.Day())

	url := fmt.Sprintf("%s/queryPractitionerDayInfo?start_date=%s&end_date=%s&guild_id=%d&uid=%s&apiToken=%s&pageSize=1000",
		GetUrlInfoByApiScene(DataHouseApiSceneOne).Url, startDate, endDate, guildId, strUid, GetUrlInfoByApiScene(DataHouseApiSceneOne).Token)
	resp, err := client.Get(url)
	if err != nil {
		log.Errorf("GetPractitionerGuildDailyDataFromDH GET fail url:%s err:%v", url, err)
		return mapUid2DailyData, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		log.Errorf("GetPractitionerGuildDailyDataFromDH GET response not ok:%s url:%s", resp.Status, url)
		return mapUid2DailyData, errors.New(resp.Status)
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("GetPractitionerGuildDailyDataFromDH ioutil.ReadAll failed url:%s err:%v", url, err)
		return mapUid2DailyData, err
	}

	respData := &struct {
		Code uint32 `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			Data []struct {
				Uid            uint32 `json:"uid"`
				GuildId        uint32 `json:"guild_id"`
				Date           string `json:"data_date"`
				LoginMin       uint32 `json:"login_dr_d"`
				MicMsgCnt      uint32 `json:"mic_message_cnt_d"`
				MicSendGiftCnt uint32 `json:"mic_sendgift_user_d"`
			} `json:"data"`
		} `json:"data"`
	}{}

	err = json.Unmarshal(body, respData)
	if err != nil {
		log.Errorf("GetPractitionerGuildDailyDataFromDH Unmarshal failed url:%s err:%v", url, err)
		return mapUid2DailyData, err
	}

	if respData.Code != 200 {
		log.Errorf("GetPractitionerGuildDailyDataFromDH Unmarshal failed url:%s respData:%v", url, respData)
		return mapUid2DailyData, errors.New(respData.Msg)
	}

	for _, data := range respData.Data.Data {
		mapUid2DailyData[fmt.Sprintf("%d-%s", data.Uid, data.Date)][0] = data.LoginMin
		mapUid2DailyData[fmt.Sprintf("%d-%s", data.Uid, data.Date)][1] = data.MicMsgCnt
		mapUid2DailyData[fmt.Sprintf("%d-%s", data.Uid, data.Date)][2] = data.MicSendGiftCnt

	}

	log.Debugf("GetPractitionerGuildDailyDataFromDH end GetPractitionerMonthlyDataFromDH begin guildId:%d uidList:%v startTm:%v endTm:%v map:%v"+
		" respData:%v", guildId, uidList, startTm, endTm, mapUid2DailyData, respData)
	return mapUid2DailyData, nil
}

// 从数仓获取房间维度的活跃天数数据
func GetChannelMonthlyDataFromDH(cid uint32, uidList []uint32, startTm, endTm time.Time) (map[uint32]uint32, map[uint32]uint32, error) {
	client := http.Client{
		Timeout: 5 * time.Second,
	}

	mapUid2FiveHourActiveCnt := make(map[uint32]uint32, 0)
	mapUid2SevenHourActiveCnt := make(map[uint32]uint32, 0)

	if len(uidList) == 0 {
		log.Debugf("GetChannelMonthlyDataFromDH is empty uidList:%v startTm:%v endTm:%v",
			uidList, startTm, endTm)
		return mapUid2FiveHourActiveCnt, mapUid2SevenHourActiveCnt, nil
	}

	log.Debugf("GetChannelMonthlyDataFromDH begin cid:%d uidList:%v startTm:%v endTm:%v",
		cid, uidList, startTm, endTm)

	strUidList := make([]string, 0)
	for _, uid := range uidList {
		strUidList = append(strUidList, fmt.Sprintf("%d", uid))
	}

	strUid := strings.Join(strUidList, ",")
	startMonth := fmt.Sprintf("%04d%02d", startTm.Year(), startTm.Month())
	endMonth := fmt.Sprintf("%04d%02d", endTm.Year(), endTm.Month())
	strCid := fmt.Sprintf("%d", cid)

	url := fmt.Sprintf("%s/queryPractitionerRoomMonthDayInfo?start_month=%s&end_month=%s&uid=%s&apiToken=%s&room_id=%s&pageSize=1000",
		GetUrlInfoByApiScene(DataHouseApiSceneOne).Url, startMonth, endMonth, strUid, GetUrlInfoByApiScene(DataHouseApiSceneOne).Token, strCid)
	resp, err := client.Get(url)
	if err != nil {
		log.Errorf("GetChannelMonthlyDataFromDH GET fail url:%s err:%v", url, err)
		return mapUid2FiveHourActiveCnt, mapUid2SevenHourActiveCnt, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		log.Errorf("GetChannelMonthlyDataFromDH GET response not ok:%s url:%s", resp.Status, url)
		return mapUid2FiveHourActiveCnt, mapUid2SevenHourActiveCnt, errors.New(resp.Status)
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("GetChannelMonthlyDataFromDH ioutil.ReadAll failed url:%s err:%v", url, err)
		return mapUid2FiveHourActiveCnt, mapUid2SevenHourActiveCnt, err
	}

	respData := &struct {
		Code uint32 `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			Data []struct {
				Uid             uint32 `json:"uid"`
				GuildId         uint32 `json:"guild_id"`
				DataMonth       string `json:"data_month"`
				LoginDaysFiveH  uint32 `json:"login_days_5h"`
				LoginDaysSevenH uint32 `json:"login_days_7h"`
			} `json:"data"`
		} `json:"data"`
	}{}

	err = json.Unmarshal(body, respData)
	if err != nil {
		log.Errorf("GetChannelMonthlyDataFromDH Unmarshal failed url:%s err:%v", url, err)
		return mapUid2FiveHourActiveCnt, mapUid2SevenHourActiveCnt, err
	}

	if respData.Code != 200 {
		log.Errorf("GetChannelMonthlyDataFromDH Unmarshal failed url:%s respData:%v", url, respData)
		return mapUid2FiveHourActiveCnt, mapUid2SevenHourActiveCnt, errors.New(respData.Msg)
	}

	for _, data := range respData.Data.Data {
		mapUid2FiveHourActiveCnt[data.Uid] = data.LoginDaysFiveH
		mapUid2SevenHourActiveCnt[data.Uid] = data.LoginDaysSevenH
	}

	log.Debugf("GetChannelMonthlyDataFromDH end GetPractitionerMonthlyDataFromDH beginuidList:%v startTm:%v endTm:%v "+
		" respData:%v map1:%v map2:%v", uidList, startTm, endTm, respData, mapUid2FiveHourActiveCnt, mapUid2SevenHourActiveCnt)
	return mapUid2FiveHourActiveCnt, mapUid2SevenHourActiveCnt, nil
}

// QueryDeviceLastLoginInfoFromDH 从数仓库获取设备登陆用户的信息
func QueryDeviceLastLoginInfoFromDH(ctx context.Context, uid uint32, deviceId string, env string) ([]*DeviceLastLoginInfo, error) {
	result := make([]*DeviceLastLoginInfo, 0)

	// 判断参数
	if (uid == 0 && deviceId == "") || env == "" {
		log.ErrorWithCtx(ctx, "QueryDeviceLastLoginInfoFromHD with null req param, uid:[%+v], deviceId:[%+v], env:[%+v]", uid, deviceId, env)
		return result, nil
	}

	client := http.Client{
		Timeout: 10 * time.Second,
	}

	// 处理参数
	var reqUid, reqDeviceId string
	reqUid = "-1"
	reqDeviceId = "-1"
	if uid != 0 {
		reqUid = fmt.Sprintf("%+v", uid)
	}
	if deviceId != "" {
		reqDeviceId = deviceId
	}

	reqUrlInfo := MapEnv2UserLoginHistoryInfoConf[env]
	if reqUrlInfo.Url == "" {
		log.ErrorWithCtx(ctx, "QueryDeviceLastLoginInfoFromHD GET url failed, env:[%+v]", env)
		return result, errors.New("获取环境参数错误！")
	}
	url := fmt.Sprintf("%+v?uid=%+v&deviceId=%+v&apiToken=%+v&pageSize=1000", reqUrlInfo.Url, reqUid, reqDeviceId, reqUrlInfo.Token)
	log.InfoWithCtx(ctx, "QueryDeviceLastLoginInfoFromHD reqUrl:[%+v]", url)
	resp, err := client.Get(url)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryDeviceLastLoginInfoFromHD GET fail url:%s err:%v", url, err)
		return result, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		log.ErrorWithCtx(ctx, "QueryDeviceLastLoginInfoFromHD GET response not ok:%s url:%s", resp.Status, url)
		return result, errors.New(resp.Status)
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryDeviceLastLoginInfoFromHD ioutil.ReadAll failed url:%s err:%v", url, err)
		return result, err
	}

	respData := &struct {
		Code    int  `json:"code"`
		Success bool `json:"success"`
		Data    struct {
			Pagination struct {
				Page      int `json:"page"`
				PageSize  int `json:"pageSize"`
				Total     int `json:"total"`
				TotalPage int `json:"totalPage"`
			} `json:"pagination"`
			Data []struct {
				DeviceId      string `json:"deviceid"`
				UID           string `json:"uid"`
				IP            string `json:"ip"`
				Platform      string `json:"platform"`
				Province      string `json:"province"`
				City          string `json:"city"`
				Ver           string `json:"ver"`
				LastLoginDate string `json:"last_login_date"`
			} `json:"data"`
		} `json:"data"`
		Msg string `json:"msg"`
	}{}

	err = json.Unmarshal(body, respData)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryDeviceLastLoginInfoFromHD Unmarshal failed url:%s err:%v", url, err)
		return result, err
	}

	if respData.Code != 200 {
		log.ErrorWithCtx(ctx, "QueryDeviceLastLoginInfoFromHD return failed url:%s respData:%v", url, respData)
		return result, errors.New(respData.Msg)
	}

	for _, data := range respData.Data.Data {
		resultUid, err := strconv.ParseUint(data.UID, 10, 64)
		if err != nil {
			log.ErrorWithCtx(ctx, "QueryDeviceLastLoginInfoFromHD ParseUint UID failed url:%s respData:%v", url, respData)
			return result, err
		}
		lastLoginDateTime, err := time.ParseInLocation("2006-01-02 15:04:05.0", data.LastLoginDate, time.Local)
		if err != nil {
			log.ErrorWithCtx(ctx, "QueryDeviceLastLoginInfoFromHD Parse LastLoginDate failed url:%s respData:%v", url, respData)
			return result, err
		}
		info := &DeviceLastLoginInfo{
			DeviceId:      data.DeviceId,
			Uid:           uint32(resultUid),
			IP:            data.IP,
			Platform:      data.Platform,
			Province:      data.Province,
			City:          data.City,
			Version:       data.Ver,
			LastLoginDate: lastLoginDateTime,
		}
		result = append(result, info)
	}

	return result, nil
}

// QueryBanLogFromDH 从数仓库获取设备被封禁的操作记录
func QueryBanLogFromDH(ctx context.Context, deviceId string, env string) ([]*BanLog, error) {
	result := make([]*BanLog, 0)

	// 判断参数
	if deviceId == "" || env == "" {
		log.ErrorWithCtx(ctx, "QueryBanLogFromHD with null req param, deviceId:[%+v], env:[%+v]", deviceId, env)
		return result, nil
	}

	client := http.Client{
		Timeout: 10 * time.Second,
	}

	reqUrlInfo := MapEnv2UserBanHistoryLogConf[env]
	if reqUrlInfo.Url == "" {
		log.ErrorWithCtx(ctx, "QueryBanLogFromHD GET url failed, env:[%+v]", env)
		return result, errors.New("获取环境参数错误！")
	}
	url := fmt.Sprintf("%+v?deviceId=%+v&apiToken=%+v&pageSize=1000", reqUrlInfo.Url, deviceId, reqUrlInfo.Token)
	log.InfoWithCtx(ctx, "QueryBanLogFromHD reqUrl:[%+v]", url)
	resp, err := client.Get(url)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryBanLogFromHD GET fail url:%s err:%v", url, err)
		return result, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		log.ErrorWithCtx(ctx, "QueryBanLogFromHD GET response not ok:%s url:%s", resp.Status, url)
		return result, errors.New(resp.Status)
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryBanLogFromHD ioutil.ReadAll failed url:%s err:%v", url, err)
		return result, err
	}

	respData := &struct {
		Code    int  `json:"code"`
		Success bool `json:"success"`
		Data    struct {
			Pagination struct {
				Page      int `json:"page"`
				PageSize  int `json:"pageSize"`
				Total     int `json:"total"`
				TotalPage int `json:"totalPage"`
			} `json:"pagination"`
			Data []struct {
				DeviceId   string `json:"deviceid"`
				OpName     string `json:"op_name"`
				At         string `json:"at"`
				BanDays    string `json:"ban_days"`
				RecoveryAt string `json:"recovery_at"`
				BanUID     string `json:"ban_uid"`
				UnbanUID   string `json:"unban_uid"`
			} `json:"data"`
		} `json:"data"`
		Msg string `json:"msg"`
	}{}

	err = json.Unmarshal(body, respData)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryBanLogFromHD Unmarshal failed url:%s err:%v", url, err)
		return result, err
	}

	if respData.Code != 200 {
		log.ErrorWithCtx(ctx, "QueryBanLogFromHD return failed url:%s respData:%v", url, respData)
		return result, errors.New(respData.Msg)
	}

	for _, data := range respData.Data.Data {
		var banUid, unbanUid uint64
		var at, recoveryAt time.Time
		opType := banOpName2OpStatus[data.OpName]
		if data.BanUID != "" {
			banUid, err = strconv.ParseUint(data.BanUID, 10, 64)
			if err != nil {
				log.ErrorWithCtx(ctx, "QueryBanLogFromHD ParseUint BanUID failed url:%s respData:%v", url, respData)
				return result, err
			}
		}
		if data.UnbanUID != "" {
			unbanUid, err = strconv.ParseUint(data.UnbanUID, 10, 64)
			if err != nil {
				log.ErrorWithCtx(ctx, "QueryBanLogFromHD ParseUint UnbanUID failed url:%s respData:%v", url, respData)
				return result, err
			}
		}
		at, err = time.ParseInLocation("2006-01-02 15:04:05.0", data.At, time.Local)
		if err != nil {
			log.ErrorWithCtx(ctx, "QueryBanLogFromHD Parse At failed url:%s respData:%v", url, respData)
			return result, err
		}
		if opType == OptionBan {
			recoveryAt, err = time.ParseInLocation("2006-01-02 15:04:05.0", data.RecoveryAt, time.Local)
			if err != nil {
				log.ErrorWithCtx(ctx, "QueryBanLogFromHD Parse RecoveryAt failed url:%s respData:%v", url, respData)
				return result, err
			}
		}
		info := &BanLog{
			DeviceId:   data.DeviceId,
			OpType:     opType,
			At:         at,
			BanDays:    data.BanDays,
			RecoveryAt: recoveryAt,
			BanUID:     uint32(banUid),
			UnbanUID:   uint32(unbanUid),
		}
		result = append(result, info)
	}

	return result, nil
}

// ==========================================================
// 以下接口使用ctx设置超时

// 成员、主播日/周数据-——按公会、主播
func QueryGuildMemberDailyInfo(ctx context.Context, uidList []uint32, guildId uint32, startTm, endTm time.Time) (list []*GuildMemberInfo, err error) {
	if len(uidList) == 0 {
		log.DebugWithCtx(ctx, "QueryGuildMemberDailyInfo uidList empty")
		return
	}

	strUidList := make([]string, 0)
	for _, uid := range uidList {
		strUidList = append(strUidList, fmt.Sprintf("%d", uid))
	}

	strUid := strings.Join(strUidList, ",")

	// todo
	url := fmt.Sprintf("%s/queryGuildMemberIndex?apiToken=%s&start_date=%s&end_date=%s&user_id=%s&guild_id=%d&pageSize=1000",
		GetUrlInfoByApiScene(DataHouseApiSceneTwo).Url, GetUrlInfoByApiScene(DataHouseApiSceneTwo).Token, startTm.Format("20060102"), endTm.Format("20060102"), strUid, guildId)

	body, err := httpGet(ctx, url)
	if err != nil {
		return
	}

	respData := &struct {
		Code    int  `json:"code"`
		Success bool `json:"success"`
		Data    struct {
			List []*GuildMemberInfo `json:"data"`
		} `json:"data"`
		Msg string `json:"msg"`
	}{}

	err = json.Unmarshal(body, respData)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryGuildMemberDailyInfo failed body:%v err:%v", body, err)
		return
	}
	if respData.Code != 200 {
		err = fmt.Errorf("url: %q, respCode is %d, msg %s", url, respData.Code, respData.Msg)
		return
	}

	list = respData.Data.List
	log.DebugWithCtx(ctx, "QueryGuildMemberDailyInfo url %q resp %+v", url, list)
	return
}

// 成员、主播日/周数据-——按签约主播，签约成员汇总
func QueryMemberDailyInfo(ctx context.Context, uid uint32, startTm, endTm time.Time) (infoList []*MemberDailyInfo, err error) {
	infoList = make([]*MemberDailyInfo, 0)

	url := fmt.Sprintf("%s/queryMemberIndex?apiToken=%s&start_date=%s&end_date=%s&user_id=%d&pageSize=1000",
		GetUrlInfoByApiScene(DataHouseApiSceneTwo).Url, GetUrlInfoByApiScene(DataHouseApiSceneTwo).Token, startTm.Format("20060102"), endTm.Format("20060102"), uid)

	body, err := httpGet(ctx, url)
	if err != nil {
		return
	}

	respData := &struct {
		Code    int  `json:"code"`
		Success bool `json:"success"`
		Data    struct {
			List []*MemberDailyInfo `json:"data"`
		} `json:"data"`
		Msg string `json:"msg"`
	}{}

	err = json.Unmarshal(body, respData)
	if err != nil {
		return
	}
	if respData.Code != 200 {
		err = fmt.Errorf("url: %q, respCode is %d, msg %s", url, respData.Code, respData.Msg)
		return
	}

	infoList = respData.Data.List

	log.DebugWithCtx(ctx, "QueryMemberDailyInfo url %v resp %v", url, respData)
	return
}

// 成员、主播- 月数据
func QueryGuildMemberMonthInfo(ctx context.Context, uidList []uint32, guildId uint32, startTm, endTm time.Time) (list []*GuildMemberInfo, err error) {
	if len(uidList) == 0 {
		log.DebugWithCtx(ctx, "QueryGuildMemberMonthInfo uidList empty")
		return
	}

	strUidList := make([]string, 0)
	for _, uid := range uidList {
		strUidList = append(strUidList, fmt.Sprintf("%d", uid))
	}

	strUid := strings.Join(strUidList, ",")

	c := Business2Config["QueryMemberMonthIndex"]
	if c == nil {
		log.ErrorWithCtx(ctx, "QueryGuildMemberMonthInfo got empty url err, key: QueryMemberMonthIndex")
		return
	}

	url := fmt.Sprintf("%squeryMemberMonthIndex?apiToken=%s&start_date=%s&end_date=%s&user_id=%s&guild_id=%d&pageSize=1000",
		c.Url, c.Token, startTm.Format("200601"), endTm.Format("200601"), strUid, guildId)

	log.InfoWithCtx(ctx, "QueryGuildMemberMonthInfo url %q", url)

	body, err := httpGet(ctx, url)
	if err != nil {
		return
	}

	respData := &struct {
		Code    int  `json:"code"`
		Success bool `json:"success"`
		Data    struct {
			List []*GuildMemberInfo `json:"data"`
		} `json:"data"`
		Msg string `json:"msg"`
	}{}

	err = json.Unmarshal(body, respData)
	if err != nil {
		return
	}
	if respData.Code != 200 {
		err = fmt.Errorf("url: %q, respCode is %d, msg %s", url, respData.Code, respData.Msg)
		return
	}

	list = respData.Data.List
	log.InfoWithCtx(ctx, "QueryGuildMemberMonthInfo url %q resp %+v", url, list)
	return
}

// 成员、主播关系链数据明细
func QueryGuildMemberChainInfo(ctx context.Context, uidList []uint32, guildId uint32, startTm, endTm time.Time) (list []*GuildMemberChainInfo, err error) {

	strUidList := make([]string, 0)
	for _, uid := range uidList {
		strUidList = append(strUidList, fmt.Sprintf("%d", uid))
	}

	strUid := strings.Join(strUidList, ",")

	url := fmt.Sprintf("%s/queryGuildMemberChainInfo?apiToken=%s&start_date=%s&end_date=%s&user_id=%s&guild_id=%d&pageSize=1000",
		GetUrlInfoByApiScene(DataHouseApiSceneTwo).Url, GetUrlInfoByApiScene(DataHouseApiSceneTwo).Token, startTm.Format("20060102"), endTm.Format("20060102"), strUid, guildId)

	body, err := httpGet(ctx, url)
	if err != nil {
		return
	}

	respData := &struct {
		Code    int  `json:"code"`
		Success bool `json:"success"`
		Data    struct {
			List []*GuildMemberChainInfo `json:"data"`
		} `json:"data"`
		Msg string `json:"msg"`
	}{}

	err = json.Unmarshal(body, respData)
	if err != nil {
		return
	}
	if respData.Code != 200 {
		err = fmt.Errorf("url: %q, respCode is %d, msg %s", url, respData.Code, respData.Msg)
		return
	}

	list = respData.Data.List
	log.DebugWithCtx(ctx, "QueryGuildMemberDailyInfo url %q resp %+v", url, list)
	return
}

// 成员、主播关系链数据汇总 - 按签约主播，签约成员维度汇总
func QueryMemberChainDailyInfo(ctx context.Context, uid uint32, startTm, endTm time.Time) (infoList []*MemberChainDailyInfo, err error) {

	infoList = make([]*MemberChainDailyInfo, 0)

	url := fmt.Sprintf("%s/queryMemberChainInfo?apiToken=%s&start_date=%s&end_date=%s&user_id=%d&pageSize=1000",
		GetUrlInfoByApiScene(DataHouseApiSceneTwo).Url, GetUrlInfoByApiScene(DataHouseApiSceneTwo).Token, startTm.Format("20060102"), endTm.Format("20060102"), uid)

	body, err := httpGet(ctx, url)
	if err != nil {
		return
	}

	respData := &struct {
		Code    int  `json:"code"`
		Success bool `json:"success"`
		Data    struct {
			List []*MemberChainDailyInfo `json:"data"`
		} `json:"data"`
		Msg string `json:"msg"`
	}{}

	err = json.Unmarshal(body, respData)
	if err != nil {
		return
	}
	if respData.Code != 200 {
		err = fmt.Errorf("url: %q, respCode is %d, msg %s", url, respData.Code, respData.Msg)
		return
	}

	infoList = respData.Data.List

	log.DebugWithCtx(ctx, "QueryMemberChainInfo url %q resp %+v", url, respData)
	return
}

// 成员、主播关系链月维度数据汇总 - 按公会+签约主播，签约成员维度汇总
func QueryMemberChainMonthInfo(ctx context.Context, guildId uint32, uidList []uint32, startTm, endTm time.Time) (infoList []*GuildMemberChainMonthInfo, err error) {
	if len(uidList) == 0 {
		log.DebugWithCtx(ctx, "QueryMemberChainMonthInfo uidList empty")
		return
	}

	strUidList := make([]string, 0)
	for _, uid := range uidList {
		strUidList = append(strUidList, fmt.Sprintf("%d", uid))
	}

	strUid := strings.Join(strUidList, ",")

	infoList = make([]*GuildMemberChainMonthInfo, 0)

	url := fmt.Sprintf("%s/queryMemberMonthChainInfo?apiToken=%s&start_date=%s&end_date=%s&user_id=%s&guild_id=%d&pageSize=1000",
		GetUrlInfoByApiScene(DataHouseApiSceneTwo).Url, GetUrlInfoByApiScene(DataHouseApiSceneTwo).Token,
		startTm.Format("200601"), endTm.Format("200601"), strUid, guildId)

	body, err := httpGet(ctx, url)
	if err != nil {
		return
	}

	respData := &struct {
		Code    int  `json:"code"`
		Success bool `json:"success"`
		Data    struct {
			List []*GuildMemberChainMonthInfo `json:"data"`
		} `json:"data"`
		Msg string `json:"msg"`
	}{}

	err = json.Unmarshal(body, respData)
	if err != nil {
		return
	}
	if respData.Code != 200 {
		err = fmt.Errorf("QueryMemberChainMonthInfo url: %q, respCode is %d, msg %s", url, respData.Code, respData.Msg)
		return
	}

	infoList = respData.Data.List

	log.DebugWithCtx(ctx, "QueryMemberChainMonthInfo url %q resp %+v", url, respData)
	return
}

// 公会后台违规查询 queryMemberSanctionRecord?
func QueryGuildViolationRecord(ctx context.Context, guildId uint32, uidList []uint32, startTm, endTm time.Time, page, pageNum uint32) (
	total uint32, List []*GuildViolationRecord, err error) {

	user_flag := 0
	strUid := "0"
	if len(uidList) == 0 {
		user_flag = 1
	} else {
		strUidList := make([]string, 0)
		for _, uid := range uidList {
			strUidList = append(strUidList, fmt.Sprintf("%d", uid))
		}
		strUid = strings.Join(strUidList, ",")
	}

	urlconfig := Business2Config["Violation"]
	if urlconfig == nil {
		log.ErrorWithCtx(ctx, "QueryGuildViolationRecord got empty url")
		return
	}

	url := fmt.Sprintf("%squeryMemberSanctionRecord?apiToken=%s&start_date=%s&end_date=%s&user_id=%s&user_flag=%d&guild_id=%d&page=%d&pageSize=%d",
		urlconfig.Url, urlconfig.Token, startTm.Format("20060102"), endTm.Format("20060102"), strUid, user_flag, guildId, page, pageNum)

	body, err := httpGet(ctx, url)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryGuildViolationRecord fail %+v", err)
		return
	}

	respData := &struct {
		Code    int  `json:"code"`
		Success bool `json:"success"`
		Data    struct {
			Pagination Pagination              `json:"pagination"`
			List       []*GuildViolationRecord `json:"data"`
		} `json:"data"`
		Msg string `json:"msg"`
	}{}

	err = json.Unmarshal(body, respData)
	if err != nil {
		err = fmt.Errorf("QueryGuildViolationRecord json.Unmarshal fail %v. url=%q, body=%q", err.Error(), url, string(body))
		return
	}
	if respData.Code != 200 {
		err = fmt.Errorf("QueryGuildViolationRecord url=%q, respCode=%d, msg=%q", url, respData.Code, respData.Msg)
		return
	}

	total = respData.Data.Pagination.Total
	List = respData.Data.List
	log.DebugWithCtx(ctx, "QueryGuildViolationRecord url=%q, total=%d, data=%+v", url, total, List)
	return
}

// 公会房间月数据
func QueryGuildChannelMonthInfo(ctx context.Context, cidList []uint32, guildId uint32, startTm, endTm time.Time) (list []*GuildChannelInfo, err error) {
	if len(cidList) == 0 {
		log.DebugWithCtx(ctx, "QueryGuildChannelMonthInfo uidList empty")
		return
	}

	strCidList := make([]string, 0)
	for _, uid := range cidList {
		strCidList = append(strCidList, fmt.Sprintf("%d", uid))
	}

	strCid := strings.Join(strCidList, ",")

	// todo
	url := fmt.Sprintf("%s/queryGuildRoomMonthIndex?apiToken=%s&start_date=%s&end_date=%s&room_id=%s&guild_id=%d&pageSize=1000",
		GetUrlInfoByApiScene(DataHouseApiSceneThree).Url, GetUrlInfoByApiScene(DataHouseApiSceneThree).Token, startTm.Format("200601"), endTm.Format("200601"), strCid, guildId)

	body, err := httpGet(ctx, url)
	if err != nil {
		return
	}

	respData := &struct {
		Code    int  `json:"code"`
		Success bool `json:"success"`
		Data    struct {
			List []*GuildChannelInfo `json:"data"`
		} `json:"data"`
		Msg string `json:"msg"`
	}{}

	err = json.Unmarshal(body, respData)
	if err != nil {
		return
	}
	if respData.Code != 200 {
		err = fmt.Errorf("url: %q, respCode is %d, msg %s", url, respData.Code, respData.Msg)
		return
	}

	list = respData.Data.List
	log.DebugWithCtx(ctx, "QueryGuildChannelMonthInfo url %q resp %+v", url, list)
	return
}

// 公会月数据
func QueryGuildMonthInfo(ctx context.Context, guildId uint32, startTm, endTm time.Time) (list []*GuildMonthInfo, err error) {
	if guildId == 0 {
		log.InfoWithCtx(ctx, "QueryGuildMonthInfo guildId invalid")
		return
	}

	url := fmt.Sprintf("%s/queryGuildMonthIndex?apiToken=%s&start_date=%s&end_date=%s&guild_id=%d&pageSize=1000",
		GetUrlInfoByApiScene(DataHouseApiSceneThree).Url, GetUrlInfoByApiScene(DataHouseApiSceneThree).Token, startTm.Format("200601"), endTm.Format("200601"), guildId)

	body, err := httpGet(ctx, url)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryGuildMonthInfo httpGet failed guildId:%d startTm:%v endTm:%v err:%v", guildId, startTm, endTm, err)
		return
	}

	respData := &struct {
		Code    int  `json:"code"`
		Success bool `json:"success"`
		Data    struct {
			List []*GuildMonthInfo `json:"data"`
		} `json:"data"`
		Msg string `json:"msg"`
	}{}

	err = json.Unmarshal(body, respData)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryGuildMonthInfo Unmarshal failed guildId:%d startTm:%v endTm:%v err:%v", guildId, startTm, endTm, err)
		return
	}
	if respData.Code != 200 {
		err = fmt.Errorf("url: %q, respCode is %d, msg %s", url, respData.Code, respData.Msg)
		log.ErrorWithCtx(ctx, "QueryGuildMonthInfo respData failed guildId:%d startTm:%v endTm:%v respData:%v", guildId, startTm, endTm, respData)
		return
	}

	list = respData.Data.List
	for _, l := range list {
		log.InfoWithCtx(ctx, "QueryGuildMonthInfo guildId:%d l:%v", guildId, l)
	}

	log.InfoWithCtx(ctx, "QueryGuildMonthInfo guildId:%d respData:%+v url:%q ", guildId, respData, url)
	return
}

func QueryMultiMemberMicStatData(ctx context.Context, req *MultiMemberMicStatReq) (list []*MultiMemberMicStatData, total uint32, err error) {
	list = make([]*MultiMemberMicStatData, 0)

	pageSize := req.Limit
	if pageSize == 0 {
		pageSize = 10
	}
	page := req.Offset/pageSize + 1
	channelIdList := make([]string, 0)
	for _, channelId := range req.ChannelIdList {
		channelIdList = append(channelIdList, fmt.Sprintf("%d", channelId))
	}
	channelIdListStr := strings.Join(channelIdList, ",")
	userIdList := make([]string, 0)
	for _, userId := range req.UidList {
		userIdList = append(userIdList, fmt.Sprintf("%d", userId))
	}
	userIdListStr := "99" // 99表示所有
	if len(userIdList) > 0 {
		userIdListStr = strings.Join(userIdList, ",")
	}
	hourListStr := "99" // 99表示所有
	if len(req.HourList) > 0 {
		hourListStr = strings.Join(req.HourList, ",")
	}

	c := Business2Config["MultiMemberMicStatData"]
	if c == nil {
		log.ErrorWithCtx(ctx, "QueryMultiMemberMicStatData got empty url err, key: MultiMemberMicStatData")
		return
	}

	url := fmt.Sprintf("%squeryMemberMicStatsV2?apiToken=%s&pageSize=%d&page=%d&start_date=%s&end_date=%s&data_hour=%s&room_id=%s&user_id=%s&time_dim=%s",
		c.Url, c.Token, pageSize, page, req.StartTime, req.EndTime, hourListStr, channelIdListStr, userIdListStr, req.TimeDim)

	log.InfoWithCtx(ctx, "QueryMultiMemberMicStatData start url:%s", url)

	//url = "http://*************:10086/107/queryMemberMicStats?apiToken=4798370757f1cce3a7fe5f1c14bec351&pageSize=10&page=1&start_date=2024-11-24&end_date=2024-11-24&data_hour=all&room_id=2255775&user_id=all"

	body, err := httpGet(ctx, url)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryMultiMemberMicStatData httpGet err:%v, url:%s, req:%+v", err, url, req)
		return
	}
	respData := &struct {
		Code    int  `json:"code"`
		Success bool `json:"success"`
		Data    struct {
			Pagination Pagination                `json:"pagination"`
			List       []*MultiMemberMicStatData `json:"data"`
		} `json:"data"`
		Msg string `json:"msg"`
	}{}
	err = json.Unmarshal(body, respData)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryGuildMonthInfo Unmarshal failed url:%s, req:%+v, err:%v", url, req, err)
		return
	}
	if respData.Code != 200 {
		err = fmt.Errorf("url: %q, respCode is %d, msg %s", url, respData.Code, respData.Msg)
		log.ErrorWithCtx(ctx, "QueryMultiMemberMicStatData respData failed url:%s, req:%+v, respData:%+v", url, req, respData)
		return
	}

	list = respData.Data.List
	total = respData.Data.Pagination.Total
	log.InfoWithCtx(ctx, "QueryMultiMemberMicStatData end url:%s, req:%+v, respData:%+v", url, req, respData)
	return
}

// QueryMultiGuildMemberDayInfo 从业者房间维度日数据
func QueryMultiGuildMemberDayInfo(ctx context.Context, req *MultiGuildMemberInfoReq) (list []*MultiGuildMemberDayInfo, total uint32, err error) {
	list = make([]*MultiGuildMemberDayInfo, 0)
	pageSize := req.Limit
	if pageSize == 0 {
		pageSize = 10
	}
	page := req.Offset/pageSize + 1
	if len(req.ChannelIdList) == 0 {
		log.ErrorWithCtx(ctx, "QueryMultiGuildMemberDayInfo channelIdList empty")
		return
	}
	channelIdList := make([]string, 0)
	for _, channelId := range req.ChannelIdList {
		channelIdList = append(channelIdList, fmt.Sprintf("%d", channelId))
	}
	channelIdListStr := strings.Join(channelIdList, ",")
	userIdList := make([]string, 0)
	for _, userId := range req.UidList {
		userIdList = append(userIdList, fmt.Sprintf("%d", userId))
	}
	userIdListStr := strings.Join(userIdList, ",")

	c := Business2Config["MultiGuildMemberDayInfo"]
	if c == nil {
		log.ErrorWithCtx(ctx, "QueryMultiGuildMemberDayInfo got empty url err, key: MultiGuildMemberDayInfo")
		return
	}

	st := req.StartDate.Format("20060102")
	et := req.EndDate.Format("20060102")

	url := fmt.Sprintf("%squeryPractitionerRoomDayInfo?apiToken=%s&pageSize=%d&page=%d&start_date=%s&end_date=%s&room_id=%s&user_id=%s",
		c.Url, c.Token, pageSize, page, st, et, channelIdListStr, userIdListStr)

	log.InfoWithCtx(ctx, "QueryMultiGuildMemberDayInfo start url:%s", url)

	//url = "http://*************:10086/107/queryPractitionerRoomDayInfo?start_date=20241209&end_date=20241209&user_id=100001&apiToken=4798370757f1cce3a7fe5f1c14bec351&page=1&pageSize=50&room_id=300001"

	body, err := httpGet(ctx, url)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryMultiGuildMemberDayInfo httpGet err:%v, url:%s, req:%+v", err, url, req)
		return
	}
	respData := &struct {
		Code    int  `json:"code"`
		Success bool `json:"success"`
		Data    struct {
			Pagination Pagination                 `json:"pagination"`
			List       []*MultiGuildMemberDayInfo `json:"data"`
		} `json:"data"`
		Msg string `json:"msg"`
	}{}
	err = json.Unmarshal(body, respData)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryMultiGuildMemberDayInfo Unmarshal failed url:%s, req:%+v, err:%v", url, req, err)
		return
	}
	if respData.Code != 200 {
		err = fmt.Errorf("url: %q, respCode is %d, msg %s", url, respData.Code, respData.Msg)
		log.ErrorWithCtx(ctx, "QueryMultiGuildMemberDayInfo respData failed url:%s, req:%+v, respData:%+v", url, req, respData)
		return
	}
	list = respData.Data.List
	total = respData.Data.Pagination.Total
	log.InfoWithCtx(ctx, "QueryMultiGuildMemberDayInfo end url:%s, req:%+v, respData:%+v", url, req, respData)
	return
}

// QueryMultiGuildMemberMonthInfo 从业者房间维度月累计数据
func QueryMultiGuildMemberMonthInfo(ctx context.Context, req *MultiGuildMemberInfoReq) (list []*MultiGuildMemberMonthInfo, total uint32, err error) {
	list = make([]*MultiGuildMemberMonthInfo, 0)
	pageSize := req.Limit
	if pageSize == 0 {
		pageSize = 10
	}
	page := req.Offset/pageSize + 1
	if len(req.ChannelIdList) == 0 {
		log.ErrorWithCtx(ctx, "QueryMultiGuildMemberMonthInfo channelIdList empty")
		return
	}
	channelIdList := make([]string, 0)
	for _, channelId := range req.ChannelIdList {
		channelIdList = append(channelIdList, fmt.Sprintf("%d", channelId))
	}
	channelIdListStr := strings.Join(channelIdList, ",")
	userIdList := make([]string, 0)
	for _, userId := range req.UidList {
		userIdList = append(userIdList, fmt.Sprintf("%d", userId))
	}
	userIdListStr := strings.Join(userIdList, ",")

	c := Business2Config["MultiGuildMemberMonthInfo"]
	if c == nil {
		log.ErrorWithCtx(ctx, "QueryMultiGuildMemberMonthInfo got empty url err, key: MultiGuildMemberMonthInfo")
		return
	}

	st := req.StartDate.Format("200601")
	et := req.EndDate.Format("200601")

	url := fmt.Sprintf("%squeryPractitionerRoomMonthInfo?apiToken=%s&pageSize=%d&page=%d&start_month=%s&end_month=%s&room_id=%s&uid=%s",
		c.Url, c.Token, pageSize, page, st, et, channelIdListStr, userIdListStr)

	log.InfoWithCtx(ctx, "QueryMultiGuildMemberMonthInfo start url:%s", url)

	//url = "http://*************:10086/107/queryPractitionerRoomMonthInfo?start_month=202412&end_month=202412&uid=100001&apiToken=4798370757f1cce3a7fe5f1c14bec351&page=1&pageSize=50&room_id=300001"

	body, err := httpGet(ctx, url)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryMultiGuildMemberMonthInfo httpGet err:%v, url:%s, req:%+v", err, url, req)
		return
	}
	respData := &struct {
		Code    int  `json:"code"`
		Success bool `json:"success"`
		Data    struct {
			Pagination Pagination                   `json:"pagination"`
			List       []*MultiGuildMemberMonthInfo `json:"data"`
		} `json:"data"`
		Msg string `json:"msg"`
	}{}
	err = json.Unmarshal(body, respData)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryMultiGuildMemberMonthInfo Unmarshal failed url:%s, req:%+v, err:%v", url, req, err)
		return
	}
	if respData.Code != 200 {
		err = fmt.Errorf("url: %q, respCode is %d, msg %s", url, respData.Code, respData.Msg)
		log.ErrorWithCtx(ctx, "QueryMultiGuildMemberMonthInfo respData failed url:%s, req:%+v, respData:%+v", url, req, respData)
		return
	}
	list = respData.Data.List
	total = respData.Data.Pagination.Total
	log.InfoWithCtx(ctx, "QueryMultiGuildMemberMonthInfo end url:%s, req:%+v, len:%+v", url, req, len(respData.Data.List))
	return
}

// QueryFaceAuthFailedDailyFromDH 人脸认证失败日数据
func QueryFaceAuthFailedDailyFromDH(ctx context.Context, req *FaceAuthFailedDailyReq) (*FaceAuthFailedDailyResp, error) {

	// 校验参数
	if req.GuildIdList == nil || len(req.GuildIdList) == 0 {
		log.ErrorWithCtx(ctx, "QueryFaceAuthFailedDailyFromDH guildIdList empty")
		return nil, errors.New("guildIdList empty")
	}
	if req.StartDate.IsZero() || req.EndDate.IsZero() {
		log.ErrorWithCtx(ctx, "QueryFaceAuthFailedDailyFromDH StartDate or EndDate invalid")
		return nil, errors.New("StartDate or EndDate invalid")
	}

	// 转换参数
	var startDate, endDate, guildParam, uidParam, identityParam string
	startDate = req.StartDate.Format("2006-01-02")
	endDate = req.EndDate.Format("2006-01-02")
	firstParam := true
	for _, guildId := range req.GuildIdList {
		if guildId == 0 {
			continue
		}
		if firstParam {
			guildParam = fmt.Sprintf("%d", guildId)
			firstParam = false
			continue
		}
		guildParam += fmt.Sprintf("&guild_id=%d", guildId)
	}
	if len(req.UidList) > 0 {
		firstParam = true
		for _, uid := range req.UidList {
			if uid == 0 {
				continue
			}
			if firstParam {
				uidParam = fmt.Sprintf("%d", uid)
				firstParam = false
				continue
			}
			uidParam += fmt.Sprintf("&user_id=%d", uid)
		}
	}
	switch req.IdentityType {
	case 0: // 多人互动
		identityParam = "多人"
	case 1: // 听听
		identityParam = "听听"
	case 2: // 电竞
		identityParam = "电竞"
	}

	c := Business2Config["FaceAuthFailedDaily"]
	if c == nil {
		log.ErrorWithCtx(ctx, "QueryMultiGuildMemberDayInfo got empty url err, key: MultiGuildMemberDayInfo")
		return nil, errors.New("FaceAuthFailedDaily config empty")
	}

	url := fmt.Sprintf("%sguildBackgroundFaceAuthFaild?apiToken=%s&start_date=%s&end_date=%s&guild_id=%s&user_id=%s&room_id=%s&page=%d&pageSize=%d&identity_type=%s",
		c.Url, c.Token, startDate, endDate, guildParam, uidParam, "", req.Page, req.PageSize, urlutil.QueryEscape(identityParam))
	log.InfoWithCtx(ctx, "QueryFaceAuthFailedDailyFromDH start url:%s", url)
	// url = "http://*************:10086/107/guildBackgroundFaceAuthFaild?start_date=2025-01-14&end_date=2025-01-14&guild_id=&user_id=&room_id=&identity_type=&apiToken=&page=1&pageSize=1"

	body, err := httpGet(ctx, url)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryFaceAuthFailedDailyFromDH httpGet err:%v, url:%s, req:%+v", err, url, req)
		return nil, err
	}
	respData := &struct {
		Code    int                      `json:"code"`
		Success bool                     `json:"success"`
		Data    *FaceAuthFailedDailyResp `json:"data"`
		Msg     string                   `json:"msg"`
	}{}
	err = json.Unmarshal(body, respData)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryFaceAuthFailedDailyFromDH Unmarshal failed url:%s, req:%+v, err:%v", url, req, err)
		return nil, err
	}

	if respData.Code != 200 || !respData.Success {
		err = fmt.Errorf("url: %q, respCode is %d, msg %s", url, respData.Code, respData.Msg)
		log.ErrorWithCtx(ctx, "QueryFaceAuthFailedDailyFromDH respData failed url:%s, req:%+v, respData:%+v", url, req, respData)
		return nil, err
	}

	log.InfoWithCtx(ctx, "QueryFaceAuthFailedDailyFromDH end url:%s, req:%+v, respData:%+v", url, req, respData)
	return respData.Data, nil
}

// QueryFaceAuthFailedWeeklyFromDH 人脸认证失败日数据
func QueryFaceAuthFailedWeeklyFromDH(ctx context.Context, req *FaceAuthFailedWeeklyReq) (*FaceAuthFailedWeeklyResp, error) {

	// 校验参数
	if req.GuildIdList == nil || len(req.GuildIdList) == 0 {
		log.ErrorWithCtx(ctx, "QueryFaceAuthFailedWeeklyFromDH guildIdList empty")
		return nil, errors.New("guildIdList empty")
	}
	if req.StartDate.IsZero() || req.EndDate.IsZero() {
		log.ErrorWithCtx(ctx, "QueryFaceAuthFailedWeeklyFromDH StartDate or EndDate invalid")
		return nil, errors.New("StartDate or EndDate invalid")
	}

	// 转换参数
	var startDate, endDate, guildParam, uidParam, identityParam string
	startDate = req.StartDate.Format("2006-01-02")
	endDate = req.EndDate.Format("2006-01-02")
	firstParam := true
	for _, guildId := range req.GuildIdList {
		if guildId == 0 {
			continue
		}
		if firstParam {
			guildParam = fmt.Sprintf("%d", guildId)
			firstParam = false
			continue
		}
		guildParam += fmt.Sprintf("&guild_id=%d", guildId)
	}
	if len(req.UidList) > 0 {
		firstParam = true
		for _, uid := range req.UidList {
			if uid == 0 {
				continue
			}
			if firstParam {
				uidParam = fmt.Sprintf("%d", uid)
				firstParam = false
				continue
			}
			uidParam += fmt.Sprintf("&user_id=%d", uid)
		}
	}
	switch req.IdentityType {
	case 0: // 多人互动
		identityParam = "多人"
	case 1: // 听听
		identityParam = "听听"
	case 2: // 电竞
		identityParam = "电竞"
	}

	c := Business2Config["FaceAuthFailedWeekly"]
	if c == nil {
		log.ErrorWithCtx(ctx, "QueryFaceAuthFailedWeeklyFromDH got empty url err, key: MultiGuildMemberDayInfo")
		return nil, errors.New("FaceAuthFailedWeekly config empty")
	}

	url := fmt.Sprintf("%sguildBackgroundFaceAuthFailWeek?apiToken=%s&start_date=%s&end_date=%s&guild_id=%s&user_id=%s&room_id=%s&page=%d&pageSize=%d&identity_type=%s",
		c.Url, c.Token, startDate, endDate, guildParam, uidParam, "", req.Page, req.PageSize, urlutil.QueryEscape(identityParam))
	log.InfoWithCtx(ctx, "QueryFaceAuthFailedWeeklyFromDH start url:%s", url)
	// url = "http://*************:10086/107/guildBackgroundFaceAuthFailWeek?start_date=2025-01-14&end_date=2025-01-14&guild_id=&user_id=&room_id=&identity_type=&apiToken=&page=1&pageSize=1"

	body, err := httpGet(ctx, url)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryFaceAuthFailedWeeklyFromDH httpGet err:%v, url:%s, req:%+v", err, url, req)
		return nil, err
	}
	respData := &struct {
		Code    int                       `json:"code"`
		Success bool                      `json:"success"`
		Data    *FaceAuthFailedWeeklyResp `json:"data"`
		Msg     string                    `json:"msg"`
	}{}
	err = json.Unmarshal(body, respData)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryFaceAuthFailedWeeklyFromDH Unmarshal failed url:%s, req:%+v, err:%v", url, req, err)
		return nil, err
	}

	if respData.Code != 200 || !respData.Success {
		err = fmt.Errorf("url: %q, respCode is %d, msg %s", url, respData.Code, respData.Msg)
		log.ErrorWithCtx(ctx, "QueryFaceAuthFailedWeeklyFromDH respData failed url:%s, req:%+v, respData:%+v", url, req, respData)
		return nil, err
	}

	log.InfoWithCtx(ctx, "QueryFaceAuthFailedWeeklyFromDH end url:%s, req:%+v, respData:%+v", url, req, respData)
	return respData.Data, nil
}

// QueryGuildFaceAuthFailedDailySumFromDH 公会人脸认证失败日汇总数据
func QueryGuildFaceAuthFailedDailySumFromDH(ctx context.Context, req *GuildFaceAuthDailySumReq) (*GuildFaceAuthDailySumResp, error) {

	// 校验参数
	if req.GuildIdList == nil || len(req.GuildIdList) == 0 {
		log.ErrorWithCtx(ctx, "QueryGuildFaceAuthFailedDailySumFromDH guildIdList empty")
		return nil, errors.New("guildIdList empty")
	}
	if req.StartDate.IsZero() || req.EndDate.IsZero() {
		log.ErrorWithCtx(ctx, "QueryGuildFaceAuthFailedDailySumFromDH StartDate or EndDate invalid")
		return nil, errors.New("StartDate or EndDate invalid")
	}

	// 转换参数
	var startDate, endDate, guildParam, identityParam string
	startDate = req.StartDate.Format("2006-01-02")
	endDate = req.EndDate.Format("2006-01-02")
	firstParam := true
	for _, guildId := range req.GuildIdList {
		if guildId == 0 {
			continue
		}
		if firstParam {
			guildParam = fmt.Sprintf("%d", guildId)
			firstParam = false
			continue
		}
		guildParam += fmt.Sprintf("&guild_id=%d", guildId)
	}
	switch req.IdentityType {
	case 0: // 多人互动
		identityParam = "多人"
	case 1: // 听听
		identityParam = "听听"
	case 2: // 电竞
		identityParam = "电竞"
	}

	c := Business2Config["GuildFaceAuthFailedDailySum"]
	if c == nil {
		log.ErrorWithCtx(ctx, "QueryGuildFaceAuthFailedDailySumFromDH got empty url err, key: MultiGuildMemberDayInfo")
		return nil, errors.New("GuildFaceAuthFailedDailySum config empty")
	}

	url := fmt.Sprintf("%sguildBackgroundFaceAuthFailSummaryDay?apiToken=%s&start_date=%s&end_date=%s&guild_id=%s&page=%d&pageSize=%d&identity_type=%s",
		c.Url, c.Token, startDate, endDate, guildParam, req.Page, req.PageSize, urlutil.QueryEscape(identityParam))
	log.InfoWithCtx(ctx, "QueryGuildFaceAuthFailedDailySumFromDH start url:%s", url)
	// url = "http://*************:10086/107/guildBackgroundFaceAuthFailSummaryDay?start_date=2025-01-14&end_date=2025-01-14&guild_id=&identity_type=&apiToken=&page=1&pageSize=1"

	body, err := httpGet(ctx, url)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryGuildFaceAuthFailedDailySumFromDH httpGet err:%v, url:%s, req:%+v", err, url, req)
		return nil, err
	}
	respData := &struct {
		Code    int                        `json:"code"`
		Success bool                       `json:"success"`
		Data    *GuildFaceAuthDailySumResp `json:"data"`
		Msg     string                     `json:"msg"`
	}{}
	err = json.Unmarshal(body, respData)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryGuildFaceAuthFailedDailySumFromDH Unmarshal failed url:%s, req:%+v, err:%v", url, req, err)
		return nil, err
	}

	if respData.Code != 200 || !respData.Success {
		err = fmt.Errorf("url: %q, respCode is %d, msg %s", url, respData.Code, respData.Msg)
		log.ErrorWithCtx(ctx, "QueryGuildFaceAuthFailedDailySumFromDH respData failed url:%s, req:%+v, respData:%+v", url, req, respData)
		return nil, err
	}

	log.InfoWithCtx(ctx, "QueryGuildFaceAuthFailedDailySumFromDH end url:%s, req:%+v, respData:%+v", url, req, respData)
	return respData.Data, nil
}

// QueryGuildFaceAuthFailedWeeklySumFromDH 公会人脸认证失败周汇总数据
func QueryGuildFaceAuthFailedWeeklySumFromDH(ctx context.Context, req *GuildFaceAuthWeeklySumReq) (*GuildFaceAuthWeeklySumResp, error) {

	// 校验参数
	if req.GuildIdList == nil || len(req.GuildIdList) == 0 {
		log.ErrorWithCtx(ctx, "QueryGuildFaceAuthFailedWeeklySumFromDH guildIdList empty")
		return nil, errors.New("guildIdList empty")
	}
	if req.StartDate.IsZero() || req.EndDate.IsZero() {
		log.ErrorWithCtx(ctx, "QueryGuildFaceAuthFailedWeeklySumFromDH StartDate or EndDate invalid")
		return nil, errors.New("StartDate or EndDate invalid")
	}

	// 转换参数
	var startDate, endDate, guildParam, identityParam string
	startDate = req.StartDate.Format("2006-01-02")
	endDate = req.EndDate.Format("2006-01-02")
	firstParam := true
	for _, guildId := range req.GuildIdList {
		if guildId == 0 {
			continue
		}
		if firstParam {
			guildParam = fmt.Sprintf("%d", guildId)
			firstParam = false
			continue
		}
		guildParam += fmt.Sprintf("&guild_id=%d", guildId)
	}
	switch req.IdentityType {
	case 0: // 多人互动
		identityParam = "多人"
	case 1: // 听听
		identityParam = "听听"
	case 2: // 电竞
		identityParam = "电竞"
	}

	c := Business2Config["GuildFaceAuthFailedWeeklySum"]
	if c == nil {
		log.ErrorWithCtx(ctx, "QueryGuildFaceAuthFailedWeeklySumFromDH got empty url err, key: MultiGuildMemberDayInfo")
		return nil, errors.New("GuildFaceAuthFailedWeeklySum config empty")
	}

	url := fmt.Sprintf("%sguildBackgroundFaceAuthFailSummaryWeek?apiToken=%s&start_date=%s&end_date=%s&guild_id=%s&page=%d&pageSize=%d&identity_type=%s",
		c.Url, c.Token, startDate, endDate, guildParam, req.Page, req.PageSize, urlutil.QueryEscape(identityParam))
	log.InfoWithCtx(ctx, "QueryGuildFaceAuthFailedWeeklySumFromDH start url:%s", url)
	// url = "http://*************:10086/107/guildBackgroundFaceAuthFailSummaryWeek?start_date=2025-01-14&end_date=2025-01-14&guild_id=&identity_type=&apiToken=&page=1&pageSize=1"

	body, err := httpGet(ctx, url)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryGuildFaceAuthFailedWeeklySumFromDH httpGet err:%v, url:%s, req:%+v", err, url, req)
		return nil, err
	}
	respData := &struct {
		Code    int                         `json:"code"`
		Success bool                        `json:"success"`
		Data    *GuildFaceAuthWeeklySumResp `json:"data"`
		Msg     string                      `json:"msg"`
	}{}
	err = json.Unmarshal(body, respData)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryGuildFaceAuthFailedWeeklySumFromDH Unmarshal failed url:%s, req:%+v, err:%v", url, req, err)
		return nil, err
	}

	if respData.Code != 200 || !respData.Success {
		err = fmt.Errorf("url: %q, respCode is %d, msg %s", url, respData.Code, respData.Msg)
		log.ErrorWithCtx(ctx, "QueryGuildFaceAuthFailedWeeklySumFromDH respData failed url:%s, req:%+v, respData:%+v", url, req, respData)
		return nil, err
	}

	log.InfoWithCtx(ctx, "QueryGuildFaceAuthFailedWeeklySumFromDH end url:%s, req:%+v, respData:%+v", url, req, respData)
	return respData.Data, nil
}

// QueryGuildFaceAuthFailedWeeklyPushFromDH 公会人脸认证失败周推送数据
func QueryGuildFaceAuthFailedWeeklyPushFromDH(ctx context.Context, req *GuildFaceAuthWeeklyPushReq) (*GuildFaceAuthWeeklyPushResp, error) {

	// 校验参数
	if req.StartDate.IsZero() || req.EndDate.IsZero() {
		log.ErrorWithCtx(ctx, "QueryGuildFaceAuthFailedWeeklyPushFromDH StartDate or EndDate invalid")
		return nil, errors.New("StartDate or EndDate invalid")
	}

	// 转换参数
	var startDate, endDate, guildParam string
	startDate = req.StartDate.Format("2006-01-02")
	endDate = req.EndDate.Format("2006-01-02")
	firstParam := true
	for _, guildId := range req.GuildIdList {
		if guildId == 0 {
			continue
		}
		if firstParam {
			guildParam = fmt.Sprintf("%d", guildId)
			firstParam = false
			continue
		}
		guildParam += fmt.Sprintf("&guild_id=%d", guildId)
	}

	c := Business2Config["GuildFaceAuthFailedWeeklyPush"]
	if c == nil {
		log.ErrorWithCtx(ctx, "QueryGuildFaceAuthFailedWeeklyPushFromDH got empty url err, key: MultiGuildMemberDayInfo")
		return nil, errors.New("GuildFaceAuthFailedWeeklyPush config empty")
	}

	url := fmt.Sprintf("%sguildBackgroundFaceAuthFailPushWeek?apiToken=%s&start_date=%s&end_date=%s&guild_id=%s&page=%d&pageSize=%d",
		c.Url, c.Token, startDate, endDate, guildParam, req.Page, req.PageSize)
	log.InfoWithCtx(ctx, "QueryGuildFaceAuthFailedWeeklyPushFromDH start url:%s", url)
	// url = "http://*************:10086/107/guildBackgroundFaceAuthFailPushWeek?start_date=2025-01-14&end_date=2025-01-14&guild_id=&identity_type=&apiToken=&page=1&pageSize=1"

	body, err := httpGet(ctx, url)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryGuildFaceAuthFailedWeeklyPushFromDH httpGet err:%v, url:%s, req:%+v", err, url, req)
		return nil, err
	}
	respData := &struct {
		Code    int                          `json:"code"`
		Success bool                         `json:"success"`
		Data    *GuildFaceAuthWeeklyPushResp `json:"data"`
		Msg     string                       `json:"msg"`
	}{}
	err = json.Unmarshal(body, respData)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryGuildFaceAuthFailedWeeklyPushFromDH Unmarshal failed url:%s, req:%+v, err:%v", url, req, err)
		return nil, err
	}

	if respData.Code != 200 || !respData.Success {
		err = fmt.Errorf("url: %q, respCode is %d, msg %s", url, respData.Code, respData.Msg)
		log.ErrorWithCtx(ctx, "QueryGuildFaceAuthFailedWeeklyPushFromDH respData failed url:%s, req:%+v, respData:%+v", url, req, respData)
		return nil, err
	}

	log.InfoWithCtx(ctx, "QueryGuildFaceAuthFailedWeeklyPushFromDH end url:%s, req:%+v, respData:%+v", url, req, respData)
	return respData.Data, nil
}

// QueryGuildFaceAuthFailedDailyPushFromDH 公会人脸认证失败日推送数据
func QueryGuildFaceAuthFailedDailyPushFromDH(ctx context.Context, req *GuildFaceAuthDailyPushReq) (*GuildFaceAuthDailyPushResp, error) {

	// 校验参数
	if req.StartDate.IsZero() || req.EndDate.IsZero() {
		log.ErrorWithCtx(ctx, "QueryGuildFaceAuthFailedDailyPushFromDH StartDate or EndDate invalid")
		return nil, errors.New("StartDate or EndDate invalid")
	}

	// 转换参数
	var startDate, endDate, guildParam string
	startDate = req.StartDate.Format("2006-01-02")
	endDate = req.EndDate.Format("2006-01-02")
	firstParam := true
	for _, guildId := range req.GuildIdList {
		if guildId == 0 {
			continue
		}
		if firstParam {
			guildParam = fmt.Sprintf("%d", guildId)
			firstParam = false
			continue
		}
		guildParam += fmt.Sprintf("&guild_id=%d", guildId)
	}

	c := Business2Config["GuildFaceAuthFailedDailyPush"]
	if c == nil {
		log.ErrorWithCtx(ctx, "QueryGuildFaceAuthFailedDailyPushFromDH got empty url err, key: MultiGuildMemberDayInfo")
		return nil, errors.New("GuildFaceAuthFailedDailyPush config empty")
	}

	url := fmt.Sprintf("%sguildBackgroundFaceAuthFailPushDay?apiToken=%s&start_date=%s&end_date=%s&guild_id=%s&page=%d&pageSize=%d",
		c.Url, c.Token, startDate, endDate, guildParam, req.Page, req.PageSize)
	log.InfoWithCtx(ctx, "QueryGuildFaceAuthFailedDailyPushFromDH start url:%s", url)
	// url = "http://*************:10086/107/guildBackgroundFaceAuthFailPushDay?start_date=2025-01-14&end_date=2025-01-14&guild_id=&identity_type=&apiToken=&page=1&pageSize=1"

	body, err := httpGet(ctx, url)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryGuildFaceAuthFailedDailyPushFromDH httpGet err:%v, url:%s, req:%+v", err, url, req)
		return nil, err
	}
	respData := &struct {
		Code    int                         `json:"code"`
		Success bool                        `json:"success"`
		Data    *GuildFaceAuthDailyPushResp `json:"data"`
		Msg     string                      `json:"msg"`
	}{}
	err = json.Unmarshal(body, respData)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryGuildFaceAuthFailedDailyPushFromDH Unmarshal failed url:%s, req:%+v, err:%v", url, req, err)
		return nil, err
	}

	if respData.Code != 200 || !respData.Success {
		err = fmt.Errorf("url: %q, respCode is %d, msg %s", url, respData.Code, respData.Msg)
		log.ErrorWithCtx(ctx, "QueryGuildFaceAuthFailedDailyPushFromDH respData failed url:%s, req:%+v, respData:%+v", url, req, respData)
		return nil, err
	}

	log.InfoWithCtx(ctx, "QueryGuildFaceAuthFailedDailyPushFromDH end url:%s, req:%+v, respData:%+v", url, req, respData)
	return respData.Data, nil
}

// queryOpdataLiveGuildStats 经营者数据平台-语音直播-公会月数据
func QueryOpdataLiveGuildStats(ctx context.Context, req *OpdataLiveGuildStatsReq) (*OpdataLiveGuildStatsResp, error) {
	// 校验参数
	if req.StartDate.IsZero() || req.EndDate.IsZero() {
		log.ErrorWithCtx(ctx, "QueryOpdataLiveGuildStats StartDate or EndDate invalid")
		return nil, errors.New("StartDate or EndDate invalid")
	}

	// 转换参数
	var startDate, endDate string
	startDate = req.StartDate.Format("2006-01-02")
	endDate = req.EndDate.Format("2006-01-02")

	c := Business2Config["QueryOpdataLiveGuildStats"]
	if c == nil {
		log.ErrorWithCtx(ctx, "QueryOpdataLiveGuildStats got empty url err, key: QueryOpdataLiveGuildStats")
		return nil, errors.New("QueryOpdataLiveGuildStats config empty")
	}

	url := fmt.Sprintf("%squeryOpdataLiveGuildStats?apiToken=%s&start_date=%s&end_date=%s&guild_id=%d&page=%d&pageSize=%d",
		c.Url, c.Token, startDate, endDate, req.GuildId, req.Page, req.PageSize)

	body, err := httpGet(ctx, url)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryOpdataLiveGuildStats httpGet err:%v, url:%s, req:%+v", err, url, req)
		return nil, err
	}
	respData := &struct {
		Code    int                       `json:"code"`
		Success bool                      `json:"success"`
		Data    *OpdataLiveGuildStatsResp `json:"data"`
		Msg     string                    `json:"msg"`
	}{}
	err = json.Unmarshal(body, respData)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryOpdataLiveGuildStats Unmarshal failed url:%s, req:%+v, err:%v", url, req, err)
		return nil, err
	}

	if respData.Code != 200 || !respData.Success {
		err = fmt.Errorf("url: %q, respCode is %d, msg %s", url, respData.Code, respData.Msg)
		log.ErrorWithCtx(ctx, "QueryOpdataLiveGuildStats respData failed url:%s, req:%+v, respData:%+v", url, req, respData)
		return nil, err
	}

	log.InfoWithCtx(ctx, "QueryOpdataLiveGuildStats end url:%s, req:%+v, respData:%+v", url, req, respData)
	return respData.Data, nil
}

// QueryOpdataLiveAnchorStats 经营者数据平台-语音直播-主播月数据
func QueryOpdataLiveAnchorStats(ctx context.Context, req *OpdataLiveAnchorStatsReq) (*OpdataLiveAnchorStatsResp, error) {
	// 校验参数
	if req.StartDate.IsZero() || req.EndDate.IsZero() {
		log.ErrorWithCtx(ctx, "QueryOpdataLiveAnchorStats StartDate or EndDate invalid")
		return nil, errors.New("StartDate or EndDate invalid")
	}

	// 转换参数
	var startDate, endDate string
	startDate = req.StartDate.Format("2006-01-02")
	endDate = req.EndDate.Format("2006-01-02")

	strGuildList := make([]string, 0)
	for _, guildId := range req.GuildIdList {
		strGuildList = append(strGuildList, fmt.Sprintf("%d", guildId))
	}

	strUidList := make([]string, 0)
	for _, uid := range req.UserIdList {
		strUidList = append(strUidList, fmt.Sprintf("%d", uid))
	}

	strGuild := strings.Join(strGuildList, ",")
	strUid := strings.Join(strUidList, ",")

	c := Business2Config["QueryOpdataLiveAnchorStats"]
	if c == nil {
		log.ErrorWithCtx(ctx, "QueryOpdataLiveAnchorStats got empty url err, key: MultiGuildMemberDayInfo")
		return nil, errors.New("QueryOpdataLiveAnchorStats config empty")
	}

	url := fmt.Sprintf("%squeryOpdataLiveAnchorStats?apiToken=%s&start_date=%s&end_date=%s&guild_id=%s&user_id=%s&page=%d&pageSize=%d",
		c.Url, c.Token, startDate, endDate, strGuild, strUid, req.Page, req.PageSize)
	body, err := httpGet(ctx, url)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryOpdataLiveAnchorStats httpGet err:%v, url:%s, req:%+v", err, url, req)
		return nil, err
	}
	respData := &struct {
		Code    int                        `json:"code"`
		Success bool                       `json:"success"`
		Data    *OpdataLiveAnchorStatsResp `json:"data"`
		Msg     string                     `json:"msg"`
	}{}
	err = json.Unmarshal(body, respData)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryOpdataLiveAnchorStats Unmarshal failed url:%s, req:%+v, err:%v", url, req, err)
		return nil, err
	}

	if respData.Code != 200 || !respData.Success {
		err = fmt.Errorf("url: %q, respCode is %d, msg %s", url, respData.Code, respData.Msg)
		log.ErrorWithCtx(ctx, "QueryOpdataLiveAnchorStats respData failed url:%s, req:%+v, respData:%+v", url, req, respData)
		return nil, err
	}

	log.InfoWithCtx(ctx, "QueryOpdataLiveAnchorStats end url:%s, req:%+v, respData:%+v", url, req, respData)
	return respData.Data, nil
}

// QueryOpdataEsportCoachStats 电竞大神数据接口
func QueryOpdataEsportCoachStats(ctx context.Context, req *OpdataEsportCoachStatsReq) (*OpdataEsportCoachStatsResp, error) {
	// 校验参数
	if req.StartDate.IsZero() || req.EndDate.IsZero() {
		log.ErrorWithCtx(ctx, "QueryOpdataEsportCoachStats StartDate or EndDate invalid")
		return nil, errors.New("StartDate or EndDate invalid")
	}

	// 转换参数
	var startDate, endDate string
	startDate = req.StartDate.Format("2006-01-02")
	endDate = req.EndDate.Format("2006-01-02")

	strGuildList := make([]string, 0)
	for _, guildId := range req.GuildIdList {
		strGuildList = append(strGuildList, fmt.Sprintf("%d", guildId))
	}

	strUidList := make([]string, 0)
	for _, uid := range req.UserIdList {
		strUidList = append(strUidList, fmt.Sprintf("%d", uid))
	}

	strGuild := strings.Join(strGuildList, ",")

	strUid := "99"
	if len(strUidList) > 0 {
		strUid = strings.Join(strUidList, ",")
	}

	c := Business2Config["QueryOpdataEsportCoachStats"]
	if c == nil {
		log.ErrorWithCtx(ctx, "QueryOpdataEsportCoachStats got empty url err, key: QueryOpdataEsportCoachStats")
		return nil, errors.New("QueryOpdataEsportCoachStats config empty")
	}

	url := fmt.Sprintf("%squeryOpdataEsportCoachStats?apiToken=%s&start_date=%s&end_date=%s&guild_id=%s&user_id=%s&page=%d&pageSize=%d&time_dim=%s",
		c.Url, c.Token, startDate, endDate, strGuild, strUid, req.Page, req.PageSize, req.TimeDim)
	body, err := httpGet(ctx, url)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryOpdataEsportCoachStats httpGet err:%v, url:%s, req:%+v", err, url, req)
		return nil, err
	}
	respData := &struct {
		Code    int                         `json:"code"`
		Success bool                        `json:"success"`
		Data    *OpdataEsportCoachStatsResp `json:"data"`
		Msg     string                      `json:"msg"`
	}{}
	err = json.Unmarshal(body, respData)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryOpdataEsportCoachStats Unmarshal failed url:%s, req:%+v, err:%v", url, req, err)
		return nil, err
	}

	if respData.Code != 200 || !respData.Success {
		err = fmt.Errorf("url: %q, respCode is %d, msg %s", url, respData.Code, respData.Msg)
		log.ErrorWithCtx(ctx, "QueryOpdataEsportCoachStats respData failed url:%s, req:%+v, respData:%+v", url, req, respData)
		return nil, err
	}

	log.InfoWithCtx(ctx, "QueryOpdataEsportCoachStats end url:%s, req:%+v, respData:%+v", url, req, respData)
	return respData.Data, nil
}

// QueryOpdataEsportGuildStats 电竞公会整体经营数据接口
func QueryOpdataEsportGuildStats(ctx context.Context, req *OpdataEsportGuildStatsReq) (*OpdataEsportGuildStatsResp, error) {
	// 校验参数
	if req.StartDate.IsZero() || req.EndDate.IsZero() {
		log.ErrorWithCtx(ctx, "QueryOpdataEsportGuildStats StartDate or EndDate invalid")
		return nil, errors.New("StartDate or EndDate invalid")
	}

	// 转换参数
	var startDate, endDate string
	startDate = req.StartDate.Format("2006-01-02")
	endDate = req.EndDate.Format("2006-01-02")

	strGuildList := make([]string, 0)
	for _, guildId := range req.GuildIdList {
		strGuildList = append(strGuildList, fmt.Sprintf("%d", guildId))
	}

	strGuild := strings.Join(strGuildList, ",")

	c := Business2Config["QueryOpdataEsportGuildStats"]
	if c == nil {
		log.ErrorWithCtx(ctx, "QueryOpdataEsportGuildStats got empty url err, key: QueryOpdataEsportGuildStats")
		return nil, errors.New("QueryOpdataEsportGuildStats config empty")
	}

	url := fmt.Sprintf("%squeryOpdataEsportGuildStats?apiToken=%s&start_date=%s&end_date=%s&guild_id=%s&page=%d&pageSize=%d&time_dim=%s",
		c.Url, c.Token, startDate, endDate, strGuild, req.Page, req.PageSize, req.TimeDim)
	body, err := httpGet(ctx, url)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryOpdataEsportGuildStats httpGet err:%v, url:%s, req:%+v", err, url, req)
		return nil, err
	}
	respData := &struct {
		Code    int                         `json:"code"`
		Success bool                        `json:"success"`
		Data    *OpdataEsportGuildStatsResp `json:"data"`
		Msg     string                      `json:"msg"`
	}{}
	err = json.Unmarshal(body, respData)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryOpdataEsportGuildStats Unmarshal failed url:%s, req:%+v, err:%v", url, req, err)
		return nil, err
	}

	if respData.Code != 200 || !respData.Success {
		err = fmt.Errorf("url: %q, respCode is %d, msg %s", url, respData.Code, respData.Msg)
		log.ErrorWithCtx(ctx, "QueryOpdataEsportGuildStats respData failed url:%s, req:%+v, respData:%+v", url, req, respData)
		return nil, err
	}

	log.InfoWithCtx(ctx, "QueryOpdataEsportGuildStats end url:%s, req:%+v, respData:%+v", url, req, respData)
	return respData.Data, nil
}

// QueryOpdataEsportRoomStats 电竞公会整体经营数据接口
func QueryOpdataEsportRoomStats(ctx context.Context, req *OpdataEsportRoomStatsReq) (*OpdataEsportRoomStatsResp, error) {
	// 校验参数
	if req.StartDate.IsZero() || req.EndDate.IsZero() {
		log.ErrorWithCtx(ctx, "QueryOpdataEsportRoomStats StartDate or EndDate invalid")
		return nil, errors.New("StartDate or EndDate invalid")
	}

	// 转换参数
	var startDate, endDate string
	startDate = req.StartDate.Format("2006-01-02")
	endDate = req.EndDate.Format("2006-01-02")

	strGuildList := make([]string, 0)
	for _, guildId := range req.GuildIdList {
		strGuildList = append(strGuildList, fmt.Sprintf("%d", guildId))
	}

	strRoomList := make([]string, 0)
	for _, roomId := range req.RoomIdList {
		strRoomList = append(strRoomList, fmt.Sprintf("%d", roomId))
	}

	strGuild := strings.Join(strGuildList, ",")

	strRoom := "99"
	if len(strRoomList) > 0 {
		strRoom = strings.Join(strRoomList, ",")
	}

	c := Business2Config["QueryOpdataEsportRoomStats"]
	if c == nil {
		log.ErrorWithCtx(ctx, "QueryOpdataEsportRoomStats got empty url err, key: QueryOpdataEsportGuildStats")
		return nil, errors.New("QueryOpdataEsportRoomStats config empty")
	}

	url := fmt.Sprintf("%squeryOpdataEsportRoomStats?apiToken=%s&start_date=%s&end_date=%s&guild_id=%s&room_id=%s&page=%d&pageSize=%d&time_dim=%s",
		c.Url, c.Token, startDate, endDate, strGuild, strRoom, req.Page, req.PageSize, req.TimeDim)
	body, err := httpGet(ctx, url)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryOpdataEsportRoomStats httpGet err:%v, url:%s, req:%+v", err, url, req)
		return nil, err
	}
	respData := &struct {
		Code    int                        `json:"code"`
		Success bool                       `json:"success"`
		Data    *OpdataEsportRoomStatsResp `json:"data"`
		Msg     string                     `json:"msg"`
	}{}
	err = json.Unmarshal(body, respData)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryOpdataEsportRoomStats Unmarshal failed url:%s, req:%+v, err:%v", url, req, err)
		return nil, err
	}

	if respData.Code != 200 || !respData.Success {
		err = fmt.Errorf("url: %q, respCode is %d, msg %s", url, respData.Code, respData.Msg)
		log.ErrorWithCtx(ctx, "QueryOpdataEsportRoomStats respData failed url:%s, req:%+v, respData:%+v", url, req, respData)
		return nil, err
	}

	log.InfoWithCtx(ctx, "QueryOpdataEsportRoomStats end url:%s, req:%+v, respData:%+v", url, req, respData)
	return respData.Data, nil
}

// queryOpdataMultiOperate 公会经营数据
func QueryOpdataMultiOperate(ctx context.Context, req *OpdataMultiOperateReq) (*OpdataMultiOperateResp, error) {
	// 校验参数
	if req.StartDate.IsZero() || req.EndDate.IsZero() {
		log.ErrorWithCtx(ctx, "QueryOpdataMultiOperate StartDate or EndDate invalid")
		return nil, errors.New("StartDate or EndDate invalid")
	}

	// 转换参数
	var startDate, endDate string
	startDate = req.StartDate.Format("2006-01-02")
	endDate = req.EndDate.Format("2006-01-02")

	strGuildList := make([]string, 0)
	for _, guildId := range req.GuildIdList {
		strGuildList = append(strGuildList, fmt.Sprintf("%d", guildId))
	}

	strGuild := strings.Join(strGuildList, ",")

	c := Business2Config["QueryOpdataMultiOperate"]
	if c == nil {
		log.ErrorWithCtx(ctx, "QueryOpdataMultiOperate got empty url err, key: QueryOpdataMultiOperate")
		return nil, errors.New("QueryOpdataMultiOperate config empty")
	}

	url := fmt.Sprintf("%squeryOpdataMultiOperate?apiToken=%s&start_date=%s&end_date=%s&guild_id=%s&page=%d&pageSize=%d&time_dim=%s",
		c.Url, c.Token, startDate, endDate, strGuild, req.Page, req.PageSize, req.TimeDim)
	body, err := httpGet(ctx, url)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryGuildBusinessData httpGet err:%v, url:%s, req:%+v", err, url, req)
		return nil, err
	}
	respData := &struct {
		Code    int                     `json:"code"`
		Success bool                    `json:"success"`
		Data    *OpdataMultiOperateResp `json:"data"`
		Msg     string                  `json:"msg"`
	}{}
	err = json.Unmarshal(body, respData)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryOpdataMultiOperate Unmarshal failed url:%s, req:%+v, err:%v", url, req, err)
		return nil, err
	}

	if respData.Code != 200 || !respData.Success {
		err = fmt.Errorf("url: %q, respCode is %d, msg %s", url, respData.Code, respData.Msg)
		log.ErrorWithCtx(ctx, "QueryOpdataMultiOperate respData failed url:%s, req:%+v, respData:%+v", url, req, respData)
		return nil, err
	}

	log.InfoWithCtx(ctx, "QueryOpdataMultiOperate end url:%s, req:%+v, respData:%+v", url, req, respData)
	return respData.Data, nil
}

// queryOpdataMultiFrontPage 公会多人互动重点数据
func QueryOpdataMultiFrontPage(ctx context.Context, req *OpdataMultiFrontPageReq) (*OpdataMultiFrontPageResp, error) {
	// 校验参数
	if req.StartDate.IsZero() || req.EndDate.IsZero() {
		log.ErrorWithCtx(ctx, "QueryOpdataMultiFrontPage StartDate or EndDate invalid")
		return nil, errors.New("StartDate or EndDate invalid")
	}

	// 转换参数
	var startDate, endDate string
	startDate = req.StartDate.Format("2006-01-02")
	endDate = req.EndDate.Format("2006-01-02")

	strGuildList := make([]string, 0)
	for _, guildId := range req.GuildIdList {
		strGuildList = append(strGuildList, fmt.Sprintf("%d", guildId))
	}

	strGuild := strings.Join(strGuildList, ",")

	c := Business2Config["QueryOpdataMultiFrontPage"]
	if c == nil {
		log.ErrorWithCtx(ctx, "QueryOpdataMultiFrontPage got empty url err, key: QueryOpdataMultiFrontPage")
		return nil, errors.New("QueryOpdataMultiFrontPage config empty")
	}

	url := fmt.Sprintf("%squeryOpdataMultiFrontPage?apiToken=%s&start_date=%s&end_date=%s&guild_id=%s&page=%d&pageSize=%d",
		c.Url, c.Token, startDate, endDate, strGuild, req.Page, req.PageSize)
	body, err := httpGet(ctx, url)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryOpdataMultiFrontPage httpGet err:%v, url:%s, req:%+v", err, url, req)
		return nil, err
	}
	respData := &struct {
		Code    int                       `json:"code"`
		Success bool                      `json:"success"`
		Data    *OpdataMultiFrontPageResp `json:"data"`
		Msg     string                    `json:"msg"`
	}{}
	err = json.Unmarshal(body, respData)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryOpdataMultiFrontPage Unmarshal failed url:%s, req:%+v, err:%v", url, req, err)
		return nil, err
	}

	if respData.Code != 200 || !respData.Success {
		err = fmt.Errorf("url: %q, respCode is %d, msg %s", url, respData.Code, respData.Msg)
		log.ErrorWithCtx(ctx, "QueryOpdataMultiFrontPage respData failed url:%s, req:%+v, respData:%+v", url, req, respData)
		return nil, err
	}

	log.InfoWithCtx(ctx, "QueryOpdataMultiFrontPage end url:%s, req:%+v, respData:%+v", url, req, respData)
	return respData.Data, nil
}

// queryOpdataMultiRoomStats 公会房间经营数据
func QueryOpdataMultiRoomStats(ctx context.Context, req *OpdataMultiRoomStatsReq) (*OpdataMultiRoomStatsResp, error) {
	// 校验参数
	if req.StartDate.IsZero() || req.EndDate.IsZero() {
		log.ErrorWithCtx(ctx, "QueryOpdataMultiRoomStats StartDate or EndDate invalid")
		return nil, errors.New("StartDate or EndDate invalid")
	}

	// 转换参数
	var startDate, endDate string
	startDate = req.StartDate.Format("2006-01-02")
	endDate = req.EndDate.Format("2006-01-02")

	strGuildList := make([]string, 0)
	for _, guildId := range req.GuildIdList {
		strGuildList = append(strGuildList, fmt.Sprintf("%d", guildId))
	}

	strRoomList := make([]string, 0)
	for _, roomId := range req.RoomIdList {
		strRoomList = append(strRoomList, fmt.Sprintf("%d", roomId))
	}

	strGuild := strings.Join(strGuildList, ",")

	strRoom := "99"
	if len(strRoomList) > 0 {
		strRoom = strings.Join(strRoomList, ",")
	}

	c := Business2Config["QueryOpdataMultiRoomStats"]
	if c == nil {
		log.ErrorWithCtx(ctx, "QueryOpdataMultiRoomStats got empty url err, key: QueryOpdataMultiRoomStats")
		return nil, errors.New("QueryOpdataMultiRoomStats config empty")
	}

	url := fmt.Sprintf("%squeryOpdataMultiRoomStats?apiToken=%s&start_date=%s&end_date=%s&guild_id=%s&room_id=%s&page=%d&pageSize=%d&time_dim=%s",
		c.Url, c.Token, startDate, endDate, strGuild, strRoom, req.Page, req.PageSize, req.TimeDim)
	body, err := httpGet(ctx, url)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryOpdataMultiRoomStats httpGet err:%v, url:%s, req:%+v", err, url, req)
		return nil, err
	}
	respData := &struct {
		Code    int                       `json:"code"`
		Success bool                      `json:"success"`
		Data    *OpdataMultiRoomStatsResp `json:"data"`
		Msg     string                    `json:"msg"`
	}{}
	err = json.Unmarshal(body, respData)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryOpdataMultiRoomStats Unmarshal failed url:%s, req:%+v, err:%v", url, req, err)
		return nil, err
	}

	if respData.Code != 200 || !respData.Success {
		err = fmt.Errorf("url: %q, respCode is %d, msg %s", url, respData.Code, respData.Msg)
		log.ErrorWithCtx(ctx, "QueryOpdataMultiRoomStats respData failed url:%s, req:%+v, respData:%+v", url, req, respData)
		return nil, err
	}

	log.InfoWithCtx(ctx, "QueryOpdataMultiRoomStats end url:%s, req:%+v, respData:%+v", url, req, respData)
	return respData.Data, nil
}

// queryGuildPromotion 语音直播经营诊断接口
func QueryGuildPromotion(ctx context.Context, req *GuildPromotionReq) (*GuildPromotionResp, error) {
	// 校验参数
	if req.StartDate.IsZero() || req.EndDate.IsZero() {
		log.ErrorWithCtx(ctx, "QueryGuildPromotion StartDate or EndDate invalid")
		return nil, errors.New("StartDate or EndDate invalid")
	}

	// 转换参数
	var startDate, endDate string
	startDate = req.StartDate.Format("2006-01-02")
	endDate = req.EndDate.Format("2006-01-02")

	strGuildList := make([]string, 0)
	for _, guildId := range req.GuildIdList {
		strGuildList = append(strGuildList, fmt.Sprintf("%d", guildId))
	}

	strGuild := strings.Join(strGuildList, ",")

	c := Business2Config["QueryGuildPromotion"]
	if c == nil {
		log.ErrorWithCtx(ctx, "QueryGuildPromotion got empty url err, key: QueryGuildPromotion")
		return nil, errors.New("QueryGuildPromotion config empty")
	}

	url := fmt.Sprintf("%squeryOpdataLiveBusiness?apiToken=%s&start_date=%s&end_date=%s&guild_id=%s&page=%d&pageSize=%d&time_dim=%s",
		c.Url, c.Token, startDate, endDate, strGuild, req.Page, req.PageSize, req.TimeDim)
	body, err := httpGet(ctx, url)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryGuildPromotion httpGet err:%v, url:%s, req:%+v", err, url, req)
		return nil, err
	}
	respData := &struct {
		Code    int                 `json:"code"`
		Success bool                `json:"success"`
		Data    *GuildPromotionResp `json:"data"`
		Msg     string              `json:"msg"`
	}{}
	err = json.Unmarshal(body, respData)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryGuildPromotion Unmarshal failed url:%s, req:%+v, err:%v", url, req, err)
		return nil, err
	}

	if respData.Code != 200 || !respData.Success {
		err = fmt.Errorf("url: %q, respCode is %d, msg %s", url, respData.Code, respData.Msg)
		log.ErrorWithCtx(ctx, "QueryGuildPromotion respData failed url:%s, req:%+v, respData:%+v", url, req, respData)
		return nil, err
	}

	log.InfoWithCtx(ctx, "QueryGuildPromotion end url:%s, req:%+v, respData:%+v", url, req, respData)
	return respData.Data, nil
}

var httpGet = func(ctx context.Context, url string) (body []byte, err error) {

	httpReq, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return
	}

	beginTime := time.Now()
	defer func() {
		metrics.ReportMetrics( // 上报
			httpReq.Method,        // HTTP 方法
			httpReq.URL.Path,      // 路径名
			http.StatusOK,         // HTTP 状态码
			time.Since(beginTime), // 耗时
		)
	}()

	resp, err := defaultHTTPClient.Do(httpReq.WithContext(ctx))
	if err != nil {
		return
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		err = fmt.Errorf("url: %q, http code: %d", url, resp.StatusCode)
		return
	}
	body, err = ioutil.ReadAll(resp.Body)
	if err != nil {
		return
	}
	return
}

func GetdefaultHTTPClient() *http.Client {
	return defaultHTTPClient
}
