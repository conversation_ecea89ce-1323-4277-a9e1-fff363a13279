syntax = "proto3";

/***************虚拟形象logic*****************/

package ga.virtual_avatar_logic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/virtual_avatar_logic";

// 获取虚拟形象入口状态
  message GetVirtualAvatarEntryRequest{
    ga.BaseReq base_req = 1;
  }
  
  message GetVirtualAvatarEntryResponse{
    ga.BaseResp base_resp = 1;
    bool have_access = 2;    // true-展示，false-不展示
  }
  
  enum VADisplayType{
    VA_DISPLAY_TYPE_UNSPECIFIED = 0;
    VA_DISPLAY_TYPE_COMMON = 1;   // 普通单人形象
    VA_DISPLAY_TYPE_CP = 2;       // 双人形象
  }
  
  
  enum VAResourceType{
      VA_RESOURCE_TYPE_UNSPECIFIED = 0;
      VA_RESOURCE_TYPE_MIC_SPACE = 1;   // 麦位资源
      VA_RESOURCE_TYPE_PERSONAL_CARD_PROFILE = 2;  // 个人主页、房间资料卡资源
      VA_RESOURCE_TYPE_ENTER_CHANNEL_EFFECT = 3;   // 进房特效资源
  }
  
    enum VAUseScopeType{
    VA_USE_SCOPE_TYPE_UNSPECIFIED = 0;
    VA_USE_SCOPE_TYPE_MIC_SPACE = 1;         // 麦位
    VA_USE_SCOPE_TYPE_PERSON_PAGE = 2;       // 个人主页
    VA_USE_SCOPE_TYPE_CH_PROFILE_CARD = 3;  // 房间资料卡
  }
  
  // 资源人物类型
  enum ResCharacterType{  
    RES_CHARACTER_TYPE_UNSPECIFIED = 0;
    RES_CHARACTER_TYPE_USER_A = 1;
    RES_CHARACTER_TYPE_USER_B = 2;
  }

  message VAResourceConf{
    string effect_res = 1;       // 特效资源
    string effect_res_md5 = 2;   // 特效资源md5
    uint32 res_type = 3;         // 资源类型，see VAResourceType
  }
  
  // 一套资源
  message VAResourceSet{  
      uint32 user_item_id = 1;
      uint32 display_type = 2;    // see VADisplayType
      repeated VAResourceConf res_cfg = 3; // 虚拟形象资源
      bool in_use = 4;            // 正在使用中
      uint32 va_id = 5;           // 虚拟形象id
  }

  // 用户虚拟形象信息
  message UserVirtualAvatarInfo{
    UserProfile user = 1;
    string name = 2;              // 名称
    string base_pic = 3;          // 虚拟形象静态预览图
    VAResourceSet res_set = 4;  // 虚拟形象资源
    
    UserProfile cp_user = 5; // 双人形象中，对方的用户信息
    string relate_name = 6;  // 关系标签名称
    uint32 user_char = 7;    // see ResCharacterType
    uint32 cp_user_char = 8; // see ResCharacterType
    
    int64 expire_ts = 9;     // 过期时间,秒级时间戳
  }
  
  // 获取用户虚拟形象列表
  message GetUserVirtualAvatarListRequest{
    ga.BaseReq base_req = 1;
  }
  
  message GetUserVirtualAvatarListResponse{
    ga.BaseResp base_resp = 1;
    repeated UserVirtualAvatarInfo va_list = 2;  // 用户虚拟形象列表
    uint32 auto_play_duration_sec = 3;  // 虚拟形象资源自动轮播间隔时间，秒
  }

// 用户佩戴虚拟形象
message SetUserVirtualAvatarInUseRequest{
  ga.BaseReq base_req = 1;
  uint32 user_item_id = 2;  // 填0时取消佩戴
  uint32 display_type = 3;   // see VADisplayType
}

message SetUserVirtualAvatarInUseResponse{
  ga.BaseResp base_resp = 1;
}

// 获取用户使用中的虚拟形象信息
message GetUserVirtualAvatarInUseRequest{
  ga.BaseReq base_req = 1;
  uint32 target_uid = 2;
  uint32 source_type = 3; //请求来源，see VAUseScopeType
}

message GetUserVirtualAvatarInUseResponse{
  ga.BaseResp base_resp = 1;
  UserVirtualAvatarInfo user_va_info = 2;  // 用户虚拟形象信息
  repeated uint32 use_scope_list = 10;  // 用户虚拟形象使用范围, VAUseScopeType
}

  // 用户设置虚拟形象使用范围
  message SetVirtualAvatarUseScopeRequest{
    ga.BaseReq base_req = 1;
    uint32 use_scope = 2;  // 虚拟形象使用范围, see VAUseScopeType
    enum OpType{
        OP_TYPE_UNSPECIFIED = 0;
        OP_TYPE_SET = 1;
        OP_TYPE_REMOVE = 2;
    }
    uint32 op_type = 3;  // 操作类型，see OpType
  }

  message SetVirtualAvatarUseScopeResponse{
    ga.BaseResp base_resp = 1;
  }

  // 获取用户虚拟形象使用范围
  message GetVirtualAvatarUseScopeRequest{
    ga.BaseReq base_req = 1;
  }

  message GetVirtualAvatarUseScopeResponse{
    ga.BaseResp base_resp = 1;
    repeated uint32 use_scope = 2;  // 虚拟形象使用范围, see VAUseScopeType
  }

// =========== 房间麦位相关 ===========

enum UserMicAvatarType{
    USER_MIC_AVATAR_TYPE_UNSPECIFIED = 0;
    USER_MIC_AVATAR_TYPE_NORMAL = 1;   // 普通形象
    USER_MIC_AVATAR_TYPE_VIRTUAL = 2;  // 虚拟形象
}

// 用户获取自己当前麦位形象状态
message GetUserCurrMicAvatarTypeRequest{
  ga.BaseReq base_req = 1;
}

message GetUserCurrMicAvatarTypeResponse{
  ga.BaseResp base_resp = 1;
  uint32 mic_avatar_type = 2;  // see UserMicAvatarType, 为0时不展示 切换按钮
}

message SetUserCurrMicAvatarTypeRequest{
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
  uint32 mic_avatar_type = 3;  // 目标切换麦位虚拟形象类型 see UserMicAvatarType
}

message SetUserCurrMicAvatarTypeResponse{
  ga.BaseResp base_resp = 1;
}

message MicVirtualAvatarInfoChanegeOpt {
  uint32 uid = 1;
  uint32 avatar_type = 2; // see UserMicAvatarType
  VAResourceConf mic_space_res = 3;// 麦位资源
  uint32 va_id = 4;           // 虚拟形象id
}

message MicVirtualAvatarInfoOpt{
  uint32 avatar_type = 1; // see UserMicAvatarType
  VAResourceConf mic_space_res = 2;// 麦位资源
  uint32 va_id = 3;           // 虚拟形象id
}

message VirtualAvatarGainNewNotify {
  uint32 uid = 1;
  UserVirtualAvatarInfo va_info = 2;
}