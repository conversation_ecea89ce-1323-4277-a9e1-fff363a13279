syntax = "proto3";

package ugc.recommendation_ulib;

option go_package = "golang.52tt.com/protocol/services/ugc/recommendation_ulib";

service  UgcRecommendationUserLibrary {
    rpc GetFocusUserByLibrary(GetFocusUserByLibraryReq) returns (GetFocusUserByLibraryResp);
    rpc SetUserOnlineStatus(SetUserOnlineStatusReq) returns (SetUserOnlineStatusResp);
}

message QueryUserOption {
    uint32 user_id = 1;
    repeated string need_tag = 2; // 需求标签
    repeated string game_tag = 3; // 游戏标签
    uint32 gender = 4; // 性别  1 男性 2 女性 0未知
}

message FilterOption {
    repeated uint32 user_id = 1;
}

message GetFocusUserByLibraryReq {
    QueryUserOption condition = 1; // 推荐用户条件
    FilterOption filter = 2; // 过滤条件
}

message GetFocusUserByLibraryResp {
    repeated uint32 user_list = 1;
}

message SetUserOnlineStatusReq {
    uint32 user_id = 1;
    uint32 status = 2;
}

message SetUserOnlineStatusResp {

}


