syntax = "proto3";

package ugc.rcmd_post;

import "common/common.proto";

option go_package = "golang.52tt.com/protocol/services/ugc/rcmd_post";

service RcmdPost {
  // 获取推荐列表(deprecated)
  rpc GetRecommendStream (GetRecommendStreamReq) returns (GetRecommendStreamResp);

  // 获取精选列表
  rpc GetOperRecommendStream (GetOperRecommendStreamReq) returns (GetOperRecommendStreamResp);

  // 获取推荐列表
  rpc GetSystemRecommendStream (GetSystemRecommendStreamReq) returns (GetSystemRecommendStreamResp);

  // 推荐流设置权重，给测试用
  rpc SetWeightForTest(SetWeightForTestReq) returns (SetWeightForTestRsp);

  //城市流
  rpc GetCityRecommendStream (GetCityRecommendStreamReq) returns (GetCityRecommendStreamRsp);

  //话题流
  rpc GetTopicRecommendStream (GetTopicRecommendStreamReq) returns (GetTopicRecommendStreamRsp);

  //测试用,profile高互动池
  rpc ProfileHighInteractPool(ProfileHighInteractPoolReq) returns (ProfileHighInteractPoolRsp);

  // 运营后台用, 查询高互动池帖子数据
  rpc GetHighInteractPostList(GetHighInteractPostListReq) returns (GetHighInteractPostListRsp);

  //运营后台用，查询话题流贴子数据
  rpc GetTopicStreamPostList(GetTopicStreamPostListReq) returns (GetTopicStreamPostListRsp);

  //声控专区（主播专区）
  rpc GetVoiceControlStream(GetVoiceControlStreamReq) returns (GetVoiceControlStreamRsp);

  //是否在声控专区用户白名单里面
  rpc CheckInVoiceControlWhite(CheckInVoiceControlWhiteReq) returns (CheckInVoiceControlWhiteRsp);

  // 获取子tab推荐列表
  rpc GetTabTopicStream(GetTabTopicStreamReq) returns (GetTabTopicStreamRsp);

  //帖子实时曝光接口
  rpc NotifyPostExpose(NotifyPostExposeReq) returns (NotifyPostExposeRsp);

  // 查询帖子是否为精品帖
  rpc CheckPostIsBoutique(CheckPostIsBoutiqueReq) returns (CheckPostIsBoutiqueRsp);
  
  // 推荐流查用户兴趣标签
  rpc GetUserPageTagList(GetUserPageTagListReq) returns (GetUserPageTagListResp);
  
  // 同城tab v2
  rpc GetSameCityStreamV2(GetSameCityStreamV2Req) returns (GetSameCityStreamV2Resp) ;
  
  // 是否需要跳转到广场
  rpc CheckJumpToSquare(CheckJumpToSquareReq) returns (CheckJumpToSquareResp);
}

//测试用的
message SetWeightForTestReq{

  // 设置权重的原因，楼主回复: "reply", 不良标签:"bad_label", 运营单个帖子提权: "operator_single" 运营话题帖子提权:"operator_topic"
  string reason = 1;

  string post_id = 2; //为某一个帖子提权
  string topic_id = 3; //或者为某一个话题提权，主要是 reason="operator_topic"的时候使用
  float value = 4; //要设置的分, reply 自动设置成int
  uint64 expire_time = 5; //截止时间，设置运营提权的时候用
}

message SetWeightForTestRsp{
  string msg = 1;
}

// 来源类型
enum PostSourceType {
  INVALID = 0;
  KOL = 1;
  TGL = 2;
}

message GetRecommendStreamReq{
  uint32 uid = 1;
  uint32 limit = 2;
  repeated uint32 tags = 3;
  string hint_post_id = 4; // 首位强制显示的post_id（一级页跳到二级页需带上）
}

message GetRecommendStreamResp{
  repeated StreamItem items = 1;
  bool bottom_reached = 2;
}

message StreamItem {
  string post_id = 1; //帖子id
  PostSourceType source_type = 2; // 来源类型
}

message GetOperRecommendStreamReq {
  uint32 uid = 1;
  uint32 limit = 2;
  repeated uint32 tags = 3;
  string hint_post_id = 4; // 首位强制显示的post_id（一级页跳到二级页需带上）

  rcmd.common.RcmdBrowseInfo browse_list = 5; //请求列表曝光信息
}

message GetOperRecommendStreamResp {
  repeated StreamItem items = 1;
  bool bottom_reached = 2;
}

message SystemRecommendStreamItem {
  string post_id = 1;

  uint32 age_layer = 2; //发帖的时候，作者的年龄圈
  uint32 sex = 3; //发帖的时候，作者性别
  string city = 4; //发帖的时候所在城市
  uint32 attitude_time = 5; //点赞次数
  uint32 comment_time = 6; //评论次数
  uint64 post_create_at = 7; //帖子创建时间
  uint32 rec_type = 8; // 1是同城非同城召回，2是高互动召回

  float std_score = 9; //高互动标准化分数
  float time_factor_score = 10; //时间衰减分数
  float reg_score = 11; //用户注册时间
  float hi_score = 12; //高互动池的召回分数
  uint64 author_reg_time = 13; //贴主注册时间

  float topic_score = 14; //话题分

  uint32 reply_count = 15; //楼主回复数
  float reply_score = 16; //回复分
  float bad_label_score = 17; // 不良标签的分

  float operator_topic_score = 18; //运营设置话题提权分，多个话题取最高，没有话题默认1
  float operator_post_score = 19; //运营设置单个帖子提权分

  string meta_id = 20; //元数据ID，以短横线拼接，如：aaa-bbb-ccc-xxxxx
  
  repeated string tag_name_list = 21; //帖子所带标签
}

message GetSystemRecommendStreamReq {
  uint32 uid = 1;
  uint32 limit = 2;
  bool bottom_flush = 3; // 是否到达底部然后下拉刷新，true为下拉刷新

  rcmd.common.RcmdBrowseInfo browse_list = 4; //请求列表曝光信息
  uint32 client_ip = 5;
  repeated string page_tag_id_list = 6; //用户新选的兴趣ID列表
}

message GetSystemRecommendStreamResp {
  repeated SystemRecommendStreamItem posts = 1;
  bool bottom_reached = 2;
}

message ProfileHighInteractPoolReq{
  uint32 age_Layer = 1; //预留字段，没什么用
  bool is_man = 2; //是否是男性请求用户
  uint32 uid = 3; //请求用户uid
}

message ProfileHighInteractPoolRsp{
  repeated SystemRecommendStreamItem posts = 1;
  uint64 last_update_time = 2;
}


message GetCityRecommendStreamReq {
  uint32 uid = 1;
  string geo_topic_id = 2;

  string post_id = 3; // 因为lxr理解错需求，加的字段，没用到的字段
  uint32 limit = 4;
  bool bottom_flush = 5; //是否底部刷新，先预留
}

message CityRecommendStream {
  string post_id = 1;

  uint32 age_layer = 2; //发帖的时候，作者的年龄圈
  uint32 sex = 3; //发帖的时候，作者性别
  string city = 4; //发帖的时候所在城市

  float cityScore = 5; //城市流计算过程中的各种分数
  float interactScore = 6;
  float hotScore = 7;
  float sexScore = 8;
  float ageScore = 9;

  uint64 post_create_at = 10; //帖子创建时间
  uint32 rec_type = 11; // 1是城市流召回，2是兜底召回

  uint32 attitude_time = 12; //点赞次数
  uint32 comment_time = 13; //评论次数
}

message GetCityRecommendStreamRsp{
  repeated CityRecommendStream posts = 1;
  bool bottom_reached = 2;
}

message GetTopicRecommendStreamReq{
  uint32 uid = 1;

  string post_id = 2;  // 因为lxr理解错需求，加的字段，没用到的字段
  string topic_id = 3; //话题

  uint32 limit = 4;
  bool bottom_flush = 5; // 是否到达底部然后下拉刷新，true为下拉刷新

  bool is_debug = 6; //加个调试参数，如果是调试的话，会返回中间过程分

}

message TopicRecommendStream{
  string post_id = 1;
  uint32 age_layer = 2; //发帖的时候，作者的年龄
  uint32 sex = 3; //发帖的时候，作者性别
  string city = 4; //发帖的时候所在城市

  float topicScore = 5; //城市流计算过程中的各种分数
  float interactScore = 6;
  float hotScore = 7;
  float sexScore = 8;
  float ageScore = 9;

  uint64 post_create_at = 10; //帖子创建时间
  uint32 rec_type = 11; // 1是城市流召回，2是兜底召回

  uint32 attitude_time = 12; //点赞次数
  uint32 comment_time = 13; //评论次数
  float origin_score = 14; //原始分
  float weight_score = 15; //提权之后的分 = 原始分*提权分
  float score_factor = 16; //提权分

}

message GetTopicRecommendStreamRsp{
  repeated TopicRecommendStream posts = 1;
  bool bottom_reached = 2;

}

//推荐流redis存储用的pb
message PostItem{
  string post_id = 1; //动态ID
  uint32 age_layer = 2; //年龄层  //这三个属性主要是在用户属性池里面用到
  uint32 sex = 3; //性别
  string city = 4; //城市

  uint32 attitude_time = 5; //点赞次数  //这两个字段主要是在高互动池里面用到
  uint32 comment_time = 6; //评论次数

  uint64 post_create_at = 7;  //发贴时间  //这两个字段用于过滤
  uint32 author_uid = 8; //发帖人uid

  float std_score = 9; // 这两个主要是算分数用的
  float time_factor_score = 10;

  //城市流
  bool delete = 11;   // 是否被删除  city pool add
  float sort_score = 12; //城市或者是话题流的排序分


  float interact_score = 13;
  float hot_score = 14;
  float sex_score = 15;
  float age_score = 16;
  float city_score = 17;

  string geo_topic_id = 18;  //地理位置
  string topic_id = 19;  //话题流

  uint32 author_reg_time = 20; //贴主注册时间

  repeated string diy_topic_ids = 21; //个人话题列表
  repeated string manual_topic_ids = 22; //运营手动添加的话题列表

}

message  HighInteractProductItem {
  string post_id = 1;
  bool attitude = 2;
  bool comment = 3;
  uint64 create_at = 4;
  uint32 author_uid = 5;
}


message PostExtraInfo {
  string post_id = 1;
  string ip_city = 2;
  uint32 author_uid = 3;
  uint64 post_create_at = 4;
  string geo_topic_id = 5;
  string topic_id = 6;

  uint64 author_reg_time = 7;
  uint32 sex = 8;

  uint32 reply_count = 9;  //楼主回复数
  float  bad_label_score = 10; //是否不良标签的降权分
  uint64 bad_label_update_time = 11; //不良标签的修改时间
  repeated string diy_topic_ids = 12; //个人话题列表
  repeated string manual_topic_ids = 13; //运营手动添加的话题列表
}

//上报10min发布的动态数 //待确认
enum HiReportType {
  WrongType = 0 ; //待定
  NewType = 1; //10min发互动的动态 +

  InteractType = 2; //有互动过
  EnterType = 3; //进入高互动池的动态数,只有top 30%才进入高互动池
  TotalType = 4; //高互动动态总数

  ExpireType = 5; // 3天被搞出去的 -
  EliminateType = 6; // 2%被淘汰的 -

}

message Report {
  uint32 hi_report_type = 1;
  repeated string post_list = 2;
  uint32 len = 3;

  uint64 time = 4; //发送的时间
}

message GetTopicStreamPostListReq{
  uint32 type = 1; //rcmd_operating_platform.proto 的 TopicStreamType,话题流里面的类型，新贴还是热贴
  string topic = 2; //某个话题
  repeated string post_id_list = 3; //帖子列表（暂时不用到）
  uint32 offset = 4; //分页参数
  uint32 limit = 5;
  bool sort_by_algo = 6; //是否算法排序
}

message GetTopicStreamPostListRsp{
  message TopicStreamPost {
    string post_id = 1;  // 动态id
    float origin_score = 2;  // 原始规则分
    float weight_score = 3;  // 提权之后分
    float weight_score_position = 4; // 提权之后分top占比
    float score_factor = 5; //提权分
    float topic_algo_score = 6; //算法离线计算打分
  }
  repeated TopicStreamPost post_list = 1;
  uint32 total_count = 2; // 池子总数
}

/*
这里的逻辑是，查询高互动帖子, 如果高互动帖子同时又有话题，则再去查话题推荐池中这个帖子的话题推荐打分。
 */
message GetHighInteractPostListReq {
  repeated string post_id_list = 1;  // 按post id查询是否属于高互动帖子, 如果非高互动贴, 返回的结构除了post_id 其他都为0
  uint32 offset = 2; // 分页返回高互动帖子, 以推荐分从大到小排序
  uint32 limit = 3;  // 返回条数
}

message GetHighInteractPostListRsp {
  message HighIteractPost {
    PostItem post = 1;
    float system_recommend_stream_ranking_position = 2;  // 推荐流推荐分排名占比
    float topic_recommend_stream_ranking_position = 3;   // 话题流推荐排名占比
  }
  repeated HighIteractPost post_list = 1;
  uint32 total_count = 4; // 高互动帖总数
}


message GetVoiceControlStreamReq {
  uint32 uid = 1;
  uint32 limit = 2;
  uint32 client_ip = 3;
  bool bottom_flush = 4; // 是否到达底部然后下拉刷新，true为下拉刷新
}

message VoiceControlStreamItem {
  string post_id = 1;
  string meta_id = 2;  //元数据ID，也作trace id用，用于追踪整个推荐链路，需要返回给客户端，客户端上报到数仓
}

message GetVoiceControlStreamRsp {
  repeated VoiceControlStreamItem item_list = 1;
  bool bottom_reached = 2;
}

message CheckInVoiceControlWhiteReq {
  uint32 uid = 1;
}

message CheckInVoiceControlWhiteRsp {
  bool in_white = 1;
}

message GetTabTopicStreamReq {
  uint32 uid = 1;
  uint32 limit = 2;
  uint32 client_ip = 3;
  repeated string topic_list = 4;
  bool bottom_flush = 5; // 是否到达底部然后下拉刷新，true为下拉刷新
  string tab_name = 6; // tab名称
}

message RspPostItem {
  string post_id = 1;
  string meta_id = 2;  //元数据ID，也作trace id用，用于追踪整个推荐链路，需要返回给客户端，客户端上报到数仓
}

message GetTabTopicStreamRsp {
  repeated RspPostItem item_list = 1;
  bool bottom_reached = 2;
}

message NotifyPostExposeReq {
  uint32 uid = 1;
  repeated string post_id_list = 2;
  string page_id = 3;
}

message NotifyPostExposeRsp {

}

message CheckPostIsBoutiqueReq {
  uint32 uid = 1;       //请求用户
  repeated string post_id_list = 2; //帖子ID
}

message PostIsBoutiqueData {
  string post_id = 1;
  bool is_boutique = 2;
  repeated string boutique_pool = 3; //预留，帖子属于哪些精品池
}

message CheckPostIsBoutiqueRsp {
  repeated PostIsBoutiqueData boutique_data = 1;
}

/*用户兴趣内容选择*/
enum PageTagType{
  PAGE_TAG_TYPE_UNSPECIFIED = 0;
  PAGE_TAG_TYPE_NEWBIE_PAGE = 10; // 新用户承接页的标签
  PAGE_TAG_TYPE_UGC_PAGE = 0x10; // 广场标签，废弃
  PAGE_TAG_TYPE_UGC_PAGE_NEW = 100; // 广场标签：广场独有的兴趣标签，用户层面用作场景
  PAGE_TAG_TYPE_CHANNEL_PAGE = 1000; // 首页标签：首页房间独有的兴趣标签，用户层面用作场景
  PAGE_TAG_TYPE_COMMON_PAGE = 10000; // 广场和首页共用标签：广场兴趣标签与首页房间标签统一共用的部分
}

message GetUserPageTagListReq{
    uint32 uid = 1;
    int32 page_tag_type_bit = 2; // PageTagType bit
    uint32 tag_config_version = 3; // 标签配置版本号
}

message GetUserPageTagListResp{
    repeated string page_tag_id_list = 1;
    uint32 tag_config_version = 2; // 标签配置版本号
}

message GetSameCityStreamV2Req {
  uint32 uid = 1;
  // string location = 2; // 不是全称不用传
  bool bottom_flush = 3; // true为下拉刷新
  uint32 client_ip = 4;
  bool is_user_location_auth_open = 5; // 用户定位授权是否开启
}

message GetSameCityStreamV2Resp {
  repeated RspPostItem item_list = 1;
  bool bottom_reached = 2;
}

message CheckJumpToSquareReq {
  uint32 uid = 1;
}

message CheckJumpToSquareResp {
  bool need_jump = 1;
}