// Protocol Buffers - Google's data interchange format
// Copyright 2008 Google Inc.  All rights reserved.
// https://developers.google.com/protocol-buffers/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//
// Test schema for proto2 messages.  This test schema is used by:
//
// - conformance tests
//

syntax = "proto2";

package protobuf_test_messages.proto2;
option java_package = "com.google.protobuf_test_messages.proto2";

// This is the default, but we specify it here explicitly.
option optimize_for = SPEED;

option cc_enable_arenas = true;

// This proto includes every type of field in both singular and repeated
// forms.
//
// Also, crucially, all messages and enums in this file are eventually
// submessages of this message.  So for example, a fuzz test of TestAllTypes
// could trigger bugs that occur in any message type in this file.  We verify
// this stays true in a unit test.
message TestAllTypesProto2 {
  message NestedMessage {
    optional int32 a = 1;
    optional TestAllTypesProto2 corecursive = 2;
  }

  enum NestedEnum {
    FOO = 0;
    BAR = 1;
    BAZ = 2;
    NEG = -1;  // Intentionally negative.
  }

  // Singular
  optional int32 optional_int32    =  1;
  optional int64 optional_int64    =  2;
  optional uint32 optional_uint32   =  3;
  optional uint64 optional_uint64   =  4;
  optional sint32 optional_sint32   =  5;
  optional sint64 optional_sint64   =  6;
  optional fixed32 optional_fixed32  =  7;
  optional fixed64 optional_fixed64  =  8;
  optional sfixed32 optional_sfixed32 =  9;
  optional sfixed64 optional_sfixed64 = 10;
  optional float optional_float    = 11;
  optional double optional_double   = 12;
  optional bool optional_bool     = 13;
  optional string optional_string   = 14;
  optional bytes optional_bytes    = 15;

  optional NestedMessage                        optional_nested_message  = 18;
  optional ForeignMessageProto2                 optional_foreign_message = 19;

  optional NestedEnum                           optional_nested_enum     = 21;
  optional ForeignEnumProto2                    optional_foreign_enum    = 22;

  optional string optional_string_piece = 24 [ctype=STRING_PIECE];
  optional string optional_cord = 25 [ctype=CORD];

  optional TestAllTypesProto2 recursive_message = 27;

  // Repeated
  repeated    int32 repeated_int32    = 31;
  repeated    int64 repeated_int64    = 32;
  repeated   uint32 repeated_uint32   = 33;
  repeated   uint64 repeated_uint64   = 34;
  repeated   sint32 repeated_sint32   = 35;
  repeated   sint64 repeated_sint64   = 36;
  repeated  fixed32 repeated_fixed32  = 37;
  repeated  fixed64 repeated_fixed64  = 38;
  repeated sfixed32 repeated_sfixed32 = 39;
  repeated sfixed64 repeated_sfixed64 = 40;
  repeated    float repeated_float    = 41;
  repeated   double repeated_double   = 42;
  repeated     bool repeated_bool     = 43;
  repeated   string repeated_string   = 44;
  repeated    bytes repeated_bytes    = 45;

  repeated NestedMessage       repeated_nested_message  = 48;
  repeated ForeignMessageProto2      repeated_foreign_message = 49;

  repeated NestedEnum          repeated_nested_enum     = 51;
  repeated ForeignEnumProto2         repeated_foreign_enum    = 52;

  repeated string repeated_string_piece = 54 [ctype=STRING_PIECE];
  repeated string repeated_cord = 55 [ctype=CORD];

  // Map
  map <   int32, int32>    map_int32_int32 = 56;
  map <   int64, int64>    map_int64_int64 = 57;
  map <  uint32, uint32>   map_uint32_uint32 = 58;
  map <  uint64, uint64>   map_uint64_uint64 = 59;
  map <  sint32, sint32>   map_sint32_sint32 = 60;
  map <  sint64, sint64>   map_sint64_sint64 = 61;
  map < fixed32, fixed32>  map_fixed32_fixed32 = 62;
  map < fixed64, fixed64>  map_fixed64_fixed64 = 63;
  map <sfixed32, sfixed32> map_sfixed32_sfixed32 = 64;
  map <sfixed64, sfixed64> map_sfixed64_sfixed64 = 65;
  map <   int32, float>    map_int32_float = 66;
  map <   int32, double>   map_int32_double = 67;
  map <    bool, bool>     map_bool_bool = 68;
  map <  string, string>   map_string_string = 69;
  map <  string, bytes>    map_string_bytes = 70;
  map <  string, NestedMessage>  map_string_nested_message = 71;
  map <  string, ForeignMessageProto2> map_string_foreign_message = 72;
  map <  string, NestedEnum>     map_string_nested_enum = 73;
  map <  string, ForeignEnumProto2>    map_string_foreign_enum = 74;

  oneof oneof_field {
    uint32 oneof_uint32 = 111;
    NestedMessage oneof_nested_message = 112;
    string oneof_string = 113;
    bytes oneof_bytes = 114;
    bool oneof_bool = 115;
    uint64 oneof_uint64 = 116;
    float oneof_float = 117;
    double oneof_double = 118;
    NestedEnum oneof_enum = 119;
  }

  // extensions
  extensions 120 to 200;

  // groups
  optional group Data = 201 {
    optional int32 group_int32 = 202;
    optional uint32 group_uint32 = 203;
  };

  // Test field-name-to-JSON-name convention.
  // (protobuf says names can be any valid C/C++ identifier.)
  optional int32 fieldname1 = 401;
  optional int32 field_name2 = 402;
  optional int32 _field_name3 = 403;
  optional int32 field__name4_ = 404;
  optional int32 field0name5 = 405;
  optional int32 field_0_name6 = 406;
  optional int32 fieldName7 = 407;
  optional int32 FieldName8 = 408;
  optional int32 field_Name9 = 409;
  optional int32 Field_Name10 = 410;
  optional int32 FIELD_NAME11 = 411;
  optional int32 FIELD_name12 = 412;
  optional int32 __field_name13 = 413;
  optional int32 __Field_name14 = 414;
  optional int32 field__name15 = 415;
  optional int32 field__Name16 = 416;
  optional int32 field_name17__ = 417;
  optional int32 Field_name18__ = 418;

  // message_set test case.
  message MessageSetCorrect {
    option message_set_wire_format = true;
    extensions 4 to max;
  }

  message MessageSetCorrectExtension1 {
    extend MessageSetCorrect {
      optional MessageSetCorrectExtension1 message_set_extension = 1547769;
    }
    optional string str = 25;
  }

  message MessageSetCorrectExtension2 {
    extend MessageSetCorrect {
      optional MessageSetCorrectExtension2 message_set_extension = 4135312;
    }
    optional int32 i = 9;
  }
}

message ForeignMessageProto2 {
  optional int32 c = 1;
}

enum ForeignEnumProto2 {
  FOREIGN_FOO = 0;
  FOREIGN_BAR = 1;
  FOREIGN_BAZ = 2;
}

extend TestAllTypesProto2 {
  optional int32 extension_int32 = 120;
}
