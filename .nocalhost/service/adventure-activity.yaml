- name: adventure-activity # 服务deploy名称
  serviceType: deployment
  containers:
    - name: service
      dev:
        ${_INCLUDE_:- ../include/service_dev_config_base.yaml | nindent 8}
        command:
          run: # run参数
            - go run ./services/adventure-activity/main.go
            - --server.configFile=/config/service-config.json
            - --server.grpcListen=:80
            - --server.logLevel=debug
          debug:
            - debug
            - ./services/adventure-activity/main.go
            - -- --server.configFile=/config/service-config.json
            - --server.grpcListen=:80
            - --server.logLevel=debug
        hotReload: false
        sync:
          mode: "pattern"
          type: "send"
          filePattern:
            ${_INCLUDE_:- ../include/common_deps.yaml | nindent 12}
            - "services/adventure-activity" # 服务代码目录
            - "services/helper-from-cpp"
            - "services/notify"
            - "services/risk-control/backpack-sender/utils"
            - "services/tt-rev/common/goroutineex"
            - "clients"
            - "protocol"
            - "pkg"
            - "go.mod"
            - "go.sum"
          ignoreFilePattern: [ ]
        portForward: []