- name: guild-management-http-logic # 服务deploy名称
  serviceType: deployment
  containers:
    - name: service
      dev:
        ${_INCLUDE_:- ../include/service_dev_config_base.yaml | nindent 8}
        command:
          run: # run参数
            - go run ./services/guild-management/guild-management-http-logic/main.go
            - --config=/config/guild-management-http-logic.json
          debug:
            - dlv # debug参数
            - --headless
            - --log
            - --listen :9999
            - --api-version 2
            - --accept-multiclient
            - debug
            - ./services/guild-management/guild-management-http-logic/main.go
            - --config=/config/guild-management-http-logic.json
        hotReload: false
        sync:
          mode: "pattern"
          type: "send"
          filePattern:
            - "services/guild-management/guild-management-http-logic" # 服务代码目录
            - "services/anchor-contract/sign-anchor-stats"
            - "services/recommend-dialog"
            - "protocol"
            - "pkg"
            - "go.mod"
            - "go.sum"
            - "clients"
          ignoreFilePattern: [ ]
        portForward: []