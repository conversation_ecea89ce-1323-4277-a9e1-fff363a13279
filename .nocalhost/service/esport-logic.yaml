- name: esport-logic # 服务deploy名称
  serviceType: deployment
  containers:
    - name: service
      dev:
        ${_INCLUDE_:- ../include/service_dev_config_base.yaml | nindent 8}
        command:
          run: # run参数
            - go run ./services/tt-rev/esport/esport-logic/main.go
            - --server.configFile=/config/esport-logic.json
            - --server.grpcListen=:80
            - --server.logLevel=debug
          debug: # debug参数
            ${_INCLUDE_:- ../include/debug_args.yaml | nindent 12}
            - ./services/tt-rev/esport/esport-logic/main.go
            - -- --server.configFile=/config/esport-logic.json
            - --server.grpcListen=:80
            - --server.logLevel=debug
        hotReload: false
        sync:
          mode: "pattern"
          type: "send"
          filePattern:
            ${_INCLUDE_:- ../include/common_deps.yaml | nindent 12}
            - "services/tt-rev/esport/esport-logic" # 服务代码目录
            - "services/tt-rev/esport/common"
            - "services/tt-rev/common"
            - "services/recommend-dialog"
            - "services/tt-rev/offer-room-common/interceptor"
            - "services/notify"
            - "services/helper-from-cpp/immsghelper"
            - "services/runtime/v2"
            - "services/logic-grpc-gateway/gwcontext"
            - "services/helper-from-cpp/accounthelper"  
            - "services/helper-from-cpp/notifyhelper"
            - "services/logic-grpc-gateway/logicproto"
          ignoreFilePattern: [ ]
        portForward: []