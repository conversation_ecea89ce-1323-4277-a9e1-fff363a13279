syntax = "proto3";

package ga.channel_core;

import "ga_base.proto";
import "channel/channel_.proto";
import "channel_audio_token/channel-audio-token_.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/channel_core";


message ChannelEnterRequest {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    string passwd = 3;                 // 密码
    uint32 follow_friend_uid = 4;      // 如果是跟随某人进房间 这里填写跟随的目标用户的UID
    string knock_door_sign = 5;        // 敲门进房带的加密签名信息
    string channel_view_id = 6;        // 新的房间显示id
    uint32 enter_source = 7;           // see channel_.proto -> EChannelEnterSource
    string enter_source_second = 8;    // 二级子来源
    string enter_source_trace_id = 9;  // 进房来源追踪id，用于推荐业务等
    string token = 10;                 // 进房通用token，有带该token，则进房服务会校验该token是否合法
}

message ChannelEnterResponse {
    ga.BaseResp base_resp = 1;
    ga.channel.ChannelDetailInfo channel_info = 2;            // 此时返回的房间详细信息里 创建者的信息包括昵称和帐号信息均为空, channel_.proto -> ChannelDetailInfo
    uint32 server_time = 4;                           // 32bit 秒级 服务器时间
    uint32 enter_source = 5;                          // see channel_.proto -> EChannelEnterSource
    ga.channel.ChannelLayoutInfo layout_info = 6;             // see channel_.proto -> ChannelLayoutInfo
    ga.channel.ChannelMicAudioInfo mic_audio_info = 7;        // see channel_.proto -> ChannelMicAudioInfo
    PushProxyInfo push_proxy_info = 8;                // 推送通道信息
    AudioTokenInfo audio_info = 9;                    // 音频token信息
    ga.channel.UKWEnterUserInfo ukw_enter_user_info = 10;     // 非神秘人信息为空, see channel_.proto -> UKWEnterUserInfo
    bool recommend_channel = 11;                      // 房间是否为推荐房，历史遗留，客户端有些麦位根据这个判断展示了不同的样式
    uint32 channel_msg_cur_seq_id = 12;
    uint32 mic_layout_template_id = 13;               // 麦位布局模板id
}

message PushProxyInfo {
    repeated ga.channel.GRPCPushProxy grpc_push_proxies = 1;  // 有值则启用GRPC独立推送通道, see channel_.proto -> GRPCPushProxy
    bool push_token_switch = 2;                       // true才进行token校验
}

message AudioTokenInfo {
    bool audio_token_switch = 1;                      // true才进行token校验
    ga.channel_audio_token.AudioToken audio_token = 2;                    // 音频token, see channel-audio-token_.proto -> AudioToken
}
