syntax="proto2";

package ga.game_card;

import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/game-card";

message ModifyGameCardOpt{
    required string opt_name = 1;
    required uint32 opt_id = 2;
    repeated string value_list = 3;
}
//ugc房间里修改游戏卡
message ModifyGameCardInChannelReq{
    required BaseReq base_req        = 1;
    required uint32 game_card_id     = 2;       // 对应旧服务usertag的tag_id (这个game_card_id应该只用于游戏卡业务内)
    required string game_card_name   = 3;       // 对应旧服务usertag的tag_name
    required uint32 u_game_id        = 4;       // 全局唯一的game_id,和游戏相关的业务通过这个u_game_id进行关联
    repeated ModifyGameCardOpt opt_list    = 5;
}

message ModifyGameCardInChannelResp
{
    required BaseResp base_resp = 1;
}


//游戏卡改版重构----------------------------------------------------------------------------------------------
enum GameCardType {
    CARD_TYPE_INVALID        = 0 ;    //无效
    CARD_TYPE_GAME           = 1;     //游戏卡
    CARD_TYPE_MUSIC          = 2;     //音乐卡
}

enum CardScopeType {
    // 普通特定玩法兴趣卡(历史默认)
    CARD_SCORE_TYPE_NORMAL           = 0;
    // 聚类卡
    CARD_SCORE_TYPE_CATEGORY         = 1;
}

//段位选项配置
message GameCardLevelConf{
    required string level_name              = 1 ;
    required string level_img_url           = 2 ;
    required string level_img_url_mic       = 3 ;
}
//带属性的选项配置
message GameCardOptWithPropConf{
    required string          prop                  = 1;
    repeated string          value_list             = 2;
}
enum GameCardOptType {
    GAME_CARD_OPT_INVALID = 0 ;   //无效
    GAME_CARD_OPT_COMMON = 1;     //普通的一级选项
    GAME_CARD_OPT_LEVEL = 2;  //段位选项 GameCardLevelConf
    GAME_CARD_OPT_WITH_PROP = 3;       //带属性的选项 GameCardOptWithPropConf
    GAME_CARD_OPT_INPUT = 4;       // 用户可填写类型模版
}

// 新增的数值类型的配置
message GameCardInputConf {
    optional string title      = 1; // 展示名称
    optional uint32 min_num       = 2;
    optional uint32 max_num       = 3;
}

message GameCardOptConf
{
    required string opt_name = 1;
    required uint32 opt_id = 2;
    required uint32 max_set_cnt = 3;
    required bool is_must          = 4;       //是否是必填项
    required uint32 text_box_style  = 5;     //客户端展示的样式:0斜杠 1带方框
    required bool display_in_channel = 6 ;   //在房间中展示
    required GameCardOptType opt_type = 7 ;  //选项类型，参考GameCardOptType,不同类型对应不同的value_list
    repeated string opt_conf_value_list     = 8;
    repeated GameCardOptWithPropConf opt_with_prop_value_list = 9;
    repeated GameCardLevelConf level_conf_value_list = 10;
    repeated GameCardInputConf input_conf_value_list = 11;  // 数值类型模版，目前战力评分字段使用
}
message GameCardConfInfo {
    required uint32 game_card_id          = 1;            // 对应旧服务usertag的tag_id
    required string game_card_name        = 2;       // 对应旧服务usertag的tag_name
    required uint32 u_game_id            = 3;       // 全局唯一的game_id,和游戏相关的业务通过这个u_game_id进行关联
    required bool has_nick_name  = 4 ;        //是否有游戏昵称
    required bool has_screenshot = 5 ;        //是否有游戏截图
    required string game_thumb_img_url = 6 ;                  //游戏缩略图
    required string game_back_img_url = 7 ;                      //新版的背景图(大尺寸)
    required string game_icon_img_url = 8 ;                      //新建游戏卡时的小icon图片
    required string game_corner_mark_img_url = 9;               //游戏卡角标图
    required string game_back_img_mini_url = 10;                //新版的背景图(小尺寸)
    required string default_game_level_img_url = 11;     //默认的段位图，没有选择段位时用的图
    required uint32 game_back_color_num = 12 ;               //背景底色值
    repeated GameCardOptConf opt_conf_list  = 13 ;  //游戏卡选择配置列表
    required uint32 card_type      = 14;          //卡片类型，之后游戏卡会演变成兴趣卡,参考GameCardType
    required RegisterJumpPage register_jump_page  = 15;  // 注册选择游戏卡后，客户端选择跳转页面
    optional bool show_team_num = 16;  // 是否展示组队数据
    optional string show_team_text = 17;   // 展示组队数据文本，{num}+text
    repeated string accounts = 18; // 返回用户账号，客户端取用户头像
    repeated UGameInfo u_game_info = 19;  // 新绑定的游戏数据
    repeated NickNameType nickname_type_list = 20; // 游戏昵称类型列表
    required bool has_quick_match_entrance = 21;  // 是否需要在专区展示快速匹配入口
    // 卡片范围类型 see CardScopeType, 6.49添加
    optional uint32 card_scope_type = 22;
    // 类型为聚合类目类型的需要额外数据给客户端，6.49添加
    optional CategoryGameCardConfInfo category_game_card_conf_info = 23;
}

message CategoryGameCardConfInfo {
    // 类目id
    optional uint32 category_id = 1;
    // 需要应用该聚类的游戏卡的玩法，这些玩法创建游戏卡的时候就使用通用的这个游戏卡模版配置
    repeated SimpleTabInfo tab_info = 2;
}

message SimpleTabInfo {
    optional uint32 tab_id = 1;
    optional string tab_name = 2;
    optional uint32 game_card_id = 3; // 组装后的游戏卡id
    optional string game_corner_mark_img_url = 4; // 该玩法对应的游戏卡角标图
    optional uint32 u_game_id = 5; // 每个定制玩法对应绑定的游戏id
}

message NickNameType {
    optional uint32 type_id = 1; // 昵称类型id
    optional string type_name = 2; // 昵称类型
}

message UGameInfo {
    optional uint32 u_game_id = 1;
    optional string u_game_name = 2; // u_game_id对应游戏名
}

enum RegisterJumpPage {
    HomePage   = 0; // 主页
    QuickMatch = 1; // 快速匹配
}

//1.游戏卡配置
message GetAllGameCardConfReq{
    required BaseReq base_req        = 1;
    optional bool is_show_input_opt = 2; // 是否展示可输入类型选项数据， 6.40版本加入
}
message GetAllGameCardConfResp{
    required BaseResp base_resp = 1;
    repeated GameCardConfInfo game_card_conf_list = 2;
    optional uint32 max_game_card_num = 3 ;     //最大的游戏卡数
}

enum EScreenShotAuditStatus{
    SCREEN_SHOT_AUDITING = 0 ;             //审核中
    SCREEN_SHOT_AUDIT_REJECT = 1 ;         //不通过
    SCREEN_SHOT_AUDIT_PASS =2 ;            //通过
}

message GameScreenshot {
    required uint32 audit_status = 1;           //图片审核状态，参考EScreenShotAuditStatus
    required string img_url = 2;                //
    required uint32 index = 3 ;                 //游戏截图索引(坑位)，从0开始
    optional bool   new_obs = 4;                //是否用新的obs
}
message GameCardOpt
{
    required string opt_name = 1;               //段位,区服等字段
    required uint32 opt_id = 2;                 //
    repeated string value_list = 3;             // 选项属性，例如段位会有:青铜，白银，王者等
    optional GameCardOptType opt_type = 4 ;  //选项类型，看 GameCardOptType
    repeated GameCardInputVal input_val = 5;    // 输入值
}
message GameCardInfo{
    required uint32 game_card_id     = 1;       // 对应旧服务usertag的tag_id (这个game_card_id应该只用于游戏卡业务内)
    required string game_card_name        = 2;       // 对应旧服务usertag的tag_name
    required uint32 u_game_id        = 3;       // 全局唯一的game_id,和游戏相关的业务通过这个u_game_id进行关联
    required string game_nickname        = 4;             //游戏昵称
    repeated GameScreenshot screenshot_list = 5; //游戏截图
    repeated GameCardOpt    opt_list  = 6 ;
    optional GameCardConfInfo conf = 7 ;
    repeated GameNickNameInfo game_nickname_list = 8;    // 游戏昵称列表\
    optional int64 update_time = 9; // 更新时间
}

message GameCardInputVal {
    optional string elem_title = 1; // 标题
    optional string elem_val      = 2; // 输入值
}

message GameNickNameInfo {
    required NickNameType nickname_type = 1;
    required string nickname = 2;
}

//2.get
message GetGameCardReq{
    required BaseReq base_req = 1;
    required uint32 target_uid = 2;   // 要获取谁的标签列表，如果是获取自己的 那么填自己的UID
    optional bool is_show_input_opt = 3; // 是否展示可输入类型选项数据， 6.40版本加入
}
message GetGameCardResp{
    required BaseResp base_resp = 1;
    required uint32 target_uid = 2;
    repeated GameCardInfo game_card_list = 3;
}

//3.创建游戏卡
message CreateGameCardReq{
    required BaseReq base_req = 1;
    required GameCardInfo game_card = 2;
}
message CreateGameCardResp{
    required BaseResp base_resp = 1;
}

//4.修改游戏卡,非覆盖式，只需填改了的那个字段(例如改了段位则只需填段位，改了昵称或截图则只需填昵称或截图),以及是否删除了昵称或截图
message SetGameCardReq{
    required BaseReq base_req = 1;
    required GameCardInfo game_card = 2 ;
    required bool del_game_nick = 3;                 //是否删除了游戏昵称
}
message SetGameCardResp{
    required BaseResp base_resp = 1;
}

//5.删除游戏卡
message DeleteGameCardReq{
    required BaseReq base_req = 1;
    required uint32 game_card_id = 2;
}
message DeleteGameCardResp{
    required BaseResp base_resp = 1;
}

//6.注册阶段创建的游戏卡
message CreateGameCardInRegisterReq{
    required BaseReq base_req = 1;
    repeated GameCardInfo game_card_list = 2;
    optional string birth_day = 3;                 //把注册时填的生日也放在这里吧，客户端不用再单独调一次接口
}
message CreateGameCardInRegisterResp{
    required BaseResp base_resp = 1;
}

message GetGameCardByTabReq{
    required BaseReq base_req = 1;
    optional uint32 tab_id = 2;
    optional bool is_show_input_opt = 3; // 是否展示可输入类型选项数据， 6.40版本加入
}
message GetGameCardByTabResp{
    required BaseResp base_resp = 1;
    optional GameCardInfo game_card_list = 2;
    optional string tab_name = 3; // 玩法名称
    optional string cards_image_url = 4; // 切换房间配图
    // 新增PC极速版大厅房间背景图
    optional string fast_pc_room_background_img_url = 5;
}

enum GameCardFilterEntrance{
    UNKNOWN_ENTRANCE = 0;
    HOME_PAGE = 1; // 首页
    GAME_ZONE = 2; // 游戏专区
    MINIGAME_ZONE = 3; // 小游戏专区
}
message GetGameCardConfReq{
    required BaseReq base_req = 1;
    required GameCardFilterEntrance filter_entrance = 2;
    optional bool is_show_input_opt = 3; // 是否展示可输入类型选项数据， 6.40版本加入
}

message GetGameCardConfResp{
    required BaseResp base_resp = 1;
    repeated GameCardConfInfo game_card_conf_list = 2;  // 只返回游戏专区开黑下的游戏卡
    optional uint32 cur_game_card_num = 3 ;     // 用户当前游戏卡数
    optional bool show_recent_play = 4 ;     //  是否展示最近玩什么游戏弹窗，判断逻辑用户游戏类的游戏卡数量是否为0
    optional uint32 max_game_card_num = 5 ;     // 可选的最大的游戏卡数
}

message BatchCreateGameCardReq{
    required BaseReq base_req = 1;
    repeated GameCardInfo game_card = 2;
}
message BatchCreateGameCardResp{
    required BaseResp base_resp = 1;
}

message GetGameCardConfByTabIdsReq {
    required BaseReq base_req = 1;
    repeated uint32 tab_ids = 2;
    enum RequestSource {
        REQUEST_SOURCE_UNSPECIFIED = 0;
        REQUEST_SOURCE_IM_GAME_CARD = 1; // im发送昵称获取游戏卡配置
    }
    optional uint32 request_source = 3;
}

message GetGameCardConfByTabIdsResp {
    required BaseResp base_resp = 1;
    repeated NewGameCardConfInfo game_card_conf_list = 2;
    optional uint32 max_game_card_num = 3 ;     //最大的游戏卡数
}

message NewGameCardConfInfo {
    required uint32 game_card_id = 1;        // 对应旧服务usertag的tag_id
    required string game_card_name = 2;       // 对应旧服务usertag的tag_name
    required uint32 u_game_id = 3;       // 全局唯一的game_id,和游戏相关的业务通过这个u_game_id进行关联
    required uint32 tab_id = 4;
    optional string game_icon_img_url = 5;    //新建游戏卡时的小icon图片
}