version: v2
modules:
  - path: .
breaking:
  use:
    - FILE
  except:
    - EXTENSION_NO_DELETE
    - FIELD_SAME_DEFAULT
lint:
  use:
    - DEFAULT
  except:
    - FIELD_NOT_REQUIRED # 屏蔽message属性不存在 required
    - SERVICE_SUFFIX # 屏蔽服务名不符合服务后缀规则
    - ENUM_ZERO_VALUE_SUFFIX # 屏蔽枚举值为0的后缀规则
    - ENUM_VALUE_PREFIX # 屏蔽枚举值前缀规则
    - PACKAGE_VERSION_SUFFIX # 屏蔽包包含版本后缀
    - ENUM_VALUE_UPPER_SNAKE_CASE # 屏蔽枚举值不符合大写蛇形命名规则
    - RPC_REQUEST_STANDARD_NAME # 屏蔽rpc请求和响应消息不符合标准命名规则
    - RPC_RESPONSE_STANDARD_NAME # 屏蔽rpc请求和响应消息不符合标准命名规则
    - RPC_REQUEST_RESPONSE_UNIQUE # 屏蔽rpc请求和响应消息不唯一
    - PACKAGE_SAME_JAVA_PACKAGE # 屏蔽包名和java包名不一致
  rpc_allow_same_request_response: true
  # 自定义配置
  custom_options:
    reserve_package_prefix: ga
    reserve_request_fields:
      - "base_req"
    reserve_response_fields:
      - "base_resp"
  ignore:
    - sync/sync.proto
    - push/push_.proto
    - auth/auth.proto
    - channel/channel_.proto
    - channel/channel_opt_.proto
    - im/im.proto
    - guild/guild_.proto
    - guild/guild2_.proto
    - ugc/ugc_.proto
    - ga_base.proto
  ignore_only:
    FILE_LOWER_SNAKE_CASE:
      - abtest/abtest_.proto
      - activity/activity_.proto
      - activitypush/activity-push_.proto
      - ad_center/ad-center-logic_.proto
      - anchor_check_logic/anchor-check-logic_.proto
      - ancient_search/ancient-search_.proto
      - audit/audit_.proto
      - avatar_logic/avatar-logic_.proto
      - backpack/backpack_.proto
      - channel/channel_dating_game_.proto
      - channel/channel_personalization_.proto
      - channel/channel_trivia_game_.proto
      - channel/channelguild_.proto
      - channel/user_tag_.proto
      - channel_audio_token/channel-audio-token_.proto
      - channel_cp_game_logic/channel-cp-game-logic_.proto
      - channel_deeplink_recommend_logic/channel-deeplink-recommend-logic_.proto
      - channel_guide_logic/channel-guide-logic_.proto
      - channel_ktv/channel-ktv-logic_.proto
      - channel_ktv_heartbeat/channel-ktv-heartbeat-logic_.proto
      - channel_level/channel-level_.proto
      - channel_live_logic/channel-live-logic_.proto
      - channel_lottery/channel-lottery_.proto
      - channel_mini_game/channel-minigame-go-logic_.proto
      - channel_open_game/channel-open-game-logic_.proto
      - channel_open_game_controller/channel-open-game-controller-logic_.proto
      - channel_performance/channel-performance_.proto
      - channel_play/channel-play-view_.proto
      - channel_play/channel-play_.proto
      - channel_quality/channel-quality_.proto
      - channel_recommend_logic/channel_recommend_logic_.proto
      - channel_red_packet_logic/channel-red-packet-logic_.proto
      - channel_roleplay_logic/channel-roleplay-logic_.proto
      - channel_scheme/channel-scheme_.proto
      - channel_scheme/channel-scheme_push_.proto
      - channel_team/channel-team-logic_.proto
      - channelbackgroundlogic/channel_background_logic_.proto
      - channellisteningautoplaylogic/channel-listening-auto-play-logic_.proto
      - channellisteninglogic/channel-listening-logic_.proto
      - channelshiftstreamlogic/channel-shift-stream-logic_.proto
      - chatcardlogic/chat-card-logic_.proto
      - circle/circle2_.proto
      - circle/circle_.proto
      - client_conf_mgr/client-conf-mgr_.proto
      - clientgenpushlogic/client-gen-push_.proto
      - competition_entrance_logic/competition-entrance-logic_.proto
      - concert_logic/concert-logic_.proto
      - dark_gift_bonus_logic/dark-gift-bonus-logic_.proto
      - demo_hello_world_logic/demo-helloworld-logic.proto
      - emoji/emoji_.proto
      - eventreportlogic/event-report-logic_.proto
      - fellow_logic/fellow-logic_.proto
      - find_friends/find_friends_.proto
      - find_friends/findfriend_matching_.proto
      - game/game_.proto
      - game_card/game_card_.proto
      - game_screenshot_logic/game-screenshot-logic_.proto
      - game_server_v2/game-server-v2_.proto
      - game_tmp_channel/game-tmp-channel_.proto
      - gameradarlogic/gameradar-logic_.proto
      - giftpkg/giftpkg2_.proto
      - giftpkg/giftpkg_.proto
      - gnobilitylogic/gnobility-logic_.proto
      - group/group_.proto
      - group/tgroup_.proto
      - guild/guildcircle_.proto
      - guildhonorhallslogic/guild-honor-halls-logic_.proto
      - hello/hello_.proto
      - helloworld_logic/helloworldlogic_.proto
      - hobby_channel/hobby-channel-view_.proto
      - hobby_channel/hobby-channel_.proto
      - hotwordsearch/hotwordsearch_.proto
      - huntmonsterlogic/hunt-monster-logic_.proto
      - im_activity_center_logic/im-activity-center-logic_.proto
      - impromotelogic/im-promote-logic_.proto
      - interact_proxy_logic/interact-proxy-logic_.proto
      - interaction_intimacy/interaction-intimacy-logic_.proto
      - invitelogic/invitelogic_.proto
      - knightgrouplogic/knight-group-logic_.proto
      - knightprivilegelogic/knight-privilege-logic_.proto
      - knocklogic/knocklogic_.proto
      - lbs/lbs_.proto
      - levelup_present_logic/levelup-present-logic_.proto
      - magic_spirit_logic/magic-spirit-logic_.proto
      - masked_call/masked-call_.proto
      - masked_pk_logic/masked-pk-logic_.proto
      - masterapprenticelogic/master-apprentice-logic_.proto
      - melee_channel/melee-channel-logic_.proto
      - missiongologic/missiongo-logic_.proto
      - molebeatlogic/molebeat-logic_.proto
      - muse_post_logic/muse-post-logic_.proto
      - music_nest_logic/music-nest-logic_.proto
      - music_topic_channel/music-topic-channel-logic_.proto
      - mystery_box_logic/mystery-box-logic_.proto
      - mystery_place_logic/mystery-place-logic_.proto
      - nameplate/nameplate_.proto
      - numeric_logic/numeric-logic_.proto
      - oauth2logic/oauth2-logic_.proto
      - official_live_channel/official-live-channel_.proto
      - one_piece_logic/one-piece-logic_.proto
      - online/friendol_.proto
      - personalcertificationlogic/personal-certification-logic_.proto
      - pgc_channel_game_logic/pgc-channel-game-logic_.proto
      - pgc_channel_pk_logic/pgc-channel-pk-logic_.proto
      - pia/pia_.proto
      - playerlogic/playerlogic_.proto
      - present_go_logic/present-go-logic_.proto
      - profile/profile_.proto
      - push_logic/push-logic_.proto
      - rap_logic/rap-logic_.proto
      - rcmd/rcmd_.proto
      - recruit/recruit_.proto
      - redpacket/redpacket_.proto
      - revenuenameplatelogic/revenue-nameplate-logic_.proto
      - rhythm/rhythm_.proto
      - rush/rush_.proto
      - sample/sample_.proto
      - session/session_.proto
      - sing_a_round_logic/sing-a-round-logic_.proto
      - singing_hall_logic/singing-hall-logic_.proto
      - slipnotelogic/slip-note-logic_.proto
      - smash_egg/smash-egg-logic_.proto
      - smash_egg_notify/smash-egg-notify-logic_.proto
      - star_trek_logic/star-trek-logic_.proto
      - super_channel/super-channel-push_.proto
      - super_channel/super-channel_.proto
      - super_player_dress_logic/super-player-dress-logic_.proto
      - superplayerlogic/super-player-logic_.proto
      - tabnotify/tab-notify_.proto
      - tbeanlogic/tbean-logic_.proto
      - team/team_.proto
      - telcalllogic/tel-call-logic_.proto
      - topic_channel/topic_channel_.proto
      - treasure_box_logic/treasure-box-logic_.proto
      - tt_rev_channel_mode_mgr_logic/tt-rev-channel-mode-mgr-logic_.proto
      - udeskapilogic/udesk-api-logic_.proto
      - unclaimed/unclaimed_.proto
      - unified_interface/unified-interface_.proto
      - unified_search/unified-search_.proto
      - user_black_list_logic/user-black-list-logic_.proto
      - user_music_rank/user-music-rank-logic_.proto
      - user_tag_v2/user_tag_v2_.proto
      - user_visitor_record/user-visitor-record-logic_.proto
      - userblog/userblog_.proto
      - usercheckinlogic/user-checkin-logic_.proto
      - usercomplaint_logic/user-complaint-logic_.proto
      - userlogic/user-logic_.proto
      - userpresent/userpresent_.proto
      - userrecommend/userrecommend_.proto
      - usual_device_logic/usual-device-logic_.proto
      - wishlistlogic/wish-list-logic_.proto
      - youknowwhologic/you-know-who-logic_.proto