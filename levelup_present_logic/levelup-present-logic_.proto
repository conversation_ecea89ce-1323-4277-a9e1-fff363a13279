syntax = "proto3";

package ga.levelup_present_logic;

import "ga_base.proto";
import "userpresent/userpresent_.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/levelup-present-logic";

/////////////////////////////////////////////////////////GetAllLevelupPresentList
//获取全部的升级礼物配置（基础+批量）请求
message GetAllLevelUpPresentListReq {
  ga.BaseReq base_req = 1;
}

//批量礼物类型定义
message LevelUpPresentBatchData {
  uint32 batch_count = 1;	//批量数
  string effect_url = 2;	//zip压缩包
  string effect_md5 = 3;	//压缩包md5
  string effect_desc = 4;	//批量描述
}

//子级礼物类型定义
message LevelUpPresentData {
  uint32 item_id = 1;
  uint32 level = 2;	//等级
  uint32 exp = 3;	//升级所需经验
  repeated LevelUpPresentBatchData batch_list = 4;	//批量配置
}

//父级礼物类型定义
message LevelUpPresentParentData {
  uint32 parent_item_id = 1;			//父级礼物item_id
  uint32 present_type = 2;		//0普通升级礼物，1活动升级礼物
  uint32 current_version = 3;	//当前礼物版本号，活动升级礼物用，普通升级礼物保持为0
  string zip_url = 4;			//压缩包
  repeated LevelUpPresentData level_list = 5;	//当前父级礼物下面的子级礼物列表，包括父级的level=1
  repeated string color_value = 6;	//色值列表
  string levelup_bg = 7;	//礼物底图
  string levelup_number = 8;	//批量上传说明图
  string levelup_level = 9;	//礼物等级说明底图
  string cms_suffix = 10; //cms后缀
}

//获取全部的升级礼物配置（基础+批量）响应
message GetAllLevelUpPresentListResp {
  ga.BaseResp base_resp = 1;
  repeated LevelUpPresentParentData list = 2;
}

/////////////////////////////////////////////////////////GetUserLevelupPresentStatus
//获取用户全部升级礼物当前版本的状态请求
message GetUserLevelUpPresentStatusReq {
  ga.BaseReq base_req = 1;
}

//用户当前等级经验定义
message UserLevelExp {
  uint32 level = 1;	//用户当前等级
  uint32 exp = 2;	//用户当前经验
}

//获取用户全部升级礼物当前版本的状态响应
message GetUserLevelUpPresentStatusResp {
  ga.BaseResp base_resp = 1;
  map<uint32, UserLevelExp> map = 2;	//key父级礼物item_id，也就是 parent_item_id
}

/////////////////////////////////////////////////////////SendLevelupPresent
//升级礼物送礼请求
message SendLevelUpPresentReq {
  ga.BaseReq base_req = 1;
  uint32 item_source = 2; //0T豆，1背包
  uint32 item_id = 3;	//当前送出的礼物ID，如果送出的是子级礼物，则是子级礼物ID
  uint32 version = 4;	//当前送出的礼物版本号，活动升级礼物用，普通升级礼物保持为0
  uint32 item_count = 5;	//送出数量
  uint32 batch_type = 6;	//0给单人送礼，1全麦送礼
  uint32 channel_id = 7;	//用户所在的频道ID
  uint32 send_source = 8;  // 赠送时的点击来源 ga.userpresent.PresentSendSourceType 0.默认类型(兼容旧版本) 1.送礼转盘 2.礼物架（点击头像或成员列表） 3.语音球
  uint32 target_uid = 9;	//收礼人uid
  uint32 backpack_item_id = 10;	//如果从背包送出，背包中物品的id
  repeated uint32 target_uid_list = 11;  // 批量送uid
}

//升级礼物送礼响应
message SendLevelUpPresentResp {
  ga.BaseResp base_resp = 1;
  uint32 level = 2;	//送礼后的等级
  uint32 exp = 3;	//送礼后的经验
  ga.userpresent.PresentSendMsg msg_info = 4;
  int64 cur_tbeans = 5;    // 当前T豆余额
  uint32 item_source = 6;  // 礼物来源 PresentSourceType
  uint32 member_contribution_added = 7;  // 增加的个人公会贡献
  uint32 source_id = 8;  // 根据 item_source 来决定取值。如果是背包物品，就是背包物品ID
  uint32 source_remain = 9;  // 根据 item_source 来决定取值。如果是背包物品，就是背包物品的剩余数量
  uint32 parent_item_id = 10;			//父级礼物item_id
  ga.userpresent.PresentBatchInfoMsg batch_msg_info = 11; // 全麦送礼下的msg_info
  uint32 expire_time = 12; // 如果是背包送礼，背包物品更新后的过期时间
}

/////////////////////////////////////////////////////////
//当用户礼物升级时，聊天区域系统消息告知用户：【用户昵称】已将【礼物名称】升级到【等级】开启新礼物特效
//当用户礼物升级，在当前房间内出现横幅公告“恭喜【用户头像】+【用户昵称】将【礼物名称】升级到XX级”开启礼物新特效
//每个礼物，每个等级前50名先升级的用户，给予全服公告“恭喜【用户头像】+【用户昵称】第10名将礼物名称升级到XX级”开启礼物新特效
message LevelUpPushMsg {
  string present_name = 1;
  uint32 level = 2;
  uint32 rank = 3;
  string icon = 4;
  uint32 item_id = 5;
}