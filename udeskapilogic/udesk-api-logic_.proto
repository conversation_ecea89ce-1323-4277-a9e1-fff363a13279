syntax = "proto2";

package ga.udeskapilogic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/udeskapilogic";



//获取小红点情况
message GetUdeskUnReadMsgReq {
     required BaseReq base_req = 1;
}
message GetUdeskUnReadMsgResp {
     required BaseResp base_resp = 1;
     optional int64 last_unread_time = 2;//最后未读消息的时间
}

//push的未读信息
message UdeskUnreadPush {
     optional int64 last_unread_time = 1;//最后未读消息的时间
}

// vip客服入口权限类型
enum VipKefuAccessType {
    VIP_KEFU_ACCESS_TYPE_UNKNOWN = 0;    // 未知，使用本地缓存
    VIP_KEFU_ACCESS_TYPE_ENABLE = 1;     // 入口可见
    VIP_KEFU_ACCESS_TYPE_DISABLE = 2;    // 入口不可见，并删除缓存
}

// 检查vip客服入口权限
message CheckVipKefuAccessReq {
    required BaseReq base_req = 1;
}
message CheckVipKefuAccessResp {
    required BaseResp base_resp = 1;
    optional VipKefuAccessType access_type = 2;
    optional uint32 unread_msg_cnt = 3;  // 未读消息数
    optional int64 last_msg_ts = 4;      // 最新消息时间戳
    optional string vip_kefu_url = 5;    // vip客服入口url
    optional string last_msg_content = 6;
}

// 确认收到vip客服入口可见
message AckVipKefuAccessReq {
    required BaseReq base_req = 1;
    optional VipKefuAccessType access_type = 2; // 填 VIP_KEFU_ACCESS_TYPE_ENABLE
}
message AckVipKefuAccessResp {
    required BaseResp base_resp = 1;
}

// 进入vip客服
message EnterVipKefuReq {
    required BaseReq base_req = 1;
}
message EnterVipKefuResp {
    required BaseResp base_resp = 1;
}

// vip客服事件推送
message VipKefuPush {
    optional VipKefuAccessType access_type = 1;
    optional uint32 unread_msg_cnt = 2;  // 未读消息数
    optional int64 last_msg_ts = 3;      // 最新消息时间戳
    optional string last_msg_content = 4;
}

