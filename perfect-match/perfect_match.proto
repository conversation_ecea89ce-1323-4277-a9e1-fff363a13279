syntax = "proto3";

package rcmd.perfect_match;

option go_package = "golang.52tt.com/protocol/services/rcmd/perfect_match";

service RCMDPerfectMatch {
  // 获取问题列表
  rpc GetQuestions(GetQuestionsReq) returns (GetQuestionsRsp);
  // 开始匹配(TT服务器上报答题内容)
  rpc Start(StartReq) returns (StartRsp);
  // 取消匹配
  rpc Cancel(CancelReq) returns (CancelRsp);
  // 获取匹配结果（包括天配结果/线索）
  rpc GetMatchedUsers(GetMatchedUsersReq) returns (GetMatchedUsersRsp);
  // 确认已获取到匹配结果
  rpc AckMatchedUsers(AckMatchedUsersReq) returns (AckMatchedUsersRsp);
  // 获取天配结果背景图
  rpc GetResult(GetResultReq) returns (GetResultRsp);
  // 获取预估等待时间
  rpc GetWaitingTime(GetWaitingTimeReq) returns (GetWaitingTimeRsp);
  // 测试接口，查看当前匹配数据
  rpc Inspect(InspectReq) returns (InspectRsp);
  // 操控命令，用于测试
  rpc Control(ControlReq) returns (ControlRsp);
  // 清空所有匹配数据（匹配队列，匹配结果），用于测试环境测试清理脏数据，生产环境调用不会生效
  rpc FlushAll(FlushAllReq) returns (FlushAllRsp);
  // 获取匹配信息，等待人数，匹配队列等待对数
  rpc GetMatchInfo(GetMatchInfoReq) returns (GetMatchInfoRsp);
}

message Answer{
  string id = 1; // 答案ID
  string answer =2; // 答案
  string clue = 3;//线索
  bool is_selected = 4;// 用户是否选择了该答案
}
message Question{
  string id = 1; // 问题ID
  string title =2; // 问题标题
  repeated Answer answers = 3; //答案列表
  repeated string categories = 4; //类别
  repeated string tags = 5; //标签
}
message Clue {
  int64 uid =1; // 答题用户UID
  string question_id = 2; // 问题ID
  string answer_id =3; // 答案ID(不是全局唯一，但是每道题中不同答案唯一)
  string clue = 4;//线索
  string label = 5;//答案标签,2-4字用于结果展示
  string answer_translate = 6;//答案英文
  int32 entity_tag = 7;//实体标签
}
message UserInfo{
  int64 uid= 1;
  int32 sex= 2;
  string order_id = 3; //当次匹配的唯一订单号
}

message GetQuestionsReq {
  UserInfo user = 1; // 请求用户
}
message GetQuestionsRsp {
  repeated Question questions = 1;
}
message StartReq {
  UserInfo user = 1; // 请求用户
  repeated Question questions = 3;//问题答案列表
}
message WaitingTime{
  // 当预估等待时间小于1分钟，min_minutes=0,max_minutes=1
  int32 min_minutes = 1;
  int32 max_minutes = 2;
}
message StartRsp {
  WaitingTime waiting_time = 1;
}
enum CancelType {
  CancelTypeUnknown = 0;
  CancelTypeSelf = 1; // 主动取消
  CancelTypeTimeout = 2;  //超时取消
}
message CancelReq {
  repeated UserInfo user_list = 1; // 请求用户列表
  repeated CancelType cancel_type_list = 2;//取消类型
}

message CancelRsp {
  int32 code = 1; //0 取消成功，1取消失败(例如已经匹配返回结果)
}
message GetMatchedUsersReq {
  int32 couple_count = 1;//匹配对数，不传默认4对
}
message MatchedUsers{
  string id = 1; // 匹配ID
  int64 male_uid = 2;// 男uid
  int64 female_uid = 3;// 女uid
}
message GetMatchedUsersRsp {
  string match_id = 1; // 匹配场次ID
  repeated MatchedUsers matched_list =2; // 匹配用户对列表
  repeated Clue clues = 3;//问答题线索列表
}
message AckMatchedUsersReq {
  string match_id = 1; // 匹配场次ID
  repeated MatchedUsers matched_list =2; // 匹配用户对列表（默认4对）
}
message AckMatchedUsersRsp {

}
//匹配后的AIGC
message MatchedContent{
  int64 male_uid = 1;// 男uid
  int64 female_uid = 2;// 女uid
  string url = 3; // 天配图片URL
  string text = 4; // 天配文本，覆盖在图片上面
  string male_text = 5; // 男描述文本(2-4字)
  string female_text = 6; // 女描述文本(2-4字)
}
message GetResultReq {
  string match_id = 1; // 匹配场次ID
  repeated MatchedUsers matched_list = 2; // 结果页用户列表
}

message GetResultRsp {
  int32 code = 1;// 0成功返回，1稍后重试，2处理失败请放弃
  repeated MatchedUsers matched_list = 2; // 结果页用户列表
  repeated MatchedContent content_list = 3; // 天配图片列表,eg.如果有4对，则返回16张图片
}
message GetWaitingTimeReq {
  UserInfo user = 1; // 请求用户
}

message GetWaitingTimeRsp {
  WaitingTime waiting_time = 1;
}

message InspectReq {
  int32 limit=1;//数据过大时指定数量
}

message InspectRsp {
  string data = 1;
}
message ControlReq {
  string action = 1;
  int32 limit=2;//数据过大时指定数量
}

message ControlRsp {
  int32 code = 1;
  string msg = 2;
  string data = 3;
}
message FlushAllReq {

}

message FlushAllRsp {

}
message GetMatchInfoReq {
}

message GetMatchInfoRsp {
  int64 male_waiting = 1;//男等待匹配人数
  int64 female_waiting = 2;//女等待匹配人数
  int64 matched_waiting = 3;//已匹配在等待下发的对数
}