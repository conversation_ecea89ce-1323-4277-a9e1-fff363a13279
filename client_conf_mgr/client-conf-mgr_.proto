syntax = "proto3";

package ga.client_conf_mgr;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/client-conf-mgr";

//获取最新配置文件
message CheckFileInfo {
    string file_name = 1;
    uint64 seq       = 2;             //本地还没有该文件时，填0
}
message GetConfFileReq {
    ga.BaseReq base_req = 1;
    repeated CheckFileInfo files = 2;   //要检查是否更新的文件列表
}
message GetConfFileResp {
    ga.BaseResp base_resp       = 1;
    repeated ConfFile files      = 2;  //返回需要更新的文件列表
}
enum ELoadType {
    E_LOAD_UNKNOWN = 0;
    E_LOAD_HW      = 1;         //从华为云下载
    E_LOAD_KV      = 2;         //kv下载
}
message ConfFile {
    uint64 seq       = 1;
    string file_name = 2;
    string md5       = 3;
    string url       = 4;
    uint32 load_type = 5;   //参考ELoadType  预留
    bool   delete    = 6;   //该配置文件是否已经被删除了
}

//拉取所有需要更新的配置文件列表
message GetConfListReq {
    ga.BaseReq base_req = 1;
    uint64 seq = 2;             //客户端当前最大的序列号
}
message GetConfListResp {
    ga.BaseResp base_resp           = 1;
    repeated ConfFile files         = 2;
    uint64   cur_max_seq            = 3;   //当次返回的最大seq
    bool     is_end                 = 4;   //是否已经拉到最后一个配置文件
}

message CheckAnnouncementUpdateReq{
    ga.BaseReq base_req = 1;
}
message CheckAnnouncementUpdateResp{
    ga.BaseResp base_resp = 1;
    string md5  =2;
}