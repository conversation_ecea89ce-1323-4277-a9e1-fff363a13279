syntax = "proto3";

package rcmd.common;

import "common/common.proto";

option go_package = "golang.52tt.com/protocol/services/rcmd/common";

// 关注的人＞一起玩过＞同城标签
enum RCMDLabel {
    None = 0;
    GangUpWithHomeOwner = 1; // 一起玩过
    ChatWithHomeOwner = 2; // 互聊(混推会下发，垂直列表已废弃)
    FollowUserInChannel = 3; // 关注
    LocShow = 4; // 同城
}

enum Source {
    Default = 0;
    RcmdGameChannel = 1; // 开黑垂直列表
    RcmdSearch = 2; // 搜索
}

message ChannelInfo {
    uint32 tag_id                  = 1; // 娱乐房tag id（区别于游戏卡片tag id）(搜索+列表))
    uint32 recall_flag             = 2; // 召回标识 (列表)
    LocationInfo loc   = 3; // (搜索+列表)
    repeated RCMDLabel rcmd_labels = 4; // 推荐标签 (搜索+列表)
    LocShowType loc_show_type = 5; // 建议显示的IP信息 (搜索+列表)
    enum LocShowType {
        LocShowType_DEFAULT = 0; // 默认
        LocShowType_PROVINCE = 2; // 建议显示省份IP位置信息
        LocShowType_CITY = 3; // 建议显示城市IP位置信息
    }
    // Deprecated: 萌新承接房逻辑已下线
    bool is_new_user_undertake = 6; // 是否萌新承接房 (搜索+列表)
    repeated uint32 follow_uid_list  = 7; // 在房关注用户uid，去除房主 (搜索+列表)
    repeated uint32 play_uid_list = 8; // 在房一起玩过用户uid，去除房主 (搜索+列表)
    repeated uint32 follow_uid_list_v2 = 9; // 新版首页在房关注用户uid (列表)
    bool is_ever_enter = 10; // 是否为过去停留时长>x的房间 (列表)
    string delivery_type = 11; // 下发的房间类型 (列表)
    string display_content = 12; // 房间中部区域展示文案 (搜索+列表)
    repeated uint32 display_uid_list = 13; // 展示的麦上用户uid列表 (搜索+列表)
    Source source = 14; // 房间来源
}