# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

434:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 434 --source api/channel_im/grpc_channel_im_go.proto --lang go --method /ga.api.channel_im.ChannelImGoLogic/SendChannelTextMsg
435:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 435 --source api/channel_im/grpc_channel_im_go.proto --lang go --method /ga.api.channel_im.ChannelImGoLogic/GetChannelMsg
456:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 456 --source api/channel_im/grpc_channel_im_go.proto --lang go --method /ga.api.channel_im.ChannelImGoLogic/SendChannelAttachmentMsg
457:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 457 --source api/channel_im/grpc_channel_im_go.proto --lang go --method /ga.api.channel_im.ChannelImGoLogic/DownloadChannelMsgAttachment
50869:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50869 --source api/channel_im/grpc_channel_im_go.proto --lang go --method /ga.api.channel_im.ChannelImGoLogic/GetChannelReliableMsg
