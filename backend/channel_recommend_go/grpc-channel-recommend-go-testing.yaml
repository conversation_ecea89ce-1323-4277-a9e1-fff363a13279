apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-channel-recommend-go-logic
spec:
  gateways:
  - istio-ingress/apiv2-gateway
  - istio-ingress/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.channel_recommend_go.ChannelRecommendLogicService/
    delegate:
       name: channel-recommend-logic-delegator-80
       namespace: quicksilver


