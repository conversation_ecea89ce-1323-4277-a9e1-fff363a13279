# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

32012:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 32012 --source api/revenue_base/grpc_revenue_base.proto --lang go --method /ga.api.revenue_base.RevenueBaseLogic/CheckCanSeeRoiUser
32013:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 32013 --source api/revenue_base/grpc_revenue_base.proto --lang go --method /ga.api.revenue_base.RevenueBaseLogic/GetUserRoiInfo
32014:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 32014 --source api/revenue_base/grpc_revenue_base.proto --lang go --method /ga.api.revenue_base.RevenueBaseLogic/ConfirmRoiHighPotentail
32015:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 32015 --source api/revenue_base/grpc_revenue_base.proto --lang go --method /ga.api.revenue_base.RevenueBaseLogic/GetRevenueAdPosInfo
32016:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 32016 --source api/revenue_base/grpc_revenue_base.proto --lang go --method /ga.api.revenue_base.RevenueBaseLogic/GetPersonalizedAdSwitch
32017:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 32017 --source api/revenue_base/grpc_revenue_base.proto --lang go --method /ga.api.revenue_base.RevenueBaseLogic/SetPersonalizedAdSwitch
32018:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 32018 --source api/revenue_base/grpc_revenue_base.proto --lang go --method /ga.api.revenue_base.RevenueBaseLogic/ProcGuildManageRoleInvite
32019:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 32019 --source api/revenue_base/grpc_revenue_base.proto --lang go --method /ga.api.revenue_base.RevenueBaseLogic/GetUserQualityInfo
32020:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 32020 --source api/revenue_base/grpc_revenue_base.proto --lang go --method /ga.api.revenue_base.RevenueBaseLogic/ConfirmQualityHighPop
