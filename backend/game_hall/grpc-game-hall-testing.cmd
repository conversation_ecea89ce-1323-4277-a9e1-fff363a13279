# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

5500:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 5500 --source api/game_hall/grpc_game_hall.proto --lang go --method /ga.api.game_hall.GameHallLogic/GetBuildChannelInfo
5501:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 5501 --source api/game_hall/grpc_game_hall.proto --lang go --method /ga.api.game_hall.GameHallLogic/GetMsgList
5502:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 5502 --source api/game_hall/grpc_game_hall.proto --lang go --method /ga.api.game_hall.GameHallLogic/SendGameImMsg
5504:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 5504 --source api/game_hall/grpc_game_hall.proto --lang go --method /ga.api.game_hall.GameHallLogic/GetGameHallTeamList
5505:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 5505 --source api/game_hall/grpc_game_hall.proto --lang go --method /ga.api.game_hall.GameHallLogic/CheckHallEnterRoom
5507:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 5507 --source api/game_hall/grpc_game_hall.proto --lang go --method /ga.api.game_hall.GameHallLogic/GetUserUnreadAtMsg
5508:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 5508 --source api/game_hall/grpc_game_hall.proto --lang go --method /ga.api.game_hall.GameHallLogic/MarkUserAtMsgRead
5509:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 5509 --source api/game_hall/grpc_game_hall.proto --lang go --method /ga.api.game_hall.GameHallLogic/CheckSendInviteRoomCond
5510:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 5510 --source api/game_hall/grpc_game_hall.proto --lang go --method /ga.api.game_hall.GameHallLogic/UpdateGameHallNotifyStatus
5511:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 5511 --source api/game_hall/grpc_game_hall.proto --lang go --method /ga.api.game_hall.GameHallLogic/GetGameHallNotifyStatus
