cmdInfoList:
  - source: api/channel_team/grpc_channel_team.proto
    cmd: 30356
    lang: go
    method: /ga.api.channel_team.ChannelTeamLogic/JoinChannelTeam
  - source: api/channel_team/grpc_channel_team.proto
    cmd: 30357
    lang: go
    method: /ga.api.channel_team.ChannelTeamLogic/GetChannelTeamApplyList
  - source: api/channel_team/grpc_channel_team.proto
    cmd: 30358
    lang: go
    method: /ga.api.channel_team.ChannelTeamLogic/AgreeChannelTeamApply
  - source: api/channel_team/grpc_channel_team.proto
    cmd: 30359
    lang: go
    method: /ga.api.channel_team.ChannelTeamLogic/TickChannelTeamMember
  - source: api/channel_team/grpc_channel_team.proto
    cmd: 30360
    lang: go
    method: /ga.api.channel_team.ChannelTeamLogic/GetChannelTeamMemberList
  - source: api/channel_team/grpc_channel_team.proto
    cmd: 30361
    lang: go
    method: /ga.api.channel_team.ChannelTeamLogic/GetGangUpHistory
  - source: api/channel_team/grpc_channel_team.proto
    cmd: 30362
    lang: go
    method: /ga.api.channel_team.ChannelTeamLogic/GetAllChannelMember
  - source: api/channel_team/grpc_channel_team.proto
    cmd: 30363
    lang: go
    method: /ga.api.channel_team.ChannelTeamLogic/SetGameNickname

