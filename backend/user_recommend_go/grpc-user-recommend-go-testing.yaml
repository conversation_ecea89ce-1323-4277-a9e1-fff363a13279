apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-user-recommend-go-logic
spec:
  gateways:
  - istio-ingress/apiv2-gateway
  - istio-ingress/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.user_recommend_go.UserRecommendLogicGo/
    delegate:
       name: userrecommendlogic_go-delegator-80
       namespace: quicksilver


