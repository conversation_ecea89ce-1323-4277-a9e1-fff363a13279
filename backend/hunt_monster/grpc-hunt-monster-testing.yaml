apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-hunt-monster-logic
spec:
  gateways:
  - istio-ingress/apiv2-gateway
  - istio-ingress/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.hunt_monster.HuntMonsterLogic/
    rewrite:
      uri: /logic.HuntMonsterLogic/
    delegate:
       name: hunt-monster-logic-delegator-80
       namespace: quicksilver


