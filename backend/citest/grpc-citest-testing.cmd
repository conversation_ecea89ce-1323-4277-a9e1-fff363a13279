# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

30130:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 30130 --source api/citest/grpc_citest.proto --lang go --method /ga.api.citest.CitestLogic/AbtestExpSync
30131:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 30131 --source api/citest/grpc_citest.proto --lang go --method /ga.api.citest.CitestLogic/AbtestExpSync1
30132:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 30132 --source api/citest/grpc_citest.proto --lang go --method /ga.api.citest.CitestLogic/AbtestExpSync2
30133:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 30133 --source api/citest/grpc_citest.proto --lang go --method /ga.api.citest.CitestLogic/AbtestExpSync3
