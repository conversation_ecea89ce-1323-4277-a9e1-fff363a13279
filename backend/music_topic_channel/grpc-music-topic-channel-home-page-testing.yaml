apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-music-topic-channel-home-page-logic
spec:
  gateways:
  - istio-ingress/apiv2-gateway
  - istio-ingress/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.music_topic_channel.MusicTopicChannelHomePageLogic/
    rewrite:
      uri: /logic.MusicTopicChannelHomePageLogic/
    delegate:
       name: music-topic-channel-logic-delegator-80
       namespace: quicksilver


