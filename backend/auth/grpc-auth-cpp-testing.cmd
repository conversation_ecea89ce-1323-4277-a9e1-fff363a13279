# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

10:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 10 --source api/auth/grpc_auth_cpp.proto --lang cpp --method /ga.api.auth.AuthCppLogic/Auth
100010:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 100010 --source api/auth/grpc_auth_cpp.proto --lang cpp --method /ga.api.auth.AuthCppLogic/RobotAuth
12:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 12 --source api/auth/grpc_auth_cpp.proto --lang cpp --method /ga.api.auth.AuthCppLogic/Reg
14:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 14 --source api/auth/grpc_auth_cpp.proto --lang cpp --method /ga.api.auth.AuthCppLogic/AccountVerifyCode
19:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 19 --source api/auth/grpc_auth_cpp.proto --lang cpp --method /ga.api.auth.AuthCppLogic/RegExistPhone
232:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 232 --source api/auth/grpc_auth_cpp.proto --lang cpp --method /ga.api.auth.AuthCppLogic/SubmitVerifyCode
30086:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 30086 --source api/auth/grpc_auth_cpp.proto --lang cpp --method /ga.api.auth.AuthCppLogic/ChinaMobileThirdPartyBindPhone
30087:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 30087 --source api/auth/grpc_auth_cpp.proto --lang cpp --method /ga.api.auth.AuthCppLogic/ChinaMobileAccountBind
33000:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 33000 --source api/auth/grpc_auth_cpp.proto --lang cpp --method /ga.api.auth.AuthCppLogic/RefreshUnionTokenService
5050:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 5050 --source api/auth/grpc_auth_cpp.proto --lang cpp --method /ga.api.auth.AuthCppLogic/AccountVoiceVerifyCode
