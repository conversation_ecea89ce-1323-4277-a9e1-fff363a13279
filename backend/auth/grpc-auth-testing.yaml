apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-auth-logic
spec:
  gateways:
  - istio-ingress/apiv2-gateway
  - istio-ingress/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.auth.AuthLogic/
    rewrite:
      uri: /logic.AuthLogic/
    delegate:
       name: auth-logic-delegator-80
       namespace: quicksilver


