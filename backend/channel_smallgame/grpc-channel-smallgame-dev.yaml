apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-channel-smallgame-logic
spec:
  gateways:
  - istio-system/apiv2-gateway
  - istio-system/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.channel_smallgame.ChannelSmallGameLogic/
    rewrite:
      uri: /logic.ChannelSmallGameLogic/
    delegate:
       name: channel-smallgame-logic-delegator-80
       namespace: quicksilver


