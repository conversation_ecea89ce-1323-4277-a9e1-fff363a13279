apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-channel-smallgame-logic
spec:
  gateways:
  - quicksilver/tt-api-gateway
  - quicksilver/tt-api-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.channel_smallgame.ChannelSmallGameLogic/
    rewrite:
      uri: /logic.ChannelSmallGameLogic/
    delegate:
       name: channel-smallgame-logic-delegator-80
       namespace: quicksilver


