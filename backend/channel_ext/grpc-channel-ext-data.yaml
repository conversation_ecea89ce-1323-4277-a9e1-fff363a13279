cmdInfoList:
  - source: api/channel_ext/grpc_channel_ext.proto
    cmd: 620
    lang: cpp
    method: /ga.api.channel_ext.ChannelExtLogic/GetCharmAndRichLableInfo
  - source: api/channel_ext/grpc_channel_ext.proto
    cmd: 621
    lang: cpp
    method: /ga.api.channel_ext.ChannelExtLogic/ChannelWeekConsumeTopN
  - source: api/channel_ext/grpc_channel_ext.proto
    cmd: 2051
    lang: cpp
    method: /ga.api.channel_ext.ChannelExtLogic/ChannelGetGfitHistory
  - source: api/channel_ext/grpc_channel_ext.proto
    cmd: 2071
    lang: cpp
    method: /ga.api.channel_ext.ChannelExtLogic/ChannelGetRecommend
  - source: api/channel_ext/grpc_channel_ext.proto
    cmd: 2073
    lang: cpp
    method: /ga.api.channel_ext.ChannelExtLogic/ChannelGetHotList
  - source: api/channel_ext/grpc_channel_ext.proto
    cmd: 2074
    lang: cpp
    method: /ga.api.channel_ext.ChannelExtLogic/channelGetShowSwitch
  - source: api/channel_ext/grpc_channel_ext.proto
    cmd: 2075
    lang: cpp
    method: /ga.api.channel_ext.ChannelExtLogic/ChannelGetTagList
  - source: api/channel_ext/grpc_channel_ext.proto
    cmd: 2076
    lang: cpp
    method: /ga.api.channel_ext.ChannelExtLogic/ChannelGetCardList
  - source: api/channel_ext/grpc_channel_ext.proto
    cmd: 2077
    lang: cpp
    method: /ga.api.channel_ext.ChannelExtLogic/ChannelGetChannelByTagId
  - source: api/channel_ext/grpc_channel_ext.proto
    cmd: 2078
    lang: cpp
    method: /ga.api.channel_ext.ChannelExtLogic/ChannelSetTagId
  - source: api/channel_ext/grpc_channel_ext.proto
    cmd: 2079
    lang: cpp
    method: /ga.api.channel_ext.ChannelExtLogic/ChannelGetTagId
  - source: api/channel_ext/grpc_channel_ext.proto
    cmd: 2080
    lang: cpp
    method: /ga.api.channel_ext.ChannelExtLogic/ChannelRefreshTime
  - source: api/channel_ext/grpc_channel_ext.proto
    cmd: 2081
    lang: cpp
    method: /ga.api.channel_ext.ChannelExtLogic/ChannelGetAdvList
  - source: api/channel_ext/grpc_channel_ext.proto
    cmd: 2082
    lang: cpp
    method: /ga.api.channel_ext.ChannelExtLogic/GetChannelRefreshCD
  - source: api/channel_ext/grpc_channel_ext.proto
    cmd: 2083
    lang: cpp
    method: /ga.api.channel_ext.ChannelExtLogic/ChannelGetHomeDetail
  - source: api/channel_ext/grpc_channel_ext.proto
    cmd: 2086
    lang: cpp
    method: /ga.api.channel_ext.ChannelExtLogic/ChannelGetGameMatchOptions
  - source: api/channel_ext/grpc_channel_ext.proto
    cmd: 2087
    lang: cpp
    method: /ga.api.channel_ext.ChannelExtLogic/ChannelGetGameMatchListHomePage
  - source: api/channel_ext/grpc_channel_ext.proto
    cmd: 2088
    lang: cpp
    method: /ga.api.channel_ext.ChannelExtLogic/ChannelGetGameMatchListByTagId
  - source: api/channel_ext/grpc_channel_ext.proto
    cmd: 2089
    lang: cpp
    method: /ga.api.channel_ext.ChannelExtLogic/ChannelStartGameMatch
  - source: api/channel_ext/grpc_channel_ext.proto
    cmd: 2520
    lang: cpp
    method: /ga.api.channel_ext.ChannelExtLogic/ChannelGetUserDecorationList
  - source: api/channel_ext/grpc_channel_ext.proto
    cmd: 2521
    lang: cpp
    method: /ga.api.channel_ext.ChannelExtLogic/ChannelActivateUserDecoration
  - source: api/channel_ext/grpc_channel_ext.proto
    cmd: 2720
    lang: cpp
    method: /ga.api.channel_ext.ChannelExtLogic/ChannelGetNoviceRecommendStatus
  - source: api/channel_ext/grpc_channel_ext.proto
    cmd: 3502
    lang: cpp
    method: /ga.api.channel_ext.ChannelExtLogic/CChannelGetMicroUserGameTagService

