# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

2500:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2500 --source api/emoji/grpc_emoji.proto --lang go --method /ga.api.emoji.EmojiLogic/SaveEmoji
2501:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2501 --source api/emoji/grpc_emoji.proto --lang go --method /ga.api.emoji.EmojiLogic/DeleteEmoji
2502:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2502 --source api/emoji/grpc_emoji.proto --lang go --method /ga.api.emoji.EmojiLogic/GetEmojiPkgList
2503:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2503 --source api/emoji/grpc_emoji.proto --lang go --method /ga.api.emoji.EmojiLogic/GetEmojiListByPkg
2511:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2511 --source api/emoji/grpc_emoji.proto --lang go --method /ga.api.emoji.EmojiLogic/GetHotEmoji
2512:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2512 --source api/emoji/grpc_emoji.proto --lang go --method /ga.api.emoji.EmojiLogic/GetSearchEmoji
2513:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2513 --source api/emoji/grpc_emoji.proto --lang go --method /ga.api.emoji.EmojiLogic/CheckHotEmojiEntrance
3038:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3038 --source api/emoji/grpc_emoji.proto --lang go --method /ga.api.emoji.EmojiLogic/TestEmojiA
