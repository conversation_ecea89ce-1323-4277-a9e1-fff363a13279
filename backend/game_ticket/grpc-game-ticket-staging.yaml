apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-game-ticket-logic-staging
spec:
  gateways:
  - quicksilver/tt-api-gateway
  - quicksilver/tt-api-gateway-internal
  hosts:
  - *
  http:
  - match:
    - uri:
        prefix: /ga.api.game_ticket.GameTicketService/
    route:
    - destination:
        host: game-ticket-logic.quicksilver.svc.cluster.local
        port:
          number: 80
