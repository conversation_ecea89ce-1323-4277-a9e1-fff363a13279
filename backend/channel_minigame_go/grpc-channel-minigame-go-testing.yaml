apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-channel-minigame-go-logic
spec:
  gateways:
  - istio-ingress/apiv2-gateway
  - istio-ingress/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.channel_minigame_go.ChannelMiniGameGoLogic/
    rewrite:
      uri: /logic.ChannelMiniGameGoLogic/
    delegate:
       name: channel-minigame-go-logic-delegator-80
       namespace: quicksilver


