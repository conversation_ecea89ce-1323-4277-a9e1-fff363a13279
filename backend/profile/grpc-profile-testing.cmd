# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

32005:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 32005 --source api/profile/grpc_profile.proto --lang go --method /ga.api.profile.ProfileLogic/GetUserExamineCert
32007:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 32007 --source api/profile/grpc_profile.proto --lang go --method /ga.api.profile.ProfileLogic/BatchGetUserExamineCert
3401:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3401 --source api/profile/grpc_profile.proto --lang go --method /ga.api.profile.ProfileLogic/UserDecorations
3402:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3402 --source api/profile/grpc_profile.proto --lang go --method /ga.api.profile.ProfileLogic/UserCurrDecoration
3403:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3403 --source api/profile/grpc_profile.proto --lang go --method /ga.api.profile.ProfileLogic/UserAdornDecoration
3404:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3404 --source api/profile/grpc_profile.proto --lang go --method /ga.api.profile.ProfileLogic/UserRemoveDecoration
3407:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3407 --source api/profile/grpc_profile.proto --lang go --method /ga.api.profile.ProfileLogic/ChangeDecorationCustomText
