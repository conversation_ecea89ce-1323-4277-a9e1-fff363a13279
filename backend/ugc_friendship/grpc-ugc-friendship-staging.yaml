apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-ugc-friendship-logic-staging
spec:
  gateways:
  - quicksilver/tt-api-gateway
  - quicksilver/tt-api-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.ugc_friendship.UgcFriendshipLogic/
    rewrite:
      uri: /logic.UgcFriendshipLogic/
    delegate:
       name: ugc-friendship-logic-delegator-80
       namespace: quicksilver


