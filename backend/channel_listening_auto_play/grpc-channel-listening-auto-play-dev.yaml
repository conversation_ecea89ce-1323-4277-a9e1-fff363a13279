apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-channel-listening-auto-play-logic
spec:
  gateways:
  - istio-system/apiv2-gateway
  - istio-system/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.channel_listening_auto_play.ChannelListeningAutoPlayLogic/
    rewrite:
      uri: /logic.ChannelListeningAutoPlayLogic/
    delegate:
       name: channel-listening-auto-play-logic-delegator-80
       namespace: quicksilver


