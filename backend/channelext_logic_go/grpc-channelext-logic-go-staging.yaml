apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-channelext-logic-go-logic-staging
spec:
  gateways:
  - quicksilver/tt-api-gateway
  - quicksilver/tt-api-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.channelext_logic_go.ChannelExtLogicGo/
    rewrite:
      uri: /logic.ChannelExtLogicGo/
    delegate:
       name: channelext-logic-go-delegator-80
       namespace: quicksilver


