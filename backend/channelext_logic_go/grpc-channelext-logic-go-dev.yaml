apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-channelext-logic-go-logic
spec:
  gateways:
  - istio-system/apiv2-gateway
  - istio-system/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.channelext_logic_go.ChannelExtLogicGo/
    rewrite:
      uri: /logic.ChannelExtLogicGo/
    delegate:
       name: channelext-logic-go-delegator-80
       namespace: quicksilver


