# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

11:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 11 --source api/im/grpc_im_cpp.proto --lang cpp --method /ga.api.im.ImCppLogic/SendMsg
23:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 23 --source api/im/grpc_im_cpp.proto --lang cpp --method /ga.api.im.ImCppLogic/UploadAttachment
25:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 25 --source api/im/grpc_im_cpp.proto --lang cpp --method /ga.api.im.ImCppLogic/DeleteMessage
410:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 410 --source api/im/grpc_im_cpp.proto --lang cpp --method /ga.api.im.ImCppLogic/UpdateMsgSetting
