# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

36300:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36300 --source api/pgc_channel_pk/grpc_pgc_channel_pk.proto --lang go --method /ga.api.pgc_channel_pk.PgcChannelPKLogic/GetPgcChannelPKEntry
36301:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36301 --source api/pgc_channel_pk/grpc_pgc_channel_pk.proto --lang go --method /ga.api.pgc_channel_pk.PgcChannelPKLogic/GetPgcChannelPKChannelList
36302:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36302 --source api/pgc_channel_pk/grpc_pgc_channel_pk.proto --lang go --method /ga.api.pgc_channel_pk.PgcChannelPKLogic/SetPgcChannelPKSwitch
36303:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36303 --source api/pgc_channel_pk/grpc_pgc_channel_pk.proto --lang go --method /ga.api.pgc_channel_pk.PgcChannelPKLogic/StartPgcChannelPK
36304:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36304 --source api/pgc_channel_pk/grpc_pgc_channel_pk.proto --lang go --method /ga.api.pgc_channel_pk.PgcChannelPKLogic/AcceptPgcChannelPK
36305:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36305 --source api/pgc_channel_pk/grpc_pgc_channel_pk.proto --lang go --method /ga.api.pgc_channel_pk.PgcChannelPKLogic/GetPgcChannelPKInfo
36306:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36306 --source api/pgc_channel_pk/grpc_pgc_channel_pk.proto --lang go --method /ga.api.pgc_channel_pk.PgcChannelPKLogic/PgcChannelPKReportClientIDChange
36307:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36307 --source api/pgc_channel_pk/grpc_pgc_channel_pk.proto --lang go --method /ga.api.pgc_channel_pk.PgcChannelPKLogic/SetPgcChannelPKOpponentMicFlag
36308:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36308 --source api/pgc_channel_pk/grpc_pgc_channel_pk.proto --lang go --method /ga.api.pgc_channel_pk.PgcChannelPKLogic/GetPgcChannelPKSendGiftScore
36309:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36309 --source api/pgc_channel_pk/grpc_pgc_channel_pk.proto --lang go --method /ga.api.pgc_channel_pk.PgcChannelPKLogic/GetPgcChannelPKAudienceRank
36310:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36310 --source api/pgc_channel_pk/grpc_pgc_channel_pk.proto --lang go --method /ga.api.pgc_channel_pk.PgcChannelPKLogic/ChoseInteraction
36311:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36311 --source api/pgc_channel_pk/grpc_pgc_channel_pk.proto --lang go --method /ga.api.pgc_channel_pk.PgcChannelPKLogic/SetPgcChannelPKEnd
