# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

40000:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40000 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/GetGameList
40001:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40001 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/GetSwitch
40002:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40002 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/GetESportApplyAccess
40003:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40003 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/EsportGetTopGameList
40004:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40004 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/GetInviteOrderRecommend
40005:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40005 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/GetGamePropertyList
40006:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40006 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/GetEsportAreaCoachList
40007:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40007 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/GetHomePageSkillProductList
40008:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40008 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/GetIMFloatWindowInfo
40009:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40009 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/InviteOrder
40010:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40010 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/PlayerPayOrder
40011:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40011 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/PlayerCancelOrder
40012:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40012 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/PlayerFinishOrder
40013:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40013 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/CoachReceiveOrder
40014:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40014 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/CoachRefuseOrder
40015:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40015 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/GetOrderDetail
40016:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40016 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/GetOrderList
40017:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40017 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/DelOrderRecord
40018:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40018 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/GetReasonTextList
40019:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40019 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/CoachNotifyFinishOrder
40020:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40020 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/AcceptRefund
40021:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40021 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/HandleInviteOrder
40022:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40022 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/GetEvaluateWordList
40024:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40024 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/Evaluate
40025:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40025 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/GetEsportGameCardConfig
40026:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40026 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/GetEsportGameCardInfo
40027:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40027 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/UpsertEsportGameCardInfo
40028:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40028 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/GetEsportGameCardList
40029:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40029 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/SendEsportGameCard
40030:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40030 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/DeleteEsportGameCard
40031:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40031 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/GetGameCardGame
40032:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40032 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/EnterEsportIMPageReport
40033:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40033 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/GetChatListEsportTags
40034:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40034 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/GetEsportQuickReplyList
40035:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40035 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/ReportQuickReply
40036:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40036 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/ReportExposeCoach
40037:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40037 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/PlayerPayOrderPreCheck
40038:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40038 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/EstimateOrderTotalPrice
40039:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40039 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/ContactCustomerService
40040:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40040 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/IsUserACustomer
40041:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40041 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/GetUGCReListEnt
40042:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40042 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/GetUGCReCoachCardInfo
40043:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40043 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/NoMoreReOnUGC
40044:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40044 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/GetRecommendedGodList
40045:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40045 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/LoginAppShowCouponRemain
40046:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40046 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/ShowManualGrantCoupon
40047:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40047 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/MarkManualGrantCouponRead
40048:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40048 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/GetCouponEntranceInfo
40049:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40049 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/GetHomeCouponEntranceInfo
40050:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40050 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/GetEsportAreaTopBannerList
40051:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40051 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/GetEsportCoachMissionInfo
40052:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40052 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/GetBackRecallReCoach
40053:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40053 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/GetNewCustomerTabSetting
40054:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40054 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/PostNewCustomerTabSetting
40055:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40055 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/GetOneKeyFindCoachEntry
40056:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40056 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/GetOneKeyPublishCfg
40057:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40057 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/PublishOneKeyFindCoach
40058:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40058 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/CancelOneKeyFindCoach
40059:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40059 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/StickOneKeyFindCoach
40060:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40060 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/GetGoingOneKeyFindCoach
40061:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40061 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/EsportReportClickIm
40062:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40062 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/EsportRegionHeartbeat
40063:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40063 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/CheckIfCanPublishOneKeyFindCoach
40064:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 40064 --source api/esport_logic/grpc_esport_logic.proto --lang go --method /ga.api.esport_logic.EsportLogic/GetGlobalOneKeyFindCfg
