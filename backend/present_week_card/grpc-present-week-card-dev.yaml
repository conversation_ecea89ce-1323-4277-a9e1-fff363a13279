apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-present-week-card-logic
spec:
  gateways:
  - istio-system/apiv2-gateway
  - istio-system/apiv2-gateway-internal
  hosts:
  - *
  http:
  - match:
    - uri:
        prefix: /ga.api.present_week_card.PresentWeekCardLogic/
    route:
    - destination:
        host: present-week-card-logic.quicksilver.svc.cluster.local
        port:
          number: 80
