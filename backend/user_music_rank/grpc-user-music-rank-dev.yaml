apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-user-music-rank-logic
spec:
  gateways:
  - istio-system/apiv2-gateway
  - istio-system/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.user_music_rank.UserMusicRankLogic/
    rewrite:
      uri: /logic.UserMusicRankLogic/
    delegate:
       name: user-music-rank-logic-delegator-80
       namespace: quicksilver


