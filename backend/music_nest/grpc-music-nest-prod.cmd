# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

31321:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 31321 --source api/music_nest/grpc_music_nest.proto --lang go --method /ga.api.music_nest.MusicNestLogic/GetMusicNestCoverAndLiveList
31322:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 31322 --source api/music_nest/grpc_music_nest.proto --lang go --method /ga.api.music_nest.MusicNestLogic/SubMusicNest
31323:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 31323 --source api/music_nest/grpc_music_nest.proto --lang go --method /ga.api.music_nest.MusicNestLogic/GetMusicNestHomePage
31324:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 31324 --source api/music_nest/grpc_music_nest.proto --lang go --method /ga.api.music_nest.MusicNestLogic/SubMusicNestActivity
31325:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 31325 --source api/music_nest/grpc_music_nest.proto --lang go --method /ga.api.music_nest.MusicNestLogic/GetMusicNestPerformance
31326:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 31326 --source api/music_nest/grpc_music_nest.proto --lang go --method /ga.api.music_nest.MusicNestLogic/SetCurrentMusicNestPerformanceStage
31328:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 31328 --source api/music_nest/grpc_music_nest.proto --lang go --method /ga.api.music_nest.MusicNestLogic/CloseNestDirectionAct
31331:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 31331 --source api/music_nest/grpc_music_nest.proto --lang go --method /ga.api.music_nest.MusicNestLogic/GetSpecifiedChannelVisitedSize
31341:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 31341 --source api/music_nest/grpc_music_nest.proto --lang go --method /ga.api.music_nest.MusicNestLogic/GetWelcomePop
31342:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 31342 --source api/music_nest/grpc_music_nest.proto --lang go --method /ga.api.music_nest.MusicNestLogic/UserClickPop
31343:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 31343 --source api/music_nest/grpc_music_nest.proto --lang go --method /ga.api.music_nest.MusicNestLogic/GetMusicNestLiveInfo
31563:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 31563 --source api/music_nest/grpc_music_nest.proto --lang go --method /ga.api.music_nest.MusicNestLogic/GetMusicNestSocialCommunity
