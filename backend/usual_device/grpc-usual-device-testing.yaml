apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-usual-device-logic
spec:
  gateways:
  - istio-ingress/apiv2-gateway
  - istio-ingress/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.usual_device.UsualDeviceLogic/
    rewrite:
      uri: /logic.UsualDeviceLogic/
    delegate:
       name: usual-device-logic-delegator-80
       namespace: quicksilver


