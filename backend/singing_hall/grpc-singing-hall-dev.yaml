apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-singing-hall-logic
spec:
  gateways:
  - istio-system/apiv2-gateway
  - istio-system/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.singing_hall.SingingHallLogic/
    rewrite:
      uri: /logic.SingingHallLogic/
    delegate:
       name: singing-hall-logic-delegator-80
       namespace: quicksilver


