# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

1350:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 1350 --source api/web_im/grpc_web_im.proto --lang go --method /ga.api.web_im.WebImLogic/SendWebImMsg
1351:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 1351 --source api/web_im/grpc_web_im.proto --lang go --method /ga.api.web_im.WebImLogic/GetWebImMsgList
1352:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 1352 --source api/web_im/grpc_web_im.proto --lang go --method /ga.api.web_im.WebImLogic/ReadWebImMsg
1353:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 1353 --source api/web_im/grpc_web_im.proto --lang go --method /ga.api.web_im.WebImLogic/GetUserWebImMsgList
1354:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 1354 --source api/web_im/grpc_web_im.proto --lang go --method /ga.api.web_im.WebImLogic/SendGroupImMsg
1355:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 1355 --source api/web_im/grpc_web_im.proto --lang go --method /ga.api.web_im.WebImLogic/BatchGetGroupLastMsg
1356:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 1356 --source api/web_im/grpc_web_im.proto --lang go --method /ga.api.web_im.WebImLogic/GetGroupMsgList
