apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-mijing-playmatel-logic
spec:
  gateways:
  - quicksilver/tt-api-gateway
  - quicksilver/tt-api-gateway-internal
  hosts:
  - apiv2.52tt.com
  http:
  - match:
    - uri:
        prefix: /ga.api.mijing_playmate.MijingPlaymateLogic/
    route:
    - destination:
        host: mijing-playmate-logic.quicksilver.svc.cluster.local
        port:
          number: 80
