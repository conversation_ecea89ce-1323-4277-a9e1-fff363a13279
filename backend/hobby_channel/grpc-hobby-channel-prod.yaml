apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-hobby-channel-logic
spec:
  gateways:
  - quicksilver/tt-api-gateway
  - quicksilver/tt-api-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.hobby_channel.HobbyChannelLogic/
    rewrite:
      uri: /logic.HobbyChannelLogic/
    delegate:
       name: hobby-channel-logic-delegator-80
       namespace: quicksilver


