# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

36651:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36651 --source api/star_train/grpc_star_train_logic.proto --lang go --method /ga.api.star_train.StarTrainLogic/GetStarTrainEntry
36652:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36652 --source api/star_train/grpc_star_train_logic.proto --lang go --method /ga.api.star_train.StarTrainLogic/GetStarTrainInfo
36653:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36653 --source api/star_train/grpc_star_train_logic.proto --lang go --method /ga.api.star_train.StarTrainLogic/GetStarTrainProgress
36654:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36654 --source api/star_train/grpc_star_train_logic.proto --lang go --method /ga.api.star_train.StarTrainLogic/GetStarTrainBroadcast
36655:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36655 --source api/star_train/grpc_star_train_logic.proto --lang go --method /ga.api.star_train.StarTrainLogic/GetStarTrainSeatList
