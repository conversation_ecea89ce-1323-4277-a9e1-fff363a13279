apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-push-logic
spec:
  gateways:
  - istio-ingress/apiv2-gateway
  - istio-ingress/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.push.PushLogic/
    rewrite:
      uri: /logic.push.PushLogic/
    delegate:
       name: push-logic-delegator-80
       namespace: quicksilver


