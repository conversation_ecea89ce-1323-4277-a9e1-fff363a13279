apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-channel-guild-logic
spec:
  gateways:
  - quicksilver/tt-api-gateway
  - quicksilver/tt-api-gateway-internal
  hosts:
  - *
  http:
  - match:
    - uri:
        prefix: /ga.api.channel_guild.ChannelGuildGoLogic/
    rewrite:
      uri: /logic.ChannelGuildGoLogic/
    route:
    - destination:
        host: channelguild-logic.quicksilver.svc.cluster.local
        port:
          number: 80
