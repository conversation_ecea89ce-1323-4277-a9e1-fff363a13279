apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: appsvr
  name: grpc-channel-guild-cpp-logic
spec:
  gateways:
  - istio-ingress/apiv2-gateway
  - istio-ingress/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.channel_guild.ChannelGuildLogic/
    delegate:
       name: channelguildlogic-delegator-80
       namespace: appsvr


