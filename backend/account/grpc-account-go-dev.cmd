# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

1201:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 1201 --source api/account/grpc_account_go.proto --lang go --method /ga.api.account.AccountGoLogic/GetPhotoAlbum
233:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 233 --source api/account/grpc_account_go.proto --lang go --method /ga.api.account.AccountGoLogic/GetUserCertificationList
27:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 27 --source api/account/grpc_account_go.proto --lang go --method /ga.api.account.AccountGoLogic/GetUserDetail
415:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 415 --source api/account/grpc_account_go.proto --lang go --method /ga.api.account.AccountGoLogic/GetUnregApplyAuditStatus
