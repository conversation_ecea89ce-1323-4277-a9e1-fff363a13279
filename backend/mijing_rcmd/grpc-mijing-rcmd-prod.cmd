# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

36080:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36080 --source api/mijing_rcmd/grpc_mijing_rcmd.proto --lang go --method /ga.api.mijing_rcmd.MijingRcmdLogic/MijingGetRecommendedScenarioForHomePage
36081:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36081 --source api/mijing_rcmd/grpc_mijing_rcmd.proto --lang go --method /ga.api.mijing_rcmd.MijingRcmdLogic/GetMijingScenarioCommentList
36082:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36082 --source api/mijing_rcmd/grpc_mijing_rcmd.proto --lang go --method /ga.api.mijing_rcmd.MijingRcmdLogic/GetMijingCommentList
36083:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36083 --source api/mijing_rcmd/grpc_mijing_rcmd.proto --lang go --method /ga.api.mijing_rcmd.MijingRcmdLogic/GetMijingPartnerCardList
36084:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36084 --source api/mijing_rcmd/grpc_mijing_rcmd.proto --lang go --method /ga.api.mijing_rcmd.MijingRcmdLogic/BatchGetMijingPartnerCardInfoByCardId
36085:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36085 --source api/mijing_rcmd/grpc_mijing_rcmd.proto --lang go --method /ga.api.mijing_rcmd.MijingRcmdLogic/BatchGetMijingPartnerCardInfoByUid
36086:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36086 --source api/mijing_rcmd/grpc_mijing_rcmd.proto --lang go --method /ga.api.mijing_rcmd.MijingRcmdLogic/UpdateMijingPartnerCardInfo
36087:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36087 --source api/mijing_rcmd/grpc_mijing_rcmd.proto --lang go --method /ga.api.mijing_rcmd.MijingRcmdLogic/MijingExposurePartnerCard
36088:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36088 --source api/mijing_rcmd/grpc_mijing_rcmd.proto --lang go --method /ga.api.mijing_rcmd.MijingRcmdLogic/MijingIncrExposurePartnerCard
36089:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36089 --source api/mijing_rcmd/grpc_mijing_rcmd.proto --lang go --method /ga.api.mijing_rcmd.MijingRcmdLogic/MijingAccostPartnerCard
36090:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36090 --source api/mijing_rcmd/grpc_mijing_rcmd.proto --lang go --method /ga.api.mijing_rcmd.MijingRcmdLogic/GetMijingScenarioGameIdList
36091:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36091 --source api/mijing_rcmd/grpc_mijing_rcmd.proto --lang go --method /ga.api.mijing_rcmd.MijingRcmdLogic/CheckMijingGenerateAvatar
36092:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36092 --source api/mijing_rcmd/grpc_mijing_rcmd.proto --lang go --method /ga.api.mijing_rcmd.MijingRcmdLogic/SubmitMijingGenerateAvatar
36093:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36093 --source api/mijing_rcmd/grpc_mijing_rcmd.proto --lang go --method /ga.api.mijing_rcmd.MijingRcmdLogic/StopMijingGenerateAvatar
36094:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36094 --source api/mijing_rcmd/grpc_mijing_rcmd.proto --lang go --method /ga.api.mijing_rcmd.MijingRcmdLogic/GetMijingGenerateAvatar
