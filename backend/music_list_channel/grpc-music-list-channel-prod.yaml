apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-music-list-channel-logic
spec:
  gateways:
  - quicksilver/tt-api-gateway
  - quicksilver/tt-api-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.music_list_channel.MusicListChannelLogic/
    rewrite:
      uri: /logic.MusicListChannelLogic/
    delegate:
       name: music-list-channel-logic-delegator-80
       namespace: quicksilver


