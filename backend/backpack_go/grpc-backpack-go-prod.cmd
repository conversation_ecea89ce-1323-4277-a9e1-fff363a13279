# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

36900:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36900 --source api/backpack_go/grpc_backpack_go.proto --lang go --method /ga.api.backpack_go.BackpackLogicGo/GetUserBackpack
36901:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36901 --source api/backpack_go/grpc_backpack_go.proto --lang go --method /ga.api.backpack_go.BackpackLogicGo/UseFuncCard
36902:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36902 --source api/backpack_go/grpc_backpack_go.proto --lang go --method /ga.api.backpack_go.BackpackLogicGo/GetUserFuncCardUse
36903:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36903 --source api/backpack_go/grpc_backpack_go.proto --lang go --method /ga.api.backpack_go.BackpackLogicGo/GetUserFragment
36904:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 36904 --source api/backpack_go/grpc_backpack_go.proto --lang go --method /ga.api.backpack_go.BackpackLogicGo/GetImBackpack
