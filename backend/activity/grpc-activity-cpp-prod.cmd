# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

1220:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 1220 --source api/activity/grpc_activity_cpp.proto --lang cpp --method /ga.api.activity.ActivityLogic/AwardUserTTGiftPkg
1221:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 1221 --source api/activity/grpc_activity_cpp.proto --lang cpp --method /ga.api.activity.ActivityLogic/CheckFirstRechargeActEntry
1222:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 1222 --source api/activity/grpc_activity_cpp.proto --lang cpp --method /ga.api.activity.ActivityLogic/GetMutiLocationActEntry
2246:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 2246 --source api/activity/grpc_activity_cpp.proto --lang cpp --method /ga.api.activity.ActivityLogic/GetCommonWebActiveBreakingInfo
2706:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 2706 --source api/activity/grpc_activity_cpp.proto --lang cpp --method /ga.api.activity.ActivityLogic/GetYearActRankingListEntry
2707:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 2707 --source api/activity/grpc_activity_cpp.proto --lang cpp --method /ga.api.activity.ActivityLogic/GetCommonRankingListConfig
2708:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 2708 --source api/activity/grpc_activity_cpp.proto --lang cpp --method /ga.api.activity.ActivityLogic/GetAnchorRankingListConfig
30080:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30080 --source api/activity/grpc_activity_cpp.proto --lang cpp --method /ga.api.activity.ActivityLogic/GetGangupTabAdvConf
400:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 400 --source api/activity/grpc_activity_cpp.proto --lang cpp --method /ga.api.activity.ActivityLogic/GetMyFirstsVoucher
