# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

96030:
api-route-configurator --etcd-endpoints *************:2379 create --id 96030 --source api/offer_room/grpc_offer_room.proto --lang go --method /ga.api.offer_room.OfferRoom/OfferRoomGetApplyList
96031:
api-route-configurator --etcd-endpoints *************:2379 create --id 96031 --source api/offer_room/grpc_offer_room.proto --lang go --method /ga.api.offer_room.OfferRoom/OfferRoomUserApply
96032:
api-route-configurator --etcd-endpoints *************:2379 create --id 96032 --source api/offer_room/grpc_offer_room.proto --lang go --method /ga.api.offer_room.OfferRoom/OfferRoomUserCancelApply
96033:
api-route-configurator --etcd-endpoints *************:2379 create --id 96033 --source api/offer_room/grpc_offer_room.proto --lang go --method /ga.api.offer_room.OfferRoom/OfferRoomGetCurOfferingGameInfo
96034:
api-route-configurator --etcd-endpoints *************:2379 create --id 96034 --source api/offer_room/grpc_offer_room.proto --lang go --method /ga.api.offer_room.OfferRoom/OfferRoomGetOfferingConfig
96035:
api-route-configurator --etcd-endpoints *************:2379 create --id 96035 --source api/offer_room/grpc_offer_room.proto --lang go --method /ga.api.offer_room.OfferRoom/OfferRoomSubmitOfferingSetting
96036:
api-route-configurator --etcd-endpoints *************:2379 create --id 96036 --source api/offer_room/grpc_offer_room.proto --lang go --method /ga.api.offer_room.OfferRoom/OfferRoomNamePriceOnce
96037:
api-route-configurator --etcd-endpoints *************:2379 create --id 96037 --source api/offer_room/grpc_offer_room.proto --lang go --method /ga.api.offer_room.OfferRoom/OfferRoomNamePriceMax
96038:
api-route-configurator --etcd-endpoints *************:2379 create --id 96038 --source api/offer_room/grpc_offer_room.proto --lang go --method /ga.api.offer_room.OfferRoom/OfferRoomOfferingSet
96039:
api-route-configurator --etcd-endpoints *************:2379 create --id 96039 --source api/offer_room/grpc_offer_room.proto --lang go --method /ga.api.offer_room.OfferRoom/OfferRoomOfferingPass
96040:
api-route-configurator --etcd-endpoints *************:2379 create --id 96040 --source api/offer_room/grpc_offer_room.proto --lang go --method /ga.api.offer_room.OfferRoom/OfferRoomOfferingEnd
96041:
api-route-configurator --etcd-endpoints *************:2379 create --id 96041 --source api/offer_room/grpc_offer_room.proto --lang go --method /ga.api.offer_room.OfferRoom/OfferRoomOfferingRelationships
96042:
api-route-configurator --etcd-endpoints *************:2379 create --id 96042 --source api/offer_room/grpc_offer_room.proto --lang go --method /ga.api.offer_room.OfferRoom/OfferRoomDeleteRelationship
96043:
api-route-configurator --etcd-endpoints *************:2379 create --id 96043 --source api/offer_room/grpc_offer_room.proto --lang go --method /ga.api.offer_room.OfferRoom/OfferRoomOfferingInit
96044:
api-route-configurator --etcd-endpoints *************:2379 create --id 96044 --source api/offer_room/grpc_offer_room.proto --lang go --method /ga.api.offer_room.OfferRoom/OfferRoomCardInfo
96045:
api-route-configurator --etcd-endpoints *************:2379 create --id 96045 --source api/offer_room/grpc_offer_room.proto --lang go --method /ga.api.offer_room.OfferRoom/OfferRoomNameplateConfig
96046:
api-route-configurator --etcd-endpoints *************:2379 create --id 96046 --source api/offer_room/grpc_offer_room.proto --lang go --method /ga.api.offer_room.OfferRoom/OfferRoomGetUserNameplateInfo
