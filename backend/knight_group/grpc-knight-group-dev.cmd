# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

3771:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 3771 --source api/knight_group/grpc_knight_group.proto --lang go --method /ga.api.knight_group.KnightGroupLogic/JoinKnightGroup
3772:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 3772 --source api/knight_group/grpc_knight_group.proto --lang go --method /ga.api.knight_group.KnightGroupLogic/GetKnightLoveRank
3773:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 3773 --source api/knight_group/grpc_knight_group.proto --lang go --method /ga.api.knight_group.KnightGroupLogic/GetKnightWeekRank
3778:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 3778 --source api/knight_group/grpc_knight_group.proto --lang go --method /ga.api.knight_group.KnightGroupLogic/GetKnightMission
3779:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 3779 --source api/knight_group/grpc_knight_group.proto --lang go --method /ga.api.knight_group.KnightGroupLogic/GetKnightGroupCampInfo
