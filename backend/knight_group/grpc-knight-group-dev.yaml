apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-knight-group-logic
spec:
  gateways:
  - istio-system/apiv2-gateway
  - istio-system/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.knight_group.KnightGroupLogic/
    rewrite:
      uri: /logic.KnightGroupLogic/
    delegate:
       name: knight-group-logic-delegator-80
       namespace: quicksilver


