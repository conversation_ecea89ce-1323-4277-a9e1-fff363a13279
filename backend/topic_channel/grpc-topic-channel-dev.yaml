apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-topic-channel-logic
spec:
  gateways:
  - istio-system/apiv2-gateway
  - istio-system/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.topic_channel.TopicChannelLogic/
    rewrite:
      uri: /logic.topic_channel.TopicChannelLogic/
    delegate:
       name: topic-channel-logic-delegator-80
       namespace: quicksilver


