cmdInfoList:
  - source: api/slip_note/grpc_slip_note.proto
    cmd: 30581
    lang: go
    method: /ga.api.slip_note.SlipNoteLogic/GetSlipNoteConfig
  - source: api/slip_note/grpc_slip_note.proto
    cmd: 30582
    lang: go
    method: /ga.api.slip_note.SlipNoteLogic/PublishSlipNote
  - source: api/slip_note/grpc_slip_note.proto
    cmd: 30583
    lang: go
    method: /ga.api.slip_note.SlipNoteLogic/CloseSlipNote
  - source: api/slip_note/grpc_slip_note.proto
    cmd: 30584
    lang: go
    method: /ga.api.slip_note.SlipNoteLogic/GetSlipNoteStatus
  - source: api/slip_note/grpc_slip_note.proto
    cmd: 30585
    lang: go
    method: /ga.api.slip_note.SlipNoteLogic/PickSlipNote
  - source: api/slip_note/grpc_slip_note.proto
    cmd: 30586
    lang: go
    method: /ga.api.slip_note.SlipNoteLogic/SetSlipNote
  - source: api/slip_note/grpc_slip_note.proto
    cmd: 30587
    lang: go
    method: /ga.api.slip_note.SlipNoteLogic/FetchSlipNote
  - source: api/slip_note/grpc_slip_note.proto
    cmd: 30588
    lang: go
    method: /ga.api.slip_note.SlipNoteLogic/CommentToSlipNote
  - source: api/slip_note/grpc_slip_note.proto
    cmd: 30589
    lang: go
    method: /ga.api.slip_note.SlipNoteLogic/PullSlipNoteCommentList
  - source: api/slip_note/grpc_slip_note.proto
    cmd: 30590
    lang: go
    method: /ga.api.slip_note.SlipNoteLogic/ReportSlipNoteStatus
  - source: api/slip_note/grpc_slip_note.proto
    cmd: 30591
    lang: go
    method: /ga.api.slip_note.SlipNoteLogic/GetAccountsForSlipNote

