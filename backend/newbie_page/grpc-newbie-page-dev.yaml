apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-newbie-page-logic
spec:
  gateways:
  - istio-system/apiv2-gateway
  - istio-system/apiv2-gateway-internal
  hosts:
  - *
  http:
  - match:
    - uri:
        prefix: /ga.api.newbie_page.NewbiePageLogic/
    route:
    - destination:
        host: newbie-page-logic.quicksilver.svc.cluster.local
        port:
          number: 80
