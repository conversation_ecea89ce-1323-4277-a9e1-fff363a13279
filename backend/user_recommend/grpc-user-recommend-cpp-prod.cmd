# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

1180:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 1180 --source api/user_recommend/grpc_user_recommend_cpp.proto --lang cpp --method /ga.api.user_recommend.UserRecommendLogic/RejectRecommend
1181:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 1181 --source api/user_recommend/grpc_user_recommend_cpp.proto --lang cpp --method /ga.api.user_recommend.UserRecommendLogic/AddOrUpdateContacts
1182:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 1182 --source api/user_recommend/grpc_user_recommend_cpp.proto --lang cpp --method /ga.api.user_recommend.UserRecommendLogic/GetRecommendFromContacts
1183:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 1183 --source api/user_recommend/grpc_user_recommend_cpp.proto --lang cpp --method /ga.api.user_recommend.UserRecommendLogic/GetUserRecommend
1184:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 1184 --source api/user_recommend/grpc_user_recommend_cpp.proto --lang cpp --method /ga.api.user_recommend.UserRecommendLogic/ChangeRecommendStatus
1185:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 1185 --source api/user_recommend/grpc_user_recommend_cpp.proto --lang cpp --method /ga.api.user_recommend.UserRecommendLogic/GetRecommendStatus
2460:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 2460 --source api/user_recommend/grpc_user_recommend_cpp.proto --lang cpp --method /ga.api.user_recommend.UserRecommendLogic/GetFindFriendExamEntrance
2461:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 2461 --source api/user_recommend/grpc_user_recommend_cpp.proto --lang cpp --method /ga.api.user_recommend.UserRecommendLogic/GetFindFriendExamQuestion
2462:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 2462 --source api/user_recommend/grpc_user_recommend_cpp.proto --lang cpp --method /ga.api.user_recommend.UserRecommendLogic/AddUserFindFriendExam
2463:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 2463 --source api/user_recommend/grpc_user_recommend_cpp.proto --lang cpp --method /ga.api.user_recommend.UserRecommendLogic/GetUserFindFriendExamResult
2464:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 2464 --source api/user_recommend/grpc_user_recommend_cpp.proto --lang cpp --method /ga.api.user_recommend.UserRecommendLogic/MatchingFriend
2465:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 2465 --source api/user_recommend/grpc_user_recommend_cpp.proto --lang cpp --method /ga.api.user_recommend.UserRecommendLogic/UploadExamInstallRecommendGame
2466:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 2466 --source api/user_recommend/grpc_user_recommend_cpp.proto --lang cpp --method /ga.api.user_recommend.UserRecommendLogic/CheckUserFinishFindFriendExam
3301:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 3301 --source api/user_recommend/grpc_user_recommend_cpp.proto --lang cpp --method /ga.api.user_recommend.UserRecommendLogic/UserTagMatchBegin
3302:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 3302 --source api/user_recommend/grpc_user_recommend_cpp.proto --lang cpp --method /ga.api.user_recommend.UserRecommendLogic/GetUserTagMatchUsers
3303:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 3303 --source api/user_recommend/grpc_user_recommend_cpp.proto --lang cpp --method /ga.api.user_recommend.UserRecommendLogic/UserTagMatchPick
5065:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 5065 --source api/user_recommend/grpc_user_recommend_cpp.proto --lang cpp --method /ga.api.user_recommend.UserRecommendLogic/GetContractInfo
