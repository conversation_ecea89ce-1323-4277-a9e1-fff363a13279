# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

100001:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 100001 --source api/super_channel/grpc_super_channel.proto --lang go --method /ga.api.super_channel.SuperChannelLogic/Enter
100002:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 100002 --source api/super_channel/grpc_super_channel.proto --lang go --method /ga.api.super_channel.SuperChannelLogic/Quit
100003:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 100003 --source api/super_channel/grpc_super_channel.proto --lang go --method /ga.api.super_channel.SuperChannelLogic/HoldMic
100004:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 100004 --source api/super_channel/grpc_super_channel.proto --lang go --method /ga.api.super_channel.SuperChannelLogic/ReleaseMic
100005:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 100005 --source api/super_channel/grpc_super_channel.proto --lang go --method /ga.api.super_channel.SuperChannelLogic/SetMicStatus
100006:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 100006 --source api/super_channel/grpc_super_channel.proto --lang go --method /ga.api.super_channel.SuperChannelLogic/SetMicMode
100007:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 100007 --source api/super_channel/grpc_super_channel.proto --lang go --method /ga.api.super_channel.SuperChannelLogic/GetMicList
100008:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 100008 --source api/super_channel/grpc_super_channel.proto --lang go --method /ga.api.super_channel.SuperChannelLogic/GetMemberList
100009:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 100009 --source api/super_channel/grpc_super_channel.proto --lang go --method /ga.api.super_channel.SuperChannelLogic/GetExtInfo
100011:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 100011 --source api/super_channel/grpc_super_channel.proto --lang go --method /ga.api.super_channel.SuperChannelLogic/ReplyHoldMicInvite
100012:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 100012 --source api/super_channel/grpc_super_channel.proto --lang go --method /ga.api.super_channel.SuperChannelLogic/ChangeMic
100013:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 100013 --source api/super_channel/grpc_super_channel.proto --lang go --method /ga.api.super_channel.SuperChannelLogic/GetChannelList
100014:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 100014 --source api/super_channel/grpc_super_channel.proto --lang go --method /ga.api.super_channel.SuperChannelLogic/CplSearch
100015:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 100015 --source api/super_channel/grpc_super_channel.proto --lang go --method /ga.api.super_channel.SuperChannelLogic/SendHoldMicInvite
100016:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 100016 --source api/super_channel/grpc_super_channel.proto --lang go --method /ga.api.super_channel.SuperChannelLogic/GetSuperChannelSchemeInfo
