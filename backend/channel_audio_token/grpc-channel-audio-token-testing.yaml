apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-channel-audio-token-logic
spec:
  gateways:
  - istio-ingress/apiv2-gateway
  - istio-ingress/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.channel_audio_token.ChannelAudioTokenLogic/
    rewrite:
      uri: /logic.ChannelAudioTokenLogic/
    delegate:
       name: channel-audio-token-logic-delegator-80
       namespace: quicksilver


