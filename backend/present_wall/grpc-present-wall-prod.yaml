apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-present-wall-logic
spec:
  gateways:
  - quicksilver/tt-api-gateway
  - quicksilver/tt-api-gateway-internal
  hosts:
  - *
  http:
  - match:
    - uri:
        prefix: /ga.api.present_wall.PresentWallLogic/
    route:
    - destination:
        host: present-wall-logic.quicksilver.svc.cluster.local
        port:
          number: 80
