syntax = "proto3";

package ga.youknowwhologic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/youknowwhologic";


// 开通状态(该状态受过期时间影响)
enum UKWOpenType {
    UKW_NO_OPEN   = 0;                                    // 未开通/已过期
    UKW_OPEN      = 1;                                    // 已开通
    UKW_FREEZE    = 2;                                    // 已冻结
    UKW_BAN       = 3;                                    // 强制封禁
}
// 开关状态（该状态受开通状态影响）
enum UKWSwitchType {
    UKW_SWITCH_OFF = 0;                                   // 神秘人关闭
    UKW_SWITCH_ON = 1;                                    // 神秘人开启
}

// 显示状态（该状态受开通状态、开关状态等影响）
enum UKWOpenStatus {
    UKW_OFF = 0;                                          // 神秘人关闭状态
    UKW_ON = 1;                                           // 神秘人开启状态
}

// 周榜/爱意榜开关（该状态受开关状态影响）
enum RankSwitchType {
    UKW_RANK_SWITCH_OFF = 0;                              // 周榜、爱意榜开关关闭
    UKW_RANK_SWITCH_ON = 1;                               // 周榜、爱意榜开关打开
}

// 神秘人勋章状态
enum MedalType {
    MEDAL_TYPE_NOT_WEAR = 0;                              // 不佩戴勋章
    MEDAL_TYPE_WEAR = 1;                                  // 佩戴勋章
}

// 神秘人权限信息
message UKWPermissionInfo {
    uint32 uid = 1;                                       // 神秘人Uid（废弃）
    string nickname = 2;                                  // 神秘人昵称，神秘人数字编号（举例：神秘人5628）
    UKWOpenType status = 3;                               // 神秘人开通状态，详见UKWOpenType
    UKWSwitchType switch = 4;                             // 神秘人开关状态，详见UKWSwitchType
    uint32 expire_time = 5;                               // 当前身份结束时间
    uint32 server_time = 6;                               // 当前服务时间
    uint32 level = 7;                                     // 神秘人等级
    RankSwitchType rank_switch = 8;                       // 周榜、排行榜开关
    uint32 fake_uid = 9;                                  // 神秘人假UID
    UKWOpenStatus is_open = 10;                           // 是否作为神秘人显示神秘人信息
    UKWSwitchType enter_notice_switch = 11;               // 是否开启神秘人进房提醒开关，详见UKWSwitchType
    string fake_account = 12;                             // 当前神秘人的账号
}

// 神秘人人物信息
message UKWPersonInfo {
    uint32 uid = 1;                                       // 神秘人Uid（废弃）
    string nickname = 2;                                  // 神秘人昵称，神秘人数字编号（举例：神秘人5628）
    string account = 3;                                   // 神秘人账号拼接头像
    string medal = 4;                                     // 勋章属性
    uint32 level = 5;                                     // 角色等级，目前默认为1，0为无效
    string head_frame = 6;                                // 神秘人头像框（静态）
}

// 神秘人个人主页/资料卡勋章
message UKWMedal {
    MedalType type = 1;                                   // 神秘人勋章状态
    string medal_url = 2;                                 // 勋章资源链接（这里根据状态自动适配置灰或正常显示）
    string jump_url = 3;                                  // 跳转链接（这里根据登陆设备信息自动适配）
    bool need_notice = 4;                                 // 是否需要提示再次购买
    string notice = 5;                                    // 浮层展示文案
    bool is_expired = 6;                                  // 用户是否过期
}

// 神秘人信息
message UKWInfo {
    uint32 uid = 1;                              // 获取到的神秘人uid（废弃）
    UKWPermissionInfo ukw_permission_info = 2;   // 神秘人权限信息
    UKWPersonInfo ukw_person_info = 3;           // 神秘人人物信息
    UKWMedal ukw_medal = 4;                      // 神秘人勋章信息
}


message OpenUKWReq {
    ga.BaseReq base_req = 1;
}

message OpenUKWResp {
    ga.BaseResp base_resp = 1;
    uint32 server_time = 2;          // 服务生效时间
    uint32 expire_time = 3;          // 当前身份结束时间
}

message ChangeUKWSwitchReq {
    ga.BaseReq base_req = 1;
    UKWSwitchType status = 2;//开关状态
    uint32 channel_id = 3; // 需要进入的房间ID
}

message ChangeUKWSwitchResp{
    ga.BaseResp base_resp = 1;
}

// 神秘人信息
message GetUKWInfoReq {
    ga.BaseReq base_req = 1;
}

message GetUKWInfoResp {
    ga.BaseResp base_resp = 1;
    UKWInfo ukw_info = 2;                      // 神秘人信息
}

// 获取神秘人人物信息
message GetUKWUserProfileReq {
    ga.BaseReq base_req = 1;
    uint32 uid = 2;                           // 根据uid进行查询
    bool is_personal_homepage = 3;            // 是否来自个人主页请求
}

message GetUKWUserProfileResp {
    ga.BaseResp base_resp = 1;
    uint32 uid = 2;                           // 神秘人uid
    ga.UserProfile user_profile = 3;          // 神秘人用户信息
    ga.UserUKWInfo user_ukw_info = 4;         // 神秘人信息
    UKWMedal ukw_medal = 5;                      // 神秘人勋章信息
}

// 批量获取神秘人信息
message BatchGetUKWInfosReq {
    ga.BaseReq base_req = 1;
    uint32 page_size = 2;                                 // 分页大小，默认大小200
    uint32 page_num = 3;                                  // 页号，起始值为1
    repeated uint32 uid_list = 4;                         // 根据神秘人uid列表获取，若为空则获取所有
}
message BatchGetUKWInfosResp {
    ga.BaseResp base_resp = 1;
    repeated UKWInfo ukw_infos = 2;                       // 神秘人信息
    uint32 page_size = 3;                                 // 当前分页大小，仅当获取所有的时候使用
    uint32 page_num = 4;                                  // 当前页号，起始值为1
    uint32 count = 5;                                     // 获取到的神秘人信息数量
}

// 神秘人周榜/爱意榜开关
message ChangeRankSwitchReq {
    ga.BaseReq base_req = 1;
    RankSwitchType rank_switch = 2;  // 周榜/爱意榜开关设置值
}
message ChangeRankSwitchResp {
    ga.BaseResp base_resp = 1;
    RankSwitchType rank_switch = 2;  // 当前周榜/爱意榜开关设置值状态
}

// 用户切换神秘人是否进房询问身份切换提醒开关
message UserChangeUKWEnterNoticeReq {
    ga.BaseReq base_req = 1;
    UKWSwitchType enter_notice_switch = 2;                // 需要变更的状态，仅可使用主动关闭或开通
}
message UserChangeUKWEnterNoticeResp {
    ga.BaseResp base_resp = 1;
    UKWSwitchType enter_notice_switch = 2;                // 变更后的状态
}

// ------------------------------------------
// 神秘人消息推送

// UKWChangeChannelMsg 神秘人房间消息通知
message UKWChangeChannelMsg {
    uint32 uid = 1;                  // 神秘人Uid（废弃）
    UKWOpenType status = 2;          // 当前神秘人状态，详见UKWOpenType
    UKWSwitchType switch = 3;        // 当前神秘人开关状态，详见UKWSwitchType
    string nickname = 4;             // 当前神秘人昵称
    string account = 5;              // 当前神秘人的account
    uint32 server_time = 6;          // 服务时间
    uint32 expire_time = 7;          // 当前身份结束时间
    RankSwitchType rank_switch = 8;  // 周榜/爱意榜同步开关
    uint32 fake_uid = 9;             // 神秘人假uid
    UKWOpenStatus is_open = 10;       // 是否作为神秘人显示神秘人信息
}

// UKWPermissionChangeMsg 神秘人用户神秘人权益变更消息
message UKWPermissionChangeMsg {
    uint32 fake_uid = 1;             // 神秘人假uid
    UKWOpenType status = 2;          // 当前神秘人状态，详见UKWOpenType
    UKWSwitchType switch = 3;        // 当前神秘人开关状态，详见UKWSwitchType
    string nickname = 4;             // 当前神秘人昵称
    string account = 5;              // 当前神秘人的account
    uint32 server_time = 6;          // 服务时间
    uint32 expire_time = 7;          // 当前身份结束时间
    RankSwitchType rank_switch = 8;  // 周榜/爱意榜同步开关
    UKWOpenStatus is_open = 9;       // 是否作为神秘人显示神秘人信息
    UKWSwitchType enter_notice_switch = 10; // 是否开启神秘人进房提醒开关，详见UKWSwitchType
}

// UKWOpenPushInfo 神秘人开通后全服推送结构
message UKWOpenPushInfo {
    string nickname = 1;             // 当前uid的真实昵称
    string account = 2;              // 当前uid真实account
    uint32 ukw_order_type_level = 3; // 神秘人订单推送等级
}

message UKWExposureChannelMsg {
    string ukw_nickname = 1;          // 神秘人昵称
    string ukw_account = 2;           // 神秘人账号
    string head_frame = 3;            // 神秘人头像框
    string nickname = 4;              // 对应用户昵称
    string account = 5;               // 对应用户账号
    uint32 uid = 6;                   // 对应用户uid
    uint32 sex = 7;                   // 对应用户性别
    uint32 old_fake_uid = 8;          // 神秘人现身之前的假uid
}

message ShowUpMsg {
    uint32 send_uid = 1;
    string send_nickname = 2;
    string send_account  = 3;
    string msg_text      = 4; //发消息时不用客户端传
    int64 weight         = 5; //用于排序大的排前面
    uint32 count         = 6; //发消息次数
}

//发互动消息
message SendShowUpMsgReq{
    ga.BaseReq base_req = 1;
    ShowUpMsg msg = 2;
    uint32 receiver_uid = 3;
    uint32 channel_id   = 4;
}

message SendShowUpMsgResp{
    ga.BaseResp base_resp = 1;
}

//取收到的消息列表
message GetShowUpMsgListReq{
    ga.BaseReq base_req = 1;
    uint32 uid = 2;
    uint32 channel_id = 3;
}

message GetShowUpMsgListResp{
    ga.BaseResp base_resp = 1;
    repeated ShowUpMsg msg_list = 2;
    uint32 sender_cnt = 3;
}

//取随机文案列表
message GetShowUpTextListReq{
    ga.BaseReq base_req = 1;
}

message GetShowUpTextListResp{
    ga.BaseResp base_resp = 1;
    repeated string text_list = 2;
}

// 神秘人现身
message ExposureUKWReq {
    ga.BaseReq base_req = 1;
}
message ExposureUKWResp {
    ga.BaseResp base_resp = 1;
}