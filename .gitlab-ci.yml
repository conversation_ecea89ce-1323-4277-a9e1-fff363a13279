#GIT_STRATEGY: none
#GIT策略，定义拉取代码的方式，有3种：clone/fetch/none，默认为clone，速度最慢，每步job都会重新clone一次代码。我们一般将它设置为none，在具体任务里设置为fetch就可以满足需求，毕竟不是每步都需要新代码，那也不符合我们测试的流程
#在job中可以用${GIT_STRATEGY}来使用这个变量。常用的预定义变量有CI_COMMIT_REF_NAME（项目所在的分支或标签名称），CI_JOB_NAME（任务名称），CI_JOB_STAGE（任务阶段）

before_script:
  - |
    ifconfig | grep "inet "    
    pwd
    export LC_ALL=en_US.UTF-8
    export LANG="en_US.UTF-8" 
#    export
# 全局变量，保证不同stage不会每次重置代码
# 某些步骤需要可以在Job中添加（如结果回调中）

#after_script:
#  - |
#    pwd

stages:
  - 目标分支检测
  - 协议路径检查
  - tt-buf-lint
#  - tt-buf-breaking
  - tt-lint
  - tt-breaking
  - iOS_协议_检测
  - iOS_协议_重复_检测
  - 结果处理
  - tt-generate-backend-file

tt_root_keeper:
  stage: 协议路径检查
  script:
    - echo ${CI_REPOSITORY_URL}
    - cd ${CI_PROJECT_DIR}
    - echo $(pwd)
    - echo ${CI_COMMIT_SHORT_SHA}
    - ./ci/rootkeeper.sh
    - echo "var $?"
  tags:
    - tt-buf
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "master"'
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"'

tt_buf_lint_check:
  stage: tt-buf-lint
  script:
    - echo ${CI_REPOSITORY_URL}
    - cd ${CI_PROJECT_DIR}
    - echo $(pwd)
    - echo ${CI_COMMIT_SHORT_SHA}
    - /usr/local/bin/buf lint
    - echo "var $?"
  tags:
    - tt-buf
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "master"'
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"'
#  artifacts:
#    expire_in: 1 day
#    when: always
#    paths:
#      - .ci_result_file

#tt_buf_breaking_check:
#  stage: tt-buf-breaking
#  script:
#    - cd ${CI_PROJECT_DIR}
#    - echo $(pwd)
#    - echo ${CI_COMMIT_SHORT_SHA}
#    - /usr/local/bin/buf breaking --against '.git#branch=origin/master'
#    - echo "var $?"
#  tags:
#    - tt-buf
#  rules:
#    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "master"'
#    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"'
##  artifacts:
##    expire_in: 1 day
##    when: always
##    paths:
##      - .ci_result_file

# #1.输出环境变量
# #gitlab系统自带环境变量：http://gitlab.code.mob.com/help/ci/variables/predefined_variables.md
oc_rule_scan: 
  stage: iOS_协议_检测
  script:
    - |
      echo "begin"
      tt ci stage begin -n "iOS协议检测" -p $CI_PIPELINE_URL -j $CI_JOB_URL -b ${CI_COMMIT_REF_NAME} -g ${CI_PROJECT_NAME}
      echo "2"
      CURRENT_PATH=`pwd`
      tt proto build --protoc=$CI_PROTOC_PATH --plugin=$CI_PROTOC_OC_PLUGIN_PATH --special=true --extra=$CI_PROTOC_PROTOBUF_SRC auto_ci
      CHECK=`tt ci stage check auto_ci`
      if [ $? -ne 0 ]; then
        exit 1
      else
        tt ci stage end
      fi            
  tags:
    - ios_proto
  variables:
    GIT_STRATEGY: clone
  artifacts:
    expire_in: 1 day
    when: always
    paths:
      - .ci_result_file

merge_oc_rule_scan: 
  stage: iOS_协议_检测
  script:
    - |
      echo "begin"
      tt ci stage begin -n "iOS协议检测" -p $CI_PIPELINE_URL -j $CI_JOB_URL -b ${CI_COMMIT_REF_NAME} -g ${CI_PROJECT_NAME}
      echo "2"
      CURRENT_PATH=`pwd`
      tt proto build --protoc=$CI_PROTOC_PATH --plugin=$CI_PROTOC_OC_PLUGIN_PATH --special=true --extra=$CI_PROTOC_PROTOBUF_SRC auto_ci
      CHECK=`tt ci stage check auto_ci`
      if [ $? -ne 0 ]; then
        exit 1
      else
        tt ci stage end
      fi  
  tags:
    - ios_proto
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME != "release/auto_gen_logicpb_from_quicksliver"'
  variables:
    GIT_STRATEGY: clone
  artifacts:
    expire_in: 1 day
    when: always
    paths:
      - .ci_result_file   

ios_proto_repeat_rule_scan: 
  stage: iOS_协议_重复_检测
  script:
    - |
      tt ci stage begin -n "iOS_协议_重复_检测" -p $CI_PIPELINE_URL -j $CI_JOB_URL -b ${CI_COMMIT_REF_NAME} -g ${CI_PROJECT_NAME}
      PROTO_REPEAT_CHECK=`tt proto check`
      if [ "$PROTO_REPEAT_CHECK" != "" ];then
        echo "$PROTO_REPEAT_CHECK"
        tt ci stage fail -d "$PROTO_REPEAT_CHECK"
        exit 1
      fi
      tt ci stage end
      
  tags:
    - ios_proto
  variables:
    GIT_STRATEGY: clone
  artifacts:
    expire_in: 1 day
    when: always
    paths:
      - .ci_result_file    

merge_ios_proto_repeat_rule_scan: 
  stage: iOS_协议_重复_检测
  script:
    - |
      tt ci stage begin -n "iOS_协议_重复_检测" -p $CI_PIPELINE_URL -j $CI_JOB_URL -b ${CI_COMMIT_REF_NAME} -g ${CI_PROJECT_NAME}
      PROTO_REPEAT_CHECK=`tt proto check`
      if [ "$PROTO_REPEAT_CHECK" != "" ];then
        echo "$PROTO_REPEAT_CHECK"
        tt ci stage fail -d "$PROTO_REPEAT_CHECK"
        exit 1
      fi
      tt ci stage end
  tags:
    - ios_proto
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME != "release/auto_gen_logicpb_from_quicksliver"'
  variables:
    GIT_STRATEGY: clone
  artifacts:
    expire_in: 1 day
    when: always
    paths:
      - .ci_result_file    


merge_result:
  stage: 结果处理
  script:
    - |
      tt ci stage report --project_id=84 --gitlab_token=********************
  when: on_failure
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME != "release/auto_gen_logicpb_from_quicksliver"'
  tags: 
    - ios_proto
  variables:
    GIT_STRATEGY: none


#5.结果处理
result:
  stage: 结果处理
  script:
    - |
      tt ci stage report --project_id=84 --gitlab_token=********************
  when: on_failure
  tags: 
    - ios_proto
  variables:
    GIT_STRATEGY: none

tt_lint_check:
  stage: tt-lint
  script:
    - echo ${CI_REPOSITORY_URL}
    - cd ${CI_PROJECT_DIR}
    - chmod +x ./ci/lint_check.sh
    - ./ci/lint_check.sh
    - echo "var $?"
  tags: 
    - tt-pbtools
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "master"'
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"'
  artifacts:
    expire_in: 1 day
    when: always
    paths:
      - .ci_result_file    

    
tt_breaking_check:
  stage: tt-breaking
  script:
    - cd ${CI_PROJECT_DIR}
    - echo ${CI_COMMIT_SHORT_SHA}
    - chmod +x ./ci/breaking_check.sh
    - ./ci/breaking_check.sh
    - echo "var $?"
  tags: 
    - tt-pbtools
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "master"'
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"'
  artifacts:
    expire_in: 1 day
    when: always
    paths:
      - .ci_result_file    


tt_generate_backend_file:
  stage: tt-generate-backend-file
  script:
    - cd ${CI_PROJECT_DIR}
    - echo ${CI_COMMIT_SHORT_SHA}
    - chmod +x ./ci/generate_backend_file.sh
    - ./ci/generate_backend_file.sh
    - echo "var $?"
  tags: 
    - tt-pbtools
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME != "release/auto_gen_logicpb_from_quicksliver"'
  artifacts:
    expire_in: 1 day
    when: always
    paths:
      - .ci_result_file    

    
merge_ios_target_master_check:
  stage: 目标分支检测
  script:
    - |
      echo "1"
      tt ci stage begin -n "目标分支检测" -p ${CI_PIPELINE_URL} -j $CI_JOB_URL -b ${CI_COMMIT_REF_NAME} -g ${CI_PROJECT_NAME}
      echo "2"
      if [ "${CI_MERGE_REQUEST_TARGET_BRANCH_NAME}" == "master" ];then              
        echo "3"
        tt gitlab commit_in_branch --commit=${CI_COMMIT_SHA} --project=84 --branch=develop auto_ci
        CHECK=`tt ci stage check auto_ci`
        if [ "$CHECK" == "0" ];then
          echo "4"
          tt ci stage fail -d "当前提交没有合并到develop; 请参考协议库规范发起合并:https://q9jvw0u5f5.feishu.cn/wiki/EPYuw6hEdiQZZFkBT87cR77dnGh"
          echo "5"
          exit 1
        else
          tt ci stage end
        fi                   
      fi
  tags:
    - ios_proto
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME != "release/auto_gen_logicpb_from_quicksliver"' 
  artifacts:
    expire_in: 1 day
    when: always
    paths:
      - .ci_result_file        
