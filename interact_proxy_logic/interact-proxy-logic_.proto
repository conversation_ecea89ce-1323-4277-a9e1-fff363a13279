syntax = "proto3";

package ga.interact_proxy_logic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/interact-proxy-logic";

message ReportCommonInvitationResultReq {
    ga.BaseReq base_req = 1;
    string invite_id = 2; //邀请id
    InviteStyle invite_style = 3;
    bytes invite_result_content = 4; //对应样式结构的序列化
}

message ReportCommonInvitationResultResp {
    ga.BaseResp base_resp = 1;
}

/////////////////////////////////////////////////

enum InviteStyle {
    NONE = 0;
    PUBLIC_SCREEN = 1; //房间公屏邀请，since v6.15.0
}

message PublicScreenInvitation {
    string content = 1;
    string accept_button_text = 2;
    string accept_button_jump_url = 3;
    string reject_button_text = 4;
    string reject_button_jump_url = 5;
    uint64 expire_at = 6; //公屏什么时候过期消失，毫秒。0则不过期
    uint32 channel = 7; //在哪个房间发公屏，防止已经切换了。0则不限制
}

message PublicScreenInvitationResult {
    uint32 click_result = 1; //点击结果，accept=1, reject=2
}

//邀请函推送
message CommonInvitationPush {
    string invite_id = 1; //邀请id
    uint32 business_id = 2; //业务ID
    uint32 from_uid = 3; //谁发起邀请
    uint32 to_uid = 4; //谁接收邀请
    map<string, string> ext = 5; //业务扩展数据，透传
    InviteStyle invite_style = 6;
    bytes invite_content = 7; //对应样式结构的序列化
    uint64 invite_at = 8; //发起邀请的时刻，毫秒
}

//业务枚举，填充在business_id
enum InvitationBusinessType {
    UNDEFINED = 0;
    MYSTERY_2MYROOM_INVITATION = 1; //谜境来我房间邀请
    MYSTERY_2GROUP_INVITATION = 2; // 谜境邀请进群
}
