syntax = "proto3";

import "common/common.proto";
// import "validate/validate.proto";
import "persona/options.proto";
import "persona/tt/channel.proto";
import "persona/tt/dimension.proto";



package rcmd.persona.tt.user;

option go_package = "golang.52tt.com/protocol/services/rcmd/persona/tt/user";

option java_package = "com.tt.protocol.services.rcmd.persona.tt.user";
option java_outer_classname = "UserProto";

message UserBasic {
    option (rcmd.persona.profileId) = 10001;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_basic";
//    option (rcmd.persona.process) = "index_es|10001,user_basic";
    option (rcmd.persona.process) = "hard_view";
    option (rcmd.persona.maxTtl) = 2592000000; //30天

    uint32 uid = 1;
    uint32 reg_time = 2;            //注册时间
    uint32 sex = 3;                 //性别  0 female, 1 male
    uint32 brithday = 4;            //生日 - tag_6
    uint32 age = 5;                 //年龄(空)
    uint32 age_group = 6;           //年龄分组(空)
    uint32 tag_2 = 7;               //标签-找人需求
    string tag_4 = 8;               //标签-游戏卡片列表
    string tag_4_13_opt1 = 9;       //标签-王者荣耀区服
    string tag_4_16_opt1 = 10;      //标签-和平精英区服
    string tag_4_13_opt2 = 11;      //标签-王者荣耀段位
    string tag_4_16_opt2 = 12;      //标签-和平精英段位
    string tag_4_13_opt3 = 13;      //标签-王者荣耀分路
    string tag_4_16_opt3 = 14;      //标签-和平精英地图

    // 已废弃，不再更新
    repeated string material_tag_names = 16 [(rcmd.persona.compress) = "dict"]; //素材标签, 字典压缩
    int64 business_id = 17; //媒体渠道id

    uint32 companion_channel_enter = 18; //  陪玩房间进入次数

    // 局部更新老的 user basic画像, 防止覆盖其它的新增字段
    message UserBasicUpdate {
        option(rcmd.persona.updateId) = 3;
        uint32 uid = 1;
        uint32 reg_time = 2;            //注册时间
        uint32 sex = 3;                 //性别  0 female, 1 male
        uint32 brithday = 4;            //生日 - tag_6
        uint32 age = 5;                 //年龄(空)
        uint32 age_group = 6;           //年龄分组(空)
        uint32 tag_2 = 7;               //标签-找人需求
        string tag_4 = 8;               //标签-游戏卡片列表
        string tag_4_13_opt1 = 9;       //标签-王者荣耀区服
        string tag_4_16_opt1 = 10;      //标签-和平精英区服
        string tag_4_13_opt2 = 11;      //标签-王者荣耀段位
        string tag_4_16_opt2 = 12;      //标签-和平精英段位
        string tag_4_13_opt3 = 13;      //标签-王者荣耀分路
        string tag_4_16_opt3 = 14;      //标签-和平精英地图
    }
    message CompanionChannelEnterIncr {
        option(rcmd.persona.profileId) = 1000104;
        uint32 companion_channel_enter = 1[(rcmd.persona.oper) = "incrby"];
    }
}

message UserAuthInfo {

    option (rcmd.persona.profileId) = 10017;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_auth";
    option (rcmd.persona.process) = "hard_view";

    uint32 uid                   = 1;
    string ip                    = 2;
    uint64 ts                    = 3;
    string phone                 = 4;
    string imei                  = 5;
    string idfa                  = 6;
    uint32 client_type           = 7;
    uint32 client_version        = 8;
    string device_id_hex         = 9;
    bool   is_anti               = 10;
    uint32 market_id             = 11;
    uint32 cmd_id                = 12;
    bool   is_auto_login         = 13;
    string pkg_channel           = 14;
    string sm_device_id          = 15;
    string device_model          = 16;
    int64  sdk_game_id           = 17;
    uint64 last_login_ts         = 18;
    string device_info           = 19;
    string oaid                  = 20;
    string android_id            = 21;
    rcmd.common.LocationInfo loc = 22;
    repeated string promotion_tag_list = 23 [(rcmd.persona.compress) = "dict"]; //千人千面素材标签, 字典压缩

    message PromotionTagListUpdate {
        option(rcmd.persona.profileId) = 1001701;
        repeated string promotion_tag_list = 1[(rcmd.persona.oper) = "set"];
    }

}

message UserOnlineState {
    option (rcmd.persona.profileId) = 10002;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_online_state";

    enum OnlineStatus {
        Offline = 0;
        Online = 1;
    }
    uint32 is_online = 1;           //在线状态#OnlineStatus
    string last_login_city = 2;     //最近活跃城市
    uint32 last_os_type = 3;        //最近手机操作系统
}

// 2021-12-27 业务没有再使用，为了释放v1 redis空间压力，进行删除
// https://q9jvw0u5f5.feishu.cn/docs/doccnbZmBjGV0AqOB08DliWxZie
message AccountState {
    option (rcmd.persona.profileId) = 10003;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "account_state";

    uint32 is_unusual = 1;       //账号是否异常
}

message UserOnMicState {
    option (rcmd.persona.profileId) = 10004;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_on_mic_state";

    enum OnMicStatus {
        OffMic = 0;
        OnMic = 1;
    }
    uint32 is_onmic = 3;            //在麦状态
}

message UserBlackProductionMark {
    option (rcmd.persona.profileId) = *********;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_black_production_mark";

    uint32 is_black_production = 1; // 黑产标记, 0不是黑产, 1是黑产 // [(validate.rules).uint32 = {in: [0,1]}]
}

message UserInferiorMark {
    option (rcmd.persona.profileId) = *********;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_inferior_mark";

    uint32 is_inferior = 1; // 劣质标记, 0不是劣质, 1是劣质 // [(validate.rules).uint32 = {in: [0,1]}]
}

message ImInfo {
    option (rcmd.persona.profileId) = 10005;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "im_info";

    uint32 im_count = 1;
    repeated uint32 im_members = 2;
    uint32 record_time = 3;
}

message UserOffline {
    option (rcmd.persona.profileId) = 10008;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_offline";
    option (rcmd.persona.process) = "hard_view";

    uint32 pref_tag_id = 1;
    uint32 pref_tag_id_kuolie = 2;
    uint32 friend_num = 3;
    uint32 friend_num_1 = 4;
    uint32 im_num = 5;
    uint32 im_num_1 = 6;
    uint32 post_num = 7;
    uint32 login_days = 8;
    double duration_ugc = 9;
    double duration_avg_ugc = 10;
    double duration_kuolie = 11;
    double duration_avg_kuolie = 12;
    double duration_small = 13;
    double duration_avg_small = 14;
    double friend_rate_1 = 15;   //男性好友比例
    double im_rate_1 = 16;        //im对象男性比例

    map<uint32, double> tf_idf = 17;     //近14天的游戏偏好

    uint32 user_channel_level = 18; // 用户房间等级
    uint32 has_exp_level_6_channel = 19; // 用户是否被曝光过等级为6的房间
    string tag_tf_idf = 20;  // 用户标签及对应tfidf值
    string tag_tf_idf_v1 = 21;  // v1版-用户标签及对应tfidf值

    map<string, string> ui_room_intent_tag_tf_28d = 22;     //近28天房间意图标签偏好tf值
    map<string, string> ui_room_intent_tag_tfidf_28d = 23;     //近28天房间意图标签偏好tfidf值

	//切换完成后会废弃这个字段
	// repeated string music_label = 24; //tabId#musiclabel

    map<string, string> u_p_room_para_tag_tfidf_map=25; //近28d用户对房间发布信息标签偏好tfidf值
	enum MUSICLABEL {
	Default = 0; // 无效
	ExcelProducer = 1; // 优秀生产者
	SocialKing = 2; // 社交牛b症
	ContentConsumer = 3; // 内容消费者
	ImpreciseUser = 4; // 非精准用户
	GoodSingle = 5; // ktv质量禁合
	BadChorus = 6; // ktv走音合唱
	PreferSingleSing = 7;  // ktv禁合用户-互动向
	PreferSingleListen = 8; // ktv禁合用户-消费向
    }
/*
	message MusicLabelUpdate {
        option(rcmd.persona.profileId) = 1000801;
        repeated string music_label = 1[(rcmd.persona.oper) = "set"];
    }
*/
    string u_p_room_audio_label = 26; //用户最新音色标签
}

message UserMusicOffline {
    option (rcmd.persona.profileId) = 110111700;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_music_offline";

  // 2022.09.07已下线
	repeated string music_label = 1; //tabId#musiclabel

	enum MUSICLABEL {
	Default = 0; // 无效
	ExcelProducer = 1; // 优秀生产者
	SocialKing = 2; // 社交牛b症
	ContentConsumer = 3; // 内容消费者
	ImpreciseUser = 4; // 非精准用户
	GoodSingle = 5; // ktv质量禁合
	BadChorus = 6; // ktv走音合唱
	PreferSingleSing = 7;  // ktv禁合用户-互动向
	PreferSingleListen = 8; // ktv禁合用户-消费向
    }

	uint32 star_level = 2; //ktv段位-星星数

	message MusicStarLevelUpdate {
        option(rcmd.persona.profileId) = 110111701;
        uint32 star_level = 1[(rcmd.persona.oper) = "set"];
    }
}



//下发玩伴离线用户侧画像
message PlaymateUser {
  option (rcmd.persona.profileId) = 10009;
  option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "playmate_user";

  uint32 u_p_rec_exposure_7d    = 1;
  uint32 u_p_rec_click_7d       = 2;
  double u_p_rec_clickrate_7d   = 3;
  uint32 u_p_rec_im_7d          = 4;
  double u_p_rec_imrate_7d      = 5;

  uint32 uid_follow7 = 6;     // 用户近7天关注次数
  uint32 uid_followed7 = 7;   // 用户近7天被关注次数
  uint32 uid_follow28 = 8;    // 用户近28天关注次数
  uint32 uid_followed28 = 9;  // 用户近28天被关注次数
}

message ChannelUser {
    option (rcmd.persona.profileId) = 10103;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "channel_user";
    option (rcmd.persona.process) = "hard_view";

    uint32 u_browse_count     = 1; //曝光次数
    uint32 u_click_count      = 2; //点击次数
    double u_click_rate      = 3;  //点击率
    map<uint32, rcmd.persona.tt.channel.ChannelExpDetail> u_exp_d_tag_id   = 4; // 近3天的对不同游戏的曝光点击数据
    map<uint32, rcmd.persona.tt.channel.ChannelEnterDetail> u_enter_d_tag_id   = 5; // 近7天 对不同游戏的进房转化数据

    uint32 u_enter_count = 6; //用户近7天UGC房进房次数
    uint32 u_team_count = 7; //用户近7天UGC房开黑次数
    double u_team_rate = 8; //用户近7天UGC房开黑率

    map<uint32, UserRoomPref> u_p_room_pref = 9; //用户近7天进房行为对房间的偏好, key为房间游戏类型
    UserSelectPref u_p_select_pref = 10; //用户近7天筛选条件对房间的偏好

    string u_pref_room_name_vec = 11; // 用户近14天偏好房间标题embeding

    map<uint32, UserSelectPref> u_p_select_tag_pref = 12; //用户近7天筛选条件对房间的偏好, key为房间游戏类型
    map<uint32, double> u_p_select_gender_pref = 13;  //用户近7天筛选条件对房主性别性别偏好

    map<string, double> u_p_game_pref_predict_score = 14;  //预测用户近14天对不同游戏的偏好分（即使用户对该游戏没有行为）, key为游戏id

    map<string, double> u_p_room_name_tag_pref_predict_score = 15; //预测用户近14天对不同房间标题标签的偏好分（仅限有点击行为用户）
    map<string, double> u_p_room_name_tag_pref_predict_score_v1 = 16;

    map<uint32, double> u_enter_28d_tf_tag_id   = 17; // 近28天的对不同游戏的点击tf值
    map<uint32, double> u_gangup_28d_tf_tag_id   =18; // 近28天的对不同游戏的开黑tf值
    map<uint32, double> u_filter_28d_tf_tag_id   = 19; // 近28天的对不同游戏的筛选tf值

}

message UserRoomPref {
    map<uint32,double> u_p_room_pref_mode = 1;     //用户近7天进房行为对房间模式偏好
    map<uint32,double> u_p_room_pref_social = 2;   //用户近7天进房行为对房间区服偏好
    map<uint32,double> u_p_room_pref_num = 3;       //用户近7天进房行为对房间人数偏好
    map<uint32,double> u_p_room_pref_theme = 4;     //用户近7天进房行为对房间主题偏好
    map<uint32,double> u_p_room_pref_route = 5;     //用户近7天进房行为对房间分路偏好
    map<uint32,double> u_p_room_pref_map = 6;       //用户近7天进房行为对房间地图偏好
    map<uint32,double> u_p_room_pref_level = 7;     //用户近7天进房行为对房间段位偏好
}

message UserSelectPref {
    map<uint32,double> u_p_select_pref_mode = 1;    //用户近7天筛选条件对房间模式偏好
    map<uint32,double> u_p_select_pref_social = 2;  //用户近7天筛选条件对房间区服偏好
    map<uint32,double> u_p_select_pref_gender = 3;  //用户近7天筛选条件对房间性别偏好
    map<uint32,double> u_p_select_pref_theme = 4;   //用户近7天筛选条件对房间主题偏好
    map<uint32,double> u_p_select_pref_route = 5;   //用户近7天筛选条件对房间分路偏好
    map<uint32,double> u_p_select_pref_map = 6;     //用户近7天筛选条件对房间地图偏好
    map<uint32,double> u_p_select_pref_level = 7;     //用户近7天筛选条件对房间段位偏好
}

// 是否核心用户相关的画像
message UserCore {
    option (rcmd.persona.profileId) = 10013;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "core_user";

    uint32 follow_count = 1;  //关注别人的次数
    uint32 post_count = 2;    //发布动态的次数
    uint32 enter_channel_count = 3; //进入房间的次数
    uint32 im_count = 4;  //主动发起im的次数

    string cp_id = 5; //渠道包ID

    string ch_type = 6; //渠道类型
    string tg_ch_type_id = 7; //推广渠道类型
    string media_id = 8; //媒体

    message SetCPId {
        option (rcmd.persona.profileId) = 1001301;
        string cp_id = 1; //渠道包ID
    }

}

// 开黑
message UserGangupRl {
    option(rcmd.persona.profileId) = 11021;
    option(rcmd.persona.app) = "user_gangup_rl";
    option(rcmd.persona.version) = 1;
    option(rcmd.persona.attr) = "facet";

    repeated uint32 gangup_time_list = 1[(rcmd.persona.type)="list", (rcmd.persona.decay)=1296000]; // 开黑时间序列, 15天

    message Append {
        option(rcmd.persona.profileId) = 1102101;
        repeated uint32 gangup_time_list = 1[(rcmd.persona.oper)="add"];
    }
}

// 单聊
message User7DayImRl {
    option(rcmd.persona.profileId) = 11022;
    option(rcmd.persona.app) = "user_7day_im_rl";
    option(rcmd.persona.version) = 1;
    option(rcmd.persona.attr) = "facet";

    repeated uint32 im_time_list = 1[(rcmd.persona.type)="list", (rcmd.persona.decay)=604800];  // 单聊时间序列, 每个值将在7d后坏死
    uint32 last_im_time = 2[(rcmd.persona.parasite)="im_time_list|last"];    // 最后一次单聊时间
    uint32 im_count = 3[(rcmd.persona.parasite)="im_time_list|len"];    // im聊天的次数

    message Append {
        option(rcmd.persona.profileId) = 1102201;
        repeated uint32 im_time_list = 1[(rcmd.persona.oper)="add"];
        uint32 last_im_time = 2;
    }
    message User7DayImSimpleRl {
        option(rcmd.persona.profileId) = 1102202;
        uint32 last_im_time = 1;    // 最后一次单聊时间
        uint32 im_count = 2; // im聊天的次数
    }
}

// 互聊
message UserMutualImRl {
    option(rcmd.persona.profileId) = 11023;
    option(rcmd.persona.app) = "user_mutual_im_rl";
    option(rcmd.persona.version) = 1;
    option(rcmd.persona.attr) = "facet";

    repeated uint32 im_time_list = 1[(rcmd.persona.type)="list",(rcmd.persona.decay)=604800]; // 互聊时间序列, 7天

    message Append {
        option(rcmd.persona.profileId) = 1102301;
        repeated uint32 im_time_list = 1[(rcmd.persona.oper)="add"];
    }
}

/*
message UserHistoryAction {
    option(rcmd.persona.profileId) = 11018;
    option(rcmd.persona.version) = 1;
    option(rcmd.persona.app) = "user_social_behavior";
    option(rcmd.persona.attr)= "facet";

    repeated uint32 gangup_list = 1 [(rcmd.persona.track)="value_time"];      // 开黑列表
    repeated uint32 im_list = 2 [(rcmd.persona.track)="value_time"];          // 互撩聊天列表
    repeated uint32 room_owner_list = 3 [(rcmd.persona.track)="value_time"];  // 进房房主列表

    message GangUp {
        option(rcmd.persona.updateId) = 1;
        repeated uint32 gangup_list = 1 [(rcmd.persona.oper) = "set"];
    }
    message Im {
        option(rcmd.persona.updateId) = 2;
        repeated uint32 im_list = 1 [(rcmd.persona.oper) = "set"];
    }
    message Room {
        option(rcmd.persona.updateId) = 3;
        repeated uint32 room_owner_list = 1 [(rcmd.persona.oper) = "set"];
    }
    message AppendGangUp {
        option(rcmd.persona.updateId) = 4;
        repeated uint32 gangup_list = 1 [(rcmd.persona.oper) = "add"];
    }
    message AppendIm {
        option(rcmd.persona.updateId) = 5;
        repeated uint32 im_list = 1 [(rcmd.persona.oper) = "add"];
    }
    message AppendRoom {
        option(rcmd.persona.updateId) = 6;
        repeated uint32 room_owner_list = 1 [(rcmd.persona.oper) = "add"];
    }

    message DeleteGangUp {
        option(rcmd.persona.updateId) = 7;
        uint32 gangup_expire_cnt = 1 [(rcmd.persona.ref)="gangup_list",(rcmd.persona.oper) = "ltrim"];
    }
    message DeleteIm {
        option(rcmd.persona.updateId) = 8;
        uint32 im_expire_cnt = 1 [(rcmd.persona.ref)="im_list",(rcmd.persona.oper) = "ltrim"];
    }
    message DeleteRoom {
        option(rcmd.persona.updateId) = 9;
        uint32 room_expire_cnt = 1 [(rcmd.persona.ref)="room_owner_list",(rcmd.persona.oper) = "ltrim"];
    }
}
*/

// 用户关注关系
message UserFollowRL {
    option(rcmd.persona.profileId) = 11026;
    option(rcmd.persona.app) = "user_follow_rl";
    option(rcmd.persona.version) = 1;
    option(rcmd.persona.attr) = "facet";

    bool follow = 1;

    message Follow {
        option(rcmd.persona.profileId) = 1102601;
        bool follow = 1;
    }
}

// 用户加入的粉丝团关系
message UserFansRL {
    option(rcmd.persona.profileId) = 11036;
    option(rcmd.persona.app) = "user_fans_rl";
    option(rcmd.persona.version) = 1;
    option(rcmd.persona.attr) = "facet";
    // 因为要存储，属性不能为空，至少需要一个属性，尽管这个属性没什么用
    bool is_valid = 1;
    message IsValid {
        option(rcmd.persona.profileId) = 1103601;
        bool is_valid = 1;
    }
}


// 用户拉黑关系
message BlackUserRL {
    option(rcmd.persona.profileId) = 11027;
    option(rcmd.persona.app) = "black_user_rl";
    option(rcmd.persona.version) = 1;
    option(rcmd.persona.attr) = "facet";

    bool black = 1;

    message Black {
        option(rcmd.persona.profileId) = 1102701;
        bool black = 1;
    }
}

// 用户房间属性
message UserPersonalChannel {
    option(rcmd.persona.profileId) = 11030;
    option(rcmd.persona.version) = 1;
    option(rcmd.persona.app) = "user_channel_info";

    uint32 channel_id = 1;
    uint32 channel_type = 2; // 房间类型
    uint32 publish_time = 3; // 发布时间

    message UserChannelBasicUpdate{
        option(rcmd.persona.profileId) = 1103001;
        uint32 channel_id = 1;
        uint32 channel_type = 2; // 房间类型
        uint32 publish_time = 3; // 发布时间
    }
}

// 用户房间关系
message UserChannelRL {
    option(rcmd.persona.profileId) = 11031;
    option(rcmd.persona.app) = "user_channel_rl";
    option(rcmd.persona.version) = 1;
    option(rcmd.persona.attr) = "facet";

    bool own = 1;

    message Own {
        option(rcmd.persona.profileId) = 1103101;
        bool own = 1;
    }
}

// 直播房用户侧画像
message ChannelLiveUserOffline{
    option (persona.profileId) = 11037;
    option (persona.version) = 1;
    option (persona.app) = "channel_live_user_offline";

    uint32 u_p_live_login_cnt_3d = 1;
    uint32 u_p_live_enter_cnt_3d = 2;
    uint32 u_p_live_enter_room_cnt_3d = 3;
    uint32 u_p_live_total_duration_3d = 4;
    uint32 u_p_live_follow_room_cnt_3d = 5;
    uint32 u_p_live_fans_room_cnt_3d = 6;
    uint32 u_p_live_gift_room_cnt_3d = 7;
    uint32 u_p_live_gift_cnt_3d = 8;
    double u_p_live_gift_amt_3d = 9;
    double u_p_live_gift_max_amt_3d = 10;
    uint32 u_p_live_login_cnt_7d = 11;
    uint32 u_p_live_enter_cnt_7d = 12;
    uint32 u_p_live_enter_room_cnt_7d = 13;
    uint32 u_p_live_total_duration_7d = 14;
    uint32 u_p_live_follow_room_cnt_7d = 15;
    uint32 u_p_live_fans_room_cnt_7d = 16;
    uint32 u_p_live_gift_room_cnt_7d = 17;
    uint32 u_p_live_gift_cnt_7d = 18;
    double u_p_live_gift_amt_7d = 19;
    double u_p_live_gift_max_amt_7d = 20;
    uint32 u_p_live_login_cnt_14d = 21;
    uint32 u_p_live_enter_cnt_14d = 22;
    uint32 u_p_live_enter_room_cnt_14d = 23;
    uint32 u_p_live_total_duration_14d = 24;
    uint32 u_p_live_follow_room_cnt_14d = 25;
    uint32 u_p_live_fans_room_cnt_14d = 26;
    uint32 u_p_live_gift_room_cnt_14d = 27;
    uint32 u_p_live_gift_cnt_14d = 28;
    double u_p_live_gift_amt_14d = 29;
    double u_p_live_gift_max_amt_14d = 30;
    map<uint32, double> u_p_live_tag_id_pref_14d = 31;
    map<uint32, double> u_p_live_gender_pref_14d = 32;
    map<uint32, double> u_p_live_age_group_pref_14d = 33;
}
// 直播房房间侧离线画像 TML
message ChannelLiveUserOfflineTML{
    option (persona.profileId) = 110114600;
    option (persona.version) = 1;
    option (persona.app) = "pgc_live_user_offline_tml";

    string u_pgc_live_tag_id_validseq_14d = 1;
    string u_pgc_live_room_id_validseq_14d = 2;
    string u_pgc_live_tag_id_payseq_14d = 3;
    string u_pgc_live_room_id_payseq_14d = 4;
}
message UserSquareOffline {
    option (rcmd.persona.profileId) = 11039;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_square_offline";

    double	u_click_rec_poster_ratio_7d	= 1;  //用户一周推荐帖子点击率
    double	u_click_poster_ratio_7d	= 2;   //用户一周帖子点击率
    double	u_click_rec_poster_ratio_3d	= 3;
    double	u_click_poster_ratio_3d	= 4;
    uint32	u_user_last_login_city_level = 5;	//用户上次定位城市级别（一线、二线....）
    uint32	u_view_post_cnt_7d = 6;	//用户一周帖子浏览量
    uint32	u_view_rec_post_cnt_7d = 7;	//用户一周推荐帖子浏览量
    uint32	u_view_post_cnt_3d = 8;
    uint32	u_view_rec_post_cnt_3d	= 9;
    uint32	u_log_days_cnt_7d = 10;	//用户最近一周登录天数
    uint32	u_log_days_cnt_3d = 11;
    uint32	u_related_ucnt = 12;	//用户相关用户量
    string	u_post_type_click_ratio = 13;	//帖子类型点击比例
    int32   u_active_hour = 14; //用户最活跃的时间（小时）

    uint32  u_view_post_cnt_14d = 15;  //用户两周帖子浏览量
    uint32  u_view_rec_post_cnt_14d = 16;  //用户两周推荐帖子浏览量
    double  u_click_poster_ratio_14d = 17;  //用户两周帖子点击率
    double  u_click_rec_poster_ratio_14d = 18;  //用户两周推荐帖子点击率
}

message PublisherSquareOffline {
    option (rcmd.persona.profileId) = 11040;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "publisher_square_offline";

    double	p_avg_view_click_ratio_28d = 1;	//用户28天帖子平均点击率
    double	p_reply_comment_ratio_28d = 2;	    //用户28天评论回复率
    double	p_max_view_click_ratio_28d = 3;	    //用户28天帖子最大点击率
    uint32	p_user_last_login_city_level = 4;	    //用户上次定位城市级别（一线、二线....）
    uint32	p_publish_post_cnt_28d	= 5;    //用户28天发帖数
    uint32	p_viewed_post_cnt_28d = 6;	    //用户28天曝光帖子数
    uint32	p_fan_cnt = 7;	    //用户粉丝量
    uint32	p_male_fan_cnt = 8;   //用户男性粉丝量

    uint32 p_personal_page_view_ucnt_14d = 9;    //用户14天内个人主页被观看人数
    uint32 p_personal_page_view_ucnt_3d = 10;    //用户3天内个人主页被观看人数
    uint32 p_comment_cnt_sum_28d = 11;           //用户28天内帖子被评论量(pv)
    double p_avg_view_comment_ratio_28d = 12;    //用户28天内帖子平均评论率
    double p_max_view_comment_ratio_28d = 13;    //用户28天内帖子最高评论率
}

message UserSquarePreferOffline {
    option (rcmd.persona.profileId) = 110110600;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_square_prefer_offline";

    double u_ctr_p_male_14d = 1;  // 用户14天男性帖子的平滑点击率
    double u_ctr_p_female_14d = 2;  // 用户14天女性帖子的平滑点击率
    double u_ctr_odd_p_female_14d = 3;  // u_ctr_p_female_14d/u_ctr_p_male_14d
    double u_ctr_p_male_7d = 4;  // 同上
    double u_ctr_p_female_7d = 5;
    double u_ctr_odd_p_female_7d = 6;
    double u_ctr_p_male_3d = 7;  // 同上
    double u_ctr_p_female_3d = 8;
    double u_ctr_odd_p_female_3d = 9;
}

message UserSquareSequenceOffline {
    option (rcmd.persona.profileId) = 110111300;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_square_sequence_offline";

    string u_thump_up_image_hw_tag_seq = 1;  // 用户点赞帖子的图片tag序列（最新20个，空格分隔）
    string u_comment_image_hw_tag_seq = 2;  // 用户评论帖子的图片tag序列（最新20个，空格分隔）
}

message UserSquareImageTagOffline {
    option (rcmd.persona.profileId) = 110112200;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_square_imagetag_offline";

    string u_prefer_imagetag_seq = 1;  // 用户偏好图片Top2一级标签序列，空格分隔
}

message UserSquarePublisherOffline {
    option (rcmd.persona.profileId) = 110112000;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_pubilsher_stat";

    map<uint32,uint32> u_p_view_p_cnt_1d        = 1; //user*pubilsher 1天停留帖子数
    map<uint32,uint32> u_p_click_p_cnt_1d       = 2; //user*pubilsher 1天点击帖子数
    map<uint32,uint32> u_p_thump_up_p_cnt_1d    = 3; //user*pubilsher 1天点赞帖子数
    map<uint32,uint32> u_p_view_time_1d         = 4; //user*pubilsher 1天停留时长
    map<uint32,uint32> u_p_view_p_cnt_3d        = 5; //user*pubilsher 3天停留帖子数
    map<uint32,uint32> u_p_click_p_cnt_3d       = 6; //user*pubilsher 3天点击帖子数
    map<uint32,uint32> u_p_thump_up_p_cnt_3d    = 7; //user*pubilsher 3天点赞帖子数
    map<uint32,uint32> u_p_view_time_3d         = 8; //user*pubilsher 3天停留时长
}

message UserChannleAccumAction{
    option (rcmd.persona.profileId) = 110110000;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_channel_accum_action";

    map<string, uint32> game_browse_count_accum=1;  //用户累计对不同游戏的浏览次数,key为游戏id
    message Append {
        option (rcmd.persona.profileId) = 110110001;

        map<string, uint32> game_browse_count_accum=1[(rcmd.persona.oper)="incrby"];
    }
}


message UserChannleEnterAction{
    option (rcmd.persona.profileId) = 110200200;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_channel_enter_action";

    // 用户近6次进房房间类型及停留时长
    repeated string UserEnter = 1[(persona.decay_len)=6, (persona.type)="list"];

    // 用户近6次开黑房间类型
    repeated string UserGangup = 2[(persona.decay_len)=6, (persona.type)="list"];


    message UserEnterInfo {
        string tag_id = 1;             //房间类型
        string room_name=2;            // 房间标题
    }

    //用户近6次进房标题序列[{'tag_id':17,'room_name':'name'},{}...]
    repeated UserEnterInfo UserEnterRoomName = 3[(persona.decay_len)=6, (persona.type)="list"];


    message AppendUserEnter{
        option (rcmd.persona.profileId) = 110200201;

        repeated string UserEnter = 1[(rcmd.persona.oper)="add"];
    }

    message AppendUserGangup{
        option (rcmd.persona.profileId) = 110200202;

        repeated string UserGangup = 1[(rcmd.persona.oper)="add"];
    }

    message AppendUserEnterName {
        option (rcmd.persona.profileId) = 110200203;
        repeated UserEnterInfo UserEnterRoomName = 1[(rcmd.persona.oper) = "add"];
    }
}


message UserChannleEnterActionForGamecard{
    option (rcmd.persona.profileId) = 110200300;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_channel_enter_action_for_gamecard";

    // 用户近6次进房房间类型及停留时长
    repeated string UserEnter = 1[(persona.decay_len)=6, (persona.type)="list"];

    message Append{
        option (rcmd.persona.profileId) = 110200301;

        repeated string UserEnter = 1[(rcmd.persona.oper)="add"];
    }
}

message UserChannleEnterActionSeq{
    option (rcmd.persona.profileId) = 110200700;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_channel_enter_action_seq";

    string user_enter_room_owner_uid = 1;
    string user_enter_tag_id = 2;
    string user_enter_room_time = 3;
    string user_enter_room_duration = 4;
    string user_gangup_room_attr = 5;
}

message UserChannleEnterRisk{
    option (rcmd.persona.profileId) = 110202800; // 修改
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_channel_enter_risk";

    uint32 enter_source = 1;
}

// 用户分数
// Deprecated
message UserSingScore{
    option (rcmd.persona.profileId) = 110110100;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_sing_score";
    uint32 song_god_score = 1;
    uint32 song_god_count = 2;
    uint32 out_tone_score = 3;
    uint32 out_tone_count = 4;
    message SongGodAppend {
        option (rcmd.persona.profileId) = 110110101;
        uint32 song_god_score = 1[(rcmd.persona.oper)="incrby"];
        uint32 song_god_count = 2[(rcmd.persona.oper)="incrby"];
        uint32 out_tone_score = 3[(rcmd.persona.oper)="incrby"];
        uint32 out_tone_count = 4[(rcmd.persona.oper)="incrby"];
    }
}

// 用户分数V2
message UserSingScoreV2 {
    option (rcmd.persona.profileId) = 110110900;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_sing_score_v2";
    map<string, uint32> tone_score = 1;
    map<string, uint32> tone_count = 2;
    message Append {
        option (rcmd.persona.profileId) = 110110901;
        map<string, uint32> tone_score=1[(rcmd.persona.oper)="incrby"];
        map<string, uint32> tone_count=2[(rcmd.persona.oper)="incrby"];
    }
}

// 用户游戏卡 跟TT的KGameCardUpdateEvent事件对应
//
message UserGameCard {
    option (rcmd.persona.profileId) = 110112100;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_game_card";

    repeated GameCardInfo cards = 1[(rcmd.persona.type)="set"]; // 游戏卡片信息列表
    string game_card_ids = 2; // 游戏卡ID，英文冒号分隔，例如 123:2322:12233
    string music_card_ids = 3; // 音乐卡ID，英文冒号分隔，例如 123:2322:12233
    string all_card_ids = 4; // 所有卡ID,英文冒号分隔，例如 123:2322:12233
}
message GameCardInfo
{
  uint32 game_card_id     = 1;            // 对应旧服务usertag的tag_id
  string game_name        = 2;            // 对应旧服务usertag的tag_name
  uint32 u_game_id        = 3;            // 全局唯一的game_id,和游戏相关的业务通过这个u_game_id进行关联
  uint32 card_type        = 4;            // 1.游戏卡 2.音乐卡
  repeated GameCardOpt    opt_list  = 5 ;
  string nick_name        = 6;            // 游戏昵称
  repeated string screenshot_list = 7;    //游戏截图
  uint32 modify_at       = 8 ;            //修改时间
}

message GameCardOpt // 游戏卡和音乐卡里面的选项
{
  string opt_name = 1;
  repeated string value_list = 2;
}


// UserGameTags 用户游戏卡片列表，包含所有的游戏卡片信息，单独profile结构
message UserGameTags {
    option (rcmd.persona.profileId) = 110110200;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_game_tags";

    repeated GameTag tags = 1[(rcmd.persona.type)="set"]; // 标签-游戏卡片信息列表（新）
    // GameTagUpdate 标签-游戏卡片信息-更新
    message UserGameTagsUpdate {
        option (rcmd.persona.profileId) = 110110201;
        repeated GameTag tags = 1;
    }
}

message GameCardInputVal {
    string elem_title = 1; // 标题
    string elem_val   = 2; // 输入的值
}

// GameTag 标签-游戏卡片信息
message GameTag {
    uint32 id = 1;  // ID
    string name = 2;    // 名称
    uint32 type = 3;    // 类型
    repeated Attr attrs = 4;    // 属性列表
    // 标签-游戏卡片属性
    message Attr {
        string name = 1;    // 属性名，中文
        repeated string values = 2; // 属性值，多个
        repeated GameCardInputVal input_val = 4;    // 输入值
    }
}


// 用户手机本地游戏列表 - 弃用，下个发版删掉
// Deprecated
message MobileGameList {
    option (rcmd.persona.profileId) = 110110300;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "mobile_game_list";
    // 转换后的tabIds
    repeated int32 tab_ids = 1[(rcmd.persona.type)="set"];
}

// 用户手机本地游戏列表V2 - 第一个版本写入了脏数据，已废弃
message MobileGameListV2 {
    option (rcmd.persona.profileId) = 110110500;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "mobile_game_list_v2";
    // 转换后的tabIds
    repeated int32 tab_ids = 1[(rcmd.persona.type)="set"];
}

// MyTagList 关于我的标签
message MyTagList {
    option (rcmd.persona.profileId) = 110110700;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "my_tag_list";
    repeated string tags = 1[(rcmd.persona.type)="set"];    // 标签列表
}


// 游戏卡提权
message GameTabWeight {
    option (rcmd.persona.profileId) = 110110800;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "game_tab_weight";
    bool cancel_weight = 1; // 新用户是否取消游戏卡提权，false:提权
    bool old_user_weight = 2; // 老用户是否取游戏卡提权，true:提权
    bool had_canceled = 3;  // 是否取消过游戏卡
    message CancelWeightAppend {
        option(rcmd.persona.profileId) = 110110801;
        bool cancel_weight = 1;
    }
    message OldUserWeightAppend {
        option(rcmd.persona.profileId) = 110110802;
        bool old_user_weight = 1;
    }
    message HadCanceledAppend {
        option(rcmd.persona.profileId) = 110110803;
        bool had_canceled = 1;
    }
}

//用户音乐偏好
message UserMusicPrefer {
	option (rcmd.persona.profileId) = 110400300;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_music_prefer";
    // option (rcmd.persona.process) = "hard_view";  // 迁移到features-collect

    uint32 uid=1;
	map<uint32,double> ui_room_tab_duration_rate_1d =2; //<tabid, rate>每个房间类型的时长占比
	map<uint32,double> ui_room_tab_duration_rate_3d =3;
	map<uint32,double> ui_room_tab_duration_rate_1w =4;
	map<uint32,double> ui_room_tab_duration_rate_2w =5;

    map<uint32,UserSongPref> ui_song_prefer=6;    //相关系数

	//你行你唱各专区逗留占比
	repeated string ui_ucus_tag_duration_rate_1d =7; //game_type_id#rate
	repeated string ui_ucus_tag_duration_rate_3d =8;
	repeated string ui_ucus_tag_duration_rate_1w =9;

    //用户音乐房进房偏好
    map<uint32, double> ui_room_music_tab_enter_pref_1w = 10;
    map<uint32, double> ui_room_music_tab_enter_pref_3w = 11;

    // 用户停留时长
    uint32 ui_room_music_duration_1w = 12;
    double ui_room_music_avg_duration_1w = 13;
    uint32 ui_room_music_duration_3w = 14;
    double ui_room_music_avg_duration_3w = 15;
    map<uint32, double> ui_room_music_tab_duration_rate_1w = 16;
    map<uint32, double> ui_room_music_tab_duration_rate_3w = 17;
    map<uint32, double> ui_room_music_tab_duration_1w = 18;
    map<uint32, double> ui_room_music_tab_avg_duration_1w = 19;
    map<uint32, double> ui_room_music_tab_duration_3w = 20;
    map<uint32, double> ui_room_music_tab_avg_duration_3w = 21;

    // 曝光数
    uint32 ui_room_music_expo_cnt_3d = 22;
    map<uint32, double> ui_room_music_tab_expo_cnt_3d = 23;

    // ctr
    double ui_room_music_ctr_3d = 24;
    map<uint32, double> ui_room_music_tab_ctr_3d = 25;

    // 点击
    uint32 ui_room_music_enter_cnt_1w = 26;
    uint32 ui_room_music_enter_cnt_3d = 27;
    uint32 ui_room_music_enter_cnt_3w = 28;
    map<uint32, uint32> ui_room_music_tab_enter_cnt_3d = 29;
    map<uint32, uint32> ui_room_music_tab_enter_cnt_1w = 30;
    map<uint32, uint32> ui_room_music_tab_enter_cnt_3w = 31;

    // 有效停留
    uint32 ui_room_music_kaihei_cnt_1w = 32;
    double ui_room_music_cvr_1w = 33;
    uint32 ui_room_music_kaihei_cnt_3w = 34;
    double ui_room_music_cvr_3w = 35;
    map<uint32, uint32> ui_room_music_tab_kaihei_cnt_1w = 36;
    map<uint32, double> ui_room_music_tab_cvr_1w = 37;
    map<uint32, uint32> ui_room_music_tab_kaihei_cnt_3w = 38;
    map<uint32, double> ui_room_music_tab_cvr_3w = 39;

    // 花花偏好
    map<string, float> ui_room_music_flower_count = 40;

    // 歌手偏好
    map<string, float> ui_room_music_list_singer_exp_cnt_1w = 41;
    map<string, float> ui_room_music_list_singer_clc_cnt_1w = 42;
    map<string, float> ui_room_music_list_singer_exp_cnt_3w = 43;
    map<string, float> ui_room_music_list_singer_clc_cnt_3w = 44;

    message UcusTagDurationUpdate {
        option(rcmd.persona.profileId) = 110400301;
        repeated string ui_ucus_tag_duration_rate_1d =1; //game_type_id#rate
		repeated string ui_ucus_tag_duration_rate_3d =2;
		repeated string ui_ucus_tag_duration_rate_1w =3;
    }

    message UserSingerPreferUpdate {
        option(rcmd.persona.profileId) = 110400302;
        map<string, float> ui_room_music_list_singer_exp_cnt_1w =1; //game_type_id#rate
        map<string, float> ui_room_music_list_singer_clc_cnt_1w =2;
        map<string, float> ui_room_music_list_singer_exp_cnt_3w =3;
        map<string, float> ui_room_music_list_singer_clc_cnt_3w =4;
    }

}

// 用户音乐二级房间偏好
message UserMusicSecondPrefer {
    option (rcmd.persona.profileId) = 110400400;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_music_second_prefer";

    map<string, double> ui_sectab_exposure_count_1w = 1;  // 用户7d内音乐二级类目曝光次数
    map<string, double> ui_sectab_exposure_count_3w = 2;  // 用户21d内音乐二级类目曝光次数
    map<string, double> ui_sectab_enter_count_1w = 3;  // 用户7d内音乐二级类目进房次数
    map<string, double> ui_sectab_enter_count_3w = 4;  // 用户21d内音乐二级类目进房次数
    map<string, double> ui_sectab_enter_duration_1w = 5;  // 用户7d内音乐二级类目进房总时长
    map<string, double> ui_sectab_enter_duration_3w = 6;  // 用户21d内音乐二级类目进房总时长

    map<string, double> ui_room_sectab_enter_pref_1w = 7; // 用户7d内音乐二级类目进房偏好
    map<string, double> ui_room_sectab_enter_pref_3w = 8; // 用户21d内音乐二级类目进房偏好

    map<string, float> ui_quick_enter_count_1w = 9;  // 用户7d内快速进房类目偏好（你行你唱有二级类目，其他只有一级类目）
    map<string, float> ui_quick_enter_count_3w = 10; // 用户21d内快速进房类目偏好（你行你唱有二级类目，其他只有一级类目）
}

// 用户对房间外显的偏好
message UserMusicExpoPrefer {
    option (rcmd.persona.profileId) = 110400500;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_music_expo_prefer";

    float u_enter_female_cnt_1w = 1;  // 用户进房次数（房主是女性）
    float u_enter_male_cnt_1w = 2;   // 用户进房次数（房主是男性）
    float u_enter_female_smooth_ratio_1w = 3;    // 用户对房主的性别偏好（平滑）：0.5无偏向
    repeated float u_enter_memberCnt_dist_1w = 4;  // 用户对房间人数的偏好
    repeated float u_enter_memberCnt_dist_195_1w = 5;  // 用户对195房间人数的偏好
    repeated float u_enter_memberCnt_dist_198_1w = 6;
    repeated float u_enter_memberCnt_dist_199_1w = 7;
    repeated float u_enter_memberCnt_dist_200_1w = 8;
    repeated float u_enter_memberCnt_dist_222_1w = 9;
    string u_expo_song_seq = 10;  // 用户进房的外显歌曲名序列（由于中文，用制表符分隔）
    string u_expo_titleTags_seq_1w = 11;  // 用户7d内进房的标题关键词标签序列
    string u_expo_titleTags_seq_3w = 12;  // 用户21d内进房的标题关键词标签序列

    float u_enter150s_female_cnt_1w = 13;
    float u_enter150s_male_cnt_1w = 14;
    float u_enter150s_female_smooth_ratio_1w = 15;
    repeated float u_enter150s_memberCnt_dist_1w = 16;
    repeated float u_enter150s_memberCnt_dist_195_1w = 17;
    repeated float u_enter150s_memberCnt_dist_198_1w = 18;
    repeated float u_enter150s_memberCnt_dist_199_1w = 19;
    repeated float u_enter150s_memberCnt_dist_200_1w = 20;
    repeated float u_enter150s_memberCnt_dist_222_1w = 21;
    string u_enter150s_expo_song_seq_1w = 22;
    string u_enter150s_expo_titleTags_seq_1w = 23;
    string u_enter150s_expo_titleTags_seq_3w = 24;

    string u_enter_age_group_seq_1w = 25;
    string u_enter150s_age_group_seq_1w = 26;
}

// 用户对房间外显的偏好(克隆画像UserMusicExpoPrefer,验证features服务上线)
message UserMusicExpoPreferClone {
    option (rcmd.persona.profileId) = 110400700;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_music_expo_prefer_clone";
}

// 用户在房内的行为统计
message UserMusicRoomActions {
    option (rcmd.persona.profileId) = 110400800;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_music_room_actions";

    map<string, float> ui_enter_cnt_28d = 1;
    map<string, float> ui_enter_duration_mean_28d = 2;
    map<string, float> ui_mic_cnt_28d = 3;
    map<string, float> ui_mic_duraiton_mean_28d = 4;
    map<string, float> ui_fastquit_cnt_28d = 5;
    map<string, float> ui_gangup_cnt_28d = 6;
    map<string, float> ui_mic_gangup_cnt_28d = 7;
    map<string, float> ui_send_msg_cnt_28d = 8;
    map<string, float> ui_follow_cnt_28d = 9;
    map<string, float> ui_gift_cnt_28d = 10;

    float u_enter_cnt_28d = 11;
    float u_mic_cnt_28d = 12;
    float u_fastquit_cnt_28d = 13;
    float u_gangup_cnt_28d = 14;
    float u_mic_gangup_cnt_28d = 15;
    float u_send_msg_cnt_28d = 16;
    float u_follow_cnt_28d = 17;
    float u_collect_cnt_28d = 18;
    float u_gift_cnt_28d = 19;
}

// 用户正向/负向行为的房间（可用于召回）
message UserMusicRoomActionsRecall {
    option (rcmd.persona.profileId) = 110400900;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_music_room_actions_recall";

    repeated string u_positive_rooms = 1;
    repeated string u_negative_rooms = 2;
}

message UserSongPref {
	map<string,double> ui_song_click_rate_1d=1;    //<songid,ctr>	//ctr
	map<string,double> ui_song_click_rate_3d=2;    //<songid,ctr>	//ctr
	map<string,double> ui_song_click_rate_1w=3;    //<songid,ctr>	//ctr

	map<string,float> ui_song_listen_rate_1d=4;    //<songid,dayListenRate> 听歌率
	map<string,float> ui_song_listen_rate_3d=5;    //<songid,dayListenRate>
    map<string,float> ui_song_listen_rate_1w=6;    //<songid,weekListenRate>

	map<string,float> ui_song_sing_rate_1d=8;    //<songid,dayListenRate> 唱歌率
	map<string,float> ui_song_sing_rate_3d=9;    //<songid,dayListenRate>
    map<string,float> ui_song_sing_rate_1w=10;    //<songid,weekListenRate>

    map<string,float> ui_song_listen_complete_rate_1d=11;	//<songid,listenCompleteRate> 完整播放率
	map<string,float> ui_song_listen_complete_rate_3d=12;	//<songid,listenCompleteRate> 完整播放率
	map<string,float> ui_song_listen_complete_rate_1w=13;	//<songid,listenCompleteRate> 完整播放率

	map<string,float> ui_song_score=14;        //<songid,maxscore>

    map<string,float> ui_song_sing_sum_1d=15;   //<songid,singsum> 演唱次数
	map<string,float> ui_song_sing_sum_3d=16;   //<songid,singsum> 演唱次数
	map<string,float> ui_song_sing_sum_1w=17;   //<songid,singsum> 演唱次数

	//https://q9jvw0u5f5.feishu.cn/docs/doccnI4cEpLWrVPUIltcAHOZNZY
    //唱维度 和 听维度 偏好系数
    map<string,float> ui_sing_song_preference_score_1d = 18;
	map<string,float> ui_sing_song_preference_score_3d = 19;
	map<string,float> ui_sing_song_preference_score_1w = 20;
    map<string,float> ui_listen_song_preference_song_1d = 21;
	map<string,float> ui_listen_song_preference_song_3d = 22;
	map<string,float> ui_listen_song_preference_song_1w = 23;

}

message UserMusicChannelEnterAction{
    option (rcmd.persona.profileId) = 110200400;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_music_channel_enter_action";

    // 用户近5次进房房间类型和逗留时常
    repeated string UserMusicChannel = 1[(persona.decay_len)=5, (persona.type)="list"];	//tag_id和duration用#号分隔,时长单位是秒
	repeated string UserMusicChannelWithRegion = 2[(persona.decay_len)=8, (persona.type)="list"];	//格式为：tag_id:region_id#duration 时长单位是秒

    message AppendMusicChannel{
        option (rcmd.persona.profileId) = 110200401;

        repeated string UserMusicChannel = 1[(rcmd.persona.oper)="add"];
		repeated string UserMusicChannelWithRegion = 2[(rcmd.persona.oper)="add"];
    }

}



// 用户当前所在房间类型
message UserInChannel{
    option (rcmd.persona.profileId) = 110111100;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_in_channel";
    enum ChannelType{
        Default = 0; // 无效
        UGCChannel = 1; // ugc房
        OtherChannel = 2; // 其它
    }
    uint32 in_channel_type = 1; // 用户当前所在房间#ChannelType
    uint32 stayChannelId = 2;

 message UpdateStayChannelId {
      option(persona.profileId) = 110111101;
      uint32 stayChannelId = 1;
  }
}

// 只有开黑预约用户才会有该画像
message UserAppStatus{
    option (rcmd.persona.profileId) = 110111200;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_app_status";
    enum AppStatus {
        AppNone = 0;   //none
        AppBackGround = 1; //后台
        AppAWait = 2;    //唤醒
    }
    uint32 app_status  = 1;
    int64 app_change_time  = 2;
}

// 用户行为动作的基数统计画像
message UserActionStat {
    option (rcmd.persona.profileId) = 110111400;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_action_stat";


   // uint32 tc_deliver_times = 1[(rcmd.persona.freeze)="last_prefer_time"]; // 新用户开黑列表下发次数
   uint32 tc_deliver_times = 1; // 新用户开黑列表下发次数

    // 已废弃
    repeated uint32 enter_channel_set = 2[(rcmd.persona.type)="set",(rcmd.persona.freeze)="last_prefer_time"]; // 进房次数

    // 用户近10次进出的房间id 以及时间戳 channel_id + _ + timestamp + _ + action + _ + tab_id
    repeated string channel_traces = 4[(persona.decay_len)=11, (persona.type)="list"];

    // 已废弃
    repeated uint64 prefer_deliver_list = 5[(rcmd.persona.type)="list"]; // 偏好确认框下发时间列表
    // 已废弃
    uint64 last_prefer_time = 6; // 最近一次偏好确认框下发时间
    // 已废弃
    int64  last_prefer_time_ttl = 7[(rcmd.persona.parasite)="last_prefer_time|ttl"];

    // 已废弃
    bool cancel_prefer_game_re_rank = 8; // 取消偏好提权

    // 用户近10次非历史偏好，有效进房5s房间id、进房时间戳、离开房间时间戳以及进房时的tab_id   channel_id + _ + enter_timestamp + _ + leave_timestamp + _ + tab_id
    repeated string valid_enter_channels = 9[(persona.decay_len)=4, (persona.type)="list"];


    enum Action {
        None = 0;
        EnterChannel = 1; // 进入房间
        LeaveChannel = 2; // 退出房间
    }

    message AddEnterChannelSet {
        option (rcmd.persona.profileId) = 110111401;
        repeated uint32 enter_channel_set = 1[(rcmd.persona.oper)="add"];
    }
    message AddChannelTraces {
        option (rcmd.persona.profileId) = 110111402;
        repeated string channel_traces = 1[(rcmd.persona.oper)="add"];
    }
    message SetLastPreferDeliverTime {
        option (rcmd.persona.profileId) = 110111403;
        uint64 last_prefer_time = 1;
    }
    message AddPreferDeliverTime {
        option (rcmd.persona.profileId) = 110111404;
        repeated uint64 prefer_deliver_list = 1[(rcmd.persona.oper)="add"]; // 偏好确认下发时间列表
    }

    message RecentEnterTrace {
        option (rcmd.persona.profileId) = 110111405;
        repeated string channel_traces = 1;
        uint64 last_prefer_time = 2; // 最近一次偏好确认框下发时间
        int64  last_prefer_time_ttl = 3;
        bool cancel_prefer_game_re_rank = 4; // 取消偏好提权
    }

    message SetCancelPreferGameReRank {
        option (rcmd.persona.profileId) = 110111406;
        bool cancel_prefer_game_re_rank = 1; // 取消偏好提权
    }
    message IncTCDeliverTimes {
        option (rcmd.persona.profileId) = 110111407;
        uint32 tc_deliver_times = 1[(rcmd.persona.oper)="incrby"];// 新用户开黑列表下发次数
    }

    message AddVaildEnterChannel {
        option (rcmd.persona.profileId) = 110111408;
       repeated string valid_enter_channels = 1[(rcmd.persona.oper)="add"];
    }
}


// 已废弃
message UserDeliverPrefGames {
    option (rcmd.persona.profileId) = 110111500;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_deliver_pref_games";

    message Game {
        uint32 tab_id = 1;
        repeated dimension.PreferGameLabel labels = 2;
    }

    // repeated Game games = 1;//用户偏好确认框选择的类型
    uint64 confirm_time = 2; // 偏好确认框提交时间, 偏好确认框的提交时间要大于 UserActionStat.last_prefer_time 才是有效的
    repeated Game selected_games = 3[(persona.type)="list"];//用户偏好确认框选择的类型
    message SetUserDeliverPrefGames {
      option (rcmd.persona.profileId) = 110111501;
      // repeated Game games = 1[(rcmd.persona.oper)="set"];
      uint64 confirm_time = 2; // 偏好确认框提交时间
      repeated Game selected_games = 3[(rcmd.persona.oper)="set"];
    }
}

//千人千面投放素材标签 leftKey为设备ID
message UserPromotionTags {
    option (rcmd.persona.profileId) = 110111600;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_promotion_tags";

    string device_id = 1; //全小写
    uint32 activate_time = 2; //激活时间
    repeated string promotion_tag_list = 3;//素材标签
}

//用户标签序列特征
message UserChannelEnterTagSeq {
    option (rcmd.persona.profileId) = 110100100;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_channel_enter_tag_seq";

    string gangup_roompara_tag_seq = 1; //开黑房间发布标签序列
    string gangup_roomname_tag_seq = 2; //开黑房间标题标签序列
    string enter_roompara_tag_seq  =3;  //进房房间发布标签序列
    string enter_roomname_tag_seq  =4;  //进房房间标题标签序列
    string enter_roompara_dur_seq  =5;  //进房房间发布标签序列
    string enter_roomname_dur_seq  =6;  //进房房间标题标签序列

}

// 用户负反馈
message UserNegativeFeed {
    option (rcmd.persona.profileId) = 110111800;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_negative_feed";

    // 用户负反馈他人
    map<uint32,int64> user_map = 1; // 房主
    map<uint32,int64> tab_map = 2; // 房间游戏类型
    map<string,int64> channel_cond_map = 3; // 发布条件
    map<string,int64> channel_name_map = 4; // 房间标题切词
    map<string,int64> reason_to_user_map = 5; // 对房主不感兴趣原因

    // 用户被他人负反馈
    int32 be_feed_times = 6;  // 用户被负反馈次数

    // 用户被他人负反馈
    map<string, int32> game_be_feed_times = 7;  // 用户被负反馈次数, 根据游戏区分

    map<uint32,int64> feed_user_in_channel_map = 8; // 房间内不感兴趣用户，访客侧

    map<uint32,int64> be_feed_user_in_channel_map = 9; // 房间内被房主反馈

    map<string, int32> user_negative_num = 10; // 房主被X个用户反馈 - 反馈类型：贩卖色情  - 反馈类型：故意辱骂


    message UserMapAppend {
        option (rcmd.persona.profileId) = 110111801;
        map<uint32,int64> user_map = 1;
    }
    message TabMapAppend {
        option (rcmd.persona.profileId) = 110111802;
        map<uint32,int64> tab_map = 1;
    }
    message ChannelCondAppend {
        option (rcmd.persona.profileId) = 110111803;
        map<string,int64> channel_cond_map = 1;
    }
    message ChannelNameAppend {
        option (rcmd.persona.profileId) = 110111804;
        map<string,int64> channel_name_map = 1;
    }
    message ReasonToUserAppend {
        option (rcmd.persona.profileId) = 110111805;
        map<string,int64> reason_to_user_map = 1;
    }
    message FeedUserInChannelMapAppend {
        option (rcmd.persona.profileId) = 110111806;
        map<uint32,int64> feed_user_in_channel_map = 1;
    }
    message BeFeedUserInChannelMapAppend {
        option (rcmd.persona.profileId) = 110111807;
        map<uint32,int64> be_feed_user_in_channel_map = 1;
    }

}

// schema: rcmd.common.LocationInfo ; 跟画像10017 UserAuthInfo同时写入
message UserLocationInfo {
    option (rcmd.persona.profileId) = 110100200;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_loc";

    string country       = 1;
    string province      = 2;
    string city          = 3;
    string country_code  = 4;
    uint32 province_code = 5;
    uint32 city_code     = 6;
    bool   show_geo_info = 7;
}
//用户等级
message UserLevel {
    option (rcmd.persona.profileId) = 110111900;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_level";

	uint32 nobility_level = 1; //用户贵族等级
	uint32 experience_level = 2; //用户经验等级

	message NobilityUpdate {
        option (rcmd.persona.profileId) = 110111901;
        uint32 nobility_level = 1;
    }

	message ExperienceUpdate {
        option (rcmd.persona.profileId) = 110111902;
        uint32 experience_level = 1;
    }
}


message UserConfig {
    option (rcmd.persona.profileId) = 110101500;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_config";

    bool is_diy_rcmd_close = 1; // 个性化推荐开关, 关闭后，不走算法推荐逻辑
}


// 用户点歌偏好
message UserMusicRoundPrefer {
    option (rcmd.persona.profileId) = 110100300;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_music_round_prefer";
    // option (rcmd.persona.process) = "hard_view";  // 迁移到features-collect

    map<string,uint32> ui_song_round_sum_1d=1;    // <songid,roundsum> 1d歌曲的点歌次数
    map<string,uint32> ui_song_round_sum_3d=2;    // <songid,roundsum> 3d歌曲的点歌次数
    map<string,uint32> ui_song_round_sum_1w=3;    // <songid,roundsum> 1w歌曲的点歌次数
    map<string,uint32> ui_song_round_sum_1m=4;    // <songid,roundsum> 1m歌曲的点歌次数
    string ui_song_round_sum_1d_str=5;    // songid_seq 1d歌曲的点歌序列字符串,songid以,分隔
    string ui_song_round_sum_3d_str=6;    // songid_seq 3d歌曲的点歌序列字符串,songid以,分隔
    string ui_song_round_sum_1w_str=7;    // songid_seq 1w歌曲的点歌序列字符串,songid以,分隔
    string ui_song_round_sum_1m_str=8;    // songid_seq 1m歌曲的点歌序列字符串,songid以,分隔
}


// 用户ktv速配进房偏好
message UserKTVenterPrefer {
    option (rcmd.persona.profileId) = 110100400;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_ktv_enter_prefer";
    // option (rcmd.persona.process) = "hard_view";  // 迁移到features-collect

    uint32 ui_ktv_enter_days_1d=1;    // <room_id,enterdays> 1d进房天数
    uint32 ui_ktv_enter_days_3d=2;    // <room_id,enterdays> 3d进房天数
    uint32 ui_ktv_enter_days_1w=3;    // <room_id,enterdays> 3d进房天数
    uint32 ui_ktv_enter_dur_1d=4;    // <room_id,duration> 1d进房时长
    uint32 ui_ktv_enter_dur_3d=5;    // <room_id,duration> 3d进房时长
    uint32 ui_ktv_enter_dur_1w=6;    // <room_id,duration> 1w进房时长
    float ui_ktv_enter_dur_day_1d=7;    // <room_id,duration> 1d日均进房时长
    float ui_ktv_enter_dur_day_3d=8;    // <room_id,duration> 3d日均进房时长
    float ui_ktv_enter_dur_day_1w=9;    // <room_id,duration> 1w日均进房时长

    map<string,uint32> ui_ktv_room_enter_days_1d=10;    // <room_id,enterdays> 1d房间的进房天数
    map<string,uint32> ui_ktv_room_enter_days_3d=11;    // <room_id,enterdays> 3d房间的进房天数
    map<string,uint32> ui_ktv_room_enter_days_1w=12;    // <room_id,enterdays> 3d房间的进房天数
    map<string,uint32> ui_ktv_room_enter_dur_1d=13;    // <room_id,duration> 1d房间的进房时长
    map<string,uint32> ui_ktv_room_enter_dur_3d=14;    // <room_id,duration> 3d房间的进房时长
    map<string,uint32> ui_ktv_room_enter_dur_1w=15;    // <room_id,duration> 1w房间的进房时长
    map<string,float> ui_ktv_room_enter_dur_day_1d=16;    // <room_id,duration> 1d房间的日均进房时长
    map<string,float> ui_ktv_room_enter_dur_day_3d=17;    // <room_id,duration> 3d房间的日均进房时长
    map<string,float> ui_ktv_room_enter_dur_day_1w=18;    // <room_id,duration> 1w房间的日均进房时长

    map<string,uint32> ui_ktv_topic_enter_days_1d=19;    // <topic_name,enterdays> 1d主题的进房天数
    map<string,uint32> ui_ktv_topic_enter_days_3d=20;    // <topic_name,enterdays> 3d主题的进房天数
    map<string,uint32> ui_ktv_topic_enter_days_1w=21;    // <topic_name,enterdays> 3d主题的进房天数
    map<string,uint32> ui_ktv_topic_enter_dur_1d=22;    // <topic_name,duration> 1d主题的进房时长
    map<string,uint32> ui_ktv_topic_enter_dur_3d=23;    // <topic_name,duration> 3d主题的进房时长
    map<string,uint32> ui_ktv_topic_enter_dur_1w=24;    // <topic_name,duration> 1w主题的进房时长
    map<string,float> ui_ktv_topic_enter_dur_day_1d=25;    // <topic_name,duration> 1d主题的日均进房时长
    map<string,float> ui_ktv_topic_enter_dur_day_3d=26;    // <topic_name,duration> 3d主题的日均进房时长
    map<string,float> ui_ktv_topic_enter_dur_day_1w=27;    // <topic_name,duration> 1w主题的日均进房时长
}

// 用户挂房进房偏好
message UserLeisureEnterPrefer {
    option (rcmd.persona.profileId) = 110100700;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_leisure_enter_prefer";
    // option (rcmd.persona.process) = "hard_view";  // 迁移到features-collect

    uint32 ui_leisure_enter_days_1d=1;                // '1d进房天数'
    uint32 ui_leisure_enter_days_3d=2;                // '3d进房天数'
    uint32 ui_leisure_enter_days_1w=3;                // '1w进房天数'
    uint32 ui_leisure_enter_dur_1d=4;                 // '1d进房时长'
    uint32 ui_leisure_enter_dur_3d=5;                 // '3d进房时长'
    uint32 ui_leisure_enter_dur_1w=6;                 // '1w进房时长'
    float ui_leisure_enter_dur_day_1d=7;             // '1d日均进房时长'
    float ui_leisure_enter_dur_day_3d=8;             // '3d日均进房时长'
    float ui_leisure_enter_dur_day_1w=9;             // '1w日均进房时长'

    map<string,uint32> ui_leisure_room_enter_days_1d=10;           // <room_id,enter_days>'1d房间的进房天数'
    map<string,uint32> ui_leisure_room_enter_days_3d=11;           // <room_id,enter_days>'3d房间的进房天数'
    map<string,uint32> ui_leisure_room_enter_days_1w=12;           // <room_id,enter_days>'1w房间的进房天数'
    map<string,uint32> ui_leisure_room_enter_dur_1d=13;            // <room_id,enter_dur>'1d房间的进房时长'
    map<string,uint32> ui_leisure_room_enter_dur_3d=14;            // <room_id,enter_dur>'3d房间的进房时长'
    map<string,uint32> ui_leisure_room_enter_dur_1w=15;            // <room_id,enter_dur>'1w房间的进房时长'
    map<string,float> ui_leisure_room_enter_dur_day_1d=16;        // <room_id,enter_dur_day>'1d房间的日均进房时长'
    map<string,float> ui_leisure_room_enter_dur_day_3d=17;        // <room_id,enter_dur_day>'3d房间的日均进房时长'
    map<string,float> ui_leisure_room_enter_dur_day_1w=18;        // <room_id,enter_dur_day>'1w房间的日均进房时长'

    map<string,uint32> ui_leisure_topic_enter_days_1d=19;          // <topic,enter_days>'1d主题的进房天数'
    map<string,uint32> ui_leisure_topic_enter_days_3d=20;          // <topic,enter_days>'3d主题的进房天数'
    map<string,uint32> ui_leisure_topic_enter_days_1w=21;          // <topic,enter_days>'1w主题的进房天数'
    map<string,uint32> ui_leisure_topic_enter_dur_1d=22;           // <topic,enter_dur>'1d主题的进房时长'
    map<string,uint32> ui_leisure_topic_enter_dur_3d=23;           // <topic,enter_dur>'3d主题的进房时长'
    map<string,uint32> ui_leisure_topic_enter_dur_1w=24;           // <topic,enter_dur>'1w主题的进房时长'
    map<string,float> ui_leisure_topic_enter_dur_day_1d=25;       // <topic,enter_dur_day>'1d主题的日均进房时长'
    map<string,float> ui_leisure_topic_enter_dur_day_3d=26;       // <topic,enter_dur_day>'3d主题的日均进房时长'
    map<string,float> ui_leisure_topic_enter_dur_day_1w=27;       // <topic,enter_dur_day>'1w主题的日均进房时长'

    float ui_room_music_quick_enter_cnt_1w=28;    //用户近7天音乐快速匹配房间进房次数
    float ui_room_music_quick_enter_cnt_3w=29;    //用户近21天音乐快速匹配房间进房次数
    float ui_room_music_quick_duration_1w=30;     //用户近7天音乐快速匹配房间停留时长
    float ui_room_music_quick_duration_3w=31;     //用户近21天音乐快速匹配房间停留时长
    float ui_room_music_quick_cvr_1w=32;          //用户近7天音乐快速匹配房间开黑率
    float ui_room_music_quick_cvr_3w=33;          //用户近21天音乐快速匹配房间开黑率
    float ui_room_music_quick_avg_duration_1w=34; //用户近7天音乐快速匹配房间平均进房停留时长
    float ui_room_music_quick_avg_duration_3w=35; //用户近21天音乐快速匹配房间平均进房停留时长

    map<string,float> ui_room_music_quick_region_enter_cnt_1w=36;       //用户近7天音乐快速匹配房间二级类型进房次数
    map<string,float> ui_room_music_quick_region_enter_cnt_3w=37;       //用户近21天音乐快速匹配房间二级类型进房次数
    map<string,float> ui_room_music_quick_region_duration_1w=38;        //用户近7天音乐快速匹配房间二级类型停留时长
    map<string,float> ui_room_music_quick_region_duration_3w=39;        //用户近21天音乐快速匹配房间二级类型停留时长
    map<string,float> ui_room_music_quick_region_cvr_1w=40;             //用户近7天音乐快速匹配房间二级类型开黑率
    map<string,float> ui_room_music_quick_region_cvr_3w=41;             //用户近21天音乐快速匹配房间二级类型开黑率
    map<string,float> ui_room_music_quick_region_enter_rate_1w=42;      //用户近7天音乐快速匹配房间二级类型进房占比
    map<string,float> ui_room_music_quick_region_enter_rate_3w=43;      //用户近21天音乐快速匹配房间二级类型进房占比
    map<string,float> ui_room_music_quick_region_duration_rate_1w=44;   //用户近7天音乐快速匹配房间二级类型停留时长占比
    map<string,float> ui_room_music_quick_region_duration_rate_3w=45;   //用户近21天音乐快速匹配房间二级类型停留时长占比
    map<string,float> ui_room_music_quick_region_avg_duration_1w=46;    //用户近7天音乐快速匹配房间二级类型平均进房停留时长
    map<string,float> ui_room_music_quick_region_avg_duration_3w=47;    //用户近21天音乐快速匹配房间二级类型平均进房停留时长

    map<uint32,float> ui_room_music_quick_tag_enter_cnt_1w=48;          //用户近7天音乐快速匹配房间类型进房次数
    map<uint32,float> ui_room_music_quick_tag_enter_cnt_3w=49;          //用户近21天音乐快速匹配房间类型进房次数
    map<uint32,float> ui_room_music_quick_tag_duration_1w=50;           //用户近7天音乐快速匹配房间类型停留时长
    map<uint32,float> ui_room_music_quick_tag_duration_3w=51;           //用户近21天音乐快速匹配房间类型停留时长
    map<uint32,float> ui_room_music_quick_tag_cvr_1w=52;                //用户近7天音乐快速匹配房间类型开黑率
    map<uint32,float> ui_room_music_quick_tag_cvr_3w=53;                //用户近21天音乐快速匹配房间类型开黑率
    map<uint32,float> ui_room_music_quick_tag_enter_rate_1w=54;         //用户近7天音乐快速匹配房间类型进房占比
    map<uint32,float> ui_room_music_quick_tag_enter_rate_3w=55;         //用户近21天音乐快速匹配房间类型进房占比
    map<uint32,float> ui_room_music_quick_tag_duration_rate_1w=56;      //用户近7天音乐快速匹配房间类型停留时长占比
    map<uint32,float> ui_room_music_quick_tag_duration_rate_3w=57;      //用户近21天音乐快速匹配房间类型停留时长占比
    map<uint32,float> ui_room_music_quick_tag_avg_duration_1w=58;       //用户近7天音乐快速匹配房间类型平均进房停留时长
    map<uint32,float> ui_room_music_quick_tag_avg_duration_3w=59;       //用户近21天音乐快速匹配房间类型平均进房停留时长
}

//用户战歌收听偏好
message UserWarsongOffline{
    option (persona.profileId) = 110100900;
    option (persona.version) = 1;
    // option (rcmd.persona.process) = "hard_view";  // 迁移到features-collect

    double ui_warsong_listen_duration_1w = 1; //近1周用户战歌听歌时长（s）
    double ui_warsong_listen_duration_3w = 2; //近3周用户战歌听歌时长（s）
    double ui_warsong_listen_song_cnt_1w = 3; //近1周用户战歌听歌歌曲数
    double ui_warsong_listen_song_cnt_3w = 4; //近3周用户战歌听歌歌曲数
    map<string, float> ui_room_music_warsong_singer_duration_1w = 5; //最近1周用户战歌-歌手听歌时长
    map<string, float> ui_room_music_warsong_singer_duration_rate_1w = 6; //最近1周用户战歌-歌手听歌时长占比
    map<string, float> ui_room_music_warsong_singer_duration_3w = 7; //最近3周用户战歌-歌手听歌时长
    map<string, float> ui_room_music_warsong_singer_duration_rate_3w = 8; //最近3周用户战歌-歌手听歌时长占比
}

// 用户有行为的房间标签序列
message UserActionRoomTagSeq {
    option (rcmd.persona.profileId) = 110100500;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_action_room_tag_seq";

    map<string,string> ut_gangup_room_para_tag_seq = 1;  // 用户有开黑行为的房间属性标签序列,key为游戏tag_id
    map<string,string> ut_gangup_room_name_tag_seq = 2;  // 用户有开黑行为的房间标题标签序列,key为游戏tag_id
    map<string,string> ut_enter_room_para_tag_seq = 3;  // 用户有进房行为的房间属性标签序列,key为游戏tag_id
    map<string,string> ut_enter_room_name_tag_seq = 4;  // 用户有进房行为的房间标题标签序列,key为游戏tag_id
    map<string,string> ut_gangup_room_para_zone_seq = 5;  // 用户有开黑行为的房间属性标签序列-区服,key为游戏tag_id
    map<string,string> ut_enter_room_para_zone_seq = 6;  // 用户有进房行为的房间属性标签序列-区服,key为游戏tag_id
    map<string,string> ut_gangup_room_para_level_seq = 7;  // 用户有开黑行为的房间属性标签序列-游戏段位,key为游戏tag_id
    map<string,string> ut_enter_room_para_level_seq = 8;  // 用户有进房行为的房间属性标签序列-游戏段位,key为游戏tag_id
    map<string,string> ut_gangup_room_name_style_seq = 9;  // 用户有开黑行为的房间标题标签序列-游戏风格,key为游戏tag_id
    map<string,string> ut_enter_room_name_style_seq = 10;  // 用户有进房行为的房间标题标签序列-游戏风格,key为游戏tag_id
}

// 用户离线画像TML版本
message UserOfflineTML {
    option (rcmd.persona.profileId) = 110100600;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_offline_profile_tml";
    option (rcmd.persona.process) = "hard_view";

    map<string, string> ui_room_kaihei_game_id_enter_cnt_7d=1;        //用户近7天对不同game_id的进房次数
    map<string, string> ui_room_kaihei_game_id_duration_sum_7d=2;   //用户近7天对不同game_id的停留时长
    map<string, string> ui_room_kaihei_game_id_gangup_cnt_7d=3;       //用户近7天对不同game_id的开黑次数
    map<string, string> ui_room_kaihei_game_id_mic_cnt_7d=4;          //用户近7天对不同game_id的上麦次数
    map<string, string> ui_room_kaihei_game_id_browse_cnt_3d=5;       //用户近3天对不同game_id的曝光次数
    map<string, string> ui_room_kaihei_game_id_click_cnt_3d=6;        //用户近3天对不同game_id的点击次数

    string ui_user_gangup_room_para_tag_list_rongyao=7;              //王者开黑发布标签序列,暂时截断30个标签
    string ui_user_gangup_room_name_tag_list_rongyao=8;              //王者开黑发布标签序列,暂时截断30个标签
}


message UserRecentFollowRL {
  option(persona.profileId) = 120100100;
  option(persona.app) = "user_recent_follow_rl";
  option(persona.version) = 1;
  repeated uint32 uids = 1[(persona.decay_len)=10, (persona.type)="list"];

  message AppendUid {
      option(persona.profileId) = 120100101;
      repeated uint32 uids = 1[(persona.oper) = "add"];
  }
  message DelUid {
      option(persona.profileId) = 120100102;
      repeated uint32 uids = 1[(persona.oper) = "remove"];
  }
}


message UserSelectPage {
    option(persona.profileId) = 120200100;
    option(persona.app) = "user_select_page";
    option(persona.version) = 1;

    string selectPageFirstTab = 1; // 新用户承接页一级标签列表
    repeated string selectPageElemTitles = 2; // 新用户承接页二级标签列表

    message UpdateSelectPageFirstTab {
        option(persona.profileId) = 120200101;
        string selectPageFirstTab = 1; // 新用户承接页一级标签
    }
}

message UserCollectRoom {
    option(rcmd.persona.profileId) = 120100200;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_collect_room";

    repeated uint32 channel_ids = 1[(rcmd.persona.type)="list"]; // 收藏的房间

    message Collect {
      option(persona.profileId) = 120100201;
      repeated uint32 channel_ids = 1[(rcmd.persona.oper) = "add"];
    }

	message CancelCollect {
      option(persona.profileId) = 120100202;
      repeated uint32 channel_ids = 1[(rcmd.persona.oper) = "remove"];
    }
}

message UserRoomDuration {
    option(rcmd.persona.profileId) = 120100300;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_room_duration";

	//所有个人房数据
    repeated uint32 ui_often_stay_cid_list_7d = 1[(rcmd.persona.type)="list"];  // T+7逗留时长大于10min房间列表
    repeated string ui_room_dur_7d = 2[(rcmd.persona.type)="list"];  // T+7 channelid#逗留时长(用#分割)
}

// 用户被举报信息
message UserTipOff {
    option(rcmd.persona.profileId) = 120100500;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_tip_off";

    uint32 t_14_count = 1; // 用户近14天被举报次数, T+1更新
    message SetT14Count {
        option(rcmd.persona.profileId) = 120100501;
        uint32 t_14_count = 1; // 用户近14天被举报次数, T+1更新
    }
}


// easy-feature 用户侧离线画像
message UserOfflineTmlV2 {
    option (rcmd.persona.profileId) = 110100800;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_offline_profile_tml_v2";

    map<string, float> ui_room_kaihei_game_id_enter_cnt_7d=1;        //用户近7天对不同game_id的进房次数
    map<string, float> ui_room_kaihei_game_id_duration_sum_7d=2;   //用户近7天对不同game_id的停留时长
    map<string, float> ui_room_kaihei_game_id_gangup_cnt_7d=3;       //用户近7天对不同game_id的开黑次数
    map<string, float> ui_room_kaihei_game_id_mic_cnt_7d=4;          //用户近7天对不同game_id的上麦次数
    map<string, float> ui_room_kaihei_game_id_browse_cnt_3d=5;       //用户近3天对不同game_id的曝光次数
    map<string, float> ui_room_kaihei_game_id_click_cnt_3d=6;        //用户近3天对不同game_id的点击次数

    string ui_user_gangup_room_para_tag_list_rongyao=7;              //王者开黑发布标签序列,暂时截断30个标签
    string ui_user_gangup_room_name_tag_list_rongyao=8;              //王者开黑发布标签序列,暂时截断30个标签
    map<string, float> ui_room_kaihei_tag_tfidf_28d=9;                 //基于用户近28d进房行为计算的tfidf值
    float ui_room_kaihei_browse_cnt_3d=10;                             //用户近3天曝光次数
    float ui_room_kaihei_click_cnt_3d=11;                             //用户近3天点击次数
    float ui_room_pgc_enter_cnt_14d=12;                             //用户近14天在pgc房点击次数
    float ui_room_pgc_duration_sum_14d=13;                             //用户近14天在pgc房总停留时长
    float ui_room_ugc_enter_cnt_14d=14;                             //用户近14天在ugc房点击次数
    float ui_room_ugc_duration_sum_14d=15;                             //用户近14天在ugc房总停留时长
    float ui_room_kuolie_enter_cnt_14d=16;                             //用户近14天在kuolie房点击次数
    float ui_room_kuolie_duration_sum_14d=17;                             //用户近14天在kuolie房总停留时长
    float ui_room_tmp_enter_cnt_14d=18;                             //用户近14天在小游戏房点击次数
    float ui_room_tmp_duration_sum_14d=19;                             //用户近14天在小游戏房总停留时长
}


message UserGameCardTokenize {
    option (rcmd.persona.profileId) = 110113000;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_game_card_tokenize";

    map<string,string> game_level = 1;
    map<string,string> service_area = 2;
    map<string,string> game_branch = 3;

}


// 202210互动关系链重逢用户规则版画像--01共同在房房主
message UserReunionMusicRoomowner {
    option (rcmd.persona.profileId) = 110115000;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_reunion_music_roomowner";

    repeated int64 u_user_music_ita_roomowner_cnt_7d=1;    // 用户近7天接触的正向互动的互动房主列表
    repeated int32 u_user_music_ita_roomowner_duration_sum_7d=2;    // 用户近7天接触的正向互动的共同在房时长列表
    repeated string u_user_music_first_tag_names_cnt_7d=3;    // 用户近7天接触的正向互动所在的一级房名列表
    repeated string u_user_music_second_tag_names_cnt_7d=4;    // 用户近7天接触的正向互动所在的二级房名列表
    repeated string u_user_music_last_songnames_cnt_7d=5;    // 用户近7天接触的正向互动的最后一首歌曲名称列表（一级房名为"一起k歌"的才有歌曲名）
    repeated string u_user_music_ita_timestamps_cnt_7d=6;    // 用户近7天接触的共同在房时间戳列表
}

// 202210互动关系链重逢用户规则版画像--02共同上麦
message UserReunionMusicMicUser {
    option (rcmd.persona.profileId) = 110116000;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_reunion_music_mic_user";

    repeated int64 u_user_music_ita_mic_user_cnt_7d=1;    // 用户近7天接触正向互动的互动上麦用户列表
    repeated int32 u_user_music_ita_mic_user_duration_sum_7d =2;    // 用户近7天接触正向互动的共同上麦时长列表
    repeated string u_user_music_mic_first_tag_names_cnt_7d=3;    // 用户近7天接触的互动上麦所在的一级房名列表
    repeated string u_user_music_mic_second_tag_names_cnt_7d=4;    // 用户近7天接触的互动上麦所在的二级房名列表
    repeated string u_user_music_mic_last_songnames_cnt_7d=5;    // 用户近7天接触的互动上麦的最后一首歌曲名称列表（一级房名为"一起k歌"的才有歌曲名）
    repeated string u_user_music_ita_mic_timestamps_cnt_7d=6;    // 用户近7天接触的互动上麦的时间戳列表
}

// 202210互动关系链重逢用户规则版画像--03互发im消息
message UserReunionMusicImUser {
    option (rcmd.persona.profileId) = 110117000;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_reunion_music_im_user";

    repeated int64 u_user_music_ita_im_user_cnt_7d=1;    // 用户近7天接触正向互动的互发im消息用户列表
    repeated int32 u_user_music_ita_im_num_cnt_7d=2;    // 用户近7天接触正向互动的互发im消息条数列表
    repeated string u_user_music_im_ita_timestamps_cnt_7d=3;    // 用户近7天接触的im消息时间戳列表
}



// 202211互动关系链重逢用户算法版画像--01未结成关系链（互发im）
message UserReunionMusicAlgorithmImUser {
    option (rcmd.persona.profileId) = 110115100;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_reunion_music_algorithm_im_user";

    repeated int64 u_user_music_ita_im_users_cnt_28d=1;    // 用户近28天内接触的互发im互动的用户列表
    repeated int32 u_user_music_st_im_nums_cnt_28d=2;    // 近28天内，用户向被互动用户发送im消息的总条数列表
    repeated int32 u_user_music_ts_im_nums_cnt_28d=3;    // 近28天内，被互动向互动用户发送im消息的总条数列表
    repeated string u_user_music_ita_im_timestamps_cnt_28d=4; //所有用户最后互动im消息的时间戳列表
}

// 202211互动关系链重逢用户算法版画像--02未结成关系链（共同上麦）
message UserReunionMusicAlgorithmMicUser {
    option (rcmd.persona.profileId) = 110115200;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_reunion_music_algorithm_mic_user";

    repeated int64 u_user_music_ita_mic_users_cnt_28d=1;    // 用户近28天内接触的共同上麦互动的用户列表
    repeated int32 u_user_music_ita_mic_dur_cnt_28d=2;    // 近28天内，单天互动上麦的最大时长列表
    repeated string u_user_music_mic_first_names_cnt_28d=3;  // 所有用户互动一级房名列表
    repeated string u_user_music_mic_second_names_cnt_28d=4; // 所有用户互动二级房名列表
    repeated string u_user_music_mic_last_songnames_cnt_28d=5; // 所有用户互动的最后歌曲名称列表
    repeated string u_user_music_ita_mic_timestamps_cnt_28d=6; //所有用户最后互动上麦时间戳列表

}

// 202211互动关系链重逢用户算法版画像--03未结成关系链（互发公屏）
message UserReunionMusicAlgorithmMsgUser {
    option (rcmd.persona.profileId) = 110115300;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_reunion_music_algorithm_msg_user";

    repeated int64 u_user_music_ita_msg_users_cnt_28d=1;    // 用户近28天内接触的互发公屏互动的用户列表
    repeated int32 u_user_music_st_msg_nums_cnt_28d=2;    // 近28天内，用户向被互动用户发送公屏消息的总条数列表
    repeated int32 u_user_music_ts_msg_nums_cnt_28d=3;    // 近28天内，被互动向互动用户发送公屏消息的总条数列表
    repeated string u_user_music_msg_first_names_cnt_28d=4;  // 所有用户互动一级房名列表
    repeated string u_user_music_msg_second_names_cnt_28d=5;  // 所有用户互动二级房名列表
    repeated string u_user_music_ita_msg_timestamps_cnt_28d=6; // 所有用户最后互动公屏时间戳列表
}

// 202211互动关系链重逢用户算法版画像--04未结成关系链（共同在房）
message UserReunionMusicAlgorithmStayUser {
    option (rcmd.persona.profileId) = 110115400;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_reunion_music_algorithm_stay_user";

    repeated int64 u_user_music_ita_stay_users_cnt_28d=1;    // 用户近28天内接触的共同在房互动的用户列表
    repeated int32 u_user_music_ita_stay_dur_cnt_28d=2;    // 近28天内，单天互动在房的最大时长列表
    repeated string u_user_music_stay_first_names_cnt_28d=3;  // 所有用户互动一级房名列表
    repeated string u_user_music_stay_second_names_cnt_28d=4; // 所有用户互动二级房名列表
    repeated string u_user_music_stay_last_songnames_cnt_28d=5; // 所有用户互动的最后歌曲名称列表
    repeated string u_user_music_ita_stay_timestamps_cnt_28d=6; //所有用户最后互动时间戳列表
}

// 202211互动关系链重逢用户算法版画像--05结成关系链（但7天内无互动）
message UserReunionMusicAlgorithmNoneItaUser {
    option (rcmd.persona.profileId) = 110115500;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_reunion_music_algorithm_none_ita_user";

    repeated int64 u_user_music_playmate_user_id_cnt_7d=1;    // 7天内结成关系链（相互关注的用户对），但是在关注后的次日起，没有互动的用户列表
}



//归因事件自带数据
message TtUserAttribution {
	option (rcmd.persona.profileId) = *********;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "tt_user_attribution";

	uint32 u_is_new_device = 1;
	uint32 u_platform = 2;
	uint32 u_is_fission = 3;
	uint32 u_is_new_account = 4;
	uint32 u_convert_event_hour = 5;

	string advertiser_id = 6;
	string convert_device_id = 7;
	string app_id = 8;
	string business_id =9;
	string attribution_ver =10;
	string device_status =11;
	string cp_id =12;
	string convert_event=13;
	string convert_event_time=14;

	message ReplyInfo {
      option(persona.profileId) = *********;
      string advertiser_id = 1;
	  string convert_device_id = 2;
	  string app_id = 3;
	  uint32 u_platform = 4;
	  string business_id =5;
	  string attribution_ver =6;
	  string device_status =7;
	  string cp_id =8;
	  string convert_event=9;
	  string convert_event_time=10;
	}
}

//关注统计数据
message TtUserFollow {
	option (rcmd.persona.profileId) = *********;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "tt_user_follow";

	map<string,int32> u_hour_follow_cnt = 1; //关注数，key是小时时间 0~23
	map<string,int32> u_hour_fan_cnt = 2; //粉丝数，key是小时时间 0~23
	int32 u_follow_cnt = 3; //当天关注数
	int32 u_fan_cnt = 4; //当天粉丝数

	//超时时间设置为第二天0点
	message FollowAppend {
        option (rcmd.persona.profileId) = 110112401;
        map<string, int32> u_hour_follow_cnt=1[(rcmd.persona.oper)="incrby"];
		int32 u_follow_cnt=2[(rcmd.persona.oper)="incrby"];
    }

	message FanAppend {
        option (rcmd.persona.profileId) = 110112402;
        map<string, int32> u_hour_fan_cnt=1[(rcmd.persona.oper)="incrby"];
		int32 u_fan_cnt=2[(rcmd.persona.oper)="incrby"];
    }
}

//进房统计数据	
message TtUserEnter {
	option (rcmd.persona.profileId) = 110112500;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "tt_user_enter";

	map<string,int32> u_hour_enter_room_cnt = 1; //进房次数，key是小时时间 0~23
	map<string,int32> u_hour_enter_room_total_duration = 2; //进房逗留总时长，key是小时时间 0~23
	int32 u_enter_room_cnt = 3; //当天进房次数
	int32 u_enter_room_total_duration = 4; //当天进房逗留总时长

	message Append {
        option (rcmd.persona.profileId) = 110112501;
        map<string, int32> u_hour_enter_room_cnt=1[(rcmd.persona.oper)="incrby"];
        map<string, int32> u_hour_enter_room_total_duration=2[(rcmd.persona.oper)="incrby"];
		int32 u_enter_room_cnt=3[(rcmd.persona.oper)="incrby"];
		int32 u_enter_room_total_duration=4[(rcmd.persona.oper)="incrby"];
    }
}

//挚友统计数据	
message TtUserGoodFrind {
	option (rcmd.persona.profileId) = 110112600;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "tt_user_good_friend";

	map<string,int32> u_hour_social_goodfriend_cnt = 1; //更新的挚友数，key是小时时间 0~23
	map<string,int32> u_hour_social_goodfriend_score = 2; //更新的挚友值，key是小时时间 0~23
	int32 u_social_goodfriend_cnt = 3; //当天更新的挚友数
	int32 u_social_goodfriend_score = 4; //当天更新的挚友值

	message Append {
        option (rcmd.persona.profileId) = 110112601;
        map<string, int32> u_hour_social_goodfriend_cnt=1[(rcmd.persona.oper)="incrby"];
        map<string, int32> u_hour_social_goodfriend_score=2[(rcmd.persona.oper)="incrby"];
		int32 u_social_goodfriend_cnt=3[(rcmd.persona.oper)="incrby"];
        int32 u_social_goodfriend_score=4[(rcmd.persona.oper)="incrby"];
    }
}

message TtUserBase {
	option (rcmd.persona.profileId) = 110112700;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "tt_user_base";

	uint32 gender = 1; //性别，1男，2女
	uint32 age = 2;

	message UpdateGender {
        option (rcmd.persona.profileId) = 110112701;
        uint32 gender=1;
    }

	message UpdateAge {
        option (rcmd.persona.profileId) = 110112702;
        uint32 age=1;
    }
}

//麦可关注统计数据
message MkUserFollow {
	option (rcmd.persona.profileId) = 110112800;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "mk_user_follow";

	map<string,int32> u_hour_follow_cnt_mk = 1; //关注数，key是小时时间 0~23
	map<string,int32> u_hour_fan_cnt_mk = 2; //粉丝数，key是小时时间 0~23
	int32 u_follow_cnt_mk = 3; //当天关注数
	int32 u_fan_cnt_mk = 4; //当天粉丝数

	//超时时间设置为第二天0点
	message FollowAppend {
        option (rcmd.persona.profileId) = 110112801;
        map<string, int32> u_hour_follow_cnt_mk=1[(rcmd.persona.oper)="incrby"];
		int32 u_follow_cnt_mk=2[(rcmd.persona.oper)="incrby"];
    }

	message FanAppend {
        option (rcmd.persona.profileId) = 110112802;
        map<string, int32> u_hour_fan_cnt_mk=1[(rcmd.persona.oper)="incrby"];
		int32 u_fan_cnt_mk=2[(rcmd.persona.oper)="incrby"];
    }
}

//麦可进房统计数据	
message MkUserEnter {
	option (rcmd.persona.profileId) = 110112900;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "mk_user_enter";

	map<string,int32> u_hour_enter_room_cnt_mk = 1; //进房次数，key是小时时间 0~23
	map<string,int32> u_hour_enter_room_total_duration_mk = 2; //进房逗留总时长，key是小时时间 0~23
	int32 u_enter_room_cnt_mk = 3; //当天进房次数
	int32 u_enter_room_total_duration_mk = 4; //当天进房逗留总时长

	message Append {
        option (rcmd.persona.profileId) = 110112901;
        map<string, int32> u_hour_enter_room_cnt_mk=1[(rcmd.persona.oper)="incrby"];
        map<string, int32> u_hour_enter_room_total_duration_mk=2[(rcmd.persona.oper)="incrby"];
		int32 u_enter_room_cnt_mk=3[(rcmd.persona.oper)="incrby"];
		int32 u_enter_room_total_duration_mk=4[(rcmd.persona.oper)="incrby"];
    }
}

//麦可挚友统计数据	
message MkUserGoodFrind {
	option (rcmd.persona.profileId) = 110113400;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "mk_user_good_friend";

	map<string,int32> u_hour_social_goodfriend_cnt_mk = 1; //更新的挚友数，key是小时时间 0~23
	map<string,int32> u_hour_social_goodfriend_score_mk = 2; //更新的挚友值，key是小时时间 0~23
	int32 u_social_goodfriend_cnt_mk = 3; //当天更新的挚友数
	int32 u_social_goodfriend_score_mk = 4; //当天更新的挚友值

	message Append {
        option (rcmd.persona.profileId) = 110113401;
        map<string, int32> u_hour_social_goodfriend_cnt_mk=1[(rcmd.persona.oper)="incrby"];
        map<string, int32> u_hour_social_goodfriend_score_mk=2[(rcmd.persona.oper)="incrby"];
		int32 u_social_goodfriend_cnt_mk=3[(rcmd.persona.oper)="incrby"];
        int32 u_social_goodfriend_score_mk=4[(rcmd.persona.oper)="incrby"];
    }
}

//欢游关注统计数据
message HyUserFollow {
	option (rcmd.persona.profileId) = 110113100;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "hy_user_follow";

	map<string,int32> u_hour_follow_cnt_hy = 1; //关注数，key是小时时间 0~23
	map<string,int32> u_hour_fan_cnt_hy = 2; //粉丝数，key是小时时间 0~23
	int32 u_follow_cnt_hy = 3; //当天关注数
	int32 u_fan_cnt_hy = 4; //当天粉丝数

	//超时时间设置为第二天0点
	message FollowAppend {
        option (rcmd.persona.profileId) = 110113101;
        map<string, int32> u_hour_follow_cnt_hy=1[(rcmd.persona.oper)="incrby"];
		int32 u_follow_cnt_hy=2[(rcmd.persona.oper)="incrby"];
    }

	message FanAppend {
        option (rcmd.persona.profileId) = 110113102;
        map<string, int32> u_hour_fan_cnt_hy=1[(rcmd.persona.oper)="incrby"];
		int32 u_fan_cnt_hy=2[(rcmd.persona.oper)="incrby"];
    }
}

//欢游进房统计数据	
message HyUserEnter {
	option (rcmd.persona.profileId) = 110113200;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "hy_user_enter";

	map<string,int32> u_hour_enter_room_cnt_hy = 1; //进房次数，key是小时时间 0~23
	map<string,int32> u_hour_enter_room_total_duration_hy = 2; //进房逗留总时长，key是小时时间 0~23
	int32 u_enter_room_cnt_hy = 3; //当天进房次数
	int32 u_enter_room_total_duration_hy = 4; //当天进房逗留总时长

	message Append {
        option (rcmd.persona.profileId) = 110113201;
        map<string, int32> u_hour_enter_room_cnt_hy=1[(rcmd.persona.oper)="incrby"];
        map<string, int32> u_hour_enter_room_total_duration_hy=2[(rcmd.persona.oper)="incrby"];
		int32 u_enter_room_cnt_hy=3[(rcmd.persona.oper)="incrby"];
		int32 u_enter_room_total_duration_hy=4[(rcmd.persona.oper)="incrby"];
    }
}

//欢游挚友统计数据	
message HyUserGoodFrind {
	option (rcmd.persona.profileId) = 110113300;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "hy_user_good_friend";

	map<string,int32> u_hour_social_goodfriend_cnt_hy = 1; //更新的挚友数，key是小时时间 0~23
	map<string,int32> u_hour_social_goodfriend_score_hy = 2; //更新的挚友值，key是小时时间 0~23
	int32 u_social_goodfriend_cnt_hy = 3; //当天更新的挚友数
	int32 u_social_goodfriend_score_hy = 4; //当天更新的挚友值

	message Append {
        option (rcmd.persona.profileId) = 110113301;
        map<string, int32> u_hour_social_goodfriend_cnt_hy=1[(rcmd.persona.oper)="incrby"];
        map<string, int32> u_hour_social_goodfriend_score_hy=2[(rcmd.persona.oper)="incrby"];
		int32 u_social_goodfriend_cnt_hy=3[(rcmd.persona.oper)="incrby"];
        int32 u_social_goodfriend_score_hy=4[(rcmd.persona.oper)="incrby"];
    }
}

/* // 算法在TML注册了, 这个画像不再在persona里提供服务
// 用户风控离线画像
message UserOfflineForRiskControl {
    option (rcmd.persona.profileId) = 110114000;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_offline_for_risk_control";

    uint32 glamour_level=1; // 用户魅力等级
    uint32 wealth_level=2; // 用户财富等级
    uint32 is_identify=3; // 是否实名用户，1
    uint32 is_guild=4; // 有无公会
    uint32 login_cnt_1w=5; // 用户近7天登录数
    uint32 login_cnt_4w=6; // 用户近28天登录数
    uint32 is_member=7; // 有无会员
    uint32 is_pay=8; // 有无消费
    uint32 ord_amt_lst_7day=9; // 最近7天现金消费金额
    uint32 ord_amt_accum=10; // 总现金消费金额
    uint32 followed_count=11; // 用户粉丝人数
}
*/

message UserOfflineGameTag {
    option (rcmd.persona.profileId) = 110114500;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_offline_game_tag";

    map<string, string> u_ugc_game_play_pref_14d = 1;     //用户的偏好游戏标签, key是游戏名, val是标签列表，用逗号分割

	message SetGameTagMap {
        option (rcmd.persona.profileId) = 110114501;
        map<string, string> u_ugc_game_play_pref_14d = 1;
    }
}

message RobotUser {
    option (rcmd.persona.profileId) = 110114700;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "robot_user";
    option (rcmd.persona.process) = "index_es|150104000,rcmd_chat_robot_user_es_view";

    uint32 uid = 1;
    uint32 sex = 2;
    uint32 birthday = 3;
    map<string, string> setting = 4; // 人设
    string nickname = 5;
    uint32 eliza_id = 6; // 人设模版
    uint32 age = 7; // 年龄
    string prompt_alias = 8; // 人设别名
    uint32 status = 9; // 状态，0 正常， 1 下线

    message UpdateSetting {
        option (rcmd.persona.profileId) = 110114701;
        map<string, string> setting = 1; // 人设
    }
    message UpdateElizaId {
        option (rcmd.persona.profileId) = 110114702;
        uint32 eliza_id = 1;
    }
    message UpdateAge {
        option (rcmd.persona.profileId) = 110114703;
        uint32 age = 1;
    }
    message UpdatePromptAlias {
        option (rcmd.persona.profileId) = 110114704;
        string prompt_alias = 1; // 人设别名
    }
    message SetStatus {
        option (rcmd.persona.profileId) = 110114705;
        uint32 status = 1; // 状态，0 正常， 1 下线
    }
}

message UserOfflineRegisterInfo {
    option (rcmd.persona.profileId) = 110114100;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_offline_register_info";

    uint32 u_mijing_login_cnt_28d=1; // 谜境用户近28天登录数
}

message UserOfflineWzInfo {
    option (rcmd.persona.profileId) = 110114800;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_offline_wz_info";

    string u_wz_service = 1;
    string u_wz_level_nd = 2;
    string u_wz_level = 3;
    repeated string u_wz_positions = 4;
    string u_wz_mode = 5;
    string u_wz_type = 6;
}


message UserMaterial {
  option (rcmd.persona.profileId) = 110114200;
  option (rcmd.persona.version) = 1;
  option (rcmd.persona.app) = "user_material";

  repeated int32 game_material_source=1; // 游戏素材来源
}


// 不良生态信息
message UserEcologyRec {
    option(rcmd.persona.profileId) = 110114300;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_ecology_rec";

    // 近7天被人审被人审处罚过次数
    int32 manual_review_punish_times = 1;

    // 用户桌球、呆鸦杀游戏的异常行为次数，包括：逃跑、外挂
    map<uint32, int32> mini_game_abnormal_times = 2;  // 根据游戏区分
    // 近7天生态识侧违规次数
    int32 ecology_rec_ml_abnormal_times = 3;
}

message UserRoomTag{
    option (rcmd.persona.profileId) = 110114900;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_room_tag";

    map<string,float> ui_room_tag_enter_cnt_3d = 1; // 近三天用户一级类型房间进房次数
    map<string,float> ui_room_tag_enter_cnt_7d = 2;  // 近一周用户一级类型房间进房次数
    map<string,float> ui_room_tag_enter_cnt_21d = 3; // 近三周用户一级类型房间进房次数
    map<string,float> ui_room_tag_duration_3d = 4; // 近三天用户一级类型房间停留时长
    map<string,float> ui_room_tag_duration_7d = 5; // 近一周用户一级类型房间停留时长
    map<string,float> ui_room_tag_duration_21d = 6; // 近三周用户一级类型房间停留时长
    map<string,float> ui_room_tag_enter_rate_3d = 7; // 近三天用户一级类型房间进房次数占比
    map<string,float> ui_room_tag_enter_rate_7d = 8; // 近一周用户一级类型房间进房次数占比
    map<string,float> ui_room_tag_enter_rate_21d = 9; // 近三周用户一级类型房间进房次数占比
    map<string, float> ui_room_tag_duration_rate_3d = 10; // 近三天用户一级类型房间停留时长占比
    map<string, float> ui_room_tag_duration_rate_7d = 11; // 近一周用户一级类型房间停留时长占比
    map<string, float> ui_room_tag_duration_rate_21d = 12; // 近三周用户一级类型房间停留时长占比
}

 message UserRegisterTabInfo{
    uint32 tab_id = 1;
    repeated string blockId_elemId = 2; // block_id + "_" + "elem_id"
    string catalog_name = 3; //  品类名
    string tag_name_present = 4;  // 兴趣标签外显名
}

message UserRegisterLabel{
    option (rcmd.persona.profileId) = 110118000;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_register_label";

    repeated int32 user_register_tab=1; // 新用户注册标签tabId
    repeated UserRegisterTabInfo user_register_tab_info=2[(persona.type)="list"]; // 新用户注册标签详细信息

}

message UserChannelEnter {
    option (rcmd.persona.profileId) = 110114400;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_channel_enter";

    uint32 channel_enter_source=1; // 用户进房enter_source
}

message UserAIPartnerInfo {
    option (rcmd.persona.profileId) = 110115700;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_ai_partner_info";

    repeated uint32 ai_partner_ids = 1[(rcmd.persona.type)="set"];
    message AddAIPartnerID {
        option(rcmd.persona.profileId) = 110115701;
        repeated uint32 ai_partner_ids = 1[(rcmd.persona.oper)="add"];
    }
}

message AIPartner {
    option (rcmd.persona.profileId) = 110115800;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "ai_partner";

    uint32 uid = 1; // 所属用户id
    string name = 2;
    string call_name = 3; // 虚拟伴侣对用户的称呼
    uint32 relationship = 4; // 虚拟伴侣与用户的关系 1. 朋友， 2. 恋人
    uint32 role_id = 5; // 虚拟伴侣角色id, 业务的id
    string role_style = 6; // AI伴侣的风格
    uint32 eliza_id = 7; // 关联的prompt id
    uint32 sex = 8; // AI伴侣的性别
    uint32 chatting_status = 9; // 聊天状态 0: 可聊天, 1: 缄口不言

    message SetChattingStatus {
        option(rcmd.persona.profileId) = 110115801;
        uint32 chatting_status = 1; // 聊天状态 0: 可聊天, 1: 缄口不言
    }
}

message UserBlackInfo {
    option (rcmd.persona.profileId) = 110116100;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_black_info";

    bool is_black=1; // 是否是黑产
}

message UserRegInfo {
    option (rcmd.persona.profileId) = 110116900;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_reg_info";

    uint32 reg_time = 1;            //注册时间
    string nickname = 2; // 用户昵称
    message SetUserRegTime {
        option (rcmd.persona.profileId) = 110116901;
        uint32 reg_time = 1;            //注册时间
    }
    message SetNickname {
        option (rcmd.persona.profileId) = 110116902;
        string nickname = 2; // 用户昵称
    }
}


// 监管人群包
message UserSupervisor {
    option (rcmd.persona.profileId) = 110117100;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_supervisor";

    bool is_target_supervisor = 1;
}

// 用户流失
message UserChurn {
    option (rcmd.persona.profileId) = 110117300;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "user_churn";

    int64 churn_marked_ts = 1; // 被标记流失的时间

    message SetChurnMarkedTs {
        option (rcmd.persona.profileId) = 110117301;
        int64 churn_marked_ts = 1; // 被标记流失的时间
    }
}